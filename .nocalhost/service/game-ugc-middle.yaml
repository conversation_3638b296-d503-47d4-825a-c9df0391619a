- name: game-ugc-middle # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../base/dev-config.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/game-ugc-middle/main.go
            - --server.configFile=/config/game-ugc-middle.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
          debug:
            - dlv # debug参数
            - --headless
            - --log
            - --listen :9999
            - --api-version 2
            - --accept-multiclient
            - debug
            - ./services/user/game-ugc-middle/main.go
            - -- --server.configFile=/config/game-ugc-middle.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            - "clients"
            - "protocol"
            - "pkg"
            - "go.mod"
            - "go.sum"
            - "services/game-ugc-middle" # 服务代码目录
            - "services/notify"
            - "services/runtime/v2"
            - "services/runtime/gwcontext"
            - "services/user/user-httplogic/models/metrics"
          ignoreFilePattern: [ ]
        portForward: []