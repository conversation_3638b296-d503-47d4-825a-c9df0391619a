version: v2
modules:
  - path: .
    excludes:
      - tt/quicksilver/smash-egg # 使用了错误的包名，导致与app协议冲突，等待修复
deps:
  - psr.ttyuyin.com/googleapis/googleapis:main
  - psr.ttyuyin.com/tt/app:b277d3ccc16e49136b683862b0bb0aafdd4698d2
breaking:
  use:
    - FILE
  except:
    - EXTENSION_NO_DELETE
    - FIELD_SAME_DEFAULT
lint:
  use:
    - DEFAULT
  ignore:
    - tt/quicksilver/smash-egg # 使用了错误的包名，导致与app协议冲突，等待修复
    - tt/quicksilver/common/tlvpickle/skbuiltintype.proto
  except:
    - PACKAGE_DIRECTORY_MATCH # 屏蔽包名和目录名不一致
    - FILE_LOWER_SNAKE_CASE # 屏蔽文件名不符合小写蛇形命名规则
    - FIELD_NOT_REQUIRED # 屏蔽message属性不存在 required
    - PACKAGE_VERSION_SUFFIX # 屏蔽包包含版本后缀
    - SERVICE_SUFFIX # 屏蔽服务名不符合服务后缀规则
    - ENUM_ZERO_VALUE_SUFFIX # 屏蔽枚举值为0的后缀规则
    - ENUM_VALUE_PREFIX # 屏蔽枚举值前缀规则
    - ENUM_VALUE_UPPER_SNAKE_CASE # 屏蔽枚举值不符合大写蛇形命名规则
    - RPC_REQUEST_STANDARD_NAME # 屏蔽rpc请求和响应消息不符合标准命名规则
    - RPC_RESPONSE_STANDARD_NAME # 屏蔽rpc请求和响应消息不符合标准命名规则
    - RPC_REQUEST_RESPONSE_UNIQUE # 屏蔽rpc请求和响应消息不唯一
  rpc_allow_same_request_response: true