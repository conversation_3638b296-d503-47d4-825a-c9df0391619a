// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mod/mod.proto

package mod // import "golang.52tt.com/protocol/web-server/mod"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// buf:lint:ignore MESSAGE_PASCAL_CASE
type GetTagListResp struct {
	Tags                 []string `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTagListResp) Reset()         { *m = GetTagListResp{} }
func (m *GetTagListResp) String() string { return proto.CompactTextString(m) }
func (*GetTagListResp) ProtoMessage()    {}
func (*GetTagListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mod_4b7c2863816cc557, []int{0}
}
func (m *GetTagListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTagListResp.Unmarshal(m, b)
}
func (m *GetTagListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTagListResp.Marshal(b, m, deterministic)
}
func (dst *GetTagListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagListResp.Merge(dst, src)
}
func (m *GetTagListResp) XXX_Size() int {
	return xxx_messageInfo_GetTagListResp.Size(m)
}
func (m *GetTagListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagListResp proto.InternalMessageInfo

func (m *GetTagListResp) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
type None struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *None) Reset()         { *m = None{} }
func (m *None) String() string { return proto.CompactTextString(m) }
func (*None) ProtoMessage()    {}
func (*None) Descriptor() ([]byte, []int) {
	return fileDescriptor_mod_4b7c2863816cc557, []int{1}
}
func (m *None) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_None.Unmarshal(m, b)
}
func (m *None) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_None.Marshal(b, m, deterministic)
}
func (dst *None) XXX_Merge(src proto.Message) {
	xxx_messageInfo_None.Merge(dst, src)
}
func (m *None) XXX_Size() int {
	return xxx_messageInfo_None.Size(m)
}
func (m *None) XXX_DiscardUnknown() {
	xxx_messageInfo_None.DiscardUnknown(m)
}

var xxx_messageInfo_None proto.InternalMessageInfo

// buf:lint:ignore MESSAGE_PASCAL_CASE
type GetModListReq struct {
	Search               string   `protobuf:"bytes,11,opt,name=search,proto3" json:"search,omitempty"`
	Id                   string   `protobuf:"bytes,12,opt,name=id,proto3" json:"id,omitempty"`
	Tag                  string   `protobuf:"bytes,13,opt,name=tag,proto3" json:"tag,omitempty"`
	Page                 uint32   `protobuf:"varint,21,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32   `protobuf:"varint,22,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetModListReq) Reset()         { *m = GetModListReq{} }
func (m *GetModListReq) String() string { return proto.CompactTextString(m) }
func (*GetModListReq) ProtoMessage()    {}
func (*GetModListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mod_4b7c2863816cc557, []int{2}
}
func (m *GetModListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetModListReq.Unmarshal(m, b)
}
func (m *GetModListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetModListReq.Marshal(b, m, deterministic)
}
func (dst *GetModListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModListReq.Merge(dst, src)
}
func (m *GetModListReq) XXX_Size() int {
	return xxx_messageInfo_GetModListReq.Size(m)
}
func (m *GetModListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetModListReq proto.InternalMessageInfo

func (m *GetModListReq) GetSearch() string {
	if m != nil {
		return m.Search
	}
	return ""
}

func (m *GetModListReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetModListReq) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *GetModListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetModListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
// buf:lint:ignore MESSAGE_PASCAL_CASE
type ModDetail struct {
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Game                 string   `protobuf:"bytes,2,opt,name=game,proto3" json:"game,omitempty"`
	Name                 string   `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,12,opt,name=desc,proto3" json:"desc,omitempty"`
	ImgUrl               string   `protobuf:"bytes,13,opt,name=imgUrl,proto3" json:"imgUrl,omitempty"`
	FileUrl              string   `protobuf:"bytes,14,opt,name=fileUrl,proto3" json:"fileUrl,omitempty"`
	FileSize             uint32   `protobuf:"varint,15,opt,name=fileSize,proto3" json:"fileSize,omitempty"`
	UpdatedAt            uint32   `protobuf:"varint,21,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	CreatedBy            string   `protobuf:"bytes,22,opt,name=createdBy,proto3" json:"createdBy,omitempty"`
	GameShortName        string   `protobuf:"bytes,33,opt,name=gameShortName,proto3" json:"gameShortName,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModDetail) Reset()         { *m = ModDetail{} }
func (m *ModDetail) String() string { return proto.CompactTextString(m) }
func (*ModDetail) ProtoMessage()    {}
func (*ModDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_mod_4b7c2863816cc557, []int{3}
}
func (m *ModDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModDetail.Unmarshal(m, b)
}
func (m *ModDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModDetail.Marshal(b, m, deterministic)
}
func (dst *ModDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModDetail.Merge(dst, src)
}
func (m *ModDetail) XXX_Size() int {
	return xxx_messageInfo_ModDetail.Size(m)
}
func (m *ModDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ModDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ModDetail proto.InternalMessageInfo

func (m *ModDetail) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ModDetail) GetGame() string {
	if m != nil {
		return m.Game
	}
	return ""
}

func (m *ModDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ModDetail) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ModDetail) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *ModDetail) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *ModDetail) GetFileSize() uint32 {
	if m != nil {
		return m.FileSize
	}
	return 0
}

func (m *ModDetail) GetUpdatedAt() uint32 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *ModDetail) GetCreatedBy() string {
	if m != nil {
		return m.CreatedBy
	}
	return ""
}

func (m *ModDetail) GetGameShortName() string {
	if m != nil {
		return m.GameShortName
	}
	return ""
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
// buf:lint:ignore MESSAGE_PASCAL_CASE
type GetModListResp struct {
	List                 []*ModDetail `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetModListResp) Reset()         { *m = GetModListResp{} }
func (m *GetModListResp) String() string { return proto.CompactTextString(m) }
func (*GetModListResp) ProtoMessage()    {}
func (*GetModListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mod_4b7c2863816cc557, []int{4}
}
func (m *GetModListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetModListResp.Unmarshal(m, b)
}
func (m *GetModListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetModListResp.Marshal(b, m, deterministic)
}
func (dst *GetModListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModListResp.Merge(dst, src)
}
func (m *GetModListResp) XXX_Size() int {
	return xxx_messageInfo_GetModListResp.Size(m)
}
func (m *GetModListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetModListResp proto.InternalMessageInfo

func (m *GetModListResp) GetList() []*ModDetail {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetModListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func init() {
	proto.RegisterType((*GetTagListResp)(nil), "mod.getTagListResp")
	proto.RegisterType((*None)(nil), "mod.none")
	proto.RegisterType((*GetModListReq)(nil), "mod.getModListReq")
	proto.RegisterType((*ModDetail)(nil), "mod.modDetail")
	proto.RegisterType((*GetModListResp)(nil), "mod.getModListResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ModClient is the client API for Mod service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ModClient interface {
	// 获取MOD列表
	// buf:lint:ignore RPC_PASCAL_CASE
	GetModList(ctx context.Context, in *GetModListReq, opts ...grpc.CallOption) (*GetModListResp, error)
	// 获取所有标签，缓存本地
	// buf:lint:ignore RPC_PASCAL_CASE
	GetTagList(ctx context.Context, in *None, opts ...grpc.CallOption) (*GetTagListResp, error)
}

type modClient struct {
	cc *grpc.ClientConn
}

func NewModClient(cc *grpc.ClientConn) ModClient {
	return &modClient{cc}
}

func (c *modClient) GetModList(ctx context.Context, in *GetModListReq, opts ...grpc.CallOption) (*GetModListResp, error) {
	out := new(GetModListResp)
	err := c.cc.Invoke(ctx, "/mod.Mod/getModList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modClient) GetTagList(ctx context.Context, in *None, opts ...grpc.CallOption) (*GetTagListResp, error) {
	out := new(GetTagListResp)
	err := c.cc.Invoke(ctx, "/mod.Mod/getTagList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModServer is the server API for Mod service.
type ModServer interface {
	// 获取MOD列表
	// buf:lint:ignore RPC_PASCAL_CASE
	GetModList(context.Context, *GetModListReq) (*GetModListResp, error)
	// 获取所有标签，缓存本地
	// buf:lint:ignore RPC_PASCAL_CASE
	GetTagList(context.Context, *None) (*GetTagListResp, error)
}

func RegisterModServer(s *grpc.Server, srv ModServer) {
	s.RegisterService(&_Mod_serviceDesc, srv)
}

func _Mod_GetModList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModServer).GetModList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mod.Mod/GetModList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModServer).GetModList(ctx, req.(*GetModListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mod_GetTagList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(None)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModServer).GetTagList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mod.Mod/GetTagList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModServer).GetTagList(ctx, req.(*None))
	}
	return interceptor(ctx, in, info, handler)
}

var _Mod_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mod.Mod",
	HandlerType: (*ModServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "getModList",
			Handler:    _Mod_GetModList_Handler,
		},
		{
			MethodName: "getTagList",
			Handler:    _Mod_GetTagList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mod/mod.proto",
}

func init() { proto.RegisterFile("mod/mod.proto", fileDescriptor_mod_4b7c2863816cc557) }

var fileDescriptor_mod_4b7c2863816cc557 = []byte{
	// 408 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x64, 0x92, 0x41, 0x8f, 0xd3, 0x30,
	0x10, 0x85, 0xb7, 0x4d, 0x29, 0x64, 0x4a, 0x0a, 0x1a, 0x60, 0x65, 0xad, 0x38, 0x14, 0x6b, 0x25,
	0xca, 0x81, 0x54, 0x2a, 0x42, 0x9c, 0x59, 0x71, 0x42, 0x2c, 0x87, 0x2c, 0x5c, 0xb8, 0x79, 0xe3,
	0xc1, 0x6b, 0x29, 0x89, 0xb3, 0xb1, 0x01, 0xc1, 0x1f, 0xe0, 0x6f, 0xa3, 0x99, 0x86, 0x96, 0x8a,
	0xdb, 0x7b, 0xdf, 0x4c, 0x26, 0xcf, 0xf6, 0x40, 0xd1, 0x06, 0xbb, 0x69, 0x83, 0x2d, 0xfb, 0x21,
	0xa4, 0x80, 0x59, 0x1b, 0xac, 0x3e, 0x87, 0xa5, 0xa3, 0xf4, 0xc9, 0xb8, 0x0f, 0x3e, 0xa6, 0x8a,
	0x62, 0x8f, 0x08, 0xb3, 0x64, 0x5c, 0x54, 0x8b, 0x55, 0xb6, 0xce, 0x2b, 0xd1, 0x7a, 0x0e, 0xb3,
	0x2e, 0x74, 0xa4, 0x6f, 0xa1, 0x70, 0x94, 0x2e, 0x83, 0xdd, 0x75, 0xdf, 0xe2, 0x29, 0xcc, 0x23,
	0x99, 0xa1, 0xbe, 0x51, 0x8b, 0xd5, 0x64, 0x9d, 0x57, 0xa3, 0xc3, 0x25, 0x4c, 0xbd, 0x55, 0xf7,
	0x85, 0x4d, 0xbd, 0xc5, 0x87, 0x90, 0x25, 0xe3, 0x54, 0x21, 0x80, 0x25, 0xff, 0xa6, 0x37, 0x8e,
	0xd4, 0x93, 0xd5, 0x64, 0x5d, 0x54, 0xa2, 0x99, 0x45, 0xff, 0x8b, 0xd4, 0xe9, 0x8e, 0xb1, 0xd6,
	0xbf, 0xa7, 0x90, 0xb7, 0xc1, 0xbe, 0xa3, 0x64, 0x7c, 0x33, 0xce, 0x9d, 0xec, 0xe7, 0x22, 0xcc,
	0x9c, 0x69, 0x49, 0x4d, 0x85, 0x88, 0x66, 0xd6, 0x31, 0xdb, 0x25, 0x12, 0xcd, 0xcc, 0x52, 0xac,
	0xc7, 0x44, 0xa2, 0x39, 0xbb, 0x6f, 0xdd, 0xe7, 0xa1, 0x19, 0x63, 0x8d, 0x0e, 0x15, 0xdc, 0xfd,
	0xea, 0x1b, 0xe2, 0xc2, 0x52, 0x0a, 0x7f, 0x2d, 0x9e, 0xc1, 0x3d, 0x96, 0x57, 0x9c, 0xf1, 0x81,
	0x64, 0xdc, 0x7b, 0x7c, 0x0a, 0xf9, 0xb7, 0xde, 0x9a, 0x44, 0xf6, 0x6d, 0x1a, 0x0f, 0x75, 0x00,
	0x5c, 0xad, 0x07, 0x62, 0x73, 0xf1, 0x53, 0x8e, 0x97, 0x57, 0x07, 0x80, 0xe7, 0x50, 0x70, 0xf2,
	0xab, 0x9b, 0x30, 0xa4, 0x8f, 0x1c, 0xfd, 0x99, 0x74, 0x1c, 0x43, 0xfd, 0x5e, 0x9e, 0x6a, 0x7f,
	0xf9, 0xb1, 0x47, 0x0d, 0xb3, 0xc6, 0xc7, 0xa4, 0x26, 0xab, 0x6c, 0xbd, 0xd8, 0x2e, 0x4b, 0x7e,
	0xdb, 0xfd, 0x5d, 0x55, 0x52, 0xc3, 0xc7, 0x70, 0x27, 0x85, 0x64, 0x1a, 0xb9, 0xa2, 0xa2, 0xda,
	0x99, 0x6d, 0x07, 0xd9, 0x65, 0xb0, 0xf8, 0x06, 0xe0, 0x30, 0x12, 0x51, 0x06, 0x1c, 0x3d, 0xf0,
	0xd9, 0xa3, 0xff, 0x58, 0xec, 0xf5, 0x09, 0x96, 0xf2, 0xe1, 0xb8, 0x36, 0x98, 0x4b, 0x13, 0x6f,
	0xc8, 0xa1, 0xff, 0x9f, 0x95, 0xd2, 0x27, 0x17, 0x2f, 0xbe, 0x3c, 0x77, 0xa1, 0x31, 0x9d, 0x2b,
	0x5f, 0x6f, 0x53, 0x2a, 0xeb, 0xd0, 0x6e, 0x64, 0x09, 0xeb, 0xd0, 0x6c, 0x7e, 0xd0, 0xf5, 0xcb,
	0x48, 0xc3, 0x77, 0x1a, 0x78, 0x39, 0xaf, 0xe7, 0x52, 0x78, 0xf5, 0x27, 0x00, 0x00, 0xff, 0xff,
	0xe8, 0xa8, 0x95, 0x6b, 0xae, 0x02, 0x00, 0x00,
}
