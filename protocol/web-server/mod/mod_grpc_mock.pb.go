// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: mod/mod.proto

package mod

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockModClient is a mock of ModClient interface.
type MockModClient struct {
	ctrl     *gomock.Controller
	recorder *MockModClientMockRecorder
}

// MockModClientMockRecorder is the mock recorder for MockModClient.
type MockModClientMockRecorder struct {
	mock *MockModClient
}

// NewMockModClient creates a new mock instance.
func NewMockModClient(ctrl *gomock.Controller) *MockModClient {
	mock := &MockModClient{ctrl: ctrl}
	mock.recorder = &MockModClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockModClient) EXPECT() *MockModClientMockRecorder {
	return m.recorder
}

// GetModList mocks base method.
func (m *MockModClient) GetModList(ctx context.Context, in *GetModListReq, opts ...grpc.CallOption) (*GetModListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetModList", varargs...)
	ret0, _ := ret[0].(*GetModListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetModList indicates an expected call of GetModList.
func (mr *MockModClientMockRecorder) GetModList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetModList", reflect.TypeOf((*MockModClient)(nil).GetModList), varargs...)
}

// GetTagList mocks base method.
func (m *MockModClient) GetTagList(ctx context.Context, in *None, opts ...grpc.CallOption) (*GetTagListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTagList", varargs...)
	ret0, _ := ret[0].(*GetTagListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTagList indicates an expected call of GetTagList.
func (mr *MockModClientMockRecorder) GetTagList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTagList", reflect.TypeOf((*MockModClient)(nil).GetTagList), varargs...)
}

// MockModServer is a mock of ModServer interface.
type MockModServer struct {
	ctrl     *gomock.Controller
	recorder *MockModServerMockRecorder
}

// MockModServerMockRecorder is the mock recorder for MockModServer.
type MockModServerMockRecorder struct {
	mock *MockModServer
}

// NewMockModServer creates a new mock instance.
func NewMockModServer(ctrl *gomock.Controller) *MockModServer {
	mock := &MockModServer{ctrl: ctrl}
	mock.recorder = &MockModServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockModServer) EXPECT() *MockModServerMockRecorder {
	return m.recorder
}

// GetModList mocks base method.
func (m *MockModServer) GetModList(ctx context.Context, in *GetModListReq) (*GetModListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetModList", ctx, in)
	ret0, _ := ret[0].(*GetModListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetModList indicates an expected call of GetModList.
func (mr *MockModServerMockRecorder) GetModList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetModList", reflect.TypeOf((*MockModServer)(nil).GetModList), ctx, in)
}

// GetTagList mocks base method.
func (m *MockModServer) GetTagList(ctx context.Context, in *None) (*GetTagListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTagList", ctx, in)
	ret0, _ := ret[0].(*GetTagListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTagList indicates an expected call of GetTagList.
func (mr *MockModServerMockRecorder) GetTagList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTagList", reflect.TypeOf((*MockModServer)(nil).GetTagList), ctx, in)
}
