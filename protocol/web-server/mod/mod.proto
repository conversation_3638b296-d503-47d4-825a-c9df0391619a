syntax = "proto3";

package mod;

option go_package = "golang.52tt.com/protocol/web-server/mod";

service Mod {
  // 获取MOD列表
  // buf:lint:ignore RPC_PASCAL_CASE
  rpc getModList(getModListReq) returns (getModListResp) {}
  // 获取所有标签，缓存本地
  // buf:lint:ignore RPC_PASCAL_CASE
  rpc getTagList(none) returns (getTagListResp) {}
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message getTagListResp{
  repeated string tags = 11;//标签，不包括【全部】这个标签
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message none{
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message getModListReq {
  string search = 11;// 搜索内容
  string id=12;// modId 精准查询，用于直接进入详情页的时候
  string tag = 13;// tag查询
  uint32 page = 21;//分页
  uint32 size = 22;//分页
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
// buf:lint:ignore MESSAGE_PASCAL_CASE
message modDetail{
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  string id = 1; //modId
  string game = 2;//关联游戏
  string name = 11;//mod 名称
  string desc=12;//mod 描述
  string imgUrl=13;//mod 图片链接
  string fileUrl=14;// 文件链接
  uint32 fileSize=15;// 文件大小，单位是KB
  uint32 updatedAt=21;// 发布时间，秒级时间戳
  string createdBy=22;// 发布者
  string gameShortName=33;// 游戏短称，用于创建客户端文件夹
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
// buf:lint:ignore MESSAGE_PASCAL_CASE
message getModListResp{
  repeated modDetail list = 1;
  uint32 total=2;
}