// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/satoshi/satoshi.proto

package satoshi // import "golang.52tt.com/protocol/services/satoshi"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type HelloReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HelloReq) Reset()         { *m = HelloReq{} }
func (m *HelloReq) String() string { return proto.CompactTextString(m) }
func (*HelloReq) ProtoMessage()    {}
func (*HelloReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{0}
}
func (m *HelloReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HelloReq.Unmarshal(m, b)
}
func (m *HelloReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HelloReq.Marshal(b, m, deterministic)
}
func (dst *HelloReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HelloReq.Merge(dst, src)
}
func (m *HelloReq) XXX_Size() int {
	return xxx_messageInfo_HelloReq.Size(m)
}
func (m *HelloReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HelloReq.DiscardUnknown(m)
}

var xxx_messageInfo_HelloReq proto.InternalMessageInfo

type HelloResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HelloResp) Reset()         { *m = HelloResp{} }
func (m *HelloResp) String() string { return proto.CompactTextString(m) }
func (*HelloResp) ProtoMessage()    {}
func (*HelloResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{1}
}
func (m *HelloResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HelloResp.Unmarshal(m, b)
}
func (m *HelloResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HelloResp.Marshal(b, m, deterministic)
}
func (dst *HelloResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HelloResp.Merge(dst, src)
}
func (m *HelloResp) XXX_Size() int {
	return xxx_messageInfo_HelloResp.Size(m)
}
func (m *HelloResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HelloResp.DiscardUnknown(m)
}

var xxx_messageInfo_HelloResp proto.InternalMessageInfo

type LogicSuperviseReq struct {
	TerminalType         uint32   `protobuf:"varint,1,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,3,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	CmdId                uint32   `protobuf:"varint,4,opt,name=cmd_id,json=cmdId,proto3" json:"cmd_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientIp             string   `protobuf:"bytes,6,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LogicSuperviseReq) Reset()         { *m = LogicSuperviseReq{} }
func (m *LogicSuperviseReq) String() string { return proto.CompactTextString(m) }
func (*LogicSuperviseReq) ProtoMessage()    {}
func (*LogicSuperviseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{2}
}
func (m *LogicSuperviseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogicSuperviseReq.Unmarshal(m, b)
}
func (m *LogicSuperviseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogicSuperviseReq.Marshal(b, m, deterministic)
}
func (dst *LogicSuperviseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogicSuperviseReq.Merge(dst, src)
}
func (m *LogicSuperviseReq) XXX_Size() int {
	return xxx_messageInfo_LogicSuperviseReq.Size(m)
}
func (m *LogicSuperviseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LogicSuperviseReq.DiscardUnknown(m)
}

var xxx_messageInfo_LogicSuperviseReq proto.InternalMessageInfo

func (m *LogicSuperviseReq) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *LogicSuperviseReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *LogicSuperviseReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *LogicSuperviseReq) GetCmdId() uint32 {
	if m != nil {
		return m.CmdId
	}
	return 0
}

func (m *LogicSuperviseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LogicSuperviseReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

type LogicSuperviseResp struct {
	ErrorCode            int32    `protobuf:"varint,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	ErrorMessage         string   `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LogicSuperviseResp) Reset()         { *m = LogicSuperviseResp{} }
func (m *LogicSuperviseResp) String() string { return proto.CompactTextString(m) }
func (*LogicSuperviseResp) ProtoMessage()    {}
func (*LogicSuperviseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{3}
}
func (m *LogicSuperviseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogicSuperviseResp.Unmarshal(m, b)
}
func (m *LogicSuperviseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogicSuperviseResp.Marshal(b, m, deterministic)
}
func (dst *LogicSuperviseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogicSuperviseResp.Merge(dst, src)
}
func (m *LogicSuperviseResp) XXX_Size() int {
	return xxx_messageInfo_LogicSuperviseResp.Size(m)
}
func (m *LogicSuperviseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LogicSuperviseResp.DiscardUnknown(m)
}

var xxx_messageInfo_LogicSuperviseResp proto.InternalMessageInfo

func (m *LogicSuperviseResp) GetErrorCode() int32 {
	if m != nil {
		return m.ErrorCode
	}
	return 0
}

func (m *LogicSuperviseResp) GetErrorMessage() string {
	if m != nil {
		return m.ErrorMessage
	}
	return ""
}

type AuthSuperviseReq struct {
	TerminalType         uint32   `protobuf:"varint,1,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,3,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	ReqSourceType        uint32   `protobuf:"varint,5,opt,name=req_source_type,json=reqSourceType,proto3" json:"req_source_type,omitempty"`
	ClientIp             string   `protobuf:"bytes,6,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuthSuperviseReq) Reset()         { *m = AuthSuperviseReq{} }
func (m *AuthSuperviseReq) String() string { return proto.CompactTextString(m) }
func (*AuthSuperviseReq) ProtoMessage()    {}
func (*AuthSuperviseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{4}
}
func (m *AuthSuperviseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthSuperviseReq.Unmarshal(m, b)
}
func (m *AuthSuperviseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthSuperviseReq.Marshal(b, m, deterministic)
}
func (dst *AuthSuperviseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthSuperviseReq.Merge(dst, src)
}
func (m *AuthSuperviseReq) XXX_Size() int {
	return xxx_messageInfo_AuthSuperviseReq.Size(m)
}
func (m *AuthSuperviseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthSuperviseReq.DiscardUnknown(m)
}

var xxx_messageInfo_AuthSuperviseReq proto.InternalMessageInfo

func (m *AuthSuperviseReq) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *AuthSuperviseReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *AuthSuperviseReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *AuthSuperviseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AuthSuperviseReq) GetReqSourceType() uint32 {
	if m != nil {
		return m.ReqSourceType
	}
	return 0
}

func (m *AuthSuperviseReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

type AuthSuperviseResp struct {
	ErrorCode            int32    `protobuf:"varint,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	ErrorMessage         string   `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuthSuperviseResp) Reset()         { *m = AuthSuperviseResp{} }
func (m *AuthSuperviseResp) String() string { return proto.CompactTextString(m) }
func (*AuthSuperviseResp) ProtoMessage()    {}
func (*AuthSuperviseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{5}
}
func (m *AuthSuperviseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthSuperviseResp.Unmarshal(m, b)
}
func (m *AuthSuperviseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthSuperviseResp.Marshal(b, m, deterministic)
}
func (dst *AuthSuperviseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthSuperviseResp.Merge(dst, src)
}
func (m *AuthSuperviseResp) XXX_Size() int {
	return xxx_messageInfo_AuthSuperviseResp.Size(m)
}
func (m *AuthSuperviseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthSuperviseResp.DiscardUnknown(m)
}

var xxx_messageInfo_AuthSuperviseResp proto.InternalMessageInfo

func (m *AuthSuperviseResp) GetErrorCode() int32 {
	if m != nil {
		return m.ErrorCode
	}
	return 0
}

func (m *AuthSuperviseResp) GetErrorMessage() string {
	if m != nil {
		return m.ErrorMessage
	}
	return ""
}

type SuperviseReq struct {
	SuperviseType        string                   `protobuf:"bytes,1,opt,name=supervise_type,json=superviseType,proto3" json:"supervise_type,omitempty"`
	ValMap               map[string]*ValueSpecial `protobuf:"bytes,2,rep,name=val_map,json=valMap,proto3" json:"val_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *SuperviseReq) Reset()         { *m = SuperviseReq{} }
func (m *SuperviseReq) String() string { return proto.CompactTextString(m) }
func (*SuperviseReq) ProtoMessage()    {}
func (*SuperviseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{6}
}
func (m *SuperviseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperviseReq.Unmarshal(m, b)
}
func (m *SuperviseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperviseReq.Marshal(b, m, deterministic)
}
func (dst *SuperviseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperviseReq.Merge(dst, src)
}
func (m *SuperviseReq) XXX_Size() int {
	return xxx_messageInfo_SuperviseReq.Size(m)
}
func (m *SuperviseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperviseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SuperviseReq proto.InternalMessageInfo

func (m *SuperviseReq) GetSuperviseType() string {
	if m != nil {
		return m.SuperviseType
	}
	return ""
}

func (m *SuperviseReq) GetValMap() map[string]*ValueSpecial {
	if m != nil {
		return m.ValMap
	}
	return nil
}

type ForbiddenMsg struct {
	ForbiddenCode        int32    `protobuf:"varint,1,opt,name=forbidden_code,json=forbiddenCode,proto3" json:"forbidden_code,omitempty"`
	ForbiddenMessage     string   `protobuf:"bytes,2,opt,name=forbidden_message,json=forbiddenMessage,proto3" json:"forbidden_message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ForbiddenMsg) Reset()         { *m = ForbiddenMsg{} }
func (m *ForbiddenMsg) String() string { return proto.CompactTextString(m) }
func (*ForbiddenMsg) ProtoMessage()    {}
func (*ForbiddenMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{7}
}
func (m *ForbiddenMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ForbiddenMsg.Unmarshal(m, b)
}
func (m *ForbiddenMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ForbiddenMsg.Marshal(b, m, deterministic)
}
func (dst *ForbiddenMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForbiddenMsg.Merge(dst, src)
}
func (m *ForbiddenMsg) XXX_Size() int {
	return xxx_messageInfo_ForbiddenMsg.Size(m)
}
func (m *ForbiddenMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ForbiddenMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ForbiddenMsg proto.InternalMessageInfo

func (m *ForbiddenMsg) GetForbiddenCode() int32 {
	if m != nil {
		return m.ForbiddenCode
	}
	return 0
}

func (m *ForbiddenMsg) GetForbiddenMessage() string {
	if m != nil {
		return m.ForbiddenMessage
	}
	return ""
}

type ValueSpecial struct {
	// Types that are valid to be assigned to Val:
	//	*ValueSpecial_Int64Val
	//	*ValueSpecial_Int32Val
	//	*ValueSpecial_Uint32Val
	//	*ValueSpecial_Uint64Val
	//	*ValueSpecial_FloatVal
	//	*ValueSpecial_StrVal
	//	*ValueSpecial_BoolVal
	Val                  isValueSpecial_Val `protobuf_oneof:"val"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ValueSpecial) Reset()         { *m = ValueSpecial{} }
func (m *ValueSpecial) String() string { return proto.CompactTextString(m) }
func (*ValueSpecial) ProtoMessage()    {}
func (*ValueSpecial) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{8}
}
func (m *ValueSpecial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValueSpecial.Unmarshal(m, b)
}
func (m *ValueSpecial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValueSpecial.Marshal(b, m, deterministic)
}
func (dst *ValueSpecial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValueSpecial.Merge(dst, src)
}
func (m *ValueSpecial) XXX_Size() int {
	return xxx_messageInfo_ValueSpecial.Size(m)
}
func (m *ValueSpecial) XXX_DiscardUnknown() {
	xxx_messageInfo_ValueSpecial.DiscardUnknown(m)
}

var xxx_messageInfo_ValueSpecial proto.InternalMessageInfo

type isValueSpecial_Val interface {
	isValueSpecial_Val()
}

type ValueSpecial_Int64Val struct {
	Int64Val int64 `protobuf:"varint,1,opt,name=int64_val,json=int64Val,proto3,oneof"`
}

type ValueSpecial_Int32Val struct {
	Int32Val int32 `protobuf:"varint,2,opt,name=int32_val,json=int32Val,proto3,oneof"`
}

type ValueSpecial_Uint32Val struct {
	Uint32Val uint32 `protobuf:"varint,3,opt,name=uint32_val,json=uint32Val,proto3,oneof"`
}

type ValueSpecial_Uint64Val struct {
	Uint64Val uint64 `protobuf:"varint,4,opt,name=uint64_val,json=uint64Val,proto3,oneof"`
}

type ValueSpecial_FloatVal struct {
	FloatVal float32 `protobuf:"fixed32,5,opt,name=float_val,json=floatVal,proto3,oneof"`
}

type ValueSpecial_StrVal struct {
	StrVal string `protobuf:"bytes,6,opt,name=str_val,json=strVal,proto3,oneof"`
}

type ValueSpecial_BoolVal struct {
	BoolVal bool `protobuf:"varint,7,opt,name=bool_val,json=boolVal,proto3,oneof"`
}

func (*ValueSpecial_Int64Val) isValueSpecial_Val() {}

func (*ValueSpecial_Int32Val) isValueSpecial_Val() {}

func (*ValueSpecial_Uint32Val) isValueSpecial_Val() {}

func (*ValueSpecial_Uint64Val) isValueSpecial_Val() {}

func (*ValueSpecial_FloatVal) isValueSpecial_Val() {}

func (*ValueSpecial_StrVal) isValueSpecial_Val() {}

func (*ValueSpecial_BoolVal) isValueSpecial_Val() {}

func (m *ValueSpecial) GetVal() isValueSpecial_Val {
	if m != nil {
		return m.Val
	}
	return nil
}

func (m *ValueSpecial) GetInt64Val() int64 {
	if x, ok := m.GetVal().(*ValueSpecial_Int64Val); ok {
		return x.Int64Val
	}
	return 0
}

func (m *ValueSpecial) GetInt32Val() int32 {
	if x, ok := m.GetVal().(*ValueSpecial_Int32Val); ok {
		return x.Int32Val
	}
	return 0
}

func (m *ValueSpecial) GetUint32Val() uint32 {
	if x, ok := m.GetVal().(*ValueSpecial_Uint32Val); ok {
		return x.Uint32Val
	}
	return 0
}

func (m *ValueSpecial) GetUint64Val() uint64 {
	if x, ok := m.GetVal().(*ValueSpecial_Uint64Val); ok {
		return x.Uint64Val
	}
	return 0
}

func (m *ValueSpecial) GetFloatVal() float32 {
	if x, ok := m.GetVal().(*ValueSpecial_FloatVal); ok {
		return x.FloatVal
	}
	return 0
}

func (m *ValueSpecial) GetStrVal() string {
	if x, ok := m.GetVal().(*ValueSpecial_StrVal); ok {
		return x.StrVal
	}
	return ""
}

func (m *ValueSpecial) GetBoolVal() bool {
	if x, ok := m.GetVal().(*ValueSpecial_BoolVal); ok {
		return x.BoolVal
	}
	return false
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*ValueSpecial) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _ValueSpecial_OneofMarshaler, _ValueSpecial_OneofUnmarshaler, _ValueSpecial_OneofSizer, []interface{}{
		(*ValueSpecial_Int64Val)(nil),
		(*ValueSpecial_Int32Val)(nil),
		(*ValueSpecial_Uint32Val)(nil),
		(*ValueSpecial_Uint64Val)(nil),
		(*ValueSpecial_FloatVal)(nil),
		(*ValueSpecial_StrVal)(nil),
		(*ValueSpecial_BoolVal)(nil),
	}
}

func _ValueSpecial_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*ValueSpecial)
	// val
	switch x := m.Val.(type) {
	case *ValueSpecial_Int64Val:
		b.EncodeVarint(1<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.Int64Val))
	case *ValueSpecial_Int32Val:
		b.EncodeVarint(2<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.Int32Val))
	case *ValueSpecial_Uint32Val:
		b.EncodeVarint(3<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.Uint32Val))
	case *ValueSpecial_Uint64Val:
		b.EncodeVarint(4<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.Uint64Val))
	case *ValueSpecial_FloatVal:
		b.EncodeVarint(5<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(math.Float32bits(x.FloatVal)))
	case *ValueSpecial_StrVal:
		b.EncodeVarint(6<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.StrVal)
	case *ValueSpecial_BoolVal:
		t := uint64(0)
		if x.BoolVal {
			t = 1
		}
		b.EncodeVarint(7<<3 | proto.WireVarint)
		b.EncodeVarint(t)
	case nil:
	default:
		return fmt.Errorf("ValueSpecial.Val has unexpected type %T", x)
	}
	return nil
}

func _ValueSpecial_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*ValueSpecial)
	switch tag {
	case 1: // val.int64_val
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Val = &ValueSpecial_Int64Val{int64(x)}
		return true, err
	case 2: // val.int32_val
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Val = &ValueSpecial_Int32Val{int32(x)}
		return true, err
	case 3: // val.uint32_val
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Val = &ValueSpecial_Uint32Val{uint32(x)}
		return true, err
	case 4: // val.uint64_val
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Val = &ValueSpecial_Uint64Val{x}
		return true, err
	case 5: // val.float_val
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.Val = &ValueSpecial_FloatVal{math.Float32frombits(uint32(x))}
		return true, err
	case 6: // val.str_val
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.Val = &ValueSpecial_StrVal{x}
		return true, err
	case 7: // val.bool_val
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Val = &ValueSpecial_BoolVal{x != 0}
		return true, err
	default:
		return false, nil
	}
}

func _ValueSpecial_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*ValueSpecial)
	// val
	switch x := m.Val.(type) {
	case *ValueSpecial_Int64Val:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.Int64Val))
	case *ValueSpecial_Int32Val:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.Int32Val))
	case *ValueSpecial_Uint32Val:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.Uint32Val))
	case *ValueSpecial_Uint64Val:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.Uint64Val))
	case *ValueSpecial_FloatVal:
		n += 1 // tag and wire
		n += 4
	case *ValueSpecial_StrVal:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.StrVal)))
		n += len(x.StrVal)
	case *ValueSpecial_BoolVal:
		n += 1 // tag and wire
		n += 1
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SuperviseResp struct {
	ForbiddenMsg         *ForbiddenMsg `protobuf:"bytes,1,opt,name=forbiddenMsg,proto3" json:"forbiddenMsg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SuperviseResp) Reset()         { *m = SuperviseResp{} }
func (m *SuperviseResp) String() string { return proto.CompactTextString(m) }
func (*SuperviseResp) ProtoMessage()    {}
func (*SuperviseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{9}
}
func (m *SuperviseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperviseResp.Unmarshal(m, b)
}
func (m *SuperviseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperviseResp.Marshal(b, m, deterministic)
}
func (dst *SuperviseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperviseResp.Merge(dst, src)
}
func (m *SuperviseResp) XXX_Size() int {
	return xxx_messageInfo_SuperviseResp.Size(m)
}
func (m *SuperviseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperviseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SuperviseResp proto.InternalMessageInfo

func (m *SuperviseResp) GetForbiddenMsg() *ForbiddenMsg {
	if m != nil {
		return m.ForbiddenMsg
	}
	return nil
}

type IsNeedStrictAuditReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	AuditSceneCode       string   `protobuf:"bytes,3,opt,name=audit_scene_code,json=auditSceneCode,proto3" json:"audit_scene_code,omitempty"`
	ClientIp             string   `protobuf:"bytes,4,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsNeedStrictAuditReq) Reset()         { *m = IsNeedStrictAuditReq{} }
func (m *IsNeedStrictAuditReq) String() string { return proto.CompactTextString(m) }
func (*IsNeedStrictAuditReq) ProtoMessage()    {}
func (*IsNeedStrictAuditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{10}
}
func (m *IsNeedStrictAuditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNeedStrictAuditReq.Unmarshal(m, b)
}
func (m *IsNeedStrictAuditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNeedStrictAuditReq.Marshal(b, m, deterministic)
}
func (dst *IsNeedStrictAuditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNeedStrictAuditReq.Merge(dst, src)
}
func (m *IsNeedStrictAuditReq) XXX_Size() int {
	return xxx_messageInfo_IsNeedStrictAuditReq.Size(m)
}
func (m *IsNeedStrictAuditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNeedStrictAuditReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsNeedStrictAuditReq proto.InternalMessageInfo

func (m *IsNeedStrictAuditReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IsNeedStrictAuditReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *IsNeedStrictAuditReq) GetAuditSceneCode() string {
	if m != nil {
		return m.AuditSceneCode
	}
	return ""
}

func (m *IsNeedStrictAuditReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

type IsNeedStrictAuditResp struct {
	IsNeed               bool     `protobuf:"varint,1,opt,name=is_need,json=isNeed,proto3" json:"is_need,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsNeedStrictAuditResp) Reset()         { *m = IsNeedStrictAuditResp{} }
func (m *IsNeedStrictAuditResp) String() string { return proto.CompactTextString(m) }
func (*IsNeedStrictAuditResp) ProtoMessage()    {}
func (*IsNeedStrictAuditResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_satoshi_bd98b11cda94baab, []int{11}
}
func (m *IsNeedStrictAuditResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNeedStrictAuditResp.Unmarshal(m, b)
}
func (m *IsNeedStrictAuditResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNeedStrictAuditResp.Marshal(b, m, deterministic)
}
func (dst *IsNeedStrictAuditResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNeedStrictAuditResp.Merge(dst, src)
}
func (m *IsNeedStrictAuditResp) XXX_Size() int {
	return xxx_messageInfo_IsNeedStrictAuditResp.Size(m)
}
func (m *IsNeedStrictAuditResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNeedStrictAuditResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsNeedStrictAuditResp proto.InternalMessageInfo

func (m *IsNeedStrictAuditResp) GetIsNeed() bool {
	if m != nil {
		return m.IsNeed
	}
	return false
}

func init() {
	proto.RegisterType((*HelloReq)(nil), "satoshi.HelloReq")
	proto.RegisterType((*HelloResp)(nil), "satoshi.HelloResp")
	proto.RegisterType((*LogicSuperviseReq)(nil), "satoshi.LogicSuperviseReq")
	proto.RegisterType((*LogicSuperviseResp)(nil), "satoshi.LogicSuperviseResp")
	proto.RegisterType((*AuthSuperviseReq)(nil), "satoshi.AuthSuperviseReq")
	proto.RegisterType((*AuthSuperviseResp)(nil), "satoshi.AuthSuperviseResp")
	proto.RegisterType((*SuperviseReq)(nil), "satoshi.SuperviseReq")
	proto.RegisterMapType((map[string]*ValueSpecial)(nil), "satoshi.SuperviseReq.ValMapEntry")
	proto.RegisterType((*ForbiddenMsg)(nil), "satoshi.ForbiddenMsg")
	proto.RegisterType((*ValueSpecial)(nil), "satoshi.ValueSpecial")
	proto.RegisterType((*SuperviseResp)(nil), "satoshi.SuperviseResp")
	proto.RegisterType((*IsNeedStrictAuditReq)(nil), "satoshi.IsNeedStrictAuditReq")
	proto.RegisterType((*IsNeedStrictAuditResp)(nil), "satoshi.IsNeedStrictAuditResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SatoshiClient is the client API for Satoshi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SatoshiClient interface {
	Hello(ctx context.Context, in *HelloReq, opts ...grpc.CallOption) (*HelloResp, error)
	LogicSupervise(ctx context.Context, in *LogicSuperviseReq, opts ...grpc.CallOption) (*LogicSuperviseResp, error)
	AuthSupervise(ctx context.Context, in *AuthSuperviseReq, opts ...grpc.CallOption) (*AuthSuperviseResp, error)
	Supervise(ctx context.Context, in *SuperviseReq, opts ...grpc.CallOption) (*SuperviseResp, error)
	IsNeedStrictAudit(ctx context.Context, in *IsNeedStrictAuditReq, opts ...grpc.CallOption) (*IsNeedStrictAuditResp, error)
}

type satoshiClient struct {
	cc *grpc.ClientConn
}

func NewSatoshiClient(cc *grpc.ClientConn) SatoshiClient {
	return &satoshiClient{cc}
}

func (c *satoshiClient) Hello(ctx context.Context, in *HelloReq, opts ...grpc.CallOption) (*HelloResp, error) {
	out := new(HelloResp)
	err := c.cc.Invoke(ctx, "/satoshi.Satoshi/Hello", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *satoshiClient) LogicSupervise(ctx context.Context, in *LogicSuperviseReq, opts ...grpc.CallOption) (*LogicSuperviseResp, error) {
	out := new(LogicSuperviseResp)
	err := c.cc.Invoke(ctx, "/satoshi.Satoshi/LogicSupervise", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *satoshiClient) AuthSupervise(ctx context.Context, in *AuthSuperviseReq, opts ...grpc.CallOption) (*AuthSuperviseResp, error) {
	out := new(AuthSuperviseResp)
	err := c.cc.Invoke(ctx, "/satoshi.Satoshi/AuthSupervise", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *satoshiClient) Supervise(ctx context.Context, in *SuperviseReq, opts ...grpc.CallOption) (*SuperviseResp, error) {
	out := new(SuperviseResp)
	err := c.cc.Invoke(ctx, "/satoshi.Satoshi/Supervise", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *satoshiClient) IsNeedStrictAudit(ctx context.Context, in *IsNeedStrictAuditReq, opts ...grpc.CallOption) (*IsNeedStrictAuditResp, error) {
	out := new(IsNeedStrictAuditResp)
	err := c.cc.Invoke(ctx, "/satoshi.Satoshi/IsNeedStrictAudit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SatoshiServer is the server API for Satoshi service.
type SatoshiServer interface {
	Hello(context.Context, *HelloReq) (*HelloResp, error)
	LogicSupervise(context.Context, *LogicSuperviseReq) (*LogicSuperviseResp, error)
	AuthSupervise(context.Context, *AuthSuperviseReq) (*AuthSuperviseResp, error)
	Supervise(context.Context, *SuperviseReq) (*SuperviseResp, error)
	IsNeedStrictAudit(context.Context, *IsNeedStrictAuditReq) (*IsNeedStrictAuditResp, error)
}

func RegisterSatoshiServer(s *grpc.Server, srv SatoshiServer) {
	s.RegisterService(&_Satoshi_serviceDesc, srv)
}

func _Satoshi_Hello_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SatoshiServer).Hello(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/satoshi.Satoshi/Hello",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SatoshiServer).Hello(ctx, req.(*HelloReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Satoshi_LogicSupervise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogicSuperviseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SatoshiServer).LogicSupervise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/satoshi.Satoshi/LogicSupervise",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SatoshiServer).LogicSupervise(ctx, req.(*LogicSuperviseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Satoshi_AuthSupervise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthSuperviseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SatoshiServer).AuthSupervise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/satoshi.Satoshi/AuthSupervise",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SatoshiServer).AuthSupervise(ctx, req.(*AuthSuperviseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Satoshi_Supervise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SuperviseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SatoshiServer).Supervise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/satoshi.Satoshi/Supervise",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SatoshiServer).Supervise(ctx, req.(*SuperviseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Satoshi_IsNeedStrictAudit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsNeedStrictAuditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SatoshiServer).IsNeedStrictAudit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/satoshi.Satoshi/IsNeedStrictAudit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SatoshiServer).IsNeedStrictAudit(ctx, req.(*IsNeedStrictAuditReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Satoshi_serviceDesc = grpc.ServiceDesc{
	ServiceName: "satoshi.Satoshi",
	HandlerType: (*SatoshiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Hello",
			Handler:    _Satoshi_Hello_Handler,
		},
		{
			MethodName: "LogicSupervise",
			Handler:    _Satoshi_LogicSupervise_Handler,
		},
		{
			MethodName: "AuthSupervise",
			Handler:    _Satoshi_AuthSupervise_Handler,
		},
		{
			MethodName: "Supervise",
			Handler:    _Satoshi_Supervise_Handler,
		},
		{
			MethodName: "IsNeedStrictAudit",
			Handler:    _Satoshi_IsNeedStrictAudit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/satoshi/satoshi.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/satoshi/satoshi.proto", fileDescriptor_satoshi_bd98b11cda94baab)
}

var fileDescriptor_satoshi_bd98b11cda94baab = []byte{
	// 797 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x55, 0xdd, 0x6e, 0x23, 0x35,
	0x14, 0xee, 0x24, 0xcd, 0xcf, 0x9c, 0x26, 0xa5, 0xb1, 0x28, 0x64, 0x53, 0x15, 0x4a, 0x60, 0x51,
	0x50, 0xa5, 0x14, 0xa5, 0x80, 0xa0, 0x77, 0xbb, 0x88, 0x55, 0x82, 0x28, 0x42, 0x0e, 0x0a, 0x88,
	0x9b, 0x91, 0x3b, 0xe3, 0x66, 0xad, 0x3a, 0x63, 0xc7, 0xf6, 0x8c, 0xd4, 0x77, 0xe0, 0xa9, 0xe0,
	0x0d, 0xb8, 0xe6, 0x11, 0x78, 0x08, 0x64, 0xcf, 0x4f, 0x32, 0xd9, 0x86, 0x2b, 0xa4, 0xbd, 0x8a,
	0xfd, 0x7d, 0x5f, 0xce, 0xf1, 0xf9, 0x7c, 0x7c, 0x06, 0x3e, 0x31, 0xe6, 0x6a, 0x9d, 0xb0, 0xf0,
	0x41, 0x33, 0x9e, 0x52, 0x75, 0xa5, 0x89, 0x11, 0xfa, 0x35, 0x2b, 0x7e, 0xc7, 0x52, 0x09, 0x23,
	0x50, 0x2b, 0xdf, 0x0e, 0x01, 0xda, 0x53, 0xca, 0xb9, 0xc0, 0x74, 0x3d, 0x3c, 0x02, 0x3f, 0x5f,
	0x6b, 0x39, 0xfc, 0xc3, 0x83, 0xde, 0x0f, 0x62, 0xc9, 0xc2, 0x79, 0x22, 0xa9, 0x4a, 0x99, 0xa6,
	0x98, 0xae, 0xd1, 0xc7, 0xd0, 0x35, 0x54, 0xad, 0x58, 0x4c, 0x78, 0x60, 0x1e, 0x25, 0xed, 0x7b,
	0x17, 0xde, 0xa8, 0x8b, 0x3b, 0x05, 0xf8, 0xf3, 0xa3, 0xa4, 0xe8, 0x0c, 0xfc, 0x15, 0x51, 0x0f,
	0xd4, 0x04, 0x2c, 0xea, 0xd7, 0x9c, 0xa0, 0x9d, 0x01, 0xb3, 0x08, 0x3d, 0x87, 0xe3, 0x90, 0x33,
	0x1a, 0x9b, 0x20, 0xa5, 0x4a, 0x33, 0x11, 0xf7, 0xeb, 0x4e, 0xd1, 0xcd, 0xd0, 0x45, 0x06, 0xa2,
	0x53, 0x68, 0x86, 0xab, 0xc8, 0x06, 0x38, 0x74, 0x74, 0x23, 0x5c, 0x45, 0xb3, 0x08, 0x9d, 0x40,
	0x3d, 0x61, 0x51, 0xbf, 0xe1, 0x30, 0xbb, 0xb4, 0xc9, 0xf2, 0x78, 0x4c, 0xf6, 0x9b, 0x17, 0xde,
	0xc8, 0xc7, 0xed, 0x0c, 0x98, 0xc9, 0xe1, 0xaf, 0x80, 0x76, 0x6b, 0xd0, 0x12, 0x9d, 0x03, 0x50,
	0xa5, 0x84, 0x0a, 0x42, 0x11, 0x65, 0x15, 0x34, 0xb0, 0xef, 0x90, 0x6f, 0x45, 0x44, 0x6d, 0x8d,
	0x19, 0xbd, 0xa2, 0x5a, 0x93, 0x25, 0x75, 0x25, 0xf8, 0xb8, 0xe3, 0xc0, 0xdb, 0x0c, 0x1b, 0xfe,
	0xe5, 0xc1, 0xc9, 0x8b, 0xc4, 0xbc, 0x7e, 0x3b, 0xee, 0xe4, 0x36, 0x1c, 0x6e, 0x6c, 0xf8, 0x14,
	0xde, 0x51, 0x74, 0x1d, 0x68, 0x91, 0xa8, 0x90, 0x66, 0xc9, 0x33, 0x93, 0xba, 0x8a, 0xae, 0xe7,
	0x0e, 0x2d, 0xb2, 0xef, 0xb7, 0xeb, 0x17, 0xe8, 0xed, 0xd4, 0xf4, 0x3f, 0xb9, 0xf5, 0xa7, 0x07,
	0x9d, 0x8a, 0x53, 0xcf, 0xe1, 0x58, 0x17, 0xfb, 0x8d, 0x55, 0x3e, 0xee, 0x96, 0xa8, 0x3b, 0xed,
	0x0d, 0xb4, 0x52, 0xc2, 0x83, 0x15, 0x91, 0xfd, 0xda, 0x45, 0x7d, 0x74, 0x34, 0xf9, 0x68, 0x5c,
	0xf4, 0xf1, 0x76, 0xb8, 0xf1, 0x82, 0xf0, 0x5b, 0x22, 0xbf, 0x8b, 0x8d, 0x7a, 0xc4, 0xcd, 0xd4,
	0x6d, 0x06, 0x3f, 0xc1, 0xd1, 0x16, 0x6c, 0x2d, 0x7b, 0xa0, 0x8f, 0x79, 0x1a, 0xbb, 0x44, 0x97,
	0xd0, 0x48, 0x09, 0x4f, 0xb2, 0x13, 0x1f, 0x4d, 0x4e, 0xcb, 0xd0, 0x0b, 0x8b, 0xce, 0x25, 0x0d,
	0x19, 0xe1, 0x38, 0xd3, 0xdc, 0xd4, 0xbe, 0xf6, 0x86, 0x77, 0xd0, 0x79, 0x25, 0xd4, 0x1d, 0x8b,
	0x22, 0x1a, 0xdf, 0xea, 0xa5, 0x2d, 0xe2, 0xbe, 0xd8, 0x6f, 0xbb, 0xd3, 0x2d, 0x51, 0xe7, 0xd0,
	0x25, 0xf4, 0x36, 0xb2, 0xaa, 0x4b, 0x27, 0x25, 0x51, 0x38, 0xf5, 0x8f, 0x07, 0x9d, 0xed, 0xfc,
	0xe8, 0x1c, 0x7c, 0x16, 0x9b, 0xaf, 0xbe, 0x08, 0x52, 0xc2, 0x5d, 0xfc, 0xfa, 0xf4, 0x00, 0xb7,
	0x1d, 0xb4, 0x28, 0xe9, 0xeb, 0x89, 0xa3, 0x6d, 0xd0, 0x46, 0x4e, 0x5f, 0x4f, 0x2c, 0xfd, 0x21,
	0x40, 0xb2, 0xe1, 0x5d, 0x2f, 0x4d, 0x0f, 0xb0, 0x9f, 0xec, 0x0a, 0xf2, 0xf8, 0xb6, 0xa1, 0x0e,
	0x0b, 0x41, 0x99, 0xe0, 0x9e, 0x0b, 0x62, 0x1c, 0x6f, 0x5b, 0xaa, 0x66, 0x13, 0x38, 0xc8, 0xd2,
	0xcf, 0xa0, 0xa5, 0x8d, 0x72, 0xa4, 0xeb, 0xa6, 0xe9, 0x01, 0x6e, 0x6a, 0xa3, 0x2c, 0x75, 0x06,
	0xed, 0x3b, 0x21, 0xb8, 0xe3, 0x5a, 0x17, 0xde, 0xa8, 0x3d, 0x3d, 0xc0, 0x2d, 0x8b, 0x2c, 0x08,
	0x7f, 0xd9, 0x80, 0x7a, 0x4a, 0xf8, 0xf0, 0x7b, 0xe8, 0x56, 0xbb, 0xed, 0x1b, 0xe8, 0xdc, 0x6f,
	0x79, 0xec, 0x2a, 0xde, 0xbe, 0x9b, 0xed, 0x0b, 0xc0, 0x15, 0xe9, 0xf0, 0x77, 0x0f, 0xde, 0x9d,
	0xe9, 0x1f, 0x29, 0x8d, 0xe6, 0x46, 0xb1, 0xd0, 0xbc, 0x48, 0x22, 0x66, 0x6c, 0xb3, 0xe5, 0xaf,
	0xc5, 0xab, 0x0c, 0x8d, 0xfd, 0x6f, 0x70, 0x04, 0x27, 0xc4, 0xfe, 0x35, 0xd0, 0x21, 0x8d, 0x69,
	0x76, 0xb1, 0x75, 0x77, 0x5d, 0xc7, 0x0e, 0x9f, 0x5b, 0xd8, 0xdd, 0x6c, 0xe5, 0x31, 0x1d, 0xee,
	0x3c, 0xa6, 0xcf, 0xe1, 0xf4, 0x89, 0xd3, 0x68, 0x89, 0xde, 0x87, 0x16, 0xd3, 0x41, 0x4c, 0x69,
	0x76, 0xa4, 0x36, 0x6e, 0x32, 0xa7, 0x9b, 0xfc, 0x5d, 0x83, 0xd6, 0x3c, 0xab, 0x13, 0x8d, 0xa1,
	0xe1, 0x66, 0x31, 0xea, 0x95, 0xa5, 0x17, 0x73, 0x7a, 0x80, 0x76, 0x21, 0x2d, 0xd1, 0x0c, 0x8e,
	0xab, 0x93, 0x0e, 0x0d, 0x4a, 0xd5, 0x1b, 0x63, 0x7c, 0x70, 0xb6, 0x97, 0xd3, 0x12, 0xbd, 0x82,
	0x6e, 0x65, 0x0a, 0xa0, 0x67, 0xa5, 0x7a, 0x77, 0xe2, 0x0d, 0x06, 0xfb, 0x28, 0x2d, 0xd1, 0x0d,
	0xf8, 0x9b, 0x18, 0xa7, 0x4f, 0x3e, 0xdc, 0xc1, 0x7b, 0x4f, 0xc1, 0x5a, 0x22, 0x0c, 0xbd, 0x37,
	0xcc, 0x43, 0xe7, 0xa5, 0xf8, 0xa9, 0x6b, 0x1e, 0x7c, 0xf0, 0x5f, 0xb4, 0x96, 0x2f, 0x2f, 0x7f,
	0xfb, 0x6c, 0x29, 0x38, 0x89, 0x97, 0xe3, 0x2f, 0x27, 0xc6, 0x8c, 0x43, 0xb1, 0xba, 0x72, 0x1f,
	0xc3, 0x50, 0xf0, 0x2b, 0x6d, 0x73, 0x87, 0x54, 0x17, 0x9f, 0xc9, 0xbb, 0xa6, 0xa3, 0xae, 0xff,
	0x0d, 0x00, 0x00, 0xff, 0xff, 0x5a, 0xa5, 0x58, 0xab, 0x4f, 0x07, 0x00, 0x00,
}
