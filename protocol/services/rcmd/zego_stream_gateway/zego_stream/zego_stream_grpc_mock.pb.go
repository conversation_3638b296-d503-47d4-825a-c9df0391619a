// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: zego_stream_gateway/zego_stream.proto

package zego_stream

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

// MockZegoStream_StreamTransferClient is a mock of ZegoStream_StreamTransferClient interface.
type MockZegoStream_StreamTransferClient struct {
	ctrl     *gomock.Controller
	recorder *MockZegoStream_StreamTransferClientMockRecorder
}

// MockZegoStream_StreamTransferClientMockRecorder is the mock recorder for MockZegoStream_StreamTransferClient.
type MockZegoStream_StreamTransferClientMockRecorder struct {
	mock *MockZegoStream_StreamTransferClient
}

// NewMockZegoStream_StreamTransferClient creates a new mock instance.
func NewMockZegoStream_StreamTransferClient(ctrl *gomock.Controller) *MockZegoStream_StreamTransferClient {
	mock := &MockZegoStream_StreamTransferClient{ctrl: ctrl}
	mock.recorder = &MockZegoStream_StreamTransferClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockZegoStream_StreamTransferClient) EXPECT() *MockZegoStream_StreamTransferClientMockRecorder {
	return m.recorder
}

// CloseSend mocks base method.
func (m *MockZegoStream_StreamTransferClient) CloseSend() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseSend")
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseSend indicates an expected call of CloseSend.
func (mr *MockZegoStream_StreamTransferClientMockRecorder) CloseSend() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseSend", reflect.TypeOf((*MockZegoStream_StreamTransferClient)(nil).CloseSend))
}

// Context mocks base method.
func (m *MockZegoStream_StreamTransferClient) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockZegoStream_StreamTransferClientMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockZegoStream_StreamTransferClient)(nil).Context))
}

// Header mocks base method.
func (m *MockZegoStream_StreamTransferClient) Header() (metadata.MD, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Header")
	ret0, _ := ret[0].(metadata.MD)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Header indicates an expected call of Header.
func (mr *MockZegoStream_StreamTransferClientMockRecorder) Header() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Header", reflect.TypeOf((*MockZegoStream_StreamTransferClient)(nil).Header))
}

// Recv mocks base method.
func (m *MockZegoStream_StreamTransferClient) Recv() (*EventWrap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*EventWrap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockZegoStream_StreamTransferClientMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockZegoStream_StreamTransferClient)(nil).Recv))
}

// RecvMsg mocks base method.
func (m *MockZegoStream_StreamTransferClient) RecvMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecvMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockZegoStream_StreamTransferClientMockRecorder) RecvMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockZegoStream_StreamTransferClient)(nil).RecvMsg), arg0)
}

// Send mocks base method.
func (m *MockZegoStream_StreamTransferClient) Send(arg0 *EventWrap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockZegoStream_StreamTransferClientMockRecorder) Send(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockZegoStream_StreamTransferClient)(nil).Send), arg0)
}

// SendMsg mocks base method.
func (m *MockZegoStream_StreamTransferClient) SendMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockZegoStream_StreamTransferClientMockRecorder) SendMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockZegoStream_StreamTransferClient)(nil).SendMsg), arg0)
}

// Trailer mocks base method.
func (m *MockZegoStream_StreamTransferClient) Trailer() metadata.MD {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Trailer")
	ret0, _ := ret[0].(metadata.MD)
	return ret0
}

// Trailer indicates an expected call of Trailer.
func (mr *MockZegoStream_StreamTransferClientMockRecorder) Trailer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trailer", reflect.TypeOf((*MockZegoStream_StreamTransferClient)(nil).Trailer))
}

// MockZegoStream_StreamTransferServer is a mock of ZegoStream_StreamTransferServer interface.
type MockZegoStream_StreamTransferServer struct {
	ctrl     *gomock.Controller
	recorder *MockZegoStream_StreamTransferServerMockRecorder
}

// MockZegoStream_StreamTransferServerMockRecorder is the mock recorder for MockZegoStream_StreamTransferServer.
type MockZegoStream_StreamTransferServerMockRecorder struct {
	mock *MockZegoStream_StreamTransferServer
}

// NewMockZegoStream_StreamTransferServer creates a new mock instance.
func NewMockZegoStream_StreamTransferServer(ctrl *gomock.Controller) *MockZegoStream_StreamTransferServer {
	mock := &MockZegoStream_StreamTransferServer{ctrl: ctrl}
	mock.recorder = &MockZegoStream_StreamTransferServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockZegoStream_StreamTransferServer) EXPECT() *MockZegoStream_StreamTransferServerMockRecorder {
	return m.recorder
}

// Context mocks base method.
func (m *MockZegoStream_StreamTransferServer) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockZegoStream_StreamTransferServerMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockZegoStream_StreamTransferServer)(nil).Context))
}

// Recv mocks base method.
func (m *MockZegoStream_StreamTransferServer) Recv() (*EventWrap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*EventWrap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockZegoStream_StreamTransferServerMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockZegoStream_StreamTransferServer)(nil).Recv))
}

// RecvMsg mocks base method.
func (m *MockZegoStream_StreamTransferServer) RecvMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecvMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockZegoStream_StreamTransferServerMockRecorder) RecvMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockZegoStream_StreamTransferServer)(nil).RecvMsg), arg0)
}

// Send mocks base method.
func (m *MockZegoStream_StreamTransferServer) Send(arg0 *EventWrap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockZegoStream_StreamTransferServerMockRecorder) Send(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockZegoStream_StreamTransferServer)(nil).Send), arg0)
}

// SendHeader mocks base method.
func (m *MockZegoStream_StreamTransferServer) SendHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendHeader indicates an expected call of SendHeader.
func (mr *MockZegoStream_StreamTransferServerMockRecorder) SendHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendHeader", reflect.TypeOf((*MockZegoStream_StreamTransferServer)(nil).SendHeader), arg0)
}

// SendMsg mocks base method.
func (m *MockZegoStream_StreamTransferServer) SendMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockZegoStream_StreamTransferServerMockRecorder) SendMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockZegoStream_StreamTransferServer)(nil).SendMsg), arg0)
}

// SetHeader mocks base method.
func (m *MockZegoStream_StreamTransferServer) SetHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHeader indicates an expected call of SetHeader.
func (mr *MockZegoStream_StreamTransferServerMockRecorder) SetHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeader", reflect.TypeOf((*MockZegoStream_StreamTransferServer)(nil).SetHeader), arg0)
}

// SetTrailer mocks base method.
func (m *MockZegoStream_StreamTransferServer) SetTrailer(arg0 metadata.MD) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTrailer", arg0)
}

// SetTrailer indicates an expected call of SetTrailer.
func (mr *MockZegoStream_StreamTransferServerMockRecorder) SetTrailer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTrailer", reflect.TypeOf((*MockZegoStream_StreamTransferServer)(nil).SetTrailer), arg0)
}

// MockZegoStreamClient is a mock of ZegoStreamClient interface.
type MockZegoStreamClient struct {
	ctrl     *gomock.Controller
	recorder *MockZegoStreamClientMockRecorder
}

// MockZegoStreamClientMockRecorder is the mock recorder for MockZegoStreamClient.
type MockZegoStreamClientMockRecorder struct {
	mock *MockZegoStreamClient
}

// NewMockZegoStreamClient creates a new mock instance.
func NewMockZegoStreamClient(ctrl *gomock.Controller) *MockZegoStreamClient {
	mock := &MockZegoStreamClient{ctrl: ctrl}
	mock.recorder = &MockZegoStreamClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockZegoStreamClient) EXPECT() *MockZegoStreamClientMockRecorder {
	return m.recorder
}

// StreamTransfer mocks base method.
func (m *MockZegoStreamClient) StreamTransfer(ctx context.Context, opts ...grpc.CallOption) (ZegoStream_StreamTransferClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StreamTransfer", varargs...)
	ret0, _ := ret[0].(ZegoStream_StreamTransferClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StreamTransfer indicates an expected call of StreamTransfer.
func (mr *MockZegoStreamClientMockRecorder) StreamTransfer(ctx interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamTransfer", reflect.TypeOf((*MockZegoStreamClient)(nil).StreamTransfer), varargs...)
}

// MockZegoStreamServer is a mock of ZegoStreamServer interface.
type MockZegoStreamServer struct {
	ctrl     *gomock.Controller
	recorder *MockZegoStreamServerMockRecorder
}

// MockZegoStreamServerMockRecorder is the mock recorder for MockZegoStreamServer.
type MockZegoStreamServerMockRecorder struct {
	mock *MockZegoStreamServer
}

// NewMockZegoStreamServer creates a new mock instance.
func NewMockZegoStreamServer(ctrl *gomock.Controller) *MockZegoStreamServer {
	mock := &MockZegoStreamServer{ctrl: ctrl}
	mock.recorder = &MockZegoStreamServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockZegoStreamServer) EXPECT() *MockZegoStreamServerMockRecorder {
	return m.recorder
}

// StreamTransfer mocks base method.
func (m *MockZegoStreamServer) StreamTransfer(server ZegoStream_StreamTransferServer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StreamTransfer", server)
	ret0, _ := ret[0].(error)
	return ret0
}

// StreamTransfer indicates an expected call of StreamTransfer.
func (mr *MockZegoStreamServerMockRecorder) StreamTransfer(server interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamTransfer", reflect.TypeOf((*MockZegoStreamServer)(nil).StreamTransfer), server)
}
