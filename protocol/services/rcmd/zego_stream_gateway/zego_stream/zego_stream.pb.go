// Code generated by protoc-gen-go. DO NOT EDIT.
// source: zego_stream_gateway/zego_stream.proto

package zego_stream // import "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EventType int32

const (
	EventType_EventTypeNone EventType = 0
	// common: 1-10
	EventType_ZegoError   EventType = 1
	EventType_ServerError EventType = 2
	EventType_ClientError EventType = 3
	EventType_Audio       EventType = 4
	// client action: 11-99
	EventType_Exit            EventType = 10
	EventType_Login           EventType = 11
	EventType_StartPullStream EventType = 12
	EventType_StopPullStream  EventType = 13
	EventType_SendTextMessage EventType = 14
	// zego callback: 100-199
	EventType_LoginResult     EventType = 101
	EventType_StreamUpdate    EventType = 102
	EventType_RoomStateUpdate EventType = 103
	// business: 200-299
	EventType_Heartbeat    EventType = 200
	EventType_HeartbeatAck EventType = 201
)

var EventType_name = map[int32]string{
	0:   "EventTypeNone",
	1:   "ZegoError",
	2:   "ServerError",
	3:   "ClientError",
	4:   "Audio",
	10:  "Exit",
	11:  "Login",
	12:  "StartPullStream",
	13:  "StopPullStream",
	14:  "SendTextMessage",
	101: "LoginResult",
	102: "StreamUpdate",
	103: "RoomStateUpdate",
	200: "Heartbeat",
	201: "HeartbeatAck",
}
var EventType_value = map[string]int32{
	"EventTypeNone":   0,
	"ZegoError":       1,
	"ServerError":     2,
	"ClientError":     3,
	"Audio":           4,
	"Exit":            10,
	"Login":           11,
	"StartPullStream": 12,
	"StopPullStream":  13,
	"SendTextMessage": 14,
	"LoginResult":     101,
	"StreamUpdate":    102,
	"RoomStateUpdate": 103,
	"Heartbeat":       200,
	"HeartbeatAck":    201,
}

func (x EventType) String() string {
	return proto.EnumName(EventType_name, int32(x))
}
func (EventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_zego_stream_0e691b4e2e7e949d, []int{0}
}

type ZegoErrCode int32

const (
	ZegoErrCode_ZegoErrCommonSuccess ZegoErrCode = 0
	// Description: The user was kicked out of the room.
	// <br>Cause: Possibly because the same user ID is logged in on another device.
	// <br>Solutions: Use a unique user ID.
	ZegoErrCode_ZegoErrRoomKickOut ZegoErrCode = 1002050
)

var ZegoErrCode_name = map[int32]string{
	0:       "ZegoErrCommonSuccess",
	1002050: "ZegoErrRoomKickOut",
}
var ZegoErrCode_value = map[string]int32{
	"ZegoErrCommonSuccess": 0,
	"ZegoErrRoomKickOut":   1002050,
}

func (x ZegoErrCode) String() string {
	return proto.EnumName(ZegoErrCode_name, int32(x))
}
func (ZegoErrCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_zego_stream_0e691b4e2e7e949d, []int{1}
}

type EventWrap struct {
	Event                EventType `protobuf:"varint,1,opt,name=event,proto3,enum=zego_stream.EventType" json:"event,omitempty"`
	Json                 string    `protobuf:"bytes,2,opt,name=json,proto3" json:"json,omitempty"`
	Data                 []byte    `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *EventWrap) Reset()         { *m = EventWrap{} }
func (m *EventWrap) String() string { return proto.CompactTextString(m) }
func (*EventWrap) ProtoMessage()    {}
func (*EventWrap) Descriptor() ([]byte, []int) {
	return fileDescriptor_zego_stream_0e691b4e2e7e949d, []int{0}
}
func (m *EventWrap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EventWrap.Unmarshal(m, b)
}
func (m *EventWrap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EventWrap.Marshal(b, m, deterministic)
}
func (dst *EventWrap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EventWrap.Merge(dst, src)
}
func (m *EventWrap) XXX_Size() int {
	return xxx_messageInfo_EventWrap.Size(m)
}
func (m *EventWrap) XXX_DiscardUnknown() {
	xxx_messageInfo_EventWrap.DiscardUnknown(m)
}

var xxx_messageInfo_EventWrap proto.InternalMessageInfo

func (m *EventWrap) GetEvent() EventType {
	if m != nil {
		return m.Event
	}
	return EventType_EventTypeNone
}

func (m *EventWrap) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

func (m *EventWrap) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func init() {
	proto.RegisterType((*EventWrap)(nil), "zego_stream.EventWrap")
	proto.RegisterEnum("zego_stream.EventType", EventType_name, EventType_value)
	proto.RegisterEnum("zego_stream.ZegoErrCode", ZegoErrCode_name, ZegoErrCode_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ZegoStreamClient is the client API for ZegoStream service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ZegoStreamClient interface {
	StreamTransfer(ctx context.Context, opts ...grpc.CallOption) (ZegoStream_StreamTransferClient, error)
}

type zegoStreamClient struct {
	cc *grpc.ClientConn
}

func NewZegoStreamClient(cc *grpc.ClientConn) ZegoStreamClient {
	return &zegoStreamClient{cc}
}

func (c *zegoStreamClient) StreamTransfer(ctx context.Context, opts ...grpc.CallOption) (ZegoStream_StreamTransferClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ZegoStream_serviceDesc.Streams[0], "/zego_stream.ZegoStream/StreamTransfer", opts...)
	if err != nil {
		return nil, err
	}
	x := &zegoStreamStreamTransferClient{stream}
	return x, nil
}

type ZegoStream_StreamTransferClient interface {
	Send(*EventWrap) error
	Recv() (*EventWrap, error)
	grpc.ClientStream
}

type zegoStreamStreamTransferClient struct {
	grpc.ClientStream
}

func (x *zegoStreamStreamTransferClient) Send(m *EventWrap) error {
	return x.ClientStream.SendMsg(m)
}

func (x *zegoStreamStreamTransferClient) Recv() (*EventWrap, error) {
	m := new(EventWrap)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ZegoStreamServer is the server API for ZegoStream service.
type ZegoStreamServer interface {
	StreamTransfer(ZegoStream_StreamTransferServer) error
}

func RegisterZegoStreamServer(s *grpc.Server, srv ZegoStreamServer) {
	s.RegisterService(&_ZegoStream_serviceDesc, srv)
}

func _ZegoStream_StreamTransfer_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ZegoStreamServer).StreamTransfer(&zegoStreamStreamTransferServer{stream})
}

type ZegoStream_StreamTransferServer interface {
	Send(*EventWrap) error
	Recv() (*EventWrap, error)
	grpc.ServerStream
}

type zegoStreamStreamTransferServer struct {
	grpc.ServerStream
}

func (x *zegoStreamStreamTransferServer) Send(m *EventWrap) error {
	return x.ServerStream.SendMsg(m)
}

func (x *zegoStreamStreamTransferServer) Recv() (*EventWrap, error) {
	m := new(EventWrap)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _ZegoStream_serviceDesc = grpc.ServiceDesc{
	ServiceName: "zego_stream.ZegoStream",
	HandlerType: (*ZegoStreamServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamTransfer",
			Handler:       _ZegoStream_StreamTransfer_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "zego_stream_gateway/zego_stream.proto",
}

func init() {
	proto.RegisterFile("zego_stream_gateway/zego_stream.proto", fileDescriptor_zego_stream_0e691b4e2e7e949d)
}

var fileDescriptor_zego_stream_0e691b4e2e7e949d = []byte{
	// 424 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x92, 0xcd, 0x8a, 0x13, 0x41,
	0x10, 0xc7, 0xb7, 0xb3, 0x59, 0x71, 0x2a, 0x1f, 0xdb, 0x5b, 0x8a, 0x0c, 0x9e, 0x82, 0x20, 0x84,
	0x45, 0x32, 0x12, 0xf1, 0xe8, 0x21, 0xae, 0x91, 0x05, 0x3f, 0x99, 0x89, 0x08, 0x7b, 0x59, 0x7a,
	0x67, 0x6a, 0x87, 0x71, 0x67, 0xba, 0x87, 0xee, 0x9a, 0xb8, 0xf1, 0x09, 0x7c, 0x00, 0x9f, 0xc8,
	0x93, 0xbe, 0x95, 0xf4, 0x4c, 0x08, 0x39, 0x28, 0xde, 0xea, 0xff, 0xeb, 0xfa, 0x77, 0x55, 0x51,
	0x05, 0x8f, 0xbf, 0x51, 0x6e, 0x2e, 0x1d, 0x5b, 0x52, 0xd5, 0x65, 0xae, 0x98, 0xbe, 0xaa, 0x4d,
	0xb4, 0xc7, 0x66, 0xb5, 0x35, 0x6c, 0x70, 0xb0, 0x87, 0x1e, 0x29, 0x08, 0x96, 0x6b, 0xd2, 0xfc,
	0xd9, 0xaa, 0x1a, 0x9f, 0xc0, 0x11, 0x79, 0x11, 0x8a, 0x89, 0x98, 0x8e, 0xe7, 0x0f, 0x66, 0xfb,
	0xe6, 0x36, 0x6d, 0xb5, 0xa9, 0x29, 0xee, 0x92, 0x10, 0xa1, 0xff, 0xc5, 0x19, 0x1d, 0xf6, 0x26,
	0x62, 0x1a, 0xc4, 0x6d, 0xec, 0x59, 0xa6, 0x58, 0x85, 0x87, 0x13, 0x31, 0x1d, 0xc6, 0x6d, 0x7c,
	0xfa, 0xbd, 0xb7, 0xad, 0xe1, 0xcd, 0x78, 0x02, 0xa3, 0x9d, 0x78, 0x6f, 0x34, 0xc9, 0x03, 0x1c,
	0x41, 0x70, 0x41, 0xb9, 0x59, 0x5a, 0x6b, 0xac, 0x14, 0x78, 0x0c, 0x83, 0x84, 0xec, 0x9a, 0x6c,
	0x07, 0x7a, 0x1e, 0x9c, 0x95, 0x05, 0x69, 0xee, 0xc0, 0x21, 0x06, 0x70, 0xb4, 0x68, 0xb2, 0xc2,
	0xc8, 0x3e, 0xde, 0x85, 0xfe, 0xf2, 0xb6, 0x60, 0x09, 0x1e, 0xbe, 0x35, 0x79, 0xa1, 0xe5, 0x00,
	0xef, 0xc1, 0x71, 0xc2, 0xca, 0xf2, 0xc7, 0xa6, 0x2c, 0x93, 0xb6, 0x7b, 0x39, 0x44, 0x84, 0x71,
	0xc2, 0xa6, 0xde, 0x63, 0xa3, 0x36, 0x91, 0x74, 0xb6, 0xa2, 0x5b, 0x7e, 0x47, 0xce, 0xa9, 0x9c,
	0xe4, 0xd8, 0x97, 0x6b, 0x3f, 0x8a, 0xc9, 0x35, 0x25, 0x4b, 0x42, 0x09, 0xc3, 0xce, 0xf1, 0xa9,
	0xce, 0x14, 0x93, 0xbc, 0xf6, 0xbe, 0xd8, 0x98, 0x2a, 0x61, 0xc5, 0xb4, 0x85, 0x39, 0x8e, 0x21,
	0x38, 0x27, 0x65, 0xf9, 0x8a, 0x14, 0xcb, 0x5f, 0x02, 0x4f, 0x60, 0xb8, 0xd3, 0x8b, 0xf4, 0x46,
	0xfe, 0x16, 0xa7, 0x0b, 0x18, 0x6c, 0x27, 0x3d, 0x33, 0x19, 0x61, 0x08, 0xf7, 0x77, 0xb2, 0xaa,
	0x8c, 0x4e, 0x9a, 0x34, 0x25, 0xe7, 0xe4, 0x01, 0x86, 0x80, 0xdb, 0x17, 0x5f, 0xe7, 0x4d, 0x91,
	0xde, 0x7c, 0x68, 0x58, 0xfe, 0xfc, 0xf1, 0x62, 0x1e, 0x03, 0xf8, 0x97, 0xae, 0x21, 0x7c, 0xe5,
	0x87, 0xf2, 0xd1, 0xca, 0x2a, 0xed, 0xae, 0xc9, 0xe2, 0x5f, 0x96, 0xe6, 0x77, 0xfb, 0xf0, 0x1f,
	0x7c, 0x2a, 0x9e, 0x8a, 0x97, 0xe7, 0x17, 0xaf, 0x73, 0x53, 0x2a, 0x9d, 0xcf, 0x9e, 0xcf, 0x99,
	0x67, 0xa9, 0xa9, 0xa2, 0xf6, 0x54, 0x52, 0x53, 0x46, 0x8e, 0xec, 0xba, 0x48, 0xc9, 0x45, 0x36,
	0xad, 0xb2, 0xe8, 0x3f, 0x17, 0x76, 0x75, 0xa7, 0xf5, 0x3d, 0xfb, 0x13, 0x00, 0x00, 0xff, 0xff,
	0x19, 0xcb, 0x14, 0x71, 0x8b, 0x02, 0x00, 0x00,
}
