// Code generated by protoc-gen-go. DO NOT EDIT.
// source: zego_stream_gateway/zego_stream.proto

package zego_stream // import "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EventType int32

const (
	EventType_EventTypeNone EventType = 0
	// common: 1-10
	EventType_ZegoError   EventType = 1
	EventType_ServerError EventType = 2
	EventType_ClientError EventType = 3
	EventType_Audio       EventType = 4
	// client action: 11-99
	EventType_Exit            EventType = 10
	EventType_Login           EventType = 11
	EventType_StartPullStream EventType = 12
	EventType_StopPullStream  EventType = 13
	EventType_SendTextMessage EventType = 14
	// zego callback: 100-199
	EventType_LoginResult  EventType = 101
	EventType_StreamUpdate EventType = 102
)

var EventType_name = map[int32]string{
	0:   "EventTypeNone",
	1:   "ZegoError",
	2:   "ServerError",
	3:   "ClientError",
	4:   "Audio",
	10:  "Exit",
	11:  "Login",
	12:  "StartPullStream",
	13:  "StopPullStream",
	14:  "SendTextMessage",
	101: "LoginResult",
	102: "StreamUpdate",
}
var EventType_value = map[string]int32{
	"EventTypeNone":   0,
	"ZegoError":       1,
	"ServerError":     2,
	"ClientError":     3,
	"Audio":           4,
	"Exit":            10,
	"Login":           11,
	"StartPullStream": 12,
	"StopPullStream":  13,
	"SendTextMessage": 14,
	"LoginResult":     101,
	"StreamUpdate":    102,
}

func (x EventType) String() string {
	return proto.EnumName(EventType_name, int32(x))
}
func (EventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_zego_stream_e470309221559a5a, []int{0}
}

type EventWrap struct {
	Event                EventType `protobuf:"varint,1,opt,name=event,proto3,enum=zego_stream.EventType" json:"event,omitempty"`
	Json                 string    `protobuf:"bytes,2,opt,name=json,proto3" json:"json,omitempty"`
	Data                 []byte    `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	PodName              string    `protobuf:"bytes,4,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *EventWrap) Reset()         { *m = EventWrap{} }
func (m *EventWrap) String() string { return proto.CompactTextString(m) }
func (*EventWrap) ProtoMessage()    {}
func (*EventWrap) Descriptor() ([]byte, []int) {
	return fileDescriptor_zego_stream_e470309221559a5a, []int{0}
}
func (m *EventWrap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EventWrap.Unmarshal(m, b)
}
func (m *EventWrap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EventWrap.Marshal(b, m, deterministic)
}
func (dst *EventWrap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EventWrap.Merge(dst, src)
}
func (m *EventWrap) XXX_Size() int {
	return xxx_messageInfo_EventWrap.Size(m)
}
func (m *EventWrap) XXX_DiscardUnknown() {
	xxx_messageInfo_EventWrap.DiscardUnknown(m)
}

var xxx_messageInfo_EventWrap proto.InternalMessageInfo

func (m *EventWrap) GetEvent() EventType {
	if m != nil {
		return m.Event
	}
	return EventType_EventTypeNone
}

func (m *EventWrap) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

func (m *EventWrap) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *EventWrap) GetPodName() string {
	if m != nil {
		return m.PodName
	}
	return ""
}

func init() {
	proto.RegisterType((*EventWrap)(nil), "zego_stream.EventWrap")
	proto.RegisterEnum("zego_stream.EventType", EventType_name, EventType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ZegoStreamClient is the client API for ZegoStream service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ZegoStreamClient interface {
	StreamTransfer(ctx context.Context, opts ...grpc.CallOption) (ZegoStream_StreamTransferClient, error)
}

type zegoStreamClient struct {
	cc *grpc.ClientConn
}

func NewZegoStreamClient(cc *grpc.ClientConn) ZegoStreamClient {
	return &zegoStreamClient{cc}
}

func (c *zegoStreamClient) StreamTransfer(ctx context.Context, opts ...grpc.CallOption) (ZegoStream_StreamTransferClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ZegoStream_serviceDesc.Streams[0], "/zego_stream.ZegoStream/StreamTransfer", opts...)
	if err != nil {
		return nil, err
	}
	x := &zegoStreamStreamTransferClient{stream}
	return x, nil
}

type ZegoStream_StreamTransferClient interface {
	Send(*EventWrap) error
	Recv() (*EventWrap, error)
	grpc.ClientStream
}

type zegoStreamStreamTransferClient struct {
	grpc.ClientStream
}

func (x *zegoStreamStreamTransferClient) Send(m *EventWrap) error {
	return x.ClientStream.SendMsg(m)
}

func (x *zegoStreamStreamTransferClient) Recv() (*EventWrap, error) {
	m := new(EventWrap)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ZegoStreamServer is the server API for ZegoStream service.
type ZegoStreamServer interface {
	StreamTransfer(ZegoStream_StreamTransferServer) error
}

func RegisterZegoStreamServer(s *grpc.Server, srv ZegoStreamServer) {
	s.RegisterService(&_ZegoStream_serviceDesc, srv)
}

func _ZegoStream_StreamTransfer_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ZegoStreamServer).StreamTransfer(&zegoStreamStreamTransferServer{stream})
}

type ZegoStream_StreamTransferServer interface {
	Send(*EventWrap) error
	Recv() (*EventWrap, error)
	grpc.ServerStream
}

type zegoStreamStreamTransferServer struct {
	grpc.ServerStream
}

func (x *zegoStreamStreamTransferServer) Send(m *EventWrap) error {
	return x.ServerStream.SendMsg(m)
}

func (x *zegoStreamStreamTransferServer) Recv() (*EventWrap, error) {
	m := new(EventWrap)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _ZegoStream_serviceDesc = grpc.ServiceDesc{
	ServiceName: "zego_stream.ZegoStream",
	HandlerType: (*ZegoStreamServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamTransfer",
			Handler:       _ZegoStream_StreamTransfer_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "zego_stream_gateway/zego_stream.proto",
}

func init() {
	proto.RegisterFile("zego_stream_gateway/zego_stream.proto", fileDescriptor_zego_stream_e470309221559a5a)
}

var fileDescriptor_zego_stream_e470309221559a5a = []byte{
	// 365 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x92, 0x5d, 0xeb, 0xd3, 0x30,
	0x14, 0xc6, 0xcd, 0x7f, 0x9d, 0xae, 0x67, 0x6f, 0xf1, 0x08, 0x52, 0xbd, 0x1a, 0x82, 0x50, 0x44,
	0x5a, 0x99, 0xf8, 0x01, 0x7c, 0x99, 0x78, 0xa1, 0x43, 0xda, 0x89, 0xb0, 0x9b, 0x11, 0xdb, 0xb3,
	0x52, 0x69, 0x93, 0x92, 0x64, 0x73, 0x13, 0x3f, 0xa4, 0x1f, 0x49, 0x92, 0x8a, 0xf4, 0x42, 0xf1,
	0xee, 0x9c, 0x1f, 0xcf, 0xf3, 0x9c, 0xe4, 0x24, 0xf0, 0xf8, 0x3b, 0x55, 0xea, 0x60, 0xac, 0x26,
	0xd1, 0x1e, 0x2a, 0x61, 0xe9, 0x9b, 0xb8, 0xa6, 0x03, 0x96, 0x74, 0x5a, 0x59, 0x85, 0xd3, 0x01,
	0x7a, 0xf4, 0x03, 0xc2, 0xcd, 0x99, 0xa4, 0xfd, 0xac, 0x45, 0x87, 0x4f, 0x61, 0x4c, 0xae, 0x89,
	0xd8, 0x8a, 0xc5, 0x8b, 0xf5, 0xfd, 0x64, 0x68, 0xf6, 0xb2, 0xdd, 0xb5, 0xa3, 0xac, 0x17, 0x21,
	0x42, 0xf0, 0xd5, 0x28, 0x19, 0xdd, 0xac, 0x58, 0x1c, 0x66, 0xbe, 0x76, 0xac, 0x14, 0x56, 0x44,
	0xa3, 0x15, 0x8b, 0x67, 0x99, 0xaf, 0xf1, 0x01, 0x4c, 0x3a, 0x55, 0x1e, 0xa4, 0x68, 0x29, 0x0a,
	0xbc, 0xf6, 0x4e, 0xa7, 0xca, 0xad, 0x68, 0xe9, 0xc9, 0x4f, 0xf6, 0x7b, 0xbc, 0xcb, 0xc5, 0xbb,
	0x30, 0xff, 0xd3, 0x6c, 0x95, 0x24, 0x7e, 0x0b, 0xe7, 0x10, 0xee, 0xa9, 0x52, 0x1b, 0xad, 0x95,
	0xe6, 0x0c, 0x97, 0x30, 0xcd, 0x49, 0x9f, 0x49, 0xf7, 0xe0, 0xc6, 0x81, 0xd7, 0x4d, 0x4d, 0xd2,
	0xf6, 0x60, 0x84, 0x21, 0x8c, 0x5f, 0x9e, 0xca, 0x5a, 0xf1, 0x00, 0x27, 0x10, 0x6c, 0x2e, 0xb5,
	0xe5, 0xe0, 0xe0, 0x7b, 0x55, 0xd5, 0x92, 0x4f, 0xf1, 0x1e, 0x2c, 0x73, 0x2b, 0xb4, 0xfd, 0x78,
	0x6a, 0x9a, 0xdc, 0x5f, 0x8c, 0xcf, 0x10, 0x61, 0x91, 0x5b, 0xd5, 0x0d, 0xd8, 0xdc, 0x0b, 0x49,
	0x96, 0x3b, 0xba, 0xd8, 0x0f, 0x64, 0x8c, 0xa8, 0x88, 0x2f, 0xdc, 0x38, 0x1f, 0x94, 0x91, 0x39,
	0x35, 0x96, 0x13, 0x72, 0x98, 0xf5, 0x8e, 0x4f, 0x5d, 0x29, 0x2c, 0xf1, 0xe3, 0x3a, 0x03, 0x70,
	0x27, 0xee, 0x29, 0xbe, 0x71, 0xc9, 0xae, 0xda, 0x69, 0x21, 0xcd, 0x91, 0x34, 0xfe, 0x65, 0xa9,
	0x6e, 0xf7, 0x0f, 0xff, 0xc1, 0x63, 0xf6, 0x8c, 0xbd, 0x7a, 0xb7, 0x7f, 0x5b, 0xa9, 0x46, 0xc8,
	0x2a, 0x79, 0xb1, 0xb6, 0x36, 0x29, 0x54, 0x9b, 0xfa, 0xa7, 0x2c, 0x54, 0x93, 0x1a, 0xd2, 0xe7,
	0xba, 0x20, 0x93, 0xea, 0xa2, 0x2d, 0xd3, 0xff, 0xfc, 0x80, 0x2f, 0xb7, 0xbd, 0xef, 0xf9, 0xaf,
	0x00, 0x00, 0x00, 0xff, 0xff, 0xf4, 0x4c, 0xde, 0xd1, 0x2b, 0x02, 0x00, 0x00,
}
