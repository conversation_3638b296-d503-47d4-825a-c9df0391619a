// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd-ai-partner/rcmd_ai_partner.proto

package rcmd_ai_partner // import "golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import partner_common "golang.52tt.com/protocol/services/rcmd/partner_common"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChattingStatus int32

const (
	ChattingStatus_Chatting ChattingStatus = 0
	ChattingStatus_Silence  ChattingStatus = 1
)

var ChattingStatus_name = map[int32]string{
	0: "Chatting",
	1: "Silence",
}
var ChattingStatus_value = map[string]int32{
	"Chatting": 0,
	"Silence":  1,
}

func (x ChattingStatus) String() string {
	return proto.EnumName(ChattingStatus_name, int32(x))
}
func (ChattingStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{0}
}

type AIRoleType int32

const (
	AIRoleType_AIRoleTypeDefault AIRoleType = 0
	AIRoleType_AIRoleTypeGame    AIRoleType = 1
	AIRoleType_AIRoleTypePet     AIRoleType = 2
	AIRoleType_AIRoleTypeGroup   AIRoleType = 3
)

var AIRoleType_name = map[int32]string{
	0: "AIRoleTypeDefault",
	1: "AIRoleTypeGame",
	2: "AIRoleTypePet",
	3: "AIRoleTypeGroup",
}
var AIRoleType_value = map[string]int32{
	"AIRoleTypeDefault": 0,
	"AIRoleTypeGame":    1,
	"AIRoleTypePet":     2,
	"AIRoleTypeGroup":   3,
}

func (x AIRoleType) String() string {
	return proto.EnumName(AIRoleType_name, int32(x))
}
func (AIRoleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{1}
}

type TriggerSource int32

const (
	TriggerSource_Invalid      TriggerSource = 0
	TriggerSource_ChattingPage TriggerSource = 1
	TriggerSource_LoginPage    TriggerSource = 2
	TriggerSource_PartnerWeb   TriggerSource = 3
	TriggerSource_Internal     TriggerSource = 1000
)

var TriggerSource_name = map[int32]string{
	0:    "Invalid",
	1:    "ChattingPage",
	2:    "LoginPage",
	3:    "PartnerWeb",
	1000: "Internal",
}
var TriggerSource_value = map[string]int32{
	"Invalid":      0,
	"ChattingPage": 1,
	"LoginPage":    2,
	"PartnerWeb":   3,
	"Internal":     1000,
}

func (x TriggerSource) String() string {
	return proto.EnumName(TriggerSource_name, int32(x))
}
func (TriggerSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{2}
}

type AIGroupType int32

const (
	AIGroupType_AIGroupType_Unknown         AIGroupType = 0
	AIGroupType_AIGroupType_SingleUser      AIGroupType = 1
	AIGroupType_AIGroupType_MultiUser       AIGroupType = 2
	AIGroupType_AIGroupType_MultiUserScript AIGroupType = 3
)

var AIGroupType_name = map[int32]string{
	0: "AIGroupType_Unknown",
	1: "AIGroupType_SingleUser",
	2: "AIGroupType_MultiUser",
	3: "AIGroupType_MultiUserScript",
}
var AIGroupType_value = map[string]int32{
	"AIGroupType_Unknown":         0,
	"AIGroupType_SingleUser":      1,
	"AIGroupType_MultiUser":       2,
	"AIGroupType_MultiUserScript": 3,
}

func (x AIGroupType) String() string {
	return proto.EnumName(AIGroupType_name, int32(x))
}
func (AIGroupType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{3}
}

type ReplyTextFormatReq_FormatType int32

const (
	ReplyTextFormatReq_FormatType_Default         ReplyTextFormatReq_FormatType = 0
	ReplyTextFormatReq_FormatType_TextProcDefault ReplyTextFormatReq_FormatType = 1
	ReplyTextFormatReq_FormatType_RemoveBracket   ReplyTextFormatReq_FormatType = 2
	ReplyTextFormatReq_FormatType_TextProcV1      ReplyTextFormatReq_FormatType = 3
	ReplyTextFormatReq_FormatType_LongReplyDetect ReplyTextFormatReq_FormatType = 4
)

var ReplyTextFormatReq_FormatType_name = map[int32]string{
	0: "FormatType_Default",
	1: "FormatType_TextProcDefault",
	2: "FormatType_RemoveBracket",
	3: "FormatType_TextProcV1",
	4: "FormatType_LongReplyDetect",
}
var ReplyTextFormatReq_FormatType_value = map[string]int32{
	"FormatType_Default":         0,
	"FormatType_TextProcDefault": 1,
	"FormatType_RemoveBracket":   2,
	"FormatType_TextProcV1":      3,
	"FormatType_LongReplyDetect": 4,
}

func (x ReplyTextFormatReq_FormatType) String() string {
	return proto.EnumName(ReplyTextFormatReq_FormatType_name, int32(x))
}
func (ReplyTextFormatReq_FormatType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{58, 0}
}

type SetAIPartnerInfoReq_Relationship int32

const (
	SetAIPartnerInfoReq_RelationshipUnknown SetAIPartnerInfoReq_Relationship = 0
	// 朋友
	SetAIPartnerInfoReq_RelationshipFriend SetAIPartnerInfoReq_Relationship = 1
	// 恋人
	SetAIPartnerInfoReq_RelationshipLover SetAIPartnerInfoReq_Relationship = 2
)

var SetAIPartnerInfoReq_Relationship_name = map[int32]string{
	0: "RelationshipUnknown",
	1: "RelationshipFriend",
	2: "RelationshipLover",
}
var SetAIPartnerInfoReq_Relationship_value = map[string]int32{
	"RelationshipUnknown": 0,
	"RelationshipFriend":  1,
	"RelationshipLover":   2,
}

func (x SetAIPartnerInfoReq_Relationship) String() string {
	return proto.EnumName(SetAIPartnerInfoReq_Relationship_name, int32(x))
}
func (SetAIPartnerInfoReq_Relationship) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{64, 0}
}

type AIRole_Type int32

const (
	AIRole_Type_Default  AIRole_Type = 0
	AIRole_Type_NoChoose AIRole_Type = 1
)

var AIRole_Type_name = map[int32]string{
	0: "Type_Default",
	1: "Type_NoChoose",
}
var AIRole_Type_value = map[string]int32{
	"Type_Default":  0,
	"Type_NoChoose": 1,
}

func (x AIRole_Type) String() string {
	return proto.EnumName(AIRole_Type_name, int32(x))
}
func (AIRole_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{65, 0}
}

type ReceiveMsgFromUserReq_MsgType int32

const (
	ReceiveMsgFromUserReq_MsgType_Unknown ReceiveMsgFromUserReq_MsgType = 0
	// 文本
	ReceiveMsgFromUserReq_MsgType_Text ReceiveMsgFromUserReq_MsgType = 1
	// 表情
	ReceiveMsgFromUserReq_MsgType_Emoticon       ReceiveMsgFromUserReq_MsgType = 3
	ReceiveMsgFromUserReq_MsgType_ImMsgAIPartner ReceiveMsgFromUserReq_MsgType = 6
)

var ReceiveMsgFromUserReq_MsgType_name = map[int32]string{
	0: "MsgType_Unknown",
	1: "MsgType_Text",
	3: "MsgType_Emoticon",
	6: "MsgType_ImMsgAIPartner",
}
var ReceiveMsgFromUserReq_MsgType_value = map[string]int32{
	"MsgType_Unknown":        0,
	"MsgType_Text":           1,
	"MsgType_Emoticon":       3,
	"MsgType_ImMsgAIPartner": 6,
}

func (x ReceiveMsgFromUserReq_MsgType) String() string {
	return proto.EnumName(ReceiveMsgFromUserReq_MsgType_name, int32(x))
}
func (ReceiveMsgFromUserReq_MsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{67, 0}
}

type ReceiveGroupMsgFromUserReq_MsgType int32

const (
	ReceiveGroupMsgFromUserReq_MsgType_Unknown ReceiveGroupMsgFromUserReq_MsgType = 0
	// 文本
	ReceiveGroupMsgFromUserReq_MsgType_Text ReceiveGroupMsgFromUserReq_MsgType = 1
	// 表情
	ReceiveGroupMsgFromUserReq_MsgType_Emoticon       ReceiveGroupMsgFromUserReq_MsgType = 3
	ReceiveGroupMsgFromUserReq_MsgType_ImMsgAIPartner ReceiveGroupMsgFromUserReq_MsgType = 6
)

var ReceiveGroupMsgFromUserReq_MsgType_name = map[int32]string{
	0: "MsgType_Unknown",
	1: "MsgType_Text",
	3: "MsgType_Emoticon",
	6: "MsgType_ImMsgAIPartner",
}
var ReceiveGroupMsgFromUserReq_MsgType_value = map[string]int32{
	"MsgType_Unknown":        0,
	"MsgType_Text":           1,
	"MsgType_Emoticon":       3,
	"MsgType_ImMsgAIPartner": 6,
}

func (x ReceiveGroupMsgFromUserReq_MsgType) String() string {
	return proto.EnumName(ReceiveGroupMsgFromUserReq_MsgType_name, int32(x))
}
func (ReceiveGroupMsgFromUserReq_MsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{82, 0}
}

type ReceiveSpecialMsgFromUserReq_SpecialMsgType int32

const (
	ReceiveSpecialMsgFromUserReq_Unknown  ReceiveSpecialMsgFromUserReq_SpecialMsgType = 0
	ReceiveSpecialMsgFromUserReq_GiveGift ReceiveSpecialMsgFromUserReq_SpecialMsgType = 1
)

var ReceiveSpecialMsgFromUserReq_SpecialMsgType_name = map[int32]string{
	0: "Unknown",
	1: "GiveGift",
}
var ReceiveSpecialMsgFromUserReq_SpecialMsgType_value = map[string]int32{
	"Unknown":  0,
	"GiveGift": 1,
}

func (x ReceiveSpecialMsgFromUserReq_SpecialMsgType) String() string {
	return proto.EnumName(ReceiveSpecialMsgFromUserReq_SpecialMsgType_name, int32(x))
}
func (ReceiveSpecialMsgFromUserReq_SpecialMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{85, 0}
}

type RewardSentenceAddReq_SentenceType int32

const (
	RewardSentenceAddReq_SentenceType_Unknown RewardSentenceAddReq_SentenceType = 0
	RewardSentenceAddReq_SentenceType_Extra   RewardSentenceAddReq_SentenceType = 1
	RewardSentenceAddReq_SentenceType_Curday  RewardSentenceAddReq_SentenceType = 2
)

var RewardSentenceAddReq_SentenceType_name = map[int32]string{
	0: "SentenceType_Unknown",
	1: "SentenceType_Extra",
	2: "SentenceType_Curday",
}
var RewardSentenceAddReq_SentenceType_value = map[string]int32{
	"SentenceType_Unknown": 0,
	"SentenceType_Extra":   1,
	"SentenceType_Curday":  2,
}

func (x RewardSentenceAddReq_SentenceType) String() string {
	return proto.EnumName(RewardSentenceAddReq_SentenceType_name, int32(x))
}
func (RewardSentenceAddReq_SentenceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{91, 0}
}

type GenVoiceChatGreetingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	VoiceId              string   `protobuf:"bytes,4,opt,name=voice_id,json=voiceId,proto3" json:"voice_id,omitempty"`
	VoiceJson            string   `protobuf:"bytes,5,opt,name=voice_json,json=voiceJson,proto3" json:"voice_json,omitempty"`
	Text                 string   `protobuf:"bytes,6,opt,name=text,proto3" json:"text,omitempty"`
	SessionId            string   `protobuf:"bytes,7,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenVoiceChatGreetingReq) Reset()         { *m = GenVoiceChatGreetingReq{} }
func (m *GenVoiceChatGreetingReq) String() string { return proto.CompactTextString(m) }
func (*GenVoiceChatGreetingReq) ProtoMessage()    {}
func (*GenVoiceChatGreetingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{0}
}
func (m *GenVoiceChatGreetingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenVoiceChatGreetingReq.Unmarshal(m, b)
}
func (m *GenVoiceChatGreetingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenVoiceChatGreetingReq.Marshal(b, m, deterministic)
}
func (dst *GenVoiceChatGreetingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenVoiceChatGreetingReq.Merge(dst, src)
}
func (m *GenVoiceChatGreetingReq) XXX_Size() int {
	return xxx_messageInfo_GenVoiceChatGreetingReq.Size(m)
}
func (m *GenVoiceChatGreetingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenVoiceChatGreetingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenVoiceChatGreetingReq proto.InternalMessageInfo

func (m *GenVoiceChatGreetingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GenVoiceChatGreetingReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GenVoiceChatGreetingReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GenVoiceChatGreetingReq) GetVoiceId() string {
	if m != nil {
		return m.VoiceId
	}
	return ""
}

func (m *GenVoiceChatGreetingReq) GetVoiceJson() string {
	if m != nil {
		return m.VoiceJson
	}
	return ""
}

func (m *GenVoiceChatGreetingReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GenVoiceChatGreetingReq) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

type GenVoiceChatGreetingResp struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Seconds              int64    `protobuf:"varint,2,opt,name=seconds,proto3" json:"seconds,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenVoiceChatGreetingResp) Reset()         { *m = GenVoiceChatGreetingResp{} }
func (m *GenVoiceChatGreetingResp) String() string { return proto.CompactTextString(m) }
func (*GenVoiceChatGreetingResp) ProtoMessage()    {}
func (*GenVoiceChatGreetingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{1}
}
func (m *GenVoiceChatGreetingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenVoiceChatGreetingResp.Unmarshal(m, b)
}
func (m *GenVoiceChatGreetingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenVoiceChatGreetingResp.Marshal(b, m, deterministic)
}
func (dst *GenVoiceChatGreetingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenVoiceChatGreetingResp.Merge(dst, src)
}
func (m *GenVoiceChatGreetingResp) XXX_Size() int {
	return xxx_messageInfo_GenVoiceChatGreetingResp.Size(m)
}
func (m *GenVoiceChatGreetingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenVoiceChatGreetingResp.DiscardUnknown(m)
}

var xxx_messageInfo_GenVoiceChatGreetingResp proto.InternalMessageInfo

func (m *GenVoiceChatGreetingResp) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GenVoiceChatGreetingResp) GetSeconds() int64 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

func (m *GenVoiceChatGreetingResp) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type TestASRReq struct {
	Timestamp            int64    `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Ctx                  string   `protobuf:"bytes,3,opt,name=ctx,proto3" json:"ctx,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestASRReq) Reset()         { *m = TestASRReq{} }
func (m *TestASRReq) String() string { return proto.CompactTextString(m) }
func (*TestASRReq) ProtoMessage()    {}
func (*TestASRReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{2}
}
func (m *TestASRReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestASRReq.Unmarshal(m, b)
}
func (m *TestASRReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestASRReq.Marshal(b, m, deterministic)
}
func (dst *TestASRReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestASRReq.Merge(dst, src)
}
func (m *TestASRReq) XXX_Size() int {
	return xxx_messageInfo_TestASRReq.Size(m)
}
func (m *TestASRReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestASRReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestASRReq proto.InternalMessageInfo

func (m *TestASRReq) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *TestASRReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *TestASRReq) GetCtx() string {
	if m != nil {
		return m.Ctx
	}
	return ""
}

type TestASRResp struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestASRResp) Reset()         { *m = TestASRResp{} }
func (m *TestASRResp) String() string { return proto.CompactTextString(m) }
func (*TestASRResp) ProtoMessage()    {}
func (*TestASRResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{3}
}
func (m *TestASRResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestASRResp.Unmarshal(m, b)
}
func (m *TestASRResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestASRResp.Marshal(b, m, deterministic)
}
func (dst *TestASRResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestASRResp.Merge(dst, src)
}
func (m *TestASRResp) XXX_Size() int {
	return xxx_messageInfo_TestASRResp.Size(m)
}
func (m *TestASRResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestASRResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestASRResp proto.InternalMessageInfo

func (m *TestASRResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type SetPartnerSilentReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	PartnerId            uint32   `protobuf:"varint,3,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	IsSilent             bool     `protobuf:"varint,4,opt,name=is_silent,json=isSilent,proto3" json:"is_silent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPartnerSilentReq) Reset()         { *m = SetPartnerSilentReq{} }
func (m *SetPartnerSilentReq) String() string { return proto.CompactTextString(m) }
func (*SetPartnerSilentReq) ProtoMessage()    {}
func (*SetPartnerSilentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{4}
}
func (m *SetPartnerSilentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPartnerSilentReq.Unmarshal(m, b)
}
func (m *SetPartnerSilentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPartnerSilentReq.Marshal(b, m, deterministic)
}
func (dst *SetPartnerSilentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPartnerSilentReq.Merge(dst, src)
}
func (m *SetPartnerSilentReq) XXX_Size() int {
	return xxx_messageInfo_SetPartnerSilentReq.Size(m)
}
func (m *SetPartnerSilentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPartnerSilentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPartnerSilentReq proto.InternalMessageInfo

func (m *SetPartnerSilentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetPartnerSilentReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *SetPartnerSilentReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *SetPartnerSilentReq) GetIsSilent() bool {
	if m != nil {
		return m.IsSilent
	}
	return false
}

type SetDeviceWhitelistReq struct {
	DeviceIdList         []string `protobuf:"bytes,1,rep,name=device_id_list,json=deviceIdList,proto3" json:"device_id_list,omitempty"`
	IsDelete             bool     `protobuf:"varint,2,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	IsCheck              bool     `protobuf:"varint,3,opt,name=is_check,json=isCheck,proto3" json:"is_check,omitempty"`
	Group                string   `protobuf:"bytes,4,opt,name=group,proto3" json:"group,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDeviceWhitelistReq) Reset()         { *m = SetDeviceWhitelistReq{} }
func (m *SetDeviceWhitelistReq) String() string { return proto.CompactTextString(m) }
func (*SetDeviceWhitelistReq) ProtoMessage()    {}
func (*SetDeviceWhitelistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{5}
}
func (m *SetDeviceWhitelistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDeviceWhitelistReq.Unmarshal(m, b)
}
func (m *SetDeviceWhitelistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDeviceWhitelistReq.Marshal(b, m, deterministic)
}
func (dst *SetDeviceWhitelistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDeviceWhitelistReq.Merge(dst, src)
}
func (m *SetDeviceWhitelistReq) XXX_Size() int {
	return xxx_messageInfo_SetDeviceWhitelistReq.Size(m)
}
func (m *SetDeviceWhitelistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDeviceWhitelistReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDeviceWhitelistReq proto.InternalMessageInfo

func (m *SetDeviceWhitelistReq) GetDeviceIdList() []string {
	if m != nil {
		return m.DeviceIdList
	}
	return nil
}

func (m *SetDeviceWhitelistReq) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

func (m *SetDeviceWhitelistReq) GetIsCheck() bool {
	if m != nil {
		return m.IsCheck
	}
	return false
}

func (m *SetDeviceWhitelistReq) GetGroup() string {
	if m != nil {
		return m.Group
	}
	return ""
}

type SetDeviceWhitelistResp struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDeviceWhitelistResp) Reset()         { *m = SetDeviceWhitelistResp{} }
func (m *SetDeviceWhitelistResp) String() string { return proto.CompactTextString(m) }
func (*SetDeviceWhitelistResp) ProtoMessage()    {}
func (*SetDeviceWhitelistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{6}
}
func (m *SetDeviceWhitelistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDeviceWhitelistResp.Unmarshal(m, b)
}
func (m *SetDeviceWhitelistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDeviceWhitelistResp.Marshal(b, m, deterministic)
}
func (dst *SetDeviceWhitelistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDeviceWhitelistResp.Merge(dst, src)
}
func (m *SetDeviceWhitelistResp) XXX_Size() int {
	return xxx_messageInfo_SetDeviceWhitelistResp.Size(m)
}
func (m *SetDeviceWhitelistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDeviceWhitelistResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDeviceWhitelistResp proto.InternalMessageInfo

func (m *SetDeviceWhitelistResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type SetPartnerSilentResp struct {
	IsSilent             bool     `protobuf:"varint,1,opt,name=is_silent,json=isSilent,proto3" json:"is_silent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPartnerSilentResp) Reset()         { *m = SetPartnerSilentResp{} }
func (m *SetPartnerSilentResp) String() string { return proto.CompactTextString(m) }
func (*SetPartnerSilentResp) ProtoMessage()    {}
func (*SetPartnerSilentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{7}
}
func (m *SetPartnerSilentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPartnerSilentResp.Unmarshal(m, b)
}
func (m *SetPartnerSilentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPartnerSilentResp.Marshal(b, m, deterministic)
}
func (dst *SetPartnerSilentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPartnerSilentResp.Merge(dst, src)
}
func (m *SetPartnerSilentResp) XXX_Size() int {
	return xxx_messageInfo_SetPartnerSilentResp.Size(m)
}
func (m *SetPartnerSilentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPartnerSilentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPartnerSilentResp proto.InternalMessageInfo

func (m *SetPartnerSilentResp) GetIsSilent() bool {
	if m != nil {
		return m.IsSilent
	}
	return false
}

type SetPartnerAutoPlayStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	AutoPlayStatus       bool     `protobuf:"varint,3,opt,name=auto_play_status,json=autoPlayStatus,proto3" json:"auto_play_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPartnerAutoPlayStatusReq) Reset()         { *m = SetPartnerAutoPlayStatusReq{} }
func (m *SetPartnerAutoPlayStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetPartnerAutoPlayStatusReq) ProtoMessage()    {}
func (*SetPartnerAutoPlayStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{8}
}
func (m *SetPartnerAutoPlayStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPartnerAutoPlayStatusReq.Unmarshal(m, b)
}
func (m *SetPartnerAutoPlayStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPartnerAutoPlayStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetPartnerAutoPlayStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPartnerAutoPlayStatusReq.Merge(dst, src)
}
func (m *SetPartnerAutoPlayStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetPartnerAutoPlayStatusReq.Size(m)
}
func (m *SetPartnerAutoPlayStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPartnerAutoPlayStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPartnerAutoPlayStatusReq proto.InternalMessageInfo

func (m *SetPartnerAutoPlayStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetPartnerAutoPlayStatusReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *SetPartnerAutoPlayStatusReq) GetAutoPlayStatus() bool {
	if m != nil {
		return m.AutoPlayStatus
	}
	return false
}

type SetPartnerAutoPlayStatusResp struct {
	AutoPlayStatus       bool     `protobuf:"varint,1,opt,name=auto_play_status,json=autoPlayStatus,proto3" json:"auto_play_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPartnerAutoPlayStatusResp) Reset()         { *m = SetPartnerAutoPlayStatusResp{} }
func (m *SetPartnerAutoPlayStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetPartnerAutoPlayStatusResp) ProtoMessage()    {}
func (*SetPartnerAutoPlayStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{9}
}
func (m *SetPartnerAutoPlayStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPartnerAutoPlayStatusResp.Unmarshal(m, b)
}
func (m *SetPartnerAutoPlayStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPartnerAutoPlayStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetPartnerAutoPlayStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPartnerAutoPlayStatusResp.Merge(dst, src)
}
func (m *SetPartnerAutoPlayStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetPartnerAutoPlayStatusResp.Size(m)
}
func (m *SetPartnerAutoPlayStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPartnerAutoPlayStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPartnerAutoPlayStatusResp proto.InternalMessageInfo

func (m *SetPartnerAutoPlayStatusResp) GetAutoPlayStatus() bool {
	if m != nil {
		return m.AutoPlayStatus
	}
	return false
}

type GetPartnerSettingsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPartnerSettingsReq) Reset()         { *m = GetPartnerSettingsReq{} }
func (m *GetPartnerSettingsReq) String() string { return proto.CompactTextString(m) }
func (*GetPartnerSettingsReq) ProtoMessage()    {}
func (*GetPartnerSettingsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{10}
}
func (m *GetPartnerSettingsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerSettingsReq.Unmarshal(m, b)
}
func (m *GetPartnerSettingsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerSettingsReq.Marshal(b, m, deterministic)
}
func (dst *GetPartnerSettingsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerSettingsReq.Merge(dst, src)
}
func (m *GetPartnerSettingsReq) XXX_Size() int {
	return xxx_messageInfo_GetPartnerSettingsReq.Size(m)
}
func (m *GetPartnerSettingsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerSettingsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerSettingsReq proto.InternalMessageInfo

func (m *GetPartnerSettingsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPartnerSettingsReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type GetPartnerSettingsResp struct {
	IsSilent             bool     `protobuf:"varint,1,opt,name=is_silent,json=isSilent,proto3" json:"is_silent,omitempty"`
	AutoPlayStatus       bool     `protobuf:"varint,2,opt,name=auto_play_status,json=autoPlayStatus,proto3" json:"auto_play_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPartnerSettingsResp) Reset()         { *m = GetPartnerSettingsResp{} }
func (m *GetPartnerSettingsResp) String() string { return proto.CompactTextString(m) }
func (*GetPartnerSettingsResp) ProtoMessage()    {}
func (*GetPartnerSettingsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{11}
}
func (m *GetPartnerSettingsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerSettingsResp.Unmarshal(m, b)
}
func (m *GetPartnerSettingsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerSettingsResp.Marshal(b, m, deterministic)
}
func (dst *GetPartnerSettingsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerSettingsResp.Merge(dst, src)
}
func (m *GetPartnerSettingsResp) XXX_Size() int {
	return xxx_messageInfo_GetPartnerSettingsResp.Size(m)
}
func (m *GetPartnerSettingsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerSettingsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerSettingsResp proto.InternalMessageInfo

func (m *GetPartnerSettingsResp) GetIsSilent() bool {
	if m != nil {
		return m.IsSilent
	}
	return false
}

func (m *GetPartnerSettingsResp) GetAutoPlayStatus() bool {
	if m != nil {
		return m.AutoPlayStatus
	}
	return false
}

type GetPartnerReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPartnerReq) Reset()         { *m = GetPartnerReq{} }
func (m *GetPartnerReq) String() string { return proto.CompactTextString(m) }
func (*GetPartnerReq) ProtoMessage()    {}
func (*GetPartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{12}
}
func (m *GetPartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerReq.Unmarshal(m, b)
}
func (m *GetPartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerReq.Marshal(b, m, deterministic)
}
func (dst *GetPartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerReq.Merge(dst, src)
}
func (m *GetPartnerReq) XXX_Size() int {
	return xxx_messageInfo_GetPartnerReq.Size(m)
}
func (m *GetPartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerReq proto.InternalMessageInfo

func (m *GetPartnerReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetPartnerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPartnerResp struct {
	Partner              *Partner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	Exist                bool     `protobuf:"varint,2,opt,name=exist,proto3" json:"exist,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPartnerResp) Reset()         { *m = GetPartnerResp{} }
func (m *GetPartnerResp) String() string { return proto.CompactTextString(m) }
func (*GetPartnerResp) ProtoMessage()    {}
func (*GetPartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{13}
}
func (m *GetPartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerResp.Unmarshal(m, b)
}
func (m *GetPartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerResp.Marshal(b, m, deterministic)
}
func (dst *GetPartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerResp.Merge(dst, src)
}
func (m *GetPartnerResp) XXX_Size() int {
	return xxx_messageInfo_GetPartnerResp.Size(m)
}
func (m *GetPartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerResp proto.InternalMessageInfo

func (m *GetPartnerResp) GetPartner() *Partner {
	if m != nil {
		return m.Partner
	}
	return nil
}

func (m *GetPartnerResp) GetExist() bool {
	if m != nil {
		return m.Exist
	}
	return false
}

type SetPartnerLoginPushTagReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	Tag                  string   `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPartnerLoginPushTagReq) Reset()         { *m = SetPartnerLoginPushTagReq{} }
func (m *SetPartnerLoginPushTagReq) String() string { return proto.CompactTextString(m) }
func (*SetPartnerLoginPushTagReq) ProtoMessage()    {}
func (*SetPartnerLoginPushTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{14}
}
func (m *SetPartnerLoginPushTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPartnerLoginPushTagReq.Unmarshal(m, b)
}
func (m *SetPartnerLoginPushTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPartnerLoginPushTagReq.Marshal(b, m, deterministic)
}
func (dst *SetPartnerLoginPushTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPartnerLoginPushTagReq.Merge(dst, src)
}
func (m *SetPartnerLoginPushTagReq) XXX_Size() int {
	return xxx_messageInfo_SetPartnerLoginPushTagReq.Size(m)
}
func (m *SetPartnerLoginPushTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPartnerLoginPushTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPartnerLoginPushTagReq proto.InternalMessageInfo

func (m *SetPartnerLoginPushTagReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetPartnerLoginPushTagReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *SetPartnerLoginPushTagReq) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

type SetPartnerLoginPushTagResp struct {
	Ok                   bool     `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPartnerLoginPushTagResp) Reset()         { *m = SetPartnerLoginPushTagResp{} }
func (m *SetPartnerLoginPushTagResp) String() string { return proto.CompactTextString(m) }
func (*SetPartnerLoginPushTagResp) ProtoMessage()    {}
func (*SetPartnerLoginPushTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{15}
}
func (m *SetPartnerLoginPushTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPartnerLoginPushTagResp.Unmarshal(m, b)
}
func (m *SetPartnerLoginPushTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPartnerLoginPushTagResp.Marshal(b, m, deterministic)
}
func (dst *SetPartnerLoginPushTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPartnerLoginPushTagResp.Merge(dst, src)
}
func (m *SetPartnerLoginPushTagResp) XXX_Size() int {
	return xxx_messageInfo_SetPartnerLoginPushTagResp.Size(m)
}
func (m *SetPartnerLoginPushTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPartnerLoginPushTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPartnerLoginPushTagResp proto.InternalMessageInfo

func (m *SetPartnerLoginPushTagResp) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

type GetPartnerByRoleIdReq struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPartnerByRoleIdReq) Reset()         { *m = GetPartnerByRoleIdReq{} }
func (m *GetPartnerByRoleIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPartnerByRoleIdReq) ProtoMessage()    {}
func (*GetPartnerByRoleIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{16}
}
func (m *GetPartnerByRoleIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerByRoleIdReq.Unmarshal(m, b)
}
func (m *GetPartnerByRoleIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerByRoleIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPartnerByRoleIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerByRoleIdReq.Merge(dst, src)
}
func (m *GetPartnerByRoleIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPartnerByRoleIdReq.Size(m)
}
func (m *GetPartnerByRoleIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerByRoleIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerByRoleIdReq proto.InternalMessageInfo

func (m *GetPartnerByRoleIdReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetPartnerByRoleIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPartnerByRoleIdResp struct {
	Partner              *Partner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	Exist                bool     `protobuf:"varint,2,opt,name=exist,proto3" json:"exist,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPartnerByRoleIdResp) Reset()         { *m = GetPartnerByRoleIdResp{} }
func (m *GetPartnerByRoleIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPartnerByRoleIdResp) ProtoMessage()    {}
func (*GetPartnerByRoleIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{17}
}
func (m *GetPartnerByRoleIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerByRoleIdResp.Unmarshal(m, b)
}
func (m *GetPartnerByRoleIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerByRoleIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPartnerByRoleIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerByRoleIdResp.Merge(dst, src)
}
func (m *GetPartnerByRoleIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPartnerByRoleIdResp.Size(m)
}
func (m *GetPartnerByRoleIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerByRoleIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerByRoleIdResp proto.InternalMessageInfo

func (m *GetPartnerByRoleIdResp) GetPartner() *Partner {
	if m != nil {
		return m.Partner
	}
	return nil
}

func (m *GetPartnerByRoleIdResp) GetExist() bool {
	if m != nil {
		return m.Exist
	}
	return false
}

type Partner struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	CallName             string   `protobuf:"bytes,4,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	RoleId               uint32   `protobuf:"varint,5,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ChattingStatus       uint32   `protobuf:"varint,6,opt,name=chatting_status,json=chattingStatus,proto3" json:"chatting_status,omitempty"`
	RelationshipId       uint32   `protobuf:"varint,7,opt,name=relationship_id,json=relationshipId,proto3" json:"relationship_id,omitempty"`
	AnimateMemes         []string `protobuf:"bytes,8,rep,name=animate_memes,json=animateMemes,proto3" json:"animate_memes,omitempty"`
	CreateTime           int64    `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           int64    `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	UserMsgCount         uint32   `protobuf:"varint,11,opt,name=user_msg_count,json=userMsgCount,proto3" json:"user_msg_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Partner) Reset()         { *m = Partner{} }
func (m *Partner) String() string { return proto.CompactTextString(m) }
func (*Partner) ProtoMessage()    {}
func (*Partner) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{18}
}
func (m *Partner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Partner.Unmarshal(m, b)
}
func (m *Partner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Partner.Marshal(b, m, deterministic)
}
func (dst *Partner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Partner.Merge(dst, src)
}
func (m *Partner) XXX_Size() int {
	return xxx_messageInfo_Partner.Size(m)
}
func (m *Partner) XXX_DiscardUnknown() {
	xxx_messageInfo_Partner.DiscardUnknown(m)
}

var xxx_messageInfo_Partner proto.InternalMessageInfo

func (m *Partner) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Partner) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Partner) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Partner) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *Partner) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *Partner) GetChattingStatus() uint32 {
	if m != nil {
		return m.ChattingStatus
	}
	return 0
}

func (m *Partner) GetRelationshipId() uint32 {
	if m != nil {
		return m.RelationshipId
	}
	return 0
}

func (m *Partner) GetAnimateMemes() []string {
	if m != nil {
		return m.AnimateMemes
	}
	return nil
}

func (m *Partner) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Partner) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Partner) GetUserMsgCount() uint32 {
	if m != nil {
		return m.UserMsgCount
	}
	return 0
}

type FormatGptAnswerReq struct {
	GptA                 string   `protobuf:"bytes,1,opt,name=gpt_a,json=gptA,proto3" json:"gpt_a,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FormatGptAnswerReq) Reset()         { *m = FormatGptAnswerReq{} }
func (m *FormatGptAnswerReq) String() string { return proto.CompactTextString(m) }
func (*FormatGptAnswerReq) ProtoMessage()    {}
func (*FormatGptAnswerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{19}
}
func (m *FormatGptAnswerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FormatGptAnswerReq.Unmarshal(m, b)
}
func (m *FormatGptAnswerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FormatGptAnswerReq.Marshal(b, m, deterministic)
}
func (dst *FormatGptAnswerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FormatGptAnswerReq.Merge(dst, src)
}
func (m *FormatGptAnswerReq) XXX_Size() int {
	return xxx_messageInfo_FormatGptAnswerReq.Size(m)
}
func (m *FormatGptAnswerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FormatGptAnswerReq.DiscardUnknown(m)
}

var xxx_messageInfo_FormatGptAnswerReq proto.InternalMessageInfo

func (m *FormatGptAnswerReq) GetGptA() string {
	if m != nil {
		return m.GptA
	}
	return ""
}

type FormatGptAnswerResp struct {
	MsgList              []*partner_common.FormatMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *FormatGptAnswerResp) Reset()         { *m = FormatGptAnswerResp{} }
func (m *FormatGptAnswerResp) String() string { return proto.CompactTextString(m) }
func (*FormatGptAnswerResp) ProtoMessage()    {}
func (*FormatGptAnswerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{20}
}
func (m *FormatGptAnswerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FormatGptAnswerResp.Unmarshal(m, b)
}
func (m *FormatGptAnswerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FormatGptAnswerResp.Marshal(b, m, deterministic)
}
func (dst *FormatGptAnswerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FormatGptAnswerResp.Merge(dst, src)
}
func (m *FormatGptAnswerResp) XXX_Size() int {
	return xxx_messageInfo_FormatGptAnswerResp.Size(m)
}
func (m *FormatGptAnswerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FormatGptAnswerResp.DiscardUnknown(m)
}

var xxx_messageInfo_FormatGptAnswerResp proto.InternalMessageInfo

func (m *FormatGptAnswerResp) GetMsgList() []*partner_common.FormatMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type GetQuickReplyListReq struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickReplyListReq) Reset()         { *m = GetQuickReplyListReq{} }
func (m *GetQuickReplyListReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickReplyListReq) ProtoMessage()    {}
func (*GetQuickReplyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{21}
}
func (m *GetQuickReplyListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickReplyListReq.Unmarshal(m, b)
}
func (m *GetQuickReplyListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickReplyListReq.Marshal(b, m, deterministic)
}
func (dst *GetQuickReplyListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickReplyListReq.Merge(dst, src)
}
func (m *GetQuickReplyListReq) XXX_Size() int {
	return xxx_messageInfo_GetQuickReplyListReq.Size(m)
}
func (m *GetQuickReplyListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickReplyListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickReplyListReq proto.InternalMessageInfo

func (m *GetQuickReplyListReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetQuickReplyListResp struct {
	ReplyList            []string `protobuf:"bytes,1,rep,name=reply_list,json=replyList,proto3" json:"reply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickReplyListResp) Reset()         { *m = GetQuickReplyListResp{} }
func (m *GetQuickReplyListResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickReplyListResp) ProtoMessage()    {}
func (*GetQuickReplyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{22}
}
func (m *GetQuickReplyListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickReplyListResp.Unmarshal(m, b)
}
func (m *GetQuickReplyListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickReplyListResp.Marshal(b, m, deterministic)
}
func (dst *GetQuickReplyListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickReplyListResp.Merge(dst, src)
}
func (m *GetQuickReplyListResp) XXX_Size() int {
	return xxx_messageInfo_GetQuickReplyListResp.Size(m)
}
func (m *GetQuickReplyListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickReplyListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickReplyListResp proto.InternalMessageInfo

func (m *GetQuickReplyListResp) GetReplyList() []string {
	if m != nil {
		return m.ReplyList
	}
	return nil
}

type GetGreetingMsgReq struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGreetingMsgReq) Reset()         { *m = GetGreetingMsgReq{} }
func (m *GetGreetingMsgReq) String() string { return proto.CompactTextString(m) }
func (*GetGreetingMsgReq) ProtoMessage()    {}
func (*GetGreetingMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{23}
}
func (m *GetGreetingMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGreetingMsgReq.Unmarshal(m, b)
}
func (m *GetGreetingMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGreetingMsgReq.Marshal(b, m, deterministic)
}
func (dst *GetGreetingMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGreetingMsgReq.Merge(dst, src)
}
func (m *GetGreetingMsgReq) XXX_Size() int {
	return xxx_messageInfo_GetGreetingMsgReq.Size(m)
}
func (m *GetGreetingMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGreetingMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGreetingMsgReq proto.InternalMessageInfo

func (m *GetGreetingMsgReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetGreetingMsgResp struct {
	MsgList              []*partner_common.FormatMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetGreetingMsgResp) Reset()         { *m = GetGreetingMsgResp{} }
func (m *GetGreetingMsgResp) String() string { return proto.CompactTextString(m) }
func (*GetGreetingMsgResp) ProtoMessage()    {}
func (*GetGreetingMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{24}
}
func (m *GetGreetingMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGreetingMsgResp.Unmarshal(m, b)
}
func (m *GetGreetingMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGreetingMsgResp.Marshal(b, m, deterministic)
}
func (dst *GetGreetingMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGreetingMsgResp.Merge(dst, src)
}
func (m *GetGreetingMsgResp) XXX_Size() int {
	return xxx_messageInfo_GetGreetingMsgResp.Size(m)
}
func (m *GetGreetingMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGreetingMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGreetingMsgResp proto.InternalMessageInfo

func (m *GetGreetingMsgResp) GetMsgList() []*partner_common.FormatMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type GetGPTReqInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGPTReqInfoReq) Reset()         { *m = GetGPTReqInfoReq{} }
func (m *GetGPTReqInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGPTReqInfoReq) ProtoMessage()    {}
func (*GetGPTReqInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{25}
}
func (m *GetGPTReqInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGPTReqInfoReq.Unmarshal(m, b)
}
func (m *GetGPTReqInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGPTReqInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGPTReqInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGPTReqInfoReq.Merge(dst, src)
}
func (m *GetGPTReqInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGPTReqInfoReq.Size(m)
}
func (m *GetGPTReqInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGPTReqInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGPTReqInfoReq proto.InternalMessageInfo

func (m *GetGPTReqInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGPTReqInfoReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetGPTReqInfoResp struct {
	PlaceholderStr       string   `protobuf:"bytes,1,opt,name=placeholder_str,json=placeholderStr,proto3" json:"placeholder_str,omitempty"`
	PromptId             string   `protobuf:"bytes,2,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	PromptVersion        string   `protobuf:"bytes,3,opt,name=prompt_version,json=promptVersion,proto3" json:"prompt_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGPTReqInfoResp) Reset()         { *m = GetGPTReqInfoResp{} }
func (m *GetGPTReqInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGPTReqInfoResp) ProtoMessage()    {}
func (*GetGPTReqInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{26}
}
func (m *GetGPTReqInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGPTReqInfoResp.Unmarshal(m, b)
}
func (m *GetGPTReqInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGPTReqInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGPTReqInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGPTReqInfoResp.Merge(dst, src)
}
func (m *GetGPTReqInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGPTReqInfoResp.Size(m)
}
func (m *GetGPTReqInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGPTReqInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGPTReqInfoResp proto.InternalMessageInfo

func (m *GetGPTReqInfoResp) GetPlaceholderStr() string {
	if m != nil {
		return m.PlaceholderStr
	}
	return ""
}

func (m *GetGPTReqInfoResp) GetPromptId() string {
	if m != nil {
		return m.PromptId
	}
	return ""
}

func (m *GetGPTReqInfoResp) GetPromptVersion() string {
	if m != nil {
		return m.PromptVersion
	}
	return ""
}

type GetUserInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfoReq) Reset()         { *m = GetUserInfoReq{} }
func (m *GetUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoReq) ProtoMessage()    {}
func (*GetUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{27}
}
func (m *GetUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoReq.Unmarshal(m, b)
}
func (m *GetUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoReq.Merge(dst, src)
}
func (m *GetUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoReq.Size(m)
}
func (m *GetUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoReq proto.InternalMessageInfo

func (m *GetUserInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserInfoResp struct {
	UserTodayMsgCount    uint32   `protobuf:"varint,1,opt,name=user_today_msg_count,json=userTodayMsgCount,proto3" json:"user_today_msg_count,omitempty"`
	UserTotalMsgCount    uint32   `protobuf:"varint,2,opt,name=user_total_msg_count,json=userTotalMsgCount,proto3" json:"user_total_msg_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfoResp) Reset()         { *m = GetUserInfoResp{} }
func (m *GetUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoResp) ProtoMessage()    {}
func (*GetUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{28}
}
func (m *GetUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoResp.Unmarshal(m, b)
}
func (m *GetUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoResp.Merge(dst, src)
}
func (m *GetUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoResp.Size(m)
}
func (m *GetUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoResp proto.InternalMessageInfo

func (m *GetUserInfoResp) GetUserTodayMsgCount() uint32 {
	if m != nil {
		return m.UserTodayMsgCount
	}
	return 0
}

func (m *GetUserInfoResp) GetUserTotalMsgCount() uint32 {
	if m != nil {
		return m.UserTotalMsgCount
	}
	return 0
}

type GetUserMsgStatReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserMsgStatReq) Reset()         { *m = GetUserMsgStatReq{} }
func (m *GetUserMsgStatReq) String() string { return proto.CompactTextString(m) }
func (*GetUserMsgStatReq) ProtoMessage()    {}
func (*GetUserMsgStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{29}
}
func (m *GetUserMsgStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMsgStatReq.Unmarshal(m, b)
}
func (m *GetUserMsgStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMsgStatReq.Marshal(b, m, deterministic)
}
func (dst *GetUserMsgStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMsgStatReq.Merge(dst, src)
}
func (m *GetUserMsgStatReq) XXX_Size() int {
	return xxx_messageInfo_GetUserMsgStatReq.Size(m)
}
func (m *GetUserMsgStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMsgStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMsgStatReq proto.InternalMessageInfo

func (m *GetUserMsgStatReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserMsgStatResp struct {
	TalkedPartnerCount   uint32   `protobuf:"varint,1,opt,name=talked_partner_count,json=talkedPartnerCount,proto3" json:"talked_partner_count,omitempty"`
	FallbackPage         uint32   `protobuf:"varint,2,opt,name=fallback_page,json=fallbackPage,proto3" json:"fallback_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserMsgStatResp) Reset()         { *m = GetUserMsgStatResp{} }
func (m *GetUserMsgStatResp) String() string { return proto.CompactTextString(m) }
func (*GetUserMsgStatResp) ProtoMessage()    {}
func (*GetUserMsgStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{30}
}
func (m *GetUserMsgStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMsgStatResp.Unmarshal(m, b)
}
func (m *GetUserMsgStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMsgStatResp.Marshal(b, m, deterministic)
}
func (dst *GetUserMsgStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMsgStatResp.Merge(dst, src)
}
func (m *GetUserMsgStatResp) XXX_Size() int {
	return xxx_messageInfo_GetUserMsgStatResp.Size(m)
}
func (m *GetUserMsgStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMsgStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMsgStatResp proto.InternalMessageInfo

func (m *GetUserMsgStatResp) GetTalkedPartnerCount() uint32 {
	if m != nil {
		return m.TalkedPartnerCount
	}
	return 0
}

func (m *GetUserMsgStatResp) GetFallbackPage() uint32 {
	if m != nil {
		return m.FallbackPage
	}
	return 0
}

type GenRoleDescReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenRoleDescReq) Reset()         { *m = GenRoleDescReq{} }
func (m *GenRoleDescReq) String() string { return proto.CompactTextString(m) }
func (*GenRoleDescReq) ProtoMessage()    {}
func (*GenRoleDescReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{31}
}
func (m *GenRoleDescReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenRoleDescReq.Unmarshal(m, b)
}
func (m *GenRoleDescReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenRoleDescReq.Marshal(b, m, deterministic)
}
func (dst *GenRoleDescReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenRoleDescReq.Merge(dst, src)
}
func (m *GenRoleDescReq) XXX_Size() int {
	return xxx_messageInfo_GenRoleDescReq.Size(m)
}
func (m *GenRoleDescReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenRoleDescReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenRoleDescReq proto.InternalMessageInfo

func (m *GenRoleDescReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GenRoleDescReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GenRoleDescReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type GenRoleDescResp struct {
	Desc                 string   `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenRoleDescResp) Reset()         { *m = GenRoleDescResp{} }
func (m *GenRoleDescResp) String() string { return proto.CompactTextString(m) }
func (*GenRoleDescResp) ProtoMessage()    {}
func (*GenRoleDescResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{32}
}
func (m *GenRoleDescResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenRoleDescResp.Unmarshal(m, b)
}
func (m *GenRoleDescResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenRoleDescResp.Marshal(b, m, deterministic)
}
func (dst *GenRoleDescResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenRoleDescResp.Merge(dst, src)
}
func (m *GenRoleDescResp) XXX_Size() int {
	return xxx_messageInfo_GenRoleDescResp.Size(m)
}
func (m *GenRoleDescResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenRoleDescResp.DiscardUnknown(m)
}

var xxx_messageInfo_GenRoleDescResp proto.InternalMessageInfo

func (m *GenRoleDescResp) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

// 	"uid":111,//uint32 用户ID
//
// "partner_id":123,//uint32,机器人ID
// "role_id":333,//uint32,角色ID
type GenRecommendReplyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenRecommendReplyReq) Reset()         { *m = GenRecommendReplyReq{} }
func (m *GenRecommendReplyReq) String() string { return proto.CompactTextString(m) }
func (*GenRecommendReplyReq) ProtoMessage()    {}
func (*GenRecommendReplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{33}
}
func (m *GenRecommendReplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenRecommendReplyReq.Unmarshal(m, b)
}
func (m *GenRecommendReplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenRecommendReplyReq.Marshal(b, m, deterministic)
}
func (dst *GenRecommendReplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenRecommendReplyReq.Merge(dst, src)
}
func (m *GenRecommendReplyReq) XXX_Size() int {
	return xxx_messageInfo_GenRecommendReplyReq.Size(m)
}
func (m *GenRecommendReplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenRecommendReplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenRecommendReplyReq proto.InternalMessageInfo

func (m *GenRecommendReplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GenRecommendReplyReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GenRecommendReplyReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GenRecommendReplyResp struct {
	ReplyList            []string `protobuf:"bytes,1,rep,name=reply_list,json=replyList,proto3" json:"reply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenRecommendReplyResp) Reset()         { *m = GenRecommendReplyResp{} }
func (m *GenRecommendReplyResp) String() string { return proto.CompactTextString(m) }
func (*GenRecommendReplyResp) ProtoMessage()    {}
func (*GenRecommendReplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{34}
}
func (m *GenRecommendReplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenRecommendReplyResp.Unmarshal(m, b)
}
func (m *GenRecommendReplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenRecommendReplyResp.Marshal(b, m, deterministic)
}
func (dst *GenRecommendReplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenRecommendReplyResp.Merge(dst, src)
}
func (m *GenRecommendReplyResp) XXX_Size() int {
	return xxx_messageInfo_GenRecommendReplyResp.Size(m)
}
func (m *GenRecommendReplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenRecommendReplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GenRecommendReplyResp proto.InternalMessageInfo

func (m *GenRecommendReplyResp) GetReplyList() []string {
	if m != nil {
		return m.ReplyList
	}
	return nil
}

type GetGenStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGenStatusReq) Reset()         { *m = GetGenStatusReq{} }
func (m *GetGenStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetGenStatusReq) ProtoMessage()    {}
func (*GetGenStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{35}
}
func (m *GetGenStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGenStatusReq.Unmarshal(m, b)
}
func (m *GetGenStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGenStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetGenStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGenStatusReq.Merge(dst, src)
}
func (m *GetGenStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetGenStatusReq.Size(m)
}
func (m *GetGenStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGenStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGenStatusReq proto.InternalMessageInfo

func (m *GetGenStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGenStatusResp struct {
	GenRoleExceeded      bool     `protobuf:"varint,1,opt,name=gen_role_exceeded,json=genRoleExceeded,proto3" json:"gen_role_exceeded,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGenStatusResp) Reset()         { *m = GetGenStatusResp{} }
func (m *GetGenStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetGenStatusResp) ProtoMessage()    {}
func (*GetGenStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{36}
}
func (m *GetGenStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGenStatusResp.Unmarshal(m, b)
}
func (m *GetGenStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGenStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetGenStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGenStatusResp.Merge(dst, src)
}
func (m *GetGenStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetGenStatusResp.Size(m)
}
func (m *GetGenStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGenStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGenStatusResp proto.InternalMessageInfo

func (m *GetGenStatusResp) GetGenRoleExceeded() bool {
	if m != nil {
		return m.GenRoleExceeded
	}
	return false
}

type GetStoryHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	StoryId              uint32   `protobuf:"varint,3,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	MaxSentAt            int64    `protobuf:"varint,4,opt,name=max_sent_at,json=maxSentAt,proto3" json:"max_sent_at,omitempty"`
	Size                 uint32   `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryHistoryReq) Reset()         { *m = GetStoryHistoryReq{} }
func (m *GetStoryHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetStoryHistoryReq) ProtoMessage()    {}
func (*GetStoryHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{37}
}
func (m *GetStoryHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryHistoryReq.Unmarshal(m, b)
}
func (m *GetStoryHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetStoryHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryHistoryReq.Merge(dst, src)
}
func (m *GetStoryHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetStoryHistoryReq.Size(m)
}
func (m *GetStoryHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryHistoryReq proto.InternalMessageInfo

func (m *GetStoryHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStoryHistoryReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetStoryHistoryReq) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

func (m *GetStoryHistoryReq) GetMaxSentAt() int64 {
	if m != nil {
		return m.MaxSentAt
	}
	return 0
}

func (m *GetStoryHistoryReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type StoryMsg struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Ext                  []byte   `protobuf:"bytes,3,opt,name=ext,proto3" json:"ext,omitempty"`
	SentAt               int64    `protobuf:"varint,4,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	MsgId                string   `protobuf:"bytes,5,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	Sender               string   `protobuf:"bytes,6,opt,name=sender,proto3" json:"sender,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoryMsg) Reset()         { *m = StoryMsg{} }
func (m *StoryMsg) String() string { return proto.CompactTextString(m) }
func (*StoryMsg) ProtoMessage()    {}
func (*StoryMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{38}
}
func (m *StoryMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoryMsg.Unmarshal(m, b)
}
func (m *StoryMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoryMsg.Marshal(b, m, deterministic)
}
func (dst *StoryMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoryMsg.Merge(dst, src)
}
func (m *StoryMsg) XXX_Size() int {
	return xxx_messageInfo_StoryMsg.Size(m)
}
func (m *StoryMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_StoryMsg.DiscardUnknown(m)
}

var xxx_messageInfo_StoryMsg proto.InternalMessageInfo

func (m *StoryMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *StoryMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *StoryMsg) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *StoryMsg) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *StoryMsg) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *StoryMsg) GetSender() string {
	if m != nil {
		return m.Sender
	}
	return ""
}

type GetStoryHistoryResp struct {
	MsgList              []*StoryMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	Size                 uint32      `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetStoryHistoryResp) Reset()         { *m = GetStoryHistoryResp{} }
func (m *GetStoryHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetStoryHistoryResp) ProtoMessage()    {}
func (*GetStoryHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{39}
}
func (m *GetStoryHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryHistoryResp.Unmarshal(m, b)
}
func (m *GetStoryHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetStoryHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryHistoryResp.Merge(dst, src)
}
func (m *GetStoryHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetStoryHistoryResp.Size(m)
}
func (m *GetStoryHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryHistoryResp proto.InternalMessageInfo

func (m *GetStoryHistoryResp) GetMsgList() []*StoryMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

func (m *GetStoryHistoryResp) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type GetStoryReplyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	StoryId              uint32   `protobuf:"varint,3,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryReplyReq) Reset()         { *m = GetStoryReplyReq{} }
func (m *GetStoryReplyReq) String() string { return proto.CompactTextString(m) }
func (*GetStoryReplyReq) ProtoMessage()    {}
func (*GetStoryReplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{40}
}
func (m *GetStoryReplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryReplyReq.Unmarshal(m, b)
}
func (m *GetStoryReplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryReplyReq.Marshal(b, m, deterministic)
}
func (dst *GetStoryReplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryReplyReq.Merge(dst, src)
}
func (m *GetStoryReplyReq) XXX_Size() int {
	return xxx_messageInfo_GetStoryReplyReq.Size(m)
}
func (m *GetStoryReplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryReplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryReplyReq proto.InternalMessageInfo

func (m *GetStoryReplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStoryReplyReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetStoryReplyReq) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

type GetStoryReplyResp struct {
	SceneId              string                            `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	DialogId             string                            `protobuf:"bytes,2,opt,name=dialog_id,json=dialogId,proto3" json:"dialog_id,omitempty"`
	ReplyOptions         []*GetStoryReplyResp_ReplyOptions `protobuf:"bytes,3,rep,name=reply_options,json=replyOptions,proto3" json:"reply_options,omitempty"`
	EnableCustomReply    bool                              `protobuf:"varint,4,opt,name=enable_custom_reply,json=enableCustomReply,proto3" json:"enable_custom_reply,omitempty"`
	CustomContentId      string                            `protobuf:"bytes,5,opt,name=custom_content_id,json=customContentId,proto3" json:"custom_content_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetStoryReplyResp) Reset()         { *m = GetStoryReplyResp{} }
func (m *GetStoryReplyResp) String() string { return proto.CompactTextString(m) }
func (*GetStoryReplyResp) ProtoMessage()    {}
func (*GetStoryReplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{41}
}
func (m *GetStoryReplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryReplyResp.Unmarshal(m, b)
}
func (m *GetStoryReplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryReplyResp.Marshal(b, m, deterministic)
}
func (dst *GetStoryReplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryReplyResp.Merge(dst, src)
}
func (m *GetStoryReplyResp) XXX_Size() int {
	return xxx_messageInfo_GetStoryReplyResp.Size(m)
}
func (m *GetStoryReplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryReplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryReplyResp proto.InternalMessageInfo

func (m *GetStoryReplyResp) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *GetStoryReplyResp) GetDialogId() string {
	if m != nil {
		return m.DialogId
	}
	return ""
}

func (m *GetStoryReplyResp) GetReplyOptions() []*GetStoryReplyResp_ReplyOptions {
	if m != nil {
		return m.ReplyOptions
	}
	return nil
}

func (m *GetStoryReplyResp) GetEnableCustomReply() bool {
	if m != nil {
		return m.EnableCustomReply
	}
	return false
}

func (m *GetStoryReplyResp) GetCustomContentId() string {
	if m != nil {
		return m.CustomContentId
	}
	return ""
}

type GetStoryReplyResp_ReplyOptions struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryReplyResp_ReplyOptions) Reset()         { *m = GetStoryReplyResp_ReplyOptions{} }
func (m *GetStoryReplyResp_ReplyOptions) String() string { return proto.CompactTextString(m) }
func (*GetStoryReplyResp_ReplyOptions) ProtoMessage()    {}
func (*GetStoryReplyResp_ReplyOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{41, 0}
}
func (m *GetStoryReplyResp_ReplyOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryReplyResp_ReplyOptions.Unmarshal(m, b)
}
func (m *GetStoryReplyResp_ReplyOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryReplyResp_ReplyOptions.Marshal(b, m, deterministic)
}
func (dst *GetStoryReplyResp_ReplyOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryReplyResp_ReplyOptions.Merge(dst, src)
}
func (m *GetStoryReplyResp_ReplyOptions) XXX_Size() int {
	return xxx_messageInfo_GetStoryReplyResp_ReplyOptions.Size(m)
}
func (m *GetStoryReplyResp_ReplyOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryReplyResp_ReplyOptions.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryReplyResp_ReplyOptions proto.InternalMessageInfo

func (m *GetStoryReplyResp_ReplyOptions) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetStoryReplyResp_ReplyOptions) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type GetStorySceneReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	StoryId              uint32   `protobuf:"varint,3,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStorySceneReq) Reset()         { *m = GetStorySceneReq{} }
func (m *GetStorySceneReq) String() string { return proto.CompactTextString(m) }
func (*GetStorySceneReq) ProtoMessage()    {}
func (*GetStorySceneReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{42}
}
func (m *GetStorySceneReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStorySceneReq.Unmarshal(m, b)
}
func (m *GetStorySceneReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStorySceneReq.Marshal(b, m, deterministic)
}
func (dst *GetStorySceneReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStorySceneReq.Merge(dst, src)
}
func (m *GetStorySceneReq) XXX_Size() int {
	return xxx_messageInfo_GetStorySceneReq.Size(m)
}
func (m *GetStorySceneReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStorySceneReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStorySceneReq proto.InternalMessageInfo

func (m *GetStorySceneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStorySceneReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetStorySceneReq) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

type GetStorySceneResp struct {
	SceneName            string   `protobuf:"bytes,1,opt,name=scene_name,json=sceneName,proto3" json:"scene_name,omitempty"`
	SceneId              string   `protobuf:"bytes,2,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	BackgroundUrl        string   `protobuf:"bytes,3,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	BackgroundMusicUrl   string   `protobuf:"bytes,4,opt,name=background_music_url,json=backgroundMusicUrl,proto3" json:"background_music_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStorySceneResp) Reset()         { *m = GetStorySceneResp{} }
func (m *GetStorySceneResp) String() string { return proto.CompactTextString(m) }
func (*GetStorySceneResp) ProtoMessage()    {}
func (*GetStorySceneResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{43}
}
func (m *GetStorySceneResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStorySceneResp.Unmarshal(m, b)
}
func (m *GetStorySceneResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStorySceneResp.Marshal(b, m, deterministic)
}
func (dst *GetStorySceneResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStorySceneResp.Merge(dst, src)
}
func (m *GetStorySceneResp) XXX_Size() int {
	return xxx_messageInfo_GetStorySceneResp.Size(m)
}
func (m *GetStorySceneResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStorySceneResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStorySceneResp proto.InternalMessageInfo

func (m *GetStorySceneResp) GetSceneName() string {
	if m != nil {
		return m.SceneName
	}
	return ""
}

func (m *GetStorySceneResp) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *GetStorySceneResp) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *GetStorySceneResp) GetBackgroundMusicUrl() string {
	if m != nil {
		return m.BackgroundMusicUrl
	}
	return ""
}

type SetStoryProgressReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	StoryId              uint32   `protobuf:"varint,3,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	Op                   uint32   `protobuf:"varint,4,opt,name=op,proto3" json:"op,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetStoryProgressReq) Reset()         { *m = SetStoryProgressReq{} }
func (m *SetStoryProgressReq) String() string { return proto.CompactTextString(m) }
func (*SetStoryProgressReq) ProtoMessage()    {}
func (*SetStoryProgressReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{44}
}
func (m *SetStoryProgressReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStoryProgressReq.Unmarshal(m, b)
}
func (m *SetStoryProgressReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStoryProgressReq.Marshal(b, m, deterministic)
}
func (dst *SetStoryProgressReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStoryProgressReq.Merge(dst, src)
}
func (m *SetStoryProgressReq) XXX_Size() int {
	return xxx_messageInfo_SetStoryProgressReq.Size(m)
}
func (m *SetStoryProgressReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStoryProgressReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetStoryProgressReq proto.InternalMessageInfo

func (m *SetStoryProgressReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetStoryProgressReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *SetStoryProgressReq) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

func (m *SetStoryProgressReq) GetOp() uint32 {
	if m != nil {
		return m.Op
	}
	return 0
}

type SetStoryProgressResp struct {
	Ok                   bool     `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetStoryProgressResp) Reset()         { *m = SetStoryProgressResp{} }
func (m *SetStoryProgressResp) String() string { return proto.CompactTextString(m) }
func (*SetStoryProgressResp) ProtoMessage()    {}
func (*SetStoryProgressResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{45}
}
func (m *SetStoryProgressResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStoryProgressResp.Unmarshal(m, b)
}
func (m *SetStoryProgressResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStoryProgressResp.Marshal(b, m, deterministic)
}
func (dst *SetStoryProgressResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStoryProgressResp.Merge(dst, src)
}
func (m *SetStoryProgressResp) XXX_Size() int {
	return xxx_messageInfo_SetStoryProgressResp.Size(m)
}
func (m *SetStoryProgressResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStoryProgressResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetStoryProgressResp proto.InternalMessageInfo

func (m *SetStoryProgressResp) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

type StartStoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	StoryId              uint32   `protobuf:"varint,3,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartStoryReq) Reset()         { *m = StartStoryReq{} }
func (m *StartStoryReq) String() string { return proto.CompactTextString(m) }
func (*StartStoryReq) ProtoMessage()    {}
func (*StartStoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{46}
}
func (m *StartStoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartStoryReq.Unmarshal(m, b)
}
func (m *StartStoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartStoryReq.Marshal(b, m, deterministic)
}
func (dst *StartStoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartStoryReq.Merge(dst, src)
}
func (m *StartStoryReq) XXX_Size() int {
	return xxx_messageInfo_StartStoryReq.Size(m)
}
func (m *StartStoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartStoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartStoryReq proto.InternalMessageInfo

func (m *StartStoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartStoryReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *StartStoryReq) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

type StartStoryResp struct {
	Ok                   bool     `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartStoryResp) Reset()         { *m = StartStoryResp{} }
func (m *StartStoryResp) String() string { return proto.CompactTextString(m) }
func (*StartStoryResp) ProtoMessage()    {}
func (*StartStoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{47}
}
func (m *StartStoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartStoryResp.Unmarshal(m, b)
}
func (m *StartStoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartStoryResp.Marshal(b, m, deterministic)
}
func (dst *StartStoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartStoryResp.Merge(dst, src)
}
func (m *StartStoryResp) XXX_Size() int {
	return xxx_messageInfo_StartStoryResp.Size(m)
}
func (m *StartStoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartStoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartStoryResp proto.InternalMessageInfo

func (m *StartStoryResp) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

type GetStoryInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	StoryId              uint32   `protobuf:"varint,3,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryInfoReq) Reset()         { *m = GetStoryInfoReq{} }
func (m *GetStoryInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetStoryInfoReq) ProtoMessage()    {}
func (*GetStoryInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{48}
}
func (m *GetStoryInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryInfoReq.Unmarshal(m, b)
}
func (m *GetStoryInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetStoryInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryInfoReq.Merge(dst, src)
}
func (m *GetStoryInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetStoryInfoReq.Size(m)
}
func (m *GetStoryInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryInfoReq proto.InternalMessageInfo

func (m *GetStoryInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStoryInfoReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetStoryInfoReq) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

type GetStoryInfoResp struct {
	TriggerTs            int64    `protobuf:"varint,1,opt,name=trigger_ts,json=triggerTs,proto3" json:"trigger_ts,omitempty"`
	IsExpire             bool     `protobuf:"varint,2,opt,name=is_expire,json=isExpire,proto3" json:"is_expire,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	StoryName            string   `protobuf:"bytes,4,opt,name=story_name,json=storyName,proto3" json:"story_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryInfoResp) Reset()         { *m = GetStoryInfoResp{} }
func (m *GetStoryInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetStoryInfoResp) ProtoMessage()    {}
func (*GetStoryInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{49}
}
func (m *GetStoryInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryInfoResp.Unmarshal(m, b)
}
func (m *GetStoryInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetStoryInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryInfoResp.Merge(dst, src)
}
func (m *GetStoryInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetStoryInfoResp.Size(m)
}
func (m *GetStoryInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryInfoResp proto.InternalMessageInfo

func (m *GetStoryInfoResp) GetTriggerTs() int64 {
	if m != nil {
		return m.TriggerTs
	}
	return 0
}

func (m *GetStoryInfoResp) GetIsExpire() bool {
	if m != nil {
		return m.IsExpire
	}
	return false
}

func (m *GetStoryInfoResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetStoryInfoResp) GetStoryName() string {
	if m != nil {
		return m.StoryName
	}
	return ""
}

type SendAnimatedMemeReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	AnimatedMeme         string   `protobuf:"bytes,3,opt,name=animated_meme,json=animatedMeme,proto3" json:"animated_meme,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendAnimatedMemeReq) Reset()         { *m = SendAnimatedMemeReq{} }
func (m *SendAnimatedMemeReq) String() string { return proto.CompactTextString(m) }
func (*SendAnimatedMemeReq) ProtoMessage()    {}
func (*SendAnimatedMemeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{50}
}
func (m *SendAnimatedMemeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendAnimatedMemeReq.Unmarshal(m, b)
}
func (m *SendAnimatedMemeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendAnimatedMemeReq.Marshal(b, m, deterministic)
}
func (dst *SendAnimatedMemeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendAnimatedMemeReq.Merge(dst, src)
}
func (m *SendAnimatedMemeReq) XXX_Size() int {
	return xxx_messageInfo_SendAnimatedMemeReq.Size(m)
}
func (m *SendAnimatedMemeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendAnimatedMemeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendAnimatedMemeReq proto.InternalMessageInfo

func (m *SendAnimatedMemeReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SendAnimatedMemeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendAnimatedMemeReq) GetAnimatedMeme() string {
	if m != nil {
		return m.AnimatedMeme
	}
	return ""
}

type SendAnimatedMemeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendAnimatedMemeResp) Reset()         { *m = SendAnimatedMemeResp{} }
func (m *SendAnimatedMemeResp) String() string { return proto.CompactTextString(m) }
func (*SendAnimatedMemeResp) ProtoMessage()    {}
func (*SendAnimatedMemeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{51}
}
func (m *SendAnimatedMemeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendAnimatedMemeResp.Unmarshal(m, b)
}
func (m *SendAnimatedMemeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendAnimatedMemeResp.Marshal(b, m, deterministic)
}
func (dst *SendAnimatedMemeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendAnimatedMemeResp.Merge(dst, src)
}
func (m *SendAnimatedMemeResp) XXX_Size() int {
	return xxx_messageInfo_SendAnimatedMemeResp.Size(m)
}
func (m *SendAnimatedMemeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendAnimatedMemeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendAnimatedMemeResp proto.InternalMessageInfo

type SetRelationshipReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	RelationshipId       uint32   `protobuf:"varint,3,opt,name=relationship_id,json=relationshipId,proto3" json:"relationship_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetRelationshipReq) Reset()         { *m = SetRelationshipReq{} }
func (m *SetRelationshipReq) String() string { return proto.CompactTextString(m) }
func (*SetRelationshipReq) ProtoMessage()    {}
func (*SetRelationshipReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{52}
}
func (m *SetRelationshipReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRelationshipReq.Unmarshal(m, b)
}
func (m *SetRelationshipReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRelationshipReq.Marshal(b, m, deterministic)
}
func (dst *SetRelationshipReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRelationshipReq.Merge(dst, src)
}
func (m *SetRelationshipReq) XXX_Size() int {
	return xxx_messageInfo_SetRelationshipReq.Size(m)
}
func (m *SetRelationshipReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRelationshipReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetRelationshipReq proto.InternalMessageInfo

func (m *SetRelationshipReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SetRelationshipReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetRelationshipReq) GetRelationshipId() uint32 {
	if m != nil {
		return m.RelationshipId
	}
	return 0
}

type SetRelationshipResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetRelationshipResp) Reset()         { *m = SetRelationshipResp{} }
func (m *SetRelationshipResp) String() string { return proto.CompactTextString(m) }
func (*SetRelationshipResp) ProtoMessage()    {}
func (*SetRelationshipResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{53}
}
func (m *SetRelationshipResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRelationshipResp.Unmarshal(m, b)
}
func (m *SetRelationshipResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRelationshipResp.Marshal(b, m, deterministic)
}
func (dst *SetRelationshipResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRelationshipResp.Merge(dst, src)
}
func (m *SetRelationshipResp) XXX_Size() int {
	return xxx_messageInfo_SetRelationshipResp.Size(m)
}
func (m *SetRelationshipResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRelationshipResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetRelationshipResp proto.InternalMessageInfo

type AddBlackListReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBlackListReq) Reset()         { *m = AddBlackListReq{} }
func (m *AddBlackListReq) String() string { return proto.CompactTextString(m) }
func (*AddBlackListReq) ProtoMessage()    {}
func (*AddBlackListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{54}
}
func (m *AddBlackListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBlackListReq.Unmarshal(m, b)
}
func (m *AddBlackListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBlackListReq.Marshal(b, m, deterministic)
}
func (dst *AddBlackListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBlackListReq.Merge(dst, src)
}
func (m *AddBlackListReq) XXX_Size() int {
	return xxx_messageInfo_AddBlackListReq.Size(m)
}
func (m *AddBlackListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBlackListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBlackListReq proto.InternalMessageInfo

func (m *AddBlackListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type AddBlackListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBlackListResp) Reset()         { *m = AddBlackListResp{} }
func (m *AddBlackListResp) String() string { return proto.CompactTextString(m) }
func (*AddBlackListResp) ProtoMessage()    {}
func (*AddBlackListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{55}
}
func (m *AddBlackListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBlackListResp.Unmarshal(m, b)
}
func (m *AddBlackListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBlackListResp.Marshal(b, m, deterministic)
}
func (dst *AddBlackListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBlackListResp.Merge(dst, src)
}
func (m *AddBlackListResp) XXX_Size() int {
	return xxx_messageInfo_AddBlackListResp.Size(m)
}
func (m *AddBlackListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBlackListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBlackListResp proto.InternalMessageInfo

type DelBlackListReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBlackListReq) Reset()         { *m = DelBlackListReq{} }
func (m *DelBlackListReq) String() string { return proto.CompactTextString(m) }
func (*DelBlackListReq) ProtoMessage()    {}
func (*DelBlackListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{56}
}
func (m *DelBlackListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBlackListReq.Unmarshal(m, b)
}
func (m *DelBlackListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBlackListReq.Marshal(b, m, deterministic)
}
func (dst *DelBlackListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBlackListReq.Merge(dst, src)
}
func (m *DelBlackListReq) XXX_Size() int {
	return xxx_messageInfo_DelBlackListReq.Size(m)
}
func (m *DelBlackListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBlackListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBlackListReq proto.InternalMessageInfo

func (m *DelBlackListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type DelBlackListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBlackListResp) Reset()         { *m = DelBlackListResp{} }
func (m *DelBlackListResp) String() string { return proto.CompactTextString(m) }
func (*DelBlackListResp) ProtoMessage()    {}
func (*DelBlackListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{57}
}
func (m *DelBlackListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBlackListResp.Unmarshal(m, b)
}
func (m *DelBlackListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBlackListResp.Marshal(b, m, deterministic)
}
func (dst *DelBlackListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBlackListResp.Merge(dst, src)
}
func (m *DelBlackListResp) XXX_Size() int {
	return xxx_messageInfo_DelBlackListResp.Size(m)
}
func (m *DelBlackListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBlackListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBlackListResp proto.InternalMessageInfo

type ReplyTextFormatReq struct {
	Text                 string                        `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	FormatType           ReplyTextFormatReq_FormatType `protobuf:"varint,2,opt,name=format_type,json=formatType,proto3,enum=rcmd.rcmd_ai_partner.ReplyTextFormatReq_FormatType" json:"format_type,omitempty"`
	LastReply            string                        `protobuf:"bytes,3,opt,name=last_reply,json=lastReply,proto3" json:"last_reply,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *ReplyTextFormatReq) Reset()         { *m = ReplyTextFormatReq{} }
func (m *ReplyTextFormatReq) String() string { return proto.CompactTextString(m) }
func (*ReplyTextFormatReq) ProtoMessage()    {}
func (*ReplyTextFormatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{58}
}
func (m *ReplyTextFormatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplyTextFormatReq.Unmarshal(m, b)
}
func (m *ReplyTextFormatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplyTextFormatReq.Marshal(b, m, deterministic)
}
func (dst *ReplyTextFormatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplyTextFormatReq.Merge(dst, src)
}
func (m *ReplyTextFormatReq) XXX_Size() int {
	return xxx_messageInfo_ReplyTextFormatReq.Size(m)
}
func (m *ReplyTextFormatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplyTextFormatReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReplyTextFormatReq proto.InternalMessageInfo

func (m *ReplyTextFormatReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ReplyTextFormatReq) GetFormatType() ReplyTextFormatReq_FormatType {
	if m != nil {
		return m.FormatType
	}
	return ReplyTextFormatReq_FormatType_Default
}

func (m *ReplyTextFormatReq) GetLastReply() string {
	if m != nil {
		return m.LastReply
	}
	return ""
}

type ReplyTextFormatResp struct {
	Texts                []string `protobuf:"bytes,1,rep,name=texts,proto3" json:"texts,omitempty"`
	DelayTimes           []int64  `protobuf:"varint,2,rep,packed,name=delay_times,json=delayTimes,proto3" json:"delay_times,omitempty"`
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReplyTextFormatResp) Reset()         { *m = ReplyTextFormatResp{} }
func (m *ReplyTextFormatResp) String() string { return proto.CompactTextString(m) }
func (*ReplyTextFormatResp) ProtoMessage()    {}
func (*ReplyTextFormatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{59}
}
func (m *ReplyTextFormatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplyTextFormatResp.Unmarshal(m, b)
}
func (m *ReplyTextFormatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplyTextFormatResp.Marshal(b, m, deterministic)
}
func (dst *ReplyTextFormatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplyTextFormatResp.Merge(dst, src)
}
func (m *ReplyTextFormatResp) XXX_Size() int {
	return xxx_messageInfo_ReplyTextFormatResp.Size(m)
}
func (m *ReplyTextFormatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplyTextFormatResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReplyTextFormatResp proto.InternalMessageInfo

func (m *ReplyTextFormatResp) GetTexts() []string {
	if m != nil {
		return m.Texts
	}
	return nil
}

func (m *ReplyTextFormatResp) GetDelayTimes() []int64 {
	if m != nil {
		return m.DelayTimes
	}
	return nil
}

func (m *ReplyTextFormatResp) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

type TriggerReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TriggerReq) Reset()         { *m = TriggerReq{} }
func (m *TriggerReq) String() string { return proto.CompactTextString(m) }
func (*TriggerReq) ProtoMessage()    {}
func (*TriggerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{60}
}
func (m *TriggerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerReq.Unmarshal(m, b)
}
func (m *TriggerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerReq.Marshal(b, m, deterministic)
}
func (dst *TriggerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerReq.Merge(dst, src)
}
func (m *TriggerReq) XXX_Size() int {
	return xxx_messageInfo_TriggerReq.Size(m)
}
func (m *TriggerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerReq.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerReq proto.InternalMessageInfo

func (m *TriggerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type TriggerResp struct {
	IsOpen               bool     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	IsOpenNoRole         bool     `protobuf:"varint,2,opt,name=is_open_no_role,json=isOpenNoRole,proto3" json:"is_open_no_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TriggerResp) Reset()         { *m = TriggerResp{} }
func (m *TriggerResp) String() string { return proto.CompactTextString(m) }
func (*TriggerResp) ProtoMessage()    {}
func (*TriggerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{61}
}
func (m *TriggerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerResp.Unmarshal(m, b)
}
func (m *TriggerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerResp.Marshal(b, m, deterministic)
}
func (dst *TriggerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerResp.Merge(dst, src)
}
func (m *TriggerResp) XXX_Size() int {
	return xxx_messageInfo_TriggerResp.Size(m)
}
func (m *TriggerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerResp.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerResp proto.InternalMessageInfo

func (m *TriggerResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *TriggerResp) GetIsOpenNoRole() bool {
	if m != nil {
		return m.IsOpenNoRole
	}
	return false
}

type SetAIPartnerChattingStatusReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32         `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Status               ChattingStatus `protobuf:"varint,3,opt,name=status,proto3,enum=rcmd.rcmd_ai_partner.ChattingStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetAIPartnerChattingStatusReq) Reset()         { *m = SetAIPartnerChattingStatusReq{} }
func (m *SetAIPartnerChattingStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetAIPartnerChattingStatusReq) ProtoMessage()    {}
func (*SetAIPartnerChattingStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{62}
}
func (m *SetAIPartnerChattingStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAIPartnerChattingStatusReq.Unmarshal(m, b)
}
func (m *SetAIPartnerChattingStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAIPartnerChattingStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetAIPartnerChattingStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAIPartnerChattingStatusReq.Merge(dst, src)
}
func (m *SetAIPartnerChattingStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetAIPartnerChattingStatusReq.Size(m)
}
func (m *SetAIPartnerChattingStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAIPartnerChattingStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAIPartnerChattingStatusReq proto.InternalMessageInfo

func (m *SetAIPartnerChattingStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetAIPartnerChattingStatusReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SetAIPartnerChattingStatusReq) GetStatus() ChattingStatus {
	if m != nil {
		return m.Status
	}
	return ChattingStatus_Chatting
}

type SetAIPartnerChattingStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAIPartnerChattingStatusResp) Reset()         { *m = SetAIPartnerChattingStatusResp{} }
func (m *SetAIPartnerChattingStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetAIPartnerChattingStatusResp) ProtoMessage()    {}
func (*SetAIPartnerChattingStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{63}
}
func (m *SetAIPartnerChattingStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAIPartnerChattingStatusResp.Unmarshal(m, b)
}
func (m *SetAIPartnerChattingStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAIPartnerChattingStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetAIPartnerChattingStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAIPartnerChattingStatusResp.Merge(dst, src)
}
func (m *SetAIPartnerChattingStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetAIPartnerChattingStatusResp.Size(m)
}
func (m *SetAIPartnerChattingStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAIPartnerChattingStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAIPartnerChattingStatusResp proto.InternalMessageInfo

type SetAIPartnerInfoReq struct {
	Uid                  uint32                           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32                           `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                           `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	CallName             string                           `protobuf:"bytes,4,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	Relationship         SetAIPartnerInfoReq_Relationship `protobuf:"varint,5,opt,name=relationship,proto3,enum=rcmd.rcmd_ai_partner.SetAIPartnerInfoReq_Relationship" json:"relationship,omitempty"`
	Role                 *AIRole                          `protobuf:"bytes,6,opt,name=role,proto3" json:"role,omitempty"`
	Status               ChattingStatus                   `protobuf:"varint,7,opt,name=status,proto3,enum=rcmd.rcmd_ai_partner.ChattingStatus" json:"status,omitempty"`
	AiRoleType           AIRoleType                       `protobuf:"varint,8,opt,name=ai_role_type,json=aiRoleType,proto3,enum=rcmd.rcmd_ai_partner.AIRoleType" json:"ai_role_type,omitempty"`
	Source               uint32                           `protobuf:"varint,9,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *SetAIPartnerInfoReq) Reset()         { *m = SetAIPartnerInfoReq{} }
func (m *SetAIPartnerInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetAIPartnerInfoReq) ProtoMessage()    {}
func (*SetAIPartnerInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{64}
}
func (m *SetAIPartnerInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAIPartnerInfoReq.Unmarshal(m, b)
}
func (m *SetAIPartnerInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAIPartnerInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetAIPartnerInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAIPartnerInfoReq.Merge(dst, src)
}
func (m *SetAIPartnerInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetAIPartnerInfoReq.Size(m)
}
func (m *SetAIPartnerInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAIPartnerInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAIPartnerInfoReq proto.InternalMessageInfo

func (m *SetAIPartnerInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetAIPartnerInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SetAIPartnerInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SetAIPartnerInfoReq) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *SetAIPartnerInfoReq) GetRelationship() SetAIPartnerInfoReq_Relationship {
	if m != nil {
		return m.Relationship
	}
	return SetAIPartnerInfoReq_RelationshipUnknown
}

func (m *SetAIPartnerInfoReq) GetRole() *AIRole {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *SetAIPartnerInfoReq) GetStatus() ChattingStatus {
	if m != nil {
		return m.Status
	}
	return ChattingStatus_Chatting
}

func (m *SetAIPartnerInfoReq) GetAiRoleType() AIRoleType {
	if m != nil {
		return m.AiRoleType
	}
	return AIRoleType_AIRoleTypeDefault
}

func (m *SetAIPartnerInfoReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type AIRole struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// AI风格
	Style string `protobuf:"bytes,2,opt,name=style,proto3" json:"style,omitempty"`
	// AI性别 0:女 1:男
	Sex                  int32       `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Type                 AIRole_Type `protobuf:"varint,4,opt,name=type,proto3,enum=rcmd.rcmd_ai_partner.AIRole_Type" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AIRole) Reset()         { *m = AIRole{} }
func (m *AIRole) String() string { return proto.CompactTextString(m) }
func (*AIRole) ProtoMessage()    {}
func (*AIRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{65}
}
func (m *AIRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRole.Unmarshal(m, b)
}
func (m *AIRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRole.Marshal(b, m, deterministic)
}
func (dst *AIRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRole.Merge(dst, src)
}
func (m *AIRole) XXX_Size() int {
	return xxx_messageInfo_AIRole.Size(m)
}
func (m *AIRole) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRole.DiscardUnknown(m)
}

var xxx_messageInfo_AIRole proto.InternalMessageInfo

func (m *AIRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIRole) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *AIRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *AIRole) GetType() AIRole_Type {
	if m != nil {
		return m.Type
	}
	return AIRole_Type_Default
}

type SetAIPartnerInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAIPartnerInfoResp) Reset()         { *m = SetAIPartnerInfoResp{} }
func (m *SetAIPartnerInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetAIPartnerInfoResp) ProtoMessage()    {}
func (*SetAIPartnerInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{66}
}
func (m *SetAIPartnerInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAIPartnerInfoResp.Unmarshal(m, b)
}
func (m *SetAIPartnerInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAIPartnerInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetAIPartnerInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAIPartnerInfoResp.Merge(dst, src)
}
func (m *SetAIPartnerInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetAIPartnerInfoResp.Size(m)
}
func (m *SetAIPartnerInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAIPartnerInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAIPartnerInfoResp proto.InternalMessageInfo

type ReceiveMsgFromUserReq struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiPartnerId uint32 `protobuf:"varint,2,opt,name=ai_partner_id,json=aiPartnerId,proto3" json:"ai_partner_id,omitempty"`
	// 消息类型
	MsgType ReceiveMsgFromUserReq_MsgType `protobuf:"varint,3,opt,name=msg_type,json=msgType,proto3,enum=rcmd.rcmd_ai_partner.ReceiveMsgFromUserReq_MsgType" json:"msg_type,omitempty"`
	// 消息内容
	Content              string                   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Ext                  []byte                   `protobuf:"bytes,5,opt,name=ext,proto3" json:"ext,omitempty"`
	MsgId                string                   `protobuf:"bytes,6,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	AiRoleType           AIRoleType               `protobuf:"varint,7,opt,name=ai_role_type,json=aiRoleType,proto3,enum=rcmd.rcmd_ai_partner.AIRoleType" json:"ai_role_type,omitempty"`
	ImBusiType           uint32                   `protobuf:"varint,8,opt,name=im_busi_type,json=imBusiType,proto3" json:"im_busi_type,omitempty"`
	ImContentType        uint32                   `protobuf:"varint,9,opt,name=im_content_type,json=imContentType,proto3" json:"im_content_type,omitempty"`
	ImCmdType            uint32                   `protobuf:"varint,10,opt,name=im_cmd_type,json=imCmdType,proto3" json:"im_cmd_type,omitempty"`
	ExtraMap             map[string]string        `protobuf:"bytes,11,rep,name=extra_map,json=extraMap,proto3" json:"extra_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IsExactlyReachLimit  bool                     `protobuf:"varint,12,opt,name=is_exactly_reach_limit,json=isExactlyReachLimit,proto3" json:"is_exactly_reach_limit,omitempty"`
	InteractiveGameInfo  *partner_common.GameInfo `protobuf:"bytes,13,opt,name=interactive_game_info,json=interactiveGameInfo,proto3" json:"interactive_game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ReceiveMsgFromUserReq) Reset()         { *m = ReceiveMsgFromUserReq{} }
func (m *ReceiveMsgFromUserReq) String() string { return proto.CompactTextString(m) }
func (*ReceiveMsgFromUserReq) ProtoMessage()    {}
func (*ReceiveMsgFromUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{67}
}
func (m *ReceiveMsgFromUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveMsgFromUserReq.Unmarshal(m, b)
}
func (m *ReceiveMsgFromUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveMsgFromUserReq.Marshal(b, m, deterministic)
}
func (dst *ReceiveMsgFromUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveMsgFromUserReq.Merge(dst, src)
}
func (m *ReceiveMsgFromUserReq) XXX_Size() int {
	return xxx_messageInfo_ReceiveMsgFromUserReq.Size(m)
}
func (m *ReceiveMsgFromUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveMsgFromUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveMsgFromUserReq proto.InternalMessageInfo

func (m *ReceiveMsgFromUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReceiveMsgFromUserReq) GetAiPartnerId() uint32 {
	if m != nil {
		return m.AiPartnerId
	}
	return 0
}

func (m *ReceiveMsgFromUserReq) GetMsgType() ReceiveMsgFromUserReq_MsgType {
	if m != nil {
		return m.MsgType
	}
	return ReceiveMsgFromUserReq_MsgType_Unknown
}

func (m *ReceiveMsgFromUserReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ReceiveMsgFromUserReq) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *ReceiveMsgFromUserReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *ReceiveMsgFromUserReq) GetAiRoleType() AIRoleType {
	if m != nil {
		return m.AiRoleType
	}
	return AIRoleType_AIRoleTypeDefault
}

func (m *ReceiveMsgFromUserReq) GetImBusiType() uint32 {
	if m != nil {
		return m.ImBusiType
	}
	return 0
}

func (m *ReceiveMsgFromUserReq) GetImContentType() uint32 {
	if m != nil {
		return m.ImContentType
	}
	return 0
}

func (m *ReceiveMsgFromUserReq) GetImCmdType() uint32 {
	if m != nil {
		return m.ImCmdType
	}
	return 0
}

func (m *ReceiveMsgFromUserReq) GetExtraMap() map[string]string {
	if m != nil {
		return m.ExtraMap
	}
	return nil
}

func (m *ReceiveMsgFromUserReq) GetIsExactlyReachLimit() bool {
	if m != nil {
		return m.IsExactlyReachLimit
	}
	return false
}

func (m *ReceiveMsgFromUserReq) GetInteractiveGameInfo() *partner_common.GameInfo {
	if m != nil {
		return m.InteractiveGameInfo
	}
	return nil
}

type ReceiveMsgFromUserResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveMsgFromUserResp) Reset()         { *m = ReceiveMsgFromUserResp{} }
func (m *ReceiveMsgFromUserResp) String() string { return proto.CompactTextString(m) }
func (*ReceiveMsgFromUserResp) ProtoMessage()    {}
func (*ReceiveMsgFromUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{68}
}
func (m *ReceiveMsgFromUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveMsgFromUserResp.Unmarshal(m, b)
}
func (m *ReceiveMsgFromUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveMsgFromUserResp.Marshal(b, m, deterministic)
}
func (dst *ReceiveMsgFromUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveMsgFromUserResp.Merge(dst, src)
}
func (m *ReceiveMsgFromUserResp) XXX_Size() int {
	return xxx_messageInfo_ReceiveMsgFromUserResp.Size(m)
}
func (m *ReceiveMsgFromUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveMsgFromUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveMsgFromUserResp proto.InternalMessageInfo

type UserEnterChattingNotifyReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiPartnerId          uint32     `protobuf:"varint,2,opt,name=ai_partner_id,json=aiPartnerId,proto3" json:"ai_partner_id,omitempty"`
	AiRoleType           AIRoleType `protobuf:"varint,3,opt,name=ai_role_type,json=aiRoleType,proto3,enum=rcmd.rcmd_ai_partner.AIRoleType" json:"ai_role_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UserEnterChattingNotifyReq) Reset()         { *m = UserEnterChattingNotifyReq{} }
func (m *UserEnterChattingNotifyReq) String() string { return proto.CompactTextString(m) }
func (*UserEnterChattingNotifyReq) ProtoMessage()    {}
func (*UserEnterChattingNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{69}
}
func (m *UserEnterChattingNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEnterChattingNotifyReq.Unmarshal(m, b)
}
func (m *UserEnterChattingNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEnterChattingNotifyReq.Marshal(b, m, deterministic)
}
func (dst *UserEnterChattingNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEnterChattingNotifyReq.Merge(dst, src)
}
func (m *UserEnterChattingNotifyReq) XXX_Size() int {
	return xxx_messageInfo_UserEnterChattingNotifyReq.Size(m)
}
func (m *UserEnterChattingNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEnterChattingNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserEnterChattingNotifyReq proto.InternalMessageInfo

func (m *UserEnterChattingNotifyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserEnterChattingNotifyReq) GetAiPartnerId() uint32 {
	if m != nil {
		return m.AiPartnerId
	}
	return 0
}

func (m *UserEnterChattingNotifyReq) GetAiRoleType() AIRoleType {
	if m != nil {
		return m.AiRoleType
	}
	return AIRoleType_AIRoleTypeDefault
}

type UserEnterChattingNotifyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserEnterChattingNotifyResp) Reset()         { *m = UserEnterChattingNotifyResp{} }
func (m *UserEnterChattingNotifyResp) String() string { return proto.CompactTextString(m) }
func (*UserEnterChattingNotifyResp) ProtoMessage()    {}
func (*UserEnterChattingNotifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{70}
}
func (m *UserEnterChattingNotifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEnterChattingNotifyResp.Unmarshal(m, b)
}
func (m *UserEnterChattingNotifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEnterChattingNotifyResp.Marshal(b, m, deterministic)
}
func (dst *UserEnterChattingNotifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEnterChattingNotifyResp.Merge(dst, src)
}
func (m *UserEnterChattingNotifyResp) XXX_Size() int {
	return xxx_messageInfo_UserEnterChattingNotifyResp.Size(m)
}
func (m *UserEnterChattingNotifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEnterChattingNotifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserEnterChattingNotifyResp proto.InternalMessageInfo

type NewTriggerReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CliVersion           uint32   `protobuf:"varint,2,opt,name=cli_version,json=cliVersion,proto3" json:"cli_version,omitempty"`
	Source               uint32   `protobuf:"varint,3,opt,name=source,proto3" json:"source,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewTriggerReq) Reset()         { *m = NewTriggerReq{} }
func (m *NewTriggerReq) String() string { return proto.CompactTextString(m) }
func (*NewTriggerReq) ProtoMessage()    {}
func (*NewTriggerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{71}
}
func (m *NewTriggerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewTriggerReq.Unmarshal(m, b)
}
func (m *NewTriggerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewTriggerReq.Marshal(b, m, deterministic)
}
func (dst *NewTriggerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewTriggerReq.Merge(dst, src)
}
func (m *NewTriggerReq) XXX_Size() int {
	return xxx_messageInfo_NewTriggerReq.Size(m)
}
func (m *NewTriggerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NewTriggerReq.DiscardUnknown(m)
}

var xxx_messageInfo_NewTriggerReq proto.InternalMessageInfo

func (m *NewTriggerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NewTriggerReq) GetCliVersion() uint32 {
	if m != nil {
		return m.CliVersion
	}
	return 0
}

func (m *NewTriggerReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *NewTriggerReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

type NewTriggerResp struct {
	IsOpen               bool                     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	IsOpenNoRole         bool                     `protobuf:"varint,2,opt,name=is_open_no_role,json=isOpenNoRole,proto3" json:"is_open_no_role,omitempty"`
	EnableMultiRoles     bool                     `protobuf:"varint,3,opt,name=enable_multi_roles,json=enableMultiRoles,proto3" json:"enable_multi_roles,omitempty"`
	EnablePet            bool                     `protobuf:"varint,4,opt,name=enable_pet,json=enablePet,proto3" json:"enable_pet,omitempty"`
	UserView             *AIPartnerNormalUserView `protobuf:"bytes,5,opt,name=user_view,json=userView,proto3" json:"user_view,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *NewTriggerResp) Reset()         { *m = NewTriggerResp{} }
func (m *NewTriggerResp) String() string { return proto.CompactTextString(m) }
func (*NewTriggerResp) ProtoMessage()    {}
func (*NewTriggerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{72}
}
func (m *NewTriggerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewTriggerResp.Unmarshal(m, b)
}
func (m *NewTriggerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewTriggerResp.Marshal(b, m, deterministic)
}
func (dst *NewTriggerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewTriggerResp.Merge(dst, src)
}
func (m *NewTriggerResp) XXX_Size() int {
	return xxx_messageInfo_NewTriggerResp.Size(m)
}
func (m *NewTriggerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NewTriggerResp.DiscardUnknown(m)
}

var xxx_messageInfo_NewTriggerResp proto.InternalMessageInfo

func (m *NewTriggerResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *NewTriggerResp) GetIsOpenNoRole() bool {
	if m != nil {
		return m.IsOpenNoRole
	}
	return false
}

func (m *NewTriggerResp) GetEnableMultiRoles() bool {
	if m != nil {
		return m.EnableMultiRoles
	}
	return false
}

func (m *NewTriggerResp) GetEnablePet() bool {
	if m != nil {
		return m.EnablePet
	}
	return false
}

func (m *NewTriggerResp) GetUserView() *AIPartnerNormalUserView {
	if m != nil {
		return m.UserView
	}
	return nil
}

type DeletePartnerReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePartnerReq) Reset()         { *m = DeletePartnerReq{} }
func (m *DeletePartnerReq) String() string { return proto.CompactTextString(m) }
func (*DeletePartnerReq) ProtoMessage()    {}
func (*DeletePartnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{73}
}
func (m *DeletePartnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePartnerReq.Unmarshal(m, b)
}
func (m *DeletePartnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePartnerReq.Marshal(b, m, deterministic)
}
func (dst *DeletePartnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePartnerReq.Merge(dst, src)
}
func (m *DeletePartnerReq) XXX_Size() int {
	return xxx_messageInfo_DeletePartnerReq.Size(m)
}
func (m *DeletePartnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePartnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePartnerReq proto.InternalMessageInfo

func (m *DeletePartnerReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DeletePartnerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DeletePartnerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePartnerResp) Reset()         { *m = DeletePartnerResp{} }
func (m *DeletePartnerResp) String() string { return proto.CompactTextString(m) }
func (*DeletePartnerResp) ProtoMessage()    {}
func (*DeletePartnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{74}
}
func (m *DeletePartnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePartnerResp.Unmarshal(m, b)
}
func (m *DeletePartnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePartnerResp.Marshal(b, m, deterministic)
}
func (dst *DeletePartnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePartnerResp.Merge(dst, src)
}
func (m *DeletePartnerResp) XXX_Size() int {
	return xxx_messageInfo_DeletePartnerResp.Size(m)
}
func (m *DeletePartnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePartnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePartnerResp proto.InternalMessageInfo

type ShowContinueChatReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowContinueChatReq) Reset()         { *m = ShowContinueChatReq{} }
func (m *ShowContinueChatReq) String() string { return proto.CompactTextString(m) }
func (*ShowContinueChatReq) ProtoMessage()    {}
func (*ShowContinueChatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{75}
}
func (m *ShowContinueChatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowContinueChatReq.Unmarshal(m, b)
}
func (m *ShowContinueChatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowContinueChatReq.Marshal(b, m, deterministic)
}
func (dst *ShowContinueChatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowContinueChatReq.Merge(dst, src)
}
func (m *ShowContinueChatReq) XXX_Size() int {
	return xxx_messageInfo_ShowContinueChatReq.Size(m)
}
func (m *ShowContinueChatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowContinueChatReq.DiscardUnknown(m)
}

var xxx_messageInfo_ShowContinueChatReq proto.InternalMessageInfo

func (m *ShowContinueChatReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ShowContinueChatReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ShowContinueChatReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *ShowContinueChatReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

type ShowContinueChatRsp struct {
	Show                 bool     `protobuf:"varint,1,opt,name=show,proto3" json:"show,omitempty"`
	ChatLeftNum          uint32   `protobuf:"varint,2,opt,name=chat_left_num,json=chatLeftNum,proto3" json:"chat_left_num,omitempty"`
	ContinueRoundLeftNum uint32   `protobuf:"varint,3,opt,name=continue_round_left_num,json=continueRoundLeftNum,proto3" json:"continue_round_left_num,omitempty"`
	ContinueTotalLeftNum uint32   `protobuf:"varint,4,opt,name=continue_total_left_num,json=continueTotalLeftNum,proto3" json:"continue_total_left_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowContinueChatRsp) Reset()         { *m = ShowContinueChatRsp{} }
func (m *ShowContinueChatRsp) String() string { return proto.CompactTextString(m) }
func (*ShowContinueChatRsp) ProtoMessage()    {}
func (*ShowContinueChatRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{76}
}
func (m *ShowContinueChatRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowContinueChatRsp.Unmarshal(m, b)
}
func (m *ShowContinueChatRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowContinueChatRsp.Marshal(b, m, deterministic)
}
func (dst *ShowContinueChatRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowContinueChatRsp.Merge(dst, src)
}
func (m *ShowContinueChatRsp) XXX_Size() int {
	return xxx_messageInfo_ShowContinueChatRsp.Size(m)
}
func (m *ShowContinueChatRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowContinueChatRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ShowContinueChatRsp proto.InternalMessageInfo

func (m *ShowContinueChatRsp) GetShow() bool {
	if m != nil {
		return m.Show
	}
	return false
}

func (m *ShowContinueChatRsp) GetChatLeftNum() uint32 {
	if m != nil {
		return m.ChatLeftNum
	}
	return 0
}

func (m *ShowContinueChatRsp) GetContinueRoundLeftNum() uint32 {
	if m != nil {
		return m.ContinueRoundLeftNum
	}
	return 0
}

func (m *ShowContinueChatRsp) GetContinueTotalLeftNum() uint32 {
	if m != nil {
		return m.ContinueTotalLeftNum
	}
	return 0
}

type ContinueChatReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContinueChatReq) Reset()         { *m = ContinueChatReq{} }
func (m *ContinueChatReq) String() string { return proto.CompactTextString(m) }
func (*ContinueChatReq) ProtoMessage()    {}
func (*ContinueChatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{77}
}
func (m *ContinueChatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContinueChatReq.Unmarshal(m, b)
}
func (m *ContinueChatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContinueChatReq.Marshal(b, m, deterministic)
}
func (dst *ContinueChatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContinueChatReq.Merge(dst, src)
}
func (m *ContinueChatReq) XXX_Size() int {
	return xxx_messageInfo_ContinueChatReq.Size(m)
}
func (m *ContinueChatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ContinueChatReq.DiscardUnknown(m)
}

var xxx_messageInfo_ContinueChatReq proto.InternalMessageInfo

func (m *ContinueChatReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ContinueChatReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ContinueChatReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type ContinueChatRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContinueChatRsp) Reset()         { *m = ContinueChatRsp{} }
func (m *ContinueChatRsp) String() string { return proto.CompactTextString(m) }
func (*ContinueChatRsp) ProtoMessage()    {}
func (*ContinueChatRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{78}
}
func (m *ContinueChatRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContinueChatRsp.Unmarshal(m, b)
}
func (m *ContinueChatRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContinueChatRsp.Marshal(b, m, deterministic)
}
func (dst *ContinueChatRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContinueChatRsp.Merge(dst, src)
}
func (m *ContinueChatRsp) XXX_Size() int {
	return xxx_messageInfo_ContinueChatRsp.Size(m)
}
func (m *ContinueChatRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ContinueChatRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ContinueChatRsp proto.InternalMessageInfo

type GetPartnerOpenReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             string   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPartnerOpenReq) Reset()         { *m = GetPartnerOpenReq{} }
func (m *GetPartnerOpenReq) String() string { return proto.CompactTextString(m) }
func (*GetPartnerOpenReq) ProtoMessage()    {}
func (*GetPartnerOpenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{79}
}
func (m *GetPartnerOpenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerOpenReq.Unmarshal(m, b)
}
func (m *GetPartnerOpenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerOpenReq.Marshal(b, m, deterministic)
}
func (dst *GetPartnerOpenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerOpenReq.Merge(dst, src)
}
func (m *GetPartnerOpenReq) XXX_Size() int {
	return xxx_messageInfo_GetPartnerOpenReq.Size(m)
}
func (m *GetPartnerOpenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerOpenReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerOpenReq proto.InternalMessageInfo

func (m *GetPartnerOpenReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPartnerOpenReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetPartnerOpenReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type AIPartnerNormalUserView struct {
	IsOnline             uint32   `protobuf:"varint,1,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	Sex                  uint32   `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	RegTime              uint32   `protobuf:"varint,3,opt,name=reg_time,json=regTime,proto3" json:"reg_time,omitempty"`
	MarketId             uint32   `protobuf:"varint,4,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Nickname             string   `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Birthday             uint32   `protobuf:"varint,6,opt,name=birthday,proto3" json:"birthday,omitempty"`
	DeviceIdHex          string   `protobuf:"bytes,7,opt,name=device_id_hex,json=deviceIdHex,proto3" json:"device_id_hex,omitempty"`
	ClientType           uint32   `protobuf:"varint,8,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIPartnerNormalUserView) Reset()         { *m = AIPartnerNormalUserView{} }
func (m *AIPartnerNormalUserView) String() string { return proto.CompactTextString(m) }
func (*AIPartnerNormalUserView) ProtoMessage()    {}
func (*AIPartnerNormalUserView) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{80}
}
func (m *AIPartnerNormalUserView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPartnerNormalUserView.Unmarshal(m, b)
}
func (m *AIPartnerNormalUserView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPartnerNormalUserView.Marshal(b, m, deterministic)
}
func (dst *AIPartnerNormalUserView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPartnerNormalUserView.Merge(dst, src)
}
func (m *AIPartnerNormalUserView) XXX_Size() int {
	return xxx_messageInfo_AIPartnerNormalUserView.Size(m)
}
func (m *AIPartnerNormalUserView) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPartnerNormalUserView.DiscardUnknown(m)
}

var xxx_messageInfo_AIPartnerNormalUserView proto.InternalMessageInfo

func (m *AIPartnerNormalUserView) GetIsOnline() uint32 {
	if m != nil {
		return m.IsOnline
	}
	return 0
}

func (m *AIPartnerNormalUserView) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *AIPartnerNormalUserView) GetRegTime() uint32 {
	if m != nil {
		return m.RegTime
	}
	return 0
}

func (m *AIPartnerNormalUserView) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *AIPartnerNormalUserView) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *AIPartnerNormalUserView) GetBirthday() uint32 {
	if m != nil {
		return m.Birthday
	}
	return 0
}

func (m *AIPartnerNormalUserView) GetDeviceIdHex() string {
	if m != nil {
		return m.DeviceIdHex
	}
	return ""
}

func (m *AIPartnerNormalUserView) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetPartnerOpenRsp struct {
	IsOpen               bool                     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	UserView             *AIPartnerNormalUserView `protobuf:"bytes,2,opt,name=user_view,json=userView,proto3" json:"user_view,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetPartnerOpenRsp) Reset()         { *m = GetPartnerOpenRsp{} }
func (m *GetPartnerOpenRsp) String() string { return proto.CompactTextString(m) }
func (*GetPartnerOpenRsp) ProtoMessage()    {}
func (*GetPartnerOpenRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{81}
}
func (m *GetPartnerOpenRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerOpenRsp.Unmarshal(m, b)
}
func (m *GetPartnerOpenRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerOpenRsp.Marshal(b, m, deterministic)
}
func (dst *GetPartnerOpenRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerOpenRsp.Merge(dst, src)
}
func (m *GetPartnerOpenRsp) XXX_Size() int {
	return xxx_messageInfo_GetPartnerOpenRsp.Size(m)
}
func (m *GetPartnerOpenRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerOpenRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerOpenRsp proto.InternalMessageInfo

func (m *GetPartnerOpenRsp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *GetPartnerOpenRsp) GetUserView() *AIPartnerNormalUserView {
	if m != nil {
		return m.UserView
	}
	return nil
}

type ReceiveGroupMsgFromUserReq struct {
	Uid                  uint32                                          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GroupTemplateId      uint32                                          `protobuf:"varint,2,opt,name=group_template_id,json=groupTemplateId,proto3" json:"group_template_id,omitempty"`
	GroupInstanceId      uint32                                          `protobuf:"varint,3,opt,name=group_instance_id,json=groupInstanceId,proto3" json:"group_instance_id,omitempty"`
	MsgType              ReceiveGroupMsgFromUserReq_MsgType              `protobuf:"varint,4,opt,name=msg_type,json=msgType,proto3,enum=rcmd.rcmd_ai_partner.ReceiveGroupMsgFromUserReq_MsgType" json:"msg_type,omitempty"`
	Content              string                                          `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	Ext                  []byte                                          `protobuf:"bytes,6,opt,name=ext,proto3" json:"ext,omitempty"`
	SeqId                uint32                                          `protobuf:"varint,7,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	AiGroupType          AIGroupType                                     `protobuf:"varint,8,opt,name=ai_group_type,json=aiGroupType,proto3,enum=rcmd.rcmd_ai_partner.AIGroupType" json:"ai_group_type,omitempty"`
	TargetRoleIds        []uint32                                        `protobuf:"varint,9,rep,packed,name=target_role_ids,json=targetRoleIds,proto3" json:"target_role_ids,omitempty"`
	TargetUids           []uint32                                        `protobuf:"varint,10,rep,packed,name=target_uids,json=targetUids,proto3" json:"target_uids,omitempty"`
	UidToInfo            map[uint32]*ReceiveGroupMsgFromUserReq_UserInfo `protobuf:"bytes,11,rep,name=uid_to_info,json=uidToInfo,proto3" json:"uid_to_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ExtraMap             map[string]string                               `protobuf:"bytes,12,rep,name=extra_map,json=extraMap,proto3" json:"extra_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                        `json:"-"`
	XXX_unrecognized     []byte                                          `json:"-"`
	XXX_sizecache        int32                                           `json:"-"`
}

func (m *ReceiveGroupMsgFromUserReq) Reset()         { *m = ReceiveGroupMsgFromUserReq{} }
func (m *ReceiveGroupMsgFromUserReq) String() string { return proto.CompactTextString(m) }
func (*ReceiveGroupMsgFromUserReq) ProtoMessage()    {}
func (*ReceiveGroupMsgFromUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{82}
}
func (m *ReceiveGroupMsgFromUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveGroupMsgFromUserReq.Unmarshal(m, b)
}
func (m *ReceiveGroupMsgFromUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveGroupMsgFromUserReq.Marshal(b, m, deterministic)
}
func (dst *ReceiveGroupMsgFromUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveGroupMsgFromUserReq.Merge(dst, src)
}
func (m *ReceiveGroupMsgFromUserReq) XXX_Size() int {
	return xxx_messageInfo_ReceiveGroupMsgFromUserReq.Size(m)
}
func (m *ReceiveGroupMsgFromUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveGroupMsgFromUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveGroupMsgFromUserReq proto.InternalMessageInfo

func (m *ReceiveGroupMsgFromUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReceiveGroupMsgFromUserReq) GetGroupTemplateId() uint32 {
	if m != nil {
		return m.GroupTemplateId
	}
	return 0
}

func (m *ReceiveGroupMsgFromUserReq) GetGroupInstanceId() uint32 {
	if m != nil {
		return m.GroupInstanceId
	}
	return 0
}

func (m *ReceiveGroupMsgFromUserReq) GetMsgType() ReceiveGroupMsgFromUserReq_MsgType {
	if m != nil {
		return m.MsgType
	}
	return ReceiveGroupMsgFromUserReq_MsgType_Unknown
}

func (m *ReceiveGroupMsgFromUserReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ReceiveGroupMsgFromUserReq) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *ReceiveGroupMsgFromUserReq) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *ReceiveGroupMsgFromUserReq) GetAiGroupType() AIGroupType {
	if m != nil {
		return m.AiGroupType
	}
	return AIGroupType_AIGroupType_Unknown
}

func (m *ReceiveGroupMsgFromUserReq) GetTargetRoleIds() []uint32 {
	if m != nil {
		return m.TargetRoleIds
	}
	return nil
}

func (m *ReceiveGroupMsgFromUserReq) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

func (m *ReceiveGroupMsgFromUserReq) GetUidToInfo() map[uint32]*ReceiveGroupMsgFromUserReq_UserInfo {
	if m != nil {
		return m.UidToInfo
	}
	return nil
}

func (m *ReceiveGroupMsgFromUserReq) GetExtraMap() map[string]string {
	if m != nil {
		return m.ExtraMap
	}
	return nil
}

type ReceiveGroupMsgFromUserReq_UserInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PlayRoleId           uint32   `protobuf:"varint,2,opt,name=play_role_id,json=playRoleId,proto3" json:"play_role_id,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveGroupMsgFromUserReq_UserInfo) Reset()         { *m = ReceiveGroupMsgFromUserReq_UserInfo{} }
func (m *ReceiveGroupMsgFromUserReq_UserInfo) String() string { return proto.CompactTextString(m) }
func (*ReceiveGroupMsgFromUserReq_UserInfo) ProtoMessage()    {}
func (*ReceiveGroupMsgFromUserReq_UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{82, 0}
}
func (m *ReceiveGroupMsgFromUserReq_UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveGroupMsgFromUserReq_UserInfo.Unmarshal(m, b)
}
func (m *ReceiveGroupMsgFromUserReq_UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveGroupMsgFromUserReq_UserInfo.Marshal(b, m, deterministic)
}
func (dst *ReceiveGroupMsgFromUserReq_UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveGroupMsgFromUserReq_UserInfo.Merge(dst, src)
}
func (m *ReceiveGroupMsgFromUserReq_UserInfo) XXX_Size() int {
	return xxx_messageInfo_ReceiveGroupMsgFromUserReq_UserInfo.Size(m)
}
func (m *ReceiveGroupMsgFromUserReq_UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveGroupMsgFromUserReq_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveGroupMsgFromUserReq_UserInfo proto.InternalMessageInfo

func (m *ReceiveGroupMsgFromUserReq_UserInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ReceiveGroupMsgFromUserReq_UserInfo) GetPlayRoleId() uint32 {
	if m != nil {
		return m.PlayRoleId
	}
	return 0
}

func (m *ReceiveGroupMsgFromUserReq_UserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type ReceiveGroupMsgFromUserResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveGroupMsgFromUserResp) Reset()         { *m = ReceiveGroupMsgFromUserResp{} }
func (m *ReceiveGroupMsgFromUserResp) String() string { return proto.CompactTextString(m) }
func (*ReceiveGroupMsgFromUserResp) ProtoMessage()    {}
func (*ReceiveGroupMsgFromUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{83}
}
func (m *ReceiveGroupMsgFromUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveGroupMsgFromUserResp.Unmarshal(m, b)
}
func (m *ReceiveGroupMsgFromUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveGroupMsgFromUserResp.Marshal(b, m, deterministic)
}
func (dst *ReceiveGroupMsgFromUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveGroupMsgFromUserResp.Merge(dst, src)
}
func (m *ReceiveGroupMsgFromUserResp) XXX_Size() int {
	return xxx_messageInfo_ReceiveGroupMsgFromUserResp.Size(m)
}
func (m *ReceiveGroupMsgFromUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveGroupMsgFromUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveGroupMsgFromUserResp proto.InternalMessageInfo

type UserGiftInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value                float64  `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserGiftInfo) Reset()         { *m = UserGiftInfo{} }
func (m *UserGiftInfo) String() string { return proto.CompactTextString(m) }
func (*UserGiftInfo) ProtoMessage()    {}
func (*UserGiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{84}
}
func (m *UserGiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGiftInfo.Unmarshal(m, b)
}
func (m *UserGiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGiftInfo.Marshal(b, m, deterministic)
}
func (dst *UserGiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGiftInfo.Merge(dst, src)
}
func (m *UserGiftInfo) XXX_Size() int {
	return xxx_messageInfo_UserGiftInfo.Size(m)
}
func (m *UserGiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserGiftInfo proto.InternalMessageInfo

func (m *UserGiftInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserGiftInfo) GetValue() float64 {
	if m != nil {
		return m.Value
	}
	return 0
}

type ReceiveSpecialMsgFromUserReq struct {
	Uid                  uint32                                      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32                                      `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	MsgType              ReceiveSpecialMsgFromUserReq_SpecialMsgType `protobuf:"varint,3,opt,name=msg_type,json=msgType,proto3,enum=rcmd.rcmd_ai_partner.ReceiveSpecialMsgFromUserReq_SpecialMsgType" json:"msg_type,omitempty"`
	GiftInfo             *UserGiftInfo                               `protobuf:"bytes,4,opt,name=gift_info,json=giftInfo,proto3" json:"gift_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                    `json:"-"`
	XXX_unrecognized     []byte                                      `json:"-"`
	XXX_sizecache        int32                                       `json:"-"`
}

func (m *ReceiveSpecialMsgFromUserReq) Reset()         { *m = ReceiveSpecialMsgFromUserReq{} }
func (m *ReceiveSpecialMsgFromUserReq) String() string { return proto.CompactTextString(m) }
func (*ReceiveSpecialMsgFromUserReq) ProtoMessage()    {}
func (*ReceiveSpecialMsgFromUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{85}
}
func (m *ReceiveSpecialMsgFromUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveSpecialMsgFromUserReq.Unmarshal(m, b)
}
func (m *ReceiveSpecialMsgFromUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveSpecialMsgFromUserReq.Marshal(b, m, deterministic)
}
func (dst *ReceiveSpecialMsgFromUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveSpecialMsgFromUserReq.Merge(dst, src)
}
func (m *ReceiveSpecialMsgFromUserReq) XXX_Size() int {
	return xxx_messageInfo_ReceiveSpecialMsgFromUserReq.Size(m)
}
func (m *ReceiveSpecialMsgFromUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveSpecialMsgFromUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveSpecialMsgFromUserReq proto.InternalMessageInfo

func (m *ReceiveSpecialMsgFromUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReceiveSpecialMsgFromUserReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ReceiveSpecialMsgFromUserReq) GetMsgType() ReceiveSpecialMsgFromUserReq_SpecialMsgType {
	if m != nil {
		return m.MsgType
	}
	return ReceiveSpecialMsgFromUserReq_Unknown
}

func (m *ReceiveSpecialMsgFromUserReq) GetGiftInfo() *UserGiftInfo {
	if m != nil {
		return m.GiftInfo
	}
	return nil
}

type ReceiveSpecialMsgFromUserResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveSpecialMsgFromUserResp) Reset()         { *m = ReceiveSpecialMsgFromUserResp{} }
func (m *ReceiveSpecialMsgFromUserResp) String() string { return proto.CompactTextString(m) }
func (*ReceiveSpecialMsgFromUserResp) ProtoMessage()    {}
func (*ReceiveSpecialMsgFromUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{86}
}
func (m *ReceiveSpecialMsgFromUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveSpecialMsgFromUserResp.Unmarshal(m, b)
}
func (m *ReceiveSpecialMsgFromUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveSpecialMsgFromUserResp.Marshal(b, m, deterministic)
}
func (dst *ReceiveSpecialMsgFromUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveSpecialMsgFromUserResp.Merge(dst, src)
}
func (m *ReceiveSpecialMsgFromUserResp) XXX_Size() int {
	return xxx_messageInfo_ReceiveSpecialMsgFromUserResp.Size(m)
}
func (m *ReceiveSpecialMsgFromUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveSpecialMsgFromUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveSpecialMsgFromUserResp proto.InternalMessageInfo

type AISendMsgToGroupReq struct {
	GroupTemplateId      uint32                                   `protobuf:"varint,1,opt,name=group_template_id,json=groupTemplateId,proto3" json:"group_template_id,omitempty"`
	GroupInstanceId      uint32                                   `protobuf:"varint,2,opt,name=group_instance_id,json=groupInstanceId,proto3" json:"group_instance_id,omitempty"`
	SeqId                uint32                                   `protobuf:"varint,3,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	UidToInfo            map[uint32]*AISendMsgToGroupReq_UserInfo `protobuf:"bytes,4,rep,name=uid_to_info,json=uidToInfo,proto3" json:"uid_to_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TargetUids           []uint32                                 `protobuf:"varint,5,rep,packed,name=target_uids,json=targetUids,proto3" json:"target_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *AISendMsgToGroupReq) Reset()         { *m = AISendMsgToGroupReq{} }
func (m *AISendMsgToGroupReq) String() string { return proto.CompactTextString(m) }
func (*AISendMsgToGroupReq) ProtoMessage()    {}
func (*AISendMsgToGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{87}
}
func (m *AISendMsgToGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AISendMsgToGroupReq.Unmarshal(m, b)
}
func (m *AISendMsgToGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AISendMsgToGroupReq.Marshal(b, m, deterministic)
}
func (dst *AISendMsgToGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AISendMsgToGroupReq.Merge(dst, src)
}
func (m *AISendMsgToGroupReq) XXX_Size() int {
	return xxx_messageInfo_AISendMsgToGroupReq.Size(m)
}
func (m *AISendMsgToGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AISendMsgToGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_AISendMsgToGroupReq proto.InternalMessageInfo

func (m *AISendMsgToGroupReq) GetGroupTemplateId() uint32 {
	if m != nil {
		return m.GroupTemplateId
	}
	return 0
}

func (m *AISendMsgToGroupReq) GetGroupInstanceId() uint32 {
	if m != nil {
		return m.GroupInstanceId
	}
	return 0
}

func (m *AISendMsgToGroupReq) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *AISendMsgToGroupReq) GetUidToInfo() map[uint32]*AISendMsgToGroupReq_UserInfo {
	if m != nil {
		return m.UidToInfo
	}
	return nil
}

func (m *AISendMsgToGroupReq) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

type AISendMsgToGroupReq_UserInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PlayRoleId           uint32   `protobuf:"varint,2,opt,name=play_role_id,json=playRoleId,proto3" json:"play_role_id,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AISendMsgToGroupReq_UserInfo) Reset()         { *m = AISendMsgToGroupReq_UserInfo{} }
func (m *AISendMsgToGroupReq_UserInfo) String() string { return proto.CompactTextString(m) }
func (*AISendMsgToGroupReq_UserInfo) ProtoMessage()    {}
func (*AISendMsgToGroupReq_UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{87, 0}
}
func (m *AISendMsgToGroupReq_UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AISendMsgToGroupReq_UserInfo.Unmarshal(m, b)
}
func (m *AISendMsgToGroupReq_UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AISendMsgToGroupReq_UserInfo.Marshal(b, m, deterministic)
}
func (dst *AISendMsgToGroupReq_UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AISendMsgToGroupReq_UserInfo.Merge(dst, src)
}
func (m *AISendMsgToGroupReq_UserInfo) XXX_Size() int {
	return xxx_messageInfo_AISendMsgToGroupReq_UserInfo.Size(m)
}
func (m *AISendMsgToGroupReq_UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AISendMsgToGroupReq_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AISendMsgToGroupReq_UserInfo proto.InternalMessageInfo

func (m *AISendMsgToGroupReq_UserInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AISendMsgToGroupReq_UserInfo) GetPlayRoleId() uint32 {
	if m != nil {
		return m.PlayRoleId
	}
	return 0
}

func (m *AISendMsgToGroupReq_UserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type AISendMsgToGroupResp struct {
	ErrMsg               string   `protobuf:"bytes,1,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AISendMsgToGroupResp) Reset()         { *m = AISendMsgToGroupResp{} }
func (m *AISendMsgToGroupResp) String() string { return proto.CompactTextString(m) }
func (*AISendMsgToGroupResp) ProtoMessage()    {}
func (*AISendMsgToGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{88}
}
func (m *AISendMsgToGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AISendMsgToGroupResp.Unmarshal(m, b)
}
func (m *AISendMsgToGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AISendMsgToGroupResp.Marshal(b, m, deterministic)
}
func (dst *AISendMsgToGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AISendMsgToGroupResp.Merge(dst, src)
}
func (m *AISendMsgToGroupResp) XXX_Size() int {
	return xxx_messageInfo_AISendMsgToGroupResp.Size(m)
}
func (m *AISendMsgToGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AISendMsgToGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_AISendMsgToGroupResp proto.InternalMessageInfo

func (m *AISendMsgToGroupResp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type AIBatchSendMsgToGroupReq struct {
	GroupTemplateId      uint32                                        `protobuf:"varint,1,opt,name=group_template_id,json=groupTemplateId,proto3" json:"group_template_id,omitempty"`
	GroupInstanceId      uint32                                        `protobuf:"varint,2,opt,name=group_instance_id,json=groupInstanceId,proto3" json:"group_instance_id,omitempty"`
	UidToInfo            map[uint32]*AIBatchSendMsgToGroupReq_UserInfo `protobuf:"bytes,3,rep,name=uid_to_info,json=uidToInfo,proto3" json:"uid_to_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TargetUidsList       []*AIBatchSendMsgToGroupReq_TargetUidList     `protobuf:"bytes,4,rep,name=target_uids_list,json=targetUidsList,proto3" json:"target_uids_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *AIBatchSendMsgToGroupReq) Reset()         { *m = AIBatchSendMsgToGroupReq{} }
func (m *AIBatchSendMsgToGroupReq) String() string { return proto.CompactTextString(m) }
func (*AIBatchSendMsgToGroupReq) ProtoMessage()    {}
func (*AIBatchSendMsgToGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{89}
}
func (m *AIBatchSendMsgToGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIBatchSendMsgToGroupReq.Unmarshal(m, b)
}
func (m *AIBatchSendMsgToGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIBatchSendMsgToGroupReq.Marshal(b, m, deterministic)
}
func (dst *AIBatchSendMsgToGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIBatchSendMsgToGroupReq.Merge(dst, src)
}
func (m *AIBatchSendMsgToGroupReq) XXX_Size() int {
	return xxx_messageInfo_AIBatchSendMsgToGroupReq.Size(m)
}
func (m *AIBatchSendMsgToGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AIBatchSendMsgToGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_AIBatchSendMsgToGroupReq proto.InternalMessageInfo

func (m *AIBatchSendMsgToGroupReq) GetGroupTemplateId() uint32 {
	if m != nil {
		return m.GroupTemplateId
	}
	return 0
}

func (m *AIBatchSendMsgToGroupReq) GetGroupInstanceId() uint32 {
	if m != nil {
		return m.GroupInstanceId
	}
	return 0
}

func (m *AIBatchSendMsgToGroupReq) GetUidToInfo() map[uint32]*AIBatchSendMsgToGroupReq_UserInfo {
	if m != nil {
		return m.UidToInfo
	}
	return nil
}

func (m *AIBatchSendMsgToGroupReq) GetTargetUidsList() []*AIBatchSendMsgToGroupReq_TargetUidList {
	if m != nil {
		return m.TargetUidsList
	}
	return nil
}

type AIBatchSendMsgToGroupReq_UserInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PlayRoleId           uint32   `protobuf:"varint,2,opt,name=play_role_id,json=playRoleId,proto3" json:"play_role_id,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIBatchSendMsgToGroupReq_UserInfo) Reset()         { *m = AIBatchSendMsgToGroupReq_UserInfo{} }
func (m *AIBatchSendMsgToGroupReq_UserInfo) String() string { return proto.CompactTextString(m) }
func (*AIBatchSendMsgToGroupReq_UserInfo) ProtoMessage()    {}
func (*AIBatchSendMsgToGroupReq_UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{89, 0}
}
func (m *AIBatchSendMsgToGroupReq_UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIBatchSendMsgToGroupReq_UserInfo.Unmarshal(m, b)
}
func (m *AIBatchSendMsgToGroupReq_UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIBatchSendMsgToGroupReq_UserInfo.Marshal(b, m, deterministic)
}
func (dst *AIBatchSendMsgToGroupReq_UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIBatchSendMsgToGroupReq_UserInfo.Merge(dst, src)
}
func (m *AIBatchSendMsgToGroupReq_UserInfo) XXX_Size() int {
	return xxx_messageInfo_AIBatchSendMsgToGroupReq_UserInfo.Size(m)
}
func (m *AIBatchSendMsgToGroupReq_UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AIBatchSendMsgToGroupReq_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AIBatchSendMsgToGroupReq_UserInfo proto.InternalMessageInfo

func (m *AIBatchSendMsgToGroupReq_UserInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIBatchSendMsgToGroupReq_UserInfo) GetPlayRoleId() uint32 {
	if m != nil {
		return m.PlayRoleId
	}
	return 0
}

func (m *AIBatchSendMsgToGroupReq_UserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type AIBatchSendMsgToGroupReq_TargetUidList struct {
	TargetUids           []uint32 `protobuf:"varint,1,rep,packed,name=target_uids,json=targetUids,proto3" json:"target_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIBatchSendMsgToGroupReq_TargetUidList) Reset() {
	*m = AIBatchSendMsgToGroupReq_TargetUidList{}
}
func (m *AIBatchSendMsgToGroupReq_TargetUidList) String() string { return proto.CompactTextString(m) }
func (*AIBatchSendMsgToGroupReq_TargetUidList) ProtoMessage()    {}
func (*AIBatchSendMsgToGroupReq_TargetUidList) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{89, 1}
}
func (m *AIBatchSendMsgToGroupReq_TargetUidList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIBatchSendMsgToGroupReq_TargetUidList.Unmarshal(m, b)
}
func (m *AIBatchSendMsgToGroupReq_TargetUidList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIBatchSendMsgToGroupReq_TargetUidList.Marshal(b, m, deterministic)
}
func (dst *AIBatchSendMsgToGroupReq_TargetUidList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIBatchSendMsgToGroupReq_TargetUidList.Merge(dst, src)
}
func (m *AIBatchSendMsgToGroupReq_TargetUidList) XXX_Size() int {
	return xxx_messageInfo_AIBatchSendMsgToGroupReq_TargetUidList.Size(m)
}
func (m *AIBatchSendMsgToGroupReq_TargetUidList) XXX_DiscardUnknown() {
	xxx_messageInfo_AIBatchSendMsgToGroupReq_TargetUidList.DiscardUnknown(m)
}

var xxx_messageInfo_AIBatchSendMsgToGroupReq_TargetUidList proto.InternalMessageInfo

func (m *AIBatchSendMsgToGroupReq_TargetUidList) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

type AIBatchSendMsgToGroupResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIBatchSendMsgToGroupResp) Reset()         { *m = AIBatchSendMsgToGroupResp{} }
func (m *AIBatchSendMsgToGroupResp) String() string { return proto.CompactTextString(m) }
func (*AIBatchSendMsgToGroupResp) ProtoMessage()    {}
func (*AIBatchSendMsgToGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{90}
}
func (m *AIBatchSendMsgToGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIBatchSendMsgToGroupResp.Unmarshal(m, b)
}
func (m *AIBatchSendMsgToGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIBatchSendMsgToGroupResp.Marshal(b, m, deterministic)
}
func (dst *AIBatchSendMsgToGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIBatchSendMsgToGroupResp.Merge(dst, src)
}
func (m *AIBatchSendMsgToGroupResp) XXX_Size() int {
	return xxx_messageInfo_AIBatchSendMsgToGroupResp.Size(m)
}
func (m *AIBatchSendMsgToGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AIBatchSendMsgToGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_AIBatchSendMsgToGroupResp proto.InternalMessageInfo

type RewardSentenceAddReq struct {
	Uid                  uint32                            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SentenceType         RewardSentenceAddReq_SentenceType `protobuf:"varint,2,opt,name=sentence_type,json=sentenceType,proto3,enum=rcmd.rcmd_ai_partner.RewardSentenceAddReq_SentenceType" json:"sentence_type,omitempty"`
	ValidDay             uint32                            `protobuf:"varint,3,opt,name=valid_day,json=validDay,proto3" json:"valid_day,omitempty"`
	AddNum               uint32                            `protobuf:"varint,4,opt,name=add_num,json=addNum,proto3" json:"add_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *RewardSentenceAddReq) Reset()         { *m = RewardSentenceAddReq{} }
func (m *RewardSentenceAddReq) String() string { return proto.CompactTextString(m) }
func (*RewardSentenceAddReq) ProtoMessage()    {}
func (*RewardSentenceAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{91}
}
func (m *RewardSentenceAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RewardSentenceAddReq.Unmarshal(m, b)
}
func (m *RewardSentenceAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RewardSentenceAddReq.Marshal(b, m, deterministic)
}
func (dst *RewardSentenceAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RewardSentenceAddReq.Merge(dst, src)
}
func (m *RewardSentenceAddReq) XXX_Size() int {
	return xxx_messageInfo_RewardSentenceAddReq.Size(m)
}
func (m *RewardSentenceAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RewardSentenceAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_RewardSentenceAddReq proto.InternalMessageInfo

func (m *RewardSentenceAddReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RewardSentenceAddReq) GetSentenceType() RewardSentenceAddReq_SentenceType {
	if m != nil {
		return m.SentenceType
	}
	return RewardSentenceAddReq_SentenceType_Unknown
}

func (m *RewardSentenceAddReq) GetValidDay() uint32 {
	if m != nil {
		return m.ValidDay
	}
	return 0
}

func (m *RewardSentenceAddReq) GetAddNum() uint32 {
	if m != nil {
		return m.AddNum
	}
	return 0
}

type RewardSentenceAddResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RewardSentenceAddResp) Reset()         { *m = RewardSentenceAddResp{} }
func (m *RewardSentenceAddResp) String() string { return proto.CompactTextString(m) }
func (*RewardSentenceAddResp) ProtoMessage()    {}
func (*RewardSentenceAddResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_b8feadafb503520f, []int{92}
}
func (m *RewardSentenceAddResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RewardSentenceAddResp.Unmarshal(m, b)
}
func (m *RewardSentenceAddResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RewardSentenceAddResp.Marshal(b, m, deterministic)
}
func (dst *RewardSentenceAddResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RewardSentenceAddResp.Merge(dst, src)
}
func (m *RewardSentenceAddResp) XXX_Size() int {
	return xxx_messageInfo_RewardSentenceAddResp.Size(m)
}
func (m *RewardSentenceAddResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RewardSentenceAddResp.DiscardUnknown(m)
}

var xxx_messageInfo_RewardSentenceAddResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GenVoiceChatGreetingReq)(nil), "rcmd.rcmd_ai_partner.GenVoiceChatGreetingReq")
	proto.RegisterType((*GenVoiceChatGreetingResp)(nil), "rcmd.rcmd_ai_partner.GenVoiceChatGreetingResp")
	proto.RegisterType((*TestASRReq)(nil), "rcmd.rcmd_ai_partner.TestASRReq")
	proto.RegisterType((*TestASRResp)(nil), "rcmd.rcmd_ai_partner.TestASRResp")
	proto.RegisterType((*SetPartnerSilentReq)(nil), "rcmd.rcmd_ai_partner.SetPartnerSilentReq")
	proto.RegisterType((*SetDeviceWhitelistReq)(nil), "rcmd.rcmd_ai_partner.SetDeviceWhitelistReq")
	proto.RegisterType((*SetDeviceWhitelistResp)(nil), "rcmd.rcmd_ai_partner.SetDeviceWhitelistResp")
	proto.RegisterType((*SetPartnerSilentResp)(nil), "rcmd.rcmd_ai_partner.SetPartnerSilentResp")
	proto.RegisterType((*SetPartnerAutoPlayStatusReq)(nil), "rcmd.rcmd_ai_partner.SetPartnerAutoPlayStatusReq")
	proto.RegisterType((*SetPartnerAutoPlayStatusResp)(nil), "rcmd.rcmd_ai_partner.SetPartnerAutoPlayStatusResp")
	proto.RegisterType((*GetPartnerSettingsReq)(nil), "rcmd.rcmd_ai_partner.GetPartnerSettingsReq")
	proto.RegisterType((*GetPartnerSettingsResp)(nil), "rcmd.rcmd_ai_partner.GetPartnerSettingsResp")
	proto.RegisterType((*GetPartnerReq)(nil), "rcmd.rcmd_ai_partner.GetPartnerReq")
	proto.RegisterType((*GetPartnerResp)(nil), "rcmd.rcmd_ai_partner.GetPartnerResp")
	proto.RegisterType((*SetPartnerLoginPushTagReq)(nil), "rcmd.rcmd_ai_partner.SetPartnerLoginPushTagReq")
	proto.RegisterType((*SetPartnerLoginPushTagResp)(nil), "rcmd.rcmd_ai_partner.SetPartnerLoginPushTagResp")
	proto.RegisterType((*GetPartnerByRoleIdReq)(nil), "rcmd.rcmd_ai_partner.GetPartnerByRoleIdReq")
	proto.RegisterType((*GetPartnerByRoleIdResp)(nil), "rcmd.rcmd_ai_partner.GetPartnerByRoleIdResp")
	proto.RegisterType((*Partner)(nil), "rcmd.rcmd_ai_partner.Partner")
	proto.RegisterType((*FormatGptAnswerReq)(nil), "rcmd.rcmd_ai_partner.FormatGptAnswerReq")
	proto.RegisterType((*FormatGptAnswerResp)(nil), "rcmd.rcmd_ai_partner.FormatGptAnswerResp")
	proto.RegisterType((*GetQuickReplyListReq)(nil), "rcmd.rcmd_ai_partner.GetQuickReplyListReq")
	proto.RegisterType((*GetQuickReplyListResp)(nil), "rcmd.rcmd_ai_partner.GetQuickReplyListResp")
	proto.RegisterType((*GetGreetingMsgReq)(nil), "rcmd.rcmd_ai_partner.GetGreetingMsgReq")
	proto.RegisterType((*GetGreetingMsgResp)(nil), "rcmd.rcmd_ai_partner.GetGreetingMsgResp")
	proto.RegisterType((*GetGPTReqInfoReq)(nil), "rcmd.rcmd_ai_partner.GetGPTReqInfoReq")
	proto.RegisterType((*GetGPTReqInfoResp)(nil), "rcmd.rcmd_ai_partner.GetGPTReqInfoResp")
	proto.RegisterType((*GetUserInfoReq)(nil), "rcmd.rcmd_ai_partner.GetUserInfoReq")
	proto.RegisterType((*GetUserInfoResp)(nil), "rcmd.rcmd_ai_partner.GetUserInfoResp")
	proto.RegisterType((*GetUserMsgStatReq)(nil), "rcmd.rcmd_ai_partner.GetUserMsgStatReq")
	proto.RegisterType((*GetUserMsgStatResp)(nil), "rcmd.rcmd_ai_partner.GetUserMsgStatResp")
	proto.RegisterType((*GenRoleDescReq)(nil), "rcmd.rcmd_ai_partner.GenRoleDescReq")
	proto.RegisterType((*GenRoleDescResp)(nil), "rcmd.rcmd_ai_partner.GenRoleDescResp")
	proto.RegisterType((*GenRecommendReplyReq)(nil), "rcmd.rcmd_ai_partner.GenRecommendReplyReq")
	proto.RegisterType((*GenRecommendReplyResp)(nil), "rcmd.rcmd_ai_partner.GenRecommendReplyResp")
	proto.RegisterType((*GetGenStatusReq)(nil), "rcmd.rcmd_ai_partner.GetGenStatusReq")
	proto.RegisterType((*GetGenStatusResp)(nil), "rcmd.rcmd_ai_partner.GetGenStatusResp")
	proto.RegisterType((*GetStoryHistoryReq)(nil), "rcmd.rcmd_ai_partner.GetStoryHistoryReq")
	proto.RegisterType((*StoryMsg)(nil), "rcmd.rcmd_ai_partner.StoryMsg")
	proto.RegisterType((*GetStoryHistoryResp)(nil), "rcmd.rcmd_ai_partner.GetStoryHistoryResp")
	proto.RegisterType((*GetStoryReplyReq)(nil), "rcmd.rcmd_ai_partner.GetStoryReplyReq")
	proto.RegisterType((*GetStoryReplyResp)(nil), "rcmd.rcmd_ai_partner.GetStoryReplyResp")
	proto.RegisterType((*GetStoryReplyResp_ReplyOptions)(nil), "rcmd.rcmd_ai_partner.GetStoryReplyResp.ReplyOptions")
	proto.RegisterType((*GetStorySceneReq)(nil), "rcmd.rcmd_ai_partner.GetStorySceneReq")
	proto.RegisterType((*GetStorySceneResp)(nil), "rcmd.rcmd_ai_partner.GetStorySceneResp")
	proto.RegisterType((*SetStoryProgressReq)(nil), "rcmd.rcmd_ai_partner.SetStoryProgressReq")
	proto.RegisterType((*SetStoryProgressResp)(nil), "rcmd.rcmd_ai_partner.SetStoryProgressResp")
	proto.RegisterType((*StartStoryReq)(nil), "rcmd.rcmd_ai_partner.StartStoryReq")
	proto.RegisterType((*StartStoryResp)(nil), "rcmd.rcmd_ai_partner.StartStoryResp")
	proto.RegisterType((*GetStoryInfoReq)(nil), "rcmd.rcmd_ai_partner.GetStoryInfoReq")
	proto.RegisterType((*GetStoryInfoResp)(nil), "rcmd.rcmd_ai_partner.GetStoryInfoResp")
	proto.RegisterType((*SendAnimatedMemeReq)(nil), "rcmd.rcmd_ai_partner.SendAnimatedMemeReq")
	proto.RegisterType((*SendAnimatedMemeResp)(nil), "rcmd.rcmd_ai_partner.SendAnimatedMemeResp")
	proto.RegisterType((*SetRelationshipReq)(nil), "rcmd.rcmd_ai_partner.SetRelationshipReq")
	proto.RegisterType((*SetRelationshipResp)(nil), "rcmd.rcmd_ai_partner.SetRelationshipResp")
	proto.RegisterType((*AddBlackListReq)(nil), "rcmd.rcmd_ai_partner.AddBlackListReq")
	proto.RegisterType((*AddBlackListResp)(nil), "rcmd.rcmd_ai_partner.AddBlackListResp")
	proto.RegisterType((*DelBlackListReq)(nil), "rcmd.rcmd_ai_partner.DelBlackListReq")
	proto.RegisterType((*DelBlackListResp)(nil), "rcmd.rcmd_ai_partner.DelBlackListResp")
	proto.RegisterType((*ReplyTextFormatReq)(nil), "rcmd.rcmd_ai_partner.ReplyTextFormatReq")
	proto.RegisterType((*ReplyTextFormatResp)(nil), "rcmd.rcmd_ai_partner.ReplyTextFormatResp")
	proto.RegisterType((*TriggerReq)(nil), "rcmd.rcmd_ai_partner.TriggerReq")
	proto.RegisterType((*TriggerResp)(nil), "rcmd.rcmd_ai_partner.TriggerResp")
	proto.RegisterType((*SetAIPartnerChattingStatusReq)(nil), "rcmd.rcmd_ai_partner.SetAIPartnerChattingStatusReq")
	proto.RegisterType((*SetAIPartnerChattingStatusResp)(nil), "rcmd.rcmd_ai_partner.SetAIPartnerChattingStatusResp")
	proto.RegisterType((*SetAIPartnerInfoReq)(nil), "rcmd.rcmd_ai_partner.SetAIPartnerInfoReq")
	proto.RegisterType((*AIRole)(nil), "rcmd.rcmd_ai_partner.AIRole")
	proto.RegisterType((*SetAIPartnerInfoResp)(nil), "rcmd.rcmd_ai_partner.SetAIPartnerInfoResp")
	proto.RegisterType((*ReceiveMsgFromUserReq)(nil), "rcmd.rcmd_ai_partner.ReceiveMsgFromUserReq")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.rcmd_ai_partner.ReceiveMsgFromUserReq.ExtraMapEntry")
	proto.RegisterType((*ReceiveMsgFromUserResp)(nil), "rcmd.rcmd_ai_partner.ReceiveMsgFromUserResp")
	proto.RegisterType((*UserEnterChattingNotifyReq)(nil), "rcmd.rcmd_ai_partner.UserEnterChattingNotifyReq")
	proto.RegisterType((*UserEnterChattingNotifyResp)(nil), "rcmd.rcmd_ai_partner.UserEnterChattingNotifyResp")
	proto.RegisterType((*NewTriggerReq)(nil), "rcmd.rcmd_ai_partner.NewTriggerReq")
	proto.RegisterType((*NewTriggerResp)(nil), "rcmd.rcmd_ai_partner.NewTriggerResp")
	proto.RegisterType((*DeletePartnerReq)(nil), "rcmd.rcmd_ai_partner.DeletePartnerReq")
	proto.RegisterType((*DeletePartnerResp)(nil), "rcmd.rcmd_ai_partner.DeletePartnerResp")
	proto.RegisterType((*ShowContinueChatReq)(nil), "rcmd.rcmd_ai_partner.ShowContinueChatReq")
	proto.RegisterType((*ShowContinueChatRsp)(nil), "rcmd.rcmd_ai_partner.ShowContinueChatRsp")
	proto.RegisterType((*ContinueChatReq)(nil), "rcmd.rcmd_ai_partner.ContinueChatReq")
	proto.RegisterType((*ContinueChatRsp)(nil), "rcmd.rcmd_ai_partner.ContinueChatRsp")
	proto.RegisterType((*GetPartnerOpenReq)(nil), "rcmd.rcmd_ai_partner.GetPartnerOpenReq")
	proto.RegisterType((*AIPartnerNormalUserView)(nil), "rcmd.rcmd_ai_partner.AIPartnerNormalUserView")
	proto.RegisterType((*GetPartnerOpenRsp)(nil), "rcmd.rcmd_ai_partner.GetPartnerOpenRsp")
	proto.RegisterType((*ReceiveGroupMsgFromUserReq)(nil), "rcmd.rcmd_ai_partner.ReceiveGroupMsgFromUserReq")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.rcmd_ai_partner.ReceiveGroupMsgFromUserReq.ExtraMapEntry")
	proto.RegisterMapType((map[uint32]*ReceiveGroupMsgFromUserReq_UserInfo)(nil), "rcmd.rcmd_ai_partner.ReceiveGroupMsgFromUserReq.UidToInfoEntry")
	proto.RegisterType((*ReceiveGroupMsgFromUserReq_UserInfo)(nil), "rcmd.rcmd_ai_partner.ReceiveGroupMsgFromUserReq.UserInfo")
	proto.RegisterType((*ReceiveGroupMsgFromUserResp)(nil), "rcmd.rcmd_ai_partner.ReceiveGroupMsgFromUserResp")
	proto.RegisterType((*UserGiftInfo)(nil), "rcmd.rcmd_ai_partner.UserGiftInfo")
	proto.RegisterType((*ReceiveSpecialMsgFromUserReq)(nil), "rcmd.rcmd_ai_partner.ReceiveSpecialMsgFromUserReq")
	proto.RegisterType((*ReceiveSpecialMsgFromUserResp)(nil), "rcmd.rcmd_ai_partner.ReceiveSpecialMsgFromUserResp")
	proto.RegisterType((*AISendMsgToGroupReq)(nil), "rcmd.rcmd_ai_partner.AISendMsgToGroupReq")
	proto.RegisterMapType((map[uint32]*AISendMsgToGroupReq_UserInfo)(nil), "rcmd.rcmd_ai_partner.AISendMsgToGroupReq.UidToInfoEntry")
	proto.RegisterType((*AISendMsgToGroupReq_UserInfo)(nil), "rcmd.rcmd_ai_partner.AISendMsgToGroupReq.UserInfo")
	proto.RegisterType((*AISendMsgToGroupResp)(nil), "rcmd.rcmd_ai_partner.AISendMsgToGroupResp")
	proto.RegisterType((*AIBatchSendMsgToGroupReq)(nil), "rcmd.rcmd_ai_partner.AIBatchSendMsgToGroupReq")
	proto.RegisterMapType((map[uint32]*AIBatchSendMsgToGroupReq_UserInfo)(nil), "rcmd.rcmd_ai_partner.AIBatchSendMsgToGroupReq.UidToInfoEntry")
	proto.RegisterType((*AIBatchSendMsgToGroupReq_UserInfo)(nil), "rcmd.rcmd_ai_partner.AIBatchSendMsgToGroupReq.UserInfo")
	proto.RegisterType((*AIBatchSendMsgToGroupReq_TargetUidList)(nil), "rcmd.rcmd_ai_partner.AIBatchSendMsgToGroupReq.TargetUidList")
	proto.RegisterType((*AIBatchSendMsgToGroupResp)(nil), "rcmd.rcmd_ai_partner.AIBatchSendMsgToGroupResp")
	proto.RegisterType((*RewardSentenceAddReq)(nil), "rcmd.rcmd_ai_partner.RewardSentenceAddReq")
	proto.RegisterType((*RewardSentenceAddResp)(nil), "rcmd.rcmd_ai_partner.RewardSentenceAddResp")
	proto.RegisterEnum("rcmd.rcmd_ai_partner.ChattingStatus", ChattingStatus_name, ChattingStatus_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner.AIRoleType", AIRoleType_name, AIRoleType_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner.TriggerSource", TriggerSource_name, TriggerSource_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner.AIGroupType", AIGroupType_name, AIGroupType_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner.ReplyTextFormatReq_FormatType", ReplyTextFormatReq_FormatType_name, ReplyTextFormatReq_FormatType_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner.SetAIPartnerInfoReq_Relationship", SetAIPartnerInfoReq_Relationship_name, SetAIPartnerInfoReq_Relationship_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner.AIRole_Type", AIRole_Type_name, AIRole_Type_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner.ReceiveMsgFromUserReq_MsgType", ReceiveMsgFromUserReq_MsgType_name, ReceiveMsgFromUserReq_MsgType_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner.ReceiveGroupMsgFromUserReq_MsgType", ReceiveGroupMsgFromUserReq_MsgType_name, ReceiveGroupMsgFromUserReq_MsgType_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner.ReceiveSpecialMsgFromUserReq_SpecialMsgType", ReceiveSpecialMsgFromUserReq_SpecialMsgType_name, ReceiveSpecialMsgFromUserReq_SpecialMsgType_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner.RewardSentenceAddReq_SentenceType", RewardSentenceAddReq_SentenceType_name, RewardSentenceAddReq_SentenceType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RCMDAIPartnerClient is the client API for RCMDAIPartner service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RCMDAIPartnerClient interface {
	// 设置AI伴侣信息
	SetAIPartnerInfo(ctx context.Context, in *SetAIPartnerInfoReq, opts ...grpc.CallOption) (*SetAIPartnerInfoResp, error)
	// 接受用户发给AI伴侣的消息
	ReceiveMsgFromUser(ctx context.Context, in *ReceiveMsgFromUserReq, opts ...grpc.CallOption) (*ReceiveMsgFromUserResp, error)
	// 接受用户发给AI群组的消息
	ReceiveGroupMsgFromUser(ctx context.Context, in *ReceiveGroupMsgFromUserReq, opts ...grpc.CallOption) (*ReceiveGroupMsgFromUserResp, error)
	// 接收用户发给AI伴侣的特殊消息(如：送礼等）
	ReceiveSpecialMsgFromUser(ctx context.Context, in *ReceiveSpecialMsgFromUserReq, opts ...grpc.CallOption) (*ReceiveSpecialMsgFromUserResp, error)
	// 用户进入虚拟陪伴聊天页通知
	UserEnterChattingNotify(ctx context.Context, in *UserEnterChattingNotifyReq, opts ...grpc.CallOption) (*UserEnterChattingNotifyResp, error)
	// 设置虚拟伴侣是否可以聊天
	SetAIPartnerChattingStatus(ctx context.Context, in *SetAIPartnerChattingStatusReq, opts ...grpc.CallOption) (*SetAIPartnerChattingStatusResp, error)
	// 添加或删除虚拟伴侣功能设备白名单
	SetDeviceWhitelist(ctx context.Context, in *SetDeviceWhitelistReq, opts ...grpc.CallOption) (*SetDeviceWhitelistResp, error)
	// 虚拟伴侣是否开启
	Trigger(ctx context.Context, in *TriggerReq, opts ...grpc.CallOption) (*TriggerResp, error)
	// 设置关系
	SetRelationship(ctx context.Context, in *SetRelationshipReq, opts ...grpc.CallOption) (*SetRelationshipResp, error)
	// 新版虚拟伴侣是否开启
	NewTrigger(ctx context.Context, in *NewTriggerReq, opts ...grpc.CallOption) (*NewTriggerResp, error)
	// 删除树洞
	DeletePartner(ctx context.Context, in *DeletePartnerReq, opts ...grpc.CallOption) (*DeletePartnerResp, error)
	// 测试接口
	// 内部测试接口：机器人回复文本切分
	ReplyTextFormat(ctx context.Context, in *ReplyTextFormatReq, opts ...grpc.CallOption) (*ReplyTextFormatResp, error)
	// 添加黑名单接口
	AddBlackList(ctx context.Context, in *AddBlackListReq, opts ...grpc.CallOption) (*AddBlackListResp, error)
	// 删除黑名单接口
	DelBlackList(ctx context.Context, in *DelBlackListReq, opts ...grpc.CallOption) (*DelBlackListResp, error)
	SendAnimatedMeme(ctx context.Context, in *SendAnimatedMemeReq, opts ...grpc.CallOption) (*SendAnimatedMemeResp, error)
	// 获取故事信息
	GetStoryInfo(ctx context.Context, in *GetStoryInfoReq, opts ...grpc.CallOption) (*GetStoryInfoResp, error)
	// 故事开始
	StartStory(ctx context.Context, in *StartStoryReq, opts ...grpc.CallOption) (*StartStoryResp, error)
	// 故事进度操作
	SetStoryProgress(ctx context.Context, in *SetStoryProgressReq, opts ...grpc.CallOption) (*SetStoryProgressResp, error)
	// 获取剧幕信息
	GetStoryScene(ctx context.Context, in *GetStorySceneReq, opts ...grpc.CallOption) (*GetStorySceneResp, error)
	// 获取回复信息
	GetStoryReply(ctx context.Context, in *GetStoryReplyReq, opts ...grpc.CallOption) (*GetStoryReplyResp, error)
	// 获取故事聊天记录
	GetStoryHistory(ctx context.Context, in *GetStoryHistoryReq, opts ...grpc.CallOption) (*GetStoryHistoryResp, error)
	// 生成角色人设说明(角色设定一键生成)
	GenRoleDesc(ctx context.Context, in *GenRoleDescReq, opts ...grpc.CallOption) (*GenRoleDescResp, error)
	// 推荐回复生成
	GenRecommendReply(ctx context.Context, in *GenRecommendReplyReq, opts ...grpc.CallOption) (*GenRecommendReplyResp, error)
	// 获取角色设定一键生成次数限制（用于前端是否显示一键生成按钮）
	GetGenStatus(ctx context.Context, in *GetGenStatusReq, opts ...grpc.CallOption) (*GetGenStatusResp, error)
	// 获取用户消息统计
	GetUserMsgStat(ctx context.Context, in *GetUserMsgStatReq, opts ...grpc.CallOption) (*GetUserMsgStatResp, error)
	// 获取用户信息
	GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error)
	// 快捷回复(一键回复)
	GetQuickReplyList(ctx context.Context, in *GetQuickReplyListReq, opts ...grpc.CallOption) (*GetQuickReplyListResp, error)
	// Get GPT Req info
	GetGPTReqInfo(ctx context.Context, in *GetGPTReqInfoReq, opts ...grpc.CallOption) (*GetGPTReqInfoResp, error)
	// 获取开场白
	GetGreetingMsg(ctx context.Context, in *GetGreetingMsgReq, opts ...grpc.CallOption) (*GetGreetingMsgResp, error)
	FormatGptAnswer(ctx context.Context, in *FormatGptAnswerReq, opts ...grpc.CallOption) (*FormatGptAnswerResp, error)
	// 通过role_id获取partner
	GetPartnerByRoleId(ctx context.Context, in *GetPartnerByRoleIdReq, opts ...grpc.CallOption) (*GetPartnerByRoleIdResp, error)
	// 设置树洞主动触发的标签
	SetPartnerLoginPushTag(ctx context.Context, in *SetPartnerLoginPushTagReq, opts ...grpc.CallOption) (*SetPartnerLoginPushTagResp, error)
	GetPartner(ctx context.Context, in *GetPartnerReq, opts ...grpc.CallOption) (*GetPartnerResp, error)
	SetPartnerSilent(ctx context.Context, in *SetPartnerSilentReq, opts ...grpc.CallOption) (*SetPartnerSilentResp, error)
	GetPartnerSettings(ctx context.Context, in *GetPartnerSettingsReq, opts ...grpc.CallOption) (*GetPartnerSettingsResp, error)
	SetPartnerAutoPlayStatus(ctx context.Context, in *SetPartnerAutoPlayStatusReq, opts ...grpc.CallOption) (*SetPartnerAutoPlayStatusResp, error)
	// 是否显示继续说
	ShowContinueChat(ctx context.Context, in *ShowContinueChatReq, opts ...grpc.CallOption) (*ShowContinueChatRsp, error)
	// 继续说
	ContinueChat(ctx context.Context, in *ContinueChatReq, opts ...grpc.CallOption) (*ContinueChatRsp, error)
	TestASR(ctx context.Context, in *TestASRReq, opts ...grpc.CallOption) (*TestASRResp, error)
	// 生成语音打招呼
	GenVoiceChatGreeting(ctx context.Context, in *GenVoiceChatGreetingReq, opts ...grpc.CallOption) (*GenVoiceChatGreetingResp, error)
	// 判断是否开启伴侣功能
	GetPartnerOpen(ctx context.Context, in *GetPartnerOpenReq, opts ...grpc.CallOption) (*GetPartnerOpenRsp, error)
	// 角色向多人群组主动发送消息
	AISendMsgToGroup(ctx context.Context, in *AISendMsgToGroupReq, opts ...grpc.CallOption) (*AISendMsgToGroupResp, error)
	// 角色批量向多人群组主动发送消息
	BatchAISendMsgToGroup(ctx context.Context, in *AIBatchSendMsgToGroupReq, opts ...grpc.CallOption) (*AIBatchSendMsgToGroupResp, error)
	// 奖励句数增加
	RewardSentenceAdd(ctx context.Context, in *RewardSentenceAddReq, opts ...grpc.CallOption) (*RewardSentenceAddResp, error)
}

type rCMDAIPartnerClient struct {
	cc *grpc.ClientConn
}

func NewRCMDAIPartnerClient(cc *grpc.ClientConn) RCMDAIPartnerClient {
	return &rCMDAIPartnerClient{cc}
}

func (c *rCMDAIPartnerClient) SetAIPartnerInfo(ctx context.Context, in *SetAIPartnerInfoReq, opts ...grpc.CallOption) (*SetAIPartnerInfoResp, error) {
	out := new(SetAIPartnerInfoResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetAIPartnerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) ReceiveMsgFromUser(ctx context.Context, in *ReceiveMsgFromUserReq, opts ...grpc.CallOption) (*ReceiveMsgFromUserResp, error) {
	out := new(ReceiveMsgFromUserResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/ReceiveMsgFromUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) ReceiveGroupMsgFromUser(ctx context.Context, in *ReceiveGroupMsgFromUserReq, opts ...grpc.CallOption) (*ReceiveGroupMsgFromUserResp, error) {
	out := new(ReceiveGroupMsgFromUserResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/ReceiveGroupMsgFromUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) ReceiveSpecialMsgFromUser(ctx context.Context, in *ReceiveSpecialMsgFromUserReq, opts ...grpc.CallOption) (*ReceiveSpecialMsgFromUserResp, error) {
	out := new(ReceiveSpecialMsgFromUserResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/ReceiveSpecialMsgFromUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) UserEnterChattingNotify(ctx context.Context, in *UserEnterChattingNotifyReq, opts ...grpc.CallOption) (*UserEnterChattingNotifyResp, error) {
	out := new(UserEnterChattingNotifyResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/UserEnterChattingNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) SetAIPartnerChattingStatus(ctx context.Context, in *SetAIPartnerChattingStatusReq, opts ...grpc.CallOption) (*SetAIPartnerChattingStatusResp, error) {
	out := new(SetAIPartnerChattingStatusResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetAIPartnerChattingStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) SetDeviceWhitelist(ctx context.Context, in *SetDeviceWhitelistReq, opts ...grpc.CallOption) (*SetDeviceWhitelistResp, error) {
	out := new(SetDeviceWhitelistResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetDeviceWhitelist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) Trigger(ctx context.Context, in *TriggerReq, opts ...grpc.CallOption) (*TriggerResp, error) {
	out := new(TriggerResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/Trigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) SetRelationship(ctx context.Context, in *SetRelationshipReq, opts ...grpc.CallOption) (*SetRelationshipResp, error) {
	out := new(SetRelationshipResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) NewTrigger(ctx context.Context, in *NewTriggerReq, opts ...grpc.CallOption) (*NewTriggerResp, error) {
	out := new(NewTriggerResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/NewTrigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) DeletePartner(ctx context.Context, in *DeletePartnerReq, opts ...grpc.CallOption) (*DeletePartnerResp, error) {
	out := new(DeletePartnerResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/DeletePartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) ReplyTextFormat(ctx context.Context, in *ReplyTextFormatReq, opts ...grpc.CallOption) (*ReplyTextFormatResp, error) {
	out := new(ReplyTextFormatResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/ReplyTextFormat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) AddBlackList(ctx context.Context, in *AddBlackListReq, opts ...grpc.CallOption) (*AddBlackListResp, error) {
	out := new(AddBlackListResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/AddBlackList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) DelBlackList(ctx context.Context, in *DelBlackListReq, opts ...grpc.CallOption) (*DelBlackListResp, error) {
	out := new(DelBlackListResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/DelBlackList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) SendAnimatedMeme(ctx context.Context, in *SendAnimatedMemeReq, opts ...grpc.CallOption) (*SendAnimatedMemeResp, error) {
	out := new(SendAnimatedMemeResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/SendAnimatedMeme", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetStoryInfo(ctx context.Context, in *GetStoryInfoReq, opts ...grpc.CallOption) (*GetStoryInfoResp, error) {
	out := new(GetStoryInfoResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetStoryInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) StartStory(ctx context.Context, in *StartStoryReq, opts ...grpc.CallOption) (*StartStoryResp, error) {
	out := new(StartStoryResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/StartStory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) SetStoryProgress(ctx context.Context, in *SetStoryProgressReq, opts ...grpc.CallOption) (*SetStoryProgressResp, error) {
	out := new(SetStoryProgressResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetStoryProgress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetStoryScene(ctx context.Context, in *GetStorySceneReq, opts ...grpc.CallOption) (*GetStorySceneResp, error) {
	out := new(GetStorySceneResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetStoryScene", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetStoryReply(ctx context.Context, in *GetStoryReplyReq, opts ...grpc.CallOption) (*GetStoryReplyResp, error) {
	out := new(GetStoryReplyResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetStoryReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetStoryHistory(ctx context.Context, in *GetStoryHistoryReq, opts ...grpc.CallOption) (*GetStoryHistoryResp, error) {
	out := new(GetStoryHistoryResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetStoryHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GenRoleDesc(ctx context.Context, in *GenRoleDescReq, opts ...grpc.CallOption) (*GenRoleDescResp, error) {
	out := new(GenRoleDescResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GenRoleDesc", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GenRecommendReply(ctx context.Context, in *GenRecommendReplyReq, opts ...grpc.CallOption) (*GenRecommendReplyResp, error) {
	out := new(GenRecommendReplyResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GenRecommendReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetGenStatus(ctx context.Context, in *GetGenStatusReq, opts ...grpc.CallOption) (*GetGenStatusResp, error) {
	out := new(GetGenStatusResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetGenStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetUserMsgStat(ctx context.Context, in *GetUserMsgStatReq, opts ...grpc.CallOption) (*GetUserMsgStatResp, error) {
	out := new(GetUserMsgStatResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetUserMsgStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error) {
	out := new(GetUserInfoResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetQuickReplyList(ctx context.Context, in *GetQuickReplyListReq, opts ...grpc.CallOption) (*GetQuickReplyListResp, error) {
	out := new(GetQuickReplyListResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetQuickReplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetGPTReqInfo(ctx context.Context, in *GetGPTReqInfoReq, opts ...grpc.CallOption) (*GetGPTReqInfoResp, error) {
	out := new(GetGPTReqInfoResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetGPTReqInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetGreetingMsg(ctx context.Context, in *GetGreetingMsgReq, opts ...grpc.CallOption) (*GetGreetingMsgResp, error) {
	out := new(GetGreetingMsgResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetGreetingMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) FormatGptAnswer(ctx context.Context, in *FormatGptAnswerReq, opts ...grpc.CallOption) (*FormatGptAnswerResp, error) {
	out := new(FormatGptAnswerResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/FormatGptAnswer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetPartnerByRoleId(ctx context.Context, in *GetPartnerByRoleIdReq, opts ...grpc.CallOption) (*GetPartnerByRoleIdResp, error) {
	out := new(GetPartnerByRoleIdResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetPartnerByRoleId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) SetPartnerLoginPushTag(ctx context.Context, in *SetPartnerLoginPushTagReq, opts ...grpc.CallOption) (*SetPartnerLoginPushTagResp, error) {
	out := new(SetPartnerLoginPushTagResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetPartnerLoginPushTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetPartner(ctx context.Context, in *GetPartnerReq, opts ...grpc.CallOption) (*GetPartnerResp, error) {
	out := new(GetPartnerResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetPartner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) SetPartnerSilent(ctx context.Context, in *SetPartnerSilentReq, opts ...grpc.CallOption) (*SetPartnerSilentResp, error) {
	out := new(SetPartnerSilentResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetPartnerSilent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetPartnerSettings(ctx context.Context, in *GetPartnerSettingsReq, opts ...grpc.CallOption) (*GetPartnerSettingsResp, error) {
	out := new(GetPartnerSettingsResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetPartnerSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) SetPartnerAutoPlayStatus(ctx context.Context, in *SetPartnerAutoPlayStatusReq, opts ...grpc.CallOption) (*SetPartnerAutoPlayStatusResp, error) {
	out := new(SetPartnerAutoPlayStatusResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetPartnerAutoPlayStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) ShowContinueChat(ctx context.Context, in *ShowContinueChatReq, opts ...grpc.CallOption) (*ShowContinueChatRsp, error) {
	out := new(ShowContinueChatRsp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/ShowContinueChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) ContinueChat(ctx context.Context, in *ContinueChatReq, opts ...grpc.CallOption) (*ContinueChatRsp, error) {
	out := new(ContinueChatRsp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/ContinueChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) TestASR(ctx context.Context, in *TestASRReq, opts ...grpc.CallOption) (*TestASRResp, error) {
	out := new(TestASRResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/TestASR", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GenVoiceChatGreeting(ctx context.Context, in *GenVoiceChatGreetingReq, opts ...grpc.CallOption) (*GenVoiceChatGreetingResp, error) {
	out := new(GenVoiceChatGreetingResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GenVoiceChatGreeting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) GetPartnerOpen(ctx context.Context, in *GetPartnerOpenReq, opts ...grpc.CallOption) (*GetPartnerOpenRsp, error) {
	out := new(GetPartnerOpenRsp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetPartnerOpen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) AISendMsgToGroup(ctx context.Context, in *AISendMsgToGroupReq, opts ...grpc.CallOption) (*AISendMsgToGroupResp, error) {
	out := new(AISendMsgToGroupResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/AISendMsgToGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) BatchAISendMsgToGroup(ctx context.Context, in *AIBatchSendMsgToGroupReq, opts ...grpc.CallOption) (*AIBatchSendMsgToGroupResp, error) {
	out := new(AIBatchSendMsgToGroupResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/BatchAISendMsgToGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAIPartnerClient) RewardSentenceAdd(ctx context.Context, in *RewardSentenceAddReq, opts ...grpc.CallOption) (*RewardSentenceAddResp, error) {
	out := new(RewardSentenceAddResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_ai_partner.RCMDAIPartner/RewardSentenceAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RCMDAIPartnerServer is the server API for RCMDAIPartner service.
type RCMDAIPartnerServer interface {
	// 设置AI伴侣信息
	SetAIPartnerInfo(context.Context, *SetAIPartnerInfoReq) (*SetAIPartnerInfoResp, error)
	// 接受用户发给AI伴侣的消息
	ReceiveMsgFromUser(context.Context, *ReceiveMsgFromUserReq) (*ReceiveMsgFromUserResp, error)
	// 接受用户发给AI群组的消息
	ReceiveGroupMsgFromUser(context.Context, *ReceiveGroupMsgFromUserReq) (*ReceiveGroupMsgFromUserResp, error)
	// 接收用户发给AI伴侣的特殊消息(如：送礼等）
	ReceiveSpecialMsgFromUser(context.Context, *ReceiveSpecialMsgFromUserReq) (*ReceiveSpecialMsgFromUserResp, error)
	// 用户进入虚拟陪伴聊天页通知
	UserEnterChattingNotify(context.Context, *UserEnterChattingNotifyReq) (*UserEnterChattingNotifyResp, error)
	// 设置虚拟伴侣是否可以聊天
	SetAIPartnerChattingStatus(context.Context, *SetAIPartnerChattingStatusReq) (*SetAIPartnerChattingStatusResp, error)
	// 添加或删除虚拟伴侣功能设备白名单
	SetDeviceWhitelist(context.Context, *SetDeviceWhitelistReq) (*SetDeviceWhitelistResp, error)
	// 虚拟伴侣是否开启
	Trigger(context.Context, *TriggerReq) (*TriggerResp, error)
	// 设置关系
	SetRelationship(context.Context, *SetRelationshipReq) (*SetRelationshipResp, error)
	// 新版虚拟伴侣是否开启
	NewTrigger(context.Context, *NewTriggerReq) (*NewTriggerResp, error)
	// 删除树洞
	DeletePartner(context.Context, *DeletePartnerReq) (*DeletePartnerResp, error)
	// 测试接口
	// 内部测试接口：机器人回复文本切分
	ReplyTextFormat(context.Context, *ReplyTextFormatReq) (*ReplyTextFormatResp, error)
	// 添加黑名单接口
	AddBlackList(context.Context, *AddBlackListReq) (*AddBlackListResp, error)
	// 删除黑名单接口
	DelBlackList(context.Context, *DelBlackListReq) (*DelBlackListResp, error)
	SendAnimatedMeme(context.Context, *SendAnimatedMemeReq) (*SendAnimatedMemeResp, error)
	// 获取故事信息
	GetStoryInfo(context.Context, *GetStoryInfoReq) (*GetStoryInfoResp, error)
	// 故事开始
	StartStory(context.Context, *StartStoryReq) (*StartStoryResp, error)
	// 故事进度操作
	SetStoryProgress(context.Context, *SetStoryProgressReq) (*SetStoryProgressResp, error)
	// 获取剧幕信息
	GetStoryScene(context.Context, *GetStorySceneReq) (*GetStorySceneResp, error)
	// 获取回复信息
	GetStoryReply(context.Context, *GetStoryReplyReq) (*GetStoryReplyResp, error)
	// 获取故事聊天记录
	GetStoryHistory(context.Context, *GetStoryHistoryReq) (*GetStoryHistoryResp, error)
	// 生成角色人设说明(角色设定一键生成)
	GenRoleDesc(context.Context, *GenRoleDescReq) (*GenRoleDescResp, error)
	// 推荐回复生成
	GenRecommendReply(context.Context, *GenRecommendReplyReq) (*GenRecommendReplyResp, error)
	// 获取角色设定一键生成次数限制（用于前端是否显示一键生成按钮）
	GetGenStatus(context.Context, *GetGenStatusReq) (*GetGenStatusResp, error)
	// 获取用户消息统计
	GetUserMsgStat(context.Context, *GetUserMsgStatReq) (*GetUserMsgStatResp, error)
	// 获取用户信息
	GetUserInfo(context.Context, *GetUserInfoReq) (*GetUserInfoResp, error)
	// 快捷回复(一键回复)
	GetQuickReplyList(context.Context, *GetQuickReplyListReq) (*GetQuickReplyListResp, error)
	// Get GPT Req info
	GetGPTReqInfo(context.Context, *GetGPTReqInfoReq) (*GetGPTReqInfoResp, error)
	// 获取开场白
	GetGreetingMsg(context.Context, *GetGreetingMsgReq) (*GetGreetingMsgResp, error)
	FormatGptAnswer(context.Context, *FormatGptAnswerReq) (*FormatGptAnswerResp, error)
	// 通过role_id获取partner
	GetPartnerByRoleId(context.Context, *GetPartnerByRoleIdReq) (*GetPartnerByRoleIdResp, error)
	// 设置树洞主动触发的标签
	SetPartnerLoginPushTag(context.Context, *SetPartnerLoginPushTagReq) (*SetPartnerLoginPushTagResp, error)
	GetPartner(context.Context, *GetPartnerReq) (*GetPartnerResp, error)
	SetPartnerSilent(context.Context, *SetPartnerSilentReq) (*SetPartnerSilentResp, error)
	GetPartnerSettings(context.Context, *GetPartnerSettingsReq) (*GetPartnerSettingsResp, error)
	SetPartnerAutoPlayStatus(context.Context, *SetPartnerAutoPlayStatusReq) (*SetPartnerAutoPlayStatusResp, error)
	// 是否显示继续说
	ShowContinueChat(context.Context, *ShowContinueChatReq) (*ShowContinueChatRsp, error)
	// 继续说
	ContinueChat(context.Context, *ContinueChatReq) (*ContinueChatRsp, error)
	TestASR(context.Context, *TestASRReq) (*TestASRResp, error)
	// 生成语音打招呼
	GenVoiceChatGreeting(context.Context, *GenVoiceChatGreetingReq) (*GenVoiceChatGreetingResp, error)
	// 判断是否开启伴侣功能
	GetPartnerOpen(context.Context, *GetPartnerOpenReq) (*GetPartnerOpenRsp, error)
	// 角色向多人群组主动发送消息
	AISendMsgToGroup(context.Context, *AISendMsgToGroupReq) (*AISendMsgToGroupResp, error)
	// 角色批量向多人群组主动发送消息
	BatchAISendMsgToGroup(context.Context, *AIBatchSendMsgToGroupReq) (*AIBatchSendMsgToGroupResp, error)
	// 奖励句数增加
	RewardSentenceAdd(context.Context, *RewardSentenceAddReq) (*RewardSentenceAddResp, error)
}

func RegisterRCMDAIPartnerServer(s *grpc.Server, srv RCMDAIPartnerServer) {
	s.RegisterService(&_RCMDAIPartner_serviceDesc, srv)
}

func _RCMDAIPartner_SetAIPartnerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAIPartnerInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).SetAIPartnerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetAIPartnerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).SetAIPartnerInfo(ctx, req.(*SetAIPartnerInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_ReceiveMsgFromUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveMsgFromUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).ReceiveMsgFromUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/ReceiveMsgFromUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).ReceiveMsgFromUser(ctx, req.(*ReceiveMsgFromUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_ReceiveGroupMsgFromUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveGroupMsgFromUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).ReceiveGroupMsgFromUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/ReceiveGroupMsgFromUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).ReceiveGroupMsgFromUser(ctx, req.(*ReceiveGroupMsgFromUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_ReceiveSpecialMsgFromUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveSpecialMsgFromUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).ReceiveSpecialMsgFromUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/ReceiveSpecialMsgFromUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).ReceiveSpecialMsgFromUser(ctx, req.(*ReceiveSpecialMsgFromUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_UserEnterChattingNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserEnterChattingNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).UserEnterChattingNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/UserEnterChattingNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).UserEnterChattingNotify(ctx, req.(*UserEnterChattingNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_SetAIPartnerChattingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAIPartnerChattingStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).SetAIPartnerChattingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetAIPartnerChattingStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).SetAIPartnerChattingStatus(ctx, req.(*SetAIPartnerChattingStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_SetDeviceWhitelist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDeviceWhitelistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).SetDeviceWhitelist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetDeviceWhitelist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).SetDeviceWhitelist(ctx, req.(*SetDeviceWhitelistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_Trigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).Trigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/Trigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).Trigger(ctx, req.(*TriggerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_SetRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRelationshipReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).SetRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).SetRelationship(ctx, req.(*SetRelationshipReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_NewTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewTriggerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).NewTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/NewTrigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).NewTrigger(ctx, req.(*NewTriggerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_DeletePartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).DeletePartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/DeletePartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).DeletePartner(ctx, req.(*DeletePartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_ReplyTextFormat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReplyTextFormatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).ReplyTextFormat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/ReplyTextFormat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).ReplyTextFormat(ctx, req.(*ReplyTextFormatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_AddBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).AddBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/AddBlackList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).AddBlackList(ctx, req.(*AddBlackListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_DelBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).DelBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/DelBlackList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).DelBlackList(ctx, req.(*DelBlackListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_SendAnimatedMeme_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendAnimatedMemeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).SendAnimatedMeme(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/SendAnimatedMeme",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).SendAnimatedMeme(ctx, req.(*SendAnimatedMemeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetStoryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoryInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetStoryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetStoryInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetStoryInfo(ctx, req.(*GetStoryInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_StartStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartStoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).StartStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/StartStory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).StartStory(ctx, req.(*StartStoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_SetStoryProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStoryProgressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).SetStoryProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetStoryProgress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).SetStoryProgress(ctx, req.(*SetStoryProgressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetStoryScene_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStorySceneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetStoryScene(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetStoryScene",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetStoryScene(ctx, req.(*GetStorySceneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetStoryReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoryReplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetStoryReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetStoryReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetStoryReply(ctx, req.(*GetStoryReplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetStoryHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoryHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetStoryHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetStoryHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetStoryHistory(ctx, req.(*GetStoryHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GenRoleDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenRoleDescReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GenRoleDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GenRoleDesc",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GenRoleDesc(ctx, req.(*GenRoleDescReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GenRecommendReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenRecommendReplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GenRecommendReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GenRecommendReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GenRecommendReply(ctx, req.(*GenRecommendReplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetGenStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGenStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetGenStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetGenStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetGenStatus(ctx, req.(*GetGenStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetUserMsgStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMsgStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetUserMsgStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetUserMsgStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetUserMsgStat(ctx, req.(*GetUserMsgStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetUserInfo(ctx, req.(*GetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetQuickReplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuickReplyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetQuickReplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetQuickReplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetQuickReplyList(ctx, req.(*GetQuickReplyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetGPTReqInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGPTReqInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetGPTReqInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetGPTReqInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetGPTReqInfo(ctx, req.(*GetGPTReqInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetGreetingMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGreetingMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetGreetingMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetGreetingMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetGreetingMsg(ctx, req.(*GetGreetingMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_FormatGptAnswer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FormatGptAnswerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).FormatGptAnswer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/FormatGptAnswer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).FormatGptAnswer(ctx, req.(*FormatGptAnswerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetPartnerByRoleId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPartnerByRoleIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetPartnerByRoleId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetPartnerByRoleId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetPartnerByRoleId(ctx, req.(*GetPartnerByRoleIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_SetPartnerLoginPushTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPartnerLoginPushTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).SetPartnerLoginPushTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetPartnerLoginPushTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).SetPartnerLoginPushTag(ctx, req.(*SetPartnerLoginPushTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPartnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetPartner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetPartner(ctx, req.(*GetPartnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_SetPartnerSilent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPartnerSilentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).SetPartnerSilent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetPartnerSilent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).SetPartnerSilent(ctx, req.(*SetPartnerSilentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetPartnerSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPartnerSettingsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetPartnerSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetPartnerSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetPartnerSettings(ctx, req.(*GetPartnerSettingsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_SetPartnerAutoPlayStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPartnerAutoPlayStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).SetPartnerAutoPlayStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/SetPartnerAutoPlayStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).SetPartnerAutoPlayStatus(ctx, req.(*SetPartnerAutoPlayStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_ShowContinueChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShowContinueChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).ShowContinueChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/ShowContinueChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).ShowContinueChat(ctx, req.(*ShowContinueChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_ContinueChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContinueChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).ContinueChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/ContinueChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).ContinueChat(ctx, req.(*ContinueChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_TestASR_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestASRReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).TestASR(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/TestASR",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).TestASR(ctx, req.(*TestASRReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GenVoiceChatGreeting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenVoiceChatGreetingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GenVoiceChatGreeting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GenVoiceChatGreeting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GenVoiceChatGreeting(ctx, req.(*GenVoiceChatGreetingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_GetPartnerOpen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPartnerOpenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).GetPartnerOpen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/GetPartnerOpen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).GetPartnerOpen(ctx, req.(*GetPartnerOpenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_AISendMsgToGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AISendMsgToGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).AISendMsgToGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/AISendMsgToGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).AISendMsgToGroup(ctx, req.(*AISendMsgToGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_BatchAISendMsgToGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AIBatchSendMsgToGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).BatchAISendMsgToGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/BatchAISendMsgToGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).BatchAISendMsgToGroup(ctx, req.(*AIBatchSendMsgToGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAIPartner_RewardSentenceAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RewardSentenceAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAIPartnerServer).RewardSentenceAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_ai_partner.RCMDAIPartner/RewardSentenceAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAIPartnerServer).RewardSentenceAdd(ctx, req.(*RewardSentenceAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RCMDAIPartner_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.rcmd_ai_partner.RCMDAIPartner",
	HandlerType: (*RCMDAIPartnerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetAIPartnerInfo",
			Handler:    _RCMDAIPartner_SetAIPartnerInfo_Handler,
		},
		{
			MethodName: "ReceiveMsgFromUser",
			Handler:    _RCMDAIPartner_ReceiveMsgFromUser_Handler,
		},
		{
			MethodName: "ReceiveGroupMsgFromUser",
			Handler:    _RCMDAIPartner_ReceiveGroupMsgFromUser_Handler,
		},
		{
			MethodName: "ReceiveSpecialMsgFromUser",
			Handler:    _RCMDAIPartner_ReceiveSpecialMsgFromUser_Handler,
		},
		{
			MethodName: "UserEnterChattingNotify",
			Handler:    _RCMDAIPartner_UserEnterChattingNotify_Handler,
		},
		{
			MethodName: "SetAIPartnerChattingStatus",
			Handler:    _RCMDAIPartner_SetAIPartnerChattingStatus_Handler,
		},
		{
			MethodName: "SetDeviceWhitelist",
			Handler:    _RCMDAIPartner_SetDeviceWhitelist_Handler,
		},
		{
			MethodName: "Trigger",
			Handler:    _RCMDAIPartner_Trigger_Handler,
		},
		{
			MethodName: "SetRelationship",
			Handler:    _RCMDAIPartner_SetRelationship_Handler,
		},
		{
			MethodName: "NewTrigger",
			Handler:    _RCMDAIPartner_NewTrigger_Handler,
		},
		{
			MethodName: "DeletePartner",
			Handler:    _RCMDAIPartner_DeletePartner_Handler,
		},
		{
			MethodName: "ReplyTextFormat",
			Handler:    _RCMDAIPartner_ReplyTextFormat_Handler,
		},
		{
			MethodName: "AddBlackList",
			Handler:    _RCMDAIPartner_AddBlackList_Handler,
		},
		{
			MethodName: "DelBlackList",
			Handler:    _RCMDAIPartner_DelBlackList_Handler,
		},
		{
			MethodName: "SendAnimatedMeme",
			Handler:    _RCMDAIPartner_SendAnimatedMeme_Handler,
		},
		{
			MethodName: "GetStoryInfo",
			Handler:    _RCMDAIPartner_GetStoryInfo_Handler,
		},
		{
			MethodName: "StartStory",
			Handler:    _RCMDAIPartner_StartStory_Handler,
		},
		{
			MethodName: "SetStoryProgress",
			Handler:    _RCMDAIPartner_SetStoryProgress_Handler,
		},
		{
			MethodName: "GetStoryScene",
			Handler:    _RCMDAIPartner_GetStoryScene_Handler,
		},
		{
			MethodName: "GetStoryReply",
			Handler:    _RCMDAIPartner_GetStoryReply_Handler,
		},
		{
			MethodName: "GetStoryHistory",
			Handler:    _RCMDAIPartner_GetStoryHistory_Handler,
		},
		{
			MethodName: "GenRoleDesc",
			Handler:    _RCMDAIPartner_GenRoleDesc_Handler,
		},
		{
			MethodName: "GenRecommendReply",
			Handler:    _RCMDAIPartner_GenRecommendReply_Handler,
		},
		{
			MethodName: "GetGenStatus",
			Handler:    _RCMDAIPartner_GetGenStatus_Handler,
		},
		{
			MethodName: "GetUserMsgStat",
			Handler:    _RCMDAIPartner_GetUserMsgStat_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _RCMDAIPartner_GetUserInfo_Handler,
		},
		{
			MethodName: "GetQuickReplyList",
			Handler:    _RCMDAIPartner_GetQuickReplyList_Handler,
		},
		{
			MethodName: "GetGPTReqInfo",
			Handler:    _RCMDAIPartner_GetGPTReqInfo_Handler,
		},
		{
			MethodName: "GetGreetingMsg",
			Handler:    _RCMDAIPartner_GetGreetingMsg_Handler,
		},
		{
			MethodName: "FormatGptAnswer",
			Handler:    _RCMDAIPartner_FormatGptAnswer_Handler,
		},
		{
			MethodName: "GetPartnerByRoleId",
			Handler:    _RCMDAIPartner_GetPartnerByRoleId_Handler,
		},
		{
			MethodName: "SetPartnerLoginPushTag",
			Handler:    _RCMDAIPartner_SetPartnerLoginPushTag_Handler,
		},
		{
			MethodName: "GetPartner",
			Handler:    _RCMDAIPartner_GetPartner_Handler,
		},
		{
			MethodName: "SetPartnerSilent",
			Handler:    _RCMDAIPartner_SetPartnerSilent_Handler,
		},
		{
			MethodName: "GetPartnerSettings",
			Handler:    _RCMDAIPartner_GetPartnerSettings_Handler,
		},
		{
			MethodName: "SetPartnerAutoPlayStatus",
			Handler:    _RCMDAIPartner_SetPartnerAutoPlayStatus_Handler,
		},
		{
			MethodName: "ShowContinueChat",
			Handler:    _RCMDAIPartner_ShowContinueChat_Handler,
		},
		{
			MethodName: "ContinueChat",
			Handler:    _RCMDAIPartner_ContinueChat_Handler,
		},
		{
			MethodName: "TestASR",
			Handler:    _RCMDAIPartner_TestASR_Handler,
		},
		{
			MethodName: "GenVoiceChatGreeting",
			Handler:    _RCMDAIPartner_GenVoiceChatGreeting_Handler,
		},
		{
			MethodName: "GetPartnerOpen",
			Handler:    _RCMDAIPartner_GetPartnerOpen_Handler,
		},
		{
			MethodName: "AISendMsgToGroup",
			Handler:    _RCMDAIPartner_AISendMsgToGroup_Handler,
		},
		{
			MethodName: "BatchAISendMsgToGroup",
			Handler:    _RCMDAIPartner_BatchAISendMsgToGroup_Handler,
		},
		{
			MethodName: "RewardSentenceAdd",
			Handler:    _RCMDAIPartner_RewardSentenceAdd_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd-ai-partner/rcmd_ai_partner.proto",
}

func init() {
	proto.RegisterFile("rcmd-ai-partner/rcmd_ai_partner.proto", fileDescriptor_rcmd_ai_partner_b8feadafb503520f)
}

var fileDescriptor_rcmd_ai_partner_b8feadafb503520f = []byte{
	// 4600 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3c, 0xc9, 0x72, 0x1b, 0x49,
	0x76, 0x03, 0x80, 0x0b, 0xf0, 0x08, 0x80, 0x60, 0x92, 0x94, 0x20, 0x68, 0x69, 0x4d, 0xb5, 0xa4,
	0x56, 0x4b, 0xdd, 0x54, 0x37, 0xd5, 0x8b, 0xc6, 0x9e, 0x25, 0xa8, 0xa5, 0xd9, 0x98, 0x10, 0xd5,
	0x9c, 0x02, 0xa5, 0xf6, 0xf4, 0x32, 0xa5, 0x52, 0x55, 0x12, 0x2c, 0xb3, 0x36, 0x55, 0x26, 0x28,
	0xd2, 0xcb, 0xd8, 0x8e, 0xb0, 0xc3, 0x07, 0x1f, 0x1c, 0xe1, 0x70, 0xd8, 0x07, 0x87, 0x6f, 0x0e,
	0x7f, 0x81, 0xc3, 0x17, 0xdb, 0x47, 0xdf, 0x7d, 0xf0, 0xc9, 0x5f, 0x30, 0xfe, 0x08, 0x47, 0x38,
	0xf2, 0x65, 0x16, 0x6a, 0x41, 0x15, 0x48, 0xa8, 0xd5, 0x0e, 0x9f, 0x50, 0xf9, 0xf2, 0x65, 0xe6,
	0xcb, 0x7c, 0x4b, 0xbe, 0xf7, 0x32, 0x13, 0x70, 0x3d, 0xb2, 0x3c, 0xfb, 0x7d, 0xd3, 0x79, 0x3f,
	0x34, 0x23, 0xee, 0xd3, 0xe8, 0x8e, 0x28, 0x1b, 0xa6, 0x63, 0xa8, 0xf2, 0x46, 0x18, 0x05, 0x3c,
	0x20, 0x6b, 0x02, 0xbc, 0x91, 0xab, 0xeb, 0x5d, 0xcb, 0x37, 0x56, 0xbf, 0x86, 0x15, 0x78, 0x5e,
	0xe0, 0xcb, 0xb6, 0xda, 0x7f, 0x54, 0xe0, 0xfc, 0x36, 0xf5, 0x9f, 0x05, 0x8e, 0x45, 0x1f, 0x1c,
	0x98, 0x7c, 0x3b, 0xa2, 0x94, 0x3b, 0xfe, 0x50, 0xa7, 0x2f, 0x49, 0x07, 0x6a, 0x23, 0xc7, 0xee,
	0x56, 0xae, 0x56, 0x6e, 0xb6, 0x74, 0xf1, 0x49, 0x2e, 0x03, 0xc4, 0xbd, 0x38, 0x76, 0xb7, 0x8a,
	0x15, 0x0d, 0x05, 0xe9, 0xdb, 0xe4, 0x3c, 0x2c, 0x46, 0x81, 0x4b, 0x45, 0x5d, 0x0d, 0xeb, 0x16,
	0x44, 0xb1, 0x6f, 0x93, 0x0b, 0x50, 0x3f, 0x12, 0x23, 0x88, 0x9a, 0xb9, 0xab, 0x95, 0x9b, 0x0d,
	0x7d, 0x11, 0xcb, 0x7d, 0xec, 0x52, 0x56, 0xfd, 0x2e, 0x0b, 0xfc, 0xee, 0x3c, 0x56, 0x36, 0x10,
	0xf2, 0x73, 0x16, 0xf8, 0x84, 0xc0, 0x1c, 0xa7, 0xc7, 0xbc, 0xbb, 0x80, 0x15, 0xf8, 0x2d, 0x9a,
	0x30, 0xca, 0x98, 0x13, 0xf8, 0xa2, 0xbf, 0x45, 0xd9, 0x44, 0x41, 0xfa, 0xb6, 0xf6, 0x1c, 0xba,
	0xc5, 0x33, 0x62, 0x21, 0x4e, 0x29, 0x72, 0x71, 0x4a, 0x0d, 0x5d, 0x7c, 0x92, 0x2e, 0x2c, 0x32,
	0x6a, 0x05, 0xbe, 0xcd, 0x70, 0x3e, 0x35, 0x3d, 0x2e, 0x8a, 0x1a, 0x2b, 0xf0, 0x39, 0xf5, 0x39,
	0xce, 0xa6, 0xa1, 0xc7, 0x45, 0x6d, 0x17, 0x60, 0x8f, 0x32, 0xbe, 0x35, 0xd0, 0xc5, 0x32, 0x5d,
	0x82, 0x06, 0x77, 0x3c, 0xca, 0xb8, 0xe9, 0x85, 0xd8, 0x73, 0x4d, 0x4f, 0x00, 0xe3, 0x09, 0x54,
	0x53, 0x13, 0xe8, 0x40, 0xcd, 0xe2, 0xc7, 0xaa, 0x57, 0xf1, 0xa9, 0xbd, 0x05, 0x4b, 0xe3, 0x1e,
	0x25, 0x99, 0x1e, 0x1b, 0xc6, 0x64, 0x7a, 0x6c, 0xa8, 0xfd, 0x01, 0xac, 0x0e, 0x28, 0xdf, 0x95,
	0x4b, 0x3d, 0x70, 0x5c, 0xea, 0xf3, 0x62, 0x16, 0xa5, 0x78, 0x50, 0xcd, 0xf0, 0x20, 0xcb, 0xbb,
	0x5a, 0x9e, 0x77, 0x17, 0xa1, 0xe1, 0x30, 0x83, 0x61, 0xcf, 0xc8, 0xa3, 0xba, 0x5e, 0x77, 0x98,
	0x1c, 0x49, 0xfb, 0x8b, 0x0a, 0xac, 0x0f, 0x28, 0x7f, 0x48, 0x8f, 0x1c, 0x8b, 0x7e, 0x79, 0xe0,
	0x70, 0xea, 0x3a, 0x0c, 0x09, 0xb8, 0x06, 0x6d, 0x1b, 0xa1, 0x86, 0x63, 0x1b, 0x02, 0xd8, 0xad,
	0x5c, 0xad, 0xdd, 0x6c, 0xe8, 0x4d, 0x09, 0xed, 0xdb, 0x8f, 0x1d, 0xc6, 0x55, 0xe7, 0x36, 0x75,
	0x29, 0xa7, 0x48, 0x16, 0x76, 0xfe, 0x10, 0xcb, 0x42, 0x38, 0x1c, 0x66, 0x58, 0x07, 0xd4, 0x3a,
	0x44, 0xb2, 0xea, 0xfa, 0xa2, 0xc3, 0x1e, 0x88, 0x22, 0x59, 0x83, 0xf9, 0x61, 0x14, 0x8c, 0x42,
	0x25, 0x34, 0xb2, 0xa0, 0xdd, 0x82, 0x73, 0x45, 0xc4, 0x14, 0xae, 0xdb, 0x5d, 0x58, 0x9b, 0x5c,
	0x37, 0x16, 0x66, 0xa7, 0x5b, 0xc9, 0x4d, 0xf7, 0x18, 0x2e, 0x26, 0x8d, 0xb6, 0x46, 0x3c, 0xd8,
	0x75, 0xcd, 0x93, 0x01, 0x37, 0xf9, 0x88, 0xbd, 0x96, 0x5e, 0xdc, 0x84, 0x8e, 0x39, 0xe2, 0x81,
	0x11, 0xba, 0xe6, 0x89, 0xc1, 0xb0, 0x1f, 0x35, 0xd3, 0xb6, 0x99, 0xe9, 0x5d, 0xfb, 0x1c, 0x2e,
	0x95, 0x8f, 0xcc, 0xc2, 0xc2, 0x9e, 0x2a, 0x25, 0x3d, 0xad, 0x6f, 0x27, 0x13, 0xa7, 0x5c, 0xa8,
	0xc0, 0x6b, 0x51, 0xaf, 0x19, 0x70, 0xae, 0xa8, 0xa7, 0x53, 0x16, 0xb1, 0x90, 0xd4, 0x6a, 0x21,
	0xa9, 0x1f, 0x42, 0x2b, 0x19, 0x40, 0x90, 0xd8, 0x86, 0xea, 0x98, 0xc2, 0xaa, 0x63, 0xc7, 0x24,
	0x57, 0xc7, 0x24, 0x6b, 0x06, 0xb4, 0xd3, 0x4d, 0x58, 0x48, 0x3e, 0x85, 0x45, 0x45, 0x32, 0x36,
	0x5c, 0xda, 0xbc, 0xbc, 0x51, 0x64, 0x16, 0x37, 0xe2, 0x36, 0x31, 0xb6, 0x90, 0x31, 0x7a, 0x2c,
	0x04, 0x57, 0x12, 0x27, 0x0b, 0xda, 0xaf, 0xe0, 0x42, 0xc2, 0x88, 0xc7, 0xc1, 0xd0, 0xf1, 0x77,
	0x47, 0xec, 0x60, 0xcf, 0x7c, 0x3d, 0xc3, 0xd8, 0x81, 0x1a, 0x37, 0x87, 0xb1, 0xc2, 0x73, 0x73,
	0xa8, 0xbd, 0x07, 0xbd, 0xb2, 0xfe, 0x59, 0x28, 0x16, 0x20, 0x38, 0x54, 0x2b, 0x5a, 0x0d, 0x0e,
	0xb5, 0xfb, 0x69, 0x66, 0xde, 0x3f, 0xd1, 0x51, 0xa3, 0x05, 0x25, 0x29, 0x6d, 0xaf, 0x64, 0xb4,
	0x7d, 0x72, 0xc9, 0x86, 0x69, 0x36, 0x26, 0x7d, 0xbc, 0xf9, 0xa5, 0xfb, 0xcf, 0x2a, 0x2c, 0x2a,
	0xd4, 0xd3, 0x39, 0x29, 0xec, 0xa3, 0x6f, 0x7a, 0x54, 0xad, 0x0d, 0x7e, 0x0b, 0xb9, 0xb2, 0x4c,
	0xd7, 0x35, 0xb0, 0x42, 0xaa, 0x7e, 0x5d, 0x00, 0x9e, 0x88, 0xca, 0xd4, 0x94, 0xe7, 0x33, 0x53,
	0x7e, 0x07, 0x96, 0xad, 0x03, 0x13, 0xc5, 0x33, 0x96, 0xb7, 0x05, 0x44, 0x68, 0xc7, 0x60, 0x29,
	0x6f, 0x02, 0x31, 0xa2, 0xae, 0xc9, 0x9d, 0xc0, 0x67, 0x07, 0x4e, 0x18, 0x6f, 0x22, 0x2d, 0xbd,
	0x9d, 0x06, 0xf7, 0x6d, 0xf2, 0x36, 0xb4, 0x4c, 0xdf, 0xf1, 0x4c, 0x4e, 0x0d, 0x8f, 0x7a, 0x94,
	0x75, 0xeb, 0xd2, 0xb6, 0x29, 0xe0, 0x8e, 0x80, 0x91, 0xb7, 0x60, 0xc9, 0x8a, 0xa8, 0xc0, 0x11,
	0x46, 0xbf, 0xdb, 0xc0, 0x0d, 0x00, 0x24, 0x68, 0xcf, 0xf1, 0xa8, 0x40, 0x18, 0x85, 0xf6, 0x18,
	0x01, 0x24, 0x82, 0x04, 0x21, 0xc2, 0x35, 0x68, 0x8f, 0x18, 0x8d, 0x0c, 0x8f, 0x0d, 0x0d, 0x2b,
	0x18, 0xf9, 0xbc, 0xbb, 0x84, 0xe4, 0x34, 0x05, 0x74, 0x87, 0x0d, 0x1f, 0x08, 0x98, 0xf6, 0x2e,
	0x90, 0xcf, 0x82, 0xc8, 0x33, 0xf9, 0x76, 0xc8, 0xb7, 0x7c, 0xf6, 0x4a, 0xaa, 0xca, 0x2a, 0xcc,
	0x0f, 0x43, 0x6e, 0x98, 0xca, 0xe6, 0xcd, 0x0d, 0x43, 0xbe, 0xa5, 0xed, 0xc2, 0xea, 0x04, 0x2a,
	0x0b, 0xc9, 0x8f, 0xa0, 0x2e, 0x86, 0x18, 0x5b, 0xe9, 0xa5, 0xcd, 0x2b, 0x92, 0xd1, 0x39, 0xcf,
	0x40, 0xb6, 0xdd, 0x61, 0x43, 0x7d, 0xd1, 0x63, 0x43, 0x61, 0xc0, 0xb5, 0x3b, 0xb0, 0xb6, 0x4d,
	0xf9, 0x2f, 0x46, 0x8e, 0x75, 0xa8, 0xd3, 0xd0, 0x3d, 0x79, 0xac, 0xcc, 0x7f, 0x99, 0xfc, 0x69,
	0x9f, 0xa0, 0xc4, 0xe6, 0x1b, 0xb0, 0x50, 0x68, 0x4a, 0x24, 0x00, 0xe9, 0xcd, 0xa2, 0x11, 0xc5,
	0x28, 0xda, 0x7b, 0xb0, 0xb2, 0x4d, 0xc7, 0x7b, 0xb6, 0xa0, 0x61, 0xda, 0x28, 0x5f, 0x00, 0xc9,
	0x63, 0x7f, 0xb7, 0x79, 0xfe, 0x04, 0x3a, 0xa2, 0xc3, 0xdd, 0x3d, 0x9d, 0xbe, 0xec, 0xfb, 0xfb,
	0xc1, 0x6c, 0x7b, 0xac, 0xf6, 0xc7, 0x15, 0x49, 0x7e, 0xaa, 0x3d, 0x0b, 0x85, 0xbc, 0x85, 0xae,
	0x69, 0xd1, 0x83, 0xc0, 0xb5, 0x69, 0x64, 0x30, 0x1e, 0x29, 0x6e, 0xb5, 0x53, 0xe0, 0x01, 0x8f,
	0x84, 0xdc, 0x87, 0x51, 0xe0, 0x85, 0x3c, 0xee, 0xb9, 0xa1, 0xd7, 0x25, 0xa0, 0x6f, 0x93, 0xeb,
	0xd0, 0x56, 0x95, 0x47, 0x34, 0x12, 0xae, 0x8e, 0x52, 0x99, 0x96, 0x84, 0x3e, 0x93, 0x40, 0x4d,
	0x43, 0xcb, 0xf8, 0x94, 0xd1, 0xa8, 0x94, 0x7e, 0x8d, 0xc1, 0x72, 0x06, 0x87, 0x85, 0xe4, 0x0e,
	0xac, 0xa1, 0x0c, 0xf2, 0xc0, 0x36, 0x4f, 0x52, 0x92, 0x28, 0x5b, 0xad, 0x88, 0xba, 0x3d, 0x51,
	0x15, 0x8b, 0x63, 0xaa, 0x01, 0x37, 0xdd, 0x54, 0x83, 0x6a, 0xba, 0x01, 0x37, 0xdd, 0xb1, 0xfc,
	0x5e, 0xc7, 0xa5, 0x79, 0x2a, 0x45, 0x5a, 0x68, 0x62, 0x31, 0x6d, 0x87, 0xc8, 0xd2, 0x0c, 0x1a,
	0x0b, 0xc9, 0x07, 0xb0, 0xc6, 0x4d, 0xf7, 0x90, 0xda, 0x46, 0xc2, 0xc3, 0x84, 0x3c, 0x22, 0xeb,
	0x94, 0xd1, 0x91, 0xf4, 0xbd, 0x0d, 0xad, 0x7d, 0xd3, 0x75, 0x5f, 0x98, 0xd6, 0xa1, 0x11, 0x9a,
	0x43, 0xaa, 0x08, 0x6b, 0xc6, 0xc0, 0x5d, 0x73, 0x48, 0xb5, 0x5d, 0xb1, 0x58, 0xbe, 0x30, 0x85,
	0x0f, 0x29, 0xb3, 0x8a, 0x99, 0xdd, 0x83, 0xba, 0xef, 0x58, 0x87, 0x68, 0x8b, 0x14, 0x4f, 0xe2,
	0xb2, 0xc0, 0x66, 0xf4, 0x58, 0x39, 0x53, 0xe2, 0x53, 0xbb, 0x2e, 0x96, 0x36, 0xd5, 0x23, 0x43,
	0x0f, 0xd0, 0xa6, 0xcc, 0x8a, 0x35, 0x54, 0x7c, 0x6b, 0xcf, 0x85, 0x3e, 0xf9, 0x3a, 0x15, 0x92,
	0x48, 0x7d, 0x1b, 0x55, 0xe4, 0x8d, 0xba, 0xdc, 0x52, 0x01, 0x27, 0x46, 0x38, 0x5d, 0x01, 0xdf,
	0x46, 0xd9, 0xd8, 0xa6, 0xfe, 0x14, 0x7f, 0x47, 0xfb, 0xa9, 0x54, 0x93, 0x04, 0x89, 0x85, 0xe4,
	0x16, 0xac, 0x0c, 0xa9, 0x6f, 0x20, 0x35, 0xf4, 0xd8, 0xa2, 0xd4, 0xa6, 0xb6, 0xda, 0xc2, 0x96,
	0x87, 0x72, 0x49, 0x1e, 0x29, 0xb0, 0xf6, 0xd7, 0x15, 0xe4, 0xf2, 0x80, 0x07, 0xd1, 0xc9, 0xe7,
	0x0e, 0x13, 0x3f, 0xaf, 0x35, 0xfb, 0x0b, 0x50, 0xc7, 0xc6, 0xc9, 0xf4, 0x17, 0xb1, 0xdc, 0xb7,
	0xc9, 0x15, 0x58, 0xf2, 0xcc, 0x63, 0x83, 0x51, 0x9f, 0x1b, 0xa6, 0xf4, 0x68, 0x6b, 0x7a, 0xc3,
	0x33, 0x8f, 0x07, 0xd4, 0xe7, 0x5b, 0x5c, 0x70, 0x85, 0x39, 0xbf, 0x47, 0xd5, 0x1e, 0x82, 0xdf,
	0xda, 0x5f, 0x55, 0xa0, 0x8e, 0x34, 0xed, 0xb0, 0x21, 0x3a, 0xee, 0x27, 0x21, 0x55, 0xd4, 0xe0,
	0x77, 0x3a, 0x24, 0xa8, 0x66, 0x42, 0x02, 0x41, 0xba, 0xf0, 0xf2, 0x05, 0x11, 0x4d, 0x5d, 0x7c,
	0x0a, 0xce, 0x64, 0x07, 0x5f, 0x60, 0x72, 0xe4, 0x75, 0x58, 0x10, 0xea, 0xa2, 0xf6, 0xaf, 0x86,
	0x3e, 0xef, 0xb1, 0x61, 0xdf, 0x26, 0xe7, 0x40, 0x20, 0xd8, 0x34, 0x52, 0xb1, 0x8e, 0x2a, 0x69,
	0x36, 0xac, 0x4e, 0x2c, 0xd5, 0x34, 0x23, 0x97, 0xdf, 0xb5, 0xe3, 0x09, 0x8d, 0x8d, 0xdc, 0x78,
	0xea, 0xd5, 0xd4, 0xd4, 0x7f, 0x85, 0x1c, 0x1d, 0xc8, 0xee, 0x5f, 0x57, 0x18, 0xcb, 0xd9, 0xa1,
	0xfd, 0x6b, 0x15, 0xd5, 0x3f, 0x3d, 0x00, 0x0b, 0xb1, 0x81, 0x45, 0xfd, 0xb1, 0x65, 0x6f, 0xe8,
	0x8b, 0x58, 0x96, 0xf1, 0x88, 0xed, 0x98, 0x6e, 0x30, 0x4c, 0xd9, 0x42, 0x09, 0xe8, 0xdb, 0xe4,
	0x97, 0xd0, 0x92, 0x32, 0x1c, 0x84, 0xb8, 0x5d, 0x77, 0x6b, 0xb8, 0x02, 0x1f, 0x15, 0xaf, 0xc0,
	0xc4, 0xb8, 0x1b, 0xf8, 0xf5, 0x85, 0x6c, 0xab, 0x37, 0xa3, 0x54, 0x89, 0x6c, 0xc0, 0x2a, 0xf5,
	0xcd, 0x17, 0x2e, 0x35, 0xac, 0x11, 0xe3, 0x81, 0x67, 0x60, 0xad, 0x8a, 0x88, 0x56, 0x64, 0xd5,
	0x03, 0xac, 0xc1, 0x4e, 0x84, 0xd8, 0x2b, 0x44, 0x25, 0x0a, 0x09, 0x63, 0x97, 0x65, 0xc5, 0x03,
	0x09, 0xef, 0xdb, 0xbd, 0x7b, 0xd0, 0x4c, 0x8f, 0x9c, 0xf2, 0x8e, 0x1a, 0xe8, 0x1d, 0x95, 0x8a,
	0x57, 0x9a, 0x3d, 0x03, 0xb1, 0x40, 0x6f, 0x9a, 0x3d, 0xff, 0x58, 0x49, 0xd8, 0xa3, 0x06, 0x90,
	0xa6, 0x42, 0xb2, 0x07, 0x8d, 0x5f, 0x45, 0x05, 0xda, 0x02, 0x82, 0x9e, 0x58, 0x9a, 0x7b, 0xd5,
	0x2c, 0xf7, 0xae, 0x43, 0x5b, 0x18, 0x59, 0x11, 0xaf, 0xf9, 0xb6, 0x21, 0x42, 0x6e, 0xb5, 0x59,
	0x25, 0xd0, 0xa7, 0x91, 0x2b, 0xcc, 0x7a, 0x0a, 0xcd, 0x1b, 0x31, 0xc7, 0x42, 0x64, 0xe9, 0xf3,
	0x91, 0xa4, 0x6e, 0x47, 0x54, 0x3d, 0x8d, 0x5c, 0xed, 0x25, 0xc6, 0xc1, 0x48, 0xe7, 0x6e, 0x14,
	0x0c, 0x23, 0xca, 0xd8, 0x9b, 0xb6, 0x1c, 0xc2, 0xf9, 0x96, 0x11, 0x67, 0x4b, 0xaf, 0x06, 0xa1,
	0x76, 0x03, 0x43, 0xc8, 0xdc, 0x90, 0x05, 0x4e, 0xfa, 0xd7, 0xd0, 0x1a, 0x70, 0x33, 0x8a, 0x65,
	0xed, 0xcd, 0x32, 0xe8, 0x2a, 0xb4, 0xd3, 0x9d, 0x17, 0x0c, 0xff, 0x2d, 0x1a, 0x6e, 0xac, 0x2f,
	0xf7, 0x5c, 0x5e, 0x9f, 0x80, 0x3f, 0xab, 0x24, 0x22, 0x38, 0xf6, 0x1a, 0x2e, 0x03, 0xf0, 0xc8,
	0x19, 0x0e, 0x85, 0x1f, 0xc0, 0xc6, 0xb9, 0x0f, 0x09, 0xd9, 0x63, 0x2a, 0x3e, 0xa4, 0xc7, 0xa1,
	0x13, 0xa5, 0xc2, 0xfe, 0x47, 0x58, 0x46, 0x7b, 0x97, 0x84, 0xc2, 0x2d, 0x5d, 0x95, 0x50, 0xe8,
	0x90, 0x86, 0x94, 0xf7, 0xdf, 0x40, 0x88, 0x10, 0x3a, 0xed, 0x1b, 0x21, 0x00, 0xbe, 0xbd, 0x25,
	0x5d, 0x70, 0x5b, 0xf8, 0xe0, 0x67, 0x0a, 0x19, 0x53, 0xce, 0xbc, 0x8d, 0xde, 0xbc, 0x92, 0xc8,
	0xd8, 0x99, 0xc7, 0x9e, 0xb4, 0x73, 0x82, 0xd7, 0xf9, 0xde, 0x59, 0xa8, 0x19, 0x40, 0x06, 0x94,
	0xeb, 0xa9, 0xf0, 0xe0, 0x6c, 0x83, 0x16, 0x84, 0x1a, 0xb5, 0xa2, 0x50, 0x43, 0x5b, 0x47, 0xb9,
	0xce, 0x0e, 0xc0, 0x42, 0xed, 0x3d, 0x58, 0xde, 0xb2, 0xed, 0xfb, 0xae, 0x69, 0x1d, 0xc6, 0x2e,
	0xf7, 0x05, 0xa8, 0x8f, 0xd2, 0xb9, 0x96, 0x96, 0xbe, 0x38, 0x72, 0x30, 0xcd, 0xa2, 0x11, 0xe8,
	0x64, 0xb1, 0x65, 0x0f, 0x0f, 0xa9, 0x3b, 0x43, 0x0f, 0x59, 0x6c, 0x16, 0x6a, 0xff, 0x56, 0x05,
	0x82, 0x66, 0x6b, 0x8f, 0x1e, 0x73, 0xe9, 0x33, 0x8b, 0x5e, 0xe2, 0xc4, 0x56, 0x25, 0x95, 0xd8,
	0xda, 0x83, 0xa5, 0x7d, 0x44, 0x30, 0x70, 0xeb, 0x14, 0x0b, 0xd1, 0xde, 0xbc, 0x5b, 0x6c, 0x95,
	0x27, 0xbb, 0x54, 0x0e, 0xf9, 0xde, 0x49, 0x48, 0x75, 0xd8, 0x1f, 0x7f, 0x0b, 0x89, 0x70, 0x4d,
	0xc6, 0x95, 0x25, 0x96, 0x6c, 0x6b, 0x08, 0x08, 0x76, 0xa1, 0xfd, 0x7d, 0x05, 0x20, 0x69, 0x49,
	0xce, 0xc5, 0x71, 0x92, 0x28, 0x19, 0x0f, 0xe9, 0xbe, 0x39, 0x72, 0x79, 0xe7, 0x07, 0xe4, 0x0a,
	0xf4, 0x52, 0x70, 0x31, 0xf0, 0x6e, 0x14, 0x58, 0x71, 0x7d, 0x85, 0x5c, 0x82, 0x6e, 0xaa, 0x5e,
	0xa7, 0x5e, 0x70, 0x44, 0xef, 0x47, 0xa6, 0x75, 0x48, 0x79, 0xa7, 0x4a, 0x2e, 0xc0, 0x7a, 0x41,
	0xeb, 0x67, 0x1f, 0x76, 0x6a, 0xb9, 0x8e, 0x1f, 0x07, 0xfe, 0x10, 0xe9, 0x7a, 0x48, 0x39, 0xb5,
	0x78, 0x67, 0x4e, 0x7b, 0x0e, 0xab, 0x13, 0x73, 0x65, 0xa1, 0x08, 0x9e, 0xc5, 0x9a, 0x31, 0xe5,
	0x82, 0xc9, 0x82, 0x08, 0x16, 0x6d, 0xea, 0x9a, 0x27, 0x18, 0x2b, 0xb2, 0x6e, 0xf5, 0x6a, 0x4d,
	0x04, 0x8b, 0x08, 0x12, 0xb1, 0x22, 0x43, 0x6f, 0xd2, 0xe4, 0x66, 0x1c, 0x2f, 0x8b, 0x6f, 0xed,
	0x0a, 0xc0, 0x9e, 0x54, 0xba, 0x62, 0x77, 0x6d, 0x07, 0x96, 0xc6, 0xf5, 0x2c, 0x14, 0x9e, 0x89,
	0xc3, 0x8c, 0x20, 0xa4, 0xbe, 0x32, 0x1f, 0x0b, 0x0e, 0xfb, 0x22, 0xa4, 0x3e, 0xb9, 0x0e, 0xcb,
	0xaa, 0xc2, 0xf0, 0x03, 0xf4, 0xe4, 0x94, 0xd6, 0x36, 0x25, 0xc2, 0x93, 0x40, 0x78, 0x71, 0xda,
	0x1f, 0xc1, 0xe5, 0x01, 0xe5, 0x5b, 0xfd, 0xd8, 0xdf, 0xce, 0x44, 0xd7, 0xc5, 0x76, 0x47, 0x6a,
	0x4a, 0x75, 0xac, 0x29, 0x3f, 0xce, 0x28, 0x7f, 0x7b, 0xf3, 0x5a, 0xb1, 0x8c, 0xe4, 0xba, 0x56,
	0x6d, 0xb4, 0xab, 0x70, 0x65, 0x1a, 0x01, 0x2c, 0xd4, 0x7e, 0x53, 0x43, 0x7d, 0x1a, 0xa3, 0x94,
	0x5b, 0xc4, 0x3c, 0x65, 0x33, 0xe7, 0x23, 0xbe, 0x82, 0x66, 0x5a, 0x97, 0x71, 0xef, 0x6f, 0x6f,
	0x7e, 0x52, 0xe2, 0x8c, 0x4d, 0xd2, 0xb4, 0x91, 0x51, 0xfa, 0x4c, 0x5f, 0xe4, 0x03, 0x98, 0x43,
	0x2e, 0x2c, 0x60, 0x5a, 0xe6, 0x52, 0x71, 0x9f, 0x5b, 0x7d, 0xc1, 0x15, 0x1d, 0x31, 0x53, 0x0b,
	0xbb, 0x38, 0xfb, 0xc2, 0x92, 0xfb, 0xd0, 0x34, 0x1d, 0xe9, 0xc2, 0xa3, 0x02, 0xd7, 0xb1, 0x8f,
	0xab, 0xd3, 0xc6, 0x95, 0xda, 0x6a, 0x3a, 0xf1, 0x37, 0xda, 0xf5, 0x60, 0x14, 0x59, 0x32, 0x15,
	0x22, 0xec, 0x3a, 0x96, 0xb4, 0x67, 0xc2, 0xf9, 0x49, 0xcd, 0xed, 0xbc, 0x50, 0x8b, 0xa4, 0xfc,
	0xd4, 0x3f, 0xf4, 0x83, 0x57, 0x7e, 0xe7, 0x07, 0x42, 0x81, 0xd3, 0x15, 0x9f, 0x45, 0x0e, 0xf5,
	0xed, 0x4e, 0x85, 0xac, 0xc3, 0x4a, 0x1a, 0xfe, 0x38, 0x38, 0xa2, 0x51, 0xa7, 0xaa, 0xfd, 0x43,
	0x05, 0x16, 0x24, 0x29, 0x13, 0xf6, 0x78, 0x0d, 0xe6, 0x19, 0x3f, 0x71, 0xe3, 0xb8, 0x4d, 0x16,
	0xd2, 0x41, 0xdb, 0x3c, 0x06, 0x6d, 0xe4, 0x63, 0xe5, 0xea, 0xcf, 0xe1, 0x74, 0x7f, 0x38, 0x6d,
	0xba, 0x1b, 0x38, 0x5f, 0x44, 0xd7, 0x6e, 0xc3, 0x1c, 0xce, 0xb8, 0x03, 0xcd, 0x9c, 0xad, 0x59,
	0x81, 0x16, 0x42, 0x9e, 0x04, 0x0f, 0x0e, 0x82, 0x80, 0xd1, 0x4e, 0x45, 0xee, 0x2c, 0x79, 0xe6,
	0xb3, 0x50, 0xfb, 0xcb, 0x05, 0x58, 0xd7, 0xa9, 0x45, 0x9d, 0x23, 0xba, 0xc3, 0x86, 0x9f, 0x45,
	0x81, 0x27, 0x62, 0xdf, 0x62, 0x59, 0xd5, 0xa0, 0x95, 0x10, 0x94, 0x6c, 0xe0, 0x4b, 0xa6, 0xb3,
	0x3b, 0xde, 0xc2, 0x9f, 0xc8, 0xb8, 0x00, 0xe7, 0x53, 0x9b, 0x6e, 0x7f, 0x0b, 0x06, 0xdd, 0xd8,
	0x61, 0x43, 0x9c, 0xa1, 0x08, 0x16, 0xf6, 0x72, 0x21, 0xcf, 0x5c, 0x61, 0xc8, 0x33, 0x9f, 0x84,
	0x3c, 0x49, 0x64, 0xb3, 0x90, 0x8e, 0x6c, 0xf2, 0x52, 0xb5, 0xf8, 0x1a, 0x52, 0x75, 0x15, 0x9a,
	0x8e, 0x67, 0xbc, 0x18, 0x31, 0x27, 0x91, 0xcc, 0x96, 0x0e, 0x8e, 0x77, 0x7f, 0xc4, 0x1c, 0xc4,
	0xb8, 0x01, 0xcb, 0x4e, 0xe2, 0x84, 0x23, 0x92, 0x14, 0xc0, 0x96, 0x13, 0xbb, 0xe0, 0x88, 0x77,
	0x05, 0x96, 0x04, 0x9e, 0x67, 0x4b, 0x1c, 0x90, 0x3e, 0x90, 0xe3, 0x3d, 0xf0, 0x6c, 0xac, 0x7f,
	0x06, 0x0d, 0x7a, 0xcc, 0x23, 0xd3, 0xf0, 0xcc, 0xb0, 0xbb, 0x84, 0x71, 0xc5, 0x8f, 0x66, 0x59,
	0xc1, 0x47, 0xa2, 0xf1, 0x8e, 0x19, 0x3e, 0xf2, 0x79, 0x74, 0xa2, 0xd7, 0xa9, 0x2a, 0x92, 0xbb,
	0x70, 0x0e, 0x9d, 0x21, 0xd3, 0xe2, 0xee, 0x89, 0x11, 0x51, 0xd3, 0x3a, 0x30, 0x5c, 0xc7, 0x73,
	0x78, 0xb7, 0x89, 0x36, 0x76, 0x55, 0x78, 0x46, 0x58, 0xa9, 0x8b, 0xba, 0xc7, 0xa2, 0x8a, 0xfc,
	0x02, 0xd6, 0x1d, 0x9f, 0xd3, 0xc8, 0xb4, 0xb8, 0x73, 0x44, 0x8d, 0xa1, 0xe9, 0x51, 0xc3, 0xf1,
	0xf7, 0x83, 0x6e, 0x2b, 0x9d, 0xa8, 0xcd, 0xe5, 0xb5, 0xb6, 0x4d, 0x8f, 0xa2, 0x7c, 0xad, 0xa6,
	0xda, 0xc6, 0xc0, 0xde, 0x6f, 0x43, 0x2b, 0x43, 0xa2, 0xe0, 0xe3, 0x21, 0x3d, 0x89, 0x0f, 0x4d,
	0x0e, 0xe9, 0x89, 0xd0, 0x9b, 0x23, 0xd3, 0x1d, 0x8d, 0xf5, 0x06, 0x0b, 0xbf, 0x55, 0xbd, 0x57,
	0xd1, 0x5e, 0xc0, 0xa2, 0x92, 0x10, 0xb2, 0x0a, 0xcb, 0xea, 0xd3, 0x48, 0x74, 0xb7, 0x03, 0xcd,
	0x18, 0x28, 0xb6, 0xbb, 0x4e, 0x85, 0xac, 0x41, 0x27, 0x86, 0x3c, 0xf2, 0x02, 0xee, 0x58, 0x81,
	0xdf, 0xa9, 0x91, 0x1e, 0x9c, 0x8b, 0xa1, 0x7d, 0x6f, 0x87, 0x0d, 0xc7, 0x7a, 0xd1, 0x59, 0xd0,
	0xba, 0x70, 0xae, 0x68, 0x65, 0x59, 0xa8, 0xfd, 0x4d, 0x05, 0x7a, 0xa2, 0xf0, 0x48, 0x4c, 0x2b,
	0x36, 0x61, 0x4f, 0x02, 0xee, 0xec, 0x9f, 0xbc, 0xbe, 0xc2, 0xe4, 0xa5, 0xb3, 0x36, 0xbb, 0x74,
	0x6a, 0x97, 0xe1, 0x62, 0x29, 0x5d, 0x2c, 0xd4, 0x8e, 0xa1, 0xf5, 0x84, 0xbe, 0x9a, 0xb6, 0x45,
	0x63, 0x16, 0xd9, 0x75, 0xc6, 0xa9, 0x3d, 0x49, 0x27, 0x58, 0xae, 0xa3, 0xf2, 0x7a, 0x29, 0xb3,
	0x5a, 0x4b, 0x9b, 0x55, 0x72, 0x09, 0x20, 0xa2, 0x2f, 0x0d, 0x9b, 0x1e, 0x25, 0x87, 0xab, 0xf5,
	0x88, 0xbe, 0x7c, 0x48, 0x8f, 0xfa, 0xb6, 0xf6, 0xdf, 0x15, 0x68, 0xa7, 0x87, 0xfe, 0xee, 0xbb,
	0x3f, 0x79, 0x0f, 0x88, 0x0a, 0x90, 0xbd, 0x91, 0xcb, 0xe5, 0xca, 0xc5, 0xc7, 0x59, 0x1d, 0x59,
	0xb3, 0x23, 0x2a, 0x04, 0x32, 0x7a, 0xf3, 0x0a, 0x3b, 0xa4, 0xf1, 0xb9, 0x62, 0x43, 0x42, 0x76,
	0x29, 0x27, 0x3f, 0x87, 0x06, 0x66, 0x11, 0x8f, 0x1c, 0xfa, 0x0a, 0x2d, 0xc9, 0xd2, 0xe6, 0xfb,
	0x65, 0x2b, 0xaf, 0x58, 0xf6, 0x44, 0xb8, 0x50, 0xae, 0x58, 0xee, 0x67, 0x0e, 0x7d, 0xa5, 0xd7,
	0x47, 0xea, 0x4b, 0xfb, 0x08, 0x7d, 0x57, 0xca, 0xe9, 0x4c, 0x27, 0x49, 0xab, 0xb0, 0x92, 0x6b,
	0xc5, 0x42, 0xed, 0x0f, 0x61, 0x75, 0x70, 0x10, 0xbc, 0x12, 0x66, 0xc3, 0xf1, 0x47, 0x78, 0x8c,
	0xfc, 0x66, 0x0f, 0xc4, 0xa7, 0x73, 0xed, 0x5f, 0x2a, 0x05, 0xe3, 0xcb, 0x4c, 0x22, 0x3b, 0x08,
	0x5e, 0x29, 0xbe, 0xe1, 0xb7, 0x10, 0x71, 0xeb, 0xc0, 0xe4, 0x86, 0x4b, 0xf7, 0xb9, 0xe1, 0x8f,
	0xbc, 0x58, 0xc4, 0x05, 0xf0, 0x31, 0xdd, 0xe7, 0x4f, 0x46, 0x1e, 0xf9, 0x18, 0xce, 0x5b, 0xaa,
	0x2b, 0x43, 0x86, 0xda, 0x63, 0x6c, 0x49, 0xd6, 0x5a, 0x5c, 0xad, 0x8b, 0xda, 0xa2, 0x66, 0x32,
	0xcd, 0x3b, 0x6e, 0x36, 0x97, 0x6d, 0x86, 0x99, 0x5e, 0xd5, 0x4c, 0xfb, 0x1a, 0x96, 0xbf, 0xb7,
	0x85, 0xd3, 0x56, 0x72, 0x9d, 0x63, 0x6c, 0xb6, 0x92, 0x1c, 0x6c, 0x09, 0x41, 0x2d, 0x1e, 0xf1,
	0x22, 0x34, 0xc6, 0x27, 0xd5, 0xe3, 0x84, 0x92, 0x3a, 0xa4, 0x16, 0x95, 0x9e, 0x19, 0x1d, 0x52,
	0x9e, 0x8c, 0x58, 0x97, 0x80, 0xbe, 0xad, 0xfd, 0x4f, 0x05, 0xce, 0x97, 0x88, 0x9f, 0x0a, 0x71,
	0x03, 0xdf, 0x75, 0xfc, 0x38, 0x55, 0x58, 0x77, 0xd8, 0x17, 0x58, 0x8e, 0x3d, 0x8d, 0xea, 0x38,
	0x3d, 0x2c, 0x42, 0xaf, 0x88, 0x0e, 0xe5, 0x41, 0x90, 0x0a, 0xb0, 0x23, 0x3a, 0xc4, 0x53, 0xa0,
	0x0c, 0x09, 0x73, 0x59, 0x12, 0x32, 0x49, 0xe8, 0xf9, 0x5c, 0x12, 0xba, 0x07, 0xf5, 0x17, 0x4e,
	0xc4, 0x0f, 0x6c, 0xf3, 0x44, 0x1d, 0x78, 0x8d, 0xcb, 0x42, 0x3a, 0x92, 0xe3, 0xf9, 0x03, 0x7a,
	0xac, 0x6e, 0x4b, 0x2c, 0xc5, 0x13, 0xff, 0x9c, 0x1e, 0x2b, 0xd3, 0x33, 0xde, 0x34, 0xeb, 0x63,
	0xd3, 0xa3, 0x76, 0x4c, 0xed, 0x78, 0x62, 0x81, 0xa7, 0x99, 0x91, 0x8c, 0x4a, 0x57, 0xbf, 0x9b,
	0x4a, 0xff, 0xd7, 0x22, 0xf4, 0xd4, 0x5e, 0xb0, 0x1d, 0x05, 0xa3, 0xf0, 0x54, 0x0f, 0xe9, 0x16,
	0xac, 0xe0, 0x1d, 0x01, 0x83, 0x53, 0x2f, 0x74, 0x4d, 0x9e, 0x3a, 0xa3, 0x59, 0xc6, 0x8a, 0x3d,
	0x05, 0xef, 0xa7, 0x70, 0x1d, 0x9f, 0x71, 0xd3, 0xb7, 0x52, 0xd2, 0x26, 0x71, 0xfb, 0x0a, 0xde,
	0xb7, 0xc9, 0x20, 0xe5, 0x55, 0x49, 0x2f, 0xf1, 0xde, 0x54, 0x9f, 0xa0, 0x80, 0xda, 0xa9, 0xae,
	0xd5, 0x7c, 0xa1, 0x6b, 0xb5, 0x90, 0x71, 0xad, 0x18, 0x7d, 0x99, 0x1c, 0x55, 0xce, 0x33, 0xfa,
	0xb2, 0x6f, 0x93, 0x47, 0xb8, 0xc1, 0xa9, 0x29, 0x27, 0x1e, 0x7b, 0xa9, 0x0b, 0x8b, 0x74, 0x21,
	0x15, 0x4b, 0xa6, 0x33, 0x2e, 0x08, 0xdf, 0x89, 0x9b, 0xd1, 0x90, 0x72, 0x43, 0x69, 0x1d, 0xeb,
	0x36, 0x30, 0x31, 0xd0, 0x92, 0x60, 0x79, 0x58, 0x8c, 0xd1, 0xa9, 0xc2, 0x1b, 0x09, 0x1c, 0x40,
	0x1c, 0x90, 0xa0, 0xa7, 0x8e, 0xcd, 0x88, 0x01, 0x4b, 0x23, 0xc7, 0x36, 0x78, 0x20, 0xbd, 0x14,
	0xe9, 0x3e, 0xfd, 0x6c, 0xe6, 0xa5, 0x7a, 0xea, 0xd8, 0x7b, 0x81, 0xf0, 0x56, 0xa4, 0x13, 0xd5,
	0x18, 0xc5, 0x65, 0xf2, 0x75, 0xda, 0x3b, 0x6b, 0x62, 0xf7, 0x3f, 0x9d, 0xb9, 0xfb, 0x12, 0x17,
	0xad, 0xa7, 0x43, 0x3d, 0x3e, 0x14, 0x1b, 0xc7, 0x81, 0x95, 0x54, 0x1c, 0x78, 0x15, 0x9a, 0x78,
	0x9b, 0x21, 0x7b, 0xf8, 0x07, 0x02, 0xa6, 0x8f, 0x8f, 0xdd, 0xb3, 0x07, 0x42, 0xbd, 0x57, 0xd0,
	0xce, 0xce, 0x26, 0xed, 0x6f, 0xb5, 0xa4, 0xbf, 0xf5, 0x45, 0xda, 0xdf, 0x3a, 0xcd, 0xdd, 0x2c,
	0x5c, 0xaf, 0xf8, 0x28, 0x2f, 0x71, 0xd5, 0xfe, 0xff, 0xfb, 0x79, 0x97, 0xe1, 0x62, 0xe9, 0x94,
	0x58, 0xa8, 0xdd, 0x83, 0xa6, 0xf8, 0xde, 0x76, 0xf6, 0x79, 0x29, 0x43, 0x32, 0x13, 0xa8, 0xa8,
	0x09, 0x68, 0x7f, 0x57, 0x85, 0x4b, 0xaa, 0xe7, 0x41, 0x48, 0x2d, 0x07, 0x0f, 0x21, 0xa7, 0xdb,
	0x8d, 0x53, 0xb6, 0xa3, 0x6f, 0x26, 0x82, 0xaa, 0xad, 0xa9, 0x3c, 0x2a, 0x1c, 0x76, 0x23, 0x81,
	0x66, 0xed, 0xc0, 0xcf, 0xa0, 0x31, 0x74, 0xf6, 0xb9, 0x54, 0x99, 0x39, 0x14, 0x01, 0xad, 0xb8,
	0xfb, 0xf4, 0x82, 0xe8, 0xf5, 0xa1, 0xfa, 0xd2, 0x6e, 0x43, 0x3b, 0xdb, 0x37, 0x59, 0x82, 0xc5,
	0x84, 0x59, 0x4d, 0xa8, 0x6f, 0x8b, 0x55, 0x76, 0xf6, 0x79, 0xa7, 0xa2, 0xbd, 0x05, 0x97, 0xa7,
	0x50, 0xc9, 0x42, 0xed, 0xdf, 0x6b, 0xb0, 0xba, 0xd5, 0x1f, 0x50, 0xdf, 0x16, 0xbd, 0x05, 0xc8,
	0x1c, 0xb1, 0x6a, 0x85, 0xb6, 0xb5, 0x32, 0x83, 0x6d, 0xad, 0x16, 0xdb, 0xd6, 0xc4, 0xb4, 0xd5,
	0xd2, 0xa6, 0xed, 0x77, 0xb2, 0xa6, 0x64, 0x0e, 0x75, 0xfd, 0x5e, 0x99, 0x61, 0x9b, 0x20, 0x77,
	0x8a, 0x0d, 0xc9, 0x59, 0xb1, 0xf9, 0xbc, 0x15, 0xfb, 0x5e, 0xec, 0x40, 0x78, 0x06, 0x3b, 0xf0,
	0x79, 0xd6, 0x0e, 0x6c, 0xce, 0x30, 0xd9, 0x49, 0x03, 0xa0, 0xdd, 0x81, 0xb5, 0x49, 0x54, 0xb9,
	0x73, 0xd3, 0x08, 0x6f, 0x9b, 0xa8, 0x49, 0x2d, 0xd0, 0x28, 0xda, 0x61, 0x43, 0xed, 0x9f, 0xe7,
	0xa0, 0xbb, 0xd5, 0xbf, 0x6f, 0x72, 0xeb, 0xe0, 0xff, 0x8e, 0xfb, 0xdf, 0x66, 0xd9, 0x2c, 0x0f,
	0xf2, 0x7e, 0x52, 0x36, 0xf3, 0x62, 0xe2, 0xa6, 0xf0, 0x7a, 0x1f, 0x3a, 0x29, 0x5e, 0xcb, 0x9c,
	0xb7, 0x14, 0xa5, 0x1f, 0xcf, 0x38, 0xc6, 0x5e, 0x2c, 0x1f, 0x98, 0x18, 0x6f, 0x27, 0xe2, 0x22,
	0xca, 0xdf, 0x8b, 0xc8, 0x7c, 0x00, 0xad, 0xcc, 0xa0, 0x79, 0xc1, 0xad, 0x4c, 0x08, 0xee, 0xe8,
	0x0c, 0x42, 0xb6, 0x93, 0x15, 0xb2, 0x4f, 0x67, 0x5d, 0xea, 0x02, 0x49, 0xbb, 0x08, 0x17, 0x4a,
	0xf0, 0x59, 0xa8, 0xfd, 0x6d, 0x15, 0xd6, 0x74, 0xfa, 0xca, 0x8c, 0xec, 0x01, 0x15, 0xde, 0x8d,
	0x45, 0xb7, 0x6c, 0xbb, 0xd8, 0x0a, 0x7f, 0x03, 0x2d, 0xa6, 0x70, 0xd2, 0x07, 0x08, 0x9f, 0x96,
	0xd9, 0xda, 0xc9, 0x4e, 0x37, 0xe2, 0x22, 0x5a, 0xd8, 0x26, 0x4b, 0x95, 0x84, 0x83, 0x7d, 0x64,
	0xba, 0x8e, 0x6d, 0x08, 0x47, 0x59, 0xf9, 0xf8, 0x08, 0x78, 0x68, 0x9e, 0x08, 0xa5, 0x30, 0x6d,
	0x3b, 0x15, 0xdb, 0x2c, 0x98, 0xb6, 0x2d, 0xa2, 0x99, 0x5f, 0x42, 0x33, 0xdd, 0x27, 0xe9, 0xe2,
	0x09, 0xd1, 0xb8, 0x6c, 0x64, 0xf2, 0x96, 0x99, 0x1a, 0xdc, 0x7d, 0x3b, 0x15, 0x72, 0x1e, 0x4f,
	0xac, 0x12, 0xf8, 0x83, 0x51, 0x64, 0x9b, 0x27, 0x9d, 0xaa, 0x76, 0x1e, 0xd6, 0x0b, 0xe6, 0xc0,
	0xc2, 0x5b, 0xb7, 0xa1, 0x9d, 0x4d, 0xd0, 0x0a, 0x13, 0x1e, 0x43, 0x3a, 0x3f, 0x10, 0xd6, 0x1d,
	0x2f, 0x59, 0x5a, 0xb4, 0x53, 0xb9, 0x65, 0x00, 0x24, 0x59, 0x09, 0xb2, 0x0e, 0x2b, 0x49, 0x29,
	0x49, 0x48, 0x12, 0x68, 0x27, 0xe0, 0x6d, 0xd3, 0xa3, 0x9d, 0x0a, 0x59, 0x81, 0x56, 0x02, 0xdb,
	0xc5, 0x53, 0x8e, 0x55, 0x58, 0x4e, 0xa1, 0x09, 0x16, 0x76, 0x6a, 0xb7, 0xbe, 0x81, 0x96, 0xca,
	0x1f, 0x0c, 0x64, 0xca, 0x61, 0x09, 0x16, 0xfb, 0x3e, 0xae, 0x9c, 0xf4, 0x04, 0x62, 0xca, 0x76,
	0xcd, 0xa1, 0xe8, 0xb7, 0x05, 0x0d, 0x79, 0xa1, 0x51, 0x14, 0xab, 0xa4, 0x0d, 0xa0, 0xf6, 0xfc,
	0x2f, 0xe9, 0x8b, 0x4e, 0x8d, 0xb4, 0xa0, 0xde, 0xf7, 0x39, 0x8d, 0x7c, 0xd3, 0xed, 0xfc, 0x66,
	0xf1, 0xd6, 0xaf, 0x61, 0x29, 0xe5, 0x96, 0x8a, 0xc5, 0x4a, 0x15, 0x53, 0xab, 0xdb, 0x83, 0x73,
	0xe9, 0x8a, 0x81, 0xe3, 0x0f, 0x5d, 0x2a, 0x84, 0xb1, 0x53, 0x21, 0x17, 0x60, 0x3d, 0x5d, 0x87,
	0xe9, 0x07, 0xac, 0xaa, 0x92, 0xb7, 0xe0, 0x62, 0x61, 0xd5, 0xc0, 0x8a, 0x9c, 0x90, 0x77, 0x6a,
	0x9b, 0xff, 0x74, 0x0d, 0x5a, 0xfa, 0x83, 0x9d, 0x87, 0x63, 0xcf, 0x84, 0x38, 0xd0, 0xc9, 0x67,
	0x6a, 0xc9, 0xbb, 0x67, 0x4e, 0xe7, 0xf7, 0x6e, 0x9d, 0x15, 0x95, 0x85, 0x24, 0x00, 0x32, 0x99,
	0xea, 0x22, 0xb7, 0x67, 0x48, 0x37, 0xf6, 0xde, 0x3b, 0x3b, 0x32, 0x0b, 0xc9, 0xaf, 0xe1, 0x7c,
	0x89, 0xcf, 0x45, 0x3e, 0x98, 0xd5, 0xeb, 0xec, 0x7d, 0x38, 0x63, 0x0b, 0x16, 0x92, 0x3f, 0xad,
	0xc0, 0x85, 0x52, 0xef, 0x83, 0x6c, 0xce, 0xee, 0x54, 0xf5, 0xee, 0xce, 0xdc, 0x46, 0x2e, 0x43,
	0x49, 0xbe, 0xae, 0x6c, 0x19, 0xca, 0xd3, 0x8e, 0x65, 0xcb, 0x30, 0x25, 0x21, 0x48, 0xfe, 0xbc,
	0x82, 0xd7, 0x7f, 0x4b, 0x4e, 0xb0, 0xc8, 0xdd, 0xd3, 0x45, 0x68, 0xe2, 0xd0, 0xad, 0xf7, 0xd1,
	0xec, 0x8d, 0xa4, 0x04, 0x4e, 0xde, 0xa5, 0x2f, 0x93, 0xc0, 0xc2, 0x27, 0x00, 0x65, 0x12, 0x58,
	0x72, 0x45, 0xff, 0x09, 0x2c, 0x2a, 0x6b, 0x42, 0x4a, 0x72, 0xac, 0x49, 0x9e, 0xb4, 0xf7, 0xc3,
	0x53, 0x30, 0x58, 0x48, 0xf6, 0x61, 0x39, 0x77, 0x70, 0x4e, 0x6e, 0x96, 0x12, 0x94, 0x3b, 0xc0,
	0xef, 0xbd, 0x7b, 0x46, 0x4c, 0x16, 0x92, 0x2f, 0x01, 0x92, 0x44, 0x2a, 0x79, 0xbb, 0xb8, 0x61,
	0x26, 0xcb, 0xdb, 0xbb, 0x76, 0x3a, 0x12, 0x0b, 0xc9, 0x73, 0x68, 0x65, 0x12, 0x90, 0xe4, 0x46,
	0x71, 0xb3, 0x7c, 0x6e, 0xb3, 0xf7, 0xce, 0x99, 0xf0, 0xe4, 0x12, 0xe5, 0x0e, 0xa0, 0xcb, 0x96,
	0x68, 0xf2, 0x4c, 0xbe, 0x6c, 0x89, 0x8a, 0x4e, 0xb4, 0xbf, 0x85, 0x66, 0xfa, 0xfa, 0x01, 0xb9,
	0x5e, 0xe2, 0x5a, 0x64, 0x2f, 0x34, 0xf4, 0x6e, 0x9c, 0x05, 0x4d, 0x76, 0x9f, 0xbe, 0x9b, 0x50,
	0xd6, 0x7d, 0xee, 0xb6, 0x43, 0xef, 0xc6, 0x59, 0xd0, 0x58, 0x28, 0xcd, 0x7e, 0xf6, 0xea, 0x47,
	0xb9, 0xd9, 0x9f, 0xb8, 0x80, 0x52, 0x6e, 0xf6, 0x27, 0x6f, 0x93, 0x88, 0x99, 0xa4, 0xaf, 0xd2,
	0x94, 0xcd, 0x24, 0x77, 0x9d, 0xa7, 0x6c, 0x26, 0x13, 0xb7, 0x72, 0xbe, 0x04, 0x48, 0xee, 0x0a,
	0x95, 0x89, 0x6a, 0xe6, 0xaa, 0x52, 0x99, 0xa8, 0xe6, 0xae, 0x1c, 0xc9, 0x9d, 0x31, 0x73, 0x13,
	0x6a, 0xca, 0xce, 0x98, 0xbf, 0xa4, 0x35, 0x65, 0x67, 0x9c, 0xbc, 0x5c, 0xf5, 0x1c, 0xdf, 0x84,
	0x24, 0xf7, 0xd1, 0xc8, 0x29, 0x93, 0x8f, 0x6f, 0xc5, 0x95, 0x69, 0xc5, 0xe4, 0xe5, 0xb6, 0xd4,
	0x08, 0xf2, 0x26, 0xdf, 0x8d, 0x33, 0xdd, 0x1e, 0x3c, 0x75, 0x84, 0xe4, 0x76, 0xe3, 0x7e, 0x72,
	0x23, 0x4b, 0xdd, 0xdc, 0x2c, 0xd3, 0xbb, 0xc9, 0xbb, 0xb0, 0x65, 0x7a, 0x57, 0x74, 0x15, 0xf4,
	0x2b, 0x58, 0x4a, 0xdd, 0x39, 0x26, 0xd7, 0xca, 0x5a, 0xa6, 0x2f, 0x3a, 0xf7, 0xae, 0x9f, 0x01,
	0x8b, 0x85, 0xc4, 0x85, 0x95, 0x89, 0x6b, 0xc4, 0xe4, 0x56, 0x79, 0xdb, 0xfc, 0x8d, 0xe6, 0xde,
	0xed, 0x33, 0xe3, 0x8e, 0x15, 0x63, 0x7c, 0xaf, 0x78, 0x8a, 0x62, 0xa4, 0x2f, 0x28, 0x4f, 0x51,
	0x8c, 0xec, 0x15, 0x65, 0x6b, 0x7c, 0x37, 0x5e, 0xdd, 0x2d, 0x27, 0xe5, 0xbc, 0xcc, 0x5e, 0x54,
	0xef, 0xdd, 0x3c, 0x1b, 0x62, 0xcc, 0x8d, 0xf1, 0xe5, 0xfa, 0x72, 0x6e, 0xa4, 0xef, 0xe8, 0x97,
	0x73, 0x23, 0x7b, 0x4b, 0x1f, 0xb9, 0x91, 0x7b, 0x55, 0x51, 0xce, 0x8d, 0xc9, 0xf7, 0x1a, 0xe5,
	0xdc, 0x28, 0x7a, 0xaa, 0x21, 0x35, 0x24, 0x79, 0xcc, 0x30, 0x45, 0x43, 0x32, 0x2f, 0x26, 0xa6,
	0x68, 0x48, 0xee, 0x65, 0x84, 0x64, 0x48, 0xea, 0xfd, 0xc6, 0x14, 0x86, 0x64, 0xdf, 0x84, 0x4c,
	0x61, 0x48, 0xfe, 0x39, 0xc8, 0x3e, 0x2c, 0xe7, 0x5e, 0xc3, 0x94, 0xa9, 0xe1, 0xe4, 0xfb, 0x9a,
	0x32, 0x35, 0x2c, 0x7a, 0x5e, 0x13, 0xe0, 0x9d, 0xf6, 0xdc, 0x03, 0x2b, 0x52, 0xbe, 0xe2, 0x93,
	0xcf, 0xb9, 0xca, 0x5c, 0xa9, 0x92, 0x77, 0x5b, 0xbf, 0x8f, 0xef, 0x20, 0x0b, 0xde, 0x90, 0x91,
	0x3b, 0xa5, 0x96, 0xb6, 0xf8, 0x45, 0x5b, 0xef, 0x83, 0xd9, 0x1a, 0xc8, 0x4d, 0x26, 0x21, 0xab,
	0x6c, 0x93, 0xc9, 0x3c, 0xeb, 0xeb, 0x5d, 0x3b, 0x1d, 0x69, 0xbc, 0xc9, 0x64, 0x5e, 0x6c, 0x4e,
	0xd9, 0x64, 0xf2, 0x2f, 0x62, 0xa7, 0x6c, 0x32, 0x93, 0x8f, 0x40, 0x33, 0x1c, 0x8b, 0x5f, 0x36,
	0x9e, 0xce, 0xb1, 0xd4, 0x6b, 0xca, 0xd3, 0x39, 0x96, 0x79, 0x30, 0xf9, 0x27, 0x15, 0xe8, 0x96,
	0xbd, 0xef, 0x24, 0x1f, 0x9e, 0x46, 0xf9, 0xc4, 0x4b, 0xd4, 0xde, 0xe6, 0xac, 0x4d, 0x58, 0x48,
	0x0e, 0xa0, 0x93, 0x3f, 0x5b, 0x2e, 0x5d, 0xdf, 0xc9, 0x33, 0xf0, 0xde, 0x59, 0x51, 0x59, 0x48,
	0xbe, 0x81, 0x66, 0x66, 0x94, 0x12, 0x23, 0x97, 0x1f, 0xe1, 0x2c, 0x68, 0x2a, 0x90, 0x90, 0x4f,
	0xa6, 0x4b, 0x03, 0x89, 0xf1, 0x1b, 0xed, 0xd2, 0x40, 0x22, 0xf5, 0xe6, 0x7a, 0x84, 0x4f, 0x72,
	0x26, 0x9e, 0x8d, 0x93, 0xf7, 0x4b, 0x37, 0xb0, 0xa2, 0x47, 0xf3, 0xbd, 0x8d, 0x59, 0xd0, 0x59,
	0x48, 0x5e, 0xa4, 0x5f, 0xb2, 0xe2, 0x01, 0xea, 0x3b, 0xa7, 0x89, 0x94, 0x3a, 0xe3, 0xee, 0x9d,
	0x0d, 0x51, 0xaa, 0x54, 0x3e, 0x13, 0x5c, 0xc6, 0xf2, 0x82, 0xe4, 0x72, 0x99, 0x4a, 0x15, 0x26,
	0x97, 0x8f, 0x61, 0x1d, 0x13, 0x81, 0x13, 0xe3, 0x6d, 0xcc, 0x96, 0x67, 0xec, 0xdd, 0x99, 0x09,
	0x5f, 0xee, 0x8d, 0x13, 0xd9, 0xb4, 0xb2, 0xbd, 0xb1, 0x28, 0x75, 0xd8, 0xbb, 0x7d, 0x66, 0x5c,
	0x16, 0xde, 0xbf, 0xf7, 0xd5, 0x27, 0xc3, 0xc0, 0x35, 0xfd, 0xe1, 0xc6, 0xc7, 0x9b, 0x9c, 0x6f,
	0x58, 0x81, 0x77, 0x07, 0xff, 0x50, 0xc1, 0x0a, 0xdc, 0x3b, 0x8c, 0x46, 0x22, 0xee, 0x65, 0xf8,
	0x77, 0x0d, 0xf9, 0xff, 0x6c, 0x78, 0xb1, 0x80, 0x78, 0x77, 0xff, 0x37, 0x00, 0x00, 0xff, 0xff,
	0x98, 0xb4, 0xba, 0x5b, 0xdd, 0x41, 0x00, 0x00,
}
