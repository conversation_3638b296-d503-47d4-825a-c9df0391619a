// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner (interfaces: RCMDAIPartnerClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	rcmd_ai_partner "golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
	grpc "google.golang.org/grpc"
)

// MockRCMDAIPartnerClient is a mock of RCMDAIPartnerClient interface.
type MockRCMDAIPartnerClient struct {
	ctrl     *gomock.Controller
	recorder *MockRCMDAIPartnerClientMockRecorder
}

// MockRCMDAIPartnerClientMockRecorder is the mock recorder for MockRCMDAIPartnerClient.
type MockRCMDAIPartnerClientMockRecorder struct {
	mock *MockRCMDAIPartnerClient
}

// NewMockRCMDAIPartnerClient creates a new mock instance.
func NewMockRCMDAIPartnerClient(ctrl *gomock.Controller) *MockRCMDAIPartnerClient {
	mock := &MockRCMDAIPartnerClient{ctrl: ctrl}
	mock.recorder = &MockRCMDAIPartnerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRCMDAIPartnerClient) EXPECT() *MockRCMDAIPartnerClientMockRecorder {
	return m.recorder
}

// AISendMsgToGroup mocks base method.
func (m *MockRCMDAIPartnerClient) AISendMsgToGroup(arg0 context.Context, arg1 *rcmd_ai_partner.AISendMsgToGroupReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.AISendMsgToGroupResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AISendMsgToGroup", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.AISendMsgToGroupResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AISendMsgToGroup indicates an expected call of AISendMsgToGroup.
func (mr *MockRCMDAIPartnerClientMockRecorder) AISendMsgToGroup(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AISendMsgToGroup", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).AISendMsgToGroup), varargs...)
}

// AddBlackList mocks base method.
func (m *MockRCMDAIPartnerClient) AddBlackList(arg0 context.Context, arg1 *rcmd_ai_partner.AddBlackListReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.AddBlackListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddBlackList", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.AddBlackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBlackList indicates an expected call of AddBlackList.
func (mr *MockRCMDAIPartnerClientMockRecorder) AddBlackList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBlackList", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).AddBlackList), varargs...)
}

// BatchAISendMsgToGroup mocks base method.
func (m *MockRCMDAIPartnerClient) BatchAISendMsgToGroup(arg0 context.Context, arg1 *rcmd_ai_partner.AIBatchSendMsgToGroupReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.AIBatchSendMsgToGroupResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchAISendMsgToGroup", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.AIBatchSendMsgToGroupResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAISendMsgToGroup indicates an expected call of BatchAISendMsgToGroup.
func (mr *MockRCMDAIPartnerClientMockRecorder) BatchAISendMsgToGroup(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAISendMsgToGroup", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).BatchAISendMsgToGroup), varargs...)
}

// ContinueChat mocks base method.
func (m *MockRCMDAIPartnerClient) ContinueChat(arg0 context.Context, arg1 *rcmd_ai_partner.ContinueChatReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.ContinueChatRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ContinueChat", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.ContinueChatRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ContinueChat indicates an expected call of ContinueChat.
func (mr *MockRCMDAIPartnerClientMockRecorder) ContinueChat(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ContinueChat", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).ContinueChat), varargs...)
}

// DelBlackList mocks base method.
func (m *MockRCMDAIPartnerClient) DelBlackList(arg0 context.Context, arg1 *rcmd_ai_partner.DelBlackListReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.DelBlackListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelBlackList", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.DelBlackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelBlackList indicates an expected call of DelBlackList.
func (mr *MockRCMDAIPartnerClientMockRecorder) DelBlackList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBlackList", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).DelBlackList), varargs...)
}

// DeletePartner mocks base method.
func (m *MockRCMDAIPartnerClient) DeletePartner(arg0 context.Context, arg1 *rcmd_ai_partner.DeletePartnerReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.DeletePartnerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeletePartner", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.DeletePartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePartner indicates an expected call of DeletePartner.
func (mr *MockRCMDAIPartnerClientMockRecorder) DeletePartner(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePartner", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).DeletePartner), varargs...)
}

// FormatGptAnswer mocks base method.
func (m *MockRCMDAIPartnerClient) FormatGptAnswer(arg0 context.Context, arg1 *rcmd_ai_partner.FormatGptAnswerReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.FormatGptAnswerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FormatGptAnswer", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.FormatGptAnswerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FormatGptAnswer indicates an expected call of FormatGptAnswer.
func (mr *MockRCMDAIPartnerClientMockRecorder) FormatGptAnswer(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FormatGptAnswer", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).FormatGptAnswer), varargs...)
}

// GenRecommendReply mocks base method.
func (m *MockRCMDAIPartnerClient) GenRecommendReply(arg0 context.Context, arg1 *rcmd_ai_partner.GenRecommendReplyReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GenRecommendReplyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenRecommendReply", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GenRecommendReplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenRecommendReply indicates an expected call of GenRecommendReply.
func (mr *MockRCMDAIPartnerClientMockRecorder) GenRecommendReply(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenRecommendReply", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GenRecommendReply), varargs...)
}

// GenRoleDesc mocks base method.
func (m *MockRCMDAIPartnerClient) GenRoleDesc(arg0 context.Context, arg1 *rcmd_ai_partner.GenRoleDescReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GenRoleDescResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenRoleDesc", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GenRoleDescResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenRoleDesc indicates an expected call of GenRoleDesc.
func (mr *MockRCMDAIPartnerClientMockRecorder) GenRoleDesc(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenRoleDesc", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GenRoleDesc), varargs...)
}

// GenVoiceChatGreeting mocks base method.
func (m *MockRCMDAIPartnerClient) GenVoiceChatGreeting(arg0 context.Context, arg1 *rcmd_ai_partner.GenVoiceChatGreetingReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GenVoiceChatGreetingResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenVoiceChatGreeting", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GenVoiceChatGreetingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenVoiceChatGreeting indicates an expected call of GenVoiceChatGreeting.
func (mr *MockRCMDAIPartnerClientMockRecorder) GenVoiceChatGreeting(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenVoiceChatGreeting", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GenVoiceChatGreeting), varargs...)
}

// GetGPTReqInfo mocks base method.
func (m *MockRCMDAIPartnerClient) GetGPTReqInfo(arg0 context.Context, arg1 *rcmd_ai_partner.GetGPTReqInfoReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetGPTReqInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGPTReqInfo", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetGPTReqInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGPTReqInfo indicates an expected call of GetGPTReqInfo.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetGPTReqInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGPTReqInfo", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetGPTReqInfo), varargs...)
}

// GetGenStatus mocks base method.
func (m *MockRCMDAIPartnerClient) GetGenStatus(arg0 context.Context, arg1 *rcmd_ai_partner.GetGenStatusReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetGenStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGenStatus", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetGenStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGenStatus indicates an expected call of GetGenStatus.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetGenStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGenStatus", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetGenStatus), varargs...)
}

// GetGreetingMsg mocks base method.
func (m *MockRCMDAIPartnerClient) GetGreetingMsg(arg0 context.Context, arg1 *rcmd_ai_partner.GetGreetingMsgReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetGreetingMsgResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGreetingMsg", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetGreetingMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGreetingMsg indicates an expected call of GetGreetingMsg.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetGreetingMsg(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGreetingMsg", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetGreetingMsg), varargs...)
}

// GetPartner mocks base method.
func (m *MockRCMDAIPartnerClient) GetPartner(arg0 context.Context, arg1 *rcmd_ai_partner.GetPartnerReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetPartnerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPartner", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetPartnerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPartner indicates an expected call of GetPartner.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetPartner(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartner", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetPartner), varargs...)
}

// GetPartnerByRoleId mocks base method.
func (m *MockRCMDAIPartnerClient) GetPartnerByRoleId(arg0 context.Context, arg1 *rcmd_ai_partner.GetPartnerByRoleIdReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetPartnerByRoleIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPartnerByRoleId", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetPartnerByRoleIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPartnerByRoleId indicates an expected call of GetPartnerByRoleId.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetPartnerByRoleId(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartnerByRoleId", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetPartnerByRoleId), varargs...)
}

// GetPartnerOpen mocks base method.
func (m *MockRCMDAIPartnerClient) GetPartnerOpen(arg0 context.Context, arg1 *rcmd_ai_partner.GetPartnerOpenReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetPartnerOpenRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPartnerOpen", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetPartnerOpenRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPartnerOpen indicates an expected call of GetPartnerOpen.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetPartnerOpen(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartnerOpen", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetPartnerOpen), varargs...)
}

// GetPartnerSettings mocks base method.
func (m *MockRCMDAIPartnerClient) GetPartnerSettings(arg0 context.Context, arg1 *rcmd_ai_partner.GetPartnerSettingsReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetPartnerSettingsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPartnerSettings", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetPartnerSettingsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPartnerSettings indicates an expected call of GetPartnerSettings.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetPartnerSettings(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartnerSettings", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetPartnerSettings), varargs...)
}

// GetQuickReplyList mocks base method.
func (m *MockRCMDAIPartnerClient) GetQuickReplyList(arg0 context.Context, arg1 *rcmd_ai_partner.GetQuickReplyListReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetQuickReplyListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetQuickReplyList", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetQuickReplyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickReplyList indicates an expected call of GetQuickReplyList.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetQuickReplyList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickReplyList", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetQuickReplyList), varargs...)
}

// GetStoryHistory mocks base method.
func (m *MockRCMDAIPartnerClient) GetStoryHistory(arg0 context.Context, arg1 *rcmd_ai_partner.GetStoryHistoryReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetStoryHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStoryHistory", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetStoryHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStoryHistory indicates an expected call of GetStoryHistory.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetStoryHistory(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStoryHistory", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetStoryHistory), varargs...)
}

// GetStoryInfo mocks base method.
func (m *MockRCMDAIPartnerClient) GetStoryInfo(arg0 context.Context, arg1 *rcmd_ai_partner.GetStoryInfoReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetStoryInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStoryInfo", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetStoryInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStoryInfo indicates an expected call of GetStoryInfo.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetStoryInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStoryInfo", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetStoryInfo), varargs...)
}

// GetStoryReply mocks base method.
func (m *MockRCMDAIPartnerClient) GetStoryReply(arg0 context.Context, arg1 *rcmd_ai_partner.GetStoryReplyReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetStoryReplyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStoryReply", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetStoryReplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStoryReply indicates an expected call of GetStoryReply.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetStoryReply(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStoryReply", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetStoryReply), varargs...)
}

// GetStoryScene mocks base method.
func (m *MockRCMDAIPartnerClient) GetStoryScene(arg0 context.Context, arg1 *rcmd_ai_partner.GetStorySceneReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetStorySceneResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStoryScene", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetStorySceneResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStoryScene indicates an expected call of GetStoryScene.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetStoryScene(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStoryScene", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetStoryScene), varargs...)
}

// GetUserInfo mocks base method.
func (m *MockRCMDAIPartnerClient) GetUserInfo(arg0 context.Context, arg1 *rcmd_ai_partner.GetUserInfoReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetUserInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserInfo", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetUserInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInfo indicates an expected call of GetUserInfo.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetUserInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfo", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetUserInfo), varargs...)
}

// GetUserMsgStat mocks base method.
func (m *MockRCMDAIPartnerClient) GetUserMsgStat(arg0 context.Context, arg1 *rcmd_ai_partner.GetUserMsgStatReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.GetUserMsgStatResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserMsgStat", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.GetUserMsgStatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserMsgStat indicates an expected call of GetUserMsgStat.
func (mr *MockRCMDAIPartnerClientMockRecorder) GetUserMsgStat(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserMsgStat", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).GetUserMsgStat), varargs...)
}

// NewTrigger mocks base method.
func (m *MockRCMDAIPartnerClient) NewTrigger(arg0 context.Context, arg1 *rcmd_ai_partner.NewTriggerReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.NewTriggerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NewTrigger", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.NewTriggerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewTrigger indicates an expected call of NewTrigger.
func (mr *MockRCMDAIPartnerClientMockRecorder) NewTrigger(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewTrigger", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).NewTrigger), varargs...)
}

// ReceiveGroupMsgFromUser mocks base method.
func (m *MockRCMDAIPartnerClient) ReceiveGroupMsgFromUser(arg0 context.Context, arg1 *rcmd_ai_partner.ReceiveGroupMsgFromUserReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.ReceiveGroupMsgFromUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReceiveGroupMsgFromUser", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.ReceiveGroupMsgFromUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReceiveGroupMsgFromUser indicates an expected call of ReceiveGroupMsgFromUser.
func (mr *MockRCMDAIPartnerClientMockRecorder) ReceiveGroupMsgFromUser(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReceiveGroupMsgFromUser", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).ReceiveGroupMsgFromUser), varargs...)
}

// ReceiveMsgFromUser mocks base method.
func (m *MockRCMDAIPartnerClient) ReceiveMsgFromUser(arg0 context.Context, arg1 *rcmd_ai_partner.ReceiveMsgFromUserReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.ReceiveMsgFromUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReceiveMsgFromUser", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.ReceiveMsgFromUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReceiveMsgFromUser indicates an expected call of ReceiveMsgFromUser.
func (mr *MockRCMDAIPartnerClientMockRecorder) ReceiveMsgFromUser(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReceiveMsgFromUser", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).ReceiveMsgFromUser), varargs...)
}

// ReceiveSpecialMsgFromUser mocks base method.
func (m *MockRCMDAIPartnerClient) ReceiveSpecialMsgFromUser(arg0 context.Context, arg1 *rcmd_ai_partner.ReceiveSpecialMsgFromUserReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.ReceiveSpecialMsgFromUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReceiveSpecialMsgFromUser", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.ReceiveSpecialMsgFromUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReceiveSpecialMsgFromUser indicates an expected call of ReceiveSpecialMsgFromUser.
func (mr *MockRCMDAIPartnerClientMockRecorder) ReceiveSpecialMsgFromUser(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReceiveSpecialMsgFromUser", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).ReceiveSpecialMsgFromUser), varargs...)
}

// ReplyTextFormat mocks base method.
func (m *MockRCMDAIPartnerClient) ReplyTextFormat(arg0 context.Context, arg1 *rcmd_ai_partner.ReplyTextFormatReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.ReplyTextFormatResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReplyTextFormat", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.ReplyTextFormatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReplyTextFormat indicates an expected call of ReplyTextFormat.
func (mr *MockRCMDAIPartnerClientMockRecorder) ReplyTextFormat(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplyTextFormat", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).ReplyTextFormat), varargs...)
}

// RewardSentenceAdd mocks base method.
func (m *MockRCMDAIPartnerClient) RewardSentenceAdd(arg0 context.Context, arg1 *rcmd_ai_partner.RewardSentenceAddReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.RewardSentenceAddResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RewardSentenceAdd", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.RewardSentenceAddResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RewardSentenceAdd indicates an expected call of RewardSentenceAdd.
func (mr *MockRCMDAIPartnerClientMockRecorder) RewardSentenceAdd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RewardSentenceAdd", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).RewardSentenceAdd), varargs...)
}

// SendAnimatedMeme mocks base method.
func (m *MockRCMDAIPartnerClient) SendAnimatedMeme(arg0 context.Context, arg1 *rcmd_ai_partner.SendAnimatedMemeReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.SendAnimatedMemeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendAnimatedMeme", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.SendAnimatedMemeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendAnimatedMeme indicates an expected call of SendAnimatedMeme.
func (mr *MockRCMDAIPartnerClientMockRecorder) SendAnimatedMeme(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAnimatedMeme", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).SendAnimatedMeme), varargs...)
}

// SetAIPartnerChattingStatus mocks base method.
func (m *MockRCMDAIPartnerClient) SetAIPartnerChattingStatus(arg0 context.Context, arg1 *rcmd_ai_partner.SetAIPartnerChattingStatusReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.SetAIPartnerChattingStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetAIPartnerChattingStatus", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.SetAIPartnerChattingStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAIPartnerChattingStatus indicates an expected call of SetAIPartnerChattingStatus.
func (mr *MockRCMDAIPartnerClientMockRecorder) SetAIPartnerChattingStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAIPartnerChattingStatus", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).SetAIPartnerChattingStatus), varargs...)
}

// SetAIPartnerInfo mocks base method.
func (m *MockRCMDAIPartnerClient) SetAIPartnerInfo(arg0 context.Context, arg1 *rcmd_ai_partner.SetAIPartnerInfoReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.SetAIPartnerInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetAIPartnerInfo", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.SetAIPartnerInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAIPartnerInfo indicates an expected call of SetAIPartnerInfo.
func (mr *MockRCMDAIPartnerClientMockRecorder) SetAIPartnerInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAIPartnerInfo", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).SetAIPartnerInfo), varargs...)
}

// SetDeviceWhitelist mocks base method.
func (m *MockRCMDAIPartnerClient) SetDeviceWhitelist(arg0 context.Context, arg1 *rcmd_ai_partner.SetDeviceWhitelistReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.SetDeviceWhitelistResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetDeviceWhitelist", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.SetDeviceWhitelistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetDeviceWhitelist indicates an expected call of SetDeviceWhitelist.
func (mr *MockRCMDAIPartnerClientMockRecorder) SetDeviceWhitelist(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDeviceWhitelist", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).SetDeviceWhitelist), varargs...)
}

// SetPartnerAutoPlayStatus mocks base method.
func (m *MockRCMDAIPartnerClient) SetPartnerAutoPlayStatus(arg0 context.Context, arg1 *rcmd_ai_partner.SetPartnerAutoPlayStatusReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.SetPartnerAutoPlayStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPartnerAutoPlayStatus", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.SetPartnerAutoPlayStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPartnerAutoPlayStatus indicates an expected call of SetPartnerAutoPlayStatus.
func (mr *MockRCMDAIPartnerClientMockRecorder) SetPartnerAutoPlayStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPartnerAutoPlayStatus", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).SetPartnerAutoPlayStatus), varargs...)
}

// SetPartnerLoginPushTag mocks base method.
func (m *MockRCMDAIPartnerClient) SetPartnerLoginPushTag(arg0 context.Context, arg1 *rcmd_ai_partner.SetPartnerLoginPushTagReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.SetPartnerLoginPushTagResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPartnerLoginPushTag", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.SetPartnerLoginPushTagResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPartnerLoginPushTag indicates an expected call of SetPartnerLoginPushTag.
func (mr *MockRCMDAIPartnerClientMockRecorder) SetPartnerLoginPushTag(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPartnerLoginPushTag", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).SetPartnerLoginPushTag), varargs...)
}

// SetPartnerSilent mocks base method.
func (m *MockRCMDAIPartnerClient) SetPartnerSilent(arg0 context.Context, arg1 *rcmd_ai_partner.SetPartnerSilentReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.SetPartnerSilentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPartnerSilent", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.SetPartnerSilentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPartnerSilent indicates an expected call of SetPartnerSilent.
func (mr *MockRCMDAIPartnerClientMockRecorder) SetPartnerSilent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPartnerSilent", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).SetPartnerSilent), varargs...)
}

// SetRelationship mocks base method.
func (m *MockRCMDAIPartnerClient) SetRelationship(arg0 context.Context, arg1 *rcmd_ai_partner.SetRelationshipReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.SetRelationshipResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetRelationship", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.SetRelationshipResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetRelationship indicates an expected call of SetRelationship.
func (mr *MockRCMDAIPartnerClientMockRecorder) SetRelationship(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRelationship", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).SetRelationship), varargs...)
}

// SetStoryProgress mocks base method.
func (m *MockRCMDAIPartnerClient) SetStoryProgress(arg0 context.Context, arg1 *rcmd_ai_partner.SetStoryProgressReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.SetStoryProgressResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetStoryProgress", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.SetStoryProgressResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetStoryProgress indicates an expected call of SetStoryProgress.
func (mr *MockRCMDAIPartnerClientMockRecorder) SetStoryProgress(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetStoryProgress", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).SetStoryProgress), varargs...)
}

// ShowContinueChat mocks base method.
func (m *MockRCMDAIPartnerClient) ShowContinueChat(arg0 context.Context, arg1 *rcmd_ai_partner.ShowContinueChatReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.ShowContinueChatRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ShowContinueChat", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.ShowContinueChatRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowContinueChat indicates an expected call of ShowContinueChat.
func (mr *MockRCMDAIPartnerClientMockRecorder) ShowContinueChat(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowContinueChat", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).ShowContinueChat), varargs...)
}

// StartStory mocks base method.
func (m *MockRCMDAIPartnerClient) StartStory(arg0 context.Context, arg1 *rcmd_ai_partner.StartStoryReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.StartStoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartStory", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.StartStoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartStory indicates an expected call of StartStory.
func (mr *MockRCMDAIPartnerClientMockRecorder) StartStory(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartStory", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).StartStory), varargs...)
}

// TestASR mocks base method.
func (m *MockRCMDAIPartnerClient) TestASR(arg0 context.Context, arg1 *rcmd_ai_partner.TestASRReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.TestASRResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestASR", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.TestASRResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestASR indicates an expected call of TestASR.
func (mr *MockRCMDAIPartnerClientMockRecorder) TestASR(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestASR", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).TestASR), varargs...)
}

// Trigger mocks base method.
func (m *MockRCMDAIPartnerClient) Trigger(arg0 context.Context, arg1 *rcmd_ai_partner.TriggerReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.TriggerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Trigger", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.TriggerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Trigger indicates an expected call of Trigger.
func (mr *MockRCMDAIPartnerClientMockRecorder) Trigger(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trigger", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).Trigger), varargs...)
}

// UserEnterChattingNotify mocks base method.
func (m *MockRCMDAIPartnerClient) UserEnterChattingNotify(arg0 context.Context, arg1 *rcmd_ai_partner.UserEnterChattingNotifyReq, arg2 ...grpc.CallOption) (*rcmd_ai_partner.UserEnterChattingNotifyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UserEnterChattingNotify", varargs...)
	ret0, _ := ret[0].(*rcmd_ai_partner.UserEnterChattingNotifyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserEnterChattingNotify indicates an expected call of UserEnterChattingNotify.
func (mr *MockRCMDAIPartnerClientMockRecorder) UserEnterChattingNotify(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserEnterChattingNotify", reflect.TypeOf((*MockRCMDAIPartnerClient)(nil).UserEnterChattingNotify), varargs...)
}
