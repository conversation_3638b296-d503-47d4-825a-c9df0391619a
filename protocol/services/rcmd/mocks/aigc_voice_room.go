// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/rcmd/aigc_voice_room (interfaces: AigcVoiceRoomServiceClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	aigc_voice_room "golang.52tt.com/protocol/services/rcmd/aigc_voice_room"
	grpc "google.golang.org/grpc"
)

// MockAigcVoiceRoomServiceClient is a mock of AigcVoiceRoomServiceClient interface.
type MockAigcVoiceRoomServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcVoiceRoomServiceClientMockRecorder
}

// MockAigcVoiceRoomServiceClientMockRecorder is the mock recorder for MockAigcVoiceRoomServiceClient.
type MockAigcVoiceRoomServiceClientMockRecorder struct {
	mock *MockAigcVoiceRoomServiceClient
}

// NewMockAigcVoiceRoomServiceClient creates a new mock instance.
func NewMockAigcVoiceRoomServiceClient(ctrl *gomock.Controller) *MockAigcVoiceRoomServiceClient {
	mock := &MockAigcVoiceRoomServiceClient{ctrl: ctrl}
	mock.recorder = &MockAigcVoiceRoomServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcVoiceRoomServiceClient) EXPECT() *MockAigcVoiceRoomServiceClientMockRecorder {
	return m.recorder
}

// RecvAudioStreamInRoom mocks base method.
func (m *MockAigcVoiceRoomServiceClient) RecvAudioStreamInRoom(arg0 context.Context, arg1 ...grpc.CallOption) (aigc_voice_room.AigcVoiceRoomService_RecvAudioStreamInRoomClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecvAudioStreamInRoom", varargs...)
	ret0, _ := ret[0].(aigc_voice_room.AigcVoiceRoomService_RecvAudioStreamInRoomClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecvAudioStreamInRoom indicates an expected call of RecvAudioStreamInRoom.
func (mr *MockAigcVoiceRoomServiceClientMockRecorder) RecvAudioStreamInRoom(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvAudioStreamInRoom", reflect.TypeOf((*MockAigcVoiceRoomServiceClient)(nil).RecvAudioStreamInRoom), varargs...)
}
