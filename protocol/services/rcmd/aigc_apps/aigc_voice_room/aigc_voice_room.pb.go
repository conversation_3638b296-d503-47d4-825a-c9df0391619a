// Code generated by protoc-gen-go. DO NOT EDIT.
// source: aigc-apps/ai_voice_room/aigc_voice_room.proto

package aigc_voice_room // import "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_voice_room"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RecvAudioStreamInRoomReq struct {
	ChannelId            string            `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	UserAudio            []byte            `protobuf:"bytes,3,opt,name=user_audio,json=userAudio,proto3" json:"user_audio,omitempty"`
	Ping                 bool              `protobuf:"varint,4,opt,name=ping,proto3" json:"ping,omitempty"`
	Extra                map[string]string `protobuf:"bytes,5,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RecvAudioStreamInRoomReq) Reset()         { *m = RecvAudioStreamInRoomReq{} }
func (m *RecvAudioStreamInRoomReq) String() string { return proto.CompactTextString(m) }
func (*RecvAudioStreamInRoomReq) ProtoMessage()    {}
func (*RecvAudioStreamInRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_voice_room_36c38df10e963b88, []int{0}
}
func (m *RecvAudioStreamInRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecvAudioStreamInRoomReq.Unmarshal(m, b)
}
func (m *RecvAudioStreamInRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecvAudioStreamInRoomReq.Marshal(b, m, deterministic)
}
func (dst *RecvAudioStreamInRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecvAudioStreamInRoomReq.Merge(dst, src)
}
func (m *RecvAudioStreamInRoomReq) XXX_Size() int {
	return xxx_messageInfo_RecvAudioStreamInRoomReq.Size(m)
}
func (m *RecvAudioStreamInRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecvAudioStreamInRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecvAudioStreamInRoomReq proto.InternalMessageInfo

func (m *RecvAudioStreamInRoomReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *RecvAudioStreamInRoomReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecvAudioStreamInRoomReq) GetUserAudio() []byte {
	if m != nil {
		return m.UserAudio
	}
	return nil
}

func (m *RecvAudioStreamInRoomReq) GetPing() bool {
	if m != nil {
		return m.Ping
	}
	return false
}

func (m *RecvAudioStreamInRoomReq) GetExtra() map[string]string {
	if m != nil {
		return m.Extra
	}
	return nil
}

type RecvAudioStreamInRoomResp struct {
	ChannelId            string   `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AiUid                uint32   `protobuf:"varint,2,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	TtsAudio             []byte   `protobuf:"bytes,3,opt,name=tts_audio,json=ttsAudio,proto3" json:"tts_audio,omitempty"`
	PodName              string   `protobuf:"bytes,4,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`
	Pong                 bool     `protobuf:"varint,5,opt,name=pong,proto3" json:"pong,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecvAudioStreamInRoomResp) Reset()         { *m = RecvAudioStreamInRoomResp{} }
func (m *RecvAudioStreamInRoomResp) String() string { return proto.CompactTextString(m) }
func (*RecvAudioStreamInRoomResp) ProtoMessage()    {}
func (*RecvAudioStreamInRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_voice_room_36c38df10e963b88, []int{1}
}
func (m *RecvAudioStreamInRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecvAudioStreamInRoomResp.Unmarshal(m, b)
}
func (m *RecvAudioStreamInRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecvAudioStreamInRoomResp.Marshal(b, m, deterministic)
}
func (dst *RecvAudioStreamInRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecvAudioStreamInRoomResp.Merge(dst, src)
}
func (m *RecvAudioStreamInRoomResp) XXX_Size() int {
	return xxx_messageInfo_RecvAudioStreamInRoomResp.Size(m)
}
func (m *RecvAudioStreamInRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecvAudioStreamInRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecvAudioStreamInRoomResp proto.InternalMessageInfo

func (m *RecvAudioStreamInRoomResp) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *RecvAudioStreamInRoomResp) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *RecvAudioStreamInRoomResp) GetTtsAudio() []byte {
	if m != nil {
		return m.TtsAudio
	}
	return nil
}

func (m *RecvAudioStreamInRoomResp) GetPodName() string {
	if m != nil {
		return m.PodName
	}
	return ""
}

func (m *RecvAudioStreamInRoomResp) GetPong() bool {
	if m != nil {
		return m.Pong
	}
	return false
}

func init() {
	proto.RegisterType((*RecvAudioStreamInRoomReq)(nil), "aigc.aigc_voice_room.RecvAudioStreamInRoomReq")
	proto.RegisterMapType((map[string]string)(nil), "aigc.aigc_voice_room.RecvAudioStreamInRoomReq.ExtraEntry")
	proto.RegisterType((*RecvAudioStreamInRoomResp)(nil), "aigc.aigc_voice_room.RecvAudioStreamInRoomResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AigcVoiceRoomServiceClient is the client API for AigcVoiceRoomService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AigcVoiceRoomServiceClient interface {
	RecvAudioStreamInRoom(ctx context.Context, opts ...grpc.CallOption) (AigcVoiceRoomService_RecvAudioStreamInRoomClient, error)
}

type aigcVoiceRoomServiceClient struct {
	cc *grpc.ClientConn
}

func NewAigcVoiceRoomServiceClient(cc *grpc.ClientConn) AigcVoiceRoomServiceClient {
	return &aigcVoiceRoomServiceClient{cc}
}

func (c *aigcVoiceRoomServiceClient) RecvAudioStreamInRoom(ctx context.Context, opts ...grpc.CallOption) (AigcVoiceRoomService_RecvAudioStreamInRoomClient, error) {
	stream, err := c.cc.NewStream(ctx, &_AigcVoiceRoomService_serviceDesc.Streams[0], "/aigc.aigc_voice_room.AigcVoiceRoomService/RecvAudioStreamInRoom", opts...)
	if err != nil {
		return nil, err
	}
	x := &aigcVoiceRoomServiceRecvAudioStreamInRoomClient{stream}
	return x, nil
}

type AigcVoiceRoomService_RecvAudioStreamInRoomClient interface {
	Send(*RecvAudioStreamInRoomReq) error
	Recv() (*RecvAudioStreamInRoomResp, error)
	grpc.ClientStream
}

type aigcVoiceRoomServiceRecvAudioStreamInRoomClient struct {
	grpc.ClientStream
}

func (x *aigcVoiceRoomServiceRecvAudioStreamInRoomClient) Send(m *RecvAudioStreamInRoomReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *aigcVoiceRoomServiceRecvAudioStreamInRoomClient) Recv() (*RecvAudioStreamInRoomResp, error) {
	m := new(RecvAudioStreamInRoomResp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// AigcVoiceRoomServiceServer is the server API for AigcVoiceRoomService service.
type AigcVoiceRoomServiceServer interface {
	RecvAudioStreamInRoom(AigcVoiceRoomService_RecvAudioStreamInRoomServer) error
}

func RegisterAigcVoiceRoomServiceServer(s *grpc.Server, srv AigcVoiceRoomServiceServer) {
	s.RegisterService(&_AigcVoiceRoomService_serviceDesc, srv)
}

func _AigcVoiceRoomService_RecvAudioStreamInRoom_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(AigcVoiceRoomServiceServer).RecvAudioStreamInRoom(&aigcVoiceRoomServiceRecvAudioStreamInRoomServer{stream})
}

type AigcVoiceRoomService_RecvAudioStreamInRoomServer interface {
	Send(*RecvAudioStreamInRoomResp) error
	Recv() (*RecvAudioStreamInRoomReq, error)
	grpc.ServerStream
}

type aigcVoiceRoomServiceRecvAudioStreamInRoomServer struct {
	grpc.ServerStream
}

func (x *aigcVoiceRoomServiceRecvAudioStreamInRoomServer) Send(m *RecvAudioStreamInRoomResp) error {
	return x.ServerStream.SendMsg(m)
}

func (x *aigcVoiceRoomServiceRecvAudioStreamInRoomServer) Recv() (*RecvAudioStreamInRoomReq, error) {
	m := new(RecvAudioStreamInRoomReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _AigcVoiceRoomService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc.aigc_voice_room.AigcVoiceRoomService",
	HandlerType: (*AigcVoiceRoomServiceServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "RecvAudioStreamInRoom",
			Handler:       _AigcVoiceRoomService_RecvAudioStreamInRoom_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "aigc-apps/ai_voice_room/aigc_voice_room.proto",
}

func init() {
	proto.RegisterFile("aigc-apps/ai_voice_room/aigc_voice_room.proto", fileDescriptor_aigc_voice_room_36c38df10e963b88)
}

var fileDescriptor_aigc_voice_room_36c38df10e963b88 = []byte{
	// 390 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x92, 0x41, 0x8b, 0xd4, 0x30,
	0x14, 0xc7, 0xc9, 0xcc, 0x76, 0x9d, 0x3e, 0x15, 0x24, 0xcc, 0x42, 0x77, 0x45, 0x28, 0x7b, 0xea,
	0x65, 0x53, 0x19, 0x11, 0x56, 0x4f, 0xee, 0xc2, 0x1e, 0xf6, 0xa2, 0x90, 0x41, 0x0f, 0x5e, 0x4a,
	0x4c, 0x42, 0x0d, 0xb6, 0x49, 0x4c, 0xd2, 0xe2, 0x80, 0x5f, 0xc0, 0xbb, 0x57, 0xbf, 0xab, 0xa4,
	0x2d, 0x8c, 0x23, 0x33, 0x2c, 0x73, 0x7b, 0xf9, 0xb7, 0xef, 0xbd, 0x1f, 0x3f, 0x1e, 0x5c, 0x31,
	0x55, 0xf3, 0x2b, 0x66, 0xad, 0x2f, 0x99, 0xaa, 0x7a, 0xa3, 0xb8, 0xac, 0x9c, 0x31, 0x6d, 0x19,
	0xf3, 0x7f, 0xde, 0xc4, 0x3a, 0x13, 0x0c, 0x5e, 0xc6, 0x98, 0xfc, 0xf7, 0xed, 0xf2, 0xd7, 0x0c,
	0x32, 0x2a, 0x79, 0x7f, 0xd3, 0x09, 0x65, 0xd6, 0xc1, 0x49, 0xd6, 0xde, 0x6b, 0x6a, 0x4c, 0x4b,
	0xe5, 0x77, 0xfc, 0x02, 0x80, 0x7f, 0x65, 0x5a, 0xcb, 0xa6, 0x52, 0x22, 0x43, 0x39, 0x2a, 0x52,
	0x9a, 0x4e, 0xc9, 0xbd, 0xc0, 0xcf, 0x60, 0xde, 0x29, 0x91, 0xcd, 0x72, 0x54, 0x3c, 0xa5, 0xb1,
	0x8c, 0x0d, 0x9d, 0x97, 0xae, 0x62, 0x71, 0x5a, 0x36, 0xcf, 0x51, 0xf1, 0x84, 0xa6, 0x31, 0x19,
	0xc6, 0x63, 0x0c, 0x27, 0x56, 0xe9, 0x3a, 0x3b, 0xc9, 0x51, 0xb1, 0xa0, 0x43, 0x8d, 0x3f, 0x40,
	0x22, 0x7f, 0x04, 0xc7, 0xb2, 0x24, 0x9f, 0x17, 0x8f, 0x57, 0x6f, 0xc8, 0x3e, 0x4c, 0x72, 0x08,
	0x91, 0xdc, 0xc5, 0xde, 0x3b, 0x1d, 0xdc, 0x86, 0x8e, 0x73, 0x2e, 0xae, 0x01, 0xb6, 0x61, 0x64,
	0xfc, 0x26, 0x37, 0x13, 0x7b, 0x2c, 0xf1, 0x12, 0x92, 0x9e, 0x35, 0x9d, 0x1c, 0xb8, 0x53, 0x3a,
	0x3e, 0xde, 0xce, 0xae, 0xd1, 0xe5, 0x1f, 0x04, 0xe7, 0x07, 0x16, 0x79, 0xfb, 0x90, 0x8c, 0x33,
	0x38, 0x65, 0xaa, 0xda, 0xfa, 0x48, 0x98, 0xfa, 0xa8, 0x04, 0x7e, 0x0e, 0x69, 0x08, 0x7e, 0x47,
	0xc8, 0x22, 0x04, 0x3f, 0xfa, 0x38, 0x87, 0x85, 0x35, 0xa2, 0xd2, 0xac, 0x95, 0x83, 0x93, 0x94,
	0x3e, 0xb2, 0x46, 0xbc, 0x67, 0xad, 0x1c, 0x54, 0x19, 0x5d, 0x67, 0xc9, 0xa4, 0xca, 0xe8, 0x7a,
	0xf5, 0x1b, 0xc1, 0xf2, 0x46, 0xd5, 0xfc, 0x53, 0xf4, 0x12, 0xb9, 0xd6, 0xd2, 0xf5, 0x8a, 0x4b,
	0xfc, 0x13, 0xce, 0xf6, 0x72, 0x63, 0x72, 0x9c, 0xcd, 0x8b, 0xf2, 0xa8, 0xff, 0xbd, 0x2d, 0xd0,
	0x4b, 0x74, 0x7b, 0xfb, 0xf9, 0x5d, 0x6d, 0x1a, 0xa6, 0x6b, 0xf2, 0x7a, 0x15, 0x02, 0xe1, 0xa6,
	0x2d, 0x87, 0x8b, 0xe3, 0xa6, 0x29, 0xfd, 0x48, 0xe8, 0x4b, 0xc7, 0x5b, 0x31, 0x1e, 0xe6, 0x74,
	0xb0, 0x3b, 0x1b, 0xbe, 0x9c, 0x0e, 0x1d, 0xaf, 0xfe, 0x06, 0x00, 0x00, 0xff, 0xff, 0x50, 0xe1,
	0x45, 0x6c, 0xd4, 0x02, 0x00, 0x00,
}
