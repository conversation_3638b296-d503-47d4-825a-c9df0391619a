// Code generated by protoc-gen-go. DO NOT EDIT.
// source: aigc-apps/ai_voice_room/aigc_voice_room.proto

package aigc_voice_room // import "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_voice_room"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RecvAudioStreamInRoomReq struct {
	ChannelId            uint32            `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	UserAudio            []byte            `protobuf:"bytes,3,opt,name=user_audio,json=userAudio,proto3" json:"user_audio,omitempty"`
	Ping                 bool              `protobuf:"varint,4,opt,name=ping,proto3" json:"ping,omitempty"`
	Extra                map[string]string `protobuf:"bytes,5,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RecvAudioStreamInRoomReq) Reset()         { *m = RecvAudioStreamInRoomReq{} }
func (m *RecvAudioStreamInRoomReq) String() string { return proto.CompactTextString(m) }
func (*RecvAudioStreamInRoomReq) ProtoMessage()    {}
func (*RecvAudioStreamInRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_voice_room_dd1cca1b0f9bc12f, []int{0}
}
func (m *RecvAudioStreamInRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecvAudioStreamInRoomReq.Unmarshal(m, b)
}
func (m *RecvAudioStreamInRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecvAudioStreamInRoomReq.Marshal(b, m, deterministic)
}
func (dst *RecvAudioStreamInRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecvAudioStreamInRoomReq.Merge(dst, src)
}
func (m *RecvAudioStreamInRoomReq) XXX_Size() int {
	return xxx_messageInfo_RecvAudioStreamInRoomReq.Size(m)
}
func (m *RecvAudioStreamInRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecvAudioStreamInRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecvAudioStreamInRoomReq proto.InternalMessageInfo

func (m *RecvAudioStreamInRoomReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecvAudioStreamInRoomReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecvAudioStreamInRoomReq) GetUserAudio() []byte {
	if m != nil {
		return m.UserAudio
	}
	return nil
}

func (m *RecvAudioStreamInRoomReq) GetPing() bool {
	if m != nil {
		return m.Ping
	}
	return false
}

func (m *RecvAudioStreamInRoomReq) GetExtra() map[string]string {
	if m != nil {
		return m.Extra
	}
	return nil
}

type RecvAudioStreamInRoomResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AiUid                uint32   `protobuf:"varint,2,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	TtsAudio             []byte   `protobuf:"bytes,3,opt,name=tts_audio,json=ttsAudio,proto3" json:"tts_audio,omitempty"`
	PodName              string   `protobuf:"bytes,4,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`
	Pong                 bool     `protobuf:"varint,5,opt,name=pong,proto3" json:"pong,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecvAudioStreamInRoomResp) Reset()         { *m = RecvAudioStreamInRoomResp{} }
func (m *RecvAudioStreamInRoomResp) String() string { return proto.CompactTextString(m) }
func (*RecvAudioStreamInRoomResp) ProtoMessage()    {}
func (*RecvAudioStreamInRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_voice_room_dd1cca1b0f9bc12f, []int{1}
}
func (m *RecvAudioStreamInRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecvAudioStreamInRoomResp.Unmarshal(m, b)
}
func (m *RecvAudioStreamInRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecvAudioStreamInRoomResp.Marshal(b, m, deterministic)
}
func (dst *RecvAudioStreamInRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecvAudioStreamInRoomResp.Merge(dst, src)
}
func (m *RecvAudioStreamInRoomResp) XXX_Size() int {
	return xxx_messageInfo_RecvAudioStreamInRoomResp.Size(m)
}
func (m *RecvAudioStreamInRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecvAudioStreamInRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecvAudioStreamInRoomResp proto.InternalMessageInfo

func (m *RecvAudioStreamInRoomResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecvAudioStreamInRoomResp) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *RecvAudioStreamInRoomResp) GetTtsAudio() []byte {
	if m != nil {
		return m.TtsAudio
	}
	return nil
}

func (m *RecvAudioStreamInRoomResp) GetPodName() string {
	if m != nil {
		return m.PodName
	}
	return ""
}

func (m *RecvAudioStreamInRoomResp) GetPong() bool {
	if m != nil {
		return m.Pong
	}
	return false
}

func init() {
	proto.RegisterType((*RecvAudioStreamInRoomReq)(nil), "aigc.aigc_voice_room.RecvAudioStreamInRoomReq")
	proto.RegisterMapType((map[string]string)(nil), "aigc.aigc_voice_room.RecvAudioStreamInRoomReq.ExtraEntry")
	proto.RegisterType((*RecvAudioStreamInRoomResp)(nil), "aigc.aigc_voice_room.RecvAudioStreamInRoomResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AigcVoiceRoomServiceClient is the client API for AigcVoiceRoomService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AigcVoiceRoomServiceClient interface {
	RecvAudioStreamInRoom(ctx context.Context, opts ...grpc.CallOption) (AigcVoiceRoomService_RecvAudioStreamInRoomClient, error)
}

type aigcVoiceRoomServiceClient struct {
	cc *grpc.ClientConn
}

func NewAigcVoiceRoomServiceClient(cc *grpc.ClientConn) AigcVoiceRoomServiceClient {
	return &aigcVoiceRoomServiceClient{cc}
}

func (c *aigcVoiceRoomServiceClient) RecvAudioStreamInRoom(ctx context.Context, opts ...grpc.CallOption) (AigcVoiceRoomService_RecvAudioStreamInRoomClient, error) {
	stream, err := c.cc.NewStream(ctx, &_AigcVoiceRoomService_serviceDesc.Streams[0], "/aigc.aigc_voice_room.AigcVoiceRoomService/RecvAudioStreamInRoom", opts...)
	if err != nil {
		return nil, err
	}
	x := &aigcVoiceRoomServiceRecvAudioStreamInRoomClient{stream}
	return x, nil
}

type AigcVoiceRoomService_RecvAudioStreamInRoomClient interface {
	Send(*RecvAudioStreamInRoomReq) error
	Recv() (*RecvAudioStreamInRoomResp, error)
	grpc.ClientStream
}

type aigcVoiceRoomServiceRecvAudioStreamInRoomClient struct {
	grpc.ClientStream
}

func (x *aigcVoiceRoomServiceRecvAudioStreamInRoomClient) Send(m *RecvAudioStreamInRoomReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *aigcVoiceRoomServiceRecvAudioStreamInRoomClient) Recv() (*RecvAudioStreamInRoomResp, error) {
	m := new(RecvAudioStreamInRoomResp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// AigcVoiceRoomServiceServer is the server API for AigcVoiceRoomService service.
type AigcVoiceRoomServiceServer interface {
	RecvAudioStreamInRoom(AigcVoiceRoomService_RecvAudioStreamInRoomServer) error
}

func RegisterAigcVoiceRoomServiceServer(s *grpc.Server, srv AigcVoiceRoomServiceServer) {
	s.RegisterService(&_AigcVoiceRoomService_serviceDesc, srv)
}

func _AigcVoiceRoomService_RecvAudioStreamInRoom_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(AigcVoiceRoomServiceServer).RecvAudioStreamInRoom(&aigcVoiceRoomServiceRecvAudioStreamInRoomServer{stream})
}

type AigcVoiceRoomService_RecvAudioStreamInRoomServer interface {
	Send(*RecvAudioStreamInRoomResp) error
	Recv() (*RecvAudioStreamInRoomReq, error)
	grpc.ServerStream
}

type aigcVoiceRoomServiceRecvAudioStreamInRoomServer struct {
	grpc.ServerStream
}

func (x *aigcVoiceRoomServiceRecvAudioStreamInRoomServer) Send(m *RecvAudioStreamInRoomResp) error {
	return x.ServerStream.SendMsg(m)
}

func (x *aigcVoiceRoomServiceRecvAudioStreamInRoomServer) Recv() (*RecvAudioStreamInRoomReq, error) {
	m := new(RecvAudioStreamInRoomReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _AigcVoiceRoomService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc.aigc_voice_room.AigcVoiceRoomService",
	HandlerType: (*AigcVoiceRoomServiceServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "RecvAudioStreamInRoom",
			Handler:       _AigcVoiceRoomService_RecvAudioStreamInRoom_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "aigc-apps/ai_voice_room/aigc_voice_room.proto",
}

func init() {
	proto.RegisterFile("aigc-apps/ai_voice_room/aigc_voice_room.proto", fileDescriptor_aigc_voice_room_dd1cca1b0f9bc12f)
}

var fileDescriptor_aigc_voice_room_dd1cca1b0f9bc12f = []byte{
	// 389 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x92, 0x41, 0x6b, 0x14, 0x31,
	0x14, 0xc7, 0xc9, 0x6e, 0xa7, 0xee, 0x3c, 0x15, 0x24, 0x6c, 0x61, 0x5a, 0x11, 0x86, 0x9e, 0xe6,
	0xd2, 0x8c, 0xac, 0x08, 0xd5, 0x93, 0x2d, 0xf4, 0xd0, 0x8b, 0x42, 0x8a, 0x1e, 0xbc, 0x0c, 0x31,
	0x09, 0x63, 0x70, 0x26, 0x89, 0x49, 0x66, 0xb0, 0xe0, 0x17, 0xf0, 0xee, 0xd5, 0xef, 0x2a, 0xc9,
	0x2c, 0xac, 0x2b, 0xbb, 0x94, 0xbd, 0xbd, 0xfc, 0x67, 0xde, 0x7b, 0x3f, 0x7e, 0x3c, 0xb8, 0x60,
	0xaa, 0xe5, 0x17, 0xcc, 0x5a, 0x5f, 0x33, 0xd5, 0x8c, 0x46, 0x71, 0xd9, 0x38, 0x63, 0xfa, 0x3a,
	0xe6, 0xff, 0xbc, 0x89, 0x75, 0x26, 0x18, 0xbc, 0x8c, 0x31, 0xf9, 0xef, 0xdb, 0xf9, 0xaf, 0x19,
	0x14, 0x54, 0xf2, 0xf1, 0x6a, 0x10, 0xca, 0xdc, 0x05, 0x27, 0x59, 0x7f, 0xab, 0xa9, 0x31, 0x3d,
	0x95, 0xdf, 0xf1, 0x0b, 0x00, 0xfe, 0x95, 0x69, 0x2d, 0xbb, 0x46, 0x89, 0x02, 0x95, 0xa8, 0x7a,
	0x4a, 0xf3, 0x75, 0x72, 0x2b, 0xf0, 0x33, 0x98, 0x0f, 0x4a, 0x14, 0xb3, 0x94, 0xc7, 0x32, 0x36,
	0x0c, 0x5e, 0xba, 0x86, 0xc5, 0x69, 0xc5, 0xbc, 0x44, 0xd5, 0x13, 0x9a, 0xc7, 0x24, 0x8d, 0xc7,
	0x18, 0x8e, 0xac, 0xd2, 0x6d, 0x71, 0x54, 0xa2, 0x6a, 0x41, 0x53, 0x8d, 0x3f, 0x40, 0x26, 0x7f,
	0x04, 0xc7, 0x8a, 0xac, 0x9c, 0x57, 0x8f, 0x57, 0x6f, 0xc8, 0x2e, 0x4c, 0xb2, 0x0f, 0x91, 0xdc,
	0xc4, 0xde, 0x1b, 0x1d, 0xdc, 0x3d, 0x9d, 0xe6, 0x9c, 0x5d, 0x02, 0x6c, 0xc2, 0xc8, 0xf8, 0x4d,
	0xde, 0x27, 0xf6, 0x9c, 0xc6, 0x12, 0x2f, 0x21, 0x1b, 0x59, 0x37, 0xc8, 0xc4, 0x9d, 0xd3, 0xe9,
	0xf1, 0x76, 0x76, 0x89, 0xce, 0xff, 0x20, 0x38, 0xdd, 0xb3, 0xc8, 0xdb, 0x87, 0x64, 0x9c, 0xc0,
	0x31, 0x53, 0xcd, 0xc6, 0x47, 0xc6, 0xd4, 0x47, 0x25, 0xf0, 0x73, 0xc8, 0x43, 0xf0, 0x5b, 0x42,
	0x16, 0x21, 0xf8, 0xc9, 0xc7, 0x29, 0x2c, 0xac, 0x11, 0x8d, 0x66, 0xbd, 0x4c, 0x4e, 0x72, 0xfa,
	0xc8, 0x1a, 0xf1, 0x9e, 0xf5, 0x32, 0xa9, 0x32, 0xba, 0x2d, 0xb2, 0xb5, 0x2a, 0xa3, 0xdb, 0xd5,
	0x6f, 0x04, 0xcb, 0x2b, 0xd5, 0xf2, 0x4f, 0xd1, 0x4b, 0xe4, 0xba, 0x93, 0x6e, 0x54, 0x5c, 0xe2,
	0x9f, 0x70, 0xb2, 0x93, 0x1b, 0x93, 0xc3, 0x6c, 0x9e, 0xd5, 0x07, 0xfd, 0xef, 0x6d, 0x85, 0x5e,
	0xa2, 0xeb, 0xeb, 0xcf, 0xef, 0x5a, 0xd3, 0x31, 0xdd, 0x92, 0xd7, 0xab, 0x10, 0x08, 0x37, 0x7d,
	0x9d, 0x2e, 0x8e, 0x9b, 0xae, 0xf6, 0x13, 0xa1, 0xaf, 0x1d, 0xef, 0xc5, 0x74, 0x98, 0xeb, 0x83,
	0xdd, 0xda, 0xf0, 0xe5, 0x38, 0x75, 0xbc, 0xfa, 0x1b, 0x00, 0x00, 0xff, 0xff, 0x33, 0x5b, 0x5b,
	0xe5, 0xd4, 0x02, 0x00, 0x00,
}
