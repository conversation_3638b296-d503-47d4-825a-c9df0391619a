// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: aigc-apps/ai_voice_room/aigc_voice_room.proto

package aigc_voice_room

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

// MockAigcVoiceRoomService_RecvAudioStreamInRoomClient is a mock of AigcVoiceRoomService_RecvAudioStreamInRoomClient interface.
type MockAigcVoiceRoomService_RecvAudioStreamInRoomClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder
}

// MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder is the mock recorder for MockAigcVoiceRoomService_RecvAudioStreamInRoomClient.
type MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder struct {
	mock *MockAigcVoiceRoomService_RecvAudioStreamInRoomClient
}

// NewMockAigcVoiceRoomService_RecvAudioStreamInRoomClient creates a new mock instance.
func NewMockAigcVoiceRoomService_RecvAudioStreamInRoomClient(ctrl *gomock.Controller) *MockAigcVoiceRoomService_RecvAudioStreamInRoomClient {
	mock := &MockAigcVoiceRoomService_RecvAudioStreamInRoomClient{ctrl: ctrl}
	mock.recorder = &MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomClient) EXPECT() *MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder {
	return m.recorder
}

// CloseSend mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomClient) CloseSend() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseSend")
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseSend indicates an expected call of CloseSend.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder) CloseSend() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseSend", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomClient)(nil).CloseSend))
}

// Context mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomClient) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomClient)(nil).Context))
}

// Header mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomClient) Header() (metadata.MD, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Header")
	ret0, _ := ret[0].(metadata.MD)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Header indicates an expected call of Header.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder) Header() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Header", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomClient)(nil).Header))
}

// Recv mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomClient) Recv() (*RecvAudioStreamInRoomResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*RecvAudioStreamInRoomResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomClient)(nil).Recv))
}

// RecvMsg mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomClient) RecvMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecvMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder) RecvMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomClient)(nil).RecvMsg), arg0)
}

// Send mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomClient) Send(arg0 *RecvAudioStreamInRoomReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder) Send(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomClient)(nil).Send), arg0)
}

// SendMsg mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomClient) SendMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder) SendMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomClient)(nil).SendMsg), arg0)
}

// Trailer mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomClient) Trailer() metadata.MD {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Trailer")
	ret0, _ := ret[0].(metadata.MD)
	return ret0
}

// Trailer indicates an expected call of Trailer.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomClientMockRecorder) Trailer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trailer", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomClient)(nil).Trailer))
}

// MockAigcVoiceRoomService_RecvAudioStreamInRoomServer is a mock of AigcVoiceRoomService_RecvAudioStreamInRoomServer interface.
type MockAigcVoiceRoomService_RecvAudioStreamInRoomServer struct {
	ctrl     *gomock.Controller
	recorder *MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder
}

// MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder is the mock recorder for MockAigcVoiceRoomService_RecvAudioStreamInRoomServer.
type MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder struct {
	mock *MockAigcVoiceRoomService_RecvAudioStreamInRoomServer
}

// NewMockAigcVoiceRoomService_RecvAudioStreamInRoomServer creates a new mock instance.
func NewMockAigcVoiceRoomService_RecvAudioStreamInRoomServer(ctrl *gomock.Controller) *MockAigcVoiceRoomService_RecvAudioStreamInRoomServer {
	mock := &MockAigcVoiceRoomService_RecvAudioStreamInRoomServer{ctrl: ctrl}
	mock.recorder = &MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomServer) EXPECT() *MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder {
	return m.recorder
}

// Context mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomServer) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomServer)(nil).Context))
}

// Recv mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomServer) Recv() (*RecvAudioStreamInRoomResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*RecvAudioStreamInRoomResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomServer)(nil).Recv))
}

// RecvMsg mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomServer) RecvMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecvMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder) RecvMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomServer)(nil).RecvMsg), arg0)
}

// Send mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomServer) Send(arg0 *RecvAudioStreamInRoomReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder) Send(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomServer)(nil).Send), arg0)
}

// SendHeader mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomServer) SendHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendHeader indicates an expected call of SendHeader.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder) SendHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendHeader", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomServer)(nil).SendHeader), arg0)
}

// SendMsg mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomServer) SendMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder) SendMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomServer)(nil).SendMsg), arg0)
}

// SetHeader mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomServer) SetHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHeader indicates an expected call of SetHeader.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder) SetHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeader", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomServer)(nil).SetHeader), arg0)
}

// SetTrailer mocks base method.
func (m *MockAigcVoiceRoomService_RecvAudioStreamInRoomServer) SetTrailer(arg0 metadata.MD) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTrailer", arg0)
}

// SetTrailer indicates an expected call of SetTrailer.
func (mr *MockAigcVoiceRoomService_RecvAudioStreamInRoomServerMockRecorder) SetTrailer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTrailer", reflect.TypeOf((*MockAigcVoiceRoomService_RecvAudioStreamInRoomServer)(nil).SetTrailer), arg0)
}

// MockAigcVoiceRoomServiceClient is a mock of AigcVoiceRoomServiceClient interface.
type MockAigcVoiceRoomServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcVoiceRoomServiceClientMockRecorder
}

// MockAigcVoiceRoomServiceClientMockRecorder is the mock recorder for MockAigcVoiceRoomServiceClient.
type MockAigcVoiceRoomServiceClientMockRecorder struct {
	mock *MockAigcVoiceRoomServiceClient
}

// NewMockAigcVoiceRoomServiceClient creates a new mock instance.
func NewMockAigcVoiceRoomServiceClient(ctrl *gomock.Controller) *MockAigcVoiceRoomServiceClient {
	mock := &MockAigcVoiceRoomServiceClient{ctrl: ctrl}
	mock.recorder = &MockAigcVoiceRoomServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcVoiceRoomServiceClient) EXPECT() *MockAigcVoiceRoomServiceClientMockRecorder {
	return m.recorder
}

// RecvAudioStreamInRoom mocks base method.
func (m *MockAigcVoiceRoomServiceClient) RecvAudioStreamInRoom(ctx context.Context, opts ...grpc.CallOption) (AigcVoiceRoomService_RecvAudioStreamInRoomClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecvAudioStreamInRoom", varargs...)
	ret0, _ := ret[0].(AigcVoiceRoomService_RecvAudioStreamInRoomClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecvAudioStreamInRoom indicates an expected call of RecvAudioStreamInRoom.
func (mr *MockAigcVoiceRoomServiceClientMockRecorder) RecvAudioStreamInRoom(ctx interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvAudioStreamInRoom", reflect.TypeOf((*MockAigcVoiceRoomServiceClient)(nil).RecvAudioStreamInRoom), varargs...)
}

// MockAigcVoiceRoomServiceServer is a mock of AigcVoiceRoomServiceServer interface.
type MockAigcVoiceRoomServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockAigcVoiceRoomServiceServerMockRecorder
}

// MockAigcVoiceRoomServiceServerMockRecorder is the mock recorder for MockAigcVoiceRoomServiceServer.
type MockAigcVoiceRoomServiceServerMockRecorder struct {
	mock *MockAigcVoiceRoomServiceServer
}

// NewMockAigcVoiceRoomServiceServer creates a new mock instance.
func NewMockAigcVoiceRoomServiceServer(ctrl *gomock.Controller) *MockAigcVoiceRoomServiceServer {
	mock := &MockAigcVoiceRoomServiceServer{ctrl: ctrl}
	mock.recorder = &MockAigcVoiceRoomServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcVoiceRoomServiceServer) EXPECT() *MockAigcVoiceRoomServiceServerMockRecorder {
	return m.recorder
}

// RecvAudioStreamInRoom mocks base method.
func (m *MockAigcVoiceRoomServiceServer) RecvAudioStreamInRoom(server AigcVoiceRoomService_RecvAudioStreamInRoomServer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecvAudioStreamInRoom", server)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvAudioStreamInRoom indicates an expected call of RecvAudioStreamInRoom.
func (mr *MockAigcVoiceRoomServiceServerMockRecorder) RecvAudioStreamInRoom(server interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvAudioStreamInRoom", reflect.TypeOf((*MockAigcVoiceRoomServiceServer)(nil).RecvAudioStreamInRoom), server)
}
