// Code generated by protoc-gen-go. DO NOT EDIT.
// source: aigc-apps/rcmd-mt-proxy/rcmd_mt_proxy.proto

package rcmd_mt_proxy // import "golang.52tt.com/protocol/services/rcmd/aigc_apps/rcmd_mt_proxy"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 代理服务业务响应码
type ProxyCode int32

const (
	ProxyCode_ProxyCode_Success            ProxyCode = 0
	ProxyCode_ProxyCode_Fail               ProxyCode = 101
	ProxyCode_ProxyCode_Invalid_Cmd        ProxyCode = 102
	ProxyCode_ProxyCode_DownStream_Error   ProxyCode = 103
	ProxyCode_ProxyCode_DownStream_Timeout ProxyCode = 104
)

var ProxyCode_name = map[int32]string{
	0:   "ProxyCode_Success",
	101: "ProxyCode_Fail",
	102: "ProxyCode_Invalid_Cmd",
	103: "ProxyCode_DownStream_Error",
	104: "ProxyCode_DownStream_Timeout",
}
var ProxyCode_value = map[string]int32{
	"ProxyCode_Success":            0,
	"ProxyCode_Fail":               101,
	"ProxyCode_Invalid_Cmd":        102,
	"ProxyCode_DownStream_Error":   103,
	"ProxyCode_DownStream_Timeout": 104,
}

func (x ProxyCode) String() string {
	return proto.EnumName(ProxyCode_name, int32(x))
}
func (ProxyCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_mt_proxy_05d53538cac78b05, []int{0}
}

// 请求对象
type CommonReq struct {
	CtxId                string   `protobuf:"bytes,1,opt,name=ctx_id,json=ctxId,proto3" json:"ctx_id,omitempty"`
	Uid                  int64    `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Cmd                  string   `protobuf:"bytes,3,opt,name=cmd,proto3" json:"cmd,omitempty"`
	Data                 string   `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	Extra                string   `protobuf:"bytes,5,opt,name=extra,proto3" json:"extra,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonReq) Reset()         { *m = CommonReq{} }
func (m *CommonReq) String() string { return proto.CompactTextString(m) }
func (*CommonReq) ProtoMessage()    {}
func (*CommonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_mt_proxy_05d53538cac78b05, []int{0}
}
func (m *CommonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonReq.Unmarshal(m, b)
}
func (m *CommonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonReq.Marshal(b, m, deterministic)
}
func (dst *CommonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonReq.Merge(dst, src)
}
func (m *CommonReq) XXX_Size() int {
	return xxx_messageInfo_CommonReq.Size(m)
}
func (m *CommonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommonReq proto.InternalMessageInfo

func (m *CommonReq) GetCtxId() string {
	if m != nil {
		return m.CtxId
	}
	return ""
}

func (m *CommonReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CommonReq) GetCmd() string {
	if m != nil {
		return m.Cmd
	}
	return ""
}

func (m *CommonReq) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

func (m *CommonReq) GetExtra() string {
	if m != nil {
		return m.Extra
	}
	return ""
}

// 响应对象
type CommonRsp struct {
	ProxyCode            int32    `protobuf:"varint,1,opt,name=proxy_code,json=proxyCode,proto3" json:"proxy_code,omitempty"`
	ProxyErrMsg          string   `protobuf:"bytes,2,opt,name=proxy_err_msg,json=proxyErrMsg,proto3" json:"proxy_err_msg,omitempty"`
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonRsp) Reset()         { *m = CommonRsp{} }
func (m *CommonRsp) String() string { return proto.CompactTextString(m) }
func (*CommonRsp) ProtoMessage()    {}
func (*CommonRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_mt_proxy_05d53538cac78b05, []int{1}
}
func (m *CommonRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonRsp.Unmarshal(m, b)
}
func (m *CommonRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonRsp.Marshal(b, m, deterministic)
}
func (dst *CommonRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonRsp.Merge(dst, src)
}
func (m *CommonRsp) XXX_Size() int {
	return xxx_messageInfo_CommonRsp.Size(m)
}
func (m *CommonRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CommonRsp proto.InternalMessageInfo

func (m *CommonRsp) GetProxyCode() int32 {
	if m != nil {
		return m.ProxyCode
	}
	return 0
}

func (m *CommonRsp) GetProxyErrMsg() string {
	if m != nil {
		return m.ProxyErrMsg
	}
	return ""
}

func (m *CommonRsp) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

// kafka异步请求对象  Topic: rcmd-mt-proxy-req
type AsyncReq struct {
	CtxId                string            `protobuf:"bytes,1,opt,name=ctx_id,json=ctxId,proto3" json:"ctx_id,omitempty"`
	Uid                  int64             `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Cmd                  string            `protobuf:"bytes,3,opt,name=cmd,proto3" json:"cmd,omitempty"`
	Data                 string            `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	Extra                string            `protobuf:"bytes,5,opt,name=extra,proto3" json:"extra,omitempty"`
	CallbackData         map[string]string `protobuf:"bytes,6,rep,name=callback_data,json=callbackData,proto3" json:"callback_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AsyncReq) Reset()         { *m = AsyncReq{} }
func (m *AsyncReq) String() string { return proto.CompactTextString(m) }
func (*AsyncReq) ProtoMessage()    {}
func (*AsyncReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_mt_proxy_05d53538cac78b05, []int{2}
}
func (m *AsyncReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AsyncReq.Unmarshal(m, b)
}
func (m *AsyncReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AsyncReq.Marshal(b, m, deterministic)
}
func (dst *AsyncReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AsyncReq.Merge(dst, src)
}
func (m *AsyncReq) XXX_Size() int {
	return xxx_messageInfo_AsyncReq.Size(m)
}
func (m *AsyncReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AsyncReq.DiscardUnknown(m)
}

var xxx_messageInfo_AsyncReq proto.InternalMessageInfo

func (m *AsyncReq) GetCtxId() string {
	if m != nil {
		return m.CtxId
	}
	return ""
}

func (m *AsyncReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AsyncReq) GetCmd() string {
	if m != nil {
		return m.Cmd
	}
	return ""
}

func (m *AsyncReq) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

func (m *AsyncReq) GetExtra() string {
	if m != nil {
		return m.Extra
	}
	return ""
}

func (m *AsyncReq) GetCallbackData() map[string]string {
	if m != nil {
		return m.CallbackData
	}
	return nil
}

// kafka异步回包对象 Topic: rcmd-mt-proxy-rsp
type AsyncRsp struct {
	CtxId                string            `protobuf:"bytes,1,opt,name=ctx_id,json=ctxId,proto3" json:"ctx_id,omitempty"`
	Uid                  int64             `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Cmd                  string            `protobuf:"bytes,3,opt,name=cmd,proto3" json:"cmd,omitempty"`
	ProxyCode            int32             `protobuf:"varint,4,opt,name=proxy_code,json=proxyCode,proto3" json:"proxy_code,omitempty"`
	ProxyErrMsg          string            `protobuf:"bytes,5,opt,name=proxy_err_msg,json=proxyErrMsg,proto3" json:"proxy_err_msg,omitempty"`
	Data                 string            `protobuf:"bytes,6,opt,name=data,proto3" json:"data,omitempty"`
	CallbackData         map[string]string `protobuf:"bytes,7,rep,name=callback_data,json=callbackData,proto3" json:"callback_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AsyncRsp) Reset()         { *m = AsyncRsp{} }
func (m *AsyncRsp) String() string { return proto.CompactTextString(m) }
func (*AsyncRsp) ProtoMessage()    {}
func (*AsyncRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_mt_proxy_05d53538cac78b05, []int{3}
}
func (m *AsyncRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AsyncRsp.Unmarshal(m, b)
}
func (m *AsyncRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AsyncRsp.Marshal(b, m, deterministic)
}
func (dst *AsyncRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AsyncRsp.Merge(dst, src)
}
func (m *AsyncRsp) XXX_Size() int {
	return xxx_messageInfo_AsyncRsp.Size(m)
}
func (m *AsyncRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AsyncRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AsyncRsp proto.InternalMessageInfo

func (m *AsyncRsp) GetCtxId() string {
	if m != nil {
		return m.CtxId
	}
	return ""
}

func (m *AsyncRsp) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AsyncRsp) GetCmd() string {
	if m != nil {
		return m.Cmd
	}
	return ""
}

func (m *AsyncRsp) GetProxyCode() int32 {
	if m != nil {
		return m.ProxyCode
	}
	return 0
}

func (m *AsyncRsp) GetProxyErrMsg() string {
	if m != nil {
		return m.ProxyErrMsg
	}
	return ""
}

func (m *AsyncRsp) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

func (m *AsyncRsp) GetCallbackData() map[string]string {
	if m != nil {
		return m.CallbackData
	}
	return nil
}

func init() {
	proto.RegisterType((*CommonReq)(nil), "rcmd.rcmd_mt_proxy.CommonReq")
	proto.RegisterType((*CommonRsp)(nil), "rcmd.rcmd_mt_proxy.CommonRsp")
	proto.RegisterType((*AsyncReq)(nil), "rcmd.rcmd_mt_proxy.AsyncReq")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.rcmd_mt_proxy.AsyncReq.CallbackDataEntry")
	proto.RegisterType((*AsyncRsp)(nil), "rcmd.rcmd_mt_proxy.AsyncRsp")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.rcmd_mt_proxy.AsyncRsp.CallbackDataEntry")
	proto.RegisterEnum("rcmd.rcmd_mt_proxy.ProxyCode", ProxyCode_name, ProxyCode_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RcmdMtProxyClient is the client API for RcmdMtProxy service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RcmdMtProxyClient interface {
	Proxy(ctx context.Context, in *CommonReq, opts ...grpc.CallOption) (*CommonRsp, error)
	ProxyStream(ctx context.Context, in *CommonReq, opts ...grpc.CallOption) (RcmdMtProxy_ProxyStreamClient, error)
}

type rcmdMtProxyClient struct {
	cc *grpc.ClientConn
}

func NewRcmdMtProxyClient(cc *grpc.ClientConn) RcmdMtProxyClient {
	return &rcmdMtProxyClient{cc}
}

func (c *rcmdMtProxyClient) Proxy(ctx context.Context, in *CommonReq, opts ...grpc.CallOption) (*CommonRsp, error) {
	out := new(CommonRsp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_mt_proxy.RcmdMtProxy/Proxy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdMtProxyClient) ProxyStream(ctx context.Context, in *CommonReq, opts ...grpc.CallOption) (RcmdMtProxy_ProxyStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RcmdMtProxy_serviceDesc.Streams[0], "/rcmd.rcmd_mt_proxy.RcmdMtProxy/ProxyStream", opts...)
	if err != nil {
		return nil, err
	}
	x := &rcmdMtProxyProxyStreamClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RcmdMtProxy_ProxyStreamClient interface {
	Recv() (*CommonRsp, error)
	grpc.ClientStream
}

type rcmdMtProxyProxyStreamClient struct {
	grpc.ClientStream
}

func (x *rcmdMtProxyProxyStreamClient) Recv() (*CommonRsp, error) {
	m := new(CommonRsp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RcmdMtProxyServer is the server API for RcmdMtProxy service.
type RcmdMtProxyServer interface {
	Proxy(context.Context, *CommonReq) (*CommonRsp, error)
	ProxyStream(*CommonReq, RcmdMtProxy_ProxyStreamServer) error
}

func RegisterRcmdMtProxyServer(s *grpc.Server, srv RcmdMtProxyServer) {
	s.RegisterService(&_RcmdMtProxy_serviceDesc, srv)
}

func _RcmdMtProxy_Proxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdMtProxyServer).Proxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_mt_proxy.RcmdMtProxy/Proxy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdMtProxyServer).Proxy(ctx, req.(*CommonReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdMtProxy_ProxyStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(CommonReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RcmdMtProxyServer).ProxyStream(m, &rcmdMtProxyProxyStreamServer{stream})
}

type RcmdMtProxy_ProxyStreamServer interface {
	Send(*CommonRsp) error
	grpc.ServerStream
}

type rcmdMtProxyProxyStreamServer struct {
	grpc.ServerStream
}

func (x *rcmdMtProxyProxyStreamServer) Send(m *CommonRsp) error {
	return x.ServerStream.SendMsg(m)
}

var _RcmdMtProxy_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.rcmd_mt_proxy.RcmdMtProxy",
	HandlerType: (*RcmdMtProxyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Proxy",
			Handler:    _RcmdMtProxy_Proxy_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "ProxyStream",
			Handler:       _RcmdMtProxy_ProxyStream_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "aigc-apps/rcmd-mt-proxy/rcmd_mt_proxy.proto",
}

func init() {
	proto.RegisterFile("aigc-apps/rcmd-mt-proxy/rcmd_mt_proxy.proto", fileDescriptor_rcmd_mt_proxy_05d53538cac78b05)
}

var fileDescriptor_rcmd_mt_proxy_05d53538cac78b05 = []byte{
	// 498 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x54, 0xcf, 0x8e, 0xd3, 0x3e,
	0x10, 0xfe, 0xa5, 0x69, 0xfa, 0x23, 0x53, 0x16, 0x65, 0x2d, 0x2a, 0x85, 0x8a, 0x45, 0x55, 0x4e,
	0x15, 0xa8, 0x29, 0x2a, 0x42, 0x42, 0x1c, 0xf8, 0xd7, 0x2d, 0xd2, 0x1e, 0x2a, 0xa1, 0x94, 0x13,
	0x17, 0xcb, 0xb5, 0x4d, 0x88, 0x36, 0x8e, 0x83, 0xed, 0x96, 0xf6, 0x01, 0x78, 0x02, 0x1e, 0x81,
	0x37, 0xe0, 0x09, 0x51, 0x9c, 0x92, 0xee, 0xaa, 0x0b, 0x7b, 0x58, 0xc1, 0x6d, 0xe6, 0x9b, 0xf1,
	0xcc, 0x37, 0xdf, 0xc8, 0x03, 0x8f, 0x48, 0x96, 0xd2, 0x11, 0x29, 0x4b, 0x3d, 0x56, 0x54, 0xb0,
	0x91, 0x30, 0xa3, 0x52, 0xc9, 0xcd, 0xd6, 0x7a, 0x58, 0x18, 0x6c, 0xbd, 0xb8, 0x54, 0xd2, 0x48,
	0x84, 0x2a, 0x30, 0xbe, 0x14, 0x89, 0x4a, 0xf0, 0xa7, 0x52, 0x08, 0x59, 0x24, 0xfc, 0x33, 0xea,
	0x41, 0x87, 0x9a, 0x0d, 0xce, 0x58, 0xe8, 0x0c, 0x9c, 0xa1, 0x9f, 0x78, 0xd4, 0x6c, 0xce, 0x18,
	0x0a, 0xc0, 0x5d, 0x65, 0x2c, 0x6c, 0x0d, 0x9c, 0xa1, 0x9b, 0x54, 0x66, 0x85, 0x50, 0xc1, 0x42,
	0xd7, 0x66, 0x55, 0x26, 0x42, 0xd0, 0x66, 0xc4, 0x90, 0xb0, 0x6d, 0x21, 0x6b, 0xa3, 0xbb, 0xe0,
	0xf1, 0x8d, 0x51, 0x24, 0xf4, 0xea, 0x6a, 0xd6, 0x89, 0x96, 0x4d, 0x47, 0x5d, 0xa2, 0x13, 0x00,
	0xcb, 0x03, 0x53, 0xc9, 0xb8, 0xed, 0xea, 0x25, 0xbe, 0x45, 0xa6, 0x92, 0x71, 0x14, 0xc1, 0x51,
	0x1d, 0xe6, 0x4a, 0x61, 0xa1, 0x53, 0xcb, 0xc1, 0x4f, 0xba, 0x16, 0x9c, 0x29, 0x35, 0xd7, 0x69,
	0xd3, 0xd9, 0xdd, 0x77, 0x8e, 0xbe, 0xb6, 0xe0, 0xd6, 0x6b, 0xbd, 0x2d, 0xe8, 0xbf, 0x9a, 0x0a,
	0x2d, 0xe0, 0x88, 0x92, 0x3c, 0x5f, 0x12, 0x7a, 0x8e, 0xed, 0x93, 0xce, 0xc0, 0x1d, 0x76, 0x27,
	0x71, 0x7c, 0xa8, 0x79, 0xfc, 0x8b, 0x59, 0x3c, 0xdd, 0xbd, 0x38, 0x25, 0x86, 0xcc, 0x0a, 0xa3,
	0xb6, 0xc9, 0x6d, 0x7a, 0x01, 0xea, 0xbf, 0x84, 0xe3, 0x83, 0x94, 0x8a, 0xe5, 0x39, 0xdf, 0xee,
	0x66, 0xa9, 0xcc, 0x8a, 0xd1, 0x9a, 0xe4, 0x2b, 0xbe, 0x53, 0xa7, 0x76, 0x9e, 0xb7, 0x9e, 0x39,
	0xd1, 0x8f, 0x46, 0x07, 0x5d, 0xde, 0x44, 0x87, 0xcb, 0x6b, 0x6a, 0x5f, 0xbb, 0x26, 0xef, 0xf7,
	0x6b, 0xea, 0x5c, 0x90, 0xf2, 0x40, 0xb4, 0xff, 0xaf, 0x13, 0x4d, 0x97, 0x7f, 0x5d, 0xb4, 0x87,
	0xdf, 0x1c, 0xf0, 0xdf, 0x35, 0xb3, 0xf5, 0xe0, 0xb8, 0x71, 0xf0, 0x62, 0x45, 0x29, 0xd7, 0x3a,
	0xf8, 0x0f, 0x21, 0xb8, 0xb3, 0x87, 0xdf, 0x92, 0x2c, 0x0f, 0x38, 0xba, 0x07, 0xbd, 0x3d, 0x76,
	0x56, 0xac, 0x49, 0x9e, 0x31, 0x3c, 0x15, 0x2c, 0xf8, 0x88, 0x1e, 0x40, 0x7f, 0x1f, 0x3a, 0x95,
	0x5f, 0x8a, 0x85, 0x51, 0x9c, 0x08, 0x3c, 0x53, 0x4a, 0xaa, 0x20, 0x45, 0x03, 0xb8, 0x7f, 0x65,
	0xfc, 0x7d, 0x26, 0xb8, 0x5c, 0x99, 0xe0, 0xd3, 0xe4, 0xbb, 0x03, 0xdd, 0x84, 0x0a, 0x36, 0x37,
	0x36, 0x11, 0xcd, 0xc0, 0xab, 0x8d, 0x93, 0xab, 0xd4, 0x6a, 0xfe, 0x74, 0xff, 0x4f, 0x61, 0x5d,
	0xa2, 0x39, 0x74, 0x6d, 0x99, 0xba, 0xdf, 0xcd, 0x8a, 0x3d, 0x76, 0xde, 0xbc, 0xfa, 0xf0, 0x22,
	0x95, 0x39, 0x29, 0xd2, 0xf8, 0xe9, 0xc4, 0x98, 0x98, 0x4a, 0x31, 0xb6, 0xb7, 0x87, 0xca, 0x7c,
	0xac, 0xb9, 0x5a, 0x67, 0x94, 0xd7, 0x97, 0x6a, 0x5c, 0x1d, 0x2e, 0xdc, 0x1c, 0xae, 0xa6, 0xda,
	0xb2, 0x63, 0xf3, 0x9f, 0xfc, 0x0c, 0x00, 0x00, 0xff, 0xff, 0x1e, 0x41, 0x30, 0x8a, 0xda, 0x04,
	0x00, 0x00,
}
