// Code generated by protoc-gen-go. DO NOT EDIT.
// source: aigc-apps/rcmd-mt-proxy/proxy_data_definations.proto

package rcmd_mt_proxy // import "golang.52tt.com/protocol/services/rcmd/aigc_apps/rcmd_mt_proxy"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// ---------- cmd: AIAccountCommentPost ----------
type AIAccountCommentPostPlaceholder struct {
	UserNickname         string   `protobuf:"bytes,1,opt,name=user_nickname,json=userNickname,proto3" json:"user_nickname,omitempty"`
	UserSex              string   `protobuf:"bytes,2,opt,name=user_sex,json=userSex,proto3" json:"user_sex,omitempty"`
	UserAge              uint32   `protobuf:"varint,3,opt,name=user_age,json=userAge,proto3" json:"user_age,omitempty"`
	UserAvatar           string   `protobuf:"bytes,4,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar,omitempty"`
	UserIpLocation       string   `protobuf:"bytes,5,opt,name=user_ip_location,json=userIpLocation,proto3" json:"user_ip_location,omitempty"`
	UserHobbyTags        []string `protobuf:"bytes,6,rep,name=user_hobby_tags,json=userHobbyTags,proto3" json:"user_hobby_tags,omitempty"`
	AiNickname           string   `protobuf:"bytes,7,opt,name=ai_nickname,json=aiNickname,proto3" json:"ai_nickname,omitempty"`
	AiIpLocation         string   `protobuf:"bytes,8,opt,name=ai_ip_location,json=aiIpLocation,proto3" json:"ai_ip_location,omitempty"`
	AiSignature          string   `protobuf:"bytes,9,opt,name=ai_signature,json=aiSignature,proto3" json:"ai_signature,omitempty"`
	AiBirthday           string   `protobuf:"bytes,10,opt,name=ai_birthday,json=aiBirthday,proto3" json:"ai_birthday,omitempty"`
	AiPrologue           string   `protobuf:"bytes,11,opt,name=ai_prologue,json=aiPrologue,proto3" json:"ai_prologue,omitempty"`
	AiPhotoImgUrls       []string `protobuf:"bytes,12,rep,name=ai_photo_img_urls,json=aiPhotoImgUrls,proto3" json:"ai_photo_img_urls,omitempty"`
	AiAvatar             string   `protobuf:"bytes,13,opt,name=ai_avatar,json=aiAvatar,proto3" json:"ai_avatar,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIAccountCommentPostPlaceholder) Reset()         { *m = AIAccountCommentPostPlaceholder{} }
func (m *AIAccountCommentPostPlaceholder) String() string { return proto.CompactTextString(m) }
func (*AIAccountCommentPostPlaceholder) ProtoMessage()    {}
func (*AIAccountCommentPostPlaceholder) Descriptor() ([]byte, []int) {
	return fileDescriptor_proxy_data_definations_b6e5570ed8a95338, []int{0}
}
func (m *AIAccountCommentPostPlaceholder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIAccountCommentPostPlaceholder.Unmarshal(m, b)
}
func (m *AIAccountCommentPostPlaceholder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIAccountCommentPostPlaceholder.Marshal(b, m, deterministic)
}
func (dst *AIAccountCommentPostPlaceholder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIAccountCommentPostPlaceholder.Merge(dst, src)
}
func (m *AIAccountCommentPostPlaceholder) XXX_Size() int {
	return xxx_messageInfo_AIAccountCommentPostPlaceholder.Size(m)
}
func (m *AIAccountCommentPostPlaceholder) XXX_DiscardUnknown() {
	xxx_messageInfo_AIAccountCommentPostPlaceholder.DiscardUnknown(m)
}

var xxx_messageInfo_AIAccountCommentPostPlaceholder proto.InternalMessageInfo

func (m *AIAccountCommentPostPlaceholder) GetUserNickname() string {
	if m != nil {
		return m.UserNickname
	}
	return ""
}

func (m *AIAccountCommentPostPlaceholder) GetUserSex() string {
	if m != nil {
		return m.UserSex
	}
	return ""
}

func (m *AIAccountCommentPostPlaceholder) GetUserAge() uint32 {
	if m != nil {
		return m.UserAge
	}
	return 0
}

func (m *AIAccountCommentPostPlaceholder) GetUserAvatar() string {
	if m != nil {
		return m.UserAvatar
	}
	return ""
}

func (m *AIAccountCommentPostPlaceholder) GetUserIpLocation() string {
	if m != nil {
		return m.UserIpLocation
	}
	return ""
}

func (m *AIAccountCommentPostPlaceholder) GetUserHobbyTags() []string {
	if m != nil {
		return m.UserHobbyTags
	}
	return nil
}

func (m *AIAccountCommentPostPlaceholder) GetAiNickname() string {
	if m != nil {
		return m.AiNickname
	}
	return ""
}

func (m *AIAccountCommentPostPlaceholder) GetAiIpLocation() string {
	if m != nil {
		return m.AiIpLocation
	}
	return ""
}

func (m *AIAccountCommentPostPlaceholder) GetAiSignature() string {
	if m != nil {
		return m.AiSignature
	}
	return ""
}

func (m *AIAccountCommentPostPlaceholder) GetAiBirthday() string {
	if m != nil {
		return m.AiBirthday
	}
	return ""
}

func (m *AIAccountCommentPostPlaceholder) GetAiPrologue() string {
	if m != nil {
		return m.AiPrologue
	}
	return ""
}

func (m *AIAccountCommentPostPlaceholder) GetAiPhotoImgUrls() []string {
	if m != nil {
		return m.AiPhotoImgUrls
	}
	return nil
}

func (m *AIAccountCommentPostPlaceholder) GetAiAvatar() string {
	if m != nil {
		return m.AiAvatar
	}
	return ""
}

type AIAccountCommentPostReq struct {
	PostType             uint32   `protobuf:"varint,1,opt,name=post_type,json=postType,proto3" json:"post_type,omitempty"`
	PostContent          string   `protobuf:"bytes,2,opt,name=post_content,json=postContent,proto3" json:"post_content,omitempty"`
	PostTab              string   `protobuf:"bytes,3,opt,name=post_tab,json=postTab,proto3" json:"post_tab,omitempty"`
	PostTopic            string   `protobuf:"bytes,4,opt,name=post_topic,json=postTopic,proto3" json:"post_topic,omitempty"`
	PostTags             []string `protobuf:"bytes,5,rep,name=post_tags,json=postTags,proto3" json:"post_tags,omitempty"`
	ImageUrls            []string `protobuf:"bytes,6,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	VideoUrl             string   `protobuf:"bytes,7,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	PromptId             uint32   `protobuf:"varint,8,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	Placeholder          string   `protobuf:"bytes,9,opt,name=placeholder,proto3" json:"placeholder,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIAccountCommentPostReq) Reset()         { *m = AIAccountCommentPostReq{} }
func (m *AIAccountCommentPostReq) String() string { return proto.CompactTextString(m) }
func (*AIAccountCommentPostReq) ProtoMessage()    {}
func (*AIAccountCommentPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_proxy_data_definations_b6e5570ed8a95338, []int{1}
}
func (m *AIAccountCommentPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIAccountCommentPostReq.Unmarshal(m, b)
}
func (m *AIAccountCommentPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIAccountCommentPostReq.Marshal(b, m, deterministic)
}
func (dst *AIAccountCommentPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIAccountCommentPostReq.Merge(dst, src)
}
func (m *AIAccountCommentPostReq) XXX_Size() int {
	return xxx_messageInfo_AIAccountCommentPostReq.Size(m)
}
func (m *AIAccountCommentPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AIAccountCommentPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_AIAccountCommentPostReq proto.InternalMessageInfo

func (m *AIAccountCommentPostReq) GetPostType() uint32 {
	if m != nil {
		return m.PostType
	}
	return 0
}

func (m *AIAccountCommentPostReq) GetPostContent() string {
	if m != nil {
		return m.PostContent
	}
	return ""
}

func (m *AIAccountCommentPostReq) GetPostTab() string {
	if m != nil {
		return m.PostTab
	}
	return ""
}

func (m *AIAccountCommentPostReq) GetPostTopic() string {
	if m != nil {
		return m.PostTopic
	}
	return ""
}

func (m *AIAccountCommentPostReq) GetPostTags() []string {
	if m != nil {
		return m.PostTags
	}
	return nil
}

func (m *AIAccountCommentPostReq) GetImageUrls() []string {
	if m != nil {
		return m.ImageUrls
	}
	return nil
}

func (m *AIAccountCommentPostReq) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *AIAccountCommentPostReq) GetPromptId() uint32 {
	if m != nil {
		return m.PromptId
	}
	return 0
}

func (m *AIAccountCommentPostReq) GetPlaceholder() string {
	if m != nil {
		return m.Placeholder
	}
	return ""
}

type AIAccountCommentPostRsp struct {
	CommentContent       string   `protobuf:"bytes,1,opt,name=comment_content,json=commentContent,proto3" json:"comment_content,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIAccountCommentPostRsp) Reset()         { *m = AIAccountCommentPostRsp{} }
func (m *AIAccountCommentPostRsp) String() string { return proto.CompactTextString(m) }
func (*AIAccountCommentPostRsp) ProtoMessage()    {}
func (*AIAccountCommentPostRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_proxy_data_definations_b6e5570ed8a95338, []int{2}
}
func (m *AIAccountCommentPostRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIAccountCommentPostRsp.Unmarshal(m, b)
}
func (m *AIAccountCommentPostRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIAccountCommentPostRsp.Marshal(b, m, deterministic)
}
func (dst *AIAccountCommentPostRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIAccountCommentPostRsp.Merge(dst, src)
}
func (m *AIAccountCommentPostRsp) XXX_Size() int {
	return xxx_messageInfo_AIAccountCommentPostRsp.Size(m)
}
func (m *AIAccountCommentPostRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AIAccountCommentPostRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AIAccountCommentPostRsp proto.InternalMessageInfo

func (m *AIAccountCommentPostRsp) GetCommentContent() string {
	if m != nil {
		return m.CommentContent
	}
	return ""
}

func (m *AIAccountCommentPostRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

// ---------- cmd: GenSoulmateGiveGiftRoleImage ----------
type GenSoulmateGiveGiftRoleImageReq struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	StyleId              uint32   `protobuf:"varint,2,opt,name=style_id,json=styleId,proto3" json:"style_id,omitempty"`
	RoleBackgroundUrl    string   `protobuf:"bytes,3,opt,name=role_background_url,json=roleBackgroundUrl,proto3" json:"role_background_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenSoulmateGiveGiftRoleImageReq) Reset()         { *m = GenSoulmateGiveGiftRoleImageReq{} }
func (m *GenSoulmateGiveGiftRoleImageReq) String() string { return proto.CompactTextString(m) }
func (*GenSoulmateGiveGiftRoleImageReq) ProtoMessage()    {}
func (*GenSoulmateGiveGiftRoleImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_proxy_data_definations_b6e5570ed8a95338, []int{3}
}
func (m *GenSoulmateGiveGiftRoleImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenSoulmateGiveGiftRoleImageReq.Unmarshal(m, b)
}
func (m *GenSoulmateGiveGiftRoleImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenSoulmateGiveGiftRoleImageReq.Marshal(b, m, deterministic)
}
func (dst *GenSoulmateGiveGiftRoleImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenSoulmateGiveGiftRoleImageReq.Merge(dst, src)
}
func (m *GenSoulmateGiveGiftRoleImageReq) XXX_Size() int {
	return xxx_messageInfo_GenSoulmateGiveGiftRoleImageReq.Size(m)
}
func (m *GenSoulmateGiveGiftRoleImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenSoulmateGiveGiftRoleImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenSoulmateGiveGiftRoleImageReq proto.InternalMessageInfo

func (m *GenSoulmateGiveGiftRoleImageReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GenSoulmateGiveGiftRoleImageReq) GetStyleId() uint32 {
	if m != nil {
		return m.StyleId
	}
	return 0
}

func (m *GenSoulmateGiveGiftRoleImageReq) GetRoleBackgroundUrl() string {
	if m != nil {
		return m.RoleBackgroundUrl
	}
	return ""
}

type GenSoulmateGiveGiftRoleImageRsp struct {
	ImageUrl             string   `protobuf:"bytes,1,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ErrMsg               string   `protobuf:"bytes,3,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenSoulmateGiveGiftRoleImageRsp) Reset()         { *m = GenSoulmateGiveGiftRoleImageRsp{} }
func (m *GenSoulmateGiveGiftRoleImageRsp) String() string { return proto.CompactTextString(m) }
func (*GenSoulmateGiveGiftRoleImageRsp) ProtoMessage()    {}
func (*GenSoulmateGiveGiftRoleImageRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_proxy_data_definations_b6e5570ed8a95338, []int{4}
}
func (m *GenSoulmateGiveGiftRoleImageRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenSoulmateGiveGiftRoleImageRsp.Unmarshal(m, b)
}
func (m *GenSoulmateGiveGiftRoleImageRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenSoulmateGiveGiftRoleImageRsp.Marshal(b, m, deterministic)
}
func (dst *GenSoulmateGiveGiftRoleImageRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenSoulmateGiveGiftRoleImageRsp.Merge(dst, src)
}
func (m *GenSoulmateGiveGiftRoleImageRsp) XXX_Size() int {
	return xxx_messageInfo_GenSoulmateGiveGiftRoleImageRsp.Size(m)
}
func (m *GenSoulmateGiveGiftRoleImageRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenSoulmateGiveGiftRoleImageRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GenSoulmateGiveGiftRoleImageRsp proto.InternalMessageInfo

func (m *GenSoulmateGiveGiftRoleImageRsp) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *GenSoulmateGiveGiftRoleImageRsp) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GenSoulmateGiveGiftRoleImageRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func init() {
	proto.RegisterType((*AIAccountCommentPostPlaceholder)(nil), "rcmd.rcmd_mt_proxy.AIAccountCommentPostPlaceholder")
	proto.RegisterType((*AIAccountCommentPostReq)(nil), "rcmd.rcmd_mt_proxy.AIAccountCommentPostReq")
	proto.RegisterType((*AIAccountCommentPostRsp)(nil), "rcmd.rcmd_mt_proxy.AIAccountCommentPostRsp")
	proto.RegisterType((*GenSoulmateGiveGiftRoleImageReq)(nil), "rcmd.rcmd_mt_proxy.GenSoulmateGiveGiftRoleImageReq")
	proto.RegisterType((*GenSoulmateGiveGiftRoleImageRsp)(nil), "rcmd.rcmd_mt_proxy.GenSoulmateGiveGiftRoleImageRsp")
}

func init() {
	proto.RegisterFile("aigc-apps/rcmd-mt-proxy/proxy_data_definations.proto", fileDescriptor_proxy_data_definations_b6e5570ed8a95338)
}

var fileDescriptor_proxy_data_definations_b6e5570ed8a95338 = []byte{
	// 683 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x54, 0x4d, 0x6f, 0xdb, 0x38,
	0x10, 0x85, 0xed, 0x8d, 0x3f, 0xe8, 0xd8, 0xd9, 0x68, 0x0f, 0xd1, 0x62, 0x11, 0xc4, 0xeb, 0x16,
	0xad, 0x7b, 0x88, 0x0d, 0xf4, 0xe3, 0x5a, 0x34, 0xc9, 0x21, 0x35, 0xd0, 0x16, 0x86, 0x93, 0x5c,
	0xda, 0x03, 0x31, 0xa6, 0x18, 0x9a, 0x88, 0x24, 0xb2, 0x24, 0x65, 0xc4, 0x3f, 0xa0, 0xbf, 0xa6,
	0x3f, 0xab, 0x7f, 0xa4, 0xe0, 0x50, 0x52, 0x1c, 0xa0, 0x45, 0x2f, 0x02, 0xf8, 0xde, 0xa3, 0x66,
	0xde, 0x3c, 0x69, 0xc8, 0x6b, 0x90, 0x82, 0x9d, 0x82, 0xd6, 0x76, 0x66, 0x58, 0x96, 0x9c, 0x66,
	0xee, 0x54, 0x1b, 0x75, 0xbf, 0x9d, 0xe1, 0x93, 0x26, 0xe0, 0x80, 0x26, 0xfc, 0x56, 0xe6, 0xe0,
	0xa4, 0xca, 0xed, 0x54, 0x1b, 0xe5, 0x54, 0x14, 0x79, 0xed, 0xd4, 0x3f, 0x68, 0xe6, 0x28, 0x4a,
	0xc7, 0x3f, 0x5a, 0xe4, 0xe4, 0x6c, 0x7e, 0xc6, 0x98, 0x2a, 0x72, 0x77, 0xa1, 0xb2, 0x8c, 0xe7,
	0x6e, 0xa1, 0xac, 0x5b, 0xa4, 0xc0, 0xf8, 0x5a, 0xa5, 0x09, 0x37, 0xd1, 0x13, 0x32, 0x28, 0x2c,
	0x37, 0x34, 0x97, 0xec, 0x2e, 0x87, 0x8c, 0xc7, 0x8d, 0x51, 0x63, 0xd2, 0x5b, 0xee, 0x7b, 0xf0,
	0x53, 0x89, 0x45, 0xff, 0x92, 0x2e, 0x8a, 0x2c, 0xbf, 0x8f, 0x9b, 0xc8, 0x77, 0xfc, 0xf9, 0x8a,
	0xdf, 0xd7, 0x14, 0x08, 0x1e, 0xb7, 0x46, 0x8d, 0xc9, 0x20, 0x50, 0x67, 0x82, 0x47, 0x27, 0xa4,
	0x1f, 0xa8, 0x0d, 0x38, 0x30, 0xf1, 0x5f, 0x78, 0x91, 0x20, 0x8b, 0x48, 0x34, 0x21, 0x7f, 0xa3,
	0x40, 0x6a, 0x9a, 0x2a, 0x86, 0x76, 0xe2, 0x3d, 0x54, 0x0d, 0x3d, 0x3e, 0xd7, 0x1f, 0x4a, 0x34,
	0x7a, 0x46, 0x0e, 0x50, 0xb9, 0x56, 0xab, 0xd5, 0x96, 0x3a, 0x10, 0x36, 0x6e, 0x8f, 0x5a, 0x93,
	0xde, 0x12, 0x9b, 0x7f, 0xef, 0xd1, 0x6b, 0x10, 0xd6, 0x97, 0x04, 0xf9, 0xe0, 0xa5, 0x13, 0x4a,
	0x82, 0xac, 0x9d, 0x3c, 0x25, 0x43, 0x90, 0x8f, 0x0a, 0x76, 0x83, 0x5f, 0x90, 0x3b, 0xe5, 0xfe,
	0x27, 0xfb, 0x20, 0xa9, 0x95, 0x22, 0x07, 0x57, 0x18, 0x1e, 0xf7, 0x50, 0xd3, 0x07, 0x79, 0x55,
	0x41, 0x65, 0xa5, 0x95, 0x34, 0x6e, 0x9d, 0xc0, 0x36, 0x26, 0x55, 0xa5, 0xf3, 0x12, 0x29, 0x05,
	0xda, 0xa8, 0x54, 0x89, 0x82, 0xc7, 0xfd, 0x4a, 0xb0, 0x28, 0x91, 0xe8, 0x05, 0x39, 0xf4, 0x82,
	0xb5, 0x72, 0x8a, 0xca, 0x4c, 0xd0, 0xc2, 0xa4, 0x36, 0xde, 0x47, 0x57, 0x43, 0x90, 0x0b, 0x8f,
	0xcf, 0x33, 0x71, 0x63, 0x52, 0x1b, 0xfd, 0x47, 0x7a, 0x20, 0xab, 0x39, 0x0e, 0xf0, 0x4d, 0x5d,
	0x90, 0x61, 0x8a, 0xe3, 0xef, 0x4d, 0x72, 0xf4, 0xab, 0x94, 0x97, 0xfc, 0xab, 0xbf, 0xa8, 0x95,
	0x75, 0xd4, 0x6d, 0x75, 0x48, 0x76, 0xb0, 0xec, 0x7a, 0xe0, 0x7a, 0xab, 0xb9, 0x77, 0x89, 0x24,
	0x53, 0xb9, 0xe3, 0xb9, 0x2b, 0x93, 0xed, 0x7b, 0xec, 0x22, 0x40, 0x3e, 0xdd, 0x70, 0x1f, 0x56,
	0x98, 0x6e, 0x6f, 0xd9, 0xc1, 0xeb, 0xb0, 0x8a, 0x8e, 0x09, 0x09, 0x94, 0xd2, 0x92, 0x95, 0xe1,
	0x62, 0xb1, 0x6b, 0x0f, 0x3c, 0x54, 0xf6, 0x59, 0xed, 0xa1, 0xab, 0x50, 0xd9, 0xc7, 0x74, 0x4c,
	0x88, 0xcc, 0x40, 0xf0, 0xe0, 0x39, 0x24, 0xd9, 0x43, 0xa4, 0xb2, 0xbb, 0x91, 0x09, 0x57, 0x9e,
	0x2e, 0x33, 0xec, 0x22, 0x70, 0x63, 0x52, 0x7c, 0xb1, 0x51, 0x99, 0x76, 0x54, 0x26, 0x18, 0x9e,
	0xb7, 0x84, 0xc0, 0x3c, 0x89, 0x46, 0xa4, 0xaf, 0x1f, 0x3e, 0xee, 0x2a, 0xb7, 0x1d, 0x68, 0xfc,
	0xe5, 0x37, 0xc3, 0xb2, 0x3a, 0x7a, 0x4e, 0x0e, 0x58, 0x40, 0xea, 0x91, 0x84, 0x9f, 0x61, 0x58,
	0xc2, 0xd5, 0x54, 0x8e, 0x48, 0x87, 0x1b, 0x43, 0x33, 0x2b, 0xca, 0x99, 0xb5, 0xb9, 0x31, 0x1f,
	0xad, 0x18, 0x7f, 0x6b, 0x90, 0x93, 0x4b, 0x9e, 0x5f, 0xa9, 0x22, 0xcd, 0xc0, 0xf1, 0x4b, 0xb9,
	0xe1, 0x97, 0xf2, 0xd6, 0x2d, 0x55, 0xca, 0xe7, 0xde, 0x9d, 0x8f, 0xe4, 0x88, 0x74, 0x8c, 0x4a,
	0xb9, 0xef, 0x3e, 0x04, 0xd2, 0xf6, 0xc7, 0x79, 0xe2, 0x67, 0x6d, 0xdd, 0x36, 0x30, 0xcd, 0xf0,
	0x27, 0xe1, 0x79, 0x9e, 0x44, 0x53, 0xf2, 0x0f, 0xde, 0x59, 0x01, 0xbb, 0x13, 0x46, 0x15, 0x79,
	0x82, 0xa3, 0x09, 0x89, 0x1c, 0x7a, 0xea, 0xbc, 0x66, 0x6e, 0x4c, 0x3a, 0xd6, 0x7f, 0x68, 0xc3,
	0x6a, 0x3f, 0xc6, 0x3a, 0x82, 0xd2, 0x66, 0xb7, 0x4a, 0x60, 0xb7, 0xc7, 0xe6, 0xa3, 0x1e, 0x77,
	0x9c, 0xb7, 0x76, 0x9d, 0x9f, 0xbf, 0xfb, 0xfc, 0x56, 0xa8, 0x14, 0x72, 0x31, 0x7d, 0xf3, 0xd2,
	0xb9, 0x29, 0x53, 0xd9, 0x0c, 0xf7, 0x12, 0x53, 0xe9, 0xcc, 0x72, 0xb3, 0x91, 0x8c, 0x87, 0x75,
	0x36, 0xf3, 0xdb, 0x8d, 0xd6, 0xdb, 0xad, 0x5e, 0x56, 0xab, 0x36, 0xea, 0x5f, 0xfd, 0x0c, 0x00,
	0x00, 0xff, 0xff, 0xc6, 0x8d, 0x47, 0xbf, 0xff, 0x04, 0x00, 0x00,
}
