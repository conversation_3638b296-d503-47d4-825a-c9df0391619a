// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/music-topic-channel/music-topic-channel.proto

package music_topic_channel // import "golang.52tt.com/protocol/services/music-topic-channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import hobby_channel "golang.52tt.com/protocol/app/hobby-channel"
import music_topic_channel "golang.52tt.com/protocol/app/music-topic-channel"
import common "golang.52tt.com/protocol/services/rcmd/common"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type HotRcmdStatus int32

const (
	HotRcmdStatus_undefined HotRcmdStatus = 0
	HotRcmdStatus_in_use    HotRcmdStatus = 1
	HotRcmdStatus_in_pause  HotRcmdStatus = 2
)

var HotRcmdStatus_name = map[int32]string{
	0: "undefined",
	1: "in_use",
	2: "in_pause",
}
var HotRcmdStatus_value = map[string]int32{
	"undefined": 0,
	"in_use":    1,
	"in_pause":  2,
}

func (x HotRcmdStatus) String() string {
	return proto.EnumName(HotRcmdStatus_name, int32(x))
}
func (HotRcmdStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{0}
}

type QualityType int32

const (
	QualityType_High_Quality QualityType = 0
	QualityType_Quality_Hot  QualityType = 1
)

var QualityType_name = map[int32]string{
	0: "High_Quality",
	1: "Quality_Hot",
}
var QualityType_value = map[string]int32{
	"High_Quality": 0,
	"Quality_Hot":  1,
}

func (x QualityType) String() string {
	return proto.EnumName(QualityType_name, int32(x))
}
func (QualityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{1}
}

// 标签属性
type FilterAttrType int32

const (
	FilterAttrType_FILTER_ATTR_TYPE_UNEXPECTED FilterAttrType = 0
	FilterAttrType_FILTER_ATTR_TYPE_SAME_CITY  FilterAttrType = 1
)

var FilterAttrType_name = map[int32]string{
	0: "FILTER_ATTR_TYPE_UNEXPECTED",
	1: "FILTER_ATTR_TYPE_SAME_CITY",
}
var FilterAttrType_value = map[string]int32{
	"FILTER_ATTR_TYPE_UNEXPECTED": 0,
	"FILTER_ATTR_TYPE_SAME_CITY":  1,
}

func (x FilterAttrType) String() string {
	return proto.EnumName(FilterAttrType_name, int32(x))
}
func (FilterAttrType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{2}
}

type ChannelDisplayType int32

const (
	ChannelDisplayType_DISPLAY_AT_MAIN_PAGE   ChannelDisplayType = 0
	ChannelDisplayType_DISMISSED              ChannelDisplayType = 1
	ChannelDisplayType_DISPLAY_AT_FIND_FRIEND ChannelDisplayType = 2
	ChannelDisplayType_TEMPORARY              ChannelDisplayType = 3
)

var ChannelDisplayType_name = map[int32]string{
	0: "DISPLAY_AT_MAIN_PAGE",
	1: "DISMISSED",
	2: "DISPLAY_AT_FIND_FRIEND",
	3: "TEMPORARY",
}
var ChannelDisplayType_value = map[string]int32{
	"DISPLAY_AT_MAIN_PAGE":   0,
	"DISMISSED":              1,
	"DISPLAY_AT_FIND_FRIEND": 2,
	"TEMPORARY":              3,
}

func (x ChannelDisplayType) String() string {
	return proto.EnumName(ChannelDisplayType_name, int32(x))
}
func (ChannelDisplayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{3}
}

type DismissType int32

const (
	DismissType_Unknown   DismissType = 0
	DismissType_Sever     DismissType = 1
	DismissType_Cancel    DismissType = 2
	DismissType_SwitchTab DismissType = 3
	DismissType_Quit      DismissType = 4
)

var DismissType_name = map[int32]string{
	0: "Unknown",
	1: "Sever",
	2: "Cancel",
	3: "SwitchTab",
	4: "Quit",
}
var DismissType_value = map[string]int32{
	"Unknown":   0,
	"Sever":     1,
	"Cancel":    2,
	"SwitchTab": 3,
	"Quit":      4,
}

func (x DismissType) String() string {
	return proto.EnumName(DismissType_name, int32(x))
}
func (DismissType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{4}
}

type HistoryType int32

const (
	HistoryType_CreateHistory HistoryType = 0
	HistoryType_UpdateHistory HistoryType = 1
)

var HistoryType_name = map[int32]string{
	0: "CreateHistory",
	1: "UpdateHistory",
}
var HistoryType_value = map[string]int32{
	"CreateHistory": 0,
	"UpdateHistory": 1,
}

func (x HistoryType) String() string {
	return proto.EnumName(HistoryType_name, int32(x))
}
func (HistoryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{5}
}

type HighQualityChannelInfoStatusType int32

const (
	HighQualityChannelInfoStatusType_UNKNOWN    HighQualityChannelInfoStatusType = 0
	HighQualityChannelInfoStatusType_NOT_ACTIVE HighQualityChannelInfoStatusType = 1
	HighQualityChannelInfoStatusType_ACTIVE     HighQualityChannelInfoStatusType = 2
	HighQualityChannelInfoStatusType_INVALID    HighQualityChannelInfoStatusType = 3
)

var HighQualityChannelInfoStatusType_name = map[int32]string{
	0: "UNKNOWN",
	1: "NOT_ACTIVE",
	2: "ACTIVE",
	3: "INVALID",
}
var HighQualityChannelInfoStatusType_value = map[string]int32{
	"UNKNOWN":    0,
	"NOT_ACTIVE": 1,
	"ACTIVE":     2,
	"INVALID":    3,
}

func (x HighQualityChannelInfoStatusType) String() string {
	return proto.EnumName(HighQualityChannelInfoStatusType_name, int32(x))
}
func (HighQualityChannelInfoStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{6}
}

type GetMusicChannelFilterV2Resp_FilterItemType int32

const (
	GetMusicChannelFilterV2Resp_HOME_FILTER_ITEM  GetMusicChannelFilterV2Resp_FilterItemType = 0
	GetMusicChannelFilterV2Resp_PAGE_FILTER_ITERM GetMusicChannelFilterV2Resp_FilterItemType = 1
	GetMusicChannelFilterV2Resp_PAGE_POST         GetMusicChannelFilterV2Resp_FilterItemType = 2
)

var GetMusicChannelFilterV2Resp_FilterItemType_name = map[int32]string{
	0: "HOME_FILTER_ITEM",
	1: "PAGE_FILTER_ITERM",
	2: "PAGE_POST",
}
var GetMusicChannelFilterV2Resp_FilterItemType_value = map[string]int32{
	"HOME_FILTER_ITEM":  0,
	"PAGE_FILTER_ITERM": 1,
	"PAGE_POST":         2,
}

func (x GetMusicChannelFilterV2Resp_FilterItemType) String() string {
	return proto.EnumName(GetMusicChannelFilterV2Resp_FilterItemType_name, int32(x))
}
func (GetMusicChannelFilterV2Resp_FilterItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{43, 0}
}

type DelPublishHotRcmdReq struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPublishHotRcmdReq) Reset()         { *m = DelPublishHotRcmdReq{} }
func (m *DelPublishHotRcmdReq) String() string { return proto.CompactTextString(m) }
func (*DelPublishHotRcmdReq) ProtoMessage()    {}
func (*DelPublishHotRcmdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{0}
}
func (m *DelPublishHotRcmdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPublishHotRcmdReq.Unmarshal(m, b)
}
func (m *DelPublishHotRcmdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPublishHotRcmdReq.Marshal(b, m, deterministic)
}
func (dst *DelPublishHotRcmdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPublishHotRcmdReq.Merge(dst, src)
}
func (m *DelPublishHotRcmdReq) XXX_Size() int {
	return xxx_messageInfo_DelPublishHotRcmdReq.Size(m)
}
func (m *DelPublishHotRcmdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPublishHotRcmdReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPublishHotRcmdReq proto.InternalMessageInfo

func (m *DelPublishHotRcmdReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type DelPublishHotRcmdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPublishHotRcmdResp) Reset()         { *m = DelPublishHotRcmdResp{} }
func (m *DelPublishHotRcmdResp) String() string { return proto.CompactTextString(m) }
func (*DelPublishHotRcmdResp) ProtoMessage()    {}
func (*DelPublishHotRcmdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{1}
}
func (m *DelPublishHotRcmdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPublishHotRcmdResp.Unmarshal(m, b)
}
func (m *DelPublishHotRcmdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPublishHotRcmdResp.Marshal(b, m, deterministic)
}
func (dst *DelPublishHotRcmdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPublishHotRcmdResp.Merge(dst, src)
}
func (m *DelPublishHotRcmdResp) XXX_Size() int {
	return xxx_messageInfo_DelPublishHotRcmdResp.Size(m)
}
func (m *DelPublishHotRcmdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPublishHotRcmdResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPublishHotRcmdResp proto.InternalMessageInfo

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SortPublishHotRcmdReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tabId,proto3" json:"tabId,omitempty"`
	Ids                  []string `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortPublishHotRcmdReq) Reset()         { *m = SortPublishHotRcmdReq{} }
func (m *SortPublishHotRcmdReq) String() string { return proto.CompactTextString(m) }
func (*SortPublishHotRcmdReq) ProtoMessage()    {}
func (*SortPublishHotRcmdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{2}
}
func (m *SortPublishHotRcmdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortPublishHotRcmdReq.Unmarshal(m, b)
}
func (m *SortPublishHotRcmdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortPublishHotRcmdReq.Marshal(b, m, deterministic)
}
func (dst *SortPublishHotRcmdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortPublishHotRcmdReq.Merge(dst, src)
}
func (m *SortPublishHotRcmdReq) XXX_Size() int {
	return xxx_messageInfo_SortPublishHotRcmdReq.Size(m)
}
func (m *SortPublishHotRcmdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SortPublishHotRcmdReq.DiscardUnknown(m)
}

var xxx_messageInfo_SortPublishHotRcmdReq proto.InternalMessageInfo

func (m *SortPublishHotRcmdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SortPublishHotRcmdReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type SortPublishHotRcmdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortPublishHotRcmdResp) Reset()         { *m = SortPublishHotRcmdResp{} }
func (m *SortPublishHotRcmdResp) String() string { return proto.CompactTextString(m) }
func (*SortPublishHotRcmdResp) ProtoMessage()    {}
func (*SortPublishHotRcmdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{3}
}
func (m *SortPublishHotRcmdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortPublishHotRcmdResp.Unmarshal(m, b)
}
func (m *SortPublishHotRcmdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortPublishHotRcmdResp.Marshal(b, m, deterministic)
}
func (dst *SortPublishHotRcmdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortPublishHotRcmdResp.Merge(dst, src)
}
func (m *SortPublishHotRcmdResp) XXX_Size() int {
	return xxx_messageInfo_SortPublishHotRcmdResp.Size(m)
}
func (m *SortPublishHotRcmdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SortPublishHotRcmdResp.DiscardUnknown(m)
}

var xxx_messageInfo_SortPublishHotRcmdResp proto.InternalMessageInfo

type SearchPublishHotRcmdReq struct {
	Id                   string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OffsetId             string        `protobuf:"bytes,2,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	TabId                uint32        `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Blocks               []*Block      `protobuf:"bytes,4,rep,name=blocks,proto3" json:"blocks,omitempty"`
	Count                uint32        `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	Status               HotRcmdStatus `protobuf:"varint,6,opt,name=status,proto3,enum=music_topic_channel.HotRcmdStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SearchPublishHotRcmdReq) Reset()         { *m = SearchPublishHotRcmdReq{} }
func (m *SearchPublishHotRcmdReq) String() string { return proto.CompactTextString(m) }
func (*SearchPublishHotRcmdReq) ProtoMessage()    {}
func (*SearchPublishHotRcmdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{4}
}
func (m *SearchPublishHotRcmdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPublishHotRcmdReq.Unmarshal(m, b)
}
func (m *SearchPublishHotRcmdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPublishHotRcmdReq.Marshal(b, m, deterministic)
}
func (dst *SearchPublishHotRcmdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPublishHotRcmdReq.Merge(dst, src)
}
func (m *SearchPublishHotRcmdReq) XXX_Size() int {
	return xxx_messageInfo_SearchPublishHotRcmdReq.Size(m)
}
func (m *SearchPublishHotRcmdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPublishHotRcmdReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPublishHotRcmdReq proto.InternalMessageInfo

func (m *SearchPublishHotRcmdReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SearchPublishHotRcmdReq) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *SearchPublishHotRcmdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SearchPublishHotRcmdReq) GetBlocks() []*Block {
	if m != nil {
		return m.Blocks
	}
	return nil
}

func (m *SearchPublishHotRcmdReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SearchPublishHotRcmdReq) GetStatus() HotRcmdStatus {
	if m != nil {
		return m.Status
	}
	return HotRcmdStatus_undefined
}

type SearchPublishHotRcmdResp struct {
	Items                []*TabPublishHotRcmd `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SearchPublishHotRcmdResp) Reset()         { *m = SearchPublishHotRcmdResp{} }
func (m *SearchPublishHotRcmdResp) String() string { return proto.CompactTextString(m) }
func (*SearchPublishHotRcmdResp) ProtoMessage()    {}
func (*SearchPublishHotRcmdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{5}
}
func (m *SearchPublishHotRcmdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPublishHotRcmdResp.Unmarshal(m, b)
}
func (m *SearchPublishHotRcmdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPublishHotRcmdResp.Marshal(b, m, deterministic)
}
func (dst *SearchPublishHotRcmdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPublishHotRcmdResp.Merge(dst, src)
}
func (m *SearchPublishHotRcmdResp) XXX_Size() int {
	return xxx_messageInfo_SearchPublishHotRcmdResp.Size(m)
}
func (m *SearchPublishHotRcmdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPublishHotRcmdResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPublishHotRcmdResp proto.InternalMessageInfo

func (m *SearchPublishHotRcmdResp) GetItems() []*TabPublishHotRcmd {
	if m != nil {
		return m.Items
	}
	return nil
}

type UpsertPublishHotRcmdReq struct {
	Items                []*TabPublishHotRcmd `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpsertPublishHotRcmdReq) Reset()         { *m = UpsertPublishHotRcmdReq{} }
func (m *UpsertPublishHotRcmdReq) String() string { return proto.CompactTextString(m) }
func (*UpsertPublishHotRcmdReq) ProtoMessage()    {}
func (*UpsertPublishHotRcmdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{6}
}
func (m *UpsertPublishHotRcmdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertPublishHotRcmdReq.Unmarshal(m, b)
}
func (m *UpsertPublishHotRcmdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertPublishHotRcmdReq.Marshal(b, m, deterministic)
}
func (dst *UpsertPublishHotRcmdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertPublishHotRcmdReq.Merge(dst, src)
}
func (m *UpsertPublishHotRcmdReq) XXX_Size() int {
	return xxx_messageInfo_UpsertPublishHotRcmdReq.Size(m)
}
func (m *UpsertPublishHotRcmdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertPublishHotRcmdReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertPublishHotRcmdReq proto.InternalMessageInfo

func (m *UpsertPublishHotRcmdReq) GetItems() []*TabPublishHotRcmd {
	if m != nil {
		return m.Items
	}
	return nil
}

type UpsertPublishHotRcmdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertPublishHotRcmdResp) Reset()         { *m = UpsertPublishHotRcmdResp{} }
func (m *UpsertPublishHotRcmdResp) String() string { return proto.CompactTextString(m) }
func (*UpsertPublishHotRcmdResp) ProtoMessage()    {}
func (*UpsertPublishHotRcmdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{7}
}
func (m *UpsertPublishHotRcmdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertPublishHotRcmdResp.Unmarshal(m, b)
}
func (m *UpsertPublishHotRcmdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertPublishHotRcmdResp.Marshal(b, m, deterministic)
}
func (dst *UpsertPublishHotRcmdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertPublishHotRcmdResp.Merge(dst, src)
}
func (m *UpsertPublishHotRcmdResp) XXX_Size() int {
	return xxx_messageInfo_UpsertPublishHotRcmdResp.Size(m)
}
func (m *UpsertPublishHotRcmdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertPublishHotRcmdResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertPublishHotRcmdResp proto.InternalMessageInfo

type TabPublishHotRcmd struct {
	Id                   string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Hint                 string        `protobuf:"bytes,3,opt,name=hint,proto3" json:"hint,omitempty"`
	TabId                uint32        `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Blocks               []*Block      `protobuf:"bytes,5,rep,name=blocks,proto3" json:"blocks,omitempty"`
	Note                 string        `protobuf:"bytes,6,opt,name=note,proto3" json:"note,omitempty"`
	CreateAt             int64         `protobuf:"varint,7,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	UpdateAt             int64         `protobuf:"varint,8,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	Status               HotRcmdStatus `protobuf:"varint,9,opt,name=status,proto3,enum=music_topic_channel.HotRcmdStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *TabPublishHotRcmd) Reset()         { *m = TabPublishHotRcmd{} }
func (m *TabPublishHotRcmd) String() string { return proto.CompactTextString(m) }
func (*TabPublishHotRcmd) ProtoMessage()    {}
func (*TabPublishHotRcmd) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{8}
}
func (m *TabPublishHotRcmd) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabPublishHotRcmd.Unmarshal(m, b)
}
func (m *TabPublishHotRcmd) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabPublishHotRcmd.Marshal(b, m, deterministic)
}
func (dst *TabPublishHotRcmd) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabPublishHotRcmd.Merge(dst, src)
}
func (m *TabPublishHotRcmd) XXX_Size() int {
	return xxx_messageInfo_TabPublishHotRcmd.Size(m)
}
func (m *TabPublishHotRcmd) XXX_DiscardUnknown() {
	xxx_messageInfo_TabPublishHotRcmd.DiscardUnknown(m)
}

var xxx_messageInfo_TabPublishHotRcmd proto.InternalMessageInfo

func (m *TabPublishHotRcmd) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *TabPublishHotRcmd) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TabPublishHotRcmd) GetHint() string {
	if m != nil {
		return m.Hint
	}
	return ""
}

func (m *TabPublishHotRcmd) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *TabPublishHotRcmd) GetBlocks() []*Block {
	if m != nil {
		return m.Blocks
	}
	return nil
}

func (m *TabPublishHotRcmd) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *TabPublishHotRcmd) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *TabPublishHotRcmd) GetUpdateAt() int64 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *TabPublishHotRcmd) GetStatus() HotRcmdStatus {
	if m != nil {
		return m.Status
	}
	return HotRcmdStatus_undefined
}

type GetTabPublishHotRcmdReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabPublishHotRcmdReq) Reset()         { *m = GetTabPublishHotRcmdReq{} }
func (m *GetTabPublishHotRcmdReq) String() string { return proto.CompactTextString(m) }
func (*GetTabPublishHotRcmdReq) ProtoMessage()    {}
func (*GetTabPublishHotRcmdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{9}
}
func (m *GetTabPublishHotRcmdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabPublishHotRcmdReq.Unmarshal(m, b)
}
func (m *GetTabPublishHotRcmdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabPublishHotRcmdReq.Marshal(b, m, deterministic)
}
func (dst *GetTabPublishHotRcmdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabPublishHotRcmdReq.Merge(dst, src)
}
func (m *GetTabPublishHotRcmdReq) XXX_Size() int {
	return xxx_messageInfo_GetTabPublishHotRcmdReq.Size(m)
}
func (m *GetTabPublishHotRcmdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabPublishHotRcmdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabPublishHotRcmdReq proto.InternalMessageInfo

func (m *GetTabPublishHotRcmdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetTabPublishHotRcmdResp struct {
	Items                []*TabPublishHotRcmd `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetTabPublishHotRcmdResp) Reset()         { *m = GetTabPublishHotRcmdResp{} }
func (m *GetTabPublishHotRcmdResp) String() string { return proto.CompactTextString(m) }
func (*GetTabPublishHotRcmdResp) ProtoMessage()    {}
func (*GetTabPublishHotRcmdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{10}
}
func (m *GetTabPublishHotRcmdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabPublishHotRcmdResp.Unmarshal(m, b)
}
func (m *GetTabPublishHotRcmdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabPublishHotRcmdResp.Marshal(b, m, deterministic)
}
func (dst *GetTabPublishHotRcmdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabPublishHotRcmdResp.Merge(dst, src)
}
func (m *GetTabPublishHotRcmdResp) XXX_Size() int {
	return xxx_messageInfo_GetTabPublishHotRcmdResp.Size(m)
}
func (m *GetTabPublishHotRcmdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabPublishHotRcmdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabPublishHotRcmdResp proto.InternalMessageInfo

func (m *GetTabPublishHotRcmdResp) GetItems() []*TabPublishHotRcmd {
	if m != nil {
		return m.Items
	}
	return nil
}

type ListMusicChannelViewPbsReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListMusicChannelViewPbsReq) Reset()         { *m = ListMusicChannelViewPbsReq{} }
func (m *ListMusicChannelViewPbsReq) String() string { return proto.CompactTextString(m) }
func (*ListMusicChannelViewPbsReq) ProtoMessage()    {}
func (*ListMusicChannelViewPbsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{11}
}
func (m *ListMusicChannelViewPbsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicChannelViewPbsReq.Unmarshal(m, b)
}
func (m *ListMusicChannelViewPbsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicChannelViewPbsReq.Marshal(b, m, deterministic)
}
func (dst *ListMusicChannelViewPbsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicChannelViewPbsReq.Merge(dst, src)
}
func (m *ListMusicChannelViewPbsReq) XXX_Size() int {
	return xxx_messageInfo_ListMusicChannelViewPbsReq.Size(m)
}
func (m *ListMusicChannelViewPbsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicChannelViewPbsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicChannelViewPbsReq proto.InternalMessageInfo

func (m *ListMusicChannelViewPbsReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type ListMusicChannelViewPbsResp struct {
	ChannelViews         map[uint32][]byte `protobuf:"bytes,1,rep,name=channel_views,json=channelViews,proto3" json:"channel_views,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ListMusicChannelViewPbsResp) Reset()         { *m = ListMusicChannelViewPbsResp{} }
func (m *ListMusicChannelViewPbsResp) String() string { return proto.CompactTextString(m) }
func (*ListMusicChannelViewPbsResp) ProtoMessage()    {}
func (*ListMusicChannelViewPbsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{12}
}
func (m *ListMusicChannelViewPbsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicChannelViewPbsResp.Unmarshal(m, b)
}
func (m *ListMusicChannelViewPbsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicChannelViewPbsResp.Marshal(b, m, deterministic)
}
func (dst *ListMusicChannelViewPbsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicChannelViewPbsResp.Merge(dst, src)
}
func (m *ListMusicChannelViewPbsResp) XXX_Size() int {
	return xxx_messageInfo_ListMusicChannelViewPbsResp.Size(m)
}
func (m *ListMusicChannelViewPbsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicChannelViewPbsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicChannelViewPbsResp proto.InternalMessageInfo

func (m *ListMusicChannelViewPbsResp) GetChannelViews() map[uint32][]byte {
	if m != nil {
		return m.ChannelViews
	}
	return nil
}

type ListMusicChannelViewsForMusicReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	IsCollect            bool     `protobuf:"varint,2,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListMusicChannelViewsForMusicReq) Reset()         { *m = ListMusicChannelViewsForMusicReq{} }
func (m *ListMusicChannelViewsForMusicReq) String() string { return proto.CompactTextString(m) }
func (*ListMusicChannelViewsForMusicReq) ProtoMessage()    {}
func (*ListMusicChannelViewsForMusicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{13}
}
func (m *ListMusicChannelViewsForMusicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicChannelViewsForMusicReq.Unmarshal(m, b)
}
func (m *ListMusicChannelViewsForMusicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicChannelViewsForMusicReq.Marshal(b, m, deterministic)
}
func (dst *ListMusicChannelViewsForMusicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicChannelViewsForMusicReq.Merge(dst, src)
}
func (m *ListMusicChannelViewsForMusicReq) XXX_Size() int {
	return xxx_messageInfo_ListMusicChannelViewsForMusicReq.Size(m)
}
func (m *ListMusicChannelViewsForMusicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicChannelViewsForMusicReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicChannelViewsForMusicReq proto.InternalMessageInfo

func (m *ListMusicChannelViewsForMusicReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

func (m *ListMusicChannelViewsForMusicReq) GetIsCollect() bool {
	if m != nil {
		return m.IsCollect
	}
	return false
}

type ListMusicChannelViewsForMusicResp struct {
	Items                map[uint32]*hobby_channel.ListHobbyChannelResp_HobbyChannelItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                        `json:"-"`
	XXX_unrecognized     []byte                                                          `json:"-"`
	XXX_sizecache        int32                                                           `json:"-"`
}

func (m *ListMusicChannelViewsForMusicResp) Reset()         { *m = ListMusicChannelViewsForMusicResp{} }
func (m *ListMusicChannelViewsForMusicResp) String() string { return proto.CompactTextString(m) }
func (*ListMusicChannelViewsForMusicResp) ProtoMessage()    {}
func (*ListMusicChannelViewsForMusicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{14}
}
func (m *ListMusicChannelViewsForMusicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicChannelViewsForMusicResp.Unmarshal(m, b)
}
func (m *ListMusicChannelViewsForMusicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicChannelViewsForMusicResp.Marshal(b, m, deterministic)
}
func (dst *ListMusicChannelViewsForMusicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicChannelViewsForMusicResp.Merge(dst, src)
}
func (m *ListMusicChannelViewsForMusicResp) XXX_Size() int {
	return xxx_messageInfo_ListMusicChannelViewsForMusicResp.Size(m)
}
func (m *ListMusicChannelViewsForMusicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicChannelViewsForMusicResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicChannelViewsForMusicResp proto.InternalMessageInfo

func (m *ListMusicChannelViewsForMusicResp) GetItems() map[uint32]*hobby_channel.ListHobbyChannelResp_HobbyChannelItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type BatchHighQualityChannelsReq struct {
	TabChannelMap        map[uint32]uint32 `protobuf:"bytes,1,rep,name=tab_channel_map,json=tabChannelMap,proto3" json:"tab_channel_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchHighQualityChannelsReq) Reset()         { *m = BatchHighQualityChannelsReq{} }
func (m *BatchHighQualityChannelsReq) String() string { return proto.CompactTextString(m) }
func (*BatchHighQualityChannelsReq) ProtoMessage()    {}
func (*BatchHighQualityChannelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{15}
}
func (m *BatchHighQualityChannelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchHighQualityChannelsReq.Unmarshal(m, b)
}
func (m *BatchHighQualityChannelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchHighQualityChannelsReq.Marshal(b, m, deterministic)
}
func (dst *BatchHighQualityChannelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchHighQualityChannelsReq.Merge(dst, src)
}
func (m *BatchHighQualityChannelsReq) XXX_Size() int {
	return xxx_messageInfo_BatchHighQualityChannelsReq.Size(m)
}
func (m *BatchHighQualityChannelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchHighQualityChannelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchHighQualityChannelsReq proto.InternalMessageInfo

func (m *BatchHighQualityChannelsReq) GetTabChannelMap() map[uint32]uint32 {
	if m != nil {
		return m.TabChannelMap
	}
	return nil
}

type BatchHighQualityChannelsResp struct {
	ChannelQualityMap    map[uint32]QualityType `protobuf:"bytes,1,rep,name=channel_quality_map,json=channelQualityMap,proto3" json:"channel_quality_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=music_topic_channel.QualityType"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchHighQualityChannelsResp) Reset()         { *m = BatchHighQualityChannelsResp{} }
func (m *BatchHighQualityChannelsResp) String() string { return proto.CompactTextString(m) }
func (*BatchHighQualityChannelsResp) ProtoMessage()    {}
func (*BatchHighQualityChannelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{16}
}
func (m *BatchHighQualityChannelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchHighQualityChannelsResp.Unmarshal(m, b)
}
func (m *BatchHighQualityChannelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchHighQualityChannelsResp.Marshal(b, m, deterministic)
}
func (dst *BatchHighQualityChannelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchHighQualityChannelsResp.Merge(dst, src)
}
func (m *BatchHighQualityChannelsResp) XXX_Size() int {
	return xxx_messageInfo_BatchHighQualityChannelsResp.Size(m)
}
func (m *BatchHighQualityChannelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchHighQualityChannelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchHighQualityChannelsResp proto.InternalMessageInfo

func (m *BatchHighQualityChannelsResp) GetChannelQualityMap() map[uint32]QualityType {
	if m != nil {
		return m.ChannelQualityMap
	}
	return nil
}

type GetFilterItemsByTabIdReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterItemsByTabIdReq) Reset()         { *m = GetFilterItemsByTabIdReq{} }
func (m *GetFilterItemsByTabIdReq) String() string { return proto.CompactTextString(m) }
func (*GetFilterItemsByTabIdReq) ProtoMessage()    {}
func (*GetFilterItemsByTabIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{17}
}
func (m *GetFilterItemsByTabIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterItemsByTabIdReq.Unmarshal(m, b)
}
func (m *GetFilterItemsByTabIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterItemsByTabIdReq.Marshal(b, m, deterministic)
}
func (dst *GetFilterItemsByTabIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterItemsByTabIdReq.Merge(dst, src)
}
func (m *GetFilterItemsByTabIdReq) XXX_Size() int {
	return xxx_messageInfo_GetFilterItemsByTabIdReq.Size(m)
}
func (m *GetFilterItemsByTabIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterItemsByTabIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterItemsByTabIdReq proto.InternalMessageInfo

func (m *GetFilterItemsByTabIdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetFilterItemsByTabIdResp struct {
	FilterIds            []string `protobuf:"bytes,1,rep,name=filter_ids,json=filterIds,proto3" json:"filter_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterItemsByTabIdResp) Reset()         { *m = GetFilterItemsByTabIdResp{} }
func (m *GetFilterItemsByTabIdResp) String() string { return proto.CompactTextString(m) }
func (*GetFilterItemsByTabIdResp) ProtoMessage()    {}
func (*GetFilterItemsByTabIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{18}
}
func (m *GetFilterItemsByTabIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterItemsByTabIdResp.Unmarshal(m, b)
}
func (m *GetFilterItemsByTabIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterItemsByTabIdResp.Marshal(b, m, deterministic)
}
func (dst *GetFilterItemsByTabIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterItemsByTabIdResp.Merge(dst, src)
}
func (m *GetFilterItemsByTabIdResp) XXX_Size() int {
	return xxx_messageInfo_GetFilterItemsByTabIdResp.Size(m)
}
func (m *GetFilterItemsByTabIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterItemsByTabIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterItemsByTabIdResp proto.InternalMessageInfo

func (m *GetFilterItemsByTabIdResp) GetFilterIds() []string {
	if m != nil {
		return m.FilterIds
	}
	return nil
}

type BatchFilterIdsByGameCardReq struct {
	GameCard             []string `protobuf:"bytes,1,rep,name=game_card,json=gameCard,proto3" json:"game_card,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchFilterIdsByGameCardReq) Reset()         { *m = BatchFilterIdsByGameCardReq{} }
func (m *BatchFilterIdsByGameCardReq) String() string { return proto.CompactTextString(m) }
func (*BatchFilterIdsByGameCardReq) ProtoMessage()    {}
func (*BatchFilterIdsByGameCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{19}
}
func (m *BatchFilterIdsByGameCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchFilterIdsByGameCardReq.Unmarshal(m, b)
}
func (m *BatchFilterIdsByGameCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchFilterIdsByGameCardReq.Marshal(b, m, deterministic)
}
func (dst *BatchFilterIdsByGameCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchFilterIdsByGameCardReq.Merge(dst, src)
}
func (m *BatchFilterIdsByGameCardReq) XXX_Size() int {
	return xxx_messageInfo_BatchFilterIdsByGameCardReq.Size(m)
}
func (m *BatchFilterIdsByGameCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchFilterIdsByGameCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchFilterIdsByGameCardReq proto.InternalMessageInfo

func (m *BatchFilterIdsByGameCardReq) GetGameCard() []string {
	if m != nil {
		return m.GameCard
	}
	return nil
}

type BatchFilterIdsByGameCardResp struct {
	GameFilters          map[string]*BatchFilterIdsByGameCardResp_Filters `protobuf:"bytes,1,rep,name=game_filters,json=gameFilters,proto3" json:"game_filters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                         `json:"-"`
	XXX_unrecognized     []byte                                           `json:"-"`
	XXX_sizecache        int32                                            `json:"-"`
}

func (m *BatchFilterIdsByGameCardResp) Reset()         { *m = BatchFilterIdsByGameCardResp{} }
func (m *BatchFilterIdsByGameCardResp) String() string { return proto.CompactTextString(m) }
func (*BatchFilterIdsByGameCardResp) ProtoMessage()    {}
func (*BatchFilterIdsByGameCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{20}
}
func (m *BatchFilterIdsByGameCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchFilterIdsByGameCardResp.Unmarshal(m, b)
}
func (m *BatchFilterIdsByGameCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchFilterIdsByGameCardResp.Marshal(b, m, deterministic)
}
func (dst *BatchFilterIdsByGameCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchFilterIdsByGameCardResp.Merge(dst, src)
}
func (m *BatchFilterIdsByGameCardResp) XXX_Size() int {
	return xxx_messageInfo_BatchFilterIdsByGameCardResp.Size(m)
}
func (m *BatchFilterIdsByGameCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchFilterIdsByGameCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchFilterIdsByGameCardResp proto.InternalMessageInfo

func (m *BatchFilterIdsByGameCardResp) GetGameFilters() map[string]*BatchFilterIdsByGameCardResp_Filters {
	if m != nil {
		return m.GameFilters
	}
	return nil
}

type BatchFilterIdsByGameCardResp_Filters struct {
	FilterIds            []string `protobuf:"bytes,1,rep,name=filter_ids,json=filterIds,proto3" json:"filter_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchFilterIdsByGameCardResp_Filters) Reset()         { *m = BatchFilterIdsByGameCardResp_Filters{} }
func (m *BatchFilterIdsByGameCardResp_Filters) String() string { return proto.CompactTextString(m) }
func (*BatchFilterIdsByGameCardResp_Filters) ProtoMessage()    {}
func (*BatchFilterIdsByGameCardResp_Filters) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{20, 1}
}
func (m *BatchFilterIdsByGameCardResp_Filters) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchFilterIdsByGameCardResp_Filters.Unmarshal(m, b)
}
func (m *BatchFilterIdsByGameCardResp_Filters) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchFilterIdsByGameCardResp_Filters.Marshal(b, m, deterministic)
}
func (dst *BatchFilterIdsByGameCardResp_Filters) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchFilterIdsByGameCardResp_Filters.Merge(dst, src)
}
func (m *BatchFilterIdsByGameCardResp_Filters) XXX_Size() int {
	return xxx_messageInfo_BatchFilterIdsByGameCardResp_Filters.Size(m)
}
func (m *BatchFilterIdsByGameCardResp_Filters) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchFilterIdsByGameCardResp_Filters.DiscardUnknown(m)
}

var xxx_messageInfo_BatchFilterIdsByGameCardResp_Filters proto.InternalMessageInfo

func (m *BatchFilterIdsByGameCardResp_Filters) GetFilterIds() []string {
	if m != nil {
		return m.FilterIds
	}
	return nil
}

type QueryFilterItemsReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FilterId             string   `protobuf:"bytes,2,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryFilterItemsReq) Reset()         { *m = QueryFilterItemsReq{} }
func (m *QueryFilterItemsReq) String() string { return proto.CompactTextString(m) }
func (*QueryFilterItemsReq) ProtoMessage()    {}
func (*QueryFilterItemsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{21}
}
func (m *QueryFilterItemsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryFilterItemsReq.Unmarshal(m, b)
}
func (m *QueryFilterItemsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryFilterItemsReq.Marshal(b, m, deterministic)
}
func (dst *QueryFilterItemsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryFilterItemsReq.Merge(dst, src)
}
func (m *QueryFilterItemsReq) XXX_Size() int {
	return xxx_messageInfo_QueryFilterItemsReq.Size(m)
}
func (m *QueryFilterItemsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryFilterItemsReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryFilterItemsReq proto.InternalMessageInfo

func (m *QueryFilterItemsReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *QueryFilterItemsReq) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *QueryFilterItemsReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type QueryFilterItemsResp struct {
	Items                []*FilterItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *QueryFilterItemsResp) Reset()         { *m = QueryFilterItemsResp{} }
func (m *QueryFilterItemsResp) String() string { return proto.CompactTextString(m) }
func (*QueryFilterItemsResp) ProtoMessage()    {}
func (*QueryFilterItemsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{22}
}
func (m *QueryFilterItemsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryFilterItemsResp.Unmarshal(m, b)
}
func (m *QueryFilterItemsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryFilterItemsResp.Marshal(b, m, deterministic)
}
func (dst *QueryFilterItemsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryFilterItemsResp.Merge(dst, src)
}
func (m *QueryFilterItemsResp) XXX_Size() int {
	return xxx_messageInfo_QueryFilterItemsResp.Size(m)
}
func (m *QueryFilterItemsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryFilterItemsResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryFilterItemsResp proto.InternalMessageInfo

func (m *QueryFilterItemsResp) GetItems() []*FilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type FilterItem struct {
	Id             string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FilterId       string `protobuf:"bytes,2,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	Name           string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	ParentFilterId string `protobuf:"bytes,4,opt,name=parent_filter_id,json=parentFilterId,proto3" json:"parent_filter_id,omitempty"`
	Tabs           []*Tab `protobuf:"bytes,5,rep,name=tabs,proto3" json:"tabs,omitempty"`
	// 仅仅首页的版本区分这么走
	IosVersion           []uint32 `protobuf:"varint,6,rep,packed,name=ios_version,json=iosVersion,proto3" json:"ios_version,omitempty"`
	AndroidVersion       []uint32 `protobuf:"varint,7,rep,packed,name=android_version,json=androidVersion,proto3" json:"android_version,omitempty"`
	Appids               []uint32 `protobuf:"varint,8,rep,packed,name=appids,proto3" json:"appids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FilterItem) Reset()         { *m = FilterItem{} }
func (m *FilterItem) String() string { return proto.CompactTextString(m) }
func (*FilterItem) ProtoMessage()    {}
func (*FilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{23}
}
func (m *FilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterItem.Unmarshal(m, b)
}
func (m *FilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterItem.Marshal(b, m, deterministic)
}
func (dst *FilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterItem.Merge(dst, src)
}
func (m *FilterItem) XXX_Size() int {
	return xxx_messageInfo_FilterItem.Size(m)
}
func (m *FilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_FilterItem proto.InternalMessageInfo

func (m *FilterItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *FilterItem) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *FilterItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *FilterItem) GetParentFilterId() string {
	if m != nil {
		return m.ParentFilterId
	}
	return ""
}

func (m *FilterItem) GetTabs() []*Tab {
	if m != nil {
		return m.Tabs
	}
	return nil
}

func (m *FilterItem) GetIosVersion() []uint32 {
	if m != nil {
		return m.IosVersion
	}
	return nil
}

func (m *FilterItem) GetAndroidVersion() []uint32 {
	if m != nil {
		return m.AndroidVersion
	}
	return nil
}

func (m *FilterItem) GetAppids() []uint32 {
	if m != nil {
		return m.Appids
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type Tab struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tabId,proto3" json:"tabId,omitempty"`
	Blocks               []*Block `protobuf:"bytes,2,rep,name=blocks,proto3" json:"blocks,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Tab) Reset()         { *m = Tab{} }
func (m *Tab) String() string { return proto.CompactTextString(m) }
func (*Tab) ProtoMessage()    {}
func (*Tab) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{24}
}
func (m *Tab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Tab.Unmarshal(m, b)
}
func (m *Tab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Tab.Marshal(b, m, deterministic)
}
func (dst *Tab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Tab.Merge(dst, src)
}
func (m *Tab) XXX_Size() int {
	return xxx_messageInfo_Tab.Size(m)
}
func (m *Tab) XXX_DiscardUnknown() {
	xxx_messageInfo_Tab.DiscardUnknown(m)
}

var xxx_messageInfo_Tab proto.InternalMessageInfo

func (m *Tab) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *Tab) GetBlocks() []*Block {
	if m != nil {
		return m.Blocks
	}
	return nil
}

type Block struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElementId            uint32   `protobuf:"varint,2,opt,name=element_id,json=elementId,proto3" json:"element_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Block) Reset()         { *m = Block{} }
func (m *Block) String() string { return proto.CompactTextString(m) }
func (*Block) ProtoMessage()    {}
func (*Block) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{25}
}
func (m *Block) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Block.Unmarshal(m, b)
}
func (m *Block) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Block.Marshal(b, m, deterministic)
}
func (dst *Block) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Block.Merge(dst, src)
}
func (m *Block) XXX_Size() int {
	return xxx_messageInfo_Block.Size(m)
}
func (m *Block) XXX_DiscardUnknown() {
	xxx_messageInfo_Block.DiscardUnknown(m)
}

var xxx_messageInfo_Block proto.InternalMessageInfo

func (m *Block) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *Block) GetElementId() uint32 {
	if m != nil {
		return m.ElementId
	}
	return 0
}

type GetMusicFilterItemByIdsReq struct {
	FilterIds            []string `protobuf:"bytes,1,rep,name=filter_ids,json=filterIds,proto3" json:"filter_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicFilterItemByIdsReq) Reset()         { *m = GetMusicFilterItemByIdsReq{} }
func (m *GetMusicFilterItemByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicFilterItemByIdsReq) ProtoMessage()    {}
func (*GetMusicFilterItemByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{26}
}
func (m *GetMusicFilterItemByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicFilterItemByIdsReq.Unmarshal(m, b)
}
func (m *GetMusicFilterItemByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicFilterItemByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicFilterItemByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicFilterItemByIdsReq.Merge(dst, src)
}
func (m *GetMusicFilterItemByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicFilterItemByIdsReq.Size(m)
}
func (m *GetMusicFilterItemByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicFilterItemByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicFilterItemByIdsReq proto.InternalMessageInfo

func (m *GetMusicFilterItemByIdsReq) GetFilterIds() []string {
	if m != nil {
		return m.FilterIds
	}
	return nil
}

type GetMusicFilterItemByIdsResp struct {
	FilterMap            map[string]*MusicFilterItem `protobuf:"bytes,1,rep,name=filter_map,json=filterMap,proto3" json:"filter_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetMusicFilterItemByIdsResp) Reset()         { *m = GetMusicFilterItemByIdsResp{} }
func (m *GetMusicFilterItemByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicFilterItemByIdsResp) ProtoMessage()    {}
func (*GetMusicFilterItemByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{27}
}
func (m *GetMusicFilterItemByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicFilterItemByIdsResp.Unmarshal(m, b)
}
func (m *GetMusicFilterItemByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicFilterItemByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicFilterItemByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicFilterItemByIdsResp.Merge(dst, src)
}
func (m *GetMusicFilterItemByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicFilterItemByIdsResp.Size(m)
}
func (m *GetMusicFilterItemByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicFilterItemByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicFilterItemByIdsResp proto.InternalMessageInfo

func (m *GetMusicFilterItemByIdsResp) GetFilterMap() map[string]*MusicFilterItem {
	if m != nil {
		return m.FilterMap
	}
	return nil
}

type MusicFilterItem struct {
	FilterId             string             `protobuf:"bytes,1,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	Name                 string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string             `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Images               []string           `protobuf:"bytes,4,rep,name=images,proto3" json:"images,omitempty"`
	SubFilters           []*MusicFilterItem `protobuf:"bytes,5,rep,name=sub_filters,json=subFilters,proto3" json:"sub_filters,omitempty"`
	FilterAttrType       uint32             `protobuf:"varint,6,opt,name=filter_attr_type,json=filterAttrType,proto3" json:"filter_attr_type,omitempty"`
	CityTitle            *SameCityTitle     `protobuf:"bytes,7,opt,name=city_title,json=cityTitle,proto3" json:"city_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MusicFilterItem) Reset()         { *m = MusicFilterItem{} }
func (m *MusicFilterItem) String() string { return proto.CompactTextString(m) }
func (*MusicFilterItem) ProtoMessage()    {}
func (*MusicFilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{28}
}
func (m *MusicFilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicFilterItem.Unmarshal(m, b)
}
func (m *MusicFilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicFilterItem.Marshal(b, m, deterministic)
}
func (dst *MusicFilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicFilterItem.Merge(dst, src)
}
func (m *MusicFilterItem) XXX_Size() int {
	return xxx_messageInfo_MusicFilterItem.Size(m)
}
func (m *MusicFilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicFilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_MusicFilterItem proto.InternalMessageInfo

func (m *MusicFilterItem) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *MusicFilterItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MusicFilterItem) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MusicFilterItem) GetImages() []string {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *MusicFilterItem) GetSubFilters() []*MusicFilterItem {
	if m != nil {
		return m.SubFilters
	}
	return nil
}

func (m *MusicFilterItem) GetFilterAttrType() uint32 {
	if m != nil {
		return m.FilterAttrType
	}
	return 0
}

func (m *MusicFilterItem) GetCityTitle() *SameCityTitle {
	if m != nil {
		return m.CityTitle
	}
	return nil
}

type RcmdInfo struct {
	ChannelInfoMap       map[uint32]*common.ChannelInfo `protobuf:"bytes,1,rep,name=channel_info_map,json=channelInfoMap,proto3" json:"channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *RcmdInfo) Reset()         { *m = RcmdInfo{} }
func (m *RcmdInfo) String() string { return proto.CompactTextString(m) }
func (*RcmdInfo) ProtoMessage()    {}
func (*RcmdInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{29}
}
func (m *RcmdInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RcmdInfo.Unmarshal(m, b)
}
func (m *RcmdInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RcmdInfo.Marshal(b, m, deterministic)
}
func (dst *RcmdInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RcmdInfo.Merge(dst, src)
}
func (m *RcmdInfo) XXX_Size() int {
	return xxx_messageInfo_RcmdInfo.Size(m)
}
func (m *RcmdInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RcmdInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RcmdInfo proto.InternalMessageInfo

func (m *RcmdInfo) GetChannelInfoMap() map[uint32]*common.ChannelInfo {
	if m != nil {
		return m.ChannelInfoMap
	}
	return nil
}

type ListMusicChannelViewsReq struct {
	ChannelIds           []uint32  `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	IsAll                bool      `protobuf:"varint,2,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	RcmdInfo             *RcmdInfo `protobuf:"bytes,3,opt,name=rcmd_info,json=rcmdInfo,proto3" json:"rcmd_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ListMusicChannelViewsReq) Reset()         { *m = ListMusicChannelViewsReq{} }
func (m *ListMusicChannelViewsReq) String() string { return proto.CompactTextString(m) }
func (*ListMusicChannelViewsReq) ProtoMessage()    {}
func (*ListMusicChannelViewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{30}
}
func (m *ListMusicChannelViewsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicChannelViewsReq.Unmarshal(m, b)
}
func (m *ListMusicChannelViewsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicChannelViewsReq.Marshal(b, m, deterministic)
}
func (dst *ListMusicChannelViewsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicChannelViewsReq.Merge(dst, src)
}
func (m *ListMusicChannelViewsReq) XXX_Size() int {
	return xxx_messageInfo_ListMusicChannelViewsReq.Size(m)
}
func (m *ListMusicChannelViewsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicChannelViewsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicChannelViewsReq proto.InternalMessageInfo

func (m *ListMusicChannelViewsReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

func (m *ListMusicChannelViewsReq) GetIsAll() bool {
	if m != nil {
		return m.IsAll
	}
	return false
}

func (m *ListMusicChannelViewsReq) GetRcmdInfo() *RcmdInfo {
	if m != nil {
		return m.RcmdInfo
	}
	return nil
}

type ListMusicChannelViewsResp struct {
	ChannelViews         map[uint32]*music_topic_channel.MusicChannel `protobuf:"bytes,1,rep,name=channel_views,json=channelViews,proto3" json:"channel_views,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *ListMusicChannelViewsResp) Reset()         { *m = ListMusicChannelViewsResp{} }
func (m *ListMusicChannelViewsResp) String() string { return proto.CompactTextString(m) }
func (*ListMusicChannelViewsResp) ProtoMessage()    {}
func (*ListMusicChannelViewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{31}
}
func (m *ListMusicChannelViewsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicChannelViewsResp.Unmarshal(m, b)
}
func (m *ListMusicChannelViewsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicChannelViewsResp.Marshal(b, m, deterministic)
}
func (dst *ListMusicChannelViewsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicChannelViewsResp.Merge(dst, src)
}
func (m *ListMusicChannelViewsResp) XXX_Size() int {
	return xxx_messageInfo_ListMusicChannelViewsResp.Size(m)
}
func (m *ListMusicChannelViewsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicChannelViewsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicChannelViewsResp proto.InternalMessageInfo

func (m *ListMusicChannelViewsResp) GetChannelViews() map[uint32]*music_topic_channel.MusicChannel {
	if m != nil {
		return m.ChannelViews
	}
	return nil
}

type ListHomePageFilterItemsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListHomePageFilterItemsReq) Reset()         { *m = ListHomePageFilterItemsReq{} }
func (m *ListHomePageFilterItemsReq) String() string { return proto.CompactTextString(m) }
func (*ListHomePageFilterItemsReq) ProtoMessage()    {}
func (*ListHomePageFilterItemsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{32}
}
func (m *ListHomePageFilterItemsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListHomePageFilterItemsReq.Unmarshal(m, b)
}
func (m *ListHomePageFilterItemsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListHomePageFilterItemsReq.Marshal(b, m, deterministic)
}
func (dst *ListHomePageFilterItemsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListHomePageFilterItemsReq.Merge(dst, src)
}
func (m *ListHomePageFilterItemsReq) XXX_Size() int {
	return xxx_messageInfo_ListHomePageFilterItemsReq.Size(m)
}
func (m *ListHomePageFilterItemsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListHomePageFilterItemsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListHomePageFilterItemsReq proto.InternalMessageInfo

type ListHomePageFilterItemsResp struct {
	Items                []*MusicFilterItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ListHomePageFilterItemsResp) Reset()         { *m = ListHomePageFilterItemsResp{} }
func (m *ListHomePageFilterItemsResp) String() string { return proto.CompactTextString(m) }
func (*ListHomePageFilterItemsResp) ProtoMessage()    {}
func (*ListHomePageFilterItemsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{33}
}
func (m *ListHomePageFilterItemsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListHomePageFilterItemsResp.Unmarshal(m, b)
}
func (m *ListHomePageFilterItemsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListHomePageFilterItemsResp.Marshal(b, m, deterministic)
}
func (dst *ListHomePageFilterItemsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListHomePageFilterItemsResp.Merge(dst, src)
}
func (m *ListHomePageFilterItemsResp) XXX_Size() int {
	return xxx_messageInfo_ListHomePageFilterItemsResp.Size(m)
}
func (m *ListHomePageFilterItemsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListHomePageFilterItemsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListHomePageFilterItemsResp proto.InternalMessageInfo

func (m *ListHomePageFilterItemsResp) GetItems() []*MusicFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type GetUserSchoolLastReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSchoolLastReq) Reset()         { *m = GetUserSchoolLastReq{} }
func (m *GetUserSchoolLastReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSchoolLastReq) ProtoMessage()    {}
func (*GetUserSchoolLastReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{34}
}
func (m *GetUserSchoolLastReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSchoolLastReq.Unmarshal(m, b)
}
func (m *GetUserSchoolLastReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSchoolLastReq.Marshal(b, m, deterministic)
}
func (dst *GetUserSchoolLastReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSchoolLastReq.Merge(dst, src)
}
func (m *GetUserSchoolLastReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSchoolLastReq.Size(m)
}
func (m *GetUserSchoolLastReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSchoolLastReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSchoolLastReq proto.InternalMessageInfo

func (m *GetUserSchoolLastReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserSchoolLastResp struct {
	Last                 uint32   `protobuf:"varint,2,opt,name=last,proto3" json:"last,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSchoolLastResp) Reset()         { *m = GetUserSchoolLastResp{} }
func (m *GetUserSchoolLastResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSchoolLastResp) ProtoMessage()    {}
func (*GetUserSchoolLastResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{35}
}
func (m *GetUserSchoolLastResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSchoolLastResp.Unmarshal(m, b)
}
func (m *GetUserSchoolLastResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSchoolLastResp.Marshal(b, m, deterministic)
}
func (dst *GetUserSchoolLastResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSchoolLastResp.Merge(dst, src)
}
func (m *GetUserSchoolLastResp) XXX_Size() int {
	return xxx_messageInfo_GetUserSchoolLastResp.Size(m)
}
func (m *GetUserSchoolLastResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSchoolLastResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSchoolLastResp proto.InternalMessageInfo

func (m *GetUserSchoolLastResp) GetLast() uint32 {
	if m != nil {
		return m.Last
	}
	return 0
}

type SetUserSchoolLastReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Last                 uint32   `protobuf:"varint,2,opt,name=last,proto3" json:"last,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSchoolLastReq) Reset()         { *m = SetUserSchoolLastReq{} }
func (m *SetUserSchoolLastReq) String() string { return proto.CompactTextString(m) }
func (*SetUserSchoolLastReq) ProtoMessage()    {}
func (*SetUserSchoolLastReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{36}
}
func (m *SetUserSchoolLastReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSchoolLastReq.Unmarshal(m, b)
}
func (m *SetUserSchoolLastReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSchoolLastReq.Marshal(b, m, deterministic)
}
func (dst *SetUserSchoolLastReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSchoolLastReq.Merge(dst, src)
}
func (m *SetUserSchoolLastReq) XXX_Size() int {
	return xxx_messageInfo_SetUserSchoolLastReq.Size(m)
}
func (m *SetUserSchoolLastReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSchoolLastReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSchoolLastReq proto.InternalMessageInfo

func (m *SetUserSchoolLastReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserSchoolLastReq) GetLast() uint32 {
	if m != nil {
		return m.Last
	}
	return 0
}

type SetUserSchoolLastResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSchoolLastResp) Reset()         { *m = SetUserSchoolLastResp{} }
func (m *SetUserSchoolLastResp) String() string { return proto.CompactTextString(m) }
func (*SetUserSchoolLastResp) ProtoMessage()    {}
func (*SetUserSchoolLastResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{37}
}
func (m *SetUserSchoolLastResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSchoolLastResp.Unmarshal(m, b)
}
func (m *SetUserSchoolLastResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSchoolLastResp.Marshal(b, m, deterministic)
}
func (dst *SetUserSchoolLastResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSchoolLastResp.Merge(dst, src)
}
func (m *SetUserSchoolLastResp) XXX_Size() int {
	return xxx_messageInfo_SetUserSchoolLastResp.Size(m)
}
func (m *SetUserSchoolLastResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSchoolLastResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSchoolLastResp proto.InternalMessageInfo

type GetRcmdPgcChannelReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRcmdPgcChannelReq) Reset()         { *m = GetRcmdPgcChannelReq{} }
func (m *GetRcmdPgcChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetRcmdPgcChannelReq) ProtoMessage()    {}
func (*GetRcmdPgcChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{38}
}
func (m *GetRcmdPgcChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRcmdPgcChannelReq.Unmarshal(m, b)
}
func (m *GetRcmdPgcChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRcmdPgcChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetRcmdPgcChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdPgcChannelReq.Merge(dst, src)
}
func (m *GetRcmdPgcChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetRcmdPgcChannelReq.Size(m)
}
func (m *GetRcmdPgcChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdPgcChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdPgcChannelReq proto.InternalMessageInfo

func (m *GetRcmdPgcChannelReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type GetRcmdPgcChannelResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRcmdPgcChannelResp) Reset()         { *m = GetRcmdPgcChannelResp{} }
func (m *GetRcmdPgcChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetRcmdPgcChannelResp) ProtoMessage()    {}
func (*GetRcmdPgcChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{39}
}
func (m *GetRcmdPgcChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRcmdPgcChannelResp.Unmarshal(m, b)
}
func (m *GetRcmdPgcChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRcmdPgcChannelResp.Marshal(b, m, deterministic)
}
func (dst *GetRcmdPgcChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdPgcChannelResp.Merge(dst, src)
}
func (m *GetRcmdPgcChannelResp) XXX_Size() int {
	return xxx_messageInfo_GetRcmdPgcChannelResp.Size(m)
}
func (m *GetRcmdPgcChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdPgcChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdPgcChannelResp proto.InternalMessageInfo

func (m *GetRcmdPgcChannelResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type IsOlderForMusicHomePageReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsOlderForMusicHomePageReq) Reset()         { *m = IsOlderForMusicHomePageReq{} }
func (m *IsOlderForMusicHomePageReq) String() string { return proto.CompactTextString(m) }
func (*IsOlderForMusicHomePageReq) ProtoMessage()    {}
func (*IsOlderForMusicHomePageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{40}
}
func (m *IsOlderForMusicHomePageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsOlderForMusicHomePageReq.Unmarshal(m, b)
}
func (m *IsOlderForMusicHomePageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsOlderForMusicHomePageReq.Marshal(b, m, deterministic)
}
func (dst *IsOlderForMusicHomePageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsOlderForMusicHomePageReq.Merge(dst, src)
}
func (m *IsOlderForMusicHomePageReq) XXX_Size() int {
	return xxx_messageInfo_IsOlderForMusicHomePageReq.Size(m)
}
func (m *IsOlderForMusicHomePageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsOlderForMusicHomePageReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsOlderForMusicHomePageReq proto.InternalMessageInfo

func (m *IsOlderForMusicHomePageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsOlderForMusicHomePageResp struct {
	IsOlder              bool     `protobuf:"varint,1,opt,name=is_older,json=isOlder,proto3" json:"is_older,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsOlderForMusicHomePageResp) Reset()         { *m = IsOlderForMusicHomePageResp{} }
func (m *IsOlderForMusicHomePageResp) String() string { return proto.CompactTextString(m) }
func (*IsOlderForMusicHomePageResp) ProtoMessage()    {}
func (*IsOlderForMusicHomePageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{41}
}
func (m *IsOlderForMusicHomePageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsOlderForMusicHomePageResp.Unmarshal(m, b)
}
func (m *IsOlderForMusicHomePageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsOlderForMusicHomePageResp.Marshal(b, m, deterministic)
}
func (dst *IsOlderForMusicHomePageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsOlderForMusicHomePageResp.Merge(dst, src)
}
func (m *IsOlderForMusicHomePageResp) XXX_Size() int {
	return xxx_messageInfo_IsOlderForMusicHomePageResp.Size(m)
}
func (m *IsOlderForMusicHomePageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsOlderForMusicHomePageResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsOlderForMusicHomePageResp proto.InternalMessageInfo

func (m *IsOlderForMusicHomePageResp) GetIsOlder() bool {
	if m != nil {
		return m.IsOlder
	}
	return false
}

type GetMusicChannelFilterV2Req struct {
	FilterType           string   `protobuf:"bytes,1,opt,name=filter_type,json=filterType,proto3" json:"filter_type,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicChannelFilterV2Req) Reset()         { *m = GetMusicChannelFilterV2Req{} }
func (m *GetMusicChannelFilterV2Req) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterV2Req) ProtoMessage()    {}
func (*GetMusicChannelFilterV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{42}
}
func (m *GetMusicChannelFilterV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Req.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Req.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Req.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Req) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Req.Size(m)
}
func (m *GetMusicChannelFilterV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Req proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Req) GetFilterType() string {
	if m != nil {
		return m.FilterType
	}
	return ""
}

func (m *GetMusicChannelFilterV2Req) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetMusicChannelFilterV2Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMusicChannelFilterV2Resp struct {
	FilterItems          []*GetMusicChannelFilterV2Resp_FilterItem `protobuf:"bytes,1,rep,name=filter_items,json=filterItems,proto3" json:"filter_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *GetMusicChannelFilterV2Resp) Reset()         { *m = GetMusicChannelFilterV2Resp{} }
func (m *GetMusicChannelFilterV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterV2Resp) ProtoMessage()    {}
func (*GetMusicChannelFilterV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{43}
}
func (m *GetMusicChannelFilterV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Resp.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp.Size(m)
}
func (m *GetMusicChannelFilterV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Resp proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Resp) GetFilterItems() []*GetMusicChannelFilterV2Resp_FilterItem {
	if m != nil {
		return m.FilterItems
	}
	return nil
}

type GetMusicChannelFilterV2Resp_FilterItem struct {
	Title                string                                       `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	FilterItemType       string                                       `protobuf:"bytes,2,opt,name=filter_item_type,json=filterItemType,proto3" json:"filter_item_type,omitempty"`
	FilterId             string                                       `protobuf:"bytes,3,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	FilterSubItems       []*GetMusicChannelFilterV2Resp_FilterSubItem `protobuf:"bytes,4,rep,name=filter_sub_items,json=filterSubItems,proto3" json:"filter_sub_items,omitempty"`
	Tip                  string                                       `protobuf:"bytes,5,opt,name=tip,proto3" json:"tip,omitempty"`
	FilterAttrType       uint32                                       `protobuf:"varint,6,opt,name=filter_attr_type,json=filterAttrType,proto3" json:"filter_attr_type,omitempty"`
	CityTitle            *SameCityTitle                               `protobuf:"bytes,7,opt,name=city_title,json=cityTitle,proto3" json:"city_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) Reset() {
	*m = GetMusicChannelFilterV2Resp_FilterItem{}
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterV2Resp_FilterItem) ProtoMessage()    {}
func (*GetMusicChannelFilterV2Resp_FilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{43, 0}
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Resp_FilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Size(m)
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterItemType() string {
	if m != nil {
		return m.FilterItemType
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterSubItems() []*GetMusicChannelFilterV2Resp_FilterSubItem {
	if m != nil {
		return m.FilterSubItems
	}
	return nil
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterAttrType() uint32 {
	if m != nil {
		return m.FilterAttrType
	}
	return 0
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetCityTitle() *SameCityTitle {
	if m != nil {
		return m.CityTitle
	}
	return nil
}

type GetMusicChannelFilterV2Resp_FilterSubItem struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	FilterSubId          string   `protobuf:"bytes,2,opt,name=filter_sub_id,json=filterSubId,proto3" json:"filter_sub_id,omitempty"`
	FilterSubItemType    string   `protobuf:"bytes,3,opt,name=filter_sub_item_type,json=filterSubItemType,proto3" json:"filter_sub_item_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) Reset() {
	*m = GetMusicChannelFilterV2Resp_FilterSubItem{}
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterV2Resp_FilterSubItem) ProtoMessage()    {}
func (*GetMusicChannelFilterV2Resp_FilterSubItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{43, 1}
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Size(m)
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) GetFilterSubId() string {
	if m != nil {
		return m.FilterSubId
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) GetFilterSubItemType() string {
	if m != nil {
		return m.FilterSubItemType
	}
	return ""
}

// 标签是同城则使用此结构体信息替换title
type SameCityTitle struct {
	CityName             string   `protobuf:"bytes,1,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	ProvinceName         string   `protobuf:"bytes,2,opt,name=province_name,json=provinceName,proto3" json:"province_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SameCityTitle) Reset()         { *m = SameCityTitle{} }
func (m *SameCityTitle) String() string { return proto.CompactTextString(m) }
func (*SameCityTitle) ProtoMessage()    {}
func (*SameCityTitle) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{44}
}
func (m *SameCityTitle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SameCityTitle.Unmarshal(m, b)
}
func (m *SameCityTitle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SameCityTitle.Marshal(b, m, deterministic)
}
func (dst *SameCityTitle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SameCityTitle.Merge(dst, src)
}
func (m *SameCityTitle) XXX_Size() int {
	return xxx_messageInfo_SameCityTitle.Size(m)
}
func (m *SameCityTitle) XXX_DiscardUnknown() {
	xxx_messageInfo_SameCityTitle.DiscardUnknown(m)
}

var xxx_messageInfo_SameCityTitle proto.InternalMessageInfo

func (m *SameCityTitle) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *SameCityTitle) GetProvinceName() string {
	if m != nil {
		return m.ProvinceName
	}
	return ""
}

type BatchIsPublishingReq struct {
	Channels             []*BatchIsPublishingReq_TabChannel `protobuf:"bytes,1,rep,name=channels,proto3" json:"channels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *BatchIsPublishingReq) Reset()         { *m = BatchIsPublishingReq{} }
func (m *BatchIsPublishingReq) String() string { return proto.CompactTextString(m) }
func (*BatchIsPublishingReq) ProtoMessage()    {}
func (*BatchIsPublishingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{45}
}
func (m *BatchIsPublishingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchIsPublishingReq.Unmarshal(m, b)
}
func (m *BatchIsPublishingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchIsPublishingReq.Marshal(b, m, deterministic)
}
func (dst *BatchIsPublishingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchIsPublishingReq.Merge(dst, src)
}
func (m *BatchIsPublishingReq) XXX_Size() int {
	return xxx_messageInfo_BatchIsPublishingReq.Size(m)
}
func (m *BatchIsPublishingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchIsPublishingReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchIsPublishingReq proto.InternalMessageInfo

func (m *BatchIsPublishingReq) GetChannels() []*BatchIsPublishingReq_TabChannel {
	if m != nil {
		return m.Channels
	}
	return nil
}

type BatchIsPublishingReq_TabChannel struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchIsPublishingReq_TabChannel) Reset()         { *m = BatchIsPublishingReq_TabChannel{} }
func (m *BatchIsPublishingReq_TabChannel) String() string { return proto.CompactTextString(m) }
func (*BatchIsPublishingReq_TabChannel) ProtoMessage()    {}
func (*BatchIsPublishingReq_TabChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{45, 0}
}
func (m *BatchIsPublishingReq_TabChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchIsPublishingReq_TabChannel.Unmarshal(m, b)
}
func (m *BatchIsPublishingReq_TabChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchIsPublishingReq_TabChannel.Marshal(b, m, deterministic)
}
func (dst *BatchIsPublishingReq_TabChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchIsPublishingReq_TabChannel.Merge(dst, src)
}
func (m *BatchIsPublishingReq_TabChannel) XXX_Size() int {
	return xxx_messageInfo_BatchIsPublishingReq_TabChannel.Size(m)
}
func (m *BatchIsPublishingReq_TabChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchIsPublishingReq_TabChannel.DiscardUnknown(m)
}

var xxx_messageInfo_BatchIsPublishingReq_TabChannel proto.InternalMessageInfo

func (m *BatchIsPublishingReq_TabChannel) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *BatchIsPublishingReq_TabChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type BatchIsPublishingResp struct {
	ChannelPublishing    map[uint32]bool `protobuf:"bytes,1,rep,name=channel_publishing,json=channelPublishing,proto3" json:"channel_publishing,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchIsPublishingResp) Reset()         { *m = BatchIsPublishingResp{} }
func (m *BatchIsPublishingResp) String() string { return proto.CompactTextString(m) }
func (*BatchIsPublishingResp) ProtoMessage()    {}
func (*BatchIsPublishingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{46}
}
func (m *BatchIsPublishingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchIsPublishingResp.Unmarshal(m, b)
}
func (m *BatchIsPublishingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchIsPublishingResp.Marshal(b, m, deterministic)
}
func (dst *BatchIsPublishingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchIsPublishingResp.Merge(dst, src)
}
func (m *BatchIsPublishingResp) XXX_Size() int {
	return xxx_messageInfo_BatchIsPublishingResp.Size(m)
}
func (m *BatchIsPublishingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchIsPublishingResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchIsPublishingResp proto.InternalMessageInfo

func (m *BatchIsPublishingResp) GetChannelPublishing() map[uint32]bool {
	if m != nil {
		return m.ChannelPublishing
	}
	return nil
}

type IsPublishingReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsPublishingReq) Reset()         { *m = IsPublishingReq{} }
func (m *IsPublishingReq) String() string { return proto.CompactTextString(m) }
func (*IsPublishingReq) ProtoMessage()    {}
func (*IsPublishingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{47}
}
func (m *IsPublishingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsPublishingReq.Unmarshal(m, b)
}
func (m *IsPublishingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsPublishingReq.Marshal(b, m, deterministic)
}
func (dst *IsPublishingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsPublishingReq.Merge(dst, src)
}
func (m *IsPublishingReq) XXX_Size() int {
	return xxx_messageInfo_IsPublishingReq.Size(m)
}
func (m *IsPublishingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsPublishingReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsPublishingReq proto.InternalMessageInfo

func (m *IsPublishingReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *IsPublishingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type IsPublishingResp struct {
	IsPublishing         bool     `protobuf:"varint,1,opt,name=isPublishing,proto3" json:"isPublishing,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsPublishingResp) Reset()         { *m = IsPublishingResp{} }
func (m *IsPublishingResp) String() string { return proto.CompactTextString(m) }
func (*IsPublishingResp) ProtoMessage()    {}
func (*IsPublishingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{48}
}
func (m *IsPublishingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsPublishingResp.Unmarshal(m, b)
}
func (m *IsPublishingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsPublishingResp.Marshal(b, m, deterministic)
}
func (dst *IsPublishingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsPublishingResp.Merge(dst, src)
}
func (m *IsPublishingResp) XXX_Size() int {
	return xxx_messageInfo_IsPublishingResp.Size(m)
}
func (m *IsPublishingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsPublishingResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsPublishingResp proto.InternalMessageInfo

func (m *IsPublishingResp) GetIsPublishing() bool {
	if m != nil {
		return m.IsPublishing
	}
	return false
}

type ListPublishingChannelIdsReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TimeOffset           int64    `protobuf:"varint,3,opt,name=time_offset,json=timeOffset,proto3" json:"time_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListPublishingChannelIdsReq) Reset()         { *m = ListPublishingChannelIdsReq{} }
func (m *ListPublishingChannelIdsReq) String() string { return proto.CompactTextString(m) }
func (*ListPublishingChannelIdsReq) ProtoMessage()    {}
func (*ListPublishingChannelIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{49}
}
func (m *ListPublishingChannelIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListPublishingChannelIdsReq.Unmarshal(m, b)
}
func (m *ListPublishingChannelIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListPublishingChannelIdsReq.Marshal(b, m, deterministic)
}
func (dst *ListPublishingChannelIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListPublishingChannelIdsReq.Merge(dst, src)
}
func (m *ListPublishingChannelIdsReq) XXX_Size() int {
	return xxx_messageInfo_ListPublishingChannelIdsReq.Size(m)
}
func (m *ListPublishingChannelIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListPublishingChannelIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListPublishingChannelIdsReq proto.InternalMessageInfo

func (m *ListPublishingChannelIdsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ListPublishingChannelIdsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ListPublishingChannelIdsReq) GetTimeOffset() int64 {
	if m != nil {
		return m.TimeOffset
	}
	return 0
}

type ListPublishingChannelIdsResp struct {
	Channels             []*ListPublishingChannelIdsRespChannel `protobuf:"bytes,1,rep,name=channels,proto3" json:"channels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *ListPublishingChannelIdsResp) Reset()         { *m = ListPublishingChannelIdsResp{} }
func (m *ListPublishingChannelIdsResp) String() string { return proto.CompactTextString(m) }
func (*ListPublishingChannelIdsResp) ProtoMessage()    {}
func (*ListPublishingChannelIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{50}
}
func (m *ListPublishingChannelIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListPublishingChannelIdsResp.Unmarshal(m, b)
}
func (m *ListPublishingChannelIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListPublishingChannelIdsResp.Marshal(b, m, deterministic)
}
func (dst *ListPublishingChannelIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListPublishingChannelIdsResp.Merge(dst, src)
}
func (m *ListPublishingChannelIdsResp) XXX_Size() int {
	return xxx_messageInfo_ListPublishingChannelIdsResp.Size(m)
}
func (m *ListPublishingChannelIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListPublishingChannelIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListPublishingChannelIdsResp proto.InternalMessageInfo

func (m *ListPublishingChannelIdsResp) GetChannels() []*ListPublishingChannelIdsRespChannel {
	if m != nil {
		return m.Channels
	}
	return nil
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
type ListPublishingChannelIdsRespChannel struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Score                int64    `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListPublishingChannelIdsRespChannel) Reset()         { *m = ListPublishingChannelIdsRespChannel{} }
func (m *ListPublishingChannelIdsRespChannel) String() string { return proto.CompactTextString(m) }
func (*ListPublishingChannelIdsRespChannel) ProtoMessage()    {}
func (*ListPublishingChannelIdsRespChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{50, 0}
}
func (m *ListPublishingChannelIdsRespChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListPublishingChannelIdsRespChannel.Unmarshal(m, b)
}
func (m *ListPublishingChannelIdsRespChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListPublishingChannelIdsRespChannel.Marshal(b, m, deterministic)
}
func (dst *ListPublishingChannelIdsRespChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListPublishingChannelIdsRespChannel.Merge(dst, src)
}
func (m *ListPublishingChannelIdsRespChannel) XXX_Size() int {
	return xxx_messageInfo_ListPublishingChannelIdsRespChannel.Size(m)
}
func (m *ListPublishingChannelIdsRespChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_ListPublishingChannelIdsRespChannel.DiscardUnknown(m)
}

var xxx_messageInfo_ListPublishingChannelIdsRespChannel proto.InternalMessageInfo

func (m *ListPublishingChannelIdsRespChannel) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ListPublishingChannelIdsRespChannel) GetScore() int64 {
	if m != nil {
		return m.Score
	}
	return 0
}

type BlockOptionList struct {
	BlockOptions         []*BlockOption `protobuf:"bytes,1,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BlockOptionList) Reset()         { *m = BlockOptionList{} }
func (m *BlockOptionList) String() string { return proto.CompactTextString(m) }
func (*BlockOptionList) ProtoMessage()    {}
func (*BlockOptionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{51}
}
func (m *BlockOptionList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOptionList.Unmarshal(m, b)
}
func (m *BlockOptionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOptionList.Marshal(b, m, deterministic)
}
func (dst *BlockOptionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOptionList.Merge(dst, src)
}
func (m *BlockOptionList) XXX_Size() int {
	return xxx_messageInfo_BlockOptionList.Size(m)
}
func (m *BlockOptionList) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOptionList.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOptionList proto.InternalMessageInfo

func (m *BlockOptionList) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	ElemVal              string   `protobuf:"bytes,3,opt,name=elem_val,json=elemVal,proto3" json:"elem_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{52}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

func (m *BlockOption) GetElemVal() string {
	if m != nil {
		return m.ElemVal
	}
	return ""
}

type MusicChannelReleaseInfo struct {
	Id                   uint32               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TabId                uint32               `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ReleaseTime          int64                `protobuf:"varint,3,opt,name=release_time,json=releaseTime,proto3" json:"release_time,omitempty"`
	BlockOptions         []*BlockOption       `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	DisplayType          []ChannelDisplayType `protobuf:"varint,5,rep,packed,name=display_type,json=displayType,proto3,enum=music_topic_channel.ChannelDisplayType" json:"display_type,omitempty"`
	ShowGeoInfo          bool                 `protobuf:"varint,6,opt,name=show_geo_info,json=showGeoInfo,proto3" json:"show_geo_info,omitempty"`
	UpdateTime           int64                `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	PublishTimes         uint32               `protobuf:"varint,8,opt,name=publish_times,json=publishTimes,proto3" json:"publish_times,omitempty"`
	GameLabels           []*GameLabel         `protobuf:"bytes,12,rep,name=game_labels,json=gameLabels,proto3" json:"game_labels,omitempty"`
	ClientType           uint32               `protobuf:"varint,13,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *MusicChannelReleaseInfo) Reset()         { *m = MusicChannelReleaseInfo{} }
func (m *MusicChannelReleaseInfo) String() string { return proto.CompactTextString(m) }
func (*MusicChannelReleaseInfo) ProtoMessage()    {}
func (*MusicChannelReleaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{53}
}
func (m *MusicChannelReleaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicChannelReleaseInfo.Unmarshal(m, b)
}
func (m *MusicChannelReleaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicChannelReleaseInfo.Marshal(b, m, deterministic)
}
func (dst *MusicChannelReleaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicChannelReleaseInfo.Merge(dst, src)
}
func (m *MusicChannelReleaseInfo) XXX_Size() int {
	return xxx_messageInfo_MusicChannelReleaseInfo.Size(m)
}
func (m *MusicChannelReleaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicChannelReleaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MusicChannelReleaseInfo proto.InternalMessageInfo

func (m *MusicChannelReleaseInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MusicChannelReleaseInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MusicChannelReleaseInfo) GetReleaseTime() int64 {
	if m != nil {
		return m.ReleaseTime
	}
	return 0
}

func (m *MusicChannelReleaseInfo) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *MusicChannelReleaseInfo) GetDisplayType() []ChannelDisplayType {
	if m != nil {
		return m.DisplayType
	}
	return nil
}

func (m *MusicChannelReleaseInfo) GetShowGeoInfo() bool {
	if m != nil {
		return m.ShowGeoInfo
	}
	return false
}

func (m *MusicChannelReleaseInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *MusicChannelReleaseInfo) GetPublishTimes() uint32 {
	if m != nil {
		return m.PublishTimes
	}
	return 0
}

func (m *MusicChannelReleaseInfo) GetGameLabels() []*GameLabel {
	if m != nil {
		return m.GameLabels
	}
	return nil
}

func (m *MusicChannelReleaseInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GameLabel struct {
	Val                  string   `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
	DisplayName          string   `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameLabel) Reset()         { *m = GameLabel{} }
func (m *GameLabel) String() string { return proto.CompactTextString(m) }
func (*GameLabel) ProtoMessage()    {}
func (*GameLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{54}
}
func (m *GameLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameLabel.Unmarshal(m, b)
}
func (m *GameLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameLabel.Marshal(b, m, deterministic)
}
func (dst *GameLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameLabel.Merge(dst, src)
}
func (m *GameLabel) XXX_Size() int {
	return xxx_messageInfo_GameLabel.Size(m)
}
func (m *GameLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_GameLabel.DiscardUnknown(m)
}

var xxx_messageInfo_GameLabel proto.InternalMessageInfo

func (m *GameLabel) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *GameLabel) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *GameLabel) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SetMusicChannelReleaseInfoReq struct {
	MusicChannelReleaseInfo *MusicChannelReleaseInfo `protobuf:"bytes,1,opt,name=Music_channel_release_info,json=MusicChannelReleaseInfo,proto3" json:"Music_channel_release_info,omitempty"`
	WantFresh               bool                     `protobuf:"varint,2,opt,name=want_fresh,json=wantFresh,proto3" json:"want_fresh,omitempty"`
	ReleaseIp               string                   `protobuf:"bytes,3,opt,name=release_ip,json=releaseIp,proto3" json:"release_ip,omitempty"`
	ChannelName             string                   `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	Creator                 uint32                   `protobuf:"varint,5,opt,name=creator,proto3" json:"creator,omitempty"`
	PublishType             string                   `protobuf:"bytes,6,opt,name=publish_type,json=publishType,proto3" json:"publish_type,omitempty"`
	AllSelectedBids         []uint32                 `protobuf:"varint,7,rep,packed,name=all_selected_bids,json=allSelectedBids,proto3" json:"all_selected_bids,omitempty"`
	UnSelectBlockId         []uint32                 `protobuf:"varint,8,rep,packed,name=un_select_block_id,json=unSelectBlockId,proto3" json:"un_select_block_id,omitempty"`
	UnSelectBlockOptions    []*BlockOption           `protobuf:"bytes,9,rep,name=un_select_block_options,json=unSelectBlockOptions,proto3" json:"un_select_block_options,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                 `json:"-"`
	XXX_unrecognized        []byte                   `json:"-"`
	XXX_sizecache           int32                    `json:"-"`
}

func (m *SetMusicChannelReleaseInfoReq) Reset()         { *m = SetMusicChannelReleaseInfoReq{} }
func (m *SetMusicChannelReleaseInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetMusicChannelReleaseInfoReq) ProtoMessage()    {}
func (*SetMusicChannelReleaseInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{55}
}
func (m *SetMusicChannelReleaseInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMusicChannelReleaseInfoReq.Unmarshal(m, b)
}
func (m *SetMusicChannelReleaseInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMusicChannelReleaseInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetMusicChannelReleaseInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMusicChannelReleaseInfoReq.Merge(dst, src)
}
func (m *SetMusicChannelReleaseInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetMusicChannelReleaseInfoReq.Size(m)
}
func (m *SetMusicChannelReleaseInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMusicChannelReleaseInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMusicChannelReleaseInfoReq proto.InternalMessageInfo

func (m *SetMusicChannelReleaseInfoReq) GetMusicChannelReleaseInfo() *MusicChannelReleaseInfo {
	if m != nil {
		return m.MusicChannelReleaseInfo
	}
	return nil
}

func (m *SetMusicChannelReleaseInfoReq) GetWantFresh() bool {
	if m != nil {
		return m.WantFresh
	}
	return false
}

func (m *SetMusicChannelReleaseInfoReq) GetReleaseIp() string {
	if m != nil {
		return m.ReleaseIp
	}
	return ""
}

func (m *SetMusicChannelReleaseInfoReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *SetMusicChannelReleaseInfoReq) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *SetMusicChannelReleaseInfoReq) GetPublishType() string {
	if m != nil {
		return m.PublishType
	}
	return ""
}

func (m *SetMusicChannelReleaseInfoReq) GetAllSelectedBids() []uint32 {
	if m != nil {
		return m.AllSelectedBids
	}
	return nil
}

func (m *SetMusicChannelReleaseInfoReq) GetUnSelectBlockId() []uint32 {
	if m != nil {
		return m.UnSelectBlockId
	}
	return nil
}

func (m *SetMusicChannelReleaseInfoReq) GetUnSelectBlockOptions() []*BlockOption {
	if m != nil {
		return m.UnSelectBlockOptions
	}
	return nil
}

type SetMusicChannelReleaseInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMusicChannelReleaseInfoResp) Reset()         { *m = SetMusicChannelReleaseInfoResp{} }
func (m *SetMusicChannelReleaseInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetMusicChannelReleaseInfoResp) ProtoMessage()    {}
func (*SetMusicChannelReleaseInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{56}
}
func (m *SetMusicChannelReleaseInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMusicChannelReleaseInfoResp.Unmarshal(m, b)
}
func (m *SetMusicChannelReleaseInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMusicChannelReleaseInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetMusicChannelReleaseInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMusicChannelReleaseInfoResp.Merge(dst, src)
}
func (m *SetMusicChannelReleaseInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetMusicChannelReleaseInfoResp.Size(m)
}
func (m *SetMusicChannelReleaseInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMusicChannelReleaseInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMusicChannelReleaseInfoResp proto.InternalMessageInfo

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type InitMusicChannelReleaseInfoReq struct {
	MusicChannelReleaseInfo *MusicChannelReleaseInfo `protobuf:"bytes,1,opt,name=Music_channel_release_info,json=MusicChannelReleaseInfo,proto3" json:"Music_channel_release_info,omitempty"`
	WantFresh               bool                     `protobuf:"varint,2,opt,name=want_fresh,json=wantFresh,proto3" json:"want_fresh,omitempty"`
	ReleaseIp               string                   `protobuf:"bytes,3,opt,name=release_ip,json=releaseIp,proto3" json:"release_ip,omitempty"`
	ChannelName             string                   `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	Creator                 uint32                   `protobuf:"varint,5,opt,name=creator,proto3" json:"creator,omitempty"`
	PublishType             string                   `protobuf:"bytes,6,opt,name=publish_type,json=publishType,proto3" json:"publish_type,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                 `json:"-"`
	XXX_unrecognized        []byte                   `json:"-"`
	XXX_sizecache           int32                    `json:"-"`
}

func (m *InitMusicChannelReleaseInfoReq) Reset()         { *m = InitMusicChannelReleaseInfoReq{} }
func (m *InitMusicChannelReleaseInfoReq) String() string { return proto.CompactTextString(m) }
func (*InitMusicChannelReleaseInfoReq) ProtoMessage()    {}
func (*InitMusicChannelReleaseInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{57}
}
func (m *InitMusicChannelReleaseInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitMusicChannelReleaseInfoReq.Unmarshal(m, b)
}
func (m *InitMusicChannelReleaseInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitMusicChannelReleaseInfoReq.Marshal(b, m, deterministic)
}
func (dst *InitMusicChannelReleaseInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitMusicChannelReleaseInfoReq.Merge(dst, src)
}
func (m *InitMusicChannelReleaseInfoReq) XXX_Size() int {
	return xxx_messageInfo_InitMusicChannelReleaseInfoReq.Size(m)
}
func (m *InitMusicChannelReleaseInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InitMusicChannelReleaseInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_InitMusicChannelReleaseInfoReq proto.InternalMessageInfo

func (m *InitMusicChannelReleaseInfoReq) GetMusicChannelReleaseInfo() *MusicChannelReleaseInfo {
	if m != nil {
		return m.MusicChannelReleaseInfo
	}
	return nil
}

func (m *InitMusicChannelReleaseInfoReq) GetWantFresh() bool {
	if m != nil {
		return m.WantFresh
	}
	return false
}

func (m *InitMusicChannelReleaseInfoReq) GetReleaseIp() string {
	if m != nil {
		return m.ReleaseIp
	}
	return ""
}

func (m *InitMusicChannelReleaseInfoReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *InitMusicChannelReleaseInfoReq) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *InitMusicChannelReleaseInfoReq) GetPublishType() string {
	if m != nil {
		return m.PublishType
	}
	return ""
}

type InitMusicChannelReleaseInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InitMusicChannelReleaseInfoResp) Reset()         { *m = InitMusicChannelReleaseInfoResp{} }
func (m *InitMusicChannelReleaseInfoResp) String() string { return proto.CompactTextString(m) }
func (*InitMusicChannelReleaseInfoResp) ProtoMessage()    {}
func (*InitMusicChannelReleaseInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{58}
}
func (m *InitMusicChannelReleaseInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitMusicChannelReleaseInfoResp.Unmarshal(m, b)
}
func (m *InitMusicChannelReleaseInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitMusicChannelReleaseInfoResp.Marshal(b, m, deterministic)
}
func (dst *InitMusicChannelReleaseInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitMusicChannelReleaseInfoResp.Merge(dst, src)
}
func (m *InitMusicChannelReleaseInfoResp) XXX_Size() int {
	return xxx_messageInfo_InitMusicChannelReleaseInfoResp.Size(m)
}
func (m *InitMusicChannelReleaseInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InitMusicChannelReleaseInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_InitMusicChannelReleaseInfoResp proto.InternalMessageInfo

type DismissMusicChannelReq struct {
	ChannelId            uint32      `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Source               string      `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	Type                 DismissType `protobuf:"varint,3,opt,name=type,proto3,enum=music_topic_channel.DismissType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *DismissMusicChannelReq) Reset()         { *m = DismissMusicChannelReq{} }
func (m *DismissMusicChannelReq) String() string { return proto.CompactTextString(m) }
func (*DismissMusicChannelReq) ProtoMessage()    {}
func (*DismissMusicChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{59}
}
func (m *DismissMusicChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissMusicChannelReq.Unmarshal(m, b)
}
func (m *DismissMusicChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissMusicChannelReq.Marshal(b, m, deterministic)
}
func (dst *DismissMusicChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissMusicChannelReq.Merge(dst, src)
}
func (m *DismissMusicChannelReq) XXX_Size() int {
	return xxx_messageInfo_DismissMusicChannelReq.Size(m)
}
func (m *DismissMusicChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissMusicChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DismissMusicChannelReq proto.InternalMessageInfo

func (m *DismissMusicChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DismissMusicChannelReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *DismissMusicChannelReq) GetType() DismissType {
	if m != nil {
		return m.Type
	}
	return DismissType_Unknown
}

type DismissMusicChannelResp struct {
	Dismiss              bool     `protobuf:"varint,1,opt,name=dismiss,proto3" json:"dismiss,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DismissMusicChannelResp) Reset()         { *m = DismissMusicChannelResp{} }
func (m *DismissMusicChannelResp) String() string { return proto.CompactTextString(m) }
func (*DismissMusicChannelResp) ProtoMessage()    {}
func (*DismissMusicChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{60}
}
func (m *DismissMusicChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissMusicChannelResp.Unmarshal(m, b)
}
func (m *DismissMusicChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissMusicChannelResp.Marshal(b, m, deterministic)
}
func (dst *DismissMusicChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissMusicChannelResp.Merge(dst, src)
}
func (m *DismissMusicChannelResp) XXX_Size() int {
	return xxx_messageInfo_DismissMusicChannelResp.Size(m)
}
func (m *DismissMusicChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissMusicChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DismissMusicChannelResp proto.InternalMessageInfo

func (m *DismissMusicChannelResp) GetDismiss() bool {
	if m != nil {
		return m.Dismiss
	}
	return false
}

type GetRecommendChannelListLoadMore struct {
	Num                  uint32   `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendChannelListLoadMore) Reset()         { *m = GetRecommendChannelListLoadMore{} }
func (m *GetRecommendChannelListLoadMore) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelListLoadMore) ProtoMessage()    {}
func (*GetRecommendChannelListLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{61}
}
func (m *GetRecommendChannelListLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelListLoadMore.Unmarshal(m, b)
}
func (m *GetRecommendChannelListLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelListLoadMore.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelListLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelListLoadMore.Merge(dst, src)
}
func (m *GetRecommendChannelListLoadMore) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelListLoadMore.Size(m)
}
func (m *GetRecommendChannelListLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelListLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelListLoadMore proto.InternalMessageInfo

func (m *GetRecommendChannelListLoadMore) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type GetMusicChannelListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TabIdList            []uint32 `protobuf:"varint,3,rep,packed,name=tab_id_list,json=tabIdList,proto3" json:"tab_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicChannelListReq) Reset()         { *m = GetMusicChannelListReq{} }
func (m *GetMusicChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelListReq) ProtoMessage()    {}
func (*GetMusicChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{62}
}
func (m *GetMusicChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelListReq.Unmarshal(m, b)
}
func (m *GetMusicChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelListReq.Merge(dst, src)
}
func (m *GetMusicChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelListReq.Size(m)
}
func (m *GetMusicChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelListReq proto.InternalMessageInfo

func (m *GetMusicChannelListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMusicChannelListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMusicChannelListReq) GetTabIdList() []uint32 {
	if m != nil {
		return m.TabIdList
	}
	return nil
}

type GetMusicChannelListResp struct {
	ChannelList          []*MusicChannelReleaseInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetMusicChannelListResp) Reset()         { *m = GetMusicChannelListResp{} }
func (m *GetMusicChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelListResp) ProtoMessage()    {}
func (*GetMusicChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{63}
}
func (m *GetMusicChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelListResp.Unmarshal(m, b)
}
func (m *GetMusicChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelListResp.Merge(dst, src)
}
func (m *GetMusicChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelListResp.Size(m)
}
func (m *GetMusicChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelListResp proto.InternalMessageInfo

func (m *GetMusicChannelListResp) GetChannelList() []*MusicChannelReleaseInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type GetMusicChannelByIdsReq struct {
	Ids                  []uint32             `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	Types                []ChannelDisplayType `protobuf:"varint,2,rep,packed,name=types,proto3,enum=music_topic_channel.ChannelDisplayType" json:"types,omitempty"`
	ReturnAll            bool                 `protobuf:"varint,3,opt,name=return_all,json=returnAll,proto3" json:"return_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetMusicChannelByIdsReq) Reset()         { *m = GetMusicChannelByIdsReq{} }
func (m *GetMusicChannelByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelByIdsReq) ProtoMessage()    {}
func (*GetMusicChannelByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{64}
}
func (m *GetMusicChannelByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelByIdsReq.Unmarshal(m, b)
}
func (m *GetMusicChannelByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelByIdsReq.Merge(dst, src)
}
func (m *GetMusicChannelByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelByIdsReq.Size(m)
}
func (m *GetMusicChannelByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelByIdsReq proto.InternalMessageInfo

func (m *GetMusicChannelByIdsReq) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *GetMusicChannelByIdsReq) GetTypes() []ChannelDisplayType {
	if m != nil {
		return m.Types
	}
	return nil
}

func (m *GetMusicChannelByIdsReq) GetReturnAll() bool {
	if m != nil {
		return m.ReturnAll
	}
	return false
}

type GetMusicChannelByIdsResp struct {
	Info                 []*MusicChannelReleaseInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetMusicChannelByIdsResp) Reset()         { *m = GetMusicChannelByIdsResp{} }
func (m *GetMusicChannelByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelByIdsResp) ProtoMessage()    {}
func (*GetMusicChannelByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{65}
}
func (m *GetMusicChannelByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelByIdsResp.Unmarshal(m, b)
}
func (m *GetMusicChannelByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelByIdsResp.Merge(dst, src)
}
func (m *GetMusicChannelByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelByIdsResp.Size(m)
}
func (m *GetMusicChannelByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelByIdsResp proto.InternalMessageInfo

func (m *GetMusicChannelByIdsResp) GetInfo() []*MusicChannelReleaseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SwitchChannelTabReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AppId                uint32   `protobuf:"varint,4,opt,name=appId,proto3" json:"appId,omitempty"`
	MarketId             uint32   `protobuf:"varint,5,opt,name=marketId,proto3" json:"marketId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChannelTabReq) Reset()         { *m = SwitchChannelTabReq{} }
func (m *SwitchChannelTabReq) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelTabReq) ProtoMessage()    {}
func (*SwitchChannelTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{66}
}
func (m *SwitchChannelTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelTabReq.Unmarshal(m, b)
}
func (m *SwitchChannelTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelTabReq.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelTabReq.Merge(dst, src)
}
func (m *SwitchChannelTabReq) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelTabReq.Size(m)
}
func (m *SwitchChannelTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelTabReq proto.InternalMessageInfo

func (m *SwitchChannelTabReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SwitchChannelTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SwitchChannelTabReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchChannelTabReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SwitchChannelTabReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type SwitchChannelTabResp struct {
	TabName              string   `protobuf:"bytes,1,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	WelcomeTxtList       []string `protobuf:"bytes,2,rep,name=welcome_txt_list,json=welcomeTxtList,proto3" json:"welcome_txt_list,omitempty"`
	MicMod               uint32   `protobuf:"varint,3,opt,name=mic_mod,json=micMod,proto3" json:"mic_mod,omitempty"`
	TabType              uint32   `protobuf:"varint,4,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	TagId                uint32   `protobuf:"varint,5,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChannelTabResp) Reset()         { *m = SwitchChannelTabResp{} }
func (m *SwitchChannelTabResp) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelTabResp) ProtoMessage()    {}
func (*SwitchChannelTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{67}
}
func (m *SwitchChannelTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelTabResp.Unmarshal(m, b)
}
func (m *SwitchChannelTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelTabResp.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelTabResp.Merge(dst, src)
}
func (m *SwitchChannelTabResp) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelTabResp.Size(m)
}
func (m *SwitchChannelTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelTabResp proto.InternalMessageInfo

func (m *SwitchChannelTabResp) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *SwitchChannelTabResp) GetWelcomeTxtList() []string {
	if m != nil {
		return m.WelcomeTxtList
	}
	return nil
}

func (m *SwitchChannelTabResp) GetMicMod() uint32 {
	if m != nil {
		return m.MicMod
	}
	return 0
}

func (m *SwitchChannelTabResp) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

func (m *SwitchChannelTabResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type DisappearChannelReq struct {
	ClientId             string                              `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AcquireDuration      uint64                              `protobuf:"varint,2,opt,name=acquire_duration,json=acquireDuration,proto3" json:"acquire_duration,omitempty"`
	TimeoutEvent         *DisappearChannelReq_Timeout        `protobuf:"bytes,10,opt,name=timeout_event,json=timeoutEvent,proto3" json:"timeout_event,omitempty"`
	KeepaliveEvent       *DisappearChannelReq_Keepalive      `protobuf:"bytes,11,opt,name=keepalive_event,json=keepaliveEvent,proto3" json:"keepalive_event,omitempty"`
	ReleaseTimeoutEvent  *DisappearChannelReq_ReleaseTimeout `protobuf:"bytes,12,opt,name=release_timeout_event,json=releaseTimeoutEvent,proto3" json:"release_timeout_event,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *DisappearChannelReq) Reset()         { *m = DisappearChannelReq{} }
func (m *DisappearChannelReq) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq) ProtoMessage()    {}
func (*DisappearChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{68}
}
func (m *DisappearChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq.Unmarshal(m, b)
}
func (m *DisappearChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq.Merge(dst, src)
}
func (m *DisappearChannelReq) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq.Size(m)
}
func (m *DisappearChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq proto.InternalMessageInfo

func (m *DisappearChannelReq) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

func (m *DisappearChannelReq) GetAcquireDuration() uint64 {
	if m != nil {
		return m.AcquireDuration
	}
	return 0
}

func (m *DisappearChannelReq) GetTimeoutEvent() *DisappearChannelReq_Timeout {
	if m != nil {
		return m.TimeoutEvent
	}
	return nil
}

func (m *DisappearChannelReq) GetKeepaliveEvent() *DisappearChannelReq_Keepalive {
	if m != nil {
		return m.KeepaliveEvent
	}
	return nil
}

func (m *DisappearChannelReq) GetReleaseTimeoutEvent() *DisappearChannelReq_ReleaseTimeout {
	if m != nil {
		return m.ReleaseTimeoutEvent
	}
	return nil
}

type DisappearChannelReq_Timeout struct {
	TimeoutDuration      uint64   `protobuf:"varint,1,opt,name=timeout_duration,json=timeoutDuration,proto3" json:"timeout_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelReq_Timeout) Reset()         { *m = DisappearChannelReq_Timeout{} }
func (m *DisappearChannelReq_Timeout) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq_Timeout) ProtoMessage()    {}
func (*DisappearChannelReq_Timeout) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{68, 0}
}
func (m *DisappearChannelReq_Timeout) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq_Timeout.Unmarshal(m, b)
}
func (m *DisappearChannelReq_Timeout) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq_Timeout.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq_Timeout) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq_Timeout.Merge(dst, src)
}
func (m *DisappearChannelReq_Timeout) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq_Timeout.Size(m)
}
func (m *DisappearChannelReq_Timeout) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq_Timeout.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq_Timeout proto.InternalMessageInfo

func (m *DisappearChannelReq_Timeout) GetTimeoutDuration() uint64 {
	if m != nil {
		return m.TimeoutDuration
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type DisappearChannelReq_Keepalive struct {
	KeepaliveDuration    uint64   `protobuf:"varint,1,opt,name=Keepalive_duration,json=KeepaliveDuration,proto3" json:"Keepalive_duration,omitempty"`
	MemberCount          uint32   `protobuf:"varint,2,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelReq_Keepalive) Reset()         { *m = DisappearChannelReq_Keepalive{} }
func (m *DisappearChannelReq_Keepalive) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq_Keepalive) ProtoMessage()    {}
func (*DisappearChannelReq_Keepalive) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{68, 1}
}
func (m *DisappearChannelReq_Keepalive) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq_Keepalive.Unmarshal(m, b)
}
func (m *DisappearChannelReq_Keepalive) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq_Keepalive.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq_Keepalive) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq_Keepalive.Merge(dst, src)
}
func (m *DisappearChannelReq_Keepalive) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq_Keepalive.Size(m)
}
func (m *DisappearChannelReq_Keepalive) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq_Keepalive.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq_Keepalive proto.InternalMessageInfo

func (m *DisappearChannelReq_Keepalive) GetKeepaliveDuration() uint64 {
	if m != nil {
		return m.KeepaliveDuration
	}
	return 0
}

func (m *DisappearChannelReq_Keepalive) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

type DisappearChannelReq_ReleaseTimeout struct {
	TimeoutDuration      uint64   `protobuf:"varint,1,opt,name=timeout_duration,json=timeoutDuration,proto3" json:"timeout_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelReq_ReleaseTimeout) Reset()         { *m = DisappearChannelReq_ReleaseTimeout{} }
func (m *DisappearChannelReq_ReleaseTimeout) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq_ReleaseTimeout) ProtoMessage()    {}
func (*DisappearChannelReq_ReleaseTimeout) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{68, 2}
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Unmarshal(m, b)
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq_ReleaseTimeout) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Merge(dst, src)
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Size(m)
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq_ReleaseTimeout proto.InternalMessageInfo

func (m *DisappearChannelReq_ReleaseTimeout) GetTimeoutDuration() uint64 {
	if m != nil {
		return m.TimeoutDuration
	}
	return 0
}

type DisappearChannelResp struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelResp) Reset()         { *m = DisappearChannelResp{} }
func (m *DisappearChannelResp) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelResp) ProtoMessage()    {}
func (*DisappearChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{69}
}
func (m *DisappearChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelResp.Unmarshal(m, b)
}
func (m *DisappearChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelResp.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelResp.Merge(dst, src)
}
func (m *DisappearChannelResp) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelResp.Size(m)
}
func (m *DisappearChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelResp proto.InternalMessageInfo

func (m *DisappearChannelResp) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type GetOnlineInfoReq struct {
	OnlineUserCount      uint32   `protobuf:"varint,1,opt,name=online_user_count,json=onlineUserCount,proto3" json:"online_user_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOnlineInfoReq) Reset()         { *m = GetOnlineInfoReq{} }
func (m *GetOnlineInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetOnlineInfoReq) ProtoMessage()    {}
func (*GetOnlineInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{70}
}
func (m *GetOnlineInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnlineInfoReq.Unmarshal(m, b)
}
func (m *GetOnlineInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnlineInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetOnlineInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnlineInfoReq.Merge(dst, src)
}
func (m *GetOnlineInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetOnlineInfoReq.Size(m)
}
func (m *GetOnlineInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnlineInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnlineInfoReq proto.InternalMessageInfo

func (m *GetOnlineInfoReq) GetOnlineUserCount() uint32 {
	if m != nil {
		return m.OnlineUserCount
	}
	return 0
}

type GetOnlineInfoResp struct {
	RoomCount            uint32   `protobuf:"varint,1,opt,name=room_count,json=roomCount,proto3" json:"room_count,omitempty"`
	OnlineUserList       []uint32 `protobuf:"varint,2,rep,packed,name=online_user_list,json=onlineUserList,proto3" json:"online_user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOnlineInfoResp) Reset()         { *m = GetOnlineInfoResp{} }
func (m *GetOnlineInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetOnlineInfoResp) ProtoMessage()    {}
func (*GetOnlineInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{71}
}
func (m *GetOnlineInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnlineInfoResp.Unmarshal(m, b)
}
func (m *GetOnlineInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnlineInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetOnlineInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnlineInfoResp.Merge(dst, src)
}
func (m *GetOnlineInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetOnlineInfoResp.Size(m)
}
func (m *GetOnlineInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnlineInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnlineInfoResp proto.InternalMessageInfo

func (m *GetOnlineInfoResp) GetRoomCount() uint32 {
	if m != nil {
		return m.RoomCount
	}
	return 0
}

func (m *GetOnlineInfoResp) GetOnlineUserList() []uint32 {
	if m != nil {
		return m.OnlineUserList
	}
	return nil
}

type FreezeChannelReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	FreezeTime           int64    `protobuf:"varint,2,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeChannelReq) Reset()         { *m = FreezeChannelReq{} }
func (m *FreezeChannelReq) String() string { return proto.CompactTextString(m) }
func (*FreezeChannelReq) ProtoMessage()    {}
func (*FreezeChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{72}
}
func (m *FreezeChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeChannelReq.Unmarshal(m, b)
}
func (m *FreezeChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeChannelReq.Marshal(b, m, deterministic)
}
func (dst *FreezeChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeChannelReq.Merge(dst, src)
}
func (m *FreezeChannelReq) XXX_Size() int {
	return xxx_messageInfo_FreezeChannelReq.Size(m)
}
func (m *FreezeChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeChannelReq proto.InternalMessageInfo

func (m *FreezeChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *FreezeChannelReq) GetFreezeTime() int64 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

type FreezeChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeChannelResp) Reset()         { *m = FreezeChannelResp{} }
func (m *FreezeChannelResp) String() string { return proto.CompactTextString(m) }
func (*FreezeChannelResp) ProtoMessage()    {}
func (*FreezeChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{73}
}
func (m *FreezeChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeChannelResp.Unmarshal(m, b)
}
func (m *FreezeChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeChannelResp.Marshal(b, m, deterministic)
}
func (dst *FreezeChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeChannelResp.Merge(dst, src)
}
func (m *FreezeChannelResp) XXX_Size() int {
	return xxx_messageInfo_FreezeChannelResp.Size(m)
}
func (m *FreezeChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeChannelResp proto.InternalMessageInfo

type UnfreezeChannelReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeChannelReq) Reset()         { *m = UnfreezeChannelReq{} }
func (m *UnfreezeChannelReq) String() string { return proto.CompactTextString(m) }
func (*UnfreezeChannelReq) ProtoMessage()    {}
func (*UnfreezeChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{74}
}
func (m *UnfreezeChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeChannelReq.Unmarshal(m, b)
}
func (m *UnfreezeChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeChannelReq.Marshal(b, m, deterministic)
}
func (dst *UnfreezeChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeChannelReq.Merge(dst, src)
}
func (m *UnfreezeChannelReq) XXX_Size() int {
	return xxx_messageInfo_UnfreezeChannelReq.Size(m)
}
func (m *UnfreezeChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeChannelReq proto.InternalMessageInfo

func (m *UnfreezeChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type UnfreezeChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeChannelResp) Reset()         { *m = UnfreezeChannelResp{} }
func (m *UnfreezeChannelResp) String() string { return proto.CompactTextString(m) }
func (*UnfreezeChannelResp) ProtoMessage()    {}
func (*UnfreezeChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{75}
}
func (m *UnfreezeChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeChannelResp.Unmarshal(m, b)
}
func (m *UnfreezeChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeChannelResp.Marshal(b, m, deterministic)
}
func (dst *UnfreezeChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeChannelResp.Merge(dst, src)
}
func (m *UnfreezeChannelResp) XXX_Size() int {
	return xxx_messageInfo_UnfreezeChannelResp.Size(m)
}
func (m *UnfreezeChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeChannelResp proto.InternalMessageInfo

type GetChannelFreezeInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelFreezeInfoReq) Reset()         { *m = GetChannelFreezeInfoReq{} }
func (m *GetChannelFreezeInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelFreezeInfoReq) ProtoMessage()    {}
func (*GetChannelFreezeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{76}
}
func (m *GetChannelFreezeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFreezeInfoReq.Unmarshal(m, b)
}
func (m *GetChannelFreezeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFreezeInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelFreezeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFreezeInfoReq.Merge(dst, src)
}
func (m *GetChannelFreezeInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelFreezeInfoReq.Size(m)
}
func (m *GetChannelFreezeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFreezeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFreezeInfoReq proto.InternalMessageInfo

func (m *GetChannelFreezeInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelFreezeInfoResp struct {
	FreezeTime           int64    `protobuf:"varint,1,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelFreezeInfoResp) Reset()         { *m = GetChannelFreezeInfoResp{} }
func (m *GetChannelFreezeInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelFreezeInfoResp) ProtoMessage()    {}
func (*GetChannelFreezeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{77}
}
func (m *GetChannelFreezeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFreezeInfoResp.Unmarshal(m, b)
}
func (m *GetChannelFreezeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFreezeInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelFreezeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFreezeInfoResp.Merge(dst, src)
}
func (m *GetChannelFreezeInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelFreezeInfoResp.Size(m)
}
func (m *GetChannelFreezeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFreezeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFreezeInfoResp proto.InternalMessageInfo

func (m *GetChannelFreezeInfoResp) GetFreezeTime() int64 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

type SetExtraHistoryReq struct {
	ChannelId            uint32      `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	HistoryType          HistoryType `protobuf:"varint,2,opt,name=history_type,json=historyType,proto3,enum=music_topic_channel.HistoryType" json:"history_type,omitempty"`
	ExpireAfter          int64       `protobuf:"varint,3,opt,name=expire_after,json=expireAfter,proto3" json:"expire_after,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SetExtraHistoryReq) Reset()         { *m = SetExtraHistoryReq{} }
func (m *SetExtraHistoryReq) String() string { return proto.CompactTextString(m) }
func (*SetExtraHistoryReq) ProtoMessage()    {}
func (*SetExtraHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{78}
}
func (m *SetExtraHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetExtraHistoryReq.Unmarshal(m, b)
}
func (m *SetExtraHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetExtraHistoryReq.Marshal(b, m, deterministic)
}
func (dst *SetExtraHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetExtraHistoryReq.Merge(dst, src)
}
func (m *SetExtraHistoryReq) XXX_Size() int {
	return xxx_messageInfo_SetExtraHistoryReq.Size(m)
}
func (m *SetExtraHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetExtraHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetExtraHistoryReq proto.InternalMessageInfo

func (m *SetExtraHistoryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetExtraHistoryReq) GetHistoryType() HistoryType {
	if m != nil {
		return m.HistoryType
	}
	return HistoryType_CreateHistory
}

func (m *SetExtraHistoryReq) GetExpireAfter() int64 {
	if m != nil {
		return m.ExpireAfter
	}
	return 0
}

type SetExtraHistoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetExtraHistoryResp) Reset()         { *m = SetExtraHistoryResp{} }
func (m *SetExtraHistoryResp) String() string { return proto.CompactTextString(m) }
func (*SetExtraHistoryResp) ProtoMessage()    {}
func (*SetExtraHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{79}
}
func (m *SetExtraHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetExtraHistoryResp.Unmarshal(m, b)
}
func (m *SetExtraHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetExtraHistoryResp.Marshal(b, m, deterministic)
}
func (dst *SetExtraHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetExtraHistoryResp.Merge(dst, src)
}
func (m *SetExtraHistoryResp) XXX_Size() int {
	return xxx_messageInfo_SetExtraHistoryResp.Size(m)
}
func (m *SetExtraHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetExtraHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetExtraHistoryResp proto.InternalMessageInfo

type GetExtraHistoryReq struct {
	ChannelId            uint32      `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	HistoryType          HistoryType `protobuf:"varint,2,opt,name=history_type,json=historyType,proto3,enum=music_topic_channel.HistoryType" json:"history_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetExtraHistoryReq) Reset()         { *m = GetExtraHistoryReq{} }
func (m *GetExtraHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetExtraHistoryReq) ProtoMessage()    {}
func (*GetExtraHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{80}
}
func (m *GetExtraHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraHistoryReq.Unmarshal(m, b)
}
func (m *GetExtraHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetExtraHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraHistoryReq.Merge(dst, src)
}
func (m *GetExtraHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetExtraHistoryReq.Size(m)
}
func (m *GetExtraHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraHistoryReq proto.InternalMessageInfo

func (m *GetExtraHistoryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetExtraHistoryReq) GetHistoryType() HistoryType {
	if m != nil {
		return m.HistoryType
	}
	return HistoryType_CreateHistory
}

type GetExtraHistoryResp struct {
	Value                string   `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtraHistoryResp) Reset()         { *m = GetExtraHistoryResp{} }
func (m *GetExtraHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetExtraHistoryResp) ProtoMessage()    {}
func (*GetExtraHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{81}
}
func (m *GetExtraHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraHistoryResp.Unmarshal(m, b)
}
func (m *GetExtraHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetExtraHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraHistoryResp.Merge(dst, src)
}
func (m *GetExtraHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetExtraHistoryResp.Size(m)
}
func (m *GetExtraHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraHistoryResp proto.InternalMessageInfo

func (m *GetExtraHistoryResp) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type GetChannelPlayModelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelPlayModelReq) Reset()         { *m = GetChannelPlayModelReq{} }
func (m *GetChannelPlayModelReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelPlayModelReq) ProtoMessage()    {}
func (*GetChannelPlayModelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{82}
}
func (m *GetChannelPlayModelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPlayModelReq.Unmarshal(m, b)
}
func (m *GetChannelPlayModelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPlayModelReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelPlayModelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPlayModelReq.Merge(dst, src)
}
func (m *GetChannelPlayModelReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelPlayModelReq.Size(m)
}
func (m *GetChannelPlayModelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPlayModelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPlayModelReq proto.InternalMessageInfo

func (m *GetChannelPlayModelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelPlayModelResp struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelPlayModelResp) Reset()         { *m = GetChannelPlayModelResp{} }
func (m *GetChannelPlayModelResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelPlayModelResp) ProtoMessage()    {}
func (*GetChannelPlayModelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{83}
}
func (m *GetChannelPlayModelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPlayModelResp.Unmarshal(m, b)
}
func (m *GetChannelPlayModelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPlayModelResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelPlayModelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPlayModelResp.Merge(dst, src)
}
func (m *GetChannelPlayModelResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelPlayModelResp.Size(m)
}
func (m *GetChannelPlayModelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPlayModelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPlayModelResp proto.InternalMessageInfo

func (m *GetChannelPlayModelResp) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetChannelRoomUserNumberReq struct {
	TabId                []uint32 `protobuf:"varint,1,rep,packed,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelRoomUserNumberReq) Reset()         { *m = GetChannelRoomUserNumberReq{} }
func (m *GetChannelRoomUserNumberReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelRoomUserNumberReq) ProtoMessage()    {}
func (*GetChannelRoomUserNumberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{84}
}
func (m *GetChannelRoomUserNumberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRoomUserNumberReq.Unmarshal(m, b)
}
func (m *GetChannelRoomUserNumberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRoomUserNumberReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelRoomUserNumberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRoomUserNumberReq.Merge(dst, src)
}
func (m *GetChannelRoomUserNumberReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelRoomUserNumberReq.Size(m)
}
func (m *GetChannelRoomUserNumberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRoomUserNumberReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRoomUserNumberReq proto.InternalMessageInfo

func (m *GetChannelRoomUserNumberReq) GetTabId() []uint32 {
	if m != nil {
		return m.TabId
	}
	return nil
}

type GetChannelRoomUserNumberResp struct {
	RoomUserInfo         []*GetChannelRoomUserNumberResp_RoomUserInfo `protobuf:"bytes,1,rep,name=room_user_info,json=roomUserInfo,proto3" json:"room_user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetChannelRoomUserNumberResp) Reset()         { *m = GetChannelRoomUserNumberResp{} }
func (m *GetChannelRoomUserNumberResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelRoomUserNumberResp) ProtoMessage()    {}
func (*GetChannelRoomUserNumberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{85}
}
func (m *GetChannelRoomUserNumberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRoomUserNumberResp.Unmarshal(m, b)
}
func (m *GetChannelRoomUserNumberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRoomUserNumberResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelRoomUserNumberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRoomUserNumberResp.Merge(dst, src)
}
func (m *GetChannelRoomUserNumberResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelRoomUserNumberResp.Size(m)
}
func (m *GetChannelRoomUserNumberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRoomUserNumberResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRoomUserNumberResp proto.InternalMessageInfo

func (m *GetChannelRoomUserNumberResp) GetRoomUserInfo() []*GetChannelRoomUserNumberResp_RoomUserInfo {
	if m != nil {
		return m.RoomUserInfo
	}
	return nil
}

type GetChannelRoomUserNumberResp_RoomUserInfo struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TotalUserNumber      int64    `protobuf:"varint,2,opt,name=total_user_number,json=totalUserNumber,proto3" json:"total_user_number,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelRoomUserNumberResp_RoomUserInfo) Reset() {
	*m = GetChannelRoomUserNumberResp_RoomUserInfo{}
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) String() string { return proto.CompactTextString(m) }
func (*GetChannelRoomUserNumberResp_RoomUserInfo) ProtoMessage()    {}
func (*GetChannelRoomUserNumberResp_RoomUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{85, 0}
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Unmarshal(m, b)
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Marshal(b, m, deterministic)
}
func (dst *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Merge(dst, src)
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Size() int {
	return xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Size(m)
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo proto.InternalMessageInfo

func (m *GetChannelRoomUserNumberResp_RoomUserInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetChannelRoomUserNumberResp_RoomUserInfo) GetTotalUserNumber() int64 {
	if m != nil {
		return m.TotalUserNumber
	}
	return 0
}

type AddTemporaryChannelReq struct {
	Channel              *MusicChannelReleaseInfo `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AddTemporaryChannelReq) Reset()         { *m = AddTemporaryChannelReq{} }
func (m *AddTemporaryChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddTemporaryChannelReq) ProtoMessage()    {}
func (*AddTemporaryChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{86}
}
func (m *AddTemporaryChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTemporaryChannelReq.Unmarshal(m, b)
}
func (m *AddTemporaryChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTemporaryChannelReq.Marshal(b, m, deterministic)
}
func (dst *AddTemporaryChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTemporaryChannelReq.Merge(dst, src)
}
func (m *AddTemporaryChannelReq) XXX_Size() int {
	return xxx_messageInfo_AddTemporaryChannelReq.Size(m)
}
func (m *AddTemporaryChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTemporaryChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTemporaryChannelReq proto.InternalMessageInfo

func (m *AddTemporaryChannelReq) GetChannel() *MusicChannelReleaseInfo {
	if m != nil {
		return m.Channel
	}
	return nil
}

type AddTemporaryChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTemporaryChannelResp) Reset()         { *m = AddTemporaryChannelResp{} }
func (m *AddTemporaryChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddTemporaryChannelResp) ProtoMessage()    {}
func (*AddTemporaryChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{87}
}
func (m *AddTemporaryChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTemporaryChannelResp.Unmarshal(m, b)
}
func (m *AddTemporaryChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTemporaryChannelResp.Marshal(b, m, deterministic)
}
func (dst *AddTemporaryChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTemporaryChannelResp.Merge(dst, src)
}
func (m *AddTemporaryChannelResp) XXX_Size() int {
	return xxx_messageInfo_AddTemporaryChannelResp.Size(m)
}
func (m *AddTemporaryChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTemporaryChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTemporaryChannelResp proto.InternalMessageInfo

// 查询优质房
type SearchHighQualityChannelsReq struct {
	Ttid                 uint32                           `protobuf:"varint,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Status               HighQualityChannelInfoStatusType `protobuf:"varint,2,opt,name=status,proto3,enum=music_topic_channel.HighQualityChannelInfoStatusType" json:"status,omitempty"`
	Page                 uint32                           `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Limit                uint32                           `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	TabId                uint32                           `protobuf:"varint,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	QualityType          int32                            `protobuf:"varint,6,opt,name=quality_type,json=qualityType,proto3" json:"quality_type,omitempty"`
	ViewId               string                           `protobuf:"bytes,7,opt,name=view_id,json=viewId,proto3" json:"view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *SearchHighQualityChannelsReq) Reset()         { *m = SearchHighQualityChannelsReq{} }
func (m *SearchHighQualityChannelsReq) String() string { return proto.CompactTextString(m) }
func (*SearchHighQualityChannelsReq) ProtoMessage()    {}
func (*SearchHighQualityChannelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{88}
}
func (m *SearchHighQualityChannelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchHighQualityChannelsReq.Unmarshal(m, b)
}
func (m *SearchHighQualityChannelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchHighQualityChannelsReq.Marshal(b, m, deterministic)
}
func (dst *SearchHighQualityChannelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchHighQualityChannelsReq.Merge(dst, src)
}
func (m *SearchHighQualityChannelsReq) XXX_Size() int {
	return xxx_messageInfo_SearchHighQualityChannelsReq.Size(m)
}
func (m *SearchHighQualityChannelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchHighQualityChannelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchHighQualityChannelsReq proto.InternalMessageInfo

func (m *SearchHighQualityChannelsReq) GetTtid() uint32 {
	if m != nil {
		return m.Ttid
	}
	return 0
}

func (m *SearchHighQualityChannelsReq) GetStatus() HighQualityChannelInfoStatusType {
	if m != nil {
		return m.Status
	}
	return HighQualityChannelInfoStatusType_UNKNOWN
}

func (m *SearchHighQualityChannelsReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *SearchHighQualityChannelsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchHighQualityChannelsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SearchHighQualityChannelsReq) GetQualityType() int32 {
	if m != nil {
		return m.QualityType
	}
	return 0
}

func (m *SearchHighQualityChannelsReq) GetViewId() string {
	if m != nil {
		return m.ViewId
	}
	return ""
}

type SearchHighQualityChannelsResp struct {
	InfoList             []*HighQualityChannelInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Count                uint32                    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SearchHighQualityChannelsResp) Reset()         { *m = SearchHighQualityChannelsResp{} }
func (m *SearchHighQualityChannelsResp) String() string { return proto.CompactTextString(m) }
func (*SearchHighQualityChannelsResp) ProtoMessage()    {}
func (*SearchHighQualityChannelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{89}
}
func (m *SearchHighQualityChannelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchHighQualityChannelsResp.Unmarshal(m, b)
}
func (m *SearchHighQualityChannelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchHighQualityChannelsResp.Marshal(b, m, deterministic)
}
func (dst *SearchHighQualityChannelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchHighQualityChannelsResp.Merge(dst, src)
}
func (m *SearchHighQualityChannelsResp) XXX_Size() int {
	return xxx_messageInfo_SearchHighQualityChannelsResp.Size(m)
}
func (m *SearchHighQualityChannelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchHighQualityChannelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchHighQualityChannelsResp proto.InternalMessageInfo

func (m *SearchHighQualityChannelsResp) GetInfoList() []*HighQualityChannelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *SearchHighQualityChannelsResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 更新优质房
type UpdateHighQualityChannelsReq struct {
	Info                 []*HighQualityChannelInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpdateHighQualityChannelsReq) Reset()         { *m = UpdateHighQualityChannelsReq{} }
func (m *UpdateHighQualityChannelsReq) String() string { return proto.CompactTextString(m) }
func (*UpdateHighQualityChannelsReq) ProtoMessage()    {}
func (*UpdateHighQualityChannelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{90}
}
func (m *UpdateHighQualityChannelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateHighQualityChannelsReq.Unmarshal(m, b)
}
func (m *UpdateHighQualityChannelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateHighQualityChannelsReq.Marshal(b, m, deterministic)
}
func (dst *UpdateHighQualityChannelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateHighQualityChannelsReq.Merge(dst, src)
}
func (m *UpdateHighQualityChannelsReq) XXX_Size() int {
	return xxx_messageInfo_UpdateHighQualityChannelsReq.Size(m)
}
func (m *UpdateHighQualityChannelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateHighQualityChannelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateHighQualityChannelsReq proto.InternalMessageInfo

func (m *UpdateHighQualityChannelsReq) GetInfo() []*HighQualityChannelInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateHighQualityChannelsResp struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateHighQualityChannelsResp) Reset()         { *m = UpdateHighQualityChannelsResp{} }
func (m *UpdateHighQualityChannelsResp) String() string { return proto.CompactTextString(m) }
func (*UpdateHighQualityChannelsResp) ProtoMessage()    {}
func (*UpdateHighQualityChannelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{91}
}
func (m *UpdateHighQualityChannelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateHighQualityChannelsResp.Unmarshal(m, b)
}
func (m *UpdateHighQualityChannelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateHighQualityChannelsResp.Marshal(b, m, deterministic)
}
func (dst *UpdateHighQualityChannelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateHighQualityChannelsResp.Merge(dst, src)
}
func (m *UpdateHighQualityChannelsResp) XXX_Size() int {
	return xxx_messageInfo_UpdateHighQualityChannelsResp.Size(m)
}
func (m *UpdateHighQualityChannelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateHighQualityChannelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateHighQualityChannelsResp proto.InternalMessageInfo

func (m *UpdateHighQualityChannelsResp) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type HighQualityChannelInfo struct {
	Uid                  uint32                           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 uint32                           `protobuf:"varint,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Name                 string                           `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	DisplayId            uint32                           `protobuf:"varint,4,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	ChannelId            uint32                           `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeginTime            int64                            `protobuf:"varint,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64                            `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	DurationTimeHour     uint32                           `protobuf:"varint,8,opt,name=duration_time_hour,json=durationTimeHour,proto3" json:"duration_time_hour,omitempty"`
	Status               HighQualityChannelInfoStatusType `protobuf:"varint,9,opt,name=status,proto3,enum=music_topic_channel.HighQualityChannelInfoStatusType" json:"status,omitempty"`
	TabId                uint32                           `protobuf:"varint,10,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Id                   string                           `protobuf:"bytes,11,opt,name=id,proto3" json:"id,omitempty"`
	QualityType          QualityType                      `protobuf:"varint,12,opt,name=quality_type,json=qualityType,proto3,enum=music_topic_channel.QualityType" json:"quality_type,omitempty"`
	ViewId               string                           `protobuf:"bytes,13,opt,name=view_id,json=viewId,proto3" json:"view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *HighQualityChannelInfo) Reset()         { *m = HighQualityChannelInfo{} }
func (m *HighQualityChannelInfo) String() string { return proto.CompactTextString(m) }
func (*HighQualityChannelInfo) ProtoMessage()    {}
func (*HighQualityChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{92}
}
func (m *HighQualityChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HighQualityChannelInfo.Unmarshal(m, b)
}
func (m *HighQualityChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HighQualityChannelInfo.Marshal(b, m, deterministic)
}
func (dst *HighQualityChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HighQualityChannelInfo.Merge(dst, src)
}
func (m *HighQualityChannelInfo) XXX_Size() int {
	return xxx_messageInfo_HighQualityChannelInfo.Size(m)
}
func (m *HighQualityChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HighQualityChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HighQualityChannelInfo proto.InternalMessageInfo

func (m *HighQualityChannelInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HighQualityChannelInfo) GetTtid() uint32 {
	if m != nil {
		return m.Ttid
	}
	return 0
}

func (m *HighQualityChannelInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *HighQualityChannelInfo) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *HighQualityChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HighQualityChannelInfo) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *HighQualityChannelInfo) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *HighQualityChannelInfo) GetDurationTimeHour() uint32 {
	if m != nil {
		return m.DurationTimeHour
	}
	return 0
}

func (m *HighQualityChannelInfo) GetStatus() HighQualityChannelInfoStatusType {
	if m != nil {
		return m.Status
	}
	return HighQualityChannelInfoStatusType_UNKNOWN
}

func (m *HighQualityChannelInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *HighQualityChannelInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *HighQualityChannelInfo) GetQualityType() QualityType {
	if m != nil {
		return m.QualityType
	}
	return QualityType_High_Quality
}

func (m *HighQualityChannelInfo) GetViewId() string {
	if m != nil {
		return m.ViewId
	}
	return ""
}

type BatchIsHighQualityChannelsReq struct {
	TabChannelMap        map[uint32]uint32 `protobuf:"bytes,1,rep,name=tab_channel_map,json=tabChannelMap,proto3" json:"tab_channel_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchIsHighQualityChannelsReq) Reset()         { *m = BatchIsHighQualityChannelsReq{} }
func (m *BatchIsHighQualityChannelsReq) String() string { return proto.CompactTextString(m) }
func (*BatchIsHighQualityChannelsReq) ProtoMessage()    {}
func (*BatchIsHighQualityChannelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{93}
}
func (m *BatchIsHighQualityChannelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchIsHighQualityChannelsReq.Unmarshal(m, b)
}
func (m *BatchIsHighQualityChannelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchIsHighQualityChannelsReq.Marshal(b, m, deterministic)
}
func (dst *BatchIsHighQualityChannelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchIsHighQualityChannelsReq.Merge(dst, src)
}
func (m *BatchIsHighQualityChannelsReq) XXX_Size() int {
	return xxx_messageInfo_BatchIsHighQualityChannelsReq.Size(m)
}
func (m *BatchIsHighQualityChannelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchIsHighQualityChannelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchIsHighQualityChannelsReq proto.InternalMessageInfo

func (m *BatchIsHighQualityChannelsReq) GetTabChannelMap() map[uint32]uint32 {
	if m != nil {
		return m.TabChannelMap
	}
	return nil
}

type BatchIsHighQualityChannelsResp struct {
	ChannelHighQualityMap map[uint32]bool `protobuf:"bytes,1,rep,name=channel_high_quality_map,json=channelHighQualityMap,proto3" json:"channel_high_quality_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral  struct{}        `json:"-"`
	XXX_unrecognized      []byte          `json:"-"`
	XXX_sizecache         int32           `json:"-"`
}

func (m *BatchIsHighQualityChannelsResp) Reset()         { *m = BatchIsHighQualityChannelsResp{} }
func (m *BatchIsHighQualityChannelsResp) String() string { return proto.CompactTextString(m) }
func (*BatchIsHighQualityChannelsResp) ProtoMessage()    {}
func (*BatchIsHighQualityChannelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{94}
}
func (m *BatchIsHighQualityChannelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchIsHighQualityChannelsResp.Unmarshal(m, b)
}
func (m *BatchIsHighQualityChannelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchIsHighQualityChannelsResp.Marshal(b, m, deterministic)
}
func (dst *BatchIsHighQualityChannelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchIsHighQualityChannelsResp.Merge(dst, src)
}
func (m *BatchIsHighQualityChannelsResp) XXX_Size() int {
	return xxx_messageInfo_BatchIsHighQualityChannelsResp.Size(m)
}
func (m *BatchIsHighQualityChannelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchIsHighQualityChannelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchIsHighQualityChannelsResp proto.InternalMessageInfo

func (m *BatchIsHighQualityChannelsResp) GetChannelHighQualityMap() map[uint32]bool {
	if m != nil {
		return m.ChannelHighQualityMap
	}
	return nil
}

type BatchSetResourceConfigReq struct {
	Info                 []*ResourceConfigInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchSetResourceConfigReq) Reset()         { *m = BatchSetResourceConfigReq{} }
func (m *BatchSetResourceConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetResourceConfigReq) ProtoMessage()    {}
func (*BatchSetResourceConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{95}
}
func (m *BatchSetResourceConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetResourceConfigReq.Unmarshal(m, b)
}
func (m *BatchSetResourceConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetResourceConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetResourceConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetResourceConfigReq.Merge(dst, src)
}
func (m *BatchSetResourceConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetResourceConfigReq.Size(m)
}
func (m *BatchSetResourceConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetResourceConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetResourceConfigReq proto.InternalMessageInfo

func (m *BatchSetResourceConfigReq) GetInfo() []*ResourceConfigInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchSetResourceConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetResourceConfigResp) Reset()         { *m = BatchSetResourceConfigResp{} }
func (m *BatchSetResourceConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetResourceConfigResp) ProtoMessage()    {}
func (*BatchSetResourceConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{96}
}
func (m *BatchSetResourceConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetResourceConfigResp.Unmarshal(m, b)
}
func (m *BatchSetResourceConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetResourceConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchSetResourceConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetResourceConfigResp.Merge(dst, src)
}
func (m *BatchSetResourceConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchSetResourceConfigResp.Size(m)
}
func (m *BatchSetResourceConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetResourceConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetResourceConfigResp proto.InternalMessageInfo

type GetResourceConfigsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetResourceConfigsReq) Reset()         { *m = GetResourceConfigsReq{} }
func (m *GetResourceConfigsReq) String() string { return proto.CompactTextString(m) }
func (*GetResourceConfigsReq) ProtoMessage()    {}
func (*GetResourceConfigsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{97}
}
func (m *GetResourceConfigsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResourceConfigsReq.Unmarshal(m, b)
}
func (m *GetResourceConfigsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResourceConfigsReq.Marshal(b, m, deterministic)
}
func (dst *GetResourceConfigsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourceConfigsReq.Merge(dst, src)
}
func (m *GetResourceConfigsReq) XXX_Size() int {
	return xxx_messageInfo_GetResourceConfigsReq.Size(m)
}
func (m *GetResourceConfigsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourceConfigsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourceConfigsReq proto.InternalMessageInfo

func (m *GetResourceConfigsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetResourceConfigsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetResourceConfigsReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GetResourceConfigsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetResourceConfigsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetResourceConfigsResp struct {
	Info                 []*ResourceConfigInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Total                uint32                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetResourceConfigsResp) Reset()         { *m = GetResourceConfigsResp{} }
func (m *GetResourceConfigsResp) String() string { return proto.CompactTextString(m) }
func (*GetResourceConfigsResp) ProtoMessage()    {}
func (*GetResourceConfigsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{98}
}
func (m *GetResourceConfigsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResourceConfigsResp.Unmarshal(m, b)
}
func (m *GetResourceConfigsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResourceConfigsResp.Marshal(b, m, deterministic)
}
func (dst *GetResourceConfigsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourceConfigsResp.Merge(dst, src)
}
func (m *GetResourceConfigsResp) XXX_Size() int {
	return xxx_messageInfo_GetResourceConfigsResp.Size(m)
}
func (m *GetResourceConfigsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourceConfigsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourceConfigsResp proto.InternalMessageInfo

func (m *GetResourceConfigsResp) GetInfo() []*ResourceConfigInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetResourceConfigsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type ResourceConfigInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	JumpUrl              string   `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	Icon                 string   `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon,omitempty"`
	Text                 string   `protobuf:"bytes,6,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResourceConfigInfo) Reset()         { *m = ResourceConfigInfo{} }
func (m *ResourceConfigInfo) String() string { return proto.CompactTextString(m) }
func (*ResourceConfigInfo) ProtoMessage()    {}
func (*ResourceConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{99}
}
func (m *ResourceConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResourceConfigInfo.Unmarshal(m, b)
}
func (m *ResourceConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResourceConfigInfo.Marshal(b, m, deterministic)
}
func (dst *ResourceConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResourceConfigInfo.Merge(dst, src)
}
func (m *ResourceConfigInfo) XXX_Size() int {
	return xxx_messageInfo_ResourceConfigInfo.Size(m)
}
func (m *ResourceConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ResourceConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ResourceConfigInfo proto.InternalMessageInfo

func (m *ResourceConfigInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ResourceConfigInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ResourceConfigInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ResourceConfigInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *ResourceConfigInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ResourceConfigInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type BatchDelResourceConfigReq struct {
	Id                   []string `protobuf:"bytes,1,rep,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelResourceConfigReq) Reset()         { *m = BatchDelResourceConfigReq{} }
func (m *BatchDelResourceConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelResourceConfigReq) ProtoMessage()    {}
func (*BatchDelResourceConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{100}
}
func (m *BatchDelResourceConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelResourceConfigReq.Unmarshal(m, b)
}
func (m *BatchDelResourceConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelResourceConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelResourceConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelResourceConfigReq.Merge(dst, src)
}
func (m *BatchDelResourceConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelResourceConfigReq.Size(m)
}
func (m *BatchDelResourceConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelResourceConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelResourceConfigReq proto.InternalMessageInfo

func (m *BatchDelResourceConfigReq) GetId() []string {
	if m != nil {
		return m.Id
	}
	return nil
}

type BatchDelResourceConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelResourceConfigResp) Reset()         { *m = BatchDelResourceConfigResp{} }
func (m *BatchDelResourceConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelResourceConfigResp) ProtoMessage()    {}
func (*BatchDelResourceConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{101}
}
func (m *BatchDelResourceConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelResourceConfigResp.Unmarshal(m, b)
}
func (m *BatchDelResourceConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelResourceConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelResourceConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelResourceConfigResp.Merge(dst, src)
}
func (m *BatchDelResourceConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelResourceConfigResp.Size(m)
}
func (m *BatchDelResourceConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelResourceConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelResourceConfigResp proto.InternalMessageInfo

type ListMuseSocialCommunityChannelsReq struct {
	SocialCommunityId    string   `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelIds           []uint32 `protobuf:"varint,3,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListMuseSocialCommunityChannelsReq) Reset()         { *m = ListMuseSocialCommunityChannelsReq{} }
func (m *ListMuseSocialCommunityChannelsReq) String() string { return proto.CompactTextString(m) }
func (*ListMuseSocialCommunityChannelsReq) ProtoMessage()    {}
func (*ListMuseSocialCommunityChannelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{102}
}
func (m *ListMuseSocialCommunityChannelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsReq.Unmarshal(m, b)
}
func (m *ListMuseSocialCommunityChannelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsReq.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialCommunityChannelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialCommunityChannelsReq.Merge(dst, src)
}
func (m *ListMuseSocialCommunityChannelsReq) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsReq.Size(m)
}
func (m *ListMuseSocialCommunityChannelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialCommunityChannelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialCommunityChannelsReq proto.InternalMessageInfo

func (m *ListMuseSocialCommunityChannelsReq) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *ListMuseSocialCommunityChannelsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListMuseSocialCommunityChannelsReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type ListMuseSocialCommunityChannelsResp struct {
	ChannelViews         map[uint32]*music_topic_channel.MuseSocialCommunityChannelInfo `protobuf:"bytes,1,rep,name=channel_views,json=channelViews,proto3" json:"channel_views,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                       `json:"-"`
	XXX_unrecognized     []byte                                                         `json:"-"`
	XXX_sizecache        int32                                                          `json:"-"`
}

func (m *ListMuseSocialCommunityChannelsResp) Reset()         { *m = ListMuseSocialCommunityChannelsResp{} }
func (m *ListMuseSocialCommunityChannelsResp) String() string { return proto.CompactTextString(m) }
func (*ListMuseSocialCommunityChannelsResp) ProtoMessage()    {}
func (*ListMuseSocialCommunityChannelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{103}
}
func (m *ListMuseSocialCommunityChannelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsResp.Unmarshal(m, b)
}
func (m *ListMuseSocialCommunityChannelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsResp.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialCommunityChannelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialCommunityChannelsResp.Merge(dst, src)
}
func (m *ListMuseSocialCommunityChannelsResp) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsResp.Size(m)
}
func (m *ListMuseSocialCommunityChannelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialCommunityChannelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialCommunityChannelsResp proto.InternalMessageInfo

func (m *ListMuseSocialCommunityChannelsResp) GetChannelViews() map[uint32]*music_topic_channel.MuseSocialCommunityChannelInfo {
	if m != nil {
		return m.ChannelViews
	}
	return nil
}

type GetFilterInfoByFilterIdsReq struct {
	FilterIds            []string `protobuf:"bytes,1,rep,name=filter_ids,json=filterIds,proto3" json:"filter_ids,omitempty"`
	NeedCheck            bool     `protobuf:"varint,2,opt,name=need_check,json=needCheck,proto3" json:"need_check,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterInfoByFilterIdsReq) Reset()         { *m = GetFilterInfoByFilterIdsReq{} }
func (m *GetFilterInfoByFilterIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetFilterInfoByFilterIdsReq) ProtoMessage()    {}
func (*GetFilterInfoByFilterIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{104}
}
func (m *GetFilterInfoByFilterIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterInfoByFilterIdsReq.Unmarshal(m, b)
}
func (m *GetFilterInfoByFilterIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterInfoByFilterIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetFilterInfoByFilterIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterInfoByFilterIdsReq.Merge(dst, src)
}
func (m *GetFilterInfoByFilterIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetFilterInfoByFilterIdsReq.Size(m)
}
func (m *GetFilterInfoByFilterIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterInfoByFilterIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterInfoByFilterIdsReq proto.InternalMessageInfo

func (m *GetFilterInfoByFilterIdsReq) GetFilterIds() []string {
	if m != nil {
		return m.FilterIds
	}
	return nil
}

func (m *GetFilterInfoByFilterIdsReq) GetNeedCheck() bool {
	if m != nil {
		return m.NeedCheck
	}
	return false
}

type MusicFilterItemV2 struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	TabIds               []uint32 `protobuf:"varint,2,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicFilterItemV2) Reset()         { *m = MusicFilterItemV2{} }
func (m *MusicFilterItemV2) String() string { return proto.CompactTextString(m) }
func (*MusicFilterItemV2) ProtoMessage()    {}
func (*MusicFilterItemV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{105}
}
func (m *MusicFilterItemV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicFilterItemV2.Unmarshal(m, b)
}
func (m *MusicFilterItemV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicFilterItemV2.Marshal(b, m, deterministic)
}
func (dst *MusicFilterItemV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicFilterItemV2.Merge(dst, src)
}
func (m *MusicFilterItemV2) XXX_Size() int {
	return xxx_messageInfo_MusicFilterItemV2.Size(m)
}
func (m *MusicFilterItemV2) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicFilterItemV2.DiscardUnknown(m)
}

var xxx_messageInfo_MusicFilterItemV2 proto.InternalMessageInfo

func (m *MusicFilterItemV2) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MusicFilterItemV2) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type GetFilterInfoByFilterIdsResp struct {
	FilterMap            map[string]*MusicFilterItemV2 `protobuf:"bytes,1,rep,name=filter_map,json=filterMap,proto3" json:"filter_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetFilterInfoByFilterIdsResp) Reset()         { *m = GetFilterInfoByFilterIdsResp{} }
func (m *GetFilterInfoByFilterIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetFilterInfoByFilterIdsResp) ProtoMessage()    {}
func (*GetFilterInfoByFilterIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{106}
}
func (m *GetFilterInfoByFilterIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterInfoByFilterIdsResp.Unmarshal(m, b)
}
func (m *GetFilterInfoByFilterIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterInfoByFilterIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetFilterInfoByFilterIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterInfoByFilterIdsResp.Merge(dst, src)
}
func (m *GetFilterInfoByFilterIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetFilterInfoByFilterIdsResp.Size(m)
}
func (m *GetFilterInfoByFilterIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterInfoByFilterIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterInfoByFilterIdsResp proto.InternalMessageInfo

func (m *GetFilterInfoByFilterIdsResp) GetFilterMap() map[string]*MusicFilterItemV2 {
	if m != nil {
		return m.FilterMap
	}
	return nil
}

type GetFilterInsertChannelIdsReq struct {
	FilterIds            []string `protobuf:"bytes,1,rep,name=filter_ids,json=filterIds,proto3" json:"filter_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterInsertChannelIdsReq) Reset()         { *m = GetFilterInsertChannelIdsReq{} }
func (m *GetFilterInsertChannelIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetFilterInsertChannelIdsReq) ProtoMessage()    {}
func (*GetFilterInsertChannelIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{107}
}
func (m *GetFilterInsertChannelIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterInsertChannelIdsReq.Unmarshal(m, b)
}
func (m *GetFilterInsertChannelIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterInsertChannelIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetFilterInsertChannelIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterInsertChannelIdsReq.Merge(dst, src)
}
func (m *GetFilterInsertChannelIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetFilterInsertChannelIdsReq.Size(m)
}
func (m *GetFilterInsertChannelIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterInsertChannelIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterInsertChannelIdsReq proto.InternalMessageInfo

func (m *GetFilterInsertChannelIdsReq) GetFilterIds() []string {
	if m != nil {
		return m.FilterIds
	}
	return nil
}

type GetFilterInsertChannelIdsResp struct {
	FilterMap            map[string]*GetFilterInsertChannelIdsResp_InsertChannelIds `protobuf:"bytes,1,rep,name=filter_map,json=filterMap,proto3" json:"filter_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                   `json:"-"`
	XXX_unrecognized     []byte                                                     `json:"-"`
	XXX_sizecache        int32                                                      `json:"-"`
}

func (m *GetFilterInsertChannelIdsResp) Reset()         { *m = GetFilterInsertChannelIdsResp{} }
func (m *GetFilterInsertChannelIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetFilterInsertChannelIdsResp) ProtoMessage()    {}
func (*GetFilterInsertChannelIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{108}
}
func (m *GetFilterInsertChannelIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterInsertChannelIdsResp.Unmarshal(m, b)
}
func (m *GetFilterInsertChannelIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterInsertChannelIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetFilterInsertChannelIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterInsertChannelIdsResp.Merge(dst, src)
}
func (m *GetFilterInsertChannelIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetFilterInsertChannelIdsResp.Size(m)
}
func (m *GetFilterInsertChannelIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterInsertChannelIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterInsertChannelIdsResp proto.InternalMessageInfo

func (m *GetFilterInsertChannelIdsResp) GetFilterMap() map[string]*GetFilterInsertChannelIdsResp_InsertChannelIds {
	if m != nil {
		return m.FilterMap
	}
	return nil
}

type GetFilterInsertChannelIdsResp_InsertChannelIds struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterInsertChannelIdsResp_InsertChannelIds) Reset() {
	*m = GetFilterInsertChannelIdsResp_InsertChannelIds{}
}
func (m *GetFilterInsertChannelIdsResp_InsertChannelIds) String() string {
	return proto.CompactTextString(m)
}
func (*GetFilterInsertChannelIdsResp_InsertChannelIds) ProtoMessage() {}
func (*GetFilterInsertChannelIdsResp_InsertChannelIds) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{108, 0}
}
func (m *GetFilterInsertChannelIdsResp_InsertChannelIds) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterInsertChannelIdsResp_InsertChannelIds.Unmarshal(m, b)
}
func (m *GetFilterInsertChannelIdsResp_InsertChannelIds) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterInsertChannelIdsResp_InsertChannelIds.Marshal(b, m, deterministic)
}
func (dst *GetFilterInsertChannelIdsResp_InsertChannelIds) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterInsertChannelIdsResp_InsertChannelIds.Merge(dst, src)
}
func (m *GetFilterInsertChannelIdsResp_InsertChannelIds) XXX_Size() int {
	return xxx_messageInfo_GetFilterInsertChannelIdsResp_InsertChannelIds.Size(m)
}
func (m *GetFilterInsertChannelIdsResp_InsertChannelIds) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterInsertChannelIdsResp_InsertChannelIds.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterInsertChannelIdsResp_InsertChannelIds proto.InternalMessageInfo

func (m *GetFilterInsertChannelIdsResp_InsertChannelIds) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type FilterIdToConfTabReq struct {
	FilterIds            []string `protobuf:"bytes,1,rep,name=filter_ids,json=filterIds,proto3" json:"filter_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FilterIdToConfTabReq) Reset()         { *m = FilterIdToConfTabReq{} }
func (m *FilterIdToConfTabReq) String() string { return proto.CompactTextString(m) }
func (*FilterIdToConfTabReq) ProtoMessage()    {}
func (*FilterIdToConfTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{109}
}
func (m *FilterIdToConfTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterIdToConfTabReq.Unmarshal(m, b)
}
func (m *FilterIdToConfTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterIdToConfTabReq.Marshal(b, m, deterministic)
}
func (dst *FilterIdToConfTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterIdToConfTabReq.Merge(dst, src)
}
func (m *FilterIdToConfTabReq) XXX_Size() int {
	return xxx_messageInfo_FilterIdToConfTabReq.Size(m)
}
func (m *FilterIdToConfTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterIdToConfTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_FilterIdToConfTabReq proto.InternalMessageInfo

func (m *FilterIdToConfTabReq) GetFilterIds() []string {
	if m != nil {
		return m.FilterIds
	}
	return nil
}

type FilterIdToConfTabResp struct {
	ConfTabs             []*FilterIdToConfTabResp_ConfTab `protobuf:"bytes,1,rep,name=conf_tabs,json=confTabs,proto3" json:"conf_tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *FilterIdToConfTabResp) Reset()         { *m = FilterIdToConfTabResp{} }
func (m *FilterIdToConfTabResp) String() string { return proto.CompactTextString(m) }
func (*FilterIdToConfTabResp) ProtoMessage()    {}
func (*FilterIdToConfTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{110}
}
func (m *FilterIdToConfTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterIdToConfTabResp.Unmarshal(m, b)
}
func (m *FilterIdToConfTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterIdToConfTabResp.Marshal(b, m, deterministic)
}
func (dst *FilterIdToConfTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterIdToConfTabResp.Merge(dst, src)
}
func (m *FilterIdToConfTabResp) XXX_Size() int {
	return xxx_messageInfo_FilterIdToConfTabResp.Size(m)
}
func (m *FilterIdToConfTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterIdToConfTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_FilterIdToConfTabResp proto.InternalMessageInfo

func (m *FilterIdToConfTabResp) GetConfTabs() []*FilterIdToConfTabResp_ConfTab {
	if m != nil {
		return m.ConfTabs
	}
	return nil
}

type FilterIdToConfTabResp_Block struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FilterIdToConfTabResp_Block) Reset()         { *m = FilterIdToConfTabResp_Block{} }
func (m *FilterIdToConfTabResp_Block) String() string { return proto.CompactTextString(m) }
func (*FilterIdToConfTabResp_Block) ProtoMessage()    {}
func (*FilterIdToConfTabResp_Block) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{110, 0}
}
func (m *FilterIdToConfTabResp_Block) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterIdToConfTabResp_Block.Unmarshal(m, b)
}
func (m *FilterIdToConfTabResp_Block) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterIdToConfTabResp_Block.Marshal(b, m, deterministic)
}
func (dst *FilterIdToConfTabResp_Block) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterIdToConfTabResp_Block.Merge(dst, src)
}
func (m *FilterIdToConfTabResp_Block) XXX_Size() int {
	return xxx_messageInfo_FilterIdToConfTabResp_Block.Size(m)
}
func (m *FilterIdToConfTabResp_Block) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterIdToConfTabResp_Block.DiscardUnknown(m)
}

var xxx_messageInfo_FilterIdToConfTabResp_Block proto.InternalMessageInfo

func (m *FilterIdToConfTabResp_Block) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *FilterIdToConfTabResp_Block) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

type FilterIdToConfTabResp_ConfTab struct {
	TabId                uint32                         `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Blocks               []*FilterIdToConfTabResp_Block `protobuf:"bytes,2,rep,name=blocks,proto3" json:"blocks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *FilterIdToConfTabResp_ConfTab) Reset()         { *m = FilterIdToConfTabResp_ConfTab{} }
func (m *FilterIdToConfTabResp_ConfTab) String() string { return proto.CompactTextString(m) }
func (*FilterIdToConfTabResp_ConfTab) ProtoMessage()    {}
func (*FilterIdToConfTabResp_ConfTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8, []int{110, 1}
}
func (m *FilterIdToConfTabResp_ConfTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterIdToConfTabResp_ConfTab.Unmarshal(m, b)
}
func (m *FilterIdToConfTabResp_ConfTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterIdToConfTabResp_ConfTab.Marshal(b, m, deterministic)
}
func (dst *FilterIdToConfTabResp_ConfTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterIdToConfTabResp_ConfTab.Merge(dst, src)
}
func (m *FilterIdToConfTabResp_ConfTab) XXX_Size() int {
	return xxx_messageInfo_FilterIdToConfTabResp_ConfTab.Size(m)
}
func (m *FilterIdToConfTabResp_ConfTab) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterIdToConfTabResp_ConfTab.DiscardUnknown(m)
}

var xxx_messageInfo_FilterIdToConfTabResp_ConfTab proto.InternalMessageInfo

func (m *FilterIdToConfTabResp_ConfTab) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *FilterIdToConfTabResp_ConfTab) GetBlocks() []*FilterIdToConfTabResp_Block {
	if m != nil {
		return m.Blocks
	}
	return nil
}

func init() {
	proto.RegisterType((*DelPublishHotRcmdReq)(nil), "music_topic_channel.DelPublishHotRcmdReq")
	proto.RegisterType((*DelPublishHotRcmdResp)(nil), "music_topic_channel.DelPublishHotRcmdResp")
	proto.RegisterType((*SortPublishHotRcmdReq)(nil), "music_topic_channel.SortPublishHotRcmdReq")
	proto.RegisterType((*SortPublishHotRcmdResp)(nil), "music_topic_channel.SortPublishHotRcmdResp")
	proto.RegisterType((*SearchPublishHotRcmdReq)(nil), "music_topic_channel.SearchPublishHotRcmdReq")
	proto.RegisterType((*SearchPublishHotRcmdResp)(nil), "music_topic_channel.SearchPublishHotRcmdResp")
	proto.RegisterType((*UpsertPublishHotRcmdReq)(nil), "music_topic_channel.UpsertPublishHotRcmdReq")
	proto.RegisterType((*UpsertPublishHotRcmdResp)(nil), "music_topic_channel.UpsertPublishHotRcmdResp")
	proto.RegisterType((*TabPublishHotRcmd)(nil), "music_topic_channel.TabPublishHotRcmd")
	proto.RegisterType((*GetTabPublishHotRcmdReq)(nil), "music_topic_channel.GetTabPublishHotRcmdReq")
	proto.RegisterType((*GetTabPublishHotRcmdResp)(nil), "music_topic_channel.GetTabPublishHotRcmdResp")
	proto.RegisterType((*ListMusicChannelViewPbsReq)(nil), "music_topic_channel.ListMusicChannelViewPbsReq")
	proto.RegisterType((*ListMusicChannelViewPbsResp)(nil), "music_topic_channel.ListMusicChannelViewPbsResp")
	proto.RegisterMapType((map[uint32][]byte)(nil), "music_topic_channel.ListMusicChannelViewPbsResp.ChannelViewsEntry")
	proto.RegisterType((*ListMusicChannelViewsForMusicReq)(nil), "music_topic_channel.ListMusicChannelViewsForMusicReq")
	proto.RegisterType((*ListMusicChannelViewsForMusicResp)(nil), "music_topic_channel.ListMusicChannelViewsForMusicResp")
	proto.RegisterMapType((map[uint32]*hobby_channel.ListHobbyChannelResp_HobbyChannelItem)(nil), "music_topic_channel.ListMusicChannelViewsForMusicResp.ItemsEntry")
	proto.RegisterType((*BatchHighQualityChannelsReq)(nil), "music_topic_channel.BatchHighQualityChannelsReq")
	proto.RegisterMapType((map[uint32]uint32)(nil), "music_topic_channel.BatchHighQualityChannelsReq.TabChannelMapEntry")
	proto.RegisterType((*BatchHighQualityChannelsResp)(nil), "music_topic_channel.BatchHighQualityChannelsResp")
	proto.RegisterMapType((map[uint32]QualityType)(nil), "music_topic_channel.BatchHighQualityChannelsResp.ChannelQualityMapEntry")
	proto.RegisterType((*GetFilterItemsByTabIdReq)(nil), "music_topic_channel.GetFilterItemsByTabIdReq")
	proto.RegisterType((*GetFilterItemsByTabIdResp)(nil), "music_topic_channel.GetFilterItemsByTabIdResp")
	proto.RegisterType((*BatchFilterIdsByGameCardReq)(nil), "music_topic_channel.BatchFilterIdsByGameCardReq")
	proto.RegisterType((*BatchFilterIdsByGameCardResp)(nil), "music_topic_channel.BatchFilterIdsByGameCardResp")
	proto.RegisterMapType((map[string]*BatchFilterIdsByGameCardResp_Filters)(nil), "music_topic_channel.BatchFilterIdsByGameCardResp.GameFiltersEntry")
	proto.RegisterType((*BatchFilterIdsByGameCardResp_Filters)(nil), "music_topic_channel.BatchFilterIdsByGameCardResp.Filters")
	proto.RegisterType((*QueryFilterItemsReq)(nil), "music_topic_channel.QueryFilterItemsReq")
	proto.RegisterType((*QueryFilterItemsResp)(nil), "music_topic_channel.QueryFilterItemsResp")
	proto.RegisterType((*FilterItem)(nil), "music_topic_channel.FilterItem")
	proto.RegisterType((*Tab)(nil), "music_topic_channel.Tab")
	proto.RegisterType((*Block)(nil), "music_topic_channel.Block")
	proto.RegisterType((*GetMusicFilterItemByIdsReq)(nil), "music_topic_channel.GetMusicFilterItemByIdsReq")
	proto.RegisterType((*GetMusicFilterItemByIdsResp)(nil), "music_topic_channel.GetMusicFilterItemByIdsResp")
	proto.RegisterMapType((map[string]*MusicFilterItem)(nil), "music_topic_channel.GetMusicFilterItemByIdsResp.FilterMapEntry")
	proto.RegisterType((*MusicFilterItem)(nil), "music_topic_channel.MusicFilterItem")
	proto.RegisterType((*RcmdInfo)(nil), "music_topic_channel.RcmdInfo")
	proto.RegisterMapType((map[uint32]*common.ChannelInfo)(nil), "music_topic_channel.RcmdInfo.ChannelInfoMapEntry")
	proto.RegisterType((*ListMusicChannelViewsReq)(nil), "music_topic_channel.ListMusicChannelViewsReq")
	proto.RegisterType((*ListMusicChannelViewsResp)(nil), "music_topic_channel.ListMusicChannelViewsResp")
	proto.RegisterMapType((map[uint32]*music_topic_channel.MusicChannel)(nil), "music_topic_channel.ListMusicChannelViewsResp.ChannelViewsEntry")
	proto.RegisterType((*ListHomePageFilterItemsReq)(nil), "music_topic_channel.ListHomePageFilterItemsReq")
	proto.RegisterType((*ListHomePageFilterItemsResp)(nil), "music_topic_channel.ListHomePageFilterItemsResp")
	proto.RegisterType((*GetUserSchoolLastReq)(nil), "music_topic_channel.GetUserSchoolLastReq")
	proto.RegisterType((*GetUserSchoolLastResp)(nil), "music_topic_channel.GetUserSchoolLastResp")
	proto.RegisterType((*SetUserSchoolLastReq)(nil), "music_topic_channel.SetUserSchoolLastReq")
	proto.RegisterType((*SetUserSchoolLastResp)(nil), "music_topic_channel.SetUserSchoolLastResp")
	proto.RegisterType((*GetRcmdPgcChannelReq)(nil), "music_topic_channel.GetRcmdPgcChannelReq")
	proto.RegisterType((*GetRcmdPgcChannelResp)(nil), "music_topic_channel.GetRcmdPgcChannelResp")
	proto.RegisterType((*IsOlderForMusicHomePageReq)(nil), "music_topic_channel.IsOlderForMusicHomePageReq")
	proto.RegisterType((*IsOlderForMusicHomePageResp)(nil), "music_topic_channel.IsOlderForMusicHomePageResp")
	proto.RegisterType((*GetMusicChannelFilterV2Req)(nil), "music_topic_channel.GetMusicChannelFilterV2Req")
	proto.RegisterType((*GetMusicChannelFilterV2Resp)(nil), "music_topic_channel.GetMusicChannelFilterV2Resp")
	proto.RegisterType((*GetMusicChannelFilterV2Resp_FilterItem)(nil), "music_topic_channel.GetMusicChannelFilterV2Resp.FilterItem")
	proto.RegisterType((*GetMusicChannelFilterV2Resp_FilterSubItem)(nil), "music_topic_channel.GetMusicChannelFilterV2Resp.FilterSubItem")
	proto.RegisterType((*SameCityTitle)(nil), "music_topic_channel.SameCityTitle")
	proto.RegisterType((*BatchIsPublishingReq)(nil), "music_topic_channel.BatchIsPublishingReq")
	proto.RegisterType((*BatchIsPublishingReq_TabChannel)(nil), "music_topic_channel.BatchIsPublishingReq.TabChannel")
	proto.RegisterType((*BatchIsPublishingResp)(nil), "music_topic_channel.BatchIsPublishingResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "music_topic_channel.BatchIsPublishingResp.ChannelPublishingEntry")
	proto.RegisterType((*IsPublishingReq)(nil), "music_topic_channel.IsPublishingReq")
	proto.RegisterType((*IsPublishingResp)(nil), "music_topic_channel.IsPublishingResp")
	proto.RegisterType((*ListPublishingChannelIdsReq)(nil), "music_topic_channel.ListPublishingChannelIdsReq")
	proto.RegisterType((*ListPublishingChannelIdsResp)(nil), "music_topic_channel.ListPublishingChannelIdsResp")
	proto.RegisterType((*ListPublishingChannelIdsRespChannel)(nil), "music_topic_channel.ListPublishingChannelIdsResp.channel")
	proto.RegisterType((*BlockOptionList)(nil), "music_topic_channel.BlockOptionList")
	proto.RegisterType((*BlockOption)(nil), "music_topic_channel.BlockOption")
	proto.RegisterType((*MusicChannelReleaseInfo)(nil), "music_topic_channel.MusicChannelReleaseInfo")
	proto.RegisterType((*GameLabel)(nil), "music_topic_channel.GameLabel")
	proto.RegisterType((*SetMusicChannelReleaseInfoReq)(nil), "music_topic_channel.SetMusicChannelReleaseInfoReq")
	proto.RegisterType((*SetMusicChannelReleaseInfoResp)(nil), "music_topic_channel.SetMusicChannelReleaseInfoResp")
	proto.RegisterType((*InitMusicChannelReleaseInfoReq)(nil), "music_topic_channel.InitMusicChannelReleaseInfoReq")
	proto.RegisterType((*InitMusicChannelReleaseInfoResp)(nil), "music_topic_channel.InitMusicChannelReleaseInfoResp")
	proto.RegisterType((*DismissMusicChannelReq)(nil), "music_topic_channel.DismissMusicChannelReq")
	proto.RegisterType((*DismissMusicChannelResp)(nil), "music_topic_channel.DismissMusicChannelResp")
	proto.RegisterType((*GetRecommendChannelListLoadMore)(nil), "music_topic_channel.GetRecommendChannelListLoadMore")
	proto.RegisterType((*GetMusicChannelListReq)(nil), "music_topic_channel.GetMusicChannelListReq")
	proto.RegisterType((*GetMusicChannelListResp)(nil), "music_topic_channel.GetMusicChannelListResp")
	proto.RegisterType((*GetMusicChannelByIdsReq)(nil), "music_topic_channel.GetMusicChannelByIdsReq")
	proto.RegisterType((*GetMusicChannelByIdsResp)(nil), "music_topic_channel.GetMusicChannelByIdsResp")
	proto.RegisterType((*SwitchChannelTabReq)(nil), "music_topic_channel.SwitchChannelTabReq")
	proto.RegisterType((*SwitchChannelTabResp)(nil), "music_topic_channel.SwitchChannelTabResp")
	proto.RegisterType((*DisappearChannelReq)(nil), "music_topic_channel.DisappearChannelReq")
	proto.RegisterType((*DisappearChannelReq_Timeout)(nil), "music_topic_channel.DisappearChannelReq.Timeout")
	proto.RegisterType((*DisappearChannelReq_Keepalive)(nil), "music_topic_channel.DisappearChannelReq.Keepalive")
	proto.RegisterType((*DisappearChannelReq_ReleaseTimeout)(nil), "music_topic_channel.DisappearChannelReq.ReleaseTimeout")
	proto.RegisterType((*DisappearChannelResp)(nil), "music_topic_channel.DisappearChannelResp")
	proto.RegisterType((*GetOnlineInfoReq)(nil), "music_topic_channel.GetOnlineInfoReq")
	proto.RegisterType((*GetOnlineInfoResp)(nil), "music_topic_channel.GetOnlineInfoResp")
	proto.RegisterType((*FreezeChannelReq)(nil), "music_topic_channel.FreezeChannelReq")
	proto.RegisterType((*FreezeChannelResp)(nil), "music_topic_channel.FreezeChannelResp")
	proto.RegisterType((*UnfreezeChannelReq)(nil), "music_topic_channel.UnfreezeChannelReq")
	proto.RegisterType((*UnfreezeChannelResp)(nil), "music_topic_channel.UnfreezeChannelResp")
	proto.RegisterType((*GetChannelFreezeInfoReq)(nil), "music_topic_channel.GetChannelFreezeInfoReq")
	proto.RegisterType((*GetChannelFreezeInfoResp)(nil), "music_topic_channel.GetChannelFreezeInfoResp")
	proto.RegisterType((*SetExtraHistoryReq)(nil), "music_topic_channel.SetExtraHistoryReq")
	proto.RegisterType((*SetExtraHistoryResp)(nil), "music_topic_channel.SetExtraHistoryResp")
	proto.RegisterType((*GetExtraHistoryReq)(nil), "music_topic_channel.GetExtraHistoryReq")
	proto.RegisterType((*GetExtraHistoryResp)(nil), "music_topic_channel.GetExtraHistoryResp")
	proto.RegisterType((*GetChannelPlayModelReq)(nil), "music_topic_channel.GetChannelPlayModelReq")
	proto.RegisterType((*GetChannelPlayModelResp)(nil), "music_topic_channel.GetChannelPlayModelResp")
	proto.RegisterType((*GetChannelRoomUserNumberReq)(nil), "music_topic_channel.GetChannelRoomUserNumberReq")
	proto.RegisterType((*GetChannelRoomUserNumberResp)(nil), "music_topic_channel.GetChannelRoomUserNumberResp")
	proto.RegisterType((*GetChannelRoomUserNumberResp_RoomUserInfo)(nil), "music_topic_channel.GetChannelRoomUserNumberResp.RoomUserInfo")
	proto.RegisterType((*AddTemporaryChannelReq)(nil), "music_topic_channel.AddTemporaryChannelReq")
	proto.RegisterType((*AddTemporaryChannelResp)(nil), "music_topic_channel.AddTemporaryChannelResp")
	proto.RegisterType((*SearchHighQualityChannelsReq)(nil), "music_topic_channel.SearchHighQualityChannelsReq")
	proto.RegisterType((*SearchHighQualityChannelsResp)(nil), "music_topic_channel.SearchHighQualityChannelsResp")
	proto.RegisterType((*UpdateHighQualityChannelsReq)(nil), "music_topic_channel.UpdateHighQualityChannelsReq")
	proto.RegisterType((*UpdateHighQualityChannelsResp)(nil), "music_topic_channel.UpdateHighQualityChannelsResp")
	proto.RegisterType((*HighQualityChannelInfo)(nil), "music_topic_channel.HighQualityChannelInfo")
	proto.RegisterType((*BatchIsHighQualityChannelsReq)(nil), "music_topic_channel.BatchIsHighQualityChannelsReq")
	proto.RegisterMapType((map[uint32]uint32)(nil), "music_topic_channel.BatchIsHighQualityChannelsReq.TabChannelMapEntry")
	proto.RegisterType((*BatchIsHighQualityChannelsResp)(nil), "music_topic_channel.BatchIsHighQualityChannelsResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "music_topic_channel.BatchIsHighQualityChannelsResp.ChannelHighQualityMapEntry")
	proto.RegisterType((*BatchSetResourceConfigReq)(nil), "music_topic_channel.BatchSetResourceConfigReq")
	proto.RegisterType((*BatchSetResourceConfigResp)(nil), "music_topic_channel.BatchSetResourceConfigResp")
	proto.RegisterType((*GetResourceConfigsReq)(nil), "music_topic_channel.GetResourceConfigsReq")
	proto.RegisterType((*GetResourceConfigsResp)(nil), "music_topic_channel.GetResourceConfigsResp")
	proto.RegisterType((*ResourceConfigInfo)(nil), "music_topic_channel.ResourceConfigInfo")
	proto.RegisterType((*BatchDelResourceConfigReq)(nil), "music_topic_channel.BatchDelResourceConfigReq")
	proto.RegisterType((*BatchDelResourceConfigResp)(nil), "music_topic_channel.BatchDelResourceConfigResp")
	proto.RegisterType((*ListMuseSocialCommunityChannelsReq)(nil), "music_topic_channel.ListMuseSocialCommunityChannelsReq")
	proto.RegisterType((*ListMuseSocialCommunityChannelsResp)(nil), "music_topic_channel.ListMuseSocialCommunityChannelsResp")
	proto.RegisterMapType((map[uint32]*music_topic_channel.MuseSocialCommunityChannelInfo)(nil), "music_topic_channel.ListMuseSocialCommunityChannelsResp.ChannelViewsEntry")
	proto.RegisterType((*GetFilterInfoByFilterIdsReq)(nil), "music_topic_channel.GetFilterInfoByFilterIdsReq")
	proto.RegisterType((*MusicFilterItemV2)(nil), "music_topic_channel.MusicFilterItemV2")
	proto.RegisterType((*GetFilterInfoByFilterIdsResp)(nil), "music_topic_channel.GetFilterInfoByFilterIdsResp")
	proto.RegisterMapType((map[string]*MusicFilterItemV2)(nil), "music_topic_channel.GetFilterInfoByFilterIdsResp.FilterMapEntry")
	proto.RegisterType((*GetFilterInsertChannelIdsReq)(nil), "music_topic_channel.GetFilterInsertChannelIdsReq")
	proto.RegisterType((*GetFilterInsertChannelIdsResp)(nil), "music_topic_channel.GetFilterInsertChannelIdsResp")
	proto.RegisterMapType((map[string]*GetFilterInsertChannelIdsResp_InsertChannelIds)(nil), "music_topic_channel.GetFilterInsertChannelIdsResp.FilterMapEntry")
	proto.RegisterType((*GetFilterInsertChannelIdsResp_InsertChannelIds)(nil), "music_topic_channel.GetFilterInsertChannelIdsResp.InsertChannelIds")
	proto.RegisterType((*FilterIdToConfTabReq)(nil), "music_topic_channel.FilterIdToConfTabReq")
	proto.RegisterType((*FilterIdToConfTabResp)(nil), "music_topic_channel.FilterIdToConfTabResp")
	proto.RegisterType((*FilterIdToConfTabResp_Block)(nil), "music_topic_channel.FilterIdToConfTabResp.Block")
	proto.RegisterType((*FilterIdToConfTabResp_ConfTab)(nil), "music_topic_channel.FilterIdToConfTabResp.ConfTab")
	proto.RegisterEnum("music_topic_channel.HotRcmdStatus", HotRcmdStatus_name, HotRcmdStatus_value)
	proto.RegisterEnum("music_topic_channel.QualityType", QualityType_name, QualityType_value)
	proto.RegisterEnum("music_topic_channel.FilterAttrType", FilterAttrType_name, FilterAttrType_value)
	proto.RegisterEnum("music_topic_channel.ChannelDisplayType", ChannelDisplayType_name, ChannelDisplayType_value)
	proto.RegisterEnum("music_topic_channel.DismissType", DismissType_name, DismissType_value)
	proto.RegisterEnum("music_topic_channel.HistoryType", HistoryType_name, HistoryType_value)
	proto.RegisterEnum("music_topic_channel.HighQualityChannelInfoStatusType", HighQualityChannelInfoStatusType_name, HighQualityChannelInfoStatusType_value)
	proto.RegisterEnum("music_topic_channel.GetMusicChannelFilterV2Resp_FilterItemType", GetMusicChannelFilterV2Resp_FilterItemType_name, GetMusicChannelFilterV2Resp_FilterItemType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MusicChannelClient is the client API for MusicChannel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MusicChannelClient interface {
	// 修改主题房字段
	SetMusicChannelReleaseInfo(ctx context.Context, in *SetMusicChannelReleaseInfoReq, opts ...grpc.CallOption) (*SetMusicChannelReleaseInfoResp, error)
	InitMusicChannelReleaseInfo(ctx context.Context, in *InitMusicChannelReleaseInfoReq, opts ...grpc.CallOption) (*InitMusicChannelReleaseInfoResp, error)
	// 解散主题房
	DismissMusicChannel(ctx context.Context, in *DismissMusicChannelReq, opts ...grpc.CallOption) (*DismissMusicChannelResp, error)
	// 获取房间列表（兜底推荐）
	GetMusicChannelList(ctx context.Context, in *GetMusicChannelListReq, opts ...grpc.CallOption) (*GetMusicChannelListResp, error)
	// 获取房间发布信息
	GetMusicChannelByIds(ctx context.Context, in *GetMusicChannelByIdsReq, opts ...grpc.CallOption) (*GetMusicChannelByIdsResp, error)
	// 清除房间展示在tab
	DisappearChannel(ctx context.Context, in *DisappearChannelReq, opts ...grpc.CallOption) (*DisappearChannelResp, error)
	// 获取主题房数，以及示例用户
	GetOnlineInfo(ctx context.Context, in *GetOnlineInfoReq, opts ...grpc.CallOption) (*GetOnlineInfoResp, error)
	// 切换房间玩法
	SwitchChannelTab(ctx context.Context, in *SwitchChannelTabReq, opts ...grpc.CallOption) (*SwitchChannelTabResp, error)
	// 冻结主题房
	FreezeChannel(ctx context.Context, in *FreezeChannelReq, opts ...grpc.CallOption) (*FreezeChannelResp, error)
	// 解除冻结主题房
	UnfreezeChannel(ctx context.Context, in *UnfreezeChannelReq, opts ...grpc.CallOption) (*UnfreezeChannelResp, error)
	GetChannelFreezeInfo(ctx context.Context, in *GetChannelFreezeInfoReq, opts ...grpc.CallOption) (*GetChannelFreezeInfoResp, error)
	// 获取指定房间类型人数
	GetChannelRoomUserNumber(ctx context.Context, in *GetChannelRoomUserNumberReq, opts ...grpc.CallOption) (*GetChannelRoomUserNumberResp, error)
	// 创建临时房
	AddTemporaryChannel(ctx context.Context, in *AddTemporaryChannelReq, opts ...grpc.CallOption) (*AddTemporaryChannelResp, error)
	// 历史记录保存
	SetExtraHistory(ctx context.Context, in *SetExtraHistoryReq, opts ...grpc.CallOption) (*SetExtraHistoryResp, error)
	GetExtraHistory(ctx context.Context, in *GetExtraHistoryReq, opts ...grpc.CallOption) (*GetExtraHistoryResp, error)
	// 获取发布中的房间
	ListPublishingChannelIds(ctx context.Context, in *ListPublishingChannelIdsReq, opts ...grpc.CallOption) (*ListPublishingChannelIdsResp, error)
	// 获取发布中的房间
	IsPublishing(ctx context.Context, in *IsPublishingReq, opts ...grpc.CallOption) (*IsPublishingResp, error)
	// 获取发布中的房间
	BatchIsPublishing(ctx context.Context, in *BatchIsPublishingReq, opts ...grpc.CallOption) (*BatchIsPublishingResp, error)
	// 获取过滤器
	GetMusicChannelFilterV2(ctx context.Context, in *GetMusicChannelFilterV2Req, opts ...grpc.CallOption) (*GetMusicChannelFilterV2Resp, error)
	// 获取过滤器
	IsOlderForMusicHomePage(ctx context.Context, in *IsOlderForMusicHomePageReq, opts ...grpc.CallOption) (*IsOlderForMusicHomePageResp, error)
	SearchHighQualityChannels(ctx context.Context, in *SearchHighQualityChannelsReq, opts ...grpc.CallOption) (*SearchHighQualityChannelsResp, error)
	UpdateHighQualityChannels(ctx context.Context, in *UpdateHighQualityChannelsReq, opts ...grpc.CallOption) (*UpdateHighQualityChannelsResp, error)
	BatchIsHighQualityChannels(ctx context.Context, in *BatchIsHighQualityChannelsReq, opts ...grpc.CallOption) (*BatchIsHighQualityChannelsResp, error)
	BatchHighQualityChannels(ctx context.Context, in *BatchHighQualityChannelsReq, opts ...grpc.CallOption) (*BatchHighQualityChannelsResp, error)
	GetRcmdPgcChannel(ctx context.Context, in *GetRcmdPgcChannelReq, opts ...grpc.CallOption) (*GetRcmdPgcChannelResp, error)
	SetUserSchoolLast(ctx context.Context, in *SetUserSchoolLastReq, opts ...grpc.CallOption) (*SetUserSchoolLastResp, error)
	GetUserSchoolLast(ctx context.Context, in *GetUserSchoolLastReq, opts ...grpc.CallOption) (*GetUserSchoolLastResp, error)
	// tt首页获取过滤器
	GetMusicFilterItemByIds(ctx context.Context, in *GetMusicFilterItemByIdsReq, opts ...grpc.CallOption) (*GetMusicFilterItemByIdsResp, error)
	ListHomePageFilterItems(ctx context.Context, in *ListHomePageFilterItemsReq, opts ...grpc.CallOption) (*ListHomePageFilterItemsResp, error)
	GetFilterIdsByTabId(ctx context.Context, in *GetFilterItemsByTabIdReq, opts ...grpc.CallOption) (*GetFilterItemsByTabIdResp, error)
	BatchFilterIdsByGameCard(ctx context.Context, in *BatchFilterIdsByGameCardReq, opts ...grpc.CallOption) (*BatchFilterIdsByGameCardResp, error)
	// 获取view
	ListMusicChannelViews(ctx context.Context, in *ListMusicChannelViewsReq, opts ...grpc.CallOption) (*ListMusicChannelViewsResp, error)
	// 获取view pb
	ListMusicChannelViewPbs(ctx context.Context, in *ListMusicChannelViewPbsReq, opts ...grpc.CallOption) (*ListMusicChannelViewPbsResp, error)
	ListMusicChannelViewsForMusic(ctx context.Context, in *ListMusicChannelViewsForMusicReq, opts ...grpc.CallOption) (*ListMusicChannelViewsForMusicResp, error)
	GetTabPublishHotRcmd(ctx context.Context, in *GetTabPublishHotRcmdReq, opts ...grpc.CallOption) (*GetTabPublishHotRcmdResp, error)
	UpsertPublishHotRcmd(ctx context.Context, in *UpsertPublishHotRcmdReq, opts ...grpc.CallOption) (*UpsertPublishHotRcmdResp, error)
	SearchPublishHotRcmd(ctx context.Context, in *SearchPublishHotRcmdReq, opts ...grpc.CallOption) (*SearchPublishHotRcmdResp, error)
	SortPublishHotRcmd(ctx context.Context, in *SortPublishHotRcmdReq, opts ...grpc.CallOption) (*SortPublishHotRcmdResp, error)
	DelPublishHotRcmd(ctx context.Context, in *DelPublishHotRcmdReq, opts ...grpc.CallOption) (*DelPublishHotRcmdResp, error)
	ListMuseSocialCommunityChannels(ctx context.Context, in *ListMuseSocialCommunityChannelsReq, opts ...grpc.CallOption) (*ListMuseSocialCommunityChannelsResp, error)
	GetFilterInfoByFilterIds(ctx context.Context, in *GetFilterInfoByFilterIdsReq, opts ...grpc.CallOption) (*GetFilterInfoByFilterIdsResp, error)
	// 获取音乐垂直列表强插的房间id
	GetFilterInsertChannelIds(ctx context.Context, in *GetFilterInsertChannelIdsReq, opts ...grpc.CallOption) (*GetFilterInsertChannelIdsResp, error)
	// 音乐垂直列表FilterID映射给推荐的TabId Block Elem信息
	FilterIdToConfTab(ctx context.Context, in *FilterIdToConfTabReq, opts ...grpc.CallOption) (*FilterIdToConfTabResp, error)
}

type musicChannelClient struct {
	cc *grpc.ClientConn
}

func NewMusicChannelClient(cc *grpc.ClientConn) MusicChannelClient {
	return &musicChannelClient{cc}
}

func (c *musicChannelClient) SetMusicChannelReleaseInfo(ctx context.Context, in *SetMusicChannelReleaseInfoReq, opts ...grpc.CallOption) (*SetMusicChannelReleaseInfoResp, error) {
	out := new(SetMusicChannelReleaseInfoResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/SetMusicChannelReleaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) InitMusicChannelReleaseInfo(ctx context.Context, in *InitMusicChannelReleaseInfoReq, opts ...grpc.CallOption) (*InitMusicChannelReleaseInfoResp, error) {
	out := new(InitMusicChannelReleaseInfoResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/InitMusicChannelReleaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) DismissMusicChannel(ctx context.Context, in *DismissMusicChannelReq, opts ...grpc.CallOption) (*DismissMusicChannelResp, error) {
	out := new(DismissMusicChannelResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/DismissMusicChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetMusicChannelList(ctx context.Context, in *GetMusicChannelListReq, opts ...grpc.CallOption) (*GetMusicChannelListResp, error) {
	out := new(GetMusicChannelListResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetMusicChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetMusicChannelByIds(ctx context.Context, in *GetMusicChannelByIdsReq, opts ...grpc.CallOption) (*GetMusicChannelByIdsResp, error) {
	out := new(GetMusicChannelByIdsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetMusicChannelByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) DisappearChannel(ctx context.Context, in *DisappearChannelReq, opts ...grpc.CallOption) (*DisappearChannelResp, error) {
	out := new(DisappearChannelResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/DisappearChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetOnlineInfo(ctx context.Context, in *GetOnlineInfoReq, opts ...grpc.CallOption) (*GetOnlineInfoResp, error) {
	out := new(GetOnlineInfoResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetOnlineInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) SwitchChannelTab(ctx context.Context, in *SwitchChannelTabReq, opts ...grpc.CallOption) (*SwitchChannelTabResp, error) {
	out := new(SwitchChannelTabResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/SwitchChannelTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) FreezeChannel(ctx context.Context, in *FreezeChannelReq, opts ...grpc.CallOption) (*FreezeChannelResp, error) {
	out := new(FreezeChannelResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/FreezeChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) UnfreezeChannel(ctx context.Context, in *UnfreezeChannelReq, opts ...grpc.CallOption) (*UnfreezeChannelResp, error) {
	out := new(UnfreezeChannelResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/UnfreezeChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetChannelFreezeInfo(ctx context.Context, in *GetChannelFreezeInfoReq, opts ...grpc.CallOption) (*GetChannelFreezeInfoResp, error) {
	out := new(GetChannelFreezeInfoResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetChannelFreezeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetChannelRoomUserNumber(ctx context.Context, in *GetChannelRoomUserNumberReq, opts ...grpc.CallOption) (*GetChannelRoomUserNumberResp, error) {
	out := new(GetChannelRoomUserNumberResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetChannelRoomUserNumber", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) AddTemporaryChannel(ctx context.Context, in *AddTemporaryChannelReq, opts ...grpc.CallOption) (*AddTemporaryChannelResp, error) {
	out := new(AddTemporaryChannelResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/AddTemporaryChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) SetExtraHistory(ctx context.Context, in *SetExtraHistoryReq, opts ...grpc.CallOption) (*SetExtraHistoryResp, error) {
	out := new(SetExtraHistoryResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/SetExtraHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetExtraHistory(ctx context.Context, in *GetExtraHistoryReq, opts ...grpc.CallOption) (*GetExtraHistoryResp, error) {
	out := new(GetExtraHistoryResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetExtraHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) ListPublishingChannelIds(ctx context.Context, in *ListPublishingChannelIdsReq, opts ...grpc.CallOption) (*ListPublishingChannelIdsResp, error) {
	out := new(ListPublishingChannelIdsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/ListPublishingChannelIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) IsPublishing(ctx context.Context, in *IsPublishingReq, opts ...grpc.CallOption) (*IsPublishingResp, error) {
	out := new(IsPublishingResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/IsPublishing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) BatchIsPublishing(ctx context.Context, in *BatchIsPublishingReq, opts ...grpc.CallOption) (*BatchIsPublishingResp, error) {
	out := new(BatchIsPublishingResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/BatchIsPublishing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetMusicChannelFilterV2(ctx context.Context, in *GetMusicChannelFilterV2Req, opts ...grpc.CallOption) (*GetMusicChannelFilterV2Resp, error) {
	out := new(GetMusicChannelFilterV2Resp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetMusicChannelFilterV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) IsOlderForMusicHomePage(ctx context.Context, in *IsOlderForMusicHomePageReq, opts ...grpc.CallOption) (*IsOlderForMusicHomePageResp, error) {
	out := new(IsOlderForMusicHomePageResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/IsOlderForMusicHomePage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) SearchHighQualityChannels(ctx context.Context, in *SearchHighQualityChannelsReq, opts ...grpc.CallOption) (*SearchHighQualityChannelsResp, error) {
	out := new(SearchHighQualityChannelsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/SearchHighQualityChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) UpdateHighQualityChannels(ctx context.Context, in *UpdateHighQualityChannelsReq, opts ...grpc.CallOption) (*UpdateHighQualityChannelsResp, error) {
	out := new(UpdateHighQualityChannelsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/UpdateHighQualityChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) BatchIsHighQualityChannels(ctx context.Context, in *BatchIsHighQualityChannelsReq, opts ...grpc.CallOption) (*BatchIsHighQualityChannelsResp, error) {
	out := new(BatchIsHighQualityChannelsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/BatchIsHighQualityChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) BatchHighQualityChannels(ctx context.Context, in *BatchHighQualityChannelsReq, opts ...grpc.CallOption) (*BatchHighQualityChannelsResp, error) {
	out := new(BatchHighQualityChannelsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/BatchHighQualityChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetRcmdPgcChannel(ctx context.Context, in *GetRcmdPgcChannelReq, opts ...grpc.CallOption) (*GetRcmdPgcChannelResp, error) {
	out := new(GetRcmdPgcChannelResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetRcmdPgcChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) SetUserSchoolLast(ctx context.Context, in *SetUserSchoolLastReq, opts ...grpc.CallOption) (*SetUserSchoolLastResp, error) {
	out := new(SetUserSchoolLastResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/SetUserSchoolLast", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetUserSchoolLast(ctx context.Context, in *GetUserSchoolLastReq, opts ...grpc.CallOption) (*GetUserSchoolLastResp, error) {
	out := new(GetUserSchoolLastResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetUserSchoolLast", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetMusicFilterItemByIds(ctx context.Context, in *GetMusicFilterItemByIdsReq, opts ...grpc.CallOption) (*GetMusicFilterItemByIdsResp, error) {
	out := new(GetMusicFilterItemByIdsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetMusicFilterItemByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) ListHomePageFilterItems(ctx context.Context, in *ListHomePageFilterItemsReq, opts ...grpc.CallOption) (*ListHomePageFilterItemsResp, error) {
	out := new(ListHomePageFilterItemsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/ListHomePageFilterItems", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetFilterIdsByTabId(ctx context.Context, in *GetFilterItemsByTabIdReq, opts ...grpc.CallOption) (*GetFilterItemsByTabIdResp, error) {
	out := new(GetFilterItemsByTabIdResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetFilterIdsByTabId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) BatchFilterIdsByGameCard(ctx context.Context, in *BatchFilterIdsByGameCardReq, opts ...grpc.CallOption) (*BatchFilterIdsByGameCardResp, error) {
	out := new(BatchFilterIdsByGameCardResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/BatchFilterIdsByGameCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) ListMusicChannelViews(ctx context.Context, in *ListMusicChannelViewsReq, opts ...grpc.CallOption) (*ListMusicChannelViewsResp, error) {
	out := new(ListMusicChannelViewsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/ListMusicChannelViews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) ListMusicChannelViewPbs(ctx context.Context, in *ListMusicChannelViewPbsReq, opts ...grpc.CallOption) (*ListMusicChannelViewPbsResp, error) {
	out := new(ListMusicChannelViewPbsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/ListMusicChannelViewPbs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) ListMusicChannelViewsForMusic(ctx context.Context, in *ListMusicChannelViewsForMusicReq, opts ...grpc.CallOption) (*ListMusicChannelViewsForMusicResp, error) {
	out := new(ListMusicChannelViewsForMusicResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/ListMusicChannelViewsForMusic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetTabPublishHotRcmd(ctx context.Context, in *GetTabPublishHotRcmdReq, opts ...grpc.CallOption) (*GetTabPublishHotRcmdResp, error) {
	out := new(GetTabPublishHotRcmdResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetTabPublishHotRcmd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) UpsertPublishHotRcmd(ctx context.Context, in *UpsertPublishHotRcmdReq, opts ...grpc.CallOption) (*UpsertPublishHotRcmdResp, error) {
	out := new(UpsertPublishHotRcmdResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/UpsertPublishHotRcmd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) SearchPublishHotRcmd(ctx context.Context, in *SearchPublishHotRcmdReq, opts ...grpc.CallOption) (*SearchPublishHotRcmdResp, error) {
	out := new(SearchPublishHotRcmdResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/SearchPublishHotRcmd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) SortPublishHotRcmd(ctx context.Context, in *SortPublishHotRcmdReq, opts ...grpc.CallOption) (*SortPublishHotRcmdResp, error) {
	out := new(SortPublishHotRcmdResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/SortPublishHotRcmd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) DelPublishHotRcmd(ctx context.Context, in *DelPublishHotRcmdReq, opts ...grpc.CallOption) (*DelPublishHotRcmdResp, error) {
	out := new(DelPublishHotRcmdResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/DelPublishHotRcmd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) ListMuseSocialCommunityChannels(ctx context.Context, in *ListMuseSocialCommunityChannelsReq, opts ...grpc.CallOption) (*ListMuseSocialCommunityChannelsResp, error) {
	out := new(ListMuseSocialCommunityChannelsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/ListMuseSocialCommunityChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetFilterInfoByFilterIds(ctx context.Context, in *GetFilterInfoByFilterIdsReq, opts ...grpc.CallOption) (*GetFilterInfoByFilterIdsResp, error) {
	out := new(GetFilterInfoByFilterIdsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetFilterInfoByFilterIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetFilterInsertChannelIds(ctx context.Context, in *GetFilterInsertChannelIdsReq, opts ...grpc.CallOption) (*GetFilterInsertChannelIdsResp, error) {
	out := new(GetFilterInsertChannelIdsResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/GetFilterInsertChannelIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) FilterIdToConfTab(ctx context.Context, in *FilterIdToConfTabReq, opts ...grpc.CallOption) (*FilterIdToConfTabResp, error) {
	out := new(FilterIdToConfTabResp)
	err := c.cc.Invoke(ctx, "/music_topic_channel.MusicChannel/FilterIdToConfTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MusicChannelServer is the server API for MusicChannel service.
type MusicChannelServer interface {
	// 修改主题房字段
	SetMusicChannelReleaseInfo(context.Context, *SetMusicChannelReleaseInfoReq) (*SetMusicChannelReleaseInfoResp, error)
	InitMusicChannelReleaseInfo(context.Context, *InitMusicChannelReleaseInfoReq) (*InitMusicChannelReleaseInfoResp, error)
	// 解散主题房
	DismissMusicChannel(context.Context, *DismissMusicChannelReq) (*DismissMusicChannelResp, error)
	// 获取房间列表（兜底推荐）
	GetMusicChannelList(context.Context, *GetMusicChannelListReq) (*GetMusicChannelListResp, error)
	// 获取房间发布信息
	GetMusicChannelByIds(context.Context, *GetMusicChannelByIdsReq) (*GetMusicChannelByIdsResp, error)
	// 清除房间展示在tab
	DisappearChannel(context.Context, *DisappearChannelReq) (*DisappearChannelResp, error)
	// 获取主题房数，以及示例用户
	GetOnlineInfo(context.Context, *GetOnlineInfoReq) (*GetOnlineInfoResp, error)
	// 切换房间玩法
	SwitchChannelTab(context.Context, *SwitchChannelTabReq) (*SwitchChannelTabResp, error)
	// 冻结主题房
	FreezeChannel(context.Context, *FreezeChannelReq) (*FreezeChannelResp, error)
	// 解除冻结主题房
	UnfreezeChannel(context.Context, *UnfreezeChannelReq) (*UnfreezeChannelResp, error)
	GetChannelFreezeInfo(context.Context, *GetChannelFreezeInfoReq) (*GetChannelFreezeInfoResp, error)
	// 获取指定房间类型人数
	GetChannelRoomUserNumber(context.Context, *GetChannelRoomUserNumberReq) (*GetChannelRoomUserNumberResp, error)
	// 创建临时房
	AddTemporaryChannel(context.Context, *AddTemporaryChannelReq) (*AddTemporaryChannelResp, error)
	// 历史记录保存
	SetExtraHistory(context.Context, *SetExtraHistoryReq) (*SetExtraHistoryResp, error)
	GetExtraHistory(context.Context, *GetExtraHistoryReq) (*GetExtraHistoryResp, error)
	// 获取发布中的房间
	ListPublishingChannelIds(context.Context, *ListPublishingChannelIdsReq) (*ListPublishingChannelIdsResp, error)
	// 获取发布中的房间
	IsPublishing(context.Context, *IsPublishingReq) (*IsPublishingResp, error)
	// 获取发布中的房间
	BatchIsPublishing(context.Context, *BatchIsPublishingReq) (*BatchIsPublishingResp, error)
	// 获取过滤器
	GetMusicChannelFilterV2(context.Context, *GetMusicChannelFilterV2Req) (*GetMusicChannelFilterV2Resp, error)
	// 获取过滤器
	IsOlderForMusicHomePage(context.Context, *IsOlderForMusicHomePageReq) (*IsOlderForMusicHomePageResp, error)
	SearchHighQualityChannels(context.Context, *SearchHighQualityChannelsReq) (*SearchHighQualityChannelsResp, error)
	UpdateHighQualityChannels(context.Context, *UpdateHighQualityChannelsReq) (*UpdateHighQualityChannelsResp, error)
	BatchIsHighQualityChannels(context.Context, *BatchIsHighQualityChannelsReq) (*BatchIsHighQualityChannelsResp, error)
	BatchHighQualityChannels(context.Context, *BatchHighQualityChannelsReq) (*BatchHighQualityChannelsResp, error)
	GetRcmdPgcChannel(context.Context, *GetRcmdPgcChannelReq) (*GetRcmdPgcChannelResp, error)
	SetUserSchoolLast(context.Context, *SetUserSchoolLastReq) (*SetUserSchoolLastResp, error)
	GetUserSchoolLast(context.Context, *GetUserSchoolLastReq) (*GetUserSchoolLastResp, error)
	// tt首页获取过滤器
	GetMusicFilterItemByIds(context.Context, *GetMusicFilterItemByIdsReq) (*GetMusicFilterItemByIdsResp, error)
	ListHomePageFilterItems(context.Context, *ListHomePageFilterItemsReq) (*ListHomePageFilterItemsResp, error)
	GetFilterIdsByTabId(context.Context, *GetFilterItemsByTabIdReq) (*GetFilterItemsByTabIdResp, error)
	BatchFilterIdsByGameCard(context.Context, *BatchFilterIdsByGameCardReq) (*BatchFilterIdsByGameCardResp, error)
	// 获取view
	ListMusicChannelViews(context.Context, *ListMusicChannelViewsReq) (*ListMusicChannelViewsResp, error)
	// 获取view pb
	ListMusicChannelViewPbs(context.Context, *ListMusicChannelViewPbsReq) (*ListMusicChannelViewPbsResp, error)
	ListMusicChannelViewsForMusic(context.Context, *ListMusicChannelViewsForMusicReq) (*ListMusicChannelViewsForMusicResp, error)
	GetTabPublishHotRcmd(context.Context, *GetTabPublishHotRcmdReq) (*GetTabPublishHotRcmdResp, error)
	UpsertPublishHotRcmd(context.Context, *UpsertPublishHotRcmdReq) (*UpsertPublishHotRcmdResp, error)
	SearchPublishHotRcmd(context.Context, *SearchPublishHotRcmdReq) (*SearchPublishHotRcmdResp, error)
	SortPublishHotRcmd(context.Context, *SortPublishHotRcmdReq) (*SortPublishHotRcmdResp, error)
	DelPublishHotRcmd(context.Context, *DelPublishHotRcmdReq) (*DelPublishHotRcmdResp, error)
	ListMuseSocialCommunityChannels(context.Context, *ListMuseSocialCommunityChannelsReq) (*ListMuseSocialCommunityChannelsResp, error)
	GetFilterInfoByFilterIds(context.Context, *GetFilterInfoByFilterIdsReq) (*GetFilterInfoByFilterIdsResp, error)
	// 获取音乐垂直列表强插的房间id
	GetFilterInsertChannelIds(context.Context, *GetFilterInsertChannelIdsReq) (*GetFilterInsertChannelIdsResp, error)
	// 音乐垂直列表FilterID映射给推荐的TabId Block Elem信息
	FilterIdToConfTab(context.Context, *FilterIdToConfTabReq) (*FilterIdToConfTabResp, error)
}

func RegisterMusicChannelServer(s *grpc.Server, srv MusicChannelServer) {
	s.RegisterService(&_MusicChannel_serviceDesc, srv)
}

func _MusicChannel_SetMusicChannelReleaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMusicChannelReleaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).SetMusicChannelReleaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/SetMusicChannelReleaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).SetMusicChannelReleaseInfo(ctx, req.(*SetMusicChannelReleaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_InitMusicChannelReleaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitMusicChannelReleaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).InitMusicChannelReleaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/InitMusicChannelReleaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).InitMusicChannelReleaseInfo(ctx, req.(*InitMusicChannelReleaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_DismissMusicChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DismissMusicChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).DismissMusicChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/DismissMusicChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).DismissMusicChannel(ctx, req.(*DismissMusicChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetMusicChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetMusicChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetMusicChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetMusicChannelList(ctx, req.(*GetMusicChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetMusicChannelByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicChannelByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetMusicChannelByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetMusicChannelByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetMusicChannelByIds(ctx, req.(*GetMusicChannelByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_DisappearChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisappearChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).DisappearChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/DisappearChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).DisappearChannel(ctx, req.(*DisappearChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetOnlineInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetOnlineInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetOnlineInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetOnlineInfo(ctx, req.(*GetOnlineInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_SwitchChannelTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchChannelTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).SwitchChannelTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/SwitchChannelTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).SwitchChannelTab(ctx, req.(*SwitchChannelTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_FreezeChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).FreezeChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/FreezeChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).FreezeChannel(ctx, req.(*FreezeChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_UnfreezeChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfreezeChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).UnfreezeChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/UnfreezeChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).UnfreezeChannel(ctx, req.(*UnfreezeChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetChannelFreezeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelFreezeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetChannelFreezeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetChannelFreezeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetChannelFreezeInfo(ctx, req.(*GetChannelFreezeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetChannelRoomUserNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelRoomUserNumberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetChannelRoomUserNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetChannelRoomUserNumber",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetChannelRoomUserNumber(ctx, req.(*GetChannelRoomUserNumberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_AddTemporaryChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTemporaryChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).AddTemporaryChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/AddTemporaryChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).AddTemporaryChannel(ctx, req.(*AddTemporaryChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_SetExtraHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetExtraHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).SetExtraHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/SetExtraHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).SetExtraHistory(ctx, req.(*SetExtraHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetExtraHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtraHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetExtraHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetExtraHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetExtraHistory(ctx, req.(*GetExtraHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_ListPublishingChannelIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPublishingChannelIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).ListPublishingChannelIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/ListPublishingChannelIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).ListPublishingChannelIds(ctx, req.(*ListPublishingChannelIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_IsPublishing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsPublishingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).IsPublishing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/IsPublishing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).IsPublishing(ctx, req.(*IsPublishingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_BatchIsPublishing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchIsPublishingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).BatchIsPublishing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/BatchIsPublishing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).BatchIsPublishing(ctx, req.(*BatchIsPublishingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetMusicChannelFilterV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicChannelFilterV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetMusicChannelFilterV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetMusicChannelFilterV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetMusicChannelFilterV2(ctx, req.(*GetMusicChannelFilterV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_IsOlderForMusicHomePage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsOlderForMusicHomePageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).IsOlderForMusicHomePage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/IsOlderForMusicHomePage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).IsOlderForMusicHomePage(ctx, req.(*IsOlderForMusicHomePageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_SearchHighQualityChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchHighQualityChannelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).SearchHighQualityChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/SearchHighQualityChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).SearchHighQualityChannels(ctx, req.(*SearchHighQualityChannelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_UpdateHighQualityChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHighQualityChannelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).UpdateHighQualityChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/UpdateHighQualityChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).UpdateHighQualityChannels(ctx, req.(*UpdateHighQualityChannelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_BatchIsHighQualityChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchIsHighQualityChannelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).BatchIsHighQualityChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/BatchIsHighQualityChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).BatchIsHighQualityChannels(ctx, req.(*BatchIsHighQualityChannelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_BatchHighQualityChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchHighQualityChannelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).BatchHighQualityChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/BatchHighQualityChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).BatchHighQualityChannels(ctx, req.(*BatchHighQualityChannelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetRcmdPgcChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRcmdPgcChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetRcmdPgcChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetRcmdPgcChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetRcmdPgcChannel(ctx, req.(*GetRcmdPgcChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_SetUserSchoolLast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserSchoolLastReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).SetUserSchoolLast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/SetUserSchoolLast",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).SetUserSchoolLast(ctx, req.(*SetUserSchoolLastReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetUserSchoolLast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSchoolLastReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetUserSchoolLast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetUserSchoolLast",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetUserSchoolLast(ctx, req.(*GetUserSchoolLastReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetMusicFilterItemByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicFilterItemByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetMusicFilterItemByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetMusicFilterItemByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetMusicFilterItemByIds(ctx, req.(*GetMusicFilterItemByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_ListHomePageFilterItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListHomePageFilterItemsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).ListHomePageFilterItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/ListHomePageFilterItems",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).ListHomePageFilterItems(ctx, req.(*ListHomePageFilterItemsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetFilterIdsByTabId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFilterItemsByTabIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetFilterIdsByTabId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetFilterIdsByTabId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetFilterIdsByTabId(ctx, req.(*GetFilterItemsByTabIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_BatchFilterIdsByGameCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchFilterIdsByGameCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).BatchFilterIdsByGameCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/BatchFilterIdsByGameCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).BatchFilterIdsByGameCard(ctx, req.(*BatchFilterIdsByGameCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_ListMusicChannelViews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMusicChannelViewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).ListMusicChannelViews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/ListMusicChannelViews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).ListMusicChannelViews(ctx, req.(*ListMusicChannelViewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_ListMusicChannelViewPbs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMusicChannelViewPbsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).ListMusicChannelViewPbs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/ListMusicChannelViewPbs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).ListMusicChannelViewPbs(ctx, req.(*ListMusicChannelViewPbsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_ListMusicChannelViewsForMusic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMusicChannelViewsForMusicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).ListMusicChannelViewsForMusic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/ListMusicChannelViewsForMusic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).ListMusicChannelViewsForMusic(ctx, req.(*ListMusicChannelViewsForMusicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetTabPublishHotRcmd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabPublishHotRcmdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetTabPublishHotRcmd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetTabPublishHotRcmd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetTabPublishHotRcmd(ctx, req.(*GetTabPublishHotRcmdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_UpsertPublishHotRcmd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertPublishHotRcmdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).UpsertPublishHotRcmd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/UpsertPublishHotRcmd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).UpsertPublishHotRcmd(ctx, req.(*UpsertPublishHotRcmdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_SearchPublishHotRcmd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPublishHotRcmdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).SearchPublishHotRcmd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/SearchPublishHotRcmd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).SearchPublishHotRcmd(ctx, req.(*SearchPublishHotRcmdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_SortPublishHotRcmd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPublishHotRcmdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).SortPublishHotRcmd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/SortPublishHotRcmd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).SortPublishHotRcmd(ctx, req.(*SortPublishHotRcmdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_DelPublishHotRcmd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPublishHotRcmdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).DelPublishHotRcmd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/DelPublishHotRcmd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).DelPublishHotRcmd(ctx, req.(*DelPublishHotRcmdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_ListMuseSocialCommunityChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMuseSocialCommunityChannelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).ListMuseSocialCommunityChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/ListMuseSocialCommunityChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).ListMuseSocialCommunityChannels(ctx, req.(*ListMuseSocialCommunityChannelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetFilterInfoByFilterIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFilterInfoByFilterIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetFilterInfoByFilterIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetFilterInfoByFilterIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetFilterInfoByFilterIds(ctx, req.(*GetFilterInfoByFilterIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetFilterInsertChannelIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFilterInsertChannelIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetFilterInsertChannelIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/GetFilterInsertChannelIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetFilterInsertChannelIds(ctx, req.(*GetFilterInsertChannelIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_FilterIdToConfTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterIdToConfTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).FilterIdToConfTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/music_topic_channel.MusicChannel/FilterIdToConfTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).FilterIdToConfTab(ctx, req.(*FilterIdToConfTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MusicChannel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "music_topic_channel.MusicChannel",
	HandlerType: (*MusicChannelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetMusicChannelReleaseInfo",
			Handler:    _MusicChannel_SetMusicChannelReleaseInfo_Handler,
		},
		{
			MethodName: "InitMusicChannelReleaseInfo",
			Handler:    _MusicChannel_InitMusicChannelReleaseInfo_Handler,
		},
		{
			MethodName: "DismissMusicChannel",
			Handler:    _MusicChannel_DismissMusicChannel_Handler,
		},
		{
			MethodName: "GetMusicChannelList",
			Handler:    _MusicChannel_GetMusicChannelList_Handler,
		},
		{
			MethodName: "GetMusicChannelByIds",
			Handler:    _MusicChannel_GetMusicChannelByIds_Handler,
		},
		{
			MethodName: "DisappearChannel",
			Handler:    _MusicChannel_DisappearChannel_Handler,
		},
		{
			MethodName: "GetOnlineInfo",
			Handler:    _MusicChannel_GetOnlineInfo_Handler,
		},
		{
			MethodName: "SwitchChannelTab",
			Handler:    _MusicChannel_SwitchChannelTab_Handler,
		},
		{
			MethodName: "FreezeChannel",
			Handler:    _MusicChannel_FreezeChannel_Handler,
		},
		{
			MethodName: "UnfreezeChannel",
			Handler:    _MusicChannel_UnfreezeChannel_Handler,
		},
		{
			MethodName: "GetChannelFreezeInfo",
			Handler:    _MusicChannel_GetChannelFreezeInfo_Handler,
		},
		{
			MethodName: "GetChannelRoomUserNumber",
			Handler:    _MusicChannel_GetChannelRoomUserNumber_Handler,
		},
		{
			MethodName: "AddTemporaryChannel",
			Handler:    _MusicChannel_AddTemporaryChannel_Handler,
		},
		{
			MethodName: "SetExtraHistory",
			Handler:    _MusicChannel_SetExtraHistory_Handler,
		},
		{
			MethodName: "GetExtraHistory",
			Handler:    _MusicChannel_GetExtraHistory_Handler,
		},
		{
			MethodName: "ListPublishingChannelIds",
			Handler:    _MusicChannel_ListPublishingChannelIds_Handler,
		},
		{
			MethodName: "IsPublishing",
			Handler:    _MusicChannel_IsPublishing_Handler,
		},
		{
			MethodName: "BatchIsPublishing",
			Handler:    _MusicChannel_BatchIsPublishing_Handler,
		},
		{
			MethodName: "GetMusicChannelFilterV2",
			Handler:    _MusicChannel_GetMusicChannelFilterV2_Handler,
		},
		{
			MethodName: "IsOlderForMusicHomePage",
			Handler:    _MusicChannel_IsOlderForMusicHomePage_Handler,
		},
		{
			MethodName: "SearchHighQualityChannels",
			Handler:    _MusicChannel_SearchHighQualityChannels_Handler,
		},
		{
			MethodName: "UpdateHighQualityChannels",
			Handler:    _MusicChannel_UpdateHighQualityChannels_Handler,
		},
		{
			MethodName: "BatchIsHighQualityChannels",
			Handler:    _MusicChannel_BatchIsHighQualityChannels_Handler,
		},
		{
			MethodName: "BatchHighQualityChannels",
			Handler:    _MusicChannel_BatchHighQualityChannels_Handler,
		},
		{
			MethodName: "GetRcmdPgcChannel",
			Handler:    _MusicChannel_GetRcmdPgcChannel_Handler,
		},
		{
			MethodName: "SetUserSchoolLast",
			Handler:    _MusicChannel_SetUserSchoolLast_Handler,
		},
		{
			MethodName: "GetUserSchoolLast",
			Handler:    _MusicChannel_GetUserSchoolLast_Handler,
		},
		{
			MethodName: "GetMusicFilterItemByIds",
			Handler:    _MusicChannel_GetMusicFilterItemByIds_Handler,
		},
		{
			MethodName: "ListHomePageFilterItems",
			Handler:    _MusicChannel_ListHomePageFilterItems_Handler,
		},
		{
			MethodName: "GetFilterIdsByTabId",
			Handler:    _MusicChannel_GetFilterIdsByTabId_Handler,
		},
		{
			MethodName: "BatchFilterIdsByGameCard",
			Handler:    _MusicChannel_BatchFilterIdsByGameCard_Handler,
		},
		{
			MethodName: "ListMusicChannelViews",
			Handler:    _MusicChannel_ListMusicChannelViews_Handler,
		},
		{
			MethodName: "ListMusicChannelViewPbs",
			Handler:    _MusicChannel_ListMusicChannelViewPbs_Handler,
		},
		{
			MethodName: "ListMusicChannelViewsForMusic",
			Handler:    _MusicChannel_ListMusicChannelViewsForMusic_Handler,
		},
		{
			MethodName: "GetTabPublishHotRcmd",
			Handler:    _MusicChannel_GetTabPublishHotRcmd_Handler,
		},
		{
			MethodName: "UpsertPublishHotRcmd",
			Handler:    _MusicChannel_UpsertPublishHotRcmd_Handler,
		},
		{
			MethodName: "SearchPublishHotRcmd",
			Handler:    _MusicChannel_SearchPublishHotRcmd_Handler,
		},
		{
			MethodName: "SortPublishHotRcmd",
			Handler:    _MusicChannel_SortPublishHotRcmd_Handler,
		},
		{
			MethodName: "DelPublishHotRcmd",
			Handler:    _MusicChannel_DelPublishHotRcmd_Handler,
		},
		{
			MethodName: "ListMuseSocialCommunityChannels",
			Handler:    _MusicChannel_ListMuseSocialCommunityChannels_Handler,
		},
		{
			MethodName: "GetFilterInfoByFilterIds",
			Handler:    _MusicChannel_GetFilterInfoByFilterIds_Handler,
		},
		{
			MethodName: "GetFilterInsertChannelIds",
			Handler:    _MusicChannel_GetFilterInsertChannelIds_Handler,
		},
		{
			MethodName: "FilterIdToConfTab",
			Handler:    _MusicChannel_FilterIdToConfTab_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/music-topic-channel/music-topic-channel.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/music-topic-channel/music-topic-channel.proto", fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8)
}

var fileDescriptor_music_topic_channel_51bfa9b8b1fa81b8 = []byte{
	// 5273 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x7c, 0x4b, 0x6f, 0x1c, 0x49,
	0x72, 0x30, 0x8b, 0xef, 0x8e, 0x66, 0x93, 0xcd, 0x24, 0x29, 0x52, 0xa5, 0xe7, 0xd4, 0x7e, 0x3b,
	0xcb, 0x95, 0x46, 0x94, 0x44, 0x49, 0xa3, 0x59, 0x69, 0x66, 0x67, 0x28, 0x92, 0x22, 0x7b, 0x56,
	0x7c, 0xa8, 0xd9, 0xd2, 0x8c, 0x3e, 0x8d, 0xb7, 0xb6, 0xba, 0x3a, 0x49, 0xd6, 0xa8, 0xba, 0xaa,
	0x58, 0x59, 0x4d, 0x89, 0x8b, 0x05, 0xc6, 0x6b, 0x78, 0x31, 0xb6, 0x0f, 0x3b, 0x30, 0xe0, 0x83,
	0x01, 0xfb, 0x60, 0xf8, 0x60, 0xc0, 0x86, 0x7f, 0x81, 0x8f, 0x86, 0x01, 0x5f, 0x16, 0xf0, 0xc2,
	0x17, 0x03, 0xbe, 0x19, 0x86, 0x0f, 0x3e, 0xf9, 0xe2, 0xb3, 0x61, 0xe4, 0xa3, 0x5e, 0x5d, 0x99,
	0xcd, 0xa6, 0xc6, 0xf0, 0x5e, 0x7c, 0xab, 0x8a, 0xcc, 0x88, 0x8c, 0x8c, 0x8c, 0x8c, 0x8c, 0x8c,
	0x88, 0x2a, 0xf8, 0x30, 0x8a, 0x6e, 0x1e, 0x75, 0x1c, 0xfb, 0x15, 0x71, 0xdc, 0x63, 0x1c, 0xde,
	0x6c, 0x77, 0x88, 0x63, 0xdf, 0x88, 0xfc, 0xc0, 0xb1, 0x6f, 0xd8, 0x87, 0x96, 0xe7, 0x61, 0x57,
	0x06, 0x5b, 0x0a, 0x42, 0x3f, 0xf2, 0xd1, 0x0c, 0x6b, 0x32, 0x59, 0x93, 0x29, 0x9a, 0xf4, 0xbb,
	0x12, 0xa0, 0x8c, 0xc6, 0x0d, 0xd7, 0x3f, 0x70, 0x6c, 0x93, 0x93, 0xd2, 0x8d, 0x43, 0xbf, 0xd9,
	0x3c, 0x49, 0xfa, 0xb3, 0xb7, 0xb8, 0x67, 0xdc, 0xe7, 0x6e, 0x17, 0xb3, 0xa1, 0xdd, 0x6e, 0xdd,
	0x70, 0x7d, 0xdb, 0x72, 0x6f, 0xda, 0x7e, 0xbb, 0xed, 0x7b, 0x37, 0x73, 0x83, 0x72, 0x2c, 0x63,
	0x11, 0x66, 0xd7, 0xb0, 0xbb, 0xdb, 0x69, 0xba, 0x0e, 0x39, 0xdc, 0xf4, 0xa3, 0xba, 0xdd, 0x6e,
	0xd5, 0xf1, 0x11, 0xaa, 0xc2, 0x90, 0xd3, 0x22, 0x0b, 0xda, 0xd5, 0xa1, 0xc5, 0x52, 0x9d, 0x3e,
	0x1a, 0xf3, 0x30, 0x27, 0xe9, 0x49, 0x02, 0xe3, 0x63, 0x98, 0xdb, 0xf3, 0xc3, 0xa8, 0x48, 0x63,
	0x16, 0x46, 0x22, 0xab, 0x59, 0x6b, 0x2d, 0x68, 0x57, 0xb5, 0xc5, 0x4a, 0x9d, 0xbf, 0xc4, 0x94,
	0x07, 0x53, 0xca, 0x0b, 0x70, 0x4e, 0x46, 0x80, 0x04, 0xc6, 0xbf, 0x6a, 0x30, 0xbf, 0x87, 0xad,
	0xd0, 0x3e, 0x2c, 0x52, 0x9f, 0x84, 0x41, 0x87, 0x93, 0x2e, 0xd5, 0x07, 0x9d, 0x16, 0xba, 0x00,
	0x25, 0x7f, 0x7f, 0x9f, 0xe0, 0xc8, 0x74, 0x5a, 0x0b, 0x83, 0x0c, 0x3c, 0xce, 0x01, 0xb5, 0x16,
	0x9a, 0x83, 0xd1, 0xc8, 0x6a, 0xd2, 0x96, 0xa1, 0x2c, 0x2f, 0xcb, 0x30, 0xda, 0x74, 0x7d, 0xfb,
	0x15, 0x59, 0x18, 0xbe, 0x3a, 0xb4, 0x58, 0x5e, 0xd6, 0x97, 0x24, 0xcb, 0xb3, 0xf4, 0x88, 0x76,
	0xa9, 0x8b, 0x9e, 0x74, 0x56, 0xb6, 0xdf, 0xf1, 0xa2, 0x85, 0x11, 0x4e, 0x89, 0xbd, 0xa0, 0x07,
	0x30, 0x4a, 0x22, 0x2b, 0xea, 0x90, 0x85, 0xd1, 0xab, 0xda, 0xe2, 0xe4, 0xb2, 0x21, 0xa5, 0x24,
	0xd8, 0xdf, 0x63, 0x3d, 0xeb, 0x02, 0xc3, 0xf8, 0x1c, 0x16, 0xe4, 0x93, 0x24, 0x01, 0xfa, 0x10,
	0x46, 0x9c, 0x08, 0xb7, 0xf9, 0x4a, 0x94, 0x97, 0xdf, 0x95, 0x92, 0x6d, 0x58, 0xcd, 0x2e, 0x54,
	0x8e, 0x64, 0x7c, 0x06, 0xf3, 0xcf, 0x02, 0x82, 0x65, 0x8b, 0xf3, 0xed, 0x08, 0xeb, 0xb0, 0x20,
	0x27, 0x4c, 0x02, 0xe3, 0xcf, 0x06, 0x61, 0xba, 0x80, 0x58, 0x58, 0x2e, 0x04, 0xc3, 0x9e, 0xd5,
	0xc6, 0x62, 0xa5, 0xd8, 0x33, 0x85, 0x1d, 0x3a, 0x5e, 0xc4, 0xd6, 0xa8, 0x54, 0x67, 0xcf, 0x99,
	0x95, 0x1b, 0x96, 0xaf, 0xdc, 0x48, 0xdf, 0x2b, 0x47, 0x87, 0xf4, 0x23, 0xcc, 0x56, 0x88, 0x0e,
	0xe9, 0x47, 0x98, 0x6a, 0x8d, 0x1d, 0x62, 0x2b, 0xc2, 0xa6, 0x15, 0x2d, 0x8c, 0x5d, 0xd5, 0x16,
	0x87, 0xea, 0xe3, 0x1c, 0xb0, 0x12, 0xd1, 0xc6, 0x4e, 0xd0, 0x12, 0x8d, 0xe3, 0xbc, 0x91, 0x03,
	0x56, 0xb2, 0x2b, 0x5e, 0x3a, 0xf3, 0x8a, 0xdf, 0x82, 0xf9, 0x0d, 0x1c, 0x15, 0xa5, 0x8b, 0x8f,
	0x32, 0xf3, 0xcd, 0xee, 0x1a, 0xaa, 0x23, 0x72, 0x8c, 0x6f, 0xad, 0x23, 0x1f, 0x81, 0xfe, 0xc4,
	0x21, 0xd1, 0x16, 0xc5, 0x59, 0xe5, 0x9d, 0x9f, 0x3b, 0xf8, 0xf5, 0x6e, 0x93, 0x50, 0x76, 0xae,
	0x40, 0x39, 0xb6, 0x33, 0xb1, 0x3d, 0xa8, 0xd4, 0x41, 0x80, 0x6a, 0x2d, 0x62, 0xfc, 0xbd, 0x06,
	0x17, 0x94, 0xf8, 0x24, 0x40, 0x07, 0x50, 0x89, 0x09, 0x1c, 0x3b, 0xf8, 0x75, 0xcc, 0xe4, 0x23,
	0x29, 0x93, 0x3d, 0x08, 0x2d, 0x65, 0x40, 0x64, 0xdd, 0x8b, 0xc2, 0x93, 0xfa, 0x84, 0x9d, 0x01,
	0xe9, 0x1f, 0xc3, 0x74, 0xa1, 0x0b, 0x35, 0x36, 0xaf, 0xf0, 0x89, 0x10, 0x25, 0x7d, 0xa4, 0xdb,
	0xf7, 0xd8, 0x72, 0x3b, 0x5c, 0xf1, 0x26, 0xea, 0xfc, 0xe5, 0xc1, 0xe0, 0x07, 0x9a, 0xd1, 0x84,
	0xab, 0xb2, 0xf1, 0xc9, 0x63, 0x3f, 0x64, 0xb0, 0x7e, 0xc4, 0x81, 0x2e, 0x01, 0x38, 0xc4, 0xb4,
	0x7d, 0xd7, 0xc5, 0x76, 0xc4, 0xc6, 0x18, 0xaf, 0x97, 0x1c, 0xb2, 0xca, 0x01, 0xc6, 0x7f, 0x68,
	0xf0, 0xce, 0x29, 0x83, 0x90, 0x00, 0x7d, 0x96, 0x5f, 0xd0, 0x95, 0xbe, 0x65, 0x95, 0x23, 0xb3,
	0x54, 0xa3, 0x34, 0xb8, 0xa8, 0x38, 0x3d, 0xfd, 0x08, 0x20, 0x05, 0x4a, 0x84, 0xb3, 0x95, 0x15,
	0x4e, 0x79, 0xf9, 0xfe, 0xd2, 0x81, 0xb5, 0x94, 0x3b, 0x7a, 0xd8, 0xa8, 0x9b, 0x14, 0x22, 0x46,
	0x65, 0x03, 0x65, 0x01, 0x94, 0x7e, 0x56, 0xaa, 0xbf, 0xd2, 0xe0, 0xc2, 0x23, 0x2b, 0xb2, 0x0f,
	0x37, 0x9d, 0x83, 0xc3, 0xa7, 0x1d, 0xcb, 0x75, 0xa2, 0xb8, 0x2b, 0x53, 0xb0, 0x57, 0x30, 0x45,
	0xf5, 0x3d, 0x96, 0x6a, 0xdb, 0x0a, 0xc4, 0xac, 0x57, 0xe5, 0x3b, 0x5a, 0x4d, 0x8a, 0xaa, 0xb8,
	0x78, 0xdd, 0xb2, 0x02, 0x3e, 0xef, 0x4a, 0x94, 0x85, 0xe9, 0x9f, 0x00, 0x2a, 0x76, 0x3a, 0x4d,
	0x49, 0x2a, 0xd9, 0xe9, 0xfc, 0xf6, 0x20, 0x5c, 0x54, 0xf3, 0x40, 0x02, 0xf4, 0x06, 0x66, 0xe2,
	0xb9, 0x1c, 0xf1, 0xe6, 0xcc, 0x9c, 0x36, 0xcf, 0x38, 0xa7, 0x54, 0xed, 0x45, 0x53, 0x32, 0xb1,
	0x69, 0xbb, 0x1b, 0xae, 0xef, 0xc3, 0x39, 0x79, 0x67, 0xc9, 0x04, 0xdf, 0xcf, 0x4e, 0x70, 0x72,
	0xf9, 0xaa, 0x94, 0x2f, 0x41, 0xa6, 0x71, 0x12, 0xe0, 0xac, 0x08, 0x6e, 0x33, 0x53, 0xf4, 0xd8,
	0x71, 0x23, 0x1c, 0x32, 0x6d, 0x7a, 0x74, 0xd2, 0xa0, 0x36, 0xaa, 0x87, 0xf5, 0x7a, 0x00, 0xe7,
	0x15, 0x28, 0x24, 0xa0, 0x5b, 0x66, 0x9f, 0xb5, 0x98, 0xa9, 0xc7, 0x51, 0xe2, 0x10, 0x6a, 0x60,
	0x1e, 0x08, 0xfd, 0x79, 0x1c, 0x43, 0x1e, 0x9d, 0x6c, 0x58, 0x6d, 0xbc, 0x6a, 0x85, 0x6c, 0xc4,
	0x0b, 0x50, 0x3a, 0xb0, 0xda, 0xd8, 0xb4, 0xad, 0xb0, 0x25, 0x90, 0xc7, 0x0f, 0x44, 0xbb, 0xf1,
	0x57, 0xf1, 0x6a, 0x49, 0x91, 0x49, 0x80, 0x30, 0x4c, 0x30, 0x6c, 0x3e, 0x5c, 0x6f, 0xe3, 0xd4,
	0x8b, 0xd0, 0x12, 0x7d, 0xe1, 0x6d, 0x62, 0xc7, 0x95, 0x0f, 0x52, 0x88, 0x7e, 0x02, 0xd5, 0xee,
	0x0e, 0xd9, 0x45, 0x29, 0xf1, 0x45, 0xd9, 0xc9, 0xef, 0xbe, 0x1f, 0x9c, 0x9d, 0x0b, 0x31, 0x40,
	0x66, 0xb5, 0xf4, 0x45, 0x18, 0x13, 0xd0, 0xd3, 0x04, 0xfd, 0x1c, 0x66, 0x9e, 0x76, 0x70, 0x78,
	0x92, 0x59, 0x26, 0x85, 0x9f, 0x95, 0x50, 0x89, 0xfd, 0xac, 0x98, 0x48, 0x72, 0xaa, 0x0f, 0xa5,
	0xa7, 0xba, 0xb1, 0x05, 0xb3, 0x45, 0xba, 0x24, 0x40, 0xf7, 0xf2, 0x56, 0xee, 0x8a, 0x74, 0xba,
	0x29, 0x52, 0x7c, 0x5e, 0x7d, 0x3d, 0x08, 0x90, 0x42, 0xbf, 0x35, 0x7b, 0x68, 0x11, 0xaa, 0x81,
	0x15, 0x62, 0x2f, 0x32, 0x53, 0xbc, 0x61, 0xd6, 0x3e, 0xc9, 0xe1, 0xb1, 0xb0, 0xd1, 0x7b, 0x30,
	0x1c, 0x59, 0xcd, 0xd8, 0xe3, 0x58, 0x50, 0x1d, 0xb3, 0x75, 0xd6, 0x8b, 0x1e, 0x15, 0x8e, 0x4f,
	0xcc, 0x63, 0x1c, 0x12, 0xc7, 0xf7, 0x16, 0x46, 0xf9, 0x51, 0xe1, 0xf8, 0xe4, 0x39, 0x87, 0xa0,
	0xef, 0xc1, 0x94, 0xe5, 0xb5, 0x42, 0xdf, 0x69, 0x25, 0x9d, 0xc6, 0x58, 0xa7, 0x49, 0x01, 0x8e,
	0x3b, 0x9e, 0x83, 0x51, 0x2b, 0x08, 0xe8, 0x9a, 0x8d, 0xb3, 0x76, 0xf1, 0x66, 0xec, 0xc0, 0x50,
	0xc3, 0x6a, 0x2a, 0xdc, 0xec, 0xd4, 0x41, 0x1a, 0xec, 0xd7, 0x41, 0x32, 0x56, 0x60, 0x84, 0x01,
	0xd0, 0x79, 0x18, 0x67, 0xa0, 0x74, 0x23, 0x8f, 0xb1, 0xf7, 0x5a, 0x8b, 0x2a, 0x11, 0x76, 0x71,
	0x9b, 0xca, 0x4b, 0x08, 0xb8, 0x52, 0x2f, 0x09, 0x48, 0xad, 0x65, 0x3c, 0x04, 0x7d, 0x03, 0xf3,
	0x73, 0x29, 0x5d, 0xa4, 0x47, 0x27, 0xb5, 0x16, 0xd3, 0xa5, 0x53, 0x34, 0xf0, 0x5f, 0x34, 0xb8,
	0xa0, 0xc4, 0x26, 0x01, 0xfa, 0x71, 0x82, 0x9e, 0x9a, 0xd4, 0x8f, 0xa5, 0xf3, 0xea, 0x41, 0x45,
	0xa8, 0x54, 0x62, 0x49, 0xc5, 0xf8, 0xd4, 0x82, 0x36, 0x61, 0x32, 0xdf, 0x28, 0xd9, 0xa4, 0x0f,
	0xf2, 0x9b, 0xf4, 0xff, 0x49, 0x87, 0xef, 0x1a, 0x3b, 0x6b, 0x3d, 0xff, 0x7c, 0x10, 0xa6, 0xba,
	0x9a, 0xf3, 0x3a, 0xab, 0x29, 0x74, 0xb6, 0xcb, 0x51, 0x76, 0x6c, 0xdf, 0x8b, 0xf5, 0x98, 0x3e,
	0x53, 0x2d, 0x71, 0xda, 0xd6, 0x01, 0xe6, 0x77, 0x99, 0x52, 0x5d, 0xbc, 0xa1, 0x75, 0x28, 0x93,
	0x4e, 0x33, 0xb1, 0x70, 0x5c, 0x79, 0xfb, 0x63, 0x1b, 0x48, 0xa7, 0x19, 0x1b, 0x8f, 0x45, 0xa8,
	0x0a, 0x1e, 0xad, 0x28, 0x0a, 0xcd, 0xe8, 0x24, 0xe0, 0x8e, 0x74, 0xa5, 0x3e, 0xc9, 0xe1, 0x2b,
	0x51, 0x14, 0xd2, 0xa3, 0x02, 0xad, 0x00, 0xd8, 0xf4, 0xd8, 0x8b, 0x9c, 0xc8, 0xc5, 0xcc, 0xa7,
	0x2e, 0x2b, 0x9c, 0xe3, 0x3d, 0x6a, 0xbb, 0xe8, 0x09, 0x43, 0x7b, 0xd6, 0x4b, 0x76, 0xfc, 0x68,
	0xfc, 0x9d, 0x06, 0xe3, 0xd4, 0x47, 0xad, 0x79, 0xfb, 0x3e, 0x7a, 0x09, 0xd5, 0xc4, 0xe7, 0xf2,
	0xf6, 0xfd, 0xcc, 0xda, 0xdf, 0x96, 0x52, 0x8d, 0x11, 0xe3, 0xa3, 0x93, 0x3e, 0x27, 0xab, 0x3d,
	0x69, 0xe7, 0x80, 0xfa, 0x4b, 0x98, 0x91, 0x74, 0x93, 0x9c, 0x98, 0x4b, 0xf9, 0x75, 0x5f, 0x58,
	0xa2, 0xf7, 0xeb, 0x25, 0x7e, 0xb3, 0xce, 0x8e, 0x94, 0x5d, 0xeb, 0x5f, 0x6a, 0xb0, 0x20, 0x75,
	0xd3, 0xfa, 0x72, 0x25, 0xe7, 0x60, 0xd4, 0x21, 0xa6, 0xe5, 0xba, 0xc2, 0x8d, 0x1c, 0x71, 0xc8,
	0x8a, 0xeb, 0xa2, 0x07, 0x50, 0xa2, 0x43, 0x33, 0x59, 0x30, 0x05, 0x28, 0x2f, 0x5f, 0xea, 0x29,
	0x87, 0xfa, 0x78, 0x28, 0x9e, 0x8c, 0x7f, 0xd7, 0xe0, 0xbc, 0x82, 0x21, 0x76, 0x18, 0x4a, 0x5d,
	0xf5, 0x4f, 0xfa, 0x77, 0x3f, 0xfb, 0x72, 0xd4, 0x71, 0x7f, 0x8e, 0x7a, 0x61, 0xa3, 0x1d, 0x58,
	0x6a, 0xa5, 0x8d, 0xbd, 0xd1, 0x8c, 0xf0, 0x2f, 0xf2, 0x7b, 0xcd, 0xa6, 0xdf, 0xc6, 0xbb, 0xd6,
	0x01, 0xce, 0x9f, 0x6a, 0xc6, 0x0b, 0x7e, 0x6b, 0x91, 0xb6, 0x92, 0x80, 0x0e, 0x9e, 0x3d, 0x9b,
	0xfa, 0xdc, 0xe5, 0xfc, 0x80, 0x5a, 0x84, 0xd9, 0x0d, 0x1c, 0x3d, 0x23, 0x38, 0xdc, 0xb3, 0x0f,
	0x7d, 0xdf, 0x7d, 0x62, 0x91, 0x48, 0x84, 0x54, 0x3a, 0x89, 0x3d, 0xa5, 0x8f, 0xc6, 0x75, 0x98,
	0x93, 0xf4, 0x24, 0x01, 0xdd, 0xdf, 0xae, 0x45, 0x22, 0x61, 0x5e, 0xd9, 0xb3, 0xf1, 0x21, 0xcc,
	0xee, 0xf5, 0x45, 0x56, 0x8a, 0x3d, 0x0f, 0x73, 0x7b, 0xb2, 0xa1, 0x8c, 0xfb, 0x8c, 0x5b, 0xaa,
	0x2b, 0xbb, 0x07, 0x89, 0x18, 0xfb, 0xb9, 0xf8, 0xbd, 0xcf, 0x98, 0xef, 0x46, 0xe4, 0xfe, 0x5c,
	0x8a, 0x29, 0xf8, 0x2a, 0x25, 0x88, 0xc6, 0x12, 0xe8, 0x35, 0xb2, 0xe3, 0xb6, 0x70, 0x18, 0x5f,
	0x56, 0xe2, 0x45, 0x90, 0x0b, 0xe9, 0x03, 0xb8, 0xa0, 0xec, 0x4f, 0x02, 0x7a, 0x54, 0x39, 0xc4,
	0xf4, 0x69, 0x3b, 0xc3, 0x1a, 0xaf, 0x8f, 0x39, 0xbc, 0xbb, 0xe1, 0xa6, 0x67, 0x91, 0xe0, 0x8f,
	0x2f, 0xd6, 0xf3, 0x65, 0x31, 0x41, 0x61, 0xd0, 0x98, 0x2d, 0xe3, 0x66, 0x57, 0x9c, 0x2f, 0xcc,
	0x8e, 0x5d, 0x80, 0x52, 0xdb, 0x0a, 0x5f, 0xe1, 0xcc, 0x41, 0x37, 0xce, 0x01, 0x3c, 0x8a, 0xd5,
	0x49, 0xa2, 0x49, 0x8c, 0xcf, 0x3f, 0x1d, 0x49, 0x0f, 0xaf, 0xc2, 0x70, 0xec, 0xf0, 0x9a, 0x88,
	0x8d, 0x7c, 0x46, 0xb3, 0x1e, 0xf6, 0x3c, 0xbe, 0x24, 0x74, 0xb2, 0x1e, 0x91, 0x98, 0x00, 0x53,
	0x5b, 0xfd, 0x1f, 0xf3, 0x7e, 0x11, 0xf5, 0x0a, 0x98, 0x01, 0xe6, 0x13, 0xe3, 0x2f, 0x19, 0x2b,
	0x4e, 0x99, 0xe0, 0x33, 0xe7, 0x07, 0xcb, 0x64, 0x4a, 0x2b, 0x9e, 0x7d, 0x7a, 0x26, 0x0d, 0x75,
	0x9d, 0x49, 0x87, 0x09, 0x19, 0x7a, 0xb4, 0xf0, 0xf9, 0xf0, 0x08, 0xda, 0x0f, 0xdf, 0x72, 0x3e,
	0x7b, 0x9d, 0x26, 0x9b, 0x92, 0x60, 0x43, 0xbc, 0x12, 0x2a, 0xe7, 0xc8, 0x09, 0x58, 0xac, 0xad,
	0x54, 0xa7, 0x8f, 0xff, 0xab, 0x07, 0x91, 0xfe, 0x53, 0xa8, 0xe4, 0xf8, 0x53, 0x88, 0xd5, 0x80,
	0x4a, 0x56, 0x1e, 0xb1, 0xe3, 0x59, 0x4e, 0x27, 0xd3, 0x42, 0x37, 0x61, 0xb6, 0x4b, 0x66, 0x9c,
	0x77, 0x2e, 0xdb, 0xe9, 0xdc, 0xbc, 0x29, 0xfb, 0xc6, 0x93, 0xd8, 0x1b, 0x49, 0xd6, 0x64, 0x16,
	0xaa, 0x9b, 0x3b, 0x5b, 0xeb, 0xe6, 0xe3, 0xda, 0x93, 0xc6, 0x7a, 0xdd, 0xac, 0x35, 0xd6, 0xb7,
	0xaa, 0x03, 0x68, 0x0e, 0xa6, 0x77, 0x57, 0x36, 0xb2, 0xd0, 0xfa, 0x56, 0x55, 0x43, 0x15, 0x28,
	0x31, 0xf0, 0xee, 0xce, 0x5e, 0xa3, 0x3a, 0x68, 0x3c, 0x85, 0x4a, 0x6e, 0x96, 0x2c, 0xf2, 0x45,
	0xa5, 0xc3, 0x9c, 0x0b, 0xe1, 0x74, 0x50, 0xc0, 0x36, 0x75, 0x30, 0xbe, 0x03, 0x95, 0x20, 0xf4,
	0x8f, 0x1d, 0xcf, 0xc6, 0x66, 0xc6, 0xfb, 0x98, 0x88, 0x81, 0xb4, 0x93, 0xf1, 0xd7, 0x1a, 0xcc,
	0xb2, 0xeb, 0x48, 0x8d, 0x88, 0xd0, 0x92, 0xe3, 0x1d, 0xd0, 0xad, 0xb5, 0x0b, 0xe3, 0x42, 0xb2,
	0xb1, 0x9a, 0xdf, 0x55, 0xdf, 0x65, 0xba, 0x90, 0x33, 0xb7, 0xf8, 0x7a, 0x42, 0x45, 0x7f, 0x04,
	0x90, 0xc2, 0x15, 0xb7, 0xcc, 0x2e, 0xc3, 0x33, 0xd8, 0x6d, 0x78, 0xfe, 0x41, 0x83, 0x39, 0xc9,
	0x88, 0x24, 0x40, 0x01, 0xa0, 0x18, 0x31, 0x48, 0x5a, 0x7a, 0x06, 0x5f, 0xa4, 0x74, 0xe2, 0x93,
	0x2f, 0x85, 0xe6, 0xef, 0xea, 0x29, 0x5c, 0x5f, 0x4b, 0xee, 0xea, 0x5d, 0x9d, 0x4f, 0x0b, 0x46,
	0x8c, 0x67, 0x8f, 0xb8, 0x0d, 0x98, 0xea, 0x16, 0xfd, 0xdb, 0x89, 0xe6, 0x7d, 0xa8, 0x16, 0x84,
	0x62, 0xc0, 0x84, 0x93, 0x81, 0x09, 0xe3, 0x9a, 0x83, 0x19, 0xaf, 0xf8, 0x29, 0x9a, 0x42, 0x56,
	0x93, 0xf3, 0xa1, 0x07, 0x33, 0xb3, 0x30, 0xe2, 0x3a, 0x6d, 0x27, 0x3e, 0xa0, 0xf8, 0x0b, 0xb5,
	0xc7, 0x91, 0xd3, 0xc6, 0x26, 0x8f, 0xd9, 0xb3, 0x6d, 0x31, 0x54, 0x07, 0x0a, 0xda, 0x61, 0x10,
	0xe3, 0x2f, 0x34, 0xb8, 0xa8, 0x1e, 0x8d, 0x04, 0xe8, 0x59, 0x41, 0xed, 0x7e, 0xa0, 0x74, 0x5d,
	0x54, 0x44, 0x96, 0xec, 0x82, 0xee, 0xdd, 0x84, 0x31, 0xf1, 0x9c, 0xb9, 0x6c, 0x56, 0xd8, 0x65,
	0x73, 0x16, 0x46, 0x88, 0xed, 0x87, 0x7c, 0x69, 0x86, 0xea, 0xfc, 0xc5, 0xf8, 0x1c, 0xa6, 0xd8,
	0x35, 0x6a, 0x27, 0x88, 0x1c, 0xdf, 0xa3, 0xa3, 0xa1, 0x75, 0xa8, 0xf0, 0x0b, 0x95, 0xcf, 0x60,
	0x31, 0x7f, 0x57, 0xd5, 0x97, 0x32, 0x8e, 0x5c, 0x9f, 0x68, 0xa6, 0x2f, 0xc4, 0xf8, 0x02, 0xca,
	0x99, 0xc6, 0x5e, 0xd7, 0xb4, 0x79, 0x18, 0xa3, 0x97, 0xb2, 0x74, 0xb5, 0x47, 0xe9, 0x6b, 0xad,
	0x45, 0x71, 0x58, 0xc3, 0xb1, 0xe5, 0x0a, 0xd3, 0xc3, 0x3a, 0x3e, 0xb7, 0x5c, 0xe3, 0x6f, 0x87,
	0x60, 0x3e, 0xe7, 0x4d, 0x61, 0x17, 0x5b, 0x04, 0x33, 0x27, 0xbc, 0x7b, 0xe6, 0xe9, 0xd2, 0x0e,
	0x66, 0x97, 0xf6, 0x1d, 0x98, 0x08, 0x39, 0x96, 0x49, 0x57, 0x4e, 0xac, 0x62, 0x59, 0xc0, 0x1a,
	0x4e, 0x1b, 0x17, 0x45, 0x31, 0xfc, 0x36, 0xa2, 0x40, 0x9f, 0xc2, 0x44, 0xcb, 0x21, 0x81, 0x6b,
	0x9d, 0x70, 0x33, 0x4a, 0xef, 0x35, 0x93, 0xcb, 0xdf, 0x93, 0x52, 0x11, 0xf3, 0x59, 0xe3, 0xfd,
	0x59, 0x3c, 0xab, 0xdc, 0x4a, 0x5f, 0xa8, 0xf9, 0x26, 0x87, 0xfe, 0x6b, 0xf3, 0x00, 0xfb, 0xdc,
	0xad, 0x1e, 0x65, 0xba, 0x5e, 0xa6, 0xc0, 0x0d, 0xec, 0x33, 0x01, 0x5c, 0x81, 0xb2, 0xc8, 0x05,
	0xb0, 0x89, 0xf1, 0x54, 0x01, 0x70, 0x10, 0x9b, 0x17, 0x35, 0x99, 0x5c, 0xa9, 0x58, 0x0f, 0xc2,
	0x12, 0x06, 0x95, 0xfa, 0x84, 0x00, 0xd2, 0x3e, 0x04, 0x7d, 0x0c, 0x2c, 0x2e, 0x64, 0xba, 0x56,
	0x93, 0x6a, 0xe9, 0x04, 0x9b, 0xfa, 0x65, 0xf9, 0x99, 0x69, 0xb5, 0xf1, 0x13, 0xda, 0xad, 0x0e,
	0x07, 0xf1, 0x23, 0x8b, 0x2a, 0xd8, 0xae, 0x43, 0x6f, 0xdf, 0x6c, 0xd6, 0x15, 0x36, 0x06, 0x70,
	0x10, 0x3b, 0x35, 0x1a, 0x50, 0x4a, 0x30, 0xa9, 0x31, 0xa1, 0xeb, 0x2c, 0xae, 0xaf, 0xc7, 0x96,
	0x4b, 0x17, 0x28, 0x16, 0x5b, 0xc6, 0xae, 0xc7, 0xd2, 0xd8, 0x16, 0x97, 0xcb, 0xe4, 0x60, 0xaa,
	0xd4, 0xd9, 0xb3, 0xf1, 0xcf, 0x43, 0x70, 0x69, 0x2f, 0x7f, 0x88, 0x67, 0xb4, 0x83, 0xee, 0x75,
	0x07, 0x74, 0xd6, 0x9a, 0x44, 0x72, 0x63, 0x3d, 0x60, 0x02, 0xd5, 0xd8, 0xe1, 0xfb, 0xde, 0xe9,
	0x0e, 0x7c, 0x86, 0xa8, 0x52, 0x17, 0x2f, 0x01, 0xbc, 0xb6, 0xbc, 0xc8, 0xdc, 0x0f, 0x31, 0x39,
	0x8c, 0x63, 0xec, 0x14, 0xf2, 0x98, 0x02, 0x68, 0x73, 0x32, 0x76, 0x20, 0x74, 0xbc, 0x24, 0x20,
	0xb5, 0x80, 0x4a, 0x20, 0x66, 0x91, 0x49, 0x80, 0xc7, 0x7a, 0x62, 0x67, 0x97, 0x49, 0x60, 0x01,
	0xc6, 0x58, 0x0e, 0xc8, 0x0f, 0x45, 0x92, 0x2f, 0x7e, 0xa5, 0xc8, 0xc9, 0x22, 0xc7, 0x8e, 0x47,
	0xa9, 0x5e, 0x8e, 0xd7, 0x98, 0x2a, 0xd3, 0x35, 0x98, 0xb6, 0x5c, 0xd7, 0x24, 0xd8, 0xc5, 0x76,
	0x84, 0x5b, 0x66, 0x93, 0xba, 0xcf, 0x3c, 0xb0, 0x33, 0x65, 0xb9, 0xee, 0x9e, 0x80, 0x3f, 0x72,
	0x5a, 0x04, 0x5d, 0x07, 0xd4, 0xf1, 0x44, 0x57, 0x33, 0xd9, 0xca, 0x3c, 0xca, 0x33, 0xd5, 0xf1,
	0x78, 0xdf, 0x47, 0x62, 0x4b, 0x7f, 0x06, 0xf3, 0xdd, 0x9d, 0xe3, 0x2d, 0x54, 0xea, 0x73, 0x0b,
	0xcd, 0xe6, 0x68, 0xc6, 0x56, 0xe5, 0x2a, 0x5c, 0xee, 0xb5, 0xb6, 0x24, 0x30, 0xfe, 0x72, 0x10,
	0x2e, 0xd7, 0x3c, 0xe7, 0xff, 0xd6, 0xff, 0xf4, 0xf5, 0x37, 0xde, 0x81, 0x2b, 0x3d, 0x45, 0x45,
	0x02, 0xe3, 0x17, 0x1a, 0x9c, 0x5b, 0x73, 0x48, 0xdb, 0x21, 0x24, 0xdf, 0xed, 0xe8, 0x94, 0xcb,
	0x13, 0x3a, 0x07, 0xa3, 0xc4, 0xef, 0x84, 0x76, 0xbc, 0x71, 0xc5, 0x1b, 0xba, 0x9b, 0xd9, 0xb3,
	0xaa, 0x70, 0xbe, 0x18, 0x91, 0x99, 0x3f, 0xbe, 0xab, 0xef, 0xc0, 0xbc, 0x94, 0x0d, 0x12, 0x50,
	0x11, 0xb4, 0x78, 0x53, 0x7c, 0xab, 0x12, 0xaf, 0xc6, 0x1d, 0xb8, 0x42, 0xef, 0x7d, 0xd8, 0xf6,
	0xdb, 0x6d, 0xec, 0xb5, 0x04, 0x12, 0x3d, 0xe5, 0x9e, 0xf8, 0x56, 0x6b, 0xcb, 0x0f, 0x31, 0x35,
	0x3b, 0x5e, 0xa7, 0x1d, 0xfb, 0x30, 0x5e, 0xa7, 0x6d, 0xfc, 0x04, 0xce, 0x75, 0xdd, 0x01, 0x28,
	0x82, 0xfc, 0xfa, 0x2a, 0x77, 0x0f, 0x2e, 0x43, 0x99, 0x1f, 0x38, 0xa6, 0xeb, 0x10, 0xea, 0x1e,
	0xd0, 0x3d, 0x52, 0x62, 0xa7, 0x0e, 0x25, 0x65, 0x7c, 0xc9, 0x52, 0xaa, 0xc5, 0x11, 0x48, 0x80,
	0x76, 0xd2, 0x15, 0x67, 0xb8, 0xfc, 0xec, 0x3d, 0x9b, 0x32, 0xc6, 0xfa, 0xc1, 0xc6, 0xfa, 0x7d,
	0xad, 0x30, 0x58, 0x12, 0xe2, 0xcc, 0x14, 0x4e, 0x54, 0x58, 0x79, 0x03, 0xfa, 0x08, 0x46, 0xa8,
	0xb4, 0x79, 0x20, 0xf6, 0x0c, 0x47, 0x14, 0xc7, 0xe2, 0xea, 0x1c, 0x75, 0x42, 0x8f, 0x85, 0x82,
	0x86, 0xb8, 0xb6, 0x73, 0xc8, 0x8a, 0xeb, 0x1a, 0x5f, 0xb0, 0x6c, 0x8c, 0x84, 0x15, 0x12, 0xa0,
	0x4f, 0x60, 0x58, 0xec, 0xbe, 0xb3, 0x4f, 0x98, 0x61, 0x1a, 0xdf, 0x68, 0x30, 0xb3, 0xf7, 0xda,
	0x89, 0xec, 0x43, 0xd1, 0xa5, 0x61, 0x35, 0xe5, 0xab, 0xa6, 0x70, 0x08, 0xf2, 0xfa, 0x3c, 0xd4,
	0xad, 0xcf, 0xb3, 0x30, 0x62, 0x05, 0x41, 0x2d, 0x49, 0xee, 0xb3, 0x17, 0xa4, 0x43, 0x72, 0xd1,
	0x16, 0x1b, 0x30, 0x79, 0xa7, 0x5e, 0xe0, 0x6c, 0x91, 0x23, 0x1e, 0x08, 0xa0, 0x0c, 0x64, 0xae,
	0x33, 0x63, 0x91, 0xd5, 0xdc, 0x16, 0x21, 0xfe, 0xd7, 0xd8, 0xb5, 0xfd, 0x36, 0x36, 0xa3, 0x37,
	0x11, 0x57, 0x02, 0x5e, 0x7f, 0x32, 0x29, 0xe0, 0x8d, 0x37, 0x11, 0xf3, 0xd3, 0xe6, 0x61, 0xac,
	0xed, 0xd8, 0x66, 0xdb, 0x8f, 0x79, 0x1d, 0x6d, 0x3b, 0xf6, 0x96, 0xdf, 0x8a, 0xa9, 0xb3, 0x4d,
	0xc6, 0x79, 0xa5, 0xd4, 0x99, 0xc1, 0x67, 0x33, 0x3f, 0xa0, 0xd3, 0x1b, 0x89, 0x67, 0x7e, 0x50,
	0x6b, 0x19, 0xbf, 0x1e, 0x86, 0x99, 0x35, 0x87, 0x58, 0x41, 0x80, 0xad, 0x30, 0xb3, 0xc3, 0xe9,
	0xbd, 0x8b, 0x9f, 0xe0, 0x69, 0xb0, 0x97, 0x03, 0x6a, 0x2d, 0xf4, 0x7d, 0xa8, 0x5a, 0xf6, 0x51,
	0xc7, 0x09, 0xb1, 0xd9, 0xea, 0x84, 0x16, 0xb5, 0xcf, 0x4c, 0x9e, 0xc3, 0xf5, 0x29, 0x01, 0x5f,
	0x13, 0x60, 0xf4, 0x0c, 0x2a, 0xd4, 0xcf, 0xf0, 0x3b, 0x91, 0x89, 0x8f, 0xb1, 0x17, 0x2d, 0x00,
	0xb3, 0xb1, 0xb7, 0x54, 0x7b, 0xbf, 0x9b, 0x91, 0xa5, 0x06, 0xc7, 0xae, 0x4f, 0x08, 0x32, 0xeb,
	0x94, 0x0a, 0x7a, 0x09, 0x53, 0xaf, 0x30, 0x0e, 0x2c, 0xd7, 0x39, 0xc6, 0x82, 0x70, 0x99, 0x11,
	0x5e, 0xee, 0x9b, 0xf0, 0x8f, 0x62, 0xfc, 0xfa, 0x64, 0x42, 0x8a, 0x13, 0x7f, 0x05, 0x73, 0x59,
	0xf7, 0x30, 0xe5, 0x7d, 0x42, 0xe4, 0x9b, 0xfb, 0x1d, 0xa2, 0x9e, 0x3a, 0x94, 0x74, 0x0a, 0x33,
	0x61, 0xee, 0x9d, 0x0d, 0xa6, 0xdf, 0x85, 0x31, 0xf1, 0x4e, 0xc5, 0x1a, 0x8f, 0x97, 0x88, 0x55,
	0xe3, 0x62, 0x15, 0xf0, 0x58, 0xac, 0xfa, 0x6f, 0x41, 0x29, 0xe1, 0x1f, 0xdd, 0x00, 0x94, 0xbc,
	0x74, 0x63, 0x4e, 0x27, 0x2d, 0xc9, 0x92, 0xbc, 0x03, 0x13, 0x6d, 0xdc, 0x6e, 0xe2, 0xd0, 0xe4,
	0x15, 0x42, 0x7c, 0x27, 0x94, 0x39, 0x6c, 0x95, 0x82, 0xf4, 0x87, 0x30, 0x99, 0xe7, 0xfd, 0x0c,
	0xbc, 0x19, 0xf7, 0x61, 0xb6, 0x28, 0x0c, 0x12, 0x9c, 0x1e, 0xab, 0xfb, 0x21, 0x54, 0x37, 0x70,
	0xb4, 0xe3, 0xb9, 0x8e, 0x97, 0x1c, 0xd8, 0xd7, 0x60, 0xda, 0x67, 0x00, 0xb3, 0x43, 0x12, 0x8e,
	0xf9, 0x86, 0x9e, 0xe2, 0x0d, 0xcf, 0x88, 0xe0, 0xda, 0xf8, 0x02, 0xa6, 0xbb, 0xf0, 0x79, 0x9c,
	0x2f, 0xf4, 0xfd, 0x76, 0x0e, 0xb3, 0x44, 0x21, 0x0c, 0x87, 0x6e, 0xba, 0x2c, 0xfd, 0x64, 0xd3,
	0x55, 0xea, 0x93, 0x29, 0x79, 0x66, 0x4e, 0x5f, 0x42, 0xf5, 0x71, 0x88, 0xf1, 0x4f, 0x71, 0x66,
	0x97, 0xbc, 0x0b, 0x53, 0xe9, 0x94, 0x52, 0xb3, 0x5d, 0xa9, 0x57, 0x92, 0x69, 0xb1, 0x0d, 0x7b,
	0x05, 0xca, 0xfb, 0x0c, 0x97, 0xbb, 0xe5, 0xfc, 0x1e, 0x06, 0x1c, 0x44, 0x25, 0x6c, 0xcc, 0xc0,
	0x74, 0x17, 0x71, 0x12, 0x18, 0x1f, 0x02, 0x7a, 0xe6, 0xed, 0xbf, 0xe5, 0x98, 0xc6, 0x1c, 0xcc,
	0x14, 0xb0, 0x49, 0x60, 0x7c, 0xc0, 0x0e, 0x85, 0x38, 0xc4, 0xc5, 0x9a, 0x63, 0x59, 0x9f, 0x12,
	0x12, 0x7d, 0xc8, 0x6c, 0xb8, 0x04, 0x93, 0xaf, 0x6d, 0x76, 0x82, 0x5a, 0x61, 0x82, 0x7f, 0xa2,
	0x01, 0xda, 0xc3, 0xd1, 0xfa, 0x9b, 0x28, 0xb4, 0x36, 0x1d, 0x12, 0xf9, 0xe1, 0x49, 0x1f, 0x8e,
	0xc4, 0x2a, 0x4c, 0x1c, 0xf2, 0xce, 0x69, 0x10, 0x50, 0xe5, 0x38, 0x08, 0xaa, 0xfc, 0xde, 0x74,
	0x98, 0xbe, 0x50, 0x7d, 0xc7, 0x6f, 0x02, 0x6a, 0xac, 0xac, 0xfd, 0x08, 0x87, 0xf1, 0x6d, 0x8f,
	0xc3, 0x56, 0x28, 0x88, 0xca, 0xaa, 0xc0, 0x1c, 0x09, 0x8c, 0x37, 0x80, 0x36, 0x7e, 0x23, 0x3c,
	0x1b, 0xd7, 0x61, 0x66, 0xa3, 0xc8, 0x50, 0x1a, 0x64, 0x11, 0x71, 0x3d, 0xf6, 0x62, 0xdc, 0x67,
	0x6e, 0x4b, 0x1c, 0xa9, 0x71, 0xad, 0x93, 0x2d, 0xbf, 0xd5, 0x8f, 0x9f, 0x26, 0x0a, 0xbc, 0x8a,
	0x88, 0x24, 0x50, 0x95, 0x48, 0xdc, 0x65, 0xd1, 0xe3, 0x58, 0x9f, 0x7c, 0xbf, 0x4d, 0xb7, 0xc7,
	0x76, 0x87, 0x5a, 0x8e, 0xee, 0x50, 0xca, 0x50, 0x8a, 0xf5, 0x4f, 0x1a, 0x5c, 0x54, 0xa3, 0x91,
	0x00, 0xb5, 0x60, 0x92, 0x6d, 0x52, 0xb6, 0x07, 0x33, 0xce, 0x80, 0x32, 0x4e, 0xab, 0x24, 0xb5,
	0x14, 0x83, 0x98, 0x6a, 0x4e, 0x84, 0x99, 0x37, 0xfd, 0x29, 0x4c, 0x64, 0x5b, 0x55, 0x81, 0x9f,
	0x6b, 0x30, 0x1d, 0xf9, 0x91, 0xe5, 0x72, 0x6e, 0x3c, 0x46, 0x5a, 0x6c, 0xd9, 0x29, 0xd6, 0x90,
	0x8e, 0x48, 0x3d, 0xc6, 0x95, 0x56, 0xab, 0x81, 0xdb, 0x81, 0x1f, 0x5a, 0xe1, 0x49, 0x66, 0x9b,
	0x3e, 0x4e, 0xe2, 0x31, 0x6f, 0x75, 0xad, 0x88, 0x91, 0x8d, 0xf3, 0x30, 0x2f, 0x1d, 0x81, 0x04,
	0xc6, 0x7f, 0x69, 0x70, 0x91, 0x97, 0x64, 0x2a, 0xaa, 0x96, 0xe8, 0x1d, 0x39, 0x4a, 0xa6, 0xc7,
	0x9e, 0xd1, 0x56, 0x52, 0x10, 0xc8, 0x15, 0xf3, 0x9e, 0x42, 0x31, 0xbb, 0x09, 0x52, 0xae, 0x78,
	0x7d, 0x20, 0xd3, 0x56, 0x41, 0x84, 0x0e, 0x11, 0x58, 0x07, 0xc9, 0x35, 0x9c, 0x3e, 0xa7, 0xae,
	0xf1, 0x70, 0xd6, 0x35, 0x4e, 0xa5, 0x3d, 0xd2, 0x15, 0x8b, 0x89, 0x2b, 0x90, 0x92, 0xbb, 0xca,
	0x48, 0xbd, 0x7c, 0x94, 0x56, 0xf5, 0x50, 0x77, 0xe7, 0xd8, 0xc1, 0xaf, 0x29, 0xea, 0x18, 0xbf,
	0x4f, 0xd0, 0xd7, 0x5a, 0xcb, 0xf8, 0x8a, 0x5e, 0xf7, 0x95, 0xf3, 0x27, 0x01, 0xda, 0x84, 0x12,
	0x4b, 0xc6, 0x66, 0x1c, 0xea, 0xeb, 0x67, 0x98, 0x6f, 0x7d, 0x9c, 0x62, 0x33, 0x0b, 0x9e, 0xd4,
	0xd3, 0x0e, 0x66, 0xea, 0x69, 0x0d, 0x13, 0x2e, 0x3e, 0x63, 0xb1, 0x15, 0xc5, 0x02, 0x7c, 0x9c,
	0x73, 0x6d, 0xcf, 0x34, 0x34, 0xf7, 0x6c, 0x6f, 0xc3, 0xa5, 0x1e, 0x03, 0x90, 0x40, 0x52, 0x01,
	0xfd, 0xab, 0x21, 0x38, 0x27, 0xa7, 0x29, 0x4f, 0xc2, 0x31, 0x0d, 0x19, 0xcc, 0x68, 0x88, 0xac,
	0xfc, 0xe4, 0x12, 0x40, 0x1c, 0x90, 0x49, 0x6a, 0x5c, 0x4b, 0x02, 0x52, 0xf0, 0x9f, 0x47, 0xba,
	0x4d, 0xe2, 0x25, 0x80, 0x26, 0x3e, 0x70, 0x3c, 0x7e, 0x38, 0x8c, 0xb2, 0xad, 0x54, 0x62, 0x10,
	0x16, 0x93, 0x3a, 0x0f, 0xe3, 0xd8, 0x6b, 0x65, 0x23, 0x56, 0x63, 0xd8, 0x6b, 0xb1, 0xa6, 0xf7,
	0x00, 0xc5, 0xee, 0x06, 0x6b, 0x37, 0x0f, 0xfd, 0x4e, 0x28, 0x62, 0x56, 0xd5, 0xb8, 0x85, 0xf6,
	0xdc, 0xf4, 0x3b, 0x61, 0x46, 0xb7, 0x4b, 0xff, 0x13, 0xba, 0x9d, 0x6a, 0x2c, 0x64, 0x35, 0x96,
	0x07, 0x19, 0xcb, 0x49, 0x2d, 0xcf, 0x6a, 0x97, 0x06, 0x4f, 0xf4, 0x59, 0xac, 0xa6, 0xd2, 0xf1,
	0x4a, 0x4e, 0xc7, 0x7f, 0xad, 0xc1, 0x25, 0x11, 0xc7, 0x57, 0x28, 0x59, 0x5b, 0x55, 0x9b, 0xb8,
	0xde, 0x2b, 0x29, 0xf0, 0x1b, 0xac, 0x4e, 0xfc, 0x4f, 0x0d, 0x2e, 0xf7, 0xe2, 0x82, 0x04, 0xe8,
	0x6b, 0x0d, 0x16, 0xe2, 0x09, 0x1d, 0x3a, 0x07, 0x87, 0x92, 0x2a, 0xc5, 0xed, 0x33, 0xcf, 0x2e,
	0xcd, 0x7d, 0x64, 0x9a, 0x93, 0x69, 0xce, 0xd9, 0xb2, 0x36, 0x7d, 0x13, 0x74, 0x35, 0xd2, 0x99,
	0xf2, 0x20, 0x9f, 0xc3, 0x79, 0xc6, 0xdd, 0x1e, 0xa6, 0x17, 0x7e, 0x16, 0x11, 0x59, 0xf5, 0xbd,
	0x7d, 0x87, 0x65, 0x44, 0x1e, 0xe6, 0x2c, 0x85, 0xfc, 0xf6, 0x9d, 0xc7, 0xca, 0x58, 0x89, 0x8b,
	0xa0, 0xab, 0x28, 0x93, 0xc0, 0xf8, 0x03, 0x8d, 0xe7, 0xc0, 0x73, 0x2d, 0x44, 0x7e, 0x3f, 0xee,
	0x9d, 0x81, 0x61, 0xe6, 0x02, 0xbf, 0x49, 0x4a, 0xdf, 0xe9, 0x33, 0x3a, 0x07, 0xa3, 0x22, 0x19,
	0xc2, 0xcd, 0x82, 0x78, 0x4b, 0x4f, 0x81, 0x91, 0xcc, 0x29, 0x60, 0xbc, 0x62, 0xbe, 0x4a, 0x81,
	0x17, 0x12, 0x7c, 0x2b, 0x09, 0xb0, 0x84, 0x27, 0x3d, 0x9a, 0x93, 0x6b, 0x3d, 0x7d, 0x31, 0xfe,
	0x58, 0x03, 0x54, 0x44, 0x29, 0x14, 0xe3, 0x09, 0x31, 0x0c, 0xaa, 0xc4, 0x50, 0x88, 0x07, 0x9c,
	0x87, 0xf1, 0x2f, 0x3b, 0xed, 0xc0, 0xec, 0x84, 0xae, 0x08, 0xcc, 0x8d, 0xd1, 0xf7, 0x67, 0xa1,
	0x9b, 0xd4, 0x3c, 0x8d, 0x64, 0x6a, 0x9e, 0x62, 0xa9, 0x8d, 0xa6, 0x52, 0x33, 0xae, 0x0b, 0x65,
	0x58, 0x63, 0x67, 0x79, 0x97, 0x32, 0xc4, 0x0c, 0x0e, 0x71, 0x06, 0x93, 0xf5, 0x95, 0x74, 0x26,
	0x81, 0xf1, 0xb5, 0x06, 0x86, 0xa8, 0x73, 0xc1, 0x7b, 0xbe, 0xed, 0x58, 0xee, 0xaa, 0xdf, 0x6e,
	0x77, 0xbc, 0x2e, 0x33, 0xb1, 0x04, 0x33, 0x84, 0xb5, 0x9a, 0x76, 0xdc, 0x9c, 0xde, 0xed, 0xa7,
	0x49, 0x1e, 0xb1, 0x26, 0x93, 0x4a, 0xd7, 0x05, 0x6e, 0xa8, 0x70, 0x81, 0xfb, 0x66, 0x10, 0xbe,
	0x73, 0x2a, 0x27, 0x24, 0x40, 0xbe, 0xbc, 0x84, 0xe7, 0xd3, 0x5e, 0x25, 0x3c, 0xbd, 0x08, 0x9e,
	0x5a, 0xcc, 0xf3, 0xba, 0xbf, 0x62, 0x9e, 0x27, 0xf9, 0x62, 0x9e, 0xf7, 0x7b, 0x14, 0xf3, 0x28,
	0xd8, 0xe9, 0xae, 0xad, 0x7a, 0xc9, 0xfc, 0x65, 0x91, 0x20, 0xf7, 0xf6, 0xfd, 0x47, 0x27, 0x49,
	0x51, 0xec, 0xe9, 0x95, 0x86, 0xb4, 0xd9, 0xc3, 0xb8, 0x65, 0xda, 0x87, 0xd8, 0x7e, 0x15, 0x87,
	0x90, 0x29, 0x64, 0x95, 0x02, 0x8c, 0x4f, 0x60, 0xba, 0xab, 0xb8, 0xe7, 0xf9, 0x72, 0x72, 0x7a,
	0x6b, 0x99, 0xd3, 0x7b, 0x1e, 0xc6, 0xf8, 0x41, 0x46, 0xc4, 0xdd, 0x76, 0x94, 0x9d, 0x64, 0xc4,
	0xf8, 0x37, 0xee, 0x98, 0x2b, 0xf8, 0x23, 0x01, 0x32, 0x25, 0xb5, 0x8c, 0x9f, 0xa8, 0x9c, 0x72,
	0x25, 0x99, 0x1e, 0xc5, 0x8c, 0xad, 0x3e, 0x8a, 0x19, 0x3f, 0xcc, 0x2f, 0xcb, 0xbb, 0xfd, 0x94,
	0x39, 0x3d, 0x5f, 0xce, 0x2e, 0xc3, 0x47, 0xb9, 0x69, 0x12, 0x1c, 0x46, 0xf9, 0x14, 0xf0, 0x29,
	0x15, 0x9f, 0x7f, 0x33, 0x08, 0x97, 0x7a, 0xe0, 0x93, 0x00, 0xfd, 0x44, 0x22, 0xa7, 0x95, 0xd3,
	0xe4, 0x54, 0xa4, 0xd3, 0x43, 0x50, 0x77, 0xa0, 0xda, 0x8d, 0x71, 0x6a, 0x44, 0x45, 0xff, 0xb9,
	0xd6, 0x87, 0x78, 0x5f, 0xe4, 0xc5, 0xbb, 0xfa, 0x16, 0x6c, 0x17, 0x80, 0x19, 0xd9, 0xdf, 0x83,
	0xd9, 0x58, 0x19, 0x1a, 0x3e, 0x35, 0x5b, 0x22, 0x38, 0x7b, 0x8a, 0xcc, 0xff, 0x70, 0x10, 0xe6,
	0x24, 0x78, 0x2c, 0x50, 0x5e, 0xb2, 0x7d, 0x6f, 0xdf, 0x64, 0x55, 0xce, 0x5c, 0xd4, 0xcb, 0xbd,
	0xaa, 0xb2, 0xf3, 0xe8, 0x4b, 0xf1, 0xf3, 0xb8, 0xcd, 0x1f, 0x88, 0xfe, 0xb0, 0x8f, 0x82, 0x62,
	0x55, 0xa6, 0x5a, 0xff, 0x12, 0xc6, 0x04, 0x45, 0xd5, 0x7d, 0x72, 0xb3, 0xab, 0xc6, 0xf9, 0xd6,
	0x19, 0x98, 0xcd, 0x55, 0x3e, 0x5f, 0xfb, 0x00, 0x2a, 0xb9, 0x2f, 0xb5, 0x50, 0x05, 0x4a, 0x1d,
	0xaf, 0x85, 0xf7, 0x1d, 0x0f, 0xb7, 0xaa, 0x03, 0x08, 0x60, 0xd4, 0xf1, 0xe8, 0xb5, 0xb5, 0xaa,
	0xa1, 0x09, 0x18, 0x77, 0x3c, 0x33, 0xb0, 0xe8, 0xdb, 0xe0, 0xb5, 0x5b, 0x50, 0xce, 0xb8, 0x9e,
	0xa8, 0x0a, 0x13, 0xd4, 0x9b, 0x31, 0x05, 0xac, 0x3a, 0x80, 0xa6, 0x92, 0x0e, 0xe6, 0xa6, 0x1f,
	0x55, 0xb5, 0x6b, 0x4f, 0x63, 0xcd, 0x49, 0x0a, 0x95, 0xae, 0xc0, 0x05, 0x51, 0xbc, 0xb3, 0xd2,
	0x68, 0xd4, 0xcd, 0xc6, 0x8b, 0xdd, 0x75, 0xf3, 0xd9, 0xf6, 0xfa, 0xe7, 0xbb, 0xeb, 0xab, 0x8d,
	0xf5, 0xb5, 0xea, 0x00, 0xba, 0x0c, 0x7a, 0xa1, 0xc3, 0xde, 0xca, 0xd6, 0xba, 0xb9, 0x5a, 0x6b,
	0xbc, 0xa8, 0x6a, 0xd7, 0x0e, 0x01, 0x15, 0x13, 0x08, 0x68, 0x01, 0x66, 0xd7, 0x6a, 0x7b, 0xbb,
	0x4f, 0x56, 0x5e, 0x98, 0x2b, 0x0d, 0x73, 0x6b, 0xa5, 0xb6, 0x6d, 0xee, 0xae, 0x6c, 0xac, 0x57,
	0x07, 0xe8, 0xec, 0xd6, 0x6a, 0x7b, 0x5b, 0xb5, 0xbd, 0xbd, 0xf5, 0xb5, 0xaa, 0x86, 0x74, 0x38,
	0x97, 0xe9, 0xf8, 0xb8, 0xb6, 0xbd, 0x66, 0x3e, 0xae, 0xd7, 0xd6, 0xb7, 0xd7, 0xaa, 0x83, 0xb4,
	0x6b, 0x63, 0x7d, 0x6b, 0x77, 0xa7, 0xbe, 0x52, 0x7f, 0x51, 0x1d, 0xba, 0xf6, 0x29, 0x94, 0x33,
	0x79, 0x24, 0x54, 0x86, 0xb1, 0x67, 0xde, 0x2b, 0xcf, 0x7f, 0xed, 0x55, 0x07, 0x50, 0x09, 0x46,
	0xf6, 0xf0, 0x31, 0x0e, 0xab, 0x1a, 0x95, 0xd7, 0xaa, 0xe5, 0xd9, 0xd8, 0xe5, 0x14, 0x78, 0xc0,
	0xbe, 0x61, 0x35, 0xab, 0x43, 0x68, 0x1c, 0x86, 0x9f, 0x76, 0x9c, 0xa8, 0x3a, 0x7c, 0xed, 0x0e,
	0x94, 0x33, 0x61, 0x1a, 0x34, 0x0d, 0x95, 0x55, 0xf6, 0xe5, 0x9d, 0x00, 0x56, 0x07, 0x28, 0x28,
	0xbe, 0xa4, 0x71, 0x90, 0x76, 0xed, 0x73, 0xb8, 0x7a, 0xda, 0x35, 0x83, 0x71, 0xb5, 0xfd, 0xa3,
	0xed, 0x9d, 0xcf, 0xb6, 0xab, 0x03, 0x68, 0x12, 0x60, 0x7b, 0xa7, 0x61, 0xae, 0xac, 0x36, 0x6a,
	0xcf, 0xd7, 0x39, 0x6b, 0xe2, 0x79, 0x90, 0x76, 0xac, 0x6d, 0x3f, 0x5f, 0x79, 0x52, 0x5b, 0xab,
	0x0e, 0x2d, 0x7f, 0xb3, 0x08, 0x13, 0xd9, 0xa0, 0x01, 0xfa, 0x85, 0x06, 0xba, 0x3a, 0x31, 0x8a,
	0xe4, 0x5b, 0xa3, 0x67, 0x96, 0x5c, 0xbf, 0x73, 0x66, 0x1c, 0x12, 0xa0, 0xdf, 0xd3, 0xe0, 0x42,
	0x8f, 0x94, 0x22, 0x92, 0x13, 0xed, 0x9d, 0xaf, 0xd5, 0xef, 0x9e, 0x1d, 0x89, 0x55, 0x4a, 0xcd,
	0x48, 0x32, 0x86, 0xe8, 0x7a, 0xaf, 0x84, 0x63, 0x57, 0x8a, 0x53, 0x7f, 0xaf, 0xff, 0xce, 0x7c,
	0x44, 0x49, 0x5e, 0x4f, 0x31, 0xa2, 0x3c, 0xc7, 0xa8, 0x18, 0x51, 0x95, 0x2e, 0x24, 0xac, 0x22,
	0xb6, 0x90, 0x51, 0x43, 0x7d, 0x51, 0x89, 0xf3, 0x80, 0xfa, 0x8d, 0x33, 0xf4, 0x66, 0x9f, 0x49,
	0x56, 0xbb, 0x43, 0xfb, 0x68, 0xb1, 0xdf, 0x74, 0x88, 0xfe, 0xfd, 0x3e, 0x7b, 0xb2, 0x32, 0xd4,
	0x4a, 0x2e, 0x94, 0x8f, 0xbe, 0xab, 0x62, 0x34, 0x97, 0x2e, 0xd0, 0xdf, 0xed, 0xa7, 0x1b, 0x9f,
	0x48, 0x77, 0x7a, 0x4e, 0x31, 0x11, 0x49, 0x5e, 0x51, 0x31, 0x11, 0x69, 0xbe, 0xef, 0xc7, 0x50,
	0xc9, 0x05, 0xf6, 0x15, 0x13, 0xe9, 0xce, 0x2c, 0x28, 0x26, 0x52, 0xc8, 0x11, 0xa0, 0x16, 0x4c,
	0x75, 0x45, 0xf9, 0x91, 0xfc, 0xea, 0x54, 0xcc, 0x24, 0xe8, 0x8b, 0xfd, 0x75, 0x4c, 0x94, 0xad,
	0x10, 0xfa, 0x57, 0x2b, 0x9b, 0x2c, 0xbf, 0xa0, 0x56, 0x36, 0x79, 0x4e, 0xe1, 0xab, 0x6c, 0xbe,
	0x21, 0x1f, 0xe9, 0x45, 0xb7, 0xce, 0x18, 0x18, 0x3e, 0xd2, 0x6f, 0x9f, 0x39, 0x94, 0x4c, 0x37,
	0xb5, 0x24, 0xf4, 0xaa, 0xd8, 0xd4, 0xf2, 0x30, 0xb0, 0x62, 0x53, 0x2b, 0x22, 0xba, 0x74, 0x35,
	0xbb, 0xf2, 0x10, 0x8a, 0xd5, 0x2c, 0xa6, 0x52, 0x14, 0xab, 0x29, 0x49, 0x6b, 0xd0, 0x51, 0x36,
	0xfa, 0x1a, 0x65, 0xa3, 0xdf, 0x51, 0x64, 0xb9, 0x8a, 0xaf, 0xf8, 0x57, 0x25, 0xb2, 0x12, 0x46,
	0xc5, 0xf2, 0xf5, 0x28, 0xd2, 0x54, 0x2c, 0x5f, 0xcf, 0x42, 0xcb, 0x97, 0x30, 0x91, 0x2d, 0x17,
	0x45, 0xf2, 0xcf, 0x23, 0xba, 0x4a, 0x53, 0xf5, 0xef, 0xf6, 0xd1, 0x8b, 0x04, 0xe8, 0x4b, 0x98,
	0x2e, 0x54, 0xd7, 0xa2, 0xef, 0xf7, 0x5d, 0x3f, 0xac, 0x5f, 0xeb, 0xbf, 0x60, 0x17, 0xfd, 0xac,
	0x50, 0xc7, 0x11, 0x97, 0xa6, 0xa3, 0x9b, 0x67, 0x2b, 0x64, 0x3f, 0xd2, 0x6f, 0x9d, 0xb5, 0xf2,
	0x9d, 0x8e, 0xae, 0xf8, 0xb2, 0x41, 0x31, 0xba, 0xfa, 0xbb, 0x09, 0xc5, 0xe8, 0xbd, 0x3e, 0x9c,
	0xf8, 0x5d, 0x0d, 0xce, 0x2b, 0x63, 0xfc, 0xe8, 0xb6, 0x42, 0xe7, 0xd5, 0x39, 0x11, 0x7d, 0xf9,
	0xac, 0x28, 0x24, 0x30, 0x06, 0x18, 0x1b, 0xca, 0x40, 0xbc, 0x82, 0x8d, 0x5e, 0x99, 0x01, 0x05,
	0x1b, 0x3d, 0x63, 0xfd, 0xc6, 0x00, 0xfa, 0x5a, 0x13, 0x91, 0x20, 0x69, 0x84, 0x53, 0xe1, 0xeb,
	0xf5, 0x0c, 0xf8, 0x2a, 0x7c, 0xbd, 0xde, 0x61, 0x54, 0x63, 0x00, 0xfd, 0x5c, 0x83, 0x05, 0xd5,
	0x17, 0xe1, 0x8a, 0xed, 0xdd, 0xe3, 0xa3, 0x78, 0xc5, 0xf6, 0xee, 0xf5, 0xc9, 0xb9, 0x31, 0x80,
	0x5c, 0x96, 0xef, 0xcf, 0x7f, 0xdb, 0xa3, 0xd8, 0x83, 0xb2, 0x8f, 0x87, 0x14, 0x7b, 0x50, 0xfa,
	0xb9, 0x10, 0x1f, 0xad, 0xf0, 0x6d, 0x92, 0x62, 0x34, 0xd9, 0x17, 0x50, 0x8a, 0xd1, 0xe4, 0x9f,
	0x3b, 0xc5, 0x73, 0xeb, 0x6b, 0xb4, 0x8d, 0xfe, 0x47, 0xdb, 0x50, 0x8c, 0x96, 0xb1, 0x30, 0x5d,
	0xdf, 0xa2, 0x9e, 0x62, 0x61, 0x8a, 0x5f, 0xcf, 0x9e, 0x62, 0x61, 0x64, 0x1f, 0xcc, 0xfe, 0x0c,
	0xe6, 0x15, 0x5f, 0xb9, 0x29, 0x46, 0x57, 0x7f, 0x31, 0xa7, 0xdf, 0x3a, 0x1b, 0x02, 0x09, 0x50,
	0xc4, 0x5c, 0xf7, 0xcc, 0xb7, 0xea, 0xec, 0x9b, 0x7f, 0x74, 0xe3, 0x94, 0x30, 0x48, 0xfe, 0x97,
	0x02, 0xfa, 0xd2, 0x59, 0xba, 0xf3, 0xd3, 0x51, 0xf5, 0x8d, 0x7c, 0xaf, 0xed, 0x23, 0xff, 0xbd,
	0x40, 0xaf, 0xed, 0xa3, 0xfa, 0xa7, 0xc0, 0x31, 0xcc, 0x49, 0x3f, 0x8e, 0x54, 0x4c, 0x5c, 0xf5,
	0x81, 0xa8, 0x62, 0xe2, 0xea, 0xcf, 0x37, 0xc5, 0x62, 0x4b, 0xfe, 0x9f, 0xd2, 0x63, 0xb1, 0xe5,
	0xbf, 0x7d, 0xe9, 0xb1, 0xd8, 0xaa, 0xff, 0xbc, 0xfc, 0x52, 0x83, 0x4b, 0x3d, 0x7f, 0x49, 0x82,
	0xee, 0xbd, 0xcd, 0x6f, 0x4c, 0x8e, 0xf4, 0xf7, 0xdf, 0xee, 0xef, 0x27, 0xc2, 0xb3, 0x2e, 0xfe,
	0x88, 0x48, 0xe9, 0x59, 0xcb, 0x7e, 0xc7, 0xa3, 0xf6, 0xac, 0xe5, 0xbf, 0xe2, 0x21, 0x30, 0x2b,
	0xfb, 0x2f, 0x92, 0x62, 0x50, 0xc5, 0xbf, 0x99, 0x14, 0x83, 0xaa, 0x7e, 0xb8, 0x44, 0x07, 0x95,
	0xfd, 0x3f, 0x4a, 0x31, 0xa8, 0xe2, 0x7f, 0x5a, 0x8a, 0x41, 0x95, 0x3f, 0xa6, 0x6a, 0x03, 0x2a,
	0xfe, 0xb4, 0x0b, 0x29, 0x4c, 0xb1, 0xec, 0xf7, 0x60, 0xfa, 0xf5, 0xbe, 0xfb, 0x72, 0xaf, 0xb0,
	0xf0, 0xf7, 0x31, 0x85, 0xd5, 0x96, 0xfd, 0xcf, 0x4c, 0x61, 0xb5, 0xa5, 0x3f, 0x34, 0x43, 0x7f,
	0xa4, 0xc1, 0x95, 0x53, 0x72, 0x23, 0xe8, 0xfe, 0xdb, 0x65, 0x54, 0x8e, 0xf4, 0x0f, 0xde, 0x36,
	0x15, 0x23, 0x6e, 0x6d, 0xd2, 0x54, 0x80, 0xfa, 0xd6, 0xa6, 0x4a, 0x90, 0xa8, 0x6f, 0x6d, 0xea,
	0x94, 0xc5, 0xef, 0x68, 0xd9, 0xdf, 0xb8, 0x74, 0x87, 0xcc, 0x6f, 0x9f, 0x35, 0xba, 0xad, 0x72,
	0xd5, 0x7a, 0xe7, 0x03, 0xbe, 0x84, 0xe9, 0x42, 0x40, 0x57, 0xa1, 0x08, 0xb2, 0xe0, 0xb8, 0x42,
	0x11, 0xa4, 0x31, 0xe2, 0x47, 0xf7, 0xff, 0xff, 0xbd, 0x03, 0xdf, 0xb5, 0xbc, 0x83, 0xa5, 0x7b,
	0xcb, 0x51, 0xb4, 0x64, 0xfb, 0xed, 0x9b, 0xec, 0xaf, 0x79, 0xb6, 0xef, 0xde, 0x24, 0x38, 0x3c,
	0x76, 0x6c, 0x4c, 0x64, 0x3f, 0xef, 0x6b, 0x8e, 0xb2, 0x6e, 0x77, 0xfe, 0x3b, 0x00, 0x00, 0xff,
	0xff, 0xad, 0xd8, 0xf2, 0xf0, 0x41, 0x50, 0x00, 0x00,
}
