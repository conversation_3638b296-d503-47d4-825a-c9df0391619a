// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channelbox/channelbox.proto

package channelbox // import "golang.52tt.com/protocol/services/channelbox"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BoxType int32

const (
	BoxType_BoxTypeRoleplay BoxType = 0
	BoxType_BoxTypeMelee    BoxType = 1
)

var BoxType_name = map[int32]string{
	0: "BoxTypeRoleplay",
	1: "BoxTypeMelee",
}
var BoxType_value = map[string]int32{
	"BoxTypeRoleplay": 0,
	"BoxTypeMelee":    1,
}

func (x BoxType) String() string {
	return proto.EnumName(BoxType_name, int32(x))
}
func (BoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{0}
}

type EnterResultBoxType int32

const (
	EnterResultBoxType_EnterType  EnterResultBoxType = 0
	EnterResultBoxType_SwitchType EnterResultBoxType = 1
)

var EnterResultBoxType_name = map[int32]string{
	0: "EnterType",
	1: "SwitchType",
}
var EnterResultBoxType_value = map[string]int32{
	"EnterType":  0,
	"SwitchType": 1,
}

func (x EnterResultBoxType) String() string {
	return proto.EnumName(EnterResultBoxType_name, int32(x))
}
func (EnterResultBoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{1}
}

type JoinBoxType int32

const (
	JoinBoxType_NoneType   JoinBoxType = 0
	JoinBoxType_AcceptType JoinBoxType = 1
	JoinBoxType_RejectType JoinBoxType = 2
)

var JoinBoxType_name = map[int32]string{
	0: "NoneType",
	1: "AcceptType",
	2: "RejectType",
}
var JoinBoxType_value = map[string]int32{
	"NoneType":   0,
	"AcceptType": 1,
	"RejectType": 2,
}

func (x JoinBoxType) String() string {
	return proto.EnumName(JoinBoxType_name, int32(x))
}
func (JoinBoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{2}
}

type MicType int32

const (
	// 自由麦
	MicType_MicTypeFree MicType = 0
	// 公共麦
	MicType_MicTypePublic MicType = 1
)

var MicType_name = map[int32]string{
	0: "MicTypeFree",
	1: "MicTypePublic",
}
var MicType_value = map[string]int32{
	"MicTypeFree":   0,
	"MicTypePublic": 1,
}

func (x MicType) String() string {
	return proto.EnumName(MicType_name, int32(x))
}
func (MicType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{3}
}

// 麦位状态
type MicStatus int32

const (
	// 申请中
	MicStatus_MicStatusApplying MicStatus = 0
	// 试用中
	MicStatus_MicStatusUsing MicStatus = 1
)

var MicStatus_name = map[int32]string{
	0: "MicStatusApplying",
	1: "MicStatusUsing",
}
var MicStatus_value = map[string]int32{
	"MicStatusApplying": 0,
	"MicStatusUsing":    1,
}

func (x MicStatus) String() string {
	return proto.EnumName(MicStatus_name, int32(x))
}
func (MicStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{4}
}

// 1000以内系统频道ID
type SystemBoxID int32

const (
	SystemBoxID_SystemBoxIDInvalid SystemBoxID = 0
	// 子频道公共频道
	SystemBoxID_SystemBoxIDSubChannel SystemBoxID = 2
	// 主房间公共频道
	SystemBoxID_SystemBoxIDMainChannel SystemBoxID = 3
	// 广播频道
	SystemBoxID_SystemBoxIDBroadcast SystemBoxID = 4
)

var SystemBoxID_name = map[int32]string{
	0: "SystemBoxIDInvalid",
	2: "SystemBoxIDSubChannel",
	3: "SystemBoxIDMainChannel",
	4: "SystemBoxIDBroadcast",
}
var SystemBoxID_value = map[string]int32{
	"SystemBoxIDInvalid":     0,
	"SystemBoxIDSubChannel":  2,
	"SystemBoxIDMainChannel": 3,
	"SystemBoxIDBroadcast":   4,
}

func (x SystemBoxID) String() string {
	return proto.EnumName(SystemBoxID_name, int32(x))
}
func (SystemBoxID) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{5}
}

type BoxInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BoxId                uint32   `protobuf:"varint,2,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	CreatorUid           uint32   `protobuf:"varint,3,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	BoxName              string   `protobuf:"bytes,5,opt,name=box_name,json=boxName,proto3" json:"box_name,omitempty"`
	MicCap               uint32   `protobuf:"varint,6,opt,name=mic_cap,json=micCap,proto3" json:"mic_cap,omitempty"`
	PublicMicList        []uint32 `protobuf:"varint,7,rep,packed,name=public_mic_list,json=publicMicList,proto3" json:"public_mic_list,omitempty"`
	NormalMicList        []uint32 `protobuf:"varint,8,rep,packed,name=normal_mic_list,json=normalMicList,proto3" json:"normal_mic_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxInfo) Reset()         { *m = BoxInfo{} }
func (m *BoxInfo) String() string { return proto.CompactTextString(m) }
func (*BoxInfo) ProtoMessage()    {}
func (*BoxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{0}
}
func (m *BoxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxInfo.Unmarshal(m, b)
}
func (m *BoxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxInfo.Marshal(b, m, deterministic)
}
func (dst *BoxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxInfo.Merge(dst, src)
}
func (m *BoxInfo) XXX_Size() int {
	return xxx_messageInfo_BoxInfo.Size(m)
}
func (m *BoxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BoxInfo proto.InternalMessageInfo

func (m *BoxInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BoxInfo) GetBoxId() uint32 {
	if m != nil {
		return m.BoxId
	}
	return 0
}

func (m *BoxInfo) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *BoxInfo) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *BoxInfo) GetBoxName() string {
	if m != nil {
		return m.BoxName
	}
	return ""
}

func (m *BoxInfo) GetMicCap() uint32 {
	if m != nil {
		return m.MicCap
	}
	return 0
}

func (m *BoxInfo) GetPublicMicList() []uint32 {
	if m != nil {
		return m.PublicMicList
	}
	return nil
}

func (m *BoxInfo) GetNormalMicList() []uint32 {
	if m != nil {
		return m.NormalMicList
	}
	return nil
}

type EnterBoxReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	CommonBoxid          uint32   `protobuf:"varint,5,opt,name=common_boxid,json=commonBoxid,proto3" json:"common_boxid,omitempty"`
	BroadcastBoxid       uint32   `protobuf:"varint,6,opt,name=broadcast_boxid,json=broadcastBoxid,proto3" json:"broadcast_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnterBoxReq) Reset()         { *m = EnterBoxReq{} }
func (m *EnterBoxReq) String() string { return proto.CompactTextString(m) }
func (*EnterBoxReq) ProtoMessage()    {}
func (*EnterBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{1}
}
func (m *EnterBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterBoxReq.Unmarshal(m, b)
}
func (m *EnterBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterBoxReq.Marshal(b, m, deterministic)
}
func (dst *EnterBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterBoxReq.Merge(dst, src)
}
func (m *EnterBoxReq) XXX_Size() int {
	return xxx_messageInfo_EnterBoxReq.Size(m)
}
func (m *EnterBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_EnterBoxReq proto.InternalMessageInfo

func (m *EnterBoxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EnterBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *EnterBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *EnterBoxReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *EnterBoxReq) GetCommonBoxid() uint32 {
	if m != nil {
		return m.CommonBoxid
	}
	return 0
}

func (m *EnterBoxReq) GetBroadcastBoxid() uint32 {
	if m != nil {
		return m.BroadcastBoxid
	}
	return 0
}

type EnterBoxResp struct {
	IsSwitch             bool            `protobuf:"varint,1,opt,name=is_switch,json=isSwitch,proto3" json:"is_switch,omitempty"`
	EnterBoxInfos        []*EnterBoxInfo `protobuf:"bytes,2,rep,name=enter_box_infos,json=enterBoxInfos,proto3" json:"enter_box_infos,omitempty"`
	Opts                 uint64          `protobuf:"varint,3,opt,name=opts,proto3" json:"opts,omitempty"`
	Channelid            uint32          `protobuf:"varint,4,opt,name=channelid,proto3" json:"channelid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *EnterBoxResp) Reset()         { *m = EnterBoxResp{} }
func (m *EnterBoxResp) String() string { return proto.CompactTextString(m) }
func (*EnterBoxResp) ProtoMessage()    {}
func (*EnterBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{2}
}
func (m *EnterBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterBoxResp.Unmarshal(m, b)
}
func (m *EnterBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterBoxResp.Marshal(b, m, deterministic)
}
func (dst *EnterBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterBoxResp.Merge(dst, src)
}
func (m *EnterBoxResp) XXX_Size() int {
	return xxx_messageInfo_EnterBoxResp.Size(m)
}
func (m *EnterBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_EnterBoxResp proto.InternalMessageInfo

func (m *EnterBoxResp) GetIsSwitch() bool {
	if m != nil {
		return m.IsSwitch
	}
	return false
}

func (m *EnterBoxResp) GetEnterBoxInfos() []*EnterBoxInfo {
	if m != nil {
		return m.EnterBoxInfos
	}
	return nil
}

func (m *EnterBoxResp) GetOpts() uint64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

func (m *EnterBoxResp) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

type EnterBoxInfo struct {
	BoxType              EnterResultBoxType `protobuf:"varint,1,opt,name=box_type,json=boxType,proto3,enum=channelbox.EnterResultBoxType" json:"box_type,omitempty"`
	Boxid                uint32             `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxCnt               uint32             `protobuf:"varint,3,opt,name=box_cnt,json=boxCnt,proto3" json:"box_cnt,omitempty"`
	AudioBoxIds          []uint32           `protobuf:"varint,4,rep,packed,name=audio_box_ids,json=audioBoxIds,proto3" json:"audio_box_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *EnterBoxInfo) Reset()         { *m = EnterBoxInfo{} }
func (m *EnterBoxInfo) String() string { return proto.CompactTextString(m) }
func (*EnterBoxInfo) ProtoMessage()    {}
func (*EnterBoxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{3}
}
func (m *EnterBoxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterBoxInfo.Unmarshal(m, b)
}
func (m *EnterBoxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterBoxInfo.Marshal(b, m, deterministic)
}
func (dst *EnterBoxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterBoxInfo.Merge(dst, src)
}
func (m *EnterBoxInfo) XXX_Size() int {
	return xxx_messageInfo_EnterBoxInfo.Size(m)
}
func (m *EnterBoxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterBoxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EnterBoxInfo proto.InternalMessageInfo

func (m *EnterBoxInfo) GetBoxType() EnterResultBoxType {
	if m != nil {
		return m.BoxType
	}
	return EnterResultBoxType_EnterType
}

func (m *EnterBoxInfo) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *EnterBoxInfo) GetBoxCnt() uint32 {
	if m != nil {
		return m.BoxCnt
	}
	return 0
}

func (m *EnterBoxInfo) GetAudioBoxIds() []uint32 {
	if m != nil {
		return m.AudioBoxIds
	}
	return nil
}

type ExitBoxReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	CommonBoxid          uint32   `protobuf:"varint,5,opt,name=common_boxid,json=commonBoxid,proto3" json:"common_boxid,omitempty"`
	BroadcastBoxid       uint32   `protobuf:"varint,6,opt,name=broadcast_boxid,json=broadcastBoxid,proto3" json:"broadcast_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExitBoxReq) Reset()         { *m = ExitBoxReq{} }
func (m *ExitBoxReq) String() string { return proto.CompactTextString(m) }
func (*ExitBoxReq) ProtoMessage()    {}
func (*ExitBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{4}
}
func (m *ExitBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitBoxReq.Unmarshal(m, b)
}
func (m *ExitBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitBoxReq.Marshal(b, m, deterministic)
}
func (dst *ExitBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitBoxReq.Merge(dst, src)
}
func (m *ExitBoxReq) XXX_Size() int {
	return xxx_messageInfo_ExitBoxReq.Size(m)
}
func (m *ExitBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExitBoxReq proto.InternalMessageInfo

func (m *ExitBoxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExitBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ExitBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *ExitBoxReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *ExitBoxReq) GetCommonBoxid() uint32 {
	if m != nil {
		return m.CommonBoxid
	}
	return 0
}

func (m *ExitBoxReq) GetBroadcastBoxid() uint32 {
	if m != nil {
		return m.BroadcastBoxid
	}
	return 0
}

type ExitBoxResp struct {
	BoxCnt               uint32   `protobuf:"varint,1,opt,name=box_cnt,json=boxCnt,proto3" json:"box_cnt,omitempty"`
	Opts                 uint64   `protobuf:"varint,2,opt,name=opts,proto3" json:"opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExitBoxResp) Reset()         { *m = ExitBoxResp{} }
func (m *ExitBoxResp) String() string { return proto.CompactTextString(m) }
func (*ExitBoxResp) ProtoMessage()    {}
func (*ExitBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{5}
}
func (m *ExitBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitBoxResp.Unmarshal(m, b)
}
func (m *ExitBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitBoxResp.Marshal(b, m, deterministic)
}
func (dst *ExitBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitBoxResp.Merge(dst, src)
}
func (m *ExitBoxResp) XXX_Size() int {
	return xxx_messageInfo_ExitBoxResp.Size(m)
}
func (m *ExitBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExitBoxResp proto.InternalMessageInfo

func (m *ExitBoxResp) GetBoxCnt() uint32 {
	if m != nil {
		return m.BoxCnt
	}
	return 0
}

func (m *ExitBoxResp) GetOpts() uint64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

type JoinBoxReq struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channelid            uint32      `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32      `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	JoinBoxType          JoinBoxType `protobuf:"varint,4,opt,name=join_box_type,json=joinBoxType,proto3,enum=channelbox.JoinBoxType" json:"join_box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *JoinBoxReq) Reset()         { *m = JoinBoxReq{} }
func (m *JoinBoxReq) String() string { return proto.CompactTextString(m) }
func (*JoinBoxReq) ProtoMessage()    {}
func (*JoinBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{6}
}
func (m *JoinBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinBoxReq.Unmarshal(m, b)
}
func (m *JoinBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinBoxReq.Marshal(b, m, deterministic)
}
func (dst *JoinBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinBoxReq.Merge(dst, src)
}
func (m *JoinBoxReq) XXX_Size() int {
	return xxx_messageInfo_JoinBoxReq.Size(m)
}
func (m *JoinBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinBoxReq proto.InternalMessageInfo

func (m *JoinBoxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *JoinBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *JoinBoxReq) GetJoinBoxType() JoinBoxType {
	if m != nil {
		return m.JoinBoxType
	}
	return JoinBoxType_NoneType
}

type JoinBoxResp struct {
	IsSwitch             bool            `protobuf:"varint,1,opt,name=is_switch,json=isSwitch,proto3" json:"is_switch,omitempty"`
	EnterBoxInfos        []*EnterBoxInfo `protobuf:"bytes,2,rep,name=enter_box_infos,json=enterBoxInfos,proto3" json:"enter_box_infos,omitempty"`
	Opts                 uint64          `protobuf:"varint,3,opt,name=opts,proto3" json:"opts,omitempty"`
	Channelid            uint32          `protobuf:"varint,4,opt,name=channelid,proto3" json:"channelid,omitempty"`
	IsIgnore             bool            `protobuf:"varint,5,opt,name=is_ignore,json=isIgnore,proto3" json:"is_ignore,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *JoinBoxResp) Reset()         { *m = JoinBoxResp{} }
func (m *JoinBoxResp) String() string { return proto.CompactTextString(m) }
func (*JoinBoxResp) ProtoMessage()    {}
func (*JoinBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{7}
}
func (m *JoinBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinBoxResp.Unmarshal(m, b)
}
func (m *JoinBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinBoxResp.Marshal(b, m, deterministic)
}
func (dst *JoinBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinBoxResp.Merge(dst, src)
}
func (m *JoinBoxResp) XXX_Size() int {
	return xxx_messageInfo_JoinBoxResp.Size(m)
}
func (m *JoinBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinBoxResp proto.InternalMessageInfo

func (m *JoinBoxResp) GetIsSwitch() bool {
	if m != nil {
		return m.IsSwitch
	}
	return false
}

func (m *JoinBoxResp) GetEnterBoxInfos() []*EnterBoxInfo {
	if m != nil {
		return m.EnterBoxInfos
	}
	return nil
}

func (m *JoinBoxResp) GetOpts() uint64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

func (m *JoinBoxResp) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *JoinBoxResp) GetIsIgnore() bool {
	if m != nil {
		return m.IsIgnore
	}
	return false
}

type BoxBaseInfo struct {
	Boxid                uint32   `protobuf:"varint,1,opt,name=boxid,proto3" json:"boxid,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Opts                 uint64   `protobuf:"varint,3,opt,name=opts,proto3" json:"opts,omitempty"`
	AudioBoxids          []uint32 `protobuf:"varint,4,rep,packed,name=audio_boxids,json=audioBoxids,proto3" json:"audio_boxids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxBaseInfo) Reset()         { *m = BoxBaseInfo{} }
func (m *BoxBaseInfo) String() string { return proto.CompactTextString(m) }
func (*BoxBaseInfo) ProtoMessage()    {}
func (*BoxBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{8}
}
func (m *BoxBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxBaseInfo.Unmarshal(m, b)
}
func (m *BoxBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxBaseInfo.Marshal(b, m, deterministic)
}
func (dst *BoxBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxBaseInfo.Merge(dst, src)
}
func (m *BoxBaseInfo) XXX_Size() int {
	return xxx_messageInfo_BoxBaseInfo.Size(m)
}
func (m *BoxBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BoxBaseInfo proto.InternalMessageInfo

func (m *BoxBaseInfo) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *BoxBaseInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BoxBaseInfo) GetOpts() uint64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

func (m *BoxBaseInfo) GetAudioBoxids() []uint32 {
	if m != nil {
		return m.AudioBoxids
	}
	return nil
}

type BoxCntInfo struct {
	Boxid                uint32   `protobuf:"varint,1,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxUserCnt           uint32   `protobuf:"varint,2,opt,name=box_user_cnt,json=boxUserCnt,proto3" json:"box_user_cnt,omitempty"`
	Channelid            uint32   `protobuf:"varint,3,opt,name=channelid,proto3" json:"channelid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxCntInfo) Reset()         { *m = BoxCntInfo{} }
func (m *BoxCntInfo) String() string { return proto.CompactTextString(m) }
func (*BoxCntInfo) ProtoMessage()    {}
func (*BoxCntInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{9}
}
func (m *BoxCntInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxCntInfo.Unmarshal(m, b)
}
func (m *BoxCntInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxCntInfo.Marshal(b, m, deterministic)
}
func (dst *BoxCntInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxCntInfo.Merge(dst, src)
}
func (m *BoxCntInfo) XXX_Size() int {
	return xxx_messageInfo_BoxCntInfo.Size(m)
}
func (m *BoxCntInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxCntInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BoxCntInfo proto.InternalMessageInfo

func (m *BoxCntInfo) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *BoxCntInfo) GetBoxUserCnt() uint32 {
	if m != nil {
		return m.BoxUserCnt
	}
	return 0
}

func (m *BoxCntInfo) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

// 客户端协议
type GetBoxInfoReq struct {
	OpeUid               uint32   `protobuf:"varint,1,opt,name=ope_uid,json=opeUid,proto3" json:"ope_uid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBoxInfoReq) Reset()         { *m = GetBoxInfoReq{} }
func (m *GetBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfoReq) ProtoMessage()    {}
func (*GetBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{10}
}
func (m *GetBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfoReq.Unmarshal(m, b)
}
func (m *GetBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfoReq.Merge(dst, src)
}
func (m *GetBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfoReq.Size(m)
}
func (m *GetBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfoReq proto.InternalMessageInfo

func (m *GetBoxInfoReq) GetOpeUid() uint32 {
	if m != nil {
		return m.OpeUid
	}
	return 0
}

func (m *GetBoxInfoReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxInfoReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type GetBoxInfoResp struct {
	BoxInfo              []*BoxCntInfo `protobuf:"bytes,1,rep,name=box_info,json=boxInfo,proto3" json:"box_info,omitempty"`
	UserBoxid            uint32        `protobuf:"varint,2,opt,name=user_boxid,json=userBoxid,proto3" json:"user_boxid,omitempty"`
	BoxList              []*BoxInfo    `protobuf:"bytes,3,rep,name=box_list,json=boxList,proto3" json:"box_list,omitempty"`
	MicTotal             uint32        `protobuf:"varint,4,opt,name=mic_total,json=micTotal,proto3" json:"mic_total,omitempty"`
	UserAudioBoxids      []uint32      `protobuf:"varint,5,rep,packed,name=user_audio_boxids,json=userAudioBoxids,proto3" json:"user_audio_boxids,omitempty"`
	MainCommonBoxid      uint32        `protobuf:"varint,6,opt,name=main_common_boxid,json=mainCommonBoxid,proto3" json:"main_common_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBoxInfoResp) Reset()         { *m = GetBoxInfoResp{} }
func (m *GetBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfoResp) ProtoMessage()    {}
func (*GetBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{11}
}
func (m *GetBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfoResp.Unmarshal(m, b)
}
func (m *GetBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfoResp.Merge(dst, src)
}
func (m *GetBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfoResp.Size(m)
}
func (m *GetBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfoResp proto.InternalMessageInfo

func (m *GetBoxInfoResp) GetBoxInfo() []*BoxCntInfo {
	if m != nil {
		return m.BoxInfo
	}
	return nil
}

func (m *GetBoxInfoResp) GetUserBoxid() uint32 {
	if m != nil {
		return m.UserBoxid
	}
	return 0
}

func (m *GetBoxInfoResp) GetBoxList() []*BoxInfo {
	if m != nil {
		return m.BoxList
	}
	return nil
}

func (m *GetBoxInfoResp) GetMicTotal() uint32 {
	if m != nil {
		return m.MicTotal
	}
	return 0
}

func (m *GetBoxInfoResp) GetUserAudioBoxids() []uint32 {
	if m != nil {
		return m.UserAudioBoxids
	}
	return nil
}

func (m *GetBoxInfoResp) GetMainCommonBoxid() uint32 {
	if m != nil {
		return m.MainCommonBoxid
	}
	return 0
}

type ClearJoinBoxReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearJoinBoxReq) Reset()         { *m = ClearJoinBoxReq{} }
func (m *ClearJoinBoxReq) String() string { return proto.CompactTextString(m) }
func (*ClearJoinBoxReq) ProtoMessage()    {}
func (*ClearJoinBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{12}
}
func (m *ClearJoinBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearJoinBoxReq.Unmarshal(m, b)
}
func (m *ClearJoinBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearJoinBoxReq.Marshal(b, m, deterministic)
}
func (dst *ClearJoinBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearJoinBoxReq.Merge(dst, src)
}
func (m *ClearJoinBoxReq) XXX_Size() int {
	return xxx_messageInfo_ClearJoinBoxReq.Size(m)
}
func (m *ClearJoinBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearJoinBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearJoinBoxReq proto.InternalMessageInfo

func (m *ClearJoinBoxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ClearJoinBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ClearJoinBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *ClearJoinBoxReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type ClearJoinBoxResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearJoinBoxResp) Reset()         { *m = ClearJoinBoxResp{} }
func (m *ClearJoinBoxResp) String() string { return proto.CompactTextString(m) }
func (*ClearJoinBoxResp) ProtoMessage()    {}
func (*ClearJoinBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{13}
}
func (m *ClearJoinBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearJoinBoxResp.Unmarshal(m, b)
}
func (m *ClearJoinBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearJoinBoxResp.Marshal(b, m, deterministic)
}
func (dst *ClearJoinBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearJoinBoxResp.Merge(dst, src)
}
func (m *ClearJoinBoxResp) XXX_Size() int {
	return xxx_messageInfo_ClearJoinBoxResp.Size(m)
}
func (m *ClearJoinBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearJoinBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearJoinBoxResp proto.InternalMessageInfo

type BatchGetBoxInfosReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetBoxInfosReq) Reset()         { *m = BatchGetBoxInfosReq{} }
func (m *BatchGetBoxInfosReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetBoxInfosReq) ProtoMessage()    {}
func (*BatchGetBoxInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{14}
}
func (m *BatchGetBoxInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBoxInfosReq.Unmarshal(m, b)
}
func (m *BatchGetBoxInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBoxInfosReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetBoxInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBoxInfosReq.Merge(dst, src)
}
func (m *BatchGetBoxInfosReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetBoxInfosReq.Size(m)
}
func (m *BatchGetBoxInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBoxInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBoxInfosReq proto.InternalMessageInfo

func (m *BatchGetBoxInfosReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetBoxInfosReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *BatchGetBoxInfosReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type BatchGetBoxInfosResp struct {
	BoxBaseInfos         []*BoxBaseInfo `protobuf:"bytes,1,rep,name=box_base_infos,json=boxBaseInfos,proto3" json:"box_base_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchGetBoxInfosResp) Reset()         { *m = BatchGetBoxInfosResp{} }
func (m *BatchGetBoxInfosResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetBoxInfosResp) ProtoMessage()    {}
func (*BatchGetBoxInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{15}
}
func (m *BatchGetBoxInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBoxInfosResp.Unmarshal(m, b)
}
func (m *BatchGetBoxInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBoxInfosResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetBoxInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBoxInfosResp.Merge(dst, src)
}
func (m *BatchGetBoxInfosResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetBoxInfosResp.Size(m)
}
func (m *BatchGetBoxInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBoxInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBoxInfosResp proto.InternalMessageInfo

func (m *BatchGetBoxInfosResp) GetBoxBaseInfos() []*BoxBaseInfo {
	if m != nil {
		return m.BoxBaseInfos
	}
	return nil
}

type GetBoxInfosByLimitReq struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	LastTime             int64    `protobuf:"varint,3,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	GetCnt               int64    `protobuf:"varint,4,opt,name=get_cnt,json=getCnt,proto3" json:"get_cnt,omitempty"`
	BoxType              BoxType  `protobuf:"varint,5,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBoxInfosByLimitReq) Reset()         { *m = GetBoxInfosByLimitReq{} }
func (m *GetBoxInfosByLimitReq) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfosByLimitReq) ProtoMessage()    {}
func (*GetBoxInfosByLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{16}
}
func (m *GetBoxInfosByLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Unmarshal(m, b)
}
func (m *GetBoxInfosByLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfosByLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfosByLimitReq.Merge(dst, src)
}
func (m *GetBoxInfosByLimitReq) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Size(m)
}
func (m *GetBoxInfosByLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfosByLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfosByLimitReq proto.InternalMessageInfo

func (m *GetBoxInfosByLimitReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetLastTime() int64 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetGetCnt() int64 {
	if m != nil {
		return m.GetCnt
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type GetBoxInfosByLimitResp struct {
	BoxBaseInfos         []*BoxBaseInfo `protobuf:"bytes,1,rep,name=box_base_infos,json=boxBaseInfos,proto3" json:"box_base_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetBoxInfosByLimitResp) Reset()         { *m = GetBoxInfosByLimitResp{} }
func (m *GetBoxInfosByLimitResp) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfosByLimitResp) ProtoMessage()    {}
func (*GetBoxInfosByLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{17}
}
func (m *GetBoxInfosByLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Unmarshal(m, b)
}
func (m *GetBoxInfosByLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfosByLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfosByLimitResp.Merge(dst, src)
}
func (m *GetBoxInfosByLimitResp) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Size(m)
}
func (m *GetBoxInfosByLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfosByLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfosByLimitResp proto.InternalMessageInfo

func (m *GetBoxInfosByLimitResp) GetBoxBaseInfos() []*BoxBaseInfo {
	if m != nil {
		return m.BoxBaseInfos
	}
	return nil
}

type GetBoxUserInMicInfosReq struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	MicUids              []uint32 `protobuf:"varint,3,rep,packed,name=mic_uids,json=micUids,proto3" json:"mic_uids,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBoxUserInMicInfosReq) Reset()         { *m = GetBoxUserInMicInfosReq{} }
func (m *GetBoxUserInMicInfosReq) String() string { return proto.CompactTextString(m) }
func (*GetBoxUserInMicInfosReq) ProtoMessage()    {}
func (*GetBoxUserInMicInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{18}
}
func (m *GetBoxUserInMicInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxUserInMicInfosReq.Unmarshal(m, b)
}
func (m *GetBoxUserInMicInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxUserInMicInfosReq.Marshal(b, m, deterministic)
}
func (dst *GetBoxUserInMicInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxUserInMicInfosReq.Merge(dst, src)
}
func (m *GetBoxUserInMicInfosReq) XXX_Size() int {
	return xxx_messageInfo_GetBoxUserInMicInfosReq.Size(m)
}
func (m *GetBoxUserInMicInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxUserInMicInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxUserInMicInfosReq proto.InternalMessageInfo

func (m *GetBoxUserInMicInfosReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxUserInMicInfosReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *GetBoxUserInMicInfosReq) GetMicUids() []uint32 {
	if m != nil {
		return m.MicUids
	}
	return nil
}

func (m *GetBoxUserInMicInfosReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type GetBoxUserInMicInfosResp struct {
	BoxUids              []uint32 `protobuf:"varint,1,rep,packed,name=box_uids,json=boxUids,proto3" json:"box_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBoxUserInMicInfosResp) Reset()         { *m = GetBoxUserInMicInfosResp{} }
func (m *GetBoxUserInMicInfosResp) String() string { return proto.CompactTextString(m) }
func (*GetBoxUserInMicInfosResp) ProtoMessage()    {}
func (*GetBoxUserInMicInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{19}
}
func (m *GetBoxUserInMicInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxUserInMicInfosResp.Unmarshal(m, b)
}
func (m *GetBoxUserInMicInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxUserInMicInfosResp.Marshal(b, m, deterministic)
}
func (dst *GetBoxUserInMicInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxUserInMicInfosResp.Merge(dst, src)
}
func (m *GetBoxUserInMicInfosResp) XXX_Size() int {
	return xxx_messageInfo_GetBoxUserInMicInfosResp.Size(m)
}
func (m *GetBoxUserInMicInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxUserInMicInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxUserInMicInfosResp proto.InternalMessageInfo

func (m *GetBoxUserInMicInfosResp) GetBoxUids() []uint32 {
	if m != nil {
		return m.BoxUids
	}
	return nil
}

type ExitChannelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExitChannelReq) Reset()         { *m = ExitChannelReq{} }
func (m *ExitChannelReq) String() string { return proto.CompactTextString(m) }
func (*ExitChannelReq) ProtoMessage()    {}
func (*ExitChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{20}
}
func (m *ExitChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitChannelReq.Unmarshal(m, b)
}
func (m *ExitChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitChannelReq.Marshal(b, m, deterministic)
}
func (dst *ExitChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitChannelReq.Merge(dst, src)
}
func (m *ExitChannelReq) XXX_Size() int {
	return xxx_messageInfo_ExitChannelReq.Size(m)
}
func (m *ExitChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExitChannelReq proto.InternalMessageInfo

func (m *ExitChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExitChannelReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ExitChannelReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type ExitChannelResp struct {
	BoxCnt               uint32   `protobuf:"varint,1,opt,name=box_cnt,json=boxCnt,proto3" json:"box_cnt,omitempty"`
	Opts                 uint64   `protobuf:"varint,2,opt,name=opts,proto3" json:"opts,omitempty"`
	BoxId                uint32   `protobuf:"varint,3,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExitChannelResp) Reset()         { *m = ExitChannelResp{} }
func (m *ExitChannelResp) String() string { return proto.CompactTextString(m) }
func (*ExitChannelResp) ProtoMessage()    {}
func (*ExitChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{21}
}
func (m *ExitChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitChannelResp.Unmarshal(m, b)
}
func (m *ExitChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitChannelResp.Marshal(b, m, deterministic)
}
func (dst *ExitChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitChannelResp.Merge(dst, src)
}
func (m *ExitChannelResp) XXX_Size() int {
	return xxx_messageInfo_ExitChannelResp.Size(m)
}
func (m *ExitChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExitChannelResp proto.InternalMessageInfo

func (m *ExitChannelResp) GetBoxCnt() uint32 {
	if m != nil {
		return m.BoxCnt
	}
	return 0
}

func (m *ExitChannelResp) GetOpts() uint64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

func (m *ExitChannelResp) GetBoxId() uint32 {
	if m != nil {
		return m.BoxId
	}
	return 0
}

func (m *ExitChannelResp) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type UpsertBoxInfoReq struct {
	Box                  *BoxInfo `protobuf:"bytes,1,opt,name=box,proto3" json:"box,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertBoxInfoReq) Reset()         { *m = UpsertBoxInfoReq{} }
func (m *UpsertBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpsertBoxInfoReq) ProtoMessage()    {}
func (*UpsertBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{22}
}
func (m *UpsertBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBoxInfoReq.Unmarshal(m, b)
}
func (m *UpsertBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpsertBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBoxInfoReq.Merge(dst, src)
}
func (m *UpsertBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpsertBoxInfoReq.Size(m)
}
func (m *UpsertBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBoxInfoReq proto.InternalMessageInfo

func (m *UpsertBoxInfoReq) GetBox() *BoxInfo {
	if m != nil {
		return m.Box
	}
	return nil
}

type UpsertBoxInfoResp struct {
	Box                  *BoxInfo `protobuf:"bytes,1,opt,name=box,proto3" json:"box,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertBoxInfoResp) Reset()         { *m = UpsertBoxInfoResp{} }
func (m *UpsertBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpsertBoxInfoResp) ProtoMessage()    {}
func (*UpsertBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{23}
}
func (m *UpsertBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBoxInfoResp.Unmarshal(m, b)
}
func (m *UpsertBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpsertBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBoxInfoResp.Merge(dst, src)
}
func (m *UpsertBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpsertBoxInfoResp.Size(m)
}
func (m *UpsertBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBoxInfoResp proto.InternalMessageInfo

func (m *UpsertBoxInfoResp) GetBox() *BoxInfo {
	if m != nil {
		return m.Box
	}
	return nil
}

type DelBoxInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BoxId                uint32   `protobuf:"varint,2,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	BoxType              BoxType  `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBoxInfoReq) Reset()         { *m = DelBoxInfoReq{} }
func (m *DelBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelBoxInfoReq) ProtoMessage()    {}
func (*DelBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{24}
}
func (m *DelBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBoxInfoReq.Unmarshal(m, b)
}
func (m *DelBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBoxInfoReq.Merge(dst, src)
}
func (m *DelBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelBoxInfoReq.Size(m)
}
func (m *DelBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBoxInfoReq proto.InternalMessageInfo

func (m *DelBoxInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DelBoxInfoReq) GetBoxId() uint32 {
	if m != nil {
		return m.BoxId
	}
	return 0
}

func (m *DelBoxInfoReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type DelBoxInfoResp struct {
	// 房间内剩余子频道数量
	BoxNum               uint32   `protobuf:"varint,1,opt,name=box_num,json=boxNum,proto3" json:"box_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBoxInfoResp) Reset()         { *m = DelBoxInfoResp{} }
func (m *DelBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelBoxInfoResp) ProtoMessage()    {}
func (*DelBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{25}
}
func (m *DelBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBoxInfoResp.Unmarshal(m, b)
}
func (m *DelBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBoxInfoResp.Merge(dst, src)
}
func (m *DelBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelBoxInfoResp.Size(m)
}
func (m *DelBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBoxInfoResp proto.InternalMessageInfo

func (m *DelBoxInfoResp) GetBoxNum() uint32 {
	if m != nil {
		return m.BoxNum
	}
	return 0
}

type ApplyOnMicTokenReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicId                uint32   `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyOnMicTokenReq) Reset()         { *m = ApplyOnMicTokenReq{} }
func (m *ApplyOnMicTokenReq) String() string { return proto.CompactTextString(m) }
func (*ApplyOnMicTokenReq) ProtoMessage()    {}
func (*ApplyOnMicTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{26}
}
func (m *ApplyOnMicTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyOnMicTokenReq.Unmarshal(m, b)
}
func (m *ApplyOnMicTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyOnMicTokenReq.Marshal(b, m, deterministic)
}
func (dst *ApplyOnMicTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyOnMicTokenReq.Merge(dst, src)
}
func (m *ApplyOnMicTokenReq) XXX_Size() int {
	return xxx_messageInfo_ApplyOnMicTokenReq.Size(m)
}
func (m *ApplyOnMicTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyOnMicTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyOnMicTokenReq proto.InternalMessageInfo

func (m *ApplyOnMicTokenReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ApplyOnMicTokenReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

type ApplyOnMicTokenResp struct {
	OnMicToken           string   `protobuf:"bytes,1,opt,name=on_mic_token,json=onMicToken,proto3" json:"on_mic_token,omitempty"`
	MicId                uint32   `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyOnMicTokenResp) Reset()         { *m = ApplyOnMicTokenResp{} }
func (m *ApplyOnMicTokenResp) String() string { return proto.CompactTextString(m) }
func (*ApplyOnMicTokenResp) ProtoMessage()    {}
func (*ApplyOnMicTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{27}
}
func (m *ApplyOnMicTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyOnMicTokenResp.Unmarshal(m, b)
}
func (m *ApplyOnMicTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyOnMicTokenResp.Marshal(b, m, deterministic)
}
func (dst *ApplyOnMicTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyOnMicTokenResp.Merge(dst, src)
}
func (m *ApplyOnMicTokenResp) XXX_Size() int {
	return xxx_messageInfo_ApplyOnMicTokenResp.Size(m)
}
func (m *ApplyOnMicTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyOnMicTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyOnMicTokenResp proto.InternalMessageInfo

func (m *ApplyOnMicTokenResp) GetOnMicToken() string {
	if m != nil {
		return m.OnMicToken
	}
	return ""
}

func (m *ApplyOnMicTokenResp) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

type SetChannelMicBoxIdReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OpenMicBoxId         uint32   `protobuf:"varint,2,opt,name=open_mic_box_id,json=openMicBoxId,proto3" json:"open_mic_box_id,omitempty"`
	OpeUid               uint32   `protobuf:"varint,3,opt,name=ope_uid,json=opeUid,proto3" json:"ope_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMicBoxIdReq) Reset()         { *m = SetChannelMicBoxIdReq{} }
func (m *SetChannelMicBoxIdReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicBoxIdReq) ProtoMessage()    {}
func (*SetChannelMicBoxIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{28}
}
func (m *SetChannelMicBoxIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicBoxIdReq.Unmarshal(m, b)
}
func (m *SetChannelMicBoxIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicBoxIdReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicBoxIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicBoxIdReq.Merge(dst, src)
}
func (m *SetChannelMicBoxIdReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicBoxIdReq.Size(m)
}
func (m *SetChannelMicBoxIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicBoxIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicBoxIdReq proto.InternalMessageInfo

func (m *SetChannelMicBoxIdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMicBoxIdReq) GetOpenMicBoxId() uint32 {
	if m != nil {
		return m.OpenMicBoxId
	}
	return 0
}

func (m *SetChannelMicBoxIdReq) GetOpeUid() uint32 {
	if m != nil {
		return m.OpeUid
	}
	return 0
}

type SetChannelMicBoxIdResp struct {
	Opts                 int64    `protobuf:"varint,1,opt,name=opts,proto3" json:"opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMicBoxIdResp) Reset()         { *m = SetChannelMicBoxIdResp{} }
func (m *SetChannelMicBoxIdResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicBoxIdResp) ProtoMessage()    {}
func (*SetChannelMicBoxIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{29}
}
func (m *SetChannelMicBoxIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicBoxIdResp.Unmarshal(m, b)
}
func (m *SetChannelMicBoxIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicBoxIdResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicBoxIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicBoxIdResp.Merge(dst, src)
}
func (m *SetChannelMicBoxIdResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicBoxIdResp.Size(m)
}
func (m *SetChannelMicBoxIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicBoxIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicBoxIdResp proto.InternalMessageInfo

func (m *SetChannelMicBoxIdResp) GetOpts() int64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

// 麦位申请记录
type MicApplyRecord struct {
	MicId                uint32    `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	MicType              MicType   `protobuf:"varint,2,opt,name=mic_type,json=micType,proto3,enum=channelbox.MicType" json:"mic_type,omitempty"`
	MicStatus            MicStatus `protobuf:"varint,3,opt,name=mic_status,json=micStatus,proto3,enum=channelbox.MicStatus" json:"mic_status,omitempty"`
	Uid                  uint32    `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	BoxId                uint32    `protobuf:"varint,5,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	UpdateAt             uint32    `protobuf:"varint,6,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *MicApplyRecord) Reset()         { *m = MicApplyRecord{} }
func (m *MicApplyRecord) String() string { return proto.CompactTextString(m) }
func (*MicApplyRecord) ProtoMessage()    {}
func (*MicApplyRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{30}
}
func (m *MicApplyRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicApplyRecord.Unmarshal(m, b)
}
func (m *MicApplyRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicApplyRecord.Marshal(b, m, deterministic)
}
func (dst *MicApplyRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicApplyRecord.Merge(dst, src)
}
func (m *MicApplyRecord) XXX_Size() int {
	return xxx_messageInfo_MicApplyRecord.Size(m)
}
func (m *MicApplyRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_MicApplyRecord.DiscardUnknown(m)
}

var xxx_messageInfo_MicApplyRecord proto.InternalMessageInfo

func (m *MicApplyRecord) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *MicApplyRecord) GetMicType() MicType {
	if m != nil {
		return m.MicType
	}
	return MicType_MicTypeFree
}

func (m *MicApplyRecord) GetMicStatus() MicStatus {
	if m != nil {
		return m.MicStatus
	}
	return MicStatus_MicStatusApplying
}

func (m *MicApplyRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MicApplyRecord) GetBoxId() uint32 {
	if m != nil {
		return m.BoxId
	}
	return 0
}

func (m *MicApplyRecord) GetUpdateAt() uint32 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

type SetMicApplyRecordsReq struct {
	ChannelId            uint32            `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Records              []*MicApplyRecord `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SetMicApplyRecordsReq) Reset()         { *m = SetMicApplyRecordsReq{} }
func (m *SetMicApplyRecordsReq) String() string { return proto.CompactTextString(m) }
func (*SetMicApplyRecordsReq) ProtoMessage()    {}
func (*SetMicApplyRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{31}
}
func (m *SetMicApplyRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicApplyRecordsReq.Unmarshal(m, b)
}
func (m *SetMicApplyRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicApplyRecordsReq.Marshal(b, m, deterministic)
}
func (dst *SetMicApplyRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicApplyRecordsReq.Merge(dst, src)
}
func (m *SetMicApplyRecordsReq) XXX_Size() int {
	return xxx_messageInfo_SetMicApplyRecordsReq.Size(m)
}
func (m *SetMicApplyRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicApplyRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicApplyRecordsReq proto.InternalMessageInfo

func (m *SetMicApplyRecordsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMicApplyRecordsReq) GetRecords() []*MicApplyRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

type SetMicApplyRecordsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMicApplyRecordsResp) Reset()         { *m = SetMicApplyRecordsResp{} }
func (m *SetMicApplyRecordsResp) String() string { return proto.CompactTextString(m) }
func (*SetMicApplyRecordsResp) ProtoMessage()    {}
func (*SetMicApplyRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{32}
}
func (m *SetMicApplyRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicApplyRecordsResp.Unmarshal(m, b)
}
func (m *SetMicApplyRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicApplyRecordsResp.Marshal(b, m, deterministic)
}
func (dst *SetMicApplyRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicApplyRecordsResp.Merge(dst, src)
}
func (m *SetMicApplyRecordsResp) XXX_Size() int {
	return xxx_messageInfo_SetMicApplyRecordsResp.Size(m)
}
func (m *SetMicApplyRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicApplyRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicApplyRecordsResp proto.InternalMessageInfo

type GetMicApplyRecordsReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMicApplyRecordsReq) Reset()         { *m = GetMicApplyRecordsReq{} }
func (m *GetMicApplyRecordsReq) String() string { return proto.CompactTextString(m) }
func (*GetMicApplyRecordsReq) ProtoMessage()    {}
func (*GetMicApplyRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{33}
}
func (m *GetMicApplyRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicApplyRecordsReq.Unmarshal(m, b)
}
func (m *GetMicApplyRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicApplyRecordsReq.Marshal(b, m, deterministic)
}
func (dst *GetMicApplyRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicApplyRecordsReq.Merge(dst, src)
}
func (m *GetMicApplyRecordsReq) XXX_Size() int {
	return xxx_messageInfo_GetMicApplyRecordsReq.Size(m)
}
func (m *GetMicApplyRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicApplyRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicApplyRecordsReq proto.InternalMessageInfo

func (m *GetMicApplyRecordsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMicApplyRecordsResp struct {
	Records              []*MicApplyRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetMicApplyRecordsResp) Reset()         { *m = GetMicApplyRecordsResp{} }
func (m *GetMicApplyRecordsResp) String() string { return proto.CompactTextString(m) }
func (*GetMicApplyRecordsResp) ProtoMessage()    {}
func (*GetMicApplyRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_beaa8997d6e457ed, []int{34}
}
func (m *GetMicApplyRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicApplyRecordsResp.Unmarshal(m, b)
}
func (m *GetMicApplyRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicApplyRecordsResp.Marshal(b, m, deterministic)
}
func (dst *GetMicApplyRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicApplyRecordsResp.Merge(dst, src)
}
func (m *GetMicApplyRecordsResp) XXX_Size() int {
	return xxx_messageInfo_GetMicApplyRecordsResp.Size(m)
}
func (m *GetMicApplyRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicApplyRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicApplyRecordsResp proto.InternalMessageInfo

func (m *GetMicApplyRecordsResp) GetRecords() []*MicApplyRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

func init() {
	proto.RegisterType((*BoxInfo)(nil), "channelbox.BoxInfo")
	proto.RegisterType((*EnterBoxReq)(nil), "channelbox.EnterBoxReq")
	proto.RegisterType((*EnterBoxResp)(nil), "channelbox.EnterBoxResp")
	proto.RegisterType((*EnterBoxInfo)(nil), "channelbox.EnterBoxInfo")
	proto.RegisterType((*ExitBoxReq)(nil), "channelbox.ExitBoxReq")
	proto.RegisterType((*ExitBoxResp)(nil), "channelbox.ExitBoxResp")
	proto.RegisterType((*JoinBoxReq)(nil), "channelbox.JoinBoxReq")
	proto.RegisterType((*JoinBoxResp)(nil), "channelbox.JoinBoxResp")
	proto.RegisterType((*BoxBaseInfo)(nil), "channelbox.BoxBaseInfo")
	proto.RegisterType((*BoxCntInfo)(nil), "channelbox.BoxCntInfo")
	proto.RegisterType((*GetBoxInfoReq)(nil), "channelbox.GetBoxInfoReq")
	proto.RegisterType((*GetBoxInfoResp)(nil), "channelbox.GetBoxInfoResp")
	proto.RegisterType((*ClearJoinBoxReq)(nil), "channelbox.ClearJoinBoxReq")
	proto.RegisterType((*ClearJoinBoxResp)(nil), "channelbox.ClearJoinBoxResp")
	proto.RegisterType((*BatchGetBoxInfosReq)(nil), "channelbox.BatchGetBoxInfosReq")
	proto.RegisterType((*BatchGetBoxInfosResp)(nil), "channelbox.BatchGetBoxInfosResp")
	proto.RegisterType((*GetBoxInfosByLimitReq)(nil), "channelbox.GetBoxInfosByLimitReq")
	proto.RegisterType((*GetBoxInfosByLimitResp)(nil), "channelbox.GetBoxInfosByLimitResp")
	proto.RegisterType((*GetBoxUserInMicInfosReq)(nil), "channelbox.GetBoxUserInMicInfosReq")
	proto.RegisterType((*GetBoxUserInMicInfosResp)(nil), "channelbox.GetBoxUserInMicInfosResp")
	proto.RegisterType((*ExitChannelReq)(nil), "channelbox.ExitChannelReq")
	proto.RegisterType((*ExitChannelResp)(nil), "channelbox.ExitChannelResp")
	proto.RegisterType((*UpsertBoxInfoReq)(nil), "channelbox.UpsertBoxInfoReq")
	proto.RegisterType((*UpsertBoxInfoResp)(nil), "channelbox.UpsertBoxInfoResp")
	proto.RegisterType((*DelBoxInfoReq)(nil), "channelbox.DelBoxInfoReq")
	proto.RegisterType((*DelBoxInfoResp)(nil), "channelbox.DelBoxInfoResp")
	proto.RegisterType((*ApplyOnMicTokenReq)(nil), "channelbox.ApplyOnMicTokenReq")
	proto.RegisterType((*ApplyOnMicTokenResp)(nil), "channelbox.ApplyOnMicTokenResp")
	proto.RegisterType((*SetChannelMicBoxIdReq)(nil), "channelbox.SetChannelMicBoxIdReq")
	proto.RegisterType((*SetChannelMicBoxIdResp)(nil), "channelbox.SetChannelMicBoxIdResp")
	proto.RegisterType((*MicApplyRecord)(nil), "channelbox.MicApplyRecord")
	proto.RegisterType((*SetMicApplyRecordsReq)(nil), "channelbox.SetMicApplyRecordsReq")
	proto.RegisterType((*SetMicApplyRecordsResp)(nil), "channelbox.SetMicApplyRecordsResp")
	proto.RegisterType((*GetMicApplyRecordsReq)(nil), "channelbox.GetMicApplyRecordsReq")
	proto.RegisterType((*GetMicApplyRecordsResp)(nil), "channelbox.GetMicApplyRecordsResp")
	proto.RegisterEnum("channelbox.BoxType", BoxType_name, BoxType_value)
	proto.RegisterEnum("channelbox.EnterResultBoxType", EnterResultBoxType_name, EnterResultBoxType_value)
	proto.RegisterEnum("channelbox.JoinBoxType", JoinBoxType_name, JoinBoxType_value)
	proto.RegisterEnum("channelbox.MicType", MicType_name, MicType_value)
	proto.RegisterEnum("channelbox.MicStatus", MicStatus_name, MicStatus_value)
	proto.RegisterEnum("channelbox.SystemBoxID", SystemBoxID_name, SystemBoxID_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelBoxClient is the client API for ChannelBox service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelBoxClient interface {
	EnterBox(ctx context.Context, in *EnterBoxReq, opts ...grpc.CallOption) (*EnterBoxResp, error)
	ExitBox(ctx context.Context, in *ExitBoxReq, opts ...grpc.CallOption) (*ExitBoxResp, error)
	JoinBox(ctx context.Context, in *JoinBoxReq, opts ...grpc.CallOption) (*JoinBoxResp, error)
	GetBoxInfo(ctx context.Context, in *GetBoxInfoReq, opts ...grpc.CallOption) (*GetBoxInfoResp, error)
	ClearJoinBox(ctx context.Context, in *ClearJoinBoxReq, opts ...grpc.CallOption) (*ClearJoinBoxResp, error)
	BatchGetBoxInfos(ctx context.Context, in *BatchGetBoxInfosReq, opts ...grpc.CallOption) (*BatchGetBoxInfosResp, error)
	GetBoxInfosByLimit(ctx context.Context, in *GetBoxInfosByLimitReq, opts ...grpc.CallOption) (*GetBoxInfosByLimitResp, error)
	GetBoxUserInMicInfos(ctx context.Context, in *GetBoxUserInMicInfosReq, opts ...grpc.CallOption) (*GetBoxUserInMicInfosResp, error)
	ExitChannel(ctx context.Context, in *ExitChannelReq, opts ...grpc.CallOption) (*ExitChannelResp, error)
	UpsertBoxInfo(ctx context.Context, in *UpsertBoxInfoReq, opts ...grpc.CallOption) (*UpsertBoxInfoResp, error)
	DelBoxInfo(ctx context.Context, in *DelBoxInfoReq, opts ...grpc.CallOption) (*DelBoxInfoResp, error)
	ApplyOnMicToken(ctx context.Context, in *ApplyOnMicTokenReq, opts ...grpc.CallOption) (*ApplyOnMicTokenResp, error)
	SetChannelMicBoxId(ctx context.Context, in *SetChannelMicBoxIdReq, opts ...grpc.CallOption) (*SetChannelMicBoxIdResp, error)
	// 麦位申请记录
	SetMicApplyRecords(ctx context.Context, in *SetMicApplyRecordsReq, opts ...grpc.CallOption) (*SetMicApplyRecordsResp, error)
	GetMicApplyRecords(ctx context.Context, in *GetMicApplyRecordsReq, opts ...grpc.CallOption) (*GetMicApplyRecordsResp, error)
}

type channelBoxClient struct {
	cc *grpc.ClientConn
}

func NewChannelBoxClient(cc *grpc.ClientConn) ChannelBoxClient {
	return &channelBoxClient{cc}
}

func (c *channelBoxClient) EnterBox(ctx context.Context, in *EnterBoxReq, opts ...grpc.CallOption) (*EnterBoxResp, error) {
	out := new(EnterBoxResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/EnterBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) ExitBox(ctx context.Context, in *ExitBoxReq, opts ...grpc.CallOption) (*ExitBoxResp, error) {
	out := new(ExitBoxResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/ExitBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) JoinBox(ctx context.Context, in *JoinBoxReq, opts ...grpc.CallOption) (*JoinBoxResp, error) {
	out := new(JoinBoxResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/JoinBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) GetBoxInfo(ctx context.Context, in *GetBoxInfoReq, opts ...grpc.CallOption) (*GetBoxInfoResp, error) {
	out := new(GetBoxInfoResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/GetBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) ClearJoinBox(ctx context.Context, in *ClearJoinBoxReq, opts ...grpc.CallOption) (*ClearJoinBoxResp, error) {
	out := new(ClearJoinBoxResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/ClearJoinBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) BatchGetBoxInfos(ctx context.Context, in *BatchGetBoxInfosReq, opts ...grpc.CallOption) (*BatchGetBoxInfosResp, error) {
	out := new(BatchGetBoxInfosResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/BatchGetBoxInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) GetBoxInfosByLimit(ctx context.Context, in *GetBoxInfosByLimitReq, opts ...grpc.CallOption) (*GetBoxInfosByLimitResp, error) {
	out := new(GetBoxInfosByLimitResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/GetBoxInfosByLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) GetBoxUserInMicInfos(ctx context.Context, in *GetBoxUserInMicInfosReq, opts ...grpc.CallOption) (*GetBoxUserInMicInfosResp, error) {
	out := new(GetBoxUserInMicInfosResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/GetBoxUserInMicInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) ExitChannel(ctx context.Context, in *ExitChannelReq, opts ...grpc.CallOption) (*ExitChannelResp, error) {
	out := new(ExitChannelResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/ExitChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) UpsertBoxInfo(ctx context.Context, in *UpsertBoxInfoReq, opts ...grpc.CallOption) (*UpsertBoxInfoResp, error) {
	out := new(UpsertBoxInfoResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/UpsertBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) DelBoxInfo(ctx context.Context, in *DelBoxInfoReq, opts ...grpc.CallOption) (*DelBoxInfoResp, error) {
	out := new(DelBoxInfoResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/DelBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) ApplyOnMicToken(ctx context.Context, in *ApplyOnMicTokenReq, opts ...grpc.CallOption) (*ApplyOnMicTokenResp, error) {
	out := new(ApplyOnMicTokenResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/ApplyOnMicToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) SetChannelMicBoxId(ctx context.Context, in *SetChannelMicBoxIdReq, opts ...grpc.CallOption) (*SetChannelMicBoxIdResp, error) {
	out := new(SetChannelMicBoxIdResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/SetChannelMicBoxId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) SetMicApplyRecords(ctx context.Context, in *SetMicApplyRecordsReq, opts ...grpc.CallOption) (*SetMicApplyRecordsResp, error) {
	out := new(SetMicApplyRecordsResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/SetMicApplyRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) GetMicApplyRecords(ctx context.Context, in *GetMicApplyRecordsReq, opts ...grpc.CallOption) (*GetMicApplyRecordsResp, error) {
	out := new(GetMicApplyRecordsResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/GetMicApplyRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelBoxServer is the server API for ChannelBox service.
type ChannelBoxServer interface {
	EnterBox(context.Context, *EnterBoxReq) (*EnterBoxResp, error)
	ExitBox(context.Context, *ExitBoxReq) (*ExitBoxResp, error)
	JoinBox(context.Context, *JoinBoxReq) (*JoinBoxResp, error)
	GetBoxInfo(context.Context, *GetBoxInfoReq) (*GetBoxInfoResp, error)
	ClearJoinBox(context.Context, *ClearJoinBoxReq) (*ClearJoinBoxResp, error)
	BatchGetBoxInfos(context.Context, *BatchGetBoxInfosReq) (*BatchGetBoxInfosResp, error)
	GetBoxInfosByLimit(context.Context, *GetBoxInfosByLimitReq) (*GetBoxInfosByLimitResp, error)
	GetBoxUserInMicInfos(context.Context, *GetBoxUserInMicInfosReq) (*GetBoxUserInMicInfosResp, error)
	ExitChannel(context.Context, *ExitChannelReq) (*ExitChannelResp, error)
	UpsertBoxInfo(context.Context, *UpsertBoxInfoReq) (*UpsertBoxInfoResp, error)
	DelBoxInfo(context.Context, *DelBoxInfoReq) (*DelBoxInfoResp, error)
	ApplyOnMicToken(context.Context, *ApplyOnMicTokenReq) (*ApplyOnMicTokenResp, error)
	SetChannelMicBoxId(context.Context, *SetChannelMicBoxIdReq) (*SetChannelMicBoxIdResp, error)
	// 麦位申请记录
	SetMicApplyRecords(context.Context, *SetMicApplyRecordsReq) (*SetMicApplyRecordsResp, error)
	GetMicApplyRecords(context.Context, *GetMicApplyRecordsReq) (*GetMicApplyRecordsResp, error)
}

func RegisterChannelBoxServer(s *grpc.Server, srv ChannelBoxServer) {
	s.RegisterService(&_ChannelBox_serviceDesc, srv)
}

func _ChannelBox_EnterBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnterBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).EnterBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/EnterBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).EnterBox(ctx, req.(*EnterBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_ExitBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExitBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).ExitBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/ExitBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).ExitBox(ctx, req.(*ExitBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_JoinBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).JoinBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/JoinBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).JoinBox(ctx, req.(*JoinBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_GetBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).GetBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/GetBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).GetBoxInfo(ctx, req.(*GetBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_ClearJoinBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearJoinBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).ClearJoinBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/ClearJoinBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).ClearJoinBox(ctx, req.(*ClearJoinBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_BatchGetBoxInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetBoxInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).BatchGetBoxInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/BatchGetBoxInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).BatchGetBoxInfos(ctx, req.(*BatchGetBoxInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_GetBoxInfosByLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBoxInfosByLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).GetBoxInfosByLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/GetBoxInfosByLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).GetBoxInfosByLimit(ctx, req.(*GetBoxInfosByLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_GetBoxUserInMicInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBoxUserInMicInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).GetBoxUserInMicInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/GetBoxUserInMicInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).GetBoxUserInMicInfos(ctx, req.(*GetBoxUserInMicInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_ExitChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExitChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).ExitChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/ExitChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).ExitChannel(ctx, req.(*ExitChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_UpsertBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).UpsertBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/UpsertBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).UpsertBoxInfo(ctx, req.(*UpsertBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_DelBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).DelBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/DelBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).DelBoxInfo(ctx, req.(*DelBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_ApplyOnMicToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyOnMicTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).ApplyOnMicToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/ApplyOnMicToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).ApplyOnMicToken(ctx, req.(*ApplyOnMicTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_SetChannelMicBoxId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMicBoxIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).SetChannelMicBoxId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/SetChannelMicBoxId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).SetChannelMicBoxId(ctx, req.(*SetChannelMicBoxIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_SetMicApplyRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMicApplyRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).SetMicApplyRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/SetMicApplyRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).SetMicApplyRecords(ctx, req.(*SetMicApplyRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_GetMicApplyRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMicApplyRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).GetMicApplyRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/GetMicApplyRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).GetMicApplyRecords(ctx, req.(*GetMicApplyRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelBox_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelbox.ChannelBox",
	HandlerType: (*ChannelBoxServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "EnterBox",
			Handler:    _ChannelBox_EnterBox_Handler,
		},
		{
			MethodName: "ExitBox",
			Handler:    _ChannelBox_ExitBox_Handler,
		},
		{
			MethodName: "JoinBox",
			Handler:    _ChannelBox_JoinBox_Handler,
		},
		{
			MethodName: "GetBoxInfo",
			Handler:    _ChannelBox_GetBoxInfo_Handler,
		},
		{
			MethodName: "ClearJoinBox",
			Handler:    _ChannelBox_ClearJoinBox_Handler,
		},
		{
			MethodName: "BatchGetBoxInfos",
			Handler:    _ChannelBox_BatchGetBoxInfos_Handler,
		},
		{
			MethodName: "GetBoxInfosByLimit",
			Handler:    _ChannelBox_GetBoxInfosByLimit_Handler,
		},
		{
			MethodName: "GetBoxUserInMicInfos",
			Handler:    _ChannelBox_GetBoxUserInMicInfos_Handler,
		},
		{
			MethodName: "ExitChannel",
			Handler:    _ChannelBox_ExitChannel_Handler,
		},
		{
			MethodName: "UpsertBoxInfo",
			Handler:    _ChannelBox_UpsertBoxInfo_Handler,
		},
		{
			MethodName: "DelBoxInfo",
			Handler:    _ChannelBox_DelBoxInfo_Handler,
		},
		{
			MethodName: "ApplyOnMicToken",
			Handler:    _ChannelBox_ApplyOnMicToken_Handler,
		},
		{
			MethodName: "SetChannelMicBoxId",
			Handler:    _ChannelBox_SetChannelMicBoxId_Handler,
		},
		{
			MethodName: "SetMicApplyRecords",
			Handler:    _ChannelBox_SetMicApplyRecords_Handler,
		},
		{
			MethodName: "GetMicApplyRecords",
			Handler:    _ChannelBox_GetMicApplyRecords_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channelbox/channelbox.proto",
}

func init() {
	proto.RegisterFile("channelbox/channelbox.proto", fileDescriptor_channelbox_beaa8997d6e457ed)
}

var fileDescriptor_channelbox_beaa8997d6e457ed = []byte{
	// 1744 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x58, 0x4b, 0x73, 0x23, 0x49,
	0x11, 0x76, 0x4b, 0xb2, 0x25, 0xa5, 0x9e, 0x2e, 0x3f, 0x24, 0xcb, 0xbb, 0x8c, 0xa7, 0x61, 0xc1,
	0x38, 0x16, 0x0f, 0x78, 0x1f, 0x11, 0xbb, 0x0b, 0x01, 0x96, 0x77, 0x18, 0x3c, 0x8c, 0x0d, 0xd1,
	0xb6, 0x63, 0x22, 0x88, 0x20, 0x14, 0xad, 0x56, 0x8d, 0xa7, 0x66, 0xba, 0xbb, 0x7a, 0x54, 0x2d,
	0x23, 0x5f, 0xb8, 0x11, 0x73, 0xe4, 0xc2, 0x81, 0x03, 0x11, 0xfc, 0x06, 0xce, 0xfc, 0x87, 0xe1,
	0xc0, 0x1f, 0x22, 0x2a, 0xab, 0x1f, 0xd5, 0x6a, 0xf9, 0xa1, 0x61, 0x0e, 0x04, 0xb7, 0xae, 0xcc,
	0xec, 0xac, 0xac, 0x2f, 0xbf, 0xac, 0xcc, 0x6e, 0xd8, 0x76, 0x5e, 0xda, 0xbe, 0x4f, 0xdd, 0x21,
	0x9f, 0x3e, 0x4a, 0x1f, 0xf7, 0x83, 0x31, 0x0f, 0x39, 0x81, 0x54, 0x62, 0xfe, 0xa5, 0x00, 0xe5,
	0x3e, 0x9f, 0x1e, 0xfb, 0x2f, 0x38, 0xf9, 0x18, 0x62, 0xcd, 0x80, 0x8d, 0xba, 0xc6, 0x8e, 0xb1,
	0xdb, 0xb0, 0xaa, 0x91, 0xe4, 0x78, 0x44, 0x36, 0x60, 0x65, 0xc8, 0xa7, 0x52, 0x55, 0x40, 0xd5,
	0xf2, 0x90, 0x4f, 0x8f, 0x47, 0xe4, 0x01, 0xd4, 0x9c, 0x31, 0xb5, 0x43, 0x3e, 0x1e, 0x4c, 0xd8,
	0xa8, 0x5b, 0x44, 0x1d, 0x44, 0xa2, 0x0b, 0x36, 0x22, 0xfb, 0x50, 0x91, 0xef, 0x85, 0xd7, 0x01,
	0xed, 0x96, 0x76, 0x8c, 0xdd, 0xe6, 0xc1, 0xda, 0xbe, 0x16, 0x53, 0x9f, 0x4f, 0xcf, 0xaf, 0x03,
	0x6a, 0x95, 0x87, 0xea, 0x81, 0x6c, 0x29, 0x7b, 0xdf, 0xf6, 0x68, 0x77, 0x79, 0xc7, 0xd8, 0xad,
	0xa2, 0xea, 0xd4, 0xf6, 0x28, 0xe9, 0x40, 0xd9, 0x63, 0xce, 0xc0, 0xb1, 0x83, 0xee, 0x0a, 0xee,
	0xb3, 0xe2, 0x31, 0xe7, 0xc8, 0x0e, 0xc8, 0xf7, 0xa1, 0x15, 0x4c, 0x86, 0x2e, 0x73, 0x06, 0x52,
	0xef, 0x32, 0x11, 0x76, 0xcb, 0x3b, 0xc5, 0xdd, 0x86, 0xd5, 0x50, 0xe2, 0x13, 0xe6, 0x3c, 0x63,
	0x22, 0x94, 0x76, 0x3e, 0x1f, 0x7b, 0xb6, 0x9b, 0xda, 0x55, 0x94, 0x9d, 0x12, 0x47, 0x76, 0xe6,
	0xbf, 0x0c, 0xa8, 0x3d, 0xf6, 0x43, 0x3a, 0xee, 0xf3, 0xa9, 0x45, 0xdf, 0x90, 0x36, 0x14, 0x27,
	0x09, 0x26, 0xf2, 0x91, 0x7c, 0x04, 0x31, 0x34, 0x09, 0x20, 0xa9, 0x80, 0xac, 0x83, 0x44, 0x27,
	0x81, 0x43, 0x2d, 0x16, 0x46, 0xe2, 0x21, 0xd4, 0x1d, 0xee, 0x79, 0xdc, 0x1f, 0x28, 0x67, 0xcb,
	0xe8, 0xac, 0xa6, 0x64, 0x7d, 0x74, 0xf9, 0x03, 0x68, 0x0d, 0xc7, 0xdc, 0x1e, 0x39, 0xb6, 0x08,
	0x23, 0x2b, 0x85, 0x4c, 0x33, 0x11, 0xa3, 0xa1, 0xf9, 0x77, 0x03, 0xea, 0xe9, 0x89, 0x44, 0x40,
	0xb6, 0xa1, 0xca, 0xc4, 0x40, 0xfc, 0x81, 0x85, 0xce, 0x4b, 0x3c, 0x58, 0xc5, 0xaa, 0x30, 0x71,
	0x86, 0x6b, 0xf2, 0x0b, 0x68, 0x51, 0x69, 0x3c, 0xc0, 0x8c, 0xfb, 0x2f, 0xb8, 0xe8, 0x16, 0x76,
	0x8a, 0xbb, 0xb5, 0x83, 0xae, 0x1e, 0x70, 0xec, 0x4f, 0xb2, 0xc7, 0x6a, 0x50, 0x6d, 0x25, 0x08,
	0x81, 0x12, 0x0f, 0x42, 0x81, 0x00, 0x94, 0x2c, 0x7c, 0xce, 0x62, 0x56, 0x9a, 0xc1, 0xcc, 0xfc,
	0x9b, 0x16, 0x21, 0xf2, 0xf1, 0x2b, 0x0d, 0x2e, 0x03, 0xe1, 0xfa, 0x4e, 0x6e, 0x77, 0x8b, 0x8a,
	0x89, 0x1b, 0xe6, 0x90, 0x4b, 0xf0, 0x2f, 0xe8, 0xf8, 0x77, 0x40, 0x1a, 0x0c, 0x1c, 0x3f, 0x8c,
	0xf2, 0x22, 0x09, 0x7d, 0xe4, 0x87, 0xc4, 0x84, 0x86, 0x3d, 0x19, 0x31, 0xae, 0x8e, 0x3b, 0x12,
	0xdd, 0x12, 0x92, 0xa2, 0x86, 0x42, 0x19, 0xce, 0x48, 0x98, 0xef, 0x0c, 0x80, 0xc7, 0x53, 0x16,
	0xfe, 0xdf, 0x30, 0xe2, 0x6b, 0xa8, 0x25, 0xe7, 0x11, 0x81, 0x0e, 0x8e, 0x91, 0x01, 0x27, 0xce,
	0x64, 0x21, 0xcd, 0xa4, 0xf9, 0x67, 0x03, 0xe0, 0x29, 0x67, 0xfe, 0x07, 0x05, 0xe3, 0x1b, 0x68,
	0xbc, 0xe2, 0x0c, 0x8f, 0xa6, 0x23, 0xd2, 0xd1, 0x11, 0x89, 0x36, 0x45, 0x54, 0x6a, 0xaf, 0xd2,
	0x85, 0xf9, 0x4f, 0x03, 0x6a, 0x49, 0x44, 0xff, 0x83, 0xf4, 0x8e, 0x02, 0x62, 0x97, 0x3e, 0x1f,
	0xab, 0x7b, 0x0d, 0x03, 0x3a, 0xc6, 0xb5, 0xe9, 0x42, 0xad, 0xcf, 0xa7, 0x7d, 0x5b, 0x50, 0x64,
	0x7e, 0x82, 0x8f, 0xa1, 0xe3, 0x13, 0xa1, 0x5c, 0x48, 0x51, 0x9e, 0x17, 0xc5, 0x43, 0xa8, 0x27,
	0x5c, 0x9e, 0x43, 0x65, 0x36, 0x12, 0xe6, 0x10, 0xa0, 0x8f, 0xb9, 0xbd, 0x65, 0xb3, 0x1d, 0xa8,
	0x4b, 0x70, 0x26, 0x82, 0x8e, 0x91, 0x13, 0x6a, 0x57, 0x18, 0xf2, 0xe9, 0x85, 0xa0, 0x63, 0xc9,
	0x8b, 0xcc, 0x71, 0x8b, 0xb3, 0xd5, 0x7c, 0x05, 0x8d, 0x27, 0x34, 0x8c, 0xd1, 0xa3, 0x6f, 0x24,
	0xbf, 0x78, 0x40, 0x07, 0x29, 0x4f, 0x56, 0x78, 0x40, 0x2f, 0xee, 0xa4, 0x8a, 0x5e, 0x21, 0xc5,
	0xbb, 0x2b, 0xc4, 0x7c, 0x5b, 0x80, 0xa6, 0xbe, 0xb1, 0x08, 0xc8, 0x4f, 0x94, 0x0b, 0x99, 0xe7,
	0xae, 0x81, 0x69, 0xde, 0x9c, 0x71, 0x11, 0x41, 0x81, 0x5e, 0xe2, 0x56, 0x88, 0x27, 0xd7, 0x2f,
	0x91, 0xaa, 0x94, 0xf4, 0xf5, 0xb2, 0xc5, 0xfe, 0x51, 0x44, 0x8f, 0xb3, 0x41, 0x25, 0xee, 0xb0,
	0xed, 0x6c, 0x43, 0x55, 0xf6, 0x9b, 0x90, 0x87, 0xb6, 0x1b, 0x31, 0xa3, 0xe2, 0x31, 0xe7, 0x5c,
	0xae, 0xc9, 0x1e, 0xac, 0xe2, 0x5e, 0x99, 0xac, 0x2d, 0x63, 0xd6, 0x5a, 0x52, 0x71, 0x98, 0x66,
	0x4e, 0xda, 0x7a, 0x36, 0xf3, 0x07, 0x99, 0x4b, 0x40, 0x95, 0x77, 0x4b, 0x2a, 0x8e, 0xd2, 0x8b,
	0xc0, 0x7c, 0x6b, 0x40, 0xeb, 0xc8, 0xa5, 0xf6, 0xf8, 0x83, 0x17, 0xea, 0x82, 0xb7, 0x96, 0x49,
	0xa0, 0x9d, 0x0d, 0x44, 0x04, 0xe6, 0x1f, 0x61, 0xad, 0x6f, 0x87, 0xce, 0xcb, 0x34, 0x57, 0x42,
	0x06, 0xb8, 0x05, 0x95, 0x09, 0x1b, 0x29, 0x64, 0x0d, 0xc4, 0xa0, 0x3c, 0x61, 0x23, 0x04, 0xf1,
	0xc3, 0xf2, 0xe4, 0x02, 0xd6, 0xf3, 0xfb, 0x8b, 0x80, 0xfc, 0x0c, 0x9a, 0xd2, 0xcf, 0xd0, 0x16,
	0x34, 0xba, 0x19, 0x14, 0x65, 0x3a, 0x33, 0xde, 0xe2, 0x5a, 0xb5, 0x64, 0x99, 0xc4, 0x0b, 0x61,
	0xfe, 0xc3, 0x80, 0x0d, 0xcd, 0x65, 0xff, 0xfa, 0x19, 0xf3, 0x58, 0x28, 0x4f, 0x96, 0x09, 0xdf,
	0xb8, 0x11, 0xe8, 0x4c, 0xc3, 0xda, 0x86, 0xaa, 0x2b, 0xaf, 0xf1, 0x90, 0x79, 0xea, 0x54, 0x45,
	0xab, 0x22, 0x05, 0xe7, 0x4c, 0x0d, 0x43, 0x97, 0x34, 0xc4, 0xe2, 0x2c, 0xa1, 0x6a, 0xe5, 0x92,
	0x86, 0xb2, 0x30, 0x75, 0x28, 0x96, 0xef, 0x01, 0xc5, 0x73, 0xd8, 0x9c, 0x17, 0xf2, 0x7f, 0x0f,
	0xc6, 0x5f, 0x0d, 0xe8, 0x28, 0xcf, 0xf2, 0xce, 0x38, 0xf6, 0x4f, 0x98, 0x93, 0x24, 0xfa, 0x7d,
	0xe0, 0xd8, 0x02, 0x59, 0x35, 0xf2, 0x0a, 0x11, 0x58, 0x76, 0x0d, 0x4b, 0x8e, 0x83, 0x17, 0xb2,
	0x30, 0x16, 0xa5, 0xe4, 0x17, 0xd0, 0x9d, 0x1f, 0x99, 0x08, 0xe2, 0x01, 0x14, 0xb7, 0x89, 0x38,
	0x28, 0xaf, 0x3d, 0x79, 0x73, 0x06, 0xd0, 0x94, 0x3d, 0xf3, 0x48, 0x79, 0x7e, 0x9f, 0x8a, 0x5a,
	0x94, 0xa7, 0x7f, 0x32, 0xa0, 0x95, 0xd9, 0x72, 0xc1, 0x56, 0xad, 0x8d, 0xed, 0x45, 0x7d, 0x6c,
	0x5f, 0x14, 0xb0, 0xaf, 0xa0, 0x7d, 0x11, 0x08, 0x3a, 0xd6, 0xaf, 0xf4, 0x4f, 0xa0, 0x38, 0xe4,
	0x53, 0x8c, 0xe1, 0x86, 0x1b, 0x50, 0xea, 0xcd, 0xaf, 0x61, 0x75, 0xe6, 0x55, 0x11, 0xdc, 0xf7,
	0xdd, 0x09, 0x34, 0xbe, 0xa5, 0xae, 0xb6, 0xe7, 0xfb, 0x7d, 0xa4, 0x2c, 0x8a, 0xfa, 0x0f, 0xa1,
	0xa9, 0x6f, 0x9b, 0x62, 0xee, 0x4f, 0x3c, 0x0d, 0xf3, 0xd3, 0x89, 0x67, 0x3e, 0x05, 0x72, 0x18,
	0x04, 0xee, 0xf5, 0x6f, 0x24, 0x89, 0xce, 0xf9, 0x6b, 0xea, 0xdf, 0x2f, 0x4c, 0xc9, 0xe4, 0x34,
	0x4c, 0x8f, 0x39, 0xc7, 0x23, 0xf3, 0x14, 0xd6, 0x72, 0xbe, 0x44, 0x20, 0x7b, 0x31, 0xf7, 0x07,
	0xaa, 0x83, 0xbc, 0xa6, 0x3e, 0xba, 0xab, 0x5a, 0xc0, 0x13, 0xab, 0x9b, 0xfc, 0x5d, 0xc1, 0xc6,
	0x19, 0x8d, 0xa9, 0x73, 0xc2, 0x1c, 0x1c, 0x65, 0xef, 0x11, 0xde, 0x27, 0xd0, 0xe2, 0x01, 0x55,
	0x5b, 0x66, 0xe0, 0xac, 0x4b, 0x71, 0xec, 0x48, 0x6f, 0xe9, 0x45, 0xbd, 0xa5, 0x9b, 0x9f, 0xc2,
	0xe6, 0xbc, 0x7d, 0x45, 0x90, 0x30, 0xd4, 0xc0, 0x1b, 0x4b, 0x0d, 0x93, 0xff, 0x36, 0xa0, 0x79,
	0xc2, 0x1c, 0x3c, 0xb9, 0x45, 0x1d, 0x3e, 0xd6, 0xf1, 0x31, 0xb4, 0xf3, 0xc8, 0x34, 0x22, 0x0a,
	0x32, 0x8d, 0x85, 0x7c, 0x1a, 0x25, 0x1c, 0x98, 0x46, 0x4f, 0x3d, 0x90, 0xcf, 0x01, 0xa4, 0xbd,
	0x08, 0xed, 0x70, 0x22, 0xa2, 0xc4, 0x6f, 0xcc, 0xbc, 0x71, 0x86, 0x4a, 0x4b, 0x36, 0x68, 0xf5,
	0x18, 0x97, 0x74, 0x29, 0x2d, 0xe9, 0x94, 0x55, 0xcb, 0x3a, 0xab, 0xb6, 0xa1, 0x3a, 0x09, 0x46,
	0x76, 0x48, 0x07, 0x76, 0x18, 0x75, 0xe1, 0x8a, 0x12, 0x1c, 0x86, 0xa6, 0x8b, 0xd8, 0x67, 0xcf,
	0x25, 0xee, 0x81, 0xfd, 0xe7, 0x50, 0x1e, 0x2b, 0xe3, 0x68, 0x26, 0xed, 0xcd, 0x04, 0xac, 0xf9,
	0xb3, 0x62, 0x53, 0xb3, 0x8b, 0x88, 0xe7, 0x76, 0x13, 0x81, 0xf9, 0x25, 0x36, 0xa4, 0x85, 0xe3,
	0x30, 0x4f, 0xb1, 0x2b, 0xcc, 0xf1, 0xa8, 0x47, 0x68, 0xdc, 0x3b, 0xc2, 0xbd, 0x1f, 0xe3, 0x8f,
	0x06, 0x4c, 0xcb, 0x1a, 0xb4, 0xe2, 0x8a, 0xe3, 0x2e, 0x0d, 0x5c, 0xfb, 0xba, 0xbd, 0x44, 0xda,
	0x50, 0x8f, 0x84, 0x27, 0xd4, 0xa5, 0xb4, 0x6d, 0xec, 0x7d, 0x06, 0x24, 0xff, 0x8d, 0x47, 0x1a,
	0x50, 0x45, 0xa9, 0x5c, 0xb4, 0x97, 0x48, 0x13, 0x40, 0x0d, 0xf5, 0xb8, 0x36, 0xf6, 0xbe, 0x49,
	0x3e, 0x03, 0xd0, 0xba, 0x0e, 0x95, 0x53, 0xee, 0xd3, 0xd4, 0xf8, 0xd0, 0x71, 0x68, 0x10, 0x2a,
	0x63, 0xb9, 0xb6, 0xe8, 0x2b, 0xea, 0xa8, 0x75, 0x61, 0xef, 0x47, 0x50, 0x8e, 0x38, 0x44, 0x5a,
	0x50, 0x8b, 0x1e, 0x7f, 0x39, 0xa6, 0xf2, 0xdd, 0x55, 0x68, 0x44, 0x82, 0xdf, 0xe2, 0x2f, 0x85,
	0xb6, 0xb1, 0xf7, 0x25, 0x54, 0x13, 0x02, 0x91, 0x0d, 0x58, 0x4d, 0x16, 0x08, 0x00, 0xf3, 0x2f,
	0xdb, 0x4b, 0x84, 0x20, 0xb7, 0x95, 0xf8, 0x42, 0x48, 0x99, 0xb1, 0x77, 0x05, 0xb5, 0xb3, 0x6b,
	0x11, 0x52, 0x4f, 0xd6, 0xc5, 0xb7, 0x64, 0x13, 0x88, 0xb6, 0x3c, 0xf6, 0xaf, 0x6c, 0x97, 0x8d,
	0xda, 0x4b, 0x64, 0x0b, 0x36, 0x34, 0xf9, 0xd9, 0x64, 0x18, 0x15, 0x54, 0xbb, 0x40, 0x7a, 0xb0,
	0xa9, 0xa9, 0x4e, 0xe4, 0xe4, 0x17, 0xe9, 0x8a, 0xa4, 0x0b, 0xeb, 0x9a, 0xae, 0x1f, 0x7f, 0xf4,
	0xb5, 0x4b, 0x07, 0xef, 0xaa, 0x00, 0x91, 0x5d, 0x9f, 0x4f, 0xc9, 0xcf, 0xa1, 0x12, 0x7f, 0xe2,
	0x90, 0xce, 0xbc, 0x0f, 0x1f, 0x8b, 0xbe, 0xe9, 0x75, 0xe7, 0x2b, 0x44, 0x60, 0x2e, 0x91, 0x9f,
	0x42, 0x39, 0xfa, 0x82, 0x24, 0x99, 0x89, 0x3a, 0xfd, 0x4c, 0xee, 0x75, 0xe6, 0xca, 0xe3, 0xb7,
	0xa3, 0x4c, 0x65, 0xdf, 0x4e, 0xc7, 0xd5, 0x5e, 0x67, 0xae, 0x1c, 0xdf, 0x7e, 0x0c, 0x90, 0x0e,
	0x2d, 0x64, 0x4b, 0x37, 0xcc, 0x7c, 0x77, 0xf4, 0x7a, 0x37, 0xa9, 0xd0, 0xcd, 0xaf, 0xa1, 0xae,
	0x8f, 0xa6, 0x64, 0x5b, 0xb7, 0x9e, 0x99, 0x9e, 0x7b, 0x1f, 0xdd, 0xac, 0x44, 0x67, 0xcf, 0xa1,
	0x3d, 0x3b, 0x53, 0x92, 0x07, 0x99, 0x3e, 0x93, 0x9f, 0x78, 0x7b, 0x3b, 0xb7, 0x1b, 0xa0, 0xe3,
	0xdf, 0x03, 0xc9, 0x4f, 0x68, 0xe4, 0xe1, 0xfc, 0x93, 0x69, 0x43, 0x67, 0xcf, 0xbc, 0xcb, 0x04,
	0xdd, 0x3b, 0xb0, 0x3e, 0x6f, 0x18, 0x22, 0xdf, 0xcd, 0xbf, 0x9d, 0x1b, 0xe4, 0x7a, 0xdf, 0xbb,
	0xdb, 0x08, 0x37, 0xf9, 0x95, 0xfa, 0xdd, 0x10, 0xf1, 0x8f, 0xf4, 0x66, 0x89, 0x91, 0xce, 0x54,
	0xbd, 0xed, 0x1b, 0x75, 0xe8, 0xe9, 0x14, 0x1a, 0x99, 0x79, 0x82, 0x64, 0xf2, 0x32, 0x3b, 0xa5,
	0xf4, 0x3e, 0xbe, 0x45, 0x1b, 0x53, 0x29, 0x6d, 0xf6, 0x59, 0x2a, 0x65, 0x66, 0x8f, 0x2c, 0x95,
	0xb2, 0xf3, 0x81, 0xb9, 0x44, 0xce, 0xa1, 0x35, 0xd3, 0xbc, 0x49, 0xe6, 0x7f, 0x55, 0x7e, 0x4a,
	0xe8, 0x3d, 0xb8, 0x55, 0x1f, 0xa7, 0x3e, 0xdf, 0x4a, 0xb3, 0xa9, 0x9f, 0xdb, 0xe2, 0xb3, 0xa9,
	0x9f, 0xdf, 0x8d, 0x13, 0xf7, 0x33, 0xb7, 0x7c, 0xce, 0x7d, 0xbe, 0x7b, 0xe4, 0xdc, 0xcf, 0x6b,
	0x3d, 0x31, 0x71, 0x6f, 0x75, 0xff, 0xe4, 0x6e, 0xf7, 0x4f, 0x6e, 0x70, 0xdf, 0xdf, 0xff, 0xdd,
	0xa7, 0x97, 0xdc, 0xb5, 0xfd, 0xcb, 0xfd, 0x2f, 0x0e, 0xc2, 0x70, 0xdf, 0xe1, 0xde, 0x23, 0xfc,
	0xc5, 0xed, 0x70, 0xf7, 0x91, 0xa0, 0xe3, 0x2b, 0xe6, 0x50, 0xa1, 0xfd, 0xff, 0x1e, 0xae, 0xa0,
	0xf6, 0xb3, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0x38, 0xbb, 0x67, 0xc6, 0x1f, 0x17, 0x00, 0x00,
}
