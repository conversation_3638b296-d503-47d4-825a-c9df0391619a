// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channelbox/channelbox.proto

package channelbox // import "golang.52tt.com/protocol/services/channelbox"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BoxType int32

const (
	BoxType_BoxTypeRoleplay BoxType = 0
	BoxType_BoxTypeMelee    BoxType = 1
)

var BoxType_name = map[int32]string{
	0: "BoxTypeRoleplay",
	1: "BoxTypeMelee",
}
var BoxType_value = map[string]int32{
	"BoxTypeRoleplay": 0,
	"BoxTypeMelee":    1,
}

func (x BoxType) String() string {
	return proto.EnumName(BoxType_name, int32(x))
}
func (BoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{0}
}

type EnterResultBoxType int32

const (
	EnterResultBoxType_EnterType  EnterResultBoxType = 0
	EnterResultBoxType_SwitchType EnterResultBoxType = 1
)

var EnterResultBoxType_name = map[int32]string{
	0: "EnterType",
	1: "SwitchType",
}
var EnterResultBoxType_value = map[string]int32{
	"EnterType":  0,
	"SwitchType": 1,
}

func (x EnterResultBoxType) String() string {
	return proto.EnumName(EnterResultBoxType_name, int32(x))
}
func (EnterResultBoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{1}
}

type JoinBoxType int32

const (
	JoinBoxType_NoneType   JoinBoxType = 0
	JoinBoxType_AcceptType JoinBoxType = 1
	JoinBoxType_RejectType JoinBoxType = 2
)

var JoinBoxType_name = map[int32]string{
	0: "NoneType",
	1: "AcceptType",
	2: "RejectType",
}
var JoinBoxType_value = map[string]int32{
	"NoneType":   0,
	"AcceptType": 1,
	"RejectType": 2,
}

func (x JoinBoxType) String() string {
	return proto.EnumName(JoinBoxType_name, int32(x))
}
func (JoinBoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{2}
}

type MicType int32

const (
	// 自由麦
	MicType_MicTypeFree MicType = 0
	// 公共麦
	MicType_MicTypePublic MicType = 1
)

var MicType_name = map[int32]string{
	0: "MicTypeFree",
	1: "MicTypePublic",
}
var MicType_value = map[string]int32{
	"MicTypeFree":   0,
	"MicTypePublic": 1,
}

func (x MicType) String() string {
	return proto.EnumName(MicType_name, int32(x))
}
func (MicType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{3}
}

// 麦位状态
type MicStatus int32

const (
	// 申请中
	MicStatus_MicStatusApplying MicStatus = 0
	// 试用中
	MicStatus_MicStatusUsing MicStatus = 1
)

var MicStatus_name = map[int32]string{
	0: "MicStatusApplying",
	1: "MicStatusUsing",
}
var MicStatus_value = map[string]int32{
	"MicStatusApplying": 0,
	"MicStatusUsing":    1,
}

func (x MicStatus) String() string {
	return proto.EnumName(MicStatus_name, int32(x))
}
func (MicStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{4}
}

// 1000以内系统频道ID
type SystemBoxID int32

const (
	SystemBoxID_SystemBoxIDInvalid SystemBoxID = 0
	// 子频道公共频道
	SystemBoxID_SystemBoxIDSubChannel SystemBoxID = 2
	// 主房间公共频道
	SystemBoxID_SystemBoxIDMainChannel SystemBoxID = 3
	// 广播频道
	SystemBoxID_SystemBoxIDBroadcast SystemBoxID = 4
)

var SystemBoxID_name = map[int32]string{
	0: "SystemBoxIDInvalid",
	2: "SystemBoxIDSubChannel",
	3: "SystemBoxIDMainChannel",
	4: "SystemBoxIDBroadcast",
}
var SystemBoxID_value = map[string]int32{
	"SystemBoxIDInvalid":     0,
	"SystemBoxIDSubChannel":  2,
	"SystemBoxIDMainChannel": 3,
	"SystemBoxIDBroadcast":   4,
}

func (x SystemBoxID) String() string {
	return proto.EnumName(SystemBoxID_name, int32(x))
}
func (SystemBoxID) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{5}
}

type BoxInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BoxId                uint32   `protobuf:"varint,2,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	CreatorUid           uint32   `protobuf:"varint,3,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	BoxName              string   `protobuf:"bytes,5,opt,name=box_name,json=boxName,proto3" json:"box_name,omitempty"`
	MicCap               uint32   `protobuf:"varint,6,opt,name=mic_cap,json=micCap,proto3" json:"mic_cap,omitempty"`
	PublicMicList        []uint32 `protobuf:"varint,7,rep,packed,name=public_mic_list,json=publicMicList,proto3" json:"public_mic_list,omitempty"`
	NormalMicList        []uint32 `protobuf:"varint,8,rep,packed,name=normal_mic_list,json=normalMicList,proto3" json:"normal_mic_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxInfo) Reset()         { *m = BoxInfo{} }
func (m *BoxInfo) String() string { return proto.CompactTextString(m) }
func (*BoxInfo) ProtoMessage()    {}
func (*BoxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{0}
}
func (m *BoxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxInfo.Unmarshal(m, b)
}
func (m *BoxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxInfo.Marshal(b, m, deterministic)
}
func (dst *BoxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxInfo.Merge(dst, src)
}
func (m *BoxInfo) XXX_Size() int {
	return xxx_messageInfo_BoxInfo.Size(m)
}
func (m *BoxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BoxInfo proto.InternalMessageInfo

func (m *BoxInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BoxInfo) GetBoxId() uint32 {
	if m != nil {
		return m.BoxId
	}
	return 0
}

func (m *BoxInfo) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *BoxInfo) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *BoxInfo) GetBoxName() string {
	if m != nil {
		return m.BoxName
	}
	return ""
}

func (m *BoxInfo) GetMicCap() uint32 {
	if m != nil {
		return m.MicCap
	}
	return 0
}

func (m *BoxInfo) GetPublicMicList() []uint32 {
	if m != nil {
		return m.PublicMicList
	}
	return nil
}

func (m *BoxInfo) GetNormalMicList() []uint32 {
	if m != nil {
		return m.NormalMicList
	}
	return nil
}

type EnterBoxReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	CommonBoxid          uint32   `protobuf:"varint,5,opt,name=common_boxid,json=commonBoxid,proto3" json:"common_boxid,omitempty"`
	BroadcastBoxid       uint32   `protobuf:"varint,6,opt,name=broadcast_boxid,json=broadcastBoxid,proto3" json:"broadcast_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnterBoxReq) Reset()         { *m = EnterBoxReq{} }
func (m *EnterBoxReq) String() string { return proto.CompactTextString(m) }
func (*EnterBoxReq) ProtoMessage()    {}
func (*EnterBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{1}
}
func (m *EnterBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterBoxReq.Unmarshal(m, b)
}
func (m *EnterBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterBoxReq.Marshal(b, m, deterministic)
}
func (dst *EnterBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterBoxReq.Merge(dst, src)
}
func (m *EnterBoxReq) XXX_Size() int {
	return xxx_messageInfo_EnterBoxReq.Size(m)
}
func (m *EnterBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_EnterBoxReq proto.InternalMessageInfo

func (m *EnterBoxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EnterBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *EnterBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *EnterBoxReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *EnterBoxReq) GetCommonBoxid() uint32 {
	if m != nil {
		return m.CommonBoxid
	}
	return 0
}

func (m *EnterBoxReq) GetBroadcastBoxid() uint32 {
	if m != nil {
		return m.BroadcastBoxid
	}
	return 0
}

type EnterBoxResp struct {
	IsSwitch             bool            `protobuf:"varint,1,opt,name=is_switch,json=isSwitch,proto3" json:"is_switch,omitempty"`
	EnterBoxInfos        []*EnterBoxInfo `protobuf:"bytes,2,rep,name=enter_box_infos,json=enterBoxInfos,proto3" json:"enter_box_infos,omitempty"`
	Opts                 uint64          `protobuf:"varint,3,opt,name=opts,proto3" json:"opts,omitempty"`
	Channelid            uint32          `protobuf:"varint,4,opt,name=channelid,proto3" json:"channelid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *EnterBoxResp) Reset()         { *m = EnterBoxResp{} }
func (m *EnterBoxResp) String() string { return proto.CompactTextString(m) }
func (*EnterBoxResp) ProtoMessage()    {}
func (*EnterBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{2}
}
func (m *EnterBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterBoxResp.Unmarshal(m, b)
}
func (m *EnterBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterBoxResp.Marshal(b, m, deterministic)
}
func (dst *EnterBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterBoxResp.Merge(dst, src)
}
func (m *EnterBoxResp) XXX_Size() int {
	return xxx_messageInfo_EnterBoxResp.Size(m)
}
func (m *EnterBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_EnterBoxResp proto.InternalMessageInfo

func (m *EnterBoxResp) GetIsSwitch() bool {
	if m != nil {
		return m.IsSwitch
	}
	return false
}

func (m *EnterBoxResp) GetEnterBoxInfos() []*EnterBoxInfo {
	if m != nil {
		return m.EnterBoxInfos
	}
	return nil
}

func (m *EnterBoxResp) GetOpts() uint64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

func (m *EnterBoxResp) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

type EnterBoxInfo struct {
	BoxType              EnterResultBoxType `protobuf:"varint,1,opt,name=box_type,json=boxType,proto3,enum=channelbox.EnterResultBoxType" json:"box_type,omitempty"`
	Boxid                uint32             `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxCnt               uint32             `protobuf:"varint,3,opt,name=box_cnt,json=boxCnt,proto3" json:"box_cnt,omitempty"`
	AudioBoxIds          []uint32           `protobuf:"varint,4,rep,packed,name=audio_box_ids,json=audioBoxIds,proto3" json:"audio_box_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *EnterBoxInfo) Reset()         { *m = EnterBoxInfo{} }
func (m *EnterBoxInfo) String() string { return proto.CompactTextString(m) }
func (*EnterBoxInfo) ProtoMessage()    {}
func (*EnterBoxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{3}
}
func (m *EnterBoxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterBoxInfo.Unmarshal(m, b)
}
func (m *EnterBoxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterBoxInfo.Marshal(b, m, deterministic)
}
func (dst *EnterBoxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterBoxInfo.Merge(dst, src)
}
func (m *EnterBoxInfo) XXX_Size() int {
	return xxx_messageInfo_EnterBoxInfo.Size(m)
}
func (m *EnterBoxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterBoxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EnterBoxInfo proto.InternalMessageInfo

func (m *EnterBoxInfo) GetBoxType() EnterResultBoxType {
	if m != nil {
		return m.BoxType
	}
	return EnterResultBoxType_EnterType
}

func (m *EnterBoxInfo) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *EnterBoxInfo) GetBoxCnt() uint32 {
	if m != nil {
		return m.BoxCnt
	}
	return 0
}

func (m *EnterBoxInfo) GetAudioBoxIds() []uint32 {
	if m != nil {
		return m.AudioBoxIds
	}
	return nil
}

type ExitBoxReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	CommonBoxid          uint32   `protobuf:"varint,5,opt,name=common_boxid,json=commonBoxid,proto3" json:"common_boxid,omitempty"`
	BroadcastBoxid       uint32   `protobuf:"varint,6,opt,name=broadcast_boxid,json=broadcastBoxid,proto3" json:"broadcast_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExitBoxReq) Reset()         { *m = ExitBoxReq{} }
func (m *ExitBoxReq) String() string { return proto.CompactTextString(m) }
func (*ExitBoxReq) ProtoMessage()    {}
func (*ExitBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{4}
}
func (m *ExitBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitBoxReq.Unmarshal(m, b)
}
func (m *ExitBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitBoxReq.Marshal(b, m, deterministic)
}
func (dst *ExitBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitBoxReq.Merge(dst, src)
}
func (m *ExitBoxReq) XXX_Size() int {
	return xxx_messageInfo_ExitBoxReq.Size(m)
}
func (m *ExitBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExitBoxReq proto.InternalMessageInfo

func (m *ExitBoxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExitBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ExitBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *ExitBoxReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *ExitBoxReq) GetCommonBoxid() uint32 {
	if m != nil {
		return m.CommonBoxid
	}
	return 0
}

func (m *ExitBoxReq) GetBroadcastBoxid() uint32 {
	if m != nil {
		return m.BroadcastBoxid
	}
	return 0
}

type ExitBoxResp struct {
	BoxCnt               uint32   `protobuf:"varint,1,opt,name=box_cnt,json=boxCnt,proto3" json:"box_cnt,omitempty"`
	Opts                 uint64   `protobuf:"varint,2,opt,name=opts,proto3" json:"opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExitBoxResp) Reset()         { *m = ExitBoxResp{} }
func (m *ExitBoxResp) String() string { return proto.CompactTextString(m) }
func (*ExitBoxResp) ProtoMessage()    {}
func (*ExitBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{5}
}
func (m *ExitBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitBoxResp.Unmarshal(m, b)
}
func (m *ExitBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitBoxResp.Marshal(b, m, deterministic)
}
func (dst *ExitBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitBoxResp.Merge(dst, src)
}
func (m *ExitBoxResp) XXX_Size() int {
	return xxx_messageInfo_ExitBoxResp.Size(m)
}
func (m *ExitBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExitBoxResp proto.InternalMessageInfo

func (m *ExitBoxResp) GetBoxCnt() uint32 {
	if m != nil {
		return m.BoxCnt
	}
	return 0
}

func (m *ExitBoxResp) GetOpts() uint64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

type JoinBoxReq struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channelid            uint32      `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32      `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	JoinBoxType          JoinBoxType `protobuf:"varint,4,opt,name=join_box_type,json=joinBoxType,proto3,enum=channelbox.JoinBoxType" json:"join_box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *JoinBoxReq) Reset()         { *m = JoinBoxReq{} }
func (m *JoinBoxReq) String() string { return proto.CompactTextString(m) }
func (*JoinBoxReq) ProtoMessage()    {}
func (*JoinBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{6}
}
func (m *JoinBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinBoxReq.Unmarshal(m, b)
}
func (m *JoinBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinBoxReq.Marshal(b, m, deterministic)
}
func (dst *JoinBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinBoxReq.Merge(dst, src)
}
func (m *JoinBoxReq) XXX_Size() int {
	return xxx_messageInfo_JoinBoxReq.Size(m)
}
func (m *JoinBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinBoxReq proto.InternalMessageInfo

func (m *JoinBoxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *JoinBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *JoinBoxReq) GetJoinBoxType() JoinBoxType {
	if m != nil {
		return m.JoinBoxType
	}
	return JoinBoxType_NoneType
}

type JoinBoxResp struct {
	IsSwitch             bool            `protobuf:"varint,1,opt,name=is_switch,json=isSwitch,proto3" json:"is_switch,omitempty"`
	EnterBoxInfos        []*EnterBoxInfo `protobuf:"bytes,2,rep,name=enter_box_infos,json=enterBoxInfos,proto3" json:"enter_box_infos,omitempty"`
	Opts                 uint64          `protobuf:"varint,3,opt,name=opts,proto3" json:"opts,omitempty"`
	Channelid            uint32          `protobuf:"varint,4,opt,name=channelid,proto3" json:"channelid,omitempty"`
	IsIgnore             bool            `protobuf:"varint,5,opt,name=is_ignore,json=isIgnore,proto3" json:"is_ignore,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *JoinBoxResp) Reset()         { *m = JoinBoxResp{} }
func (m *JoinBoxResp) String() string { return proto.CompactTextString(m) }
func (*JoinBoxResp) ProtoMessage()    {}
func (*JoinBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{7}
}
func (m *JoinBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinBoxResp.Unmarshal(m, b)
}
func (m *JoinBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinBoxResp.Marshal(b, m, deterministic)
}
func (dst *JoinBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinBoxResp.Merge(dst, src)
}
func (m *JoinBoxResp) XXX_Size() int {
	return xxx_messageInfo_JoinBoxResp.Size(m)
}
func (m *JoinBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinBoxResp proto.InternalMessageInfo

func (m *JoinBoxResp) GetIsSwitch() bool {
	if m != nil {
		return m.IsSwitch
	}
	return false
}

func (m *JoinBoxResp) GetEnterBoxInfos() []*EnterBoxInfo {
	if m != nil {
		return m.EnterBoxInfos
	}
	return nil
}

func (m *JoinBoxResp) GetOpts() uint64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

func (m *JoinBoxResp) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *JoinBoxResp) GetIsIgnore() bool {
	if m != nil {
		return m.IsIgnore
	}
	return false
}

type BoxBaseInfo struct {
	Boxid                uint32   `protobuf:"varint,1,opt,name=boxid,proto3" json:"boxid,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Opts                 uint64   `protobuf:"varint,3,opt,name=opts,proto3" json:"opts,omitempty"`
	AudioBoxids          []uint32 `protobuf:"varint,4,rep,packed,name=audio_boxids,json=audioBoxids,proto3" json:"audio_boxids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxBaseInfo) Reset()         { *m = BoxBaseInfo{} }
func (m *BoxBaseInfo) String() string { return proto.CompactTextString(m) }
func (*BoxBaseInfo) ProtoMessage()    {}
func (*BoxBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{8}
}
func (m *BoxBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxBaseInfo.Unmarshal(m, b)
}
func (m *BoxBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxBaseInfo.Marshal(b, m, deterministic)
}
func (dst *BoxBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxBaseInfo.Merge(dst, src)
}
func (m *BoxBaseInfo) XXX_Size() int {
	return xxx_messageInfo_BoxBaseInfo.Size(m)
}
func (m *BoxBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BoxBaseInfo proto.InternalMessageInfo

func (m *BoxBaseInfo) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *BoxBaseInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BoxBaseInfo) GetOpts() uint64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

func (m *BoxBaseInfo) GetAudioBoxids() []uint32 {
	if m != nil {
		return m.AudioBoxids
	}
	return nil
}

type BoxCntInfo struct {
	Boxid                uint32   `protobuf:"varint,1,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxUserCnt           uint32   `protobuf:"varint,2,opt,name=box_user_cnt,json=boxUserCnt,proto3" json:"box_user_cnt,omitempty"`
	Channelid            uint32   `protobuf:"varint,3,opt,name=channelid,proto3" json:"channelid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxCntInfo) Reset()         { *m = BoxCntInfo{} }
func (m *BoxCntInfo) String() string { return proto.CompactTextString(m) }
func (*BoxCntInfo) ProtoMessage()    {}
func (*BoxCntInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{9}
}
func (m *BoxCntInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxCntInfo.Unmarshal(m, b)
}
func (m *BoxCntInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxCntInfo.Marshal(b, m, deterministic)
}
func (dst *BoxCntInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxCntInfo.Merge(dst, src)
}
func (m *BoxCntInfo) XXX_Size() int {
	return xxx_messageInfo_BoxCntInfo.Size(m)
}
func (m *BoxCntInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxCntInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BoxCntInfo proto.InternalMessageInfo

func (m *BoxCntInfo) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *BoxCntInfo) GetBoxUserCnt() uint32 {
	if m != nil {
		return m.BoxUserCnt
	}
	return 0
}

func (m *BoxCntInfo) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

// 客户端协议
type GetBoxInfoReq struct {
	OpeUid               uint32   `protobuf:"varint,1,opt,name=ope_uid,json=opeUid,proto3" json:"ope_uid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBoxInfoReq) Reset()         { *m = GetBoxInfoReq{} }
func (m *GetBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfoReq) ProtoMessage()    {}
func (*GetBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{10}
}
func (m *GetBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfoReq.Unmarshal(m, b)
}
func (m *GetBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfoReq.Merge(dst, src)
}
func (m *GetBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfoReq.Size(m)
}
func (m *GetBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfoReq proto.InternalMessageInfo

func (m *GetBoxInfoReq) GetOpeUid() uint32 {
	if m != nil {
		return m.OpeUid
	}
	return 0
}

func (m *GetBoxInfoReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxInfoReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type GetBoxInfoResp struct {
	BoxInfo              []*BoxCntInfo `protobuf:"bytes,1,rep,name=box_info,json=boxInfo,proto3" json:"box_info,omitempty"`
	UserBoxid            uint32        `protobuf:"varint,2,opt,name=user_boxid,json=userBoxid,proto3" json:"user_boxid,omitempty"`
	BoxList              []*BoxInfo    `protobuf:"bytes,3,rep,name=box_list,json=boxList,proto3" json:"box_list,omitempty"`
	MicTotal             uint32        `protobuf:"varint,4,opt,name=mic_total,json=micTotal,proto3" json:"mic_total,omitempty"`
	UserAudioBoxids      []uint32      `protobuf:"varint,5,rep,packed,name=user_audio_boxids,json=userAudioBoxids,proto3" json:"user_audio_boxids,omitempty"`
	MainCommonBoxid      uint32        `protobuf:"varint,6,opt,name=main_common_boxid,json=mainCommonBoxid,proto3" json:"main_common_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBoxInfoResp) Reset()         { *m = GetBoxInfoResp{} }
func (m *GetBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfoResp) ProtoMessage()    {}
func (*GetBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{11}
}
func (m *GetBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfoResp.Unmarshal(m, b)
}
func (m *GetBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfoResp.Merge(dst, src)
}
func (m *GetBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfoResp.Size(m)
}
func (m *GetBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfoResp proto.InternalMessageInfo

func (m *GetBoxInfoResp) GetBoxInfo() []*BoxCntInfo {
	if m != nil {
		return m.BoxInfo
	}
	return nil
}

func (m *GetBoxInfoResp) GetUserBoxid() uint32 {
	if m != nil {
		return m.UserBoxid
	}
	return 0
}

func (m *GetBoxInfoResp) GetBoxList() []*BoxInfo {
	if m != nil {
		return m.BoxList
	}
	return nil
}

func (m *GetBoxInfoResp) GetMicTotal() uint32 {
	if m != nil {
		return m.MicTotal
	}
	return 0
}

func (m *GetBoxInfoResp) GetUserAudioBoxids() []uint32 {
	if m != nil {
		return m.UserAudioBoxids
	}
	return nil
}

func (m *GetBoxInfoResp) GetMainCommonBoxid() uint32 {
	if m != nil {
		return m.MainCommonBoxid
	}
	return 0
}

type ClearJoinBoxReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearJoinBoxReq) Reset()         { *m = ClearJoinBoxReq{} }
func (m *ClearJoinBoxReq) String() string { return proto.CompactTextString(m) }
func (*ClearJoinBoxReq) ProtoMessage()    {}
func (*ClearJoinBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{12}
}
func (m *ClearJoinBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearJoinBoxReq.Unmarshal(m, b)
}
func (m *ClearJoinBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearJoinBoxReq.Marshal(b, m, deterministic)
}
func (dst *ClearJoinBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearJoinBoxReq.Merge(dst, src)
}
func (m *ClearJoinBoxReq) XXX_Size() int {
	return xxx_messageInfo_ClearJoinBoxReq.Size(m)
}
func (m *ClearJoinBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearJoinBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearJoinBoxReq proto.InternalMessageInfo

func (m *ClearJoinBoxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ClearJoinBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ClearJoinBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *ClearJoinBoxReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type ClearJoinBoxResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearJoinBoxResp) Reset()         { *m = ClearJoinBoxResp{} }
func (m *ClearJoinBoxResp) String() string { return proto.CompactTextString(m) }
func (*ClearJoinBoxResp) ProtoMessage()    {}
func (*ClearJoinBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{13}
}
func (m *ClearJoinBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearJoinBoxResp.Unmarshal(m, b)
}
func (m *ClearJoinBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearJoinBoxResp.Marshal(b, m, deterministic)
}
func (dst *ClearJoinBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearJoinBoxResp.Merge(dst, src)
}
func (m *ClearJoinBoxResp) XXX_Size() int {
	return xxx_messageInfo_ClearJoinBoxResp.Size(m)
}
func (m *ClearJoinBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearJoinBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearJoinBoxResp proto.InternalMessageInfo

type BatchGetBoxInfosReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetBoxInfosReq) Reset()         { *m = BatchGetBoxInfosReq{} }
func (m *BatchGetBoxInfosReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetBoxInfosReq) ProtoMessage()    {}
func (*BatchGetBoxInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{14}
}
func (m *BatchGetBoxInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBoxInfosReq.Unmarshal(m, b)
}
func (m *BatchGetBoxInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBoxInfosReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetBoxInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBoxInfosReq.Merge(dst, src)
}
func (m *BatchGetBoxInfosReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetBoxInfosReq.Size(m)
}
func (m *BatchGetBoxInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBoxInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBoxInfosReq proto.InternalMessageInfo

func (m *BatchGetBoxInfosReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetBoxInfosReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *BatchGetBoxInfosReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type BatchGetBoxInfosResp struct {
	BoxBaseInfos         []*BoxBaseInfo `protobuf:"bytes,1,rep,name=box_base_infos,json=boxBaseInfos,proto3" json:"box_base_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchGetBoxInfosResp) Reset()         { *m = BatchGetBoxInfosResp{} }
func (m *BatchGetBoxInfosResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetBoxInfosResp) ProtoMessage()    {}
func (*BatchGetBoxInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{15}
}
func (m *BatchGetBoxInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBoxInfosResp.Unmarshal(m, b)
}
func (m *BatchGetBoxInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBoxInfosResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetBoxInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBoxInfosResp.Merge(dst, src)
}
func (m *BatchGetBoxInfosResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetBoxInfosResp.Size(m)
}
func (m *BatchGetBoxInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBoxInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBoxInfosResp proto.InternalMessageInfo

func (m *BatchGetBoxInfosResp) GetBoxBaseInfos() []*BoxBaseInfo {
	if m != nil {
		return m.BoxBaseInfos
	}
	return nil
}

type GetBoxInfosByLimitReq struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	LastTime             int64    `protobuf:"varint,3,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	GetCnt               int64    `protobuf:"varint,4,opt,name=get_cnt,json=getCnt,proto3" json:"get_cnt,omitempty"`
	BoxType              BoxType  `protobuf:"varint,5,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBoxInfosByLimitReq) Reset()         { *m = GetBoxInfosByLimitReq{} }
func (m *GetBoxInfosByLimitReq) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfosByLimitReq) ProtoMessage()    {}
func (*GetBoxInfosByLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{16}
}
func (m *GetBoxInfosByLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Unmarshal(m, b)
}
func (m *GetBoxInfosByLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfosByLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfosByLimitReq.Merge(dst, src)
}
func (m *GetBoxInfosByLimitReq) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Size(m)
}
func (m *GetBoxInfosByLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfosByLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfosByLimitReq proto.InternalMessageInfo

func (m *GetBoxInfosByLimitReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetLastTime() int64 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetGetCnt() int64 {
	if m != nil {
		return m.GetCnt
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type GetBoxInfosByLimitResp struct {
	BoxBaseInfos         []*BoxBaseInfo `protobuf:"bytes,1,rep,name=box_base_infos,json=boxBaseInfos,proto3" json:"box_base_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetBoxInfosByLimitResp) Reset()         { *m = GetBoxInfosByLimitResp{} }
func (m *GetBoxInfosByLimitResp) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfosByLimitResp) ProtoMessage()    {}
func (*GetBoxInfosByLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{17}
}
func (m *GetBoxInfosByLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Unmarshal(m, b)
}
func (m *GetBoxInfosByLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfosByLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfosByLimitResp.Merge(dst, src)
}
func (m *GetBoxInfosByLimitResp) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Size(m)
}
func (m *GetBoxInfosByLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfosByLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfosByLimitResp proto.InternalMessageInfo

func (m *GetBoxInfosByLimitResp) GetBoxBaseInfos() []*BoxBaseInfo {
	if m != nil {
		return m.BoxBaseInfos
	}
	return nil
}

type GetBoxUserInMicInfosReq struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	MicUids              []uint32 `protobuf:"varint,3,rep,packed,name=mic_uids,json=micUids,proto3" json:"mic_uids,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBoxUserInMicInfosReq) Reset()         { *m = GetBoxUserInMicInfosReq{} }
func (m *GetBoxUserInMicInfosReq) String() string { return proto.CompactTextString(m) }
func (*GetBoxUserInMicInfosReq) ProtoMessage()    {}
func (*GetBoxUserInMicInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{18}
}
func (m *GetBoxUserInMicInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxUserInMicInfosReq.Unmarshal(m, b)
}
func (m *GetBoxUserInMicInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxUserInMicInfosReq.Marshal(b, m, deterministic)
}
func (dst *GetBoxUserInMicInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxUserInMicInfosReq.Merge(dst, src)
}
func (m *GetBoxUserInMicInfosReq) XXX_Size() int {
	return xxx_messageInfo_GetBoxUserInMicInfosReq.Size(m)
}
func (m *GetBoxUserInMicInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxUserInMicInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxUserInMicInfosReq proto.InternalMessageInfo

func (m *GetBoxUserInMicInfosReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxUserInMicInfosReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *GetBoxUserInMicInfosReq) GetMicUids() []uint32 {
	if m != nil {
		return m.MicUids
	}
	return nil
}

func (m *GetBoxUserInMicInfosReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type GetBoxUserInMicInfosResp struct {
	BoxUids              []uint32 `protobuf:"varint,1,rep,packed,name=box_uids,json=boxUids,proto3" json:"box_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBoxUserInMicInfosResp) Reset()         { *m = GetBoxUserInMicInfosResp{} }
func (m *GetBoxUserInMicInfosResp) String() string { return proto.CompactTextString(m) }
func (*GetBoxUserInMicInfosResp) ProtoMessage()    {}
func (*GetBoxUserInMicInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{19}
}
func (m *GetBoxUserInMicInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxUserInMicInfosResp.Unmarshal(m, b)
}
func (m *GetBoxUserInMicInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxUserInMicInfosResp.Marshal(b, m, deterministic)
}
func (dst *GetBoxUserInMicInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxUserInMicInfosResp.Merge(dst, src)
}
func (m *GetBoxUserInMicInfosResp) XXX_Size() int {
	return xxx_messageInfo_GetBoxUserInMicInfosResp.Size(m)
}
func (m *GetBoxUserInMicInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxUserInMicInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxUserInMicInfosResp proto.InternalMessageInfo

func (m *GetBoxUserInMicInfosResp) GetBoxUids() []uint32 {
	if m != nil {
		return m.BoxUids
	}
	return nil
}

type ExitChannelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	BoxType              BoxType  `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExitChannelReq) Reset()         { *m = ExitChannelReq{} }
func (m *ExitChannelReq) String() string { return proto.CompactTextString(m) }
func (*ExitChannelReq) ProtoMessage()    {}
func (*ExitChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{20}
}
func (m *ExitChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitChannelReq.Unmarshal(m, b)
}
func (m *ExitChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitChannelReq.Marshal(b, m, deterministic)
}
func (dst *ExitChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitChannelReq.Merge(dst, src)
}
func (m *ExitChannelReq) XXX_Size() int {
	return xxx_messageInfo_ExitChannelReq.Size(m)
}
func (m *ExitChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExitChannelReq proto.InternalMessageInfo

func (m *ExitChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExitChannelReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ExitChannelReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type ExitChannelResp struct {
	BoxCnt               uint32   `protobuf:"varint,1,opt,name=box_cnt,json=boxCnt,proto3" json:"box_cnt,omitempty"`
	Opts                 uint64   `protobuf:"varint,2,opt,name=opts,proto3" json:"opts,omitempty"`
	BoxId                uint32   `protobuf:"varint,3,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	BoxType              BoxType  `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExitChannelResp) Reset()         { *m = ExitChannelResp{} }
func (m *ExitChannelResp) String() string { return proto.CompactTextString(m) }
func (*ExitChannelResp) ProtoMessage()    {}
func (*ExitChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{21}
}
func (m *ExitChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitChannelResp.Unmarshal(m, b)
}
func (m *ExitChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitChannelResp.Marshal(b, m, deterministic)
}
func (dst *ExitChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitChannelResp.Merge(dst, src)
}
func (m *ExitChannelResp) XXX_Size() int {
	return xxx_messageInfo_ExitChannelResp.Size(m)
}
func (m *ExitChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExitChannelResp proto.InternalMessageInfo

func (m *ExitChannelResp) GetBoxCnt() uint32 {
	if m != nil {
		return m.BoxCnt
	}
	return 0
}

func (m *ExitChannelResp) GetOpts() uint64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

func (m *ExitChannelResp) GetBoxId() uint32 {
	if m != nil {
		return m.BoxId
	}
	return 0
}

func (m *ExitChannelResp) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type UpsertBoxInfoReq struct {
	Box                  *BoxInfo `protobuf:"bytes,1,opt,name=box,proto3" json:"box,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertBoxInfoReq) Reset()         { *m = UpsertBoxInfoReq{} }
func (m *UpsertBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpsertBoxInfoReq) ProtoMessage()    {}
func (*UpsertBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{22}
}
func (m *UpsertBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBoxInfoReq.Unmarshal(m, b)
}
func (m *UpsertBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpsertBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBoxInfoReq.Merge(dst, src)
}
func (m *UpsertBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpsertBoxInfoReq.Size(m)
}
func (m *UpsertBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBoxInfoReq proto.InternalMessageInfo

func (m *UpsertBoxInfoReq) GetBox() *BoxInfo {
	if m != nil {
		return m.Box
	}
	return nil
}

type UpsertBoxInfoResp struct {
	Box                  *BoxInfo `protobuf:"bytes,1,opt,name=box,proto3" json:"box,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertBoxInfoResp) Reset()         { *m = UpsertBoxInfoResp{} }
func (m *UpsertBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpsertBoxInfoResp) ProtoMessage()    {}
func (*UpsertBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{23}
}
func (m *UpsertBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBoxInfoResp.Unmarshal(m, b)
}
func (m *UpsertBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpsertBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBoxInfoResp.Merge(dst, src)
}
func (m *UpsertBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpsertBoxInfoResp.Size(m)
}
func (m *UpsertBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBoxInfoResp proto.InternalMessageInfo

func (m *UpsertBoxInfoResp) GetBox() *BoxInfo {
	if m != nil {
		return m.Box
	}
	return nil
}

type DelBoxInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BoxId                uint32   `protobuf:"varint,2,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	BoxType              BoxType  `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=channelbox.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBoxInfoReq) Reset()         { *m = DelBoxInfoReq{} }
func (m *DelBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelBoxInfoReq) ProtoMessage()    {}
func (*DelBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{24}
}
func (m *DelBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBoxInfoReq.Unmarshal(m, b)
}
func (m *DelBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBoxInfoReq.Merge(dst, src)
}
func (m *DelBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelBoxInfoReq.Size(m)
}
func (m *DelBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBoxInfoReq proto.InternalMessageInfo

func (m *DelBoxInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DelBoxInfoReq) GetBoxId() uint32 {
	if m != nil {
		return m.BoxId
	}
	return 0
}

func (m *DelBoxInfoReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type DelBoxInfoResp struct {
	// 房间内剩余子频道数量
	BoxNum               uint32   `protobuf:"varint,1,opt,name=box_num,json=boxNum,proto3" json:"box_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBoxInfoResp) Reset()         { *m = DelBoxInfoResp{} }
func (m *DelBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelBoxInfoResp) ProtoMessage()    {}
func (*DelBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{25}
}
func (m *DelBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBoxInfoResp.Unmarshal(m, b)
}
func (m *DelBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBoxInfoResp.Merge(dst, src)
}
func (m *DelBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelBoxInfoResp.Size(m)
}
func (m *DelBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBoxInfoResp proto.InternalMessageInfo

func (m *DelBoxInfoResp) GetBoxNum() uint32 {
	if m != nil {
		return m.BoxNum
	}
	return 0
}

type ApplyOnMicTokenReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicId                uint32   `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyOnMicTokenReq) Reset()         { *m = ApplyOnMicTokenReq{} }
func (m *ApplyOnMicTokenReq) String() string { return proto.CompactTextString(m) }
func (*ApplyOnMicTokenReq) ProtoMessage()    {}
func (*ApplyOnMicTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{26}
}
func (m *ApplyOnMicTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyOnMicTokenReq.Unmarshal(m, b)
}
func (m *ApplyOnMicTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyOnMicTokenReq.Marshal(b, m, deterministic)
}
func (dst *ApplyOnMicTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyOnMicTokenReq.Merge(dst, src)
}
func (m *ApplyOnMicTokenReq) XXX_Size() int {
	return xxx_messageInfo_ApplyOnMicTokenReq.Size(m)
}
func (m *ApplyOnMicTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyOnMicTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyOnMicTokenReq proto.InternalMessageInfo

func (m *ApplyOnMicTokenReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ApplyOnMicTokenReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

type ApplyOnMicTokenResp struct {
	OnMicToken           string   `protobuf:"bytes,1,opt,name=on_mic_token,json=onMicToken,proto3" json:"on_mic_token,omitempty"`
	MicId                uint32   `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyOnMicTokenResp) Reset()         { *m = ApplyOnMicTokenResp{} }
func (m *ApplyOnMicTokenResp) String() string { return proto.CompactTextString(m) }
func (*ApplyOnMicTokenResp) ProtoMessage()    {}
func (*ApplyOnMicTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{27}
}
func (m *ApplyOnMicTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyOnMicTokenResp.Unmarshal(m, b)
}
func (m *ApplyOnMicTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyOnMicTokenResp.Marshal(b, m, deterministic)
}
func (dst *ApplyOnMicTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyOnMicTokenResp.Merge(dst, src)
}
func (m *ApplyOnMicTokenResp) XXX_Size() int {
	return xxx_messageInfo_ApplyOnMicTokenResp.Size(m)
}
func (m *ApplyOnMicTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyOnMicTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyOnMicTokenResp proto.InternalMessageInfo

func (m *ApplyOnMicTokenResp) GetOnMicToken() string {
	if m != nil {
		return m.OnMicToken
	}
	return ""
}

func (m *ApplyOnMicTokenResp) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

type SetChannelMicBoxIdReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OpenMicBoxId         uint32   `protobuf:"varint,2,opt,name=open_mic_box_id,json=openMicBoxId,proto3" json:"open_mic_box_id,omitempty"`
	OpeUid               uint32   `protobuf:"varint,3,opt,name=ope_uid,json=opeUid,proto3" json:"ope_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMicBoxIdReq) Reset()         { *m = SetChannelMicBoxIdReq{} }
func (m *SetChannelMicBoxIdReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicBoxIdReq) ProtoMessage()    {}
func (*SetChannelMicBoxIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{28}
}
func (m *SetChannelMicBoxIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicBoxIdReq.Unmarshal(m, b)
}
func (m *SetChannelMicBoxIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicBoxIdReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicBoxIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicBoxIdReq.Merge(dst, src)
}
func (m *SetChannelMicBoxIdReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicBoxIdReq.Size(m)
}
func (m *SetChannelMicBoxIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicBoxIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicBoxIdReq proto.InternalMessageInfo

func (m *SetChannelMicBoxIdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMicBoxIdReq) GetOpenMicBoxId() uint32 {
	if m != nil {
		return m.OpenMicBoxId
	}
	return 0
}

func (m *SetChannelMicBoxIdReq) GetOpeUid() uint32 {
	if m != nil {
		return m.OpeUid
	}
	return 0
}

type SetChannelMicBoxIdResp struct {
	Opts                 int64    `protobuf:"varint,1,opt,name=opts,proto3" json:"opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMicBoxIdResp) Reset()         { *m = SetChannelMicBoxIdResp{} }
func (m *SetChannelMicBoxIdResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicBoxIdResp) ProtoMessage()    {}
func (*SetChannelMicBoxIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{29}
}
func (m *SetChannelMicBoxIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicBoxIdResp.Unmarshal(m, b)
}
func (m *SetChannelMicBoxIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicBoxIdResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicBoxIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicBoxIdResp.Merge(dst, src)
}
func (m *SetChannelMicBoxIdResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicBoxIdResp.Size(m)
}
func (m *SetChannelMicBoxIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicBoxIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicBoxIdResp proto.InternalMessageInfo

func (m *SetChannelMicBoxIdResp) GetOpts() int64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

// 麦位申请记录
type MicApplyRecord struct {
	MicId                uint32    `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	MicType              MicType   `protobuf:"varint,2,opt,name=mic_type,json=micType,proto3,enum=channelbox.MicType" json:"mic_type,omitempty"`
	MicStatus            MicStatus `protobuf:"varint,3,opt,name=mic_status,json=micStatus,proto3,enum=channelbox.MicStatus" json:"mic_status,omitempty"`
	Uid                  uint32    `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	BoxId                uint32    `protobuf:"varint,5,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	UpdateAt             uint32    `protobuf:"varint,6,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *MicApplyRecord) Reset()         { *m = MicApplyRecord{} }
func (m *MicApplyRecord) String() string { return proto.CompactTextString(m) }
func (*MicApplyRecord) ProtoMessage()    {}
func (*MicApplyRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{30}
}
func (m *MicApplyRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicApplyRecord.Unmarshal(m, b)
}
func (m *MicApplyRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicApplyRecord.Marshal(b, m, deterministic)
}
func (dst *MicApplyRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicApplyRecord.Merge(dst, src)
}
func (m *MicApplyRecord) XXX_Size() int {
	return xxx_messageInfo_MicApplyRecord.Size(m)
}
func (m *MicApplyRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_MicApplyRecord.DiscardUnknown(m)
}

var xxx_messageInfo_MicApplyRecord proto.InternalMessageInfo

func (m *MicApplyRecord) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *MicApplyRecord) GetMicType() MicType {
	if m != nil {
		return m.MicType
	}
	return MicType_MicTypeFree
}

func (m *MicApplyRecord) GetMicStatus() MicStatus {
	if m != nil {
		return m.MicStatus
	}
	return MicStatus_MicStatusApplying
}

func (m *MicApplyRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MicApplyRecord) GetBoxId() uint32 {
	if m != nil {
		return m.BoxId
	}
	return 0
}

func (m *MicApplyRecord) GetUpdateAt() uint32 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

type SetMicApplyRecordsReq struct {
	ChannelId            uint32            `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Records              []*MicApplyRecord `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SetMicApplyRecordsReq) Reset()         { *m = SetMicApplyRecordsReq{} }
func (m *SetMicApplyRecordsReq) String() string { return proto.CompactTextString(m) }
func (*SetMicApplyRecordsReq) ProtoMessage()    {}
func (*SetMicApplyRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{31}
}
func (m *SetMicApplyRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicApplyRecordsReq.Unmarshal(m, b)
}
func (m *SetMicApplyRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicApplyRecordsReq.Marshal(b, m, deterministic)
}
func (dst *SetMicApplyRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicApplyRecordsReq.Merge(dst, src)
}
func (m *SetMicApplyRecordsReq) XXX_Size() int {
	return xxx_messageInfo_SetMicApplyRecordsReq.Size(m)
}
func (m *SetMicApplyRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicApplyRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicApplyRecordsReq proto.InternalMessageInfo

func (m *SetMicApplyRecordsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMicApplyRecordsReq) GetRecords() []*MicApplyRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

type SetMicApplyRecordsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMicApplyRecordsResp) Reset()         { *m = SetMicApplyRecordsResp{} }
func (m *SetMicApplyRecordsResp) String() string { return proto.CompactTextString(m) }
func (*SetMicApplyRecordsResp) ProtoMessage()    {}
func (*SetMicApplyRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{32}
}
func (m *SetMicApplyRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicApplyRecordsResp.Unmarshal(m, b)
}
func (m *SetMicApplyRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicApplyRecordsResp.Marshal(b, m, deterministic)
}
func (dst *SetMicApplyRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicApplyRecordsResp.Merge(dst, src)
}
func (m *SetMicApplyRecordsResp) XXX_Size() int {
	return xxx_messageInfo_SetMicApplyRecordsResp.Size(m)
}
func (m *SetMicApplyRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicApplyRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicApplyRecordsResp proto.InternalMessageInfo

type GetMicApplyRecordsReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMicApplyRecordsReq) Reset()         { *m = GetMicApplyRecordsReq{} }
func (m *GetMicApplyRecordsReq) String() string { return proto.CompactTextString(m) }
func (*GetMicApplyRecordsReq) ProtoMessage()    {}
func (*GetMicApplyRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{33}
}
func (m *GetMicApplyRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicApplyRecordsReq.Unmarshal(m, b)
}
func (m *GetMicApplyRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicApplyRecordsReq.Marshal(b, m, deterministic)
}
func (dst *GetMicApplyRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicApplyRecordsReq.Merge(dst, src)
}
func (m *GetMicApplyRecordsReq) XXX_Size() int {
	return xxx_messageInfo_GetMicApplyRecordsReq.Size(m)
}
func (m *GetMicApplyRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicApplyRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicApplyRecordsReq proto.InternalMessageInfo

func (m *GetMicApplyRecordsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMicApplyRecordsResp struct {
	Records              []*MicApplyRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetMicApplyRecordsResp) Reset()         { *m = GetMicApplyRecordsResp{} }
func (m *GetMicApplyRecordsResp) String() string { return proto.CompactTextString(m) }
func (*GetMicApplyRecordsResp) ProtoMessage()    {}
func (*GetMicApplyRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{34}
}
func (m *GetMicApplyRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicApplyRecordsResp.Unmarshal(m, b)
}
func (m *GetMicApplyRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicApplyRecordsResp.Marshal(b, m, deterministic)
}
func (dst *GetMicApplyRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicApplyRecordsResp.Merge(dst, src)
}
func (m *GetMicApplyRecordsResp) XXX_Size() int {
	return xxx_messageInfo_GetMicApplyRecordsResp.Size(m)
}
func (m *GetMicApplyRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicApplyRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicApplyRecordsResp proto.InternalMessageInfo

func (m *GetMicApplyRecordsResp) GetRecords() []*MicApplyRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

type AddMainChannelUserReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMainChannelUserReq) Reset()         { *m = AddMainChannelUserReq{} }
func (m *AddMainChannelUserReq) String() string { return proto.CompactTextString(m) }
func (*AddMainChannelUserReq) ProtoMessage()    {}
func (*AddMainChannelUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{35}
}
func (m *AddMainChannelUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMainChannelUserReq.Unmarshal(m, b)
}
func (m *AddMainChannelUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMainChannelUserReq.Marshal(b, m, deterministic)
}
func (dst *AddMainChannelUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMainChannelUserReq.Merge(dst, src)
}
func (m *AddMainChannelUserReq) XXX_Size() int {
	return xxx_messageInfo_AddMainChannelUserReq.Size(m)
}
func (m *AddMainChannelUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMainChannelUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddMainChannelUserReq proto.InternalMessageInfo

func (m *AddMainChannelUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddMainChannelUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type AddMainChannelUserResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMainChannelUserResp) Reset()         { *m = AddMainChannelUserResp{} }
func (m *AddMainChannelUserResp) String() string { return proto.CompactTextString(m) }
func (*AddMainChannelUserResp) ProtoMessage()    {}
func (*AddMainChannelUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{36}
}
func (m *AddMainChannelUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMainChannelUserResp.Unmarshal(m, b)
}
func (m *AddMainChannelUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMainChannelUserResp.Marshal(b, m, deterministic)
}
func (dst *AddMainChannelUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMainChannelUserResp.Merge(dst, src)
}
func (m *AddMainChannelUserResp) XXX_Size() int {
	return xxx_messageInfo_AddMainChannelUserResp.Size(m)
}
func (m *AddMainChannelUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMainChannelUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddMainChannelUserResp proto.InternalMessageInfo

type GetMainChannelUsersReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMainChannelUsersReq) Reset()         { *m = GetMainChannelUsersReq{} }
func (m *GetMainChannelUsersReq) String() string { return proto.CompactTextString(m) }
func (*GetMainChannelUsersReq) ProtoMessage()    {}
func (*GetMainChannelUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{37}
}
func (m *GetMainChannelUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMainChannelUsersReq.Unmarshal(m, b)
}
func (m *GetMainChannelUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMainChannelUsersReq.Marshal(b, m, deterministic)
}
func (dst *GetMainChannelUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMainChannelUsersReq.Merge(dst, src)
}
func (m *GetMainChannelUsersReq) XXX_Size() int {
	return xxx_messageInfo_GetMainChannelUsersReq.Size(m)
}
func (m *GetMainChannelUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMainChannelUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMainChannelUsersReq proto.InternalMessageInfo

func (m *GetMainChannelUsersReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMainChannelUsersResp struct {
	UserIds              []uint32 `protobuf:"varint,1,rep,packed,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMainChannelUsersResp) Reset()         { *m = GetMainChannelUsersResp{} }
func (m *GetMainChannelUsersResp) String() string { return proto.CompactTextString(m) }
func (*GetMainChannelUsersResp) ProtoMessage()    {}
func (*GetMainChannelUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelbox_5a1dd58cca48abc7, []int{38}
}
func (m *GetMainChannelUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMainChannelUsersResp.Unmarshal(m, b)
}
func (m *GetMainChannelUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMainChannelUsersResp.Marshal(b, m, deterministic)
}
func (dst *GetMainChannelUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMainChannelUsersResp.Merge(dst, src)
}
func (m *GetMainChannelUsersResp) XXX_Size() int {
	return xxx_messageInfo_GetMainChannelUsersResp.Size(m)
}
func (m *GetMainChannelUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMainChannelUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMainChannelUsersResp proto.InternalMessageInfo

func (m *GetMainChannelUsersResp) GetUserIds() []uint32 {
	if m != nil {
		return m.UserIds
	}
	return nil
}

func init() {
	proto.RegisterType((*BoxInfo)(nil), "channelbox.BoxInfo")
	proto.RegisterType((*EnterBoxReq)(nil), "channelbox.EnterBoxReq")
	proto.RegisterType((*EnterBoxResp)(nil), "channelbox.EnterBoxResp")
	proto.RegisterType((*EnterBoxInfo)(nil), "channelbox.EnterBoxInfo")
	proto.RegisterType((*ExitBoxReq)(nil), "channelbox.ExitBoxReq")
	proto.RegisterType((*ExitBoxResp)(nil), "channelbox.ExitBoxResp")
	proto.RegisterType((*JoinBoxReq)(nil), "channelbox.JoinBoxReq")
	proto.RegisterType((*JoinBoxResp)(nil), "channelbox.JoinBoxResp")
	proto.RegisterType((*BoxBaseInfo)(nil), "channelbox.BoxBaseInfo")
	proto.RegisterType((*BoxCntInfo)(nil), "channelbox.BoxCntInfo")
	proto.RegisterType((*GetBoxInfoReq)(nil), "channelbox.GetBoxInfoReq")
	proto.RegisterType((*GetBoxInfoResp)(nil), "channelbox.GetBoxInfoResp")
	proto.RegisterType((*ClearJoinBoxReq)(nil), "channelbox.ClearJoinBoxReq")
	proto.RegisterType((*ClearJoinBoxResp)(nil), "channelbox.ClearJoinBoxResp")
	proto.RegisterType((*BatchGetBoxInfosReq)(nil), "channelbox.BatchGetBoxInfosReq")
	proto.RegisterType((*BatchGetBoxInfosResp)(nil), "channelbox.BatchGetBoxInfosResp")
	proto.RegisterType((*GetBoxInfosByLimitReq)(nil), "channelbox.GetBoxInfosByLimitReq")
	proto.RegisterType((*GetBoxInfosByLimitResp)(nil), "channelbox.GetBoxInfosByLimitResp")
	proto.RegisterType((*GetBoxUserInMicInfosReq)(nil), "channelbox.GetBoxUserInMicInfosReq")
	proto.RegisterType((*GetBoxUserInMicInfosResp)(nil), "channelbox.GetBoxUserInMicInfosResp")
	proto.RegisterType((*ExitChannelReq)(nil), "channelbox.ExitChannelReq")
	proto.RegisterType((*ExitChannelResp)(nil), "channelbox.ExitChannelResp")
	proto.RegisterType((*UpsertBoxInfoReq)(nil), "channelbox.UpsertBoxInfoReq")
	proto.RegisterType((*UpsertBoxInfoResp)(nil), "channelbox.UpsertBoxInfoResp")
	proto.RegisterType((*DelBoxInfoReq)(nil), "channelbox.DelBoxInfoReq")
	proto.RegisterType((*DelBoxInfoResp)(nil), "channelbox.DelBoxInfoResp")
	proto.RegisterType((*ApplyOnMicTokenReq)(nil), "channelbox.ApplyOnMicTokenReq")
	proto.RegisterType((*ApplyOnMicTokenResp)(nil), "channelbox.ApplyOnMicTokenResp")
	proto.RegisterType((*SetChannelMicBoxIdReq)(nil), "channelbox.SetChannelMicBoxIdReq")
	proto.RegisterType((*SetChannelMicBoxIdResp)(nil), "channelbox.SetChannelMicBoxIdResp")
	proto.RegisterType((*MicApplyRecord)(nil), "channelbox.MicApplyRecord")
	proto.RegisterType((*SetMicApplyRecordsReq)(nil), "channelbox.SetMicApplyRecordsReq")
	proto.RegisterType((*SetMicApplyRecordsResp)(nil), "channelbox.SetMicApplyRecordsResp")
	proto.RegisterType((*GetMicApplyRecordsReq)(nil), "channelbox.GetMicApplyRecordsReq")
	proto.RegisterType((*GetMicApplyRecordsResp)(nil), "channelbox.GetMicApplyRecordsResp")
	proto.RegisterType((*AddMainChannelUserReq)(nil), "channelbox.AddMainChannelUserReq")
	proto.RegisterType((*AddMainChannelUserResp)(nil), "channelbox.AddMainChannelUserResp")
	proto.RegisterType((*GetMainChannelUsersReq)(nil), "channelbox.GetMainChannelUsersReq")
	proto.RegisterType((*GetMainChannelUsersResp)(nil), "channelbox.GetMainChannelUsersResp")
	proto.RegisterEnum("channelbox.BoxType", BoxType_name, BoxType_value)
	proto.RegisterEnum("channelbox.EnterResultBoxType", EnterResultBoxType_name, EnterResultBoxType_value)
	proto.RegisterEnum("channelbox.JoinBoxType", JoinBoxType_name, JoinBoxType_value)
	proto.RegisterEnum("channelbox.MicType", MicType_name, MicType_value)
	proto.RegisterEnum("channelbox.MicStatus", MicStatus_name, MicStatus_value)
	proto.RegisterEnum("channelbox.SystemBoxID", SystemBoxID_name, SystemBoxID_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelBoxClient is the client API for ChannelBox service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelBoxClient interface {
	EnterBox(ctx context.Context, in *EnterBoxReq, opts ...grpc.CallOption) (*EnterBoxResp, error)
	ExitBox(ctx context.Context, in *ExitBoxReq, opts ...grpc.CallOption) (*ExitBoxResp, error)
	JoinBox(ctx context.Context, in *JoinBoxReq, opts ...grpc.CallOption) (*JoinBoxResp, error)
	GetBoxInfo(ctx context.Context, in *GetBoxInfoReq, opts ...grpc.CallOption) (*GetBoxInfoResp, error)
	ClearJoinBox(ctx context.Context, in *ClearJoinBoxReq, opts ...grpc.CallOption) (*ClearJoinBoxResp, error)
	BatchGetBoxInfos(ctx context.Context, in *BatchGetBoxInfosReq, opts ...grpc.CallOption) (*BatchGetBoxInfosResp, error)
	GetBoxInfosByLimit(ctx context.Context, in *GetBoxInfosByLimitReq, opts ...grpc.CallOption) (*GetBoxInfosByLimitResp, error)
	GetBoxUserInMicInfos(ctx context.Context, in *GetBoxUserInMicInfosReq, opts ...grpc.CallOption) (*GetBoxUserInMicInfosResp, error)
	ExitChannel(ctx context.Context, in *ExitChannelReq, opts ...grpc.CallOption) (*ExitChannelResp, error)
	UpsertBoxInfo(ctx context.Context, in *UpsertBoxInfoReq, opts ...grpc.CallOption) (*UpsertBoxInfoResp, error)
	DelBoxInfo(ctx context.Context, in *DelBoxInfoReq, opts ...grpc.CallOption) (*DelBoxInfoResp, error)
	ApplyOnMicToken(ctx context.Context, in *ApplyOnMicTokenReq, opts ...grpc.CallOption) (*ApplyOnMicTokenResp, error)
	SetChannelMicBoxId(ctx context.Context, in *SetChannelMicBoxIdReq, opts ...grpc.CallOption) (*SetChannelMicBoxIdResp, error)
	// 麦位申请记录
	SetMicApplyRecords(ctx context.Context, in *SetMicApplyRecordsReq, opts ...grpc.CallOption) (*SetMicApplyRecordsResp, error)
	GetMicApplyRecords(ctx context.Context, in *GetMicApplyRecordsReq, opts ...grpc.CallOption) (*GetMicApplyRecordsResp, error)
	// 主房间用户管理
	AddMainChannelUser(ctx context.Context, in *AddMainChannelUserReq, opts ...grpc.CallOption) (*AddMainChannelUserResp, error)
	GetMainChannelUsers(ctx context.Context, in *GetMainChannelUsersReq, opts ...grpc.CallOption) (*GetMainChannelUsersResp, error)
}

type channelBoxClient struct {
	cc *grpc.ClientConn
}

func NewChannelBoxClient(cc *grpc.ClientConn) ChannelBoxClient {
	return &channelBoxClient{cc}
}

func (c *channelBoxClient) EnterBox(ctx context.Context, in *EnterBoxReq, opts ...grpc.CallOption) (*EnterBoxResp, error) {
	out := new(EnterBoxResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/EnterBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) ExitBox(ctx context.Context, in *ExitBoxReq, opts ...grpc.CallOption) (*ExitBoxResp, error) {
	out := new(ExitBoxResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/ExitBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) JoinBox(ctx context.Context, in *JoinBoxReq, opts ...grpc.CallOption) (*JoinBoxResp, error) {
	out := new(JoinBoxResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/JoinBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) GetBoxInfo(ctx context.Context, in *GetBoxInfoReq, opts ...grpc.CallOption) (*GetBoxInfoResp, error) {
	out := new(GetBoxInfoResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/GetBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) ClearJoinBox(ctx context.Context, in *ClearJoinBoxReq, opts ...grpc.CallOption) (*ClearJoinBoxResp, error) {
	out := new(ClearJoinBoxResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/ClearJoinBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) BatchGetBoxInfos(ctx context.Context, in *BatchGetBoxInfosReq, opts ...grpc.CallOption) (*BatchGetBoxInfosResp, error) {
	out := new(BatchGetBoxInfosResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/BatchGetBoxInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) GetBoxInfosByLimit(ctx context.Context, in *GetBoxInfosByLimitReq, opts ...grpc.CallOption) (*GetBoxInfosByLimitResp, error) {
	out := new(GetBoxInfosByLimitResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/GetBoxInfosByLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) GetBoxUserInMicInfos(ctx context.Context, in *GetBoxUserInMicInfosReq, opts ...grpc.CallOption) (*GetBoxUserInMicInfosResp, error) {
	out := new(GetBoxUserInMicInfosResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/GetBoxUserInMicInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) ExitChannel(ctx context.Context, in *ExitChannelReq, opts ...grpc.CallOption) (*ExitChannelResp, error) {
	out := new(ExitChannelResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/ExitChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) UpsertBoxInfo(ctx context.Context, in *UpsertBoxInfoReq, opts ...grpc.CallOption) (*UpsertBoxInfoResp, error) {
	out := new(UpsertBoxInfoResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/UpsertBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) DelBoxInfo(ctx context.Context, in *DelBoxInfoReq, opts ...grpc.CallOption) (*DelBoxInfoResp, error) {
	out := new(DelBoxInfoResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/DelBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) ApplyOnMicToken(ctx context.Context, in *ApplyOnMicTokenReq, opts ...grpc.CallOption) (*ApplyOnMicTokenResp, error) {
	out := new(ApplyOnMicTokenResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/ApplyOnMicToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) SetChannelMicBoxId(ctx context.Context, in *SetChannelMicBoxIdReq, opts ...grpc.CallOption) (*SetChannelMicBoxIdResp, error) {
	out := new(SetChannelMicBoxIdResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/SetChannelMicBoxId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) SetMicApplyRecords(ctx context.Context, in *SetMicApplyRecordsReq, opts ...grpc.CallOption) (*SetMicApplyRecordsResp, error) {
	out := new(SetMicApplyRecordsResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/SetMicApplyRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) GetMicApplyRecords(ctx context.Context, in *GetMicApplyRecordsReq, opts ...grpc.CallOption) (*GetMicApplyRecordsResp, error) {
	out := new(GetMicApplyRecordsResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/GetMicApplyRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) AddMainChannelUser(ctx context.Context, in *AddMainChannelUserReq, opts ...grpc.CallOption) (*AddMainChannelUserResp, error) {
	out := new(AddMainChannelUserResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/AddMainChannelUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelBoxClient) GetMainChannelUsers(ctx context.Context, in *GetMainChannelUsersReq, opts ...grpc.CallOption) (*GetMainChannelUsersResp, error) {
	out := new(GetMainChannelUsersResp)
	err := c.cc.Invoke(ctx, "/channelbox.ChannelBox/GetMainChannelUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelBoxServer is the server API for ChannelBox service.
type ChannelBoxServer interface {
	EnterBox(context.Context, *EnterBoxReq) (*EnterBoxResp, error)
	ExitBox(context.Context, *ExitBoxReq) (*ExitBoxResp, error)
	JoinBox(context.Context, *JoinBoxReq) (*JoinBoxResp, error)
	GetBoxInfo(context.Context, *GetBoxInfoReq) (*GetBoxInfoResp, error)
	ClearJoinBox(context.Context, *ClearJoinBoxReq) (*ClearJoinBoxResp, error)
	BatchGetBoxInfos(context.Context, *BatchGetBoxInfosReq) (*BatchGetBoxInfosResp, error)
	GetBoxInfosByLimit(context.Context, *GetBoxInfosByLimitReq) (*GetBoxInfosByLimitResp, error)
	GetBoxUserInMicInfos(context.Context, *GetBoxUserInMicInfosReq) (*GetBoxUserInMicInfosResp, error)
	ExitChannel(context.Context, *ExitChannelReq) (*ExitChannelResp, error)
	UpsertBoxInfo(context.Context, *UpsertBoxInfoReq) (*UpsertBoxInfoResp, error)
	DelBoxInfo(context.Context, *DelBoxInfoReq) (*DelBoxInfoResp, error)
	ApplyOnMicToken(context.Context, *ApplyOnMicTokenReq) (*ApplyOnMicTokenResp, error)
	SetChannelMicBoxId(context.Context, *SetChannelMicBoxIdReq) (*SetChannelMicBoxIdResp, error)
	// 麦位申请记录
	SetMicApplyRecords(context.Context, *SetMicApplyRecordsReq) (*SetMicApplyRecordsResp, error)
	GetMicApplyRecords(context.Context, *GetMicApplyRecordsReq) (*GetMicApplyRecordsResp, error)
	// 主房间用户管理
	AddMainChannelUser(context.Context, *AddMainChannelUserReq) (*AddMainChannelUserResp, error)
	GetMainChannelUsers(context.Context, *GetMainChannelUsersReq) (*GetMainChannelUsersResp, error)
}

func RegisterChannelBoxServer(s *grpc.Server, srv ChannelBoxServer) {
	s.RegisterService(&_ChannelBox_serviceDesc, srv)
}

func _ChannelBox_EnterBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnterBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).EnterBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/EnterBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).EnterBox(ctx, req.(*EnterBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_ExitBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExitBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).ExitBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/ExitBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).ExitBox(ctx, req.(*ExitBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_JoinBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).JoinBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/JoinBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).JoinBox(ctx, req.(*JoinBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_GetBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).GetBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/GetBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).GetBoxInfo(ctx, req.(*GetBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_ClearJoinBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearJoinBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).ClearJoinBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/ClearJoinBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).ClearJoinBox(ctx, req.(*ClearJoinBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_BatchGetBoxInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetBoxInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).BatchGetBoxInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/BatchGetBoxInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).BatchGetBoxInfos(ctx, req.(*BatchGetBoxInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_GetBoxInfosByLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBoxInfosByLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).GetBoxInfosByLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/GetBoxInfosByLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).GetBoxInfosByLimit(ctx, req.(*GetBoxInfosByLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_GetBoxUserInMicInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBoxUserInMicInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).GetBoxUserInMicInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/GetBoxUserInMicInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).GetBoxUserInMicInfos(ctx, req.(*GetBoxUserInMicInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_ExitChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExitChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).ExitChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/ExitChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).ExitChannel(ctx, req.(*ExitChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_UpsertBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).UpsertBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/UpsertBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).UpsertBoxInfo(ctx, req.(*UpsertBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_DelBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).DelBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/DelBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).DelBoxInfo(ctx, req.(*DelBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_ApplyOnMicToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyOnMicTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).ApplyOnMicToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/ApplyOnMicToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).ApplyOnMicToken(ctx, req.(*ApplyOnMicTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_SetChannelMicBoxId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMicBoxIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).SetChannelMicBoxId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/SetChannelMicBoxId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).SetChannelMicBoxId(ctx, req.(*SetChannelMicBoxIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_SetMicApplyRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMicApplyRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).SetMicApplyRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/SetMicApplyRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).SetMicApplyRecords(ctx, req.(*SetMicApplyRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_GetMicApplyRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMicApplyRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).GetMicApplyRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/GetMicApplyRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).GetMicApplyRecords(ctx, req.(*GetMicApplyRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_AddMainChannelUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMainChannelUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).AddMainChannelUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/AddMainChannelUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).AddMainChannelUser(ctx, req.(*AddMainChannelUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelBox_GetMainChannelUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMainChannelUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelBoxServer).GetMainChannelUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelbox.ChannelBox/GetMainChannelUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelBoxServer).GetMainChannelUsers(ctx, req.(*GetMainChannelUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelBox_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelbox.ChannelBox",
	HandlerType: (*ChannelBoxServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "EnterBox",
			Handler:    _ChannelBox_EnterBox_Handler,
		},
		{
			MethodName: "ExitBox",
			Handler:    _ChannelBox_ExitBox_Handler,
		},
		{
			MethodName: "JoinBox",
			Handler:    _ChannelBox_JoinBox_Handler,
		},
		{
			MethodName: "GetBoxInfo",
			Handler:    _ChannelBox_GetBoxInfo_Handler,
		},
		{
			MethodName: "ClearJoinBox",
			Handler:    _ChannelBox_ClearJoinBox_Handler,
		},
		{
			MethodName: "BatchGetBoxInfos",
			Handler:    _ChannelBox_BatchGetBoxInfos_Handler,
		},
		{
			MethodName: "GetBoxInfosByLimit",
			Handler:    _ChannelBox_GetBoxInfosByLimit_Handler,
		},
		{
			MethodName: "GetBoxUserInMicInfos",
			Handler:    _ChannelBox_GetBoxUserInMicInfos_Handler,
		},
		{
			MethodName: "ExitChannel",
			Handler:    _ChannelBox_ExitChannel_Handler,
		},
		{
			MethodName: "UpsertBoxInfo",
			Handler:    _ChannelBox_UpsertBoxInfo_Handler,
		},
		{
			MethodName: "DelBoxInfo",
			Handler:    _ChannelBox_DelBoxInfo_Handler,
		},
		{
			MethodName: "ApplyOnMicToken",
			Handler:    _ChannelBox_ApplyOnMicToken_Handler,
		},
		{
			MethodName: "SetChannelMicBoxId",
			Handler:    _ChannelBox_SetChannelMicBoxId_Handler,
		},
		{
			MethodName: "SetMicApplyRecords",
			Handler:    _ChannelBox_SetMicApplyRecords_Handler,
		},
		{
			MethodName: "GetMicApplyRecords",
			Handler:    _ChannelBox_GetMicApplyRecords_Handler,
		},
		{
			MethodName: "AddMainChannelUser",
			Handler:    _ChannelBox_AddMainChannelUser_Handler,
		},
		{
			MethodName: "GetMainChannelUsers",
			Handler:    _ChannelBox_GetMainChannelUsers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channelbox/channelbox.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channelbox/channelbox.proto", fileDescriptor_channelbox_5a1dd58cca48abc7)
}

var fileDescriptor_channelbox_5a1dd58cca48abc7 = []byte{
	// 1836 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x59, 0x5f, 0x73, 0x23, 0x47,
	0x11, 0xf7, 0x4a, 0xfe, 0x23, 0xb5, 0x2c, 0x4b, 0x37, 0x3e, 0xdb, 0xb2, 0x9c, 0x70, 0xbe, 0x0d,
	0x01, 0xe3, 0x0a, 0x3e, 0xb8, 0x5c, 0x42, 0x25, 0x81, 0x02, 0xcb, 0x39, 0x2e, 0x0e, 0xb1, 0xa1,
	0xd6, 0x76, 0xa5, 0x8a, 0x2a, 0x4a, 0xac, 0x76, 0x27, 0xbe, 0xb9, 0xdb, 0xdd, 0xd9, 0xd3, 0x8c,
	0x8c, 0xfc, 0xc2, 0x1b, 0x95, 0xc7, 0x7b, 0xe1, 0x81, 0x07, 0xaa, 0xf8, 0x0c, 0x3c, 0xf3, 0x1d,
	0xe0, 0x81, 0x2f, 0x44, 0x4d, 0xcf, 0xfe, 0x99, 0xd5, 0xae, 0xff, 0x1d, 0xf7, 0x40, 0xf1, 0xb6,
	0xd3, 0xdd, 0xd3, 0xdd, 0xf3, 0xeb, 0xee, 0xe9, 0x1e, 0x09, 0x76, 0xa5, 0x7c, 0xf4, 0x6a, 0xc2,
	0xbc, 0x97, 0x82, 0x05, 0x17, 0x74, 0xfc, 0xc8, 0x7b, 0xee, 0x46, 0x11, 0x0d, 0x46, 0x7c, 0x6a,
	0x7c, 0xee, 0xc5, 0x63, 0x2e, 0x39, 0x81, 0x9c, 0x62, 0xff, 0xb9, 0x06, 0x4b, 0x03, 0x3e, 0x3d,
	0x8c, 0xbe, 0xe1, 0xe4, 0x5d, 0x48, 0x39, 0x43, 0xe6, 0xf7, 0xac, 0x6d, 0x6b, 0xa7, 0xed, 0x34,
	0x13, 0xca, 0xa1, 0x4f, 0xd6, 0x60, 0x71, 0xc4, 0xa7, 0x8a, 0x55, 0x43, 0xd6, 0xc2, 0x88, 0x4f,
	0x0f, 0x7d, 0xf2, 0x00, 0x5a, 0xde, 0x98, 0xba, 0x92, 0x8f, 0x87, 0x13, 0xe6, 0xf7, 0xea, 0xc8,
	0x83, 0x84, 0x74, 0xc6, 0x7c, 0xb2, 0x07, 0x0d, 0xb5, 0x4f, 0x5e, 0xc6, 0xb4, 0x37, 0xbf, 0x6d,
	0xed, 0xac, 0x3c, 0x5e, 0xdd, 0x33, 0x7c, 0x1a, 0xf0, 0xe9, 0xe9, 0x65, 0x4c, 0x9d, 0xa5, 0x91,
	0xfe, 0x20, 0x9b, 0x5a, 0x3e, 0x72, 0x43, 0xda, 0x5b, 0xd8, 0xb6, 0x76, 0x9a, 0xc8, 0x3a, 0x76,
	0x43, 0x4a, 0x36, 0x60, 0x29, 0x64, 0xde, 0xd0, 0x73, 0xe3, 0xde, 0x22, 0xda, 0x59, 0x0c, 0x99,
	0x77, 0xe0, 0xc6, 0xe4, 0x7b, 0xd0, 0x89, 0x27, 0xa3, 0x80, 0x79, 0x43, 0xc5, 0x0f, 0x98, 0x90,
	0xbd, 0xa5, 0xed, 0xfa, 0x4e, 0xdb, 0x69, 0x6b, 0xf2, 0x11, 0xf3, 0xbe, 0x62, 0x42, 0x2a, 0xb9,
	0x88, 0x8f, 0x43, 0x37, 0xc8, 0xe5, 0x1a, 0x5a, 0x4e, 0x93, 0x13, 0x39, 0xfb, 0x5f, 0x16, 0xb4,
	0x9e, 0x46, 0x92, 0x8e, 0x07, 0x7c, 0xea, 0xd0, 0x57, 0xa4, 0x0b, 0xf5, 0x49, 0x86, 0x89, 0xfa,
	0x24, 0xef, 0x40, 0x0a, 0x4d, 0x06, 0x48, 0x4e, 0x20, 0xf7, 0x41, 0xa1, 0x93, 0xc1, 0xa1, 0x17,
	0x77, 0x46, 0xe2, 0x21, 0x2c, 0x7b, 0x3c, 0x0c, 0x79, 0x34, 0xd4, 0xca, 0x16, 0x50, 0x59, 0x4b,
	0xd3, 0x06, 0xa8, 0xf2, 0xfb, 0xd0, 0x19, 0x8d, 0xb9, 0xeb, 0x7b, 0xae, 0x90, 0x89, 0x94, 0x46,
	0x66, 0x25, 0x23, 0xa3, 0xa0, 0xfd, 0x37, 0x0b, 0x96, 0xf3, 0x13, 0x89, 0x98, 0x6c, 0x41, 0x93,
	0x89, 0xa1, 0xf8, 0x03, 0x93, 0xde, 0x73, 0x3c, 0x58, 0xc3, 0x69, 0x30, 0x71, 0x82, 0x6b, 0xf2,
	0x0b, 0xe8, 0x50, 0x25, 0x3c, 0xc4, 0x88, 0x47, 0xdf, 0x70, 0xd1, 0xab, 0x6d, 0xd7, 0x77, 0x5a,
	0x8f, 0x7b, 0xa6, 0xc3, 0xa9, 0x3e, 0x95, 0x3d, 0x4e, 0x9b, 0x1a, 0x2b, 0x41, 0x08, 0xcc, 0xf3,
	0x58, 0x0a, 0x04, 0x60, 0xde, 0xc1, 0xef, 0x22, 0x66, 0xf3, 0x33, 0x98, 0xd9, 0x7f, 0x35, 0x3c,
	0xc4, 0x7c, 0xfc, 0xc4, 0x80, 0xcb, 0x42, 0xb8, 0xbe, 0x53, 0xb2, 0xee, 0x50, 0x31, 0x09, 0x64,
	0x09, 0xb9, 0x0c, 0xff, 0x9a, 0x89, 0xff, 0x06, 0x28, 0x81, 0xa1, 0x17, 0xc9, 0x24, 0x2e, 0x2a,
	0xa1, 0x0f, 0x22, 0x49, 0x6c, 0x68, 0xbb, 0x13, 0x9f, 0x71, 0x7d, 0x5c, 0x5f, 0xf4, 0xe6, 0x31,
	0x29, 0x5a, 0x48, 0x54, 0xee, 0xf8, 0xc2, 0xfe, 0xa7, 0x05, 0xf0, 0x74, 0xca, 0xe4, 0xff, 0x4d,
	0x46, 0x7c, 0x0a, 0xad, 0xec, 0x3c, 0x22, 0x36, 0xc1, 0xb1, 0x0a, 0xe0, 0xa4, 0x91, 0xac, 0xe5,
	0x91, 0xb4, 0x5f, 0x5b, 0x00, 0x5f, 0x72, 0x16, 0xbd, 0x55, 0x30, 0x3e, 0x83, 0xf6, 0x0b, 0xce,
	0xf0, 0x68, 0x26, 0x22, 0x1b, 0x26, 0x22, 0x89, 0x51, 0x44, 0xa5, 0xf5, 0x22, 0x5f, 0xd8, 0xff,
	0xb0, 0xa0, 0x95, 0x79, 0xf4, 0x3f, 0x98, 0xde, 0x89, 0x43, 0xec, 0x3c, 0xe2, 0x63, 0x7d, 0xaf,
	0xa1, 0x43, 0x87, 0xb8, 0xb6, 0x03, 0x68, 0x0d, 0xf8, 0x74, 0xe0, 0x0a, 0x8a, 0x99, 0x9f, 0xe1,
	0x63, 0x99, 0xf8, 0x24, 0x28, 0xd7, 0x72, 0x94, 0xab, 0xbc, 0x78, 0x08, 0xcb, 0x59, 0x2e, 0x57,
	0xa4, 0x32, 0xf3, 0x85, 0x3d, 0x02, 0x18, 0x60, 0x6c, 0xaf, 0x31, 0xb6, 0x0d, 0xcb, 0x0a, 0x9c,
	0x89, 0xa0, 0x63, 0xcc, 0x09, 0x6d, 0x15, 0x46, 0x7c, 0x7a, 0x26, 0xe8, 0x58, 0xe5, 0x45, 0xe1,
	0xb8, 0xf5, 0xd9, 0x6a, 0xbe, 0x80, 0xf6, 0x33, 0x2a, 0x53, 0xf4, 0xe8, 0x2b, 0x95, 0x5f, 0x3c,
	0xa6, 0xc3, 0x3c, 0x4f, 0x16, 0x79, 0x4c, 0xcf, 0x6e, 0x4c, 0x15, 0xb3, 0x42, 0xea, 0x37, 0x57,
	0x88, 0xfd, 0x6d, 0x0d, 0x56, 0x4c, 0xc3, 0x22, 0x26, 0x3f, 0xd6, 0x2a, 0x54, 0x9c, 0x7b, 0x16,
	0x86, 0x79, 0x7d, 0x46, 0x45, 0x02, 0x05, 0x6a, 0x49, 0x5b, 0x21, 0x9e, 0xdc, 0xbc, 0x44, 0x9a,
	0x8a, 0x32, 0x30, 0xcb, 0x16, 0xfb, 0x47, 0x1d, 0x35, 0xce, 0x3a, 0x95, 0xa9, 0xc3, 0xb6, 0xb3,
	0x05, 0x4d, 0xd5, 0x6f, 0x24, 0x97, 0x6e, 0x90, 0x64, 0x46, 0x23, 0x64, 0xde, 0xa9, 0x5a, 0x93,
	0x5d, 0xb8, 0x87, 0xb6, 0x0a, 0x51, 0x5b, 0xc0, 0xa8, 0x75, 0x14, 0x63, 0x3f, 0x8f, 0x9c, 0x92,
	0x0d, 0x5d, 0x16, 0x0d, 0x0b, 0x97, 0x80, 0x2e, 0xef, 0x8e, 0x62, 0x1c, 0xe4, 0x17, 0x81, 0xfd,
	0xad, 0x05, 0x9d, 0x83, 0x80, 0xba, 0xe3, 0xb7, 0x5e, 0xa8, 0x77, 0xbc, 0xb5, 0x6c, 0x02, 0xdd,
	0xa2, 0x23, 0x22, 0xb6, 0xff, 0x08, 0xab, 0x03, 0x57, 0x7a, 0xcf, 0xf3, 0x58, 0x09, 0xe5, 0xe0,
	0x26, 0x34, 0x26, 0xcc, 0xd7, 0xc8, 0x5a, 0x88, 0xc1, 0xd2, 0x84, 0xf9, 0x08, 0xe2, 0xdb, 0xcd,
	0x93, 0x33, 0xb8, 0x5f, 0xb6, 0x2f, 0x62, 0xf2, 0x33, 0x58, 0x51, 0x7a, 0x46, 0xae, 0xa0, 0xc9,
	0xcd, 0xa0, 0x53, 0x66, 0x63, 0x46, 0x5b, 0x5a, 0xab, 0x8e, 0x2a, 0x93, 0x74, 0x21, 0xec, 0xbf,
	0x5b, 0xb0, 0x66, 0xa8, 0x1c, 0x5c, 0x7e, 0xc5, 0x42, 0x26, 0xd5, 0xc9, 0x0a, 0xee, 0x5b, 0x57,
	0x02, 0x5d, 0x68, 0x58, 0x5b, 0xd0, 0x0c, 0xd4, 0x35, 0x2e, 0x59, 0xa8, 0x4f, 0x55, 0x77, 0x1a,
	0x8a, 0x70, 0xca, 0xf4, 0x30, 0x74, 0x4e, 0x25, 0x16, 0xe7, 0x3c, 0xb2, 0x16, 0xcf, 0xa9, 0x54,
	0x85, 0x69, 0x42, 0xb1, 0x70, 0x0b, 0x28, 0xbe, 0x86, 0xf5, 0x2a, 0x97, 0xff, 0x7b, 0x30, 0xfe,
	0x62, 0xc1, 0x86, 0xd6, 0xac, 0xee, 0x8c, 0xc3, 0xe8, 0x88, 0x79, 0x59, 0xa0, 0xdf, 0x04, 0x8e,
	0x4d, 0x50, 0x55, 0xa3, 0xae, 0x10, 0x81, 0x65, 0xd7, 0x76, 0xd4, 0x38, 0x78, 0xa6, 0x0a, 0xe3,
	0xae, 0x29, 0xf9, 0x11, 0xf4, 0xaa, 0x3d, 0x13, 0x71, 0x3a, 0x80, 0xa2, 0x99, 0x24, 0x07, 0xd5,
	0xb5, 0xa7, 0x6e, 0xce, 0x18, 0x56, 0x54, 0xcf, 0x3c, 0xd0, 0x9a, 0xdf, 0xa4, 0xa2, 0xee, 0x9a,
	0xa7, 0x7f, 0xb2, 0xa0, 0x53, 0x30, 0x79, 0xc7, 0x56, 0x6d, 0x8c, 0xed, 0x75, 0x73, 0x6c, 0xbf,
	0x2b, 0x60, 0x9f, 0x40, 0xf7, 0x2c, 0x16, 0x74, 0x6c, 0x5e, 0xe9, 0xef, 0x43, 0x7d, 0xc4, 0xa7,
	0xe8, 0xc3, 0x15, 0x37, 0xa0, 0xe2, 0xdb, 0x9f, 0xc2, 0xbd, 0x99, 0xad, 0x22, 0xbe, 0xed, 0xde,
	0x09, 0xb4, 0x3f, 0xa7, 0x81, 0x61, 0xf3, 0xcd, 0x1e, 0x29, 0x77, 0x45, 0xfd, 0x07, 0xb0, 0x62,
	0x9a, 0xcd, 0x31, 0x8f, 0x26, 0xa1, 0x81, 0xf9, 0xf1, 0x24, 0xb4, 0xbf, 0x04, 0xb2, 0x1f, 0xc7,
	0xc1, 0xe5, 0xaf, 0x55, 0x12, 0x9d, 0xf2, 0x97, 0x34, 0xba, 0x9d, 0x9b, 0x2a, 0x93, 0x73, 0x37,
	0x43, 0xe6, 0x1d, 0xfa, 0xf6, 0x31, 0xac, 0x96, 0x74, 0x89, 0x58, 0xf5, 0x62, 0x1e, 0x0d, 0x75,
	0x07, 0x79, 0x49, 0x23, 0x54, 0xd7, 0x74, 0x80, 0x67, 0x52, 0x57, 0xe9, 0xbb, 0x80, 0xb5, 0x13,
	0x9a, 0xa6, 0xce, 0x11, 0xf3, 0x70, 0x94, 0xbd, 0x85, 0x7b, 0xef, 0x43, 0x87, 0xc7, 0x54, 0x9b,
	0x2c, 0xc0, 0xb9, 0xac, 0xc8, 0xa9, 0x22, 0xb3, 0xa5, 0xd7, 0xcd, 0x96, 0x6e, 0x7f, 0x00, 0xeb,
	0x55, 0x76, 0x45, 0x9c, 0x65, 0xa8, 0x85, 0x37, 0x96, 0x1e, 0x26, 0xff, 0x6d, 0xc1, 0xca, 0x11,
	0xf3, 0xf0, 0xe4, 0x0e, 0xf5, 0xf8, 0xd8, 0xc4, 0xc7, 0x32, 0xce, 0xa3, 0xc2, 0x88, 0x28, 0xa8,
	0x30, 0xd6, 0xca, 0x61, 0x54, 0x70, 0x60, 0x18, 0x43, 0xfd, 0x41, 0x9e, 0x00, 0x28, 0x79, 0x21,
	0x5d, 0x39, 0x11, 0x49, 0xe0, 0xd7, 0x66, 0x76, 0x9c, 0x20, 0xd3, 0x51, 0x0d, 0x5a, 0x7f, 0xa6,
	0x25, 0x3d, 0x9f, 0x97, 0x74, 0x9e, 0x55, 0x0b, 0x66, 0x56, 0x6d, 0x41, 0x73, 0x12, 0xfb, 0xae,
	0xa4, 0x43, 0x57, 0x26, 0x5d, 0xb8, 0xa1, 0x09, 0xfb, 0xd2, 0x0e, 0x10, 0xfb, 0xe2, 0xb9, 0xc4,
	0x2d, 0xb0, 0x7f, 0x02, 0x4b, 0x63, 0x2d, 0x9c, 0xcc, 0xa4, 0xfd, 0x19, 0x87, 0x0d, 0x7d, 0x4e,
	0x2a, 0x6a, 0xf7, 0x10, 0xf1, 0x92, 0x35, 0x11, 0xdb, 0x1f, 0x63, 0x43, 0xba, 0xb3, 0x1f, 0xf6,
	0x31, 0x76, 0x85, 0x0a, 0x8d, 0xa6, 0x87, 0xd6, 0xed, 0x3d, 0xfc, 0x02, 0xd6, 0xf6, 0x7d, 0xff,
	0x48, 0x0d, 0x29, 0x5a, 0x58, 0xdd, 0xbc, 0xb7, 0xc0, 0xa3, 0x34, 0xf5, 0xaa, 0xb3, 0x56, 0x69,
	0x12, 0xb1, 0xfd, 0x13, 0xed, 0x73, 0x91, 0x73, 0x9b, 0xc3, 0x3e, 0xc1, 0x46, 0x55, 0xde, 0xa8,
	0xbb, 0x01, 0x8e, 0x67, 0x46, 0x37, 0x50, 0xeb, 0x43, 0x5f, 0xec, 0xfe, 0x08, 0x7f, 0x3b, 0xc1,
	0x4c, 0x5b, 0x85, 0x4e, 0x7a, 0x89, 0xf0, 0x80, 0xc6, 0x81, 0x7b, 0xd9, 0x9d, 0x23, 0x5d, 0x58,
	0x4e, 0x88, 0x47, 0x34, 0xa0, 0xb4, 0x6b, 0xed, 0x7e, 0x08, 0xa4, 0xfc, 0x6c, 0x25, 0x6d, 0x68,
	0x22, 0x55, 0x2d, 0xba, 0x73, 0x64, 0x05, 0x40, 0xbf, 0x53, 0x70, 0x6d, 0xed, 0x7e, 0x96, 0xbd,
	0x6c, 0x50, 0x7a, 0x19, 0x1a, 0xc7, 0x3c, 0xa2, 0xb9, 0xf0, 0xbe, 0xe7, 0xd1, 0x58, 0x6a, 0x61,
	0xb5, 0x76, 0xe8, 0x0b, 0xea, 0xe9, 0x75, 0x6d, 0xf7, 0x87, 0xb0, 0x94, 0x94, 0x05, 0xe9, 0x40,
	0x2b, 0xf9, 0xfc, 0xe5, 0x98, 0xaa, 0xbd, 0xf7, 0xa0, 0x9d, 0x10, 0x7e, 0x83, 0xbf, 0x92, 0x74,
	0xad, 0xdd, 0x8f, 0xa1, 0x99, 0xd5, 0x04, 0x59, 0x83, 0x7b, 0xd9, 0x02, 0x63, 0xca, 0xa2, 0xf3,
	0xee, 0x1c, 0x21, 0x58, 0xae, 0x9a, 0x7c, 0x26, 0x14, 0xcd, 0xda, 0xbd, 0x80, 0xd6, 0xc9, 0xa5,
	0x90, 0x34, 0x54, 0xa5, 0xfe, 0x39, 0x59, 0x07, 0x62, 0x2c, 0x0f, 0xa3, 0x0b, 0x37, 0x60, 0x7e,
	0x77, 0x8e, 0x6c, 0xc2, 0x9a, 0x41, 0x3f, 0x99, 0x8c, 0x12, 0xb8, 0xbb, 0x35, 0xd2, 0x87, 0x75,
	0x83, 0x65, 0x84, 0xa2, 0x5b, 0x27, 0x3d, 0xb8, 0x6f, 0xf0, 0x06, 0xe9, 0x3b, 0xb6, 0x3b, 0xff,
	0xf8, 0x75, 0x0b, 0x20, 0x91, 0x1b, 0xf0, 0x29, 0xf9, 0x39, 0x34, 0xd2, 0x57, 0x1b, 0xd9, 0xa8,
	0x7a, 0xcb, 0x39, 0xf4, 0x55, 0xbf, 0x57, 0xcd, 0x10, 0xb1, 0x3d, 0x47, 0x7e, 0x0a, 0x4b, 0xc9,
	0xa3, 0x98, 0x14, 0x1e, 0x09, 0xf9, 0xcb, 0xbf, 0xbf, 0x51, 0x49, 0x4f, 0x77, 0x27, 0x91, 0x2a,
	0xee, 0xce, 0x27, 0xf0, 0xfe, 0x46, 0x25, 0x1d, 0x77, 0x3f, 0x05, 0xc8, 0xe7, 0x30, 0xb2, 0x69,
	0x0a, 0x16, 0x9e, 0x52, 0xfd, 0xfe, 0x55, 0x2c, 0x54, 0xf3, 0x2b, 0x58, 0x36, 0xa7, 0x6d, 0xb2,
	0x65, 0x4a, 0xcf, 0x3c, 0x08, 0xfa, 0xef, 0x5c, 0xcd, 0x44, 0x65, 0x5f, 0x43, 0x77, 0x76, 0x4c,
	0x26, 0x0f, 0x0a, 0xad, 0xb3, 0x3c, 0xc4, 0xf7, 0xb7, 0xaf, 0x17, 0x40, 0xc5, 0xbf, 0x03, 0x52,
	0x1e, 0x3a, 0xc9, 0xc3, 0xea, 0x93, 0x19, 0x73, 0x74, 0xdf, 0xbe, 0x49, 0x04, 0xd5, 0x7b, 0x70,
	0xbf, 0x6a, 0xbe, 0x23, 0xef, 0x95, 0x77, 0x97, 0x66, 0xd3, 0xfe, 0x77, 0x6f, 0x16, 0x42, 0x23,
	0x5f, 0xe8, 0x5f, 0x50, 0x92, 0xfc, 0x23, 0xfd, 0xd9, 0xc4, 0xc8, 0xc7, 0xc4, 0xfe, 0xd6, 0x95,
	0x3c, 0xd4, 0x74, 0x0c, 0xed, 0xc2, 0x88, 0x44, 0x0a, 0x71, 0x99, 0x1d, 0xbc, 0xfa, 0xef, 0x5e,
	0xc3, 0x4d, 0x53, 0x29, 0x9f, 0x5f, 0x8a, 0xa9, 0x54, 0x18, 0xa7, 0x8a, 0xa9, 0x54, 0x1c, 0x79,
	0xec, 0x39, 0x72, 0x0a, 0x9d, 0x99, 0x79, 0x84, 0x14, 0x7e, 0x82, 0x2b, 0x0f, 0x3e, 0xfd, 0x07,
	0xd7, 0xf2, 0xd3, 0xd0, 0x97, 0xa7, 0x83, 0x62, 0xe8, 0x2b, 0xa7, 0x96, 0x62, 0xe8, 0xab, 0x07,
	0x8c, 0x4c, 0xfd, 0x4c, 0xe3, 0x2a, 0xa9, 0x2f, 0x37, 0xc4, 0x92, 0xfa, 0xaa, 0x6e, 0x9a, 0x26,
	0xee, 0xb5, 0xea, 0x9f, 0xdd, 0xac, 0xfe, 0xd9, 0x35, 0xea, 0xcb, 0xcd, 0xad, 0xa8, 0xbe, 0xb2,
	0x8d, 0x16, 0xd5, 0x5f, 0xd1, 0x1f, 0xe7, 0xc8, 0xef, 0x61, 0xb5, 0xa2, 0xd1, 0x91, 0x92, 0x6f,
	0xe5, 0x16, 0xda, 0x7f, 0xef, 0x46, 0x19, 0x65, 0x61, 0xb0, 0xf7, 0xdb, 0x0f, 0xce, 0x79, 0xe0,
	0x46, 0xe7, 0x7b, 0x1f, 0x3d, 0x96, 0x72, 0xcf, 0xe3, 0xe1, 0x23, 0xfc, 0xdb, 0xc1, 0xe3, 0xc1,
	0x23, 0x41, 0xc7, 0x17, 0xcc, 0xa3, 0xc2, 0xf8, 0x4f, 0x62, 0xb4, 0x88, 0xdc, 0x0f, 0xff, 0x13,
	0x00, 0x00, 0xff, 0xff, 0xb1, 0x94, 0x5b, 0xcb, 0xc2, 0x18, 0x00, 0x00,
}
