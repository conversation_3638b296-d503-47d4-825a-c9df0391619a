// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/topic_channel/recommendation_gen.proto

package recommendation_gen // import "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"

/*
buf:lint:ignore DIRECTORY_SAME_PACKAGE
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import common "golang.52tt.com/protocol/services/rcmd/common"
import rcmd_channel_label "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
import recommendation_common "golang.52tt.com/protocol/services/topic_channel/recommendation_common"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// IM页房间推荐分组
type IMChannelListABGroup int32

const (
	IMChannelListABGroup_DEFAULT             IMChannelListABGroup = 0
	IMChannelListABGroup_RegLess72Hour_Exp_A IMChannelListABGroup = 1
	IMChannelListABGroup_RegLess72Hour_Exp_B IMChannelListABGroup = 2
	IMChannelListABGroup_RegMore72Hour_Exp   IMChannelListABGroup = 3
)

var IMChannelListABGroup_name = map[int32]string{
	0: "DEFAULT",
	1: "RegLess72Hour_Exp_A",
	2: "RegLess72Hour_Exp_B",
	3: "RegMore72Hour_Exp",
}
var IMChannelListABGroup_value = map[string]int32{
	"DEFAULT":             0,
	"RegLess72Hour_Exp_A": 1,
	"RegLess72Hour_Exp_B": 2,
	"RegMore72Hour_Exp":   3,
}

func (x IMChannelListABGroup) String() string {
	return proto.EnumName(IMChannelListABGroup_name, int32(x))
}
func (IMChannelListABGroup) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{0}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type REGULATORY_LEVEL int32

const (
	REGULATORY_LEVEL_FREE         REGULATORY_LEVEL = 0
	REGULATORY_LEVEL_SIMPLE_MINOR REGULATORY_LEVEL = 1
)

var REGULATORY_LEVEL_name = map[int32]string{
	0: "FREE",
	1: "SIMPLE_MINOR",
}
var REGULATORY_LEVEL_value = map[string]int32{
	"FREE":         0,
	"SIMPLE_MINOR": 1,
}

func (x REGULATORY_LEVEL) String() string {
	return proto.EnumName(REGULATORY_LEVEL_name, int32(x))
}
func (REGULATORY_LEVEL) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{1}
}

type PrefGameLabelType int32

const (
	PrefGameLabelType_SYSTEM PrefGameLabelType = 0
	PrefGameLabelType_CUSTOM PrefGameLabelType = 1
)

var PrefGameLabelType_name = map[int32]string{
	0: "SYSTEM",
	1: "CUSTOM",
}
var PrefGameLabelType_value = map[string]int32{
	"SYSTEM": 0,
	"CUSTOM": 1,
}

func (x PrefGameLabelType) String() string {
	return proto.EnumName(PrefGameLabelType_name, int32(x))
}
func (PrefGameLabelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{2}
}

type BusinessBlockEnum int32

const (
	BusinessBlockEnum_RoomMode      BusinessBlockEnum = 0
	BusinessBlockEnum_GameCondition BusinessBlockEnum = 1
)

var BusinessBlockEnum_name = map[int32]string{
	0: "RoomMode",
	1: "GameCondition",
}
var BusinessBlockEnum_value = map[string]int32{
	"RoomMode":      0,
	"GameCondition": 1,
}

func (x BusinessBlockEnum) String() string {
	return proto.EnumName(BusinessBlockEnum_name, int32(x))
}
func (BusinessBlockEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{3}
}

type BusinessElemEnum int32

const (
	BusinessElemEnum_NoLimit BusinessElemEnum = 0
	BusinessElemEnum_Single  BusinessElemEnum = 1
	BusinessElemEnum_Double  BusinessElemEnum = 2
	BusinessElemEnum_Waiting BusinessElemEnum = 3
	BusinessElemEnum_Started BusinessElemEnum = 4
)

var BusinessElemEnum_name = map[int32]string{
	0: "NoLimit",
	1: "Single",
	2: "Double",
	3: "Waiting",
	4: "Started",
}
var BusinessElemEnum_value = map[string]int32{
	"NoLimit": 0,
	"Single":  1,
	"Double":  2,
	"Waiting": 3,
	"Started": 4,
}

func (x BusinessElemEnum) String() string {
	return proto.EnumName(BusinessElemEnum_name, int32(x))
}
func (BusinessElemEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{4}
}

// 关注的人＞一起玩过＞同城标签
type RCMDLabel int32

const (
	RCMDLabel_None                          RCMDLabel = 0
	RCMDLabel_GangUpWithHomeOwner           RCMDLabel = 1
	RCMDLabel_ChatWithHomeOwner             RCMDLabel = 2
	RCMDLabel_FollowUserInChannel           RCMDLabel = 3
	RCMDLabel_LocShow                       RCMDLabel = 4
	RCMDLabel_MtFollowUserInChannel         RCMDLabel = 5
	RCMDLabel_MtEverEnterChannel            RCMDLabel = 6
	RCMDLabel_MtManyUsersInChannel          RCMDLabel = 7
	RCMDLabel_MtManyUsersFollowChannelOwner RCMDLabel = 8
	RCMDLabel_MtGuessYouLikeChannelOwner    RCMDLabel = 9
	RCMDLabel_MtRecentLikeTabChannelDouDi   RCMDLabel = 10
	RCMDLabel_MtHotGameHotChannelDouDi      RCMDLabel = 11
	RCMDLabel_MtFriendOfFriend              RCMDLabel = 12
	RCMDLabel_MtRecentFollow                RCMDLabel = 13
)

var RCMDLabel_name = map[int32]string{
	0:  "None",
	1:  "GangUpWithHomeOwner",
	2:  "ChatWithHomeOwner",
	3:  "FollowUserInChannel",
	4:  "LocShow",
	5:  "MtFollowUserInChannel",
	6:  "MtEverEnterChannel",
	7:  "MtManyUsersInChannel",
	8:  "MtManyUsersFollowChannelOwner",
	9:  "MtGuessYouLikeChannelOwner",
	10: "MtRecentLikeTabChannelDouDi",
	11: "MtHotGameHotChannelDouDi",
	12: "MtFriendOfFriend",
	13: "MtRecentFollow",
}
var RCMDLabel_value = map[string]int32{
	"None":                          0,
	"GangUpWithHomeOwner":           1,
	"ChatWithHomeOwner":             2,
	"FollowUserInChannel":           3,
	"LocShow":                       4,
	"MtFollowUserInChannel":         5,
	"MtEverEnterChannel":            6,
	"MtManyUsersInChannel":          7,
	"MtManyUsersFollowChannelOwner": 8,
	"MtGuessYouLikeChannelOwner":    9,
	"MtRecentLikeTabChannelDouDi":   10,
	"MtHotGameHotChannelDouDi":      11,
	"MtFriendOfFriend":              12,
	"MtRecentFollow":                13,
}

func (x RCMDLabel) String() string {
	return proto.EnumName(RCMDLabel_name, int32(x))
}
func (RCMDLabel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{5}
}

type RCMDReason int32

const (
	RCMDReason_Reason_None       RCMDReason = 0
	RCMDReason_Reason_HotGameTop RCMDReason = 1
)

var RCMDReason_name = map[int32]string{
	0: "Reason_None",
	1: "Reason_HotGameTop",
}
var RCMDReason_value = map[string]int32{
	"Reason_None":       0,
	"Reason_HotGameTop": 1,
}

func (x RCMDReason) String() string {
	return proto.EnumName(RCMDReason_name, int32(x))
}
func (RCMDReason) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{6}
}

type GetRCMDCfgReq_SOURCE int32

const (
	GetRCMDCfgReq_SOURCE_DEFAULT GetRCMDCfgReq_SOURCE = 0
	GetRCMDCfgReq_SOURCE_TT      GetRCMDCfgReq_SOURCE = 1
)

var GetRCMDCfgReq_SOURCE_name = map[int32]string{
	0: "SOURCE_DEFAULT",
	1: "SOURCE_TT",
}
var GetRCMDCfgReq_SOURCE_value = map[string]int32{
	"SOURCE_DEFAULT": 0,
	"SOURCE_TT":      1,
}

func (x GetRCMDCfgReq_SOURCE) String() string {
	return proto.EnumName(GetRCMDCfgReq_SOURCE_name, int32(x))
}
func (GetRCMDCfgReq_SOURCE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{2, 0}
}

type GetRecommendationListReq_GetListMode int32

const (
	GetRecommendationListReq_DEFAULT  GetRecommendationListReq_GetListMode = 0
	GetRecommendationListReq_NEXTPAGE GetRecommendationListReq_GetListMode = 1
	GetRecommendationListReq_REFRESH  GetRecommendationListReq_GetListMode = 2
	GetRecommendationListReq_INSERT   GetRecommendationListReq_GetListMode = 3
)

var GetRecommendationListReq_GetListMode_name = map[int32]string{
	0: "DEFAULT",
	1: "NEXTPAGE",
	2: "REFRESH",
	3: "INSERT",
}
var GetRecommendationListReq_GetListMode_value = map[string]int32{
	"DEFAULT":  0,
	"NEXTPAGE": 1,
	"REFRESH":  2,
	"INSERT":   3,
}

func (x GetRecommendationListReq_GetListMode) String() string {
	return proto.EnumName(GetRecommendationListReq_GetListMode_name, int32(x))
}
func (GetRecommendationListReq_GetListMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{8, 0}
}

type GetRecommendationListReq_Environment int32

const (
	GetRecommendationListReq_production GetRecommendationListReq_Environment = 0
	GetRecommendationListReq_staging    GetRecommendationListReq_Environment = 1
	GetRecommendationListReq_test       GetRecommendationListReq_Environment = 2
)

var GetRecommendationListReq_Environment_name = map[int32]string{
	0: "production",
	1: "staging",
	2: "test",
}
var GetRecommendationListReq_Environment_value = map[string]int32{
	"production": 0,
	"staging":    1,
	"test":       2,
}

func (x GetRecommendationListReq_Environment) String() string {
	return proto.EnumName(GetRecommendationListReq_Environment_name, int32(x))
}
func (GetRecommendationListReq_Environment) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{8, 1}
}

type GetRecommendationListReq_Scene int32

const (
	GetRecommendationListReq_Default GetRecommendationListReq_Scene = 0
	GetRecommendationListReq_GangUp  GetRecommendationListReq_Scene = 1
	GetRecommendationListReq_Theme   GetRecommendationListReq_Scene = 2
	GetRecommendationListReq_Music   GetRecommendationListReq_Scene = 3
	// 100以上服务端业务使用
	GetRecommendationListReq_BussGangUp GetRecommendationListReq_Scene = 100
	GetRecommendationListReq_BussMusic  GetRecommendationListReq_Scene = 101
)

var GetRecommendationListReq_Scene_name = map[int32]string{
	0:   "Default",
	1:   "GangUp",
	2:   "Theme",
	3:   "Music",
	100: "BussGangUp",
	101: "BussMusic",
}
var GetRecommendationListReq_Scene_value = map[string]int32{
	"Default":    0,
	"GangUp":     1,
	"Theme":      2,
	"Music":      3,
	"BussGangUp": 100,
	"BussMusic":  101,
}

func (x GetRecommendationListReq_Scene) String() string {
	return proto.EnumName(GetRecommendationListReq_Scene_name, int32(x))
}
func (GetRecommendationListReq_Scene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{8, 2}
}

type GetRecommendationListReq_FilterModel int32

const (
	GetRecommendationListReq_MtModel GetRecommendationListReq_FilterModel = 0
	GetRecommendationListReq_KhModel GetRecommendationListReq_FilterModel = 1
)

var GetRecommendationListReq_FilterModel_name = map[int32]string{
	0: "MtModel",
	1: "KhModel",
}
var GetRecommendationListReq_FilterModel_value = map[string]int32{
	"MtModel": 0,
	"KhModel": 1,
}

func (x GetRecommendationListReq_FilterModel) String() string {
	return proto.EnumName(GetRecommendationListReq_FilterModel_name, int32(x))
}
func (GetRecommendationListReq_FilterModel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{8, 3}
}

type GetHomeMixedListReq_GetListMode int32

const (
	GetHomeMixedListReq_DEFAULT  GetHomeMixedListReq_GetListMode = 0
	GetHomeMixedListReq_NEXTPAGE GetHomeMixedListReq_GetListMode = 1
	GetHomeMixedListReq_REFRESH  GetHomeMixedListReq_GetListMode = 2
	GetHomeMixedListReq_INSERT   GetHomeMixedListReq_GetListMode = 3
)

var GetHomeMixedListReq_GetListMode_name = map[int32]string{
	0: "DEFAULT",
	1: "NEXTPAGE",
	2: "REFRESH",
	3: "INSERT",
}
var GetHomeMixedListReq_GetListMode_value = map[string]int32{
	"DEFAULT":  0,
	"NEXTPAGE": 1,
	"REFRESH":  2,
	"INSERT":   3,
}

func (x GetHomeMixedListReq_GetListMode) String() string {
	return proto.EnumName(GetHomeMixedListReq_GetListMode_name, int32(x))
}
func (GetHomeMixedListReq_GetListMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{10, 0}
}

type GetHomeMixedListReq_Environment int32

const (
	GetHomeMixedListReq_production GetHomeMixedListReq_Environment = 0
	GetHomeMixedListReq_staging    GetHomeMixedListReq_Environment = 1
	GetHomeMixedListReq_test       GetHomeMixedListReq_Environment = 2
)

var GetHomeMixedListReq_Environment_name = map[int32]string{
	0: "production",
	1: "staging",
	2: "test",
}
var GetHomeMixedListReq_Environment_value = map[string]int32{
	"production": 0,
	"staging":    1,
	"test":       2,
}

func (x GetHomeMixedListReq_Environment) String() string {
	return proto.EnumName(GetHomeMixedListReq_Environment_name, int32(x))
}
func (GetHomeMixedListReq_Environment) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{10, 1}
}

type ChannelInfo_LocShowType int32

const (
	ChannelInfo_LocShowType_DEFAULT  ChannelInfo_LocShowType = 0
	ChannelInfo_LocShowType_PROVINCE ChannelInfo_LocShowType = 2
	ChannelInfo_LocShowType_CITY     ChannelInfo_LocShowType = 3
)

var ChannelInfo_LocShowType_name = map[int32]string{
	0: "LocShowType_DEFAULT",
	2: "LocShowType_PROVINCE",
	3: "LocShowType_CITY",
}
var ChannelInfo_LocShowType_value = map[string]int32{
	"LocShowType_DEFAULT":  0,
	"LocShowType_PROVINCE": 2,
	"LocShowType_CITY":     3,
}

func (x ChannelInfo_LocShowType) String() string {
	return proto.EnumName(ChannelInfo_LocShowType_name, int32(x))
}
func (ChannelInfo_LocShowType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{11, 0}
}

type GetRecommendationListResp_NotifyType int32

const (
	GetRecommendationListResp_DEFAULT     GetRecommendationListResp_NotifyType = 0
	GetRecommendationListResp_APPOINTMENT GetRecommendationListResp_NotifyType = 1
	GetRecommendationListResp_RefreshSucc GetRecommendationListResp_NotifyType = 2
)

var GetRecommendationListResp_NotifyType_name = map[int32]string{
	0: "DEFAULT",
	1: "APPOINTMENT",
	2: "RefreshSucc",
}
var GetRecommendationListResp_NotifyType_value = map[string]int32{
	"DEFAULT":     0,
	"APPOINTMENT": 1,
	"RefreshSucc": 2,
}

func (x GetRecommendationListResp_NotifyType) String() string {
	return proto.EnumName(GetRecommendationListResp_NotifyType_name, int32(x))
}
func (GetRecommendationListResp_NotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{12, 0}
}

type GetHomeMixedListResp_NotifyType int32

const (
	GetHomeMixedListResp_DEFAULT     GetHomeMixedListResp_NotifyType = 0
	GetHomeMixedListResp_APPOINTMENT GetHomeMixedListResp_NotifyType = 1
	GetHomeMixedListResp_RefreshSucc GetHomeMixedListResp_NotifyType = 2
)

var GetHomeMixedListResp_NotifyType_name = map[int32]string{
	0: "DEFAULT",
	1: "APPOINTMENT",
	2: "RefreshSucc",
}
var GetHomeMixedListResp_NotifyType_value = map[string]int32{
	"DEFAULT":     0,
	"APPOINTMENT": 1,
	"RefreshSucc": 2,
}

func (x GetHomeMixedListResp_NotifyType) String() string {
	return proto.EnumName(GetHomeMixedListResp_NotifyType_name, int32(x))
}
func (GetHomeMixedListResp_NotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{13, 0}
}

type BaseRCMDResp_PlanBStrategy int32

const (
	BaseRCMDResp_PlanBStrategy_INVALID BaseRCMDResp_PlanBStrategy = 0
	BaseRCMDResp_PlanBStrategy_TT      BaseRCMDResp_PlanBStrategy = 1
	BaseRCMDResp_PlanBStrategy_RANDOM  BaseRCMDResp_PlanBStrategy = 2
)

var BaseRCMDResp_PlanBStrategy_name = map[int32]string{
	0: "PlanBStrategy_INVALID",
	1: "PlanBStrategy_TT",
	2: "PlanBStrategy_RANDOM",
}
var BaseRCMDResp_PlanBStrategy_value = map[string]int32{
	"PlanBStrategy_INVALID": 0,
	"PlanBStrategy_TT":      1,
	"PlanBStrategy_RANDOM":  2,
}

func (x BaseRCMDResp_PlanBStrategy) String() string {
	return proto.EnumName(BaseRCMDResp_PlanBStrategy_name, int32(x))
}
func (BaseRCMDResp_PlanBStrategy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{14, 0}
}

type SetTopicChannelQualityReq_TopicChannelQuality int32

const (
	SetTopicChannelQualityReq_DEFAULT SetTopicChannelQualityReq_TopicChannelQuality = 0
	SetTopicChannelQualityReq_LOW     SetTopicChannelQualityReq_TopicChannelQuality = 1
)

var SetTopicChannelQualityReq_TopicChannelQuality_name = map[int32]string{
	0: "DEFAULT",
	1: "LOW",
}
var SetTopicChannelQualityReq_TopicChannelQuality_value = map[string]int32{
	"DEFAULT": 0,
	"LOW":     1,
}

func (x SetTopicChannelQualityReq_TopicChannelQuality) String() string {
	return proto.EnumName(SetTopicChannelQualityReq_TopicChannelQuality_name, int32(x))
}
func (SetTopicChannelQualityReq_TopicChannelQuality) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{15, 0}
}

type NegativeFeedEvent_UpdateType int32

const (
	NegativeFeedEvent_Invalid     NegativeFeedEvent_UpdateType = 0
	NegativeFeedEvent_User        NegativeFeedEvent_UpdateType = 1
	NegativeFeedEvent_Tab         NegativeFeedEvent_UpdateType = 2
	NegativeFeedEvent_ChannelCond NegativeFeedEvent_UpdateType = 3
	NegativeFeedEvent_ChannelName NegativeFeedEvent_UpdateType = 4
)

var NegativeFeedEvent_UpdateType_name = map[int32]string{
	0: "Invalid",
	1: "User",
	2: "Tab",
	3: "ChannelCond",
	4: "ChannelName",
}
var NegativeFeedEvent_UpdateType_value = map[string]int32{
	"Invalid":     0,
	"User":        1,
	"Tab":         2,
	"ChannelCond": 3,
	"ChannelName": 4,
}

func (x NegativeFeedEvent_UpdateType) String() string {
	return proto.EnumName(NegativeFeedEvent_UpdateType_name, int32(x))
}
func (NegativeFeedEvent_UpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{18, 0}
}

type ResetFilterReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResetFilterReq) Reset()         { *m = ResetFilterReq{} }
func (m *ResetFilterReq) String() string { return proto.CompactTextString(m) }
func (*ResetFilterReq) ProtoMessage()    {}
func (*ResetFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{0}
}
func (m *ResetFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetFilterReq.Unmarshal(m, b)
}
func (m *ResetFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetFilterReq.Marshal(b, m, deterministic)
}
func (dst *ResetFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetFilterReq.Merge(dst, src)
}
func (m *ResetFilterReq) XXX_Size() int {
	return xxx_messageInfo_ResetFilterReq.Size(m)
}
func (m *ResetFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_ResetFilterReq proto.InternalMessageInfo

func (m *ResetFilterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ResetFilterResp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResetFilterResp) Reset()         { *m = ResetFilterResp{} }
func (m *ResetFilterResp) String() string { return proto.CompactTextString(m) }
func (*ResetFilterResp) ProtoMessage()    {}
func (*ResetFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{1}
}
func (m *ResetFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetFilterResp.Unmarshal(m, b)
}
func (m *ResetFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetFilterResp.Marshal(b, m, deterministic)
}
func (dst *ResetFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetFilterResp.Merge(dst, src)
}
func (m *ResetFilterResp) XXX_Size() int {
	return xxx_messageInfo_ResetFilterResp.Size(m)
}
func (m *ResetFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_ResetFilterResp proto.InternalMessageInfo

func (m *ResetFilterResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ResetFilterResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type GetRCMDCfgReq struct {
	Source               GetRCMDCfgReq_SOURCE `protobuf:"varint,1,opt,name=source,proto3,enum=topic_channel.recommendation_gen.GetRCMDCfgReq_SOURCE" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetRCMDCfgReq) Reset()         { *m = GetRCMDCfgReq{} }
func (m *GetRCMDCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetRCMDCfgReq) ProtoMessage()    {}
func (*GetRCMDCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{2}
}
func (m *GetRCMDCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRCMDCfgReq.Unmarshal(m, b)
}
func (m *GetRCMDCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRCMDCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetRCMDCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRCMDCfgReq.Merge(dst, src)
}
func (m *GetRCMDCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetRCMDCfgReq.Size(m)
}
func (m *GetRCMDCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRCMDCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRCMDCfgReq proto.InternalMessageInfo

func (m *GetRCMDCfgReq) GetSource() GetRCMDCfgReq_SOURCE {
	if m != nil {
		return m.Source
	}
	return GetRCMDCfgReq_SOURCE_DEFAULT
}

type GetRCMDCfgResp struct {
	SupervisorWhiteList  []uint32 `protobuf:"varint,1,rep,packed,name=supervisor_white_list,json=supervisorWhiteList,proto3" json:"supervisor_white_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRCMDCfgResp) Reset()         { *m = GetRCMDCfgResp{} }
func (m *GetRCMDCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetRCMDCfgResp) ProtoMessage()    {}
func (*GetRCMDCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{3}
}
func (m *GetRCMDCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRCMDCfgResp.Unmarshal(m, b)
}
func (m *GetRCMDCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRCMDCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetRCMDCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRCMDCfgResp.Merge(dst, src)
}
func (m *GetRCMDCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetRCMDCfgResp.Size(m)
}
func (m *GetRCMDCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRCMDCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRCMDCfgResp proto.InternalMessageInfo

func (m *GetRCMDCfgResp) GetSupervisorWhiteList() []uint32 {
	if m != nil {
		return m.SupervisorWhiteList
	}
	return nil
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{4}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

type RecommendationReqReserve struct {
	ImListExpGroup       IMChannelListABGroup `protobuf:"varint,1,opt,name=im_list_exp_group,json=imListExpGroup,proto3,enum=topic_channel.recommendation_gen.IMChannelListABGroup" json:"im_list_exp_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *RecommendationReqReserve) Reset()         { *m = RecommendationReqReserve{} }
func (m *RecommendationReqReserve) String() string { return proto.CompactTextString(m) }
func (*RecommendationReqReserve) ProtoMessage()    {}
func (*RecommendationReqReserve) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{5}
}
func (m *RecommendationReqReserve) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendationReqReserve.Unmarshal(m, b)
}
func (m *RecommendationReqReserve) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendationReqReserve.Marshal(b, m, deterministic)
}
func (dst *RecommendationReqReserve) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendationReqReserve.Merge(dst, src)
}
func (m *RecommendationReqReserve) XXX_Size() int {
	return xxx_messageInfo_RecommendationReqReserve.Size(m)
}
func (m *RecommendationReqReserve) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendationReqReserve.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendationReqReserve proto.InternalMessageInfo

func (m *RecommendationReqReserve) GetImListExpGroup() IMChannelListABGroup {
	if m != nil {
		return m.ImListExpGroup
	}
	return IMChannelListABGroup_DEFAULT
}

type PrefGameLabel struct {
	Id                   uint32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Val                  string            `protobuf:"bytes,2,opt,name=val,proto3" json:"val,omitempty"`
	Type                 PrefGameLabelType `protobuf:"varint,3,opt,name=type,proto3,enum=topic_channel.recommendation_gen.PrefGameLabelType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PrefGameLabel) Reset()         { *m = PrefGameLabel{} }
func (m *PrefGameLabel) String() string { return proto.CompactTextString(m) }
func (*PrefGameLabel) ProtoMessage()    {}
func (*PrefGameLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{6}
}
func (m *PrefGameLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrefGameLabel.Unmarshal(m, b)
}
func (m *PrefGameLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrefGameLabel.Marshal(b, m, deterministic)
}
func (dst *PrefGameLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrefGameLabel.Merge(dst, src)
}
func (m *PrefGameLabel) XXX_Size() int {
	return xxx_messageInfo_PrefGameLabel.Size(m)
}
func (m *PrefGameLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_PrefGameLabel.DiscardUnknown(m)
}

var xxx_messageInfo_PrefGameLabel proto.InternalMessageInfo

func (m *PrefGameLabel) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PrefGameLabel) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *PrefGameLabel) GetType() PrefGameLabelType {
	if m != nil {
		return m.Type
	}
	return PrefGameLabelType_SYSTEM
}

type PrefGame struct {
	TabId                uint32           `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Labels               []*PrefGameLabel `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PrefGame) Reset()         { *m = PrefGame{} }
func (m *PrefGame) String() string { return proto.CompactTextString(m) }
func (*PrefGame) ProtoMessage()    {}
func (*PrefGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{7}
}
func (m *PrefGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrefGame.Unmarshal(m, b)
}
func (m *PrefGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrefGame.Marshal(b, m, deterministic)
}
func (dst *PrefGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrefGame.Merge(dst, src)
}
func (m *PrefGame) XXX_Size() int {
	return xxx_messageInfo_PrefGame.Size(m)
}
func (m *PrefGame) XXX_DiscardUnknown() {
	xxx_messageInfo_PrefGame.DiscardUnknown(m)
}

var xxx_messageInfo_PrefGame proto.InternalMessageInfo

func (m *PrefGame) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *PrefGame) GetLabels() []*PrefGameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

type GetRecommendationListReq struct {
	Uid                uint32                               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit              uint32                               `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TabId              uint32                               `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions       []*BlockOption                       `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	GetMode            GetRecommendationListReq_GetListMode `protobuf:"varint,5,opt,name=get_mode,json=getMode,proto3,enum=topic_channel.recommendation_gen.GetRecommendationListReq_GetListMode" json:"get_mode,omitempty"`
	ChannelEnterSource uint32                               `protobuf:"varint,6,opt,name=channel_enter_source,json=channelEnterSource,proto3" json:"channel_enter_source,omitempty"`
	ClientType         uint32                               `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion      uint32                               `protobuf:"varint,8,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	MarketId           uint32                               `protobuf:"varint,9,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	// Deprecated
	Env              GetRecommendationListReq_Environment `protobuf:"varint,10,opt,name=env,proto3,enum=topic_channel.recommendation_gen.GetRecommendationListReq_Environment" json:"env,omitempty"`
	ChannelPackageId string                               `protobuf:"bytes,11,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	// Deprecated
	TraceId string `protobuf:"bytes,12,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	// Deprecated
	ModelVersion string `protobuf:"bytes,13,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	// Deprecated
	DebugFlag uint32 `protobuf:"varint,14,opt,name=debug_flag,json=debugFlag,proto3" json:"debug_flag,omitempty"`
	// Deprecated
	Reserve             string                 `protobuf:"bytes,15,opt,name=reserve,proto3" json:"reserve,omitempty"`
	BaseReq             *common.RcmdBaseReq    `protobuf:"bytes,16,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Sex                 uint32                 `protobuf:"varint,17,opt,name=sex,proto3" json:"sex,omitempty"`
	BrowseList          *common.RcmdBrowseInfo `protobuf:"bytes,18,opt,name=browse_list,json=browseList,proto3" json:"browse_list,omitempty"`
	RegulatoryLevel     REGULATORY_LEVEL       `protobuf:"varint,19,opt,name=regulatory_level,json=regulatoryLevel,proto3,enum=topic_channel.recommendation_gen.REGULATORY_LEVEL" json:"regulatory_level,omitempty"`
	IsMinorityParentTab bool                   `protobuf:"varint,20,opt,name=is_minority_parent_tab,json=isMinorityParentTab,proto3" json:"is_minority_parent_tab,omitempty"`
	CategoryIds         []uint32               `protobuf:"varint,22,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	TabIds              []uint32               `protobuf:"varint,23,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	// Deprecated
	IsQmFramework bool `protobuf:"varint,24,opt,name=is_qm_framework,json=isQmFramework,proto3" json:"is_qm_framework,omitempty"`
	// Deprecated: 功能已下线
	PrefGames      []*PrefGame                                `protobuf:"bytes,25,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	Labels         []*rcmd_channel_label.GameLabel            `protobuf:"bytes,26,rep,name=labels,proto3" json:"labels,omitempty"`
	BusinessFilter []*GetRecommendationListReq_BusinessFilter `protobuf:"bytes,27,rep,name=business_filter,json=businessFilter,proto3" json:"business_filter,omitempty"`
	GameSettings   []*recommendation_common.TcGameSetting     `protobuf:"bytes,28,rep,name=game_settings,json=gameSettings,proto3" json:"game_settings,omitempty"`
	// Deprecated
	IsFallbackReq  bool     `protobuf:"varint,29,opt,name=is_fallback_req,json=isFallbackReq,proto3" json:"is_fallback_req,omitempty"`
	InterestLabels []string `protobuf:"bytes,30,rep,name=interest_labels,json=interestLabels,proto3" json:"interest_labels,omitempty"`
	DeliveryType   string   `protobuf:"bytes,31,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	// Deprecated
	IsEnableGameLabel      bool                                    `protobuf:"varint,32,opt,name=is_enable_game_label,json=isEnableGameLabel,proto3" json:"is_enable_game_label,omitempty"`
	ShieldFilterWords      []string                                `protobuf:"bytes,33,rep,name=shield_filter_words,json=shieldFilterWords,proto3" json:"shield_filter_words,omitempty"`
	ClassifyLabels         []*rcmd_channel_label.ClassifyLabelList `protobuf:"bytes,34,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	Scene                  uint32                                  `protobuf:"varint,35,opt,name=scene,proto3" json:"scene,omitempty"`
	FilterId               string                                  `protobuf:"bytes,36,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	ChannelTopics          []*ChannelTopic                         `protobuf:"bytes,37,rep,name=channel_topics,json=channelTopics,proto3" json:"channel_topics,omitempty"`
	IsUserLocationAuthOpen bool                                    `protobuf:"varint,38,opt,name=is_user_location_auth_open,json=isUserLocationAuthOpen,proto3" json:"is_user_location_auth_open,omitempty"`
	FilterModel            uint32                                  `protobuf:"varint,39,opt,name=filter_model,json=filterModel,proto3" json:"filter_model,omitempty"`
	UserGpsLocation        *common.LocationInfo                    `protobuf:"bytes,40,opt,name=user_gps_location,json=userGpsLocation,proto3" json:"user_gps_location,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                                `json:"-"`
	XXX_unrecognized       []byte                                  `json:"-"`
	XXX_sizecache          int32                                   `json:"-"`
}

func (m *GetRecommendationListReq) Reset()         { *m = GetRecommendationListReq{} }
func (m *GetRecommendationListReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationListReq) ProtoMessage()    {}
func (*GetRecommendationListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{8}
}
func (m *GetRecommendationListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationListReq.Unmarshal(m, b)
}
func (m *GetRecommendationListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationListReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationListReq.Merge(dst, src)
}
func (m *GetRecommendationListReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationListReq.Size(m)
}
func (m *GetRecommendationListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationListReq proto.InternalMessageInfo

func (m *GetRecommendationListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendationListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetRecommendationListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetRecommendationListReq) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *GetRecommendationListReq) GetGetMode() GetRecommendationListReq_GetListMode {
	if m != nil {
		return m.GetMode
	}
	return GetRecommendationListReq_DEFAULT
}

func (m *GetRecommendationListReq) GetChannelEnterSource() uint32 {
	if m != nil {
		return m.ChannelEnterSource
	}
	return 0
}

func (m *GetRecommendationListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *GetRecommendationListReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetRecommendationListReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetRecommendationListReq) GetEnv() GetRecommendationListReq_Environment {
	if m != nil {
		return m.Env
	}
	return GetRecommendationListReq_production
}

func (m *GetRecommendationListReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *GetRecommendationListReq) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *GetRecommendationListReq) GetModelVersion() string {
	if m != nil {
		return m.ModelVersion
	}
	return ""
}

func (m *GetRecommendationListReq) GetDebugFlag() uint32 {
	if m != nil {
		return m.DebugFlag
	}
	return 0
}

func (m *GetRecommendationListReq) GetReserve() string {
	if m != nil {
		return m.Reserve
	}
	return ""
}

func (m *GetRecommendationListReq) GetBaseReq() *common.RcmdBaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRecommendationListReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GetRecommendationListReq) GetBrowseList() *common.RcmdBrowseInfo {
	if m != nil {
		return m.BrowseList
	}
	return nil
}

func (m *GetRecommendationListReq) GetRegulatoryLevel() REGULATORY_LEVEL {
	if m != nil {
		return m.RegulatoryLevel
	}
	return REGULATORY_LEVEL_FREE
}

func (m *GetRecommendationListReq) GetIsMinorityParentTab() bool {
	if m != nil {
		return m.IsMinorityParentTab
	}
	return false
}

func (m *GetRecommendationListReq) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

func (m *GetRecommendationListReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *GetRecommendationListReq) GetIsQmFramework() bool {
	if m != nil {
		return m.IsQmFramework
	}
	return false
}

func (m *GetRecommendationListReq) GetPrefGames() []*PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *GetRecommendationListReq) GetLabels() []*rcmd_channel_label.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *GetRecommendationListReq) GetBusinessFilter() []*GetRecommendationListReq_BusinessFilter {
	if m != nil {
		return m.BusinessFilter
	}
	return nil
}

func (m *GetRecommendationListReq) GetGameSettings() []*recommendation_common.TcGameSetting {
	if m != nil {
		return m.GameSettings
	}
	return nil
}

func (m *GetRecommendationListReq) GetIsFallbackReq() bool {
	if m != nil {
		return m.IsFallbackReq
	}
	return false
}

func (m *GetRecommendationListReq) GetInterestLabels() []string {
	if m != nil {
		return m.InterestLabels
	}
	return nil
}

func (m *GetRecommendationListReq) GetDeliveryType() string {
	if m != nil {
		return m.DeliveryType
	}
	return ""
}

func (m *GetRecommendationListReq) GetIsEnableGameLabel() bool {
	if m != nil {
		return m.IsEnableGameLabel
	}
	return false
}

func (m *GetRecommendationListReq) GetShieldFilterWords() []string {
	if m != nil {
		return m.ShieldFilterWords
	}
	return nil
}

func (m *GetRecommendationListReq) GetClassifyLabels() []*rcmd_channel_label.ClassifyLabelList {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

func (m *GetRecommendationListReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *GetRecommendationListReq) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *GetRecommendationListReq) GetChannelTopics() []*ChannelTopic {
	if m != nil {
		return m.ChannelTopics
	}
	return nil
}

func (m *GetRecommendationListReq) GetIsUserLocationAuthOpen() bool {
	if m != nil {
		return m.IsUserLocationAuthOpen
	}
	return false
}

func (m *GetRecommendationListReq) GetFilterModel() uint32 {
	if m != nil {
		return m.FilterModel
	}
	return 0
}

func (m *GetRecommendationListReq) GetUserGpsLocation() *common.LocationInfo {
	if m != nil {
		return m.UserGpsLocation
	}
	return nil
}

// 业务筛选字段
type GetRecommendationListReq_BusinessFilter struct {
	// 业务block
	BlockType BusinessBlockEnum `protobuf:"varint,1,opt,name=block_type,json=blockType,proto3,enum=topic_channel.recommendation_gen.BusinessBlockEnum" json:"block_type,omitempty"`
	// 业务elem
	ElemType             []BusinessElemEnum `protobuf:"varint,2,rep,packed,name=elem_type,json=elemType,proto3,enum=topic_channel.recommendation_gen.BusinessElemEnum" json:"elem_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetRecommendationListReq_BusinessFilter) Reset() {
	*m = GetRecommendationListReq_BusinessFilter{}
}
func (m *GetRecommendationListReq_BusinessFilter) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationListReq_BusinessFilter) ProtoMessage()    {}
func (*GetRecommendationListReq_BusinessFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{8, 0}
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Unmarshal(m, b)
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationListReq_BusinessFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Merge(dst, src)
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Size(m)
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationListReq_BusinessFilter.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationListReq_BusinessFilter proto.InternalMessageInfo

func (m *GetRecommendationListReq_BusinessFilter) GetBlockType() BusinessBlockEnum {
	if m != nil {
		return m.BlockType
	}
	return BusinessBlockEnum_RoomMode
}

func (m *GetRecommendationListReq_BusinessFilter) GetElemType() []BusinessElemEnum {
	if m != nil {
		return m.ElemType
	}
	return nil
}

type ChannelTopic struct {
	TabId                uint32         `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*BlockOption `protobuf:"bytes,2,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ChannelTopic) Reset()         { *m = ChannelTopic{} }
func (m *ChannelTopic) String() string { return proto.CompactTextString(m) }
func (*ChannelTopic) ProtoMessage()    {}
func (*ChannelTopic) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{9}
}
func (m *ChannelTopic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTopic.Unmarshal(m, b)
}
func (m *ChannelTopic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTopic.Marshal(b, m, deterministic)
}
func (dst *ChannelTopic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTopic.Merge(dst, src)
}
func (m *ChannelTopic) XXX_Size() int {
	return xxx_messageInfo_ChannelTopic.Size(m)
}
func (m *ChannelTopic) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTopic.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTopic proto.InternalMessageInfo

func (m *ChannelTopic) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ChannelTopic) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

type GetHomeMixedListReq struct {
	Uid                  uint32                                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32                                 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TabId                uint32                                 `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*BlockOption                         `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	GetMode              GetHomeMixedListReq_GetListMode        `protobuf:"varint,5,opt,name=get_mode,json=getMode,proto3,enum=topic_channel.recommendation_gen.GetHomeMixedListReq_GetListMode" json:"get_mode,omitempty"`
	ChannelEnterSource   uint32                                 `protobuf:"varint,6,opt,name=channel_enter_source,json=channelEnterSource,proto3" json:"channel_enter_source,omitempty"`
	ClientType           uint32                                 `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion        uint32                                 `protobuf:"varint,8,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	MarketId             uint32                                 `protobuf:"varint,9,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Env                  GetHomeMixedListReq_Environment        `protobuf:"varint,10,opt,name=env,proto3,enum=topic_channel.recommendation_gen.GetHomeMixedListReq_Environment" json:"env,omitempty"`
	ChannelPackageId     string                                 `protobuf:"bytes,11,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	TraceId              string                                 `protobuf:"bytes,12,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	ModelVersion         string                                 `protobuf:"bytes,13,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	DebugFlag            uint32                                 `protobuf:"varint,14,opt,name=debug_flag,json=debugFlag,proto3" json:"debug_flag,omitempty"`
	Reserve              string                                 `protobuf:"bytes,15,opt,name=reserve,proto3" json:"reserve,omitempty"`
	BaseReq              *common.RcmdBaseReq                    `protobuf:"bytes,16,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Sex                  uint32                                 `protobuf:"varint,17,opt,name=sex,proto3" json:"sex,omitempty"`
	BrowseList           *common.RcmdBrowseInfo                 `protobuf:"bytes,18,opt,name=browse_list,json=browseList,proto3" json:"browse_list,omitempty"`
	RegulatoryLevel      REGULATORY_LEVEL                       `protobuf:"varint,19,opt,name=regulatory_level,json=regulatoryLevel,proto3,enum=topic_channel.recommendation_gen.REGULATORY_LEVEL" json:"regulatory_level,omitempty"`
	IsMinorityParentTab  bool                                   `protobuf:"varint,20,opt,name=is_minority_parent_tab,json=isMinorityParentTab,proto3" json:"is_minority_parent_tab,omitempty"`
	CategoryIds          []uint32                               `protobuf:"varint,22,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	TabIds               []uint32                               `protobuf:"varint,23,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	IsQmFramework        bool                                   `protobuf:"varint,24,opt,name=is_qm_framework,json=isQmFramework,proto3" json:"is_qm_framework,omitempty"`
	PrefGames            []*PrefGame                            `protobuf:"bytes,25,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	Labels               []*rcmd_channel_label.GameLabel        `protobuf:"bytes,26,rep,name=labels,proto3" json:"labels,omitempty"`
	BusinessFilter       []*GetHomeMixedListReq_BusinessFilter  `protobuf:"bytes,27,rep,name=business_filter,json=businessFilter,proto3" json:"business_filter,omitempty"`
	GameSettings         []*recommendation_common.TcGameSetting `protobuf:"bytes,28,rep,name=game_settings,json=gameSettings,proto3" json:"game_settings,omitempty"`
	IsFallbackReq        bool                                   `protobuf:"varint,29,opt,name=is_fallback_req,json=isFallbackReq,proto3" json:"is_fallback_req,omitempty"`
	InterestLabels       []string                               `protobuf:"bytes,30,rep,name=interest_labels,json=interestLabels,proto3" json:"interest_labels,omitempty"`
	DeliveryType         string                                 `protobuf:"bytes,31,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetHomeMixedListReq) Reset()         { *m = GetHomeMixedListReq{} }
func (m *GetHomeMixedListReq) String() string { return proto.CompactTextString(m) }
func (*GetHomeMixedListReq) ProtoMessage()    {}
func (*GetHomeMixedListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{10}
}
func (m *GetHomeMixedListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomeMixedListReq.Unmarshal(m, b)
}
func (m *GetHomeMixedListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomeMixedListReq.Marshal(b, m, deterministic)
}
func (dst *GetHomeMixedListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomeMixedListReq.Merge(dst, src)
}
func (m *GetHomeMixedListReq) XXX_Size() int {
	return xxx_messageInfo_GetHomeMixedListReq.Size(m)
}
func (m *GetHomeMixedListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomeMixedListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomeMixedListReq proto.InternalMessageInfo

func (m *GetHomeMixedListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetHomeMixedListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetHomeMixedListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetHomeMixedListReq) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *GetHomeMixedListReq) GetGetMode() GetHomeMixedListReq_GetListMode {
	if m != nil {
		return m.GetMode
	}
	return GetHomeMixedListReq_DEFAULT
}

func (m *GetHomeMixedListReq) GetChannelEnterSource() uint32 {
	if m != nil {
		return m.ChannelEnterSource
	}
	return 0
}

func (m *GetHomeMixedListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *GetHomeMixedListReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetHomeMixedListReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetHomeMixedListReq) GetEnv() GetHomeMixedListReq_Environment {
	if m != nil {
		return m.Env
	}
	return GetHomeMixedListReq_production
}

func (m *GetHomeMixedListReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *GetHomeMixedListReq) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *GetHomeMixedListReq) GetModelVersion() string {
	if m != nil {
		return m.ModelVersion
	}
	return ""
}

func (m *GetHomeMixedListReq) GetDebugFlag() uint32 {
	if m != nil {
		return m.DebugFlag
	}
	return 0
}

func (m *GetHomeMixedListReq) GetReserve() string {
	if m != nil {
		return m.Reserve
	}
	return ""
}

func (m *GetHomeMixedListReq) GetBaseReq() *common.RcmdBaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetHomeMixedListReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GetHomeMixedListReq) GetBrowseList() *common.RcmdBrowseInfo {
	if m != nil {
		return m.BrowseList
	}
	return nil
}

func (m *GetHomeMixedListReq) GetRegulatoryLevel() REGULATORY_LEVEL {
	if m != nil {
		return m.RegulatoryLevel
	}
	return REGULATORY_LEVEL_FREE
}

func (m *GetHomeMixedListReq) GetIsMinorityParentTab() bool {
	if m != nil {
		return m.IsMinorityParentTab
	}
	return false
}

func (m *GetHomeMixedListReq) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

func (m *GetHomeMixedListReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *GetHomeMixedListReq) GetIsQmFramework() bool {
	if m != nil {
		return m.IsQmFramework
	}
	return false
}

func (m *GetHomeMixedListReq) GetPrefGames() []*PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *GetHomeMixedListReq) GetLabels() []*rcmd_channel_label.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *GetHomeMixedListReq) GetBusinessFilter() []*GetHomeMixedListReq_BusinessFilter {
	if m != nil {
		return m.BusinessFilter
	}
	return nil
}

func (m *GetHomeMixedListReq) GetGameSettings() []*recommendation_common.TcGameSetting {
	if m != nil {
		return m.GameSettings
	}
	return nil
}

func (m *GetHomeMixedListReq) GetIsFallbackReq() bool {
	if m != nil {
		return m.IsFallbackReq
	}
	return false
}

func (m *GetHomeMixedListReq) GetInterestLabels() []string {
	if m != nil {
		return m.InterestLabels
	}
	return nil
}

func (m *GetHomeMixedListReq) GetDeliveryType() string {
	if m != nil {
		return m.DeliveryType
	}
	return ""
}

// 业务筛选字段
type GetHomeMixedListReq_BusinessFilter struct {
	// 业务block
	BlockType BusinessBlockEnum `protobuf:"varint,1,opt,name=block_type,json=blockType,proto3,enum=topic_channel.recommendation_gen.BusinessBlockEnum" json:"block_type,omitempty"`
	// 业务elem
	ElemType             []BusinessElemEnum `protobuf:"varint,2,rep,packed,name=elem_type,json=elemType,proto3,enum=topic_channel.recommendation_gen.BusinessElemEnum" json:"elem_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetHomeMixedListReq_BusinessFilter) Reset()         { *m = GetHomeMixedListReq_BusinessFilter{} }
func (m *GetHomeMixedListReq_BusinessFilter) String() string { return proto.CompactTextString(m) }
func (*GetHomeMixedListReq_BusinessFilter) ProtoMessage()    {}
func (*GetHomeMixedListReq_BusinessFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{10, 0}
}
func (m *GetHomeMixedListReq_BusinessFilter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomeMixedListReq_BusinessFilter.Unmarshal(m, b)
}
func (m *GetHomeMixedListReq_BusinessFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomeMixedListReq_BusinessFilter.Marshal(b, m, deterministic)
}
func (dst *GetHomeMixedListReq_BusinessFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomeMixedListReq_BusinessFilter.Merge(dst, src)
}
func (m *GetHomeMixedListReq_BusinessFilter) XXX_Size() int {
	return xxx_messageInfo_GetHomeMixedListReq_BusinessFilter.Size(m)
}
func (m *GetHomeMixedListReq_BusinessFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomeMixedListReq_BusinessFilter.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomeMixedListReq_BusinessFilter proto.InternalMessageInfo

func (m *GetHomeMixedListReq_BusinessFilter) GetBlockType() BusinessBlockEnum {
	if m != nil {
		return m.BlockType
	}
	return BusinessBlockEnum_RoomMode
}

func (m *GetHomeMixedListReq_BusinessFilter) GetElemType() []BusinessElemEnum {
	if m != nil {
		return m.ElemType
	}
	return nil
}

type ChannelInfo struct {
	TagId       uint32                  `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	RecallFlag  uint32                  `protobuf:"varint,2,opt,name=recall_flag,json=recallFlag,proto3" json:"recall_flag,omitempty"`
	Loc         *common.LocationInfo    `protobuf:"bytes,3,opt,name=loc,proto3" json:"loc,omitempty"`
	RcmdLabels  []RCMDLabel             `protobuf:"varint,4,rep,packed,name=rcmd_labels,json=rcmdLabels,proto3,enum=topic_channel.recommendation_gen.RCMDLabel" json:"rcmd_labels,omitempty"`
	LocShowType ChannelInfo_LocShowType `protobuf:"varint,5,opt,name=loc_show_type,json=locShowType,proto3,enum=topic_channel.recommendation_gen.ChannelInfo_LocShowType" json:"loc_show_type,omitempty"`
	// Deprecated: 萌新承接房逻辑已下线
	IsNewUserUndertake   bool     `protobuf:"varint,6,opt,name=is_new_user_undertake,json=isNewUserUndertake,proto3" json:"is_new_user_undertake,omitempty"`
	FollowUidList        []uint32 `protobuf:"varint,7,rep,packed,name=follow_uid_list,json=followUidList,proto3" json:"follow_uid_list,omitempty"`
	PlayUidList          []uint32 `protobuf:"varint,8,rep,packed,name=play_uid_list,json=playUidList,proto3" json:"play_uid_list,omitempty"`
	FollowUidListV2      []uint32 `protobuf:"varint,9,rep,packed,name=follow_uid_list_v2,json=followUidListV2,proto3" json:"follow_uid_list_v2,omitempty"`
	IsEverEnter          bool     `protobuf:"varint,10,opt,name=is_ever_enter,json=isEverEnter,proto3" json:"is_ever_enter,omitempty"`
	DeliveryType         string   `protobuf:"bytes,11,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	DisplayContent       string   `protobuf:"bytes,12,opt,name=display_content,json=displayContent,proto3" json:"display_content,omitempty"`
	DisplayUidList       []uint32 `protobuf:"varint,13,rep,packed,name=display_uid_list,json=displayUidList,proto3" json:"display_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelInfo) Reset()         { *m = ChannelInfo{} }
func (m *ChannelInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelInfo) ProtoMessage()    {}
func (*ChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{11}
}
func (m *ChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInfo.Unmarshal(m, b)
}
func (m *ChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInfo.Merge(dst, src)
}
func (m *ChannelInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelInfo.Size(m)
}
func (m *ChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInfo proto.InternalMessageInfo

func (m *ChannelInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ChannelInfo) GetRecallFlag() uint32 {
	if m != nil {
		return m.RecallFlag
	}
	return 0
}

func (m *ChannelInfo) GetLoc() *common.LocationInfo {
	if m != nil {
		return m.Loc
	}
	return nil
}

func (m *ChannelInfo) GetRcmdLabels() []RCMDLabel {
	if m != nil {
		return m.RcmdLabels
	}
	return nil
}

func (m *ChannelInfo) GetLocShowType() ChannelInfo_LocShowType {
	if m != nil {
		return m.LocShowType
	}
	return ChannelInfo_LocShowType_DEFAULT
}

func (m *ChannelInfo) GetIsNewUserUndertake() bool {
	if m != nil {
		return m.IsNewUserUndertake
	}
	return false
}

func (m *ChannelInfo) GetFollowUidList() []uint32 {
	if m != nil {
		return m.FollowUidList
	}
	return nil
}

func (m *ChannelInfo) GetPlayUidList() []uint32 {
	if m != nil {
		return m.PlayUidList
	}
	return nil
}

func (m *ChannelInfo) GetFollowUidListV2() []uint32 {
	if m != nil {
		return m.FollowUidListV2
	}
	return nil
}

func (m *ChannelInfo) GetIsEverEnter() bool {
	if m != nil {
		return m.IsEverEnter
	}
	return false
}

func (m *ChannelInfo) GetDeliveryType() string {
	if m != nil {
		return m.DeliveryType
	}
	return ""
}

func (m *ChannelInfo) GetDisplayContent() string {
	if m != nil {
		return m.DisplayContent
	}
	return ""
}

func (m *ChannelInfo) GetDisplayUidList() []uint32 {
	if m != nil {
		return m.DisplayUidList
	}
	return nil
}

type GetRecommendationListResp struct {
	ChannelId            []uint32                               `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BottomReached        bool                                   `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	ChannelInfoMap       map[uint32]*ChannelInfo                `protobuf:"bytes,3,rep,name=channel_info_map,json=channelInfoMap,proto3" json:"channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TraceId              string                                 `protobuf:"bytes,4,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	DebugInfoMap         map[string]string                      `protobuf:"bytes,5,rep,name=debug_info_map,json=debugInfoMap,proto3" json:"debug_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SelfLoc              *common.LocationInfo                   `protobuf:"bytes,6,opt,name=self_loc,json=selfLoc,proto3" json:"self_loc,omitempty"`
	PrefGames            []*PrefGame                            `protobuf:"bytes,7,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	InsertPos            uint32                                 `protobuf:"varint,8,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	NotifyList           []GetRecommendationListResp_NotifyType `protobuf:"varint,9,rep,packed,name=notify_list,json=notifyList,proto3,enum=topic_channel.recommendation_gen.GetRecommendationListResp_NotifyType" json:"notify_list,omitempty"`
	Footprint            string                                 `protobuf:"bytes,10,opt,name=footprint,proto3" json:"footprint,omitempty"`
	BaseResp             *BaseRCMDResp                          `protobuf:"bytes,11,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CommonChannelInfoMap map[uint32]*common.ChannelInfo         `protobuf:"bytes,12,rep,name=common_channel_info_map,json=commonChannelInfoMap,proto3" json:"common_channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Labels               []*rcmd_channel_label.GameLabel        `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetRecommendationListResp) Reset()         { *m = GetRecommendationListResp{} }
func (m *GetRecommendationListResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationListResp) ProtoMessage()    {}
func (*GetRecommendationListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{12}
}
func (m *GetRecommendationListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationListResp.Unmarshal(m, b)
}
func (m *GetRecommendationListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationListResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationListResp.Merge(dst, src)
}
func (m *GetRecommendationListResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationListResp.Size(m)
}
func (m *GetRecommendationListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationListResp proto.InternalMessageInfo

func (m *GetRecommendationListResp) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *GetRecommendationListResp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

func (m *GetRecommendationListResp) GetChannelInfoMap() map[uint32]*ChannelInfo {
	if m != nil {
		return m.ChannelInfoMap
	}
	return nil
}

func (m *GetRecommendationListResp) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *GetRecommendationListResp) GetDebugInfoMap() map[string]string {
	if m != nil {
		return m.DebugInfoMap
	}
	return nil
}

func (m *GetRecommendationListResp) GetSelfLoc() *common.LocationInfo {
	if m != nil {
		return m.SelfLoc
	}
	return nil
}

func (m *GetRecommendationListResp) GetPrefGames() []*PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *GetRecommendationListResp) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *GetRecommendationListResp) GetNotifyList() []GetRecommendationListResp_NotifyType {
	if m != nil {
		return m.NotifyList
	}
	return nil
}

func (m *GetRecommendationListResp) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

func (m *GetRecommendationListResp) GetBaseResp() *BaseRCMDResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecommendationListResp) GetCommonChannelInfoMap() map[uint32]*common.ChannelInfo {
	if m != nil {
		return m.CommonChannelInfoMap
	}
	return nil
}

func (m *GetRecommendationListResp) GetLabels() []*rcmd_channel_label.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

type GetHomeMixedListResp struct {
	ChannelId            []uint32                          `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BottomReached        bool                              `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	ChannelInfoMap       map[uint32]*ChannelInfo           `protobuf:"bytes,3,rep,name=channel_info_map,json=channelInfoMap,proto3" json:"channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TraceId              string                            `protobuf:"bytes,4,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	DebugInfoMap         map[string]string                 `protobuf:"bytes,5,rep,name=debug_info_map,json=debugInfoMap,proto3" json:"debug_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SelfLoc              *common.LocationInfo              `protobuf:"bytes,6,opt,name=self_loc,json=selfLoc,proto3" json:"self_loc,omitempty"`
	PrefGames            []*PrefGame                       `protobuf:"bytes,7,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	InsertPos            uint32                            `protobuf:"varint,8,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	NotifyList           []GetHomeMixedListResp_NotifyType `protobuf:"varint,9,rep,packed,name=notify_list,json=notifyList,proto3,enum=topic_channel.recommendation_gen.GetHomeMixedListResp_NotifyType" json:"notify_list,omitempty"`
	Footprint            string                            `protobuf:"bytes,10,opt,name=footprint,proto3" json:"footprint,omitempty"`
	BaseResp             *BaseRCMDResp                     `protobuf:"bytes,11,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetHomeMixedListResp) Reset()         { *m = GetHomeMixedListResp{} }
func (m *GetHomeMixedListResp) String() string { return proto.CompactTextString(m) }
func (*GetHomeMixedListResp) ProtoMessage()    {}
func (*GetHomeMixedListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{13}
}
func (m *GetHomeMixedListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomeMixedListResp.Unmarshal(m, b)
}
func (m *GetHomeMixedListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomeMixedListResp.Marshal(b, m, deterministic)
}
func (dst *GetHomeMixedListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomeMixedListResp.Merge(dst, src)
}
func (m *GetHomeMixedListResp) XXX_Size() int {
	return xxx_messageInfo_GetHomeMixedListResp.Size(m)
}
func (m *GetHomeMixedListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomeMixedListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomeMixedListResp proto.InternalMessageInfo

func (m *GetHomeMixedListResp) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *GetHomeMixedListResp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

func (m *GetHomeMixedListResp) GetChannelInfoMap() map[uint32]*ChannelInfo {
	if m != nil {
		return m.ChannelInfoMap
	}
	return nil
}

func (m *GetHomeMixedListResp) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *GetHomeMixedListResp) GetDebugInfoMap() map[string]string {
	if m != nil {
		return m.DebugInfoMap
	}
	return nil
}

func (m *GetHomeMixedListResp) GetSelfLoc() *common.LocationInfo {
	if m != nil {
		return m.SelfLoc
	}
	return nil
}

func (m *GetHomeMixedListResp) GetPrefGames() []*PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *GetHomeMixedListResp) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *GetHomeMixedListResp) GetNotifyList() []GetHomeMixedListResp_NotifyType {
	if m != nil {
		return m.NotifyList
	}
	return nil
}

func (m *GetHomeMixedListResp) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

func (m *GetHomeMixedListResp) GetBaseResp() *BaseRCMDResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type BaseRCMDResp struct {
	Ok                   bool                       `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	Code                 uint64                     `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string                     `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	PlanBStrategy        BaseRCMDResp_PlanBStrategy `protobuf:"varint,4,opt,name=plan_b_strategy,json=planBStrategy,proto3,enum=topic_channel.recommendation_gen.BaseRCMDResp_PlanBStrategy" json:"plan_b_strategy,omitempty"`
	IsAlgo               bool                       `protobuf:"varint,5,opt,name=is_algo,json=isAlgo,proto3" json:"is_algo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *BaseRCMDResp) Reset()         { *m = BaseRCMDResp{} }
func (m *BaseRCMDResp) String() string { return proto.CompactTextString(m) }
func (*BaseRCMDResp) ProtoMessage()    {}
func (*BaseRCMDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{14}
}
func (m *BaseRCMDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRCMDResp.Unmarshal(m, b)
}
func (m *BaseRCMDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRCMDResp.Marshal(b, m, deterministic)
}
func (dst *BaseRCMDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRCMDResp.Merge(dst, src)
}
func (m *BaseRCMDResp) XXX_Size() int {
	return xxx_messageInfo_BaseRCMDResp.Size(m)
}
func (m *BaseRCMDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRCMDResp.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRCMDResp proto.InternalMessageInfo

func (m *BaseRCMDResp) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

func (m *BaseRCMDResp) GetCode() uint64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseRCMDResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *BaseRCMDResp) GetPlanBStrategy() BaseRCMDResp_PlanBStrategy {
	if m != nil {
		return m.PlanBStrategy
	}
	return BaseRCMDResp_PlanBStrategy_INVALID
}

func (m *BaseRCMDResp) GetIsAlgo() bool {
	if m != nil {
		return m.IsAlgo
	}
	return false
}

type SetTopicChannelQualityReq struct {
	ChannelId            []uint32                                      `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Quality              SetTopicChannelQualityReq_TopicChannelQuality `protobuf:"varint,2,opt,name=quality,proto3,enum=topic_channel.recommendation_gen.SetTopicChannelQualityReq_TopicChannelQuality" json:"quality,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *SetTopicChannelQualityReq) Reset()         { *m = SetTopicChannelQualityReq{} }
func (m *SetTopicChannelQualityReq) String() string { return proto.CompactTextString(m) }
func (*SetTopicChannelQualityReq) ProtoMessage()    {}
func (*SetTopicChannelQualityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{15}
}
func (m *SetTopicChannelQualityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTopicChannelQualityReq.Unmarshal(m, b)
}
func (m *SetTopicChannelQualityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTopicChannelQualityReq.Marshal(b, m, deterministic)
}
func (dst *SetTopicChannelQualityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTopicChannelQualityReq.Merge(dst, src)
}
func (m *SetTopicChannelQualityReq) XXX_Size() int {
	return xxx_messageInfo_SetTopicChannelQualityReq.Size(m)
}
func (m *SetTopicChannelQualityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTopicChannelQualityReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetTopicChannelQualityReq proto.InternalMessageInfo

func (m *SetTopicChannelQualityReq) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *SetTopicChannelQualityReq) GetQuality() SetTopicChannelQualityReq_TopicChannelQuality {
	if m != nil {
		return m.Quality
	}
	return SetTopicChannelQualityReq_DEFAULT
}

type SetTopicChannelQualityResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTopicChannelQualityResp) Reset()         { *m = SetTopicChannelQualityResp{} }
func (m *SetTopicChannelQualityResp) String() string { return proto.CompactTextString(m) }
func (*SetTopicChannelQualityResp) ProtoMessage()    {}
func (*SetTopicChannelQualityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{16}
}
func (m *SetTopicChannelQualityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTopicChannelQualityResp.Unmarshal(m, b)
}
func (m *SetTopicChannelQualityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTopicChannelQualityResp.Marshal(b, m, deterministic)
}
func (dst *SetTopicChannelQualityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTopicChannelQualityResp.Merge(dst, src)
}
func (m *SetTopicChannelQualityResp) XXX_Size() int {
	return xxx_messageInfo_SetTopicChannelQualityResp.Size(m)
}
func (m *SetTopicChannelQualityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTopicChannelQualityResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTopicChannelQualityResp proto.InternalMessageInfo

type CompanionChannelEvent struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompanionChannelEvent) Reset()         { *m = CompanionChannelEvent{} }
func (m *CompanionChannelEvent) String() string { return proto.CompactTextString(m) }
func (*CompanionChannelEvent) ProtoMessage()    {}
func (*CompanionChannelEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{17}
}
func (m *CompanionChannelEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompanionChannelEvent.Unmarshal(m, b)
}
func (m *CompanionChannelEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompanionChannelEvent.Marshal(b, m, deterministic)
}
func (dst *CompanionChannelEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompanionChannelEvent.Merge(dst, src)
}
func (m *CompanionChannelEvent) XXX_Size() int {
	return xxx_messageInfo_CompanionChannelEvent.Size(m)
}
func (m *CompanionChannelEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_CompanionChannelEvent.DiscardUnknown(m)
}

var xxx_messageInfo_CompanionChannelEvent proto.InternalMessageInfo

func (m *CompanionChannelEvent) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type NegativeFeedEvent struct {
	Uid                  uint32                         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserMap              map[uint32]int64               `protobuf:"bytes,2,rep,name=user_map,json=userMap,proto3" json:"user_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	TabMap               map[uint32]int64               `protobuf:"bytes,3,rep,name=tab_map,json=tabMap,proto3" json:"tab_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelCondMap       map[string]int64               `protobuf:"bytes,4,rep,name=channel_cond_map,json=channelCondMap,proto3" json:"channel_cond_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelNameMap       map[string]int64               `protobuf:"bytes,5,rep,name=channel_name_map,json=channelNameMap,proto3" json:"channel_name_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UpdateTypeList       []NegativeFeedEvent_UpdateType `protobuf:"varint,6,rep,packed,name=update_type_list,json=updateTypeList,proto3,enum=topic_channel.recommendation_gen.NegativeFeedEvent_UpdateType" json:"update_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *NegativeFeedEvent) Reset()         { *m = NegativeFeedEvent{} }
func (m *NegativeFeedEvent) String() string { return proto.CompactTextString(m) }
func (*NegativeFeedEvent) ProtoMessage()    {}
func (*NegativeFeedEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{18}
}
func (m *NegativeFeedEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NegativeFeedEvent.Unmarshal(m, b)
}
func (m *NegativeFeedEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NegativeFeedEvent.Marshal(b, m, deterministic)
}
func (dst *NegativeFeedEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NegativeFeedEvent.Merge(dst, src)
}
func (m *NegativeFeedEvent) XXX_Size() int {
	return xxx_messageInfo_NegativeFeedEvent.Size(m)
}
func (m *NegativeFeedEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_NegativeFeedEvent.DiscardUnknown(m)
}

var xxx_messageInfo_NegativeFeedEvent proto.InternalMessageInfo

func (m *NegativeFeedEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NegativeFeedEvent) GetUserMap() map[uint32]int64 {
	if m != nil {
		return m.UserMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetTabMap() map[uint32]int64 {
	if m != nil {
		return m.TabMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetChannelCondMap() map[string]int64 {
	if m != nil {
		return m.ChannelCondMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetChannelNameMap() map[string]int64 {
	if m != nil {
		return m.ChannelNameMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetUpdateTypeList() []NegativeFeedEvent_UpdateType {
	if m != nil {
		return m.UpdateTypeList
	}
	return nil
}

type UserNegativeFeed struct {
	UserMap              map[uint32]int64 `protobuf:"bytes,1,rep,name=user_map,json=userMap,proto3" json:"user_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	TabMap               map[uint32]int64 `protobuf:"bytes,2,rep,name=tab_map,json=tabMap,proto3" json:"tab_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelCondMap       map[string]int64 `protobuf:"bytes,3,rep,name=channel_cond_map,json=channelCondMap,proto3" json:"channel_cond_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelNameMap       map[string]int64 `protobuf:"bytes,4,rep,name=channel_name_map,json=channelNameMap,proto3" json:"channel_name_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserNegativeFeed) Reset()         { *m = UserNegativeFeed{} }
func (m *UserNegativeFeed) String() string { return proto.CompactTextString(m) }
func (*UserNegativeFeed) ProtoMessage()    {}
func (*UserNegativeFeed) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{19}
}
func (m *UserNegativeFeed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNegativeFeed.Unmarshal(m, b)
}
func (m *UserNegativeFeed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNegativeFeed.Marshal(b, m, deterministic)
}
func (dst *UserNegativeFeed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNegativeFeed.Merge(dst, src)
}
func (m *UserNegativeFeed) XXX_Size() int {
	return xxx_messageInfo_UserNegativeFeed.Size(m)
}
func (m *UserNegativeFeed) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNegativeFeed.DiscardUnknown(m)
}

var xxx_messageInfo_UserNegativeFeed proto.InternalMessageInfo

func (m *UserNegativeFeed) GetUserMap() map[uint32]int64 {
	if m != nil {
		return m.UserMap
	}
	return nil
}

func (m *UserNegativeFeed) GetTabMap() map[uint32]int64 {
	if m != nil {
		return m.TabMap
	}
	return nil
}

func (m *UserNegativeFeed) GetChannelCondMap() map[string]int64 {
	if m != nil {
		return m.ChannelCondMap
	}
	return nil
}

func (m *UserNegativeFeed) GetChannelNameMap() map[string]int64 {
	if m != nil {
		return m.ChannelNameMap
	}
	return nil
}

type ChannelTitleTokenizeEvent struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Tags                 []string `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelTitleTokenizeEvent) Reset()         { *m = ChannelTitleTokenizeEvent{} }
func (m *ChannelTitleTokenizeEvent) String() string { return proto.CompactTextString(m) }
func (*ChannelTitleTokenizeEvent) ProtoMessage()    {}
func (*ChannelTitleTokenizeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{20}
}
func (m *ChannelTitleTokenizeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTitleTokenizeEvent.Unmarshal(m, b)
}
func (m *ChannelTitleTokenizeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTitleTokenizeEvent.Marshal(b, m, deterministic)
}
func (dst *ChannelTitleTokenizeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTitleTokenizeEvent.Merge(dst, src)
}
func (m *ChannelTitleTokenizeEvent) XXX_Size() int {
	return xxx_messageInfo_ChannelTitleTokenizeEvent.Size(m)
}
func (m *ChannelTitleTokenizeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTitleTokenizeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTitleTokenizeEvent proto.InternalMessageInfo

func (m *ChannelTitleTokenizeEvent) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ChannelTitleTokenizeEvent) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

type PlaymateIntentionLimitEvent struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LimitTime            int64    `protobuf:"varint,2,opt,name=limit_time,json=limitTime,proto3" json:"limit_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaymateIntentionLimitEvent) Reset()         { *m = PlaymateIntentionLimitEvent{} }
func (m *PlaymateIntentionLimitEvent) String() string { return proto.CompactTextString(m) }
func (*PlaymateIntentionLimitEvent) ProtoMessage()    {}
func (*PlaymateIntentionLimitEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{21}
}
func (m *PlaymateIntentionLimitEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateIntentionLimitEvent.Unmarshal(m, b)
}
func (m *PlaymateIntentionLimitEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateIntentionLimitEvent.Marshal(b, m, deterministic)
}
func (dst *PlaymateIntentionLimitEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateIntentionLimitEvent.Merge(dst, src)
}
func (m *PlaymateIntentionLimitEvent) XXX_Size() int {
	return xxx_messageInfo_PlaymateIntentionLimitEvent.Size(m)
}
func (m *PlaymateIntentionLimitEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateIntentionLimitEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateIntentionLimitEvent proto.InternalMessageInfo

func (m *PlaymateIntentionLimitEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PlaymateIntentionLimitEvent) GetLimitTime() int64 {
	if m != nil {
		return m.LimitTime
	}
	return 0
}

type GetRecommendationReasonReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendationReasonReq) Reset()         { *m = GetRecommendationReasonReq{} }
func (m *GetRecommendationReasonReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationReasonReq) ProtoMessage()    {}
func (*GetRecommendationReasonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{22}
}
func (m *GetRecommendationReasonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationReasonReq.Unmarshal(m, b)
}
func (m *GetRecommendationReasonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationReasonReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationReasonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationReasonReq.Merge(dst, src)
}
func (m *GetRecommendationReasonReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationReasonReq.Size(m)
}
func (m *GetRecommendationReasonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationReasonReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationReasonReq proto.InternalMessageInfo

func (m *GetRecommendationReasonReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendationReasonReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetRecommendationReasonResp struct {
	Reason               uint32   `protobuf:"varint,1,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendationReasonResp) Reset()         { *m = GetRecommendationReasonResp{} }
func (m *GetRecommendationReasonResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationReasonResp) ProtoMessage()    {}
func (*GetRecommendationReasonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_b0f7b99ec629ae3e, []int{23}
}
func (m *GetRecommendationReasonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationReasonResp.Unmarshal(m, b)
}
func (m *GetRecommendationReasonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationReasonResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationReasonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationReasonResp.Merge(dst, src)
}
func (m *GetRecommendationReasonResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationReasonResp.Size(m)
}
func (m *GetRecommendationReasonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationReasonResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationReasonResp proto.InternalMessageInfo

func (m *GetRecommendationReasonResp) GetReason() uint32 {
	if m != nil {
		return m.Reason
	}
	return 0
}

func init() {
	proto.RegisterType((*ResetFilterReq)(nil), "topic_channel.recommendation_gen.ResetFilterReq")
	proto.RegisterType((*ResetFilterResp)(nil), "topic_channel.recommendation_gen.ResetFilterResp")
	proto.RegisterType((*GetRCMDCfgReq)(nil), "topic_channel.recommendation_gen.GetRCMDCfgReq")
	proto.RegisterType((*GetRCMDCfgResp)(nil), "topic_channel.recommendation_gen.GetRCMDCfgResp")
	proto.RegisterType((*BlockOption)(nil), "topic_channel.recommendation_gen.BlockOption")
	proto.RegisterType((*RecommendationReqReserve)(nil), "topic_channel.recommendation_gen.RecommendationReqReserve")
	proto.RegisterType((*PrefGameLabel)(nil), "topic_channel.recommendation_gen.PrefGameLabel")
	proto.RegisterType((*PrefGame)(nil), "topic_channel.recommendation_gen.PrefGame")
	proto.RegisterType((*GetRecommendationListReq)(nil), "topic_channel.recommendation_gen.GetRecommendationListReq")
	proto.RegisterType((*GetRecommendationListReq_BusinessFilter)(nil), "topic_channel.recommendation_gen.GetRecommendationListReq.BusinessFilter")
	proto.RegisterType((*ChannelTopic)(nil), "topic_channel.recommendation_gen.ChannelTopic")
	proto.RegisterType((*GetHomeMixedListReq)(nil), "topic_channel.recommendation_gen.GetHomeMixedListReq")
	proto.RegisterType((*GetHomeMixedListReq_BusinessFilter)(nil), "topic_channel.recommendation_gen.GetHomeMixedListReq.BusinessFilter")
	proto.RegisterType((*ChannelInfo)(nil), "topic_channel.recommendation_gen.ChannelInfo")
	proto.RegisterType((*GetRecommendationListResp)(nil), "topic_channel.recommendation_gen.GetRecommendationListResp")
	proto.RegisterMapType((map[uint32]*ChannelInfo)(nil), "topic_channel.recommendation_gen.GetRecommendationListResp.ChannelInfoMapEntry")
	proto.RegisterMapType((map[uint32]*common.ChannelInfo)(nil), "topic_channel.recommendation_gen.GetRecommendationListResp.CommonChannelInfoMapEntry")
	proto.RegisterMapType((map[string]string)(nil), "topic_channel.recommendation_gen.GetRecommendationListResp.DebugInfoMapEntry")
	proto.RegisterType((*GetHomeMixedListResp)(nil), "topic_channel.recommendation_gen.GetHomeMixedListResp")
	proto.RegisterMapType((map[uint32]*ChannelInfo)(nil), "topic_channel.recommendation_gen.GetHomeMixedListResp.ChannelInfoMapEntry")
	proto.RegisterMapType((map[string]string)(nil), "topic_channel.recommendation_gen.GetHomeMixedListResp.DebugInfoMapEntry")
	proto.RegisterType((*BaseRCMDResp)(nil), "topic_channel.recommendation_gen.BaseRCMDResp")
	proto.RegisterType((*SetTopicChannelQualityReq)(nil), "topic_channel.recommendation_gen.SetTopicChannelQualityReq")
	proto.RegisterType((*SetTopicChannelQualityResp)(nil), "topic_channel.recommendation_gen.SetTopicChannelQualityResp")
	proto.RegisterType((*CompanionChannelEvent)(nil), "topic_channel.recommendation_gen.CompanionChannelEvent")
	proto.RegisterType((*NegativeFeedEvent)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.ChannelCondMapEntry")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.ChannelNameMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.TabMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.UserMapEntry")
	proto.RegisterType((*UserNegativeFeed)(nil), "topic_channel.recommendation_gen.UserNegativeFeed")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.ChannelCondMapEntry")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.ChannelNameMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.TabMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.UserMapEntry")
	proto.RegisterType((*ChannelTitleTokenizeEvent)(nil), "topic_channel.recommendation_gen.ChannelTitleTokenizeEvent")
	proto.RegisterType((*PlaymateIntentionLimitEvent)(nil), "topic_channel.recommendation_gen.PlaymateIntentionLimitEvent")
	proto.RegisterType((*GetRecommendationReasonReq)(nil), "topic_channel.recommendation_gen.GetRecommendationReasonReq")
	proto.RegisterType((*GetRecommendationReasonResp)(nil), "topic_channel.recommendation_gen.GetRecommendationReasonResp")
	proto.RegisterEnum("topic_channel.recommendation_gen.IMChannelListABGroup", IMChannelListABGroup_name, IMChannelListABGroup_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.REGULATORY_LEVEL", REGULATORY_LEVEL_name, REGULATORY_LEVEL_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.PrefGameLabelType", PrefGameLabelType_name, PrefGameLabelType_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.BusinessBlockEnum", BusinessBlockEnum_name, BusinessBlockEnum_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.BusinessElemEnum", BusinessElemEnum_name, BusinessElemEnum_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.RCMDLabel", RCMDLabel_name, RCMDLabel_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.RCMDReason", RCMDReason_name, RCMDReason_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRCMDCfgReq_SOURCE", GetRCMDCfgReq_SOURCE_name, GetRCMDCfgReq_SOURCE_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRecommendationListReq_GetListMode", GetRecommendationListReq_GetListMode_name, GetRecommendationListReq_GetListMode_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRecommendationListReq_Environment", GetRecommendationListReq_Environment_name, GetRecommendationListReq_Environment_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRecommendationListReq_Scene", GetRecommendationListReq_Scene_name, GetRecommendationListReq_Scene_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRecommendationListReq_FilterModel", GetRecommendationListReq_FilterModel_name, GetRecommendationListReq_FilterModel_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetHomeMixedListReq_GetListMode", GetHomeMixedListReq_GetListMode_name, GetHomeMixedListReq_GetListMode_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetHomeMixedListReq_Environment", GetHomeMixedListReq_Environment_name, GetHomeMixedListReq_Environment_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.ChannelInfo_LocShowType", ChannelInfo_LocShowType_name, ChannelInfo_LocShowType_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRecommendationListResp_NotifyType", GetRecommendationListResp_NotifyType_name, GetRecommendationListResp_NotifyType_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetHomeMixedListResp_NotifyType", GetHomeMixedListResp_NotifyType_name, GetHomeMixedListResp_NotifyType_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.BaseRCMDResp_PlanBStrategy", BaseRCMDResp_PlanBStrategy_name, BaseRCMDResp_PlanBStrategy_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.SetTopicChannelQualityReq_TopicChannelQuality", SetTopicChannelQualityReq_TopicChannelQuality_name, SetTopicChannelQualityReq_TopicChannelQuality_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.NegativeFeedEvent_UpdateType", NegativeFeedEvent_UpdateType_name, NegativeFeedEvent_UpdateType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GenRecommendationClient is the client API for GenRecommendation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GenRecommendationClient interface {
	// 获取推荐房列表（个数为1的时候特殊处理，获取最合适的/home/<USER>/go_project/tt19/rcmd/api/rcmd/topic_channel）
	GetRecommendationList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error)
	// expose for algorithm 0.2
	RecallChannelList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error)
	SetTopicChannelQuality(ctx context.Context, in *SetTopicChannelQualityReq, opts ...grpc.CallOption) (*SetTopicChannelQualityResp, error)
	// rcmd-common config
	GetRCMDCfg(ctx context.Context, in *GetRCMDCfgReq, opts ...grpc.CallOption) (*GetRCMDCfgResp, error)
	// 清空下发过滤器
	ResetFilter(ctx context.Context, in *ResetFilterReq, opts ...grpc.CallOption) (*ResetFilterResp, error)
	// 首页混推新接口 -- 从上面原接口GetRecommendationList复制出来的
	GetHomeMixedList(ctx context.Context, in *GetHomeMixedListReq, opts ...grpc.CallOption) (*GetHomeMixedListResp, error)
	// 异步获取推荐理由
	GetRecommendationReason(ctx context.Context, in *GetRecommendationReasonReq, opts ...grpc.CallOption) (*GetRecommendationReasonResp, error)
}

type genRecommendationClient struct {
	cc *grpc.ClientConn
}

func NewGenRecommendationClient(cc *grpc.ClientConn) GenRecommendationClient {
	return &genRecommendationClient{cc}
}

func (c *genRecommendationClient) GetRecommendationList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error) {
	out := new(GetRecommendationListResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/GetRecommendationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) RecallChannelList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error) {
	out := new(GetRecommendationListResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/RecallChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) SetTopicChannelQuality(ctx context.Context, in *SetTopicChannelQualityReq, opts ...grpc.CallOption) (*SetTopicChannelQualityResp, error) {
	out := new(SetTopicChannelQualityResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/SetTopicChannelQuality", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) GetRCMDCfg(ctx context.Context, in *GetRCMDCfgReq, opts ...grpc.CallOption) (*GetRCMDCfgResp, error) {
	out := new(GetRCMDCfgResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/GetRCMDCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) ResetFilter(ctx context.Context, in *ResetFilterReq, opts ...grpc.CallOption) (*ResetFilterResp, error) {
	out := new(ResetFilterResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/ResetFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) GetHomeMixedList(ctx context.Context, in *GetHomeMixedListReq, opts ...grpc.CallOption) (*GetHomeMixedListResp, error) {
	out := new(GetHomeMixedListResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/GetHomeMixedList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) GetRecommendationReason(ctx context.Context, in *GetRecommendationReasonReq, opts ...grpc.CallOption) (*GetRecommendationReasonResp, error) {
	out := new(GetRecommendationReasonResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/GetRecommendationReason", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GenRecommendationServer is the server API for GenRecommendation service.
type GenRecommendationServer interface {
	// 获取推荐房列表（个数为1的时候特殊处理，获取最合适的/home/<USER>/go_project/tt19/rcmd/api/rcmd/topic_channel）
	GetRecommendationList(context.Context, *GetRecommendationListReq) (*GetRecommendationListResp, error)
	// expose for algorithm 0.2
	RecallChannelList(context.Context, *GetRecommendationListReq) (*GetRecommendationListResp, error)
	SetTopicChannelQuality(context.Context, *SetTopicChannelQualityReq) (*SetTopicChannelQualityResp, error)
	// rcmd-common config
	GetRCMDCfg(context.Context, *GetRCMDCfgReq) (*GetRCMDCfgResp, error)
	// 清空下发过滤器
	ResetFilter(context.Context, *ResetFilterReq) (*ResetFilterResp, error)
	// 首页混推新接口 -- 从上面原接口GetRecommendationList复制出来的
	GetHomeMixedList(context.Context, *GetHomeMixedListReq) (*GetHomeMixedListResp, error)
	// 异步获取推荐理由
	GetRecommendationReason(context.Context, *GetRecommendationReasonReq) (*GetRecommendationReasonResp, error)
}

func RegisterGenRecommendationServer(s *grpc.Server, srv GenRecommendationServer) {
	s.RegisterService(&_GenRecommendation_serviceDesc, srv)
}

func _GenRecommendation_GetRecommendationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).GetRecommendationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/GetRecommendationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).GetRecommendationList(ctx, req.(*GetRecommendationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_RecallChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).RecallChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/RecallChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).RecallChannelList(ctx, req.(*GetRecommendationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_SetTopicChannelQuality_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTopicChannelQualityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).SetTopicChannelQuality(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/SetTopicChannelQuality",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).SetTopicChannelQuality(ctx, req.(*SetTopicChannelQualityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_GetRCMDCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRCMDCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).GetRCMDCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/GetRCMDCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).GetRCMDCfg(ctx, req.(*GetRCMDCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_ResetFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).ResetFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/ResetFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).ResetFilter(ctx, req.(*ResetFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_GetHomeMixedList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHomeMixedListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).GetHomeMixedList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/GetHomeMixedList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).GetHomeMixedList(ctx, req.(*GetHomeMixedListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_GetRecommendationReason_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendationReasonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).GetRecommendationReason(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/GetRecommendationReason",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).GetRecommendationReason(ctx, req.(*GetRecommendationReasonReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GenRecommendation_serviceDesc = grpc.ServiceDesc{
	ServiceName: "topic_channel.recommendation_gen.GenRecommendation",
	HandlerType: (*GenRecommendationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRecommendationList",
			Handler:    _GenRecommendation_GetRecommendationList_Handler,
		},
		{
			MethodName: "RecallChannelList",
			Handler:    _GenRecommendation_RecallChannelList_Handler,
		},
		{
			MethodName: "SetTopicChannelQuality",
			Handler:    _GenRecommendation_SetTopicChannelQuality_Handler,
		},
		{
			MethodName: "GetRCMDCfg",
			Handler:    _GenRecommendation_GetRCMDCfg_Handler,
		},
		{
			MethodName: "ResetFilter",
			Handler:    _GenRecommendation_ResetFilter_Handler,
		},
		{
			MethodName: "GetHomeMixedList",
			Handler:    _GenRecommendation_GetHomeMixedList_Handler,
		},
		{
			MethodName: "GetRecommendationReason",
			Handler:    _GenRecommendation_GetRecommendationReason_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/topic_channel/recommendation_gen.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/topic_channel/recommendation_gen.proto", fileDescriptor_recommendation_gen_b0f7b99ec629ae3e)
}

var fileDescriptor_recommendation_gen_b0f7b99ec629ae3e = []byte{
	// 3455 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x3b, 0x4d, 0x73, 0x1b, 0x47,
	0x76, 0x02, 0x40, 0x12, 0xc0, 0x03, 0x01, 0x0e, 0x9b, 0x94, 0x34, 0x82, 0xac, 0x35, 0x0d, 0x67,
	0x6d, 0x96, 0x14, 0x93, 0xbb, 0x94, 0xbc, 0x1f, 0xb6, 0xb3, 0x0e, 0x3f, 0x40, 0x0a, 0x59, 0x82,
	0xa4, 0x07, 0xa0, 0x64, 0x7b, 0x77, 0x6b, 0xaa, 0x31, 0xd3, 0x04, 0x3b, 0x9c, 0x99, 0x1e, 0x4d,
	0x37, 0x40, 0x71, 0xab, 0x72, 0xc8, 0x29, 0x17, 0x1f, 0x52, 0x95, 0x4a, 0x2a, 0xb7, 0x5c, 0xf3,
	0x07, 0x72, 0x4b, 0x55, 0x7e, 0x41, 0x2e, 0xf9, 0x0d, 0xf9, 0x13, 0xb9, 0xa5, 0xba, 0x7b, 0x00,
	0xcc, 0x10, 0xa0, 0x49, 0x82, 0xa9, 0xda, 0xa8, 0xec, 0x13, 0xbb, 0xdf, 0x7b, 0xfd, 0xde, 0xeb,
	0xd7, 0xef, 0xab, 0x7b, 0x40, 0xf8, 0x54, 0x88, 0xf5, 0x37, 0x3d, 0xea, 0x9c, 0x71, 0xea, 0xf5,
	0x49, 0xb4, 0x2e, 0x58, 0x48, 0x1d, 0xdb, 0x39, 0xc5, 0x41, 0x40, 0xbc, 0xf5, 0x88, 0x38, 0xcc,
	0xf7, 0x49, 0xe0, 0x62, 0x41, 0x59, 0x60, 0x77, 0x49, 0xb0, 0x16, 0x46, 0x4c, 0x30, 0xb4, 0x92,
	0xa2, 0x5b, 0x1b, 0xa7, 0xab, 0x7e, 0x72, 0x89, 0x71, 0xe4, 0xf8, 0xee, 0x27, 0x1e, 0x73, 0xb0,
	0xb7, 0x2e, 0x69, 0x59, 0x10, 0xff, 0xd1, 0x0c, 0xab, 0x2f, 0xae, 0x25, 0x4f, 0x4b, 0xd4, 0xab,
	0xae, 0xd1, 0xde, 0xf1, 0xdd, 0xc1, 0xc4, 0xf6, 0x70, 0x67, 0xb8, 0xec, 0x57, 0xb7, 0xd9, 0x74,
	0x52, 0xcd, 0x5a, 0x0d, 0x2a, 0x16, 0xe1, 0x44, 0xec, 0x52, 0x4f, 0x90, 0xc8, 0x22, 0x6f, 0x90,
	0x01, 0xb9, 0x1e, 0x75, 0xcd, 0xcc, 0x4a, 0x66, 0xb5, 0x6c, 0xc9, 0x61, 0xed, 0x97, 0xb0, 0x90,
	0xa2, 0xe1, 0x21, 0x42, 0x30, 0xe3, 0x30, 0x97, 0xc4, 0x54, 0x6a, 0x2c, 0x17, 0xfa, 0xbc, 0x6b,
	0x66, 0x57, 0x32, 0xab, 0x45, 0x4b, 0x0e, 0x6b, 0xdf, 0x65, 0xa0, 0xbc, 0x47, 0x84, 0xb5, 0xdd,
	0xdc, 0xd9, 0x3e, 0xe9, 0x4a, 0xe6, 0x07, 0x30, 0xc7, 0x59, 0x2f, 0x72, 0xf4, 0xca, 0xca, 0xc6,
	0x2f, 0xd6, 0xae, 0xb3, 0xfb, 0x5a, 0x8a, 0xc1, 0x5a, 0xeb, 0xf0, 0xd8, 0xda, 0xae, 0x5b, 0x31,
	0x97, 0xda, 0x33, 0x98, 0xd3, 0x10, 0x84, 0xa0, 0xa2, 0x47, 0xf6, 0x4e, 0x7d, 0x77, 0xf3, 0x78,
	0xbf, 0x6d, 0xdc, 0x43, 0x65, 0x28, 0xc6, 0xb0, 0x76, 0xdb, 0xc8, 0xd4, 0x76, 0xa0, 0x92, 0x64,
	0xc6, 0x43, 0xb4, 0x01, 0xf7, 0x79, 0x2f, 0x24, 0x51, 0x9f, 0x72, 0x16, 0xd9, 0xe7, 0xa7, 0x54,
	0x10, 0xdb, 0xa3, 0x5c, 0x98, 0x99, 0x95, 0xdc, 0x6a, 0xd9, 0x5a, 0x1a, 0x21, 0x5f, 0x4b, 0xdc,
	0x3e, 0xe5, 0xa2, 0xb6, 0x09, 0xa5, 0x2d, 0x8f, 0x39, 0x67, 0x87, 0xa1, 0x54, 0x11, 0x3d, 0x82,
	0x42, 0x47, 0x4e, 0xed, 0xa1, 0xcd, 0xf2, 0x6a, 0xde, 0x70, 0xd1, 0x43, 0xc8, 0x13, 0x8f, 0xf8,
	0x12, 0x93, 0x55, 0x98, 0x39, 0x39, 0x6d, 0xb8, 0xb5, 0xbf, 0x01, 0xd3, 0x4a, 0x6d, 0xd4, 0x22,
	0x6f, 0xa4, 0x85, 0xa3, 0x3e, 0x41, 0x18, 0x16, 0xa9, 0xaf, 0x94, 0xb0, 0xc9, 0xdb, 0xd0, 0xee,
	0x46, 0xac, 0x17, 0xde, 0xdc, 0x58, 0x8d, 0xe6, 0xb6, 0x46, 0x4a, 0x55, 0x37, 0xb7, 0xf6, 0xe4,
	0x6a, 0xab, 0x42, 0x7d, 0x39, 0xad, 0xbf, 0x0d, 0xd5, 0xbc, 0xf6, 0x47, 0x28, 0x1f, 0x45, 0xe4,
	0x64, 0x0f, 0xfb, 0x64, 0x5f, 0x3a, 0x11, 0xaa, 0x40, 0x76, 0xa8, 0x7d, 0x96, 0xba, 0xf2, 0x24,
	0xfb, 0xd8, 0x1b, 0x9c, 0x64, 0x1f, 0x7b, 0x68, 0x0f, 0x66, 0xc4, 0x45, 0x48, 0xcc, 0x9c, 0x52,
	0xe4, 0xf9, 0xf5, 0x8a, 0xa4, 0x04, 0xb4, 0x2f, 0x42, 0x62, 0x29, 0x06, 0xb5, 0xbf, 0x86, 0xc2,
	0x00, 0x85, 0xee, 0xc3, 0x9c, 0xc0, 0x9d, 0x91, 0xe1, 0x66, 0x05, 0xee, 0x34, 0x5c, 0xb4, 0x07,
	0x73, 0xca, 0xb7, 0xb9, 0x99, 0x5d, 0xc9, 0xad, 0x96, 0x36, 0xd6, 0x6f, 0x29, 0xcd, 0x8a, 0x97,
	0xd7, 0xfe, 0x79, 0x09, 0x4c, 0x79, 0xe0, 0x29, 0x7a, 0x69, 0x89, 0x89, 0x6e, 0x8e, 0x96, 0x61,
	0xd6, 0xa3, 0x3e, 0x15, 0xf1, 0x61, 0xe9, 0x49, 0x42, 0xc9, 0x5c, 0x52, 0x49, 0x0b, 0xca, 0xfa,
	0xd8, 0x99, 0x72, 0x03, 0x6e, 0xce, 0x28, 0x5d, 0x3f, 0xb9, 0x5e, 0xd7, 0x84, 0xf3, 0x58, 0xf3,
	0x9d, 0xd1, 0x84, 0x23, 0x0c, 0x85, 0x2e, 0x11, 0xb6, 0x2f, 0x03, 0x6b, 0x56, 0x19, 0x7a, 0xf7,
	0x66, 0xe1, 0x31, 0x69, 0x83, 0x12, 0x21, 0x87, 0x4d, 0xe6, 0x12, 0x2b, 0xdf, 0x25, 0x6a, 0x80,
	0x7e, 0x06, 0xcb, 0x83, 0xfc, 0x41, 0x02, 0x41, 0x22, 0x3b, 0x8e, 0xc6, 0x39, 0xb5, 0x37, 0x14,
	0xe3, 0xea, 0x12, 0xd5, 0x52, 0x18, 0xf4, 0x3e, 0x94, 0x1c, 0x8f, 0x92, 0x40, 0xd8, 0xca, 0x01,
	0xf2, 0x8a, 0x10, 0x34, 0x48, 0x9e, 0x2b, 0xfa, 0x29, 0x54, 0x62, 0x82, 0x3e, 0x89, 0x38, 0x65,
	0x81, 0x59, 0x50, 0x34, 0x65, 0x0d, 0x7d, 0xa5, 0x81, 0xe8, 0x31, 0x14, 0x7d, 0x1c, 0x9d, 0x11,
	0x21, 0x4d, 0x59, 0x54, 0x14, 0x05, 0x0d, 0x68, 0xb8, 0xe8, 0x6b, 0xc8, 0x91, 0xa0, 0x6f, 0xc2,
	0x9d, 0x37, 0x5d, 0x0f, 0xfa, 0x34, 0x62, 0x81, 0x4f, 0x02, 0x61, 0x49, 0x96, 0xe8, 0xcf, 0x61,
	0xb0, 0x29, 0x3b, 0xc4, 0xce, 0x19, 0xee, 0x12, 0x29, 0xbf, 0xa4, 0x3c, 0xdb, 0x88, 0x31, 0x47,
	0x1a, 0xd1, 0x70, 0x65, 0x30, 0x8b, 0x08, 0x3b, 0x8a, 0x66, 0x5e, 0xd1, 0xe4, 0xd5, 0xbc, 0xe1,
	0xa2, 0x0f, 0xa1, 0x2c, 0x0f, 0xc6, 0x1b, 0xee, 0xb2, 0xac, 0xf0, 0xf3, 0x0a, 0x38, 0xd8, 0xe4,
	0x13, 0x00, 0x97, 0x74, 0x7a, 0x5d, 0xfb, 0xc4, 0xc3, 0x5d, 0xb3, 0xa2, 0x76, 0x59, 0x54, 0x90,
	0x5d, 0x0f, 0x77, 0x91, 0x09, 0xf9, 0x48, 0x87, 0xb9, 0xb9, 0xa0, 0xb9, 0xc7, 0x53, 0xf4, 0x1c,
	0x0a, 0x1d, 0xcc, 0x89, 0x1d, 0x91, 0x37, 0xa6, 0xb1, 0x92, 0x59, 0x2d, 0x6d, 0x98, 0x6b, 0x32,
	0xdb, 0xaf, 0xc5, 0xc9, 0xda, 0x72, 0x7c, 0x77, 0x0b, 0x73, 0x22, 0x13, 0x45, 0xbe, 0xa3, 0x07,
	0xd2, 0x85, 0x39, 0x79, 0x6b, 0x2e, 0x6a, 0x17, 0xe6, 0xe4, 0x2d, 0xfa, 0x02, 0x4a, 0x9d, 0x88,
	0x9d, 0xf3, 0x38, 0x8b, 0x21, 0xc5, 0xe9, 0xf1, 0x38, 0x27, 0x45, 0xd3, 0x08, 0x4e, 0x98, 0x05,
	0x9a, 0x5e, 0xda, 0x0f, 0xfd, 0x01, 0x8c, 0x88, 0x74, 0x7b, 0x1e, 0x16, 0x2c, 0xba, 0xb0, 0x3d,
	0xd2, 0x27, 0x9e, 0xb9, 0xa4, 0x8e, 0x64, 0xe3, 0xfa, 0x23, 0xb1, 0xea, 0x7b, 0xc7, 0xfb, 0x9b,
	0xed, 0x43, 0xeb, 0x1b, 0x7b, 0xbf, 0xfe, 0xaa, 0xbe, 0x6f, 0x2d, 0x8c, 0x78, 0xed, 0x4b, 0x56,
	0xe8, 0x39, 0x3c, 0xa0, 0xdc, 0xf6, 0x69, 0xc0, 0x22, 0x2a, 0x2e, 0xec, 0x10, 0x47, 0xca, 0xab,
	0x70, 0xc7, 0x5c, 0x5e, 0xc9, 0xac, 0x16, 0xac, 0x25, 0xca, 0x9b, 0x31, 0xf2, 0x48, 0xe1, 0xda,
	0xb8, 0x83, 0x3e, 0x80, 0x79, 0x07, 0x0b, 0xd2, 0x95, 0x1a, 0x51, 0x97, 0x9b, 0x0f, 0x54, 0x62,
	0x2e, 0x0d, 0x60, 0x0d, 0x97, 0xcb, 0x34, 0xab, 0x23, 0x94, 0x9b, 0x0f, 0x15, 0x76, 0x4e, 0x85,
	0x28, 0x47, 0x1f, 0xc1, 0x02, 0xe5, 0xf6, 0x1b, 0xdf, 0x3e, 0x89, 0xb0, 0x4f, 0xce, 0x59, 0x74,
	0x66, 0x9a, 0x4a, 0x52, 0x99, 0xf2, 0xaf, 0xfc, 0xdd, 0x01, 0x10, 0x35, 0x00, 0xc2, 0x88, 0x9c,
	0xd8, 0x5d, 0xec, 0x13, 0x6e, 0x3e, 0x52, 0x81, 0xfc, 0xf4, 0xe6, 0x49, 0xc7, 0x2a, 0x86, 0xf1,
	0x88, 0xa3, 0xed, 0x61, 0xee, 0xaa, 0x2a, 0x36, 0xcf, 0x2e, 0xb3, 0x19, 0xaf, 0xe0, 0x63, 0x79,
	0x0b, 0x45, 0xb0, 0xd0, 0xe9, 0x71, 0x1a, 0x10, 0xce, 0xed, 0x13, 0x55, 0x73, 0xcd, 0xc7, 0x8a,
	0x5b, 0xe3, 0x0e, 0x91, 0xb1, 0x15, 0x73, 0x8c, 0x8b, 0x78, 0xa5, 0x93, 0x9a, 0xa3, 0xd7, 0x50,
	0x96, 0xdb, 0xb7, 0x39, 0x11, 0x82, 0x06, 0x5d, 0x6e, 0xbe, 0xa7, 0x24, 0x5e, 0x73, 0xf0, 0xb1,
	0x4f, 0xb5, 0x1d, 0xb9, 0x89, 0x96, 0x5e, 0x6a, 0xcd, 0x77, 0x47, 0x93, 0xc1, 0x21, 0x9c, 0x60,
	0xcf, 0xeb, 0x60, 0xe7, 0x4c, 0x39, 0xf8, 0x93, 0xc1, 0x21, 0xec, 0xc6, 0x50, 0xe9, 0xcc, 0x1f,
	0xc3, 0x02, 0x95, 0x69, 0x87, 0x70, 0x61, 0xc7, 0x26, 0xfc, 0xc9, 0x4a, 0x6e, 0xb5, 0x68, 0x55,
	0x06, 0xe0, 0x7d, 0x6d, 0x9d, 0x0f, 0xa1, 0xec, 0x12, 0x8f, 0xf6, 0x49, 0x74, 0xa1, 0x53, 0xd2,
	0xfb, 0x3a, 0x10, 0x07, 0x40, 0x95, 0x94, 0xd6, 0x61, 0x99, 0x72, 0x9b, 0x04, 0xb8, 0xe3, 0x11,
	0x75, 0xae, 0x9a, 0xa7, 0xb9, 0xa2, 0x44, 0x2f, 0x52, 0x5e, 0x57, 0xa8, 0x51, 0x09, 0x5c, 0x83,
	0x25, 0x7e, 0x4a, 0x89, 0xe7, 0xc6, 0x16, 0xb7, 0xcf, 0x59, 0xe4, 0x72, 0xf3, 0x03, 0xa5, 0xc2,
	0xa2, 0x46, 0x69, 0x53, 0xbd, 0x96, 0x08, 0xf4, 0x7b, 0x58, 0x70, 0x3c, 0xcc, 0x39, 0x3d, 0xb9,
	0x18, 0xa8, 0x5b, 0x53, 0x16, 0x7b, 0x7e, 0xfd, 0x89, 0x6f, 0xc7, 0x0b, 0x95, 0x64, 0x75, 0x3e,
	0x15, 0x27, 0x09, 0xe2, 0xb2, 0x14, 0x71, 0x87, 0x04, 0xc4, 0xfc, 0x50, 0xd7, 0x1c, 0x35, 0x91,
	0x29, 0x34, 0x56, 0x8e, 0xba, 0xe6, 0x9f, 0xa9, 0x5d, 0x17, 0x34, 0xa0, 0xe1, 0xa2, 0x63, 0xa8,
	0x0c, 0xa4, 0x28, 0x05, 0xb8, 0xf9, 0x53, 0xa5, 0xcf, 0xda, 0xf5, 0x3e, 0x13, 0xb7, 0x0c, 0x6d,
	0x49, 0x67, 0x95, 0x9d, 0xc4, 0x8c, 0xa3, 0xcf, 0xa0, 0x4a, 0xb9, 0xdd, 0xe3, 0x24, 0xb2, 0x65,
	0xf3, 0xaa, 0xd6, 0xe0, 0x9e, 0x38, 0xb5, 0x59, 0x48, 0x02, 0xf3, 0x23, 0x65, 0xce, 0x07, 0x94,
	0x1f, 0x73, 0x12, 0xed, 0xc7, 0xf8, 0xcd, 0x9e, 0x38, 0x3d, 0x0c, 0x49, 0x20, 0x63, 0x37, 0xd6,
	0x57, 0x25, 0x49, 0xf3, 0x63, 0xb5, 0x99, 0x92, 0x86, 0xc9, 0x72, 0xe4, 0xa1, 0x3a, 0x2c, 0x2a,
	0xde, 0xdd, 0x90, 0x0f, 0xf9, 0x9b, 0xab, 0x2a, 0x6d, 0x3d, 0x4a, 0xa5, 0xad, 0x01, 0x73, 0x95,
	0xb4, 0x16, 0xe4, 0x9a, 0xbd, 0x90, 0x0f, 0x80, 0xd5, 0x7f, 0xcb, 0x40, 0x25, 0xed, 0xe0, 0xc8,
	0x02, 0xd0, 0x05, 0x5a, 0xf9, 0x48, 0xe6, 0xa6, 0x7d, 0xcb, 0x80, 0x8b, 0xaa, 0xd2, 0xf5, 0xa0,
	0xe7, 0x5b, 0x45, 0xc5, 0x46, 0x79, 0xd5, 0x21, 0x14, 0x55, 0x43, 0xa7, 0x58, 0xca, 0xe6, 0xe4,
	0x46, 0x99, 0x71, 0xc0, 0xb2, 0xee, 0x11, 0x5f, 0x71, 0x2c, 0x48, 0x26, 0x92, 0xa1, 0xec, 0x25,
	0x13, 0x65, 0x1a, 0x95, 0x20, 0x3f, 0x6a, 0x5e, 0xe7, 0xa1, 0x70, 0x50, 0xff, 0xba, 0x7d, 0xb4,
	0xb9, 0x57, 0x37, 0x32, 0x12, 0x65, 0xd5, 0x77, 0xad, 0x7a, 0xeb, 0xa5, 0x91, 0x45, 0x00, 0x73,
	0x8d, 0x83, 0x56, 0xdd, 0x6a, 0x1b, 0xb9, 0xda, 0x0b, 0x28, 0x25, 0x8a, 0x1e, 0xaa, 0xc8, 0x5c,
	0xc6, 0xdc, 0x9e, 0x23, 0xc5, 0x1b, 0xf7, 0xe4, 0x3a, 0x2e, 0x70, 0x97, 0x06, 0x5d, 0x23, 0x83,
	0x0a, 0x30, 0x23, 0x08, 0x17, 0x46, 0xb6, 0x76, 0x0c, 0xb3, 0x2d, 0xe5, 0x53, 0x52, 0x24, 0x39,
	0xc1, 0x3d, 0x4f, 0x18, 0xf7, 0x24, 0xdf, 0x3d, 0x1c, 0x74, 0x8f, 0x43, 0x23, 0x83, 0x8a, 0x30,
	0xdb, 0x3e, 0x25, 0x3e, 0x31, 0xb2, 0x72, 0xd8, 0xec, 0x71, 0xea, 0x18, 0x39, 0xc9, 0x7e, 0xab,
	0xc7, 0x79, 0x4c, 0xe5, 0xca, 0x0e, 0x5b, 0xce, 0x35, 0x9a, 0xd4, 0x3e, 0x86, 0xd2, 0x6e, 0xe2,
	0x74, 0x4b, 0x90, 0x6f, 0xaa, 0x9d, 0x79, 0x5a, 0x93, 0xdf, 0x9e, 0xea, 0x49, 0xa6, 0x76, 0x01,
	0xf3, 0x49, 0xaf, 0xbb, 0xaa, 0x15, 0x1c, 0xeb, 0xb2, 0xb2, 0x77, 0xee, 0xb2, 0x6a, 0xff, 0x58,
	0x81, 0xa5, 0x3d, 0x22, 0x5e, 0x32, 0x9f, 0x34, 0xe9, 0x5b, 0xe2, 0xfe, 0x3f, 0x6e, 0x08, 0x7f,
	0x3f, 0xd6, 0x10, 0x6e, 0xde, 0xa8, 0x02, 0x5c, 0xde, 0xdb, 0x3b, 0xd5, 0x0b, 0xb6, 0x92, 0xbd,
	0xe0, 0x94, 0xfb, 0xfd, 0xb1, 0x0d, 0xfc, 0xb1, 0x0d, 0x7c, 0xe7, 0xdb, 0x40, 0xff, 0xaa, 0x36,
	0x70, 0x67, 0xba, 0xa0, 0xf8, 0x41, 0x75, 0x80, 0x3f, 0xb6, 0x04, 0xb7, 0x6d, 0x09, 0xfe, 0x63,
	0x16, 0x4a, 0x71, 0x4d, 0x96, 0x29, 0x41, 0xd7, 0xb9, 0x6e, 0xaa, 0x24, 0x77, 0x1b, 0xae, 0xac,
	0x01, 0x11, 0x71, 0xb0, 0xe7, 0xe9, 0xe4, 0xa6, 0x4b, 0x23, 0x68, 0x90, 0xca, 0x6e, 0xcf, 0x20,
	0xe7, 0x31, 0x47, 0x15, 0xc7, 0xef, 0x6d, 0xe2, 0x24, 0x15, 0xda, 0x87, 0x92, 0x8a, 0x85, 0xf8,
	0xbc, 0x67, 0x94, 0x01, 0x9f, 0xdd, 0x20, 0xcd, 0x6c, 0x37, 0x77, 0x74, 0xb4, 0x80, 0x5c, 0x1f,
	0x3b, 0xc6, 0x1f, 0xa0, 0xec, 0x31, 0xc7, 0xe6, 0xa7, 0xec, 0x5c, 0x1f, 0x88, 0x2e, 0x9a, 0xbf,
	0xbe, 0x71, 0x0b, 0x2c, 0x15, 0x93, 0x5a, 0xb6, 0x4e, 0xd9, 0xb9, 0x7a, 0xb4, 0x2a, 0x79, 0xa3,
	0x09, 0xfa, 0x39, 0xdc, 0xa7, 0xdc, 0x0e, 0xc8, 0xb9, 0xee, 0x87, 0x7b, 0x81, 0x4b, 0x22, 0x81,
	0xcf, 0x74, 0xc5, 0x2c, 0x58, 0x88, 0xf2, 0x03, 0x72, 0x2e, 0x3b, 0xe1, 0xe3, 0x01, 0x46, 0xfa,
	0xfe, 0x09, 0xf3, 0x3c, 0x76, 0x6e, 0xf7, 0xa8, 0xab, 0xb3, 0x71, 0x5e, 0x25, 0xa7, 0xb2, 0x06,
	0x1f, 0x53, 0x15, 0x9e, 0xa8, 0x06, 0xe5, 0xd0, 0xc3, 0x17, 0x23, 0xaa, 0x82, 0x4e, 0x70, 0x12,
	0x38, 0xa0, 0x79, 0x06, 0xe8, 0x12, 0x2f, 0xbb, 0xbf, 0x61, 0x16, 0x15, 0xe1, 0x42, 0x8a, 0xdd,
	0xab, 0x0d, 0xc9, 0x50, 0x5e, 0x80, 0xfa, 0x24, 0xd2, 0xc5, 0x5d, 0xd5, 0xd3, 0x82, 0x55, 0xa2,
	0xbc, 0xde, 0x27, 0x91, 0x2a, 0xea, 0xe3, 0x71, 0x54, 0x9a, 0x70, 0x93, 0xfa, 0x18, 0x16, 0x5c,
	0xca, 0x95, 0x72, 0x0e, 0x0b, 0x04, 0x09, 0x44, 0x5c, 0x12, 0x2b, 0x31, 0x78, 0x5b, 0x43, 0xd1,
	0x2a, 0x18, 0x03, 0xc2, 0xe1, 0x2e, 0xca, 0x4a, 0xb9, 0x01, 0x65, 0xac, 0x5d, 0xed, 0x15, 0x94,
	0x12, 0x36, 0x46, 0x0f, 0x61, 0x29, 0x31, 0x4d, 0x3c, 0xdf, 0x9a, 0xb0, 0x9c, 0x44, 0x1c, 0x59,
	0x87, 0xaf, 0x1a, 0x07, 0xdb, 0x75, 0x23, 0x8b, 0x96, 0xc1, 0x48, 0x62, 0xb6, 0x1b, 0xed, 0x6f,
	0x8c, 0x5c, 0xed, 0xef, 0x00, 0x1e, 0x5d, 0x71, 0xff, 0xe5, 0xa1, 0x2c, 0xca, 0x83, 0x8c, 0xab,
	0x7c, 0x5a, 0x6a, 0x56, 0x8c, 0x21, 0x0d, 0x57, 0xb6, 0x2e, 0x1d, 0x26, 0x04, 0xf3, 0xed, 0x88,
	0x60, 0xe7, 0x94, 0xe8, 0x37, 0xdb, 0x82, 0x55, 0xd6, 0x50, 0x4b, 0x03, 0xd1, 0x05, 0x18, 0x43,
	0x2e, 0xc1, 0x09, 0xb3, 0x7d, 0x1c, 0x9a, 0x39, 0x95, 0x28, 0x0f, 0xa7, 0xbe, 0x9c, 0xf3, 0x30,
	0xe9, 0x7f, 0x4d, 0x1c, 0xd6, 0x03, 0x11, 0x5d, 0x58, 0x83, 0xfb, 0x5c, 0x0c, 0x4c, 0x75, 0x25,
	0x33, 0xe9, 0xae, 0x84, 0x43, 0x45, 0x37, 0x1c, 0x43, 0x9d, 0x66, 0x95, 0x4e, 0xcd, 0xbb, 0xe8,
	0xb4, 0x23, 0x39, 0xa6, 0x34, 0x9a, 0x77, 0x13, 0x20, 0xf4, 0x02, 0x0a, 0x9c, 0x78, 0x27, 0xf2,
	0xde, 0xa6, 0x22, 0xe0, 0x7b, 0xa3, 0x3d, 0x2f, 0x49, 0xf7, 0x99, 0x73, 0xa9, 0xca, 0xe6, 0xef,
	0x52, 0x65, 0x9f, 0x00, 0xd0, 0x80, 0x93, 0x48, 0xd8, 0x21, 0xe3, 0x71, 0xa7, 0x59, 0xd4, 0x90,
	0x23, 0xc6, 0x51, 0x17, 0x4a, 0x01, 0x13, 0xea, 0x82, 0x2e, 0x7d, 0xb1, 0xa8, 0x72, 0xcb, 0xee,
	0x5d, 0x2c, 0x72, 0xa0, 0xd8, 0xa9, 0xc4, 0x00, 0x9a, 0xb5, 0x0a, 0xcc, 0xf7, 0xa0, 0x78, 0xc2,
	0x98, 0x08, 0x23, 0x1a, 0x08, 0x15, 0x67, 0x45, 0x6b, 0x04, 0x40, 0xbf, 0x85, 0x62, 0xdc, 0xd3,
	0xf1, 0x50, 0x45, 0xd8, 0x8d, 0xee, 0xe4, 0xaa, 0xcb, 0xdb, 0x6e, 0xee, 0x48, 0xb9, 0x56, 0x41,
	0xb7, 0x7a, 0x3c, 0x44, 0xdf, 0x65, 0xe0, 0xa1, 0x36, 0xaf, 0x3d, 0xe6, 0x86, 0xf3, 0xca, 0x96,
	0xc7, 0x77, 0x72, 0x43, 0xc5, 0x7a, 0x92, 0x33, 0x2e, 0x3b, 0x13, 0x50, 0x89, 0x3e, 0xa7, 0x3c,
	0x75, 0x9f, 0x53, 0x0d, 0x61, 0x69, 0x82, 0x44, 0xd9, 0xd6, 0x9e, 0x91, 0x8b, 0xc1, 0x7d, 0xec,
	0x8c, 0x5c, 0xa0, 0x6d, 0x98, 0xed, 0x63, 0xaf, 0x47, 0x54, 0x64, 0xde, 0xe8, 0x6a, 0x95, 0xe0,
	0x6b, 0xe9, 0xb5, 0x9f, 0x65, 0x7f, 0x95, 0xa9, 0x7e, 0x09, 0x8b, 0x63, 0xce, 0x9d, 0x94, 0x57,
	0xd4, 0xf2, 0x96, 0x93, 0xf2, 0x8a, 0x49, 0x06, 0x18, 0x1e, 0x5d, 0x69, 0xaa, 0x09, 0x8a, 0xaf,
	0xa5, 0x15, 0x4f, 0xf7, 0xf4, 0x93, 0x75, 0xac, 0x7d, 0x0e, 0x30, 0x72, 0xb7, 0x74, 0x1b, 0xb0,
	0x00, 0xa5, 0xcd, 0xa3, 0xa3, 0xc3, 0xc6, 0x41, 0xbb, 0x59, 0x3f, 0x68, 0x1b, 0x19, 0x09, 0xb0,
	0xc8, 0x49, 0x44, 0xf8, 0x69, 0xab, 0xe7, 0x38, 0x46, 0xb6, 0xf6, 0xef, 0x79, 0x58, 0x1e, 0x6f,
	0x01, 0xff, 0xcf, 0x92, 0xa0, 0xb8, 0x32, 0x09, 0xfe, 0xd5, 0x34, 0xad, 0xe9, 0xdd, 0xf3, 0x5f,
	0x70, 0x45, 0xfe, 0x7b, 0x39, 0xa5, 0x3a, 0xef, 0x7a, 0xea, 0xeb, 0x4c, 0x4a, 0x7d, 0x9b, 0x53,
	0x1a, 0xe3, 0x4f, 0x9e, 0xf5, 0xde, 0xc1, 0x0c, 0x71, 0xb7, 0xf0, 0xfd, 0x97, 0x2c, 0xcc, 0x27,
	0x4d, 0x81, 0x2a, 0x90, 0x65, 0x67, 0x4a, 0x70, 0xc1, 0xca, 0xb2, 0xb3, 0xe1, 0xe7, 0x77, 0x29,
	0x76, 0x26, 0xfd, 0xf9, 0x3d, 0x37, 0xfc, 0xfc, 0x8e, 0x5c, 0x58, 0x08, 0x3d, 0x1c, 0xd8, 0x1d,
	0x9b, 0x8b, 0x48, 0xde, 0x94, 0x2f, 0x54, 0xdc, 0x54, 0x36, 0xbe, 0xb8, 0xdd, 0x49, 0xac, 0x1d,
	0x79, 0x38, 0xd8, 0x6a, 0xc5, 0x3c, 0x2c, 0xd9, 0xa9, 0x8e, 0xa6, 0xf2, 0xde, 0x4d, 0xb9, 0x8d,
	0xbd, 0x2e, 0x53, 0xed, 0x76, 0xc1, 0x9a, 0xa3, 0x7c, 0xd3, 0xeb, 0xb2, 0xda, 0xb7, 0x50, 0x4e,
	0x2d, 0x44, 0x8f, 0xe0, 0x7e, 0x0a, 0x60, 0x37, 0x0e, 0x5e, 0x6d, 0xee, 0x37, 0x76, 0x8c, 0x7b,
	0xb2, 0xa1, 0x4b, 0xa3, 0xda, 0xd2, 0x30, 0x26, 0x2c, 0xa7, 0xa1, 0xd6, 0xe6, 0xc1, 0xce, 0x61,
	0xd3, 0xc8, 0xd6, 0xfe, 0x2b, 0x03, 0x8f, 0x5a, 0x44, 0xa8, 0xc7, 0xc3, 0xf8, 0x08, 0xbf, 0xea,
	0x61, 0x8f, 0x8a, 0x0b, 0x79, 0x93, 0xbc, 0x26, 0xcb, 0x51, 0xc8, 0xbf, 0xd1, 0xc4, 0xca, 0x80,
	0x95, 0x9b, 0xb4, 0x6e, 0x57, 0x0a, 0x5b, 0x9b, 0x04, 0x1e, 0xf0, 0xaf, 0x3d, 0x83, 0xa5, 0x09,
	0xf8, 0xb4, 0x3f, 0xe4, 0x21, 0xb7, 0x7f, 0xf8, 0xda, 0xc8, 0xd4, 0xde, 0x83, 0xea, 0x55, 0x62,
	0x78, 0x58, 0xfb, 0x12, 0xee, 0x6f, 0x33, 0x3f, 0xc4, 0x01, 0x1d, 0x96, 0x9d, 0x7a, 0x5f, 0x36,
	0xde, 0x1f, 0xc1, 0xc2, 0x68, 0xb7, 0xc9, 0x9f, 0x2f, 0x94, 0x87, 0x5b, 0x56, 0x6d, 0xf7, 0xff,
	0xcc, 0xc1, 0xe2, 0x01, 0xe9, 0x62, 0x41, 0xfb, 0x64, 0x97, 0x10, 0x57, 0xaf, 0x1e, 0x7f, 0xf6,
	0xfc, 0x1d, 0x14, 0xd4, 0xfd, 0x46, 0xa6, 0x51, 0xfd, 0xde, 0xfa, 0x97, 0xd7, 0xdb, 0x67, 0x8c,
	0xf1, 0x9a, 0xbc, 0x08, 0x0d, 0xd3, 0x67, 0xbe, 0xa7, 0x67, 0xe8, 0x6b, 0xfd, 0x4a, 0x33, 0xaa,
	0x18, 0x5f, 0x4e, 0xc3, 0xbb, 0x8d, 0x3b, 0x43, 0xd6, 0x73, 0x42, 0x4d, 0xd0, 0x9b, 0x51, 0x51,
	0x72, 0x58, 0xe0, 0x2a, 0x11, 0xfa, 0x0d, 0x76, 0x6f, 0x1a, 0x11, 0xb1, 0x89, 0xb7, 0x59, 0xe0,
	0x8e, 0x55, 0xa4, 0x18, 0x98, 0x14, 0x19, 0x60, 0x9f, 0x24, 0x0a, 0xcf, 0x5d, 0x44, 0x1e, 0x60,
	0x9f, 0x8c, 0x89, 0x8c, 0x81, 0xe8, 0x14, 0x8c, 0x5e, 0xe8, 0x62, 0x41, 0xd4, 0x8d, 0x4d, 0x9f,
	0xf6, 0x9c, 0x4a, 0xef, 0xbf, 0x99, 0xea, 0x90, 0x14, 0x2f, 0x95, 0xdb, 0x2b, 0xbd, 0xe1, 0x58,
	0xba, 0x4b, 0xf5, 0x33, 0x98, 0x4f, 0x1e, 0xe1, 0x84, 0x6c, 0x9b, 0xca, 0x7e, 0xb9, 0x64, 0xfa,
	0xfc, 0x35, 0x94, 0x12, 0x47, 0x74, 0xab, 0xa5, 0x9b, 0xc3, 0x5c, 0x9f, 0x34, 0xfd, 0x75, 0xb9,
	0xf7, 0x0a, 0x16, 0x49, 0x53, 0xde, 0x86, 0x45, 0xed, 0x00, 0x60, 0x64, 0x1a, 0x19, 0xae, 0x8d,
	0xa0, 0x8f, 0x3d, 0xea, 0x1a, 0xf7, 0x50, 0x01, 0x66, 0xa4, 0x5d, 0x8c, 0x8c, 0x0c, 0xdc, 0x36,
	0xee, 0x18, 0x59, 0x99, 0xc0, 0x13, 0x3a, 0x1b, 0xb9, 0x04, 0x40, 0x6a, 0x60, 0xcc, 0xd4, 0xfe,
	0x75, 0x16, 0x0c, 0xb9, 0x2a, 0x79, 0x02, 0xe8, 0xdb, 0x44, 0xa0, 0x65, 0x6e, 0x1a, 0x0c, 0x97,
	0xb9, 0x5c, 0x11, 0x67, 0xaf, 0x47, 0x71, 0xa6, 0x63, 0xf8, 0x37, 0x53, 0xb0, 0x9e, 0x14, 0x66,
	0xe1, 0x84, 0x30, 0xd3, 0x91, 0xbc, 0x3b, 0x85, 0x84, 0x9b, 0x44, 0x59, 0x38, 0x21, 0xca, 0x66,
	0xee, 0x2a, 0xf1, 0xfb, 0x82, 0xec, 0x87, 0xed, 0xfa, 0x9b, 0xf0, 0x68, 0xf0, 0x69, 0x8e, 0x0a,
	0x8f, 0xb4, 0xd9, 0x19, 0x09, 0xe8, 0x1f, 0xc9, 0xb0, 0x5a, 0x38, 0xa3, 0x6a, 0xe1, 0x50, 0x57,
	0xb6, 0x22, 0x02, 0x77, 0xf5, 0x97, 0xb9, 0xa2, 0xa5, 0xc6, 0xb5, 0xdf, 0xc1, 0xe3, 0x23, 0x0f,
	0x5f, 0xf8, 0x58, 0x90, 0x86, 0x7a, 0x1c, 0x52, 0x77, 0x4c, 0x9f, 0x0a, 0xcd, 0xe4, 0x72, 0x79,
	0xce, 0xa4, 0xcb, 0xf3, 0x13, 0x00, 0xf5, 0xa5, 0xcd, 0x16, 0xd4, 0x1f, 0xe8, 0x57, 0x54, 0x90,
	0x36, 0xf5, 0x49, 0xad, 0x09, 0xd5, 0xb1, 0x0b, 0xac, 0x45, 0x30, 0x57, 0xbf, 0xa2, 0x9b, 0x50,
	0xce, 0xd2, 0xd2, 0xb2, 0x97, 0xa4, 0xd5, 0x3e, 0x85, 0xc7, 0x57, 0xb2, 0xe3, 0x21, 0x7a, 0x00,
	0x73, 0x91, 0x9a, 0xc5, 0x2c, 0xe3, 0xd9, 0x53, 0x0f, 0x96, 0x27, 0xfd, 0xd6, 0x2e, 0x5d, 0xd9,
	0x1f, 0xc2, 0x92, 0x45, 0xba, 0xfb, 0x84, 0xf3, 0x5f, 0x6e, 0xbc, 0x64, 0xbd, 0xc8, 0xae, 0xbf,
	0x0d, 0xed, 0x4d, 0x23, 0x33, 0x19, 0xb1, 0x65, 0x64, 0xd1, 0x7d, 0x58, 0xb4, 0x48, 0xb7, 0xc9,
	0x22, 0x32, 0x42, 0x18, 0xb9, 0xa7, 0x6b, 0x60, 0x5c, 0xfe, 0xb0, 0x22, 0xf3, 0xd0, 0xae, 0x55,
	0xaf, 0x1b, 0xf7, 0x90, 0x01, 0xf3, 0xad, 0x46, 0xf3, 0x68, 0xbf, 0x6e, 0x37, 0x1b, 0x07, 0x87,
	0x96, 0x91, 0x79, 0xfa, 0x0c, 0x16, 0xc7, 0x7e, 0x80, 0x87, 0x00, 0xe6, 0x5a, 0xdf, 0xb4, 0xda,
	0xf5, 0xa6, 0xfe, 0xd2, 0xbb, 0x7d, 0xdc, 0x6a, 0x1f, 0x36, 0x8d, 0xcc, 0xd3, 0x17, 0xb0, 0x38,
	0xf6, 0xc4, 0x8d, 0xe6, 0xa1, 0x60, 0x31, 0xe6, 0x37, 0x99, 0x4b, 0x8c, 0x7b, 0x68, 0x11, 0xca,
	0x92, 0x97, 0x74, 0x4b, 0xaa, 0x5e, 0x91, 0x33, 0x4f, 0xbf, 0x02, 0xe3, 0xf2, 0x2b, 0xb6, 0xdc,
	0xfc, 0x01, 0x53, 0x07, 0xad, 0x45, 0xb4, 0x68, 0xd0, 0xf5, 0x88, 0x91, 0x91, 0xe3, 0x1d, 0xd6,
	0xeb, 0x78, 0xc4, 0xc8, 0x4a, 0xa2, 0xd7, 0x98, 0x0a, 0x1a, 0x74, 0x8d, 0x9c, 0x9c, 0xb4, 0x04,
	0x8e, 0x04, 0x71, 0x8d, 0x99, 0xa7, 0xff, 0x9d, 0x85, 0xe2, 0xf0, 0x61, 0x57, 0xee, 0xef, 0x80,
	0x05, 0x44, 0x9b, 0x51, 0x7f, 0x70, 0x7e, 0x4d, 0xc5, 0xa9, 0xbc, 0x9f, 0x1c, 0x9e, 0x07, 0x2a,
	0x01, 0xdf, 0x87, 0xc5, 0xed, 0x53, 0x2c, 0xd2, 0xe0, 0xac, 0xa4, 0xdf, 0xd5, 0xcf, 0xa1, 0x9c,
	0x44, 0x8d, 0x41, 0xb3, 0xa4, 0xa5, 0xc5, 0xcf, 0x86, 0xc6, 0x8c, 0xec, 0x46, 0x9b, 0x62, 0x12,
	0xdd, 0x2c, 0x7a, 0x00, 0xa8, 0x29, 0x86, 0xef, 0xa4, 0x03, 0xf8, 0x9c, 0xec, 0x47, 0x9b, 0xa2,
	0x89, 0x83, 0x0b, 0xb9, 0x80, 0x8f, 0x56, 0xe4, 0xd1, 0x07, 0xf0, 0x24, 0x81, 0xd1, 0x5c, 0x63,
	0xac, 0xd6, 0xaa, 0x80, 0x7e, 0x02, 0xd5, 0xa6, 0xd8, 0xeb, 0x11, 0xce, 0xbf, 0x61, 0xbd, 0x7d,
	0x7a, 0x46, 0x52, 0xf8, 0x22, 0x7a, 0x1f, 0x1e, 0x37, 0xa5, 0x1f, 0x92, 0x40, 0x48, 0x6c, 0x1b,
	0x77, 0x62, 0x82, 0x1d, 0xd6, 0xdb, 0xa1, 0x06, 0xa0, 0xf7, 0xc0, 0x6c, 0x8a, 0x97, 0x4c, 0xc8,
	0x93, 0x78, 0xc9, 0x44, 0x0a, 0x5b, 0x92, 0x1d, 0x74, 0x53, 0xec, 0x46, 0x94, 0x04, 0xee, 0xe1,
	0x89, 0xfe, 0x6b, 0xcc, 0x23, 0x04, 0x95, 0x01, 0x53, 0xad, 0x94, 0x51, 0x7e, 0xfa, 0x02, 0x40,
	0x77, 0xf6, 0xd2, 0x91, 0xf5, 0xe5, 0x43, 0x8e, 0xec, 0xd8, 0xda, 0xca, 0x05, 0x15, 0x20, 0x96,
	0xd5, 0x66, 0xa1, 0x91, 0xd9, 0xf8, 0xcf, 0x3c, 0x2c, 0xee, 0x91, 0x20, 0x1d, 0x28, 0xe8, 0xef,
	0x33, 0x70, 0x7f, 0xe2, 0x73, 0x12, 0xfa, 0x6c, 0xfa, 0xdf, 0x2a, 0x55, 0x3f, 0xbf, 0xc3, 0x1b,
	0x16, 0xfa, 0x2e, 0x23, 0x37, 0xe0, 0x60, 0xcf, 0x4b, 0x84, 0xe7, 0x9f, 0x4e, 0x9d, 0x7f, 0xc8,
	0xc0, 0x83, 0xc9, 0x5d, 0x3d, 0xfa, 0xfc, 0x0e, 0xd7, 0x8e, 0xea, 0x17, 0xd3, 0x2f, 0xe6, 0x21,
	0x62, 0x00, 0xa3, 0x9f, 0x42, 0xa3, 0xf5, 0x5b, 0xfe, 0x0a, 0xbb, 0xfa, 0xb3, 0xdb, 0x2d, 0xe0,
	0x21, 0x8a, 0xa4, 0x9b, 0x0d, 0x7f, 0x43, 0x8e, 0x6e, 0xc0, 0x20, 0xfd, 0xb3, 0xf4, 0xea, 0xcf,
	0x6f, 0xb9, 0x82, 0x87, 0xe8, 0x6f, 0x33, 0x60, 0x5c, 0x7e, 0xd1, 0x40, 0x9f, 0x4e, 0xf5, 0xf1,
	0xb4, 0xfa, 0x8b, 0xe9, 0x1e, 0x4f, 0xd0, 0x3f, 0x65, 0xe0, 0xe1, 0x15, 0xf5, 0x05, 0x7d, 0x31,
	0x85, 0x5f, 0x0d, 0x2b, 0x5d, 0xf5, 0x2f, 0xee, 0xb0, 0x9a, 0x87, 0x5b, 0x3b, 0xdf, 0x6e, 0x75,
	0x99, 0x87, 0x83, 0xee, 0xda, 0xa7, 0x1b, 0x42, 0xac, 0x39, 0xcc, 0x5f, 0x57, 0xff, 0x11, 0xe0,
	0x30, 0x6f, 0x9d, 0x93, 0xa8, 0x4f, 0x1d, 0xc2, 0xaf, 0xfd, 0xe7, 0x89, 0xce, 0x9c, 0x5a, 0xf3,
	0xfc, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x10, 0x13, 0x37, 0x67, 0x76, 0x31, 0x00, 0x00,
}
