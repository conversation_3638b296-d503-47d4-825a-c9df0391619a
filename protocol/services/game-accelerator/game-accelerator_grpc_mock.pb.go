// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/game-accelerator/game-accelerator.proto

package game_accelerator

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockGameAcceleratorClient is a mock of GameAcceleratorClient interface.
type MockGameAcceleratorClient struct {
	ctrl     *gomock.Controller
	recorder *MockGameAcceleratorClientMockRecorder
}

// MockGameAcceleratorClientMockRecorder is the mock recorder for MockGameAcceleratorClient.
type MockGameAcceleratorClientMockRecorder struct {
	mock *MockGameAcceleratorClient
}

// NewMockGameAcceleratorClient creates a new mock instance.
func NewMockGameAcceleratorClient(ctrl *gomock.Controller) *MockGameAcceleratorClient {
	mock := &MockGameAcceleratorClient{ctrl: ctrl}
	mock.recorder = &MockGameAcceleratorClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameAcceleratorClient) EXPECT() *MockGameAcceleratorClientMockRecorder {
	return m.recorder
}

// AddUserAcceleratorAuthorize mocks base method.
func (m *MockGameAcceleratorClient) AddUserAcceleratorAuthorize(ctx context.Context, in *AddUserAcceleratorAuthorizeReq, opts ...grpc.CallOption) (*AddUserAcceleratorAuthorizeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserAcceleratorAuthorize", varargs...)
	ret0, _ := ret[0].(*AddUserAcceleratorAuthorizeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserAcceleratorAuthorize indicates an expected call of AddUserAcceleratorAuthorize.
func (mr *MockGameAcceleratorClientMockRecorder) AddUserAcceleratorAuthorize(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserAcceleratorAuthorize", reflect.TypeOf((*MockGameAcceleratorClient)(nil).AddUserAcceleratorAuthorize), varargs...)
}

// AdminAddUserAccelerator mocks base method.
func (m *MockGameAcceleratorClient) AdminAddUserAccelerator(ctx context.Context, in *AdminAddUserAcceleratorReq, opts ...grpc.CallOption) (*AdminAddUserAcceleratorResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AdminAddUserAccelerator", varargs...)
	ret0, _ := ret[0].(*AdminAddUserAcceleratorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminAddUserAccelerator indicates an expected call of AdminAddUserAccelerator.
func (mr *MockGameAcceleratorClientMockRecorder) AdminAddUserAccelerator(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminAddUserAccelerator", reflect.TypeOf((*MockGameAcceleratorClient)(nil).AdminAddUserAccelerator), varargs...)
}

// BanUserUseAccelerator mocks base method.
func (m *MockGameAcceleratorClient) BanUserUseAccelerator(ctx context.Context, in *BanUserUseAcceleratorReq, opts ...grpc.CallOption) (*BanUserUseAcceleratorResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BanUserUseAccelerator", varargs...)
	ret0, _ := ret[0].(*BanUserUseAcceleratorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BanUserUseAccelerator indicates an expected call of BanUserUseAccelerator.
func (mr *MockGameAcceleratorClientMockRecorder) BanUserUseAccelerator(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BanUserUseAccelerator", reflect.TypeOf((*MockGameAcceleratorClient)(nil).BanUserUseAccelerator), varargs...)
}

// GetAccelerateEntrance mocks base method.
func (m *MockGameAcceleratorClient) GetAccelerateEntrance(ctx context.Context, in *GetAccelerateEntranceRequest, opts ...grpc.CallOption) (*GetAccelerateEntranceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccelerateEntrance", varargs...)
	ret0, _ := ret[0].(*GetAccelerateEntranceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccelerateEntrance indicates an expected call of GetAccelerateEntrance.
func (mr *MockGameAcceleratorClientMockRecorder) GetAccelerateEntrance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccelerateEntrance", reflect.TypeOf((*MockGameAcceleratorClient)(nil).GetAccelerateEntrance), varargs...)
}

// GetAcceleratorOrder mocks base method.
func (m *MockGameAcceleratorClient) GetAcceleratorOrder(ctx context.Context, in *GetAcceleratorOrderReq, opts ...grpc.CallOption) (*GetAcceleratorOrderResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAcceleratorOrder", varargs...)
	ret0, _ := ret[0].(*GetAcceleratorOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcceleratorOrder indicates an expected call of GetAcceleratorOrder.
func (mr *MockGameAcceleratorClientMockRecorder) GetAcceleratorOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcceleratorOrder", reflect.TypeOf((*MockGameAcceleratorClient)(nil).GetAcceleratorOrder), varargs...)
}

// GetAcceleratorToken mocks base method.
func (m *MockGameAcceleratorClient) GetAcceleratorToken(ctx context.Context, in *GetAcceleratorTokenReq, opts ...grpc.CallOption) (*GetAcceleratorTokenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAcceleratorToken", varargs...)
	ret0, _ := ret[0].(*GetAcceleratorTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcceleratorToken indicates an expected call of GetAcceleratorToken.
func (mr *MockGameAcceleratorClientMockRecorder) GetAcceleratorToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcceleratorToken", reflect.TypeOf((*MockGameAcceleratorClient)(nil).GetAcceleratorToken), varargs...)
}

// GetAcceleratorUserList mocks base method.
func (m *MockGameAcceleratorClient) GetAcceleratorUserList(ctx context.Context, in *GetAcceleratorUserListReq, opts ...grpc.CallOption) (*GetAcceleratorUserListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAcceleratorUserList", varargs...)
	ret0, _ := ret[0].(*GetAcceleratorUserListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcceleratorUserList indicates an expected call of GetAcceleratorUserList.
func (mr *MockGameAcceleratorClientMockRecorder) GetAcceleratorUserList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcceleratorUserList", reflect.TypeOf((*MockGameAcceleratorClient)(nil).GetAcceleratorUserList), varargs...)
}

// GetGames mocks base method.
func (m *MockGameAcceleratorClient) GetGames(ctx context.Context, in *GetGamesRequest, opts ...grpc.CallOption) (*GetGamesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGames", varargs...)
	ret0, _ := ret[0].(*GetGamesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGames indicates an expected call of GetGames.
func (mr *MockGameAcceleratorClientMockRecorder) GetGames(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGames", reflect.TypeOf((*MockGameAcceleratorClient)(nil).GetGames), varargs...)
}

// GetNewbieOrderByUids mocks base method.
func (m *MockGameAcceleratorClient) GetNewbieOrderByUids(ctx context.Context, in *GetNewbieOrderByUidsReq, opts ...grpc.CallOption) (*GetNewbieOrderByUidsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewbieOrderByUids", varargs...)
	ret0, _ := ret[0].(*GetNewbieOrderByUidsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieOrderByUids indicates an expected call of GetNewbieOrderByUids.
func (mr *MockGameAcceleratorClientMockRecorder) GetNewbieOrderByUids(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieOrderByUids", reflect.TypeOf((*MockGameAcceleratorClient)(nil).GetNewbieOrderByUids), varargs...)
}

// GetPreviewExpireTime mocks base method.
func (m *MockGameAcceleratorClient) GetPreviewExpireTime(ctx context.Context, in *GetPreviewExpireTimeReq, opts ...grpc.CallOption) (*GetPreviewExpireTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPreviewExpireTime", varargs...)
	ret0, _ := ret[0].(*GetPreviewExpireTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreviewExpireTime indicates an expected call of GetPreviewExpireTime.
func (mr *MockGameAcceleratorClientMockRecorder) GetPreviewExpireTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreviewExpireTime", reflect.TypeOf((*MockGameAcceleratorClient)(nil).GetPreviewExpireTime), varargs...)
}

// GetUserAcceleratorInfo mocks base method.
func (m *MockGameAcceleratorClient) GetUserAcceleratorInfo(ctx context.Context, in *GetUserAcceleratorInfoReq, opts ...grpc.CallOption) (*GetUserAcceleratorInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAcceleratorInfo", varargs...)
	ret0, _ := ret[0].(*GetUserAcceleratorInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAcceleratorInfo indicates an expected call of GetUserAcceleratorInfo.
func (mr *MockGameAcceleratorClientMockRecorder) GetUserAcceleratorInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAcceleratorInfo", reflect.TypeOf((*MockGameAcceleratorClient)(nil).GetUserAcceleratorInfo), varargs...)
}

// GetUserAcceleratorLog mocks base method.
func (m *MockGameAcceleratorClient) GetUserAcceleratorLog(ctx context.Context, in *GetUserAcceleratorLogReq, opts ...grpc.CallOption) (*GetUserAcceleratorLogResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAcceleratorLog", varargs...)
	ret0, _ := ret[0].(*GetUserAcceleratorLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAcceleratorLog indicates an expected call of GetUserAcceleratorLog.
func (mr *MockGameAcceleratorClientMockRecorder) GetUserAcceleratorLog(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAcceleratorLog", reflect.TypeOf((*MockGameAcceleratorClient)(nil).GetUserAcceleratorLog), varargs...)
}

// SearchAccelerateGame mocks base method.
func (m *MockGameAcceleratorClient) SearchAccelerateGame(ctx context.Context, in *SearchAccelerateGameRequest, opts ...grpc.CallOption) (*SearchAccelerateGameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchAccelerateGame", varargs...)
	ret0, _ := ret[0].(*SearchAccelerateGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAccelerateGame indicates an expected call of SearchAccelerateGame.
func (mr *MockGameAcceleratorClientMockRecorder) SearchAccelerateGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAccelerateGame", reflect.TypeOf((*MockGameAcceleratorClient)(nil).SearchAccelerateGame), varargs...)
}

// SyncAccelerateGameList mocks base method.
func (m *MockGameAcceleratorClient) SyncAccelerateGameList(ctx context.Context, in *SyncAccelerateGameListRequest, opts ...grpc.CallOption) (*SyncAccelerateGameListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SyncAccelerateGameList", varargs...)
	ret0, _ := ret[0].(*SyncAccelerateGameListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncAccelerateGameList indicates an expected call of SyncAccelerateGameList.
func (mr *MockGameAcceleratorClientMockRecorder) SyncAccelerateGameList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncAccelerateGameList", reflect.TypeOf((*MockGameAcceleratorClient)(nil).SyncAccelerateGameList), varargs...)
}

// UpdateAccelerateEntrance mocks base method.
func (m *MockGameAcceleratorClient) UpdateAccelerateEntrance(ctx context.Context, in *UpdateAccelerateEntranceRequest, opts ...grpc.CallOption) (*UpdateAccelerateEntranceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAccelerateEntrance", varargs...)
	ret0, _ := ret[0].(*UpdateAccelerateEntranceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAccelerateEntrance indicates an expected call of UpdateAccelerateEntrance.
func (mr *MockGameAcceleratorClientMockRecorder) UpdateAccelerateEntrance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccelerateEntrance", reflect.TypeOf((*MockGameAcceleratorClient)(nil).UpdateAccelerateEntrance), varargs...)
}

// UpdateGame mocks base method.
func (m *MockGameAcceleratorClient) UpdateGame(ctx context.Context, in *UpdateGameRequest, opts ...grpc.CallOption) (*UpdateGameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateGame", varargs...)
	ret0, _ := ret[0].(*UpdateGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGame indicates an expected call of UpdateGame.
func (mr *MockGameAcceleratorClientMockRecorder) UpdateGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGame", reflect.TypeOf((*MockGameAcceleratorClient)(nil).UpdateGame), varargs...)
}

// MockGameAcceleratorServer is a mock of GameAcceleratorServer interface.
type MockGameAcceleratorServer struct {
	ctrl     *gomock.Controller
	recorder *MockGameAcceleratorServerMockRecorder
}

// MockGameAcceleratorServerMockRecorder is the mock recorder for MockGameAcceleratorServer.
type MockGameAcceleratorServerMockRecorder struct {
	mock *MockGameAcceleratorServer
}

// NewMockGameAcceleratorServer creates a new mock instance.
func NewMockGameAcceleratorServer(ctrl *gomock.Controller) *MockGameAcceleratorServer {
	mock := &MockGameAcceleratorServer{ctrl: ctrl}
	mock.recorder = &MockGameAcceleratorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameAcceleratorServer) EXPECT() *MockGameAcceleratorServerMockRecorder {
	return m.recorder
}

// AddUserAcceleratorAuthorize mocks base method.
func (m *MockGameAcceleratorServer) AddUserAcceleratorAuthorize(ctx context.Context, in *AddUserAcceleratorAuthorizeReq) (*AddUserAcceleratorAuthorizeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserAcceleratorAuthorize", ctx, in)
	ret0, _ := ret[0].(*AddUserAcceleratorAuthorizeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserAcceleratorAuthorize indicates an expected call of AddUserAcceleratorAuthorize.
func (mr *MockGameAcceleratorServerMockRecorder) AddUserAcceleratorAuthorize(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserAcceleratorAuthorize", reflect.TypeOf((*MockGameAcceleratorServer)(nil).AddUserAcceleratorAuthorize), ctx, in)
}

// AdminAddUserAccelerator mocks base method.
func (m *MockGameAcceleratorServer) AdminAddUserAccelerator(ctx context.Context, in *AdminAddUserAcceleratorReq) (*AdminAddUserAcceleratorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdminAddUserAccelerator", ctx, in)
	ret0, _ := ret[0].(*AdminAddUserAcceleratorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminAddUserAccelerator indicates an expected call of AdminAddUserAccelerator.
func (mr *MockGameAcceleratorServerMockRecorder) AdminAddUserAccelerator(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminAddUserAccelerator", reflect.TypeOf((*MockGameAcceleratorServer)(nil).AdminAddUserAccelerator), ctx, in)
}

// BanUserUseAccelerator mocks base method.
func (m *MockGameAcceleratorServer) BanUserUseAccelerator(ctx context.Context, in *BanUserUseAcceleratorReq) (*BanUserUseAcceleratorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BanUserUseAccelerator", ctx, in)
	ret0, _ := ret[0].(*BanUserUseAcceleratorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BanUserUseAccelerator indicates an expected call of BanUserUseAccelerator.
func (mr *MockGameAcceleratorServerMockRecorder) BanUserUseAccelerator(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BanUserUseAccelerator", reflect.TypeOf((*MockGameAcceleratorServer)(nil).BanUserUseAccelerator), ctx, in)
}

// GetAccelerateEntrance mocks base method.
func (m *MockGameAcceleratorServer) GetAccelerateEntrance(ctx context.Context, in *GetAccelerateEntranceRequest) (*GetAccelerateEntranceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccelerateEntrance", ctx, in)
	ret0, _ := ret[0].(*GetAccelerateEntranceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccelerateEntrance indicates an expected call of GetAccelerateEntrance.
func (mr *MockGameAcceleratorServerMockRecorder) GetAccelerateEntrance(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccelerateEntrance", reflect.TypeOf((*MockGameAcceleratorServer)(nil).GetAccelerateEntrance), ctx, in)
}

// GetAcceleratorOrder mocks base method.
func (m *MockGameAcceleratorServer) GetAcceleratorOrder(ctx context.Context, in *GetAcceleratorOrderReq) (*GetAcceleratorOrderResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcceleratorOrder", ctx, in)
	ret0, _ := ret[0].(*GetAcceleratorOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcceleratorOrder indicates an expected call of GetAcceleratorOrder.
func (mr *MockGameAcceleratorServerMockRecorder) GetAcceleratorOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcceleratorOrder", reflect.TypeOf((*MockGameAcceleratorServer)(nil).GetAcceleratorOrder), ctx, in)
}

// GetAcceleratorToken mocks base method.
func (m *MockGameAcceleratorServer) GetAcceleratorToken(ctx context.Context, in *GetAcceleratorTokenReq) (*GetAcceleratorTokenResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcceleratorToken", ctx, in)
	ret0, _ := ret[0].(*GetAcceleratorTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcceleratorToken indicates an expected call of GetAcceleratorToken.
func (mr *MockGameAcceleratorServerMockRecorder) GetAcceleratorToken(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcceleratorToken", reflect.TypeOf((*MockGameAcceleratorServer)(nil).GetAcceleratorToken), ctx, in)
}

// GetAcceleratorUserList mocks base method.
func (m *MockGameAcceleratorServer) GetAcceleratorUserList(ctx context.Context, in *GetAcceleratorUserListReq) (*GetAcceleratorUserListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcceleratorUserList", ctx, in)
	ret0, _ := ret[0].(*GetAcceleratorUserListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcceleratorUserList indicates an expected call of GetAcceleratorUserList.
func (mr *MockGameAcceleratorServerMockRecorder) GetAcceleratorUserList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcceleratorUserList", reflect.TypeOf((*MockGameAcceleratorServer)(nil).GetAcceleratorUserList), ctx, in)
}

// GetGames mocks base method.
func (m *MockGameAcceleratorServer) GetGames(ctx context.Context, in *GetGamesRequest) (*GetGamesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGames", ctx, in)
	ret0, _ := ret[0].(*GetGamesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGames indicates an expected call of GetGames.
func (mr *MockGameAcceleratorServerMockRecorder) GetGames(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGames", reflect.TypeOf((*MockGameAcceleratorServer)(nil).GetGames), ctx, in)
}

// GetNewbieOrderByUids mocks base method.
func (m *MockGameAcceleratorServer) GetNewbieOrderByUids(ctx context.Context, in *GetNewbieOrderByUidsReq) (*GetNewbieOrderByUidsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewbieOrderByUids", ctx, in)
	ret0, _ := ret[0].(*GetNewbieOrderByUidsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieOrderByUids indicates an expected call of GetNewbieOrderByUids.
func (mr *MockGameAcceleratorServerMockRecorder) GetNewbieOrderByUids(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieOrderByUids", reflect.TypeOf((*MockGameAcceleratorServer)(nil).GetNewbieOrderByUids), ctx, in)
}

// GetPreviewExpireTime mocks base method.
func (m *MockGameAcceleratorServer) GetPreviewExpireTime(ctx context.Context, in *GetPreviewExpireTimeReq) (*GetPreviewExpireTimeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreviewExpireTime", ctx, in)
	ret0, _ := ret[0].(*GetPreviewExpireTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreviewExpireTime indicates an expected call of GetPreviewExpireTime.
func (mr *MockGameAcceleratorServerMockRecorder) GetPreviewExpireTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreviewExpireTime", reflect.TypeOf((*MockGameAcceleratorServer)(nil).GetPreviewExpireTime), ctx, in)
}

// GetUserAcceleratorInfo mocks base method.
func (m *MockGameAcceleratorServer) GetUserAcceleratorInfo(ctx context.Context, in *GetUserAcceleratorInfoReq) (*GetUserAcceleratorInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAcceleratorInfo", ctx, in)
	ret0, _ := ret[0].(*GetUserAcceleratorInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAcceleratorInfo indicates an expected call of GetUserAcceleratorInfo.
func (mr *MockGameAcceleratorServerMockRecorder) GetUserAcceleratorInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAcceleratorInfo", reflect.TypeOf((*MockGameAcceleratorServer)(nil).GetUserAcceleratorInfo), ctx, in)
}

// GetUserAcceleratorLog mocks base method.
func (m *MockGameAcceleratorServer) GetUserAcceleratorLog(ctx context.Context, in *GetUserAcceleratorLogReq) (*GetUserAcceleratorLogResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAcceleratorLog", ctx, in)
	ret0, _ := ret[0].(*GetUserAcceleratorLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAcceleratorLog indicates an expected call of GetUserAcceleratorLog.
func (mr *MockGameAcceleratorServerMockRecorder) GetUserAcceleratorLog(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAcceleratorLog", reflect.TypeOf((*MockGameAcceleratorServer)(nil).GetUserAcceleratorLog), ctx, in)
}

// SearchAccelerateGame mocks base method.
func (m *MockGameAcceleratorServer) SearchAccelerateGame(ctx context.Context, in *SearchAccelerateGameRequest) (*SearchAccelerateGameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAccelerateGame", ctx, in)
	ret0, _ := ret[0].(*SearchAccelerateGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAccelerateGame indicates an expected call of SearchAccelerateGame.
func (mr *MockGameAcceleratorServerMockRecorder) SearchAccelerateGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAccelerateGame", reflect.TypeOf((*MockGameAcceleratorServer)(nil).SearchAccelerateGame), ctx, in)
}

// SyncAccelerateGameList mocks base method.
func (m *MockGameAcceleratorServer) SyncAccelerateGameList(ctx context.Context, in *SyncAccelerateGameListRequest) (*SyncAccelerateGameListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncAccelerateGameList", ctx, in)
	ret0, _ := ret[0].(*SyncAccelerateGameListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncAccelerateGameList indicates an expected call of SyncAccelerateGameList.
func (mr *MockGameAcceleratorServerMockRecorder) SyncAccelerateGameList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncAccelerateGameList", reflect.TypeOf((*MockGameAcceleratorServer)(nil).SyncAccelerateGameList), ctx, in)
}

// UpdateAccelerateEntrance mocks base method.
func (m *MockGameAcceleratorServer) UpdateAccelerateEntrance(ctx context.Context, in *UpdateAccelerateEntranceRequest) (*UpdateAccelerateEntranceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccelerateEntrance", ctx, in)
	ret0, _ := ret[0].(*UpdateAccelerateEntranceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAccelerateEntrance indicates an expected call of UpdateAccelerateEntrance.
func (mr *MockGameAcceleratorServerMockRecorder) UpdateAccelerateEntrance(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccelerateEntrance", reflect.TypeOf((*MockGameAcceleratorServer)(nil).UpdateAccelerateEntrance), ctx, in)
}

// UpdateGame mocks base method.
func (m *MockGameAcceleratorServer) UpdateGame(ctx context.Context, in *UpdateGameRequest) (*UpdateGameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGame", ctx, in)
	ret0, _ := ret[0].(*UpdateGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGame indicates an expected call of UpdateGame.
func (mr *MockGameAcceleratorServerMockRecorder) UpdateGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGame", reflect.TypeOf((*MockGameAcceleratorServer)(nil).UpdateGame), ctx, in)
}
