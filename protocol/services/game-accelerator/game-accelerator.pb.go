// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/game-accelerator/game-accelerator.proto

package game_accelerator // import "golang.52tt.com/protocol/services/game-accelerator"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EntranceShowType int32

const (
	EntranceShowType_ENTRANCE_SHOW_TYPE_UNSPECIFIED EntranceShowType = 0
	EntranceShowType_ENTRANCE_SHOW_TYPE_ALL         EntranceShowType = 1
	EntranceShowType_ENTRANCE_SHOW_TYPE_CROWD       EntranceShowType = 2
)

var EntranceShowType_name = map[int32]string{
	0: "ENTRANCE_SHOW_TYPE_UNSPECIFIED",
	1: "ENTRANCE_SHOW_TYPE_ALL",
	2: "ENTRANCE_SHOW_TYPE_CROWD",
}
var EntranceShowType_value = map[string]int32{
	"ENTRANCE_SHOW_TYPE_UNSPECIFIED": 0,
	"ENTRANCE_SHOW_TYPE_ALL":         1,
	"ENTRANCE_SHOW_TYPE_CROWD":       2,
}

func (x EntranceShowType) String() string {
	return proto.EnumName(EntranceShowType_name, int32(x))
}
func (EntranceShowType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{0}
}

type AcceleratorGameState int32

const (
	AcceleratorGameState_ACCELERATOR_GAME_STATE_UNSPECIAL AcceleratorGameState = 0
	AcceleratorGameState_ACCELERATOR_GAME_STATE_NORMAL    AcceleratorGameState = 1
	AcceleratorGameState_ACCELERATOR_GAME_STATE_BANNED    AcceleratorGameState = 2
	AcceleratorGameState_ACCELERATOR_GAME_STATE_OFF_SHELF AcceleratorGameState = 3
)

var AcceleratorGameState_name = map[int32]string{
	0: "ACCELERATOR_GAME_STATE_UNSPECIAL",
	1: "ACCELERATOR_GAME_STATE_NORMAL",
	2: "ACCELERATOR_GAME_STATE_BANNED",
	3: "ACCELERATOR_GAME_STATE_OFF_SHELF",
}
var AcceleratorGameState_value = map[string]int32{
	"ACCELERATOR_GAME_STATE_UNSPECIAL": 0,
	"ACCELERATOR_GAME_STATE_NORMAL":    1,
	"ACCELERATOR_GAME_STATE_BANNED":    2,
	"ACCELERATOR_GAME_STATE_OFF_SHELF": 3,
}

func (x AcceleratorGameState) String() string {
	return proto.EnumName(AcceleratorGameState_name, int32(x))
}
func (AcceleratorGameState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{1}
}

type AcceleratorLabel int32

const (
	AcceleratorLabel_LABEL_UNSPECIAL AcceleratorLabel = 0
	AcceleratorLabel_LABEL_RECOMMEND AcceleratorLabel = 1
	AcceleratorLabel_LABEL_HOT       AcceleratorLabel = 2
)

var AcceleratorLabel_name = map[int32]string{
	0: "LABEL_UNSPECIAL",
	1: "LABEL_RECOMMEND",
	2: "LABEL_HOT",
}
var AcceleratorLabel_value = map[string]int32{
	"LABEL_UNSPECIAL": 0,
	"LABEL_RECOMMEND": 1,
	"LABEL_HOT":       2,
}

func (x AcceleratorLabel) String() string {
	return proto.EnumName(AcceleratorLabel_name, int32(x))
}
func (AcceleratorLabel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{2}
}

type AcceleratorOrderStatus int32

const (
	AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_UNSPECIFIED AcceleratorOrderStatus = 0
	AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_PENDING     AcceleratorOrderStatus = 1
	AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_SUCCESS     AcceleratorOrderStatus = 2
	AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_FAIL        AcceleratorOrderStatus = 3
	AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_INVALID     AcceleratorOrderStatus = 4
)

var AcceleratorOrderStatus_name = map[int32]string{
	0: "ACCELERATOR_ORDER_STATUS_UNSPECIFIED",
	1: "ACCELERATOR_ORDER_STATUS_PENDING",
	2: "ACCELERATOR_ORDER_STATUS_SUCCESS",
	3: "ACCELERATOR_ORDER_STATUS_FAIL",
	4: "ACCELERATOR_ORDER_STATUS_INVALID",
}
var AcceleratorOrderStatus_value = map[string]int32{
	"ACCELERATOR_ORDER_STATUS_UNSPECIFIED": 0,
	"ACCELERATOR_ORDER_STATUS_PENDING":     1,
	"ACCELERATOR_ORDER_STATUS_SUCCESS":     2,
	"ACCELERATOR_ORDER_STATUS_FAIL":        3,
	"ACCELERATOR_ORDER_STATUS_INVALID":     4,
}

func (x AcceleratorOrderStatus) String() string {
	return proto.EnumName(AcceleratorOrderStatus_name, int32(x))
}
func (AcceleratorOrderStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{3}
}

// 订单类型
type AcceleratorOrderType int32

const (
	AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_UNSPECIFIED AcceleratorOrderType = 0
	AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_NEWBIE      AcceleratorOrderType = 1
	AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_MONTH       AcceleratorOrderType = 2
)

var AcceleratorOrderType_name = map[int32]string{
	0: "ACCELERATOR_ORDER_TYPE_UNSPECIFIED",
	1: "ACCELERATOR_ORDER_TYPE_NEWBIE",
	2: "ACCELERATOR_ORDER_TYPE_MONTH",
}
var AcceleratorOrderType_value = map[string]int32{
	"ACCELERATOR_ORDER_TYPE_UNSPECIFIED": 0,
	"ACCELERATOR_ORDER_TYPE_NEWBIE":      1,
	"ACCELERATOR_ORDER_TYPE_MONTH":       2,
}

func (x AcceleratorOrderType) String() string {
	return proto.EnumName(AcceleratorOrderType_name, int32(x))
}
func (AcceleratorOrderType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{4}
}

// 平台类型
type AcceleratorPlatformType int32

const (
	AcceleratorPlatformType_ACCELERATOR_PLATFORM_TYPE_UNSPECIFIED AcceleratorPlatformType = 0
	AcceleratorPlatformType_ACCELERATOR_PLATFORM_TYPE_WINDOWS     AcceleratorPlatformType = 1
)

var AcceleratorPlatformType_name = map[int32]string{
	0: "ACCELERATOR_PLATFORM_TYPE_UNSPECIFIED",
	1: "ACCELERATOR_PLATFORM_TYPE_WINDOWS",
}
var AcceleratorPlatformType_value = map[string]int32{
	"ACCELERATOR_PLATFORM_TYPE_UNSPECIFIED": 0,
	"ACCELERATOR_PLATFORM_TYPE_WINDOWS":     1,
}

func (x AcceleratorPlatformType) String() string {
	return proto.EnumName(AcceleratorPlatformType_name, int32(x))
}
func (AcceleratorPlatformType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{5}
}

type UserIdentity int32

const (
	UserIdentity_USER_IDENTITY_UNSPECIFIED UserIdentity = 0
	UserIdentity_USER_IDENTITY_FREE        UserIdentity = 1
	UserIdentity_USER_IDENTITY_VIP         UserIdentity = 2
)

var UserIdentity_name = map[int32]string{
	0: "USER_IDENTITY_UNSPECIFIED",
	1: "USER_IDENTITY_FREE",
	2: "USER_IDENTITY_VIP",
}
var UserIdentity_value = map[string]int32{
	"USER_IDENTITY_UNSPECIFIED": 0,
	"USER_IDENTITY_FREE":        1,
	"USER_IDENTITY_VIP":         2,
}

func (x UserIdentity) String() string {
	return proto.EnumName(UserIdentity_name, int32(x))
}
func (UserIdentity) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{6}
}

type UserStatus int32

const (
	UserStatus_USER_STATUS_UNSPECIFIED UserStatus = 0
	UserStatus_USER_STATUS_NORMAL      UserStatus = 1
	UserStatus_USER_STATUS_EXPIRED     UserStatus = 2
	UserStatus_USER_STATUS_BAN         UserStatus = 3
)

var UserStatus_name = map[int32]string{
	0: "USER_STATUS_UNSPECIFIED",
	1: "USER_STATUS_NORMAL",
	2: "USER_STATUS_EXPIRED",
	3: "USER_STATUS_BAN",
}
var UserStatus_value = map[string]int32{
	"USER_STATUS_UNSPECIFIED": 0,
	"USER_STATUS_NORMAL":      1,
	"USER_STATUS_EXPIRED":     2,
	"USER_STATUS_BAN":         3,
}

func (x UserStatus) String() string {
	return proto.EnumName(UserStatus_name, int32(x))
}
func (UserStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{7}
}

type AcceleratorBusinessType int32

const (
	AcceleratorBusinessType_ACCELERATOR_BUSINESS_TYPE_UNSPECIFIED AcceleratorBusinessType = 0
	AcceleratorBusinessType_ACCELERATOR_BUSINESS_TYPE_ACTIVITY    AcceleratorBusinessType = 1
	AcceleratorBusinessType_ACCELERATOR_BUSINESS_TYPE_ADMIN       AcceleratorBusinessType = 2
)

var AcceleratorBusinessType_name = map[int32]string{
	0: "ACCELERATOR_BUSINESS_TYPE_UNSPECIFIED",
	1: "ACCELERATOR_BUSINESS_TYPE_ACTIVITY",
	2: "ACCELERATOR_BUSINESS_TYPE_ADMIN",
}
var AcceleratorBusinessType_value = map[string]int32{
	"ACCELERATOR_BUSINESS_TYPE_UNSPECIFIED": 0,
	"ACCELERATOR_BUSINESS_TYPE_ACTIVITY":    1,
	"ACCELERATOR_BUSINESS_TYPE_ADMIN":       2,
}

func (x AcceleratorBusinessType) String() string {
	return proto.EnumName(AcceleratorBusinessType_name, int32(x))
}
func (AcceleratorBusinessType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{8}
}

type BanUserUseAcceleratorReq_Action int32

const (
	BanUserUseAcceleratorReq_ACTION_UNSPECIFIED BanUserUseAcceleratorReq_Action = 0
	BanUserUseAcceleratorReq_ACTION_BAN         BanUserUseAcceleratorReq_Action = 1
	BanUserUseAcceleratorReq_ACTION_UNBAN       BanUserUseAcceleratorReq_Action = 2
)

var BanUserUseAcceleratorReq_Action_name = map[int32]string{
	0: "ACTION_UNSPECIFIED",
	1: "ACTION_BAN",
	2: "ACTION_UNBAN",
}
var BanUserUseAcceleratorReq_Action_value = map[string]int32{
	"ACTION_UNSPECIFIED": 0,
	"ACTION_BAN":         1,
	"ACTION_UNBAN":       2,
}

func (x BanUserUseAcceleratorReq_Action) String() string {
	return proto.EnumName(BanUserUseAcceleratorReq_Action_name, int32(x))
}
func (BanUserUseAcceleratorReq_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{22, 0}
}

type AccelerateEntrance struct {
	ShowType             EntranceShowType `protobuf:"varint,1,opt,name=show_type,json=showType,proto3,enum=game_accelerator.EntranceShowType" json:"show_type,omitempty"`
	CrowdGroupId         string           `protobuf:"bytes,2,opt,name=crowd_group_id,json=crowdGroupId,proto3" json:"crowd_group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AccelerateEntrance) Reset()         { *m = AccelerateEntrance{} }
func (m *AccelerateEntrance) String() string { return proto.CompactTextString(m) }
func (*AccelerateEntrance) ProtoMessage()    {}
func (*AccelerateEntrance) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{0}
}
func (m *AccelerateEntrance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccelerateEntrance.Unmarshal(m, b)
}
func (m *AccelerateEntrance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccelerateEntrance.Marshal(b, m, deterministic)
}
func (dst *AccelerateEntrance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccelerateEntrance.Merge(dst, src)
}
func (m *AccelerateEntrance) XXX_Size() int {
	return xxx_messageInfo_AccelerateEntrance.Size(m)
}
func (m *AccelerateEntrance) XXX_DiscardUnknown() {
	xxx_messageInfo_AccelerateEntrance.DiscardUnknown(m)
}

var xxx_messageInfo_AccelerateEntrance proto.InternalMessageInfo

func (m *AccelerateEntrance) GetShowType() EntranceShowType {
	if m != nil {
		return m.ShowType
	}
	return EntranceShowType_ENTRANCE_SHOW_TYPE_UNSPECIFIED
}

func (m *AccelerateEntrance) GetCrowdGroupId() string {
	if m != nil {
		return m.CrowdGroupId
	}
	return ""
}

type UpdateAccelerateEntranceRequest struct {
	Entrance             *AccelerateEntrance `protobuf:"bytes,1,opt,name=entrance,proto3" json:"entrance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UpdateAccelerateEntranceRequest) Reset()         { *m = UpdateAccelerateEntranceRequest{} }
func (m *UpdateAccelerateEntranceRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateAccelerateEntranceRequest) ProtoMessage()    {}
func (*UpdateAccelerateEntranceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{1}
}
func (m *UpdateAccelerateEntranceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAccelerateEntranceRequest.Unmarshal(m, b)
}
func (m *UpdateAccelerateEntranceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAccelerateEntranceRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateAccelerateEntranceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAccelerateEntranceRequest.Merge(dst, src)
}
func (m *UpdateAccelerateEntranceRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateAccelerateEntranceRequest.Size(m)
}
func (m *UpdateAccelerateEntranceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAccelerateEntranceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAccelerateEntranceRequest proto.InternalMessageInfo

func (m *UpdateAccelerateEntranceRequest) GetEntrance() *AccelerateEntrance {
	if m != nil {
		return m.Entrance
	}
	return nil
}

type UpdateAccelerateEntranceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAccelerateEntranceResponse) Reset()         { *m = UpdateAccelerateEntranceResponse{} }
func (m *UpdateAccelerateEntranceResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateAccelerateEntranceResponse) ProtoMessage()    {}
func (*UpdateAccelerateEntranceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{2}
}
func (m *UpdateAccelerateEntranceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAccelerateEntranceResponse.Unmarshal(m, b)
}
func (m *UpdateAccelerateEntranceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAccelerateEntranceResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateAccelerateEntranceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAccelerateEntranceResponse.Merge(dst, src)
}
func (m *UpdateAccelerateEntranceResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateAccelerateEntranceResponse.Size(m)
}
func (m *UpdateAccelerateEntranceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAccelerateEntranceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAccelerateEntranceResponse proto.InternalMessageInfo

type GetAccelerateEntranceRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAccelerateEntranceRequest) Reset()         { *m = GetAccelerateEntranceRequest{} }
func (m *GetAccelerateEntranceRequest) String() string { return proto.CompactTextString(m) }
func (*GetAccelerateEntranceRequest) ProtoMessage()    {}
func (*GetAccelerateEntranceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{3}
}
func (m *GetAccelerateEntranceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccelerateEntranceRequest.Unmarshal(m, b)
}
func (m *GetAccelerateEntranceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccelerateEntranceRequest.Marshal(b, m, deterministic)
}
func (dst *GetAccelerateEntranceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccelerateEntranceRequest.Merge(dst, src)
}
func (m *GetAccelerateEntranceRequest) XXX_Size() int {
	return xxx_messageInfo_GetAccelerateEntranceRequest.Size(m)
}
func (m *GetAccelerateEntranceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccelerateEntranceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccelerateEntranceRequest proto.InternalMessageInfo

type GetAccelerateEntranceResponse struct {
	Entrance             *AccelerateEntrance `protobuf:"bytes,1,opt,name=entrance,proto3" json:"entrance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAccelerateEntranceResponse) Reset()         { *m = GetAccelerateEntranceResponse{} }
func (m *GetAccelerateEntranceResponse) String() string { return proto.CompactTextString(m) }
func (*GetAccelerateEntranceResponse) ProtoMessage()    {}
func (*GetAccelerateEntranceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{4}
}
func (m *GetAccelerateEntranceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccelerateEntranceResponse.Unmarshal(m, b)
}
func (m *GetAccelerateEntranceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccelerateEntranceResponse.Marshal(b, m, deterministic)
}
func (dst *GetAccelerateEntranceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccelerateEntranceResponse.Merge(dst, src)
}
func (m *GetAccelerateEntranceResponse) XXX_Size() int {
	return xxx_messageInfo_GetAccelerateEntranceResponse.Size(m)
}
func (m *GetAccelerateEntranceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccelerateEntranceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccelerateEntranceResponse proto.InternalMessageInfo

func (m *GetAccelerateEntranceResponse) GetEntrance() *AccelerateEntrance {
	if m != nil {
		return m.Entrance
	}
	return nil
}

type Game struct {
	Id                   uint32               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Logo                 string               `protobuf:"bytes,3,opt,name=logo,proto3" json:"logo,omitempty"`
	SupportAccModes      []uint32             `protobuf:"varint,4,rep,packed,name=support_acc_modes,json=supportAccModes,proto3" json:"support_acc_modes,omitempty"`
	State                AcceleratorGameState `protobuf:"varint,5,opt,name=state,proto3,enum=game_accelerator.AcceleratorGameState" json:"state,omitempty"`
	Labels               []AcceleratorLabel   `protobuf:"varint,6,rep,packed,name=labels,proto3,enum=game_accelerator.AcceleratorLabel" json:"labels,omitempty"`
	KeyWords             []string             `protobuf:"bytes,7,rep,name=key_words,json=keyWords,proto3" json:"key_words,omitempty"`
	UpdateTime           int64                `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	RecentUserCount      uint32               `protobuf:"varint,9,opt,name=recent_user_count,json=recentUserCount,proto3" json:"recent_user_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *Game) Reset()         { *m = Game{} }
func (m *Game) String() string { return proto.CompactTextString(m) }
func (*Game) ProtoMessage()    {}
func (*Game) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{5}
}
func (m *Game) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Game.Unmarshal(m, b)
}
func (m *Game) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Game.Marshal(b, m, deterministic)
}
func (dst *Game) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Game.Merge(dst, src)
}
func (m *Game) XXX_Size() int {
	return xxx_messageInfo_Game.Size(m)
}
func (m *Game) XXX_DiscardUnknown() {
	xxx_messageInfo_Game.DiscardUnknown(m)
}

var xxx_messageInfo_Game proto.InternalMessageInfo

func (m *Game) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Game) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Game) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *Game) GetSupportAccModes() []uint32 {
	if m != nil {
		return m.SupportAccModes
	}
	return nil
}

func (m *Game) GetState() AcceleratorGameState {
	if m != nil {
		return m.State
	}
	return AcceleratorGameState_ACCELERATOR_GAME_STATE_UNSPECIAL
}

func (m *Game) GetLabels() []AcceleratorLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *Game) GetKeyWords() []string {
	if m != nil {
		return m.KeyWords
	}
	return nil
}

func (m *Game) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Game) GetRecentUserCount() uint32 {
	if m != nil {
		return m.RecentUserCount
	}
	return 0
}

type SearchAccelerateGameRequest struct {
	LastId               string                                       `protobuf:"bytes,1,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	Limit                uint32                                       `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	SourceTag            string                                       `protobuf:"bytes,3,opt,name=source_tag,json=sourceTag,proto3" json:"source_tag,omitempty"`
	FilterOption         *SearchAccelerateGameRequest_FilterOption    `protobuf:"bytes,4,opt,name=filter_option,json=filterOption,proto3" json:"filter_option,omitempty"`
	ForceInsertIds       []uint32                                     `protobuf:"varint,5,rep,packed,name=force_insert_ids,json=forceInsertIds,proto3" json:"force_insert_ids,omitempty"`
	ExtraInfoOption      *SearchAccelerateGameRequest_ExtraInfoOption `protobuf:"bytes,6,opt,name=extra_info_option,json=extraInfoOption,proto3" json:"extra_info_option,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *SearchAccelerateGameRequest) Reset()         { *m = SearchAccelerateGameRequest{} }
func (m *SearchAccelerateGameRequest) String() string { return proto.CompactTextString(m) }
func (*SearchAccelerateGameRequest) ProtoMessage()    {}
func (*SearchAccelerateGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{6}
}
func (m *SearchAccelerateGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchAccelerateGameRequest.Unmarshal(m, b)
}
func (m *SearchAccelerateGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchAccelerateGameRequest.Marshal(b, m, deterministic)
}
func (dst *SearchAccelerateGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchAccelerateGameRequest.Merge(dst, src)
}
func (m *SearchAccelerateGameRequest) XXX_Size() int {
	return xxx_messageInfo_SearchAccelerateGameRequest.Size(m)
}
func (m *SearchAccelerateGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchAccelerateGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchAccelerateGameRequest proto.InternalMessageInfo

func (m *SearchAccelerateGameRequest) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

func (m *SearchAccelerateGameRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchAccelerateGameRequest) GetSourceTag() string {
	if m != nil {
		return m.SourceTag
	}
	return ""
}

func (m *SearchAccelerateGameRequest) GetFilterOption() *SearchAccelerateGameRequest_FilterOption {
	if m != nil {
		return m.FilterOption
	}
	return nil
}

func (m *SearchAccelerateGameRequest) GetForceInsertIds() []uint32 {
	if m != nil {
		return m.ForceInsertIds
	}
	return nil
}

func (m *SearchAccelerateGameRequest) GetExtraInfoOption() *SearchAccelerateGameRequest_ExtraInfoOption {
	if m != nil {
		return m.ExtraInfoOption
	}
	return nil
}

type SearchAccelerateGameRequest_FilterOption struct {
	Name                 string               `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Id                   uint32               `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	State                AcceleratorGameState `protobuf:"varint,3,opt,name=state,proto3,enum=game_accelerator.AcceleratorGameState" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SearchAccelerateGameRequest_FilterOption) Reset() {
	*m = SearchAccelerateGameRequest_FilterOption{}
}
func (m *SearchAccelerateGameRequest_FilterOption) String() string { return proto.CompactTextString(m) }
func (*SearchAccelerateGameRequest_FilterOption) ProtoMessage()    {}
func (*SearchAccelerateGameRequest_FilterOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{6, 0}
}
func (m *SearchAccelerateGameRequest_FilterOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchAccelerateGameRequest_FilterOption.Unmarshal(m, b)
}
func (m *SearchAccelerateGameRequest_FilterOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchAccelerateGameRequest_FilterOption.Marshal(b, m, deterministic)
}
func (dst *SearchAccelerateGameRequest_FilterOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchAccelerateGameRequest_FilterOption.Merge(dst, src)
}
func (m *SearchAccelerateGameRequest_FilterOption) XXX_Size() int {
	return xxx_messageInfo_SearchAccelerateGameRequest_FilterOption.Size(m)
}
func (m *SearchAccelerateGameRequest_FilterOption) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchAccelerateGameRequest_FilterOption.DiscardUnknown(m)
}

var xxx_messageInfo_SearchAccelerateGameRequest_FilterOption proto.InternalMessageInfo

func (m *SearchAccelerateGameRequest_FilterOption) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchAccelerateGameRequest_FilterOption) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SearchAccelerateGameRequest_FilterOption) GetState() AcceleratorGameState {
	if m != nil {
		return m.State
	}
	return AcceleratorGameState_ACCELERATOR_GAME_STATE_UNSPECIAL
}

type SearchAccelerateGameRequest_ExtraInfoOption struct {
	NeedRecentUserCount  bool     `protobuf:"varint,1,opt,name=need_recent_user_count,json=needRecentUserCount,proto3" json:"need_recent_user_count,omitempty"`
	NeedDoFilter         bool     `protobuf:"varint,2,opt,name=need_do_filter,json=needDoFilter,proto3" json:"need_do_filter,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchAccelerateGameRequest_ExtraInfoOption) Reset() {
	*m = SearchAccelerateGameRequest_ExtraInfoOption{}
}
func (m *SearchAccelerateGameRequest_ExtraInfoOption) String() string {
	return proto.CompactTextString(m)
}
func (*SearchAccelerateGameRequest_ExtraInfoOption) ProtoMessage() {}
func (*SearchAccelerateGameRequest_ExtraInfoOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{6, 1}
}
func (m *SearchAccelerateGameRequest_ExtraInfoOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchAccelerateGameRequest_ExtraInfoOption.Unmarshal(m, b)
}
func (m *SearchAccelerateGameRequest_ExtraInfoOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchAccelerateGameRequest_ExtraInfoOption.Marshal(b, m, deterministic)
}
func (dst *SearchAccelerateGameRequest_ExtraInfoOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchAccelerateGameRequest_ExtraInfoOption.Merge(dst, src)
}
func (m *SearchAccelerateGameRequest_ExtraInfoOption) XXX_Size() int {
	return xxx_messageInfo_SearchAccelerateGameRequest_ExtraInfoOption.Size(m)
}
func (m *SearchAccelerateGameRequest_ExtraInfoOption) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchAccelerateGameRequest_ExtraInfoOption.DiscardUnknown(m)
}

var xxx_messageInfo_SearchAccelerateGameRequest_ExtraInfoOption proto.InternalMessageInfo

func (m *SearchAccelerateGameRequest_ExtraInfoOption) GetNeedRecentUserCount() bool {
	if m != nil {
		return m.NeedRecentUserCount
	}
	return false
}

func (m *SearchAccelerateGameRequest_ExtraInfoOption) GetNeedDoFilter() bool {
	if m != nil {
		return m.NeedDoFilter
	}
	return false
}

type SearchAccelerateGameResponse struct {
	Games                []*Game  `protobuf:"bytes,1,rep,name=games,proto3" json:"games,omitempty"`
	LastId               string   `protobuf:"bytes,2,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchAccelerateGameResponse) Reset()         { *m = SearchAccelerateGameResponse{} }
func (m *SearchAccelerateGameResponse) String() string { return proto.CompactTextString(m) }
func (*SearchAccelerateGameResponse) ProtoMessage()    {}
func (*SearchAccelerateGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{7}
}
func (m *SearchAccelerateGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchAccelerateGameResponse.Unmarshal(m, b)
}
func (m *SearchAccelerateGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchAccelerateGameResponse.Marshal(b, m, deterministic)
}
func (dst *SearchAccelerateGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchAccelerateGameResponse.Merge(dst, src)
}
func (m *SearchAccelerateGameResponse) XXX_Size() int {
	return xxx_messageInfo_SearchAccelerateGameResponse.Size(m)
}
func (m *SearchAccelerateGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchAccelerateGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchAccelerateGameResponse proto.InternalMessageInfo

func (m *SearchAccelerateGameResponse) GetGames() []*Game {
	if m != nil {
		return m.Games
	}
	return nil
}

func (m *SearchAccelerateGameResponse) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

type UpdateGameRequest struct {
	Game                 *UpdateGameRequest_Game `protobuf:"bytes,1,opt,name=game,proto3" json:"game,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpdateGameRequest) Reset()         { *m = UpdateGameRequest{} }
func (m *UpdateGameRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateGameRequest) ProtoMessage()    {}
func (*UpdateGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{8}
}
func (m *UpdateGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGameRequest.Unmarshal(m, b)
}
func (m *UpdateGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGameRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGameRequest.Merge(dst, src)
}
func (m *UpdateGameRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateGameRequest.Size(m)
}
func (m *UpdateGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGameRequest proto.InternalMessageInfo

func (m *UpdateGameRequest) GetGame() *UpdateGameRequest_Game {
	if m != nil {
		return m.Game
	}
	return nil
}

type UpdateGameRequest_Game struct {
	Id                   uint32               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Logo                 string               `protobuf:"bytes,3,opt,name=logo,proto3" json:"logo,omitempty"`
	State                AcceleratorGameState `protobuf:"varint,4,opt,name=state,proto3,enum=game_accelerator.AcceleratorGameState" json:"state,omitempty"`
	Labels               []AcceleratorLabel   `protobuf:"varint,5,rep,packed,name=labels,proto3,enum=game_accelerator.AcceleratorLabel" json:"labels,omitempty"`
	KeyWords             []string             `protobuf:"bytes,6,rep,name=key_words,json=keyWords,proto3" json:"key_words,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdateGameRequest_Game) Reset()         { *m = UpdateGameRequest_Game{} }
func (m *UpdateGameRequest_Game) String() string { return proto.CompactTextString(m) }
func (*UpdateGameRequest_Game) ProtoMessage()    {}
func (*UpdateGameRequest_Game) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{8, 0}
}
func (m *UpdateGameRequest_Game) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGameRequest_Game.Unmarshal(m, b)
}
func (m *UpdateGameRequest_Game) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGameRequest_Game.Marshal(b, m, deterministic)
}
func (dst *UpdateGameRequest_Game) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGameRequest_Game.Merge(dst, src)
}
func (m *UpdateGameRequest_Game) XXX_Size() int {
	return xxx_messageInfo_UpdateGameRequest_Game.Size(m)
}
func (m *UpdateGameRequest_Game) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGameRequest_Game.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGameRequest_Game proto.InternalMessageInfo

func (m *UpdateGameRequest_Game) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateGameRequest_Game) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdateGameRequest_Game) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *UpdateGameRequest_Game) GetState() AcceleratorGameState {
	if m != nil {
		return m.State
	}
	return AcceleratorGameState_ACCELERATOR_GAME_STATE_UNSPECIAL
}

func (m *UpdateGameRequest_Game) GetLabels() []AcceleratorLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *UpdateGameRequest_Game) GetKeyWords() []string {
	if m != nil {
		return m.KeyWords
	}
	return nil
}

type UpdateGameResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGameResponse) Reset()         { *m = UpdateGameResponse{} }
func (m *UpdateGameResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateGameResponse) ProtoMessage()    {}
func (*UpdateGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{9}
}
func (m *UpdateGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGameResponse.Unmarshal(m, b)
}
func (m *UpdateGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGameResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGameResponse.Merge(dst, src)
}
func (m *UpdateGameResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateGameResponse.Size(m)
}
func (m *UpdateGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGameResponse proto.InternalMessageInfo

type SyncAccelerateGameListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SyncAccelerateGameListRequest) Reset()         { *m = SyncAccelerateGameListRequest{} }
func (m *SyncAccelerateGameListRequest) String() string { return proto.CompactTextString(m) }
func (*SyncAccelerateGameListRequest) ProtoMessage()    {}
func (*SyncAccelerateGameListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{10}
}
func (m *SyncAccelerateGameListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncAccelerateGameListRequest.Unmarshal(m, b)
}
func (m *SyncAccelerateGameListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncAccelerateGameListRequest.Marshal(b, m, deterministic)
}
func (dst *SyncAccelerateGameListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncAccelerateGameListRequest.Merge(dst, src)
}
func (m *SyncAccelerateGameListRequest) XXX_Size() int {
	return xxx_messageInfo_SyncAccelerateGameListRequest.Size(m)
}
func (m *SyncAccelerateGameListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncAccelerateGameListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SyncAccelerateGameListRequest proto.InternalMessageInfo

type SyncAccelerateGameListResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SyncAccelerateGameListResponse) Reset()         { *m = SyncAccelerateGameListResponse{} }
func (m *SyncAccelerateGameListResponse) String() string { return proto.CompactTextString(m) }
func (*SyncAccelerateGameListResponse) ProtoMessage()    {}
func (*SyncAccelerateGameListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{11}
}
func (m *SyncAccelerateGameListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncAccelerateGameListResponse.Unmarshal(m, b)
}
func (m *SyncAccelerateGameListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncAccelerateGameListResponse.Marshal(b, m, deterministic)
}
func (dst *SyncAccelerateGameListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncAccelerateGameListResponse.Merge(dst, src)
}
func (m *SyncAccelerateGameListResponse) XXX_Size() int {
	return xxx_messageInfo_SyncAccelerateGameListResponse.Size(m)
}
func (m *SyncAccelerateGameListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncAccelerateGameListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SyncAccelerateGameListResponse proto.InternalMessageInfo

// 根据游戏id列表获取游戏信息，不返回最近14天加速用户数
type GetGamesRequest struct {
	GameIds              []uint32 `protobuf:"varint,1,rep,packed,name=game_ids,json=gameIds,proto3" json:"game_ids,omitempty"`
	NeedRecentUserCount  bool     `protobuf:"varint,2,opt,name=need_recent_user_count,json=needRecentUserCount,proto3" json:"need_recent_user_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGamesRequest) Reset()         { *m = GetGamesRequest{} }
func (m *GetGamesRequest) String() string { return proto.CompactTextString(m) }
func (*GetGamesRequest) ProtoMessage()    {}
func (*GetGamesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{12}
}
func (m *GetGamesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamesRequest.Unmarshal(m, b)
}
func (m *GetGamesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamesRequest.Marshal(b, m, deterministic)
}
func (dst *GetGamesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamesRequest.Merge(dst, src)
}
func (m *GetGamesRequest) XXX_Size() int {
	return xxx_messageInfo_GetGamesRequest.Size(m)
}
func (m *GetGamesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamesRequest proto.InternalMessageInfo

func (m *GetGamesRequest) GetGameIds() []uint32 {
	if m != nil {
		return m.GameIds
	}
	return nil
}

func (m *GetGamesRequest) GetNeedRecentUserCount() bool {
	if m != nil {
		return m.NeedRecentUserCount
	}
	return false
}

type GetGamesResponse struct {
	Games                []*Game  `protobuf:"bytes,1,rep,name=games,proto3" json:"games,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGamesResponse) Reset()         { *m = GetGamesResponse{} }
func (m *GetGamesResponse) String() string { return proto.CompactTextString(m) }
func (*GetGamesResponse) ProtoMessage()    {}
func (*GetGamesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{13}
}
func (m *GetGamesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamesResponse.Unmarshal(m, b)
}
func (m *GetGamesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamesResponse.Marshal(b, m, deterministic)
}
func (dst *GetGamesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamesResponse.Merge(dst, src)
}
func (m *GetGamesResponse) XXX_Size() int {
	return xxx_messageInfo_GetGamesResponse.Size(m)
}
func (m *GetGamesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamesResponse proto.InternalMessageInfo

func (m *GetGamesResponse) GetGames() []*Game {
	if m != nil {
		return m.Games
	}
	return nil
}

type GetAcceleratorOrderReq struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ThirdPartyOrderId    string                 `protobuf:"bytes,2,opt,name=third_party_order_id,json=thirdPartyOrderId,proto3" json:"third_party_order_id,omitempty"`
	StartTime            int64                  `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64                  `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Status               AcceleratorOrderStatus `protobuf:"varint,5,opt,name=status,proto3,enum=game_accelerator.AcceleratorOrderStatus" json:"status,omitempty"`
	Page                 int32                  `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             int32                  `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetAcceleratorOrderReq) Reset()         { *m = GetAcceleratorOrderReq{} }
func (m *GetAcceleratorOrderReq) String() string { return proto.CompactTextString(m) }
func (*GetAcceleratorOrderReq) ProtoMessage()    {}
func (*GetAcceleratorOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{14}
}
func (m *GetAcceleratorOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAcceleratorOrderReq.Unmarshal(m, b)
}
func (m *GetAcceleratorOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAcceleratorOrderReq.Marshal(b, m, deterministic)
}
func (dst *GetAcceleratorOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAcceleratorOrderReq.Merge(dst, src)
}
func (m *GetAcceleratorOrderReq) XXX_Size() int {
	return xxx_messageInfo_GetAcceleratorOrderReq.Size(m)
}
func (m *GetAcceleratorOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAcceleratorOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAcceleratorOrderReq proto.InternalMessageInfo

func (m *GetAcceleratorOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAcceleratorOrderReq) GetThirdPartyOrderId() string {
	if m != nil {
		return m.ThirdPartyOrderId
	}
	return ""
}

func (m *GetAcceleratorOrderReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetAcceleratorOrderReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetAcceleratorOrderReq) GetStatus() AcceleratorOrderStatus {
	if m != nil {
		return m.Status
	}
	return AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_UNSPECIFIED
}

func (m *GetAcceleratorOrderReq) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAcceleratorOrderReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type AcceleratorOrderItem struct {
	// 订单id
	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// 用户id
	Uid uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// 订单类型
	OrderType AcceleratorOrderType `protobuf:"varint,3,opt,name=order_type,json=orderType,proto3,enum=game_accelerator.AcceleratorOrderType" json:"order_type,omitempty"`
	// 申请数量
	OrderCount uint32 `protobuf:"varint,4,opt,name=order_count,json=orderCount,proto3" json:"order_count,omitempty"`
	// 授权结果状态
	Status AcceleratorOrderStatus `protobuf:"varint,5,opt,name=status,proto3,enum=game_accelerator.AcceleratorOrderStatus" json:"status,omitempty"`
	// 订单创建时间,单位秒
	CreateTime int64 `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 订单到期时间,单位秒
	ExpireTime int64 `protobuf:"varint,7,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// 平台类型
	PlatformType AcceleratorPlatformType `protobuf:"varint,8,opt,name=platform_type,json=platformType,proto3,enum=game_accelerator.AcceleratorPlatformType" json:"platform_type,omitempty"`
	// 操作人
	Operator string `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator,omitempty"`
	// 授权备注
	Remark string `protobuf:"bytes,10,opt,name=remark,proto3" json:"remark,omitempty"`
	// 业务调用方订单id
	OutTradeOrderId string `protobuf:"bytes,11,opt,name=out_trade_order_id,json=outTradeOrderId,proto3" json:"out_trade_order_id,omitempty"`
	// 第三方订单号
	ThirdPartyOrderId    string   `protobuf:"bytes,12,opt,name=third_party_order_id,json=thirdPartyOrderId,proto3" json:"third_party_order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceleratorOrderItem) Reset()         { *m = AcceleratorOrderItem{} }
func (m *AcceleratorOrderItem) String() string { return proto.CompactTextString(m) }
func (*AcceleratorOrderItem) ProtoMessage()    {}
func (*AcceleratorOrderItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{15}
}
func (m *AcceleratorOrderItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceleratorOrderItem.Unmarshal(m, b)
}
func (m *AcceleratorOrderItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceleratorOrderItem.Marshal(b, m, deterministic)
}
func (dst *AcceleratorOrderItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceleratorOrderItem.Merge(dst, src)
}
func (m *AcceleratorOrderItem) XXX_Size() int {
	return xxx_messageInfo_AcceleratorOrderItem.Size(m)
}
func (m *AcceleratorOrderItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceleratorOrderItem.DiscardUnknown(m)
}

var xxx_messageInfo_AcceleratorOrderItem proto.InternalMessageInfo

func (m *AcceleratorOrderItem) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AcceleratorOrderItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AcceleratorOrderItem) GetOrderType() AcceleratorOrderType {
	if m != nil {
		return m.OrderType
	}
	return AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_UNSPECIFIED
}

func (m *AcceleratorOrderItem) GetOrderCount() uint32 {
	if m != nil {
		return m.OrderCount
	}
	return 0
}

func (m *AcceleratorOrderItem) GetStatus() AcceleratorOrderStatus {
	if m != nil {
		return m.Status
	}
	return AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_UNSPECIFIED
}

func (m *AcceleratorOrderItem) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *AcceleratorOrderItem) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *AcceleratorOrderItem) GetPlatformType() AcceleratorPlatformType {
	if m != nil {
		return m.PlatformType
	}
	return AcceleratorPlatformType_ACCELERATOR_PLATFORM_TYPE_UNSPECIFIED
}

func (m *AcceleratorOrderItem) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *AcceleratorOrderItem) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *AcceleratorOrderItem) GetOutTradeOrderId() string {
	if m != nil {
		return m.OutTradeOrderId
	}
	return ""
}

func (m *AcceleratorOrderItem) GetThirdPartyOrderId() string {
	if m != nil {
		return m.ThirdPartyOrderId
	}
	return ""
}

type GetAcceleratorOrderResp struct {
	TotalCount           int64                   `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	OrderList            []*AcceleratorOrderItem `protobuf:"bytes,2,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetAcceleratorOrderResp) Reset()         { *m = GetAcceleratorOrderResp{} }
func (m *GetAcceleratorOrderResp) String() string { return proto.CompactTextString(m) }
func (*GetAcceleratorOrderResp) ProtoMessage()    {}
func (*GetAcceleratorOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{16}
}
func (m *GetAcceleratorOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAcceleratorOrderResp.Unmarshal(m, b)
}
func (m *GetAcceleratorOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAcceleratorOrderResp.Marshal(b, m, deterministic)
}
func (dst *GetAcceleratorOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAcceleratorOrderResp.Merge(dst, src)
}
func (m *GetAcceleratorOrderResp) XXX_Size() int {
	return xxx_messageInfo_GetAcceleratorOrderResp.Size(m)
}
func (m *GetAcceleratorOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAcceleratorOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAcceleratorOrderResp proto.InternalMessageInfo

func (m *GetAcceleratorOrderResp) GetTotalCount() int64 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetAcceleratorOrderResp) GetOrderList() []*AcceleratorOrderItem {
	if m != nil {
		return m.OrderList
	}
	return nil
}

type GetAcceleratorUserListReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MembershipStartTime  int64      `protobuf:"varint,2,opt,name=membership_start_time,json=membershipStartTime,proto3" json:"membership_start_time,omitempty"`
	MembershipEndTime    int64      `protobuf:"varint,3,opt,name=membership_end_time,json=membershipEndTime,proto3" json:"membership_end_time,omitempty"`
	UserStatus           UserStatus `protobuf:"varint,4,opt,name=user_status,json=userStatus,proto3,enum=game_accelerator.UserStatus" json:"user_status,omitempty"`
	LastId               string     `protobuf:"bytes,5,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAcceleratorUserListReq) Reset()         { *m = GetAcceleratorUserListReq{} }
func (m *GetAcceleratorUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetAcceleratorUserListReq) ProtoMessage()    {}
func (*GetAcceleratorUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{17}
}
func (m *GetAcceleratorUserListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAcceleratorUserListReq.Unmarshal(m, b)
}
func (m *GetAcceleratorUserListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAcceleratorUserListReq.Marshal(b, m, deterministic)
}
func (dst *GetAcceleratorUserListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAcceleratorUserListReq.Merge(dst, src)
}
func (m *GetAcceleratorUserListReq) XXX_Size() int {
	return xxx_messageInfo_GetAcceleratorUserListReq.Size(m)
}
func (m *GetAcceleratorUserListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAcceleratorUserListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAcceleratorUserListReq proto.InternalMessageInfo

func (m *GetAcceleratorUserListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAcceleratorUserListReq) GetMembershipStartTime() int64 {
	if m != nil {
		return m.MembershipStartTime
	}
	return 0
}

func (m *GetAcceleratorUserListReq) GetMembershipEndTime() int64 {
	if m != nil {
		return m.MembershipEndTime
	}
	return 0
}

func (m *GetAcceleratorUserListReq) GetUserStatus() UserStatus {
	if m != nil {
		return m.UserStatus
	}
	return UserStatus_USER_STATUS_UNSPECIFIED
}

func (m *GetAcceleratorUserListReq) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

type AcceleratorUserItem struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserIdentity         UserIdentity `protobuf:"varint,2,opt,name=user_identity,json=userIdentity,proto3,enum=game_accelerator.UserIdentity" json:"user_identity,omitempty"`
	UserStatus           UserStatus   `protobuf:"varint,3,opt,name=user_status,json=userStatus,proto3,enum=game_accelerator.UserStatus" json:"user_status,omitempty"`
	CreateTime           int64        `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	MembershipExpireTime int64        `protobuf:"varint,5,opt,name=membership_expire_time,json=membershipExpireTime,proto3" json:"membership_expire_time,omitempty"`
	AuthorizeExpireTime  int64        `protobuf:"varint,6,opt,name=authorize_expire_time,json=authorizeExpireTime,proto3" json:"authorize_expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AcceleratorUserItem) Reset()         { *m = AcceleratorUserItem{} }
func (m *AcceleratorUserItem) String() string { return proto.CompactTextString(m) }
func (*AcceleratorUserItem) ProtoMessage()    {}
func (*AcceleratorUserItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{18}
}
func (m *AcceleratorUserItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceleratorUserItem.Unmarshal(m, b)
}
func (m *AcceleratorUserItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceleratorUserItem.Marshal(b, m, deterministic)
}
func (dst *AcceleratorUserItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceleratorUserItem.Merge(dst, src)
}
func (m *AcceleratorUserItem) XXX_Size() int {
	return xxx_messageInfo_AcceleratorUserItem.Size(m)
}
func (m *AcceleratorUserItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceleratorUserItem.DiscardUnknown(m)
}

var xxx_messageInfo_AcceleratorUserItem proto.InternalMessageInfo

func (m *AcceleratorUserItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AcceleratorUserItem) GetUserIdentity() UserIdentity {
	if m != nil {
		return m.UserIdentity
	}
	return UserIdentity_USER_IDENTITY_UNSPECIFIED
}

func (m *AcceleratorUserItem) GetUserStatus() UserStatus {
	if m != nil {
		return m.UserStatus
	}
	return UserStatus_USER_STATUS_UNSPECIFIED
}

func (m *AcceleratorUserItem) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *AcceleratorUserItem) GetMembershipExpireTime() int64 {
	if m != nil {
		return m.MembershipExpireTime
	}
	return 0
}

func (m *AcceleratorUserItem) GetAuthorizeExpireTime() int64 {
	if m != nil {
		return m.AuthorizeExpireTime
	}
	return 0
}

type GetAcceleratorUserListResp struct {
	TotalCount           int64                  `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	UserList             []*AcceleratorUserItem `protobuf:"bytes,2,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	LastId               string                 `protobuf:"bytes,3,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetAcceleratorUserListResp) Reset()         { *m = GetAcceleratorUserListResp{} }
func (m *GetAcceleratorUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetAcceleratorUserListResp) ProtoMessage()    {}
func (*GetAcceleratorUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{19}
}
func (m *GetAcceleratorUserListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAcceleratorUserListResp.Unmarshal(m, b)
}
func (m *GetAcceleratorUserListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAcceleratorUserListResp.Marshal(b, m, deterministic)
}
func (dst *GetAcceleratorUserListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAcceleratorUserListResp.Merge(dst, src)
}
func (m *GetAcceleratorUserListResp) XXX_Size() int {
	return xxx_messageInfo_GetAcceleratorUserListResp.Size(m)
}
func (m *GetAcceleratorUserListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAcceleratorUserListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAcceleratorUserListResp proto.InternalMessageInfo

func (m *GetAcceleratorUserListResp) GetTotalCount() int64 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetAcceleratorUserListResp) GetUserList() []*AcceleratorUserItem {
	if m != nil {
		return m.UserList
	}
	return nil
}

func (m *GetAcceleratorUserListResp) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

type GetUserAcceleratorLogReq struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	AppIds               string   `protobuf:"bytes,2,opt,name=app_ids,json=appIds,proto3" json:"app_ids,omitempty"`
	StartTime            int64    `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Page                 int32    `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             int32    `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAcceleratorLogReq) Reset()         { *m = GetUserAcceleratorLogReq{} }
func (m *GetUserAcceleratorLogReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAcceleratorLogReq) ProtoMessage()    {}
func (*GetUserAcceleratorLogReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{20}
}
func (m *GetUserAcceleratorLogReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAcceleratorLogReq.Unmarshal(m, b)
}
func (m *GetUserAcceleratorLogReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAcceleratorLogReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAcceleratorLogReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAcceleratorLogReq.Merge(dst, src)
}
func (m *GetUserAcceleratorLogReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAcceleratorLogReq.Size(m)
}
func (m *GetUserAcceleratorLogReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAcceleratorLogReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAcceleratorLogReq proto.InternalMessageInfo

func (m *GetUserAcceleratorLogReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetUserAcceleratorLogReq) GetAppIds() string {
	if m != nil {
		return m.AppIds
	}
	return ""
}

func (m *GetUserAcceleratorLogReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetUserAcceleratorLogReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetUserAcceleratorLogReq) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetUserAcceleratorLogReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetUserAcceleratorLogResp struct {
	Code                 int32                           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message              string                          `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data                 *GetUserAcceleratorLogResp_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetUserAcceleratorLogResp) Reset()         { *m = GetUserAcceleratorLogResp{} }
func (m *GetUserAcceleratorLogResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAcceleratorLogResp) ProtoMessage()    {}
func (*GetUserAcceleratorLogResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{21}
}
func (m *GetUserAcceleratorLogResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAcceleratorLogResp.Unmarshal(m, b)
}
func (m *GetUserAcceleratorLogResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAcceleratorLogResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAcceleratorLogResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAcceleratorLogResp.Merge(dst, src)
}
func (m *GetUserAcceleratorLogResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAcceleratorLogResp.Size(m)
}
func (m *GetUserAcceleratorLogResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAcceleratorLogResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAcceleratorLogResp proto.InternalMessageInfo

func (m *GetUserAcceleratorLogResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetUserAcceleratorLogResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetUserAcceleratorLogResp) GetData() *GetUserAcceleratorLogResp_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetUserAcceleratorLogResp_Data struct {
	Items                []*GetUserAcceleratorLogResp_Data_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total                int32                                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PerPage              int32                                  `protobuf:"varint,3,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	CurrentPage          int32                                  `protobuf:"varint,4,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	ServerTime           string                                 `protobuf:"bytes,5,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetUserAcceleratorLogResp_Data) Reset()         { *m = GetUserAcceleratorLogResp_Data{} }
func (m *GetUserAcceleratorLogResp_Data) String() string { return proto.CompactTextString(m) }
func (*GetUserAcceleratorLogResp_Data) ProtoMessage()    {}
func (*GetUserAcceleratorLogResp_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{21, 0}
}
func (m *GetUserAcceleratorLogResp_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAcceleratorLogResp_Data.Unmarshal(m, b)
}
func (m *GetUserAcceleratorLogResp_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAcceleratorLogResp_Data.Marshal(b, m, deterministic)
}
func (dst *GetUserAcceleratorLogResp_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAcceleratorLogResp_Data.Merge(dst, src)
}
func (m *GetUserAcceleratorLogResp_Data) XXX_Size() int {
	return xxx_messageInfo_GetUserAcceleratorLogResp_Data.Size(m)
}
func (m *GetUserAcceleratorLogResp_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAcceleratorLogResp_Data.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAcceleratorLogResp_Data proto.InternalMessageInfo

func (m *GetUserAcceleratorLogResp_Data) GetItems() []*GetUserAcceleratorLogResp_Data_Item {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetUserAcceleratorLogResp_Data) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetUserAcceleratorLogResp_Data) GetPerPage() int32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *GetUserAcceleratorLogResp_Data) GetCurrentPage() int32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *GetUserAcceleratorLogResp_Data) GetServerTime() string {
	if m != nil {
		return m.ServerTime
	}
	return ""
}

type GetUserAcceleratorLogResp_Data_Item struct {
	Imei                 string   `protobuf:"bytes,1,opt,name=imei,proto3" json:"imei,omitempty"`
	AppIds               []string `protobuf:"bytes,2,rep,name=app_ids,json=appIds,proto3" json:"app_ids,omitempty"`
	StartTime            string   `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              string   `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	FlowUse              string   `protobuf:"bytes,5,opt,name=flow_use,json=flowUse,proto3" json:"flow_use,omitempty"`
	MaxWidth             string   `protobuf:"bytes,6,opt,name=max_width,json=maxWidth,proto3" json:"max_width,omitempty"`
	MinWidth             string   `protobuf:"bytes,7,opt,name=min_width,json=minWidth,proto3" json:"min_width,omitempty"`
	AvgWidth             string   `protobuf:"bytes,8,opt,name=avg_width,json=avgWidth,proto3" json:"avg_width,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAcceleratorLogResp_Data_Item) Reset()         { *m = GetUserAcceleratorLogResp_Data_Item{} }
func (m *GetUserAcceleratorLogResp_Data_Item) String() string { return proto.CompactTextString(m) }
func (*GetUserAcceleratorLogResp_Data_Item) ProtoMessage()    {}
func (*GetUserAcceleratorLogResp_Data_Item) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{21, 0, 0}
}
func (m *GetUserAcceleratorLogResp_Data_Item) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAcceleratorLogResp_Data_Item.Unmarshal(m, b)
}
func (m *GetUserAcceleratorLogResp_Data_Item) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAcceleratorLogResp_Data_Item.Marshal(b, m, deterministic)
}
func (dst *GetUserAcceleratorLogResp_Data_Item) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAcceleratorLogResp_Data_Item.Merge(dst, src)
}
func (m *GetUserAcceleratorLogResp_Data_Item) XXX_Size() int {
	return xxx_messageInfo_GetUserAcceleratorLogResp_Data_Item.Size(m)
}
func (m *GetUserAcceleratorLogResp_Data_Item) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAcceleratorLogResp_Data_Item.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAcceleratorLogResp_Data_Item proto.InternalMessageInfo

func (m *GetUserAcceleratorLogResp_Data_Item) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *GetUserAcceleratorLogResp_Data_Item) GetAppIds() []string {
	if m != nil {
		return m.AppIds
	}
	return nil
}

func (m *GetUserAcceleratorLogResp_Data_Item) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GetUserAcceleratorLogResp_Data_Item) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GetUserAcceleratorLogResp_Data_Item) GetFlowUse() string {
	if m != nil {
		return m.FlowUse
	}
	return ""
}

func (m *GetUserAcceleratorLogResp_Data_Item) GetMaxWidth() string {
	if m != nil {
		return m.MaxWidth
	}
	return ""
}

func (m *GetUserAcceleratorLogResp_Data_Item) GetMinWidth() string {
	if m != nil {
		return m.MinWidth
	}
	return ""
}

func (m *GetUserAcceleratorLogResp_Data_Item) GetAvgWidth() string {
	if m != nil {
		return m.AvgWidth
	}
	return ""
}

type BanUserUseAcceleratorReq struct {
	Uid                  uint32                          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Action               BanUserUseAcceleratorReq_Action `protobuf:"varint,2,opt,name=action,proto3,enum=game_accelerator.BanUserUseAcceleratorReq_Action" json:"action,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *BanUserUseAcceleratorReq) Reset()         { *m = BanUserUseAcceleratorReq{} }
func (m *BanUserUseAcceleratorReq) String() string { return proto.CompactTextString(m) }
func (*BanUserUseAcceleratorReq) ProtoMessage()    {}
func (*BanUserUseAcceleratorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{22}
}
func (m *BanUserUseAcceleratorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanUserUseAcceleratorReq.Unmarshal(m, b)
}
func (m *BanUserUseAcceleratorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanUserUseAcceleratorReq.Marshal(b, m, deterministic)
}
func (dst *BanUserUseAcceleratorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanUserUseAcceleratorReq.Merge(dst, src)
}
func (m *BanUserUseAcceleratorReq) XXX_Size() int {
	return xxx_messageInfo_BanUserUseAcceleratorReq.Size(m)
}
func (m *BanUserUseAcceleratorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BanUserUseAcceleratorReq.DiscardUnknown(m)
}

var xxx_messageInfo_BanUserUseAcceleratorReq proto.InternalMessageInfo

func (m *BanUserUseAcceleratorReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BanUserUseAcceleratorReq) GetAction() BanUserUseAcceleratorReq_Action {
	if m != nil {
		return m.Action
	}
	return BanUserUseAcceleratorReq_ACTION_UNSPECIFIED
}

type BanUserUseAcceleratorResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanUserUseAcceleratorResp) Reset()         { *m = BanUserUseAcceleratorResp{} }
func (m *BanUserUseAcceleratorResp) String() string { return proto.CompactTextString(m) }
func (*BanUserUseAcceleratorResp) ProtoMessage()    {}
func (*BanUserUseAcceleratorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{23}
}
func (m *BanUserUseAcceleratorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanUserUseAcceleratorResp.Unmarshal(m, b)
}
func (m *BanUserUseAcceleratorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanUserUseAcceleratorResp.Marshal(b, m, deterministic)
}
func (dst *BanUserUseAcceleratorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanUserUseAcceleratorResp.Merge(dst, src)
}
func (m *BanUserUseAcceleratorResp) XXX_Size() int {
	return xxx_messageInfo_BanUserUseAcceleratorResp.Size(m)
}
func (m *BanUserUseAcceleratorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BanUserUseAcceleratorResp.DiscardUnknown(m)
}

var xxx_messageInfo_BanUserUseAcceleratorResp proto.InternalMessageInfo

type AdminAddUserAcceleratorReq struct {
	Uids                 []uint32                `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	Operator             string                  `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	AcceleratorOrderType AcceleratorOrderType    `protobuf:"varint,3,opt,name=accelerator_order_type,json=acceleratorOrderType,proto3,enum=game_accelerator.AcceleratorOrderType" json:"accelerator_order_type,omitempty"`
	OrderCount           uint32                  `protobuf:"varint,4,opt,name=order_count,json=orderCount,proto3" json:"order_count,omitempty"`
	Remark               string                  `protobuf:"bytes,5,opt,name=remark,proto3" json:"remark,omitempty"`
	PlatformType         AcceleratorPlatformType `protobuf:"varint,6,opt,name=platform_type,json=platformType,proto3,enum=game_accelerator.AcceleratorPlatformType" json:"platform_type,omitempty"`
	OutTradeOrderId      string                  `protobuf:"bytes,7,opt,name=out_trade_order_id,json=outTradeOrderId,proto3" json:"out_trade_order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AdminAddUserAcceleratorReq) Reset()         { *m = AdminAddUserAcceleratorReq{} }
func (m *AdminAddUserAcceleratorReq) String() string { return proto.CompactTextString(m) }
func (*AdminAddUserAcceleratorReq) ProtoMessage()    {}
func (*AdminAddUserAcceleratorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{24}
}
func (m *AdminAddUserAcceleratorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdminAddUserAcceleratorReq.Unmarshal(m, b)
}
func (m *AdminAddUserAcceleratorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdminAddUserAcceleratorReq.Marshal(b, m, deterministic)
}
func (dst *AdminAddUserAcceleratorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminAddUserAcceleratorReq.Merge(dst, src)
}
func (m *AdminAddUserAcceleratorReq) XXX_Size() int {
	return xxx_messageInfo_AdminAddUserAcceleratorReq.Size(m)
}
func (m *AdminAddUserAcceleratorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminAddUserAcceleratorReq.DiscardUnknown(m)
}

var xxx_messageInfo_AdminAddUserAcceleratorReq proto.InternalMessageInfo

func (m *AdminAddUserAcceleratorReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *AdminAddUserAcceleratorReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *AdminAddUserAcceleratorReq) GetAcceleratorOrderType() AcceleratorOrderType {
	if m != nil {
		return m.AcceleratorOrderType
	}
	return AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_UNSPECIFIED
}

func (m *AdminAddUserAcceleratorReq) GetOrderCount() uint32 {
	if m != nil {
		return m.OrderCount
	}
	return 0
}

func (m *AdminAddUserAcceleratorReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *AdminAddUserAcceleratorReq) GetPlatformType() AcceleratorPlatformType {
	if m != nil {
		return m.PlatformType
	}
	return AcceleratorPlatformType_ACCELERATOR_PLATFORM_TYPE_UNSPECIFIED
}

func (m *AdminAddUserAcceleratorReq) GetOutTradeOrderId() string {
	if m != nil {
		return m.OutTradeOrderId
	}
	return ""
}

type AddAcceleratorResult struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Code                 int32    `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Message              string   `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAcceleratorResult) Reset()         { *m = AddAcceleratorResult{} }
func (m *AddAcceleratorResult) String() string { return proto.CompactTextString(m) }
func (*AddAcceleratorResult) ProtoMessage()    {}
func (*AddAcceleratorResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{25}
}
func (m *AddAcceleratorResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAcceleratorResult.Unmarshal(m, b)
}
func (m *AddAcceleratorResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAcceleratorResult.Marshal(b, m, deterministic)
}
func (dst *AddAcceleratorResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAcceleratorResult.Merge(dst, src)
}
func (m *AddAcceleratorResult) XXX_Size() int {
	return xxx_messageInfo_AddAcceleratorResult.Size(m)
}
func (m *AddAcceleratorResult) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAcceleratorResult.DiscardUnknown(m)
}

var xxx_messageInfo_AddAcceleratorResult proto.InternalMessageInfo

func (m *AddAcceleratorResult) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddAcceleratorResult) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AddAcceleratorResult) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type AdminAddUserAcceleratorResp struct {
	SuccessNum           uint32                  `protobuf:"varint,1,opt,name=success_num,json=successNum,proto3" json:"success_num,omitempty"`
	FailUids             []uint32                `protobuf:"varint,2,rep,packed,name=fail_uids,json=failUids,proto3" json:"fail_uids,omitempty"` // Deprecated: Do not use.
	FailResult           []*AddAcceleratorResult `protobuf:"bytes,3,rep,name=fail_result,json=failResult,proto3" json:"fail_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AdminAddUserAcceleratorResp) Reset()         { *m = AdminAddUserAcceleratorResp{} }
func (m *AdminAddUserAcceleratorResp) String() string { return proto.CompactTextString(m) }
func (*AdminAddUserAcceleratorResp) ProtoMessage()    {}
func (*AdminAddUserAcceleratorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{26}
}
func (m *AdminAddUserAcceleratorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdminAddUserAcceleratorResp.Unmarshal(m, b)
}
func (m *AdminAddUserAcceleratorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdminAddUserAcceleratorResp.Marshal(b, m, deterministic)
}
func (dst *AdminAddUserAcceleratorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminAddUserAcceleratorResp.Merge(dst, src)
}
func (m *AdminAddUserAcceleratorResp) XXX_Size() int {
	return xxx_messageInfo_AdminAddUserAcceleratorResp.Size(m)
}
func (m *AdminAddUserAcceleratorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminAddUserAcceleratorResp.DiscardUnknown(m)
}

var xxx_messageInfo_AdminAddUserAcceleratorResp proto.InternalMessageInfo

func (m *AdminAddUserAcceleratorResp) GetSuccessNum() uint32 {
	if m != nil {
		return m.SuccessNum
	}
	return 0
}

// Deprecated: Do not use.
func (m *AdminAddUserAcceleratorResp) GetFailUids() []uint32 {
	if m != nil {
		return m.FailUids
	}
	return nil
}

func (m *AdminAddUserAcceleratorResp) GetFailResult() []*AddAcceleratorResult {
	if m != nil {
		return m.FailResult
	}
	return nil
}

type GetPreviewExpireTimeReq struct {
	Uid                  uint32               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AcceleratorOrderType AcceleratorOrderType `protobuf:"varint,2,opt,name=accelerator_order_type,json=acceleratorOrderType,proto3,enum=game_accelerator.AcceleratorOrderType" json:"accelerator_order_type,omitempty"`
	OrderCount           uint32               `protobuf:"varint,3,opt,name=order_count,json=orderCount,proto3" json:"order_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPreviewExpireTimeReq) Reset()         { *m = GetPreviewExpireTimeReq{} }
func (m *GetPreviewExpireTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetPreviewExpireTimeReq) ProtoMessage()    {}
func (*GetPreviewExpireTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{27}
}
func (m *GetPreviewExpireTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPreviewExpireTimeReq.Unmarshal(m, b)
}
func (m *GetPreviewExpireTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPreviewExpireTimeReq.Marshal(b, m, deterministic)
}
func (dst *GetPreviewExpireTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPreviewExpireTimeReq.Merge(dst, src)
}
func (m *GetPreviewExpireTimeReq) XXX_Size() int {
	return xxx_messageInfo_GetPreviewExpireTimeReq.Size(m)
}
func (m *GetPreviewExpireTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPreviewExpireTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPreviewExpireTimeReq proto.InternalMessageInfo

func (m *GetPreviewExpireTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPreviewExpireTimeReq) GetAcceleratorOrderType() AcceleratorOrderType {
	if m != nil {
		return m.AcceleratorOrderType
	}
	return AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_UNSPECIFIED
}

func (m *GetPreviewExpireTimeReq) GetOrderCount() uint32 {
	if m != nil {
		return m.OrderCount
	}
	return 0
}

type GetPreviewExpireTimeResp struct {
	ExpireTime           string   `protobuf:"bytes,1,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPreviewExpireTimeResp) Reset()         { *m = GetPreviewExpireTimeResp{} }
func (m *GetPreviewExpireTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetPreviewExpireTimeResp) ProtoMessage()    {}
func (*GetPreviewExpireTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{28}
}
func (m *GetPreviewExpireTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPreviewExpireTimeResp.Unmarshal(m, b)
}
func (m *GetPreviewExpireTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPreviewExpireTimeResp.Marshal(b, m, deterministic)
}
func (dst *GetPreviewExpireTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPreviewExpireTimeResp.Merge(dst, src)
}
func (m *GetPreviewExpireTimeResp) XXX_Size() int {
	return xxx_messageInfo_GetPreviewExpireTimeResp.Size(m)
}
func (m *GetPreviewExpireTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPreviewExpireTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPreviewExpireTimeResp proto.InternalMessageInfo

func (m *GetPreviewExpireTimeResp) GetExpireTime() string {
	if m != nil {
		return m.ExpireTime
	}
	return ""
}

type GetAcceleratorTokenReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Imei                 string   `protobuf:"bytes,2,opt,name=imei,proto3" json:"imei,omitempty"`
	AppId                string   `protobuf:"bytes,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAcceleratorTokenReq) Reset()         { *m = GetAcceleratorTokenReq{} }
func (m *GetAcceleratorTokenReq) String() string { return proto.CompactTextString(m) }
func (*GetAcceleratorTokenReq) ProtoMessage()    {}
func (*GetAcceleratorTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{29}
}
func (m *GetAcceleratorTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAcceleratorTokenReq.Unmarshal(m, b)
}
func (m *GetAcceleratorTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAcceleratorTokenReq.Marshal(b, m, deterministic)
}
func (dst *GetAcceleratorTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAcceleratorTokenReq.Merge(dst, src)
}
func (m *GetAcceleratorTokenReq) XXX_Size() int {
	return xxx_messageInfo_GetAcceleratorTokenReq.Size(m)
}
func (m *GetAcceleratorTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAcceleratorTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAcceleratorTokenReq proto.InternalMessageInfo

func (m *GetAcceleratorTokenReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAcceleratorTokenReq) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *GetAcceleratorTokenReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

type GetAcceleratorTokenResp struct {
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAcceleratorTokenResp) Reset()         { *m = GetAcceleratorTokenResp{} }
func (m *GetAcceleratorTokenResp) String() string { return proto.CompactTextString(m) }
func (*GetAcceleratorTokenResp) ProtoMessage()    {}
func (*GetAcceleratorTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{30}
}
func (m *GetAcceleratorTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAcceleratorTokenResp.Unmarshal(m, b)
}
func (m *GetAcceleratorTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAcceleratorTokenResp.Marshal(b, m, deterministic)
}
func (dst *GetAcceleratorTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAcceleratorTokenResp.Merge(dst, src)
}
func (m *GetAcceleratorTokenResp) XXX_Size() int {
	return xxx_messageInfo_GetAcceleratorTokenResp.Size(m)
}
func (m *GetAcceleratorTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAcceleratorTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAcceleratorTokenResp proto.InternalMessageInfo

func (m *GetAcceleratorTokenResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type AddUserAcceleratorAuthorizeReq struct {
	Uid                  uint32                  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BusinessId           AcceleratorBusinessType `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,enum=game_accelerator.AcceleratorBusinessType" json:"business_id,omitempty"`
	AcceleratorOrderType AcceleratorOrderType    `protobuf:"varint,3,opt,name=accelerator_order_type,json=acceleratorOrderType,proto3,enum=game_accelerator.AcceleratorOrderType" json:"accelerator_order_type,omitempty"`
	OrderCount           uint32                  `protobuf:"varint,4,opt,name=order_count,json=orderCount,proto3" json:"order_count,omitempty"`
	PlatformType         AcceleratorPlatformType `protobuf:"varint,5,opt,name=platform_type,json=platformType,proto3,enum=game_accelerator.AcceleratorPlatformType" json:"platform_type,omitempty"`
	Remark               string                  `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark,omitempty"`
	OutTradeOrderId      string                  `protobuf:"bytes,7,opt,name=out_trade_order_id,json=outTradeOrderId,proto3" json:"out_trade_order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AddUserAcceleratorAuthorizeReq) Reset()         { *m = AddUserAcceleratorAuthorizeReq{} }
func (m *AddUserAcceleratorAuthorizeReq) String() string { return proto.CompactTextString(m) }
func (*AddUserAcceleratorAuthorizeReq) ProtoMessage()    {}
func (*AddUserAcceleratorAuthorizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{31}
}
func (m *AddUserAcceleratorAuthorizeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserAcceleratorAuthorizeReq.Unmarshal(m, b)
}
func (m *AddUserAcceleratorAuthorizeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserAcceleratorAuthorizeReq.Marshal(b, m, deterministic)
}
func (dst *AddUserAcceleratorAuthorizeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserAcceleratorAuthorizeReq.Merge(dst, src)
}
func (m *AddUserAcceleratorAuthorizeReq) XXX_Size() int {
	return xxx_messageInfo_AddUserAcceleratorAuthorizeReq.Size(m)
}
func (m *AddUserAcceleratorAuthorizeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserAcceleratorAuthorizeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserAcceleratorAuthorizeReq proto.InternalMessageInfo

func (m *AddUserAcceleratorAuthorizeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserAcceleratorAuthorizeReq) GetBusinessId() AcceleratorBusinessType {
	if m != nil {
		return m.BusinessId
	}
	return AcceleratorBusinessType_ACCELERATOR_BUSINESS_TYPE_UNSPECIFIED
}

func (m *AddUserAcceleratorAuthorizeReq) GetAcceleratorOrderType() AcceleratorOrderType {
	if m != nil {
		return m.AcceleratorOrderType
	}
	return AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_UNSPECIFIED
}

func (m *AddUserAcceleratorAuthorizeReq) GetOrderCount() uint32 {
	if m != nil {
		return m.OrderCount
	}
	return 0
}

func (m *AddUserAcceleratorAuthorizeReq) GetPlatformType() AcceleratorPlatformType {
	if m != nil {
		return m.PlatformType
	}
	return AcceleratorPlatformType_ACCELERATOR_PLATFORM_TYPE_UNSPECIFIED
}

func (m *AddUserAcceleratorAuthorizeReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *AddUserAcceleratorAuthorizeReq) GetOutTradeOrderId() string {
	if m != nil {
		return m.OutTradeOrderId
	}
	return ""
}

type AddUserAcceleratorAuthorizeResp struct {
	SuccessNum           uint32   `protobuf:"varint,1,opt,name=success_num,json=successNum,proto3" json:"success_num,omitempty"`  // Deprecated: Do not use.
	FailUids             []uint32 `protobuf:"varint,2,rep,packed,name=fail_uids,json=failUids,proto3" json:"fail_uids,omitempty"` // Deprecated: Do not use.
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserAcceleratorAuthorizeResp) Reset()         { *m = AddUserAcceleratorAuthorizeResp{} }
func (m *AddUserAcceleratorAuthorizeResp) String() string { return proto.CompactTextString(m) }
func (*AddUserAcceleratorAuthorizeResp) ProtoMessage()    {}
func (*AddUserAcceleratorAuthorizeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{32}
}
func (m *AddUserAcceleratorAuthorizeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserAcceleratorAuthorizeResp.Unmarshal(m, b)
}
func (m *AddUserAcceleratorAuthorizeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserAcceleratorAuthorizeResp.Marshal(b, m, deterministic)
}
func (dst *AddUserAcceleratorAuthorizeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserAcceleratorAuthorizeResp.Merge(dst, src)
}
func (m *AddUserAcceleratorAuthorizeResp) XXX_Size() int {
	return xxx_messageInfo_AddUserAcceleratorAuthorizeResp.Size(m)
}
func (m *AddUserAcceleratorAuthorizeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserAcceleratorAuthorizeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserAcceleratorAuthorizeResp proto.InternalMessageInfo

// Deprecated: Do not use.
func (m *AddUserAcceleratorAuthorizeResp) GetSuccessNum() uint32 {
	if m != nil {
		return m.SuccessNum
	}
	return 0
}

// Deprecated: Do not use.
func (m *AddUserAcceleratorAuthorizeResp) GetFailUids() []uint32 {
	if m != nil {
		return m.FailUids
	}
	return nil
}

type GetUserAcceleratorInfoReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAcceleratorInfoReq) Reset()         { *m = GetUserAcceleratorInfoReq{} }
func (m *GetUserAcceleratorInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAcceleratorInfoReq) ProtoMessage()    {}
func (*GetUserAcceleratorInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{33}
}
func (m *GetUserAcceleratorInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAcceleratorInfoReq.Unmarshal(m, b)
}
func (m *GetUserAcceleratorInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAcceleratorInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAcceleratorInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAcceleratorInfoReq.Merge(dst, src)
}
func (m *GetUserAcceleratorInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAcceleratorInfoReq.Size(m)
}
func (m *GetUserAcceleratorInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAcceleratorInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAcceleratorInfoReq proto.InternalMessageInfo

func (m *GetUserAcceleratorInfoReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetUserAcceleratorInfoResp struct {
	Items                []*AcceleratorUserItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserAcceleratorInfoResp) Reset()         { *m = GetUserAcceleratorInfoResp{} }
func (m *GetUserAcceleratorInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAcceleratorInfoResp) ProtoMessage()    {}
func (*GetUserAcceleratorInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{34}
}
func (m *GetUserAcceleratorInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAcceleratorInfoResp.Unmarshal(m, b)
}
func (m *GetUserAcceleratorInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAcceleratorInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAcceleratorInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAcceleratorInfoResp.Merge(dst, src)
}
func (m *GetUserAcceleratorInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAcceleratorInfoResp.Size(m)
}
func (m *GetUserAcceleratorInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAcceleratorInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAcceleratorInfoResp proto.InternalMessageInfo

func (m *GetUserAcceleratorInfoResp) GetItems() []*AcceleratorUserItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type GetNewbieOrderByUidsReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewbieOrderByUidsReq) Reset()         { *m = GetNewbieOrderByUidsReq{} }
func (m *GetNewbieOrderByUidsReq) String() string { return proto.CompactTextString(m) }
func (*GetNewbieOrderByUidsReq) ProtoMessage()    {}
func (*GetNewbieOrderByUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{35}
}
func (m *GetNewbieOrderByUidsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieOrderByUidsReq.Unmarshal(m, b)
}
func (m *GetNewbieOrderByUidsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieOrderByUidsReq.Marshal(b, m, deterministic)
}
func (dst *GetNewbieOrderByUidsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieOrderByUidsReq.Merge(dst, src)
}
func (m *GetNewbieOrderByUidsReq) XXX_Size() int {
	return xxx_messageInfo_GetNewbieOrderByUidsReq.Size(m)
}
func (m *GetNewbieOrderByUidsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieOrderByUidsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieOrderByUidsReq proto.InternalMessageInfo

func (m *GetNewbieOrderByUidsReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetNewbieOrderByUidsResp struct {
	ItemMap              map[uint32]*AcceleratorOrderItem `protobuf:"bytes,1,rep,name=item_map,json=itemMap,proto3" json:"item_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetNewbieOrderByUidsResp) Reset()         { *m = GetNewbieOrderByUidsResp{} }
func (m *GetNewbieOrderByUidsResp) String() string { return proto.CompactTextString(m) }
func (*GetNewbieOrderByUidsResp) ProtoMessage()    {}
func (*GetNewbieOrderByUidsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_accelerator_e36673c1364de5ca, []int{36}
}
func (m *GetNewbieOrderByUidsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieOrderByUidsResp.Unmarshal(m, b)
}
func (m *GetNewbieOrderByUidsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieOrderByUidsResp.Marshal(b, m, deterministic)
}
func (dst *GetNewbieOrderByUidsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieOrderByUidsResp.Merge(dst, src)
}
func (m *GetNewbieOrderByUidsResp) XXX_Size() int {
	return xxx_messageInfo_GetNewbieOrderByUidsResp.Size(m)
}
func (m *GetNewbieOrderByUidsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieOrderByUidsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieOrderByUidsResp proto.InternalMessageInfo

func (m *GetNewbieOrderByUidsResp) GetItemMap() map[uint32]*AcceleratorOrderItem {
	if m != nil {
		return m.ItemMap
	}
	return nil
}

func init() {
	proto.RegisterType((*AccelerateEntrance)(nil), "game_accelerator.AccelerateEntrance")
	proto.RegisterType((*UpdateAccelerateEntranceRequest)(nil), "game_accelerator.UpdateAccelerateEntranceRequest")
	proto.RegisterType((*UpdateAccelerateEntranceResponse)(nil), "game_accelerator.UpdateAccelerateEntranceResponse")
	proto.RegisterType((*GetAccelerateEntranceRequest)(nil), "game_accelerator.GetAccelerateEntranceRequest")
	proto.RegisterType((*GetAccelerateEntranceResponse)(nil), "game_accelerator.GetAccelerateEntranceResponse")
	proto.RegisterType((*Game)(nil), "game_accelerator.Game")
	proto.RegisterType((*SearchAccelerateGameRequest)(nil), "game_accelerator.SearchAccelerateGameRequest")
	proto.RegisterType((*SearchAccelerateGameRequest_FilterOption)(nil), "game_accelerator.SearchAccelerateGameRequest.FilterOption")
	proto.RegisterType((*SearchAccelerateGameRequest_ExtraInfoOption)(nil), "game_accelerator.SearchAccelerateGameRequest.ExtraInfoOption")
	proto.RegisterType((*SearchAccelerateGameResponse)(nil), "game_accelerator.SearchAccelerateGameResponse")
	proto.RegisterType((*UpdateGameRequest)(nil), "game_accelerator.UpdateGameRequest")
	proto.RegisterType((*UpdateGameRequest_Game)(nil), "game_accelerator.UpdateGameRequest.Game")
	proto.RegisterType((*UpdateGameResponse)(nil), "game_accelerator.UpdateGameResponse")
	proto.RegisterType((*SyncAccelerateGameListRequest)(nil), "game_accelerator.SyncAccelerateGameListRequest")
	proto.RegisterType((*SyncAccelerateGameListResponse)(nil), "game_accelerator.SyncAccelerateGameListResponse")
	proto.RegisterType((*GetGamesRequest)(nil), "game_accelerator.GetGamesRequest")
	proto.RegisterType((*GetGamesResponse)(nil), "game_accelerator.GetGamesResponse")
	proto.RegisterType((*GetAcceleratorOrderReq)(nil), "game_accelerator.GetAcceleratorOrderReq")
	proto.RegisterType((*AcceleratorOrderItem)(nil), "game_accelerator.AcceleratorOrderItem")
	proto.RegisterType((*GetAcceleratorOrderResp)(nil), "game_accelerator.GetAcceleratorOrderResp")
	proto.RegisterType((*GetAcceleratorUserListReq)(nil), "game_accelerator.GetAcceleratorUserListReq")
	proto.RegisterType((*AcceleratorUserItem)(nil), "game_accelerator.AcceleratorUserItem")
	proto.RegisterType((*GetAcceleratorUserListResp)(nil), "game_accelerator.GetAcceleratorUserListResp")
	proto.RegisterType((*GetUserAcceleratorLogReq)(nil), "game_accelerator.GetUserAcceleratorLogReq")
	proto.RegisterType((*GetUserAcceleratorLogResp)(nil), "game_accelerator.GetUserAcceleratorLogResp")
	proto.RegisterType((*GetUserAcceleratorLogResp_Data)(nil), "game_accelerator.GetUserAcceleratorLogResp.Data")
	proto.RegisterType((*GetUserAcceleratorLogResp_Data_Item)(nil), "game_accelerator.GetUserAcceleratorLogResp.Data.Item")
	proto.RegisterType((*BanUserUseAcceleratorReq)(nil), "game_accelerator.BanUserUseAcceleratorReq")
	proto.RegisterType((*BanUserUseAcceleratorResp)(nil), "game_accelerator.BanUserUseAcceleratorResp")
	proto.RegisterType((*AdminAddUserAcceleratorReq)(nil), "game_accelerator.AdminAddUserAcceleratorReq")
	proto.RegisterType((*AddAcceleratorResult)(nil), "game_accelerator.AddAcceleratorResult")
	proto.RegisterType((*AdminAddUserAcceleratorResp)(nil), "game_accelerator.AdminAddUserAcceleratorResp")
	proto.RegisterType((*GetPreviewExpireTimeReq)(nil), "game_accelerator.GetPreviewExpireTimeReq")
	proto.RegisterType((*GetPreviewExpireTimeResp)(nil), "game_accelerator.GetPreviewExpireTimeResp")
	proto.RegisterType((*GetAcceleratorTokenReq)(nil), "game_accelerator.GetAcceleratorTokenReq")
	proto.RegisterType((*GetAcceleratorTokenResp)(nil), "game_accelerator.GetAcceleratorTokenResp")
	proto.RegisterType((*AddUserAcceleratorAuthorizeReq)(nil), "game_accelerator.AddUserAcceleratorAuthorizeReq")
	proto.RegisterType((*AddUserAcceleratorAuthorizeResp)(nil), "game_accelerator.AddUserAcceleratorAuthorizeResp")
	proto.RegisterType((*GetUserAcceleratorInfoReq)(nil), "game_accelerator.GetUserAcceleratorInfoReq")
	proto.RegisterType((*GetUserAcceleratorInfoResp)(nil), "game_accelerator.GetUserAcceleratorInfoResp")
	proto.RegisterType((*GetNewbieOrderByUidsReq)(nil), "game_accelerator.GetNewbieOrderByUidsReq")
	proto.RegisterType((*GetNewbieOrderByUidsResp)(nil), "game_accelerator.GetNewbieOrderByUidsResp")
	proto.RegisterMapType((map[uint32]*AcceleratorOrderItem)(nil), "game_accelerator.GetNewbieOrderByUidsResp.ItemMapEntry")
	proto.RegisterEnum("game_accelerator.EntranceShowType", EntranceShowType_name, EntranceShowType_value)
	proto.RegisterEnum("game_accelerator.AcceleratorGameState", AcceleratorGameState_name, AcceleratorGameState_value)
	proto.RegisterEnum("game_accelerator.AcceleratorLabel", AcceleratorLabel_name, AcceleratorLabel_value)
	proto.RegisterEnum("game_accelerator.AcceleratorOrderStatus", AcceleratorOrderStatus_name, AcceleratorOrderStatus_value)
	proto.RegisterEnum("game_accelerator.AcceleratorOrderType", AcceleratorOrderType_name, AcceleratorOrderType_value)
	proto.RegisterEnum("game_accelerator.AcceleratorPlatformType", AcceleratorPlatformType_name, AcceleratorPlatformType_value)
	proto.RegisterEnum("game_accelerator.UserIdentity", UserIdentity_name, UserIdentity_value)
	proto.RegisterEnum("game_accelerator.UserStatus", UserStatus_name, UserStatus_value)
	proto.RegisterEnum("game_accelerator.AcceleratorBusinessType", AcceleratorBusinessType_name, AcceleratorBusinessType_value)
	proto.RegisterEnum("game_accelerator.BanUserUseAcceleratorReq_Action", BanUserUseAcceleratorReq_Action_name, BanUserUseAcceleratorReq_Action_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameAcceleratorClient is the client API for GameAccelerator service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameAcceleratorClient interface {
	// 更新入口信息
	UpdateAccelerateEntrance(ctx context.Context, in *UpdateAccelerateEntranceRequest, opts ...grpc.CallOption) (*UpdateAccelerateEntranceResponse, error)
	// 获取入口信息
	GetAccelerateEntrance(ctx context.Context, in *GetAccelerateEntranceRequest, opts ...grpc.CallOption) (*GetAccelerateEntranceResponse, error)
	// 搜索游戏列表
	SearchAccelerateGame(ctx context.Context, in *SearchAccelerateGameRequest, opts ...grpc.CallOption) (*SearchAccelerateGameResponse, error)
	// 更新游戏信息
	UpdateGame(ctx context.Context, in *UpdateGameRequest, opts ...grpc.CallOption) (*UpdateGameResponse, error)
	// 同步游戏信息
	SyncAccelerateGameList(ctx context.Context, in *SyncAccelerateGameListRequest, opts ...grpc.CallOption) (*SyncAccelerateGameListResponse, error)
	// 根据游戏id列表获取游戏信息
	GetGames(ctx context.Context, in *GetGamesRequest, opts ...grpc.CallOption) (*GetGamesResponse, error)
	// 授权相关
	GetAcceleratorOrder(ctx context.Context, in *GetAcceleratorOrderReq, opts ...grpc.CallOption) (*GetAcceleratorOrderResp, error)
	AdminAddUserAccelerator(ctx context.Context, in *AdminAddUserAcceleratorReq, opts ...grpc.CallOption) (*AdminAddUserAcceleratorResp, error)
	GetPreviewExpireTime(ctx context.Context, in *GetPreviewExpireTimeReq, opts ...grpc.CallOption) (*GetPreviewExpireTimeResp, error)
	// 获取用户token
	GetAcceleratorToken(ctx context.Context, in *GetAcceleratorTokenReq, opts ...grpc.CallOption) (*GetAcceleratorTokenResp, error)
	// 加速器用户管理
	GetAcceleratorUserList(ctx context.Context, in *GetAcceleratorUserListReq, opts ...grpc.CallOption) (*GetAcceleratorUserListResp, error)
	GetUserAcceleratorLog(ctx context.Context, in *GetUserAcceleratorLogReq, opts ...grpc.CallOption) (*GetUserAcceleratorLogResp, error)
	BanUserUseAccelerator(ctx context.Context, in *BanUserUseAcceleratorReq, opts ...grpc.CallOption) (*BanUserUseAcceleratorResp, error)
	// 活动接口
	AddUserAcceleratorAuthorize(ctx context.Context, in *AddUserAcceleratorAuthorizeReq, opts ...grpc.CallOption) (*AddUserAcceleratorAuthorizeResp, error)
	GetUserAcceleratorInfo(ctx context.Context, in *GetUserAcceleratorInfoReq, opts ...grpc.CallOption) (*GetUserAcceleratorInfoResp, error)
	GetNewbieOrderByUids(ctx context.Context, in *GetNewbieOrderByUidsReq, opts ...grpc.CallOption) (*GetNewbieOrderByUidsResp, error)
}

type gameAcceleratorClient struct {
	cc *grpc.ClientConn
}

func NewGameAcceleratorClient(cc *grpc.ClientConn) GameAcceleratorClient {
	return &gameAcceleratorClient{cc}
}

func (c *gameAcceleratorClient) UpdateAccelerateEntrance(ctx context.Context, in *UpdateAccelerateEntranceRequest, opts ...grpc.CallOption) (*UpdateAccelerateEntranceResponse, error) {
	out := new(UpdateAccelerateEntranceResponse)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/UpdateAccelerateEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) GetAccelerateEntrance(ctx context.Context, in *GetAccelerateEntranceRequest, opts ...grpc.CallOption) (*GetAccelerateEntranceResponse, error) {
	out := new(GetAccelerateEntranceResponse)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/GetAccelerateEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) SearchAccelerateGame(ctx context.Context, in *SearchAccelerateGameRequest, opts ...grpc.CallOption) (*SearchAccelerateGameResponse, error) {
	out := new(SearchAccelerateGameResponse)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/SearchAccelerateGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) UpdateGame(ctx context.Context, in *UpdateGameRequest, opts ...grpc.CallOption) (*UpdateGameResponse, error) {
	out := new(UpdateGameResponse)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/UpdateGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) SyncAccelerateGameList(ctx context.Context, in *SyncAccelerateGameListRequest, opts ...grpc.CallOption) (*SyncAccelerateGameListResponse, error) {
	out := new(SyncAccelerateGameListResponse)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/SyncAccelerateGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) GetGames(ctx context.Context, in *GetGamesRequest, opts ...grpc.CallOption) (*GetGamesResponse, error) {
	out := new(GetGamesResponse)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/GetGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) GetAcceleratorOrder(ctx context.Context, in *GetAcceleratorOrderReq, opts ...grpc.CallOption) (*GetAcceleratorOrderResp, error) {
	out := new(GetAcceleratorOrderResp)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/GetAcceleratorOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) AdminAddUserAccelerator(ctx context.Context, in *AdminAddUserAcceleratorReq, opts ...grpc.CallOption) (*AdminAddUserAcceleratorResp, error) {
	out := new(AdminAddUserAcceleratorResp)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/AdminAddUserAccelerator", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) GetPreviewExpireTime(ctx context.Context, in *GetPreviewExpireTimeReq, opts ...grpc.CallOption) (*GetPreviewExpireTimeResp, error) {
	out := new(GetPreviewExpireTimeResp)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/GetPreviewExpireTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) GetAcceleratorToken(ctx context.Context, in *GetAcceleratorTokenReq, opts ...grpc.CallOption) (*GetAcceleratorTokenResp, error) {
	out := new(GetAcceleratorTokenResp)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/GetAcceleratorToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) GetAcceleratorUserList(ctx context.Context, in *GetAcceleratorUserListReq, opts ...grpc.CallOption) (*GetAcceleratorUserListResp, error) {
	out := new(GetAcceleratorUserListResp)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/GetAcceleratorUserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) GetUserAcceleratorLog(ctx context.Context, in *GetUserAcceleratorLogReq, opts ...grpc.CallOption) (*GetUserAcceleratorLogResp, error) {
	out := new(GetUserAcceleratorLogResp)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/GetUserAcceleratorLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) BanUserUseAccelerator(ctx context.Context, in *BanUserUseAcceleratorReq, opts ...grpc.CallOption) (*BanUserUseAcceleratorResp, error) {
	out := new(BanUserUseAcceleratorResp)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/BanUserUseAccelerator", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) AddUserAcceleratorAuthorize(ctx context.Context, in *AddUserAcceleratorAuthorizeReq, opts ...grpc.CallOption) (*AddUserAcceleratorAuthorizeResp, error) {
	out := new(AddUserAcceleratorAuthorizeResp)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/AddUserAcceleratorAuthorize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) GetUserAcceleratorInfo(ctx context.Context, in *GetUserAcceleratorInfoReq, opts ...grpc.CallOption) (*GetUserAcceleratorInfoResp, error) {
	out := new(GetUserAcceleratorInfoResp)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/GetUserAcceleratorInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameAcceleratorClient) GetNewbieOrderByUids(ctx context.Context, in *GetNewbieOrderByUidsReq, opts ...grpc.CallOption) (*GetNewbieOrderByUidsResp, error) {
	out := new(GetNewbieOrderByUidsResp)
	err := c.cc.Invoke(ctx, "/game_accelerator.GameAccelerator/GetNewbieOrderByUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameAcceleratorServer is the server API for GameAccelerator service.
type GameAcceleratorServer interface {
	// 更新入口信息
	UpdateAccelerateEntrance(context.Context, *UpdateAccelerateEntranceRequest) (*UpdateAccelerateEntranceResponse, error)
	// 获取入口信息
	GetAccelerateEntrance(context.Context, *GetAccelerateEntranceRequest) (*GetAccelerateEntranceResponse, error)
	// 搜索游戏列表
	SearchAccelerateGame(context.Context, *SearchAccelerateGameRequest) (*SearchAccelerateGameResponse, error)
	// 更新游戏信息
	UpdateGame(context.Context, *UpdateGameRequest) (*UpdateGameResponse, error)
	// 同步游戏信息
	SyncAccelerateGameList(context.Context, *SyncAccelerateGameListRequest) (*SyncAccelerateGameListResponse, error)
	// 根据游戏id列表获取游戏信息
	GetGames(context.Context, *GetGamesRequest) (*GetGamesResponse, error)
	// 授权相关
	GetAcceleratorOrder(context.Context, *GetAcceleratorOrderReq) (*GetAcceleratorOrderResp, error)
	AdminAddUserAccelerator(context.Context, *AdminAddUserAcceleratorReq) (*AdminAddUserAcceleratorResp, error)
	GetPreviewExpireTime(context.Context, *GetPreviewExpireTimeReq) (*GetPreviewExpireTimeResp, error)
	// 获取用户token
	GetAcceleratorToken(context.Context, *GetAcceleratorTokenReq) (*GetAcceleratorTokenResp, error)
	// 加速器用户管理
	GetAcceleratorUserList(context.Context, *GetAcceleratorUserListReq) (*GetAcceleratorUserListResp, error)
	GetUserAcceleratorLog(context.Context, *GetUserAcceleratorLogReq) (*GetUserAcceleratorLogResp, error)
	BanUserUseAccelerator(context.Context, *BanUserUseAcceleratorReq) (*BanUserUseAcceleratorResp, error)
	// 活动接口
	AddUserAcceleratorAuthorize(context.Context, *AddUserAcceleratorAuthorizeReq) (*AddUserAcceleratorAuthorizeResp, error)
	GetUserAcceleratorInfo(context.Context, *GetUserAcceleratorInfoReq) (*GetUserAcceleratorInfoResp, error)
	GetNewbieOrderByUids(context.Context, *GetNewbieOrderByUidsReq) (*GetNewbieOrderByUidsResp, error)
}

func RegisterGameAcceleratorServer(s *grpc.Server, srv GameAcceleratorServer) {
	s.RegisterService(&_GameAccelerator_serviceDesc, srv)
}

func _GameAccelerator_UpdateAccelerateEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAccelerateEntranceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).UpdateAccelerateEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/UpdateAccelerateEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).UpdateAccelerateEntrance(ctx, req.(*UpdateAccelerateEntranceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_GetAccelerateEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccelerateEntranceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).GetAccelerateEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/GetAccelerateEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).GetAccelerateEntrance(ctx, req.(*GetAccelerateEntranceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_SearchAccelerateGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAccelerateGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).SearchAccelerateGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/SearchAccelerateGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).SearchAccelerateGame(ctx, req.(*SearchAccelerateGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_UpdateGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).UpdateGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/UpdateGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).UpdateGame(ctx, req.(*UpdateGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_SyncAccelerateGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncAccelerateGameListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).SyncAccelerateGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/SyncAccelerateGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).SyncAccelerateGameList(ctx, req.(*SyncAccelerateGameListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_GetGames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).GetGames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/GetGames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).GetGames(ctx, req.(*GetGamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_GetAcceleratorOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAcceleratorOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).GetAcceleratorOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/GetAcceleratorOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).GetAcceleratorOrder(ctx, req.(*GetAcceleratorOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_AdminAddUserAccelerator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminAddUserAcceleratorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).AdminAddUserAccelerator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/AdminAddUserAccelerator",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).AdminAddUserAccelerator(ctx, req.(*AdminAddUserAcceleratorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_GetPreviewExpireTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPreviewExpireTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).GetPreviewExpireTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/GetPreviewExpireTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).GetPreviewExpireTime(ctx, req.(*GetPreviewExpireTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_GetAcceleratorToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAcceleratorTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).GetAcceleratorToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/GetAcceleratorToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).GetAcceleratorToken(ctx, req.(*GetAcceleratorTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_GetAcceleratorUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAcceleratorUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).GetAcceleratorUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/GetAcceleratorUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).GetAcceleratorUserList(ctx, req.(*GetAcceleratorUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_GetUserAcceleratorLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAcceleratorLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).GetUserAcceleratorLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/GetUserAcceleratorLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).GetUserAcceleratorLog(ctx, req.(*GetUserAcceleratorLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_BanUserUseAccelerator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BanUserUseAcceleratorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).BanUserUseAccelerator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/BanUserUseAccelerator",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).BanUserUseAccelerator(ctx, req.(*BanUserUseAcceleratorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_AddUserAcceleratorAuthorize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserAcceleratorAuthorizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).AddUserAcceleratorAuthorize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/AddUserAcceleratorAuthorize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).AddUserAcceleratorAuthorize(ctx, req.(*AddUserAcceleratorAuthorizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_GetUserAcceleratorInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAcceleratorInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).GetUserAcceleratorInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/GetUserAcceleratorInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).GetUserAcceleratorInfo(ctx, req.(*GetUserAcceleratorInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameAccelerator_GetNewbieOrderByUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewbieOrderByUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameAcceleratorServer).GetNewbieOrderByUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_accelerator.GameAccelerator/GetNewbieOrderByUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameAcceleratorServer).GetNewbieOrderByUids(ctx, req.(*GetNewbieOrderByUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameAccelerator_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_accelerator.GameAccelerator",
	HandlerType: (*GameAcceleratorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateAccelerateEntrance",
			Handler:    _GameAccelerator_UpdateAccelerateEntrance_Handler,
		},
		{
			MethodName: "GetAccelerateEntrance",
			Handler:    _GameAccelerator_GetAccelerateEntrance_Handler,
		},
		{
			MethodName: "SearchAccelerateGame",
			Handler:    _GameAccelerator_SearchAccelerateGame_Handler,
		},
		{
			MethodName: "UpdateGame",
			Handler:    _GameAccelerator_UpdateGame_Handler,
		},
		{
			MethodName: "SyncAccelerateGameList",
			Handler:    _GameAccelerator_SyncAccelerateGameList_Handler,
		},
		{
			MethodName: "GetGames",
			Handler:    _GameAccelerator_GetGames_Handler,
		},
		{
			MethodName: "GetAcceleratorOrder",
			Handler:    _GameAccelerator_GetAcceleratorOrder_Handler,
		},
		{
			MethodName: "AdminAddUserAccelerator",
			Handler:    _GameAccelerator_AdminAddUserAccelerator_Handler,
		},
		{
			MethodName: "GetPreviewExpireTime",
			Handler:    _GameAccelerator_GetPreviewExpireTime_Handler,
		},
		{
			MethodName: "GetAcceleratorToken",
			Handler:    _GameAccelerator_GetAcceleratorToken_Handler,
		},
		{
			MethodName: "GetAcceleratorUserList",
			Handler:    _GameAccelerator_GetAcceleratorUserList_Handler,
		},
		{
			MethodName: "GetUserAcceleratorLog",
			Handler:    _GameAccelerator_GetUserAcceleratorLog_Handler,
		},
		{
			MethodName: "BanUserUseAccelerator",
			Handler:    _GameAccelerator_BanUserUseAccelerator_Handler,
		},
		{
			MethodName: "AddUserAcceleratorAuthorize",
			Handler:    _GameAccelerator_AddUserAcceleratorAuthorize_Handler,
		},
		{
			MethodName: "GetUserAcceleratorInfo",
			Handler:    _GameAccelerator_GetUserAcceleratorInfo_Handler,
		},
		{
			MethodName: "GetNewbieOrderByUids",
			Handler:    _GameAccelerator_GetNewbieOrderByUids_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/game-accelerator/game-accelerator.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/game-accelerator/game-accelerator.proto", fileDescriptor_game_accelerator_e36673c1364de5ca)
}

var fileDescriptor_game_accelerator_e36673c1364de5ca = []byte{
	// 2850 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x1a, 0x4d, 0x6f, 0xe3, 0xc6,
	0xd5, 0xa4, 0x3e, 0x2c, 0x3d, 0xf9, 0x83, 0x3b, 0xf6, 0x7a, 0xb5, 0xda, 0x0f, 0x7b, 0x99, 0x4d,
	0xe0, 0x38, 0x59, 0x3b, 0x71, 0x92, 0xb6, 0xc8, 0x07, 0x1a, 0xd9, 0xa2, 0xbd, 0x6c, 0x6c, 0xc9,
	0xa0, 0xe4, 0x75, 0xb7, 0x08, 0x40, 0x70, 0xc5, 0xb1, 0x4c, 0x58, 0x12, 0xb9, 0xfc, 0xb0, 0xd7,
	0x5b, 0xf4, 0x50, 0xa4, 0x87, 0x5e, 0xd3, 0x63, 0x7f, 0x41, 0x9a, 0x63, 0x0f, 0xbd, 0xb4, 0x97,
	0xfe, 0x81, 0xa2, 0x28, 0x50, 0xf4, 0xd8, 0x5e, 0xfb, 0x2f, 0x8a, 0x99, 0x21, 0x29, 0x4a, 0x1a,
	0x6a, 0xe5, 0xdd, 0x00, 0x3d, 0x89, 0xf3, 0xde, 0x9b, 0x79, 0x6f, 0xde, 0xf7, 0xcc, 0x08, 0x7e,
	0xe4, 0xfb, 0x5b, 0xcf, 0x03, 0xab, 0x7d, 0xee, 0x59, 0xdd, 0x0b, 0xec, 0x6e, 0x75, 0x8c, 0x1e,
	0x7e, 0x64, 0xb4, 0xdb, 0xb8, 0x8b, 0x5d, 0xc3, 0xb7, 0xc7, 0x01, 0x9b, 0x8e, 0x6b, 0xfb, 0x36,
	0x92, 0x08, 0x5c, 0x4f, 0xc0, 0xe5, 0x5f, 0x02, 0xaa, 0x46, 0x43, 0xac, 0xf4, 0x7d, 0xd7, 0xe8,
	0xb7, 0x31, 0xfa, 0x29, 0x14, 0xbd, 0x33, 0xfb, 0x52, 0xf7, 0xaf, 0x1c, 0x5c, 0x16, 0xd6, 0x84,
	0xf5, 0x85, 0x6d, 0x79, 0x73, 0x74, 0xee, 0x66, 0x44, 0xde, 0x3c, 0xb3, 0x2f, 0x5b, 0x57, 0x0e,
	0xd6, 0x0a, 0x5e, 0xf8, 0x85, 0x1e, 0xc2, 0x42, 0xdb, 0xb5, 0x2f, 0x4d, 0xbd, 0xe3, 0xda, 0x81,
	0xa3, 0x5b, 0x66, 0x59, 0x5c, 0x13, 0xd6, 0x8b, 0xda, 0x1c, 0x85, 0xee, 0x13, 0xa0, 0x6a, 0xca,
	0x6d, 0x58, 0x3d, 0x76, 0x4c, 0xc3, 0xc7, 0xe3, 0x22, 0x68, 0xf8, 0x79, 0x80, 0x3d, 0x1f, 0x7d,
	0x09, 0x05, 0x1c, 0x82, 0xa8, 0x20, 0xa5, 0xed, 0x87, 0xe3, 0x82, 0x70, 0xa6, 0xc7, 0xb3, 0x64,
	0x19, 0xd6, 0xd2, 0x99, 0x78, 0x8e, 0xdd, 0xf7, 0xb0, 0x7c, 0x1f, 0xee, 0xee, 0x63, 0x3f, 0x55,
	0x0a, 0xd9, 0x80, 0x7b, 0x29, 0x78, 0xb6, 0xc0, 0x0f, 0x20, 0xe6, 0xdf, 0x44, 0xc8, 0xee, 0x1b,
	0x3d, 0x8c, 0x16, 0x40, 0xb4, 0x4c, 0xba, 0xc8, 0xbc, 0x26, 0x5a, 0x26, 0x42, 0x90, 0xed, 0x1b,
	0x3d, 0x1c, 0x2a, 0x90, 0x7e, 0x13, 0x58, 0xd7, 0xee, 0xd8, 0xe5, 0x0c, 0x83, 0x91, 0x6f, 0xb4,
	0x01, 0x37, 0xbc, 0xc0, 0x71, 0x6c, 0xd7, 0x27, 0x4c, 0xf5, 0x9e, 0x6d, 0x62, 0xaf, 0x9c, 0x5d,
	0xcb, 0xac, 0xcf, 0x6b, 0x8b, 0x21, 0xa2, 0xda, 0x6e, 0x1f, 0x12, 0x30, 0xfa, 0x1c, 0x72, 0x9e,
	0x6f, 0xf8, 0xb8, 0x9c, 0xa3, 0xb6, 0x7d, 0x67, 0x82, 0xac, 0xb6, 0x4b, 0xa4, 0x6a, 0x12, 0x6a,
	0x8d, 0x4d, 0x42, 0x9f, 0x42, 0xbe, 0x6b, 0x3c, 0xc3, 0x5d, 0xaf, 0x9c, 0x5f, 0xcb, 0xf0, 0x5d,
	0x23, 0x31, 0xfd, 0x80, 0x90, 0x6a, 0xe1, 0x0c, 0x74, 0x07, 0x8a, 0xe7, 0xf8, 0x4a, 0xbf, 0xb4,
	0x5d, 0xd3, 0x2b, 0xcf, 0xae, 0x65, 0xd6, 0x8b, 0x5a, 0xe1, 0x1c, 0x5f, 0x9d, 0x90, 0x31, 0x5a,
	0x85, 0x52, 0x40, 0x4d, 0xa5, 0xfb, 0x56, 0x0f, 0x97, 0x0b, 0x6b, 0xc2, 0x7a, 0x46, 0x03, 0x06,
	0x6a, 0x59, 0x3d, 0x4c, 0xf6, 0xe8, 0xe2, 0x36, 0xee, 0xfb, 0x7a, 0xe0, 0x61, 0x57, 0x6f, 0xdb,
	0x41, 0xdf, 0x2f, 0x17, 0xa9, 0xaa, 0x16, 0x19, 0xe2, 0xd8, 0xc3, 0xee, 0x2e, 0x01, 0xcb, 0x7f,
	0xcd, 0xc2, 0x9d, 0x26, 0x36, 0xdc, 0xf6, 0xd9, 0x40, 0xef, 0x64, 0x2b, 0x91, 0x67, 0xdd, 0x82,
	0xd9, 0xae, 0xe1, 0xf9, 0x7a, 0xa8, 0xec, 0x22, 0x11, 0xd1, 0xf3, 0x55, 0x13, 0x2d, 0x43, 0xae,
	0x6b, 0xf5, 0x2c, 0x9f, 0x6a, 0x7c, 0x5e, 0x63, 0x03, 0x74, 0x0f, 0xc0, 0xb3, 0x03, 0xb7, 0x8d,
	0x75, 0xdf, 0xe8, 0x84, 0x8a, 0x2f, 0x32, 0x48, 0xcb, 0xe8, 0x20, 0x1d, 0xe6, 0x4f, 0xad, 0xae,
	0x8f, 0x5d, 0xdd, 0x76, 0x7c, 0xcb, 0xee, 0x97, 0xb3, 0xd4, 0x0b, 0x3e, 0x1d, 0x57, 0xcd, 0x04,
	0x99, 0x36, 0xf7, 0xe8, 0x12, 0x0d, 0xba, 0x82, 0x36, 0x77, 0x9a, 0x18, 0xa1, 0x75, 0x90, 0x4e,
	0x6d, 0xc2, 0xde, 0xea, 0x7b, 0xd8, 0x25, 0x62, 0x7b, 0xe5, 0x1c, 0xb5, 0xee, 0x02, 0x85, 0xab,
	0x14, 0xac, 0x9a, 0x1e, 0xb2, 0xe0, 0x06, 0x7e, 0xe1, 0xbb, 0x86, 0x6e, 0xf5, 0x4f, 0xed, 0x48,
	0x9c, 0x3c, 0x15, 0xe7, 0x8b, 0xeb, 0x89, 0xa3, 0x90, 0x65, 0xd4, 0xfe, 0xa9, 0x1d, 0x4a, 0xb4,
	0x88, 0x87, 0x01, 0x15, 0x07, 0xe6, 0x92, 0x22, 0xc7, 0xbe, 0x2a, 0x24, 0x7c, 0x95, 0xf9, 0xb3,
	0x18, 0xfb, 0x73, 0xec, 0x7b, 0x99, 0xd7, 0xf0, 0xbd, 0x4a, 0x17, 0x16, 0x47, 0xa4, 0x42, 0x1f,
	0xc1, 0x4a, 0x1f, 0x63, 0x53, 0x1f, 0xf7, 0x0c, 0x22, 0x46, 0x41, 0x5b, 0x22, 0x58, 0x6d, 0xd8,
	0x3b, 0x48, 0x82, 0xa2, 0x93, 0x4c, 0x5b, 0x67, 0x6a, 0xa6, 0x12, 0x16, 0xb4, 0x39, 0x02, 0xad,
	0xd9, 0x6c, 0x57, 0x32, 0x86, 0xbb, 0x7c, 0xfd, 0x84, 0x61, 0xff, 0x3e, 0xe4, 0x88, 0xf4, 0x5e,
	0x59, 0x58, 0xcb, 0xac, 0x97, 0xb6, 0x57, 0xc6, 0xf7, 0x42, 0xc9, 0x19, 0x51, 0xd2, 0xe3, 0xc4,
	0xa4, 0xc7, 0xc9, 0x7f, 0x10, 0xe1, 0x06, 0xcb, 0x51, 0x49, 0x07, 0xfd, 0x1c, 0xb2, 0x9d, 0x48,
	0x99, 0xa5, 0xed, 0xf5, 0xf1, 0xb5, 0xc7, 0xa6, 0x30, 0x6e, 0x74, 0x56, 0xe5, 0x9f, 0xc2, 0x1b,
	0xe6, 0x93, 0xd8, 0x4e, 0xd9, 0x37, 0xcb, 0x11, 0xb9, 0x37, 0xcb, 0x11, 0xf9, 0xe1, 0x1c, 0x21,
	0x2f, 0x03, 0x4a, 0xee, 0x3b, 0x4c, 0xe0, 0xab, 0x70, 0xaf, 0x79, 0xd5, 0x6f, 0x0f, 0x9b, 0xe9,
	0xc0, 0xf2, 0xfc, 0x28, 0x83, 0xaf, 0xc1, 0xfd, 0x34, 0x82, 0x70, 0x09, 0x03, 0x16, 0xf7, 0xb1,
	0x4f, 0xc0, 0x5e, 0x64, 0x81, 0xdb, 0x50, 0xa0, 0x52, 0x93, 0x58, 0x13, 0x68, 0xac, 0xcd, 0x92,
	0x31, 0x09, 0xb2, 0x74, 0xa7, 0x13, 0x53, 0x9d, 0x4e, 0xfe, 0x12, 0xa4, 0x01, 0x8b, 0xd7, 0x71,
	0x21, 0xf9, 0xb7, 0x22, 0xac, 0x24, 0x2b, 0x91, 0xed, 0x36, 0x5c, 0x13, 0xbb, 0x1a, 0x7e, 0x8e,
	0x24, 0xc8, 0x04, 0xb1, 0xa1, 0xc9, 0x27, 0xda, 0x82, 0x65, 0xff, 0xcc, 0x72, 0x4d, 0xdd, 0x31,
	0x5c, 0xff, 0x4a, 0xb7, 0x09, 0xe5, 0xc0, 0xf9, 0x6e, 0x50, 0xdc, 0x11, 0x41, 0xd1, 0x35, 0x54,
	0x93, 0xe6, 0x38, 0xdf, 0x70, 0x7d, 0x96, 0x7e, 0x33, 0x34, 0xfd, 0x16, 0x29, 0x84, 0x66, 0xdf,
	0xdb, 0xa4, 0xc8, 0x99, 0x0c, 0x99, 0xa5, 0xc8, 0x59, 0xdc, 0x37, 0x29, 0xea, 0x4b, 0xc8, 0x13,
	0xbb, 0x07, 0x5e, 0x58, 0x51, 0xd6, 0x27, 0x9a, 0x9b, 0xf2, 0x6b, 0x52, 0x7a, 0x2d, 0x9c, 0x47,
	0x5c, 0xd0, 0x31, 0x3a, 0x98, 0x26, 0xaa, 0x9c, 0x46, 0xbf, 0x89, 0x23, 0x90, 0x5f, 0xdd, 0xb3,
	0x5e, 0xe2, 0xf2, 0x2c, 0x45, 0x14, 0x08, 0xa0, 0x69, 0xbd, 0xc4, 0xf2, 0xb7, 0x59, 0x58, 0x1e,
	0x5d, 0x53, 0xf5, 0x71, 0x8f, 0x88, 0x19, 0x6f, 0x95, 0x25, 0xa2, 0x59, 0x3b, 0xdc, 0x60, 0xa8,
	0x23, 0x71, 0xa0, 0x23, 0x05, 0x80, 0x11, 0xd3, 0x56, 0x67, 0x9a, 0x94, 0x44, 0x19, 0xd1, 0x76,
	0xa7, 0x68, 0x47, 0x9f, 0xa4, 0x72, 0xb1, 0x65, 0x98, 0x0f, 0x64, 0x29, 0x03, 0xb6, 0x32, 0xcb,
	0x37, 0x6f, 0xae, 0xa0, 0x55, 0x28, 0xb5, 0x5d, 0x1c, 0x17, 0xc7, 0x3c, 0x2b, 0x8e, 0x0c, 0x44,
	0x6d, 0xb0, 0x0a, 0x25, 0xfc, 0xc2, 0xb1, 0xdc, 0x90, 0x60, 0x96, 0x11, 0x30, 0x10, 0x25, 0xa8,
	0xc3, 0xbc, 0xd3, 0x35, 0xfc, 0x53, 0xdb, 0xed, 0xb1, 0xed, 0x16, 0xa8, 0x28, 0xef, 0x4e, 0x14,
	0xe5, 0x28, 0x9c, 0x41, 0x77, 0x3c, 0xe7, 0x24, 0x46, 0xa8, 0x02, 0x05, 0xdb, 0x61, 0x54, 0xb4,
	0x08, 0x17, 0xb5, 0x78, 0x8c, 0x56, 0x20, 0xef, 0xe2, 0x9e, 0xe1, 0x9e, 0x97, 0x81, 0xa5, 0x3a,
	0x36, 0x42, 0xef, 0x01, 0xb2, 0x03, 0x5f, 0xf7, 0x5d, 0xc3, 0xc4, 0x03, 0x8f, 0x2c, 0x51, 0x9a,
	0x45, 0x3b, 0xf0, 0x5b, 0x04, 0x11, 0xf9, 0x63, 0x9a, 0x03, 0xcf, 0xa5, 0x38, 0xb0, 0xfc, 0x6b,
	0x01, 0x6e, 0x71, 0xc3, 0xc3, 0x73, 0x88, 0x7a, 0x7c, 0xdb, 0x37, 0xba, 0x89, 0xda, 0x90, 0xd1,
	0x80, 0x82, 0x98, 0x89, 0x62, 0x57, 0xe8, 0x5a, 0x1e, 0x09, 0x63, 0x12, 0x8e, 0x53, 0xb8, 0x02,
	0xf1, 0xb9, 0xd0, 0x15, 0x48, 0x3e, 0x91, 0xff, 0x2b, 0xc0, 0xed, 0x61, 0x19, 0x48, 0x02, 0x08,
	0x73, 0x11, 0x27, 0x4a, 0xb7, 0xe1, 0x66, 0x0f, 0xf7, 0x9e, 0x61, 0xd7, 0x3b, 0xb3, 0x1c, 0x3d,
	0x11, 0x7f, 0x22, 0x95, 0x70, 0x69, 0x80, 0x6c, 0xc6, 0x91, 0xb8, 0x09, 0x09, 0xb0, 0x1e, 0x07,
	0x25, 0x8b, 0xd8, 0x1b, 0x03, 0x94, 0x12, 0x86, 0xe7, 0x17, 0x50, 0xa2, 0x19, 0x2a, 0x74, 0x41,
	0x96, 0xd1, 0xef, 0x72, 0x2a, 0x8a, 0x17, 0xbb, 0x1d, 0x04, 0xf1, 0x77, 0xb2, 0x70, 0xe5, 0x86,
	0x0a, 0xd7, 0x9f, 0x44, 0x58, 0x1a, 0xd9, 0x28, 0x0d, 0xc1, 0xf1, 0x5d, 0xee, 0xc2, 0x3c, 0x95,
	0xc0, 0x32, 0x71, 0xdf, 0xb7, 0xfc, 0x2b, 0xba, 0xbb, 0x85, 0xed, 0xfb, 0x7c, 0x19, 0xd4, 0x90,
	0x4a, 0x9b, 0x0b, 0x12, 0xa3, 0xd1, 0x6d, 0x64, 0xae, 0xb9, 0x8d, 0x91, 0x08, 0xca, 0x8e, 0x45,
	0xd0, 0xc7, 0xb0, 0x92, 0x54, 0x6b, 0x22, 0x98, 0x72, 0x94, 0x76, 0x39, 0xa1, 0xd9, 0x41, 0x58,
	0x6d, 0xc3, 0x4d, 0x23, 0xf0, 0xcf, 0x6c, 0xd7, 0x7a, 0x89, 0x87, 0x26, 0xb1, 0x10, 0x5d, 0x8a,
	0x91, 0x83, 0x39, 0xf2, 0xef, 0x05, 0xa8, 0xa4, 0x39, 0xc9, 0x34, 0xbe, 0xba, 0x03, 0x45, 0xaa,
	0x89, 0x84, 0xab, 0xbe, 0x3d, 0xd1, 0x55, 0x23, 0xd3, 0x68, 0x85, 0x20, 0x64, 0x94, 0xb4, 0x6a,
	0x66, 0xc8, 0xaa, 0x7f, 0x14, 0xa0, 0xbc, 0x8f, 0x69, 0xdd, 0x4a, 0xd6, 0x68, 0xbb, 0x43, 0x1c,
	0x18, 0x41, 0xd6, 0xf7, 0xe3, 0xcc, 0x4a, 0xbf, 0xc9, 0x4a, 0x86, 0xe3, 0xd0, 0x32, 0x19, 0x36,
	0x36, 0x86, 0xe3, 0x90, 0x2a, 0xf9, 0xfa, 0x05, 0x25, 0x2a, 0x07, 0xb9, 0xb4, 0x72, 0x90, 0x1f,
	0x29, 0x07, 0x7f, 0xc9, 0xd2, 0xb0, 0xe3, 0x09, 0xed, 0x39, 0x64, 0xb9, 0xb6, 0x6d, 0xb2, 0x5e,
	0x2a, 0xa7, 0xd1, 0x6f, 0x54, 0x86, 0xd9, 0x1e, 0xf6, 0x3c, 0xc2, 0x85, 0x49, 0x1d, 0x0d, 0x51,
	0x0d, 0xb2, 0xa6, 0xe1, 0x1b, 0x54, 0xe0, 0xd2, 0xf6, 0x07, 0x9c, 0x92, 0x9c, 0xc6, 0x68, 0xb3,
	0x66, 0xf8, 0x86, 0x46, 0x67, 0x57, 0xbe, 0xcf, 0x40, 0x96, 0x0c, 0xd1, 0x57, 0x90, 0xb3, 0x7c,
	0xdc, 0x8b, 0x4a, 0xfc, 0x27, 0xd7, 0x5d, 0x6f, 0x93, 0x1a, 0x8e, 0xad, 0x41, 0x4e, 0x27, 0xd4,
	0x0f, 0xa8, 0xcc, 0x39, 0x8d, 0x0d, 0x88, 0x26, 0x1d, 0xec, 0xea, 0x54, 0x65, 0x19, 0x8a, 0x98,
	0x75, 0xb0, 0x7b, 0x44, 0x36, 0xf3, 0x00, 0xe6, 0xda, 0x81, 0xeb, 0x92, 0x2e, 0x85, 0xa2, 0xb3,
	0x14, 0x5d, 0x0a, 0x61, 0x94, 0x64, 0x15, 0x4a, 0x1e, 0x76, 0x2f, 0x48, 0x15, 0x8c, 0x9c, 0xbd,
	0xa8, 0x01, 0x03, 0x11, 0x6b, 0x54, 0xfe, 0x2d, 0x40, 0x96, 0x06, 0x36, 0x82, 0xac, 0xd5, 0xc3,
	0x56, 0x64, 0x7d, 0xf2, 0x3d, 0x6c, 0xfd, 0xcc, 0x44, 0xeb, 0x17, 0x27, 0x59, 0xbf, 0x38, 0xb0,
	0xfe, 0x6d, 0x28, 0x9c, 0x76, 0xed, 0x4b, 0xd2, 0x56, 0x85, 0xd2, 0xcc, 0x92, 0xf1, 0xb1, 0x47,
	0x9d, 0xa0, 0x67, 0xbc, 0xd0, 0x2f, 0x2d, 0xd3, 0x3f, 0xa3, 0x4e, 0x50, 0xd4, 0x0a, 0x3d, 0xe3,
	0xc5, 0x09, 0x19, 0x53, 0xa4, 0xd5, 0x0f, 0x91, 0xb3, 0x21, 0xd2, 0xea, 0xc7, 0x48, 0xe3, 0xa2,
	0x13, 0x22, 0x0b, 0x0c, 0x69, 0x5c, 0x74, 0x28, 0x52, 0xfe, 0xb3, 0x00, 0xe5, 0x1d, 0xa3, 0x4f,
	0xac, 0x70, 0xec, 0xe1, 0x84, 0x21, 0xf8, 0x49, 0x5b, 0x85, 0xbc, 0xd1, 0xa6, 0x07, 0x2b, 0x96,
	0xc7, 0x3e, 0x1c, 0xb7, 0x69, 0xda, 0x6a, 0x9b, 0x55, 0x3a, 0x51, 0x0b, 0x17, 0x90, 0x77, 0x20,
	0xcf, 0x20, 0x68, 0x05, 0x50, 0x75, 0xb7, 0xa5, 0x36, 0xea, 0xfa, 0x71, 0xbd, 0x79, 0xa4, 0xec,
	0xaa, 0x7b, 0xaa, 0x52, 0x93, 0x66, 0xd0, 0x02, 0x40, 0x08, 0xdf, 0xa9, 0xd6, 0x25, 0x01, 0x49,
	0x30, 0x17, 0xd3, 0x11, 0x88, 0x28, 0xdf, 0x81, 0xdb, 0x29, 0xec, 0x3c, 0x47, 0xfe, 0x8f, 0x08,
	0x95, 0xaa, 0xd9, 0xb3, 0xfa, 0x55, 0xd3, 0x1c, 0xf1, 0xb2, 0x30, 0xa0, 0x83, 0x41, 0x83, 0x4b,
	0xbf, 0x87, 0x2a, 0xbb, 0x38, 0x52, 0xd9, 0xbf, 0x86, 0x95, 0xc4, 0x36, 0xf5, 0xd7, 0xee, 0x9e,
	0x96, 0x0d, 0x0e, 0xf4, 0xd5, 0x8d, 0xd4, 0xa0, 0xb1, 0xc8, 0x0d, 0x35, 0x16, 0x63, 0xcd, 0x4d,
	0xfe, 0xcd, 0x9a, 0x1b, 0x7e, 0xa3, 0x32, 0xcb, 0x6d, 0x54, 0xe4, 0x27, 0xb0, 0x5c, 0x35, 0xcd,
	0x61, 0xc5, 0x07, 0x5d, 0x9f, 0xe3, 0x38, 0x51, 0x22, 0x12, 0xf9, 0x89, 0x28, 0x33, 0x94, 0x88,
	0xe4, 0xef, 0x04, 0xb8, 0x93, 0x6a, 0x3a, 0x56, 0x27, 0xbc, 0xa0, 0xdd, 0xc6, 0x9e, 0xa7, 0xf7,
	0x83, 0x5e, 0xc8, 0x07, 0x42, 0x50, 0x3d, 0xe8, 0xa1, 0x55, 0x28, 0x9e, 0x1a, 0x56, 0x57, 0x0f,
	0xa2, 0xe8, 0x9c, 0xdf, 0x11, 0xcb, 0x82, 0x56, 0x20, 0xc0, 0x63, 0x62, 0xe9, 0x7d, 0x28, 0x51,
	0x02, 0x97, 0x0a, 0x5c, 0xce, 0xa4, 0x76, 0x3d, 0x9c, 0xed, 0x69, 0x40, 0xa6, 0xb2, 0x6f, 0xf9,
	0x7b, 0xd6, 0x7a, 0x1d, 0xb9, 0xf8, 0xc2, 0xc2, 0x97, 0x83, 0x52, 0xc7, 0x8f, 0x9f, 0x74, 0x27,
	0x12, 0x7f, 0x78, 0x27, 0xca, 0x8c, 0x3a, 0x91, 0xfc, 0x19, 0x2d, 0x70, 0x1c, 0x59, 0x99, 0x4e,
	0x93, 0x45, 0x9c, 0x65, 0xba, 0x44, 0x1b, 0x2d, 0x1f, 0x8f, 0x1e, 0xc1, 0x5a, 0xf6, 0x39, 0xee,
	0xf3, 0xf7, 0x19, 0xe5, 0x4b, 0x31, 0x91, 0x2f, 0x6f, 0x42, 0x9e, 0xe5, 0xcb, 0xd0, 0xda, 0x39,
	0x9a, 0x2e, 0xe5, 0xad, 0xd1, 0xd6, 0x35, 0x5c, 0xd6, 0x73, 0x58, 0xce, 0x3f, 0xc7, 0xfd, 0x50,
	0x18, 0x36, 0x90, 0x7f, 0x97, 0x81, 0xfb, 0xe3, 0x7e, 0x51, 0x8d, 0xba, 0x0d, 0xbe, 0x40, 0x3f,
	0x83, 0xd2, 0xb3, 0xc0, 0xb3, 0xfa, 0xc4, 0x65, 0xc2, 0x93, 0xd0, 0xab, 0x82, 0x64, 0x27, 0xa4,
	0xa7, 0x0a, 0x87, 0x68, 0xb6, 0x6a, 0xfe, 0xbf, 0x33, 0xc1, 0x58, 0xc4, 0xe7, 0xde, 0x2c, 0xe2,
	0x07, 0x99, 0x25, 0x3f, 0xc5, 0x91, 0x25, 0x25, 0x13, 0x74, 0x60, 0x75, 0xa2, 0x4d, 0x3c, 0x07,
	0xbd, 0xc5, 0x09, 0x5a, 0x1a, 0x95, 0xd7, 0x09, 0x5c, 0x79, 0x8b, 0xd7, 0xee, 0xa8, 0xfd, 0x53,
	0x3b, 0x25, 0xa7, 0xcb, 0x4f, 0x69, 0xc7, 0xc9, 0x9d, 0xe0, 0x39, 0xe8, 0xb3, 0xe1, 0x1e, 0x65,
	0xca, 0x66, 0x92, 0xcd, 0x91, 0x1f, 0x51, 0xd7, 0xad, 0xe3, 0xcb, 0x67, 0x16, 0x53, 0xc4, 0xce,
	0x15, 0x91, 0x31, 0x4d, 0x92, 0x7f, 0xb0, 0xfe, 0x92, 0x43, 0xef, 0x39, 0x48, 0x83, 0x02, 0x59,
	0x54, 0xef, 0x19, 0x4e, 0x28, 0xcb, 0x8f, 0xb9, 0xfd, 0x12, 0x77, 0x36, 0xed, 0x94, 0x0e, 0x0d,
	0x47, 0xe9, 0xfb, 0xee, 0x95, 0x36, 0x6b, 0xb1, 0x51, 0xe5, 0x19, 0xcc, 0x25, 0x11, 0x24, 0x2c,
	0xce, 0xf1, 0x55, 0x14, 0x16, 0xe7, 0xf8, 0x0a, 0x7d, 0x0e, 0xb9, 0x0b, 0xa3, 0x1b, 0xb0, 0xf4,
	0x33, 0xfd, 0xb1, 0x8f, 0x4d, 0xfa, 0x54, 0xfc, 0x89, 0xb0, 0xe1, 0x80, 0x34, 0xfa, 0x1e, 0x82,
	0x64, 0xb8, 0xaf, 0xd4, 0x5b, 0x5a, 0xb5, 0xbe, 0xab, 0xe8, 0xcd, 0xc7, 0x8d, 0x13, 0xbd, 0xf5,
	0xf4, 0x48, 0x19, 0x29, 0xee, 0x15, 0x58, 0xe1, 0xd0, 0x54, 0x0f, 0x0e, 0x24, 0x01, 0xdd, 0x85,
	0x32, 0x07, 0xb7, 0xab, 0x35, 0x4e, 0x6a, 0x92, 0xb8, 0xf1, 0x9d, 0x30, 0x74, 0x01, 0x12, 0x5f,
	0xc1, 0xa1, 0x87, 0xb0, 0x56, 0xdd, 0xdd, 0x55, 0x0e, 0x14, 0xad, 0xda, 0x6a, 0x68, 0xfa, 0x7e,
	0xf5, 0x50, 0xd1, 0x9b, 0xad, 0x6a, 0x2b, 0x66, 0x5d, 0x3d, 0x90, 0x66, 0xd0, 0x03, 0xb8, 0x97,
	0x42, 0x55, 0x6f, 0x68, 0x87, 0x55, 0xc2, 0x3f, 0x9d, 0x64, 0xa7, 0x5a, 0xaf, 0x2b, 0x35, 0x49,
	0x9c, 0xc0, 0xab, 0xb1, 0xb7, 0xa7, 0x37, 0x1f, 0x2b, 0x07, 0x7b, 0x52, 0x66, 0xe3, 0x2b, 0x90,
	0x46, 0x6f, 0xfb, 0xd0, 0x12, 0x2c, 0x1e, 0x54, 0x77, 0x94, 0x83, 0x21, 0xa1, 0x62, 0xa0, 0xa6,
	0xec, 0x36, 0x0e, 0x0f, 0x95, 0x7a, 0x4d, 0x12, 0xd0, 0x3c, 0x14, 0x19, 0xf0, 0x71, 0xa3, 0x25,
	0x89, 0x1b, 0xff, 0x12, 0x60, 0x85, 0x7f, 0x57, 0x82, 0xd6, 0xe1, 0x61, 0x52, 0x9a, 0x86, 0x56,
	0x53, 0x34, 0x2a, 0xce, 0x71, 0x73, 0x44, 0xed, 0x23, 0x72, 0x0f, 0x51, 0x1e, 0x29, 0xf5, 0x9a,
	0x5a, 0xdf, 0x97, 0x84, 0x89, 0x54, 0xcd, 0xe3, 0xdd, 0x5d, 0xa5, 0xd9, 0x94, 0xc4, 0x51, 0x35,
	0x0d, 0x51, 0xed, 0x55, 0xd5, 0x03, 0x29, 0x33, 0x71, 0x21, 0xb5, 0xfe, 0xa4, 0x7a, 0xa0, 0xd6,
	0xa4, 0xec, 0xc6, 0x37, 0xc2, 0xf8, 0x95, 0x16, 0x75, 0xa4, 0x77, 0x40, 0x1e, 0x9f, 0xce, 0x71,
	0x26, 0xae, 0x24, 0x94, 0xae, 0xae, 0x9c, 0xec, 0xa8, 0x8a, 0x24, 0xa0, 0x35, 0xb8, 0x9b, 0x42,
	0x72, 0xd8, 0xa8, 0xb7, 0x1e, 0x4b, 0xe2, 0xc6, 0x39, 0xdc, 0x4a, 0x49, 0x98, 0xe8, 0x5d, 0x78,
	0x3b, 0x39, 0xf9, 0xe8, 0xa0, 0xda, 0xda, 0x6b, 0x68, 0x87, 0x3c, 0x51, 0xde, 0x86, 0x07, 0xe9,
	0xa4, 0x27, 0x6a, 0xbd, 0xd6, 0x38, 0x69, 0x4a, 0xc2, 0xc6, 0xd7, 0x30, 0x97, 0x3c, 0xf0, 0xa3,
	0x7b, 0x70, 0xfb, 0xb8, 0xa9, 0x68, 0xba, 0x5a, 0x53, 0xea, 0x2d, 0xb5, 0xf5, 0x74, 0x64, 0xd5,
	0x15, 0x40, 0xc3, 0xe8, 0x3d, 0x4d, 0x21, 0xbb, 0xba, 0x09, 0x37, 0x86, 0xe1, 0x4f, 0xd4, 0x23,
	0x49, 0xdc, 0xb0, 0x01, 0x06, 0x77, 0x01, 0xe8, 0x0e, 0xdc, 0xa2, 0x44, 0x5c, 0x87, 0x88, 0x56,
	0x0e, 0x91, 0x71, 0x0c, 0xdc, 0x82, 0xa5, 0x24, 0x5c, 0xf9, 0xf9, 0x91, 0xaa, 0x51, 0xcf, 0x5f,
	0x82, 0xc5, 0x24, 0x82, 0x34, 0xe2, 0x99, 0x8d, 0x6f, 0x85, 0x21, 0xe5, 0x25, 0x4b, 0xe7, 0xa8,
	0xf2, 0x76, 0x8e, 0x9b, 0x6a, 0x5d, 0x69, 0x36, 0x79, 0xca, 0x1b, 0xb1, 0xf7, 0x30, 0x29, 0xe9,
	0xfd, 0x9f, 0xa8, 0xad, 0xa7, 0x92, 0x80, 0xde, 0x82, 0xd5, 0x09, 0x74, 0xb5, 0x43, 0xb5, 0x2e,
	0x89, 0xdb, 0x7f, 0x5f, 0x80, 0x45, 0x92, 0x1c, 0x12, 0x72, 0xa1, 0x6f, 0x04, 0x28, 0xa7, 0xbd,
	0x8a, 0xa2, 0x0f, 0xd3, 0x9e, 0x1a, 0x52, 0x1f, 0x48, 0x2b, 0xdb, 0xd7, 0x99, 0x12, 0x5e, 0xb8,
	0xcf, 0xa0, 0x97, 0x70, 0x93, 0xfb, 0xac, 0x8a, 0x36, 0xb9, 0x29, 0x3f, 0x9d, 0xfd, 0xd6, 0xd4,
	0xf4, 0x31, 0xef, 0x4b, 0x58, 0xe6, 0x3d, 0xed, 0xa0, 0x47, 0xd7, 0x7a, 0x22, 0xab, 0x6c, 0x4e,
	0x4b, 0x1e, 0x33, 0x7e, 0x0a, 0x30, 0x78, 0xc0, 0x40, 0x6f, 0x4d, 0xf1, 0xac, 0x53, 0x79, 0x38,
	0x99, 0x28, 0x5e, 0xfa, 0x57, 0xb0, 0xc2, 0x7f, 0xe4, 0x40, 0x1c, 0x05, 0x4d, 0x7c, 0x2f, 0xa9,
	0x7c, 0x30, 0xfd, 0x84, 0x98, 0x7d, 0x13, 0x0a, 0xd1, 0xf3, 0x06, 0x7a, 0xc0, 0xb5, 0x48, 0xf2,
	0x75, 0xa5, 0x22, 0x4f, 0x22, 0x89, 0x17, 0xed, 0xc2, 0x12, 0xe7, 0x46, 0x17, 0xad, 0x4f, 0xb6,
	0xf8, 0xe0, 0x5d, 0xa4, 0xf2, 0xee, 0x94, 0x94, 0x9e, 0x23, 0xcf, 0xa0, 0x17, 0x70, 0x2b, 0xe5,
	0xbc, 0x85, 0xde, 0xe7, 0x1d, 0x8a, 0xd2, 0x4e, 0xd5, 0x95, 0x47, 0xd7, 0xa0, 0xa6, 0x9c, 0x6d,
	0x58, 0xe6, 0x1d, 0x49, 0x10, 0x5f, 0x7c, 0xde, 0x31, 0xab, 0xb2, 0x31, 0x2d, 0x29, 0x65, 0x38,
	0xa6, 0x58, 0x7a, 0xde, 0x78, 0xb5, 0x62, 0xa3, 0xd3, 0xce, 0xab, 0x15, 0x1b, 0x1f, 0x60, 0xe4,
	0x19, 0x14, 0x8c, 0x1e, 0x9a, 0xa2, 0xfb, 0x4e, 0xf4, 0xde, 0xab, 0x96, 0x49, 0x5c, 0x9f, 0x57,
	0xde, 0x9f, 0x9e, 0x98, 0xb2, 0x75, 0x69, 0x86, 0x19, 0xbf, 0x5b, 0x43, 0x1b, 0x53, 0x5f, 0xc2,
	0x3d, 0xaf, 0xbc, 0x77, 0x8d, 0x0b, 0x3b, 0xc6, 0x93, 0x7b, 0x19, 0xc3, 0xe3, 0x99, 0x76, 0x49,
	0xc4, 0xe3, 0x99, 0x7e, 0xc3, 0x33, 0x83, 0x7e, 0x43, 0x2f, 0x0a, 0x52, 0xcf, 0x1d, 0xe8, 0x03,
	0xee, 0x89, 0x7e, 0xc2, 0xd1, 0xb1, 0xf2, 0xe1, 0x35, 0x67, 0x24, 0xac, 0xcc, 0x39, 0x63, 0xa0,
	0xa9, 0x74, 0x18, 0x1e, 0x5f, 0x52, 0xac, 0x9c, 0x72, 0x74, 0x89, 0x63, 0x67, 0xec, 0x44, 0x90,
	0x12, 0x3b, 0xbc, 0x73, 0x4a, 0x4a, 0xec, 0x70, 0x0f, 0x19, 0xf2, 0xcc, 0xce, 0xc7, 0xbf, 0xd8,
	0xee, 0xd8, 0x5d, 0xa3, 0xdf, 0xd9, 0xfc, 0x64, 0xdb, 0xf7, 0x37, 0xdb, 0x76, 0x6f, 0x8b, 0xfe,
	0xc1, 0xaa, 0x6d, 0x77, 0xb7, 0x3c, 0xec, 0x5e, 0x58, 0x6d, 0xec, 0x8d, 0xfd, 0x07, 0xeb, 0x59,
	0x9e, 0xd2, 0x7c, 0xf4, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x01, 0xfa, 0xd0, 0x06, 0xbe, 0x25,
	0x00, 0x00,
}
