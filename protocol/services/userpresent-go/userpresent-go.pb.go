// Code generated by protoc-gen-go. DO NOT EDIT.
// source: userpresent-go/userpresent-go.proto

package userpresent_go // import "golang.52tt.com/protocol/services/userpresent-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 购买礼物的货币
type PresentPriceType int32

const (
	PresentPriceType_PRESENT_PRICE_UNKNOWN     PresentPriceType = 0
	PresentPriceType_PRESENT_PRICE_RED_DIAMOND PresentPriceType = 1
	PresentPriceType_PRESENT_PRICE_TBEAN       PresentPriceType = 2
)

var PresentPriceType_name = map[int32]string{
	0: "PRESENT_PRICE_UNKNOWN",
	1: "PRESENT_PRICE_RED_DIAMOND",
	2: "PRESENT_PRICE_TBEAN",
}
var PresentPriceType_value = map[string]int32{
	"PRESENT_PRICE_UNKNOWN":     0,
	"PRESENT_PRICE_RED_DIAMOND": 1,
	"PRESENT_PRICE_TBEAN":       2,
}

func (x PresentPriceType) String() string {
	return proto.EnumName(PresentPriceType_name, int32(x))
}
func (PresentPriceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{0}
}

// 礼物配置列表类型
type ConfigListTypeBitMap int32

const (
	ConfigListTypeBitMap_CONFIG_UNLIMIT     ConfigListTypeBitMap = 0
	ConfigListTypeBitMap_CONFIG_NOT_EXPIRED ConfigListTypeBitMap = 1
	ConfigListTypeBitMap_CONFIG_NOT_DELETED ConfigListTypeBitMap = 2
)

var ConfigListTypeBitMap_name = map[int32]string{
	0: "CONFIG_UNLIMIT",
	1: "CONFIG_NOT_EXPIRED",
	2: "CONFIG_NOT_DELETED",
}
var ConfigListTypeBitMap_value = map[string]int32{
	"CONFIG_UNLIMIT":     0,
	"CONFIG_NOT_EXPIRED": 1,
	"CONFIG_NOT_DELETED": 2,
}

func (x ConfigListTypeBitMap) String() string {
	return proto.EnumName(ConfigListTypeBitMap_name, int32(x))
}
func (ConfigListTypeBitMap) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{1}
}

type PresentEnterType int32

const (
	PresentEnterType_PresentEnterTypeUnknown  PresentEnterType = 0
	PresentEnterType_PresentEnterTypeShelf    PresentEnterType = 1
	PresentEnterType_PresentEnterTypeLottery  PresentEnterType = 2
	PresentEnterType_PresentEnterTypeWishList PresentEnterType = 3
)

var PresentEnterType_name = map[int32]string{
	0: "PresentEnterTypeUnknown",
	1: "PresentEnterTypeShelf",
	2: "PresentEnterTypeLottery",
	3: "PresentEnterTypeWishList",
}
var PresentEnterType_value = map[string]int32{
	"PresentEnterTypeUnknown":  0,
	"PresentEnterTypeShelf":    1,
	"PresentEnterTypeLottery":  2,
	"PresentEnterTypeWishList": 3,
}

func (x PresentEnterType) String() string {
	return proto.EnumName(PresentEnterType_name, int32(x))
}
func (PresentEnterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{2}
}

type StConfigIosExtend struct {
	VideoEffectUrl       []byte   `protobuf:"bytes,1,opt,name=video_effect_url,json=videoEffectUrl,proto3" json:"video_effect_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StConfigIosExtend) Reset()         { *m = StConfigIosExtend{} }
func (m *StConfigIosExtend) String() string { return proto.CompactTextString(m) }
func (*StConfigIosExtend) ProtoMessage()    {}
func (*StConfigIosExtend) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{0}
}
func (m *StConfigIosExtend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StConfigIosExtend.Unmarshal(m, b)
}
func (m *StConfigIosExtend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StConfigIosExtend.Marshal(b, m, deterministic)
}
func (dst *StConfigIosExtend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StConfigIosExtend.Merge(dst, src)
}
func (m *StConfigIosExtend) XXX_Size() int {
	return xxx_messageInfo_StConfigIosExtend.Size(m)
}
func (m *StConfigIosExtend) XXX_DiscardUnknown() {
	xxx_messageInfo_StConfigIosExtend.DiscardUnknown(m)
}

var xxx_messageInfo_StConfigIosExtend proto.InternalMessageInfo

func (m *StConfigIosExtend) GetVideoEffectUrl() []byte {
	if m != nil {
		return m.VideoEffectUrl
	}
	return nil
}

type StPresentItemConfigExtend struct {
	ItemId               uint32             `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	VideoEffectUrl       []byte             `protobuf:"bytes,2,opt,name=video_effect_url,json=videoEffectUrl,proto3" json:"video_effect_url,omitempty"`
	ShowEffect           uint32             `protobuf:"varint,3,opt,name=show_effect,json=showEffect,proto3" json:"show_effect,omitempty"`
	UnshowBatchOption    bool               `protobuf:"varint,4,opt,name=unshow_batch_option,json=unshowBatchOption,proto3" json:"unshow_batch_option,omitempty"`
	IsTest               bool               `protobuf:"varint,5,opt,name=is_test,json=isTest,proto3" json:"is_test,omitempty"`
	FlowId               uint32             `protobuf:"varint,6,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	IosExtend            *StConfigIosExtend `protobuf:"bytes,7,opt,name=ios_extend,json=iosExtend,proto3" json:"ios_extend,omitempty"`
	NotifyAll            bool               `protobuf:"varint,8,opt,name=notify_all,json=notifyAll,proto3" json:"notify_all,omitempty"`
	Tag                  uint32             `protobuf:"varint,9,opt,name=tag,proto3" json:"tag,omitempty"`
	ForceSendable        bool               `protobuf:"varint,10,opt,name=force_sendable,json=forceSendable,proto3" json:"force_sendable,omitempty"`
	NobilityLevel        uint32             `protobuf:"varint,11,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	UnshowPresentShelf   bool               `protobuf:"varint,12,opt,name=unshow_present_shelf,json=unshowPresentShelf,proto3" json:"unshow_present_shelf,omitempty"`
	ShowEffectEnd        bool               `protobuf:"varint,13,opt,name=show_effect_end,json=showEffectEnd,proto3" json:"show_effect_end,omitempty"`
	EffectEndDelay       bool               `protobuf:"varint,14,opt,name=effect_end_delay,json=effectEndDelay,proto3" json:"effect_end_delay,omitempty"`
	CustomText           []*CustomText      `protobuf:"bytes,15,rep,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	SmallVapUrl          string             `protobuf:"bytes,16,opt,name=small_vap_url,json=smallVapUrl,proto3" json:"small_vap_url,omitempty"`
	SmallVapMd5          string             `protobuf:"bytes,17,opt,name=small_vap_md5,json=smallVapMd5,proto3" json:"small_vap_md5,omitempty"`
	MicEffectUrl         string             `protobuf:"bytes,18,opt,name=mic_effect_url,json=micEffectUrl,proto3" json:"mic_effect_url,omitempty"`
	MicEffectMd5         string             `protobuf:"bytes,19,opt,name=mic_effect_md5,json=micEffectMd5,proto3" json:"mic_effect_md5,omitempty"`
	FusionPresent        bool               `protobuf:"varint,20,opt,name=fusion_present,json=fusionPresent,proto3" json:"fusion_present,omitempty"`
	IsBoxBreaking        bool               `protobuf:"varint,21,opt,name=is_box_breaking,json=isBoxBreaking,proto3" json:"is_box_breaking,omitempty"`
	FansLevel            uint32             `protobuf:"varint,22,opt,name=fans_level,json=fansLevel,proto3" json:"fans_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *StPresentItemConfigExtend) Reset()         { *m = StPresentItemConfigExtend{} }
func (m *StPresentItemConfigExtend) String() string { return proto.CompactTextString(m) }
func (*StPresentItemConfigExtend) ProtoMessage()    {}
func (*StPresentItemConfigExtend) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{1}
}
func (m *StPresentItemConfigExtend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StPresentItemConfigExtend.Unmarshal(m, b)
}
func (m *StPresentItemConfigExtend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StPresentItemConfigExtend.Marshal(b, m, deterministic)
}
func (dst *StPresentItemConfigExtend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StPresentItemConfigExtend.Merge(dst, src)
}
func (m *StPresentItemConfigExtend) XXX_Size() int {
	return xxx_messageInfo_StPresentItemConfigExtend.Size(m)
}
func (m *StPresentItemConfigExtend) XXX_DiscardUnknown() {
	xxx_messageInfo_StPresentItemConfigExtend.DiscardUnknown(m)
}

var xxx_messageInfo_StPresentItemConfigExtend proto.InternalMessageInfo

func (m *StPresentItemConfigExtend) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetVideoEffectUrl() []byte {
	if m != nil {
		return m.VideoEffectUrl
	}
	return nil
}

func (m *StPresentItemConfigExtend) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetUnshowBatchOption() bool {
	if m != nil {
		return m.UnshowBatchOption
	}
	return false
}

func (m *StPresentItemConfigExtend) GetIsTest() bool {
	if m != nil {
		return m.IsTest
	}
	return false
}

func (m *StPresentItemConfigExtend) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetIosExtend() *StConfigIosExtend {
	if m != nil {
		return m.IosExtend
	}
	return nil
}

func (m *StPresentItemConfigExtend) GetNotifyAll() bool {
	if m != nil {
		return m.NotifyAll
	}
	return false
}

func (m *StPresentItemConfigExtend) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetForceSendable() bool {
	if m != nil {
		return m.ForceSendable
	}
	return false
}

func (m *StPresentItemConfigExtend) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetUnshowPresentShelf() bool {
	if m != nil {
		return m.UnshowPresentShelf
	}
	return false
}

func (m *StPresentItemConfigExtend) GetShowEffectEnd() bool {
	if m != nil {
		return m.ShowEffectEnd
	}
	return false
}

func (m *StPresentItemConfigExtend) GetEffectEndDelay() bool {
	if m != nil {
		return m.EffectEndDelay
	}
	return false
}

func (m *StPresentItemConfigExtend) GetCustomText() []*CustomText {
	if m != nil {
		return m.CustomText
	}
	return nil
}

func (m *StPresentItemConfigExtend) GetSmallVapUrl() string {
	if m != nil {
		return m.SmallVapUrl
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetSmallVapMd5() string {
	if m != nil {
		return m.SmallVapMd5
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetMicEffectUrl() string {
	if m != nil {
		return m.MicEffectUrl
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetMicEffectMd5() string {
	if m != nil {
		return m.MicEffectMd5
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetFusionPresent() bool {
	if m != nil {
		return m.FusionPresent
	}
	return false
}

func (m *StPresentItemConfigExtend) GetIsBoxBreaking() bool {
	if m != nil {
		return m.IsBoxBreaking
	}
	return false
}

func (m *StPresentItemConfigExtend) GetFansLevel() uint32 {
	if m != nil {
		return m.FansLevel
	}
	return 0
}

type CustomText struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Text                 []string `protobuf:"bytes,2,rep,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomText) Reset()         { *m = CustomText{} }
func (m *CustomText) String() string { return proto.CompactTextString(m) }
func (*CustomText) ProtoMessage()    {}
func (*CustomText) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{2}
}
func (m *CustomText) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomText.Unmarshal(m, b)
}
func (m *CustomText) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomText.Marshal(b, m, deterministic)
}
func (dst *CustomText) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomText.Merge(dst, src)
}
func (m *CustomText) XXX_Size() int {
	return xxx_messageInfo_CustomText.Size(m)
}
func (m *CustomText) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomText.DiscardUnknown(m)
}

var xxx_messageInfo_CustomText proto.InternalMessageInfo

func (m *CustomText) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CustomText) GetText() []string {
	if m != nil {
		return m.Text
	}
	return nil
}

// 礼物配置信息
type StPresentItemConfig struct {
	ItemId               uint32                     `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Name                 string                     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string                     `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32                     `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	Score                uint32                     `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	Charm                uint32                     `protobuf:"varint,6,opt,name=charm,proto3" json:"charm,omitempty"`
	Rank                 uint32                     `protobuf:"varint,7,opt,name=rank,proto3" json:"rank,omitempty"`
	EffectBegin          uint32                     `protobuf:"varint,8,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                     `protobuf:"varint,9,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	UpdateTime           uint32                     `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           uint32                     `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	IsDel                bool                       `protobuf:"varint,12,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	PriceType            uint32                     `protobuf:"varint,13,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	RichValue            uint32                     `protobuf:"varint,14,opt,name=rich_value,json=richValue,proto3" json:"rich_value,omitempty"`
	Extend               *StPresentItemConfigExtend `protobuf:"bytes,15,opt,name=extend,proto3" json:"extend,omitempty"`
	RankFloat            float32                    `protobuf:"fixed32,16,opt,name=rank_float,json=rankFloat,proto3" json:"rank_float,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *StPresentItemConfig) Reset()         { *m = StPresentItemConfig{} }
func (m *StPresentItemConfig) String() string { return proto.CompactTextString(m) }
func (*StPresentItemConfig) ProtoMessage()    {}
func (*StPresentItemConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{3}
}
func (m *StPresentItemConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StPresentItemConfig.Unmarshal(m, b)
}
func (m *StPresentItemConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StPresentItemConfig.Marshal(b, m, deterministic)
}
func (dst *StPresentItemConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StPresentItemConfig.Merge(dst, src)
}
func (m *StPresentItemConfig) XXX_Size() int {
	return xxx_messageInfo_StPresentItemConfig.Size(m)
}
func (m *StPresentItemConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_StPresentItemConfig.DiscardUnknown(m)
}

var xxx_messageInfo_StPresentItemConfig proto.InternalMessageInfo

func (m *StPresentItemConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StPresentItemConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StPresentItemConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *StPresentItemConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *StPresentItemConfig) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *StPresentItemConfig) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *StPresentItemConfig) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *StPresentItemConfig) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *StPresentItemConfig) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *StPresentItemConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *StPresentItemConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *StPresentItemConfig) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *StPresentItemConfig) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *StPresentItemConfig) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *StPresentItemConfig) GetExtend() *StPresentItemConfigExtend {
	if m != nil {
		return m.Extend
	}
	return nil
}

func (m *StPresentItemConfig) GetRankFloat() float32 {
	if m != nil {
		return m.RankFloat
	}
	return 0
}

// 用户的礼物汇总
type StUserPresentSummary struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemId               uint32   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StUserPresentSummary) Reset()         { *m = StUserPresentSummary{} }
func (m *StUserPresentSummary) String() string { return proto.CompactTextString(m) }
func (*StUserPresentSummary) ProtoMessage()    {}
func (*StUserPresentSummary) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{4}
}
func (m *StUserPresentSummary) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StUserPresentSummary.Unmarshal(m, b)
}
func (m *StUserPresentSummary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StUserPresentSummary.Marshal(b, m, deterministic)
}
func (dst *StUserPresentSummary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StUserPresentSummary.Merge(dst, src)
}
func (m *StUserPresentSummary) XXX_Size() int {
	return xxx_messageInfo_StUserPresentSummary.Size(m)
}
func (m *StUserPresentSummary) XXX_DiscardUnknown() {
	xxx_messageInfo_StUserPresentSummary.DiscardUnknown(m)
}

var xxx_messageInfo_StUserPresentSummary proto.InternalMessageInfo

func (m *StUserPresentSummary) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StUserPresentSummary) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StUserPresentSummary) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 用户的礼物明细
type StUserPresentDetail struct {
	FromUid              uint32               `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	TargetUid            uint32               `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,3,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	ReceiveTime          uint32               `protobuf:"varint,4,opt,name=receive_time,json=receiveTime,proto3" json:"receive_time,omitempty"`
	ItemCount            uint32               `protobuf:"varint,5,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	AddCharm             uint32               `protobuf:"varint,6,opt,name=add_charm,json=addCharm,proto3" json:"add_charm,omitempty"`
	OrderId              string               `protobuf:"bytes,7,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ItemId               uint32               `protobuf:"varint,8,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Score                uint32               `protobuf:"varint,9,opt,name=score,proto3" json:"score,omitempty"`
	SendSource           uint32               `protobuf:"varint,10,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	SendMethod           uint32               `protobuf:"varint,11,opt,name=send_method,json=sendMethod,proto3" json:"send_method,omitempty"`
	AddRich              uint32               `protobuf:"varint,12,opt,name=add_rich,json=addRich,proto3" json:"add_rich,omitempty"`
	IsUkw                bool                 `protobuf:"varint,13,opt,name=is_ukw,json=isUkw,proto3" json:"is_ukw,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *StUserPresentDetail) Reset()         { *m = StUserPresentDetail{} }
func (m *StUserPresentDetail) String() string { return proto.CompactTextString(m) }
func (*StUserPresentDetail) ProtoMessage()    {}
func (*StUserPresentDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{5}
}
func (m *StUserPresentDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StUserPresentDetail.Unmarshal(m, b)
}
func (m *StUserPresentDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StUserPresentDetail.Marshal(b, m, deterministic)
}
func (dst *StUserPresentDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StUserPresentDetail.Merge(dst, src)
}
func (m *StUserPresentDetail) XXX_Size() int {
	return xxx_messageInfo_StUserPresentDetail.Size(m)
}
func (m *StUserPresentDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_StUserPresentDetail.DiscardUnknown(m)
}

var xxx_messageInfo_StUserPresentDetail proto.InternalMessageInfo

func (m *StUserPresentDetail) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *StUserPresentDetail) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *StUserPresentDetail) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

func (m *StUserPresentDetail) GetReceiveTime() uint32 {
	if m != nil {
		return m.ReceiveTime
	}
	return 0
}

func (m *StUserPresentDetail) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *StUserPresentDetail) GetAddCharm() uint32 {
	if m != nil {
		return m.AddCharm
	}
	return 0
}

func (m *StUserPresentDetail) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *StUserPresentDetail) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StUserPresentDetail) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *StUserPresentDetail) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *StUserPresentDetail) GetSendMethod() uint32 {
	if m != nil {
		return m.SendMethod
	}
	return 0
}

func (m *StUserPresentDetail) GetAddRich() uint32 {
	if m != nil {
		return m.AddRich
	}
	return 0
}

func (m *StUserPresentDetail) GetIsUkw() bool {
	if m != nil {
		return m.IsUkw
	}
	return false
}

type GetPresentConfigListReq struct {
	TypeBitmap           uint32   `protobuf:"varint,1,opt,name=type_bitmap,json=typeBitmap,proto3" json:"type_bitmap,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigListReq) Reset()         { *m = GetPresentConfigListReq{} }
func (m *GetPresentConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListReq) ProtoMessage()    {}
func (*GetPresentConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{6}
}
func (m *GetPresentConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListReq.Unmarshal(m, b)
}
func (m *GetPresentConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListReq.Merge(dst, src)
}
func (m *GetPresentConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListReq.Size(m)
}
func (m *GetPresentConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListReq proto.InternalMessageInfo

func (m *GetPresentConfigListReq) GetTypeBitmap() uint32 {
	if m != nil {
		return m.TypeBitmap
	}
	return 0
}

func (m *GetPresentConfigListReq) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetPresentConfigListResp struct {
	ItemList             []*StPresentItemConfig `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	UpdateTime           uint32                 `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPresentConfigListResp) Reset()         { *m = GetPresentConfigListResp{} }
func (m *GetPresentConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListResp) ProtoMessage()    {}
func (*GetPresentConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{7}
}
func (m *GetPresentConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListResp.Unmarshal(m, b)
}
func (m *GetPresentConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListResp.Merge(dst, src)
}
func (m *GetPresentConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListResp.Size(m)
}
func (m *GetPresentConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListResp proto.InternalMessageInfo

func (m *GetPresentConfigListResp) GetItemList() []*StPresentItemConfig {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GetPresentConfigListResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetPresentConfigListV3Req struct {
	TypeBitmap           uint32   `protobuf:"varint,1,opt,name=type_bitmap,json=typeBitmap,proto3" json:"type_bitmap,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigListV3Req) Reset()         { *m = GetPresentConfigListV3Req{} }
func (m *GetPresentConfigListV3Req) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListV3Req) ProtoMessage()    {}
func (*GetPresentConfigListV3Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{8}
}
func (m *GetPresentConfigListV3Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListV3Req.Unmarshal(m, b)
}
func (m *GetPresentConfigListV3Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListV3Req.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListV3Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListV3Req.Merge(dst, src)
}
func (m *GetPresentConfigListV3Req) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListV3Req.Size(m)
}
func (m *GetPresentConfigListV3Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListV3Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListV3Req proto.InternalMessageInfo

func (m *GetPresentConfigListV3Req) GetTypeBitmap() uint32 {
	if m != nil {
		return m.TypeBitmap
	}
	return 0
}

func (m *GetPresentConfigListV3Req) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetPresentConfigListV3Resp struct {
	ItemList             []*PresentConfigNew      `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	LastUpdateTime       uint32                   `protobuf:"varint,2,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	EnterBlackList       []*PresentEnterBlacklist `protobuf:"bytes,3,rep,name=enter_black_list,json=enterBlackList,proto3" json:"enter_black_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetPresentConfigListV3Resp) Reset()         { *m = GetPresentConfigListV3Resp{} }
func (m *GetPresentConfigListV3Resp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListV3Resp) ProtoMessage()    {}
func (*GetPresentConfigListV3Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{9}
}
func (m *GetPresentConfigListV3Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListV3Resp.Unmarshal(m, b)
}
func (m *GetPresentConfigListV3Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListV3Resp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListV3Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListV3Resp.Merge(dst, src)
}
func (m *GetPresentConfigListV3Resp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListV3Resp.Size(m)
}
func (m *GetPresentConfigListV3Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListV3Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListV3Resp proto.InternalMessageInfo

func (m *GetPresentConfigListV3Resp) GetItemList() []*PresentConfigNew {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GetPresentConfigListV3Resp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *GetPresentConfigListV3Resp) GetEnterBlackList() []*PresentEnterBlacklist {
	if m != nil {
		return m.EnterBlackList
	}
	return nil
}

// 增加礼物配置
type AddPresentConfigReq struct {
	Name                 string                     `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string                     `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32                     `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	EffectBegin          uint32                     `protobuf:"varint,4,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                     `protobuf:"varint,5,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	Rank                 uint32                     `protobuf:"varint,6,opt,name=rank,proto3" json:"rank,omitempty"`
	PriceType            uint32                     `protobuf:"varint,7,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	Extend               *StPresentItemConfigExtend `protobuf:"bytes,8,opt,name=extend,proto3" json:"extend,omitempty"`
	RankFloat            float32                    `protobuf:"fixed32,9,opt,name=rank_float,json=rankFloat,proto3" json:"rank_float,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *AddPresentConfigReq) Reset()         { *m = AddPresentConfigReq{} }
func (m *AddPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddPresentConfigReq) ProtoMessage()    {}
func (*AddPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{10}
}
func (m *AddPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentConfigReq.Unmarshal(m, b)
}
func (m *AddPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentConfigReq.Merge(dst, src)
}
func (m *AddPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddPresentConfigReq.Size(m)
}
func (m *AddPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentConfigReq proto.InternalMessageInfo

func (m *AddPresentConfigReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AddPresentConfigReq) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *AddPresentConfigReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *AddPresentConfigReq) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *AddPresentConfigReq) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *AddPresentConfigReq) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *AddPresentConfigReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *AddPresentConfigReq) GetExtend() *StPresentItemConfigExtend {
	if m != nil {
		return m.Extend
	}
	return nil
}

func (m *AddPresentConfigReq) GetRankFloat() float32 {
	if m != nil {
		return m.RankFloat
	}
	return 0
}

type AddPresentConfigResp struct {
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddPresentConfigResp) Reset()         { *m = AddPresentConfigResp{} }
func (m *AddPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddPresentConfigResp) ProtoMessage()    {}
func (*AddPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{11}
}
func (m *AddPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentConfigResp.Unmarshal(m, b)
}
func (m *AddPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentConfigResp.Merge(dst, src)
}
func (m *AddPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddPresentConfigResp.Size(m)
}
func (m *AddPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentConfigResp proto.InternalMessageInfo

func (m *AddPresentConfigResp) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

// 删除礼物配置
type DelPresentConfigReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentConfigReq) Reset()         { *m = DelPresentConfigReq{} }
func (m *DelPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelPresentConfigReq) ProtoMessage()    {}
func (*DelPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{12}
}
func (m *DelPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentConfigReq.Unmarshal(m, b)
}
func (m *DelPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentConfigReq.Merge(dst, src)
}
func (m *DelPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelPresentConfigReq.Size(m)
}
func (m *DelPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentConfigReq proto.InternalMessageInfo

func (m *DelPresentConfigReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type DelPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentConfigResp) Reset()         { *m = DelPresentConfigResp{} }
func (m *DelPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelPresentConfigResp) ProtoMessage()    {}
func (*DelPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{13}
}
func (m *DelPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentConfigResp.Unmarshal(m, b)
}
func (m *DelPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentConfigResp.Merge(dst, src)
}
func (m *DelPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelPresentConfigResp.Size(m)
}
func (m *DelPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentConfigResp proto.InternalMessageInfo

// 更新礼物配置
type UpdatePresentConfigReq struct {
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdatePresentConfigReq) Reset()         { *m = UpdatePresentConfigReq{} }
func (m *UpdatePresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentConfigReq) ProtoMessage()    {}
func (*UpdatePresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{14}
}
func (m *UpdatePresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentConfigReq.Unmarshal(m, b)
}
func (m *UpdatePresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentConfigReq.Merge(dst, src)
}
func (m *UpdatePresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentConfigReq.Size(m)
}
func (m *UpdatePresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentConfigReq proto.InternalMessageInfo

func (m *UpdatePresentConfigReq) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

type UpdatePresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentConfigResp) Reset()         { *m = UpdatePresentConfigResp{} }
func (m *UpdatePresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentConfigResp) ProtoMessage()    {}
func (*UpdatePresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{15}
}
func (m *UpdatePresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentConfigResp.Unmarshal(m, b)
}
func (m *UpdatePresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentConfigResp.Merge(dst, src)
}
func (m *UpdatePresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentConfigResp.Size(m)
}
func (m *UpdatePresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentConfigResp proto.InternalMessageInfo

type GetPresentConfigByIdReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigByIdReq) Reset()         { *m = GetPresentConfigByIdReq{} }
func (m *GetPresentConfigByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdReq) ProtoMessage()    {}
func (*GetPresentConfigByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{16}
}
func (m *GetPresentConfigByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdReq.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdReq.Merge(dst, src)
}
func (m *GetPresentConfigByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdReq.Size(m)
}
func (m *GetPresentConfigByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdReq proto.InternalMessageInfo

func (m *GetPresentConfigByIdReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type GetPresentConfigByIdResp struct {
	ItemConfig           *PresentConfigNew `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPresentConfigByIdResp) Reset()         { *m = GetPresentConfigByIdResp{} }
func (m *GetPresentConfigByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdResp) ProtoMessage()    {}
func (*GetPresentConfigByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{17}
}
func (m *GetPresentConfigByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdResp.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdResp.Merge(dst, src)
}
func (m *GetPresentConfigByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdResp.Size(m)
}
func (m *GetPresentConfigByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdResp proto.InternalMessageInfo

func (m *GetPresentConfigByIdResp) GetItemConfig() *PresentConfigNew {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

type GetPresentConfigListByIdListReq struct {
	ItemId               []uint32 `protobuf:"varint,1,rep,packed,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigListByIdListReq) Reset()         { *m = GetPresentConfigListByIdListReq{} }
func (m *GetPresentConfigListByIdListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListByIdListReq) ProtoMessage()    {}
func (*GetPresentConfigListByIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{18}
}
func (m *GetPresentConfigListByIdListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListByIdListReq.Unmarshal(m, b)
}
func (m *GetPresentConfigListByIdListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListByIdListReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListByIdListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListByIdListReq.Merge(dst, src)
}
func (m *GetPresentConfigListByIdListReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListByIdListReq.Size(m)
}
func (m *GetPresentConfigListByIdListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListByIdListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListByIdListReq proto.InternalMessageInfo

func (m *GetPresentConfigListByIdListReq) GetItemId() []uint32 {
	if m != nil {
		return m.ItemId
	}
	return nil
}

type GetPresentConfigListByIdListResp struct {
	ItemConfig           []*PresentConfigNew `protobuf:"bytes,1,rep,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPresentConfigListByIdListResp) Reset()         { *m = GetPresentConfigListByIdListResp{} }
func (m *GetPresentConfigListByIdListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListByIdListResp) ProtoMessage()    {}
func (*GetPresentConfigListByIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{19}
}
func (m *GetPresentConfigListByIdListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListByIdListResp.Unmarshal(m, b)
}
func (m *GetPresentConfigListByIdListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListByIdListResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListByIdListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListByIdListResp.Merge(dst, src)
}
func (m *GetPresentConfigListByIdListResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListByIdListResp.Size(m)
}
func (m *GetPresentConfigListByIdListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListByIdListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListByIdListResp proto.InternalMessageInfo

func (m *GetPresentConfigListByIdListResp) GetItemConfig() []*PresentConfigNew {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

// 用来模拟旧的resp
type GetPresentConfigByIdOldResp struct {
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPresentConfigByIdOldResp) Reset()         { *m = GetPresentConfigByIdOldResp{} }
func (m *GetPresentConfigByIdOldResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdOldResp) ProtoMessage()    {}
func (*GetPresentConfigByIdOldResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{20}
}
func (m *GetPresentConfigByIdOldResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdOldResp.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdOldResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdOldResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdOldResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdOldResp.Merge(dst, src)
}
func (m *GetPresentConfigByIdOldResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdOldResp.Size(m)
}
func (m *GetPresentConfigByIdOldResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdOldResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdOldResp proto.InternalMessageInfo

func (m *GetPresentConfigByIdOldResp) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

type GetPresentConfigByIdListResp struct {
	ItemList             []*StPresentItemConfig `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPresentConfigByIdListResp) Reset()         { *m = GetPresentConfigByIdListResp{} }
func (m *GetPresentConfigByIdListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdListResp) ProtoMessage()    {}
func (*GetPresentConfigByIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{21}
}
func (m *GetPresentConfigByIdListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdListResp.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdListResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdListResp.Merge(dst, src)
}
func (m *GetPresentConfigByIdListResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdListResp.Size(m)
}
func (m *GetPresentConfigByIdListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdListResp proto.InternalMessageInfo

func (m *GetPresentConfigByIdListResp) GetItemList() []*StPresentItemConfig {
	if m != nil {
		return m.ItemList
	}
	return nil
}

// 礼物配置 - 新
type PresentConfigNew struct {
	BaseConfig           *PresentBaseConfig   `protobuf:"bytes,1,opt,name=base_config,json=baseConfig,proto3" json:"base_config,omitempty"`
	EffectConfig         *PresentEffectConfig `protobuf:"bytes,2,opt,name=effect_config,json=effectConfig,proto3" json:"effect_config,omitempty"`
	EnterConfig          *PresentEnterConfig  `protobuf:"bytes,3,opt,name=enter_config,json=enterConfig,proto3" json:"enter_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *PresentConfigNew) Reset()         { *m = PresentConfigNew{} }
func (m *PresentConfigNew) String() string { return proto.CompactTextString(m) }
func (*PresentConfigNew) ProtoMessage()    {}
func (*PresentConfigNew) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{22}
}
func (m *PresentConfigNew) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentConfigNew.Unmarshal(m, b)
}
func (m *PresentConfigNew) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentConfigNew.Marshal(b, m, deterministic)
}
func (dst *PresentConfigNew) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentConfigNew.Merge(dst, src)
}
func (m *PresentConfigNew) XXX_Size() int {
	return xxx_messageInfo_PresentConfigNew.Size(m)
}
func (m *PresentConfigNew) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentConfigNew.DiscardUnknown(m)
}

var xxx_messageInfo_PresentConfigNew proto.InternalMessageInfo

func (m *PresentConfigNew) GetBaseConfig() *PresentBaseConfig {
	if m != nil {
		return m.BaseConfig
	}
	return nil
}

func (m *PresentConfigNew) GetEffectConfig() *PresentEffectConfig {
	if m != nil {
		return m.EffectConfig
	}
	return nil
}

func (m *PresentConfigNew) GetEnterConfig() *PresentEnterConfig {
	if m != nil {
		return m.EnterConfig
	}
	return nil
}

// 新版的礼物配置 - 基础配置
type PresentBaseConfig struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string   `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	IsDel                bool     `protobuf:"varint,5,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	PriceType            uint32   `protobuf:"varint,6,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	RichValue            uint32   `protobuf:"varint,7,opt,name=rich_value,json=richValue,proto3" json:"rich_value,omitempty"`
	Score                uint32   `protobuf:"varint,8,opt,name=score,proto3" json:"score,omitempty"`
	Charm                uint32   `protobuf:"varint,9,opt,name=charm,proto3" json:"charm,omitempty"`
	ForceSendable        bool     `protobuf:"varint,10,opt,name=force_sendable,json=forceSendable,proto3" json:"force_sendable,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           uint32   `protobuf:"varint,12,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	IsTest               bool     `protobuf:"varint,13,opt,name=is_test,json=isTest,proto3" json:"is_test,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentBaseConfig) Reset()         { *m = PresentBaseConfig{} }
func (m *PresentBaseConfig) String() string { return proto.CompactTextString(m) }
func (*PresentBaseConfig) ProtoMessage()    {}
func (*PresentBaseConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{23}
}
func (m *PresentBaseConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentBaseConfig.Unmarshal(m, b)
}
func (m *PresentBaseConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentBaseConfig.Marshal(b, m, deterministic)
}
func (dst *PresentBaseConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentBaseConfig.Merge(dst, src)
}
func (m *PresentBaseConfig) XXX_Size() int {
	return xxx_messageInfo_PresentBaseConfig.Size(m)
}
func (m *PresentBaseConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentBaseConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PresentBaseConfig proto.InternalMessageInfo

func (m *PresentBaseConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentBaseConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PresentBaseConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *PresentBaseConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PresentBaseConfig) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *PresentBaseConfig) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *PresentBaseConfig) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *PresentBaseConfig) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *PresentBaseConfig) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *PresentBaseConfig) GetForceSendable() bool {
	if m != nil {
		return m.ForceSendable
	}
	return false
}

func (m *PresentBaseConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *PresentBaseConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PresentBaseConfig) GetIsTest() bool {
	if m != nil {
		return m.IsTest
	}
	return false
}

// 新版的礼物配置 - 入口配置
type PresentEnterConfig struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	UnshowBatchOption    bool     `protobuf:"varint,2,opt,name=unshow_batch_option,json=unshowBatchOption,proto3" json:"unshow_batch_option,omitempty"`
	Rank                 float32  `protobuf:"fixed32,3,opt,name=rank,proto3" json:"rank,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,4,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,5,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	NobilityLevel        uint32   `protobuf:"varint,6,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	UnshowPresentShelf   bool     `protobuf:"varint,7,opt,name=unshow_present_shelf,json=unshowPresentShelf,proto3" json:"unshow_present_shelf,omitempty"`
	ShowEffectEnd        bool     `protobuf:"varint,8,opt,name=show_effect_end,json=showEffectEnd,proto3" json:"show_effect_end,omitempty"`
	EffectEndDelay       bool     `protobuf:"varint,9,opt,name=effect_end_delay,json=effectEndDelay,proto3" json:"effect_end_delay,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Tag                  uint32   `protobuf:"varint,11,opt,name=tag,proto3" json:"tag,omitempty"`
	FansLevel            uint32   `protobuf:"varint,12,opt,name=fans_level,json=fansLevel,proto3" json:"fans_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentEnterConfig) Reset()         { *m = PresentEnterConfig{} }
func (m *PresentEnterConfig) String() string { return proto.CompactTextString(m) }
func (*PresentEnterConfig) ProtoMessage()    {}
func (*PresentEnterConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{24}
}
func (m *PresentEnterConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEnterConfig.Unmarshal(m, b)
}
func (m *PresentEnterConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEnterConfig.Marshal(b, m, deterministic)
}
func (dst *PresentEnterConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEnterConfig.Merge(dst, src)
}
func (m *PresentEnterConfig) XXX_Size() int {
	return xxx_messageInfo_PresentEnterConfig.Size(m)
}
func (m *PresentEnterConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEnterConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEnterConfig proto.InternalMessageInfo

func (m *PresentEnterConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentEnterConfig) GetUnshowBatchOption() bool {
	if m != nil {
		return m.UnshowBatchOption
	}
	return false
}

func (m *PresentEnterConfig) GetRank() float32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *PresentEnterConfig) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *PresentEnterConfig) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *PresentEnterConfig) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *PresentEnterConfig) GetUnshowPresentShelf() bool {
	if m != nil {
		return m.UnshowPresentShelf
	}
	return false
}

func (m *PresentEnterConfig) GetShowEffectEnd() bool {
	if m != nil {
		return m.ShowEffectEnd
	}
	return false
}

func (m *PresentEnterConfig) GetEffectEndDelay() bool {
	if m != nil {
		return m.EffectEndDelay
	}
	return false
}

func (m *PresentEnterConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *PresentEnterConfig) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *PresentEnterConfig) GetFansLevel() uint32 {
	if m != nil {
		return m.FansLevel
	}
	return 0
}

// 新版的礼物配置 - 特效配置
type PresentEffectConfig struct {
	ItemId               uint32        `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	VideoEffectUrl       []byte        `protobuf:"bytes,2,opt,name=video_effect_url,json=videoEffectUrl,proto3" json:"video_effect_url,omitempty"`
	ShowEffect           uint32        `protobuf:"varint,3,opt,name=show_effect,json=showEffect,proto3" json:"show_effect,omitempty"`
	FlowId               uint32        `protobuf:"varint,4,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	NotifyAll            bool          `protobuf:"varint,5,opt,name=notify_all,json=notifyAll,proto3" json:"notify_all,omitempty"`
	CustomText           []*CustomText `protobuf:"bytes,6,rep,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	SmallVapUrl          string        `protobuf:"bytes,7,opt,name=small_vap_url,json=smallVapUrl,proto3" json:"small_vap_url,omitempty"`
	SmallVapMd5          string        `protobuf:"bytes,8,opt,name=small_vap_md5,json=smallVapMd5,proto3" json:"small_vap_md5,omitempty"`
	MicEffectUrl         string        `protobuf:"bytes,9,opt,name=mic_effect_url,json=micEffectUrl,proto3" json:"mic_effect_url,omitempty"`
	MicEffectMd5         string        `protobuf:"bytes,10,opt,name=mic_effect_md5,json=micEffectMd5,proto3" json:"mic_effect_md5,omitempty"`
	UpdateTime           uint32        `protobuf:"varint,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	IsBoxBreaking        bool          `protobuf:"varint,12,opt,name=is_box_breaking,json=isBoxBreaking,proto3" json:"is_box_breaking,omitempty"`
	FusionPresent        bool          `protobuf:"varint,13,opt,name=fusion_present,json=fusionPresent,proto3" json:"fusion_present,omitempty"`
	IosVideoEffectUrl    []byte        `protobuf:"bytes,14,opt,name=ios_video_effect_url,json=iosVideoEffectUrl,proto3" json:"ios_video_effect_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PresentEffectConfig) Reset()         { *m = PresentEffectConfig{} }
func (m *PresentEffectConfig) String() string { return proto.CompactTextString(m) }
func (*PresentEffectConfig) ProtoMessage()    {}
func (*PresentEffectConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{25}
}
func (m *PresentEffectConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectConfig.Unmarshal(m, b)
}
func (m *PresentEffectConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectConfig.Marshal(b, m, deterministic)
}
func (dst *PresentEffectConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectConfig.Merge(dst, src)
}
func (m *PresentEffectConfig) XXX_Size() int {
	return xxx_messageInfo_PresentEffectConfig.Size(m)
}
func (m *PresentEffectConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectConfig proto.InternalMessageInfo

func (m *PresentEffectConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentEffectConfig) GetVideoEffectUrl() []byte {
	if m != nil {
		return m.VideoEffectUrl
	}
	return nil
}

func (m *PresentEffectConfig) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *PresentEffectConfig) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *PresentEffectConfig) GetNotifyAll() bool {
	if m != nil {
		return m.NotifyAll
	}
	return false
}

func (m *PresentEffectConfig) GetCustomText() []*CustomText {
	if m != nil {
		return m.CustomText
	}
	return nil
}

func (m *PresentEffectConfig) GetSmallVapUrl() string {
	if m != nil {
		return m.SmallVapUrl
	}
	return ""
}

func (m *PresentEffectConfig) GetSmallVapMd5() string {
	if m != nil {
		return m.SmallVapMd5
	}
	return ""
}

func (m *PresentEffectConfig) GetMicEffectUrl() string {
	if m != nil {
		return m.MicEffectUrl
	}
	return ""
}

func (m *PresentEffectConfig) GetMicEffectMd5() string {
	if m != nil {
		return m.MicEffectMd5
	}
	return ""
}

func (m *PresentEffectConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *PresentEffectConfig) GetIsBoxBreaking() bool {
	if m != nil {
		return m.IsBoxBreaking
	}
	return false
}

func (m *PresentEffectConfig) GetFusionPresent() bool {
	if m != nil {
		return m.FusionPresent
	}
	return false
}

func (m *PresentEffectConfig) GetIosVideoEffectUrl() []byte {
	if m != nil {
		return m.IosVideoEffectUrl
	}
	return nil
}

type PresentEnterBlacklist struct {
	EnterType            PresentEnterType `protobuf:"varint,1,opt,name=enter_type,json=enterType,proto3,enum=userpresent_go.PresentEnterType" json:"enter_type,omitempty"`
	GiftItemList         []uint32         `protobuf:"varint,2,rep,packed,name=gift_item_list,json=giftItemList,proto3" json:"gift_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PresentEnterBlacklist) Reset()         { *m = PresentEnterBlacklist{} }
func (m *PresentEnterBlacklist) String() string { return proto.CompactTextString(m) }
func (*PresentEnterBlacklist) ProtoMessage()    {}
func (*PresentEnterBlacklist) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{26}
}
func (m *PresentEnterBlacklist) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEnterBlacklist.Unmarshal(m, b)
}
func (m *PresentEnterBlacklist) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEnterBlacklist.Marshal(b, m, deterministic)
}
func (dst *PresentEnterBlacklist) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEnterBlacklist.Merge(dst, src)
}
func (m *PresentEnterBlacklist) XXX_Size() int {
	return xxx_messageInfo_PresentEnterBlacklist.Size(m)
}
func (m *PresentEnterBlacklist) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEnterBlacklist.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEnterBlacklist proto.InternalMessageInfo

func (m *PresentEnterBlacklist) GetEnterType() PresentEnterType {
	if m != nil {
		return m.EnterType
	}
	return PresentEnterType_PresentEnterTypeUnknown
}

func (m *PresentEnterBlacklist) GetGiftItemList() []uint32 {
	if m != nil {
		return m.GiftItemList
	}
	return nil
}

type GetLivePresentOrderListReq struct {
	ToUid                uint32   `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeginTime            uint32   `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLivePresentOrderListReq) Reset()         { *m = GetLivePresentOrderListReq{} }
func (m *GetLivePresentOrderListReq) String() string { return proto.CompactTextString(m) }
func (*GetLivePresentOrderListReq) ProtoMessage()    {}
func (*GetLivePresentOrderListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{27}
}
func (m *GetLivePresentOrderListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLivePresentOrderListReq.Unmarshal(m, b)
}
func (m *GetLivePresentOrderListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLivePresentOrderListReq.Marshal(b, m, deterministic)
}
func (dst *GetLivePresentOrderListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLivePresentOrderListReq.Merge(dst, src)
}
func (m *GetLivePresentOrderListReq) XXX_Size() int {
	return xxx_messageInfo_GetLivePresentOrderListReq.Size(m)
}
func (m *GetLivePresentOrderListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLivePresentOrderListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLivePresentOrderListReq proto.InternalMessageInfo

func (m *GetLivePresentOrderListReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *GetLivePresentOrderListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetLivePresentOrderListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetLivePresentOrderListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetLivePresentOrderListResp struct {
	OrderList            []*LivePresentOrder `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetLivePresentOrderListResp) Reset()         { *m = GetLivePresentOrderListResp{} }
func (m *GetLivePresentOrderListResp) String() string { return proto.CompactTextString(m) }
func (*GetLivePresentOrderListResp) ProtoMessage()    {}
func (*GetLivePresentOrderListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{28}
}
func (m *GetLivePresentOrderListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLivePresentOrderListResp.Unmarshal(m, b)
}
func (m *GetLivePresentOrderListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLivePresentOrderListResp.Marshal(b, m, deterministic)
}
func (dst *GetLivePresentOrderListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLivePresentOrderListResp.Merge(dst, src)
}
func (m *GetLivePresentOrderListResp) XXX_Size() int {
	return xxx_messageInfo_GetLivePresentOrderListResp.Size(m)
}
func (m *GetLivePresentOrderListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLivePresentOrderListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLivePresentOrderListResp proto.InternalMessageInfo

func (m *GetLivePresentOrderListResp) GetOrderList() []*LivePresentOrder {
	if m != nil {
		return m.OrderList
	}
	return nil
}

type LivePresentOrder struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ItemId               uint32   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	ToUid                uint32   `protobuf:"varint,4,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	CreateTime           uint32   `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LivePresentOrder) Reset()         { *m = LivePresentOrder{} }
func (m *LivePresentOrder) String() string { return proto.CompactTextString(m) }
func (*LivePresentOrder) ProtoMessage()    {}
func (*LivePresentOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_54677f92ec8f1a21, []int{29}
}
func (m *LivePresentOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LivePresentOrder.Unmarshal(m, b)
}
func (m *LivePresentOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LivePresentOrder.Marshal(b, m, deterministic)
}
func (dst *LivePresentOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LivePresentOrder.Merge(dst, src)
}
func (m *LivePresentOrder) XXX_Size() int {
	return xxx_messageInfo_LivePresentOrder.Size(m)
}
func (m *LivePresentOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_LivePresentOrder.DiscardUnknown(m)
}

var xxx_messageInfo_LivePresentOrder proto.InternalMessageInfo

func (m *LivePresentOrder) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *LivePresentOrder) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LivePresentOrder) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *LivePresentOrder) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *LivePresentOrder) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func init() {
	proto.RegisterType((*StConfigIosExtend)(nil), "userpresent_go.StConfigIosExtend")
	proto.RegisterType((*StPresentItemConfigExtend)(nil), "userpresent_go.StPresentItemConfigExtend")
	proto.RegisterType((*CustomText)(nil), "userpresent_go.CustomText")
	proto.RegisterType((*StPresentItemConfig)(nil), "userpresent_go.StPresentItemConfig")
	proto.RegisterType((*StUserPresentSummary)(nil), "userpresent_go.StUserPresentSummary")
	proto.RegisterType((*StUserPresentDetail)(nil), "userpresent_go.StUserPresentDetail")
	proto.RegisterType((*GetPresentConfigListReq)(nil), "userpresent_go.GetPresentConfigListReq")
	proto.RegisterType((*GetPresentConfigListResp)(nil), "userpresent_go.GetPresentConfigListResp")
	proto.RegisterType((*GetPresentConfigListV3Req)(nil), "userpresent_go.GetPresentConfigListV3Req")
	proto.RegisterType((*GetPresentConfigListV3Resp)(nil), "userpresent_go.GetPresentConfigListV3Resp")
	proto.RegisterType((*AddPresentConfigReq)(nil), "userpresent_go.AddPresentConfigReq")
	proto.RegisterType((*AddPresentConfigResp)(nil), "userpresent_go.AddPresentConfigResp")
	proto.RegisterType((*DelPresentConfigReq)(nil), "userpresent_go.DelPresentConfigReq")
	proto.RegisterType((*DelPresentConfigResp)(nil), "userpresent_go.DelPresentConfigResp")
	proto.RegisterType((*UpdatePresentConfigReq)(nil), "userpresent_go.UpdatePresentConfigReq")
	proto.RegisterType((*UpdatePresentConfigResp)(nil), "userpresent_go.UpdatePresentConfigResp")
	proto.RegisterType((*GetPresentConfigByIdReq)(nil), "userpresent_go.GetPresentConfigByIdReq")
	proto.RegisterType((*GetPresentConfigByIdResp)(nil), "userpresent_go.GetPresentConfigByIdResp")
	proto.RegisterType((*GetPresentConfigListByIdListReq)(nil), "userpresent_go.GetPresentConfigListByIdListReq")
	proto.RegisterType((*GetPresentConfigListByIdListResp)(nil), "userpresent_go.GetPresentConfigListByIdListResp")
	proto.RegisterType((*GetPresentConfigByIdOldResp)(nil), "userpresent_go.GetPresentConfigByIdOldResp")
	proto.RegisterType((*GetPresentConfigByIdListResp)(nil), "userpresent_go.GetPresentConfigByIdListResp")
	proto.RegisterType((*PresentConfigNew)(nil), "userpresent_go.PresentConfigNew")
	proto.RegisterType((*PresentBaseConfig)(nil), "userpresent_go.PresentBaseConfig")
	proto.RegisterType((*PresentEnterConfig)(nil), "userpresent_go.PresentEnterConfig")
	proto.RegisterType((*PresentEffectConfig)(nil), "userpresent_go.PresentEffectConfig")
	proto.RegisterType((*PresentEnterBlacklist)(nil), "userpresent_go.PresentEnterBlacklist")
	proto.RegisterType((*GetLivePresentOrderListReq)(nil), "userpresent_go.GetLivePresentOrderListReq")
	proto.RegisterType((*GetLivePresentOrderListResp)(nil), "userpresent_go.GetLivePresentOrderListResp")
	proto.RegisterType((*LivePresentOrder)(nil), "userpresent_go.LivePresentOrder")
	proto.RegisterEnum("userpresent_go.PresentPriceType", PresentPriceType_name, PresentPriceType_value)
	proto.RegisterEnum("userpresent_go.ConfigListTypeBitMap", ConfigListTypeBitMap_name, ConfigListTypeBitMap_value)
	proto.RegisterEnum("userpresent_go.PresentEnterType", PresentEnterType_name, PresentEnterType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserPresentGOClient is the client API for UserPresentGO service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserPresentGOClient interface {
	// 礼物列表 - 旧版
	GetPresentConfigList(ctx context.Context, in *GetPresentConfigListReq, opts ...grpc.CallOption) (*GetPresentConfigListResp, error)
	// 增删查改
	AddPresentConfig(ctx context.Context, in *AddPresentConfigReq, opts ...grpc.CallOption) (*AddPresentConfigResp, error)
	UpdatePresentConfig(ctx context.Context, in *UpdatePresentConfigReq, opts ...grpc.CallOption) (*UpdatePresentConfigResp, error)
	DelPresentConfig(ctx context.Context, in *DelPresentConfigReq, opts ...grpc.CallOption) (*DelPresentConfigResp, error)
	// 查询礼物列表 - 新版 - V2旧的userpresent协议用过了
	GetPresentConfigListV3(ctx context.Context, in *GetPresentConfigListV3Req, opts ...grpc.CallOption) (*GetPresentConfigListV3Resp, error)
	GetPresentConfigById(ctx context.Context, in *GetPresentConfigByIdReq, opts ...grpc.CallOption) (*GetPresentConfigByIdResp, error)
	GetPresentConfigListByIdList(ctx context.Context, in *GetPresentConfigListByIdListReq, opts ...grpc.CallOption) (*GetPresentConfigListByIdListResp, error)
	// 获取虚拟直播间主播某时间段收礼流水
	GetLivePresentOrderList(ctx context.Context, in *GetLivePresentOrderListReq, opts ...grpc.CallOption) (*GetLivePresentOrderListResp, error)
}

type userPresentGOClient struct {
	cc *grpc.ClientConn
}

func NewUserPresentGOClient(cc *grpc.ClientConn) UserPresentGOClient {
	return &userPresentGOClient{cc}
}

func (c *userPresentGOClient) GetPresentConfigList(ctx context.Context, in *GetPresentConfigListReq, opts ...grpc.CallOption) (*GetPresentConfigListResp, error) {
	out := new(GetPresentConfigListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) AddPresentConfig(ctx context.Context, in *AddPresentConfigReq, opts ...grpc.CallOption) (*AddPresentConfigResp, error) {
	out := new(AddPresentConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/AddPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) UpdatePresentConfig(ctx context.Context, in *UpdatePresentConfigReq, opts ...grpc.CallOption) (*UpdatePresentConfigResp, error) {
	out := new(UpdatePresentConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/UpdatePresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) DelPresentConfig(ctx context.Context, in *DelPresentConfigReq, opts ...grpc.CallOption) (*DelPresentConfigResp, error) {
	out := new(DelPresentConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/DelPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentConfigListV3(ctx context.Context, in *GetPresentConfigListV3Req, opts ...grpc.CallOption) (*GetPresentConfigListV3Resp, error) {
	out := new(GetPresentConfigListV3Resp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentConfigListV3", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentConfigById(ctx context.Context, in *GetPresentConfigByIdReq, opts ...grpc.CallOption) (*GetPresentConfigByIdResp, error) {
	out := new(GetPresentConfigByIdResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentConfigById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentConfigListByIdList(ctx context.Context, in *GetPresentConfigListByIdListReq, opts ...grpc.CallOption) (*GetPresentConfigListByIdListResp, error) {
	out := new(GetPresentConfigListByIdListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentConfigListByIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetLivePresentOrderList(ctx context.Context, in *GetLivePresentOrderListReq, opts ...grpc.CallOption) (*GetLivePresentOrderListResp, error) {
	out := new(GetLivePresentOrderListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetLivePresentOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserPresentGOServer is the server API for UserPresentGO service.
type UserPresentGOServer interface {
	// 礼物列表 - 旧版
	GetPresentConfigList(context.Context, *GetPresentConfigListReq) (*GetPresentConfigListResp, error)
	// 增删查改
	AddPresentConfig(context.Context, *AddPresentConfigReq) (*AddPresentConfigResp, error)
	UpdatePresentConfig(context.Context, *UpdatePresentConfigReq) (*UpdatePresentConfigResp, error)
	DelPresentConfig(context.Context, *DelPresentConfigReq) (*DelPresentConfigResp, error)
	// 查询礼物列表 - 新版 - V2旧的userpresent协议用过了
	GetPresentConfigListV3(context.Context, *GetPresentConfigListV3Req) (*GetPresentConfigListV3Resp, error)
	GetPresentConfigById(context.Context, *GetPresentConfigByIdReq) (*GetPresentConfigByIdResp, error)
	GetPresentConfigListByIdList(context.Context, *GetPresentConfigListByIdListReq) (*GetPresentConfigListByIdListResp, error)
	// 获取虚拟直播间主播某时间段收礼流水
	GetLivePresentOrderList(context.Context, *GetLivePresentOrderListReq) (*GetLivePresentOrderListResp, error)
}

func RegisterUserPresentGOServer(s *grpc.Server, srv UserPresentGOServer) {
	s.RegisterService(&_UserPresentGO_serviceDesc, srv)
}

func _UserPresentGO_GetPresentConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentConfigList(ctx, req.(*GetPresentConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_AddPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).AddPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/AddPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).AddPresentConfig(ctx, req.(*AddPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_UpdatePresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).UpdatePresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/UpdatePresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).UpdatePresentConfig(ctx, req.(*UpdatePresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_DelPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).DelPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/DelPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).DelPresentConfig(ctx, req.(*DelPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentConfigListV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigListV3Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentConfigListV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentConfigListV3",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentConfigListV3(ctx, req.(*GetPresentConfigListV3Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentConfigById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentConfigById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentConfigById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentConfigById(ctx, req.(*GetPresentConfigByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentConfigListByIdList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigListByIdListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentConfigListByIdList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentConfigListByIdList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentConfigListByIdList(ctx, req.(*GetPresentConfigListByIdListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetLivePresentOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLivePresentOrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetLivePresentOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetLivePresentOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetLivePresentOrderList(ctx, req.(*GetLivePresentOrderListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserPresentGO_serviceDesc = grpc.ServiceDesc{
	ServiceName: "userpresent_go.UserPresentGO",
	HandlerType: (*UserPresentGOServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPresentConfigList",
			Handler:    _UserPresentGO_GetPresentConfigList_Handler,
		},
		{
			MethodName: "AddPresentConfig",
			Handler:    _UserPresentGO_AddPresentConfig_Handler,
		},
		{
			MethodName: "UpdatePresentConfig",
			Handler:    _UserPresentGO_UpdatePresentConfig_Handler,
		},
		{
			MethodName: "DelPresentConfig",
			Handler:    _UserPresentGO_DelPresentConfig_Handler,
		},
		{
			MethodName: "GetPresentConfigListV3",
			Handler:    _UserPresentGO_GetPresentConfigListV3_Handler,
		},
		{
			MethodName: "GetPresentConfigById",
			Handler:    _UserPresentGO_GetPresentConfigById_Handler,
		},
		{
			MethodName: "GetPresentConfigListByIdList",
			Handler:    _UserPresentGO_GetPresentConfigListByIdList_Handler,
		},
		{
			MethodName: "GetLivePresentOrderList",
			Handler:    _UserPresentGO_GetLivePresentOrderList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "userpresent-go/userpresent-go.proto",
}

func init() {
	proto.RegisterFile("userpresent-go/userpresent-go.proto", fileDescriptor_userpresent_go_54677f92ec8f1a21)
}

var fileDescriptor_userpresent_go_54677f92ec8f1a21 = []byte{
	// 2122 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x59, 0xcd, 0x72, 0xdb, 0xc8,
	0x11, 0x16, 0x49, 0x89, 0x24, 0x9a, 0x3f, 0xa6, 0x47, 0xb2, 0x4d, 0xc9, 0xeb, 0x5a, 0x19, 0xfb,
	0xa7, 0x75, 0x2a, 0xb2, 0x4b, 0x5b, 0xbe, 0x24, 0xb5, 0x95, 0x15, 0x4d, 0xae, 0xc3, 0x8a, 0x4c,
	0xb9, 0x20, 0xca, 0x4e, 0x6d, 0xb2, 0x8b, 0x80, 0xc0, 0x90, 0x9c, 0x12, 0x08, 0x70, 0x31, 0x43,
	0xfd, 0x1c, 0x92, 0x4b, 0x92, 0x5b, 0x8e, 0x79, 0x83, 0xbc, 0x40, 0x92, 0x87, 0xc9, 0x1b, 0xa4,
	0x72, 0xcb, 0x29, 0x0f, 0x90, 0x9a, 0x06, 0x40, 0xe2, 0x8f, 0x12, 0x95, 0x28, 0xb9, 0x61, 0x7a,
	0x7a, 0xba, 0x7b, 0x7a, 0xba, 0xbf, 0x99, 0x6e, 0xc0, 0x47, 0x33, 0x4e, 0xbd, 0xa9, 0x47, 0x39,
	0x75, 0xc4, 0x0f, 0x47, 0xee, 0xf3, 0xf8, 0x70, 0x7f, 0xea, 0xb9, 0xc2, 0x25, 0xf5, 0x08, 0x55,
	0x1f, 0xb9, 0xea, 0x97, 0x70, 0xff, 0x44, 0xbc, 0x72, 0x9d, 0x21, 0x1b, 0x75, 0x5d, 0xde, 0xb9,
	0x14, 0xd4, 0xb1, 0xc8, 0x1e, 0x34, 0xce, 0x99, 0x45, 0x5d, 0x9d, 0x0e, 0x87, 0xd4, 0x14, 0xfa,
	0xcc, 0xb3, 0x9b, 0xb9, 0xdd, 0xdc, 0x5e, 0x55, 0xab, 0x23, 0xbd, 0x83, 0xe4, 0x53, 0xcf, 0x56,
	0xff, 0x5e, 0x84, 0xed, 0x13, 0xf1, 0xd6, 0x97, 0xd7, 0x15, 0x74, 0xe2, 0x8b, 0x0a, 0xe4, 0x3c,
	0x82, 0x12, 0x13, 0x74, 0xa2, 0x33, 0x0b, 0x97, 0xd7, 0xb4, 0xa2, 0x1c, 0x76, 0xb3, 0x15, 0xe4,
	0xb3, 0x14, 0x90, 0x0f, 0xa1, 0xc2, 0xc7, 0xee, 0x45, 0xc0, 0xd8, 0x2c, 0xa0, 0x18, 0x90, 0x24,
	0x9f, 0x87, 0xec, 0xc3, 0xe6, 0xcc, 0x41, 0x96, 0x81, 0x21, 0xcc, 0xb1, 0xee, 0x4e, 0x05, 0x73,
	0x9d, 0xe6, 0xfa, 0x6e, 0x6e, 0xaf, 0xac, 0xdd, 0xf7, 0xa7, 0x5a, 0x72, 0xe6, 0x18, 0x27, 0xd0,
	0x26, 0xae, 0x0b, 0xca, 0x45, 0x73, 0x03, 0x79, 0x8a, 0x8c, 0xf7, 0x29, 0x17, 0x72, 0x62, 0x68,
	0xbb, 0x17, 0xd2, 0xd8, 0xa2, 0x6f, 0xac, 0x1c, 0x76, 0x2d, 0xf2, 0x15, 0x00, 0x73, 0xb9, 0x4e,
	0x71, 0x4f, 0xcd, 0xd2, 0x6e, 0x6e, 0xaf, 0x72, 0xf0, 0x74, 0x3f, 0xee, 0xc7, 0xfd, 0x94, 0x13,
	0x35, 0x85, 0xcd, 0xfd, 0xf9, 0x04, 0xc0, 0x71, 0x05, 0x1b, 0x5e, 0xe9, 0x86, 0x6d, 0x37, 0xcb,
	0xa8, 0x56, 0xf1, 0x29, 0x87, 0xb6, 0x4d, 0x1a, 0x50, 0x10, 0xc6, 0xa8, 0xa9, 0xa0, 0x56, 0xf9,
	0x49, 0x3e, 0x81, 0xfa, 0xd0, 0xf5, 0x4c, 0xaa, 0x73, 0xea, 0x58, 0xc6, 0xc0, 0xa6, 0x4d, 0xc0,
	0x45, 0x35, 0xa4, 0x9e, 0x04, 0x44, 0xc9, 0xe6, 0xb8, 0x03, 0x66, 0x33, 0x71, 0xa5, 0xdb, 0xf4,
	0x9c, 0xda, 0xcd, 0x0a, 0xca, 0xa8, 0x85, 0xd4, 0x23, 0x49, 0x24, 0x2f, 0x60, 0x2b, 0x70, 0x51,
	0x68, 0x30, 0x1f, 0x53, 0x7b, 0xd8, 0xac, 0xa2, 0x4c, 0xe2, 0xcf, 0x05, 0x67, 0x78, 0x22, 0x67,
	0xc8, 0xa7, 0x70, 0x2f, 0xe2, 0x75, 0x5d, 0xee, 0xbb, 0xe6, 0x1b, 0xb0, 0xf0, 0x7c, 0xc7, 0x0f,
	0x94, 0x05, 0x8b, 0x6e, 0x51, 0xdb, 0xb8, 0x6a, 0xd6, 0x91, 0xb1, 0x4e, 0x43, 0xa6, 0xb6, 0xa4,
	0x92, 0x1f, 0x43, 0xc5, 0x9c, 0x71, 0xe1, 0x4e, 0x74, 0x41, 0x2f, 0x45, 0xf3, 0xde, 0x6e, 0x61,
	0xaf, 0x72, 0xb0, 0x93, 0xf4, 0xe2, 0x2b, 0x64, 0xe9, 0xd3, 0x4b, 0xa1, 0x81, 0x39, 0xff, 0x26,
	0x2a, 0xd4, 0xf8, 0xc4, 0xb0, 0x6d, 0xfd, 0xdc, 0x98, 0x62, 0xac, 0x34, 0x76, 0x73, 0x7b, 0x8a,
	0x56, 0x41, 0xe2, 0x3b, 0x63, 0x2a, 0x03, 0x25, 0xc6, 0x33, 0xb1, 0x5e, 0x36, 0xef, 0xc7, 0x79,
	0xde, 0x58, 0x2f, 0xc9, 0xc7, 0x50, 0x9f, 0x30, 0x33, 0x1a, 0x74, 0x04, 0x99, 0xaa, 0x13, 0x66,
	0x2e, 0x42, 0x2e, 0xce, 0x25, 0x45, 0x6d, 0x26, 0xb8, 0xa4, 0x2c, 0x79, 0x44, 0x33, 0xce, 0x5c,
	0x27, 0x74, 0x6a, 0x73, 0x2b, 0x38, 0x22, 0xa4, 0x06, 0xee, 0x94, 0x9e, 0x64, 0x5c, 0x1f, 0xb8,
	0x97, 0xfa, 0xc0, 0xa3, 0xc6, 0x19, 0x73, 0x46, 0xcd, 0x07, 0x3e, 0x1f, 0xe3, 0x2d, 0xf7, 0xb2,
	0x15, 0x10, 0x65, 0x88, 0x0c, 0x0d, 0x87, 0x07, 0xc7, 0xf8, 0x10, 0x8f, 0x51, 0x91, 0x14, 0x3c,
	0x42, 0xf5, 0x00, 0x60, 0xe1, 0x1b, 0x19, 0x30, 0x67, 0xf4, 0x0a, 0x73, 0x4a, 0xd1, 0xe4, 0x27,
	0x21, 0xb0, 0x8e, 0x7e, 0xcd, 0xef, 0x16, 0xf6, 0x14, 0x0d, 0xbf, 0xd5, 0x7f, 0x16, 0x60, 0x33,
	0x23, 0x37, 0x97, 0x67, 0x25, 0x81, 0x75, 0xc7, 0x98, 0x50, 0xcc, 0x44, 0x45, 0xc3, 0x6f, 0xb2,
	0x0d, 0x65, 0x66, 0xba, 0x0e, 0x3a, 0xab, 0x80, 0xf4, 0x92, 0x1c, 0x4b, 0x3f, 0x6d, 0xc1, 0xc6,
	0xd4, 0x63, 0x26, 0xc5, 0x5c, 0xab, 0x69, 0xfe, 0x40, 0x52, 0xb9, 0xe9, 0x7a, 0x14, 0xb3, 0xab,
	0xa6, 0xf9, 0x03, 0x49, 0x35, 0xc7, 0x86, 0x37, 0x09, 0x52, 0xcb, 0x1f, 0x48, 0x85, 0x9e, 0xe1,
	0x9c, 0x61, 0x4e, 0xd5, 0x34, 0xfc, 0x26, 0x4f, 0xa1, 0x1a, 0x78, 0x7e, 0x40, 0x47, 0xcc, 0xc1,
	0x6c, 0xa9, 0x69, 0x15, 0x9f, 0xd6, 0x92, 0x24, 0xe9, 0xab, 0x48, 0x60, 0xfa, 0x69, 0xa3, 0xcc,
	0xe3, 0x4d, 0x42, 0xc6, 0x6c, 0x6a, 0x19, 0x82, 0xea, 0x82, 0x4d, 0xfc, 0xcc, 0xa9, 0x69, 0xe0,
	0x93, 0xfa, 0x6c, 0x42, 0x25, 0x83, 0xe9, 0xd1, 0x39, 0x83, 0x9f, 0x33, 0xe0, 0x93, 0x90, 0xe1,
	0x01, 0x14, 0x19, 0x97, 0xe1, 0x1c, 0xa4, 0xc8, 0x06, 0xe3, 0x6d, 0x6a, 0x4b, 0xbd, 0xb8, 0x47,
	0x5d, 0x5c, 0x4d, 0x29, 0x26, 0x44, 0x4d, 0x53, 0x90, 0xd2, 0xbf, 0x9a, 0x52, 0x39, 0xed, 0x31,
	0x73, 0xac, 0x9f, 0x1b, 0xf6, 0x8c, 0x62, 0x1a, 0xd4, 0x34, 0x45, 0x52, 0xde, 0x49, 0x02, 0x39,
	0x84, 0x62, 0x00, 0x21, 0xf7, 0x10, 0x42, 0x3e, 0x4f, 0x43, 0xc8, 0x12, 0x1c, 0xd5, 0x82, 0x85,
	0xa8, 0xc1, 0x70, 0xce, 0xf4, 0xa1, 0xed, 0x1a, 0x02, 0x93, 0x20, 0xaf, 0x29, 0x92, 0xf2, 0xb5,
	0x24, 0xa8, 0xef, 0x61, 0xeb, 0x44, 0x9c, 0x72, 0xea, 0x85, 0xb9, 0x3c, 0x9b, 0x4c, 0x0c, 0xef,
	0x4a, 0x86, 0xcb, 0x6c, 0x7e, 0xd8, 0xf2, 0x33, 0x1a, 0x02, 0xf9, 0x58, 0x08, 0xc8, 0x73, 0x72,
	0x67, 0x4e, 0x08, 0xb4, 0xfe, 0x40, 0xfd, 0x2b, 0x46, 0x52, 0x44, 0x72, 0x9b, 0x0a, 0x83, 0xd9,
	0x32, 0x38, 0x86, 0x9e, 0x3b, 0xd1, 0x17, 0xd2, 0x4b, 0x72, 0x7c, 0xca, 0xd0, 0x54, 0x61, 0x78,
	0x23, 0x2a, 0x70, 0xd2, 0x57, 0xa2, 0xf8, 0x14, 0x39, 0xdd, 0x86, 0x0a, 0x1a, 0x60, 0xe2, 0x36,
	0x51, 0x5b, 0xe5, 0xe0, 0xa3, 0x15, 0x3c, 0xa2, 0x01, 0x5b, 0x44, 0xf2, 0x53, 0xa8, 0x7a, 0xd4,
	0xa4, 0xec, 0x3c, 0x38, 0x49, 0x3f, 0x10, 0x2b, 0x01, 0x0d, 0x8f, 0xf2, 0x09, 0x40, 0xa0, 0x48,
	0xee, 0xca, 0x8f, 0x49, 0xc5, 0x17, 0x31, 0x73, 0x04, 0x79, 0x0c, 0x8a, 0x61, 0x59, 0x7a, 0x34,
	0x36, 0xcb, 0x86, 0x65, 0xbd, 0xc2, 0xf0, 0xdc, 0x86, 0xb2, 0xeb, 0x59, 0xd4, 0x93, 0x6e, 0x2a,
	0xf9, 0xb1, 0x8f, 0xe3, 0x6e, 0xcc, 0x81, 0xe5, 0xa4, 0x03, 0xfd, 0xf0, 0x57, 0xa2, 0xe1, 0x2f,
	0x6f, 0x31, 0x89, 0x90, 0xdc, 0x9d, 0x79, 0xe6, 0x3c, 0x24, 0x25, 0xe9, 0x04, 0x29, 0x73, 0x86,
	0x09, 0x15, 0x63, 0xd7, 0x0a, 0x43, 0x52, 0x92, 0xde, 0x20, 0x45, 0xda, 0x22, 0x0d, 0x95, 0xe1,
	0x84, 0x41, 0x59, 0xd3, 0x4a, 0x86, 0x65, 0x69, 0xcc, 0x1c, 0x07, 0xd1, 0x3a, 0x3b, 0xbb, 0x08,
	0x30, 0x7a, 0x83, 0xf1, 0xd3, 0xb3, 0x0b, 0xf5, 0x17, 0xf0, 0xe8, 0x35, 0x0d, 0x1d, 0xe8, 0x3b,
	0xec, 0x88, 0x71, 0xa1, 0xd1, 0xef, 0xa5, 0x36, 0x19, 0xc2, 0xfa, 0x80, 0x89, 0x89, 0x31, 0x0d,
	0x8e, 0x0e, 0x24, 0xa9, 0x85, 0x94, 0x64, 0x0a, 0xe5, 0x93, 0x29, 0xa4, 0xfe, 0x1a, 0x9a, 0xd9,
	0xc2, 0xf9, 0x94, 0x7c, 0x05, 0xe8, 0x60, 0xdd, 0x66, 0x5c, 0x34, 0x73, 0x08, 0xf4, 0x2b, 0x9d,
	0x6c, 0x59, 0xae, 0x92, 0x52, 0x6e, 0x56, 0xff, 0x2d, 0x6c, 0x67, 0xa9, 0x7f, 0xf7, 0xc5, 0xdd,
	0xec, 0xee, 0x6f, 0x39, 0xd8, 0x59, 0x26, 0x9f, 0x4f, 0xc9, 0x97, 0xe9, 0x0d, 0xee, 0x26, 0x37,
	0x18, 0x5b, 0xdb, 0xa3, 0x17, 0x91, 0xdd, 0xed, 0x41, 0xc3, 0x36, 0xb8, 0xd0, 0xd3, 0x36, 0xd4,
	0x25, 0xfd, 0x74, 0x01, 0x54, 0xc7, 0xd0, 0xa0, 0x8e, 0xa0, 0x9e, 0x3e, 0xb0, 0x0d, 0xf3, 0xcc,
	0xd7, 0x57, 0x40, 0x7d, 0x9f, 0x2c, 0xd1, 0xd7, 0x91, 0xec, 0x2d, 0xc9, 0x2d, 0x99, 0xb5, 0x3a,
	0x9d, 0x8f, 0xa5, 0x6a, 0xf5, 0x2f, 0x79, 0xd8, 0x3c, 0xb4, 0xac, 0x98, 0x71, 0xd2, 0x65, 0x21,
	0xf2, 0xe7, 0x96, 0x20, 0x7f, 0x7e, 0x09, 0xf2, 0x17, 0xa2, 0xc8, 0x9f, 0x44, 0xee, 0xf5, 0x9b,
	0x90, 0x7b, 0x23, 0x89, 0xdc, 0xe1, 0x7d, 0x50, 0x8c, 0xdc, 0x07, 0x71, 0xd0, 0x2d, 0x25, 0x41,
	0x77, 0x81, 0xaa, 0xe5, 0xbb, 0x41, 0x55, 0x25, 0x89, 0xaa, 0xbf, 0x84, 0xad, 0xb4, 0xcb, 0xf8,
	0x34, 0x09, 0x61, 0xb9, 0xff, 0x08, 0xc2, 0xd4, 0x7d, 0xd8, 0x6c, 0x53, 0x3b, 0x75, 0x20, 0xcb,
	0xee, 0x68, 0xf5, 0x21, 0x6c, 0xa5, 0xf9, 0xf9, 0x54, 0xfd, 0x0e, 0x1e, 0xfa, 0x81, 0x93, 0x12,
	0x75, 0x37, 0x76, 0x6e, 0xc3, 0xa3, 0x4c, 0xf9, 0x7c, 0xaa, 0x1e, 0xa4, 0x81, 0xa6, 0x75, 0xd5,
	0xb5, 0xae, 0xdd, 0xc6, 0xb7, 0x69, 0xfc, 0xf0, 0xd7, 0xf0, 0x29, 0x39, 0xcc, 0x32, 0xf8, 0xe6,
	0x04, 0x8b, 0x5a, 0xfb, 0x23, 0xf8, 0x30, 0x2b, 0x7f, 0xa5, 0x8a, 0x10, 0x03, 0x63, 0xa6, 0x15,
	0x22, 0xa6, 0x51, 0xd8, 0xbd, 0x7e, 0x6d, 0x96, 0x89, 0x85, 0x5b, 0x9b, 0x68, 0xc2, 0xe3, 0x2c,
	0x0f, 0x1c, 0xdb, 0xd6, 0x1d, 0x46, 0xd7, 0xaf, 0xe0, 0x83, 0x2c, 0x25, 0x77, 0x07, 0xd5, 0xea,
	0x3f, 0x72, 0xd0, 0x48, 0xee, 0x93, 0xb4, 0xa0, 0x32, 0x30, 0x38, 0x8d, 0x1b, 0xff, 0x74, 0x89,
	0x7b, 0x5a, 0x06, 0xa7, 0xa1, 0xe9, 0x83, 0xf9, 0x37, 0xf9, 0x29, 0xd4, 0x02, 0xa8, 0x08, 0xa4,
	0xe4, 0xb3, 0x5d, 0x10, 0x02, 0x1f, 0xf2, 0x06, 0x72, 0x02, 0x1c, 0x0a, 0x24, 0x75, 0xa0, 0xea,
	0xa3, 0x68, 0xec, 0xb1, 0xa1, 0x5e, 0x87, 0xa0, 0x81, 0x9c, 0x0a, 0x5d, 0x0c, 0xd4, 0x7f, 0xe5,
	0xe1, 0x7e, 0xca, 0xe4, 0xff, 0xf1, 0x63, 0x7a, 0xf1, 0x10, 0xdd, 0x58, 0xfe, 0x10, 0x2d, 0x5e,
	0xff, 0x10, 0x2d, 0x25, 0x1f, 0xa2, 0xf3, 0x27, 0x4a, 0x39, 0xf3, 0x85, 0xae, 0x44, 0x5f, 0xe8,
	0x2b, 0x16, 0xa2, 0x89, 0x1b, 0xb5, 0x72, 0xd3, 0x93, 0xbb, 0x9a, 0x7a, 0x72, 0x47, 0xca, 0xf2,
	0x5a, 0xb4, 0x2c, 0x57, 0xff, 0x5c, 0x00, 0x92, 0x3e, 0x9a, 0xe5, 0x7e, 0x5f, 0xd2, 0x0f, 0xc8,
	0x2f, 0xeb, 0x07, 0x84, 0x77, 0x4e, 0x01, 0x71, 0x3f, 0xbb, 0x06, 0xb9, 0xfd, 0x4d, 0x96, 0xae,
	0xcc, 0x8b, 0xb7, 0xa9, 0xcc, 0x4b, 0xb7, 0xa9, 0xcc, 0xcb, 0xab, 0x56, 0xe6, 0x4a, 0x66, 0x65,
	0x7e, 0x63, 0xb9, 0x14, 0xb4, 0x27, 0x2a, 0x8b, 0xf6, 0x44, 0xbc, 0x58, 0xad, 0x26, 0x8b, 0xd5,
	0x3f, 0xad, 0xc3, 0x66, 0x46, 0x5a, 0xfe, 0x5f, 0xda, 0x41, 0x91, 0x2e, 0xce, 0x7a, 0xac, 0x8b,
	0x13, 0xef, 0xc1, 0x6c, 0x24, 0x7b, 0x30, 0x89, 0xfe, 0x44, 0xf1, 0xbf, 0xeb, 0x4f, 0x94, 0x56,
	0xe8, 0x4f, 0x94, 0x57, 0xe9, 0x4f, 0x28, 0x2b, 0xf5, 0x27, 0x20, 0xa3, 0x3f, 0x71, 0x63, 0x4a,
	0x66, 0x74, 0x26, 0xaa, 0x59, 0x9d, 0x89, 0x74, 0xa3, 0xa3, 0x96, 0xd5, 0xe8, 0x78, 0x0e, 0x5b,
	0xcc, 0xe5, 0x7a, 0xea, 0x1c, 0xeb, 0x78, 0x8e, 0xf7, 0x99, 0xcb, 0xdf, 0xc5, 0x5b, 0x87, 0xbf,
	0x81, 0x07, 0x99, 0x8f, 0x56, 0xf2, 0x13, 0x00, 0x1f, 0xaf, 0x11, 0xdd, 0x64, 0xa4, 0xd4, 0x97,
	0xde, 0xad, 0xb8, 0x54, 0x82, 0x9e, 0xa6, 0xd0, 0xf0, 0x53, 0x3a, 0x68, 0xc4, 0x86, 0x42, 0x5f,
	0x5c, 0x6d, 0x79, 0xbc, 0xe1, 0xab, 0x92, 0xda, 0x0d, 0x6f, 0xae, 0x3f, 0xf8, 0x8f, 0xfc, 0x23,
	0x76, 0x1e, 0xbe, 0x69, 0x8e, 0x65, 0x71, 0x17, 0xbe, 0x0f, 0x1e, 0x40, 0x51, 0xb8, 0x91, 0xca,
	0x76, 0x43, 0xb8, 0x41, 0x5d, 0x6b, 0x8e, 0x0d, 0xc7, 0xa1, 0xf6, 0xa2, 0x78, 0x56, 0x02, 0x8a,
	0x1f, 0x65, 0x08, 0x19, 0xbe, 0xd3, 0xfd, 0xf0, 0x54, 0x90, 0x82, 0x3e, 0xdf, 0x86, 0xb2, 0x4c,
	0xc7, 0x48, 0xb1, 0x5a, 0xa2, 0x8e, 0x85, 0x35, 0xc7, 0x77, 0xf8, 0x1e, 0xc8, 0xb6, 0x86, 0x4f,
	0xa5, 0x53, 0xfc, 0x5a, 0xf4, 0xba, 0xa2, 0x23, 0xb9, 0x5a, 0x53, 0xdc, 0x50, 0x88, 0xfa, 0xc7,
	0x1c, 0x34, 0x92, 0xf3, 0xd7, 0x15, 0xf0, 0xb7, 0x6b, 0x11, 0x44, 0xdc, 0xb5, 0x1e, 0x75, 0x57,
	0x02, 0xf7, 0x37, 0x92, 0xb8, 0xff, 0x8c, 0xce, 0x9f, 0x0f, 0x6f, 0xe7, 0xf7, 0xd7, 0x36, 0x3c,
	0x78, 0xab, 0x75, 0x4e, 0x3a, 0xbd, 0xbe, 0xfe, 0x56, 0xeb, 0xbe, 0xea, 0xe8, 0xa7, 0xbd, 0x9f,
	0xf5, 0x8e, 0xdf, 0xf7, 0x1a, 0x6b, 0xe4, 0x09, 0x6c, 0xc7, 0xa7, 0xb4, 0x4e, 0x5b, 0x6f, 0x77,
	0x0f, 0xdf, 0x1c, 0xf7, 0xda, 0x8d, 0x1c, 0x79, 0x04, 0x9b, 0xf1, 0xe9, 0x7e, 0xab, 0x73, 0xd8,
	0x6b, 0xe4, 0x9f, 0x7d, 0x03, 0x5b, 0x8b, 0xa7, 0x5c, 0xdf, 0x2f, 0x05, 0xdf, 0x18, 0x53, 0x42,
	0xa0, 0xfe, 0xea, 0xb8, 0xf7, 0x75, 0xf7, 0xb5, 0x7e, 0xda, 0x3b, 0xea, 0xbe, 0xe9, 0xf6, 0x1b,
	0x6b, 0xe4, 0x21, 0x90, 0x80, 0xd6, 0x3b, 0xee, 0xeb, 0x9d, 0x9f, 0xbf, 0xed, 0x6a, 0x1d, 0x29,
	0x3c, 0x4e, 0x6f, 0x77, 0x8e, 0x3a, 0xfd, 0x4e, 0xbb, 0x91, 0x7f, 0xf6, 0xfb, 0xc5, 0x13, 0x68,
	0x1e, 0x8e, 0xe4, 0x31, 0x3c, 0x4a, 0xd2, 0x4e, 0x9d, 0x33, 0xc7, 0xbd, 0x70, 0x1a, 0x6b, 0xb8,
	0xc1, 0xc4, 0x24, 0xa2, 0x7b, 0x23, 0x97, 0xb5, 0xee, 0xc8, 0x15, 0x82, 0x7a, 0x57, 0x8d, 0x3c,
	0xf9, 0x00, 0x9a, 0xc9, 0xc9, 0xf7, 0x8c, 0x8f, 0xe5, 0x9e, 0x1a, 0x85, 0x83, 0xdf, 0x95, 0xa0,
	0x16, 0xe9, 0xd1, 0xbc, 0x3e, 0x26, 0x67, 0xb0, 0x95, 0xf5, 0x94, 0x25, 0x9f, 0x25, 0x03, 0x67,
	0x49, 0xa3, 0x60, 0x67, 0x6f, 0x35, 0x46, 0x3e, 0x55, 0xd7, 0x88, 0x0e, 0x8d, 0x64, 0x9d, 0x44,
	0x52, 0xaf, 0xb5, 0x8c, 0xe2, 0x73, 0xe7, 0xe3, 0x9b, 0x99, 0x50, 0xc1, 0x18, 0x36, 0x33, 0x4a,
	0x10, 0xf2, 0x69, 0x72, 0x79, 0x76, 0x1d, 0xb4, 0xf3, 0xd9, 0x4a, 0x7c, 0xe1, 0x56, 0x92, 0x45,
	0x56, 0x7a, 0x2b, 0x19, 0x65, 0x5b, 0x7a, 0x2b, 0x99, 0xb5, 0xda, 0x1a, 0xf9, 0x1e, 0x1e, 0x66,
	0xf7, 0x17, 0xc8, 0xe7, 0xab, 0x78, 0x1c, 0xfb, 0x1c, 0x3b, 0xcf, 0x56, 0x65, 0x45, 0x95, 0x19,
	0xb1, 0x20, 0x4b, 0x81, 0x9b, 0x63, 0x21, 0xa8, 0xe5, 0x6e, 0x8e, 0x85, 0xb0, 0x80, 0x53, 0xd7,
	0xc8, 0x6f, 0x73, 0xe9, 0xc2, 0x23, 0x5a, 0x44, 0x91, 0xe7, 0xab, 0xd8, 0x1e, 0x29, 0xd7, 0x76,
	0x5e, 0xdc, 0x6e, 0x01, 0x5a, 0x21, 0xb0, 0x30, 0xcd, 0x82, 0x54, 0x92, 0xe5, 0xbb, 0x25, 0x37,
	0xc1, 0xce, 0x0f, 0x56, 0xe6, 0x95, 0x5a, 0x5b, 0x07, 0xdf, 0xbc, 0x18, 0xb9, 0xb6, 0xe1, 0x8c,
	0xf6, 0x5f, 0x1e, 0x08, 0xb1, 0x6f, 0xba, 0x93, 0xe7, 0xf8, 0xeb, 0xcd, 0x74, 0xed, 0xe7, 0x9c,
	0x7a, 0xe7, 0xcc, 0xa4, 0x3c, 0xf1, 0x6f, 0x6e, 0x50, 0x44, 0x8e, 0x2f, 0xfe, 0x1d, 0x00, 0x00,
	0xff, 0xff, 0x33, 0x77, 0x69, 0x0a, 0xc3, 0x1b, 0x00, 0x00,
}
