// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/fast-pc/fast-pc.proto

package fast_pc

import (
	gomock "github.com/golang/mock/gomock"
)

// MockFastPcClient is a mock of FastPcClient interface.
type MockFastPcClient struct {
	ctrl     *gomock.Controller
	recorder *MockFastPcClientMockRecorder
}

// MockFastPcClientMockRecorder is the mock recorder for MockFastPcClient.
type MockFastPcClientMockRecorder struct {
	mock *MockFastPcClient
}

// NewMockFastPcClient creates a new mock instance.
func NewMockFastPcClient(ctrl *gomock.Controller) *MockFastPcClient {
	mock := &MockFastPcClient{ctrl: ctrl}
	mock.recorder = &MockFastPcClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFastPcClient) EXPECT() *MockFastPcClientMockRecorder {
	return m.recorder
}

// MockFastPcServer is a mock of FastPcServer interface.
type MockFastPcServer struct {
	ctrl     *gomock.Controller
	recorder *MockFastPcServerMockRecorder
}

// MockFastPcServerMockRecorder is the mock recorder for MockFastPcServer.
type MockFastPcServerMockRecorder struct {
	mock *MockFastPcServer
}

// NewMockFastPcServer creates a new mock instance.
func NewMockFastPcServer(ctrl *gomock.Controller) *MockFastPcServer {
	mock := &MockFastPcServer{ctrl: ctrl}
	mock.recorder = &MockFastPcServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFastPcServer) EXPECT() *MockFastPcServerMockRecorder {
	return m.recorder
}
