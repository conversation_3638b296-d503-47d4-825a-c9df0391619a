// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/fast-pc/fast-pc.proto

package fast_pc // import "golang.52tt.com/protocol/services/fast-pc"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// FastPcClient is the client API for FastPc service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FastPcClient interface {
}

type fastPcClient struct {
	cc *grpc.ClientConn
}

func NewFastPcClient(cc *grpc.ClientConn) FastPcClient {
	return &fastPcClient{cc}
}

// FastPcServer is the server API for FastPc service.
type FastPcServer interface {
}

func RegisterFastPcServer(s *grpc.Server, srv FastPcServer) {
	s.RegisterService(&_FastPc_serviceDesc, srv)
}

var _FastPc_serviceDesc = grpc.ServiceDesc{
	ServiceName: "fast_pc.FastPc",
	HandlerType: (*FastPcServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams:     []grpc.StreamDesc{},
	Metadata:    "tt/quicksilver/fast-pc/fast-pc.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/fast-pc/fast-pc.proto", fileDescriptor_fast_pc_71145daefffbed4f)
}

var fileDescriptor_fast_pc_71145daefffbed4f = []byte{
	// 113 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x29, 0x29, 0xd1, 0x2f,
	0x2c, 0xcd, 0x4c, 0xce, 0x2e, 0xce, 0xcc, 0x29, 0x4b, 0x2d, 0xd2, 0x4f, 0x4b, 0x2c, 0x2e, 0xd1,
	0x2d, 0x48, 0x86, 0xd1, 0x7a, 0x05, 0x45, 0xf9, 0x25, 0xf9, 0x42, 0xec, 0x20, 0x6e, 0x7c, 0x41,
	0xb2, 0x11, 0x07, 0x17, 0x9b, 0x5b, 0x62, 0x71, 0x49, 0x40, 0xb2, 0x93, 0x76, 0x94, 0x66, 0x7a,
	0x7e, 0x4e, 0x62, 0x5e, 0xba, 0x9e, 0xa9, 0x51, 0x49, 0x89, 0x5e, 0x72, 0x7e, 0xae, 0x3e, 0x58,
	0x6d, 0x72, 0x7e, 0x8e, 0x7e, 0x71, 0x6a, 0x51, 0x59, 0x66, 0x72, 0x6a, 0x31, 0xcc, 0x94, 0x24,
	0x36, 0xb0, 0x94, 0x31, 0x20, 0x00, 0x00, 0xff, 0xff, 0x11, 0x08, 0xcd, 0x5c, 0x6e, 0x00, 0x00,
	0x00,
}
