// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/game-fre-server/game-fre-server.proto

package game_fre_server // import "golang.52tt.com/protocol/services/game-fre-server"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FreCountSource int32

const (
	FreCountSource_FRE_COUNT_SOURCE_UNSPECIFIED                          FreCountSource = 0
	FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_TEAM_IM_INVITE_ROOM        FreCountSource = 1
	FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_SEND_TEAM_MSG              FreCountSource = 2
	FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_SEND_CHANNEL_INVITE_ROOM   FreCountSource = 3
	FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_DAY_TOTAL   FreCountSource = 4
	FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_INTERVAL    FreCountSource = 5
	FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_GLOBAL_SEND_MSG            FreCountSource = 6
	FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_NORMAL_MSG_INTERVAL        FreCountSource = 7
	FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_SCREEN_SHOT_MSG_INTERVAL   FreCountSource = 8
	FreCountSource_FRE_COUNT_SOURCE_ENTER_ROOM_PUSH_INTERVAL             FreCountSource = 9
	FreCountSource_FRE_COUNT_SOURCE_FRIEND_RETURN_USER_LOGIN             FreCountSource = 10
	FreCountSource_FRE_COUNT_SOURCE_FAST_PC_FEEDBACK                     FreCountSource = 11
	FreCountSource_FRE_COUNT_SOURCE_AIGC_GROUP_LOW_FREQ_SPEAK_REMIND     FreCountSource = 12
	FreCountSource_FRE_COUNT_SOURCE_AIGC_COMMUNITY_POST_GUIDE_TASK_LIMIT FreCountSource = 13
	FreCountSource_FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_USER_LIMIT        FreCountSource = 14
	FreCountSource_FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_ROLE_LIMIT        FreCountSource = 15
	FreCountSource_FRE_COUNT_SOURCE_FAST_PC_SEND_CERTIFICATION_LIMIT     FreCountSource = 16
)

var FreCountSource_name = map[int32]string{
	0:  "FRE_COUNT_SOURCE_UNSPECIFIED",
	1:  "FRE_COUNT_SOURCE_GAME_HALL_TEAM_IM_INVITE_ROOM",
	2:  "FRE_COUNT_SOURCE_GAME_HALL_SEND_TEAM_MSG",
	3:  "FRE_COUNT_SOURCE_GAME_HALL_SEND_CHANNEL_INVITE_ROOM",
	4:  "FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_DAY_TOTAL",
	5:  "FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_INTERVAL",
	6:  "FRE_COUNT_SOURCE_GAME_HALL_GLOBAL_SEND_MSG",
	7:  "FRE_COUNT_SOURCE_GAME_HALL_NORMAL_MSG_INTERVAL",
	8:  "FRE_COUNT_SOURCE_GAME_HALL_SCREEN_SHOT_MSG_INTERVAL",
	9:  "FRE_COUNT_SOURCE_ENTER_ROOM_PUSH_INTERVAL",
	10: "FRE_COUNT_SOURCE_FRIEND_RETURN_USER_LOGIN",
	11: "FRE_COUNT_SOURCE_FAST_PC_FEEDBACK",
	12: "FRE_COUNT_SOURCE_AIGC_GROUP_LOW_FREQ_SPEAK_REMIND",
	13: "FRE_COUNT_SOURCE_AIGC_COMMUNITY_POST_GUIDE_TASK_LIMIT",
	14: "FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_USER_LIMIT",
	15: "FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_ROLE_LIMIT",
	16: "FRE_COUNT_SOURCE_FAST_PC_SEND_CERTIFICATION_LIMIT",
}
var FreCountSource_value = map[string]int32{
	"FRE_COUNT_SOURCE_UNSPECIFIED":                          0,
	"FRE_COUNT_SOURCE_GAME_HALL_TEAM_IM_INVITE_ROOM":        1,
	"FRE_COUNT_SOURCE_GAME_HALL_SEND_TEAM_MSG":              2,
	"FRE_COUNT_SOURCE_GAME_HALL_SEND_CHANNEL_INVITE_ROOM":   3,
	"FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_DAY_TOTAL":   4,
	"FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_INTERVAL":    5,
	"FRE_COUNT_SOURCE_GAME_HALL_GLOBAL_SEND_MSG":            6,
	"FRE_COUNT_SOURCE_GAME_HALL_NORMAL_MSG_INTERVAL":        7,
	"FRE_COUNT_SOURCE_GAME_HALL_SCREEN_SHOT_MSG_INTERVAL":   8,
	"FRE_COUNT_SOURCE_ENTER_ROOM_PUSH_INTERVAL":             9,
	"FRE_COUNT_SOURCE_FRIEND_RETURN_USER_LOGIN":             10,
	"FRE_COUNT_SOURCE_FAST_PC_FEEDBACK":                     11,
	"FRE_COUNT_SOURCE_AIGC_GROUP_LOW_FREQ_SPEAK_REMIND":     12,
	"FRE_COUNT_SOURCE_AIGC_COMMUNITY_POST_GUIDE_TASK_LIMIT": 13,
	"FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_USER_LIMIT":        14,
	"FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_ROLE_LIMIT":        15,
	"FRE_COUNT_SOURCE_FAST_PC_SEND_CERTIFICATION_LIMIT":     16,
}

func (x FreCountSource) String() string {
	return proto.EnumName(FreCountSource_name, int32(x))
}
func (FreCountSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_fre_server_8f4608118d639f13, []int{0}
}

type GetFreCountBySourceReq struct {
	// see FreCountSource
	FreCountSource uint32 `protobuf:"varint,1,opt,name=fre_count_source,json=freCountSource,proto3" json:"fre_count_source,omitempty"`
	// 特定场景特殊后缀
	Suffix string `protobuf:"bytes,2,opt,name=suffix,proto3" json:"suffix,omitempty"`
	// 获取某用户的频率次数
	UserId               uint32   `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFreCountBySourceReq) Reset()         { *m = GetFreCountBySourceReq{} }
func (m *GetFreCountBySourceReq) String() string { return proto.CompactTextString(m) }
func (*GetFreCountBySourceReq) ProtoMessage()    {}
func (*GetFreCountBySourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_fre_server_8f4608118d639f13, []int{0}
}
func (m *GetFreCountBySourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFreCountBySourceReq.Unmarshal(m, b)
}
func (m *GetFreCountBySourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFreCountBySourceReq.Marshal(b, m, deterministic)
}
func (dst *GetFreCountBySourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFreCountBySourceReq.Merge(dst, src)
}
func (m *GetFreCountBySourceReq) XXX_Size() int {
	return xxx_messageInfo_GetFreCountBySourceReq.Size(m)
}
func (m *GetFreCountBySourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFreCountBySourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFreCountBySourceReq proto.InternalMessageInfo

func (m *GetFreCountBySourceReq) GetFreCountSource() uint32 {
	if m != nil {
		return m.FreCountSource
	}
	return 0
}

func (m *GetFreCountBySourceReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *GetFreCountBySourceReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type GetFreCountBySourceResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFreCountBySourceResp) Reset()         { *m = GetFreCountBySourceResp{} }
func (m *GetFreCountBySourceResp) String() string { return proto.CompactTextString(m) }
func (*GetFreCountBySourceResp) ProtoMessage()    {}
func (*GetFreCountBySourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_fre_server_8f4608118d639f13, []int{1}
}
func (m *GetFreCountBySourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFreCountBySourceResp.Unmarshal(m, b)
}
func (m *GetFreCountBySourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFreCountBySourceResp.Marshal(b, m, deterministic)
}
func (dst *GetFreCountBySourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFreCountBySourceResp.Merge(dst, src)
}
func (m *GetFreCountBySourceResp) XXX_Size() int {
	return xxx_messageInfo_GetFreCountBySourceResp.Size(m)
}
func (m *GetFreCountBySourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFreCountBySourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFreCountBySourceResp proto.InternalMessageInfo

func (m *GetFreCountBySourceResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type BatGetFreItem struct {
	// see FreCountSource
	FreCountSource uint32 `protobuf:"varint,1,opt,name=fre_count_source,json=freCountSource,proto3" json:"fre_count_source,omitempty"`
	// 特定场景特殊后缀
	Suffix string `protobuf:"bytes,2,opt,name=suffix,proto3" json:"suffix,omitempty"`
	// 获取某用户的频率次数
	UserId               uint32   `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetFreItem) Reset()         { *m = BatGetFreItem{} }
func (m *BatGetFreItem) String() string { return proto.CompactTextString(m) }
func (*BatGetFreItem) ProtoMessage()    {}
func (*BatGetFreItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_fre_server_8f4608118d639f13, []int{2}
}
func (m *BatGetFreItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetFreItem.Unmarshal(m, b)
}
func (m *BatGetFreItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetFreItem.Marshal(b, m, deterministic)
}
func (dst *BatGetFreItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetFreItem.Merge(dst, src)
}
func (m *BatGetFreItem) XXX_Size() int {
	return xxx_messageInfo_BatGetFreItem.Size(m)
}
func (m *BatGetFreItem) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetFreItem.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetFreItem proto.InternalMessageInfo

func (m *BatGetFreItem) GetFreCountSource() uint32 {
	if m != nil {
		return m.FreCountSource
	}
	return 0
}

func (m *BatGetFreItem) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *BatGetFreItem) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type BatGetFreCountBySourcesReq struct {
	Items                []*BatGetFreItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatGetFreCountBySourcesReq) Reset()         { *m = BatGetFreCountBySourcesReq{} }
func (m *BatGetFreCountBySourcesReq) String() string { return proto.CompactTextString(m) }
func (*BatGetFreCountBySourcesReq) ProtoMessage()    {}
func (*BatGetFreCountBySourcesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_fre_server_8f4608118d639f13, []int{3}
}
func (m *BatGetFreCountBySourcesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetFreCountBySourcesReq.Unmarshal(m, b)
}
func (m *BatGetFreCountBySourcesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetFreCountBySourcesReq.Marshal(b, m, deterministic)
}
func (dst *BatGetFreCountBySourcesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetFreCountBySourcesReq.Merge(dst, src)
}
func (m *BatGetFreCountBySourcesReq) XXX_Size() int {
	return xxx_messageInfo_BatGetFreCountBySourcesReq.Size(m)
}
func (m *BatGetFreCountBySourcesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetFreCountBySourcesReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetFreCountBySourcesReq proto.InternalMessageInfo

func (m *BatGetFreCountBySourcesReq) GetItems() []*BatGetFreItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type BatGetFreCountBySourcesResp struct {
	// uid_FreCountSource_suffix -> count
	ResMap               map[string]uint32 `protobuf:"bytes,1,rep,name=res_map,json=resMap,proto3" json:"res_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetFreCountBySourcesResp) Reset()         { *m = BatGetFreCountBySourcesResp{} }
func (m *BatGetFreCountBySourcesResp) String() string { return proto.CompactTextString(m) }
func (*BatGetFreCountBySourcesResp) ProtoMessage()    {}
func (*BatGetFreCountBySourcesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_fre_server_8f4608118d639f13, []int{4}
}
func (m *BatGetFreCountBySourcesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetFreCountBySourcesResp.Unmarshal(m, b)
}
func (m *BatGetFreCountBySourcesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetFreCountBySourcesResp.Marshal(b, m, deterministic)
}
func (dst *BatGetFreCountBySourcesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetFreCountBySourcesResp.Merge(dst, src)
}
func (m *BatGetFreCountBySourcesResp) XXX_Size() int {
	return xxx_messageInfo_BatGetFreCountBySourcesResp.Size(m)
}
func (m *BatGetFreCountBySourcesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetFreCountBySourcesResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetFreCountBySourcesResp proto.InternalMessageInfo

func (m *BatGetFreCountBySourcesResp) GetResMap() map[string]uint32 {
	if m != nil {
		return m.ResMap
	}
	return nil
}

type IncFreCountBySourceReq struct {
	// see FreCountSource
	FreCountSource uint32 `protobuf:"varint,1,opt,name=fre_count_source,json=freCountSource,proto3" json:"fre_count_source,omitempty"`
	// 特定场景特殊后缀
	Suffix string `protobuf:"bytes,2,opt,name=suffix,proto3" json:"suffix,omitempty"`
	// 过期时间
	ExpireTime uint64 `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// 增加某用户的频率次数
	UserId               uint32   `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncFreCountBySourceReq) Reset()         { *m = IncFreCountBySourceReq{} }
func (m *IncFreCountBySourceReq) String() string { return proto.CompactTextString(m) }
func (*IncFreCountBySourceReq) ProtoMessage()    {}
func (*IncFreCountBySourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_fre_server_8f4608118d639f13, []int{5}
}
func (m *IncFreCountBySourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncFreCountBySourceReq.Unmarshal(m, b)
}
func (m *IncFreCountBySourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncFreCountBySourceReq.Marshal(b, m, deterministic)
}
func (dst *IncFreCountBySourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncFreCountBySourceReq.Merge(dst, src)
}
func (m *IncFreCountBySourceReq) XXX_Size() int {
	return xxx_messageInfo_IncFreCountBySourceReq.Size(m)
}
func (m *IncFreCountBySourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IncFreCountBySourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_IncFreCountBySourceReq proto.InternalMessageInfo

func (m *IncFreCountBySourceReq) GetFreCountSource() uint32 {
	if m != nil {
		return m.FreCountSource
	}
	return 0
}

func (m *IncFreCountBySourceReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *IncFreCountBySourceReq) GetExpireTime() uint64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *IncFreCountBySourceReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type IncFreCountBySourceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncFreCountBySourceResp) Reset()         { *m = IncFreCountBySourceResp{} }
func (m *IncFreCountBySourceResp) String() string { return proto.CompactTextString(m) }
func (*IncFreCountBySourceResp) ProtoMessage()    {}
func (*IncFreCountBySourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_fre_server_8f4608118d639f13, []int{6}
}
func (m *IncFreCountBySourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncFreCountBySourceResp.Unmarshal(m, b)
}
func (m *IncFreCountBySourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncFreCountBySourceResp.Marshal(b, m, deterministic)
}
func (dst *IncFreCountBySourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncFreCountBySourceResp.Merge(dst, src)
}
func (m *IncFreCountBySourceResp) XXX_Size() int {
	return xxx_messageInfo_IncFreCountBySourceResp.Size(m)
}
func (m *IncFreCountBySourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IncFreCountBySourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_IncFreCountBySourceResp proto.InternalMessageInfo

type BatIncFreCountBySourcesReq struct {
	Items                []*BatIncFreItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatIncFreCountBySourcesReq) Reset()         { *m = BatIncFreCountBySourcesReq{} }
func (m *BatIncFreCountBySourcesReq) String() string { return proto.CompactTextString(m) }
func (*BatIncFreCountBySourcesReq) ProtoMessage()    {}
func (*BatIncFreCountBySourcesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_fre_server_8f4608118d639f13, []int{7}
}
func (m *BatIncFreCountBySourcesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatIncFreCountBySourcesReq.Unmarshal(m, b)
}
func (m *BatIncFreCountBySourcesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatIncFreCountBySourcesReq.Marshal(b, m, deterministic)
}
func (dst *BatIncFreCountBySourcesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatIncFreCountBySourcesReq.Merge(dst, src)
}
func (m *BatIncFreCountBySourcesReq) XXX_Size() int {
	return xxx_messageInfo_BatIncFreCountBySourcesReq.Size(m)
}
func (m *BatIncFreCountBySourcesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatIncFreCountBySourcesReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatIncFreCountBySourcesReq proto.InternalMessageInfo

func (m *BatIncFreCountBySourcesReq) GetItems() []*BatIncFreItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type BatIncFreItem struct {
	// see FreCountSource
	FreCountSource uint32 `protobuf:"varint,1,opt,name=fre_count_source,json=freCountSource,proto3" json:"fre_count_source,omitempty"`
	// 特定场景特殊后缀
	Suffix string `protobuf:"bytes,2,opt,name=suffix,proto3" json:"suffix,omitempty"`
	// 过期时间
	ExpireTime uint64 `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// 增加某用户的频率次数
	UserId               uint32   `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatIncFreItem) Reset()         { *m = BatIncFreItem{} }
func (m *BatIncFreItem) String() string { return proto.CompactTextString(m) }
func (*BatIncFreItem) ProtoMessage()    {}
func (*BatIncFreItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_fre_server_8f4608118d639f13, []int{8}
}
func (m *BatIncFreItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatIncFreItem.Unmarshal(m, b)
}
func (m *BatIncFreItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatIncFreItem.Marshal(b, m, deterministic)
}
func (dst *BatIncFreItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatIncFreItem.Merge(dst, src)
}
func (m *BatIncFreItem) XXX_Size() int {
	return xxx_messageInfo_BatIncFreItem.Size(m)
}
func (m *BatIncFreItem) XXX_DiscardUnknown() {
	xxx_messageInfo_BatIncFreItem.DiscardUnknown(m)
}

var xxx_messageInfo_BatIncFreItem proto.InternalMessageInfo

func (m *BatIncFreItem) GetFreCountSource() uint32 {
	if m != nil {
		return m.FreCountSource
	}
	return 0
}

func (m *BatIncFreItem) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *BatIncFreItem) GetExpireTime() uint64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *BatIncFreItem) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type BatIncFreCountBySourcesResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatIncFreCountBySourcesResp) Reset()         { *m = BatIncFreCountBySourcesResp{} }
func (m *BatIncFreCountBySourcesResp) String() string { return proto.CompactTextString(m) }
func (*BatIncFreCountBySourcesResp) ProtoMessage()    {}
func (*BatIncFreCountBySourcesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_fre_server_8f4608118d639f13, []int{9}
}
func (m *BatIncFreCountBySourcesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatIncFreCountBySourcesResp.Unmarshal(m, b)
}
func (m *BatIncFreCountBySourcesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatIncFreCountBySourcesResp.Marshal(b, m, deterministic)
}
func (dst *BatIncFreCountBySourcesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatIncFreCountBySourcesResp.Merge(dst, src)
}
func (m *BatIncFreCountBySourcesResp) XXX_Size() int {
	return xxx_messageInfo_BatIncFreCountBySourcesResp.Size(m)
}
func (m *BatIncFreCountBySourcesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatIncFreCountBySourcesResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatIncFreCountBySourcesResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetFreCountBySourceReq)(nil), "game_fre_server.GetFreCountBySourceReq")
	proto.RegisterType((*GetFreCountBySourceResp)(nil), "game_fre_server.GetFreCountBySourceResp")
	proto.RegisterType((*BatGetFreItem)(nil), "game_fre_server.BatGetFreItem")
	proto.RegisterType((*BatGetFreCountBySourcesReq)(nil), "game_fre_server.BatGetFreCountBySourcesReq")
	proto.RegisterType((*BatGetFreCountBySourcesResp)(nil), "game_fre_server.BatGetFreCountBySourcesResp")
	proto.RegisterMapType((map[string]uint32)(nil), "game_fre_server.BatGetFreCountBySourcesResp.ResMapEntry")
	proto.RegisterType((*IncFreCountBySourceReq)(nil), "game_fre_server.IncFreCountBySourceReq")
	proto.RegisterType((*IncFreCountBySourceResp)(nil), "game_fre_server.IncFreCountBySourceResp")
	proto.RegisterType((*BatIncFreCountBySourcesReq)(nil), "game_fre_server.BatIncFreCountBySourcesReq")
	proto.RegisterType((*BatIncFreItem)(nil), "game_fre_server.BatIncFreItem")
	proto.RegisterType((*BatIncFreCountBySourcesResp)(nil), "game_fre_server.BatIncFreCountBySourcesResp")
	proto.RegisterEnum("game_fre_server.FreCountSource", FreCountSource_name, FreCountSource_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameFreServerClient is the client API for GameFreServer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameFreServerClient interface {
	GetFreCountBySource(ctx context.Context, in *GetFreCountBySourceReq, opts ...grpc.CallOption) (*GetFreCountBySourceResp, error)
	BatGetFreCountBySources(ctx context.Context, in *BatGetFreCountBySourcesReq, opts ...grpc.CallOption) (*BatGetFreCountBySourcesResp, error)
	IncFreCountBySource(ctx context.Context, in *IncFreCountBySourceReq, opts ...grpc.CallOption) (*IncFreCountBySourceResp, error)
	BatIncFreCountBySources(ctx context.Context, in *BatIncFreCountBySourcesReq, opts ...grpc.CallOption) (*BatIncFreCountBySourcesResp, error)
}

type gameFreServerClient struct {
	cc *grpc.ClientConn
}

func NewGameFreServerClient(cc *grpc.ClientConn) GameFreServerClient {
	return &gameFreServerClient{cc}
}

func (c *gameFreServerClient) GetFreCountBySource(ctx context.Context, in *GetFreCountBySourceReq, opts ...grpc.CallOption) (*GetFreCountBySourceResp, error) {
	out := new(GetFreCountBySourceResp)
	err := c.cc.Invoke(ctx, "/game_fre_server.GameFreServer/GetFreCountBySource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameFreServerClient) BatGetFreCountBySources(ctx context.Context, in *BatGetFreCountBySourcesReq, opts ...grpc.CallOption) (*BatGetFreCountBySourcesResp, error) {
	out := new(BatGetFreCountBySourcesResp)
	err := c.cc.Invoke(ctx, "/game_fre_server.GameFreServer/BatGetFreCountBySources", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameFreServerClient) IncFreCountBySource(ctx context.Context, in *IncFreCountBySourceReq, opts ...grpc.CallOption) (*IncFreCountBySourceResp, error) {
	out := new(IncFreCountBySourceResp)
	err := c.cc.Invoke(ctx, "/game_fre_server.GameFreServer/IncFreCountBySource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameFreServerClient) BatIncFreCountBySources(ctx context.Context, in *BatIncFreCountBySourcesReq, opts ...grpc.CallOption) (*BatIncFreCountBySourcesResp, error) {
	out := new(BatIncFreCountBySourcesResp)
	err := c.cc.Invoke(ctx, "/game_fre_server.GameFreServer/BatIncFreCountBySources", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameFreServerServer is the server API for GameFreServer service.
type GameFreServerServer interface {
	GetFreCountBySource(context.Context, *GetFreCountBySourceReq) (*GetFreCountBySourceResp, error)
	BatGetFreCountBySources(context.Context, *BatGetFreCountBySourcesReq) (*BatGetFreCountBySourcesResp, error)
	IncFreCountBySource(context.Context, *IncFreCountBySourceReq) (*IncFreCountBySourceResp, error)
	BatIncFreCountBySources(context.Context, *BatIncFreCountBySourcesReq) (*BatIncFreCountBySourcesResp, error)
}

func RegisterGameFreServerServer(s *grpc.Server, srv GameFreServerServer) {
	s.RegisterService(&_GameFreServer_serviceDesc, srv)
}

func _GameFreServer_GetFreCountBySource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFreCountBySourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameFreServerServer).GetFreCountBySource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_fre_server.GameFreServer/GetFreCountBySource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameFreServerServer).GetFreCountBySource(ctx, req.(*GetFreCountBySourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameFreServer_BatGetFreCountBySources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetFreCountBySourcesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameFreServerServer).BatGetFreCountBySources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_fre_server.GameFreServer/BatGetFreCountBySources",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameFreServerServer).BatGetFreCountBySources(ctx, req.(*BatGetFreCountBySourcesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameFreServer_IncFreCountBySource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncFreCountBySourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameFreServerServer).IncFreCountBySource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_fre_server.GameFreServer/IncFreCountBySource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameFreServerServer).IncFreCountBySource(ctx, req.(*IncFreCountBySourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameFreServer_BatIncFreCountBySources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatIncFreCountBySourcesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameFreServerServer).BatIncFreCountBySources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_fre_server.GameFreServer/BatIncFreCountBySources",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameFreServerServer).BatIncFreCountBySources(ctx, req.(*BatIncFreCountBySourcesReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameFreServer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_fre_server.GameFreServer",
	HandlerType: (*GameFreServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFreCountBySource",
			Handler:    _GameFreServer_GetFreCountBySource_Handler,
		},
		{
			MethodName: "BatGetFreCountBySources",
			Handler:    _GameFreServer_BatGetFreCountBySources_Handler,
		},
		{
			MethodName: "IncFreCountBySource",
			Handler:    _GameFreServer_IncFreCountBySource_Handler,
		},
		{
			MethodName: "BatIncFreCountBySources",
			Handler:    _GameFreServer_BatIncFreCountBySources_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/game-fre-server/game-fre-server.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/game-fre-server/game-fre-server.proto", fileDescriptor_game_fre_server_8f4608118d639f13)
}

var fileDescriptor_game_fre_server_8f4608118d639f13 = []byte{
	// 844 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x56, 0xdd, 0x52, 0xdb, 0x46,
	0x14, 0x46, 0x18, 0x4c, 0x39, 0xd4, 0x44, 0xb3, 0xed, 0x00, 0xa5, 0x7f, 0xd4, 0x33, 0x9d, 0xba,
	0x69, 0xb0, 0x27, 0x26, 0xb4, 0x49, 0xef, 0x64, 0x79, 0x25, 0xb6, 0xe8, 0xc7, 0x59, 0xad, 0xd2,
	0x49, 0x6f, 0x76, 0x5c, 0x67, 0xcd, 0x28, 0x41, 0x58, 0x48, 0x32, 0x13, 0x9e, 0xa1, 0xb7, 0x7d,
	0x8b, 0x3e, 0x50, 0x9f, 0xa3, 0x6f, 0xd0, 0xd1, 0x4a, 0x69, 0x30, 0x5e, 0x07, 0xf7, 0x22, 0x93,
	0x3b, 0x9d, 0xb3, 0xdf, 0xb7, 0xe7, 0x7c, 0xe7, 0xec, 0x9c, 0x23, 0x78, 0x94, 0xe7, 0x9d, 0xcb,
	0x69, 0x34, 0x7a, 0x95, 0x45, 0xe7, 0x57, 0x22, 0xed, 0x9c, 0x0d, 0x63, 0x71, 0x38, 0x4e, 0xc5,
	0x61, 0x26, 0x52, 0x85, 0xdd, 0x4e, 0xd2, 0x49, 0x3e, 0x41, 0xf7, 0x0a, 0x37, 0x1f, 0xa7, 0x82,
	0x97, 0xee, 0x66, 0x06, 0x3b, 0xb6, 0xc8, 0xad, 0x54, 0x98, 0x93, 0xe9, 0x45, 0xde, 0xbb, 0x0e,
	0x26, 0xd3, 0x74, 0x24, 0xa8, 0xb8, 0x44, 0x2d, 0xd0, 0x0b, 0xdc, 0xa8, 0xf0, 0xf3, 0x4c, 0xba,
	0xf7, 0xb4, 0x03, 0xad, 0xd5, 0xa0, 0xdb, 0xe3, 0x0a, 0x5e, 0x82, 0xd1, 0x0e, 0xd4, 0xb3, 0xe9,
	0x78, 0x1c, 0xbd, 0xde, 0x5b, 0x3d, 0xd0, 0x5a, 0x9b, 0xb4, 0xb2, 0xd0, 0x2e, 0x6c, 0x4c, 0x33,
	0x91, 0xf2, 0xe8, 0xc5, 0x5e, 0x4d, 0x12, 0xeb, 0x85, 0x49, 0x5e, 0x34, 0x3b, 0xb0, 0xab, 0x0c,
	0x9a, 0x25, 0xe8, 0x53, 0x58, 0x97, 0x11, 0xab, 0x50, 0xa5, 0xd1, 0x7c, 0x09, 0x8d, 0xde, 0x30,
	0x2f, 0x39, 0x24, 0x17, 0xf1, 0xfb, 0x4c, 0x8e, 0xc2, 0xfe, 0x7f, 0xb1, 0x66, 0xf2, 0xcb, 0x8a,
	0xaa, 0x3c, 0x82, 0xf5, 0x28, 0x17, 0x71, 0xb6, 0xa7, 0x1d, 0xd4, 0x5a, 0x5b, 0xdd, 0xaf, 0xda,
	0xb7, 0x0a, 0xda, 0x9e, 0xc9, 0x93, 0x96, 0xe0, 0xe6, 0x5f, 0x1a, 0x7c, 0xbe, 0xf0, 0xd2, 0x2c,
	0x41, 0x4f, 0x61, 0x23, 0x15, 0x19, 0x8f, 0x87, 0x49, 0x75, 0xef, 0xe3, 0xc5, 0xf7, 0xce, 0xd3,
	0xdb, 0x54, 0x64, 0xee, 0x30, 0xc1, 0x17, 0x79, 0x7a, 0x4d, 0xeb, 0xa9, 0x34, 0xf6, 0x9f, 0xc0,
	0xd6, 0x0d, 0x37, 0xd2, 0xa1, 0xf6, 0x4a, 0x5c, 0xcb, 0x1a, 0x6d, 0xd2, 0xe2, 0xb3, 0xa8, 0xf4,
	0xd5, 0xf0, 0x7c, 0x2a, 0x64, 0x5d, 0x1a, 0xb4, 0x34, 0x7e, 0x5e, 0x7d, 0xac, 0x35, 0xff, 0xd4,
	0x60, 0x87, 0x5c, 0x8c, 0xde, 0xcf, 0xa3, 0xf8, 0x1a, 0xb6, 0xc4, 0xeb, 0x24, 0x4a, 0x05, 0xcf,
	0xa3, 0x58, 0xc8, 0xda, 0xaf, 0x51, 0x28, 0x5d, 0x2c, 0x8a, 0xc5, 0xcd, 0xc6, 0xac, 0xcd, 0x34,
	0xe6, 0x33, 0xd8, 0x55, 0x66, 0x95, 0x25, 0x55, 0xcf, 0x14, 0xa7, 0x4b, 0xf7, 0xac, 0xe4, 0xde,
	0xec, 0xd9, 0x1f, 0x9a, 0x7c, 0x74, 0x6f, 0x0f, 0x3e, 0xa8, 0xf8, 0x2f, 0xe5, 0x03, 0x52, 0x2b,
	0xcc, 0x92, 0xfb, 0xff, 0xd4, 0x61, 0xdb, 0x9a, 0xcd, 0xe1, 0x00, 0xbe, 0xb0, 0x28, 0xe6, 0xa6,
	0x1f, 0x7a, 0x8c, 0x07, 0x7e, 0x48, 0x4d, 0xcc, 0x43, 0x2f, 0x18, 0x60, 0x93, 0x58, 0x04, 0xf7,
	0xf5, 0x15, 0xd4, 0x85, 0xf6, 0x1c, 0xc2, 0x36, 0x5c, 0xcc, 0x4f, 0x0c, 0xc7, 0xe1, 0x0c, 0x1b,
	0x2e, 0x27, 0x2e, 0x27, 0xde, 0x33, 0xc2, 0x30, 0xa7, 0xbe, 0xef, 0xea, 0x1a, 0x7a, 0x00, 0xad,
	0x77, 0x70, 0x02, 0xec, 0xf5, 0x4b, 0xa2, 0x1b, 0xd8, 0xfa, 0x2a, 0xfa, 0x09, 0x8e, 0xee, 0x42,
	0x9b, 0x27, 0x86, 0xe7, 0x61, 0x67, 0x26, 0x4c, 0xed, 0x0e, 0xe2, 0x2f, 0x3e, 0xf1, 0xca, 0x30,
	0x83, 0x30, 0x38, 0xe1, 0x7d, 0xe3, 0x39, 0x67, 0x3e, 0x33, 0x1c, 0x7d, 0x0d, 0xfd, 0x08, 0xdd,
	0xe5, 0x89, 0xc4, 0x63, 0x98, 0x3e, 0x33, 0x1c, 0x7d, 0x1d, 0xb5, 0xe1, 0xfe, 0x3b, 0x78, 0xb6,
	0xe3, 0xf7, 0x8c, 0x2a, 0xe1, 0x42, 0x59, 0xfd, 0x8e, 0xda, 0x79, 0x3e, 0x75, 0x0d, 0xa7, 0x80,
	0xbe, 0x8d, 0xb1, 0x71, 0x57, 0x35, 0x4c, 0x8a, 0xb1, 0xc7, 0x83, 0x13, 0x9f, 0xcd, 0x12, 0x3f,
	0x42, 0x87, 0xf0, 0xfd, 0x1c, 0x11, 0x17, 0xc7, 0xb2, 0x5c, 0xb7, 0xb4, 0x6c, 0x2a, 0xe1, 0x16,
	0x25, 0x45, 0xf2, 0x14, 0xb3, 0x90, 0x7a, 0x3c, 0x0c, 0x30, 0xe5, 0x8e, 0x6f, 0x13, 0x4f, 0x07,
	0xf4, 0x2d, 0x7c, 0x33, 0x0f, 0x37, 0x02, 0xc6, 0x07, 0x26, 0xb7, 0x30, 0xee, 0xf7, 0x0c, 0xf3,
	0x54, 0xdf, 0x42, 0xc7, 0xf0, 0x70, 0x0e, 0x66, 0x10, 0xdb, 0xe4, 0x36, 0xf5, 0xc3, 0x01, 0x77,
	0xfc, 0x5f, 0xb9, 0x45, 0xf1, 0x53, 0x1e, 0x0c, 0xb0, 0x71, 0xca, 0x29, 0x76, 0x89, 0xd7, 0xd7,
	0x3f, 0x46, 0x4f, 0xe0, 0x58, 0x4d, 0x33, 0x7d, 0xd7, 0x0d, 0x3d, 0xc2, 0x9e, 0xf3, 0x81, 0x1f,
	0x30, 0x6e, 0x87, 0xa4, 0x8f, 0x39, 0x33, 0x82, 0x53, 0xee, 0x10, 0x97, 0x30, 0xbd, 0xa1, 0xac,
	0xb1, 0xa4, 0xca, 0x3e, 0xd8, 0xc4, 0x62, 0x9c, 0x91, 0x41, 0x25, 0x46, 0x72, 0xb6, 0x97, 0xe5,
	0x50, 0xdf, 0xc1, 0x15, 0xe7, 0x9e, 0x52, 0xd9, 0x9b, 0x02, 0x94, 0x6f, 0x14, 0x53, 0x46, 0x2c,
	0x62, 0x1a, 0x8c, 0xf8, 0x5e, 0x45, 0xd3, 0xbb, 0x7f, 0xd7, 0xa0, 0x61, 0x0f, 0x63, 0x61, 0xa5,
	0x22, 0x90, 0x73, 0x04, 0xbd, 0x84, 0x4f, 0x14, 0x33, 0x1a, 0x7d, 0x37, 0x37, 0x70, 0xd4, 0x2b,
	0x77, 0xbf, 0xb5, 0x1c, 0x30, 0x4b, 0x9a, 0x2b, 0xe8, 0x0a, 0x76, 0x17, 0xac, 0x04, 0xf4, 0xc3,
	0xf2, 0xcb, 0xe3, 0x72, 0xff, 0xc1, 0xff, 0xd9, 0x34, 0xcd, 0x95, 0x42, 0xa3, 0x62, 0x0a, 0x29,
	0x34, 0xaa, 0x37, 0x88, 0x42, 0xe3, 0xa2, 0xa1, 0xfe, 0x46, 0xa3, 0x6a, 0xe8, 0xa9, 0x35, 0x2e,
	0x58, 0x00, 0x6a, 0x8d, 0x8b, 0x66, 0x69, 0x73, 0xa5, 0x77, 0xf4, 0xdb, 0xc3, 0xb3, 0xc9, 0xf9,
	0xf0, 0xe2, 0xac, 0x7d, 0xdc, 0xcd, 0xf3, 0xf6, 0x68, 0x12, 0x77, 0xe4, 0xef, 0xd3, 0x68, 0x72,
	0xde, 0x29, 0x2e, 0x88, 0x46, 0x22, 0xbb, 0xfd, 0x83, 0xf5, 0x7b, 0x5d, 0x42, 0x8e, 0xfe, 0x0d,
	0x00, 0x00, 0xff, 0xff, 0x9e, 0xb6, 0x04, 0xfe, 0x99, 0x09, 0x00, 0x00,
}
