// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-ext-content/aigc-ext-content.proto

package aigc_ext_content // import "golang.52tt.com/protocol/services/aigc/aigc-ext-content"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AsrTextType int32

const (
	AsrTextType_ASR_TEXT_TYPE_UNSPECIFIED AsrTextType = 0
	AsrTextType_ASR_TEXT_TYPE_NORMAL      AsrTextType = 1
	AsrTextType_ASR_TEXT_TYPE_INVALID     AsrTextType = 2
)

var AsrTextType_name = map[int32]string{
	0: "ASR_TEXT_TYPE_UNSPECIFIED",
	1: "ASR_TEXT_TYPE_NORMAL",
	2: "ASR_TEXT_TYPE_INVALID",
}
var AsrTextType_value = map[string]int32{
	"ASR_TEXT_TYPE_UNSPECIFIED": 0,
	"ASR_TEXT_TYPE_NORMAL":      1,
	"ASR_TEXT_TYPE_INVALID":     2,
}

func (x AsrTextType) String() string {
	return proto.EnumName(AsrTextType_name, int32(x))
}
func (AsrTextType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_ext_content_7aa797c05e27de05, []int{0}
}

type AIGiftMsg_AIGiftType int32

const (
	AIGiftMsg_AI_GIFT_TYPE_UNSPECIFIED AIGiftMsg_AIGiftType = 0
	// 句数
	AIGiftMsg_AI_GIFT_TYPE_SENTENCE_NUMBER AIGiftMsg_AIGiftType = 1
	// 聊天背景
	AIGiftMsg_AI_GIFT_TYPE_CHAT_BACKGROUND AIGiftMsg_AIGiftType = 2
)

var AIGiftMsg_AIGiftType_name = map[int32]string{
	0: "AI_GIFT_TYPE_UNSPECIFIED",
	1: "AI_GIFT_TYPE_SENTENCE_NUMBER",
	2: "AI_GIFT_TYPE_CHAT_BACKGROUND",
}
var AIGiftMsg_AIGiftType_value = map[string]int32{
	"AI_GIFT_TYPE_UNSPECIFIED":     0,
	"AI_GIFT_TYPE_SENTENCE_NUMBER": 1,
	"AI_GIFT_TYPE_CHAT_BACKGROUND": 2,
}

func (x AIGiftMsg_AIGiftType) String() string {
	return proto.EnumName(AIGiftMsg_AIGiftType_name, int32(x))
}
func (AIGiftMsg_AIGiftType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_ext_content_7aa797c05e27de05, []int{5, 0}
}

// 前端上传的asr信息
type AsrExtContent struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Seconds              uint32   `protobuf:"varint,2,opt,name=seconds,proto3" json:"seconds,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	Key                  string   `protobuf:"bytes,4,opt,name=key,proto3" json:"key,omitempty"`
	AsrTextType          uint32   `protobuf:"varint,5,opt,name=asr_text_type,json=asrTextType,proto3" json:"asr_text_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AsrExtContent) Reset()         { *m = AsrExtContent{} }
func (m *AsrExtContent) String() string { return proto.CompactTextString(m) }
func (*AsrExtContent) ProtoMessage()    {}
func (*AsrExtContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_ext_content_7aa797c05e27de05, []int{0}
}
func (m *AsrExtContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AsrExtContent.Unmarshal(m, b)
}
func (m *AsrExtContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AsrExtContent.Marshal(b, m, deterministic)
}
func (dst *AsrExtContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AsrExtContent.Merge(dst, src)
}
func (m *AsrExtContent) XXX_Size() int {
	return xxx_messageInfo_AsrExtContent.Size(m)
}
func (m *AsrExtContent) XXX_DiscardUnknown() {
	xxx_messageInfo_AsrExtContent.DiscardUnknown(m)
}

var xxx_messageInfo_AsrExtContent proto.InternalMessageInfo

func (m *AsrExtContent) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *AsrExtContent) GetSeconds() uint32 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

func (m *AsrExtContent) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *AsrExtContent) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *AsrExtContent) GetAsrTextType() uint32 {
	if m != nil {
		return m.AsrTextType
	}
	return 0
}

// 单聊，单人群聊句数提示ext
type SentenceTips struct {
	Tips                 string   `protobuf:"bytes,1,opt,name=tips,proto3" json:"tips,omitempty"`
	ShowMore             bool     `protobuf:"varint,2,opt,name=show_more,json=showMore,proto3" json:"show_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SentenceTips) Reset()         { *m = SentenceTips{} }
func (m *SentenceTips) String() string { return proto.CompactTextString(m) }
func (*SentenceTips) ProtoMessage()    {}
func (*SentenceTips) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_ext_content_7aa797c05e27de05, []int{1}
}
func (m *SentenceTips) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SentenceTips.Unmarshal(m, b)
}
func (m *SentenceTips) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SentenceTips.Marshal(b, m, deterministic)
}
func (dst *SentenceTips) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SentenceTips.Merge(dst, src)
}
func (m *SentenceTips) XXX_Size() int {
	return xxx_messageInfo_SentenceTips.Size(m)
}
func (m *SentenceTips) XXX_DiscardUnknown() {
	xxx_messageInfo_SentenceTips.DiscardUnknown(m)
}

var xxx_messageInfo_SentenceTips proto.InternalMessageInfo

func (m *SentenceTips) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

func (m *SentenceTips) GetShowMore() bool {
	if m != nil {
		return m.ShowMore
	}
	return false
}

// 欢迎语消息 IM_CMD_TYPE_WELCOME_MSG = 4
type WelcomeMsg struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	SpecialToUid         uint32   `protobuf:"varint,3,opt,name=special_to_uid,json=specialToUid,proto3" json:"special_to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WelcomeMsg) Reset()         { *m = WelcomeMsg{} }
func (m *WelcomeMsg) String() string { return proto.CompactTextString(m) }
func (*WelcomeMsg) ProtoMessage()    {}
func (*WelcomeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_ext_content_7aa797c05e27de05, []int{2}
}
func (m *WelcomeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WelcomeMsg.Unmarshal(m, b)
}
func (m *WelcomeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WelcomeMsg.Marshal(b, m, deterministic)
}
func (dst *WelcomeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WelcomeMsg.Merge(dst, src)
}
func (m *WelcomeMsg) XXX_Size() int {
	return xxx_messageInfo_WelcomeMsg.Size(m)
}
func (m *WelcomeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_WelcomeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_WelcomeMsg proto.InternalMessageInfo

func (m *WelcomeMsg) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *WelcomeMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *WelcomeMsg) GetSpecialToUid() uint32 {
	if m != nil {
		return m.SpecialToUid
	}
	return 0
}

// 进群提示信息 IM_CMD_TYPE_WELCOME_MSG = 3
type JoinGroupTips struct {
	Content              string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	JoinUid              uint32   `protobuf:"varint,2,opt,name=join_uid,json=joinUid,proto3" json:"join_uid,omitempty"`
	OtherUids            []uint32 `protobuf:"varint,3,rep,packed,name=other_uids,json=otherUids,proto3" json:"other_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinGroupTips) Reset()         { *m = JoinGroupTips{} }
func (m *JoinGroupTips) String() string { return proto.CompactTextString(m) }
func (*JoinGroupTips) ProtoMessage()    {}
func (*JoinGroupTips) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_ext_content_7aa797c05e27de05, []int{3}
}
func (m *JoinGroupTips) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinGroupTips.Unmarshal(m, b)
}
func (m *JoinGroupTips) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinGroupTips.Marshal(b, m, deterministic)
}
func (dst *JoinGroupTips) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinGroupTips.Merge(dst, src)
}
func (m *JoinGroupTips) XXX_Size() int {
	return xxx_messageInfo_JoinGroupTips.Size(m)
}
func (m *JoinGroupTips) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinGroupTips.DiscardUnknown(m)
}

var xxx_messageInfo_JoinGroupTips proto.InternalMessageInfo

func (m *JoinGroupTips) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *JoinGroupTips) GetJoinUid() uint32 {
	if m != nil {
		return m.JoinUid
	}
	return 0
}

func (m *JoinGroupTips) GetOtherUids() []uint32 {
	if m != nil {
		return m.OtherUids
	}
	return nil
}

type UserPresentMsg struct {
	Source               string   `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Icon                 string   `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPresentMsg) Reset()         { *m = UserPresentMsg{} }
func (m *UserPresentMsg) String() string { return proto.CompactTextString(m) }
func (*UserPresentMsg) ProtoMessage()    {}
func (*UserPresentMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_ext_content_7aa797c05e27de05, []int{4}
}
func (m *UserPresentMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPresentMsg.Unmarshal(m, b)
}
func (m *UserPresentMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPresentMsg.Marshal(b, m, deterministic)
}
func (dst *UserPresentMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPresentMsg.Merge(dst, src)
}
func (m *UserPresentMsg) XXX_Size() int {
	return xxx_messageInfo_UserPresentMsg.Size(m)
}
func (m *UserPresentMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPresentMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UserPresentMsg proto.InternalMessageInfo

func (m *UserPresentMsg) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *UserPresentMsg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *UserPresentMsg) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *UserPresentMsg) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type AIGiftMsg struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Icon                 string   `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Num                  uint32   `protobuf:"varint,4,opt,name=num,proto3" json:"num,omitempty"`
	Desc                 string   `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIGiftMsg) Reset()         { *m = AIGiftMsg{} }
func (m *AIGiftMsg) String() string { return proto.CompactTextString(m) }
func (*AIGiftMsg) ProtoMessage()    {}
func (*AIGiftMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_ext_content_7aa797c05e27de05, []int{5}
}
func (m *AIGiftMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIGiftMsg.Unmarshal(m, b)
}
func (m *AIGiftMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIGiftMsg.Marshal(b, m, deterministic)
}
func (dst *AIGiftMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIGiftMsg.Merge(dst, src)
}
func (m *AIGiftMsg) XXX_Size() int {
	return xxx_messageInfo_AIGiftMsg.Size(m)
}
func (m *AIGiftMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_AIGiftMsg.DiscardUnknown(m)
}

var xxx_messageInfo_AIGiftMsg proto.InternalMessageInfo

func (m *AIGiftMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AIGiftMsg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *AIGiftMsg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIGiftMsg) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *AIGiftMsg) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type UserPresentGuideMsg struct {
	// 引导内容
	Content              string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPresentGuideMsg) Reset()         { *m = UserPresentGuideMsg{} }
func (m *UserPresentGuideMsg) String() string { return proto.CompactTextString(m) }
func (*UserPresentGuideMsg) ProtoMessage()    {}
func (*UserPresentGuideMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_ext_content_7aa797c05e27de05, []int{6}
}
func (m *UserPresentGuideMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPresentGuideMsg.Unmarshal(m, b)
}
func (m *UserPresentGuideMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPresentGuideMsg.Marshal(b, m, deterministic)
}
func (dst *UserPresentGuideMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPresentGuideMsg.Merge(dst, src)
}
func (m *UserPresentGuideMsg) XXX_Size() int {
	return xxx_messageInfo_UserPresentGuideMsg.Size(m)
}
func (m *UserPresentGuideMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPresentGuideMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UserPresentGuideMsg proto.InternalMessageInfo

func (m *UserPresentGuideMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func init() {
	proto.RegisterType((*AsrExtContent)(nil), "aigc_ext_content.AsrExtContent")
	proto.RegisterType((*SentenceTips)(nil), "aigc_ext_content.SentenceTips")
	proto.RegisterType((*WelcomeMsg)(nil), "aigc_ext_content.WelcomeMsg")
	proto.RegisterType((*JoinGroupTips)(nil), "aigc_ext_content.JoinGroupTips")
	proto.RegisterType((*UserPresentMsg)(nil), "aigc_ext_content.UserPresentMsg")
	proto.RegisterType((*AIGiftMsg)(nil), "aigc_ext_content.AIGiftMsg")
	proto.RegisterType((*UserPresentGuideMsg)(nil), "aigc_ext_content.UserPresentGuideMsg")
	proto.RegisterEnum("aigc_ext_content.AsrTextType", AsrTextType_name, AsrTextType_value)
	proto.RegisterEnum("aigc_ext_content.AIGiftMsg_AIGiftType", AIGiftMsg_AIGiftType_name, AIGiftMsg_AIGiftType_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-ext-content/aigc-ext-content.proto", fileDescriptor_aigc_ext_content_7aa797c05e27de05)
}

var fileDescriptor_aigc_ext_content_7aa797c05e27de05 = []byte{
	// 588 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x53, 0x6f, 0x6f, 0xd3, 0x3e,
	0x10, 0xfe, 0xa5, 0xdd, 0xbf, 0xde, 0xd6, 0xa9, 0xf2, 0x6f, 0xa0, 0x4c, 0x6c, 0x52, 0x15, 0xf1,
	0x62, 0x42, 0xda, 0x2a, 0x81, 0x10, 0x42, 0xbc, 0x40, 0x59, 0x97, 0x95, 0xc0, 0x9a, 0x4d, 0x69,
	0x02, 0x83, 0x37, 0x51, 0x70, 0x6e, 0x9d, 0x59, 0x1a, 0x07, 0xdb, 0x19, 0xed, 0x07, 0xe0, 0x63,
	0xf2, 0x5d, 0x90, 0xdd, 0x14, 0x36, 0xad, 0xbc, 0xb1, 0x9e, 0x7b, 0xee, 0x7c, 0xf7, 0xdc, 0x9d,
	0x0d, 0x6f, 0x94, 0xea, 0x7d, 0xaf, 0x18, 0xbd, 0x91, 0x2c, 0xbf, 0x45, 0xd1, 0x4b, 0xd9, 0x98,
	0x9a, 0xe3, 0x10, 0xa7, 0xea, 0x90, 0xf2, 0x42, 0x61, 0xa1, 0x1e, 0x10, 0x47, 0xa5, 0xe0, 0x8a,
	0x93, 0x8e, 0xe6, 0x13, 0x9c, 0xaa, 0xa4, 0xe6, 0x9d, 0x9f, 0x16, 0xb4, 0x5d, 0x29, 0xbc, 0xa9,
	0xea, 0xcf, 0x19, 0xd2, 0x81, 0x66, 0x25, 0x72, 0xdb, 0xea, 0x5a, 0x07, 0xad, 0x50, 0x43, 0x62,
	0xc3, 0xba, 0x44, 0xca, 0x8b, 0x4c, 0xda, 0x8d, 0xae, 0x75, 0xd0, 0x0e, 0x17, 0x26, 0x21, 0xb0,
	0xa2, 0x70, 0xaa, 0xec, 0xa6, 0x09, 0x36, 0x58, 0xdf, 0xbf, 0xc1, 0x99, 0xbd, 0x32, 0xbf, 0x7f,
	0x83, 0x33, 0xe2, 0x40, 0x3b, 0x95, 0x22, 0xd1, 0xde, 0x44, 0xcd, 0x4a, 0xb4, 0x57, 0x4d, 0x96,
	0xcd, 0x54, 0x8a, 0x08, 0xa7, 0x2a, 0x9a, 0x95, 0xe8, 0xbc, 0x85, 0xad, 0x11, 0x6a, 0x01, 0x14,
	0x23, 0x56, 0xce, 0x33, 0xb3, 0x52, 0xd6, 0x32, 0x0c, 0x26, 0x4f, 0xa0, 0x25, 0xaf, 0xf9, 0x8f,
	0x64, 0xc2, 0x05, 0x1a, 0x25, 0x1b, 0xe1, 0x86, 0x26, 0x86, 0x5c, 0xa0, 0x73, 0x09, 0xf0, 0x09,
	0x73, 0xca, 0x27, 0x38, 0x94, 0xe3, 0x25, 0x4d, 0x2c, 0xa4, 0x36, 0xee, 0x48, 0x7d, 0x0a, 0xdb,
	0xb2, 0x44, 0xca, 0xd2, 0x3c, 0x51, 0x3c, 0xa9, 0x58, 0x66, 0x1a, 0x69, 0x87, 0x5b, 0x35, 0x1b,
	0xf1, 0x98, 0x65, 0x0e, 0x85, 0xf6, 0x7b, 0xce, 0x8a, 0x81, 0xe0, 0x55, 0x69, 0xb4, 0xd9, 0xb0,
	0x5e, 0x8f, 0xaf, 0x2e, 0xb0, 0x30, 0xc9, 0x2e, 0x6c, 0x7c, 0xe3, 0xac, 0x30, 0xa9, 0xea, 0x51,
	0x69, 0x3b, 0x66, 0x19, 0xd9, 0x07, 0xe0, 0xea, 0x1a, 0x85, 0xf6, 0x49, 0xbb, 0xd9, 0x6d, 0x1e,
	0xb4, 0xc3, 0x96, 0x61, 0x62, 0x96, 0x49, 0xe7, 0x0a, 0xb6, 0x63, 0x89, 0xe2, 0x42, 0xa0, 0xc4,
	0x42, 0xe9, 0x16, 0x1e, 0xc3, 0x9a, 0xe4, 0x95, 0xa0, 0x58, 0x17, 0xa9, 0x2d, 0xdd, 0x08, 0xa3,
	0xbc, 0x58, 0x34, 0xa2, 0x31, 0xd9, 0x81, 0x55, 0xc5, 0x54, 0x8e, 0xf5, 0x22, 0xe6, 0x86, 0x8e,
	0xcc, 0x50, 0xd2, 0x7a, 0x15, 0x06, 0x3b, 0xbf, 0x2c, 0x68, 0xb9, 0xfe, 0x80, 0x5d, 0x99, 0x1a,
	0x7a, 0x28, 0x7a, 0x21, 0x96, 0xd1, 0x6a, 0xf0, 0xd2, 0xfc, 0x04, 0x56, 0x8a, 0x74, 0xb2, 0x48,
	0x6f, 0xb0, 0x1e, 0x71, 0x51, 0x4d, 0x4c, 0xf2, 0x76, 0xa8, 0xe1, 0x9f, 0x7a, 0xab, 0x77, 0xea,
	0x15, 0x00, 0xf3, 0x72, 0x7a, 0xcb, 0x64, 0x0f, 0x6c, 0xd7, 0x4f, 0x06, 0xfe, 0x69, 0x94, 0x44,
	0x9f, 0x2f, 0xbc, 0x24, 0x0e, 0x46, 0x17, 0x5e, 0xdf, 0x3f, 0xf5, 0xbd, 0x93, 0xce, 0x7f, 0xa4,
	0x0b, 0x7b, 0xf7, 0xbc, 0x23, 0x2f, 0x88, 0xbc, 0xa0, 0xef, 0x25, 0x41, 0x3c, 0x3c, 0xf6, 0xc2,
	0x8e, 0xf5, 0x20, 0xa2, 0xff, 0xce, 0x8d, 0x92, 0x63, 0xb7, 0xff, 0x61, 0x10, 0x9e, 0xc7, 0xc1,
	0x49, 0xa7, 0xe1, 0xf4, 0xe0, 0xff, 0x3b, 0x73, 0x1c, 0x54, 0x2c, 0x33, 0xef, 0xe1, 0x9f, 0x2b,
	0x7b, 0x96, 0xc2, 0xa6, 0xfb, 0xf7, 0x1d, 0x92, 0x7d, 0xd8, 0x75, 0x47, 0x61, 0x12, 0x79, 0x97,
	0x4b, 0x25, 0xda, 0xb0, 0x73, 0xdf, 0x1d, 0x9c, 0x87, 0x43, 0xf7, 0xac, 0x63, 0x91, 0x5d, 0x78,
	0x74, 0xdf, 0xe3, 0x07, 0x1f, 0xdd, 0x33, 0xff, 0xa4, 0xd3, 0x38, 0x7e, 0xfd, 0xe5, 0xd5, 0x98,
	0xe7, 0x69, 0x31, 0x3e, 0x7a, 0xf9, 0x5c, 0xa9, 0x23, 0xca, 0x27, 0x3d, 0xf3, 0x1d, 0x29, 0xcf,
	0x7b, 0x12, 0xc5, 0x2d, 0xa3, 0x28, 0x97, 0xff, 0xe3, 0xaf, 0x6b, 0x26, 0xf0, 0xc5, 0xef, 0x00,
	0x00, 0x00, 0xff, 0xff, 0xaf, 0x80, 0x7a, 0xc2, 0xf6, 0x03, 0x00, 0x00,
}
