// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-common/aigc-common.proto

package aigc_common // import "golang.52tt.com/protocol/services/aigc/aigc-common"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ObjectType int32

const (
	ObjectType_OBJECT_TYPE_UNSPECIFIED ObjectType = 0
	// 评论
	ObjectType_OBJECT_TYPE_GROUP_TEMPLATE ObjectType = 1
)

var ObjectType_name = map[int32]string{
	0: "OBJECT_TYPE_UNSPECIFIED",
	1: "OBJECT_TYPE_GROUP_TEMPLATE",
}
var ObjectType_value = map[string]int32{
	"OBJECT_TYPE_UNSPECIFIED":    0,
	"OBJECT_TYPE_GROUP_TEMPLATE": 1,
}

func (x ObjectType) String() string {
	return proto.EnumName(ObjectType_name, int32(x))
}
func (ObjectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{0}
}

type AttitudeOp int32

const (
	AttitudeOp_ATTITUDE_OP_UNSPECIFIED AttitudeOp = 0
	// 点赞
	AttitudeOp_ATTITUDE_OP_LIKE AttitudeOp = 1
	// 取消点赞
	AttitudeOp_ATTITUDE_OP_UNLIKE AttitudeOp = 2
)

var AttitudeOp_name = map[int32]string{
	0: "ATTITUDE_OP_UNSPECIFIED",
	1: "ATTITUDE_OP_LIKE",
	2: "ATTITUDE_OP_UNLIKE",
}
var AttitudeOp_value = map[string]int32{
	"ATTITUDE_OP_UNSPECIFIED": 0,
	"ATTITUDE_OP_LIKE":        1,
	"ATTITUDE_OP_UNLIKE":      2,
}

func (x AttitudeOp) String() string {
	return proto.EnumName(AttitudeOp_name, int32(x))
}
func (AttitudeOp) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{1}
}

type RewardType int32

const (
	RewardType_REWARD_TYPE_UNSPECIFIED RewardType = 0
	RewardType_REWARD_TYPE_SHARE       RewardType = 1
	RewardType_REWARD_TYPE_TARGET      RewardType = 2
	RewardType_REWARD_TYPE_POST        RewardType = 3
	RewardType_REWARD_TYPE_PRESENT     RewardType = 4
)

var RewardType_name = map[int32]string{
	0: "REWARD_TYPE_UNSPECIFIED",
	1: "REWARD_TYPE_SHARE",
	2: "REWARD_TYPE_TARGET",
	3: "REWARD_TYPE_POST",
	4: "REWARD_TYPE_PRESENT",
}
var RewardType_value = map[string]int32{
	"REWARD_TYPE_UNSPECIFIED": 0,
	"REWARD_TYPE_SHARE":       1,
	"REWARD_TYPE_TARGET":      2,
	"REWARD_TYPE_POST":        3,
	"REWARD_TYPE_PRESENT":     4,
}

func (x RewardType) String() string {
	return proto.EnumName(RewardType_name, int32(x))
}
func (RewardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{2}
}

type CountType int32

const (
	CountType_COUNT_TYPE_UNSPECIFIED CountType = 0
	CountType_COUNT_TYPE_TODAY       CountType = 1
	CountType_COUNT_TYPE_TOTAL       CountType = 2
)

var CountType_name = map[int32]string{
	0: "COUNT_TYPE_UNSPECIFIED",
	1: "COUNT_TYPE_TODAY",
	2: "COUNT_TYPE_TOTAL",
}
var CountType_value = map[string]int32{
	"COUNT_TYPE_UNSPECIFIED": 0,
	"COUNT_TYPE_TODAY":       1,
	"COUNT_TYPE_TOTAL":       2,
}

func (x CountType) String() string {
	return proto.EnumName(CountType_name, int32(x))
}
func (CountType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{3}
}

type PushTo int32

const (
	PushTo_PUSH_TO_UNSPECIFIED PushTo = 0
	PushTo_PUSH_TO_PARTNER     PushTo = 1
	PushTo_PUSH_TO_GROUP       PushTo = 2
)

var PushTo_name = map[int32]string{
	0: "PUSH_TO_UNSPECIFIED",
	1: "PUSH_TO_PARTNER",
	2: "PUSH_TO_GROUP",
}
var PushTo_value = map[string]int32{
	"PUSH_TO_UNSPECIFIED": 0,
	"PUSH_TO_PARTNER":     1,
	"PUSH_TO_GROUP":       2,
}

func (x PushTo) String() string {
	return proto.EnumName(PushTo_name, int32(x))
}
func (PushTo) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{4}
}

type Entity_Type int32

const (
	Entity_TYPE_UNSPECIFIED  Entity_Type = 0
	Entity_TYPE_PARTNER      Entity_Type = 1
	Entity_TYPE_MUTI_GROUP   Entity_Type = 2
	Entity_TYPE_SINGLE_GROUP Entity_Type = 3
)

var Entity_Type_name = map[int32]string{
	0: "TYPE_UNSPECIFIED",
	1: "TYPE_PARTNER",
	2: "TYPE_MUTI_GROUP",
	3: "TYPE_SINGLE_GROUP",
}
var Entity_Type_value = map[string]int32{
	"TYPE_UNSPECIFIED":  0,
	"TYPE_PARTNER":      1,
	"TYPE_MUTI_GROUP":   2,
	"TYPE_SINGLE_GROUP": 3,
}

func (x Entity_Type) String() string {
	return proto.EnumName(Entity_Type_name, int32(x))
}
func (Entity_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{7, 0}
}

type Object struct {
	ObjectType           ObjectType `protobuf:"varint,1,opt,name=object_type,json=objectType,proto3,enum=aigc_common.ObjectType" json:"object_type,omitempty"`
	ObjectId             uint32     `protobuf:"varint,2,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *Object) Reset()         { *m = Object{} }
func (m *Object) String() string { return proto.CompactTextString(m) }
func (*Object) ProtoMessage()    {}
func (*Object) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{0}
}
func (m *Object) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Object.Unmarshal(m, b)
}
func (m *Object) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Object.Marshal(b, m, deterministic)
}
func (dst *Object) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Object.Merge(dst, src)
}
func (m *Object) XXX_Size() int {
	return xxx_messageInfo_Object.Size(m)
}
func (m *Object) XXX_DiscardUnknown() {
	xxx_messageInfo_Object.DiscardUnknown(m)
}

var xxx_messageInfo_Object proto.InternalMessageInfo

func (m *Object) GetObjectType() ObjectType {
	if m != nil {
		return m.ObjectType
	}
	return ObjectType_OBJECT_TYPE_UNSPECIFIED
}

func (m *Object) GetObjectId() uint32 {
	if m != nil {
		return m.ObjectId
	}
	return 0
}

// 点赞
type AttitudeRequest struct {
	AttitudeObject       *Object    `protobuf:"bytes,1,opt,name=attitude_object,json=attitudeObject,proto3" json:"attitude_object,omitempty"`
	Op                   AttitudeOp `protobuf:"varint,2,opt,name=op,proto3,enum=aigc_common.AttitudeOp" json:"op,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *AttitudeRequest) Reset()         { *m = AttitudeRequest{} }
func (m *AttitudeRequest) String() string { return proto.CompactTextString(m) }
func (*AttitudeRequest) ProtoMessage()    {}
func (*AttitudeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{1}
}
func (m *AttitudeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeRequest.Unmarshal(m, b)
}
func (m *AttitudeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeRequest.Marshal(b, m, deterministic)
}
func (dst *AttitudeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeRequest.Merge(dst, src)
}
func (m *AttitudeRequest) XXX_Size() int {
	return xxx_messageInfo_AttitudeRequest.Size(m)
}
func (m *AttitudeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeRequest proto.InternalMessageInfo

func (m *AttitudeRequest) GetAttitudeObject() *Object {
	if m != nil {
		return m.AttitudeObject
	}
	return nil
}

func (m *AttitudeRequest) GetOp() AttitudeOp {
	if m != nil {
		return m.Op
	}
	return AttitudeOp_ATTITUDE_OP_UNSPECIFIED
}

type AttitudeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttitudeResponse) Reset()         { *m = AttitudeResponse{} }
func (m *AttitudeResponse) String() string { return proto.CompactTextString(m) }
func (*AttitudeResponse) ProtoMessage()    {}
func (*AttitudeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{2}
}
func (m *AttitudeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeResponse.Unmarshal(m, b)
}
func (m *AttitudeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeResponse.Marshal(b, m, deterministic)
}
func (dst *AttitudeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeResponse.Merge(dst, src)
}
func (m *AttitudeResponse) XXX_Size() int {
	return xxx_messageInfo_AttitudeResponse.Size(m)
}
func (m *AttitudeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeResponse proto.InternalMessageInfo

type GetAttitudeCountRequest struct {
	Objects              []*Object `protobuf:"bytes,1,rep,name=objects,proto3" json:"objects,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetAttitudeCountRequest) Reset()         { *m = GetAttitudeCountRequest{} }
func (m *GetAttitudeCountRequest) String() string { return proto.CompactTextString(m) }
func (*GetAttitudeCountRequest) ProtoMessage()    {}
func (*GetAttitudeCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{3}
}
func (m *GetAttitudeCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAttitudeCountRequest.Unmarshal(m, b)
}
func (m *GetAttitudeCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAttitudeCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetAttitudeCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAttitudeCountRequest.Merge(dst, src)
}
func (m *GetAttitudeCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetAttitudeCountRequest.Size(m)
}
func (m *GetAttitudeCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAttitudeCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAttitudeCountRequest proto.InternalMessageInfo

func (m *GetAttitudeCountRequest) GetObjects() []*Object {
	if m != nil {
		return m.Objects
	}
	return nil
}

type GetAttitudeCountResponse struct {
	AttitudeCountInfos   []*GetAttitudeCountResponse_AttitudeCountInfo `protobuf:"bytes,1,rep,name=attitude_count_infos,json=attitudeCountInfos,proto3" json:"attitude_count_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *GetAttitudeCountResponse) Reset()         { *m = GetAttitudeCountResponse{} }
func (m *GetAttitudeCountResponse) String() string { return proto.CompactTextString(m) }
func (*GetAttitudeCountResponse) ProtoMessage()    {}
func (*GetAttitudeCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{4}
}
func (m *GetAttitudeCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAttitudeCountResponse.Unmarshal(m, b)
}
func (m *GetAttitudeCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAttitudeCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetAttitudeCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAttitudeCountResponse.Merge(dst, src)
}
func (m *GetAttitudeCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetAttitudeCountResponse.Size(m)
}
func (m *GetAttitudeCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAttitudeCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAttitudeCountResponse proto.InternalMessageInfo

func (m *GetAttitudeCountResponse) GetAttitudeCountInfos() []*GetAttitudeCountResponse_AttitudeCountInfo {
	if m != nil {
		return m.AttitudeCountInfos
	}
	return nil
}

type GetAttitudeCountResponse_AttitudeCountInfo struct {
	Object               *Object  `protobuf:"bytes,1,opt,name=object,proto3" json:"object,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAttitudeCountResponse_AttitudeCountInfo) Reset() {
	*m = GetAttitudeCountResponse_AttitudeCountInfo{}
}
func (m *GetAttitudeCountResponse_AttitudeCountInfo) String() string {
	return proto.CompactTextString(m)
}
func (*GetAttitudeCountResponse_AttitudeCountInfo) ProtoMessage() {}
func (*GetAttitudeCountResponse_AttitudeCountInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{4, 0}
}
func (m *GetAttitudeCountResponse_AttitudeCountInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAttitudeCountResponse_AttitudeCountInfo.Unmarshal(m, b)
}
func (m *GetAttitudeCountResponse_AttitudeCountInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAttitudeCountResponse_AttitudeCountInfo.Marshal(b, m, deterministic)
}
func (dst *GetAttitudeCountResponse_AttitudeCountInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAttitudeCountResponse_AttitudeCountInfo.Merge(dst, src)
}
func (m *GetAttitudeCountResponse_AttitudeCountInfo) XXX_Size() int {
	return xxx_messageInfo_GetAttitudeCountResponse_AttitudeCountInfo.Size(m)
}
func (m *GetAttitudeCountResponse_AttitudeCountInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAttitudeCountResponse_AttitudeCountInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetAttitudeCountResponse_AttitudeCountInfo proto.InternalMessageInfo

func (m *GetAttitudeCountResponse_AttitudeCountInfo) GetObject() *Object {
	if m != nil {
		return m.Object
	}
	return nil
}

func (m *GetAttitudeCountResponse_AttitudeCountInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type HadAttitudeRequest struct {
	AttitudeObject       *Object  `protobuf:"bytes,1,opt,name=attitude_object,json=attitudeObject,proto3" json:"attitude_object,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HadAttitudeRequest) Reset()         { *m = HadAttitudeRequest{} }
func (m *HadAttitudeRequest) String() string { return proto.CompactTextString(m) }
func (*HadAttitudeRequest) ProtoMessage()    {}
func (*HadAttitudeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{5}
}
func (m *HadAttitudeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HadAttitudeRequest.Unmarshal(m, b)
}
func (m *HadAttitudeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HadAttitudeRequest.Marshal(b, m, deterministic)
}
func (dst *HadAttitudeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HadAttitudeRequest.Merge(dst, src)
}
func (m *HadAttitudeRequest) XXX_Size() int {
	return xxx_messageInfo_HadAttitudeRequest.Size(m)
}
func (m *HadAttitudeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HadAttitudeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HadAttitudeRequest proto.InternalMessageInfo

func (m *HadAttitudeRequest) GetAttitudeObject() *Object {
	if m != nil {
		return m.AttitudeObject
	}
	return nil
}

type HadAttitudeResponse struct {
	HadAttitude          bool     `protobuf:"varint,1,opt,name=had_attitude,json=hadAttitude,proto3" json:"had_attitude,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HadAttitudeResponse) Reset()         { *m = HadAttitudeResponse{} }
func (m *HadAttitudeResponse) String() string { return proto.CompactTextString(m) }
func (*HadAttitudeResponse) ProtoMessage()    {}
func (*HadAttitudeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{6}
}
func (m *HadAttitudeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HadAttitudeResponse.Unmarshal(m, b)
}
func (m *HadAttitudeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HadAttitudeResponse.Marshal(b, m, deterministic)
}
func (dst *HadAttitudeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HadAttitudeResponse.Merge(dst, src)
}
func (m *HadAttitudeResponse) XXX_Size() int {
	return xxx_messageInfo_HadAttitudeResponse.Size(m)
}
func (m *HadAttitudeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HadAttitudeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HadAttitudeResponse proto.InternalMessageInfo

func (m *HadAttitudeResponse) GetHadAttitude() bool {
	if m != nil {
		return m.HadAttitude
	}
	return false
}

type Entity struct {
	// e.g:partner_id
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 亲密度主体类型
	Type                 Entity_Type `protobuf:"varint,2,opt,name=type,proto3,enum=aigc_common.Entity_Type" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Entity) Reset()         { *m = Entity{} }
func (m *Entity) String() string { return proto.CompactTextString(m) }
func (*Entity) ProtoMessage()    {}
func (*Entity) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{7}
}
func (m *Entity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Entity.Unmarshal(m, b)
}
func (m *Entity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Entity.Marshal(b, m, deterministic)
}
func (dst *Entity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Entity.Merge(dst, src)
}
func (m *Entity) XXX_Size() int {
	return xxx_messageInfo_Entity.Size(m)
}
func (m *Entity) XXX_DiscardUnknown() {
	xxx_messageInfo_Entity.DiscardUnknown(m)
}

var xxx_messageInfo_Entity proto.InternalMessageInfo

func (m *Entity) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Entity) GetType() Entity_Type {
	if m != nil {
		return m.Type
	}
	return Entity_TYPE_UNSPECIFIED
}

type GetSentenceCountMapRequest struct {
	// 句子类型
	Type                 []uint32 `protobuf:"varint,1,rep,packed,name=type,proto3" json:"type,omitempty"`
	Entity               *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSentenceCountMapRequest) Reset()         { *m = GetSentenceCountMapRequest{} }
func (m *GetSentenceCountMapRequest) String() string { return proto.CompactTextString(m) }
func (*GetSentenceCountMapRequest) ProtoMessage()    {}
func (*GetSentenceCountMapRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{8}
}
func (m *GetSentenceCountMapRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSentenceCountMapRequest.Unmarshal(m, b)
}
func (m *GetSentenceCountMapRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSentenceCountMapRequest.Marshal(b, m, deterministic)
}
func (dst *GetSentenceCountMapRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSentenceCountMapRequest.Merge(dst, src)
}
func (m *GetSentenceCountMapRequest) XXX_Size() int {
	return xxx_messageInfo_GetSentenceCountMapRequest.Size(m)
}
func (m *GetSentenceCountMapRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSentenceCountMapRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSentenceCountMapRequest proto.InternalMessageInfo

func (m *GetSentenceCountMapRequest) GetType() []uint32 {
	if m != nil {
		return m.Type
	}
	return nil
}

func (m *GetSentenceCountMapRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *GetSentenceCountMapRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSentenceCountMapResponse struct {
	CountMap             map[uint32]uint32 `protobuf:"bytes,1,rep,name=count_map,json=countMap,proto3" json:"count_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetSentenceCountMapResponse) Reset()         { *m = GetSentenceCountMapResponse{} }
func (m *GetSentenceCountMapResponse) String() string { return proto.CompactTextString(m) }
func (*GetSentenceCountMapResponse) ProtoMessage()    {}
func (*GetSentenceCountMapResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{9}
}
func (m *GetSentenceCountMapResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSentenceCountMapResponse.Unmarshal(m, b)
}
func (m *GetSentenceCountMapResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSentenceCountMapResponse.Marshal(b, m, deterministic)
}
func (dst *GetSentenceCountMapResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSentenceCountMapResponse.Merge(dst, src)
}
func (m *GetSentenceCountMapResponse) XXX_Size() int {
	return xxx_messageInfo_GetSentenceCountMapResponse.Size(m)
}
func (m *GetSentenceCountMapResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSentenceCountMapResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSentenceCountMapResponse proto.InternalMessageInfo

func (m *GetSentenceCountMapResponse) GetCountMap() map[uint32]uint32 {
	if m != nil {
		return m.CountMap
	}
	return nil
}

type AddSentenceCountRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Entity               *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSentenceCountRequest) Reset()         { *m = AddSentenceCountRequest{} }
func (m *AddSentenceCountRequest) String() string { return proto.CompactTextString(m) }
func (*AddSentenceCountRequest) ProtoMessage()    {}
func (*AddSentenceCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{10}
}
func (m *AddSentenceCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSentenceCountRequest.Unmarshal(m, b)
}
func (m *AddSentenceCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSentenceCountRequest.Marshal(b, m, deterministic)
}
func (dst *AddSentenceCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSentenceCountRequest.Merge(dst, src)
}
func (m *AddSentenceCountRequest) XXX_Size() int {
	return xxx_messageInfo_AddSentenceCountRequest.Size(m)
}
func (m *AddSentenceCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSentenceCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddSentenceCountRequest proto.InternalMessageInfo

func (m *AddSentenceCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddSentenceCountRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *AddSentenceCountRequest) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type AddSentenceCountResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSentenceCountResponse) Reset()         { *m = AddSentenceCountResponse{} }
func (m *AddSentenceCountResponse) String() string { return proto.CompactTextString(m) }
func (*AddSentenceCountResponse) ProtoMessage()    {}
func (*AddSentenceCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{11}
}
func (m *AddSentenceCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSentenceCountResponse.Unmarshal(m, b)
}
func (m *AddSentenceCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSentenceCountResponse.Marshal(b, m, deterministic)
}
func (dst *AddSentenceCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSentenceCountResponse.Merge(dst, src)
}
func (m *AddSentenceCountResponse) XXX_Size() int {
	return xxx_messageInfo_AddSentenceCountResponse.Size(m)
}
func (m *AddSentenceCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSentenceCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddSentenceCountResponse proto.InternalMessageInfo

type GetUserSentenceCountReq struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SentenceType         uint32      `protobuf:"varint,2,opt,name=sentence_type,json=sentenceType,proto3" json:"sentence_type,omitempty"`
	EntityType           Entity_Type `protobuf:"varint,3,opt,name=entity_type,json=entityType,proto3,enum=aigc_common.Entity_Type" json:"entity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetUserSentenceCountReq) Reset()         { *m = GetUserSentenceCountReq{} }
func (m *GetUserSentenceCountReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSentenceCountReq) ProtoMessage()    {}
func (*GetUserSentenceCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{12}
}
func (m *GetUserSentenceCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSentenceCountReq.Unmarshal(m, b)
}
func (m *GetUserSentenceCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSentenceCountReq.Marshal(b, m, deterministic)
}
func (dst *GetUserSentenceCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSentenceCountReq.Merge(dst, src)
}
func (m *GetUserSentenceCountReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSentenceCountReq.Size(m)
}
func (m *GetUserSentenceCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSentenceCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSentenceCountReq proto.InternalMessageInfo

func (m *GetUserSentenceCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserSentenceCountReq) GetSentenceType() uint32 {
	if m != nil {
		return m.SentenceType
	}
	return 0
}

func (m *GetUserSentenceCountReq) GetEntityType() Entity_Type {
	if m != nil {
		return m.EntityType
	}
	return Entity_TYPE_UNSPECIFIED
}

type GetUserSentenceCountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSentenceCountResp) Reset()         { *m = GetUserSentenceCountResp{} }
func (m *GetUserSentenceCountResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSentenceCountResp) ProtoMessage()    {}
func (*GetUserSentenceCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{13}
}
func (m *GetUserSentenceCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSentenceCountResp.Unmarshal(m, b)
}
func (m *GetUserSentenceCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSentenceCountResp.Marshal(b, m, deterministic)
}
func (dst *GetUserSentenceCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSentenceCountResp.Merge(dst, src)
}
func (m *GetUserSentenceCountResp) XXX_Size() int {
	return xxx_messageInfo_GetUserSentenceCountResp.Size(m)
}
func (m *GetUserSentenceCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSentenceCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSentenceCountResp proto.InternalMessageInfo

func (m *GetUserSentenceCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type AddBussSentenceCountRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Entity               *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	BusinessType         uint32   `protobuf:"varint,4,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBussSentenceCountRequest) Reset()         { *m = AddBussSentenceCountRequest{} }
func (m *AddBussSentenceCountRequest) String() string { return proto.CompactTextString(m) }
func (*AddBussSentenceCountRequest) ProtoMessage()    {}
func (*AddBussSentenceCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{14}
}
func (m *AddBussSentenceCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBussSentenceCountRequest.Unmarshal(m, b)
}
func (m *AddBussSentenceCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBussSentenceCountRequest.Marshal(b, m, deterministic)
}
func (dst *AddBussSentenceCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBussSentenceCountRequest.Merge(dst, src)
}
func (m *AddBussSentenceCountRequest) XXX_Size() int {
	return xxx_messageInfo_AddBussSentenceCountRequest.Size(m)
}
func (m *AddBussSentenceCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBussSentenceCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddBussSentenceCountRequest proto.InternalMessageInfo

func (m *AddBussSentenceCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddBussSentenceCountRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *AddBussSentenceCountRequest) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AddBussSentenceCountRequest) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type AddBussSentenceCountResponse struct {
	CurNum               uint32   `protobuf:"varint,1,opt,name=cur_num,json=curNum,proto3" json:"cur_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBussSentenceCountResponse) Reset()         { *m = AddBussSentenceCountResponse{} }
func (m *AddBussSentenceCountResponse) String() string { return proto.CompactTextString(m) }
func (*AddBussSentenceCountResponse) ProtoMessage()    {}
func (*AddBussSentenceCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{15}
}
func (m *AddBussSentenceCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBussSentenceCountResponse.Unmarshal(m, b)
}
func (m *AddBussSentenceCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBussSentenceCountResponse.Marshal(b, m, deterministic)
}
func (dst *AddBussSentenceCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBussSentenceCountResponse.Merge(dst, src)
}
func (m *AddBussSentenceCountResponse) XXX_Size() int {
	return xxx_messageInfo_AddBussSentenceCountResponse.Size(m)
}
func (m *AddBussSentenceCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBussSentenceCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddBussSentenceCountResponse proto.InternalMessageInfo

func (m *AddBussSentenceCountResponse) GetCurNum() uint32 {
	if m != nil {
		return m.CurNum
	}
	return 0
}

type DecrBussSentenceCountRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Entity               *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	BusinessType         uint32   `protobuf:"varint,4,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecrBussSentenceCountRequest) Reset()         { *m = DecrBussSentenceCountRequest{} }
func (m *DecrBussSentenceCountRequest) String() string { return proto.CompactTextString(m) }
func (*DecrBussSentenceCountRequest) ProtoMessage()    {}
func (*DecrBussSentenceCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{16}
}
func (m *DecrBussSentenceCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecrBussSentenceCountRequest.Unmarshal(m, b)
}
func (m *DecrBussSentenceCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecrBussSentenceCountRequest.Marshal(b, m, deterministic)
}
func (dst *DecrBussSentenceCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecrBussSentenceCountRequest.Merge(dst, src)
}
func (m *DecrBussSentenceCountRequest) XXX_Size() int {
	return xxx_messageInfo_DecrBussSentenceCountRequest.Size(m)
}
func (m *DecrBussSentenceCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DecrBussSentenceCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DecrBussSentenceCountRequest proto.InternalMessageInfo

func (m *DecrBussSentenceCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DecrBussSentenceCountRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *DecrBussSentenceCountRequest) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *DecrBussSentenceCountRequest) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type DecrBussSentenceCountResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecrBussSentenceCountResponse) Reset()         { *m = DecrBussSentenceCountResponse{} }
func (m *DecrBussSentenceCountResponse) String() string { return proto.CompactTextString(m) }
func (*DecrBussSentenceCountResponse) ProtoMessage()    {}
func (*DecrBussSentenceCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{17}
}
func (m *DecrBussSentenceCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecrBussSentenceCountResponse.Unmarshal(m, b)
}
func (m *DecrBussSentenceCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecrBussSentenceCountResponse.Marshal(b, m, deterministic)
}
func (dst *DecrBussSentenceCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecrBussSentenceCountResponse.Merge(dst, src)
}
func (m *DecrBussSentenceCountResponse) XXX_Size() int {
	return xxx_messageInfo_DecrBussSentenceCountResponse.Size(m)
}
func (m *DecrBussSentenceCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DecrBussSentenceCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DecrBussSentenceCountResponse proto.InternalMessageInfo

type BatAddBussSentenceCountRequest struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	Entity               *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	BusinessType         uint32   `protobuf:"varint,4,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatAddBussSentenceCountRequest) Reset()         { *m = BatAddBussSentenceCountRequest{} }
func (m *BatAddBussSentenceCountRequest) String() string { return proto.CompactTextString(m) }
func (*BatAddBussSentenceCountRequest) ProtoMessage()    {}
func (*BatAddBussSentenceCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{18}
}
func (m *BatAddBussSentenceCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatAddBussSentenceCountRequest.Unmarshal(m, b)
}
func (m *BatAddBussSentenceCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatAddBussSentenceCountRequest.Marshal(b, m, deterministic)
}
func (dst *BatAddBussSentenceCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatAddBussSentenceCountRequest.Merge(dst, src)
}
func (m *BatAddBussSentenceCountRequest) XXX_Size() int {
	return xxx_messageInfo_BatAddBussSentenceCountRequest.Size(m)
}
func (m *BatAddBussSentenceCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatAddBussSentenceCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatAddBussSentenceCountRequest proto.InternalMessageInfo

func (m *BatAddBussSentenceCountRequest) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *BatAddBussSentenceCountRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *BatAddBussSentenceCountRequest) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BatAddBussSentenceCountRequest) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type BatAddBussSentenceCountResponse struct {
	CurNumMap            map[uint32]uint32 `protobuf:"bytes,1,rep,name=cur_num_map,json=curNumMap,proto3" json:"cur_num_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatAddBussSentenceCountResponse) Reset()         { *m = BatAddBussSentenceCountResponse{} }
func (m *BatAddBussSentenceCountResponse) String() string { return proto.CompactTextString(m) }
func (*BatAddBussSentenceCountResponse) ProtoMessage()    {}
func (*BatAddBussSentenceCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{19}
}
func (m *BatAddBussSentenceCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatAddBussSentenceCountResponse.Unmarshal(m, b)
}
func (m *BatAddBussSentenceCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatAddBussSentenceCountResponse.Marshal(b, m, deterministic)
}
func (dst *BatAddBussSentenceCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatAddBussSentenceCountResponse.Merge(dst, src)
}
func (m *BatAddBussSentenceCountResponse) XXX_Size() int {
	return xxx_messageInfo_BatAddBussSentenceCountResponse.Size(m)
}
func (m *BatAddBussSentenceCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatAddBussSentenceCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatAddBussSentenceCountResponse proto.InternalMessageInfo

func (m *BatAddBussSentenceCountResponse) GetCurNumMap() map[uint32]uint32 {
	if m != nil {
		return m.CurNumMap
	}
	return nil
}

// 添加额外句数（中台用）
type AddExtraCountRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ExtraCount           uint32   `protobuf:"varint,2,opt,name=extra_count,json=extraCount,proto3" json:"extra_count,omitempty"`
	ExpireTime           int64    `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	BusinessType         uint32   `protobuf:"varint,4,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	RewardType           uint32   `protobuf:"varint,5,opt,name=reward_type,json=rewardType,proto3" json:"reward_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddExtraCountRequest) Reset()         { *m = AddExtraCountRequest{} }
func (m *AddExtraCountRequest) String() string { return proto.CompactTextString(m) }
func (*AddExtraCountRequest) ProtoMessage()    {}
func (*AddExtraCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{20}
}
func (m *AddExtraCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddExtraCountRequest.Unmarshal(m, b)
}
func (m *AddExtraCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddExtraCountRequest.Marshal(b, m, deterministic)
}
func (dst *AddExtraCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddExtraCountRequest.Merge(dst, src)
}
func (m *AddExtraCountRequest) XXX_Size() int {
	return xxx_messageInfo_AddExtraCountRequest.Size(m)
}
func (m *AddExtraCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddExtraCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddExtraCountRequest proto.InternalMessageInfo

func (m *AddExtraCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddExtraCountRequest) GetExtraCount() uint32 {
	if m != nil {
		return m.ExtraCount
	}
	return 0
}

func (m *AddExtraCountRequest) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *AddExtraCountRequest) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *AddExtraCountRequest) GetRewardType() uint32 {
	if m != nil {
		return m.RewardType
	}
	return 0
}

type AddExtraCountResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddExtraCountResponse) Reset()         { *m = AddExtraCountResponse{} }
func (m *AddExtraCountResponse) String() string { return proto.CompactTextString(m) }
func (*AddExtraCountResponse) ProtoMessage()    {}
func (*AddExtraCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{21}
}
func (m *AddExtraCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddExtraCountResponse.Unmarshal(m, b)
}
func (m *AddExtraCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddExtraCountResponse.Marshal(b, m, deterministic)
}
func (dst *AddExtraCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddExtraCountResponse.Merge(dst, src)
}
func (m *AddExtraCountResponse) XXX_Size() int {
	return xxx_messageInfo_AddExtraCountResponse.Size(m)
}
func (m *AddExtraCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddExtraCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddExtraCountResponse proto.InternalMessageInfo

// 获取用户使用的句数信息
type GetUsedSentenceCountReq struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BusinessType         uint32      `protobuf:"varint,2,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	CountTypes           []CountType `protobuf:"varint,3,rep,packed,name=count_types,json=countTypes,proto3,enum=aigc_common.CountType" json:"count_types,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetUsedSentenceCountReq) Reset()         { *m = GetUsedSentenceCountReq{} }
func (m *GetUsedSentenceCountReq) String() string { return proto.CompactTextString(m) }
func (*GetUsedSentenceCountReq) ProtoMessage()    {}
func (*GetUsedSentenceCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{22}
}
func (m *GetUsedSentenceCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUsedSentenceCountReq.Unmarshal(m, b)
}
func (m *GetUsedSentenceCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUsedSentenceCountReq.Marshal(b, m, deterministic)
}
func (dst *GetUsedSentenceCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUsedSentenceCountReq.Merge(dst, src)
}
func (m *GetUsedSentenceCountReq) XXX_Size() int {
	return xxx_messageInfo_GetUsedSentenceCountReq.Size(m)
}
func (m *GetUsedSentenceCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUsedSentenceCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUsedSentenceCountReq proto.InternalMessageInfo

func (m *GetUsedSentenceCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUsedSentenceCountReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *GetUsedSentenceCountReq) GetCountTypes() []CountType {
	if m != nil {
		return m.CountTypes
	}
	return nil
}

type GetUsedSentenceCountResp struct {
	TodayTotalCount      uint32   `protobuf:"varint,1,opt,name=today_total_count,json=todayTotalCount,proto3" json:"today_total_count,omitempty"`
	TotalCount           uint32   `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUsedSentenceCountResp) Reset()         { *m = GetUsedSentenceCountResp{} }
func (m *GetUsedSentenceCountResp) String() string { return proto.CompactTextString(m) }
func (*GetUsedSentenceCountResp) ProtoMessage()    {}
func (*GetUsedSentenceCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{23}
}
func (m *GetUsedSentenceCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUsedSentenceCountResp.Unmarshal(m, b)
}
func (m *GetUsedSentenceCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUsedSentenceCountResp.Marshal(b, m, deterministic)
}
func (dst *GetUsedSentenceCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUsedSentenceCountResp.Merge(dst, src)
}
func (m *GetUsedSentenceCountResp) XXX_Size() int {
	return xxx_messageInfo_GetUsedSentenceCountResp.Size(m)
}
func (m *GetUsedSentenceCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUsedSentenceCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUsedSentenceCountResp proto.InternalMessageInfo

func (m *GetUsedSentenceCountResp) GetTodayTotalCount() uint32 {
	if m != nil {
		return m.TodayTotalCount
	}
	return 0
}

func (m *GetUsedSentenceCountResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

// 消耗句数
type ConsumeSentenceCountRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Entity               *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	SentenceType         uint32   `protobuf:"varint,3,opt,name=sentence_type,json=sentenceType,proto3" json:"sentence_type,omitempty"`
	BusinessType         uint32   `protobuf:"varint,4,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	RoleType             uint32   `protobuf:"varint,5,opt,name=role_type,json=roleType,proto3" json:"role_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeSentenceCountRequest) Reset()         { *m = ConsumeSentenceCountRequest{} }
func (m *ConsumeSentenceCountRequest) String() string { return proto.CompactTextString(m) }
func (*ConsumeSentenceCountRequest) ProtoMessage()    {}
func (*ConsumeSentenceCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{24}
}
func (m *ConsumeSentenceCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeSentenceCountRequest.Unmarshal(m, b)
}
func (m *ConsumeSentenceCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeSentenceCountRequest.Marshal(b, m, deterministic)
}
func (dst *ConsumeSentenceCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeSentenceCountRequest.Merge(dst, src)
}
func (m *ConsumeSentenceCountRequest) XXX_Size() int {
	return xxx_messageInfo_ConsumeSentenceCountRequest.Size(m)
}
func (m *ConsumeSentenceCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeSentenceCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeSentenceCountRequest proto.InternalMessageInfo

func (m *ConsumeSentenceCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConsumeSentenceCountRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *ConsumeSentenceCountRequest) GetSentenceType() uint32 {
	if m != nil {
		return m.SentenceType
	}
	return 0
}

func (m *ConsumeSentenceCountRequest) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *ConsumeSentenceCountRequest) GetRoleType() uint32 {
	if m != nil {
		return m.RoleType
	}
	return 0
}

type ConsumeSentenceCountResponse struct {
	CurNum               uint32   `protobuf:"varint,1,opt,name=cur_num,json=curNum,proto3" json:"cur_num,omitempty"`
	Success              bool     `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeSentenceCountResponse) Reset()         { *m = ConsumeSentenceCountResponse{} }
func (m *ConsumeSentenceCountResponse) String() string { return proto.CompactTextString(m) }
func (*ConsumeSentenceCountResponse) ProtoMessage()    {}
func (*ConsumeSentenceCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{25}
}
func (m *ConsumeSentenceCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeSentenceCountResponse.Unmarshal(m, b)
}
func (m *ConsumeSentenceCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeSentenceCountResponse.Marshal(b, m, deterministic)
}
func (dst *ConsumeSentenceCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeSentenceCountResponse.Merge(dst, src)
}
func (m *ConsumeSentenceCountResponse) XXX_Size() int {
	return xxx_messageInfo_ConsumeSentenceCountResponse.Size(m)
}
func (m *ConsumeSentenceCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeSentenceCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeSentenceCountResponse proto.InternalMessageInfo

func (m *ConsumeSentenceCountResponse) GetCurNum() uint32 {
	if m != nil {
		return m.CurNum
	}
	return 0
}

func (m *ConsumeSentenceCountResponse) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

type BatGetCurSentenceCountRequest struct {
	Uid                     uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Entity                  *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	SentenceType            []uint32 `protobuf:"varint,3,rep,packed,name=sentence_type,json=sentenceType,proto3" json:"sentence_type,omitempty"`
	BusinessType            uint32   `protobuf:"varint,4,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	RoleType                uint32   `protobuf:"varint,5,opt,name=role_type,json=roleType,proto3" json:"role_type,omitempty"`
	NeedAvailableExtraCount bool     `protobuf:"varint,6,opt,name=need_available_extra_count,json=needAvailableExtraCount,proto3" json:"need_available_extra_count,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *BatGetCurSentenceCountRequest) Reset()         { *m = BatGetCurSentenceCountRequest{} }
func (m *BatGetCurSentenceCountRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetCurSentenceCountRequest) ProtoMessage()    {}
func (*BatGetCurSentenceCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{26}
}
func (m *BatGetCurSentenceCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetCurSentenceCountRequest.Unmarshal(m, b)
}
func (m *BatGetCurSentenceCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetCurSentenceCountRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetCurSentenceCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetCurSentenceCountRequest.Merge(dst, src)
}
func (m *BatGetCurSentenceCountRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetCurSentenceCountRequest.Size(m)
}
func (m *BatGetCurSentenceCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetCurSentenceCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetCurSentenceCountRequest proto.InternalMessageInfo

func (m *BatGetCurSentenceCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatGetCurSentenceCountRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *BatGetCurSentenceCountRequest) GetSentenceType() []uint32 {
	if m != nil {
		return m.SentenceType
	}
	return nil
}

func (m *BatGetCurSentenceCountRequest) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *BatGetCurSentenceCountRequest) GetRoleType() uint32 {
	if m != nil {
		return m.RoleType
	}
	return 0
}

func (m *BatGetCurSentenceCountRequest) GetNeedAvailableExtraCount() bool {
	if m != nil {
		return m.NeedAvailableExtraCount
	}
	return false
}

type BatGetCurSentenceCountResponse struct {
	// cur_num_map返回今日已使用数量
	CurNumMap            map[uint32]uint32 `protobuf:"bytes,1,rep,name=cur_num_map,json=curNumMap,proto3" json:"cur_num_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	AvailableExtraNum    uint32            `protobuf:"varint,2,opt,name=available_extra_num,json=availableExtraNum,proto3" json:"available_extra_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetCurSentenceCountResponse) Reset()         { *m = BatGetCurSentenceCountResponse{} }
func (m *BatGetCurSentenceCountResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetCurSentenceCountResponse) ProtoMessage()    {}
func (*BatGetCurSentenceCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{27}
}
func (m *BatGetCurSentenceCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetCurSentenceCountResponse.Unmarshal(m, b)
}
func (m *BatGetCurSentenceCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetCurSentenceCountResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetCurSentenceCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetCurSentenceCountResponse.Merge(dst, src)
}
func (m *BatGetCurSentenceCountResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetCurSentenceCountResponse.Size(m)
}
func (m *BatGetCurSentenceCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetCurSentenceCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetCurSentenceCountResponse proto.InternalMessageInfo

func (m *BatGetCurSentenceCountResponse) GetCurNumMap() map[uint32]uint32 {
	if m != nil {
		return m.CurNumMap
	}
	return nil
}

func (m *BatGetCurSentenceCountResponse) GetAvailableExtraNum() uint32 {
	if m != nil {
		return m.AvailableExtraNum
	}
	return 0
}

type GetExtraCountByRewardTypesRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RewardTypes          []uint32 `protobuf:"varint,2,rep,packed,name=reward_types,json=rewardTypes,proto3" json:"reward_types,omitempty"`
	BusinessType         uint32   `protobuf:"varint,3,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtraCountByRewardTypesRequest) Reset()         { *m = GetExtraCountByRewardTypesRequest{} }
func (m *GetExtraCountByRewardTypesRequest) String() string { return proto.CompactTextString(m) }
func (*GetExtraCountByRewardTypesRequest) ProtoMessage()    {}
func (*GetExtraCountByRewardTypesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{28}
}
func (m *GetExtraCountByRewardTypesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraCountByRewardTypesRequest.Unmarshal(m, b)
}
func (m *GetExtraCountByRewardTypesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraCountByRewardTypesRequest.Marshal(b, m, deterministic)
}
func (dst *GetExtraCountByRewardTypesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraCountByRewardTypesRequest.Merge(dst, src)
}
func (m *GetExtraCountByRewardTypesRequest) XXX_Size() int {
	return xxx_messageInfo_GetExtraCountByRewardTypesRequest.Size(m)
}
func (m *GetExtraCountByRewardTypesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraCountByRewardTypesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraCountByRewardTypesRequest proto.InternalMessageInfo

func (m *GetExtraCountByRewardTypesRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetExtraCountByRewardTypesRequest) GetRewardTypes() []uint32 {
	if m != nil {
		return m.RewardTypes
	}
	return nil
}

func (m *GetExtraCountByRewardTypesRequest) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type GetExtraCountByRewardTypesResponse struct {
	ExtraCountMap        map[uint32]uint32 `protobuf:"bytes,1,rep,name=extra_count_map,json=extraCountMap,proto3" json:"extra_count_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetExtraCountByRewardTypesResponse) Reset()         { *m = GetExtraCountByRewardTypesResponse{} }
func (m *GetExtraCountByRewardTypesResponse) String() string { return proto.CompactTextString(m) }
func (*GetExtraCountByRewardTypesResponse) ProtoMessage()    {}
func (*GetExtraCountByRewardTypesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{29}
}
func (m *GetExtraCountByRewardTypesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraCountByRewardTypesResponse.Unmarshal(m, b)
}
func (m *GetExtraCountByRewardTypesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraCountByRewardTypesResponse.Marshal(b, m, deterministic)
}
func (dst *GetExtraCountByRewardTypesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraCountByRewardTypesResponse.Merge(dst, src)
}
func (m *GetExtraCountByRewardTypesResponse) XXX_Size() int {
	return xxx_messageInfo_GetExtraCountByRewardTypesResponse.Size(m)
}
func (m *GetExtraCountByRewardTypesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraCountByRewardTypesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraCountByRewardTypesResponse proto.InternalMessageInfo

func (m *GetExtraCountByRewardTypesResponse) GetExtraCountMap() map[uint32]uint32 {
	if m != nil {
		return m.ExtraCountMap
	}
	return nil
}

type AddToDelayQueueRequest struct {
	DelayTime            int64    `protobuf:"varint,1,opt,name=delay_time,json=delayTime,proto3" json:"delay_time,omitempty"`
	Data                 []byte   `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	PushTo               PushTo   `protobuf:"varint,3,opt,name=push_to,json=pushTo,proto3,enum=aigc_common.PushTo" json:"push_to,omitempty"`
	ReceiveMsg           []byte   `protobuf:"bytes,4,opt,name=receive_msg,json=receiveMsg,proto3" json:"receive_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddToDelayQueueRequest) Reset()         { *m = AddToDelayQueueRequest{} }
func (m *AddToDelayQueueRequest) String() string { return proto.CompactTextString(m) }
func (*AddToDelayQueueRequest) ProtoMessage()    {}
func (*AddToDelayQueueRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{30}
}
func (m *AddToDelayQueueRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddToDelayQueueRequest.Unmarshal(m, b)
}
func (m *AddToDelayQueueRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddToDelayQueueRequest.Marshal(b, m, deterministic)
}
func (dst *AddToDelayQueueRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddToDelayQueueRequest.Merge(dst, src)
}
func (m *AddToDelayQueueRequest) XXX_Size() int {
	return xxx_messageInfo_AddToDelayQueueRequest.Size(m)
}
func (m *AddToDelayQueueRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddToDelayQueueRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddToDelayQueueRequest proto.InternalMessageInfo

func (m *AddToDelayQueueRequest) GetDelayTime() int64 {
	if m != nil {
		return m.DelayTime
	}
	return 0
}

func (m *AddToDelayQueueRequest) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *AddToDelayQueueRequest) GetPushTo() PushTo {
	if m != nil {
		return m.PushTo
	}
	return PushTo_PUSH_TO_UNSPECIFIED
}

func (m *AddToDelayQueueRequest) GetReceiveMsg() []byte {
	if m != nil {
		return m.ReceiveMsg
	}
	return nil
}

type AddToDelayQueueResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddToDelayQueueResponse) Reset()         { *m = AddToDelayQueueResponse{} }
func (m *AddToDelayQueueResponse) String() string { return proto.CompactTextString(m) }
func (*AddToDelayQueueResponse) ProtoMessage()    {}
func (*AddToDelayQueueResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_common_5fb2984104eccf81, []int{31}
}
func (m *AddToDelayQueueResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddToDelayQueueResponse.Unmarshal(m, b)
}
func (m *AddToDelayQueueResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddToDelayQueueResponse.Marshal(b, m, deterministic)
}
func (dst *AddToDelayQueueResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddToDelayQueueResponse.Merge(dst, src)
}
func (m *AddToDelayQueueResponse) XXX_Size() int {
	return xxx_messageInfo_AddToDelayQueueResponse.Size(m)
}
func (m *AddToDelayQueueResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddToDelayQueueResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddToDelayQueueResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*Object)(nil), "aigc_common.Object")
	proto.RegisterType((*AttitudeRequest)(nil), "aigc_common.AttitudeRequest")
	proto.RegisterType((*AttitudeResponse)(nil), "aigc_common.AttitudeResponse")
	proto.RegisterType((*GetAttitudeCountRequest)(nil), "aigc_common.GetAttitudeCountRequest")
	proto.RegisterType((*GetAttitudeCountResponse)(nil), "aigc_common.GetAttitudeCountResponse")
	proto.RegisterType((*GetAttitudeCountResponse_AttitudeCountInfo)(nil), "aigc_common.GetAttitudeCountResponse.AttitudeCountInfo")
	proto.RegisterType((*HadAttitudeRequest)(nil), "aigc_common.HadAttitudeRequest")
	proto.RegisterType((*HadAttitudeResponse)(nil), "aigc_common.HadAttitudeResponse")
	proto.RegisterType((*Entity)(nil), "aigc_common.Entity")
	proto.RegisterType((*GetSentenceCountMapRequest)(nil), "aigc_common.GetSentenceCountMapRequest")
	proto.RegisterType((*GetSentenceCountMapResponse)(nil), "aigc_common.GetSentenceCountMapResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "aigc_common.GetSentenceCountMapResponse.CountMapEntry")
	proto.RegisterType((*AddSentenceCountRequest)(nil), "aigc_common.AddSentenceCountRequest")
	proto.RegisterType((*AddSentenceCountResponse)(nil), "aigc_common.AddSentenceCountResponse")
	proto.RegisterType((*GetUserSentenceCountReq)(nil), "aigc_common.GetUserSentenceCountReq")
	proto.RegisterType((*GetUserSentenceCountResp)(nil), "aigc_common.GetUserSentenceCountResp")
	proto.RegisterType((*AddBussSentenceCountRequest)(nil), "aigc_common.AddBussSentenceCountRequest")
	proto.RegisterType((*AddBussSentenceCountResponse)(nil), "aigc_common.AddBussSentenceCountResponse")
	proto.RegisterType((*DecrBussSentenceCountRequest)(nil), "aigc_common.DecrBussSentenceCountRequest")
	proto.RegisterType((*DecrBussSentenceCountResponse)(nil), "aigc_common.DecrBussSentenceCountResponse")
	proto.RegisterType((*BatAddBussSentenceCountRequest)(nil), "aigc_common.BatAddBussSentenceCountRequest")
	proto.RegisterType((*BatAddBussSentenceCountResponse)(nil), "aigc_common.BatAddBussSentenceCountResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "aigc_common.BatAddBussSentenceCountResponse.CurNumMapEntry")
	proto.RegisterType((*AddExtraCountRequest)(nil), "aigc_common.AddExtraCountRequest")
	proto.RegisterType((*AddExtraCountResponse)(nil), "aigc_common.AddExtraCountResponse")
	proto.RegisterType((*GetUsedSentenceCountReq)(nil), "aigc_common.GetUsedSentenceCountReq")
	proto.RegisterType((*GetUsedSentenceCountResp)(nil), "aigc_common.GetUsedSentenceCountResp")
	proto.RegisterType((*ConsumeSentenceCountRequest)(nil), "aigc_common.ConsumeSentenceCountRequest")
	proto.RegisterType((*ConsumeSentenceCountResponse)(nil), "aigc_common.ConsumeSentenceCountResponse")
	proto.RegisterType((*BatGetCurSentenceCountRequest)(nil), "aigc_common.BatGetCurSentenceCountRequest")
	proto.RegisterType((*BatGetCurSentenceCountResponse)(nil), "aigc_common.BatGetCurSentenceCountResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "aigc_common.BatGetCurSentenceCountResponse.CurNumMapEntry")
	proto.RegisterType((*GetExtraCountByRewardTypesRequest)(nil), "aigc_common.GetExtraCountByRewardTypesRequest")
	proto.RegisterType((*GetExtraCountByRewardTypesResponse)(nil), "aigc_common.GetExtraCountByRewardTypesResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "aigc_common.GetExtraCountByRewardTypesResponse.ExtraCountMapEntry")
	proto.RegisterType((*AddToDelayQueueRequest)(nil), "aigc_common.AddToDelayQueueRequest")
	proto.RegisterType((*AddToDelayQueueResponse)(nil), "aigc_common.AddToDelayQueueResponse")
	proto.RegisterEnum("aigc_common.ObjectType", ObjectType_name, ObjectType_value)
	proto.RegisterEnum("aigc_common.AttitudeOp", AttitudeOp_name, AttitudeOp_value)
	proto.RegisterEnum("aigc_common.RewardType", RewardType_name, RewardType_value)
	proto.RegisterEnum("aigc_common.CountType", CountType_name, CountType_value)
	proto.RegisterEnum("aigc_common.PushTo", PushTo_name, PushTo_value)
	proto.RegisterEnum("aigc_common.Entity_Type", Entity_Type_name, Entity_Type_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AigcCommonClient is the client API for AigcCommon service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AigcCommonClient interface {
	// 点赞
	Attitude(ctx context.Context, in *AttitudeRequest, opts ...grpc.CallOption) (*AttitudeResponse, error)
	// 获取点赞数
	GetAttitudeCount(ctx context.Context, in *GetAttitudeCountRequest, opts ...grpc.CallOption) (*GetAttitudeCountResponse, error)
	// 是否已点赞
	HadAttitude(ctx context.Context, in *HadAttitudeRequest, opts ...grpc.CallOption) (*HadAttitudeResponse, error)
	// 句数类型使用次数
	GetSentenceCountMap(ctx context.Context, in *GetSentenceCountMapRequest, opts ...grpc.CallOption) (*GetSentenceCountMapResponse, error)
	// 添加句数
	AddSentenceCount(ctx context.Context, in *AddSentenceCountRequest, opts ...grpc.CallOption) (*AddSentenceCountResponse, error)
	// 获取用户当日使用的专属句数总数
	GetUserSentenceCount(ctx context.Context, in *GetUserSentenceCountReq, opts ...grpc.CallOption) (*GetUserSentenceCountResp, error)
	// 句数改造后
	// 根据业务加已消耗得句数
	AddBussSentenceCount(ctx context.Context, in *AddBussSentenceCountRequest, opts ...grpc.CallOption) (*AddBussSentenceCountResponse, error)
	// 根据业务减少已消耗句数
	DecrBussSentenceCount(ctx context.Context, in *DecrBussSentenceCountRequest, opts ...grpc.CallOption) (*DecrBussSentenceCountResponse, error)
	// 批量添加句数
	BatAddBussSentenceCount(ctx context.Context, in *BatAddBussSentenceCountRequest, opts ...grpc.CallOption) (*BatAddBussSentenceCountResponse, error)
	// 添加额外句数（中台用）
	AddExtraCount(ctx context.Context, in *AddExtraCountRequest, opts ...grpc.CallOption) (*AddExtraCountResponse, error)
	// 获取已使用的句数信息
	GetUsedSentenceCount(ctx context.Context, in *GetUsedSentenceCountReq, opts ...grpc.CallOption) (*GetUsedSentenceCountResp, error)
	// 消耗句数
	ConsumeSentenceCount(ctx context.Context, in *ConsumeSentenceCountRequest, opts ...grpc.CallOption) (*ConsumeSentenceCountResponse, error)
	// 批量获取句数
	BatGetCurSentenceCount(ctx context.Context, in *BatGetCurSentenceCountRequest, opts ...grpc.CallOption) (*BatGetCurSentenceCountResponse, error)
	// 根据rewardType获取有效期内额外句数总数
	GetExtraCountByRewardTypes(ctx context.Context, in *GetExtraCountByRewardTypesRequest, opts ...grpc.CallOption) (*GetExtraCountByRewardTypesResponse, error)
	// 添加到延迟队列
	AddToDelayQueue(ctx context.Context, in *AddToDelayQueueRequest, opts ...grpc.CallOption) (*AddToDelayQueueResponse, error)
}

type aigcCommonClient struct {
	cc *grpc.ClientConn
}

func NewAigcCommonClient(cc *grpc.ClientConn) AigcCommonClient {
	return &aigcCommonClient{cc}
}

func (c *aigcCommonClient) Attitude(ctx context.Context, in *AttitudeRequest, opts ...grpc.CallOption) (*AttitudeResponse, error) {
	out := new(AttitudeResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/Attitude", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) GetAttitudeCount(ctx context.Context, in *GetAttitudeCountRequest, opts ...grpc.CallOption) (*GetAttitudeCountResponse, error) {
	out := new(GetAttitudeCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/GetAttitudeCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) HadAttitude(ctx context.Context, in *HadAttitudeRequest, opts ...grpc.CallOption) (*HadAttitudeResponse, error) {
	out := new(HadAttitudeResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/HadAttitude", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) GetSentenceCountMap(ctx context.Context, in *GetSentenceCountMapRequest, opts ...grpc.CallOption) (*GetSentenceCountMapResponse, error) {
	out := new(GetSentenceCountMapResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/GetSentenceCountMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) AddSentenceCount(ctx context.Context, in *AddSentenceCountRequest, opts ...grpc.CallOption) (*AddSentenceCountResponse, error) {
	out := new(AddSentenceCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/AddSentenceCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) GetUserSentenceCount(ctx context.Context, in *GetUserSentenceCountReq, opts ...grpc.CallOption) (*GetUserSentenceCountResp, error) {
	out := new(GetUserSentenceCountResp)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/GetUserSentenceCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) AddBussSentenceCount(ctx context.Context, in *AddBussSentenceCountRequest, opts ...grpc.CallOption) (*AddBussSentenceCountResponse, error) {
	out := new(AddBussSentenceCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/AddBussSentenceCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) DecrBussSentenceCount(ctx context.Context, in *DecrBussSentenceCountRequest, opts ...grpc.CallOption) (*DecrBussSentenceCountResponse, error) {
	out := new(DecrBussSentenceCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/DecrBussSentenceCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) BatAddBussSentenceCount(ctx context.Context, in *BatAddBussSentenceCountRequest, opts ...grpc.CallOption) (*BatAddBussSentenceCountResponse, error) {
	out := new(BatAddBussSentenceCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/BatAddBussSentenceCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) AddExtraCount(ctx context.Context, in *AddExtraCountRequest, opts ...grpc.CallOption) (*AddExtraCountResponse, error) {
	out := new(AddExtraCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/AddExtraCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) GetUsedSentenceCount(ctx context.Context, in *GetUsedSentenceCountReq, opts ...grpc.CallOption) (*GetUsedSentenceCountResp, error) {
	out := new(GetUsedSentenceCountResp)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/GetUsedSentenceCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) ConsumeSentenceCount(ctx context.Context, in *ConsumeSentenceCountRequest, opts ...grpc.CallOption) (*ConsumeSentenceCountResponse, error) {
	out := new(ConsumeSentenceCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/ConsumeSentenceCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) BatGetCurSentenceCount(ctx context.Context, in *BatGetCurSentenceCountRequest, opts ...grpc.CallOption) (*BatGetCurSentenceCountResponse, error) {
	out := new(BatGetCurSentenceCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/BatGetCurSentenceCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) GetExtraCountByRewardTypes(ctx context.Context, in *GetExtraCountByRewardTypesRequest, opts ...grpc.CallOption) (*GetExtraCountByRewardTypesResponse, error) {
	out := new(GetExtraCountByRewardTypesResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/GetExtraCountByRewardTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcCommonClient) AddToDelayQueue(ctx context.Context, in *AddToDelayQueueRequest, opts ...grpc.CallOption) (*AddToDelayQueueResponse, error) {
	out := new(AddToDelayQueueResponse)
	err := c.cc.Invoke(ctx, "/aigc_common.AigcCommon/AddToDelayQueue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AigcCommonServer is the server API for AigcCommon service.
type AigcCommonServer interface {
	// 点赞
	Attitude(context.Context, *AttitudeRequest) (*AttitudeResponse, error)
	// 获取点赞数
	GetAttitudeCount(context.Context, *GetAttitudeCountRequest) (*GetAttitudeCountResponse, error)
	// 是否已点赞
	HadAttitude(context.Context, *HadAttitudeRequest) (*HadAttitudeResponse, error)
	// 句数类型使用次数
	GetSentenceCountMap(context.Context, *GetSentenceCountMapRequest) (*GetSentenceCountMapResponse, error)
	// 添加句数
	AddSentenceCount(context.Context, *AddSentenceCountRequest) (*AddSentenceCountResponse, error)
	// 获取用户当日使用的专属句数总数
	GetUserSentenceCount(context.Context, *GetUserSentenceCountReq) (*GetUserSentenceCountResp, error)
	// 句数改造后
	// 根据业务加已消耗得句数
	AddBussSentenceCount(context.Context, *AddBussSentenceCountRequest) (*AddBussSentenceCountResponse, error)
	// 根据业务减少已消耗句数
	DecrBussSentenceCount(context.Context, *DecrBussSentenceCountRequest) (*DecrBussSentenceCountResponse, error)
	// 批量添加句数
	BatAddBussSentenceCount(context.Context, *BatAddBussSentenceCountRequest) (*BatAddBussSentenceCountResponse, error)
	// 添加额外句数（中台用）
	AddExtraCount(context.Context, *AddExtraCountRequest) (*AddExtraCountResponse, error)
	// 获取已使用的句数信息
	GetUsedSentenceCount(context.Context, *GetUsedSentenceCountReq) (*GetUsedSentenceCountResp, error)
	// 消耗句数
	ConsumeSentenceCount(context.Context, *ConsumeSentenceCountRequest) (*ConsumeSentenceCountResponse, error)
	// 批量获取句数
	BatGetCurSentenceCount(context.Context, *BatGetCurSentenceCountRequest) (*BatGetCurSentenceCountResponse, error)
	// 根据rewardType获取有效期内额外句数总数
	GetExtraCountByRewardTypes(context.Context, *GetExtraCountByRewardTypesRequest) (*GetExtraCountByRewardTypesResponse, error)
	// 添加到延迟队列
	AddToDelayQueue(context.Context, *AddToDelayQueueRequest) (*AddToDelayQueueResponse, error)
}

func RegisterAigcCommonServer(s *grpc.Server, srv AigcCommonServer) {
	s.RegisterService(&_AigcCommon_serviceDesc, srv)
}

func _AigcCommon_Attitude_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AttitudeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).Attitude(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/Attitude",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).Attitude(ctx, req.(*AttitudeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_GetAttitudeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAttitudeCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).GetAttitudeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/GetAttitudeCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).GetAttitudeCount(ctx, req.(*GetAttitudeCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_HadAttitude_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HadAttitudeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).HadAttitude(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/HadAttitude",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).HadAttitude(ctx, req.(*HadAttitudeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_GetSentenceCountMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSentenceCountMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).GetSentenceCountMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/GetSentenceCountMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).GetSentenceCountMap(ctx, req.(*GetSentenceCountMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_AddSentenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSentenceCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).AddSentenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/AddSentenceCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).AddSentenceCount(ctx, req.(*AddSentenceCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_GetUserSentenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSentenceCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).GetUserSentenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/GetUserSentenceCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).GetUserSentenceCount(ctx, req.(*GetUserSentenceCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_AddBussSentenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBussSentenceCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).AddBussSentenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/AddBussSentenceCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).AddBussSentenceCount(ctx, req.(*AddBussSentenceCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_DecrBussSentenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecrBussSentenceCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).DecrBussSentenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/DecrBussSentenceCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).DecrBussSentenceCount(ctx, req.(*DecrBussSentenceCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_BatAddBussSentenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatAddBussSentenceCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).BatAddBussSentenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/BatAddBussSentenceCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).BatAddBussSentenceCount(ctx, req.(*BatAddBussSentenceCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_AddExtraCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddExtraCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).AddExtraCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/AddExtraCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).AddExtraCount(ctx, req.(*AddExtraCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_GetUsedSentenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUsedSentenceCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).GetUsedSentenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/GetUsedSentenceCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).GetUsedSentenceCount(ctx, req.(*GetUsedSentenceCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_ConsumeSentenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsumeSentenceCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).ConsumeSentenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/ConsumeSentenceCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).ConsumeSentenceCount(ctx, req.(*ConsumeSentenceCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_BatGetCurSentenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetCurSentenceCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).BatGetCurSentenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/BatGetCurSentenceCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).BatGetCurSentenceCount(ctx, req.(*BatGetCurSentenceCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_GetExtraCountByRewardTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtraCountByRewardTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).GetExtraCountByRewardTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/GetExtraCountByRewardTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).GetExtraCountByRewardTypes(ctx, req.(*GetExtraCountByRewardTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcCommon_AddToDelayQueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddToDelayQueueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcCommonServer).AddToDelayQueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_common.AigcCommon/AddToDelayQueue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcCommonServer).AddToDelayQueue(ctx, req.(*AddToDelayQueueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AigcCommon_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc_common.AigcCommon",
	HandlerType: (*AigcCommonServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Attitude",
			Handler:    _AigcCommon_Attitude_Handler,
		},
		{
			MethodName: "GetAttitudeCount",
			Handler:    _AigcCommon_GetAttitudeCount_Handler,
		},
		{
			MethodName: "HadAttitude",
			Handler:    _AigcCommon_HadAttitude_Handler,
		},
		{
			MethodName: "GetSentenceCountMap",
			Handler:    _AigcCommon_GetSentenceCountMap_Handler,
		},
		{
			MethodName: "AddSentenceCount",
			Handler:    _AigcCommon_AddSentenceCount_Handler,
		},
		{
			MethodName: "GetUserSentenceCount",
			Handler:    _AigcCommon_GetUserSentenceCount_Handler,
		},
		{
			MethodName: "AddBussSentenceCount",
			Handler:    _AigcCommon_AddBussSentenceCount_Handler,
		},
		{
			MethodName: "DecrBussSentenceCount",
			Handler:    _AigcCommon_DecrBussSentenceCount_Handler,
		},
		{
			MethodName: "BatAddBussSentenceCount",
			Handler:    _AigcCommon_BatAddBussSentenceCount_Handler,
		},
		{
			MethodName: "AddExtraCount",
			Handler:    _AigcCommon_AddExtraCount_Handler,
		},
		{
			MethodName: "GetUsedSentenceCount",
			Handler:    _AigcCommon_GetUsedSentenceCount_Handler,
		},
		{
			MethodName: "ConsumeSentenceCount",
			Handler:    _AigcCommon_ConsumeSentenceCount_Handler,
		},
		{
			MethodName: "BatGetCurSentenceCount",
			Handler:    _AigcCommon_BatGetCurSentenceCount_Handler,
		},
		{
			MethodName: "GetExtraCountByRewardTypes",
			Handler:    _AigcCommon_GetExtraCountByRewardTypes_Handler,
		},
		{
			MethodName: "AddToDelayQueue",
			Handler:    _AigcCommon_AddToDelayQueue_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/aigc/aigc-common/aigc-common.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-common/aigc-common.proto", fileDescriptor_aigc_common_5fb2984104eccf81)
}

var fileDescriptor_aigc_common_5fb2984104eccf81 = []byte{
	// 1736 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x58, 0xdf, 0x72, 0xdb, 0x54,
	0x13, 0xb7, 0xec, 0xd4, 0x49, 0xd6, 0x71, 0xa2, 0x9c, 0xfc, 0xb1, 0x3f, 0x27, 0x69, 0x12, 0xb5,
	0xdf, 0x34, 0x9f, 0xdb, 0x3a, 0x1f, 0xe1, 0x4f, 0x4b, 0xdb, 0x0b, 0x1c, 0x47, 0x24, 0x86, 0x24,
	0x76, 0x65, 0x85, 0xd2, 0x32, 0x83, 0x46, 0x91, 0x4e, 0x13, 0xb5, 0xb6, 0xe5, 0x5a, 0x52, 0xa8,
	0x2f, 0x18, 0x66, 0xb8, 0x61, 0x18, 0xae, 0xe1, 0xa2, 0x97, 0xf0, 0x00, 0xf0, 0x00, 0xdc, 0xf1,
	0x06, 0xdc, 0xf3, 0x02, 0x3c, 0x05, 0xa3, 0x73, 0x24, 0x59, 0x92, 0x25, 0xdb, 0x19, 0x4a, 0x87,
	0x9b, 0xe4, 0x78, 0xcf, 0x6a, 0xf7, 0x77, 0x7e, 0xbb, 0x5a, 0xed, 0x1e, 0x78, 0xcb, 0x34, 0xb7,
	0x5f, 0x58, 0x9a, 0xf2, 0xdc, 0xd0, 0x9a, 0x17, 0xb8, 0xbb, 0x2d, 0x6b, 0x67, 0x0a, 0xf9, 0x73,
	0x5b, 0xd1, 0x5b, 0x2d, 0xbd, 0xed, 0x5f, 0x97, 0x3a, 0x5d, 0xdd, 0xd4, 0x51, 0xc6, 0x16, 0x49,
	0x54, 0xc4, 0x49, 0x90, 0xae, 0x9d, 0x3e, 0xc3, 0x8a, 0x89, 0xee, 0x42, 0x46, 0x27, 0x2b, 0xc9,
	0xec, 0x75, 0x70, 0x9e, 0xd9, 0x60, 0xb6, 0x66, 0x77, 0x72, 0x25, 0x9f, 0x72, 0x89, 0x6a, 0x8a,
	0xbd, 0x0e, 0x16, 0x40, 0xf7, 0xd6, 0x68, 0x05, 0xa6, 0x9d, 0x27, 0x35, 0x35, 0x9f, 0xdc, 0x60,
	0xb6, 0xb2, 0xc2, 0x14, 0x15, 0x54, 0x55, 0xee, 0x25, 0xcc, 0x95, 0x4d, 0x53, 0x33, 0x2d, 0x15,
	0x0b, 0xf8, 0x85, 0x85, 0x0d, 0x13, 0x3d, 0x80, 0x39, 0xd9, 0x11, 0x49, 0x54, 0x8f, 0x78, 0xcb,
	0xec, 0x2c, 0x44, 0x78, 0x13, 0x66, 0x5d, 0x5d, 0x07, 0xe7, 0x0d, 0x48, 0xea, 0x1d, 0xe2, 0x26,
	0x0c, 0xcf, 0xf5, 0x53, 0xeb, 0x08, 0x49, 0xbd, 0xc3, 0x21, 0x60, 0xfb, 0x9e, 0x8d, 0x8e, 0xde,
	0x36, 0x30, 0x77, 0x00, 0xb9, 0x7d, 0x6c, 0xba, 0xe2, 0x8a, 0x6e, 0xb5, 0x4d, 0x17, 0xd5, 0x6d,
	0x98, 0xa4, 0x60, 0x8c, 0x3c, 0xb3, 0x91, 0x8a, 0x43, 0xe3, 0xea, 0x70, 0x7f, 0x30, 0x90, 0x1f,
	0x34, 0x45, 0xdd, 0x20, 0x0d, 0x16, 0xbd, 0x13, 0x2a, 0xf6, 0x8e, 0xa4, 0xb5, 0x9f, 0xea, 0xae,
	0xe1, 0x3b, 0x01, 0xc3, 0x71, 0x46, 0x4a, 0x01, 0x69, 0xb5, 0xfd, 0x54, 0x17, 0x90, 0x1c, 0x16,
	0x19, 0x85, 0x4f, 0x60, 0x7e, 0x40, 0x11, 0xdd, 0x84, 0xf4, 0x68, 0x62, 0x1d, 0x15, 0xb4, 0x08,
	0x57, 0x08, 0x46, 0x27, 0x74, 0xf4, 0x07, 0x27, 0x00, 0x3a, 0x90, 0xd5, 0xd7, 0x1a, 0x3a, 0xee,
	0x2e, 0x2c, 0x04, 0x6c, 0x3a, 0x6c, 0x6d, 0xc2, 0xcc, 0xb9, 0xac, 0x4a, 0xae, 0x32, 0xb1, 0x38,
	0x25, 0x64, 0xce, 0xfb, 0xaa, 0xdc, 0x8f, 0x0c, 0xa4, 0xf9, 0xb6, 0xa9, 0x99, 0x3d, 0x34, 0x0b,
	0x49, 0x4d, 0x25, 0x3a, 0x59, 0x21, 0xa9, 0xa9, 0xe8, 0x16, 0x4c, 0x90, 0x84, 0xa5, 0x19, 0x91,
	0x0f, 0xe0, 0xa0, 0x8f, 0x94, 0x48, 0xc6, 0x12, 0x2d, 0xee, 0x09, 0x4c, 0x90, 0x9c, 0x5d, 0x04,
	0x56, 0x7c, 0x5c, 0xe7, 0xa5, 0x93, 0xe3, 0x46, 0x9d, 0xaf, 0x54, 0x3f, 0xac, 0xf2, 0x7b, 0x6c,
	0x02, 0xb1, 0x30, 0x43, 0xa4, 0xf5, 0xb2, 0x20, 0x1e, 0xf3, 0x02, 0xcb, 0xa0, 0x05, 0x98, 0x23,
	0x92, 0xa3, 0x13, 0xb1, 0x2a, 0xed, 0x0b, 0xb5, 0x93, 0x3a, 0x9b, 0x44, 0x4b, 0x30, 0x4f, 0x84,
	0x8d, 0xea, 0xf1, 0xfe, 0x21, 0xef, 0x88, 0x53, 0x9c, 0x0e, 0x85, 0x7d, 0x6c, 0x36, 0x70, 0xdb,
	0xc4, 0x6d, 0x85, 0x46, 0xe3, 0x48, 0xee, 0xb8, 0xd4, 0x21, 0x07, 0xa7, 0x9d, 0x03, 0x59, 0x8a,
	0xc6, 0x8e, 0x13, 0x26, 0x10, 0x09, 0xfa, 0x30, 0x8b, 0x14, 0xbd, 0xe0, 0xa8, 0x20, 0x16, 0x52,
	0x96, 0xa6, 0xe6, 0x53, 0xe4, 0xe4, 0xf6, 0x92, 0xfb, 0x99, 0x81, 0x95, 0x48, 0x8f, 0x0e, 0xb1,
	0x0d, 0x98, 0xa6, 0xd9, 0xd7, 0x92, 0x3b, 0x4e, 0xee, 0xbd, 0x17, 0xce, 0xbd, 0xb8, 0x87, 0x4b,
	0xae, 0x80, 0x6f, 0x9b, 0xdd, 0x9e, 0x30, 0xa5, 0x38, 0x3f, 0x0b, 0xf7, 0x21, 0x1b, 0xd8, 0xb2,
	0x71, 0x3d, 0xc7, 0x3d, 0x27, 0x22, 0xf6, 0xd2, 0xce, 0xa8, 0x0b, 0xb9, 0x69, 0x61, 0x37, 0xa3,
	0xc8, 0x8f, 0x7b, 0xc9, 0xbb, 0x0c, 0xd7, 0x84, 0x5c, 0x59, 0x55, 0x03, 0x3e, 0x5d, 0x7e, 0x9c,
	0xe3, 0x31, 0xde, 0xf1, 0x2e, 0xc7, 0x8e, 0x4b, 0x2f, 0xa5, 0x87, 0x06, 0xbb, 0x00, 0xf9, 0x41,
	0x6f, 0x4e, 0x25, 0xf8, 0x8e, 0x21, 0xa5, 0xe0, 0xc4, 0xc0, 0xdd, 0x30, 0x9c, 0x08, 0x28, 0xd7,
	0x20, 0x6b, 0x38, 0x5a, 0x92, 0x97, 0x6d, 0x59, 0x61, 0xc6, 0x15, 0x92, 0x9c, 0x7a, 0x1f, 0x32,
	0x14, 0x8c, 0xe4, 0x21, 0x19, 0x96, 0x90, 0x40, 0x95, 0xed, 0x35, 0xf7, 0x7f, 0x52, 0x4c, 0x22,
	0xc0, 0x18, 0x9d, 0xfe, 0xfb, 0xc9, 0xf8, 0xdf, 0xcf, 0xef, 0x19, 0x58, 0x29, 0xab, 0xea, 0xae,
	0x65, 0x18, 0x6f, 0x88, 0x4e, 0x9b, 0x84, 0x53, 0xcb, 0xd0, 0xda, 0xd8, 0x30, 0xe8, 0x09, 0x27,
	0x28, 0x09, 0xae, 0x90, 0x9c, 0xe4, 0x0e, 0xac, 0x46, 0xc3, 0x72, 0x72, 0x32, 0x07, 0x93, 0x8a,
	0xd5, 0x95, 0xda, 0x56, 0xcb, 0xc1, 0x96, 0x56, 0xac, 0xee, 0xb1, 0xd5, 0xe2, 0x7e, 0x60, 0x60,
	0x75, 0x0f, 0x2b, 0xdd, 0x7f, 0xdd, 0x89, 0xd6, 0x61, 0x2d, 0x06, 0x97, 0x93, 0x4a, 0xaf, 0x18,
	0xb8, 0xba, 0x2b, 0x9b, 0xc3, 0xa2, 0x81, 0x60, 0xc2, 0xd2, 0x54, 0xc3, 0x7d, 0xf9, 0xed, 0xf5,
	0x1b, 0x42, 0xff, 0x2b, 0x03, 0xeb, 0xb1, 0xe0, 0x9c, 0x98, 0x7c, 0x06, 0x19, 0x27, 0x26, 0xbe,
	0x4a, 0x71, 0x3f, 0x00, 0x67, 0x84, 0x89, 0x52, 0x85, 0x04, 0xd1, 0x2b, 0x17, 0xd3, 0x8a, 0xfb,
	0xbb, 0xf0, 0x00, 0x66, 0x83, 0x9b, 0x97, 0x2a, 0x18, 0xbf, 0x30, 0xb0, 0x58, 0x56, 0x55, 0xfe,
	0xa5, 0xd9, 0x95, 0x47, 0x64, 0xc3, 0x3a, 0x64, 0xb0, 0xad, 0x26, 0xf9, 0xbf, 0x66, 0x80, 0xbd,
	0x27, 0xa9, 0x42, 0x47, 0xeb, 0x62, 0xc9, 0xd4, 0x5a, 0x94, 0xca, 0x94, 0xad, 0x60, 0x8b, 0x44,
	0xad, 0x35, 0x1e, 0xa1, 0xb6, 0x95, 0x2e, 0xfe, 0x42, 0xee, 0xaa, 0x54, 0xe5, 0x0a, 0x75, 0x43,
	0x45, 0x84, 0xf1, 0x1c, 0x2c, 0x85, 0x10, 0x3b, 0x79, 0xf2, 0xad, 0x57, 0x72, 0xd4, 0xf1, 0x4a,
	0x4e, 0x10, 0x4c, 0x32, 0x02, 0xcc, 0x1d, 0xc8, 0xd0, 0x0a, 0x6f, 0x6b, 0x18, 0xf9, 0xd4, 0x46,
	0x6a, 0x6b, 0x76, 0x67, 0x39, 0x10, 0x39, 0xe2, 0x82, 0x16, 0x1c, 0xc5, 0x5d, 0x1a, 0xdc, 0x99,
	0x5b, 0x70, 0x06, 0xcb, 0x23, 0x2a, 0xc2, 0xbc, 0xa9, 0xab, 0x72, 0x4f, 0x32, 0x75, 0x53, 0x6e,
	0x4a, 0xfe, 0xe2, 0x33, 0x47, 0x36, 0x44, 0x5b, 0xee, 0x71, 0xea, 0xd7, 0x72, 0x48, 0x37, 0x3d,
	0x05, 0xee, 0x37, 0x06, 0x56, 0x2a, 0x7a, 0xdb, 0xb0, 0x5a, 0xf8, 0x9f, 0x78, 0xab, 0x07, 0x0a,
	0x73, 0x2a, 0xa2, 0x30, 0x8f, 0x15, 0xd7, 0x15, 0x98, 0xee, 0xea, 0x4d, 0xec, 0x8f, 0xea, 0x94,
	0x2d, 0x20, 0x31, 0x7d, 0x08, 0xab, 0xd1, 0x87, 0x18, 0x51, 0xd5, 0x50, 0x1e, 0x26, 0x0d, 0x4b,
	0x51, 0xb0, 0x61, 0x90, 0xd3, 0x4c, 0x09, 0xee, 0x4f, 0xee, 0xeb, 0x24, 0xac, 0xed, 0xca, 0xe6,
	0x3e, 0x36, 0x2b, 0x56, 0xf7, 0x0d, 0x51, 0x93, 0x7a, 0xfd, 0xd4, 0xa0, 0xfb, 0x50, 0x68, 0x63,
	0xac, 0x4a, 0xf2, 0x85, 0xac, 0x35, 0xe5, 0xd3, 0x26, 0x96, 0xfc, 0x6f, 0x61, 0x9a, 0x1c, 0x3a,
	0x67, 0x6b, 0x94, 0x5d, 0x85, 0xfe, 0xab, 0xc1, 0xfd, 0x49, 0x4b, 0x67, 0x24, 0x09, 0x0e, 0xb5,
	0x4f, 0xa2, 0x8a, 0xd3, 0xbd, 0x70, 0x71, 0x1a, 0x62, 0x21, 0xbe, 0x36, 0xa1, 0x12, 0x2c, 0x84,
	0x61, 0xdb, 0x21, 0xa4, 0x59, 0x3c, 0x2f, 0x07, 0x00, 0x1f, 0x5b, 0xad, 0xbf, 0x59, 0xcb, 0xbe,
	0x84, 0xcd, 0x7d, 0x6c, 0xf6, 0x4f, 0xbf, 0xdb, 0x13, 0xbc, 0xaa, 0x61, 0xc4, 0x07, 0x7d, 0x13,
	0x66, 0x7c, 0x05, 0xc7, 0xce, 0x23, 0x3b, 0x8c, 0x99, 0x7e, 0xc5, 0x31, 0x06, 0xa3, 0x98, 0x8a,
	0xf8, 0x12, 0xfc, 0xce, 0x00, 0x37, 0xcc, 0xbf, 0xc3, 0xf7, 0x33, 0x98, 0xf3, 0x05, 0xd0, 0xc7,
	0xf9, 0x6e, 0xb8, 0x75, 0x1c, 0x61, 0xa9, 0xd4, 0xdf, 0xf7, 0xb8, 0xcf, 0x62, 0xbf, 0xac, 0xf0,
	0x01, 0xa0, 0x41, 0xa5, 0x4b, 0x71, 0xfa, 0x8a, 0x81, 0xe5, 0xb2, 0xaa, 0x8a, 0xfa, 0x1e, 0x6e,
	0xca, 0xbd, 0x87, 0x16, 0xb6, 0xbc, 0x59, 0x65, 0x0d, 0x40, 0xb5, 0x85, 0xb4, 0xda, 0x33, 0xa4,
	0xda, 0x4f, 0x13, 0x09, 0x29, 0xf6, 0x08, 0x26, 0x54, 0xd9, 0x94, 0x89, 0xc9, 0x19, 0x81, 0xac,
	0xd1, 0x2d, 0x98, 0xec, 0x58, 0xc6, 0xb9, 0x64, 0xea, 0x4e, 0xf7, 0x16, 0x7c, 0xc1, 0xea, 0x96,
	0x71, 0x2e, 0xea, 0x42, 0xba, 0x43, 0xfe, 0xd3, 0x2f, 0x81, 0x82, 0xb5, 0x0b, 0x2c, 0xb5, 0x8c,
	0x33, 0xf2, 0xe6, 0xcc, 0xd8, 0x5f, 0x02, 0x22, 0x3a, 0x32, 0xce, 0xb8, 0xff, 0x90, 0x6e, 0x37,
	0x88, 0x8d, 0x72, 0x53, 0xac, 0x02, 0xd4, 0xfc, 0x13, 0x74, 0xae, 0xb6, 0xfb, 0x11, 0x5f, 0x11,
	0xa5, 0x88, 0xa1, 0xe4, 0x2a, 0x14, 0xfc, 0x9b, 0x64, 0xda, 0x90, 0x44, 0xfe, 0xa8, 0x7e, 0x58,
	0x16, 0x79, 0x96, 0x29, 0x3e, 0x02, 0xe8, 0x4f, 0xbe, 0xb6, 0xa9, 0xb2, 0x28, 0x56, 0xc5, 0x93,
	0x3d, 0x5e, 0xaa, 0xd5, 0x43, 0xa6, 0x16, 0x81, 0xf5, 0x6f, 0x1e, 0x56, 0x3f, 0xe6, 0x59, 0x06,
	0x2d, 0x03, 0x0a, 0x3e, 0x42, 0xe4, 0xc9, 0xe2, 0x37, 0x0c, 0x40, 0x3f, 0xae, 0xb6, 0x65, 0x81,
	0x7f, 0x54, 0x16, 0xf6, 0xa2, 0x40, 0x2e, 0xc1, 0xbc, 0x7f, 0xb3, 0x71, 0x50, 0x16, 0x1c, 0xd3,
	0x7e, 0xb1, 0x58, 0x16, 0xf6, 0x79, 0x91, 0x4d, 0xda, 0x40, 0xfc, 0xf2, 0x7a, 0xad, 0x21, 0xb2,
	0x29, 0x94, 0x83, 0x85, 0x80, 0x54, 0xe0, 0x1b, 0xfc, 0xb1, 0xc8, 0x4e, 0x14, 0x1b, 0x30, 0xed,
	0x7d, 0xc6, 0x50, 0x01, 0x96, 0x2b, 0xb5, 0x93, 0xe3, 0x48, 0xae, 0x16, 0x81, 0xf5, 0xed, 0x89,
	0xb5, 0xbd, 0xf2, 0x63, 0x96, 0x19, 0x90, 0x8a, 0xe5, 0x43, 0x36, 0x59, 0xac, 0x42, 0x9a, 0x06,
	0xd4, 0xf6, 0x5b, 0x3f, 0x69, 0x1c, 0x48, 0x62, 0x2d, 0x64, 0x6e, 0x01, 0xe6, 0xdc, 0x8d, 0xfe,
	0x48, 0x38, 0x0f, 0x59, 0x57, 0xe8, 0x0c, 0x84, 0x3b, 0x3f, 0xcd, 0x00, 0x94, 0xb5, 0x33, 0xa5,
	0x42, 0xf2, 0x04, 0x55, 0x61, 0xca, 0x8d, 0x08, 0x5a, 0x8d, 0xbc, 0xa2, 0x70, 0x72, 0xb4, 0xb0,
	0x16, 0xb3, 0xeb, 0x74, 0x0c, 0x09, 0x24, 0x03, 0x1b, 0xbe, 0x20, 0x40, 0xd7, 0x47, 0xdc, 0x1f,
	0x50, 0xd3, 0xff, 0x1d, 0xeb, 0x96, 0x81, 0x4b, 0x20, 0x01, 0x32, 0xbe, 0xa9, 0x1c, 0xad, 0x07,
	0x9e, 0x1b, 0xbc, 0x03, 0x28, 0x6c, 0xc4, 0x2b, 0x78, 0x36, 0x9f, 0xc1, 0x42, 0xc4, 0x6c, 0x89,
	0x6e, 0x8c, 0x9e, 0x3e, 0xa9, 0x8f, 0xad, 0x71, 0xc7, 0x54, 0x4a, 0x51, 0x78, 0xca, 0x0b, 0x51,
	0x14, 0x33, 0x72, 0x86, 0x28, 0x8a, 0x1d, 0x15, 0x13, 0x08, 0xc3, 0x62, 0xd4, 0x78, 0x36, 0x18,
	0x89, 0xa8, 0x71, 0x72, 0x30, 0x12, 0x91, 0x73, 0x1e, 0x97, 0x40, 0x2d, 0xd2, 0xeb, 0x0e, 0x34,
	0xd9, 0x68, 0x2b, 0x8c, 0x33, 0x6e, 0xce, 0x28, 0xfc, 0x6f, 0x0c, 0x4d, 0xef, 0x54, 0x1d, 0x58,
	0x8a, 0x1c, 0x6c, 0x50, 0xd0, 0xca, 0xb0, 0xa1, 0xac, 0x50, 0x1c, 0x47, 0xd5, 0xf3, 0x78, 0x01,
	0xb9, 0x98, 0x41, 0x02, 0xdd, 0x1c, 0x6f, 0xdc, 0xa0, 0x5e, 0x6f, 0x5d, 0x66, 0x36, 0xe1, 0x12,
	0xe8, 0x53, 0xc8, 0x06, 0x5a, 0x72, 0xb4, 0x19, 0xe6, 0x69, 0x60, 0xc0, 0x28, 0x70, 0xc3, 0x54,
	0x06, 0x33, 0x43, 0x1d, 0x9d, 0x19, 0xea, 0x58, 0x99, 0xa1, 0xc6, 0x64, 0x46, 0x54, 0xff, 0x19,
	0xca, 0x8c, 0x21, 0x7d, 0x76, 0x28, 0x33, 0x86, 0x35, 0xb3, 0x5c, 0x02, 0x19, 0xb0, 0x1c, 0xdd,
	0x53, 0xa1, 0xe2, 0x58, 0x8d, 0x17, 0x75, 0x79, 0xf3, 0x12, 0x4d, 0x1a, 0x97, 0x40, 0x5f, 0x91,
	0xeb, 0xb3, 0x98, 0xa6, 0x02, 0x95, 0xc6, 0xee, 0x3e, 0xa8, 0xf3, 0xed, 0x4b, 0x76, 0x2b, 0x5c,
	0x02, 0x7d, 0x0e, 0x73, 0xa1, 0xcf, 0x35, 0xba, 0x16, 0x4e, 0x82, 0x88, 0x46, 0xa3, 0x70, 0x7d,
	0xb8, 0x92, 0x6b, 0x7f, 0xf7, 0x9d, 0x27, 0x3b, 0x67, 0x7a, 0x53, 0x6e, 0x9f, 0x95, 0xde, 0xdd,
	0x31, 0xcd, 0x92, 0xa2, 0xb7, 0xb6, 0xc9, 0x8d, 0xbc, 0xa2, 0x37, 0xb7, 0x0d, 0xdc, 0xbd, 0xd0,
	0x14, 0x6c, 0x0c, 0x5c, 0xe0, 0x9f, 0xa6, 0x89, 0xce, 0xdb, 0x7f, 0x05, 0x00, 0x00, 0xff, 0xff,
	0x71, 0xc0, 0xd4, 0x5d, 0xea, 0x17, 0x00, 0x00,
}
