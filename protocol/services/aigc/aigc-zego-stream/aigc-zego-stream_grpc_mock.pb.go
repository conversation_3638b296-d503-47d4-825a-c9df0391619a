// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-zego-stream/aigc-zego-stream.proto

package aigc_zego_stream

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAigcZegoStreamClient is a mock of AigcZegoStreamClient interface.
type MockAigcZegoStreamClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcZegoStreamClientMockRecorder
}

// MockAigcZegoStreamClientMockRecorder is the mock recorder for MockAigcZegoStreamClient.
type MockAigcZegoStreamClientMockRecorder struct {
	mock *MockAigcZegoStreamClient
}

// NewMockAigcZegoStreamClient creates a new mock instance.
func NewMockAigcZegoStreamClient(ctrl *gomock.Controller) *MockAigcZegoStreamClient {
	mock := &MockAigcZegoStreamClient{ctrl: ctrl}
	mock.recorder = &MockAigcZegoStreamClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcZegoStreamClient) EXPECT() *MockAigcZegoStreamClientMockRecorder {
	return m.recorder
}

// Login mocks base method.
func (m *MockAigcZegoStreamClient) Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Login", varargs...)
	ret0, _ := ret[0].(*LoginResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Login indicates an expected call of Login.
func (mr *MockAigcZegoStreamClientMockRecorder) Login(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Login", reflect.TypeOf((*MockAigcZegoStreamClient)(nil).Login), varargs...)
}

// MockAigcZegoStreamServer is a mock of AigcZegoStreamServer interface.
type MockAigcZegoStreamServer struct {
	ctrl     *gomock.Controller
	recorder *MockAigcZegoStreamServerMockRecorder
}

// MockAigcZegoStreamServerMockRecorder is the mock recorder for MockAigcZegoStreamServer.
type MockAigcZegoStreamServerMockRecorder struct {
	mock *MockAigcZegoStreamServer
}

// NewMockAigcZegoStreamServer creates a new mock instance.
func NewMockAigcZegoStreamServer(ctrl *gomock.Controller) *MockAigcZegoStreamServer {
	mock := &MockAigcZegoStreamServer{ctrl: ctrl}
	mock.recorder = &MockAigcZegoStreamServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcZegoStreamServer) EXPECT() *MockAigcZegoStreamServerMockRecorder {
	return m.recorder
}

// Login mocks base method.
func (m *MockAigcZegoStreamServer) Login(ctx context.Context, in *LoginReq) (*LoginResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Login", ctx, in)
	ret0, _ := ret[0].(*LoginResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Login indicates an expected call of Login.
func (mr *MockAigcZegoStreamServerMockRecorder) Login(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Login", reflect.TypeOf((*MockAigcZegoStreamServer)(nil).Login), ctx, in)
}
