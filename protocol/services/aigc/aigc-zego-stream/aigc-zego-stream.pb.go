// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-zego-stream/aigc-zego-stream.proto

package aigc_zego_stream // import "golang.52tt.com/protocol/services/aigc/aigc-zego-stream"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type LoginReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LoginReq) Reset()         { *m = LoginReq{} }
func (m *LoginReq) String() string { return proto.CompactTextString(m) }
func (*LoginReq) ProtoMessage()    {}
func (*LoginReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_zego_stream_2b909bf1692e30a5, []int{0}
}
func (m *LoginReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoginReq.Unmarshal(m, b)
}
func (m *LoginReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoginReq.Marshal(b, m, deterministic)
}
func (dst *LoginReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoginReq.Merge(dst, src)
}
func (m *LoginReq) XXX_Size() int {
	return xxx_messageInfo_LoginReq.Size(m)
}
func (m *LoginReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LoginReq.DiscardUnknown(m)
}

var xxx_messageInfo_LoginReq proto.InternalMessageInfo

func (m *LoginReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LoginReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type LoginResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LoginResp) Reset()         { *m = LoginResp{} }
func (m *LoginResp) String() string { return proto.CompactTextString(m) }
func (*LoginResp) ProtoMessage()    {}
func (*LoginResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_zego_stream_2b909bf1692e30a5, []int{1}
}
func (m *LoginResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoginResp.Unmarshal(m, b)
}
func (m *LoginResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoginResp.Marshal(b, m, deterministic)
}
func (dst *LoginResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoginResp.Merge(dst, src)
}
func (m *LoginResp) XXX_Size() int {
	return xxx_messageInfo_LoginResp.Size(m)
}
func (m *LoginResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LoginResp.DiscardUnknown(m)
}

var xxx_messageInfo_LoginResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*LoginReq)(nil), "aigc_zego_stream.LoginReq")
	proto.RegisterType((*LoginResp)(nil), "aigc_zego_stream.LoginResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AigcZegoStreamClient is the client API for AigcZegoStream service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AigcZegoStreamClient interface {
	Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error)
}

type aigcZegoStreamClient struct {
	cc *grpc.ClientConn
}

func NewAigcZegoStreamClient(cc *grpc.ClientConn) AigcZegoStreamClient {
	return &aigcZegoStreamClient{cc}
}

func (c *aigcZegoStreamClient) Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error) {
	out := new(LoginResp)
	err := c.cc.Invoke(ctx, "/aigc_zego_stream.AigcZegoStream/Login", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AigcZegoStreamServer is the server API for AigcZegoStream service.
type AigcZegoStreamServer interface {
	Login(context.Context, *LoginReq) (*LoginResp, error)
}

func RegisterAigcZegoStreamServer(s *grpc.Server, srv AigcZegoStreamServer) {
	s.RegisterService(&_AigcZegoStream_serviceDesc, srv)
}

func _AigcZegoStream_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcZegoStreamServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_zego_stream.AigcZegoStream/Login",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcZegoStreamServer).Login(ctx, req.(*LoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AigcZegoStream_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc_zego_stream.AigcZegoStream",
	HandlerType: (*AigcZegoStreamServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Login",
			Handler:    _AigcZegoStream_Login_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/aigc/aigc-zego-stream/aigc-zego-stream.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-zego-stream/aigc-zego-stream.proto", fileDescriptor_aigc_zego_stream_2b909bf1692e30a5)
}

var fileDescriptor_aigc_zego_stream_2b909bf1692e30a5 = []byte{
	// 212 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xb2, 0x2e, 0x29, 0xd1, 0x2f,
	0x2c, 0xcd, 0x4c, 0xce, 0x2e, 0xce, 0xcc, 0x29, 0x4b, 0x2d, 0xd2, 0x4f, 0xcc, 0x4c, 0x4f, 0x06,
	0x13, 0xba, 0x55, 0xa9, 0xe9, 0xf9, 0xba, 0xc5, 0x25, 0x45, 0xa9, 0x89, 0xb9, 0x18, 0x02, 0x7a,
	0x05, 0x45, 0xf9, 0x25, 0xf9, 0x42, 0x02, 0x20, 0xf1, 0x78, 0x90, 0x78, 0x3c, 0x44, 0x5c, 0xc9,
	0x9a, 0x8b, 0xc3, 0x27, 0x3f, 0x3d, 0x33, 0x2f, 0x28, 0xb5, 0x50, 0x48, 0x96, 0x8b, 0x2b, 0x39,
	0x23, 0x31, 0x2f, 0x2f, 0x35, 0x27, 0x3e, 0x33, 0x45, 0x82, 0x51, 0x81, 0x51, 0x83, 0x37, 0x88,
	0x13, 0x2a, 0xe2, 0x99, 0x22, 0x24, 0xc0, 0xc5, 0x5c, 0x9a, 0x99, 0x22, 0xc1, 0x04, 0x16, 0x07,
	0x31, 0x95, 0xb8, 0xb9, 0x38, 0xa1, 0x9a, 0x8b, 0x0b, 0x8c, 0x82, 0xb8, 0xf8, 0x1c, 0x33, 0xd3,
	0x93, 0xa3, 0x52, 0xd3, 0xf3, 0x83, 0xc1, 0x66, 0x0b, 0x39, 0x70, 0xb1, 0x82, 0xa5, 0x85, 0xa4,
	0xf4, 0xd0, 0xed, 0xd5, 0x83, 0x59, 0x2a, 0x25, 0x8d, 0x53, 0xae, 0xb8, 0xc0, 0xc9, 0x32, 0xca,
	0x3c, 0x3d, 0x3f, 0x27, 0x31, 0x2f, 0x5d, 0xcf, 0xd4, 0xa8, 0xa4, 0x44, 0x2f, 0x39, 0x3f, 0x57,
	0x1f, 0xec, 0x91, 0xe4, 0xfc, 0x1c, 0xfd, 0xe2, 0xd4, 0xa2, 0xb2, 0xcc, 0xe4, 0xd4, 0x62, 0xec,
	0x21, 0x90, 0xc4, 0x06, 0x56, 0x68, 0x0c, 0x08, 0x00, 0x00, 0xff, 0xff, 0x58, 0xcb, 0x23, 0x0c,
	0x30, 0x01, 0x00, 0x00,
}
