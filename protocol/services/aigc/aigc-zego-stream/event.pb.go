// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-zego-stream/event.proto

package aigc_zego_stream // import "golang.52tt.com/protocol/services/aigc/aigc-zego-stream"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AigcStreamReconnectEvent struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AigcStreamReconnectEvent) Reset()         { *m = AigcStreamReconnectEvent{} }
func (m *AigcStreamReconnectEvent) String() string { return proto.CompactTextString(m) }
func (*AigcStreamReconnectEvent) ProtoMessage()    {}
func (*AigcStreamReconnectEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_88f585b8c88aaeef, []int{0}
}
func (m *AigcStreamReconnectEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AigcStreamReconnectEvent.Unmarshal(m, b)
}
func (m *AigcStreamReconnectEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AigcStreamReconnectEvent.Marshal(b, m, deterministic)
}
func (dst *AigcStreamReconnectEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AigcStreamReconnectEvent.Merge(dst, src)
}
func (m *AigcStreamReconnectEvent) XXX_Size() int {
	return xxx_messageInfo_AigcStreamReconnectEvent.Size(m)
}
func (m *AigcStreamReconnectEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_AigcStreamReconnectEvent.DiscardUnknown(m)
}

var xxx_messageInfo_AigcStreamReconnectEvent proto.InternalMessageInfo

func (m *AigcStreamReconnectEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AigcStreamReconnectEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func init() {
	proto.RegisterType((*AigcStreamReconnectEvent)(nil), "aigc_zego_stream.AigcStreamReconnectEvent")
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-zego-stream/event.proto", fileDescriptor_event_88f585b8c88aaeef)
}

var fileDescriptor_event_88f585b8c88aaeef = []byte{
	// 183 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x8e, 0x31, 0x0b, 0xc2, 0x30,
	0x10, 0x46, 0xa9, 0x82, 0x60, 0x40, 0x28, 0x9d, 0xba, 0x08, 0xe2, 0xe4, 0xd2, 0x46, 0x14, 0x11,
	0x47, 0x05, 0x07, 0x71, 0xab, 0x9b, 0x4b, 0x89, 0xd7, 0x23, 0x06, 0xd3, 0x44, 0x93, 0x6b, 0x07,
	0x7f, 0xbd, 0x24, 0x3a, 0xba, 0x1c, 0x77, 0xef, 0xdd, 0x71, 0x1f, 0x5b, 0x12, 0xf1, 0x57, 0xa7,
	0xe0, 0xe1, 0x95, 0xee, 0xd1, 0x71, 0xa1, 0x24, 0xc4, 0x52, 0xbc, 0x51, 0xda, 0xc2, 0x93, 0x43,
	0xd1, 0x72, 0xec, 0xd1, 0x50, 0xf9, 0x74, 0x96, 0x6c, 0x96, 0x06, 0x5b, 0x07, 0x5b, 0x7f, 0xed,
	0xfc, 0xcc, 0xf2, 0xbd, 0x92, 0x70, 0x89, 0x53, 0x85, 0x60, 0x8d, 0x41, 0xa0, 0x63, 0xb8, 0xc9,
	0xa6, 0x8c, 0xc1, 0x5d, 0x18, 0x83, 0xba, 0x56, 0x4d, 0x9e, 0xcc, 0x92, 0xc5, 0xa4, 0x1a, 0xff,
	0xc8, 0xa9, 0xc9, 0x52, 0x36, 0xec, 0x54, 0x93, 0x0f, 0x22, 0x0f, 0xed, 0x61, 0x77, 0xdd, 0x4a,
	0xab, 0x85, 0x91, 0xe5, 0x66, 0x45, 0x54, 0x82, 0x6d, 0x79, 0xfc, 0x0b, 0x56, 0x73, 0x8f, 0xae,
	0x57, 0x80, 0xfe, 0x7f, 0xca, 0xdb, 0x28, 0x2e, 0xae, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xd4,
	0xe8, 0x77, 0xab, 0xd4, 0x00, 0x00, 0x00,
}
