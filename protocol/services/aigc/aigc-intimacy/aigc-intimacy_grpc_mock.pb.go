// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-intimacy/aigc-intimacy.proto

package aigc_intimacy

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAigcIntimacyClient is a mock of AigcIntimacyClient interface.
type MockAigcIntimacyClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcIntimacyClientMockRecorder
}

// MockAigcIntimacyClientMockRecorder is the mock recorder for MockAigcIntimacyClient.
type MockAigcIntimacyClientMockRecorder struct {
	mock *MockAigcIntimacyClient
}

// NewMockAigcIntimacyClient creates a new mock instance.
func NewMockAigcIntimacyClient(ctrl *gomock.Controller) *MockAigcIntimacyClient {
	mock := &MockAigcIntimacyClient{ctrl: ctrl}
	mock.recorder = &MockAigcIntimacyClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcIntimacyClient) EXPECT() *MockAigcIntimacyClientMockRecorder {
	return m.recorder
}

// BatchGetCurRelation mocks base method.
func (m *MockAigcIntimacyClient) BatchGetCurRelation(ctx context.Context, in *BatchGetCurRelationRequest, opts ...grpc.CallOption) (*BatchGetCurRelationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetCurRelation", varargs...)
	ret0, _ := ret[0].(*BatchGetCurRelationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCurRelation indicates an expected call of BatchGetCurRelation.
func (mr *MockAigcIntimacyClientMockRecorder) BatchGetCurRelation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCurRelation", reflect.TypeOf((*MockAigcIntimacyClient)(nil).BatchGetCurRelation), varargs...)
}

// CreateChatBackground mocks base method.
func (m *MockAigcIntimacyClient) CreateChatBackground(ctx context.Context, in *CreateChatBackgroundRequest, opts ...grpc.CallOption) (*CreateChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateChatBackground", varargs...)
	ret0, _ := ret[0].(*CreateChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChatBackground indicates an expected call of CreateChatBackground.
func (mr *MockAigcIntimacyClientMockRecorder) CreateChatBackground(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChatBackground", reflect.TypeOf((*MockAigcIntimacyClient)(nil).CreateChatBackground), varargs...)
}

// CreateIntimacyCondition mocks base method.
func (m *MockAigcIntimacyClient) CreateIntimacyCondition(ctx context.Context, in *CreateIntimacyConditionRequest, opts ...grpc.CallOption) (*CreateIntimacyConditionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateIntimacyCondition", varargs...)
	ret0, _ := ret[0].(*CreateIntimacyConditionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIntimacyCondition indicates an expected call of CreateIntimacyCondition.
func (mr *MockAigcIntimacyClientMockRecorder) CreateIntimacyCondition(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIntimacyCondition", reflect.TypeOf((*MockAigcIntimacyClient)(nil).CreateIntimacyCondition), varargs...)
}

// CreateIntimacyLevelConf mocks base method.
func (m *MockAigcIntimacyClient) CreateIntimacyLevelConf(ctx context.Context, in *CreateIntimacyLevelConfRequest, opts ...grpc.CallOption) (*CreateIntimacyLevelConfResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateIntimacyLevelConf", varargs...)
	ret0, _ := ret[0].(*CreateIntimacyLevelConfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIntimacyLevelConf indicates an expected call of CreateIntimacyLevelConf.
func (mr *MockAigcIntimacyClientMockRecorder) CreateIntimacyLevelConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIntimacyLevelConf", reflect.TypeOf((*MockAigcIntimacyClient)(nil).CreateIntimacyLevelConf), varargs...)
}

// CreateRelationship mocks base method.
func (m *MockAigcIntimacyClient) CreateRelationship(ctx context.Context, in *CreateRelationshipRequest, opts ...grpc.CallOption) (*CreateRelationshipResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRelationship", varargs...)
	ret0, _ := ret[0].(*CreateRelationshipResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRelationship indicates an expected call of CreateRelationship.
func (mr *MockAigcIntimacyClientMockRecorder) CreateRelationship(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRelationship", reflect.TypeOf((*MockAigcIntimacyClient)(nil).CreateRelationship), varargs...)
}

// DeleteChatBackground mocks base method.
func (m *MockAigcIntimacyClient) DeleteChatBackground(ctx context.Context, in *DeleteChatBackgroundRequest, opts ...grpc.CallOption) (*DeleteChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteChatBackground", varargs...)
	ret0, _ := ret[0].(*DeleteChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteChatBackground indicates an expected call of DeleteChatBackground.
func (mr *MockAigcIntimacyClientMockRecorder) DeleteChatBackground(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChatBackground", reflect.TypeOf((*MockAigcIntimacyClient)(nil).DeleteChatBackground), varargs...)
}

// DeleteIntimacyCondition mocks base method.
func (m *MockAigcIntimacyClient) DeleteIntimacyCondition(ctx context.Context, in *DeleteIntimacyConditionRequest, opts ...grpc.CallOption) (*DeleteIntimacyConditionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteIntimacyCondition", varargs...)
	ret0, _ := ret[0].(*DeleteIntimacyConditionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteIntimacyCondition indicates an expected call of DeleteIntimacyCondition.
func (mr *MockAigcIntimacyClientMockRecorder) DeleteIntimacyCondition(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIntimacyCondition", reflect.TypeOf((*MockAigcIntimacyClient)(nil).DeleteIntimacyCondition), varargs...)
}

// DeleteIntimacyLevelConf mocks base method.
func (m *MockAigcIntimacyClient) DeleteIntimacyLevelConf(ctx context.Context, in *DeleteIntimacyLevelConfRequest, opts ...grpc.CallOption) (*DeleteIntimacyLevelConfResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteIntimacyLevelConf", varargs...)
	ret0, _ := ret[0].(*DeleteIntimacyLevelConfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteIntimacyLevelConf indicates an expected call of DeleteIntimacyLevelConf.
func (mr *MockAigcIntimacyClientMockRecorder) DeleteIntimacyLevelConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIntimacyLevelConf", reflect.TypeOf((*MockAigcIntimacyClient)(nil).DeleteIntimacyLevelConf), varargs...)
}

// DeleteRelationship mocks base method.
func (m *MockAigcIntimacyClient) DeleteRelationship(ctx context.Context, in *DeleteRelationshipRequest, opts ...grpc.CallOption) (*DeleteRelationshipResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteRelationship", varargs...)
	ret0, _ := ret[0].(*DeleteRelationshipResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRelationship indicates an expected call of DeleteRelationship.
func (mr *MockAigcIntimacyClientMockRecorder) DeleteRelationship(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRelationship", reflect.TypeOf((*MockAigcIntimacyClient)(nil).DeleteRelationship), varargs...)
}

// GenerateChatBackground mocks base method.
func (m *MockAigcIntimacyClient) GenerateChatBackground(ctx context.Context, in *GenerateChatBackgroundRequest, opts ...grpc.CallOption) (*GenerateChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateChatBackground", varargs...)
	ret0, _ := ret[0].(*GenerateChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateChatBackground indicates an expected call of GenerateChatBackground.
func (mr *MockAigcIntimacyClientMockRecorder) GenerateChatBackground(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateChatBackground", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GenerateChatBackground), varargs...)
}

// GetAvailableBackground mocks base method.
func (m *MockAigcIntimacyClient) GetAvailableBackground(ctx context.Context, in *GetAvailableBackgroundRequest, opts ...grpc.CallOption) (*GetAvailableBackgroundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAvailableBackground", varargs...)
	ret0, _ := ret[0].(*GetAvailableBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableBackground indicates an expected call of GetAvailableBackground.
func (mr *MockAigcIntimacyClientMockRecorder) GetAvailableBackground(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableBackground", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetAvailableBackground), varargs...)
}

// GetBenefitConfig mocks base method.
func (m *MockAigcIntimacyClient) GetBenefitConfig(ctx context.Context, in *GetBenefitConfigRequest, opts ...grpc.CallOption) (*GetBenefitConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBenefitConfig", varargs...)
	ret0, _ := ret[0].(*GetBenefitConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBenefitConfig indicates an expected call of GetBenefitConfig.
func (mr *MockAigcIntimacyClientMockRecorder) GetBenefitConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBenefitConfig", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetBenefitConfig), varargs...)
}

// GetBindBackground mocks base method.
func (m *MockAigcIntimacyClient) GetBindBackground(ctx context.Context, in *GetBindBackgroundRequest, opts ...grpc.CallOption) (*GetBindBackgroundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBindBackground", varargs...)
	ret0, _ := ret[0].(*GetBindBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindBackground indicates an expected call of GetBindBackground.
func (mr *MockAigcIntimacyClientMockRecorder) GetBindBackground(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindBackground", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetBindBackground), varargs...)
}

// GetBindChatBackground mocks base method.
func (m *MockAigcIntimacyClient) GetBindChatBackground(ctx context.Context, in *GetBindChatBackgroundRequest, opts ...grpc.CallOption) (*GetBindChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBindChatBackground", varargs...)
	ret0, _ := ret[0].(*GetBindChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindChatBackground indicates an expected call of GetBindChatBackground.
func (mr *MockAigcIntimacyClientMockRecorder) GetBindChatBackground(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindChatBackground", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetBindChatBackground), varargs...)
}

// GetChatBackgroundById mocks base method.
func (m *MockAigcIntimacyClient) GetChatBackgroundById(ctx context.Context, in *GetChatBackgroundByIdRequest, opts ...grpc.CallOption) (*GetChatBackgroundByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChatBackgroundById", varargs...)
	ret0, _ := ret[0].(*GetChatBackgroundByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChatBackgroundById indicates an expected call of GetChatBackgroundById.
func (mr *MockAigcIntimacyClientMockRecorder) GetChatBackgroundById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChatBackgroundById", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetChatBackgroundById), varargs...)
}

// GetIntimacyConditionList mocks base method.
func (m *MockAigcIntimacyClient) GetIntimacyConditionList(ctx context.Context, in *GetIntimacyConditionListRequest, opts ...grpc.CallOption) (*GetIntimacyConditionListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIntimacyConditionList", varargs...)
	ret0, _ := ret[0].(*GetIntimacyConditionListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIntimacyConditionList indicates an expected call of GetIntimacyConditionList.
func (mr *MockAigcIntimacyClientMockRecorder) GetIntimacyConditionList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIntimacyConditionList", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetIntimacyConditionList), varargs...)
}

// GetIntimacyLevelConfList mocks base method.
func (m *MockAigcIntimacyClient) GetIntimacyLevelConfList(ctx context.Context, in *GetIntimacyLevelConfListRequest, opts ...grpc.CallOption) (*GetIntimacyLevelConfListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIntimacyLevelConfList", varargs...)
	ret0, _ := ret[0].(*GetIntimacyLevelConfListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIntimacyLevelConfList indicates an expected call of GetIntimacyLevelConfList.
func (mr *MockAigcIntimacyClientMockRecorder) GetIntimacyLevelConfList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIntimacyLevelConfList", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetIntimacyLevelConfList), varargs...)
}

// GetLevel mocks base method.
func (m *MockAigcIntimacyClient) GetLevel(ctx context.Context, in *GetLevelRequest, opts ...grpc.CallOption) (*GetLevelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLevel", varargs...)
	ret0, _ := ret[0].(*GetLevelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevel indicates an expected call of GetLevel.
func (mr *MockAigcIntimacyClientMockRecorder) GetLevel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevel", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetLevel), varargs...)
}

// GetLevelUpConditionProgress mocks base method.
func (m *MockAigcIntimacyClient) GetLevelUpConditionProgress(ctx context.Context, in *GetLevelUpConditionProgressRequest, opts ...grpc.CallOption) (*GetLevelUpConditionProgressResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLevelUpConditionProgress", varargs...)
	ret0, _ := ret[0].(*GetLevelUpConditionProgressResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelUpConditionProgress indicates an expected call of GetLevelUpConditionProgress.
func (mr *MockAigcIntimacyClientMockRecorder) GetLevelUpConditionProgress(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelUpConditionProgress", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetLevelUpConditionProgress), varargs...)
}

// GetPageChatBackground mocks base method.
func (m *MockAigcIntimacyClient) GetPageChatBackground(ctx context.Context, in *GetPageChatBackgroundRequest, opts ...grpc.CallOption) (*GetPageChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPageChatBackground", varargs...)
	ret0, _ := ret[0].(*GetPageChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPageChatBackground indicates an expected call of GetPageChatBackground.
func (mr *MockAigcIntimacyClientMockRecorder) GetPageChatBackground(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPageChatBackground", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetPageChatBackground), varargs...)
}

// GetReadHeartCount mocks base method.
func (m *MockAigcIntimacyClient) GetReadHeartCount(ctx context.Context, in *GetReadHeartCountRequest, opts ...grpc.CallOption) (*GetReadHeartCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReadHeartCount", varargs...)
	ret0, _ := ret[0].(*GetReadHeartCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReadHeartCount indicates an expected call of GetReadHeartCount.
func (mr *MockAigcIntimacyClientMockRecorder) GetReadHeartCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReadHeartCount", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetReadHeartCount), varargs...)
}

// GetRelationsByEntity mocks base method.
func (m *MockAigcIntimacyClient) GetRelationsByEntity(ctx context.Context, in *GetRelationsByEntityRequest, opts ...grpc.CallOption) (*GetRelationsByEntityResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRelationsByEntity", varargs...)
	ret0, _ := ret[0].(*GetRelationsByEntityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationsByEntity indicates an expected call of GetRelationsByEntity.
func (mr *MockAigcIntimacyClientMockRecorder) GetRelationsByEntity(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationsByEntity", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetRelationsByEntity), varargs...)
}

// GetRelationshipList mocks base method.
func (m *MockAigcIntimacyClient) GetRelationshipList(ctx context.Context, in *GetRelationshipListRequest, opts ...grpc.CallOption) (*GetRelationshipListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRelationshipList", varargs...)
	ret0, _ := ret[0].(*GetRelationshipListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationshipList indicates an expected call of GetRelationshipList.
func (mr *MockAigcIntimacyClientMockRecorder) GetRelationshipList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationshipList", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GetRelationshipList), varargs...)
}

// GrantChatBackground mocks base method.
func (m *MockAigcIntimacyClient) GrantChatBackground(ctx context.Context, in *GrantChatBackgroundRequest, opts ...grpc.CallOption) (*GrantChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GrantChatBackground", varargs...)
	ret0, _ := ret[0].(*GrantChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GrantChatBackground indicates an expected call of GrantChatBackground.
func (mr *MockAigcIntimacyClientMockRecorder) GrantChatBackground(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrantChatBackground", reflect.TypeOf((*MockAigcIntimacyClient)(nil).GrantChatBackground), varargs...)
}

// SwitchChatBackground mocks base method.
func (m *MockAigcIntimacyClient) SwitchChatBackground(ctx context.Context, in *SwitchChatBackgroundRequest, opts ...grpc.CallOption) (*SwitchChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SwitchChatBackground", varargs...)
	ret0, _ := ret[0].(*SwitchChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwitchChatBackground indicates an expected call of SwitchChatBackground.
func (mr *MockAigcIntimacyClientMockRecorder) SwitchChatBackground(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchChatBackground", reflect.TypeOf((*MockAigcIntimacyClient)(nil).SwitchChatBackground), varargs...)
}

// SwitchRelation mocks base method.
func (m *MockAigcIntimacyClient) SwitchRelation(ctx context.Context, in *SwitchRelationRequest, opts ...grpc.CallOption) (*SwitchRelationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SwitchRelation", varargs...)
	ret0, _ := ret[0].(*SwitchRelationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwitchRelation indicates an expected call of SwitchRelation.
func (mr *MockAigcIntimacyClientMockRecorder) SwitchRelation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchRelation", reflect.TypeOf((*MockAigcIntimacyClient)(nil).SwitchRelation), varargs...)
}

// UpdateChatBackground mocks base method.
func (m *MockAigcIntimacyClient) UpdateChatBackground(ctx context.Context, in *UpdateChatBackgroundRequest, opts ...grpc.CallOption) (*UpdateChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateChatBackground", varargs...)
	ret0, _ := ret[0].(*UpdateChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateChatBackground indicates an expected call of UpdateChatBackground.
func (mr *MockAigcIntimacyClientMockRecorder) UpdateChatBackground(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChatBackground", reflect.TypeOf((*MockAigcIntimacyClient)(nil).UpdateChatBackground), varargs...)
}

// UpdateIntimacyCondition mocks base method.
func (m *MockAigcIntimacyClient) UpdateIntimacyCondition(ctx context.Context, in *UpdateIntimacyConditionRequest, opts ...grpc.CallOption) (*UpdateIntimacyConditionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateIntimacyCondition", varargs...)
	ret0, _ := ret[0].(*UpdateIntimacyConditionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateIntimacyCondition indicates an expected call of UpdateIntimacyCondition.
func (mr *MockAigcIntimacyClientMockRecorder) UpdateIntimacyCondition(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateIntimacyCondition", reflect.TypeOf((*MockAigcIntimacyClient)(nil).UpdateIntimacyCondition), varargs...)
}

// UpdateIntimacyLevelConf mocks base method.
func (m *MockAigcIntimacyClient) UpdateIntimacyLevelConf(ctx context.Context, in *UpdateIntimacyLevelConfRequest, opts ...grpc.CallOption) (*UpdateIntimacyLevelConfResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateIntimacyLevelConf", varargs...)
	ret0, _ := ret[0].(*UpdateIntimacyLevelConfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateIntimacyLevelConf indicates an expected call of UpdateIntimacyLevelConf.
func (mr *MockAigcIntimacyClientMockRecorder) UpdateIntimacyLevelConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateIntimacyLevelConf", reflect.TypeOf((*MockAigcIntimacyClient)(nil).UpdateIntimacyLevelConf), varargs...)
}

// UpdateRelationship mocks base method.
func (m *MockAigcIntimacyClient) UpdateRelationship(ctx context.Context, in *UpdateRelationshipRequest, opts ...grpc.CallOption) (*UpdateRelationshipResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRelationship", varargs...)
	ret0, _ := ret[0].(*UpdateRelationshipResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRelationship indicates an expected call of UpdateRelationship.
func (mr *MockAigcIntimacyClientMockRecorder) UpdateRelationship(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRelationship", reflect.TypeOf((*MockAigcIntimacyClient)(nil).UpdateRelationship), varargs...)
}

// MockAigcIntimacyServer is a mock of AigcIntimacyServer interface.
type MockAigcIntimacyServer struct {
	ctrl     *gomock.Controller
	recorder *MockAigcIntimacyServerMockRecorder
}

// MockAigcIntimacyServerMockRecorder is the mock recorder for MockAigcIntimacyServer.
type MockAigcIntimacyServerMockRecorder struct {
	mock *MockAigcIntimacyServer
}

// NewMockAigcIntimacyServer creates a new mock instance.
func NewMockAigcIntimacyServer(ctrl *gomock.Controller) *MockAigcIntimacyServer {
	mock := &MockAigcIntimacyServer{ctrl: ctrl}
	mock.recorder = &MockAigcIntimacyServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcIntimacyServer) EXPECT() *MockAigcIntimacyServerMockRecorder {
	return m.recorder
}

// BatchGetCurRelation mocks base method.
func (m *MockAigcIntimacyServer) BatchGetCurRelation(ctx context.Context, in *BatchGetCurRelationRequest) (*BatchGetCurRelationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetCurRelation", ctx, in)
	ret0, _ := ret[0].(*BatchGetCurRelationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCurRelation indicates an expected call of BatchGetCurRelation.
func (mr *MockAigcIntimacyServerMockRecorder) BatchGetCurRelation(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCurRelation", reflect.TypeOf((*MockAigcIntimacyServer)(nil).BatchGetCurRelation), ctx, in)
}

// CreateChatBackground mocks base method.
func (m *MockAigcIntimacyServer) CreateChatBackground(ctx context.Context, in *CreateChatBackgroundRequest) (*CreateChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChatBackground", ctx, in)
	ret0, _ := ret[0].(*CreateChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChatBackground indicates an expected call of CreateChatBackground.
func (mr *MockAigcIntimacyServerMockRecorder) CreateChatBackground(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChatBackground", reflect.TypeOf((*MockAigcIntimacyServer)(nil).CreateChatBackground), ctx, in)
}

// CreateIntimacyCondition mocks base method.
func (m *MockAigcIntimacyServer) CreateIntimacyCondition(ctx context.Context, in *CreateIntimacyConditionRequest) (*CreateIntimacyConditionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIntimacyCondition", ctx, in)
	ret0, _ := ret[0].(*CreateIntimacyConditionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIntimacyCondition indicates an expected call of CreateIntimacyCondition.
func (mr *MockAigcIntimacyServerMockRecorder) CreateIntimacyCondition(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIntimacyCondition", reflect.TypeOf((*MockAigcIntimacyServer)(nil).CreateIntimacyCondition), ctx, in)
}

// CreateIntimacyLevelConf mocks base method.
func (m *MockAigcIntimacyServer) CreateIntimacyLevelConf(ctx context.Context, in *CreateIntimacyLevelConfRequest) (*CreateIntimacyLevelConfResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIntimacyLevelConf", ctx, in)
	ret0, _ := ret[0].(*CreateIntimacyLevelConfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIntimacyLevelConf indicates an expected call of CreateIntimacyLevelConf.
func (mr *MockAigcIntimacyServerMockRecorder) CreateIntimacyLevelConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIntimacyLevelConf", reflect.TypeOf((*MockAigcIntimacyServer)(nil).CreateIntimacyLevelConf), ctx, in)
}

// CreateRelationship mocks base method.
func (m *MockAigcIntimacyServer) CreateRelationship(ctx context.Context, in *CreateRelationshipRequest) (*CreateRelationshipResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRelationship", ctx, in)
	ret0, _ := ret[0].(*CreateRelationshipResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRelationship indicates an expected call of CreateRelationship.
func (mr *MockAigcIntimacyServerMockRecorder) CreateRelationship(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRelationship", reflect.TypeOf((*MockAigcIntimacyServer)(nil).CreateRelationship), ctx, in)
}

// DeleteChatBackground mocks base method.
func (m *MockAigcIntimacyServer) DeleteChatBackground(ctx context.Context, in *DeleteChatBackgroundRequest) (*DeleteChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteChatBackground", ctx, in)
	ret0, _ := ret[0].(*DeleteChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteChatBackground indicates an expected call of DeleteChatBackground.
func (mr *MockAigcIntimacyServerMockRecorder) DeleteChatBackground(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChatBackground", reflect.TypeOf((*MockAigcIntimacyServer)(nil).DeleteChatBackground), ctx, in)
}

// DeleteIntimacyCondition mocks base method.
func (m *MockAigcIntimacyServer) DeleteIntimacyCondition(ctx context.Context, in *DeleteIntimacyConditionRequest) (*DeleteIntimacyConditionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteIntimacyCondition", ctx, in)
	ret0, _ := ret[0].(*DeleteIntimacyConditionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteIntimacyCondition indicates an expected call of DeleteIntimacyCondition.
func (mr *MockAigcIntimacyServerMockRecorder) DeleteIntimacyCondition(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIntimacyCondition", reflect.TypeOf((*MockAigcIntimacyServer)(nil).DeleteIntimacyCondition), ctx, in)
}

// DeleteIntimacyLevelConf mocks base method.
func (m *MockAigcIntimacyServer) DeleteIntimacyLevelConf(ctx context.Context, in *DeleteIntimacyLevelConfRequest) (*DeleteIntimacyLevelConfResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteIntimacyLevelConf", ctx, in)
	ret0, _ := ret[0].(*DeleteIntimacyLevelConfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteIntimacyLevelConf indicates an expected call of DeleteIntimacyLevelConf.
func (mr *MockAigcIntimacyServerMockRecorder) DeleteIntimacyLevelConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIntimacyLevelConf", reflect.TypeOf((*MockAigcIntimacyServer)(nil).DeleteIntimacyLevelConf), ctx, in)
}

// DeleteRelationship mocks base method.
func (m *MockAigcIntimacyServer) DeleteRelationship(ctx context.Context, in *DeleteRelationshipRequest) (*DeleteRelationshipResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRelationship", ctx, in)
	ret0, _ := ret[0].(*DeleteRelationshipResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRelationship indicates an expected call of DeleteRelationship.
func (mr *MockAigcIntimacyServerMockRecorder) DeleteRelationship(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRelationship", reflect.TypeOf((*MockAigcIntimacyServer)(nil).DeleteRelationship), ctx, in)
}

// GenerateChatBackground mocks base method.
func (m *MockAigcIntimacyServer) GenerateChatBackground(ctx context.Context, in *GenerateChatBackgroundRequest) (*GenerateChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateChatBackground", ctx, in)
	ret0, _ := ret[0].(*GenerateChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateChatBackground indicates an expected call of GenerateChatBackground.
func (mr *MockAigcIntimacyServerMockRecorder) GenerateChatBackground(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateChatBackground", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GenerateChatBackground), ctx, in)
}

// GetAvailableBackground mocks base method.
func (m *MockAigcIntimacyServer) GetAvailableBackground(ctx context.Context, in *GetAvailableBackgroundRequest) (*GetAvailableBackgroundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvailableBackground", ctx, in)
	ret0, _ := ret[0].(*GetAvailableBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableBackground indicates an expected call of GetAvailableBackground.
func (mr *MockAigcIntimacyServerMockRecorder) GetAvailableBackground(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableBackground", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetAvailableBackground), ctx, in)
}

// GetBenefitConfig mocks base method.
func (m *MockAigcIntimacyServer) GetBenefitConfig(ctx context.Context, in *GetBenefitConfigRequest) (*GetBenefitConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBenefitConfig", ctx, in)
	ret0, _ := ret[0].(*GetBenefitConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBenefitConfig indicates an expected call of GetBenefitConfig.
func (mr *MockAigcIntimacyServerMockRecorder) GetBenefitConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBenefitConfig", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetBenefitConfig), ctx, in)
}

// GetBindBackground mocks base method.
func (m *MockAigcIntimacyServer) GetBindBackground(ctx context.Context, in *GetBindBackgroundRequest) (*GetBindBackgroundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindBackground", ctx, in)
	ret0, _ := ret[0].(*GetBindBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindBackground indicates an expected call of GetBindBackground.
func (mr *MockAigcIntimacyServerMockRecorder) GetBindBackground(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindBackground", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetBindBackground), ctx, in)
}

// GetBindChatBackground mocks base method.
func (m *MockAigcIntimacyServer) GetBindChatBackground(ctx context.Context, in *GetBindChatBackgroundRequest) (*GetBindChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindChatBackground", ctx, in)
	ret0, _ := ret[0].(*GetBindChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindChatBackground indicates an expected call of GetBindChatBackground.
func (mr *MockAigcIntimacyServerMockRecorder) GetBindChatBackground(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindChatBackground", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetBindChatBackground), ctx, in)
}

// GetChatBackgroundById mocks base method.
func (m *MockAigcIntimacyServer) GetChatBackgroundById(ctx context.Context, in *GetChatBackgroundByIdRequest) (*GetChatBackgroundByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChatBackgroundById", ctx, in)
	ret0, _ := ret[0].(*GetChatBackgroundByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChatBackgroundById indicates an expected call of GetChatBackgroundById.
func (mr *MockAigcIntimacyServerMockRecorder) GetChatBackgroundById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChatBackgroundById", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetChatBackgroundById), ctx, in)
}

// GetIntimacyConditionList mocks base method.
func (m *MockAigcIntimacyServer) GetIntimacyConditionList(ctx context.Context, in *GetIntimacyConditionListRequest) (*GetIntimacyConditionListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIntimacyConditionList", ctx, in)
	ret0, _ := ret[0].(*GetIntimacyConditionListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIntimacyConditionList indicates an expected call of GetIntimacyConditionList.
func (mr *MockAigcIntimacyServerMockRecorder) GetIntimacyConditionList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIntimacyConditionList", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetIntimacyConditionList), ctx, in)
}

// GetIntimacyLevelConfList mocks base method.
func (m *MockAigcIntimacyServer) GetIntimacyLevelConfList(ctx context.Context, in *GetIntimacyLevelConfListRequest) (*GetIntimacyLevelConfListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIntimacyLevelConfList", ctx, in)
	ret0, _ := ret[0].(*GetIntimacyLevelConfListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIntimacyLevelConfList indicates an expected call of GetIntimacyLevelConfList.
func (mr *MockAigcIntimacyServerMockRecorder) GetIntimacyLevelConfList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIntimacyLevelConfList", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetIntimacyLevelConfList), ctx, in)
}

// GetLevel mocks base method.
func (m *MockAigcIntimacyServer) GetLevel(ctx context.Context, in *GetLevelRequest) (*GetLevelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevel", ctx, in)
	ret0, _ := ret[0].(*GetLevelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevel indicates an expected call of GetLevel.
func (mr *MockAigcIntimacyServerMockRecorder) GetLevel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevel", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetLevel), ctx, in)
}

// GetLevelUpConditionProgress mocks base method.
func (m *MockAigcIntimacyServer) GetLevelUpConditionProgress(ctx context.Context, in *GetLevelUpConditionProgressRequest) (*GetLevelUpConditionProgressResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelUpConditionProgress", ctx, in)
	ret0, _ := ret[0].(*GetLevelUpConditionProgressResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelUpConditionProgress indicates an expected call of GetLevelUpConditionProgress.
func (mr *MockAigcIntimacyServerMockRecorder) GetLevelUpConditionProgress(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelUpConditionProgress", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetLevelUpConditionProgress), ctx, in)
}

// GetPageChatBackground mocks base method.
func (m *MockAigcIntimacyServer) GetPageChatBackground(ctx context.Context, in *GetPageChatBackgroundRequest) (*GetPageChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPageChatBackground", ctx, in)
	ret0, _ := ret[0].(*GetPageChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPageChatBackground indicates an expected call of GetPageChatBackground.
func (mr *MockAigcIntimacyServerMockRecorder) GetPageChatBackground(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPageChatBackground", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetPageChatBackground), ctx, in)
}

// GetReadHeartCount mocks base method.
func (m *MockAigcIntimacyServer) GetReadHeartCount(ctx context.Context, in *GetReadHeartCountRequest) (*GetReadHeartCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReadHeartCount", ctx, in)
	ret0, _ := ret[0].(*GetReadHeartCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReadHeartCount indicates an expected call of GetReadHeartCount.
func (mr *MockAigcIntimacyServerMockRecorder) GetReadHeartCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReadHeartCount", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetReadHeartCount), ctx, in)
}

// GetRelationsByEntity mocks base method.
func (m *MockAigcIntimacyServer) GetRelationsByEntity(ctx context.Context, in *GetRelationsByEntityRequest) (*GetRelationsByEntityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationsByEntity", ctx, in)
	ret0, _ := ret[0].(*GetRelationsByEntityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationsByEntity indicates an expected call of GetRelationsByEntity.
func (mr *MockAigcIntimacyServerMockRecorder) GetRelationsByEntity(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationsByEntity", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetRelationsByEntity), ctx, in)
}

// GetRelationshipList mocks base method.
func (m *MockAigcIntimacyServer) GetRelationshipList(ctx context.Context, in *GetRelationshipListRequest) (*GetRelationshipListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationshipList", ctx, in)
	ret0, _ := ret[0].(*GetRelationshipListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationshipList indicates an expected call of GetRelationshipList.
func (mr *MockAigcIntimacyServerMockRecorder) GetRelationshipList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationshipList", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GetRelationshipList), ctx, in)
}

// GrantChatBackground mocks base method.
func (m *MockAigcIntimacyServer) GrantChatBackground(ctx context.Context, in *GrantChatBackgroundRequest) (*GrantChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GrantChatBackground", ctx, in)
	ret0, _ := ret[0].(*GrantChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GrantChatBackground indicates an expected call of GrantChatBackground.
func (mr *MockAigcIntimacyServerMockRecorder) GrantChatBackground(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrantChatBackground", reflect.TypeOf((*MockAigcIntimacyServer)(nil).GrantChatBackground), ctx, in)
}

// SwitchChatBackground mocks base method.
func (m *MockAigcIntimacyServer) SwitchChatBackground(ctx context.Context, in *SwitchChatBackgroundRequest) (*SwitchChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwitchChatBackground", ctx, in)
	ret0, _ := ret[0].(*SwitchChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwitchChatBackground indicates an expected call of SwitchChatBackground.
func (mr *MockAigcIntimacyServerMockRecorder) SwitchChatBackground(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchChatBackground", reflect.TypeOf((*MockAigcIntimacyServer)(nil).SwitchChatBackground), ctx, in)
}

// SwitchRelation mocks base method.
func (m *MockAigcIntimacyServer) SwitchRelation(ctx context.Context, in *SwitchRelationRequest) (*SwitchRelationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwitchRelation", ctx, in)
	ret0, _ := ret[0].(*SwitchRelationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwitchRelation indicates an expected call of SwitchRelation.
func (mr *MockAigcIntimacyServerMockRecorder) SwitchRelation(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchRelation", reflect.TypeOf((*MockAigcIntimacyServer)(nil).SwitchRelation), ctx, in)
}

// UpdateChatBackground mocks base method.
func (m *MockAigcIntimacyServer) UpdateChatBackground(ctx context.Context, in *UpdateChatBackgroundRequest) (*UpdateChatBackgroundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChatBackground", ctx, in)
	ret0, _ := ret[0].(*UpdateChatBackgroundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateChatBackground indicates an expected call of UpdateChatBackground.
func (mr *MockAigcIntimacyServerMockRecorder) UpdateChatBackground(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChatBackground", reflect.TypeOf((*MockAigcIntimacyServer)(nil).UpdateChatBackground), ctx, in)
}

// UpdateIntimacyCondition mocks base method.
func (m *MockAigcIntimacyServer) UpdateIntimacyCondition(ctx context.Context, in *UpdateIntimacyConditionRequest) (*UpdateIntimacyConditionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateIntimacyCondition", ctx, in)
	ret0, _ := ret[0].(*UpdateIntimacyConditionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateIntimacyCondition indicates an expected call of UpdateIntimacyCondition.
func (mr *MockAigcIntimacyServerMockRecorder) UpdateIntimacyCondition(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateIntimacyCondition", reflect.TypeOf((*MockAigcIntimacyServer)(nil).UpdateIntimacyCondition), ctx, in)
}

// UpdateIntimacyLevelConf mocks base method.
func (m *MockAigcIntimacyServer) UpdateIntimacyLevelConf(ctx context.Context, in *UpdateIntimacyLevelConfRequest) (*UpdateIntimacyLevelConfResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateIntimacyLevelConf", ctx, in)
	ret0, _ := ret[0].(*UpdateIntimacyLevelConfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateIntimacyLevelConf indicates an expected call of UpdateIntimacyLevelConf.
func (mr *MockAigcIntimacyServerMockRecorder) UpdateIntimacyLevelConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateIntimacyLevelConf", reflect.TypeOf((*MockAigcIntimacyServer)(nil).UpdateIntimacyLevelConf), ctx, in)
}

// UpdateRelationship mocks base method.
func (m *MockAigcIntimacyServer) UpdateRelationship(ctx context.Context, in *UpdateRelationshipRequest) (*UpdateRelationshipResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRelationship", ctx, in)
	ret0, _ := ret[0].(*UpdateRelationshipResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRelationship indicates an expected call of UpdateRelationship.
func (mr *MockAigcIntimacyServerMockRecorder) UpdateRelationship(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRelationship", reflect.TypeOf((*MockAigcIntimacyServer)(nil).UpdateRelationship), ctx, in)
}
