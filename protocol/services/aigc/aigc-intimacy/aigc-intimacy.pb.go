// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-intimacy/aigc-intimacy.proto

package aigc_intimacy // import "golang.52tt.com/protocol/services/aigc/aigc-intimacy"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 聊天背景类型
type ChatBackgroundType int32

const (
	ChatBackgroundType_CHAT_BACKGROUND_TYPE_UNSPECIFIED ChatBackgroundType = 0
	// 亲密度
	ChatBackgroundType_CHAT_BACKGROUND_TYPE_INTIMACY ChatBackgroundType = 1
	// 送礼
	ChatBackgroundType_CHAT_BACKGROUND_TYPE_PRESENT ChatBackgroundType = 2
)

var ChatBackgroundType_name = map[int32]string{
	0: "CHAT_BACKGROUND_TYPE_UNSPECIFIED",
	1: "CHAT_BACKGROUND_TYPE_INTIMACY",
	2: "CHAT_BACKGROUND_TYPE_PRESENT",
}
var ChatBackgroundType_value = map[string]int32{
	"CHAT_BACKGROUND_TYPE_UNSPECIFIED": 0,
	"CHAT_BACKGROUND_TYPE_INTIMACY":    1,
	"CHAT_BACKGROUND_TYPE_PRESENT":     2,
}

func (x ChatBackgroundType) String() string {
	return proto.EnumName(ChatBackgroundType_name, int32(x))
}
func (ChatBackgroundType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{0}
}

type ConditionType int32

const (
	ConditionType_CONDITION_TYPE_UNSPECIFIED ConditionType = 0
	// 聊天句数
	ConditionType_CONDITION_TYPE_CHAT_MSG_COUNT ConditionType = 1
	// 聊天天数
	ConditionType_CONDITION_TYPE_CHAT_DAY ConditionType = 2
)

var ConditionType_name = map[int32]string{
	0: "CONDITION_TYPE_UNSPECIFIED",
	1: "CONDITION_TYPE_CHAT_MSG_COUNT",
	2: "CONDITION_TYPE_CHAT_DAY",
}
var ConditionType_value = map[string]int32{
	"CONDITION_TYPE_UNSPECIFIED":    0,
	"CONDITION_TYPE_CHAT_MSG_COUNT": 1,
	"CONDITION_TYPE_CHAT_DAY":       2,
}

func (x ConditionType) String() string {
	return proto.EnumName(ConditionType_name, int32(x))
}
func (ConditionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{1}
}

type Entity_Type int32

const (
	Entity_TYPE_UNSPECIFIED  Entity_Type = 0
	Entity_TYPE_PARTNER      Entity_Type = 1
	Entity_TYPE_MUTI_GROUP   Entity_Type = 2
	Entity_TYPE_SINGLE_GROUP Entity_Type = 3
)

var Entity_Type_name = map[int32]string{
	0: "TYPE_UNSPECIFIED",
	1: "TYPE_PARTNER",
	2: "TYPE_MUTI_GROUP",
	3: "TYPE_SINGLE_GROUP",
}
var Entity_Type_value = map[string]int32{
	"TYPE_UNSPECIFIED":  0,
	"TYPE_PARTNER":      1,
	"TYPE_MUTI_GROUP":   2,
	"TYPE_SINGLE_GROUP": 3,
}

func (x Entity_Type) String() string {
	return proto.EnumName(Entity_Type_name, int32(x))
}
func (Entity_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{0, 0}
}

type ChatBackground_BindEntityType int32

const (
	ChatBackground_BIND_ENTITY_TYPE_UNSPECIFIED ChatBackground_BindEntityType = 0
	// 绑定所有角色
	ChatBackground_BIND_ENTITY_TYPE_ALL_ROLE ChatBackground_BindEntityType = 1
	// 绑定指定角色
	ChatBackground_BIND_ENTITY_TYPE_ROLE_LIST ChatBackground_BindEntityType = 2
)

var ChatBackground_BindEntityType_name = map[int32]string{
	0: "BIND_ENTITY_TYPE_UNSPECIFIED",
	1: "BIND_ENTITY_TYPE_ALL_ROLE",
	2: "BIND_ENTITY_TYPE_ROLE_LIST",
}
var ChatBackground_BindEntityType_value = map[string]int32{
	"BIND_ENTITY_TYPE_UNSPECIFIED": 0,
	"BIND_ENTITY_TYPE_ALL_ROLE":    1,
	"BIND_ENTITY_TYPE_ROLE_LIST":   2,
}

func (x ChatBackground_BindEntityType) String() string {
	return proto.EnumName(ChatBackground_BindEntityType_name, int32(x))
}
func (ChatBackground_BindEntityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{6, 0}
}

type BindEntity_EntityType int32

const (
	BindEntity_ENTITY_TYPE_UNSPECIFIED BindEntity_EntityType = 0
	BindEntity_ENTITY_TYPE_ROLE        BindEntity_EntityType = 1
)

var BindEntity_EntityType_name = map[int32]string{
	0: "ENTITY_TYPE_UNSPECIFIED",
	1: "ENTITY_TYPE_ROLE",
}
var BindEntity_EntityType_value = map[string]int32{
	"ENTITY_TYPE_UNSPECIFIED": 0,
	"ENTITY_TYPE_ROLE":        1,
}

func (x BindEntity_EntityType) String() string {
	return proto.EnumName(BindEntity_EntityType_name, int32(x))
}
func (BindEntity_EntityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{7, 0}
}

type SwitchChatBackgroundRequest_Op int32

const (
	SwitchChatBackgroundRequest_OP_UNSPECIFIED SwitchChatBackgroundRequest_Op = 0
	SwitchChatBackgroundRequest_OP_SWITCH      SwitchChatBackgroundRequest_Op = 1
	SwitchChatBackgroundRequest_OP_DEL         SwitchChatBackgroundRequest_Op = 2
)

var SwitchChatBackgroundRequest_Op_name = map[int32]string{
	0: "OP_UNSPECIFIED",
	1: "OP_SWITCH",
	2: "OP_DEL",
}
var SwitchChatBackgroundRequest_Op_value = map[string]int32{
	"OP_UNSPECIFIED": 0,
	"OP_SWITCH":      1,
	"OP_DEL":         2,
}

func (x SwitchChatBackgroundRequest_Op) String() string {
	return proto.EnumName(SwitchChatBackgroundRequest_Op_name, int32(x))
}
func (SwitchChatBackgroundRequest_Op) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{52, 0}
}

type SwitchRelationRequest_Op int32

const (
	SwitchRelationRequest_OP_UNSPECIFIED SwitchRelationRequest_Op = 0
	SwitchRelationRequest_OP_SWITCH      SwitchRelationRequest_Op = 1
	SwitchRelationRequest_OP_DEL         SwitchRelationRequest_Op = 2
)

var SwitchRelationRequest_Op_name = map[int32]string{
	0: "OP_UNSPECIFIED",
	1: "OP_SWITCH",
	2: "OP_DEL",
}
var SwitchRelationRequest_Op_value = map[string]int32{
	"OP_UNSPECIFIED": 0,
	"OP_SWITCH":      1,
	"OP_DEL":         2,
}

func (x SwitchRelationRequest_Op) String() string {
	return proto.EnumName(SwitchRelationRequest_Op_name, int32(x))
}
func (SwitchRelationRequest_Op) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{54, 0}
}

type Entity struct {
	// 亲密度主体ID e.g:partner_id
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 亲密度主体类型
	Type                 Entity_Type `protobuf:"varint,2,opt,name=type,proto3,enum=aigc_intimacy.Entity_Type" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Entity) Reset()         { *m = Entity{} }
func (m *Entity) String() string { return proto.CompactTextString(m) }
func (*Entity) ProtoMessage()    {}
func (*Entity) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{0}
}
func (m *Entity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Entity.Unmarshal(m, b)
}
func (m *Entity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Entity.Marshal(b, m, deterministic)
}
func (dst *Entity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Entity.Merge(dst, src)
}
func (m *Entity) XXX_Size() int {
	return xxx_messageInfo_Entity.Size(m)
}
func (m *Entity) XXX_DiscardUnknown() {
	xxx_messageInfo_Entity.DiscardUnknown(m)
}

var xxx_messageInfo_Entity proto.InternalMessageInfo

func (m *Entity) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Entity) GetType() Entity_Type {
	if m != nil {
		return m.Type
	}
	return Entity_TYPE_UNSPECIFIED
}

type LevelUpConditionProgress struct {
	// 关联的升级条件
	Condition *LevelUpConditionProgress_LevelUpCondition `protobuf:"bytes,1,opt,name=condition,proto3" json:"condition,omitempty"`
	// 完成条件增长的亲密值
	GrowthValue          uint32   `protobuf:"varint,2,opt,name=growth_value,json=growthValue,proto3" json:"growth_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelUpConditionProgress) Reset()         { *m = LevelUpConditionProgress{} }
func (m *LevelUpConditionProgress) String() string { return proto.CompactTextString(m) }
func (*LevelUpConditionProgress) ProtoMessage()    {}
func (*LevelUpConditionProgress) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{1}
}
func (m *LevelUpConditionProgress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelUpConditionProgress.Unmarshal(m, b)
}
func (m *LevelUpConditionProgress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelUpConditionProgress.Marshal(b, m, deterministic)
}
func (dst *LevelUpConditionProgress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelUpConditionProgress.Merge(dst, src)
}
func (m *LevelUpConditionProgress) XXX_Size() int {
	return xxx_messageInfo_LevelUpConditionProgress.Size(m)
}
func (m *LevelUpConditionProgress) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelUpConditionProgress.DiscardUnknown(m)
}

var xxx_messageInfo_LevelUpConditionProgress proto.InternalMessageInfo

func (m *LevelUpConditionProgress) GetCondition() *LevelUpConditionProgress_LevelUpCondition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (m *LevelUpConditionProgress) GetGrowthValue() uint32 {
	if m != nil {
		return m.GrowthValue
	}
	return 0
}

type LevelUpConditionProgress_LevelUpCondition struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Icon                 string   `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Title                string   `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelUpConditionProgress_LevelUpCondition) Reset() {
	*m = LevelUpConditionProgress_LevelUpCondition{}
}
func (m *LevelUpConditionProgress_LevelUpCondition) String() string { return proto.CompactTextString(m) }
func (*LevelUpConditionProgress_LevelUpCondition) ProtoMessage()    {}
func (*LevelUpConditionProgress_LevelUpCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{1, 0}
}
func (m *LevelUpConditionProgress_LevelUpCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelUpConditionProgress_LevelUpCondition.Unmarshal(m, b)
}
func (m *LevelUpConditionProgress_LevelUpCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelUpConditionProgress_LevelUpCondition.Marshal(b, m, deterministic)
}
func (dst *LevelUpConditionProgress_LevelUpCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelUpConditionProgress_LevelUpCondition.Merge(dst, src)
}
func (m *LevelUpConditionProgress_LevelUpCondition) XXX_Size() int {
	return xxx_messageInfo_LevelUpConditionProgress_LevelUpCondition.Size(m)
}
func (m *LevelUpConditionProgress_LevelUpCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelUpConditionProgress_LevelUpCondition.DiscardUnknown(m)
}

var xxx_messageInfo_LevelUpConditionProgress_LevelUpCondition proto.InternalMessageInfo

func (m *LevelUpConditionProgress_LevelUpCondition) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *LevelUpConditionProgress_LevelUpCondition) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *LevelUpConditionProgress_LevelUpCondition) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *LevelUpConditionProgress_LevelUpCondition) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type GetLevelRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Entity               *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLevelRequest) Reset()         { *m = GetLevelRequest{} }
func (m *GetLevelRequest) String() string { return proto.CompactTextString(m) }
func (*GetLevelRequest) ProtoMessage()    {}
func (*GetLevelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{2}
}
func (m *GetLevelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelRequest.Unmarshal(m, b)
}
func (m *GetLevelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelRequest.Marshal(b, m, deterministic)
}
func (dst *GetLevelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelRequest.Merge(dst, src)
}
func (m *GetLevelRequest) XXX_Size() int {
	return xxx_messageInfo_GetLevelRequest.Size(m)
}
func (m *GetLevelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelRequest proto.InternalMessageInfo

func (m *GetLevelRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLevelRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

type GetLevelResponse struct {
	// 当前亲密度等级
	Level uint32 `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	// 当前亲密度值
	Value uint32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	// 开始计算亲密度的时间
	StartedAt            int64    `protobuf:"varint,3,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLevelResponse) Reset()         { *m = GetLevelResponse{} }
func (m *GetLevelResponse) String() string { return proto.CompactTextString(m) }
func (*GetLevelResponse) ProtoMessage()    {}
func (*GetLevelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{3}
}
func (m *GetLevelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelResponse.Unmarshal(m, b)
}
func (m *GetLevelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelResponse.Marshal(b, m, deterministic)
}
func (dst *GetLevelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelResponse.Merge(dst, src)
}
func (m *GetLevelResponse) XXX_Size() int {
	return xxx_messageInfo_GetLevelResponse.Size(m)
}
func (m *GetLevelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelResponse proto.InternalMessageInfo

func (m *GetLevelResponse) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetLevelResponse) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *GetLevelResponse) GetStartedAt() int64 {
	if m != nil {
		return m.StartedAt
	}
	return 0
}

type GetLevelUpConditionProgressRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Entity               *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLevelUpConditionProgressRequest) Reset()         { *m = GetLevelUpConditionProgressRequest{} }
func (m *GetLevelUpConditionProgressRequest) String() string { return proto.CompactTextString(m) }
func (*GetLevelUpConditionProgressRequest) ProtoMessage()    {}
func (*GetLevelUpConditionProgressRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{4}
}
func (m *GetLevelUpConditionProgressRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelUpConditionProgressRequest.Unmarshal(m, b)
}
func (m *GetLevelUpConditionProgressRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelUpConditionProgressRequest.Marshal(b, m, deterministic)
}
func (dst *GetLevelUpConditionProgressRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelUpConditionProgressRequest.Merge(dst, src)
}
func (m *GetLevelUpConditionProgressRequest) XXX_Size() int {
	return xxx_messageInfo_GetLevelUpConditionProgressRequest.Size(m)
}
func (m *GetLevelUpConditionProgressRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelUpConditionProgressRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelUpConditionProgressRequest proto.InternalMessageInfo

func (m *GetLevelUpConditionProgressRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLevelUpConditionProgressRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

type GetLevelUpConditionProgressResponse struct {
	List []*LevelUpConditionProgress `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 今日增长的亲密度
	TodayGrowthValue     uint32   `protobuf:"varint,2,opt,name=today_growth_value,json=todayGrowthValue,proto3" json:"today_growth_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLevelUpConditionProgressResponse) Reset()         { *m = GetLevelUpConditionProgressResponse{} }
func (m *GetLevelUpConditionProgressResponse) String() string { return proto.CompactTextString(m) }
func (*GetLevelUpConditionProgressResponse) ProtoMessage()    {}
func (*GetLevelUpConditionProgressResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{5}
}
func (m *GetLevelUpConditionProgressResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelUpConditionProgressResponse.Unmarshal(m, b)
}
func (m *GetLevelUpConditionProgressResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelUpConditionProgressResponse.Marshal(b, m, deterministic)
}
func (dst *GetLevelUpConditionProgressResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelUpConditionProgressResponse.Merge(dst, src)
}
func (m *GetLevelUpConditionProgressResponse) XXX_Size() int {
	return xxx_messageInfo_GetLevelUpConditionProgressResponse.Size(m)
}
func (m *GetLevelUpConditionProgressResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelUpConditionProgressResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelUpConditionProgressResponse proto.InternalMessageInfo

func (m *GetLevelUpConditionProgressResponse) GetList() []*LevelUpConditionProgress {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetLevelUpConditionProgressResponse) GetTodayGrowthValue() uint32 {
	if m != nil {
		return m.TodayGrowthValue
	}
	return 0
}

type ChatBackground struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 背景名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 背景图链接
	ImageUrl string `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	// 是否展示头像
	ShowAvatar bool `protobuf:"varint,4,opt,name=show_avatar,json=showAvatar,proto3" json:"show_avatar,omitempty"`
	// 是否默认拥有背景
	DefaultUnlock bool `protobuf:"varint,5,opt,name=default_unlock,json=defaultUnlock,proto3" json:"default_unlock,omitempty"`
	// 外显解锁条件
	UnlockCondition string `protobuf:"bytes,6,opt,name=unlock_condition,json=unlockCondition,proto3" json:"unlock_condition,omitempty"`
	// 绑定的对象类型
	BindEntityType ChatBackground_BindEntityType `protobuf:"varint,7,opt,name=bind_entity_type,json=bindEntityType,proto3,enum=aigc_intimacy.ChatBackground_BindEntityType" json:"bind_entity_type,omitempty"`
	// 绑定的对象ID列表
	BindEntityIds []uint32 `protobuf:"varint,8,rep,packed,name=bind_entity_ids,json=bindEntityIds,proto3" json:"bind_entity_ids,omitempty"`
	// 背景类型
	Type                 ChatBackgroundType `protobuf:"varint,9,opt,name=type,proto3,enum=aigc_intimacy.ChatBackgroundType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ChatBackground) Reset()         { *m = ChatBackground{} }
func (m *ChatBackground) String() string { return proto.CompactTextString(m) }
func (*ChatBackground) ProtoMessage()    {}
func (*ChatBackground) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{6}
}
func (m *ChatBackground) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatBackground.Unmarshal(m, b)
}
func (m *ChatBackground) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatBackground.Marshal(b, m, deterministic)
}
func (dst *ChatBackground) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatBackground.Merge(dst, src)
}
func (m *ChatBackground) XXX_Size() int {
	return xxx_messageInfo_ChatBackground.Size(m)
}
func (m *ChatBackground) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatBackground.DiscardUnknown(m)
}

var xxx_messageInfo_ChatBackground proto.InternalMessageInfo

func (m *ChatBackground) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ChatBackground) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChatBackground) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *ChatBackground) GetShowAvatar() bool {
	if m != nil {
		return m.ShowAvatar
	}
	return false
}

func (m *ChatBackground) GetDefaultUnlock() bool {
	if m != nil {
		return m.DefaultUnlock
	}
	return false
}

func (m *ChatBackground) GetUnlockCondition() string {
	if m != nil {
		return m.UnlockCondition
	}
	return ""
}

func (m *ChatBackground) GetBindEntityType() ChatBackground_BindEntityType {
	if m != nil {
		return m.BindEntityType
	}
	return ChatBackground_BIND_ENTITY_TYPE_UNSPECIFIED
}

func (m *ChatBackground) GetBindEntityIds() []uint32 {
	if m != nil {
		return m.BindEntityIds
	}
	return nil
}

func (m *ChatBackground) GetType() ChatBackgroundType {
	if m != nil {
		return m.Type
	}
	return ChatBackgroundType_CHAT_BACKGROUND_TYPE_UNSPECIFIED
}

type BindEntity struct {
	Type                 BindEntity_EntityType `protobuf:"varint,1,opt,name=type,proto3,enum=aigc_intimacy.BindEntity_EntityType" json:"type,omitempty"`
	Id                   uint32                `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BindEntity) Reset()         { *m = BindEntity{} }
func (m *BindEntity) String() string { return proto.CompactTextString(m) }
func (*BindEntity) ProtoMessage()    {}
func (*BindEntity) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{7}
}
func (m *BindEntity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindEntity.Unmarshal(m, b)
}
func (m *BindEntity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindEntity.Marshal(b, m, deterministic)
}
func (dst *BindEntity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindEntity.Merge(dst, src)
}
func (m *BindEntity) XXX_Size() int {
	return xxx_messageInfo_BindEntity.Size(m)
}
func (m *BindEntity) XXX_DiscardUnknown() {
	xxx_messageInfo_BindEntity.DiscardUnknown(m)
}

var xxx_messageInfo_BindEntity proto.InternalMessageInfo

func (m *BindEntity) GetType() BindEntity_EntityType {
	if m != nil {
		return m.Type
	}
	return BindEntity_ENTITY_TYPE_UNSPECIFIED
}

func (m *BindEntity) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type CreateChatBackgroundRequest struct {
	Background           *ChatBackground `protobuf:"bytes,1,opt,name=background,proto3" json:"background,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CreateChatBackgroundRequest) Reset()         { *m = CreateChatBackgroundRequest{} }
func (m *CreateChatBackgroundRequest) String() string { return proto.CompactTextString(m) }
func (*CreateChatBackgroundRequest) ProtoMessage()    {}
func (*CreateChatBackgroundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{8}
}
func (m *CreateChatBackgroundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChatBackgroundRequest.Unmarshal(m, b)
}
func (m *CreateChatBackgroundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChatBackgroundRequest.Marshal(b, m, deterministic)
}
func (dst *CreateChatBackgroundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChatBackgroundRequest.Merge(dst, src)
}
func (m *CreateChatBackgroundRequest) XXX_Size() int {
	return xxx_messageInfo_CreateChatBackgroundRequest.Size(m)
}
func (m *CreateChatBackgroundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChatBackgroundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChatBackgroundRequest proto.InternalMessageInfo

func (m *CreateChatBackgroundRequest) GetBackground() *ChatBackground {
	if m != nil {
		return m.Background
	}
	return nil
}

type CreateChatBackgroundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChatBackgroundResponse) Reset()         { *m = CreateChatBackgroundResponse{} }
func (m *CreateChatBackgroundResponse) String() string { return proto.CompactTextString(m) }
func (*CreateChatBackgroundResponse) ProtoMessage()    {}
func (*CreateChatBackgroundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{9}
}
func (m *CreateChatBackgroundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChatBackgroundResponse.Unmarshal(m, b)
}
func (m *CreateChatBackgroundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChatBackgroundResponse.Marshal(b, m, deterministic)
}
func (dst *CreateChatBackgroundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChatBackgroundResponse.Merge(dst, src)
}
func (m *CreateChatBackgroundResponse) XXX_Size() int {
	return xxx_messageInfo_CreateChatBackgroundResponse.Size(m)
}
func (m *CreateChatBackgroundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChatBackgroundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChatBackgroundResponse proto.InternalMessageInfo

type UpdateChatBackgroundRequest struct {
	Background           *ChatBackground `protobuf:"bytes,1,opt,name=background,proto3" json:"background,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateChatBackgroundRequest) Reset()         { *m = UpdateChatBackgroundRequest{} }
func (m *UpdateChatBackgroundRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateChatBackgroundRequest) ProtoMessage()    {}
func (*UpdateChatBackgroundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{10}
}
func (m *UpdateChatBackgroundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChatBackgroundRequest.Unmarshal(m, b)
}
func (m *UpdateChatBackgroundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChatBackgroundRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateChatBackgroundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChatBackgroundRequest.Merge(dst, src)
}
func (m *UpdateChatBackgroundRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateChatBackgroundRequest.Size(m)
}
func (m *UpdateChatBackgroundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChatBackgroundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChatBackgroundRequest proto.InternalMessageInfo

func (m *UpdateChatBackgroundRequest) GetBackground() *ChatBackground {
	if m != nil {
		return m.Background
	}
	return nil
}

type UpdateChatBackgroundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChatBackgroundResponse) Reset()         { *m = UpdateChatBackgroundResponse{} }
func (m *UpdateChatBackgroundResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateChatBackgroundResponse) ProtoMessage()    {}
func (*UpdateChatBackgroundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{11}
}
func (m *UpdateChatBackgroundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChatBackgroundResponse.Unmarshal(m, b)
}
func (m *UpdateChatBackgroundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChatBackgroundResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateChatBackgroundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChatBackgroundResponse.Merge(dst, src)
}
func (m *UpdateChatBackgroundResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateChatBackgroundResponse.Size(m)
}
func (m *UpdateChatBackgroundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChatBackgroundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChatBackgroundResponse proto.InternalMessageInfo

type DeleteChatBackgroundRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteChatBackgroundRequest) Reset()         { *m = DeleteChatBackgroundRequest{} }
func (m *DeleteChatBackgroundRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteChatBackgroundRequest) ProtoMessage()    {}
func (*DeleteChatBackgroundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{12}
}
func (m *DeleteChatBackgroundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteChatBackgroundRequest.Unmarshal(m, b)
}
func (m *DeleteChatBackgroundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteChatBackgroundRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteChatBackgroundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteChatBackgroundRequest.Merge(dst, src)
}
func (m *DeleteChatBackgroundRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteChatBackgroundRequest.Size(m)
}
func (m *DeleteChatBackgroundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteChatBackgroundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteChatBackgroundRequest proto.InternalMessageInfo

func (m *DeleteChatBackgroundRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DeleteChatBackgroundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteChatBackgroundResponse) Reset()         { *m = DeleteChatBackgroundResponse{} }
func (m *DeleteChatBackgroundResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteChatBackgroundResponse) ProtoMessage()    {}
func (*DeleteChatBackgroundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{13}
}
func (m *DeleteChatBackgroundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteChatBackgroundResponse.Unmarshal(m, b)
}
func (m *DeleteChatBackgroundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteChatBackgroundResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteChatBackgroundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteChatBackgroundResponse.Merge(dst, src)
}
func (m *DeleteChatBackgroundResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteChatBackgroundResponse.Size(m)
}
func (m *DeleteChatBackgroundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteChatBackgroundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteChatBackgroundResponse proto.InternalMessageInfo

type GetChatBackgroundByIdRequest struct {
	IdList               []string `protobuf:"bytes,1,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChatBackgroundByIdRequest) Reset()         { *m = GetChatBackgroundByIdRequest{} }
func (m *GetChatBackgroundByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetChatBackgroundByIdRequest) ProtoMessage()    {}
func (*GetChatBackgroundByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{14}
}
func (m *GetChatBackgroundByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChatBackgroundByIdRequest.Unmarshal(m, b)
}
func (m *GetChatBackgroundByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChatBackgroundByIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetChatBackgroundByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChatBackgroundByIdRequest.Merge(dst, src)
}
func (m *GetChatBackgroundByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetChatBackgroundByIdRequest.Size(m)
}
func (m *GetChatBackgroundByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChatBackgroundByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChatBackgroundByIdRequest proto.InternalMessageInfo

func (m *GetChatBackgroundByIdRequest) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

type GetChatBackgroundByIdResponse struct {
	Backgrounds          []*ChatBackground `protobuf:"bytes,1,rep,name=backgrounds,proto3" json:"backgrounds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetChatBackgroundByIdResponse) Reset()         { *m = GetChatBackgroundByIdResponse{} }
func (m *GetChatBackgroundByIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetChatBackgroundByIdResponse) ProtoMessage()    {}
func (*GetChatBackgroundByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{15}
}
func (m *GetChatBackgroundByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChatBackgroundByIdResponse.Unmarshal(m, b)
}
func (m *GetChatBackgroundByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChatBackgroundByIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetChatBackgroundByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChatBackgroundByIdResponse.Merge(dst, src)
}
func (m *GetChatBackgroundByIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetChatBackgroundByIdResponse.Size(m)
}
func (m *GetChatBackgroundByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChatBackgroundByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChatBackgroundByIdResponse proto.InternalMessageInfo

func (m *GetChatBackgroundByIdResponse) GetBackgrounds() []*ChatBackground {
	if m != nil {
		return m.Backgrounds
	}
	return nil
}

type GetPageChatBackgroundRequest struct {
	Page                 uint32             `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32             `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	NeedCount            bool               `protobuf:"varint,3,opt,name=need_count,json=needCount,proto3" json:"need_count,omitempty"`
	Name                 string             `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Type                 ChatBackgroundType `protobuf:"varint,5,opt,name=type,proto3,enum=aigc_intimacy.ChatBackgroundType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetPageChatBackgroundRequest) Reset()         { *m = GetPageChatBackgroundRequest{} }
func (m *GetPageChatBackgroundRequest) String() string { return proto.CompactTextString(m) }
func (*GetPageChatBackgroundRequest) ProtoMessage()    {}
func (*GetPageChatBackgroundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{16}
}
func (m *GetPageChatBackgroundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPageChatBackgroundRequest.Unmarshal(m, b)
}
func (m *GetPageChatBackgroundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPageChatBackgroundRequest.Marshal(b, m, deterministic)
}
func (dst *GetPageChatBackgroundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPageChatBackgroundRequest.Merge(dst, src)
}
func (m *GetPageChatBackgroundRequest) XXX_Size() int {
	return xxx_messageInfo_GetPageChatBackgroundRequest.Size(m)
}
func (m *GetPageChatBackgroundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPageChatBackgroundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPageChatBackgroundRequest proto.InternalMessageInfo

func (m *GetPageChatBackgroundRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPageChatBackgroundRequest) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetPageChatBackgroundRequest) GetNeedCount() bool {
	if m != nil {
		return m.NeedCount
	}
	return false
}

func (m *GetPageChatBackgroundRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetPageChatBackgroundRequest) GetType() ChatBackgroundType {
	if m != nil {
		return m.Type
	}
	return ChatBackgroundType_CHAT_BACKGROUND_TYPE_UNSPECIFIED
}

type GetPageChatBackgroundResponse struct {
	Backgrounds          []*ChatBackground `protobuf:"bytes,1,rep,name=backgrounds,proto3" json:"backgrounds,omitempty"`
	Total                int64             `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPageChatBackgroundResponse) Reset()         { *m = GetPageChatBackgroundResponse{} }
func (m *GetPageChatBackgroundResponse) String() string { return proto.CompactTextString(m) }
func (*GetPageChatBackgroundResponse) ProtoMessage()    {}
func (*GetPageChatBackgroundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{17}
}
func (m *GetPageChatBackgroundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPageChatBackgroundResponse.Unmarshal(m, b)
}
func (m *GetPageChatBackgroundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPageChatBackgroundResponse.Marshal(b, m, deterministic)
}
func (dst *GetPageChatBackgroundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPageChatBackgroundResponse.Merge(dst, src)
}
func (m *GetPageChatBackgroundResponse) XXX_Size() int {
	return xxx_messageInfo_GetPageChatBackgroundResponse.Size(m)
}
func (m *GetPageChatBackgroundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPageChatBackgroundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPageChatBackgroundResponse proto.InternalMessageInfo

func (m *GetPageChatBackgroundResponse) GetBackgrounds() []*ChatBackground {
	if m != nil {
		return m.Backgrounds
	}
	return nil
}

func (m *GetPageChatBackgroundResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取角色绑定的聊天背景
type GetBindChatBackgroundRequest struct {
	Entity               *BindEntity        `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	Type                 ChatBackgroundType `protobuf:"varint,2,opt,name=type,proto3,enum=aigc_intimacy.ChatBackgroundType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetBindChatBackgroundRequest) Reset()         { *m = GetBindChatBackgroundRequest{} }
func (m *GetBindChatBackgroundRequest) String() string { return proto.CompactTextString(m) }
func (*GetBindChatBackgroundRequest) ProtoMessage()    {}
func (*GetBindChatBackgroundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{18}
}
func (m *GetBindChatBackgroundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindChatBackgroundRequest.Unmarshal(m, b)
}
func (m *GetBindChatBackgroundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindChatBackgroundRequest.Marshal(b, m, deterministic)
}
func (dst *GetBindChatBackgroundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindChatBackgroundRequest.Merge(dst, src)
}
func (m *GetBindChatBackgroundRequest) XXX_Size() int {
	return xxx_messageInfo_GetBindChatBackgroundRequest.Size(m)
}
func (m *GetBindChatBackgroundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindChatBackgroundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindChatBackgroundRequest proto.InternalMessageInfo

func (m *GetBindChatBackgroundRequest) GetEntity() *BindEntity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *GetBindChatBackgroundRequest) GetType() ChatBackgroundType {
	if m != nil {
		return m.Type
	}
	return ChatBackgroundType_CHAT_BACKGROUND_TYPE_UNSPECIFIED
}

type GetBindChatBackgroundResponse struct {
	Backgrounds          []*ChatBackground `protobuf:"bytes,1,rep,name=backgrounds,proto3" json:"backgrounds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetBindChatBackgroundResponse) Reset()         { *m = GetBindChatBackgroundResponse{} }
func (m *GetBindChatBackgroundResponse) String() string { return proto.CompactTextString(m) }
func (*GetBindChatBackgroundResponse) ProtoMessage()    {}
func (*GetBindChatBackgroundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{19}
}
func (m *GetBindChatBackgroundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindChatBackgroundResponse.Unmarshal(m, b)
}
func (m *GetBindChatBackgroundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindChatBackgroundResponse.Marshal(b, m, deterministic)
}
func (dst *GetBindChatBackgroundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindChatBackgroundResponse.Merge(dst, src)
}
func (m *GetBindChatBackgroundResponse) XXX_Size() int {
	return xxx_messageInfo_GetBindChatBackgroundResponse.Size(m)
}
func (m *GetBindChatBackgroundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindChatBackgroundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindChatBackgroundResponse proto.InternalMessageInfo

func (m *GetBindChatBackgroundResponse) GetBackgrounds() []*ChatBackground {
	if m != nil {
		return m.Backgrounds
	}
	return nil
}

type Relationship struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 关系名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 关系参数（传输模型理解）
	AigcParams string `protobuf:"bytes,3,opt,name=aigc_params,json=aigcParams,proto3" json:"aigc_params,omitempty"`
	// 关联的聊天背景ID
	BindBackgroundId string `protobuf:"bytes,4,opt,name=bind_background_id,json=bindBackgroundId,proto3" json:"bind_background_id,omitempty"`
	// 外显解锁条件
	UnlockCondition string `protobuf:"bytes,5,opt,name=unlock_condition,json=unlockCondition,proto3" json:"unlock_condition,omitempty"`
	// 外显icon
	Icon string `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`
	// 亲密度页面展示背景图
	IntimacyBackgroundImg string   `protobuf:"bytes,7,opt,name=intimacy_background_img,json=intimacyBackgroundImg,proto3" json:"intimacy_background_img,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *Relationship) Reset()         { *m = Relationship{} }
func (m *Relationship) String() string { return proto.CompactTextString(m) }
func (*Relationship) ProtoMessage()    {}
func (*Relationship) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{20}
}
func (m *Relationship) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Relationship.Unmarshal(m, b)
}
func (m *Relationship) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Relationship.Marshal(b, m, deterministic)
}
func (dst *Relationship) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Relationship.Merge(dst, src)
}
func (m *Relationship) XXX_Size() int {
	return xxx_messageInfo_Relationship.Size(m)
}
func (m *Relationship) XXX_DiscardUnknown() {
	xxx_messageInfo_Relationship.DiscardUnknown(m)
}

var xxx_messageInfo_Relationship proto.InternalMessageInfo

func (m *Relationship) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Relationship) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Relationship) GetAigcParams() string {
	if m != nil {
		return m.AigcParams
	}
	return ""
}

func (m *Relationship) GetBindBackgroundId() string {
	if m != nil {
		return m.BindBackgroundId
	}
	return ""
}

func (m *Relationship) GetUnlockCondition() string {
	if m != nil {
		return m.UnlockCondition
	}
	return ""
}

func (m *Relationship) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *Relationship) GetIntimacyBackgroundImg() string {
	if m != nil {
		return m.IntimacyBackgroundImg
	}
	return ""
}

type CreateRelationshipRequest struct {
	Relationship         *Relationship `protobuf:"bytes,1,opt,name=relationship,proto3" json:"relationship,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CreateRelationshipRequest) Reset()         { *m = CreateRelationshipRequest{} }
func (m *CreateRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*CreateRelationshipRequest) ProtoMessage()    {}
func (*CreateRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{21}
}
func (m *CreateRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRelationshipRequest.Unmarshal(m, b)
}
func (m *CreateRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *CreateRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRelationshipRequest.Merge(dst, src)
}
func (m *CreateRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_CreateRelationshipRequest.Size(m)
}
func (m *CreateRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRelationshipRequest proto.InternalMessageInfo

func (m *CreateRelationshipRequest) GetRelationship() *Relationship {
	if m != nil {
		return m.Relationship
	}
	return nil
}

type CreateRelationshipResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateRelationshipResponse) Reset()         { *m = CreateRelationshipResponse{} }
func (m *CreateRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*CreateRelationshipResponse) ProtoMessage()    {}
func (*CreateRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{22}
}
func (m *CreateRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRelationshipResponse.Unmarshal(m, b)
}
func (m *CreateRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *CreateRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRelationshipResponse.Merge(dst, src)
}
func (m *CreateRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_CreateRelationshipResponse.Size(m)
}
func (m *CreateRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRelationshipResponse proto.InternalMessageInfo

type UpdateRelationshipRequest struct {
	Relationship         *Relationship `protobuf:"bytes,1,opt,name=relationship,proto3" json:"relationship,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateRelationshipRequest) Reset()         { *m = UpdateRelationshipRequest{} }
func (m *UpdateRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateRelationshipRequest) ProtoMessage()    {}
func (*UpdateRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{23}
}
func (m *UpdateRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRelationshipRequest.Unmarshal(m, b)
}
func (m *UpdateRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRelationshipRequest.Merge(dst, src)
}
func (m *UpdateRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateRelationshipRequest.Size(m)
}
func (m *UpdateRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRelationshipRequest proto.InternalMessageInfo

func (m *UpdateRelationshipRequest) GetRelationship() *Relationship {
	if m != nil {
		return m.Relationship
	}
	return nil
}

type UpdateRelationshipResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRelationshipResponse) Reset()         { *m = UpdateRelationshipResponse{} }
func (m *UpdateRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateRelationshipResponse) ProtoMessage()    {}
func (*UpdateRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{24}
}
func (m *UpdateRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRelationshipResponse.Unmarshal(m, b)
}
func (m *UpdateRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRelationshipResponse.Merge(dst, src)
}
func (m *UpdateRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateRelationshipResponse.Size(m)
}
func (m *UpdateRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRelationshipResponse proto.InternalMessageInfo

type DeleteRelationshipRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRelationshipRequest) Reset()         { *m = DeleteRelationshipRequest{} }
func (m *DeleteRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteRelationshipRequest) ProtoMessage()    {}
func (*DeleteRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{25}
}
func (m *DeleteRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRelationshipRequest.Unmarshal(m, b)
}
func (m *DeleteRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRelationshipRequest.Merge(dst, src)
}
func (m *DeleteRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteRelationshipRequest.Size(m)
}
func (m *DeleteRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRelationshipRequest proto.InternalMessageInfo

func (m *DeleteRelationshipRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DeleteRelationshipResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRelationshipResponse) Reset()         { *m = DeleteRelationshipResponse{} }
func (m *DeleteRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteRelationshipResponse) ProtoMessage()    {}
func (*DeleteRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{26}
}
func (m *DeleteRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRelationshipResponse.Unmarshal(m, b)
}
func (m *DeleteRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRelationshipResponse.Merge(dst, src)
}
func (m *DeleteRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteRelationshipResponse.Size(m)
}
func (m *DeleteRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRelationshipResponse proto.InternalMessageInfo

type GetRelationshipListRequest struct {
	// 获取所有
	GetAll bool `protobuf:"varint,1,opt,name=get_all,json=getAll,proto3" json:"get_all,omitempty"`
	// 指定ID列表查询
	IdList []string `protobuf:"bytes,2,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	// 获取绑定的聊天背景
	GetBindInfo          bool     `protobuf:"varint,3,opt,name=get_bind_info,json=getBindInfo,proto3" json:"get_bind_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRelationshipListRequest) Reset()         { *m = GetRelationshipListRequest{} }
func (m *GetRelationshipListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRelationshipListRequest) ProtoMessage()    {}
func (*GetRelationshipListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{27}
}
func (m *GetRelationshipListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRelationshipListRequest.Unmarshal(m, b)
}
func (m *GetRelationshipListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRelationshipListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRelationshipListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRelationshipListRequest.Merge(dst, src)
}
func (m *GetRelationshipListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRelationshipListRequest.Size(m)
}
func (m *GetRelationshipListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRelationshipListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRelationshipListRequest proto.InternalMessageInfo

func (m *GetRelationshipListRequest) GetGetAll() bool {
	if m != nil {
		return m.GetAll
	}
	return false
}

func (m *GetRelationshipListRequest) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *GetRelationshipListRequest) GetGetBindInfo() bool {
	if m != nil {
		return m.GetBindInfo
	}
	return false
}

type GetRelationshipListResponse struct {
	Relationships        []*Relationship            `protobuf:"bytes,1,rep,name=relationships,proto3" json:"relationships,omitempty"`
	BackgroundInfoMap    map[string]*ChatBackground `protobuf:"bytes,2,rep,name=background_info_map,json=backgroundInfoMap,proto3" json:"background_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetRelationshipListResponse) Reset()         { *m = GetRelationshipListResponse{} }
func (m *GetRelationshipListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRelationshipListResponse) ProtoMessage()    {}
func (*GetRelationshipListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{28}
}
func (m *GetRelationshipListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRelationshipListResponse.Unmarshal(m, b)
}
func (m *GetRelationshipListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRelationshipListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRelationshipListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRelationshipListResponse.Merge(dst, src)
}
func (m *GetRelationshipListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRelationshipListResponse.Size(m)
}
func (m *GetRelationshipListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRelationshipListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRelationshipListResponse proto.InternalMessageInfo

func (m *GetRelationshipListResponse) GetRelationships() []*Relationship {
	if m != nil {
		return m.Relationships
	}
	return nil
}

func (m *GetRelationshipListResponse) GetBackgroundInfoMap() map[string]*ChatBackground {
	if m != nil {
		return m.BackgroundInfoMap
	}
	return nil
}

type LevelBenefits struct {
	// 角色额外句数
	ExtraSendMsgCount uint32 `protobuf:"varint,1,opt,name=extra_send_msg_count,json=extraSendMsgCount,proto3" json:"extra_send_msg_count,omitempty"`
	// 角色额外读心次数
	ExtraReadMindCount uint32 `protobuf:"varint,2,opt,name=extra_read_mind_count,json=extraReadMindCount,proto3" json:"extra_read_mind_count,omitempty"`
	// 解锁可绑定关系ID
	UnlockRelationships []string `protobuf:"bytes,3,rep,name=unlock_relationships,json=unlockRelationships,proto3" json:"unlock_relationships,omitempty"`
	// 解锁可使用聊天背景ID
	UnlockBackgrounds    []string `protobuf:"bytes,4,rep,name=unlock_backgrounds,json=unlockBackgrounds,proto3" json:"unlock_backgrounds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelBenefits) Reset()         { *m = LevelBenefits{} }
func (m *LevelBenefits) String() string { return proto.CompactTextString(m) }
func (*LevelBenefits) ProtoMessage()    {}
func (*LevelBenefits) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{29}
}
func (m *LevelBenefits) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelBenefits.Unmarshal(m, b)
}
func (m *LevelBenefits) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelBenefits.Marshal(b, m, deterministic)
}
func (dst *LevelBenefits) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelBenefits.Merge(dst, src)
}
func (m *LevelBenefits) XXX_Size() int {
	return xxx_messageInfo_LevelBenefits.Size(m)
}
func (m *LevelBenefits) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelBenefits.DiscardUnknown(m)
}

var xxx_messageInfo_LevelBenefits proto.InternalMessageInfo

func (m *LevelBenefits) GetExtraSendMsgCount() uint32 {
	if m != nil {
		return m.ExtraSendMsgCount
	}
	return 0
}

func (m *LevelBenefits) GetExtraReadMindCount() uint32 {
	if m != nil {
		return m.ExtraReadMindCount
	}
	return 0
}

func (m *LevelBenefits) GetUnlockRelationships() []string {
	if m != nil {
		return m.UnlockRelationships
	}
	return nil
}

func (m *LevelBenefits) GetUnlockBackgrounds() []string {
	if m != nil {
		return m.UnlockBackgrounds
	}
	return nil
}

type IntimacyLevelConf struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 等级
	Level uint32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	// 所需亲密度
	RequireValue uint32 `protobuf:"varint,3,opt,name=require_value,json=requireValue,proto3" json:"require_value,omitempty"`
	// 解锁权益
	Benefits *LevelBenefits `protobuf:"bytes,4,opt,name=benefits,proto3" json:"benefits,omitempty"`
	// 展示给用户的权益文案
	BenefitDesc          string   `protobuf:"bytes,5,opt,name=benefit_desc,json=benefitDesc,proto3" json:"benefit_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IntimacyLevelConf) Reset()         { *m = IntimacyLevelConf{} }
func (m *IntimacyLevelConf) String() string { return proto.CompactTextString(m) }
func (*IntimacyLevelConf) ProtoMessage()    {}
func (*IntimacyLevelConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{30}
}
func (m *IntimacyLevelConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IntimacyLevelConf.Unmarshal(m, b)
}
func (m *IntimacyLevelConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IntimacyLevelConf.Marshal(b, m, deterministic)
}
func (dst *IntimacyLevelConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IntimacyLevelConf.Merge(dst, src)
}
func (m *IntimacyLevelConf) XXX_Size() int {
	return xxx_messageInfo_IntimacyLevelConf.Size(m)
}
func (m *IntimacyLevelConf) XXX_DiscardUnknown() {
	xxx_messageInfo_IntimacyLevelConf.DiscardUnknown(m)
}

var xxx_messageInfo_IntimacyLevelConf proto.InternalMessageInfo

func (m *IntimacyLevelConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *IntimacyLevelConf) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *IntimacyLevelConf) GetRequireValue() uint32 {
	if m != nil {
		return m.RequireValue
	}
	return 0
}

func (m *IntimacyLevelConf) GetBenefits() *LevelBenefits {
	if m != nil {
		return m.Benefits
	}
	return nil
}

func (m *IntimacyLevelConf) GetBenefitDesc() string {
	if m != nil {
		return m.BenefitDesc
	}
	return ""
}

type CreateIntimacyLevelConfRequest struct {
	LevelConfig          *IntimacyLevelConf `protobuf:"bytes,1,opt,name=level_config,json=levelConfig,proto3" json:"level_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CreateIntimacyLevelConfRequest) Reset()         { *m = CreateIntimacyLevelConfRequest{} }
func (m *CreateIntimacyLevelConfRequest) String() string { return proto.CompactTextString(m) }
func (*CreateIntimacyLevelConfRequest) ProtoMessage()    {}
func (*CreateIntimacyLevelConfRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{31}
}
func (m *CreateIntimacyLevelConfRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateIntimacyLevelConfRequest.Unmarshal(m, b)
}
func (m *CreateIntimacyLevelConfRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateIntimacyLevelConfRequest.Marshal(b, m, deterministic)
}
func (dst *CreateIntimacyLevelConfRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateIntimacyLevelConfRequest.Merge(dst, src)
}
func (m *CreateIntimacyLevelConfRequest) XXX_Size() int {
	return xxx_messageInfo_CreateIntimacyLevelConfRequest.Size(m)
}
func (m *CreateIntimacyLevelConfRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateIntimacyLevelConfRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateIntimacyLevelConfRequest proto.InternalMessageInfo

func (m *CreateIntimacyLevelConfRequest) GetLevelConfig() *IntimacyLevelConf {
	if m != nil {
		return m.LevelConfig
	}
	return nil
}

type CreateIntimacyLevelConfResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateIntimacyLevelConfResponse) Reset()         { *m = CreateIntimacyLevelConfResponse{} }
func (m *CreateIntimacyLevelConfResponse) String() string { return proto.CompactTextString(m) }
func (*CreateIntimacyLevelConfResponse) ProtoMessage()    {}
func (*CreateIntimacyLevelConfResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{32}
}
func (m *CreateIntimacyLevelConfResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateIntimacyLevelConfResponse.Unmarshal(m, b)
}
func (m *CreateIntimacyLevelConfResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateIntimacyLevelConfResponse.Marshal(b, m, deterministic)
}
func (dst *CreateIntimacyLevelConfResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateIntimacyLevelConfResponse.Merge(dst, src)
}
func (m *CreateIntimacyLevelConfResponse) XXX_Size() int {
	return xxx_messageInfo_CreateIntimacyLevelConfResponse.Size(m)
}
func (m *CreateIntimacyLevelConfResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateIntimacyLevelConfResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateIntimacyLevelConfResponse proto.InternalMessageInfo

type UpdateIntimacyLevelConfRequest struct {
	LevelConfig          *IntimacyLevelConf `protobuf:"bytes,1,opt,name=level_config,json=levelConfig,proto3" json:"level_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateIntimacyLevelConfRequest) Reset()         { *m = UpdateIntimacyLevelConfRequest{} }
func (m *UpdateIntimacyLevelConfRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateIntimacyLevelConfRequest) ProtoMessage()    {}
func (*UpdateIntimacyLevelConfRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{33}
}
func (m *UpdateIntimacyLevelConfRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateIntimacyLevelConfRequest.Unmarshal(m, b)
}
func (m *UpdateIntimacyLevelConfRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateIntimacyLevelConfRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateIntimacyLevelConfRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateIntimacyLevelConfRequest.Merge(dst, src)
}
func (m *UpdateIntimacyLevelConfRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateIntimacyLevelConfRequest.Size(m)
}
func (m *UpdateIntimacyLevelConfRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateIntimacyLevelConfRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateIntimacyLevelConfRequest proto.InternalMessageInfo

func (m *UpdateIntimacyLevelConfRequest) GetLevelConfig() *IntimacyLevelConf {
	if m != nil {
		return m.LevelConfig
	}
	return nil
}

type UpdateIntimacyLevelConfResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateIntimacyLevelConfResponse) Reset()         { *m = UpdateIntimacyLevelConfResponse{} }
func (m *UpdateIntimacyLevelConfResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateIntimacyLevelConfResponse) ProtoMessage()    {}
func (*UpdateIntimacyLevelConfResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{34}
}
func (m *UpdateIntimacyLevelConfResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateIntimacyLevelConfResponse.Unmarshal(m, b)
}
func (m *UpdateIntimacyLevelConfResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateIntimacyLevelConfResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateIntimacyLevelConfResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateIntimacyLevelConfResponse.Merge(dst, src)
}
func (m *UpdateIntimacyLevelConfResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateIntimacyLevelConfResponse.Size(m)
}
func (m *UpdateIntimacyLevelConfResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateIntimacyLevelConfResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateIntimacyLevelConfResponse proto.InternalMessageInfo

type DeleteIntimacyLevelConfRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteIntimacyLevelConfRequest) Reset()         { *m = DeleteIntimacyLevelConfRequest{} }
func (m *DeleteIntimacyLevelConfRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteIntimacyLevelConfRequest) ProtoMessage()    {}
func (*DeleteIntimacyLevelConfRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{35}
}
func (m *DeleteIntimacyLevelConfRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteIntimacyLevelConfRequest.Unmarshal(m, b)
}
func (m *DeleteIntimacyLevelConfRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteIntimacyLevelConfRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteIntimacyLevelConfRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteIntimacyLevelConfRequest.Merge(dst, src)
}
func (m *DeleteIntimacyLevelConfRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteIntimacyLevelConfRequest.Size(m)
}
func (m *DeleteIntimacyLevelConfRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteIntimacyLevelConfRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteIntimacyLevelConfRequest proto.InternalMessageInfo

func (m *DeleteIntimacyLevelConfRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteIntimacyLevelConfResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteIntimacyLevelConfResponse) Reset()         { *m = DeleteIntimacyLevelConfResponse{} }
func (m *DeleteIntimacyLevelConfResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteIntimacyLevelConfResponse) ProtoMessage()    {}
func (*DeleteIntimacyLevelConfResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{36}
}
func (m *DeleteIntimacyLevelConfResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteIntimacyLevelConfResponse.Unmarshal(m, b)
}
func (m *DeleteIntimacyLevelConfResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteIntimacyLevelConfResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteIntimacyLevelConfResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteIntimacyLevelConfResponse.Merge(dst, src)
}
func (m *DeleteIntimacyLevelConfResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteIntimacyLevelConfResponse.Size(m)
}
func (m *DeleteIntimacyLevelConfResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteIntimacyLevelConfResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteIntimacyLevelConfResponse proto.InternalMessageInfo

type GetIntimacyLevelConfListRequest struct {
	// 获取所有
	GetAll bool `protobuf:"varint,1,opt,name=get_all,json=getAll,proto3" json:"get_all,omitempty"`
	// 指定ID列表查询
	IdList []uint32 `protobuf:"varint,2,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	// 获取绑定的关系和背景
	GetBindInfo          bool     `protobuf:"varint,3,opt,name=get_bind_info,json=getBindInfo,proto3" json:"get_bind_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIntimacyLevelConfListRequest) Reset()         { *m = GetIntimacyLevelConfListRequest{} }
func (m *GetIntimacyLevelConfListRequest) String() string { return proto.CompactTextString(m) }
func (*GetIntimacyLevelConfListRequest) ProtoMessage()    {}
func (*GetIntimacyLevelConfListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{37}
}
func (m *GetIntimacyLevelConfListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIntimacyLevelConfListRequest.Unmarshal(m, b)
}
func (m *GetIntimacyLevelConfListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIntimacyLevelConfListRequest.Marshal(b, m, deterministic)
}
func (dst *GetIntimacyLevelConfListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIntimacyLevelConfListRequest.Merge(dst, src)
}
func (m *GetIntimacyLevelConfListRequest) XXX_Size() int {
	return xxx_messageInfo_GetIntimacyLevelConfListRequest.Size(m)
}
func (m *GetIntimacyLevelConfListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIntimacyLevelConfListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetIntimacyLevelConfListRequest proto.InternalMessageInfo

func (m *GetIntimacyLevelConfListRequest) GetGetAll() bool {
	if m != nil {
		return m.GetAll
	}
	return false
}

func (m *GetIntimacyLevelConfListRequest) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *GetIntimacyLevelConfListRequest) GetGetBindInfo() bool {
	if m != nil {
		return m.GetBindInfo
	}
	return false
}

type GetIntimacyLevelConfListResponse struct {
	ConfigList           []*IntimacyLevelConf       `protobuf:"bytes,1,rep,name=config_list,json=configList,proto3" json:"config_list,omitempty"`
	BackgroundInfoMap    map[string]*ChatBackground `protobuf:"bytes,2,rep,name=background_info_map,json=backgroundInfoMap,proto3" json:"background_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	RelationshipInfoMap  map[string]*Relationship   `protobuf:"bytes,3,rep,name=relationship_info_map,json=relationshipInfoMap,proto3" json:"relationship_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetIntimacyLevelConfListResponse) Reset()         { *m = GetIntimacyLevelConfListResponse{} }
func (m *GetIntimacyLevelConfListResponse) String() string { return proto.CompactTextString(m) }
func (*GetIntimacyLevelConfListResponse) ProtoMessage()    {}
func (*GetIntimacyLevelConfListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{38}
}
func (m *GetIntimacyLevelConfListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIntimacyLevelConfListResponse.Unmarshal(m, b)
}
func (m *GetIntimacyLevelConfListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIntimacyLevelConfListResponse.Marshal(b, m, deterministic)
}
func (dst *GetIntimacyLevelConfListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIntimacyLevelConfListResponse.Merge(dst, src)
}
func (m *GetIntimacyLevelConfListResponse) XXX_Size() int {
	return xxx_messageInfo_GetIntimacyLevelConfListResponse.Size(m)
}
func (m *GetIntimacyLevelConfListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIntimacyLevelConfListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetIntimacyLevelConfListResponse proto.InternalMessageInfo

func (m *GetIntimacyLevelConfListResponse) GetConfigList() []*IntimacyLevelConf {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

func (m *GetIntimacyLevelConfListResponse) GetBackgroundInfoMap() map[string]*ChatBackground {
	if m != nil {
		return m.BackgroundInfoMap
	}
	return nil
}

func (m *GetIntimacyLevelConfListResponse) GetRelationshipInfoMap() map[string]*Relationship {
	if m != nil {
		return m.RelationshipInfoMap
	}
	return nil
}

type IntimacyCondition struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 条件类型
	ConditionType ConditionType `protobuf:"varint,2,opt,name=condition_type,json=conditionType,proto3,enum=aigc_intimacy.ConditionType" json:"condition_type,omitempty"`
	// 达成数量
	RequireCount uint32 `protobuf:"varint,3,opt,name=require_count,json=requireCount,proto3" json:"require_count,omitempty"`
	// 亲密度增加值
	ValueAdd uint32 `protobuf:"varint,4,opt,name=value_add,json=valueAdd,proto3" json:"value_add,omitempty"`
	// 每日达成次数上限
	DailyLimit uint32 `protobuf:"varint,5,opt,name=daily_limit,json=dailyLimit,proto3" json:"daily_limit,omitempty"`
	// 外显icon
	Icon string `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`
	// 外显标题
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	// 外显描述
	Desc                 string   `protobuf:"bytes,8,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IntimacyCondition) Reset()         { *m = IntimacyCondition{} }
func (m *IntimacyCondition) String() string { return proto.CompactTextString(m) }
func (*IntimacyCondition) ProtoMessage()    {}
func (*IntimacyCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{39}
}
func (m *IntimacyCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IntimacyCondition.Unmarshal(m, b)
}
func (m *IntimacyCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IntimacyCondition.Marshal(b, m, deterministic)
}
func (dst *IntimacyCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IntimacyCondition.Merge(dst, src)
}
func (m *IntimacyCondition) XXX_Size() int {
	return xxx_messageInfo_IntimacyCondition.Size(m)
}
func (m *IntimacyCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_IntimacyCondition.DiscardUnknown(m)
}

var xxx_messageInfo_IntimacyCondition proto.InternalMessageInfo

func (m *IntimacyCondition) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *IntimacyCondition) GetConditionType() ConditionType {
	if m != nil {
		return m.ConditionType
	}
	return ConditionType_CONDITION_TYPE_UNSPECIFIED
}

func (m *IntimacyCondition) GetRequireCount() uint32 {
	if m != nil {
		return m.RequireCount
	}
	return 0
}

func (m *IntimacyCondition) GetValueAdd() uint32 {
	if m != nil {
		return m.ValueAdd
	}
	return 0
}

func (m *IntimacyCondition) GetDailyLimit() uint32 {
	if m != nil {
		return m.DailyLimit
	}
	return 0
}

func (m *IntimacyCondition) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *IntimacyCondition) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *IntimacyCondition) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type CreateIntimacyConditionRequest struct {
	Condition            *IntimacyCondition `protobuf:"bytes,1,opt,name=condition,proto3" json:"condition,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CreateIntimacyConditionRequest) Reset()         { *m = CreateIntimacyConditionRequest{} }
func (m *CreateIntimacyConditionRequest) String() string { return proto.CompactTextString(m) }
func (*CreateIntimacyConditionRequest) ProtoMessage()    {}
func (*CreateIntimacyConditionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{40}
}
func (m *CreateIntimacyConditionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateIntimacyConditionRequest.Unmarshal(m, b)
}
func (m *CreateIntimacyConditionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateIntimacyConditionRequest.Marshal(b, m, deterministic)
}
func (dst *CreateIntimacyConditionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateIntimacyConditionRequest.Merge(dst, src)
}
func (m *CreateIntimacyConditionRequest) XXX_Size() int {
	return xxx_messageInfo_CreateIntimacyConditionRequest.Size(m)
}
func (m *CreateIntimacyConditionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateIntimacyConditionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateIntimacyConditionRequest proto.InternalMessageInfo

func (m *CreateIntimacyConditionRequest) GetCondition() *IntimacyCondition {
	if m != nil {
		return m.Condition
	}
	return nil
}

type CreateIntimacyConditionResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateIntimacyConditionResponse) Reset()         { *m = CreateIntimacyConditionResponse{} }
func (m *CreateIntimacyConditionResponse) String() string { return proto.CompactTextString(m) }
func (*CreateIntimacyConditionResponse) ProtoMessage()    {}
func (*CreateIntimacyConditionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{41}
}
func (m *CreateIntimacyConditionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateIntimacyConditionResponse.Unmarshal(m, b)
}
func (m *CreateIntimacyConditionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateIntimacyConditionResponse.Marshal(b, m, deterministic)
}
func (dst *CreateIntimacyConditionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateIntimacyConditionResponse.Merge(dst, src)
}
func (m *CreateIntimacyConditionResponse) XXX_Size() int {
	return xxx_messageInfo_CreateIntimacyConditionResponse.Size(m)
}
func (m *CreateIntimacyConditionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateIntimacyConditionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateIntimacyConditionResponse proto.InternalMessageInfo

type UpdateIntimacyConditionRequest struct {
	Condition            *IntimacyCondition `protobuf:"bytes,1,opt,name=condition,proto3" json:"condition,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateIntimacyConditionRequest) Reset()         { *m = UpdateIntimacyConditionRequest{} }
func (m *UpdateIntimacyConditionRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateIntimacyConditionRequest) ProtoMessage()    {}
func (*UpdateIntimacyConditionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{42}
}
func (m *UpdateIntimacyConditionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateIntimacyConditionRequest.Unmarshal(m, b)
}
func (m *UpdateIntimacyConditionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateIntimacyConditionRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateIntimacyConditionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateIntimacyConditionRequest.Merge(dst, src)
}
func (m *UpdateIntimacyConditionRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateIntimacyConditionRequest.Size(m)
}
func (m *UpdateIntimacyConditionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateIntimacyConditionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateIntimacyConditionRequest proto.InternalMessageInfo

func (m *UpdateIntimacyConditionRequest) GetCondition() *IntimacyCondition {
	if m != nil {
		return m.Condition
	}
	return nil
}

type UpdateIntimacyConditionResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateIntimacyConditionResponse) Reset()         { *m = UpdateIntimacyConditionResponse{} }
func (m *UpdateIntimacyConditionResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateIntimacyConditionResponse) ProtoMessage()    {}
func (*UpdateIntimacyConditionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{43}
}
func (m *UpdateIntimacyConditionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateIntimacyConditionResponse.Unmarshal(m, b)
}
func (m *UpdateIntimacyConditionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateIntimacyConditionResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateIntimacyConditionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateIntimacyConditionResponse.Merge(dst, src)
}
func (m *UpdateIntimacyConditionResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateIntimacyConditionResponse.Size(m)
}
func (m *UpdateIntimacyConditionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateIntimacyConditionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateIntimacyConditionResponse proto.InternalMessageInfo

type DeleteIntimacyConditionRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteIntimacyConditionRequest) Reset()         { *m = DeleteIntimacyConditionRequest{} }
func (m *DeleteIntimacyConditionRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteIntimacyConditionRequest) ProtoMessage()    {}
func (*DeleteIntimacyConditionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{44}
}
func (m *DeleteIntimacyConditionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteIntimacyConditionRequest.Unmarshal(m, b)
}
func (m *DeleteIntimacyConditionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteIntimacyConditionRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteIntimacyConditionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteIntimacyConditionRequest.Merge(dst, src)
}
func (m *DeleteIntimacyConditionRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteIntimacyConditionRequest.Size(m)
}
func (m *DeleteIntimacyConditionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteIntimacyConditionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteIntimacyConditionRequest proto.InternalMessageInfo

func (m *DeleteIntimacyConditionRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DeleteIntimacyConditionResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteIntimacyConditionResponse) Reset()         { *m = DeleteIntimacyConditionResponse{} }
func (m *DeleteIntimacyConditionResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteIntimacyConditionResponse) ProtoMessage()    {}
func (*DeleteIntimacyConditionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{45}
}
func (m *DeleteIntimacyConditionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteIntimacyConditionResponse.Unmarshal(m, b)
}
func (m *DeleteIntimacyConditionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteIntimacyConditionResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteIntimacyConditionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteIntimacyConditionResponse.Merge(dst, src)
}
func (m *DeleteIntimacyConditionResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteIntimacyConditionResponse.Size(m)
}
func (m *DeleteIntimacyConditionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteIntimacyConditionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteIntimacyConditionResponse proto.InternalMessageInfo

type GetIntimacyConditionListRequest struct {
	// 获取所有
	GetAll bool `protobuf:"varint,1,opt,name=get_all,json=getAll,proto3" json:"get_all,omitempty"`
	// 指定ID列表查询
	IdList               []string `protobuf:"bytes,2,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIntimacyConditionListRequest) Reset()         { *m = GetIntimacyConditionListRequest{} }
func (m *GetIntimacyConditionListRequest) String() string { return proto.CompactTextString(m) }
func (*GetIntimacyConditionListRequest) ProtoMessage()    {}
func (*GetIntimacyConditionListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{46}
}
func (m *GetIntimacyConditionListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIntimacyConditionListRequest.Unmarshal(m, b)
}
func (m *GetIntimacyConditionListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIntimacyConditionListRequest.Marshal(b, m, deterministic)
}
func (dst *GetIntimacyConditionListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIntimacyConditionListRequest.Merge(dst, src)
}
func (m *GetIntimacyConditionListRequest) XXX_Size() int {
	return xxx_messageInfo_GetIntimacyConditionListRequest.Size(m)
}
func (m *GetIntimacyConditionListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIntimacyConditionListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetIntimacyConditionListRequest proto.InternalMessageInfo

func (m *GetIntimacyConditionListRequest) GetGetAll() bool {
	if m != nil {
		return m.GetAll
	}
	return false
}

func (m *GetIntimacyConditionListRequest) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

type GetIntimacyConditionListResponse struct {
	Conditions           []*IntimacyCondition `protobuf:"bytes,1,rep,name=conditions,proto3" json:"conditions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetIntimacyConditionListResponse) Reset()         { *m = GetIntimacyConditionListResponse{} }
func (m *GetIntimacyConditionListResponse) String() string { return proto.CompactTextString(m) }
func (*GetIntimacyConditionListResponse) ProtoMessage()    {}
func (*GetIntimacyConditionListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{47}
}
func (m *GetIntimacyConditionListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIntimacyConditionListResponse.Unmarshal(m, b)
}
func (m *GetIntimacyConditionListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIntimacyConditionListResponse.Marshal(b, m, deterministic)
}
func (dst *GetIntimacyConditionListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIntimacyConditionListResponse.Merge(dst, src)
}
func (m *GetIntimacyConditionListResponse) XXX_Size() int {
	return xxx_messageInfo_GetIntimacyConditionListResponse.Size(m)
}
func (m *GetIntimacyConditionListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIntimacyConditionListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetIntimacyConditionListResponse proto.InternalMessageInfo

func (m *GetIntimacyConditionListResponse) GetConditions() []*IntimacyCondition {
	if m != nil {
		return m.Conditions
	}
	return nil
}

type GetBindBackgroundRequest struct {
	Entity               *Entity  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBindBackgroundRequest) Reset()         { *m = GetBindBackgroundRequest{} }
func (m *GetBindBackgroundRequest) String() string { return proto.CompactTextString(m) }
func (*GetBindBackgroundRequest) ProtoMessage()    {}
func (*GetBindBackgroundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{48}
}
func (m *GetBindBackgroundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindBackgroundRequest.Unmarshal(m, b)
}
func (m *GetBindBackgroundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindBackgroundRequest.Marshal(b, m, deterministic)
}
func (dst *GetBindBackgroundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindBackgroundRequest.Merge(dst, src)
}
func (m *GetBindBackgroundRequest) XXX_Size() int {
	return xxx_messageInfo_GetBindBackgroundRequest.Size(m)
}
func (m *GetBindBackgroundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindBackgroundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindBackgroundRequest proto.InternalMessageInfo

func (m *GetBindBackgroundRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

type GetBindBackgroundResponse struct {
	CurBackground        *ChatBackground `protobuf:"bytes,1,opt,name=cur_background,json=curBackground,proto3" json:"cur_background,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetBindBackgroundResponse) Reset()         { *m = GetBindBackgroundResponse{} }
func (m *GetBindBackgroundResponse) String() string { return proto.CompactTextString(m) }
func (*GetBindBackgroundResponse) ProtoMessage()    {}
func (*GetBindBackgroundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{49}
}
func (m *GetBindBackgroundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindBackgroundResponse.Unmarshal(m, b)
}
func (m *GetBindBackgroundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindBackgroundResponse.Marshal(b, m, deterministic)
}
func (dst *GetBindBackgroundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindBackgroundResponse.Merge(dst, src)
}
func (m *GetBindBackgroundResponse) XXX_Size() int {
	return xxx_messageInfo_GetBindBackgroundResponse.Size(m)
}
func (m *GetBindBackgroundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindBackgroundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindBackgroundResponse proto.InternalMessageInfo

func (m *GetBindBackgroundResponse) GetCurBackground() *ChatBackground {
	if m != nil {
		return m.CurBackground
	}
	return nil
}

type GetAvailableBackgroundRequest struct {
	Entity               *Entity     `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	BindEntity           *BindEntity `protobuf:"bytes,2,opt,name=bind_entity,json=bindEntity,proto3" json:"bind_entity,omitempty"`
	Uid                  uint32      `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetAvailableBackgroundRequest) Reset()         { *m = GetAvailableBackgroundRequest{} }
func (m *GetAvailableBackgroundRequest) String() string { return proto.CompactTextString(m) }
func (*GetAvailableBackgroundRequest) ProtoMessage()    {}
func (*GetAvailableBackgroundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{50}
}
func (m *GetAvailableBackgroundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvailableBackgroundRequest.Unmarshal(m, b)
}
func (m *GetAvailableBackgroundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvailableBackgroundRequest.Marshal(b, m, deterministic)
}
func (dst *GetAvailableBackgroundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvailableBackgroundRequest.Merge(dst, src)
}
func (m *GetAvailableBackgroundRequest) XXX_Size() int {
	return xxx_messageInfo_GetAvailableBackgroundRequest.Size(m)
}
func (m *GetAvailableBackgroundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvailableBackgroundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvailableBackgroundRequest proto.InternalMessageInfo

func (m *GetAvailableBackgroundRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *GetAvailableBackgroundRequest) GetBindEntity() *BindEntity {
	if m != nil {
		return m.BindEntity
	}
	return nil
}

func (m *GetAvailableBackgroundRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAvailableBackgroundResponse struct {
	CurBackground        *ChatBackground   `protobuf:"bytes,1,opt,name=cur_background,json=curBackground,proto3" json:"cur_background,omitempty"`
	UnLockBackgrounds    []*ChatBackground `protobuf:"bytes,2,rep,name=un_lock_backgrounds,json=unLockBackgrounds,proto3" json:"un_lock_backgrounds,omitempty"`
	LockBackgrounds      []*ChatBackground `protobuf:"bytes,3,rep,name=lock_backgrounds,json=lockBackgrounds,proto3" json:"lock_backgrounds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAvailableBackgroundResponse) Reset()         { *m = GetAvailableBackgroundResponse{} }
func (m *GetAvailableBackgroundResponse) String() string { return proto.CompactTextString(m) }
func (*GetAvailableBackgroundResponse) ProtoMessage()    {}
func (*GetAvailableBackgroundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{51}
}
func (m *GetAvailableBackgroundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvailableBackgroundResponse.Unmarshal(m, b)
}
func (m *GetAvailableBackgroundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvailableBackgroundResponse.Marshal(b, m, deterministic)
}
func (dst *GetAvailableBackgroundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvailableBackgroundResponse.Merge(dst, src)
}
func (m *GetAvailableBackgroundResponse) XXX_Size() int {
	return xxx_messageInfo_GetAvailableBackgroundResponse.Size(m)
}
func (m *GetAvailableBackgroundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvailableBackgroundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvailableBackgroundResponse proto.InternalMessageInfo

func (m *GetAvailableBackgroundResponse) GetCurBackground() *ChatBackground {
	if m != nil {
		return m.CurBackground
	}
	return nil
}

func (m *GetAvailableBackgroundResponse) GetUnLockBackgrounds() []*ChatBackground {
	if m != nil {
		return m.UnLockBackgrounds
	}
	return nil
}

func (m *GetAvailableBackgroundResponse) GetLockBackgrounds() []*ChatBackground {
	if m != nil {
		return m.LockBackgrounds
	}
	return nil
}

type SwitchChatBackgroundRequest struct {
	Entity               *Entity                        `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	BackgroundId         string                         `protobuf:"bytes,2,opt,name=background_id,json=backgroundId,proto3" json:"background_id,omitempty"`
	Op                   SwitchChatBackgroundRequest_Op `protobuf:"varint,3,opt,name=op,proto3,enum=aigc_intimacy.SwitchChatBackgroundRequest_Op" json:"op,omitempty"`
	BindEntity           *BindEntity                    `protobuf:"bytes,4,opt,name=bind_entity,json=bindEntity,proto3" json:"bind_entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *SwitchChatBackgroundRequest) Reset()         { *m = SwitchChatBackgroundRequest{} }
func (m *SwitchChatBackgroundRequest) String() string { return proto.CompactTextString(m) }
func (*SwitchChatBackgroundRequest) ProtoMessage()    {}
func (*SwitchChatBackgroundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{52}
}
func (m *SwitchChatBackgroundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChatBackgroundRequest.Unmarshal(m, b)
}
func (m *SwitchChatBackgroundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChatBackgroundRequest.Marshal(b, m, deterministic)
}
func (dst *SwitchChatBackgroundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChatBackgroundRequest.Merge(dst, src)
}
func (m *SwitchChatBackgroundRequest) XXX_Size() int {
	return xxx_messageInfo_SwitchChatBackgroundRequest.Size(m)
}
func (m *SwitchChatBackgroundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChatBackgroundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChatBackgroundRequest proto.InternalMessageInfo

func (m *SwitchChatBackgroundRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *SwitchChatBackgroundRequest) GetBackgroundId() string {
	if m != nil {
		return m.BackgroundId
	}
	return ""
}

func (m *SwitchChatBackgroundRequest) GetOp() SwitchChatBackgroundRequest_Op {
	if m != nil {
		return m.Op
	}
	return SwitchChatBackgroundRequest_OP_UNSPECIFIED
}

func (m *SwitchChatBackgroundRequest) GetBindEntity() *BindEntity {
	if m != nil {
		return m.BindEntity
	}
	return nil
}

type SwitchChatBackgroundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChatBackgroundResponse) Reset()         { *m = SwitchChatBackgroundResponse{} }
func (m *SwitchChatBackgroundResponse) String() string { return proto.CompactTextString(m) }
func (*SwitchChatBackgroundResponse) ProtoMessage()    {}
func (*SwitchChatBackgroundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{53}
}
func (m *SwitchChatBackgroundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChatBackgroundResponse.Unmarshal(m, b)
}
func (m *SwitchChatBackgroundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChatBackgroundResponse.Marshal(b, m, deterministic)
}
func (dst *SwitchChatBackgroundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChatBackgroundResponse.Merge(dst, src)
}
func (m *SwitchChatBackgroundResponse) XXX_Size() int {
	return xxx_messageInfo_SwitchChatBackgroundResponse.Size(m)
}
func (m *SwitchChatBackgroundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChatBackgroundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChatBackgroundResponse proto.InternalMessageInfo

type SwitchRelationRequest struct {
	Entity               *Entity                  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	RelationId           string                   `protobuf:"bytes,2,opt,name=relation_id,json=relationId,proto3" json:"relation_id,omitempty"`
	Op                   SwitchRelationRequest_Op `protobuf:"varint,3,opt,name=op,proto3,enum=aigc_intimacy.SwitchRelationRequest_Op" json:"op,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *SwitchRelationRequest) Reset()         { *m = SwitchRelationRequest{} }
func (m *SwitchRelationRequest) String() string { return proto.CompactTextString(m) }
func (*SwitchRelationRequest) ProtoMessage()    {}
func (*SwitchRelationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{54}
}
func (m *SwitchRelationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchRelationRequest.Unmarshal(m, b)
}
func (m *SwitchRelationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchRelationRequest.Marshal(b, m, deterministic)
}
func (dst *SwitchRelationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchRelationRequest.Merge(dst, src)
}
func (m *SwitchRelationRequest) XXX_Size() int {
	return xxx_messageInfo_SwitchRelationRequest.Size(m)
}
func (m *SwitchRelationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchRelationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchRelationRequest proto.InternalMessageInfo

func (m *SwitchRelationRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *SwitchRelationRequest) GetRelationId() string {
	if m != nil {
		return m.RelationId
	}
	return ""
}

func (m *SwitchRelationRequest) GetOp() SwitchRelationRequest_Op {
	if m != nil {
		return m.Op
	}
	return SwitchRelationRequest_OP_UNSPECIFIED
}

type SwitchRelationResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchRelationResponse) Reset()         { *m = SwitchRelationResponse{} }
func (m *SwitchRelationResponse) String() string { return proto.CompactTextString(m) }
func (*SwitchRelationResponse) ProtoMessage()    {}
func (*SwitchRelationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{55}
}
func (m *SwitchRelationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchRelationResponse.Unmarshal(m, b)
}
func (m *SwitchRelationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchRelationResponse.Marshal(b, m, deterministic)
}
func (dst *SwitchRelationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchRelationResponse.Merge(dst, src)
}
func (m *SwitchRelationResponse) XXX_Size() int {
	return xxx_messageInfo_SwitchRelationResponse.Size(m)
}
func (m *SwitchRelationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchRelationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchRelationResponse proto.InternalMessageInfo

type GetRelationsByEntityRequest struct {
	Entity               *Entity  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRelationsByEntityRequest) Reset()         { *m = GetRelationsByEntityRequest{} }
func (m *GetRelationsByEntityRequest) String() string { return proto.CompactTextString(m) }
func (*GetRelationsByEntityRequest) ProtoMessage()    {}
func (*GetRelationsByEntityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{56}
}
func (m *GetRelationsByEntityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRelationsByEntityRequest.Unmarshal(m, b)
}
func (m *GetRelationsByEntityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRelationsByEntityRequest.Marshal(b, m, deterministic)
}
func (dst *GetRelationsByEntityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRelationsByEntityRequest.Merge(dst, src)
}
func (m *GetRelationsByEntityRequest) XXX_Size() int {
	return xxx_messageInfo_GetRelationsByEntityRequest.Size(m)
}
func (m *GetRelationsByEntityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRelationsByEntityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRelationsByEntityRequest proto.InternalMessageInfo

func (m *GetRelationsByEntityRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *GetRelationsByEntityRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRelationsByEntityResponse struct {
	CurRelation          *Relationship   `protobuf:"bytes,1,opt,name=cur_relation,json=curRelation,proto3" json:"cur_relation,omitempty"`
	UnLockRelations      []*Relationship `protobuf:"bytes,2,rep,name=un_lock_relations,json=unLockRelations,proto3" json:"un_lock_relations,omitempty"`
	LockRelations        []*Relationship `protobuf:"bytes,3,rep,name=lock_relations,json=lockRelations,proto3" json:"lock_relations,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetRelationsByEntityResponse) Reset()         { *m = GetRelationsByEntityResponse{} }
func (m *GetRelationsByEntityResponse) String() string { return proto.CompactTextString(m) }
func (*GetRelationsByEntityResponse) ProtoMessage()    {}
func (*GetRelationsByEntityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{57}
}
func (m *GetRelationsByEntityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRelationsByEntityResponse.Unmarshal(m, b)
}
func (m *GetRelationsByEntityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRelationsByEntityResponse.Marshal(b, m, deterministic)
}
func (dst *GetRelationsByEntityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRelationsByEntityResponse.Merge(dst, src)
}
func (m *GetRelationsByEntityResponse) XXX_Size() int {
	return xxx_messageInfo_GetRelationsByEntityResponse.Size(m)
}
func (m *GetRelationsByEntityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRelationsByEntityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRelationsByEntityResponse proto.InternalMessageInfo

func (m *GetRelationsByEntityResponse) GetCurRelation() *Relationship {
	if m != nil {
		return m.CurRelation
	}
	return nil
}

func (m *GetRelationsByEntityResponse) GetUnLockRelations() []*Relationship {
	if m != nil {
		return m.UnLockRelations
	}
	return nil
}

func (m *GetRelationsByEntityResponse) GetLockRelations() []*Relationship {
	if m != nil {
		return m.LockRelations
	}
	return nil
}

type GetReadHeartCountRequest struct {
	Entity               *Entity  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReadHeartCountRequest) Reset()         { *m = GetReadHeartCountRequest{} }
func (m *GetReadHeartCountRequest) String() string { return proto.CompactTextString(m) }
func (*GetReadHeartCountRequest) ProtoMessage()    {}
func (*GetReadHeartCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{58}
}
func (m *GetReadHeartCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReadHeartCountRequest.Unmarshal(m, b)
}
func (m *GetReadHeartCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReadHeartCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetReadHeartCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReadHeartCountRequest.Merge(dst, src)
}
func (m *GetReadHeartCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetReadHeartCountRequest.Size(m)
}
func (m *GetReadHeartCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReadHeartCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetReadHeartCountRequest proto.InternalMessageInfo

func (m *GetReadHeartCountRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *GetReadHeartCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetReadHeartCountResponse struct {
	ReadHeartCount       uint32   `protobuf:"varint,1,opt,name=read_heart_count,json=readHeartCount,proto3" json:"read_heart_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReadHeartCountResponse) Reset()         { *m = GetReadHeartCountResponse{} }
func (m *GetReadHeartCountResponse) String() string { return proto.CompactTextString(m) }
func (*GetReadHeartCountResponse) ProtoMessage()    {}
func (*GetReadHeartCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{59}
}
func (m *GetReadHeartCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReadHeartCountResponse.Unmarshal(m, b)
}
func (m *GetReadHeartCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReadHeartCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetReadHeartCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReadHeartCountResponse.Merge(dst, src)
}
func (m *GetReadHeartCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetReadHeartCountResponse.Size(m)
}
func (m *GetReadHeartCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReadHeartCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetReadHeartCountResponse proto.InternalMessageInfo

func (m *GetReadHeartCountResponse) GetReadHeartCount() uint32 {
	if m != nil {
		return m.ReadHeartCount
	}
	return 0
}

// 升级通知
type LevelUpNotify struct {
	Uid    uint32  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Entity *Entity `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	// 最新等级
	Level uint32 `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	// 最新亲密值
	Value uint32 `protobuf:"varint,4,opt,name=value,proto3" json:"value,omitempty"`
	// 当前等级亲密值范围
	MinValue uint32 `protobuf:"varint,6,opt,name=min_value,json=minValue,proto3" json:"min_value,omitempty"`
	MaxValue uint32 `protobuf:"varint,7,opt,name=max_value,json=maxValue,proto3" json:"max_value,omitempty"`
	// 是否升级了
	LevelUpgraded bool `protobuf:"varint,8,opt,name=level_upgraded,json=levelUpgraded,proto3" json:"level_upgraded,omitempty"`
	// 触发时间(秒)
	TriggeredAt          int64    `protobuf:"varint,9,opt,name=triggered_at,json=triggeredAt,proto3" json:"triggered_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelUpNotify) Reset()         { *m = LevelUpNotify{} }
func (m *LevelUpNotify) String() string { return proto.CompactTextString(m) }
func (*LevelUpNotify) ProtoMessage()    {}
func (*LevelUpNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{60}
}
func (m *LevelUpNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelUpNotify.Unmarshal(m, b)
}
func (m *LevelUpNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelUpNotify.Marshal(b, m, deterministic)
}
func (dst *LevelUpNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelUpNotify.Merge(dst, src)
}
func (m *LevelUpNotify) XXX_Size() int {
	return xxx_messageInfo_LevelUpNotify.Size(m)
}
func (m *LevelUpNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelUpNotify.DiscardUnknown(m)
}

var xxx_messageInfo_LevelUpNotify proto.InternalMessageInfo

func (m *LevelUpNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LevelUpNotify) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *LevelUpNotify) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelUpNotify) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *LevelUpNotify) GetMinValue() uint32 {
	if m != nil {
		return m.MinValue
	}
	return 0
}

func (m *LevelUpNotify) GetMaxValue() uint32 {
	if m != nil {
		return m.MaxValue
	}
	return 0
}

func (m *LevelUpNotify) GetLevelUpgraded() bool {
	if m != nil {
		return m.LevelUpgraded
	}
	return false
}

func (m *LevelUpNotify) GetTriggeredAt() int64 {
	if m != nil {
		return m.TriggeredAt
	}
	return 0
}

type BatchGetCurRelationRequest struct {
	EntityList           []*Entity `protobuf:"bytes,1,rep,name=entity_list,json=entityList,proto3" json:"entity_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *BatchGetCurRelationRequest) Reset()         { *m = BatchGetCurRelationRequest{} }
func (m *BatchGetCurRelationRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetCurRelationRequest) ProtoMessage()    {}
func (*BatchGetCurRelationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{61}
}
func (m *BatchGetCurRelationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCurRelationRequest.Unmarshal(m, b)
}
func (m *BatchGetCurRelationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCurRelationRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetCurRelationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCurRelationRequest.Merge(dst, src)
}
func (m *BatchGetCurRelationRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetCurRelationRequest.Size(m)
}
func (m *BatchGetCurRelationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCurRelationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCurRelationRequest proto.InternalMessageInfo

func (m *BatchGetCurRelationRequest) GetEntityList() []*Entity {
	if m != nil {
		return m.EntityList
	}
	return nil
}

type BatchGetCurRelationResponse struct {
	CurRelations         []*BatchGetCurRelationResponse_CurRelationBindInfo `protobuf:"bytes,1,rep,name=cur_relations,json=curRelations,proto3" json:"cur_relations,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                           `json:"-"`
	XXX_unrecognized     []byte                                             `json:"-"`
	XXX_sizecache        int32                                              `json:"-"`
}

func (m *BatchGetCurRelationResponse) Reset()         { *m = BatchGetCurRelationResponse{} }
func (m *BatchGetCurRelationResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetCurRelationResponse) ProtoMessage()    {}
func (*BatchGetCurRelationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{62}
}
func (m *BatchGetCurRelationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCurRelationResponse.Unmarshal(m, b)
}
func (m *BatchGetCurRelationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCurRelationResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetCurRelationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCurRelationResponse.Merge(dst, src)
}
func (m *BatchGetCurRelationResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetCurRelationResponse.Size(m)
}
func (m *BatchGetCurRelationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCurRelationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCurRelationResponse proto.InternalMessageInfo

func (m *BatchGetCurRelationResponse) GetCurRelations() []*BatchGetCurRelationResponse_CurRelationBindInfo {
	if m != nil {
		return m.CurRelations
	}
	return nil
}

type BatchGetCurRelationResponse_CurRelationBindInfo struct {
	Entity               *Entity       `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	Relation             *Relationship `protobuf:"bytes,2,opt,name=relation,proto3" json:"relation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchGetCurRelationResponse_CurRelationBindInfo) Reset() {
	*m = BatchGetCurRelationResponse_CurRelationBindInfo{}
}
func (m *BatchGetCurRelationResponse_CurRelationBindInfo) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetCurRelationResponse_CurRelationBindInfo) ProtoMessage() {}
func (*BatchGetCurRelationResponse_CurRelationBindInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{62, 0}
}
func (m *BatchGetCurRelationResponse_CurRelationBindInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCurRelationResponse_CurRelationBindInfo.Unmarshal(m, b)
}
func (m *BatchGetCurRelationResponse_CurRelationBindInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCurRelationResponse_CurRelationBindInfo.Marshal(b, m, deterministic)
}
func (dst *BatchGetCurRelationResponse_CurRelationBindInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCurRelationResponse_CurRelationBindInfo.Merge(dst, src)
}
func (m *BatchGetCurRelationResponse_CurRelationBindInfo) XXX_Size() int {
	return xxx_messageInfo_BatchGetCurRelationResponse_CurRelationBindInfo.Size(m)
}
func (m *BatchGetCurRelationResponse_CurRelationBindInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCurRelationResponse_CurRelationBindInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCurRelationResponse_CurRelationBindInfo proto.InternalMessageInfo

func (m *BatchGetCurRelationResponse_CurRelationBindInfo) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *BatchGetCurRelationResponse_CurRelationBindInfo) GetRelation() *Relationship {
	if m != nil {
		return m.Relation
	}
	return nil
}

type GetBenefitConfigRequest struct {
	Entity               *Entity  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBenefitConfigRequest) Reset()         { *m = GetBenefitConfigRequest{} }
func (m *GetBenefitConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetBenefitConfigRequest) ProtoMessage()    {}
func (*GetBenefitConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{63}
}
func (m *GetBenefitConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBenefitConfigRequest.Unmarshal(m, b)
}
func (m *GetBenefitConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBenefitConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetBenefitConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBenefitConfigRequest.Merge(dst, src)
}
func (m *GetBenefitConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetBenefitConfigRequest.Size(m)
}
func (m *GetBenefitConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBenefitConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBenefitConfigRequest proto.InternalMessageInfo

func (m *GetBenefitConfigRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *GetBenefitConfigRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetBenefitConfigResponse struct {
	Benefits             *LevelBenefits `protobuf:"bytes,1,opt,name=benefits,proto3" json:"benefits,omitempty"`
	CurLevel             uint32         `protobuf:"varint,2,opt,name=cur_level,json=curLevel,proto3" json:"cur_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetBenefitConfigResponse) Reset()         { *m = GetBenefitConfigResponse{} }
func (m *GetBenefitConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetBenefitConfigResponse) ProtoMessage()    {}
func (*GetBenefitConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{64}
}
func (m *GetBenefitConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBenefitConfigResponse.Unmarshal(m, b)
}
func (m *GetBenefitConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBenefitConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetBenefitConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBenefitConfigResponse.Merge(dst, src)
}
func (m *GetBenefitConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetBenefitConfigResponse.Size(m)
}
func (m *GetBenefitConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBenefitConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBenefitConfigResponse proto.InternalMessageInfo

func (m *GetBenefitConfigResponse) GetBenefits() *LevelBenefits {
	if m != nil {
		return m.Benefits
	}
	return nil
}

func (m *GetBenefitConfigResponse) GetCurLevel() uint32 {
	if m != nil {
		return m.CurLevel
	}
	return 0
}

type GrantChatBackgroundRequest struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BgId                 string      `protobuf:"bytes,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	Entity               *BindEntity `protobuf:"bytes,3,opt,name=entity,proto3" json:"entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GrantChatBackgroundRequest) Reset()         { *m = GrantChatBackgroundRequest{} }
func (m *GrantChatBackgroundRequest) String() string { return proto.CompactTextString(m) }
func (*GrantChatBackgroundRequest) ProtoMessage()    {}
func (*GrantChatBackgroundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{65}
}
func (m *GrantChatBackgroundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantChatBackgroundRequest.Unmarshal(m, b)
}
func (m *GrantChatBackgroundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantChatBackgroundRequest.Marshal(b, m, deterministic)
}
func (dst *GrantChatBackgroundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantChatBackgroundRequest.Merge(dst, src)
}
func (m *GrantChatBackgroundRequest) XXX_Size() int {
	return xxx_messageInfo_GrantChatBackgroundRequest.Size(m)
}
func (m *GrantChatBackgroundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantChatBackgroundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GrantChatBackgroundRequest proto.InternalMessageInfo

func (m *GrantChatBackgroundRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GrantChatBackgroundRequest) GetBgId() string {
	if m != nil {
		return m.BgId
	}
	return ""
}

func (m *GrantChatBackgroundRequest) GetEntity() *BindEntity {
	if m != nil {
		return m.Entity
	}
	return nil
}

type GrantChatBackgroundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrantChatBackgroundResponse) Reset()         { *m = GrantChatBackgroundResponse{} }
func (m *GrantChatBackgroundResponse) String() string { return proto.CompactTextString(m) }
func (*GrantChatBackgroundResponse) ProtoMessage()    {}
func (*GrantChatBackgroundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{66}
}
func (m *GrantChatBackgroundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantChatBackgroundResponse.Unmarshal(m, b)
}
func (m *GrantChatBackgroundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantChatBackgroundResponse.Marshal(b, m, deterministic)
}
func (dst *GrantChatBackgroundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantChatBackgroundResponse.Merge(dst, src)
}
func (m *GrantChatBackgroundResponse) XXX_Size() int {
	return xxx_messageInfo_GrantChatBackgroundResponse.Size(m)
}
func (m *GrantChatBackgroundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantChatBackgroundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GrantChatBackgroundResponse proto.InternalMessageInfo

type GenerateChatBackgroundRequest struct {
	// 绑定主体
	Entity *BindEntity `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	// 主体的背景图
	EntityBg string `protobuf:"bytes,2,opt,name=entity_bg,json=entityBg,proto3" json:"entity_bg,omitempty"`
	// 背景参数
	Backgrounds          []*GenerateChatBackgroundRequest_Background `protobuf:"bytes,3,rep,name=backgrounds,proto3" json:"backgrounds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                    `json:"-"`
	XXX_unrecognized     []byte                                      `json:"-"`
	XXX_sizecache        int32                                       `json:"-"`
}

func (m *GenerateChatBackgroundRequest) Reset()         { *m = GenerateChatBackgroundRequest{} }
func (m *GenerateChatBackgroundRequest) String() string { return proto.CompactTextString(m) }
func (*GenerateChatBackgroundRequest) ProtoMessage()    {}
func (*GenerateChatBackgroundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{67}
}
func (m *GenerateChatBackgroundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateChatBackgroundRequest.Unmarshal(m, b)
}
func (m *GenerateChatBackgroundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateChatBackgroundRequest.Marshal(b, m, deterministic)
}
func (dst *GenerateChatBackgroundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateChatBackgroundRequest.Merge(dst, src)
}
func (m *GenerateChatBackgroundRequest) XXX_Size() int {
	return xxx_messageInfo_GenerateChatBackgroundRequest.Size(m)
}
func (m *GenerateChatBackgroundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateChatBackgroundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateChatBackgroundRequest proto.InternalMessageInfo

func (m *GenerateChatBackgroundRequest) GetEntity() *BindEntity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *GenerateChatBackgroundRequest) GetEntityBg() string {
	if m != nil {
		return m.EntityBg
	}
	return ""
}

func (m *GenerateChatBackgroundRequest) GetBackgrounds() []*GenerateChatBackgroundRequest_Background {
	if m != nil {
		return m.Backgrounds
	}
	return nil
}

type GenerateChatBackgroundRequest_Background struct {
	// 背景名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 是否展示头像
	ShowAvatar bool `protobuf:"varint,2,opt,name=show_avatar,json=showAvatar,proto3" json:"show_avatar,omitempty"`
	// 外显解锁条件
	UnlockCondition string `protobuf:"bytes,3,opt,name=unlock_condition,json=unlockCondition,proto3" json:"unlock_condition,omitempty"`
	// 风格id
	StyleId              uint32   `protobuf:"varint,4,opt,name=style_id,json=styleId,proto3" json:"style_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenerateChatBackgroundRequest_Background) Reset() {
	*m = GenerateChatBackgroundRequest_Background{}
}
func (m *GenerateChatBackgroundRequest_Background) String() string { return proto.CompactTextString(m) }
func (*GenerateChatBackgroundRequest_Background) ProtoMessage()    {}
func (*GenerateChatBackgroundRequest_Background) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{67, 0}
}
func (m *GenerateChatBackgroundRequest_Background) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateChatBackgroundRequest_Background.Unmarshal(m, b)
}
func (m *GenerateChatBackgroundRequest_Background) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateChatBackgroundRequest_Background.Marshal(b, m, deterministic)
}
func (dst *GenerateChatBackgroundRequest_Background) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateChatBackgroundRequest_Background.Merge(dst, src)
}
func (m *GenerateChatBackgroundRequest_Background) XXX_Size() int {
	return xxx_messageInfo_GenerateChatBackgroundRequest_Background.Size(m)
}
func (m *GenerateChatBackgroundRequest_Background) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateChatBackgroundRequest_Background.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateChatBackgroundRequest_Background proto.InternalMessageInfo

func (m *GenerateChatBackgroundRequest_Background) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GenerateChatBackgroundRequest_Background) GetShowAvatar() bool {
	if m != nil {
		return m.ShowAvatar
	}
	return false
}

func (m *GenerateChatBackgroundRequest_Background) GetUnlockCondition() string {
	if m != nil {
		return m.UnlockCondition
	}
	return ""
}

func (m *GenerateChatBackgroundRequest_Background) GetStyleId() uint32 {
	if m != nil {
		return m.StyleId
	}
	return 0
}

type GenerateChatBackgroundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenerateChatBackgroundResponse) Reset()         { *m = GenerateChatBackgroundResponse{} }
func (m *GenerateChatBackgroundResponse) String() string { return proto.CompactTextString(m) }
func (*GenerateChatBackgroundResponse) ProtoMessage()    {}
func (*GenerateChatBackgroundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_intimacy_dcff022ea511d6ea, []int{68}
}
func (m *GenerateChatBackgroundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateChatBackgroundResponse.Unmarshal(m, b)
}
func (m *GenerateChatBackgroundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateChatBackgroundResponse.Marshal(b, m, deterministic)
}
func (dst *GenerateChatBackgroundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateChatBackgroundResponse.Merge(dst, src)
}
func (m *GenerateChatBackgroundResponse) XXX_Size() int {
	return xxx_messageInfo_GenerateChatBackgroundResponse.Size(m)
}
func (m *GenerateChatBackgroundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateChatBackgroundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateChatBackgroundResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*Entity)(nil), "aigc_intimacy.Entity")
	proto.RegisterType((*LevelUpConditionProgress)(nil), "aigc_intimacy.LevelUpConditionProgress")
	proto.RegisterType((*LevelUpConditionProgress_LevelUpCondition)(nil), "aigc_intimacy.LevelUpConditionProgress.LevelUpCondition")
	proto.RegisterType((*GetLevelRequest)(nil), "aigc_intimacy.GetLevelRequest")
	proto.RegisterType((*GetLevelResponse)(nil), "aigc_intimacy.GetLevelResponse")
	proto.RegisterType((*GetLevelUpConditionProgressRequest)(nil), "aigc_intimacy.GetLevelUpConditionProgressRequest")
	proto.RegisterType((*GetLevelUpConditionProgressResponse)(nil), "aigc_intimacy.GetLevelUpConditionProgressResponse")
	proto.RegisterType((*ChatBackground)(nil), "aigc_intimacy.ChatBackground")
	proto.RegisterType((*BindEntity)(nil), "aigc_intimacy.BindEntity")
	proto.RegisterType((*CreateChatBackgroundRequest)(nil), "aigc_intimacy.CreateChatBackgroundRequest")
	proto.RegisterType((*CreateChatBackgroundResponse)(nil), "aigc_intimacy.CreateChatBackgroundResponse")
	proto.RegisterType((*UpdateChatBackgroundRequest)(nil), "aigc_intimacy.UpdateChatBackgroundRequest")
	proto.RegisterType((*UpdateChatBackgroundResponse)(nil), "aigc_intimacy.UpdateChatBackgroundResponse")
	proto.RegisterType((*DeleteChatBackgroundRequest)(nil), "aigc_intimacy.DeleteChatBackgroundRequest")
	proto.RegisterType((*DeleteChatBackgroundResponse)(nil), "aigc_intimacy.DeleteChatBackgroundResponse")
	proto.RegisterType((*GetChatBackgroundByIdRequest)(nil), "aigc_intimacy.GetChatBackgroundByIdRequest")
	proto.RegisterType((*GetChatBackgroundByIdResponse)(nil), "aigc_intimacy.GetChatBackgroundByIdResponse")
	proto.RegisterType((*GetPageChatBackgroundRequest)(nil), "aigc_intimacy.GetPageChatBackgroundRequest")
	proto.RegisterType((*GetPageChatBackgroundResponse)(nil), "aigc_intimacy.GetPageChatBackgroundResponse")
	proto.RegisterType((*GetBindChatBackgroundRequest)(nil), "aigc_intimacy.GetBindChatBackgroundRequest")
	proto.RegisterType((*GetBindChatBackgroundResponse)(nil), "aigc_intimacy.GetBindChatBackgroundResponse")
	proto.RegisterType((*Relationship)(nil), "aigc_intimacy.Relationship")
	proto.RegisterType((*CreateRelationshipRequest)(nil), "aigc_intimacy.CreateRelationshipRequest")
	proto.RegisterType((*CreateRelationshipResponse)(nil), "aigc_intimacy.CreateRelationshipResponse")
	proto.RegisterType((*UpdateRelationshipRequest)(nil), "aigc_intimacy.UpdateRelationshipRequest")
	proto.RegisterType((*UpdateRelationshipResponse)(nil), "aigc_intimacy.UpdateRelationshipResponse")
	proto.RegisterType((*DeleteRelationshipRequest)(nil), "aigc_intimacy.DeleteRelationshipRequest")
	proto.RegisterType((*DeleteRelationshipResponse)(nil), "aigc_intimacy.DeleteRelationshipResponse")
	proto.RegisterType((*GetRelationshipListRequest)(nil), "aigc_intimacy.GetRelationshipListRequest")
	proto.RegisterType((*GetRelationshipListResponse)(nil), "aigc_intimacy.GetRelationshipListResponse")
	proto.RegisterMapType((map[string]*ChatBackground)(nil), "aigc_intimacy.GetRelationshipListResponse.BackgroundInfoMapEntry")
	proto.RegisterType((*LevelBenefits)(nil), "aigc_intimacy.LevelBenefits")
	proto.RegisterType((*IntimacyLevelConf)(nil), "aigc_intimacy.IntimacyLevelConf")
	proto.RegisterType((*CreateIntimacyLevelConfRequest)(nil), "aigc_intimacy.CreateIntimacyLevelConfRequest")
	proto.RegisterType((*CreateIntimacyLevelConfResponse)(nil), "aigc_intimacy.CreateIntimacyLevelConfResponse")
	proto.RegisterType((*UpdateIntimacyLevelConfRequest)(nil), "aigc_intimacy.UpdateIntimacyLevelConfRequest")
	proto.RegisterType((*UpdateIntimacyLevelConfResponse)(nil), "aigc_intimacy.UpdateIntimacyLevelConfResponse")
	proto.RegisterType((*DeleteIntimacyLevelConfRequest)(nil), "aigc_intimacy.DeleteIntimacyLevelConfRequest")
	proto.RegisterType((*DeleteIntimacyLevelConfResponse)(nil), "aigc_intimacy.DeleteIntimacyLevelConfResponse")
	proto.RegisterType((*GetIntimacyLevelConfListRequest)(nil), "aigc_intimacy.GetIntimacyLevelConfListRequest")
	proto.RegisterType((*GetIntimacyLevelConfListResponse)(nil), "aigc_intimacy.GetIntimacyLevelConfListResponse")
	proto.RegisterMapType((map[string]*ChatBackground)(nil), "aigc_intimacy.GetIntimacyLevelConfListResponse.BackgroundInfoMapEntry")
	proto.RegisterMapType((map[string]*Relationship)(nil), "aigc_intimacy.GetIntimacyLevelConfListResponse.RelationshipInfoMapEntry")
	proto.RegisterType((*IntimacyCondition)(nil), "aigc_intimacy.IntimacyCondition")
	proto.RegisterType((*CreateIntimacyConditionRequest)(nil), "aigc_intimacy.CreateIntimacyConditionRequest")
	proto.RegisterType((*CreateIntimacyConditionResponse)(nil), "aigc_intimacy.CreateIntimacyConditionResponse")
	proto.RegisterType((*UpdateIntimacyConditionRequest)(nil), "aigc_intimacy.UpdateIntimacyConditionRequest")
	proto.RegisterType((*UpdateIntimacyConditionResponse)(nil), "aigc_intimacy.UpdateIntimacyConditionResponse")
	proto.RegisterType((*DeleteIntimacyConditionRequest)(nil), "aigc_intimacy.DeleteIntimacyConditionRequest")
	proto.RegisterType((*DeleteIntimacyConditionResponse)(nil), "aigc_intimacy.DeleteIntimacyConditionResponse")
	proto.RegisterType((*GetIntimacyConditionListRequest)(nil), "aigc_intimacy.GetIntimacyConditionListRequest")
	proto.RegisterType((*GetIntimacyConditionListResponse)(nil), "aigc_intimacy.GetIntimacyConditionListResponse")
	proto.RegisterType((*GetBindBackgroundRequest)(nil), "aigc_intimacy.GetBindBackgroundRequest")
	proto.RegisterType((*GetBindBackgroundResponse)(nil), "aigc_intimacy.GetBindBackgroundResponse")
	proto.RegisterType((*GetAvailableBackgroundRequest)(nil), "aigc_intimacy.GetAvailableBackgroundRequest")
	proto.RegisterType((*GetAvailableBackgroundResponse)(nil), "aigc_intimacy.GetAvailableBackgroundResponse")
	proto.RegisterType((*SwitchChatBackgroundRequest)(nil), "aigc_intimacy.SwitchChatBackgroundRequest")
	proto.RegisterType((*SwitchChatBackgroundResponse)(nil), "aigc_intimacy.SwitchChatBackgroundResponse")
	proto.RegisterType((*SwitchRelationRequest)(nil), "aigc_intimacy.SwitchRelationRequest")
	proto.RegisterType((*SwitchRelationResponse)(nil), "aigc_intimacy.SwitchRelationResponse")
	proto.RegisterType((*GetRelationsByEntityRequest)(nil), "aigc_intimacy.GetRelationsByEntityRequest")
	proto.RegisterType((*GetRelationsByEntityResponse)(nil), "aigc_intimacy.GetRelationsByEntityResponse")
	proto.RegisterType((*GetReadHeartCountRequest)(nil), "aigc_intimacy.GetReadHeartCountRequest")
	proto.RegisterType((*GetReadHeartCountResponse)(nil), "aigc_intimacy.GetReadHeartCountResponse")
	proto.RegisterType((*LevelUpNotify)(nil), "aigc_intimacy.LevelUpNotify")
	proto.RegisterType((*BatchGetCurRelationRequest)(nil), "aigc_intimacy.BatchGetCurRelationRequest")
	proto.RegisterType((*BatchGetCurRelationResponse)(nil), "aigc_intimacy.BatchGetCurRelationResponse")
	proto.RegisterType((*BatchGetCurRelationResponse_CurRelationBindInfo)(nil), "aigc_intimacy.BatchGetCurRelationResponse.CurRelationBindInfo")
	proto.RegisterType((*GetBenefitConfigRequest)(nil), "aigc_intimacy.GetBenefitConfigRequest")
	proto.RegisterType((*GetBenefitConfigResponse)(nil), "aigc_intimacy.GetBenefitConfigResponse")
	proto.RegisterType((*GrantChatBackgroundRequest)(nil), "aigc_intimacy.GrantChatBackgroundRequest")
	proto.RegisterType((*GrantChatBackgroundResponse)(nil), "aigc_intimacy.GrantChatBackgroundResponse")
	proto.RegisterType((*GenerateChatBackgroundRequest)(nil), "aigc_intimacy.GenerateChatBackgroundRequest")
	proto.RegisterType((*GenerateChatBackgroundRequest_Background)(nil), "aigc_intimacy.GenerateChatBackgroundRequest.Background")
	proto.RegisterType((*GenerateChatBackgroundResponse)(nil), "aigc_intimacy.GenerateChatBackgroundResponse")
	proto.RegisterEnum("aigc_intimacy.ChatBackgroundType", ChatBackgroundType_name, ChatBackgroundType_value)
	proto.RegisterEnum("aigc_intimacy.ConditionType", ConditionType_name, ConditionType_value)
	proto.RegisterEnum("aigc_intimacy.Entity_Type", Entity_Type_name, Entity_Type_value)
	proto.RegisterEnum("aigc_intimacy.ChatBackground_BindEntityType", ChatBackground_BindEntityType_name, ChatBackground_BindEntityType_value)
	proto.RegisterEnum("aigc_intimacy.BindEntity_EntityType", BindEntity_EntityType_name, BindEntity_EntityType_value)
	proto.RegisterEnum("aigc_intimacy.SwitchChatBackgroundRequest_Op", SwitchChatBackgroundRequest_Op_name, SwitchChatBackgroundRequest_Op_value)
	proto.RegisterEnum("aigc_intimacy.SwitchRelationRequest_Op", SwitchRelationRequest_Op_name, SwitchRelationRequest_Op_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AigcIntimacyClient is the client API for AigcIntimacy service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AigcIntimacyClient interface {
	// 查询亲密度等级
	GetLevel(ctx context.Context, in *GetLevelRequest, opts ...grpc.CallOption) (*GetLevelResponse, error)
	// 获取亲密度升级条件进度
	GetLevelUpConditionProgress(ctx context.Context, in *GetLevelUpConditionProgressRequest, opts ...grpc.CallOption) (*GetLevelUpConditionProgressResponse, error)
	// ========== 运营后台 ==========
	// 聊天背景配置
	CreateChatBackground(ctx context.Context, in *CreateChatBackgroundRequest, opts ...grpc.CallOption) (*CreateChatBackgroundResponse, error)
	UpdateChatBackground(ctx context.Context, in *UpdateChatBackgroundRequest, opts ...grpc.CallOption) (*UpdateChatBackgroundResponse, error)
	DeleteChatBackground(ctx context.Context, in *DeleteChatBackgroundRequest, opts ...grpc.CallOption) (*DeleteChatBackgroundResponse, error)
	GetChatBackgroundById(ctx context.Context, in *GetChatBackgroundByIdRequest, opts ...grpc.CallOption) (*GetChatBackgroundByIdResponse, error)
	GetPageChatBackground(ctx context.Context, in *GetPageChatBackgroundRequest, opts ...grpc.CallOption) (*GetPageChatBackgroundResponse, error)
	GetBindChatBackground(ctx context.Context, in *GetBindChatBackgroundRequest, opts ...grpc.CallOption) (*GetBindChatBackgroundResponse, error)
	// 关系配置
	CreateRelationship(ctx context.Context, in *CreateRelationshipRequest, opts ...grpc.CallOption) (*CreateRelationshipResponse, error)
	UpdateRelationship(ctx context.Context, in *UpdateRelationshipRequest, opts ...grpc.CallOption) (*UpdateRelationshipResponse, error)
	DeleteRelationship(ctx context.Context, in *DeleteRelationshipRequest, opts ...grpc.CallOption) (*DeleteRelationshipResponse, error)
	GetRelationshipList(ctx context.Context, in *GetRelationshipListRequest, opts ...grpc.CallOption) (*GetRelationshipListResponse, error)
	// 亲密度等级配置
	CreateIntimacyLevelConf(ctx context.Context, in *CreateIntimacyLevelConfRequest, opts ...grpc.CallOption) (*CreateIntimacyLevelConfResponse, error)
	UpdateIntimacyLevelConf(ctx context.Context, in *UpdateIntimacyLevelConfRequest, opts ...grpc.CallOption) (*UpdateIntimacyLevelConfResponse, error)
	DeleteIntimacyLevelConf(ctx context.Context, in *DeleteIntimacyLevelConfRequest, opts ...grpc.CallOption) (*DeleteIntimacyLevelConfResponse, error)
	GetIntimacyLevelConfList(ctx context.Context, in *GetIntimacyLevelConfListRequest, opts ...grpc.CallOption) (*GetIntimacyLevelConfListResponse, error)
	// 亲密度升级条件配置
	CreateIntimacyCondition(ctx context.Context, in *CreateIntimacyConditionRequest, opts ...grpc.CallOption) (*CreateIntimacyConditionResponse, error)
	UpdateIntimacyCondition(ctx context.Context, in *UpdateIntimacyConditionRequest, opts ...grpc.CallOption) (*UpdateIntimacyConditionResponse, error)
	DeleteIntimacyCondition(ctx context.Context, in *DeleteIntimacyConditionRequest, opts ...grpc.CallOption) (*DeleteIntimacyConditionResponse, error)
	GetIntimacyConditionList(ctx context.Context, in *GetIntimacyConditionListRequest, opts ...grpc.CallOption) (*GetIntimacyConditionListResponse, error)
	// 绑定关系
	GetBindBackground(ctx context.Context, in *GetBindBackgroundRequest, opts ...grpc.CallOption) (*GetBindBackgroundResponse, error)
	GetAvailableBackground(ctx context.Context, in *GetAvailableBackgroundRequest, opts ...grpc.CallOption) (*GetAvailableBackgroundResponse, error)
	SwitchChatBackground(ctx context.Context, in *SwitchChatBackgroundRequest, opts ...grpc.CallOption) (*SwitchChatBackgroundResponse, error)
	SwitchRelation(ctx context.Context, in *SwitchRelationRequest, opts ...grpc.CallOption) (*SwitchRelationResponse, error)
	GetRelationsByEntity(ctx context.Context, in *GetRelationsByEntityRequest, opts ...grpc.CallOption) (*GetRelationsByEntityResponse, error)
	BatchGetCurRelation(ctx context.Context, in *BatchGetCurRelationRequest, opts ...grpc.CallOption) (*BatchGetCurRelationResponse, error)
	// 读心次数
	GetReadHeartCount(ctx context.Context, in *GetReadHeartCountRequest, opts ...grpc.CallOption) (*GetReadHeartCountResponse, error)
	// 获取当前权益配置
	GetBenefitConfig(ctx context.Context, in *GetBenefitConfigRequest, opts ...grpc.CallOption) (*GetBenefitConfigResponse, error)
	// 聊天背景
	GrantChatBackground(ctx context.Context, in *GrantChatBackgroundRequest, opts ...grpc.CallOption) (*GrantChatBackgroundResponse, error)
	GenerateChatBackground(ctx context.Context, in *GenerateChatBackgroundRequest, opts ...grpc.CallOption) (*GenerateChatBackgroundResponse, error)
}

type aigcIntimacyClient struct {
	cc *grpc.ClientConn
}

func NewAigcIntimacyClient(cc *grpc.ClientConn) AigcIntimacyClient {
	return &aigcIntimacyClient{cc}
}

func (c *aigcIntimacyClient) GetLevel(ctx context.Context, in *GetLevelRequest, opts ...grpc.CallOption) (*GetLevelResponse, error) {
	out := new(GetLevelResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetLevelUpConditionProgress(ctx context.Context, in *GetLevelUpConditionProgressRequest, opts ...grpc.CallOption) (*GetLevelUpConditionProgressResponse, error) {
	out := new(GetLevelUpConditionProgressResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetLevelUpConditionProgress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) CreateChatBackground(ctx context.Context, in *CreateChatBackgroundRequest, opts ...grpc.CallOption) (*CreateChatBackgroundResponse, error) {
	out := new(CreateChatBackgroundResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/CreateChatBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) UpdateChatBackground(ctx context.Context, in *UpdateChatBackgroundRequest, opts ...grpc.CallOption) (*UpdateChatBackgroundResponse, error) {
	out := new(UpdateChatBackgroundResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/UpdateChatBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) DeleteChatBackground(ctx context.Context, in *DeleteChatBackgroundRequest, opts ...grpc.CallOption) (*DeleteChatBackgroundResponse, error) {
	out := new(DeleteChatBackgroundResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/DeleteChatBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetChatBackgroundById(ctx context.Context, in *GetChatBackgroundByIdRequest, opts ...grpc.CallOption) (*GetChatBackgroundByIdResponse, error) {
	out := new(GetChatBackgroundByIdResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetChatBackgroundById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetPageChatBackground(ctx context.Context, in *GetPageChatBackgroundRequest, opts ...grpc.CallOption) (*GetPageChatBackgroundResponse, error) {
	out := new(GetPageChatBackgroundResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetPageChatBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetBindChatBackground(ctx context.Context, in *GetBindChatBackgroundRequest, opts ...grpc.CallOption) (*GetBindChatBackgroundResponse, error) {
	out := new(GetBindChatBackgroundResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetBindChatBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) CreateRelationship(ctx context.Context, in *CreateRelationshipRequest, opts ...grpc.CallOption) (*CreateRelationshipResponse, error) {
	out := new(CreateRelationshipResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/CreateRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) UpdateRelationship(ctx context.Context, in *UpdateRelationshipRequest, opts ...grpc.CallOption) (*UpdateRelationshipResponse, error) {
	out := new(UpdateRelationshipResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/UpdateRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) DeleteRelationship(ctx context.Context, in *DeleteRelationshipRequest, opts ...grpc.CallOption) (*DeleteRelationshipResponse, error) {
	out := new(DeleteRelationshipResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/DeleteRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetRelationshipList(ctx context.Context, in *GetRelationshipListRequest, opts ...grpc.CallOption) (*GetRelationshipListResponse, error) {
	out := new(GetRelationshipListResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetRelationshipList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) CreateIntimacyLevelConf(ctx context.Context, in *CreateIntimacyLevelConfRequest, opts ...grpc.CallOption) (*CreateIntimacyLevelConfResponse, error) {
	out := new(CreateIntimacyLevelConfResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/CreateIntimacyLevelConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) UpdateIntimacyLevelConf(ctx context.Context, in *UpdateIntimacyLevelConfRequest, opts ...grpc.CallOption) (*UpdateIntimacyLevelConfResponse, error) {
	out := new(UpdateIntimacyLevelConfResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/UpdateIntimacyLevelConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) DeleteIntimacyLevelConf(ctx context.Context, in *DeleteIntimacyLevelConfRequest, opts ...grpc.CallOption) (*DeleteIntimacyLevelConfResponse, error) {
	out := new(DeleteIntimacyLevelConfResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/DeleteIntimacyLevelConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetIntimacyLevelConfList(ctx context.Context, in *GetIntimacyLevelConfListRequest, opts ...grpc.CallOption) (*GetIntimacyLevelConfListResponse, error) {
	out := new(GetIntimacyLevelConfListResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetIntimacyLevelConfList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) CreateIntimacyCondition(ctx context.Context, in *CreateIntimacyConditionRequest, opts ...grpc.CallOption) (*CreateIntimacyConditionResponse, error) {
	out := new(CreateIntimacyConditionResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/CreateIntimacyCondition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) UpdateIntimacyCondition(ctx context.Context, in *UpdateIntimacyConditionRequest, opts ...grpc.CallOption) (*UpdateIntimacyConditionResponse, error) {
	out := new(UpdateIntimacyConditionResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/UpdateIntimacyCondition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) DeleteIntimacyCondition(ctx context.Context, in *DeleteIntimacyConditionRequest, opts ...grpc.CallOption) (*DeleteIntimacyConditionResponse, error) {
	out := new(DeleteIntimacyConditionResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/DeleteIntimacyCondition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetIntimacyConditionList(ctx context.Context, in *GetIntimacyConditionListRequest, opts ...grpc.CallOption) (*GetIntimacyConditionListResponse, error) {
	out := new(GetIntimacyConditionListResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetIntimacyConditionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetBindBackground(ctx context.Context, in *GetBindBackgroundRequest, opts ...grpc.CallOption) (*GetBindBackgroundResponse, error) {
	out := new(GetBindBackgroundResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetBindBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetAvailableBackground(ctx context.Context, in *GetAvailableBackgroundRequest, opts ...grpc.CallOption) (*GetAvailableBackgroundResponse, error) {
	out := new(GetAvailableBackgroundResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetAvailableBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) SwitchChatBackground(ctx context.Context, in *SwitchChatBackgroundRequest, opts ...grpc.CallOption) (*SwitchChatBackgroundResponse, error) {
	out := new(SwitchChatBackgroundResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/SwitchChatBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) SwitchRelation(ctx context.Context, in *SwitchRelationRequest, opts ...grpc.CallOption) (*SwitchRelationResponse, error) {
	out := new(SwitchRelationResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/SwitchRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetRelationsByEntity(ctx context.Context, in *GetRelationsByEntityRequest, opts ...grpc.CallOption) (*GetRelationsByEntityResponse, error) {
	out := new(GetRelationsByEntityResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetRelationsByEntity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) BatchGetCurRelation(ctx context.Context, in *BatchGetCurRelationRequest, opts ...grpc.CallOption) (*BatchGetCurRelationResponse, error) {
	out := new(BatchGetCurRelationResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/BatchGetCurRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetReadHeartCount(ctx context.Context, in *GetReadHeartCountRequest, opts ...grpc.CallOption) (*GetReadHeartCountResponse, error) {
	out := new(GetReadHeartCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetReadHeartCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GetBenefitConfig(ctx context.Context, in *GetBenefitConfigRequest, opts ...grpc.CallOption) (*GetBenefitConfigResponse, error) {
	out := new(GetBenefitConfigResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GetBenefitConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GrantChatBackground(ctx context.Context, in *GrantChatBackgroundRequest, opts ...grpc.CallOption) (*GrantChatBackgroundResponse, error) {
	out := new(GrantChatBackgroundResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GrantChatBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcIntimacyClient) GenerateChatBackground(ctx context.Context, in *GenerateChatBackgroundRequest, opts ...grpc.CallOption) (*GenerateChatBackgroundResponse, error) {
	out := new(GenerateChatBackgroundResponse)
	err := c.cc.Invoke(ctx, "/aigc_intimacy.AigcIntimacy/GenerateChatBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AigcIntimacyServer is the server API for AigcIntimacy service.
type AigcIntimacyServer interface {
	// 查询亲密度等级
	GetLevel(context.Context, *GetLevelRequest) (*GetLevelResponse, error)
	// 获取亲密度升级条件进度
	GetLevelUpConditionProgress(context.Context, *GetLevelUpConditionProgressRequest) (*GetLevelUpConditionProgressResponse, error)
	// ========== 运营后台 ==========
	// 聊天背景配置
	CreateChatBackground(context.Context, *CreateChatBackgroundRequest) (*CreateChatBackgroundResponse, error)
	UpdateChatBackground(context.Context, *UpdateChatBackgroundRequest) (*UpdateChatBackgroundResponse, error)
	DeleteChatBackground(context.Context, *DeleteChatBackgroundRequest) (*DeleteChatBackgroundResponse, error)
	GetChatBackgroundById(context.Context, *GetChatBackgroundByIdRequest) (*GetChatBackgroundByIdResponse, error)
	GetPageChatBackground(context.Context, *GetPageChatBackgroundRequest) (*GetPageChatBackgroundResponse, error)
	GetBindChatBackground(context.Context, *GetBindChatBackgroundRequest) (*GetBindChatBackgroundResponse, error)
	// 关系配置
	CreateRelationship(context.Context, *CreateRelationshipRequest) (*CreateRelationshipResponse, error)
	UpdateRelationship(context.Context, *UpdateRelationshipRequest) (*UpdateRelationshipResponse, error)
	DeleteRelationship(context.Context, *DeleteRelationshipRequest) (*DeleteRelationshipResponse, error)
	GetRelationshipList(context.Context, *GetRelationshipListRequest) (*GetRelationshipListResponse, error)
	// 亲密度等级配置
	CreateIntimacyLevelConf(context.Context, *CreateIntimacyLevelConfRequest) (*CreateIntimacyLevelConfResponse, error)
	UpdateIntimacyLevelConf(context.Context, *UpdateIntimacyLevelConfRequest) (*UpdateIntimacyLevelConfResponse, error)
	DeleteIntimacyLevelConf(context.Context, *DeleteIntimacyLevelConfRequest) (*DeleteIntimacyLevelConfResponse, error)
	GetIntimacyLevelConfList(context.Context, *GetIntimacyLevelConfListRequest) (*GetIntimacyLevelConfListResponse, error)
	// 亲密度升级条件配置
	CreateIntimacyCondition(context.Context, *CreateIntimacyConditionRequest) (*CreateIntimacyConditionResponse, error)
	UpdateIntimacyCondition(context.Context, *UpdateIntimacyConditionRequest) (*UpdateIntimacyConditionResponse, error)
	DeleteIntimacyCondition(context.Context, *DeleteIntimacyConditionRequest) (*DeleteIntimacyConditionResponse, error)
	GetIntimacyConditionList(context.Context, *GetIntimacyConditionListRequest) (*GetIntimacyConditionListResponse, error)
	// 绑定关系
	GetBindBackground(context.Context, *GetBindBackgroundRequest) (*GetBindBackgroundResponse, error)
	GetAvailableBackground(context.Context, *GetAvailableBackgroundRequest) (*GetAvailableBackgroundResponse, error)
	SwitchChatBackground(context.Context, *SwitchChatBackgroundRequest) (*SwitchChatBackgroundResponse, error)
	SwitchRelation(context.Context, *SwitchRelationRequest) (*SwitchRelationResponse, error)
	GetRelationsByEntity(context.Context, *GetRelationsByEntityRequest) (*GetRelationsByEntityResponse, error)
	BatchGetCurRelation(context.Context, *BatchGetCurRelationRequest) (*BatchGetCurRelationResponse, error)
	// 读心次数
	GetReadHeartCount(context.Context, *GetReadHeartCountRequest) (*GetReadHeartCountResponse, error)
	// 获取当前权益配置
	GetBenefitConfig(context.Context, *GetBenefitConfigRequest) (*GetBenefitConfigResponse, error)
	// 聊天背景
	GrantChatBackground(context.Context, *GrantChatBackgroundRequest) (*GrantChatBackgroundResponse, error)
	GenerateChatBackground(context.Context, *GenerateChatBackgroundRequest) (*GenerateChatBackgroundResponse, error)
}

func RegisterAigcIntimacyServer(s *grpc.Server, srv AigcIntimacyServer) {
	s.RegisterService(&_AigcIntimacy_serviceDesc, srv)
}

func _AigcIntimacy_GetLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLevelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetLevel(ctx, req.(*GetLevelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetLevelUpConditionProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLevelUpConditionProgressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetLevelUpConditionProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetLevelUpConditionProgress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetLevelUpConditionProgress(ctx, req.(*GetLevelUpConditionProgressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_CreateChatBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChatBackgroundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).CreateChatBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/CreateChatBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).CreateChatBackground(ctx, req.(*CreateChatBackgroundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_UpdateChatBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChatBackgroundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).UpdateChatBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/UpdateChatBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).UpdateChatBackground(ctx, req.(*UpdateChatBackgroundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_DeleteChatBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteChatBackgroundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).DeleteChatBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/DeleteChatBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).DeleteChatBackground(ctx, req.(*DeleteChatBackgroundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetChatBackgroundById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChatBackgroundByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetChatBackgroundById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetChatBackgroundById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetChatBackgroundById(ctx, req.(*GetChatBackgroundByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetPageChatBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPageChatBackgroundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetPageChatBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetPageChatBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetPageChatBackground(ctx, req.(*GetPageChatBackgroundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetBindChatBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBindChatBackgroundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetBindChatBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetBindChatBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetBindChatBackground(ctx, req.(*GetBindChatBackgroundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_CreateRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).CreateRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/CreateRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).CreateRelationship(ctx, req.(*CreateRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_UpdateRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).UpdateRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/UpdateRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).UpdateRelationship(ctx, req.(*UpdateRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_DeleteRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).DeleteRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/DeleteRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).DeleteRelationship(ctx, req.(*DeleteRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetRelationshipList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRelationshipListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetRelationshipList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetRelationshipList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetRelationshipList(ctx, req.(*GetRelationshipListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_CreateIntimacyLevelConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIntimacyLevelConfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).CreateIntimacyLevelConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/CreateIntimacyLevelConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).CreateIntimacyLevelConf(ctx, req.(*CreateIntimacyLevelConfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_UpdateIntimacyLevelConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateIntimacyLevelConfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).UpdateIntimacyLevelConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/UpdateIntimacyLevelConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).UpdateIntimacyLevelConf(ctx, req.(*UpdateIntimacyLevelConfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_DeleteIntimacyLevelConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIntimacyLevelConfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).DeleteIntimacyLevelConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/DeleteIntimacyLevelConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).DeleteIntimacyLevelConf(ctx, req.(*DeleteIntimacyLevelConfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetIntimacyLevelConfList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIntimacyLevelConfListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetIntimacyLevelConfList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetIntimacyLevelConfList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetIntimacyLevelConfList(ctx, req.(*GetIntimacyLevelConfListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_CreateIntimacyCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIntimacyConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).CreateIntimacyCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/CreateIntimacyCondition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).CreateIntimacyCondition(ctx, req.(*CreateIntimacyConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_UpdateIntimacyCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateIntimacyConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).UpdateIntimacyCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/UpdateIntimacyCondition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).UpdateIntimacyCondition(ctx, req.(*UpdateIntimacyConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_DeleteIntimacyCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIntimacyConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).DeleteIntimacyCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/DeleteIntimacyCondition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).DeleteIntimacyCondition(ctx, req.(*DeleteIntimacyConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetIntimacyConditionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIntimacyConditionListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetIntimacyConditionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetIntimacyConditionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetIntimacyConditionList(ctx, req.(*GetIntimacyConditionListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetBindBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBindBackgroundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetBindBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetBindBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetBindBackground(ctx, req.(*GetBindBackgroundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetAvailableBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableBackgroundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetAvailableBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetAvailableBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetAvailableBackground(ctx, req.(*GetAvailableBackgroundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_SwitchChatBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchChatBackgroundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).SwitchChatBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/SwitchChatBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).SwitchChatBackground(ctx, req.(*SwitchChatBackgroundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_SwitchRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).SwitchRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/SwitchRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).SwitchRelation(ctx, req.(*SwitchRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetRelationsByEntity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRelationsByEntityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetRelationsByEntity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetRelationsByEntity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetRelationsByEntity(ctx, req.(*GetRelationsByEntityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_BatchGetCurRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCurRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).BatchGetCurRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/BatchGetCurRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).BatchGetCurRelation(ctx, req.(*BatchGetCurRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetReadHeartCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReadHeartCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetReadHeartCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetReadHeartCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetReadHeartCount(ctx, req.(*GetReadHeartCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GetBenefitConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBenefitConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GetBenefitConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GetBenefitConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GetBenefitConfig(ctx, req.(*GetBenefitConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GrantChatBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantChatBackgroundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GrantChatBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GrantChatBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GrantChatBackground(ctx, req.(*GrantChatBackgroundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcIntimacy_GenerateChatBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateChatBackgroundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcIntimacyServer).GenerateChatBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_intimacy.AigcIntimacy/GenerateChatBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcIntimacyServer).GenerateChatBackground(ctx, req.(*GenerateChatBackgroundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AigcIntimacy_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc_intimacy.AigcIntimacy",
	HandlerType: (*AigcIntimacyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLevel",
			Handler:    _AigcIntimacy_GetLevel_Handler,
		},
		{
			MethodName: "GetLevelUpConditionProgress",
			Handler:    _AigcIntimacy_GetLevelUpConditionProgress_Handler,
		},
		{
			MethodName: "CreateChatBackground",
			Handler:    _AigcIntimacy_CreateChatBackground_Handler,
		},
		{
			MethodName: "UpdateChatBackground",
			Handler:    _AigcIntimacy_UpdateChatBackground_Handler,
		},
		{
			MethodName: "DeleteChatBackground",
			Handler:    _AigcIntimacy_DeleteChatBackground_Handler,
		},
		{
			MethodName: "GetChatBackgroundById",
			Handler:    _AigcIntimacy_GetChatBackgroundById_Handler,
		},
		{
			MethodName: "GetPageChatBackground",
			Handler:    _AigcIntimacy_GetPageChatBackground_Handler,
		},
		{
			MethodName: "GetBindChatBackground",
			Handler:    _AigcIntimacy_GetBindChatBackground_Handler,
		},
		{
			MethodName: "CreateRelationship",
			Handler:    _AigcIntimacy_CreateRelationship_Handler,
		},
		{
			MethodName: "UpdateRelationship",
			Handler:    _AigcIntimacy_UpdateRelationship_Handler,
		},
		{
			MethodName: "DeleteRelationship",
			Handler:    _AigcIntimacy_DeleteRelationship_Handler,
		},
		{
			MethodName: "GetRelationshipList",
			Handler:    _AigcIntimacy_GetRelationshipList_Handler,
		},
		{
			MethodName: "CreateIntimacyLevelConf",
			Handler:    _AigcIntimacy_CreateIntimacyLevelConf_Handler,
		},
		{
			MethodName: "UpdateIntimacyLevelConf",
			Handler:    _AigcIntimacy_UpdateIntimacyLevelConf_Handler,
		},
		{
			MethodName: "DeleteIntimacyLevelConf",
			Handler:    _AigcIntimacy_DeleteIntimacyLevelConf_Handler,
		},
		{
			MethodName: "GetIntimacyLevelConfList",
			Handler:    _AigcIntimacy_GetIntimacyLevelConfList_Handler,
		},
		{
			MethodName: "CreateIntimacyCondition",
			Handler:    _AigcIntimacy_CreateIntimacyCondition_Handler,
		},
		{
			MethodName: "UpdateIntimacyCondition",
			Handler:    _AigcIntimacy_UpdateIntimacyCondition_Handler,
		},
		{
			MethodName: "DeleteIntimacyCondition",
			Handler:    _AigcIntimacy_DeleteIntimacyCondition_Handler,
		},
		{
			MethodName: "GetIntimacyConditionList",
			Handler:    _AigcIntimacy_GetIntimacyConditionList_Handler,
		},
		{
			MethodName: "GetBindBackground",
			Handler:    _AigcIntimacy_GetBindBackground_Handler,
		},
		{
			MethodName: "GetAvailableBackground",
			Handler:    _AigcIntimacy_GetAvailableBackground_Handler,
		},
		{
			MethodName: "SwitchChatBackground",
			Handler:    _AigcIntimacy_SwitchChatBackground_Handler,
		},
		{
			MethodName: "SwitchRelation",
			Handler:    _AigcIntimacy_SwitchRelation_Handler,
		},
		{
			MethodName: "GetRelationsByEntity",
			Handler:    _AigcIntimacy_GetRelationsByEntity_Handler,
		},
		{
			MethodName: "BatchGetCurRelation",
			Handler:    _AigcIntimacy_BatchGetCurRelation_Handler,
		},
		{
			MethodName: "GetReadHeartCount",
			Handler:    _AigcIntimacy_GetReadHeartCount_Handler,
		},
		{
			MethodName: "GetBenefitConfig",
			Handler:    _AigcIntimacy_GetBenefitConfig_Handler,
		},
		{
			MethodName: "GrantChatBackground",
			Handler:    _AigcIntimacy_GrantChatBackground_Handler,
		},
		{
			MethodName: "GenerateChatBackground",
			Handler:    _AigcIntimacy_GenerateChatBackground_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/aigc/aigc-intimacy/aigc-intimacy.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-intimacy/aigc-intimacy.proto", fileDescriptor_aigc_intimacy_dcff022ea511d6ea)
}

var fileDescriptor_aigc_intimacy_dcff022ea511d6ea = []byte{
	// 3073 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5b, 0xcd, 0x6f, 0xdb, 0xc8,
	0x15, 0x37, 0x25, 0xc7, 0x91, 0x9e, 0x2c, 0x45, 0x1e, 0xc7, 0x1b, 0x5b, 0x72, 0x1c, 0x87, 0xfb,
	0x11, 0x6f, 0xb2, 0x56, 0x1a, 0x6f, 0x77, 0x13, 0x6c, 0xb1, 0xbb, 0x95, 0x65, 0xaf, 0x23, 0xac,
	0xbf, 0x40, 0xcb, 0x29, 0xb2, 0xdd, 0x96, 0xa1, 0xc5, 0xb1, 0x4c, 0x44, 0x26, 0x65, 0x92, 0x72,
	0xd6, 0x8b, 0x2e, 0xda, 0x02, 0x05, 0xda, 0x5e, 0x8a, 0xde, 0x7a, 0xe9, 0xa5, 0x40, 0x7b, 0xef,
	0xb1, 0x40, 0xff, 0x8d, 0xee, 0x5f, 0x51, 0xa0, 0xd7, 0xf6, 0xd6, 0x82, 0x33, 0x43, 0x72, 0x48,
	0x0e, 0x29, 0x39, 0x9b, 0xf4, 0x12, 0x88, 0x6f, 0x66, 0xde, 0x7b, 0xf3, 0x66, 0xde, 0x6f, 0xde,
	0x87, 0x03, 0x1f, 0xb8, 0xee, 0xfd, 0xb3, 0xa1, 0xd1, 0x7d, 0xee, 0x18, 0xfd, 0x73, 0x6c, 0xdf,
	0xd7, 0x8c, 0x5e, 0x97, 0xfc, 0xb3, 0x6a, 0x98, 0xae, 0x71, 0xaa, 0x75, 0x2f, 0xa2, 0x5f, 0x8d,
	0x81, 0x6d, 0xb9, 0x16, 0x2a, 0x7b, 0x44, 0xd5, 0x27, 0xca, 0x7f, 0x96, 0x60, 0x6a, 0xd3, 0x74,
	0x0d, 0xf7, 0x02, 0x55, 0x20, 0x67, 0xe8, 0xf3, 0xd2, 0xb2, 0xb4, 0x52, 0x56, 0x72, 0x86, 0x8e,
	0x1a, 0x30, 0xe9, 0x5e, 0x0c, 0xf0, 0x7c, 0x6e, 0x59, 0x5a, 0xa9, 0xac, 0xd5, 0x1a, 0x91, 0x85,
	0x0d, 0xba, 0xa8, 0xd1, 0xb9, 0x18, 0x60, 0x85, 0xcc, 0x93, 0xbf, 0x80, 0x49, 0xef, 0x0b, 0x5d,
	0x87, 0x6a, 0xe7, 0xe9, 0xfe, 0xa6, 0x7a, 0xb8, 0x7b, 0xb0, 0xbf, 0xd9, 0x6a, 0x7f, 0xd6, 0xde,
	0xdc, 0xa8, 0x4e, 0xa0, 0x2a, 0x4c, 0x13, 0xea, 0x7e, 0x53, 0xe9, 0xec, 0x6e, 0x2a, 0x55, 0x09,
	0xcd, 0xc2, 0x35, 0x42, 0xd9, 0x39, 0xec, 0xb4, 0xd5, 0x2d, 0x65, 0xef, 0x70, 0xbf, 0x9a, 0x43,
	0x73, 0x30, 0x43, 0x88, 0x07, 0xed, 0xdd, 0xad, 0xed, 0x4d, 0x46, 0xce, 0xcb, 0xff, 0x96, 0x60,
	0x7e, 0x1b, 0x9f, 0xe3, 0xfe, 0xe1, 0xa0, 0x65, 0x99, 0xba, 0xe1, 0x1a, 0x96, 0xb9, 0x6f, 0x5b,
	0x3d, 0x1b, 0x3b, 0x0e, 0x7a, 0x02, 0xc5, 0xae, 0x4f, 0x24, 0xfa, 0x97, 0xd6, 0x1e, 0xc5, 0xb4,
	0x4d, 0x5b, 0x9b, 0x18, 0x50, 0x42, 0x56, 0xe8, 0x36, 0x4c, 0xf7, 0x6c, 0xeb, 0x85, 0x7b, 0xa2,
	0x9e, 0x6b, 0xfd, 0x21, 0x35, 0x44, 0x59, 0x29, 0x51, 0xda, 0x13, 0x8f, 0x54, 0x7b, 0x06, 0xd5,
	0x38, 0x07, 0xce, 0x8e, 0x45, 0x62, 0x47, 0x04, 0x93, 0x46, 0xd7, 0x32, 0xc9, 0xf2, 0xa2, 0x42,
	0x7e, 0x7b, 0x34, 0x1d, 0x3b, 0xdd, 0xf9, 0x3c, 0xa5, 0x79, 0xbf, 0xd1, 0x75, 0xb8, 0xe2, 0x1a,
	0x6e, 0x1f, 0xcf, 0x4f, 0x12, 0x22, 0xfd, 0x90, 0x15, 0xb8, 0xb6, 0x85, 0x5d, 0x22, 0x44, 0xc1,
	0x67, 0x43, 0xec, 0xb8, 0xa8, 0x0a, 0xf9, 0x61, 0x70, 0x52, 0xde, 0x4f, 0xb4, 0x0a, 0x53, 0x98,
	0x9c, 0x07, 0x11, 0x52, 0x5a, 0x9b, 0x13, 0x1e, 0x96, 0xc2, 0x26, 0xc9, 0x3f, 0x81, 0x6a, 0xc8,
	0xd3, 0x19, 0x58, 0xa6, 0xe3, 0x9d, 0xda, 0x95, 0xbe, 0x47, 0x60, 0x6c, 0xe9, 0x87, 0x47, 0xe5,
	0xf7, 0x4e, 0x3f, 0xd0, 0x4d, 0x00, 0xc7, 0xd5, 0x6c, 0x17, 0xeb, 0xaa, 0xe6, 0x92, 0x3d, 0xe4,
	0x95, 0x22, 0xa3, 0x34, 0x5d, 0x19, 0x83, 0xec, 0xb3, 0x17, 0x98, 0xfc, 0x95, 0xed, 0xe2, 0xf7,
	0x12, 0xbc, 0x99, 0x29, 0x87, 0xed, 0xec, 0x07, 0x30, 0xd9, 0x37, 0x1c, 0x77, 0x5e, 0x5a, 0xce,
	0xaf, 0x94, 0xd6, 0xee, 0x8c, 0x79, 0x33, 0x14, 0xb2, 0x08, 0xbd, 0x07, 0xc8, 0xb5, 0x74, 0xed,
	0x42, 0x15, 0xdc, 0x84, 0x2a, 0x19, 0xd9, 0x0a, 0xaf, 0x83, 0xfc, 0xaf, 0x3c, 0x54, 0x5a, 0x27,
	0x9a, 0xbb, 0xae, 0x75, 0x9f, 0xf7, 0x6c, 0x6b, 0x68, 0xea, 0xa2, 0xdb, 0x60, 0x6a, 0xa7, 0xd8,
	0xbf, 0x0d, 0xde, 0x6f, 0x54, 0x87, 0xa2, 0x71, 0xaa, 0xf5, 0xb0, 0x3a, 0xb4, 0xfb, 0xec, 0x4a,
	0x14, 0x08, 0xe1, 0xd0, 0xee, 0xa3, 0x5b, 0x50, 0x72, 0x4e, 0xac, 0x17, 0xaa, 0x76, 0xae, 0xb9,
	0x9a, 0x4d, 0x2e, 0x47, 0x41, 0x01, 0x8f, 0xd4, 0x24, 0x14, 0xf4, 0x36, 0x54, 0x74, 0x7c, 0xac,
	0x0d, 0xfb, 0xae, 0x3a, 0x34, 0xfb, 0x56, 0xf7, 0xf9, 0xfc, 0x15, 0x32, 0xa7, 0xcc, 0xa8, 0x87,
	0x84, 0x88, 0xde, 0x85, 0x2a, 0x1d, 0x56, 0x43, 0x67, 0x99, 0x22, 0xb2, 0xae, 0x51, 0x7a, 0x78,
	0x83, 0x9f, 0x40, 0xf5, 0xc8, 0x30, 0x75, 0x95, 0x1a, 0x5a, 0x25, 0x28, 0x70, 0x95, 0xa0, 0xc0,
	0x7b, 0x31, 0xeb, 0x45, 0x37, 0xdb, 0x58, 0x37, 0x4c, 0x9d, 0x9e, 0x12, 0xc1, 0x85, 0xca, 0x51,
	0xe4, 0x1b, 0xbd, 0x03, 0xd7, 0x78, 0xbe, 0x86, 0xee, 0xcc, 0x17, 0x96, 0xf3, 0x2b, 0x65, 0xa5,
	0x1c, 0x4e, 0x6c, 0xeb, 0x0e, 0xfa, 0x80, 0x21, 0x4f, 0x91, 0xc8, 0xbc, 0x9d, 0x29, 0x93, 0x03,
	0xa0, 0x33, 0xa8, 0x44, 0x15, 0x40, 0xcb, 0xb0, 0xb8, 0xde, 0xde, 0xdd, 0x50, 0x37, 0x77, 0x3b,
	0xed, 0xce, 0x53, 0x55, 0x00, 0x4b, 0x37, 0x61, 0x21, 0x31, 0xa3, 0xb9, 0xbd, 0xad, 0x2a, 0x7b,
	0xdb, 0x9b, 0x55, 0x09, 0x2d, 0x41, 0x2d, 0x31, 0xec, 0x0d, 0xa9, 0xdb, 0xed, 0x83, 0x4e, 0x35,
	0x27, 0xff, 0x41, 0x02, 0x08, 0x65, 0xa2, 0x47, 0x4c, 0x71, 0x89, 0x28, 0xfe, 0x56, 0x4c, 0xf1,
	0x70, 0x62, 0x83, 0x33, 0x12, 0x59, 0xc1, 0xae, 0x49, 0xce, 0x07, 0x5f, 0xf9, 0x53, 0x00, 0x6e,
	0x1f, 0x75, 0xb8, 0x91, 0xbe, 0x85, 0xeb, 0x50, 0x8d, 0xab, 0x57, 0x95, 0xe4, 0x2f, 0xa1, 0xde,
	0xb2, 0xb1, 0xe6, 0xe2, 0xa8, 0xb9, 0x7c, 0xef, 0xfb, 0x18, 0xe0, 0x28, 0x20, 0x32, 0xd0, 0xbc,
	0x99, 0x69, 0x68, 0x85, 0x5b, 0x20, 0x2f, 0xc1, 0xa2, 0x98, 0x3b, 0xf5, 0x39, 0x4f, 0xfa, 0xe1,
	0x40, 0x7f, 0x8d, 0xd2, 0xc5, 0xdc, 0x99, 0xf4, 0x55, 0xa8, 0x6f, 0xe0, 0x3e, 0x4e, 0x93, 0x1e,
	0x73, 0x49, 0x8f, 0x9d, 0x78, 0x3a, 0x63, 0xf7, 0x10, 0x16, 0xb7, 0xb0, 0x1b, 0x1d, 0x5c, 0xbf,
	0x68, 0x07, 0xfc, 0x6e, 0xc0, 0x55, 0x43, 0x57, 0x03, 0x8c, 0x29, 0x2a, 0x53, 0x86, 0xbe, 0x6d,
	0x38, 0xae, 0xfc, 0x0c, 0x6e, 0xa6, 0x2c, 0x64, 0xd0, 0xf4, 0x29, 0x94, 0xc2, 0x6d, 0x39, 0x0c,
	0xa1, 0x46, 0x18, 0x82, 0x5f, 0x21, 0xff, 0x55, 0x22, 0xba, 0xed, 0x6b, 0xbd, 0x94, 0xbd, 0x22,
	0x98, 0x1c, 0x68, 0x3d, 0xcc, 0x60, 0x96, 0xfc, 0xf6, 0x68, 0x8e, 0xf1, 0xb5, 0x8f, 0x62, 0xe4,
	0xb7, 0x07, 0xe9, 0x26, 0xc6, 0xba, 0xda, 0xb5, 0x86, 0x26, 0x85, 0xf4, 0x82, 0x52, 0xf4, 0x28,
	0x2d, 0x8f, 0x10, 0xa0, 0xd6, 0x24, 0x87, 0x5a, 0xbe, 0x97, 0x5e, 0xb9, 0x9c, 0x97, 0x9e, 0x13,
	0xa3, 0x88, 0x34, 0x7e, 0x45, 0x46, 0x21, 0x0f, 0xa9, 0xe5, 0x6a, 0x7d, 0xb2, 0xc1, 0xbc, 0x42,
	0x3f, 0xe4, 0xdf, 0x50, 0x53, 0x79, 0x4e, 0x28, 0x36, 0xd5, 0x83, 0xe0, 0xf9, 0xa1, 0x17, 0x72,
	0x21, 0xd5, 0x7d, 0xfd, 0x27, 0x28, 0x30, 0x41, 0xee, 0x72, 0x26, 0xa0, 0xf7, 0x42, 0xa4, 0xc9,
	0xab, 0xba, 0x17, 0xff, 0x91, 0x60, 0x5a, 0xc1, 0x7d, 0xcd, 0x83, 0x73, 0xe7, 0xc4, 0x18, 0x8c,
	0xf5, 0x0c, 0xdd, 0x82, 0x12, 0x91, 0x30, 0xd0, 0x6c, 0xed, 0xd4, 0x61, 0x0f, 0x11, 0x78, 0xa4,
	0x7d, 0x42, 0xf1, 0x1e, 0x43, 0x82, 0xdf, 0xa1, 0x24, 0xd5, 0xd0, 0xd9, 0x9d, 0x20, 0x2f, 0x46,
	0xa8, 0x4e, 0x5b, 0x17, 0x3e, 0x38, 0x57, 0xc4, 0x0f, 0x8e, 0x1f, 0x22, 0x4d, 0x71, 0x21, 0xd2,
	0x87, 0x70, 0xc3, 0xdf, 0x6a, 0x44, 0xe0, 0x69, 0x8f, 0xbc, 0x45, 0x45, 0x65, 0xce, 0x1f, 0xe6,
	0xa4, 0x9e, 0xf6, 0xe4, 0x2f, 0x61, 0x81, 0x42, 0x13, 0xbf, 0x7f, 0xff, 0x8c, 0x3f, 0x85, 0x69,
	0x9b, 0x23, 0xb3, 0x93, 0xae, 0xc7, 0x2c, 0x1b, 0x59, 0x19, 0x59, 0x20, 0x2f, 0x42, 0x4d, 0xc4,
	0x3d, 0x80, 0xbd, 0x05, 0x0a, 0x4c, 0xaf, 0x4b, 0xb6, 0x88, 0x3b, 0x93, 0x7d, 0x0f, 0x16, 0x28,
	0x8a, 0x89, 0x64, 0xc7, 0x21, 0x6f, 0x11, 0x6a, 0xa2, 0xc9, 0x8c, 0x95, 0x0d, 0xb5, 0x2d, 0xec,
	0xf2, 0x43, 0x1e, 0x9c, 0x71, 0x70, 0xd7, 0xc3, 0xae, 0xaa, 0xf5, 0x69, 0xac, 0x58, 0x50, 0xa6,
	0x7a, 0xd8, 0x6d, 0xf6, 0xfb, 0x3c, 0x0e, 0xe6, 0x78, 0x1c, 0x44, 0x32, 0x94, 0xbd, 0x15, 0xe4,
	0xee, 0x18, 0xe6, 0xb1, 0xc5, 0xf0, 0xa5, 0xd4, 0xa3, 0x4e, 0xd0, 0x36, 0x8f, 0x2d, 0xf9, 0x6f,
	0x39, 0xa8, 0x0b, 0x85, 0x32, 0x97, 0x68, 0x42, 0x99, 0x37, 0x86, 0xef, 0x14, 0x99, 0xe6, 0x8b,
	0xae, 0x40, 0x67, 0x30, 0xcb, 0x5f, 0x24, 0xf3, 0xd8, 0x52, 0x4f, 0xb5, 0x01, 0xd1, 0xb5, 0xb4,
	0xd6, 0x8c, 0x31, 0xca, 0xd0, 0xa5, 0xc1, 0x5d, 0x38, 0xf3, 0xd8, 0xda, 0xd1, 0x06, 0x9b, 0xa6,
	0x6b, 0x5f, 0x28, 0x33, 0x47, 0x71, 0x7a, 0xad, 0x0b, 0x6f, 0x88, 0x27, 0x7b, 0xe1, 0xef, 0x73,
	0x7c, 0xc1, 0x8e, 0xc4, 0xfb, 0x89, 0xde, 0xe7, 0x63, 0xed, 0x91, 0xee, 0x4e, 0xe7, 0x7e, 0x94,
	0x7b, 0x24, 0xc9, 0xdf, 0x4a, 0x50, 0x26, 0x61, 0xec, 0x3a, 0x36, 0xf1, 0xb1, 0xe1, 0x3a, 0xe8,
	0x3e, 0x5c, 0xc7, 0x5f, 0xb9, 0xb6, 0xa6, 0x3a, 0xd8, 0xd4, 0xd5, 0x53, 0xa7, 0xc7, 0x70, 0x9d,
	0xbe, 0x02, 0x33, 0x64, 0xec, 0x00, 0x9b, 0xfa, 0x8e, 0xd3, 0xa3, 0xf8, 0xfe, 0x00, 0xe6, 0xe8,
	0x02, 0x1b, 0x6b, 0xba, 0x7a, 0xea, 0x1d, 0x14, 0x5d, 0x41, 0xdf, 0x08, 0x44, 0x06, 0x15, 0xac,
	0xe9, 0x3b, 0x1e, 0x68, 0xb1, 0x25, 0xd7, 0x99, 0x7b, 0x47, 0xcf, 0x25, 0x4f, 0x8e, 0x7e, 0x96,
	0x8e, 0x29, 0x91, 0x03, 0x58, 0x05, 0xc4, 0x96, 0xf0, 0xe8, 0x36, 0x49, 0x16, 0xcc, 0xd0, 0x91,
	0x75, 0x0e, 0xc4, 0xfe, 0x2e, 0xc1, 0x4c, 0x9b, 0x6d, 0x9f, 0xec, 0xaf, 0x65, 0x99, 0xc7, 0x89,
	0x34, 0x35, 0x48, 0x5c, 0x72, 0x7c, 0xe2, 0xf2, 0xa6, 0x77, 0x5d, 0xce, 0x86, 0x86, 0x8d, 0x59,
	0xc8, 0x9e, 0x27, 0xa3, 0xd3, 0x8c, 0x48, 0xc2, 0x75, 0xf4, 0x08, 0x0a, 0x47, 0xcc, 0x64, 0x04,
	0xc5, 0x4a, 0x6b, 0x8b, 0xa2, 0xec, 0xc0, 0x37, 0xab, 0x12, 0xcc, 0xf6, 0x52, 0x43, 0xf6, 0x5b,
	0x25, 0x79, 0x1c, 0xc5, 0xb5, 0x12, 0xa3, 0x6d, 0x60, 0xa7, 0x2b, 0x63, 0x58, 0xa2, 0x48, 0x91,
	0xd8, 0x82, 0xef, 0x48, 0x2d, 0x98, 0x26, 0xca, 0x7a, 0xf8, 0x78, 0x6c, 0xf4, 0x18, 0x20, 0x2c,
	0xc7, 0x54, 0x48, 0x2e, 0x2f, 0xf5, 0xfd, 0x9f, 0x46, 0x4f, 0xbe, 0x0d, 0xb7, 0x52, 0xc5, 0x30,
	0x77, 0xc6, 0xb0, 0x44, 0x71, 0xe3, 0xb5, 0x6b, 0x92, 0x2a, 0x86, 0x69, 0xf2, 0x3d, 0x58, 0xa2,
	0xb0, 0x93, 0xaa, 0x49, 0xec, 0x74, 0x3d, 0xa6, 0xa9, 0x2b, 0x18, 0xd3, 0x17, 0x70, 0x6b, 0x0b,
	0xbb, 0x89, 0xf1, 0x97, 0x81, 0xac, 0xf2, 0xa5, 0x20, 0xeb, 0x2f, 0x93, 0xb0, 0x9c, 0x2e, 0x39,
	0xc0, 0xad, 0x12, 0x35, 0xaa, 0xca, 0x25, 0xa1, 0xa3, 0x2d, 0x0b, 0x74, 0x11, 0xd1, 0xe5, 0x3c,
	0x0b, 0xb7, 0x3e, 0x4b, 0xe2, 0x56, 0xa6, 0x42, 0xe3, 0x83, 0x17, 0xfa, 0x19, 0xcc, 0xf1, 0xae,
	0x1d, 0x4a, 0xce, 0x13, 0xc9, 0x8f, 0x2f, 0x2b, 0x99, 0x07, 0x83, 0x88, 0xec, 0x59, 0x3b, 0x39,
	0xf2, 0x7f, 0x81, 0xce, 0x5a, 0x17, 0xe6, 0xd3, 0xb4, 0x12, 0x88, 0x79, 0x10, 0x15, 0x93, 0xf9,
	0xf6, 0x70, 0xf8, 0xfc, 0xdb, 0x5c, 0x88, 0x63, 0xe9, 0x65, 0xa2, 0x16, 0x54, 0x82, 0x38, 0x49,
	0xe5, 0xa2, 0xca, 0x38, 0x24, 0x05, 0x1c, 0x48, 0x40, 0x59, 0xee, 0xf2, 0x9f, 0x3c, 0xec, 0x85,
	0x91, 0x7c, 0x08, 0x7b, 0x14, 0xb9, 0xeb, 0x50, 0x24, 0xca, 0xa9, 0x9a, 0x4e, 0xa3, 0xb7, 0xb2,
	0x52, 0x20, 0x84, 0xa6, 0xae, 0x7b, 0x41, 0xa0, 0xae, 0x19, 0xfd, 0x0b, 0xb5, 0x6f, 0x9c, 0x1a,
	0x2e, 0x01, 0xb6, 0xb2, 0x02, 0x84, 0xb4, 0xed, 0x51, 0x84, 0xb1, 0x5a, 0x50, 0xba, 0xba, 0xca,
	0x95, 0xae, 0x82, 0x22, 0x57, 0x21, 0x2c, 0x72, 0xc9, 0xcf, 0xe2, 0xa8, 0x18, 0x56, 0xde, 0x98,
	0xaf, 0x7e, 0x92, 0xac, 0xe6, 0xa5, 0xb9, 0x8b, 0xa8, 0x6a, 0x97, 0x04, 0x44, 0x4e, 0x02, 0x43,
	0x8c, 0x67, 0x71, 0x40, 0x7c, 0x1d, 0x4a, 0xa4, 0x4a, 0x48, 0xc3, 0xc2, 0x84, 0x12, 0xf1, 0xa0,
	0x2d, 0x81, 0x85, 0x49, 0xa6, 0x07, 0x11, 0x2c, 0x0c, 0xc6, 0xbf, 0x53, 0xf8, 0x26, 0xeb, 0x11,
	0x98, 0x8b, 0x31, 0x65, 0x30, 0xf7, 0x43, 0x80, 0x60, 0xf7, 0xce, 0x08, 0x94, 0x0b, 0xd5, 0xe6,
	0xd6, 0xc8, 0x6d, 0x98, 0x67, 0x49, 0x51, 0x32, 0x35, 0x5b, 0x8d, 0xa5, 0x66, 0x23, 0x2a, 0x83,
	0x1a, 0x2c, 0x08, 0x58, 0x31, 0x4d, 0x37, 0xa0, 0xd2, 0x1d, 0xda, 0xea, 0x65, 0xeb, 0x0f, 0xe5,
	0xee, 0xd0, 0x0e, 0x3f, 0xe5, 0x3f, 0x4a, 0x24, 0x87, 0x6b, 0x9e, 0x6b, 0x46, 0x5f, 0x3b, 0xea,
	0xe3, 0xef, 0xaa, 0x33, 0xfa, 0x08, 0x4a, 0x5c, 0x6d, 0x8c, 0x21, 0x4c, 0x46, 0x0a, 0x0a, 0x61,
	0xc9, 0xcc, 0x2f, 0xa5, 0xe6, 0x83, 0x52, 0xaa, 0xfc, 0x5f, 0x09, 0x96, 0xd2, 0xd4, 0x7b, 0x95,
	0x76, 0x40, 0x3b, 0x30, 0x3b, 0x34, 0xd5, 0x44, 0x4c, 0x97, 0x1b, 0x27, 0x63, 0x9d, 0x19, 0x9a,
	0xdb, 0xd1, 0x90, 0x0f, 0x3d, 0x86, 0x6a, 0x82, 0x57, 0x7e, 0x1c, 0x5e, 0xd7, 0xe2, 0xc1, 0xe3,
	0x9f, 0x72, 0x50, 0x3f, 0x78, 0x61, 0xb8, 0xdd, 0x13, 0x71, 0xb6, 0x7f, 0xc9, 0xe3, 0x79, 0x13,
	0xca, 0xd1, 0xac, 0x97, 0x26, 0xce, 0xd3, 0x47, 0x7c, 0xc6, 0xfb, 0x31, 0xe4, 0xac, 0x01, 0x39,
	0x86, 0xca, 0xda, 0x6a, 0x8c, 0x5f, 0x86, 0x2e, 0x8d, 0xbd, 0x81, 0x92, 0xb3, 0x06, 0xf1, 0x2b,
	0x30, 0x79, 0x89, 0x2b, 0x20, 0xbf, 0x0f, 0xb9, 0xbd, 0x01, 0x42, 0x50, 0xd9, 0xdb, 0x8f, 0x95,
	0x07, 0xcb, 0x50, 0xdc, 0xdb, 0x57, 0x0f, 0x7e, 0xd4, 0xee, 0xb4, 0x1e, 0x57, 0x25, 0x04, 0x30,
	0xb5, 0xb7, 0xaf, 0x6e, 0x6c, 0x6e, 0x57, 0x73, 0xf2, 0x12, 0x2c, 0x8a, 0xd5, 0x62, 0x68, 0xf2,
	0xad, 0x04, 0x73, 0x74, 0x82, 0xff, 0xb4, 0xbd, 0xa4, 0xf5, 0x6e, 0x41, 0xc9, 0x7f, 0xe2, 0x43,
	0xdb, 0x81, 0x4f, 0x6a, 0xeb, 0xe8, 0x21, 0x67, 0xb9, 0x3b, 0x42, 0xcb, 0xc5, 0x34, 0x60, 0x36,
	0x7b, 0xb9, 0x7d, 0xcf, 0xc3, 0x1b, 0x71, 0xa6, 0x6c, 0xc7, 0x3f, 0x8d, 0x26, 0xa1, 0xeb, 0x17,
	0x6c, 0x23, 0x2f, 0xb7, 0x6d, 0xe6, 0x97, 0xb9, 0xd0, 0x2f, 0xff, 0x49, 0x8b, 0x50, 0x02, 0x01,
	0xcc, 0x2b, 0x3f, 0x81, 0x69, 0xcf, 0x2b, 0x7d, 0xd3, 0x8c, 0x53, 0x24, 0x28, 0x75, 0x87, 0xb6,
	0x4f, 0x40, 0x5b, 0x30, 0xe3, 0xfb, 0x63, 0x10, 0x54, 0x31, 0x6f, 0xcc, 0x64, 0x72, 0x8d, 0xfa,
	0x62, 0x40, 0x43, 0xeb, 0x50, 0x89, 0x71, 0xc9, 0x8f, 0x91, 0x70, 0x47, 0x72, 0x3e, 0xf9, 0xc7,
	0x04, 0xd2, 0xbd, 0xb4, 0xf1, 0x31, 0xd6, 0x6c, 0x97, 0x44, 0x1f, 0xaf, 0xcc, 0x94, 0x9b, 0x04,
	0xe4, 0xe3, 0xcc, 0x99, 0x19, 0x57, 0xa0, 0x4a, 0x32, 0xd9, 0x13, 0x6f, 0x28, 0x92, 0xfc, 0x56,
	0xec, 0xc8, 0x0a, 0xf9, 0x17, 0x39, 0x96, 0x3c, 0x1f, 0x0e, 0x76, 0x2d, 0xd7, 0x38, 0xbe, 0xf8,
	0xce, 0x8d, 0xa9, 0x30, 0x23, 0xcd, 0x0b, 0x5b, 0x69, 0x93, 0x7c, 0x2b, 0xad, 0x0e, 0xc5, 0x53,
	0xc3, 0x64, 0x39, 0xea, 0x14, 0x8d, 0xc5, 0x4e, 0x0d, 0xf3, 0x49, 0x30, 0xa8, 0x7d, 0xc5, 0x06,
	0xaf, 0xb2, 0x41, 0xed, 0x2b, 0x3a, 0xf8, 0x36, 0x54, 0x68, 0xce, 0x36, 0x1c, 0xf4, 0x6c, 0x4d,
	0xc7, 0x3a, 0x89, 0xb3, 0x0a, 0x4a, 0xb9, 0x4f, 0x77, 0x43, 0x89, 0x5e, 0xa6, 0xea, 0xda, 0x46,
	0xaf, 0x87, 0x6d, 0xda, 0xad, 0x2b, 0x92, 0x9a, 0x68, 0x29, 0xa0, 0x35, 0x5d, 0xb9, 0x03, 0xb5,
	0x75, 0xcd, 0xed, 0x9e, 0x6c, 0x61, 0xb7, 0x15, 0x5e, 0x25, 0xff, 0xa0, 0x3e, 0x84, 0x12, 0xeb,
	0xd7, 0x70, 0x09, 0x4c, 0x8a, 0x05, 0x80, 0xce, 0x24, 0x51, 0xc3, 0x2f, 0x73, 0x50, 0x17, 0xb2,
	0x65, 0x47, 0xd4, 0x85, 0x32, 0x7f, 0xd3, 0xfd, 0xa0, 0xe1, 0x93, 0x38, 0xde, 0xa5, 0xb3, 0x68,
	0x70, 0x34, 0x3f, 0x29, 0x53, 0xa6, 0x39, 0x6f, 0x70, 0x6a, 0xdf, 0xc0, 0xac, 0x60, 0xd2, 0x65,
	0x2f, 0xdf, 0x43, 0x28, 0x04, 0x0e, 0x39, 0x46, 0xe8, 0x1f, 0x4c, 0x96, 0xbf, 0x80, 0x1b, 0x5e,
	0x20, 0x42, 0xab, 0x02, 0x34, 0x4d, 0x7e, 0x65, 0xf7, 0xff, 0x8c, 0xc6, 0x4b, 0x51, 0xde, 0xcc,
	0xb6, 0x7c, 0x61, 0x43, 0xba, 0x54, 0x61, 0xa3, 0x0e, 0x45, 0xef, 0x54, 0xf8, 0x8a, 0x4a, 0xa1,
	0x3b, 0xb4, 0xc9, 0x74, 0xf9, 0x1c, 0x6a, 0x5b, 0xb6, 0x66, 0xba, 0xe2, 0x17, 0x35, 0xe9, 0x37,
	0xb3, 0x70, 0xe5, 0xa8, 0x17, 0x02, 0xfe, 0xe4, 0x51, 0xaf, 0xad, 0x73, 0x65, 0xf6, 0xfc, 0x98,
	0x65, 0x76, 0xf9, 0x26, 0xd4, 0x85, 0x72, 0x19, 0x68, 0xff, 0x23, 0xe7, 0xc5, 0x62, 0x26, 0xb6,
	0x53, 0xfb, 0x4d, 0x2f, 0x51, 0xda, 0xaf, 0x43, 0x91, 0x5d, 0xfb, 0xa3, 0x1e, 0xd3, 0xbf, 0x40,
	0x09, 0xeb, 0x3d, 0xf4, 0x34, 0x5a, 0x9f, 0xa7, 0xc8, 0xf8, 0x30, 0x91, 0x0f, 0x67, 0xa8, 0xd4,
	0x48, 0xa9, 0xdc, 0xd7, 0x7e, 0x2d, 0x01, 0x70, 0xf1, 0x95, 0x5f, 0xa7, 0x97, 0xa2, 0x75, 0x7a,
	0xbe, 0x23, 0x9c, 0x4b, 0x74, 0x84, 0x45, 0x95, 0xf7, 0xbc, 0xb8, 0xf2, 0xbe, 0x00, 0x05, 0xc7,
	0xbd, 0xe8, 0x63, 0xbf, 0x90, 0x5f, 0x56, 0xae, 0x92, 0xef, 0xb6, 0x2e, 0x2f, 0x7b, 0x21, 0xa4,
	0x78, 0x0b, 0xd4, 0xf0, 0x77, 0x7f, 0x0e, 0x28, 0xd9, 0xe3, 0x40, 0x6f, 0xc1, 0x72, 0xeb, 0x71,
	0xb3, 0xa3, 0xae, 0x37, 0x5b, 0x9f, 0x6f, 0x29, 0x7b, 0x87, 0xbb, 0x1b, 0xa2, 0xae, 0xe5, 0x6d,
	0xb8, 0x29, 0x9c, 0xd5, 0xde, 0xed, 0xb4, 0x77, 0x9a, 0xad, 0xa7, 0x55, 0x09, 0x2d, 0xc3, 0xa2,
	0x70, 0xca, 0xbe, 0xb2, 0x79, 0xb0, 0xb9, 0xdb, 0xa9, 0xe6, 0xee, 0x5a, 0x50, 0x8e, 0xa4, 0xc3,
	0x68, 0x09, 0x6a, 0xad, 0xbd, 0xdd, 0x8d, 0x76, 0xa7, 0xbd, 0xb7, 0x9b, 0x26, 0x35, 0x3a, 0x4e,
	0x24, 0xec, 0x1c, 0x6c, 0xa9, 0xad, 0xbd, 0xc3, 0xdd, 0x4e, 0x55, 0x42, 0x75, 0xb8, 0x21, 0x9a,
	0xb2, 0xd1, 0x7c, 0x5a, 0xcd, 0xad, 0xfd, 0xae, 0x0e, 0xd3, 0x4d, 0xa3, 0xd7, 0xf5, 0x53, 0x19,
	0xf4, 0x39, 0x14, 0xfc, 0xbf, 0x41, 0x40, 0x4b, 0xc9, 0x82, 0x08, 0xff, 0x77, 0x1b, 0xb5, 0x5b,
	0xa9, 0xe3, 0xcc, 0x6d, 0x7f, 0x25, 0x91, 0xf0, 0x23, 0xf5, 0x0f, 0x5d, 0x1e, 0xa4, 0x30, 0x48,
	0xff, 0x2b, 0x8b, 0xda, 0xda, 0x65, 0x96, 0x30, 0x35, 0xce, 0xe0, 0xba, 0xa8, 0xb9, 0x8b, 0xee,
	0xc6, 0x43, 0xf0, 0xf4, 0xfe, 0x72, 0xed, 0xde, 0x58, 0x73, 0x99, 0x03, 0x4f, 0x78, 0x22, 0x45,
	0x1d, 0xdd, 0x84, 0xc8, 0x8c, 0xa6, 0x72, 0x42, 0x64, 0x66, 0x8b, 0x98, 0x88, 0x14, 0x75, 0x7d,
	0x13, 0x22, 0x33, 0x3a, 0xc9, 0x09, 0x91, 0x99, 0x6d, 0xe4, 0x09, 0xe4, 0xc2, 0x9c, 0xb0, 0x1f,
	0x8c, 0xee, 0x25, 0x4f, 0x29, 0xb5, 0xdd, 0x5c, 0x7b, 0x6f, 0xbc, 0xc9, 0x31, 0xa9, 0xc9, 0x86,
	0xab, 0x48, 0x6a, 0x6a, 0x23, 0x59, 0x24, 0x35, 0xbd, 0x87, 0x1b, 0x48, 0x4d, 0xf6, 0x38, 0x45,
	0x52, 0x53, 0x7b, 0xb2, 0x22, 0xa9, 0xe9, 0x6d, 0x53, 0x79, 0x02, 0x3d, 0x07, 0x94, 0x6c, 0xcf,
	0xa1, 0x15, 0xe1, 0x65, 0x14, 0xf4, 0xc9, 0x6a, 0xef, 0x8e, 0x31, 0x93, 0x17, 0x96, 0xec, 0xc7,
	0x25, 0x84, 0xa5, 0x36, 0x04, 0x13, 0xc2, 0x32, 0x9a, 0x7b, 0x44, 0x58, 0xb2, 0x63, 0x97, 0x10,
	0x96, 0xda, 0x01, 0x4c, 0x08, 0xcb, 0x68, 0xff, 0x4d, 0x20, 0x13, 0x66, 0x05, 0xfd, 0x2f, 0xf4,
	0xee, 0x38, 0x3d, 0x32, 0x2a, 0xee, 0xee, 0xf8, 0xed, 0x34, 0x79, 0x02, 0x7d, 0x0d, 0x37, 0x52,
	0x9a, 0x18, 0x68, 0x55, 0x78, 0x22, 0x69, 0xfd, 0x83, 0x5a, 0x63, 0xdc, 0xe9, 0xbc, 0xec, 0x94,
	0xb6, 0x45, 0x42, 0x76, 0x76, 0x17, 0x25, 0x21, 0x7b, 0x54, 0x37, 0x84, 0xc8, 0x4e, 0xe9, 0x6e,
	0x24, 0x64, 0x67, 0xf7, 0x4d, 0x12, 0xb2, 0x47, 0x35, 0x4d, 0x26, 0xd0, 0x37, 0x24, 0x7e, 0x14,
	0x56, 0xec, 0x51, 0x63, 0xec, 0xd2, 0x3e, 0x95, 0x7e, 0xff, 0x92, 0xad, 0x00, 0xd1, 0x91, 0x87,
	0x31, 0x49, 0xf6, 0x91, 0xc7, 0xcb, 0xa4, 0x23, 0x8e, 0x3c, 0x59, 0x23, 0x15, 0x1c, 0x79, 0xba,
	0xec, 0xec, 0x3a, 0xf1, 0x88, 0x23, 0x4f, 0x91, 0x9d, 0x52, 0xc4, 0x1d, 0x71, 0xe4, 0x23, 0x65,
	0x8f, 0xaa, 0x0d, 0xc7, 0x8f, 0x3c, 0x52, 0xc8, 0xcd, 0x3a, 0x72, 0x51, 0x19, 0x39, 0xeb, 0xc8,
	0x85, 0x15, 0x62, 0x79, 0x02, 0x9d, 0xc0, 0x4c, 0xa2, 0x2c, 0x8b, 0xee, 0x88, 0x11, 0x3e, 0xf9,
	0x14, 0xac, 0x8c, 0x9e, 0x18, 0x48, 0x7a, 0x01, 0x6f, 0x88, 0xab, 0x9f, 0x48, 0xf0, 0xa0, 0xa4,
	0xd7, 0x70, 0x6b, 0xab, 0x63, 0xce, 0xe6, 0x83, 0x0a, 0x51, 0x45, 0x2d, 0x11, 0x54, 0x64, 0x54,
	0x03, 0x13, 0x41, 0x45, 0x66, 0x89, 0x6e, 0x02, 0xa9, 0x50, 0x89, 0x16, 0xb3, 0xd0, 0x5b, 0xe3,
	0x14, 0xd0, 0x6a, 0x6f, 0x8f, 0x98, 0xc5, 0xef, 0x49, 0x54, 0xb2, 0x42, 0x59, 0x10, 0x1f, 0x2b,
	0x9c, 0xd5, 0xee, 0x8d, 0x35, 0x97, 0x7f, 0x7f, 0x04, 0x79, 0x7f, 0xe2, 0xfd, 0x49, 0xaf, 0x5a,
	0x24, 0xde, 0x9f, 0x8c, 0x32, 0x42, 0x70, 0x33, 0xa3, 0xb5, 0x24, 0xd1, 0xcd, 0x14, 0x96, 0xb2,
	0x44, 0x37, 0x53, 0x5c, 0x96, 0x92, 0x27, 0x10, 0x26, 0x7f, 0x7a, 0x1d, 0xc9, 0xda, 0xd1, 0x3b,
	0x82, 0x9b, 0x2d, 0x28, 0x19, 0xd4, 0xee, 0x8c, 0x9c, 0x17, 0x79, 0xc0, 0x93, 0x19, 0x73, 0xf2,
	0x01, 0x4f, 0xcd, 0xe6, 0x93, 0x0f, 0x78, 0x46, 0x02, 0xce, 0x1c, 0x4e, 0x94, 0x2b, 0x0a, 0x1c,
	0x2e, 0x23, 0x2b, 0x16, 0x38, 0x5c, 0x56, 0x02, 0x2a, 0x4f, 0xac, 0x7f, 0xf8, 0xc5, 0xf7, 0x7b,
	0x56, 0x5f, 0x33, 0x7b, 0x8d, 0x0f, 0xd6, 0x5c, 0xb7, 0xd1, 0xb5, 0x4e, 0xef, 0x93, 0xff, 0xe7,
	0xd0, 0xb5, 0xfa, 0xf7, 0x1d, 0x6c, 0x9f, 0x1b, 0x5d, 0xec, 0x08, 0xfe, 0x6b, 0xc4, 0xd1, 0x14,
	0x99, 0xf5, 0xfe, 0xff, 0x02, 0x00, 0x00, 0xff, 0xff, 0xb2, 0x33, 0x08, 0x4b, 0x46, 0x31, 0x00,
	0x00,
}
