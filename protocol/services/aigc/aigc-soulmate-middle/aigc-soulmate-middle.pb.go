// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-soulmate-middle/aigc-soulmate-middle.proto

package aigc_soulmate_middle // import "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ImMsgType int32

const (
	ImMsgType_ImMsgTypeUnknown ImMsgType = 0
	// 文本
	ImMsgType_ImMsgTypeText ImMsgType = 1
	// CUE
	ImMsgType_ImMsgTypeCue ImMsgType = 2
	// 表情
	ImMsgType_ImMsgTypeEmoticon ImMsgType = 3
	// 沉默(AI伴侣由活跃切为沉默)
	ImMsgType_ImMsgTypeSilence ImMsgType = 4
	// 传送门
	ImMsgType_ImMsgTypeAirTicket ImMsgType = 5
	// AI伴侣统一消息通道
	ImMsgType_ImMsgAIPartner ImMsgType = 6
)

var ImMsgType_name = map[int32]string{
	0: "ImMsgTypeUnknown",
	1: "ImMsgTypeText",
	2: "ImMsgTypeCue",
	3: "ImMsgTypeEmoticon",
	4: "ImMsgTypeSilence",
	5: "ImMsgTypeAirTicket",
	6: "ImMsgAIPartner",
}
var ImMsgType_value = map[string]int32{
	"ImMsgTypeUnknown":   0,
	"ImMsgTypeText":      1,
	"ImMsgTypeCue":       2,
	"ImMsgTypeEmoticon":  3,
	"ImMsgTypeSilence":   4,
	"ImMsgTypeAirTicket": 5,
	"ImMsgAIPartner":     6,
}

func (x ImMsgType) String() string {
	return proto.EnumName(ImMsgType_name, int32(x))
}
func (ImMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{0}
}

// 多人群组开始起用
// 内容类型
// 改动需同步到web_im_logic
type ImMsgContentType int32

const (
	ImMsgContentType_IM_MSG_CONTENT_TYPE_UNSPECIFIED ImMsgContentType = 0
	// 文本
	ImMsgContentType_IM_MSG_CONTENT_TYPE_TEXT ImMsgContentType = 1
	// 文本+语音
	ImMsgContentType_IM_MSG_CONTENT_TYPE_TEXT_VOICE ImMsgContentType = 2
	// 表情
	ImMsgContentType_IM_MSG_CONTENT_TYPE_EMOTION ImMsgContentType = 3
	// 图片
	ImMsgContentType_IM_MSG_CONTENT_TYPE_IMAGE ImMsgContentType = 4
)

var ImMsgContentType_name = map[int32]string{
	0: "IM_MSG_CONTENT_TYPE_UNSPECIFIED",
	1: "IM_MSG_CONTENT_TYPE_TEXT",
	2: "IM_MSG_CONTENT_TYPE_TEXT_VOICE",
	3: "IM_MSG_CONTENT_TYPE_EMOTION",
	4: "IM_MSG_CONTENT_TYPE_IMAGE",
}
var ImMsgContentType_value = map[string]int32{
	"IM_MSG_CONTENT_TYPE_UNSPECIFIED": 0,
	"IM_MSG_CONTENT_TYPE_TEXT":        1,
	"IM_MSG_CONTENT_TYPE_TEXT_VOICE":  2,
	"IM_MSG_CONTENT_TYPE_EMOTION":     3,
	"IM_MSG_CONTENT_TYPE_IMAGE":       4,
}

func (x ImMsgContentType) String() string {
	return proto.EnumName(ImMsgContentType_name, int32(x))
}
func (ImMsgContentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{1}
}

// 业务类型
// 改动需同步到web_im_logic
type ImBusiType int32

const (
	// 未知类型
	ImBusiType_IM_BUSI_TYPE_UNSPECIFIED ImBusiType = 0
	// 单人群聊
	ImBusiType_IM_BUSI_TYPE_SINGLE_GROUP ImBusiType = 1
	// 多人群聊
	ImBusiType_IM_BUSI_TYPE_MULTI_GROUP ImBusiType = 2
	// 多角色聊天
	ImBusiType_IM_BUSI_TYPE_MULTI_ROLE ImBusiType = 3
)

var ImBusiType_name = map[int32]string{
	0: "IM_BUSI_TYPE_UNSPECIFIED",
	1: "IM_BUSI_TYPE_SINGLE_GROUP",
	2: "IM_BUSI_TYPE_MULTI_GROUP",
	3: "IM_BUSI_TYPE_MULTI_ROLE",
}
var ImBusiType_value = map[string]int32{
	"IM_BUSI_TYPE_UNSPECIFIED":  0,
	"IM_BUSI_TYPE_SINGLE_GROUP": 1,
	"IM_BUSI_TYPE_MULTI_GROUP":  2,
	"IM_BUSI_TYPE_MULTI_ROLE":   3,
}

func (x ImBusiType) String() string {
	return proto.EnumName(ImBusiType_name, int32(x))
}
func (ImBusiType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{2}
}

// 命令类型 非内容消息需要指定业务类型前缀
// 改动需同步到web_im_logic
type ImCmdType int32

const (
	// 未知类型
	ImCmdType_IM_CMD_TYPE_UNSPECIFIED ImCmdType = 0
	// 内容消息
	ImCmdType_IM_CMD_TYPE_CONTENT_MSG ImCmdType = 1
	// 句数提示
	ImCmdType_IM_CMD_TYPE_SENTENCE_TIP ImCmdType = 2
	// 进群提示消息
	ImCmdType_IM_CMD_TYPE_JOIN_GROUP_TIP ImCmdType = 3
	// 欢迎语消息
	ImCmdType_IM_CMD_TYPE_WELCOME_MSG ImCmdType = 4
)

var ImCmdType_name = map[int32]string{
	0: "IM_CMD_TYPE_UNSPECIFIED",
	1: "IM_CMD_TYPE_CONTENT_MSG",
	2: "IM_CMD_TYPE_SENTENCE_TIP",
	3: "IM_CMD_TYPE_JOIN_GROUP_TIP",
	4: "IM_CMD_TYPE_WELCOME_MSG",
}
var ImCmdType_value = map[string]int32{
	"IM_CMD_TYPE_UNSPECIFIED":    0,
	"IM_CMD_TYPE_CONTENT_MSG":    1,
	"IM_CMD_TYPE_SENTENCE_TIP":   2,
	"IM_CMD_TYPE_JOIN_GROUP_TIP": 3,
	"IM_CMD_TYPE_WELCOME_MSG":    4,
}

func (x ImCmdType) String() string {
	return proto.EnumName(ImCmdType_name, int32(x))
}
func (ImCmdType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{3}
}

type GroupSendType int32

const (
	GroupSendType_GroupSendTypeUnknown GroupSendType = 0
	// 用户发AI
	GroupSendType_GroupSendTypeUser2AI GroupSendType = 1
	// AI发用户
	GroupSendType_GroupSendTypeAI2User GroupSendType = 2
)

var GroupSendType_name = map[int32]string{
	0: "GroupSendTypeUnknown",
	1: "GroupSendTypeUser2AI",
	2: "GroupSendTypeAI2User",
}
var GroupSendType_value = map[string]int32{
	"GroupSendTypeUnknown": 0,
	"GroupSendTypeUser2AI": 1,
	"GroupSendTypeAI2User": 2,
}

func (x GroupSendType) String() string {
	return proto.EnumName(GroupSendType_name, int32(x))
}
func (GroupSendType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{4}
}

// 目前只有角色专属句数有记录，其他的枚举先定
type SentenceType int32

const (
	SentenceType_SENTENCE_TYPE_UNSPECIFIED    SentenceType = 0
	SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED SentenceType = 1
	SentenceType_SENTENCE_TYPE_CUR_DAY        SentenceType = 2
	SentenceType_SENTENCE_TYPE_EXTRA          SentenceType = 3
)

var SentenceType_name = map[int32]string{
	0: "SENTENCE_TYPE_UNSPECIFIED",
	1: "SENTENCE_TYPE_ROLE_SPECIFIED",
	2: "SENTENCE_TYPE_CUR_DAY",
	3: "SENTENCE_TYPE_EXTRA",
}
var SentenceType_value = map[string]int32{
	"SENTENCE_TYPE_UNSPECIFIED":    0,
	"SENTENCE_TYPE_ROLE_SPECIFIED": 1,
	"SENTENCE_TYPE_CUR_DAY":        2,
	"SENTENCE_TYPE_EXTRA":          3,
}

func (x SentenceType) String() string {
	return proto.EnumName(SentenceType_name, int32(x))
}
func (SentenceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{5}
}

type Entity_Type int32

const (
	Entity_TYPE_UNSPECIFIED  Entity_Type = 0
	Entity_TYPE_PARTNER      Entity_Type = 1
	Entity_TYPE_MUTI_GROUP   Entity_Type = 2
	Entity_TYPE_SINGLE_GROUP Entity_Type = 3
)

var Entity_Type_name = map[int32]string{
	0: "TYPE_UNSPECIFIED",
	1: "TYPE_PARTNER",
	2: "TYPE_MUTI_GROUP",
	3: "TYPE_SINGLE_GROUP",
}
var Entity_Type_value = map[string]int32{
	"TYPE_UNSPECIFIED":  0,
	"TYPE_PARTNER":      1,
	"TYPE_MUTI_GROUP":   2,
	"TYPE_SINGLE_GROUP": 3,
}

func (x Entity_Type) String() string {
	return proto.EnumName(Entity_Type_name, int32(x))
}
func (Entity_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{23, 0}
}

type ImMsg struct {
	// 消息类型
	Type ImMsgType `protobuf:"varint,1,opt,name=type,proto3,enum=aigc_soulmate_middle.ImMsgType" json:"type,omitempty"`
	// 消息内容
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	// 扩展消息
	Ext []byte `protobuf:"bytes,3,opt,name=ext,proto3" json:"ext,omitempty"`
	// 消息发送时间(毫秒)
	SentAt               int64    `protobuf:"varint,4,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	SeqId                uint32   `protobuf:"varint,5,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	ImBusiType           uint32   `protobuf:"varint,6,opt,name=im_busi_type,json=imBusiType,proto3" json:"im_busi_type,omitempty"`
	ContentType          uint32   `protobuf:"varint,7,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	ImCmdType            uint32   `protobuf:"varint,8,opt,name=im_cmd_type,json=imCmdType,proto3" json:"im_cmd_type,omitempty"`
	TransparentExtMsg    string   `protobuf:"bytes,9,opt,name=transparent_ext_msg,json=transparentExtMsg,proto3" json:"transparent_ext_msg,omitempty"`
	RcmdContent          string   `protobuf:"bytes,10,opt,name=rcmd_content,json=rcmdContent,proto3" json:"rcmd_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImMsg) Reset()         { *m = ImMsg{} }
func (m *ImMsg) String() string { return proto.CompactTextString(m) }
func (*ImMsg) ProtoMessage()    {}
func (*ImMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{0}
}
func (m *ImMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImMsg.Unmarshal(m, b)
}
func (m *ImMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImMsg.Marshal(b, m, deterministic)
}
func (dst *ImMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImMsg.Merge(dst, src)
}
func (m *ImMsg) XXX_Size() int {
	return xxx_messageInfo_ImMsg.Size(m)
}
func (m *ImMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ImMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ImMsg proto.InternalMessageInfo

func (m *ImMsg) GetType() ImMsgType {
	if m != nil {
		return m.Type
	}
	return ImMsgType_ImMsgTypeUnknown
}

func (m *ImMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ImMsg) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *ImMsg) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *ImMsg) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *ImMsg) GetImBusiType() uint32 {
	if m != nil {
		return m.ImBusiType
	}
	return 0
}

func (m *ImMsg) GetContentType() uint32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *ImMsg) GetImCmdType() uint32 {
	if m != nil {
		return m.ImCmdType
	}
	return 0
}

func (m *ImMsg) GetTransparentExtMsg() string {
	if m != nil {
		return m.TransparentExtMsg
	}
	return ""
}

func (m *ImMsg) GetRcmdContent() string {
	if m != nil {
		return m.RcmdContent
	}
	return ""
}

type GroupTimeLineMsg struct {
	// 群实例id
	GroupId         uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupTemplateId uint32   `protobuf:"varint,2,opt,name=group_template_id,json=groupTemplateId,proto3" json:"group_template_id,omitempty"`
	RoleIds         []uint32 `protobuf:"varint,3,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	// 用户id
	Uid                  uint32        `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Msg                  *ImMsg        `protobuf:"bytes,5,opt,name=msg,proto3" json:"msg,omitempty"`
	GroupSendType        GroupSendType `protobuf:"varint,6,opt,name=group_send_type,json=groupSendType,proto3,enum=aigc_soulmate_middle.GroupSendType" json:"group_send_type,omitempty"`
	AtUids               []uint32      `protobuf:"varint,7,rep,packed,name=at_uids,json=atUids,proto3" json:"at_uids,omitempty"`
	AtRoleIds            []uint32      `protobuf:"varint,8,rep,packed,name=at_role_ids,json=atRoleIds,proto3" json:"at_role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GroupTimeLineMsg) Reset()         { *m = GroupTimeLineMsg{} }
func (m *GroupTimeLineMsg) String() string { return proto.CompactTextString(m) }
func (*GroupTimeLineMsg) ProtoMessage()    {}
func (*GroupTimeLineMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{1}
}
func (m *GroupTimeLineMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupTimeLineMsg.Unmarshal(m, b)
}
func (m *GroupTimeLineMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupTimeLineMsg.Marshal(b, m, deterministic)
}
func (dst *GroupTimeLineMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTimeLineMsg.Merge(dst, src)
}
func (m *GroupTimeLineMsg) XXX_Size() int {
	return xxx_messageInfo_GroupTimeLineMsg.Size(m)
}
func (m *GroupTimeLineMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTimeLineMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTimeLineMsg proto.InternalMessageInfo

func (m *GroupTimeLineMsg) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupTimeLineMsg) GetGroupTemplateId() uint32 {
	if m != nil {
		return m.GroupTemplateId
	}
	return 0
}

func (m *GroupTimeLineMsg) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

func (m *GroupTimeLineMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GroupTimeLineMsg) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *GroupTimeLineMsg) GetGroupSendType() GroupSendType {
	if m != nil {
		return m.GroupSendType
	}
	return GroupSendType_GroupSendTypeUnknown
}

func (m *GroupTimeLineMsg) GetAtUids() []uint32 {
	if m != nil {
		return m.AtUids
	}
	return nil
}

func (m *GroupTimeLineMsg) GetAtRoleIds() []uint32 {
	if m != nil {
		return m.AtRoleIds
	}
	return nil
}

type SendOption struct {
	// 是否发送离线推送
	WithOfflinePush bool `protobuf:"varint,1,opt,name=with_offline_push,json=withOfflinePush,proto3" json:"with_offline_push,omitempty"`
	// 离线推送内容
	OfflinePushContent string `protobuf:"bytes,2,opt,name=offline_push_content,json=offlinePushContent,proto3" json:"offline_push_content,omitempty"`
	// 是否送审
	WithAudit bool `protobuf:"varint,3,opt,name=with_audit,json=withAudit,proto3" json:"with_audit,omitempty"`
	// 送审文本内容
	AuditText string `protobuf:"bytes,4,opt,name=audit_text,json=auditText,proto3" json:"audit_text,omitempty"`
	// 是否特殊存储的消息
	WithSpecialStorage   bool     `protobuf:"varint,5,opt,name=with_special_storage,json=withSpecialStorage,proto3" json:"with_special_storage,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendOption) Reset()         { *m = SendOption{} }
func (m *SendOption) String() string { return proto.CompactTextString(m) }
func (*SendOption) ProtoMessage()    {}
func (*SendOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{2}
}
func (m *SendOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendOption.Unmarshal(m, b)
}
func (m *SendOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendOption.Marshal(b, m, deterministic)
}
func (dst *SendOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendOption.Merge(dst, src)
}
func (m *SendOption) XXX_Size() int {
	return xxx_messageInfo_SendOption.Size(m)
}
func (m *SendOption) XXX_DiscardUnknown() {
	xxx_messageInfo_SendOption.DiscardUnknown(m)
}

var xxx_messageInfo_SendOption proto.InternalMessageInfo

func (m *SendOption) GetWithOfflinePush() bool {
	if m != nil {
		return m.WithOfflinePush
	}
	return false
}

func (m *SendOption) GetOfflinePushContent() string {
	if m != nil {
		return m.OfflinePushContent
	}
	return ""
}

func (m *SendOption) GetWithAudit() bool {
	if m != nil {
		return m.WithAudit
	}
	return false
}

func (m *SendOption) GetAuditText() string {
	if m != nil {
		return m.AuditText
	}
	return ""
}

func (m *SendOption) GetWithSpecialStorage() bool {
	if m != nil {
		return m.WithSpecialStorage
	}
	return false
}

type SendGroupImMsgReq struct {
	TimeLineMsg *GroupTimeLineMsg `protobuf:"bytes,1,opt,name=time_line_msg,json=timeLineMsg,proto3" json:"time_line_msg,omitempty"`
	// 发送消息可选参数
	Opt                  *SendOption `protobuf:"bytes,2,opt,name=opt,proto3" json:"opt,omitempty"`
	IsTrigger            bool        `protobuf:"varint,3,opt,name=is_trigger,json=isTrigger,proto3" json:"is_trigger,omitempty"`
	IsReachLimit         bool        `protobuf:"varint,4,opt,name=is_reach_limit,json=isReachLimit,proto3" json:"is_reach_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SendGroupImMsgReq) Reset()         { *m = SendGroupImMsgReq{} }
func (m *SendGroupImMsgReq) String() string { return proto.CompactTextString(m) }
func (*SendGroupImMsgReq) ProtoMessage()    {}
func (*SendGroupImMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{3}
}
func (m *SendGroupImMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGroupImMsgReq.Unmarshal(m, b)
}
func (m *SendGroupImMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGroupImMsgReq.Marshal(b, m, deterministic)
}
func (dst *SendGroupImMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGroupImMsgReq.Merge(dst, src)
}
func (m *SendGroupImMsgReq) XXX_Size() int {
	return xxx_messageInfo_SendGroupImMsgReq.Size(m)
}
func (m *SendGroupImMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGroupImMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendGroupImMsgReq proto.InternalMessageInfo

func (m *SendGroupImMsgReq) GetTimeLineMsg() *GroupTimeLineMsg {
	if m != nil {
		return m.TimeLineMsg
	}
	return nil
}

func (m *SendGroupImMsgReq) GetOpt() *SendOption {
	if m != nil {
		return m.Opt
	}
	return nil
}

func (m *SendGroupImMsgReq) GetIsTrigger() bool {
	if m != nil {
		return m.IsTrigger
	}
	return false
}

func (m *SendGroupImMsgReq) GetIsReachLimit() bool {
	if m != nil {
		return m.IsReachLimit
	}
	return false
}

type SendGroupImMsgResp struct {
	// 发送时间(毫秒)
	SentAt int64 `protobuf:"varint,1,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	// 消息唯一标识
	SeqId                uint32   `protobuf:"varint,2,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGroupImMsgResp) Reset()         { *m = SendGroupImMsgResp{} }
func (m *SendGroupImMsgResp) String() string { return proto.CompactTextString(m) }
func (*SendGroupImMsgResp) ProtoMessage()    {}
func (*SendGroupImMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{4}
}
func (m *SendGroupImMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGroupImMsgResp.Unmarshal(m, b)
}
func (m *SendGroupImMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGroupImMsgResp.Marshal(b, m, deterministic)
}
func (dst *SendGroupImMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGroupImMsgResp.Merge(dst, src)
}
func (m *SendGroupImMsgResp) XXX_Size() int {
	return xxx_messageInfo_SendGroupImMsgResp.Size(m)
}
func (m *SendGroupImMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGroupImMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendGroupImMsgResp proto.InternalMessageInfo

func (m *SendGroupImMsgResp) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *SendGroupImMsgResp) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

type BatchGetGroupLastMsgListReq struct {
	GroupIds             []uint32 `protobuf:"varint,1,rep,packed,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGroupLastMsgListReq) Reset()         { *m = BatchGetGroupLastMsgListReq{} }
func (m *BatchGetGroupLastMsgListReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGroupLastMsgListReq) ProtoMessage()    {}
func (*BatchGetGroupLastMsgListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{5}
}
func (m *BatchGetGroupLastMsgListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGroupLastMsgListReq.Unmarshal(m, b)
}
func (m *BatchGetGroupLastMsgListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGroupLastMsgListReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetGroupLastMsgListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGroupLastMsgListReq.Merge(dst, src)
}
func (m *BatchGetGroupLastMsgListReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetGroupLastMsgListReq.Size(m)
}
func (m *BatchGetGroupLastMsgListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGroupLastMsgListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGroupLastMsgListReq proto.InternalMessageInfo

func (m *BatchGetGroupLastMsgListReq) GetGroupIds() []uint32 {
	if m != nil {
		return m.GroupIds
	}
	return nil
}

type GroupLastMsg struct {
	GroupId              uint32            `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	TimeLineMsg          *GroupTimeLineMsg `protobuf:"bytes,2,opt,name=time_line_msg,json=timeLineMsg,proto3" json:"time_line_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GroupLastMsg) Reset()         { *m = GroupLastMsg{} }
func (m *GroupLastMsg) String() string { return proto.CompactTextString(m) }
func (*GroupLastMsg) ProtoMessage()    {}
func (*GroupLastMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{6}
}
func (m *GroupLastMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupLastMsg.Unmarshal(m, b)
}
func (m *GroupLastMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupLastMsg.Marshal(b, m, deterministic)
}
func (dst *GroupLastMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupLastMsg.Merge(dst, src)
}
func (m *GroupLastMsg) XXX_Size() int {
	return xxx_messageInfo_GroupLastMsg.Size(m)
}
func (m *GroupLastMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupLastMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GroupLastMsg proto.InternalMessageInfo

func (m *GroupLastMsg) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupLastMsg) GetTimeLineMsg() *GroupTimeLineMsg {
	if m != nil {
		return m.TimeLineMsg
	}
	return nil
}

type BatchGetGroupLastMsgListResp struct {
	GroupMsgs            []*GroupLastMsg `protobuf:"bytes,1,rep,name=group_msgs,json=groupMsgs,proto3" json:"group_msgs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetGroupLastMsgListResp) Reset()         { *m = BatchGetGroupLastMsgListResp{} }
func (m *BatchGetGroupLastMsgListResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGroupLastMsgListResp) ProtoMessage()    {}
func (*BatchGetGroupLastMsgListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{7}
}
func (m *BatchGetGroupLastMsgListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGroupLastMsgListResp.Unmarshal(m, b)
}
func (m *BatchGetGroupLastMsgListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGroupLastMsgListResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetGroupLastMsgListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGroupLastMsgListResp.Merge(dst, src)
}
func (m *BatchGetGroupLastMsgListResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetGroupLastMsgListResp.Size(m)
}
func (m *BatchGetGroupLastMsgListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGroupLastMsgListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGroupLastMsgListResp proto.InternalMessageInfo

func (m *BatchGetGroupLastMsgListResp) GetGroupMsgs() []*GroupLastMsg {
	if m != nil {
		return m.GroupMsgs
	}
	return nil
}

type GetGroupMsgListReq struct {
	GroupId uint32 `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	SeqId   uint32 `protobuf:"varint,2,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	// 一次拉取的数量
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupMsgListReq) Reset()         { *m = GetGroupMsgListReq{} }
func (m *GetGroupMsgListReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupMsgListReq) ProtoMessage()    {}
func (*GetGroupMsgListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{8}
}
func (m *GetGroupMsgListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMsgListReq.Unmarshal(m, b)
}
func (m *GetGroupMsgListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMsgListReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupMsgListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMsgListReq.Merge(dst, src)
}
func (m *GetGroupMsgListReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupMsgListReq.Size(m)
}
func (m *GetGroupMsgListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMsgListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMsgListReq proto.InternalMessageInfo

func (m *GetGroupMsgListReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GetGroupMsgListReq) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *GetGroupMsgListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetGroupMsgListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGroupMsgListResp struct {
	TimeLineMsgs         []*GroupTimeLineMsg `protobuf:"bytes,2,rep,name=time_line_msgs,json=timeLineMsgs,proto3" json:"time_line_msgs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetGroupMsgListResp) Reset()         { *m = GetGroupMsgListResp{} }
func (m *GetGroupMsgListResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupMsgListResp) ProtoMessage()    {}
func (*GetGroupMsgListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{9}
}
func (m *GetGroupMsgListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMsgListResp.Unmarshal(m, b)
}
func (m *GetGroupMsgListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMsgListResp.Marshal(b, m, deterministic)
}
func (dst *GetGroupMsgListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMsgListResp.Merge(dst, src)
}
func (m *GetGroupMsgListResp) XXX_Size() int {
	return xxx_messageInfo_GetGroupMsgListResp.Size(m)
}
func (m *GetGroupMsgListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMsgListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMsgListResp proto.InternalMessageInfo

func (m *GetGroupMsgListResp) GetTimeLineMsgs() []*GroupTimeLineMsg {
	if m != nil {
		return m.TimeLineMsgs
	}
	return nil
}

type GroupTemplateInfo struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 群名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 群聊描述
	Character string `protobuf:"bytes,3,opt,name=character,proto3" json:"character,omitempty"`
	// 群标签
	Tags []string `protobuf:"bytes,4,rep,name=tags,proto3" json:"tags,omitempty"`
	// 群头像
	Avatar string `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 群性别0:女 1:男 2：其他
	Sex int32 `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	// 聊天页背景图
	ChatBackgroundImg string `protobuf:"bytes,7,opt,name=chat_background_img,json=chatBackgroundImg,proto3" json:"chat_background_img,omitempty"`
	// 首页背景图
	HomeBackgroundImg string `protobuf:"bytes,8,opt,name=home_background_img,json=homeBackgroundImg,proto3" json:"home_background_img,omitempty"`
	// 群聊icon
	GroupIcon string `protobuf:"bytes,9,opt,name=group_icon,json=groupIcon,proto3" json:"group_icon,omitempty"`
	// 是否展示/曝光到首页 true:展示 false:不展示
	Exposed bool `protobuf:"varint,10,opt,name=exposed,proto3" json:"exposed,omitempty"`
	// 强插位置
	InsertPos uint32 `protobuf:"varint,11,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	// 角标 url
	CornerIcon string `protobuf:"bytes,12,opt,name=corner_icon,json=cornerIcon,proto3" json:"corner_icon,omitempty"`
	// 群组所属分类ID
	CategoryId string `protobuf:"bytes,13,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 配置点赞数
	ConfigLikeNum int32 `protobuf:"varint,14,opt,name=config_like_num,json=configLikeNum,proto3" json:"config_like_num,omitempty"`
	// 配置的角色id列表
	TemplateRoles []*GroupTemplateInfo_TemplateRole `protobuf:"bytes,15,rep,name=template_roles,json=templateRoles,proto3" json:"template_roles,omitempty"`
	// 展示在IMtab的标签
	ImTabTags string `protobuf:"bytes,16,opt,name=im_tab_tags,json=imTabTags,proto3" json:"im_tab_tags,omitempty"`
	// 类型，见aigc-group.proto GroupType
	GroupType uint32 `protobuf:"varint,17,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	// 多人剧本玩法额外字段
	ScriptInfo       *ScriptInfo                                       `protobuf:"bytes,18,opt,name=script_info,json=scriptInfo,proto3" json:"script_info,omitempty"`
	SuitableSex      []int32                                           `protobuf:"varint,19,rep,packed,name=suitable_sex,json=suitableSex,proto3" json:"suitable_sex,omitempty"`
	DefaultPrologues []*GroupTemplateInfo_TemplateRole_AIGroupPrologue `protobuf:"bytes,20,rep,name=default_prologues,json=defaultPrologues,proto3" json:"default_prologues,omitempty"`
	// 多用户玩法一键发言列表
	QuickSpeakTexts []string `protobuf:"bytes,21,rep,name=quick_speak_texts,json=quickSpeakTexts,proto3" json:"quick_speak_texts,omitempty"`
	// 离开策略 see aigc-group.proto enum LeaveStrategy
	LeaveStrategy        uint32   `protobuf:"varint,22,opt,name=leave_strategy,json=leaveStrategy,proto3" json:"leave_strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupTemplateInfo) Reset()         { *m = GroupTemplateInfo{} }
func (m *GroupTemplateInfo) String() string { return proto.CompactTextString(m) }
func (*GroupTemplateInfo) ProtoMessage()    {}
func (*GroupTemplateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{10}
}
func (m *GroupTemplateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupTemplateInfo.Unmarshal(m, b)
}
func (m *GroupTemplateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupTemplateInfo.Marshal(b, m, deterministic)
}
func (dst *GroupTemplateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTemplateInfo.Merge(dst, src)
}
func (m *GroupTemplateInfo) XXX_Size() int {
	return xxx_messageInfo_GroupTemplateInfo.Size(m)
}
func (m *GroupTemplateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTemplateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTemplateInfo proto.InternalMessageInfo

func (m *GroupTemplateInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupTemplateInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupTemplateInfo) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *GroupTemplateInfo) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GroupTemplateInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GroupTemplateInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GroupTemplateInfo) GetChatBackgroundImg() string {
	if m != nil {
		return m.ChatBackgroundImg
	}
	return ""
}

func (m *GroupTemplateInfo) GetHomeBackgroundImg() string {
	if m != nil {
		return m.HomeBackgroundImg
	}
	return ""
}

func (m *GroupTemplateInfo) GetGroupIcon() string {
	if m != nil {
		return m.GroupIcon
	}
	return ""
}

func (m *GroupTemplateInfo) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *GroupTemplateInfo) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *GroupTemplateInfo) GetCornerIcon() string {
	if m != nil {
		return m.CornerIcon
	}
	return ""
}

func (m *GroupTemplateInfo) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *GroupTemplateInfo) GetConfigLikeNum() int32 {
	if m != nil {
		return m.ConfigLikeNum
	}
	return 0
}

func (m *GroupTemplateInfo) GetTemplateRoles() []*GroupTemplateInfo_TemplateRole {
	if m != nil {
		return m.TemplateRoles
	}
	return nil
}

func (m *GroupTemplateInfo) GetImTabTags() string {
	if m != nil {
		return m.ImTabTags
	}
	return ""
}

func (m *GroupTemplateInfo) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GroupTemplateInfo) GetScriptInfo() *ScriptInfo {
	if m != nil {
		return m.ScriptInfo
	}
	return nil
}

func (m *GroupTemplateInfo) GetSuitableSex() []int32 {
	if m != nil {
		return m.SuitableSex
	}
	return nil
}

func (m *GroupTemplateInfo) GetDefaultPrologues() []*GroupTemplateInfo_TemplateRole_AIGroupPrologue {
	if m != nil {
		return m.DefaultPrologues
	}
	return nil
}

func (m *GroupTemplateInfo) GetQuickSpeakTexts() []string {
	if m != nil {
		return m.QuickSpeakTexts
	}
	return nil
}

func (m *GroupTemplateInfo) GetLeaveStrategy() uint32 {
	if m != nil {
		return m.LeaveStrategy
	}
	return 0
}

type GroupTemplateInfo_TemplateRole struct {
	RoleId uint32 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 音色
	Timbre string `protobuf:"bytes,4,opt,name=timbre,proto3" json:"timbre,omitempty"`
	// 性别 0:女 1:男 2:其他
	Sex int32 `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	// 外显用，角色描述
	DisplayCharacter string `protobuf:"bytes,6,opt,name=display_character,json=displayCharacter,proto3" json:"display_character,omitempty"`
	// 调用ai用，角色设定
	AiCharacter string `protobuf:"bytes,7,opt,name=ai_character,json=aiCharacter,proto3" json:"ai_character,omitempty"`
	// 调用ai用，角色群聊描述
	ChatRoleDesc string `protobuf:"bytes,8,opt,name=chat_role_desc,json=chatRoleDesc,proto3" json:"chat_role_desc,omitempty"`
	// 调用ai用，关系描述
	RelationCharacter string                                            `protobuf:"bytes,9,opt,name=relation_character,json=relationCharacter,proto3" json:"relation_character,omitempty"`
	Prologues         []*GroupTemplateInfo_TemplateRole_AIGroupPrologue `protobuf:"bytes,10,rep,name=prologues,proto3" json:"prologues,omitempty"`
	// 欢迎语
	WelcomePrologues     []*GroupTemplateInfo_TemplateRole_AIGroupPrologue `protobuf:"bytes,11,rep,name=welcome_prologues,json=welcomePrologues,proto3" json:"welcome_prologues,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *GroupTemplateInfo_TemplateRole) Reset()         { *m = GroupTemplateInfo_TemplateRole{} }
func (m *GroupTemplateInfo_TemplateRole) String() string { return proto.CompactTextString(m) }
func (*GroupTemplateInfo_TemplateRole) ProtoMessage()    {}
func (*GroupTemplateInfo_TemplateRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{10, 0}
}
func (m *GroupTemplateInfo_TemplateRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupTemplateInfo_TemplateRole.Unmarshal(m, b)
}
func (m *GroupTemplateInfo_TemplateRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupTemplateInfo_TemplateRole.Marshal(b, m, deterministic)
}
func (dst *GroupTemplateInfo_TemplateRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTemplateInfo_TemplateRole.Merge(dst, src)
}
func (m *GroupTemplateInfo_TemplateRole) XXX_Size() int {
	return xxx_messageInfo_GroupTemplateInfo_TemplateRole.Size(m)
}
func (m *GroupTemplateInfo_TemplateRole) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTemplateInfo_TemplateRole.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTemplateInfo_TemplateRole proto.InternalMessageInfo

func (m *GroupTemplateInfo_TemplateRole) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GroupTemplateInfo_TemplateRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupTemplateInfo_TemplateRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GroupTemplateInfo_TemplateRole) GetTimbre() string {
	if m != nil {
		return m.Timbre
	}
	return ""
}

func (m *GroupTemplateInfo_TemplateRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GroupTemplateInfo_TemplateRole) GetDisplayCharacter() string {
	if m != nil {
		return m.DisplayCharacter
	}
	return ""
}

func (m *GroupTemplateInfo_TemplateRole) GetAiCharacter() string {
	if m != nil {
		return m.AiCharacter
	}
	return ""
}

func (m *GroupTemplateInfo_TemplateRole) GetChatRoleDesc() string {
	if m != nil {
		return m.ChatRoleDesc
	}
	return ""
}

func (m *GroupTemplateInfo_TemplateRole) GetRelationCharacter() string {
	if m != nil {
		return m.RelationCharacter
	}
	return ""
}

func (m *GroupTemplateInfo_TemplateRole) GetPrologues() []*GroupTemplateInfo_TemplateRole_AIGroupPrologue {
	if m != nil {
		return m.Prologues
	}
	return nil
}

func (m *GroupTemplateInfo_TemplateRole) GetWelcomePrologues() []*GroupTemplateInfo_TemplateRole_AIGroupPrologue {
	if m != nil {
		return m.WelcomePrologues
	}
	return nil
}

// 群开场白
type GroupTemplateInfo_TemplateRole_AIGroupPrologue struct {
	// 开场白文本内容
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 开场白语音链接
	Audio string `protobuf:"bytes,2,opt,name=audio,proto3" json:"audio,omitempty"`
	// 开场白顺序
	Priority             uint32   `protobuf:"varint,3,opt,name=priority,proto3" json:"priority,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupTemplateInfo_TemplateRole_AIGroupPrologue) Reset() {
	*m = GroupTemplateInfo_TemplateRole_AIGroupPrologue{}
}
func (m *GroupTemplateInfo_TemplateRole_AIGroupPrologue) String() string {
	return proto.CompactTextString(m)
}
func (*GroupTemplateInfo_TemplateRole_AIGroupPrologue) ProtoMessage() {}
func (*GroupTemplateInfo_TemplateRole_AIGroupPrologue) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{10, 0, 0}
}
func (m *GroupTemplateInfo_TemplateRole_AIGroupPrologue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupTemplateInfo_TemplateRole_AIGroupPrologue.Unmarshal(m, b)
}
func (m *GroupTemplateInfo_TemplateRole_AIGroupPrologue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupTemplateInfo_TemplateRole_AIGroupPrologue.Marshal(b, m, deterministic)
}
func (dst *GroupTemplateInfo_TemplateRole_AIGroupPrologue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTemplateInfo_TemplateRole_AIGroupPrologue.Merge(dst, src)
}
func (m *GroupTemplateInfo_TemplateRole_AIGroupPrologue) XXX_Size() int {
	return xxx_messageInfo_GroupTemplateInfo_TemplateRole_AIGroupPrologue.Size(m)
}
func (m *GroupTemplateInfo_TemplateRole_AIGroupPrologue) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTemplateInfo_TemplateRole_AIGroupPrologue.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTemplateInfo_TemplateRole_AIGroupPrologue proto.InternalMessageInfo

func (m *GroupTemplateInfo_TemplateRole_AIGroupPrologue) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GroupTemplateInfo_TemplateRole_AIGroupPrologue) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *GroupTemplateInfo_TemplateRole_AIGroupPrologue) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

// 用户扮演的角色信息
type PlayRole struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Avatar               string   `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	SuitableSex          []int32  `protobuf:"varint,4,rep,packed,name=suitable_sex,json=suitableSex,proto3" json:"suitable_sex,omitempty"`
	Character            string   `protobuf:"bytes,5,opt,name=character,proto3" json:"character,omitempty"`
	QuickSpeakTexts      []string `protobuf:"bytes,6,rep,name=quick_speak_texts,json=quickSpeakTexts,proto3" json:"quick_speak_texts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayRole) Reset()         { *m = PlayRole{} }
func (m *PlayRole) String() string { return proto.CompactTextString(m) }
func (*PlayRole) ProtoMessage()    {}
func (*PlayRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{11}
}
func (m *PlayRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayRole.Unmarshal(m, b)
}
func (m *PlayRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayRole.Marshal(b, m, deterministic)
}
func (dst *PlayRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayRole.Merge(dst, src)
}
func (m *PlayRole) XXX_Size() int {
	return xxx_messageInfo_PlayRole.Size(m)
}
func (m *PlayRole) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayRole.DiscardUnknown(m)
}

var xxx_messageInfo_PlayRole proto.InternalMessageInfo

func (m *PlayRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PlayRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *PlayRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PlayRole) GetSuitableSex() []int32 {
	if m != nil {
		return m.SuitableSex
	}
	return nil
}

func (m *PlayRole) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *PlayRole) GetQuickSpeakTexts() []string {
	if m != nil {
		return m.QuickSpeakTexts
	}
	return nil
}

type ScriptInfo struct {
	PlayRoles            []*PlayRole `protobuf:"bytes,1,rep,name=play_roles,json=playRoles,proto3" json:"play_roles,omitempty"`
	ButtonDisplayText    string      `protobuf:"bytes,2,opt,name=button_display_text,json=buttonDisplayText,proto3" json:"button_display_text,omitempty"`
	BackgroundMusic      string      `protobuf:"bytes,3,opt,name=background_music,json=backgroundMusic,proto3" json:"background_music,omitempty"`
	UserNum              uint32      `protobuf:"varint,4,opt,name=user_num,json=userNum,proto3" json:"user_num,omitempty"`
	MaleNum              uint32      `protobuf:"varint,6,opt,name=male_num,json=maleNum,proto3" json:"male_num,omitempty"`
	FemaleNum            uint32      `protobuf:"varint,7,opt,name=female_num,json=femaleNum,proto3" json:"female_num,omitempty"`
	Sort                 uint32      `protobuf:"varint,8,opt,name=sort,proto3" json:"sort,omitempty"`
	AnyNum               uint32      `protobuf:"varint,9,opt,name=any_num,json=anyNum,proto3" json:"any_num,omitempty"`
	MatchStrategy        uint32      `protobuf:"varint,10,opt,name=match_strategy,json=matchStrategy,proto3" json:"match_strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ScriptInfo) Reset()         { *m = ScriptInfo{} }
func (m *ScriptInfo) String() string { return proto.CompactTextString(m) }
func (*ScriptInfo) ProtoMessage()    {}
func (*ScriptInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{12}
}
func (m *ScriptInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScriptInfo.Unmarshal(m, b)
}
func (m *ScriptInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScriptInfo.Marshal(b, m, deterministic)
}
func (dst *ScriptInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScriptInfo.Merge(dst, src)
}
func (m *ScriptInfo) XXX_Size() int {
	return xxx_messageInfo_ScriptInfo.Size(m)
}
func (m *ScriptInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScriptInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScriptInfo proto.InternalMessageInfo

func (m *ScriptInfo) GetPlayRoles() []*PlayRole {
	if m != nil {
		return m.PlayRoles
	}
	return nil
}

func (m *ScriptInfo) GetButtonDisplayText() string {
	if m != nil {
		return m.ButtonDisplayText
	}
	return ""
}

func (m *ScriptInfo) GetBackgroundMusic() string {
	if m != nil {
		return m.BackgroundMusic
	}
	return ""
}

func (m *ScriptInfo) GetUserNum() uint32 {
	if m != nil {
		return m.UserNum
	}
	return 0
}

func (m *ScriptInfo) GetMaleNum() uint32 {
	if m != nil {
		return m.MaleNum
	}
	return 0
}

func (m *ScriptInfo) GetFemaleNum() uint32 {
	if m != nil {
		return m.FemaleNum
	}
	return 0
}

func (m *ScriptInfo) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *ScriptInfo) GetAnyNum() uint32 {
	if m != nil {
		return m.AnyNum
	}
	return 0
}

func (m *ScriptInfo) GetMatchStrategy() uint32 {
	if m != nil {
		return m.MatchStrategy
	}
	return 0
}

// 创建群模板
type CreateGroupTemplateReq struct {
	GroupTemplate        *GroupTemplateInfo `protobuf:"bytes,1,opt,name=group_template,json=groupTemplate,proto3" json:"group_template,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CreateGroupTemplateReq) Reset()         { *m = CreateGroupTemplateReq{} }
func (m *CreateGroupTemplateReq) String() string { return proto.CompactTextString(m) }
func (*CreateGroupTemplateReq) ProtoMessage()    {}
func (*CreateGroupTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{13}
}
func (m *CreateGroupTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupTemplateReq.Unmarshal(m, b)
}
func (m *CreateGroupTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupTemplateReq.Marshal(b, m, deterministic)
}
func (dst *CreateGroupTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupTemplateReq.Merge(dst, src)
}
func (m *CreateGroupTemplateReq) XXX_Size() int {
	return xxx_messageInfo_CreateGroupTemplateReq.Size(m)
}
func (m *CreateGroupTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupTemplateReq proto.InternalMessageInfo

func (m *CreateGroupTemplateReq) GetGroupTemplate() *GroupTemplateInfo {
	if m != nil {
		return m.GroupTemplate
	}
	return nil
}

type CreateGroupTemplateResp struct {
	TemplateId           uint32   `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGroupTemplateResp) Reset()         { *m = CreateGroupTemplateResp{} }
func (m *CreateGroupTemplateResp) String() string { return proto.CompactTextString(m) }
func (*CreateGroupTemplateResp) ProtoMessage()    {}
func (*CreateGroupTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{14}
}
func (m *CreateGroupTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupTemplateResp.Unmarshal(m, b)
}
func (m *CreateGroupTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupTemplateResp.Marshal(b, m, deterministic)
}
func (dst *CreateGroupTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupTemplateResp.Merge(dst, src)
}
func (m *CreateGroupTemplateResp) XXX_Size() int {
	return xxx_messageInfo_CreateGroupTemplateResp.Size(m)
}
func (m *CreateGroupTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupTemplateResp proto.InternalMessageInfo

func (m *CreateGroupTemplateResp) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

// 更新群模板
type UpdateGroupTemplateReq struct {
	GroupTemplate        *GroupTemplateInfo `protobuf:"bytes,1,opt,name=group_template,json=groupTemplate,proto3" json:"group_template,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateGroupTemplateReq) Reset()         { *m = UpdateGroupTemplateReq{} }
func (m *UpdateGroupTemplateReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupTemplateReq) ProtoMessage()    {}
func (*UpdateGroupTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{15}
}
func (m *UpdateGroupTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupTemplateReq.Unmarshal(m, b)
}
func (m *UpdateGroupTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupTemplateReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupTemplateReq.Merge(dst, src)
}
func (m *UpdateGroupTemplateReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupTemplateReq.Size(m)
}
func (m *UpdateGroupTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupTemplateReq proto.InternalMessageInfo

func (m *UpdateGroupTemplateReq) GetGroupTemplate() *GroupTemplateInfo {
	if m != nil {
		return m.GroupTemplate
	}
	return nil
}

type UpdateGroupTemplateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupTemplateResp) Reset()         { *m = UpdateGroupTemplateResp{} }
func (m *UpdateGroupTemplateResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupTemplateResp) ProtoMessage()    {}
func (*UpdateGroupTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{16}
}
func (m *UpdateGroupTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupTemplateResp.Unmarshal(m, b)
}
func (m *UpdateGroupTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupTemplateResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupTemplateResp.Merge(dst, src)
}
func (m *UpdateGroupTemplateResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupTemplateResp.Size(m)
}
func (m *UpdateGroupTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupTemplateResp proto.InternalMessageInfo

// 删除群模板
type DeleteGroupTemplateReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGroupTemplateReq) Reset()         { *m = DeleteGroupTemplateReq{} }
func (m *DeleteGroupTemplateReq) String() string { return proto.CompactTextString(m) }
func (*DeleteGroupTemplateReq) ProtoMessage()    {}
func (*DeleteGroupTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{17}
}
func (m *DeleteGroupTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGroupTemplateReq.Unmarshal(m, b)
}
func (m *DeleteGroupTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGroupTemplateReq.Marshal(b, m, deterministic)
}
func (dst *DeleteGroupTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGroupTemplateReq.Merge(dst, src)
}
func (m *DeleteGroupTemplateReq) XXX_Size() int {
	return xxx_messageInfo_DeleteGroupTemplateReq.Size(m)
}
func (m *DeleteGroupTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGroupTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGroupTemplateReq proto.InternalMessageInfo

func (m *DeleteGroupTemplateReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteGroupTemplateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGroupTemplateResp) Reset()         { *m = DeleteGroupTemplateResp{} }
func (m *DeleteGroupTemplateResp) String() string { return proto.CompactTextString(m) }
func (*DeleteGroupTemplateResp) ProtoMessage()    {}
func (*DeleteGroupTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{18}
}
func (m *DeleteGroupTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGroupTemplateResp.Unmarshal(m, b)
}
func (m *DeleteGroupTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGroupTemplateResp.Marshal(b, m, deterministic)
}
func (dst *DeleteGroupTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGroupTemplateResp.Merge(dst, src)
}
func (m *DeleteGroupTemplateResp) XXX_Size() int {
	return xxx_messageInfo_DeleteGroupTemplateResp.Size(m)
}
func (m *DeleteGroupTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGroupTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGroupTemplateResp proto.InternalMessageInfo

// 根据id获取群模板列表
type GetGroupTemplateByIdReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupTemplateByIdReq) Reset()         { *m = GetGroupTemplateByIdReq{} }
func (m *GetGroupTemplateByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupTemplateByIdReq) ProtoMessage()    {}
func (*GetGroupTemplateByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{19}
}
func (m *GetGroupTemplateByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupTemplateByIdReq.Unmarshal(m, b)
}
func (m *GetGroupTemplateByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupTemplateByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupTemplateByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupTemplateByIdReq.Merge(dst, src)
}
func (m *GetGroupTemplateByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupTemplateByIdReq.Size(m)
}
func (m *GetGroupTemplateByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupTemplateByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupTemplateByIdReq proto.InternalMessageInfo

func (m *GetGroupTemplateByIdReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetGroupTemplateByIdResp struct {
	TemplateInfo         *GroupTemplateInfo `protobuf:"bytes,1,opt,name=template_info,json=templateInfo,proto3" json:"template_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGroupTemplateByIdResp) Reset()         { *m = GetGroupTemplateByIdResp{} }
func (m *GetGroupTemplateByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupTemplateByIdResp) ProtoMessage()    {}
func (*GetGroupTemplateByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{20}
}
func (m *GetGroupTemplateByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupTemplateByIdResp.Unmarshal(m, b)
}
func (m *GetGroupTemplateByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupTemplateByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetGroupTemplateByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupTemplateByIdResp.Merge(dst, src)
}
func (m *GetGroupTemplateByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetGroupTemplateByIdResp.Size(m)
}
func (m *GetGroupTemplateByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupTemplateByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupTemplateByIdResp proto.InternalMessageInfo

func (m *GetGroupTemplateByIdResp) GetTemplateInfo() *GroupTemplateInfo {
	if m != nil {
		return m.TemplateInfo
	}
	return nil
}

// 分页获取群模板
type GetGroupTemplateByPageReq struct {
	Page                 int64    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 int64    `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	GroupType            []uint32 `protobuf:"varint,3,rep,packed,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupTemplateByPageReq) Reset()         { *m = GetGroupTemplateByPageReq{} }
func (m *GetGroupTemplateByPageReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupTemplateByPageReq) ProtoMessage()    {}
func (*GetGroupTemplateByPageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{21}
}
func (m *GetGroupTemplateByPageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupTemplateByPageReq.Unmarshal(m, b)
}
func (m *GetGroupTemplateByPageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupTemplateByPageReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupTemplateByPageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupTemplateByPageReq.Merge(dst, src)
}
func (m *GetGroupTemplateByPageReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupTemplateByPageReq.Size(m)
}
func (m *GetGroupTemplateByPageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupTemplateByPageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupTemplateByPageReq proto.InternalMessageInfo

func (m *GetGroupTemplateByPageReq) GetPage() int64 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGroupTemplateByPageReq) GetSize() int64 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetGroupTemplateByPageReq) GetGroupType() []uint32 {
	if m != nil {
		return m.GroupType
	}
	return nil
}

type GetGroupTemplateByPageResp struct {
	List                 []*GroupTemplateInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                int64                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetGroupTemplateByPageResp) Reset()         { *m = GetGroupTemplateByPageResp{} }
func (m *GetGroupTemplateByPageResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupTemplateByPageResp) ProtoMessage()    {}
func (*GetGroupTemplateByPageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{22}
}
func (m *GetGroupTemplateByPageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupTemplateByPageResp.Unmarshal(m, b)
}
func (m *GetGroupTemplateByPageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupTemplateByPageResp.Marshal(b, m, deterministic)
}
func (dst *GetGroupTemplateByPageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupTemplateByPageResp.Merge(dst, src)
}
func (m *GetGroupTemplateByPageResp) XXX_Size() int {
	return xxx_messageInfo_GetGroupTemplateByPageResp.Size(m)
}
func (m *GetGroupTemplateByPageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupTemplateByPageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupTemplateByPageResp proto.InternalMessageInfo

func (m *GetGroupTemplateByPageResp) GetList() []*GroupTemplateInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetGroupTemplateByPageResp) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type Entity struct {
	// e.g:partner_id
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 亲密度主体类型
	Type                 Entity_Type `protobuf:"varint,2,opt,name=type,proto3,enum=aigc_soulmate_middle.Entity_Type" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Entity) Reset()         { *m = Entity{} }
func (m *Entity) String() string { return proto.CompactTextString(m) }
func (*Entity) ProtoMessage()    {}
func (*Entity) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{23}
}
func (m *Entity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Entity.Unmarshal(m, b)
}
func (m *Entity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Entity.Marshal(b, m, deterministic)
}
func (dst *Entity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Entity.Merge(dst, src)
}
func (m *Entity) XXX_Size() int {
	return xxx_messageInfo_Entity.Size(m)
}
func (m *Entity) XXX_DiscardUnknown() {
	xxx_messageInfo_Entity.DiscardUnknown(m)
}

var xxx_messageInfo_Entity proto.InternalMessageInfo

func (m *Entity) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Entity) GetType() Entity_Type {
	if m != nil {
		return m.Type
	}
	return Entity_TYPE_UNSPECIFIED
}

type GetSentenceAvailableCountReq struct {
	// 句子类型
	Type                 []SentenceType `protobuf:"varint,1,rep,packed,name=type,proto3,enum=aigc_soulmate_middle.SentenceType" json:"type,omitempty"`
	Entity               *Entity        `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	Uid                  uint32         `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetSentenceAvailableCountReq) Reset()         { *m = GetSentenceAvailableCountReq{} }
func (m *GetSentenceAvailableCountReq) String() string { return proto.CompactTextString(m) }
func (*GetSentenceAvailableCountReq) ProtoMessage()    {}
func (*GetSentenceAvailableCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{24}
}
func (m *GetSentenceAvailableCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSentenceAvailableCountReq.Unmarshal(m, b)
}
func (m *GetSentenceAvailableCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSentenceAvailableCountReq.Marshal(b, m, deterministic)
}
func (dst *GetSentenceAvailableCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSentenceAvailableCountReq.Merge(dst, src)
}
func (m *GetSentenceAvailableCountReq) XXX_Size() int {
	return xxx_messageInfo_GetSentenceAvailableCountReq.Size(m)
}
func (m *GetSentenceAvailableCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSentenceAvailableCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSentenceAvailableCountReq proto.InternalMessageInfo

func (m *GetSentenceAvailableCountReq) GetType() []SentenceType {
	if m != nil {
		return m.Type
	}
	return nil
}

func (m *GetSentenceAvailableCountReq) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *GetSentenceAvailableCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSentenceAvailableCountResp struct {
	AvailableCountMap    map[uint32]*GetSentenceAvailableCountResp_SentenceCount `protobuf:"bytes,1,rep,name=available_count_map,json=availableCountMap,proto3" json:"available_count_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                `json:"-"`
	XXX_unrecognized     []byte                                                  `json:"-"`
	XXX_sizecache        int32                                                   `json:"-"`
}

func (m *GetSentenceAvailableCountResp) Reset()         { *m = GetSentenceAvailableCountResp{} }
func (m *GetSentenceAvailableCountResp) String() string { return proto.CompactTextString(m) }
func (*GetSentenceAvailableCountResp) ProtoMessage()    {}
func (*GetSentenceAvailableCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{25}
}
func (m *GetSentenceAvailableCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSentenceAvailableCountResp.Unmarshal(m, b)
}
func (m *GetSentenceAvailableCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSentenceAvailableCountResp.Marshal(b, m, deterministic)
}
func (dst *GetSentenceAvailableCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSentenceAvailableCountResp.Merge(dst, src)
}
func (m *GetSentenceAvailableCountResp) XXX_Size() int {
	return xxx_messageInfo_GetSentenceAvailableCountResp.Size(m)
}
func (m *GetSentenceAvailableCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSentenceAvailableCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSentenceAvailableCountResp proto.InternalMessageInfo

func (m *GetSentenceAvailableCountResp) GetAvailableCountMap() map[uint32]*GetSentenceAvailableCountResp_SentenceCount {
	if m != nil {
		return m.AvailableCountMap
	}
	return nil
}

type GetSentenceAvailableCountResp_SentenceCount struct {
	ConfigNum            uint32   `protobuf:"varint,1,opt,name=config_num,json=configNum,proto3" json:"config_num,omitempty"`
	AvailableNum         uint32   `protobuf:"varint,2,opt,name=available_num,json=availableNum,proto3" json:"available_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSentenceAvailableCountResp_SentenceCount) Reset() {
	*m = GetSentenceAvailableCountResp_SentenceCount{}
}
func (m *GetSentenceAvailableCountResp_SentenceCount) String() string {
	return proto.CompactTextString(m)
}
func (*GetSentenceAvailableCountResp_SentenceCount) ProtoMessage() {}
func (*GetSentenceAvailableCountResp_SentenceCount) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{25, 0}
}
func (m *GetSentenceAvailableCountResp_SentenceCount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSentenceAvailableCountResp_SentenceCount.Unmarshal(m, b)
}
func (m *GetSentenceAvailableCountResp_SentenceCount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSentenceAvailableCountResp_SentenceCount.Marshal(b, m, deterministic)
}
func (dst *GetSentenceAvailableCountResp_SentenceCount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSentenceAvailableCountResp_SentenceCount.Merge(dst, src)
}
func (m *GetSentenceAvailableCountResp_SentenceCount) XXX_Size() int {
	return xxx_messageInfo_GetSentenceAvailableCountResp_SentenceCount.Size(m)
}
func (m *GetSentenceAvailableCountResp_SentenceCount) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSentenceAvailableCountResp_SentenceCount.DiscardUnknown(m)
}

var xxx_messageInfo_GetSentenceAvailableCountResp_SentenceCount proto.InternalMessageInfo

func (m *GetSentenceAvailableCountResp_SentenceCount) GetConfigNum() uint32 {
	if m != nil {
		return m.ConfigNum
	}
	return 0
}

func (m *GetSentenceAvailableCountResp_SentenceCount) GetAvailableNum() uint32 {
	if m != nil {
		return m.AvailableNum
	}
	return 0
}

type GetUserSentenceCountReq struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type                 SentenceType `protobuf:"varint,2,opt,name=type,proto3,enum=aigc_soulmate_middle.SentenceType" json:"type,omitempty"`
	EntityType           Entity_Type  `protobuf:"varint,3,opt,name=entity_type,json=entityType,proto3,enum=aigc_soulmate_middle.Entity_Type" json:"entity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserSentenceCountReq) Reset()         { *m = GetUserSentenceCountReq{} }
func (m *GetUserSentenceCountReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSentenceCountReq) ProtoMessage()    {}
func (*GetUserSentenceCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{26}
}
func (m *GetUserSentenceCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSentenceCountReq.Unmarshal(m, b)
}
func (m *GetUserSentenceCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSentenceCountReq.Marshal(b, m, deterministic)
}
func (dst *GetUserSentenceCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSentenceCountReq.Merge(dst, src)
}
func (m *GetUserSentenceCountReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSentenceCountReq.Size(m)
}
func (m *GetUserSentenceCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSentenceCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSentenceCountReq proto.InternalMessageInfo

func (m *GetUserSentenceCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserSentenceCountReq) GetType() SentenceType {
	if m != nil {
		return m.Type
	}
	return SentenceType_SENTENCE_TYPE_UNSPECIFIED
}

func (m *GetUserSentenceCountReq) GetEntityType() Entity_Type {
	if m != nil {
		return m.EntityType
	}
	return Entity_TYPE_UNSPECIFIED
}

type GetUserSentenceCountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSentenceCountResp) Reset()         { *m = GetUserSentenceCountResp{} }
func (m *GetUserSentenceCountResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSentenceCountResp) ProtoMessage()    {}
func (*GetUserSentenceCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{27}
}
func (m *GetUserSentenceCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSentenceCountResp.Unmarshal(m, b)
}
func (m *GetUserSentenceCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSentenceCountResp.Marshal(b, m, deterministic)
}
func (dst *GetUserSentenceCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSentenceCountResp.Merge(dst, src)
}
func (m *GetUserSentenceCountResp) XXX_Size() int {
	return xxx_messageInfo_GetUserSentenceCountResp.Size(m)
}
func (m *GetUserSentenceCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSentenceCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSentenceCountResp proto.InternalMessageInfo

func (m *GetUserSentenceCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type IsReachLimitReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Entity               *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	BusinessType         uint32   `protobuf:"varint,3,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsReachLimitReq) Reset()         { *m = IsReachLimitReq{} }
func (m *IsReachLimitReq) String() string { return proto.CompactTextString(m) }
func (*IsReachLimitReq) ProtoMessage()    {}
func (*IsReachLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{28}
}
func (m *IsReachLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsReachLimitReq.Unmarshal(m, b)
}
func (m *IsReachLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsReachLimitReq.Marshal(b, m, deterministic)
}
func (dst *IsReachLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsReachLimitReq.Merge(dst, src)
}
func (m *IsReachLimitReq) XXX_Size() int {
	return xxx_messageInfo_IsReachLimitReq.Size(m)
}
func (m *IsReachLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsReachLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsReachLimitReq proto.InternalMessageInfo

func (m *IsReachLimitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IsReachLimitReq) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *IsReachLimitReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type IsReachLimitResp struct {
	IsReach              bool     `protobuf:"varint,1,opt,name=is_reach,json=isReach,proto3" json:"is_reach,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsReachLimitResp) Reset()         { *m = IsReachLimitResp{} }
func (m *IsReachLimitResp) String() string { return proto.CompactTextString(m) }
func (*IsReachLimitResp) ProtoMessage()    {}
func (*IsReachLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{29}
}
func (m *IsReachLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsReachLimitResp.Unmarshal(m, b)
}
func (m *IsReachLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsReachLimitResp.Marshal(b, m, deterministic)
}
func (dst *IsReachLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsReachLimitResp.Merge(dst, src)
}
func (m *IsReachLimitResp) XXX_Size() int {
	return xxx_messageInfo_IsReachLimitResp.Size(m)
}
func (m *IsReachLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsReachLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsReachLimitResp proto.InternalMessageInfo

func (m *IsReachLimitResp) GetIsReach() bool {
	if m != nil {
		return m.IsReach
	}
	return false
}

type ConsumeSentenceCountReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Entity               *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	RoleType             uint32   `protobuf:"varint,3,opt,name=role_type,json=roleType,proto3" json:"role_type,omitempty"`
	BusinessType         uint32   `protobuf:"varint,4,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeSentenceCountReq) Reset()         { *m = ConsumeSentenceCountReq{} }
func (m *ConsumeSentenceCountReq) String() string { return proto.CompactTextString(m) }
func (*ConsumeSentenceCountReq) ProtoMessage()    {}
func (*ConsumeSentenceCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{30}
}
func (m *ConsumeSentenceCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeSentenceCountReq.Unmarshal(m, b)
}
func (m *ConsumeSentenceCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeSentenceCountReq.Marshal(b, m, deterministic)
}
func (dst *ConsumeSentenceCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeSentenceCountReq.Merge(dst, src)
}
func (m *ConsumeSentenceCountReq) XXX_Size() int {
	return xxx_messageInfo_ConsumeSentenceCountReq.Size(m)
}
func (m *ConsumeSentenceCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeSentenceCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeSentenceCountReq proto.InternalMessageInfo

func (m *ConsumeSentenceCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConsumeSentenceCountReq) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *ConsumeSentenceCountReq) GetRoleType() uint32 {
	if m != nil {
		return m.RoleType
	}
	return 0
}

func (m *ConsumeSentenceCountReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type ConsumeSentenceCountResp struct {
	Success              bool         `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	NeedSpecialTip       bool         `protobuf:"varint,2,opt,name=need_special_tip,json=needSpecialTip,proto3" json:"need_special_tip,omitempty"`
	NeedCurDayTip        bool         `protobuf:"varint,3,opt,name=need_cur_day_tip,json=needCurDayTip,proto3" json:"need_cur_day_tip,omitempty"`
	NeedExtraTip         bool         `protobuf:"varint,4,opt,name=need_extra_tip,json=needExtraTip,proto3" json:"need_extra_tip,omitempty"`
	UsedType             SentenceType `protobuf:"varint,5,opt,name=used_type,json=usedType,proto3,enum=aigc_soulmate_middle.SentenceType" json:"used_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ConsumeSentenceCountResp) Reset()         { *m = ConsumeSentenceCountResp{} }
func (m *ConsumeSentenceCountResp) String() string { return proto.CompactTextString(m) }
func (*ConsumeSentenceCountResp) ProtoMessage()    {}
func (*ConsumeSentenceCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{31}
}
func (m *ConsumeSentenceCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeSentenceCountResp.Unmarshal(m, b)
}
func (m *ConsumeSentenceCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeSentenceCountResp.Marshal(b, m, deterministic)
}
func (dst *ConsumeSentenceCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeSentenceCountResp.Merge(dst, src)
}
func (m *ConsumeSentenceCountResp) XXX_Size() int {
	return xxx_messageInfo_ConsumeSentenceCountResp.Size(m)
}
func (m *ConsumeSentenceCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeSentenceCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeSentenceCountResp proto.InternalMessageInfo

func (m *ConsumeSentenceCountResp) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *ConsumeSentenceCountResp) GetNeedSpecialTip() bool {
	if m != nil {
		return m.NeedSpecialTip
	}
	return false
}

func (m *ConsumeSentenceCountResp) GetNeedCurDayTip() bool {
	if m != nil {
		return m.NeedCurDayTip
	}
	return false
}

func (m *ConsumeSentenceCountResp) GetNeedExtraTip() bool {
	if m != nil {
		return m.NeedExtraTip
	}
	return false
}

func (m *ConsumeSentenceCountResp) GetUsedType() SentenceType {
	if m != nil {
		return m.UsedType
	}
	return SentenceType_SENTENCE_TYPE_UNSPECIFIED
}

// 发句数提示需要的参数
type GetTipsCountRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Entity               *Entity  `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	SentenceType         []uint32 `protobuf:"varint,3,rep,packed,name=sentence_type,json=sentenceType,proto3" json:"sentence_type,omitempty"`
	BusinessType         uint32   `protobuf:"varint,4,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	RoleType             uint32   `protobuf:"varint,5,opt,name=role_type,json=roleType,proto3" json:"role_type,omitempty"`
	TipType              uint32   `protobuf:"varint,6,opt,name=tip_type,json=tipType,proto3" json:"tip_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTipsCountRequest) Reset()         { *m = GetTipsCountRequest{} }
func (m *GetTipsCountRequest) String() string { return proto.CompactTextString(m) }
func (*GetTipsCountRequest) ProtoMessage()    {}
func (*GetTipsCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{32}
}
func (m *GetTipsCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTipsCountRequest.Unmarshal(m, b)
}
func (m *GetTipsCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTipsCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetTipsCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTipsCountRequest.Merge(dst, src)
}
func (m *GetTipsCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetTipsCountRequest.Size(m)
}
func (m *GetTipsCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTipsCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTipsCountRequest proto.InternalMessageInfo

func (m *GetTipsCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTipsCountRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *GetTipsCountRequest) GetSentenceType() []uint32 {
	if m != nil {
		return m.SentenceType
	}
	return nil
}

func (m *GetTipsCountRequest) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *GetTipsCountRequest) GetRoleType() uint32 {
	if m != nil {
		return m.RoleType
	}
	return 0
}

func (m *GetTipsCountRequest) GetTipType() uint32 {
	if m != nil {
		return m.TipType
	}
	return 0
}

type GetTipsCountResponse struct {
	CurDayCfgNum         uint32   `protobuf:"varint,1,opt,name=cur_day_cfg_num,json=curDayCfgNum,proto3" json:"cur_day_cfg_num,omitempty"`
	ExtraAvailableNum    uint32   `protobuf:"varint,2,opt,name=extra_available_num,json=extraAvailableNum,proto3" json:"extra_available_num,omitempty"`
	RoleSpecifiedCfgNum  uint32   `protobuf:"varint,3,opt,name=role_specified_cfg_num,json=roleSpecifiedCfgNum,proto3" json:"role_specified_cfg_num,omitempty"`
	AvailableCurDayNum   uint32   `protobuf:"varint,4,opt,name=available_cur_day_num,json=availableCurDayNum,proto3" json:"available_cur_day_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTipsCountResponse) Reset()         { *m = GetTipsCountResponse{} }
func (m *GetTipsCountResponse) String() string { return proto.CompactTextString(m) }
func (*GetTipsCountResponse) ProtoMessage()    {}
func (*GetTipsCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{33}
}
func (m *GetTipsCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTipsCountResponse.Unmarshal(m, b)
}
func (m *GetTipsCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTipsCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetTipsCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTipsCountResponse.Merge(dst, src)
}
func (m *GetTipsCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetTipsCountResponse.Size(m)
}
func (m *GetTipsCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTipsCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTipsCountResponse proto.InternalMessageInfo

func (m *GetTipsCountResponse) GetCurDayCfgNum() uint32 {
	if m != nil {
		return m.CurDayCfgNum
	}
	return 0
}

func (m *GetTipsCountResponse) GetExtraAvailableNum() uint32 {
	if m != nil {
		return m.ExtraAvailableNum
	}
	return 0
}

func (m *GetTipsCountResponse) GetRoleSpecifiedCfgNum() uint32 {
	if m != nil {
		return m.RoleSpecifiedCfgNum
	}
	return 0
}

func (m *GetTipsCountResponse) GetAvailableCurDayNum() uint32 {
	if m != nil {
		return m.AvailableCurDayNum
	}
	return 0
}

type User struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,6,opt,name=nickname,proto3" json:"nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *User) Reset()         { *m = User{} }
func (m *User) String() string { return proto.CompactTextString(m) }
func (*User) ProtoMessage()    {}
func (*User) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{34}
}
func (m *User) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_User.Unmarshal(m, b)
}
func (m *User) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_User.Marshal(b, m, deterministic)
}
func (dst *User) XXX_Merge(src proto.Message) {
	xxx_messageInfo_User.Merge(dst, src)
}
func (m *User) XXX_Size() int {
	return xxx_messageInfo_User.Size(m)
}
func (m *User) XXX_DiscardUnknown() {
	xxx_messageInfo_User.DiscardUnknown(m)
}

var xxx_messageInfo_User proto.InternalMessageInfo

func (m *User) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *User) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *User) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

type BatchAllocRoleUserRequest struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAllocRoleUserRequest) Reset()         { *m = BatchAllocRoleUserRequest{} }
func (m *BatchAllocRoleUserRequest) String() string { return proto.CompactTextString(m) }
func (*BatchAllocRoleUserRequest) ProtoMessage()    {}
func (*BatchAllocRoleUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{35}
}
func (m *BatchAllocRoleUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAllocRoleUserRequest.Unmarshal(m, b)
}
func (m *BatchAllocRoleUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAllocRoleUserRequest.Marshal(b, m, deterministic)
}
func (dst *BatchAllocRoleUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAllocRoleUserRequest.Merge(dst, src)
}
func (m *BatchAllocRoleUserRequest) XXX_Size() int {
	return xxx_messageInfo_BatchAllocRoleUserRequest.Size(m)
}
func (m *BatchAllocRoleUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAllocRoleUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAllocRoleUserRequest proto.InternalMessageInfo

func (m *BatchAllocRoleUserRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *BatchAllocRoleUserRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchAllocRoleUserResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAllocRoleUserResponse) Reset()         { *m = BatchAllocRoleUserResponse{} }
func (m *BatchAllocRoleUserResponse) String() string { return proto.CompactTextString(m) }
func (*BatchAllocRoleUserResponse) ProtoMessage()    {}
func (*BatchAllocRoleUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{36}
}
func (m *BatchAllocRoleUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAllocRoleUserResponse.Unmarshal(m, b)
}
func (m *BatchAllocRoleUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAllocRoleUserResponse.Marshal(b, m, deterministic)
}
func (dst *BatchAllocRoleUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAllocRoleUserResponse.Merge(dst, src)
}
func (m *BatchAllocRoleUserResponse) XXX_Size() int {
	return xxx_messageInfo_BatchAllocRoleUserResponse.Size(m)
}
func (m *BatchAllocRoleUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAllocRoleUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAllocRoleUserResponse proto.InternalMessageInfo

type BatchDeallocRoleUserRequest struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeallocRoleUserRequest) Reset()         { *m = BatchDeallocRoleUserRequest{} }
func (m *BatchDeallocRoleUserRequest) String() string { return proto.CompactTextString(m) }
func (*BatchDeallocRoleUserRequest) ProtoMessage()    {}
func (*BatchDeallocRoleUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{37}
}
func (m *BatchDeallocRoleUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeallocRoleUserRequest.Unmarshal(m, b)
}
func (m *BatchDeallocRoleUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeallocRoleUserRequest.Marshal(b, m, deterministic)
}
func (dst *BatchDeallocRoleUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeallocRoleUserRequest.Merge(dst, src)
}
func (m *BatchDeallocRoleUserRequest) XXX_Size() int {
	return xxx_messageInfo_BatchDeallocRoleUserRequest.Size(m)
}
func (m *BatchDeallocRoleUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeallocRoleUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeallocRoleUserRequest proto.InternalMessageInfo

func (m *BatchDeallocRoleUserRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *BatchDeallocRoleUserRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchDeallocRoleUserResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeallocRoleUserResponse) Reset()         { *m = BatchDeallocRoleUserResponse{} }
func (m *BatchDeallocRoleUserResponse) String() string { return proto.CompactTextString(m) }
func (*BatchDeallocRoleUserResponse) ProtoMessage()    {}
func (*BatchDeallocRoleUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{38}
}
func (m *BatchDeallocRoleUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeallocRoleUserResponse.Unmarshal(m, b)
}
func (m *BatchDeallocRoleUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeallocRoleUserResponse.Marshal(b, m, deterministic)
}
func (dst *BatchDeallocRoleUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeallocRoleUserResponse.Merge(dst, src)
}
func (m *BatchDeallocRoleUserResponse) XXX_Size() int {
	return xxx_messageInfo_BatchDeallocRoleUserResponse.Size(m)
}
func (m *BatchDeallocRoleUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeallocRoleUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeallocRoleUserResponse proto.InternalMessageInfo

type GetRoleAllocUserListRequest struct {
	RoleId uint32 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 分页游标，第一页为空
	Cursor string `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	// 每页数量，最大500
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoleAllocUserListRequest) Reset()         { *m = GetRoleAllocUserListRequest{} }
func (m *GetRoleAllocUserListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRoleAllocUserListRequest) ProtoMessage()    {}
func (*GetRoleAllocUserListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{39}
}
func (m *GetRoleAllocUserListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleAllocUserListRequest.Unmarshal(m, b)
}
func (m *GetRoleAllocUserListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleAllocUserListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRoleAllocUserListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleAllocUserListRequest.Merge(dst, src)
}
func (m *GetRoleAllocUserListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRoleAllocUserListRequest.Size(m)
}
func (m *GetRoleAllocUserListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleAllocUserListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleAllocUserListRequest proto.InternalMessageInfo

func (m *GetRoleAllocUserListRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetRoleAllocUserListRequest) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

func (m *GetRoleAllocUserListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRoleAllocUserListResponse struct {
	List []*User `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 是否还有下一页
	HasMore bool `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	// 用于回填到cursor请求下一页
	NextCursor           string   `protobuf:"bytes,3,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoleAllocUserListResponse) Reset()         { *m = GetRoleAllocUserListResponse{} }
func (m *GetRoleAllocUserListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRoleAllocUserListResponse) ProtoMessage()    {}
func (*GetRoleAllocUserListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{40}
}
func (m *GetRoleAllocUserListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleAllocUserListResponse.Unmarshal(m, b)
}
func (m *GetRoleAllocUserListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleAllocUserListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRoleAllocUserListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleAllocUserListResponse.Merge(dst, src)
}
func (m *GetRoleAllocUserListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRoleAllocUserListResponse.Size(m)
}
func (m *GetRoleAllocUserListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleAllocUserListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleAllocUserListResponse proto.InternalMessageInfo

func (m *GetRoleAllocUserListResponse) GetList() []*User {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetRoleAllocUserListResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *GetRoleAllocUserListResponse) GetNextCursor() string {
	if m != nil {
		return m.NextCursor
	}
	return ""
}

type TriggerAIPresentEventRequest struct {
	Event                *TriggerAIPresentEventRequest_AIPresentEvent `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *TriggerAIPresentEventRequest) Reset()         { *m = TriggerAIPresentEventRequest{} }
func (m *TriggerAIPresentEventRequest) String() string { return proto.CompactTextString(m) }
func (*TriggerAIPresentEventRequest) ProtoMessage()    {}
func (*TriggerAIPresentEventRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{41}
}
func (m *TriggerAIPresentEventRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerAIPresentEventRequest.Unmarshal(m, b)
}
func (m *TriggerAIPresentEventRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerAIPresentEventRequest.Marshal(b, m, deterministic)
}
func (dst *TriggerAIPresentEventRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerAIPresentEventRequest.Merge(dst, src)
}
func (m *TriggerAIPresentEventRequest) XXX_Size() int {
	return xxx_messageInfo_TriggerAIPresentEventRequest.Size(m)
}
func (m *TriggerAIPresentEventRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerAIPresentEventRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerAIPresentEventRequest proto.InternalMessageInfo

func (m *TriggerAIPresentEventRequest) GetEvent() *TriggerAIPresentEventRequest_AIPresentEvent {
	if m != nil {
		return m.Event
	}
	return nil
}

type TriggerAIPresentEventRequest_AIPresentEvent struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	TargetRoleId         uint32   `protobuf:"varint,2,opt,name=target_role_id,json=targetRoleId,proto3" json:"target_role_id,omitempty"`
	TargetPartnerId      uint32   `protobuf:"varint,3,opt,name=target_partner_id,json=targetPartnerId,proto3" json:"target_partner_id,omitempty"`
	ItemId               uint32   `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemCount            uint32   `protobuf:"varint,5,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	TotalPrice           uint32   `protobuf:"varint,6,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	IsFirst              bool     `protobuf:"varint,7,opt,name=is_first,json=isFirst,proto3" json:"is_first,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TriggerAIPresentEventRequest_AIPresentEvent) Reset() {
	*m = TriggerAIPresentEventRequest_AIPresentEvent{}
}
func (m *TriggerAIPresentEventRequest_AIPresentEvent) String() string {
	return proto.CompactTextString(m)
}
func (*TriggerAIPresentEventRequest_AIPresentEvent) ProtoMessage() {}
func (*TriggerAIPresentEventRequest_AIPresentEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{41, 0}
}
func (m *TriggerAIPresentEventRequest_AIPresentEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerAIPresentEventRequest_AIPresentEvent.Unmarshal(m, b)
}
func (m *TriggerAIPresentEventRequest_AIPresentEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerAIPresentEventRequest_AIPresentEvent.Marshal(b, m, deterministic)
}
func (dst *TriggerAIPresentEventRequest_AIPresentEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerAIPresentEventRequest_AIPresentEvent.Merge(dst, src)
}
func (m *TriggerAIPresentEventRequest_AIPresentEvent) XXX_Size() int {
	return xxx_messageInfo_TriggerAIPresentEventRequest_AIPresentEvent.Size(m)
}
func (m *TriggerAIPresentEventRequest_AIPresentEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerAIPresentEventRequest_AIPresentEvent.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerAIPresentEventRequest_AIPresentEvent proto.InternalMessageInfo

func (m *TriggerAIPresentEventRequest_AIPresentEvent) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *TriggerAIPresentEventRequest_AIPresentEvent) GetTargetRoleId() uint32 {
	if m != nil {
		return m.TargetRoleId
	}
	return 0
}

func (m *TriggerAIPresentEventRequest_AIPresentEvent) GetTargetPartnerId() uint32 {
	if m != nil {
		return m.TargetPartnerId
	}
	return 0
}

func (m *TriggerAIPresentEventRequest_AIPresentEvent) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *TriggerAIPresentEventRequest_AIPresentEvent) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *TriggerAIPresentEventRequest_AIPresentEvent) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *TriggerAIPresentEventRequest_AIPresentEvent) GetIsFirst() bool {
	if m != nil {
		return m.IsFirst
	}
	return false
}

type TriggerAIPresentEventResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TriggerAIPresentEventResponse) Reset()         { *m = TriggerAIPresentEventResponse{} }
func (m *TriggerAIPresentEventResponse) String() string { return proto.CompactTextString(m) }
func (*TriggerAIPresentEventResponse) ProtoMessage()    {}
func (*TriggerAIPresentEventResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04, []int{42}
}
func (m *TriggerAIPresentEventResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerAIPresentEventResponse.Unmarshal(m, b)
}
func (m *TriggerAIPresentEventResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerAIPresentEventResponse.Marshal(b, m, deterministic)
}
func (dst *TriggerAIPresentEventResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerAIPresentEventResponse.Merge(dst, src)
}
func (m *TriggerAIPresentEventResponse) XXX_Size() int {
	return xxx_messageInfo_TriggerAIPresentEventResponse.Size(m)
}
func (m *TriggerAIPresentEventResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerAIPresentEventResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerAIPresentEventResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ImMsg)(nil), "aigc_soulmate_middle.ImMsg")
	proto.RegisterType((*GroupTimeLineMsg)(nil), "aigc_soulmate_middle.GroupTimeLineMsg")
	proto.RegisterType((*SendOption)(nil), "aigc_soulmate_middle.SendOption")
	proto.RegisterType((*SendGroupImMsgReq)(nil), "aigc_soulmate_middle.SendGroupImMsgReq")
	proto.RegisterType((*SendGroupImMsgResp)(nil), "aigc_soulmate_middle.SendGroupImMsgResp")
	proto.RegisterType((*BatchGetGroupLastMsgListReq)(nil), "aigc_soulmate_middle.BatchGetGroupLastMsgListReq")
	proto.RegisterType((*GroupLastMsg)(nil), "aigc_soulmate_middle.GroupLastMsg")
	proto.RegisterType((*BatchGetGroupLastMsgListResp)(nil), "aigc_soulmate_middle.BatchGetGroupLastMsgListResp")
	proto.RegisterType((*GetGroupMsgListReq)(nil), "aigc_soulmate_middle.GetGroupMsgListReq")
	proto.RegisterType((*GetGroupMsgListResp)(nil), "aigc_soulmate_middle.GetGroupMsgListResp")
	proto.RegisterType((*GroupTemplateInfo)(nil), "aigc_soulmate_middle.GroupTemplateInfo")
	proto.RegisterType((*GroupTemplateInfo_TemplateRole)(nil), "aigc_soulmate_middle.GroupTemplateInfo.TemplateRole")
	proto.RegisterType((*GroupTemplateInfo_TemplateRole_AIGroupPrologue)(nil), "aigc_soulmate_middle.GroupTemplateInfo.TemplateRole.AIGroupPrologue")
	proto.RegisterType((*PlayRole)(nil), "aigc_soulmate_middle.PlayRole")
	proto.RegisterType((*ScriptInfo)(nil), "aigc_soulmate_middle.ScriptInfo")
	proto.RegisterType((*CreateGroupTemplateReq)(nil), "aigc_soulmate_middle.CreateGroupTemplateReq")
	proto.RegisterType((*CreateGroupTemplateResp)(nil), "aigc_soulmate_middle.CreateGroupTemplateResp")
	proto.RegisterType((*UpdateGroupTemplateReq)(nil), "aigc_soulmate_middle.UpdateGroupTemplateReq")
	proto.RegisterType((*UpdateGroupTemplateResp)(nil), "aigc_soulmate_middle.UpdateGroupTemplateResp")
	proto.RegisterType((*DeleteGroupTemplateReq)(nil), "aigc_soulmate_middle.DeleteGroupTemplateReq")
	proto.RegisterType((*DeleteGroupTemplateResp)(nil), "aigc_soulmate_middle.DeleteGroupTemplateResp")
	proto.RegisterType((*GetGroupTemplateByIdReq)(nil), "aigc_soulmate_middle.GetGroupTemplateByIdReq")
	proto.RegisterType((*GetGroupTemplateByIdResp)(nil), "aigc_soulmate_middle.GetGroupTemplateByIdResp")
	proto.RegisterType((*GetGroupTemplateByPageReq)(nil), "aigc_soulmate_middle.GetGroupTemplateByPageReq")
	proto.RegisterType((*GetGroupTemplateByPageResp)(nil), "aigc_soulmate_middle.GetGroupTemplateByPageResp")
	proto.RegisterType((*Entity)(nil), "aigc_soulmate_middle.Entity")
	proto.RegisterType((*GetSentenceAvailableCountReq)(nil), "aigc_soulmate_middle.GetSentenceAvailableCountReq")
	proto.RegisterType((*GetSentenceAvailableCountResp)(nil), "aigc_soulmate_middle.GetSentenceAvailableCountResp")
	proto.RegisterMapType((map[uint32]*GetSentenceAvailableCountResp_SentenceCount)(nil), "aigc_soulmate_middle.GetSentenceAvailableCountResp.AvailableCountMapEntry")
	proto.RegisterType((*GetSentenceAvailableCountResp_SentenceCount)(nil), "aigc_soulmate_middle.GetSentenceAvailableCountResp.SentenceCount")
	proto.RegisterType((*GetUserSentenceCountReq)(nil), "aigc_soulmate_middle.GetUserSentenceCountReq")
	proto.RegisterType((*GetUserSentenceCountResp)(nil), "aigc_soulmate_middle.GetUserSentenceCountResp")
	proto.RegisterType((*IsReachLimitReq)(nil), "aigc_soulmate_middle.IsReachLimitReq")
	proto.RegisterType((*IsReachLimitResp)(nil), "aigc_soulmate_middle.IsReachLimitResp")
	proto.RegisterType((*ConsumeSentenceCountReq)(nil), "aigc_soulmate_middle.ConsumeSentenceCountReq")
	proto.RegisterType((*ConsumeSentenceCountResp)(nil), "aigc_soulmate_middle.ConsumeSentenceCountResp")
	proto.RegisterType((*GetTipsCountRequest)(nil), "aigc_soulmate_middle.GetTipsCountRequest")
	proto.RegisterType((*GetTipsCountResponse)(nil), "aigc_soulmate_middle.GetTipsCountResponse")
	proto.RegisterType((*User)(nil), "aigc_soulmate_middle.User")
	proto.RegisterType((*BatchAllocRoleUserRequest)(nil), "aigc_soulmate_middle.BatchAllocRoleUserRequest")
	proto.RegisterType((*BatchAllocRoleUserResponse)(nil), "aigc_soulmate_middle.BatchAllocRoleUserResponse")
	proto.RegisterType((*BatchDeallocRoleUserRequest)(nil), "aigc_soulmate_middle.BatchDeallocRoleUserRequest")
	proto.RegisterType((*BatchDeallocRoleUserResponse)(nil), "aigc_soulmate_middle.BatchDeallocRoleUserResponse")
	proto.RegisterType((*GetRoleAllocUserListRequest)(nil), "aigc_soulmate_middle.GetRoleAllocUserListRequest")
	proto.RegisterType((*GetRoleAllocUserListResponse)(nil), "aigc_soulmate_middle.GetRoleAllocUserListResponse")
	proto.RegisterType((*TriggerAIPresentEventRequest)(nil), "aigc_soulmate_middle.TriggerAIPresentEventRequest")
	proto.RegisterType((*TriggerAIPresentEventRequest_AIPresentEvent)(nil), "aigc_soulmate_middle.TriggerAIPresentEventRequest.AIPresentEvent")
	proto.RegisterType((*TriggerAIPresentEventResponse)(nil), "aigc_soulmate_middle.TriggerAIPresentEventResponse")
	proto.RegisterEnum("aigc_soulmate_middle.ImMsgType", ImMsgType_name, ImMsgType_value)
	proto.RegisterEnum("aigc_soulmate_middle.ImMsgContentType", ImMsgContentType_name, ImMsgContentType_value)
	proto.RegisterEnum("aigc_soulmate_middle.ImBusiType", ImBusiType_name, ImBusiType_value)
	proto.RegisterEnum("aigc_soulmate_middle.ImCmdType", ImCmdType_name, ImCmdType_value)
	proto.RegisterEnum("aigc_soulmate_middle.GroupSendType", GroupSendType_name, GroupSendType_value)
	proto.RegisterEnum("aigc_soulmate_middle.SentenceType", SentenceType_name, SentenceType_value)
	proto.RegisterEnum("aigc_soulmate_middle.Entity_Type", Entity_Type_name, Entity_Type_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AigcSoulmateMiddleClient is the client API for AigcSoulmateMiddle service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AigcSoulmateMiddleClient interface {
	// 获取群组消息列表
	GetGroupMsgList(ctx context.Context, in *GetGroupMsgListReq, opts ...grpc.CallOption) (*GetGroupMsgListResp, error)
	// 批量获取群组最新消息列表
	BatchGetGroupLastMsgList(ctx context.Context, in *BatchGetGroupLastMsgListReq, opts ...grpc.CallOption) (*BatchGetGroupLastMsgListResp, error)
	// 写timeline消息
	SendGroupImMsg(ctx context.Context, in *SendGroupImMsgReq, opts ...grpc.CallOption) (*SendGroupImMsgResp, error)
	// 群聊模板运营后台
	CreateGroupTemplate(ctx context.Context, in *CreateGroupTemplateReq, opts ...grpc.CallOption) (*CreateGroupTemplateResp, error)
	UpdateGroupTemplate(ctx context.Context, in *UpdateGroupTemplateReq, opts ...grpc.CallOption) (*UpdateGroupTemplateResp, error)
	DeleteGroupTemplate(ctx context.Context, in *DeleteGroupTemplateReq, opts ...grpc.CallOption) (*DeleteGroupTemplateResp, error)
	GetGroupTemplateById(ctx context.Context, in *GetGroupTemplateByIdReq, opts ...grpc.CallOption) (*GetGroupTemplateByIdResp, error)
	GetGroupTemplateByPage(ctx context.Context, in *GetGroupTemplateByPageReq, opts ...grpc.CallOption) (*GetGroupTemplateByPageResp, error)
	// 句子类型剩余数量
	GetSentenceAvailableCount(ctx context.Context, in *GetSentenceAvailableCountReq, opts ...grpc.CallOption) (*GetSentenceAvailableCountResp, error)
	// 获取用户当日使用的专属句数总数
	GetUserSentenceCount(ctx context.Context, in *GetUserSentenceCountReq, opts ...grpc.CallOption) (*GetUserSentenceCountResp, error)
	// 是否达到句数上限
	IsReachLimit(ctx context.Context, in *IsReachLimitReq, opts ...grpc.CallOption) (*IsReachLimitResp, error)
	// 消耗句数
	ConsumeSentenceCount(ctx context.Context, in *ConsumeSentenceCountReq, opts ...grpc.CallOption) (*ConsumeSentenceCountResp, error)
	// 获取句数提示需要的配置
	GetTipsCount(ctx context.Context, in *GetTipsCountRequest, opts ...grpc.CallOption) (*GetTipsCountResponse, error)
	// 专属角色
	BatchAllocRoleUser(ctx context.Context, in *BatchAllocRoleUserRequest, opts ...grpc.CallOption) (*BatchAllocRoleUserResponse, error)
	BatchDeallocRoleUser(ctx context.Context, in *BatchDeallocRoleUserRequest, opts ...grpc.CallOption) (*BatchDeallocRoleUserResponse, error)
	GetRoleAllocUserList(ctx context.Context, in *GetRoleAllocUserListRequest, opts ...grpc.CallOption) (*GetRoleAllocUserListResponse, error)
	// 触发ai送礼事件
	TriggerAIPresentEvent(ctx context.Context, in *TriggerAIPresentEventRequest, opts ...grpc.CallOption) (*TriggerAIPresentEventResponse, error)
}

type aigcSoulmateMiddleClient struct {
	cc *grpc.ClientConn
}

func NewAigcSoulmateMiddleClient(cc *grpc.ClientConn) AigcSoulmateMiddleClient {
	return &aigcSoulmateMiddleClient{cc}
}

func (c *aigcSoulmateMiddleClient) GetGroupMsgList(ctx context.Context, in *GetGroupMsgListReq, opts ...grpc.CallOption) (*GetGroupMsgListResp, error) {
	out := new(GetGroupMsgListResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/GetGroupMsgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) BatchGetGroupLastMsgList(ctx context.Context, in *BatchGetGroupLastMsgListReq, opts ...grpc.CallOption) (*BatchGetGroupLastMsgListResp, error) {
	out := new(BatchGetGroupLastMsgListResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/BatchGetGroupLastMsgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) SendGroupImMsg(ctx context.Context, in *SendGroupImMsgReq, opts ...grpc.CallOption) (*SendGroupImMsgResp, error) {
	out := new(SendGroupImMsgResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/SendGroupImMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) CreateGroupTemplate(ctx context.Context, in *CreateGroupTemplateReq, opts ...grpc.CallOption) (*CreateGroupTemplateResp, error) {
	out := new(CreateGroupTemplateResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/CreateGroupTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) UpdateGroupTemplate(ctx context.Context, in *UpdateGroupTemplateReq, opts ...grpc.CallOption) (*UpdateGroupTemplateResp, error) {
	out := new(UpdateGroupTemplateResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/UpdateGroupTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) DeleteGroupTemplate(ctx context.Context, in *DeleteGroupTemplateReq, opts ...grpc.CallOption) (*DeleteGroupTemplateResp, error) {
	out := new(DeleteGroupTemplateResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/DeleteGroupTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) GetGroupTemplateById(ctx context.Context, in *GetGroupTemplateByIdReq, opts ...grpc.CallOption) (*GetGroupTemplateByIdResp, error) {
	out := new(GetGroupTemplateByIdResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/GetGroupTemplateById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) GetGroupTemplateByPage(ctx context.Context, in *GetGroupTemplateByPageReq, opts ...grpc.CallOption) (*GetGroupTemplateByPageResp, error) {
	out := new(GetGroupTemplateByPageResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/GetGroupTemplateByPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) GetSentenceAvailableCount(ctx context.Context, in *GetSentenceAvailableCountReq, opts ...grpc.CallOption) (*GetSentenceAvailableCountResp, error) {
	out := new(GetSentenceAvailableCountResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/GetSentenceAvailableCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) GetUserSentenceCount(ctx context.Context, in *GetUserSentenceCountReq, opts ...grpc.CallOption) (*GetUserSentenceCountResp, error) {
	out := new(GetUserSentenceCountResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/GetUserSentenceCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) IsReachLimit(ctx context.Context, in *IsReachLimitReq, opts ...grpc.CallOption) (*IsReachLimitResp, error) {
	out := new(IsReachLimitResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/IsReachLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) ConsumeSentenceCount(ctx context.Context, in *ConsumeSentenceCountReq, opts ...grpc.CallOption) (*ConsumeSentenceCountResp, error) {
	out := new(ConsumeSentenceCountResp)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/ConsumeSentenceCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) GetTipsCount(ctx context.Context, in *GetTipsCountRequest, opts ...grpc.CallOption) (*GetTipsCountResponse, error) {
	out := new(GetTipsCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/GetTipsCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) BatchAllocRoleUser(ctx context.Context, in *BatchAllocRoleUserRequest, opts ...grpc.CallOption) (*BatchAllocRoleUserResponse, error) {
	out := new(BatchAllocRoleUserResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/BatchAllocRoleUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) BatchDeallocRoleUser(ctx context.Context, in *BatchDeallocRoleUserRequest, opts ...grpc.CallOption) (*BatchDeallocRoleUserResponse, error) {
	out := new(BatchDeallocRoleUserResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/BatchDeallocRoleUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) GetRoleAllocUserList(ctx context.Context, in *GetRoleAllocUserListRequest, opts ...grpc.CallOption) (*GetRoleAllocUserListResponse, error) {
	out := new(GetRoleAllocUserListResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/GetRoleAllocUserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcSoulmateMiddleClient) TriggerAIPresentEvent(ctx context.Context, in *TriggerAIPresentEventRequest, opts ...grpc.CallOption) (*TriggerAIPresentEventResponse, error) {
	out := new(TriggerAIPresentEventResponse)
	err := c.cc.Invoke(ctx, "/aigc_soulmate_middle.AigcSoulmateMiddle/TriggerAIPresentEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AigcSoulmateMiddleServer is the server API for AigcSoulmateMiddle service.
type AigcSoulmateMiddleServer interface {
	// 获取群组消息列表
	GetGroupMsgList(context.Context, *GetGroupMsgListReq) (*GetGroupMsgListResp, error)
	// 批量获取群组最新消息列表
	BatchGetGroupLastMsgList(context.Context, *BatchGetGroupLastMsgListReq) (*BatchGetGroupLastMsgListResp, error)
	// 写timeline消息
	SendGroupImMsg(context.Context, *SendGroupImMsgReq) (*SendGroupImMsgResp, error)
	// 群聊模板运营后台
	CreateGroupTemplate(context.Context, *CreateGroupTemplateReq) (*CreateGroupTemplateResp, error)
	UpdateGroupTemplate(context.Context, *UpdateGroupTemplateReq) (*UpdateGroupTemplateResp, error)
	DeleteGroupTemplate(context.Context, *DeleteGroupTemplateReq) (*DeleteGroupTemplateResp, error)
	GetGroupTemplateById(context.Context, *GetGroupTemplateByIdReq) (*GetGroupTemplateByIdResp, error)
	GetGroupTemplateByPage(context.Context, *GetGroupTemplateByPageReq) (*GetGroupTemplateByPageResp, error)
	// 句子类型剩余数量
	GetSentenceAvailableCount(context.Context, *GetSentenceAvailableCountReq) (*GetSentenceAvailableCountResp, error)
	// 获取用户当日使用的专属句数总数
	GetUserSentenceCount(context.Context, *GetUserSentenceCountReq) (*GetUserSentenceCountResp, error)
	// 是否达到句数上限
	IsReachLimit(context.Context, *IsReachLimitReq) (*IsReachLimitResp, error)
	// 消耗句数
	ConsumeSentenceCount(context.Context, *ConsumeSentenceCountReq) (*ConsumeSentenceCountResp, error)
	// 获取句数提示需要的配置
	GetTipsCount(context.Context, *GetTipsCountRequest) (*GetTipsCountResponse, error)
	// 专属角色
	BatchAllocRoleUser(context.Context, *BatchAllocRoleUserRequest) (*BatchAllocRoleUserResponse, error)
	BatchDeallocRoleUser(context.Context, *BatchDeallocRoleUserRequest) (*BatchDeallocRoleUserResponse, error)
	GetRoleAllocUserList(context.Context, *GetRoleAllocUserListRequest) (*GetRoleAllocUserListResponse, error)
	// 触发ai送礼事件
	TriggerAIPresentEvent(context.Context, *TriggerAIPresentEventRequest) (*TriggerAIPresentEventResponse, error)
}

func RegisterAigcSoulmateMiddleServer(s *grpc.Server, srv AigcSoulmateMiddleServer) {
	s.RegisterService(&_AigcSoulmateMiddle_serviceDesc, srv)
}

func _AigcSoulmateMiddle_GetGroupMsgList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupMsgListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).GetGroupMsgList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/GetGroupMsgList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).GetGroupMsgList(ctx, req.(*GetGroupMsgListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_BatchGetGroupLastMsgList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGroupLastMsgListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).BatchGetGroupLastMsgList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/BatchGetGroupLastMsgList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).BatchGetGroupLastMsgList(ctx, req.(*BatchGetGroupLastMsgListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_SendGroupImMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendGroupImMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).SendGroupImMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/SendGroupImMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).SendGroupImMsg(ctx, req.(*SendGroupImMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_CreateGroupTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGroupTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).CreateGroupTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/CreateGroupTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).CreateGroupTemplate(ctx, req.(*CreateGroupTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_UpdateGroupTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).UpdateGroupTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/UpdateGroupTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).UpdateGroupTemplate(ctx, req.(*UpdateGroupTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_DeleteGroupTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGroupTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).DeleteGroupTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/DeleteGroupTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).DeleteGroupTemplate(ctx, req.(*DeleteGroupTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_GetGroupTemplateById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupTemplateByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).GetGroupTemplateById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/GetGroupTemplateById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).GetGroupTemplateById(ctx, req.(*GetGroupTemplateByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_GetGroupTemplateByPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupTemplateByPageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).GetGroupTemplateByPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/GetGroupTemplateByPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).GetGroupTemplateByPage(ctx, req.(*GetGroupTemplateByPageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_GetSentenceAvailableCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSentenceAvailableCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).GetSentenceAvailableCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/GetSentenceAvailableCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).GetSentenceAvailableCount(ctx, req.(*GetSentenceAvailableCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_GetUserSentenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSentenceCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).GetUserSentenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/GetUserSentenceCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).GetUserSentenceCount(ctx, req.(*GetUserSentenceCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_IsReachLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsReachLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).IsReachLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/IsReachLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).IsReachLimit(ctx, req.(*IsReachLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_ConsumeSentenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsumeSentenceCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).ConsumeSentenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/ConsumeSentenceCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).ConsumeSentenceCount(ctx, req.(*ConsumeSentenceCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_GetTipsCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTipsCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).GetTipsCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/GetTipsCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).GetTipsCount(ctx, req.(*GetTipsCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_BatchAllocRoleUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAllocRoleUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).BatchAllocRoleUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/BatchAllocRoleUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).BatchAllocRoleUser(ctx, req.(*BatchAllocRoleUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_BatchDeallocRoleUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeallocRoleUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).BatchDeallocRoleUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/BatchDeallocRoleUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).BatchDeallocRoleUser(ctx, req.(*BatchDeallocRoleUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_GetRoleAllocUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoleAllocUserListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).GetRoleAllocUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/GetRoleAllocUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).GetRoleAllocUserList(ctx, req.(*GetRoleAllocUserListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcSoulmateMiddle_TriggerAIPresentEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerAIPresentEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcSoulmateMiddleServer).TriggerAIPresentEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_soulmate_middle.AigcSoulmateMiddle/TriggerAIPresentEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcSoulmateMiddleServer).TriggerAIPresentEvent(ctx, req.(*TriggerAIPresentEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AigcSoulmateMiddle_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc_soulmate_middle.AigcSoulmateMiddle",
	HandlerType: (*AigcSoulmateMiddleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGroupMsgList",
			Handler:    _AigcSoulmateMiddle_GetGroupMsgList_Handler,
		},
		{
			MethodName: "BatchGetGroupLastMsgList",
			Handler:    _AigcSoulmateMiddle_BatchGetGroupLastMsgList_Handler,
		},
		{
			MethodName: "SendGroupImMsg",
			Handler:    _AigcSoulmateMiddle_SendGroupImMsg_Handler,
		},
		{
			MethodName: "CreateGroupTemplate",
			Handler:    _AigcSoulmateMiddle_CreateGroupTemplate_Handler,
		},
		{
			MethodName: "UpdateGroupTemplate",
			Handler:    _AigcSoulmateMiddle_UpdateGroupTemplate_Handler,
		},
		{
			MethodName: "DeleteGroupTemplate",
			Handler:    _AigcSoulmateMiddle_DeleteGroupTemplate_Handler,
		},
		{
			MethodName: "GetGroupTemplateById",
			Handler:    _AigcSoulmateMiddle_GetGroupTemplateById_Handler,
		},
		{
			MethodName: "GetGroupTemplateByPage",
			Handler:    _AigcSoulmateMiddle_GetGroupTemplateByPage_Handler,
		},
		{
			MethodName: "GetSentenceAvailableCount",
			Handler:    _AigcSoulmateMiddle_GetSentenceAvailableCount_Handler,
		},
		{
			MethodName: "GetUserSentenceCount",
			Handler:    _AigcSoulmateMiddle_GetUserSentenceCount_Handler,
		},
		{
			MethodName: "IsReachLimit",
			Handler:    _AigcSoulmateMiddle_IsReachLimit_Handler,
		},
		{
			MethodName: "ConsumeSentenceCount",
			Handler:    _AigcSoulmateMiddle_ConsumeSentenceCount_Handler,
		},
		{
			MethodName: "GetTipsCount",
			Handler:    _AigcSoulmateMiddle_GetTipsCount_Handler,
		},
		{
			MethodName: "BatchAllocRoleUser",
			Handler:    _AigcSoulmateMiddle_BatchAllocRoleUser_Handler,
		},
		{
			MethodName: "BatchDeallocRoleUser",
			Handler:    _AigcSoulmateMiddle_BatchDeallocRoleUser_Handler,
		},
		{
			MethodName: "GetRoleAllocUserList",
			Handler:    _AigcSoulmateMiddle_GetRoleAllocUserList_Handler,
		},
		{
			MethodName: "TriggerAIPresentEvent",
			Handler:    _AigcSoulmateMiddle_TriggerAIPresentEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/aigc/aigc-soulmate-middle/aigc-soulmate-middle.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-soulmate-middle/aigc-soulmate-middle.proto", fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04)
}

var fileDescriptor_aigc_soulmate_middle_954a3cd12625ff04 = []byte{
	// 3400 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x3a, 0xcb, 0x6e, 0x23, 0x49,
	0x72, 0x5d, 0xa4, 0x5e, 0x0c, 0x91, 0x14, 0x95, 0x52, 0xab, 0x29, 0xf6, 0x4b, 0x53, 0xf3, 0xd2,
	0xb4, 0xdd, 0xea, 0x59, 0xcd, 0xee, 0xc2, 0x98, 0xc5, 0xc2, 0x60, 0x53, 0x5c, 0x99, 0x63, 0xbd,
	0x5c, 0xa4, 0x3c, 0xbb, 0x6b, 0x18, 0x85, 0x52, 0x31, 0x45, 0x25, 0xc4, 0x7a, 0x74, 0x65, 0x56,
	0x8f, 0xb4, 0xc0, 0x1a, 0x06, 0x76, 0x61, 0x03, 0xfe, 0x08, 0xdb, 0x80, 0x0f, 0x3e, 0xf8, 0xe8,
	0x93, 0xe1, 0x8f, 0xb0, 0x31, 0x80, 0x01, 0xc3, 0x17, 0x7f, 0x80, 0x01, 0x03, 0xbe, 0xfa, 0x64,
	0x44, 0x66, 0x16, 0xab, 0x48, 0x55, 0x11, 0xea, 0x46, 0xc3, 0x17, 0x81, 0x19, 0x11, 0x19, 0x19,
	0x11, 0x19, 0x19, 0xaf, 0x12, 0x74, 0x84, 0x78, 0xf5, 0x26, 0x66, 0xee, 0x35, 0x67, 0xe3, 0xb7,
	0x34, 0x7a, 0xe5, 0xb0, 0x91, 0x2b, 0xff, 0xbc, 0xe4, 0x41, 0x3c, 0xf6, 0x1c, 0x41, 0x5f, 0x7a,
	0x6c, 0x38, 0x1c, 0xd3, 0x5c, 0xe0, 0x5e, 0x18, 0x05, 0x22, 0x20, 0x9b, 0x88, 0xb3, 0x13, 0x9c,
	0xad, 0x70, 0xe6, 0xbf, 0x96, 0x60, 0xb1, 0xe7, 0x1d, 0xf3, 0x11, 0xf9, 0x0a, 0x16, 0xc4, 0x6d,
	0x48, 0x9b, 0xc6, 0x8e, 0xb1, 0x5b, 0xdf, 0x7f, 0xbe, 0x97, 0x47, 0xbe, 0x27, 0x49, 0x07, 0xb7,
	0x21, 0xb5, 0x24, 0x31, 0x69, 0xc2, 0xb2, 0x1b, 0xf8, 0x82, 0xfa, 0xa2, 0x59, 0xda, 0x31, 0x76,
	0x2b, 0x56, 0xb2, 0x24, 0x0d, 0x28, 0xd3, 0x1b, 0xd1, 0x2c, 0xef, 0x18, 0xbb, 0x55, 0x0b, 0x7f,
	0x92, 0x47, 0xb0, 0xcc, 0xa9, 0x2f, 0x6c, 0x47, 0x34, 0x17, 0x76, 0x8c, 0xdd, 0xb2, 0xb5, 0x84,
	0xcb, 0xb6, 0x20, 0x0f, 0x61, 0x89, 0xd3, 0x37, 0x36, 0x1b, 0x36, 0x17, 0x77, 0x8c, 0xdd, 0x9a,
	0xb5, 0xc8, 0xe9, 0x9b, 0xde, 0x90, 0xec, 0x40, 0x95, 0x79, 0xf6, 0x45, 0xcc, 0x99, 0x2d, 0x05,
	0x5b, 0x92, 0x48, 0x60, 0xde, 0xeb, 0x98, 0x33, 0x94, 0x81, 0x7c, 0x04, 0x55, 0x7d, 0x9c, 0xa2,
	0x58, 0x96, 0x14, 0xab, 0x1a, 0x26, 0x49, 0x9e, 0xc1, 0x2a, 0xf3, 0x6c, 0xd7, 0x1b, 0x2a, 0x8a,
	0x15, 0x49, 0x51, 0x61, 0x5e, 0xc7, 0x1b, 0x4a, 0xfc, 0x1e, 0x6c, 0x88, 0xc8, 0xf1, 0x79, 0xe8,
	0x44, 0xc8, 0x86, 0xde, 0x08, 0xdb, 0xe3, 0xa3, 0x66, 0x45, 0x2a, 0xb3, 0x9e, 0x41, 0x75, 0x6f,
	0x04, 0x5a, 0xe9, 0x23, 0xa8, 0x46, 0xc8, 0x2d, 0xd1, 0x1a, 0x24, 0xe1, 0x2a, 0xc2, 0x3a, 0x0a,
	0x64, 0xfe, 0x73, 0x09, 0x1a, 0x87, 0x51, 0x10, 0x87, 0x03, 0xe6, 0xd1, 0x23, 0xe6, 0x53, 0xdc,
	0xb7, 0x0d, 0x2b, 0x23, 0x84, 0xa1, 0x96, 0x86, 0x14, 0x62, 0x59, 0xae, 0x7b, 0x43, 0xf2, 0x02,
	0xd6, 0x15, 0x4a, 0x50, 0x2f, 0x1c, 0xa3, 0xb1, 0xd9, 0x50, 0x5a, 0xb3, 0x66, 0xad, 0x49, 0xc4,
	0x40, 0xc3, 0x7b, 0x43, 0x64, 0x13, 0x05, 0x63, 0xa4, 0xe0, 0xcd, 0xf2, 0x4e, 0x19, 0xd9, 0xe0,
	0xba, 0x37, 0xe4, 0x68, 0xf0, 0x98, 0x0d, 0xa5, 0x69, 0x6b, 0x16, 0xfe, 0x24, 0x2f, 0xa1, 0x8c,
	0xba, 0xa0, 0x51, 0x57, 0xf7, 0x1f, 0xcf, 0xb9, 0x50, 0x0b, 0xe9, 0xc8, 0x1f, 0x82, 0x3a, 0xce,
	0xe6, 0xd4, 0x1f, 0xa6, 0x26, 0xaf, 0xef, 0x7f, 0x9c, 0xbf, 0x55, 0xea, 0xd8, 0xa7, 0xbe, 0x34,
	0xa4, 0x55, 0x1b, 0x65, 0x97, 0x78, 0xd9, 0x8e, 0xb0, 0x63, 0x94, 0x73, 0x59, 0xca, 0xb9, 0xe4,
	0x88, 0x73, 0x36, 0xe4, 0x78, 0x21, 0x8e, 0xb0, 0x27, 0x4a, 0xac, 0x48, 0x64, 0xc5, 0x11, 0x96,
	0x52, 0xc3, 0xfc, 0x0f, 0x03, 0x00, 0xb9, 0x9c, 0x86, 0x82, 0x05, 0x3e, 0x1a, 0xe7, 0x3b, 0x26,
	0xae, 0xec, 0xe0, 0xf2, 0x72, 0xcc, 0x7c, 0x6a, 0x87, 0x31, 0xbf, 0x92, 0x06, 0x5c, 0xb1, 0xd6,
	0x10, 0x71, 0xaa, 0xe0, 0x67, 0x31, 0xbf, 0x22, 0x5f, 0xc2, 0x66, 0x96, 0xcc, 0x9e, 0xf6, 0x4c,
	0x12, 0xa4, 0xa4, 0xfa, 0xaa, 0xc8, 0x53, 0x00, 0xc9, 0xdd, 0x89, 0x87, 0x4c, 0xf9, 0xea, 0x8a,
	0x55, 0x41, 0x48, 0x1b, 0x01, 0x88, 0x96, 0x18, 0x5b, 0xa0, 0x2b, 0x2f, 0x48, 0x36, 0x15, 0x09,
	0x19, 0xa0, 0x43, 0x7f, 0x09, 0x9b, 0x72, 0x37, 0x0f, 0xa9, 0xcb, 0x9c, 0xb1, 0xcd, 0x45, 0x10,
	0x39, 0x23, 0x2a, 0x0d, 0xbe, 0x62, 0x11, 0xc4, 0xf5, 0x15, 0xaa, 0xaf, 0x30, 0xe6, 0xbf, 0x1b,
	0xb0, 0x8e, 0xca, 0x49, 0xd3, 0x29, 0xd3, 0xd3, 0x37, 0xe4, 0x1b, 0xa8, 0x09, 0xe6, 0x51, 0x5b,
	0x4a, 0x8e, 0x37, 0x66, 0xc8, 0x1b, 0xfb, 0x6c, 0x8e, 0xd9, 0x33, 0xae, 0x65, 0xad, 0x8a, 0x8c,
	0x9f, 0xed, 0x43, 0x39, 0x08, 0x95, 0xca, 0xab, 0xfb, 0x3b, 0xf9, 0x1c, 0x52, 0xf3, 0x5a, 0x48,
	0x8c, 0x6a, 0x32, 0x6e, 0x8b, 0x88, 0x8d, 0x46, 0x34, 0x4a, 0xac, 0xc0, 0xf8, 0x40, 0x01, 0xc8,
	0x27, 0x50, 0x67, 0xdc, 0x8e, 0xa8, 0xe3, 0x5e, 0xd9, 0x63, 0xe6, 0x31, 0x65, 0x89, 0x15, 0xab,
	0xca, 0xb8, 0x85, 0xc0, 0x23, 0x84, 0x99, 0x07, 0x40, 0x66, 0x35, 0xe3, 0x61, 0xf6, 0xcd, 0x1b,
	0x05, 0x6f, 0xbe, 0x94, 0x79, 0xf3, 0xe6, 0xd7, 0xf0, 0xf8, 0xb5, 0x23, 0xdc, 0xab, 0x43, 0x2a,
	0x24, 0xa7, 0x23, 0x87, 0xe3, 0xb3, 0x3b, 0x62, 0x5c, 0xa0, 0xa5, 0x1e, 0x43, 0x25, 0x79, 0x45,
	0xbc, 0x69, 0x48, 0xd7, 0x59, 0xd1, 0xcf, 0x88, 0x9b, 0x31, 0x54, 0xb3, 0x7b, 0xe6, 0x3d, 0xb9,
	0x3b, 0x16, 0x2f, 0xbd, 0xb7, 0xc5, 0x4d, 0x07, 0x9e, 0x14, 0x8b, 0xcc, 0x43, 0xd2, 0x06, 0x50,
	0x62, 0x78, 0x7c, 0xa4, 0x84, 0x5e, 0xdd, 0x37, 0xe7, 0x1c, 0xa4, 0xf7, 0x5b, 0x4a, 0xd3, 0x63,
	0x3e, 0xe2, 0xa6, 0x0f, 0x24, 0xe1, 0x9e, 0x31, 0xc6, 0x1c, 0xfd, 0xf2, 0xad, 0x4b, 0x36, 0x61,
	0x51, 0x5d, 0x60, 0x59, 0x41, 0xe5, 0xe2, 0x6e, 0xe0, 0x30, 0x5d, 0xd8, 0xb8, 0x73, 0x1e, 0x0f,
	0xc9, 0x11, 0xd4, 0xa7, 0xac, 0xc6, 0x9b, 0x25, 0xa9, 0xcd, 0x7d, 0xcd, 0x56, 0xcd, 0x98, 0x8d,
	0x9b, 0xdf, 0xaf, 0xc2, 0xfa, 0xe1, 0x54, 0x78, 0xf3, 0x2f, 0x03, 0x52, 0x87, 0xd2, 0x44, 0x9d,
	0x12, 0x1b, 0x12, 0x02, 0x0b, 0xbe, 0xe3, 0x51, 0xfd, 0x86, 0xe5, 0x6f, 0xf2, 0x04, 0x2a, 0xee,
	0x95, 0x13, 0x39, 0xae, 0xd0, 0xee, 0x5a, 0xb1, 0x52, 0x00, 0xee, 0x10, 0xce, 0x88, 0x37, 0x17,
	0x76, 0xca, 0xb8, 0x03, 0x7f, 0x93, 0x2d, 0x58, 0x72, 0xde, 0x3a, 0xc2, 0x89, 0xe4, 0xdb, 0xac,
	0x58, 0x7a, 0x85, 0xaa, 0x73, 0x7a, 0x23, 0xc3, 0xdc, 0xa2, 0x85, 0x3f, 0x31, 0x1f, 0xb8, 0x57,
	0x8e, 0xb0, 0x2f, 0x1c, 0xf7, 0x1a, 0xad, 0xe9, 0x0f, 0x6d, 0xe6, 0x8d, 0x64, 0x66, 0xa9, 0x58,
	0xeb, 0x88, 0x7a, 0x3d, 0xc1, 0xf4, 0xbc, 0x11, 0xd2, 0x5f, 0x05, 0x1e, 0x9d, 0xa5, 0x5f, 0x51,
	0xf4, 0x88, 0x9a, 0xa6, 0x7f, 0x9a, 0x78, 0x03, 0x73, 0x03, 0x5f, 0xa7, 0x19, 0x75, 0xd3, 0x3d,
	0x37, 0xf0, 0x31, 0x9f, 0xd2, 0x9b, 0x30, 0xe0, 0x74, 0x28, 0x33, 0xcb, 0x8a, 0x95, 0x2c, 0xe5,
	0x23, 0xf5, 0x39, 0x8d, 0x84, 0x1d, 0x06, 0xbc, 0xb9, 0xaa, 0xf3, 0x98, 0x84, 0x9c, 0x05, 0x9c,
	0x3c, 0x87, 0x55, 0x37, 0x88, 0x7c, 0x1a, 0x29, 0xc6, 0x55, 0xc9, 0x18, 0x14, 0x48, 0x72, 0x46,
	0x02, 0x47, 0xd0, 0x51, 0x10, 0xdd, 0xa2, 0x5f, 0xd4, 0x34, 0x81, 0x06, 0xf5, 0x86, 0xe4, 0x33,
	0x58, 0x73, 0x03, 0xff, 0x92, 0x8d, 0xec, 0x31, 0xbb, 0xa6, 0xb6, 0x1f, 0x7b, 0xcd, 0xba, 0xb4,
	0x4b, 0x4d, 0x81, 0x8f, 0xd8, 0x35, 0x3d, 0x89, 0x3d, 0xf2, 0x27, 0x50, 0x9f, 0x24, 0x2a, 0x0c,
	0xe3, 0xbc, 0xb9, 0x26, 0xbd, 0xe0, 0x87, 0xf3, 0xbc, 0x20, 0x73, 0xc5, 0x7b, 0xc9, 0x02, 0x23,
	0xbe, 0x55, 0x13, 0x99, 0x15, 0xd7, 0xe9, 0x5a, 0x38, 0x17, 0xb6, 0xbc, 0xc3, 0x86, 0xb2, 0x0f,
	0xf3, 0x06, 0xce, 0xc5, 0x00, 0x2f, 0x72, 0x62, 0x3e, 0x99, 0x9e, 0xd6, 0x95, 0x15, 0x54, 0x92,
	0xc4, 0xac, 0xd3, 0x86, 0x55, 0xee, 0x46, 0x2c, 0x14, 0x36, 0xf3, 0x2f, 0x83, 0x26, 0x99, 0x1b,
	0x05, 0x25, 0x21, 0x4a, 0x64, 0x01, 0x9f, 0xfc, 0xc6, 0x04, 0xcf, 0x63, 0x26, 0x9c, 0x8b, 0x31,
	0xb5, 0xd1, 0x37, 0x36, 0x76, 0xca, 0xbb, 0x8b, 0xd6, 0x6a, 0x02, 0xeb, 0xd3, 0x1b, 0xf2, 0x06,
	0xd6, 0x87, 0xf4, 0xd2, 0x89, 0xc7, 0xc2, 0x0e, 0xa3, 0x60, 0x1c, 0x8c, 0x62, 0xca, 0x9b, 0x9b,
	0xd2, 0x08, 0x07, 0xef, 0x63, 0x84, 0xbd, 0x76, 0x4f, 0x12, 0x9c, 0x69, 0x66, 0x56, 0x43, 0xb3,
	0x4f, 0x00, 0x1c, 0xd3, 0xa0, 0x2c, 0x00, 0x31, 0xd7, 0x38, 0xd7, 0x32, 0x1f, 0xf1, 0xe6, 0x43,
	0xe9, 0xe1, 0x6b, 0x12, 0xd1, 0x47, 0x38, 0x66, 0x25, 0x4e, 0x3e, 0x85, 0xfa, 0x98, 0x3a, 0x6f,
	0xa9, 0xcd, 0x45, 0x84, 0xd7, 0x7b, 0xdb, 0xdc, 0x92, 0x76, 0xaa, 0x49, 0x68, 0x5f, 0x03, 0x5b,
	0xdf, 0x2f, 0x40, 0x35, 0x2b, 0x05, 0xc6, 0x6a, 0x9d, 0x96, 0xf5, 0xfb, 0x5b, 0x52, 0xa5, 0x45,
	0xee, 0x1b, 0x4c, 0x5f, 0x54, 0x79, 0xea, 0x45, 0x6d, 0xc1, 0x92, 0x60, 0xde, 0x45, 0x44, 0x75,
	0xba, 0xd4, 0xab, 0xe4, 0xa5, 0x2d, 0xa6, 0x2f, 0xed, 0x77, 0x60, 0x7d, 0xc8, 0x78, 0x38, 0x76,
	0x6e, 0xed, 0xf4, 0x35, 0x2f, 0xc9, 0x4d, 0x0d, 0x8d, 0xe8, 0x4c, 0x1e, 0xf5, 0x47, 0x50, 0x75,
	0x58, 0x86, 0x4e, 0xbd, 0xc7, 0x55, 0x87, 0xa5, 0x24, 0x9f, 0x40, 0x5d, 0xbe, 0x5c, 0xa9, 0xc3,
	0x90, 0x72, 0x57, 0x3f, 0xc2, 0x2a, 0x42, 0x51, 0xc1, 0x03, 0xca, 0x5d, 0xf2, 0x12, 0x48, 0x44,
	0xc7, 0x0e, 0x26, 0xbf, 0x0c, 0x3b, 0x5d, 0xee, 0x25, 0x98, 0x94, 0xe9, 0x05, 0x54, 0xd2, 0x2b,
	0x86, 0x0f, 0x78, 0xc5, 0x29, 0x5b, 0x74, 0xa7, 0xef, 0xe8, 0xd8, 0xc5, 0x28, 0x92, 0x9e, 0xb5,
	0xfa, 0x21, 0xdd, 0x49, 0xb3, 0x9f, 0xb8, 0x53, 0xeb, 0x5b, 0x58, 0x9b, 0x21, 0x92, 0x61, 0x13,
	0xab, 0x1c, 0x43, 0x5d, 0x32, 0xfe, 0xc6, 0x7c, 0x81, 0xd5, 0x4e, 0xa0, 0x6f, 0x5e, 0x2d, 0x48,
	0x0b, 0x56, 0xc2, 0x88, 0x05, 0x11, 0x13, 0xb7, 0x3a, 0x91, 0x4c, 0xd6, 0xe6, 0x3f, 0x1a, 0xb0,
	0x72, 0x36, 0x76, 0x6e, 0xa5, 0x43, 0xcd, 0xc6, 0xf2, 0xd4, 0x67, 0x4a, 0x53, 0x3e, 0x93, 0xf8,
	0x57, 0x39, 0xe3, 0x5f, 0xb3, 0xcf, 0x70, 0xe1, 0xee, 0x33, 0x9c, 0x4a, 0x03, 0x8b, 0xb3, 0x69,
	0x20, 0xf7, 0xc5, 0x2c, 0xe5, 0xbe, 0x18, 0xf3, 0x5f, 0x4a, 0x00, 0x69, 0x38, 0x20, 0x3f, 0x05,
	0x90, 0x6e, 0xa9, 0xa2, 0x9b, 0xca, 0xd8, 0xcf, 0xf2, 0x6f, 0x22, 0xd1, 0xd5, 0xaa, 0x84, 0xfa,
	0x17, 0xc7, 0x94, 0x70, 0x11, 0x0b, 0x11, 0xf8, 0x76, 0xe2, 0xdf, 0xd2, 0xb0, 0x4a, 0xe7, 0x75,
	0x85, 0x3a, 0x50, 0x18, 0x59, 0x46, 0x7e, 0x01, 0x8d, 0x4c, 0xf6, 0xf0, 0x62, 0xce, 0x5c, 0x6d,
	0x8a, 0xb5, 0x14, 0x7e, 0x8c, 0x60, 0x4c, 0xf9, 0x31, 0xa7, 0x91, 0x0c, 0xce, 0x2a, 0x5f, 0x2f,
	0xe3, 0x1a, 0xc3, 0xf2, 0x36, 0xac, 0x78, 0xce, 0x58, 0xc5, 0x6d, 0xd5, 0x29, 0x2d, 0xe3, 0x1a,
	0x51, 0x4f, 0x01, 0x2e, 0xe9, 0x04, 0xa9, 0x9a, 0xa4, 0x8a, 0x82, 0x20, 0x9a, 0xc0, 0x02, 0x0f,
	0x22, 0xa1, 0x7b, 0x23, 0xf9, 0x5b, 0x96, 0xef, 0xfe, 0xad, 0xa4, 0xaf, 0xa8, 0x58, 0xe0, 0xf8,
	0xb7, 0x48, 0xfc, 0x29, 0xd4, 0x3d, 0xac, 0x76, 0xd2, 0xe0, 0x02, 0x2a, 0xb8, 0x48, 0x68, 0x12,
	0x5c, 0xcc, 0x2b, 0xd8, 0xea, 0x44, 0xd4, 0x11, 0x74, 0xca, 0x55, 0xb1, 0x6a, 0x39, 0x81, 0xfa,
	0x74, 0xb7, 0xa3, 0xab, 0xdd, 0xcf, 0xef, 0xe9, 0xea, 0xba, 0xd1, 0x48, 0x40, 0xe6, 0xd7, 0xf0,
	0x28, 0xf7, 0x24, 0x1e, 0x62, 0xca, 0xcb, 0xb6, 0x54, 0xca, 0x11, 0x41, 0x4c, 0xba, 0x29, 0x94,
	0xf2, 0x3c, 0x1c, 0xfe, 0x7f, 0x48, 0xb9, 0x0d, 0x8f, 0x72, 0x4f, 0xe2, 0xa1, 0xb9, 0x0b, 0x5b,
	0x07, 0x74, 0x4c, 0x73, 0x84, 0x98, 0x79, 0x3f, 0xc8, 0x24, 0x97, 0x92, 0x87, 0xe6, 0x17, 0xf0,
	0x28, 0xa9, 0xd8, 0x12, 0xf8, 0xeb, 0xdb, 0xde, 0x30, 0x8f, 0xcb, 0x15, 0x34, 0xf3, 0x49, 0x65,
	0x85, 0x57, 0x4b, 0x2d, 0x86, 0x19, 0xf4, 0x1d, 0xb5, 0xae, 0x8a, 0xcc, 0xca, 0xbc, 0x80, 0xed,
	0xbb, 0x27, 0x9d, 0x39, 0x23, 0xa9, 0x1c, 0x81, 0x85, 0x10, 0x9b, 0x25, 0xd5, 0x16, 0xc8, 0xdf,
	0xd2, 0x13, 0xd9, 0xaf, 0x54, 0xa2, 0x29, 0x5b, 0xf2, 0xf7, 0x4c, 0xc6, 0x57, 0x3d, 0x6f, 0x9a,
	0xf1, 0xcd, 0x00, 0x5a, 0x45, 0x67, 0xf0, 0x90, 0xfc, 0x04, 0x16, 0xc6, 0x8c, 0x0b, 0xfd, 0x86,
	0xef, 0xad, 0x86, 0xdc, 0x84, 0xd1, 0x4f, 0x04, 0xc2, 0x19, 0x6b, 0x71, 0xd4, 0xc2, 0xfc, 0x07,
	0x03, 0x96, 0xba, 0xbe, 0x60, 0xe2, 0xf6, 0x4e, 0x7c, 0xfb, 0x91, 0x9e, 0xa0, 0x94, 0x64, 0xd7,
	0xfc, 0x51, 0xfe, 0x69, 0x6a, 0xef, 0x5e, 0x3a, 0x43, 0x31, 0x7f, 0x09, 0x0b, 0xb2, 0x78, 0xd9,
	0x84, 0xc6, 0xe0, 0x17, 0x67, 0x5d, 0xfb, 0xfc, 0xa4, 0x7f, 0xd6, 0xed, 0xf4, 0x7e, 0xd6, 0xeb,
	0x1e, 0x34, 0x1e, 0x90, 0x06, 0x54, 0x25, 0xf4, 0xac, 0x6d, 0x0d, 0x4e, 0xba, 0x56, 0xc3, 0x20,
	0x1b, 0xb0, 0x26, 0x21, 0xc7, 0xe7, 0x83, 0x9e, 0x7d, 0x68, 0x9d, 0x9e, 0x9f, 0x35, 0x4a, 0xe4,
	0x21, 0xac, 0x4b, 0x60, 0xbf, 0x77, 0x72, 0x78, 0xd4, 0xd5, 0xe0, 0xb2, 0xf9, 0xd7, 0x06, 0x3c,
	0x39, 0xa4, 0xa2, 0x4f, 0xb1, 0xdf, 0x75, 0x69, 0xfb, 0xad, 0xc3, 0xc6, 0x18, 0x3f, 0x3b, 0x41,
	0xec, 0xcb, 0x26, 0xe2, 0xc7, 0x93, 0xa9, 0x4f, 0x79, 0xb7, 0x5e, 0xd4, 0x97, 0x24, 0xdb, 0x33,
	0x83, 0x9f, 0x1f, 0xc2, 0x12, 0x95, 0x9a, 0xe8, 0xd6, 0xe9, 0xc9, 0x3c, 0x6d, 0x2d, 0x4d, 0x9b,
	0xb4, 0x1a, 0xe5, 0xb4, 0xd5, 0xf8, 0xdf, 0x12, 0x3c, 0x9d, 0x23, 0x20, 0x0f, 0xc9, 0xaf, 0x60,
	0xc3, 0x49, 0xa0, 0xb6, 0x8b, 0x60, 0xdb, 0x73, 0x42, 0x7d, 0xa5, 0xdf, 0x14, 0x5c, 0xe9, 0x3c,
	0x8e, 0x7b, 0xd3, 0xa0, 0x63, 0x27, 0xec, 0xfa, 0x22, 0xba, 0xb5, 0xd6, 0x9d, 0x59, 0x78, 0xab,
	0x0f, 0xb5, 0x84, 0x8f, 0x84, 0xa1, 0x37, 0xea, 0x22, 0x19, 0x43, 0xa3, 0xba, 0xfa, 0x8a, 0x82,
	0x60, 0x74, 0xfc, 0x18, 0x6a, 0xa9, 0xac, 0x48, 0xa1, 0xda, 0xaf, 0xea, 0x04, 0x78, 0x12, 0x7b,
	0xad, 0xbf, 0x34, 0x60, 0x2b, 0x5f, 0x04, 0xb4, 0xcf, 0x35, 0xbd, 0xd5, 0x7c, 0xf1, 0x27, 0xf9,
	0x16, 0x16, 0xdf, 0x3a, 0xe3, 0x98, 0x6a, 0x33, 0xb7, 0xdf, 0x47, 0xdf, 0x29, 0x15, 0x2c, 0xc5,
	0xef, 0xeb, 0xd2, 0xef, 0x19, 0xe6, 0xdf, 0x1b, 0x32, 0x6c, 0x9c, 0x73, 0x1a, 0x4d, 0xd3, 0xd0,
	0x37, 0xc9, 0x55, 0x19, 0xe9, 0x38, 0xe9, 0xc7, 0x53, 0xee, 0x7d, 0x7f, 0x57, 0x79, 0x0d, 0xab,
	0xea, 0xfa, 0x93, 0x27, 0x7c, 0xcf, 0xd7, 0x01, 0x6a, 0x97, 0x7c, 0xe6, 0x5f, 0xca, 0xa0, 0x95,
	0x23, 0x28, 0x0f, 0xf1, 0x9d, 0x4a, 0xb7, 0xd0, 0xb2, 0xaa, 0x85, 0xf9, 0x67, 0xb0, 0xd6, 0xcb,
	0xcc, 0x27, 0xf2, 0x55, 0x7a, 0x3f, 0x2f, 0xfe, 0x18, 0x6a, 0x17, 0x31, 0x67, 0x3e, 0xe5, 0x3c,
	0x55, 0xa9, 0x66, 0x55, 0x13, 0xa0, 0x94, 0xf8, 0x25, 0x34, 0xa6, 0xcf, 0xe7, 0x21, 0xe6, 0xe8,
	0x64, 0x92, 0xa2, 0x67, 0x58, 0xcb, 0x7a, 0x86, 0x62, 0xfe, 0x9d, 0x01, 0x8f, 0x3a, 0x81, 0xcf,
	0x63, 0x8f, 0xde, 0xe3, 0x2a, 0xde, 0x4f, 0xee, 0xc7, 0x50, 0x91, 0xc5, 0x71, 0x46, 0x66, 0x39,
	0x4d, 0x94, 0xd1, 0xe7, 0x8e, 0x52, 0x0b, 0x39, 0x4a, 0xfd, 0xb7, 0x01, 0xcd, 0x7c, 0x29, 0x79,
	0x88, 0xbd, 0x2b, 0x8f, 0x5d, 0x97, 0x72, 0x9e, 0x28, 0xa7, 0x97, 0x64, 0x17, 0x1a, 0x3e, 0xa5,
	0xc3, 0xc9, 0xa0, 0x4c, 0xb0, 0x50, 0x0a, 0xbe, 0x62, 0xd5, 0x11, 0xae, 0x87, 0x64, 0x03, 0x16,
	0x92, 0xcf, 0x35, 0xa5, 0x1b, 0x47, 0xf6, 0x10, 0x2b, 0x27, 0x16, 0xea, 0x81, 0x54, 0x0d, 0xe1,
	0x9d, 0x38, 0x3a, 0x70, 0x6e, 0x91, 0xf0, 0x13, 0x90, 0x5b, 0x6d, 0x7a, 0x23, 0x22, 0x47, 0x92,
	0xe9, 0xa1, 0x14, 0x42, 0xbb, 0x08, 0x44, 0xaa, 0xdf, 0x87, 0x4a, 0xcc, 0xa9, 0x1e, 0x66, 0x2e,
	0xde, 0xdb, 0x6f, 0xb1, 0xc8, 0x92, 0x63, 0x4c, 0xf3, 0x3f, 0x0d, 0x39, 0x0a, 0x19, 0xb0, 0x90,
	0x27, 0xd7, 0x11, 0x53, 0x2e, 0x3e, 0xa4, 0x2b, 0x71, 0x7d, 0x72, 0x36, 0xc1, 0x55, 0x79, 0x46,
	0x9c, 0x7b, 0x5d, 0xcd, 0xf4, 0xe5, 0x2e, 0xce, 0x5c, 0xee, 0x36, 0xac, 0x08, 0x16, 0x66, 0xc7,
	0xe8, 0xcb, 0x82, 0xa9, 0x04, 0xfa, 0x6f, 0x06, 0x6c, 0x4e, 0x6b, 0xc8, 0xc3, 0xc0, 0xe7, 0x94,
	0x7c, 0x0a, 0x6b, 0xc9, 0x2d, 0xb8, 0x97, 0xd9, 0x78, 0x57, 0x75, 0xe5, 0x2d, 0x74, 0x2e, 0x65,
	0xc8, 0xdb, 0x83, 0x0d, 0x75, 0x07, 0x79, 0x81, 0x6f, 0x5d, 0xa2, 0xda, 0x99, 0xe8, 0x47, 0xbe,
	0x82, 0x2d, 0x29, 0xa7, 0xf4, 0x85, 0x4b, 0x86, 0x77, 0xad, 0xb9, 0x2b, 0x8f, 0xdc, 0x40, 0x6c,
	0x3f, 0x41, 0xea, 0x43, 0x7e, 0x00, 0x0f, 0x33, 0x39, 0x40, 0x4b, 0x95, 0x16, 0xc1, 0x24, 0x8d,
	0xdc, 0x52, 0xb4, 0x93, 0xd8, 0x33, 0xff, 0x00, 0x16, 0x30, 0x5c, 0xe4, 0xdc, 0x14, 0x76, 0x3a,
	0x42, 0x8f, 0xc6, 0xb0, 0xd3, 0x11, 0x6c, 0x88, 0x3d, 0x8d, 0xcf, 0xdc, 0x6b, 0xd9, 0x86, 0xa8,
	0x1e, 0x74, 0xb2, 0x36, 0x4f, 0x61, 0x5b, 0x0e, 0xf8, 0xda, 0xe3, 0x71, 0xe0, 0x62, 0x89, 0x8f,
	0x7c, 0x13, 0x47, 0x28, 0x6c, 0x9a, 0xb1, 0x54, 0x67, 0x43, 0x5b, 0x96, 0x1f, 0x25, 0x35, 0xa9,
	0x8f, 0xd9, 0xf0, 0x88, 0x71, 0x61, 0x3e, 0x81, 0x56, 0x1e, 0x43, 0x65, 0x77, 0xf3, 0x8f, 0xf4,
	0x08, 0xf4, 0x80, 0x3a, 0x1f, 0xea, 0xc0, 0x67, 0x7a, 0x44, 0x79, 0x87, 0xa5, 0x3e, 0x72, 0x08,
	0x8f, 0x0f, 0xa9, 0xec, 0x91, 0xa5, 0x48, 0x88, 0xd3, 0x43, 0xc6, 0xf9, 0x47, 0x6e, 0xc1, 0x92,
	0x1b, 0x47, 0x3c, 0x98, 0x34, 0x74, 0x6a, 0x95, 0x3f, 0x67, 0x34, 0xff, 0x4a, 0xd5, 0x22, 0x39,
	0xc7, 0x68, 0x8f, 0xdb, 0x9b, 0xaa, 0xd6, 0x5a, 0xf9, 0x0f, 0x48, 0x0a, 0xae, 0x0a, 0xb4, 0x6d,
	0x58, 0xb9, 0x72, 0xb8, 0xed, 0x05, 0x11, 0xd5, 0xe1, 0x64, 0xf9, 0xca, 0xe1, 0xc7, 0x41, 0x44,
	0xb1, 0xf4, 0xf7, 0xe9, 0x8d, 0xb0, 0xb5, 0x78, 0xaa, 0x9d, 0x02, 0x04, 0x75, 0x24, 0xc4, 0xfc,
	0xaf, 0x12, 0x3c, 0xd1, 0x03, 0xee, 0x76, 0xef, 0x2c, 0xa2, 0xf8, 0xe0, 0xba, 0x6f, 0x69, 0xfa,
	0xc2, 0xbf, 0x85, 0x45, 0x8a, 0x6b, 0x5d, 0x02, 0x17, 0x24, 0xde, 0x79, 0x2c, 0xf6, 0x66, 0xa0,
	0x8a, 0x5f, 0xeb, 0x7f, 0x0c, 0xa8, 0x4f, 0x63, 0x50, 0x91, 0xcb, 0x28, 0xf0, 0xec, 0xd4, 0x51,
	0x97, 0x71, 0x7d, 0xce, 0x86, 0x18, 0xe7, 0x84, 0x13, 0x8d, 0xe8, 0xe4, 0x93, 0x49, 0x52, 0x52,
	0x28, 0xa8, 0xfa, 0x6a, 0x82, 0xcd, 0xae, 0xa6, 0x0a, 0x9d, 0x48, 0xc8, 0x29, 0x60, 0x52, 0x65,
	0xad, 0x29, 0xc4, 0x99, 0x82, 0xf7, 0x86, 0x78, 0x9b, 0x4c, 0x50, 0xcf, 0x9e, 0x8c, 0x7c, 0x97,
	0x70, 0xd9, 0x53, 0x13, 0x46, 0x44, 0xa8, 0x64, 0xba, 0xa8, 0x27, 0x8c, 0x82, 0x7a, 0xaa, 0xf4,
	0xc1, 0x6e, 0x0a, 0x2b, 0x60, 0x3b, 0x8c, 0x98, 0x3b, 0xf9, 0x1a, 0x27, 0x41, 0x67, 0x08, 0xd1,
	0xd9, 0xed, 0x92, 0x45, 0x5c, 0xc8, 0x26, 0x53, 0x66, 0xb7, 0x9f, 0xe1, 0xd2, 0x7c, 0x0e, 0x4f,
	0x0b, 0x2c, 0xa5, 0xae, 0xfe, 0xc5, 0xdf, 0x18, 0x50, 0x99, 0x7c, 0x5b, 0xc4, 0x4a, 0x78, 0xb2,
	0x38, 0xf7, 0xaf, 0xfd, 0xe0, 0x3b, 0xbf, 0xf1, 0x80, 0xac, 0x43, 0x6d, 0x02, 0xc5, 0xc6, 0xb9,
	0x61, 0x60, 0x71, 0x3c, 0x01, 0x75, 0x62, 0xaa, 0xea, 0xe0, 0x09, 0xa4, 0xeb, 0x05, 0x82, 0xb9,
	0x81, 0xdf, 0x28, 0x4f, 0x71, 0xec, 0xb3, 0x31, 0x86, 0xd6, 0xc6, 0x02, 0xd9, 0x02, 0x32, 0x81,
	0xb6, 0x59, 0x34, 0x60, 0xee, 0x35, 0x15, 0x8d, 0x45, 0x42, 0xa0, 0x2e, 0xe1, 0xed, 0x9e, 0x36,
	0x5b, 0x63, 0xe9, 0xc5, 0x3f, 0x19, 0x9a, 0x45, 0x27, 0xf3, 0x75, 0xf1, 0x63, 0x78, 0xde, 0x3b,
	0xb6, 0x8f, 0xfb, 0x87, 0x76, 0xe7, 0xf4, 0x64, 0xd0, 0x3d, 0x19, 0xd8, 0x39, 0x15, 0xfc, 0x13,
	0x68, 0xe6, 0x11, 0x0d, 0xba, 0x3f, 0x1f, 0x34, 0x0c, 0x62, 0xc2, 0xb3, 0x22, 0xac, 0xfd, 0xc7,
	0xa7, 0xbd, 0x4e, 0xb7, 0x51, 0x22, 0xcf, 0xe1, 0x71, 0x1e, 0x4d, 0xf7, 0xf8, 0x74, 0xd0, 0x3b,
	0x3d, 0x69, 0x94, 0xc9, 0x53, 0xd8, 0xce, 0x23, 0xe8, 0x1d, 0xb7, 0x0f, 0xbb, 0x8d, 0x85, 0x17,
	0xbf, 0x35, 0x00, 0x7a, 0xe9, 0x67, 0x53, 0x25, 0xd0, 0xeb, 0xf3, 0x7e, 0x2f, 0x4f, 0x5c, 0xc5,
	0x2b, 0xc5, 0x4e, 0x75, 0x14, 0xc6, 0x9d, 0xcd, 0xc7, 0xe7, 0x47, 0x99, 0x36, 0xe4, 0x31, 0x3c,
	0xca, 0xc1, 0x5a, 0xa7, 0x47, 0xdd, 0x46, 0xf9, 0xc5, 0xdf, 0xca, 0x4b, 0x4e, 0xbe, 0xbc, 0x2a,
	0xd2, 0xce, 0xf1, 0x41, 0x9e, 0x10, 0x33, 0xc8, 0x44, 0xab, 0xe3, 0xfe, 0xe1, 0x44, 0x84, 0x09,
	0xb2, 0xdf, 0x45, 0x64, 0xa7, 0x6b, 0x0f, 0x7a, 0x28, 0xc2, 0x33, 0x68, 0x65, 0xb1, 0xdf, 0x9c,
	0xf6, 0x4e, 0x94, 0x78, 0x12, 0x5f, 0x9e, 0x65, 0xfd, 0x6d, 0xf7, 0xa8, 0x73, 0x7a, 0xdc, 0x95,
	0xac, 0x17, 0x5e, 0xfc, 0x29, 0xd4, 0xa6, 0x3e, 0x6b, 0x92, 0x26, 0x6c, 0x4e, 0x01, 0x52, 0x77,
	0xbc, 0x83, 0xe1, 0x34, 0xda, 0x6f, 0xf7, 0x1a, 0xc6, 0x1d, 0x4c, 0xbb, 0xb7, 0x8f, 0xc8, 0x46,
	0xe9, 0xc5, 0x6f, 0x0c, 0xa8, 0x66, 0x2b, 0x0d, 0x34, 0x76, 0x2a, 0xfe, 0x5d, 0x33, 0xec, 0xc0,
	0x93, 0x69, 0x34, 0x5a, 0xd2, 0x4e, 0x29, 0x0c, 0xb2, 0x0d, 0x0f, 0xa7, 0x29, 0x3a, 0xe7, 0x96,
	0x7d, 0xd0, 0xfe, 0x45, 0xa3, 0x44, 0x1e, 0xc1, 0xc6, 0x34, 0xaa, 0xfb, 0xf3, 0x81, 0xd5, 0x6e,
	0x94, 0xf7, 0x7f, 0xdb, 0x00, 0xd2, 0x66, 0x23, 0xb7, 0xaf, 0x83, 0xd9, 0xb1, 0x8c, 0x65, 0xe4,
	0x0a, 0xd6, 0x66, 0xbe, 0xfa, 0x90, 0xdd, 0xc2, 0x76, 0x63, 0xe6, 0x63, 0x54, 0xeb, 0x8b, 0x7b,
	0x52, 0xf2, 0xd0, 0x7c, 0x40, 0x7e, 0x63, 0x40, 0xb3, 0xe8, 0x9b, 0x19, 0xf9, 0x41, 0x3e, 0xa7,
	0x39, 0x9f, 0x05, 0x5b, 0xfb, 0xef, 0xba, 0x45, 0x4a, 0x41, 0xa1, 0x3e, 0xfd, 0xc5, 0x92, 0x7c,
	0x5e, 0xfc, 0xbd, 0x74, 0xea, 0x8b, 0x6d, 0x6b, 0xf7, 0x7e, 0x84, 0xf2, 0x18, 0x01, 0x1b, 0x39,
	0x03, 0x2a, 0xf2, 0xbb, 0xf9, 0x2c, 0xf2, 0xa7, 0x66, 0xad, 0x97, 0xef, 0x40, 0x9d, 0x9c, 0x9a,
	0x33, 0x70, 0x2a, 0x3a, 0x35, 0x7f, 0x0a, 0x56, 0x74, 0x6a, 0xd1, 0x24, 0x4b, 0x9e, 0x9a, 0x33,
	0xa1, 0x2a, 0x3a, 0x35, 0x7f, 0xec, 0x55, 0x74, 0x6a, 0xd1, 0xe8, 0xeb, 0x01, 0xf9, 0x4e, 0x56,
	0xb0, 0x77, 0x26, 0x5a, 0xe4, 0xe5, 0x7c, 0x9f, 0x9c, 0x19, 0x94, 0xb5, 0xf6, 0xde, 0x85, 0x5c,
	0x1e, 0xfc, 0x6b, 0xd8, 0xca, 0x1f, 0x3e, 0x91, 0x57, 0xf7, 0xe5, 0xa5, 0xc7, 0x61, 0xad, 0x2f,
	0xdf, 0x6d, 0x83, 0x3c, 0xfe, 0x2f, 0x0c, 0x39, 0x60, 0xcb, 0xef, 0xfc, 0xc9, 0xfe, 0x3b, 0x8f,
	0x0a, 0xde, 0xb4, 0xbe, 0x7a, 0x8f, 0xf1, 0xc2, 0xe4, 0x02, 0xee, 0x74, 0xe7, 0x73, 0x2e, 0x20,
	0x6f, 0xe4, 0x30, 0xe7, 0x02, 0x72, 0x1b, 0x7f, 0xf3, 0x01, 0xb1, 0xa1, 0x9a, 0x6d, 0xb2, 0xc9,
	0xa7, 0x05, 0xff, 0xe4, 0x32, 0x3d, 0x08, 0x68, 0x7d, 0x76, 0x1f, 0xb2, 0x44, 0xb3, 0xbc, 0x7e,
	0xb7, 0x48, 0xb3, 0x82, 0x0e, 0xbe, 0x48, 0xb3, 0xa2, 0x56, 0xda, 0x7c, 0x40, 0x46, 0x50, 0xcd,
	0x76, 0x65, 0xa4, 0x38, 0xbe, 0xce, 0xf6, 0xa6, 0xad, 0x17, 0xf7, 0x21, 0xd5, 0x95, 0x3f, 0x6a,
	0x48, 0xee, 0x36, 0x23, 0x45, 0xfe, 0x5b, 0xd8, 0x07, 0x15, 0xf9, 0x6f, 0x71, 0x9f, 0x43, 0x7e,
	0x0d, 0x9b, 0x79, 0x4d, 0xc9, 0xdc, 0xf8, 0x9f, 0xdf, 0x13, 0xcd, 0x8d, 0xff, 0x05, 0x3d, 0x0f,
	0x1e, 0x9f, 0xd7, 0x8c, 0x14, 0x1d, 0x3f, 0xa7, 0x3f, 0x6a, 0xed, 0xbf, 0xcb, 0x16, 0x7d, 0xfc,
	0x9f, 0x1b, 0xf0, 0x30, 0xb7, 0x24, 0x2e, 0x7a, 0xb7, 0xf3, 0x3a, 0x8d, 0xa2, 0x77, 0x3b, 0xb7,
	0xe6, 0x7e, 0xfd, 0xd3, 0x5f, 0xfe, 0x64, 0x14, 0x8c, 0x1d, 0x7f, 0xb4, 0xf7, 0xa3, 0x7d, 0x21,
	0xf6, 0xdc, 0xc0, 0x7b, 0x25, 0xff, 0x53, 0xd0, 0x0d, 0xc6, 0xaf, 0x38, 0x8d, 0xde, 0x32, 0x97,
	0xf2, 0xe2, 0x7f, 0x35, 0xbc, 0x58, 0x92, 0xc4, 0x5f, 0xfd, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff,
	0x7e, 0x6d, 0x31, 0xc9, 0x9d, 0x28, 0x00, 0x00,
}
