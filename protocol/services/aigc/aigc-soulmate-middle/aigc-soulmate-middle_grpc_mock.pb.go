// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-soulmate-middle/aigc-soulmate-middle.proto

package aigc_soulmate_middle

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAigcSoulmateMiddleClient is a mock of AigcSoulmateMiddleClient interface.
type MockAigcSoulmateMiddleClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcSoulmateMiddleClientMockRecorder
}

// MockAigcSoulmateMiddleClientMockRecorder is the mock recorder for MockAigcSoulmateMiddleClient.
type MockAigcSoulmateMiddleClientMockRecorder struct {
	mock *MockAigcSoulmateMiddleClient
}

// NewMockAigcSoulmateMiddleClient creates a new mock instance.
func NewMockAigcSoulmateMiddleClient(ctrl *gomock.Controller) *MockAigcSoulmateMiddleClient {
	mock := &MockAigcSoulmateMiddleClient{ctrl: ctrl}
	mock.recorder = &MockAigcSoulmateMiddleClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcSoulmateMiddleClient) EXPECT() *MockAigcSoulmateMiddleClientMockRecorder {
	return m.recorder
}

// BatchAllocRoleUser mocks base method.
func (m *MockAigcSoulmateMiddleClient) BatchAllocRoleUser(ctx context.Context, in *BatchAllocRoleUserRequest, opts ...grpc.CallOption) (*BatchAllocRoleUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchAllocRoleUser", varargs...)
	ret0, _ := ret[0].(*BatchAllocRoleUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAllocRoleUser indicates an expected call of BatchAllocRoleUser.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) BatchAllocRoleUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAllocRoleUser", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).BatchAllocRoleUser), varargs...)
}

// BatchDeallocRoleUser mocks base method.
func (m *MockAigcSoulmateMiddleClient) BatchDeallocRoleUser(ctx context.Context, in *BatchDeallocRoleUserRequest, opts ...grpc.CallOption) (*BatchDeallocRoleUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDeallocRoleUser", varargs...)
	ret0, _ := ret[0].(*BatchDeallocRoleUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeallocRoleUser indicates an expected call of BatchDeallocRoleUser.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) BatchDeallocRoleUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeallocRoleUser", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).BatchDeallocRoleUser), varargs...)
}

// BatchGetGroupLastMsgList mocks base method.
func (m *MockAigcSoulmateMiddleClient) BatchGetGroupLastMsgList(ctx context.Context, in *BatchGetGroupLastMsgListReq, opts ...grpc.CallOption) (*BatchGetGroupLastMsgListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetGroupLastMsgList", varargs...)
	ret0, _ := ret[0].(*BatchGetGroupLastMsgListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGroupLastMsgList indicates an expected call of BatchGetGroupLastMsgList.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) BatchGetGroupLastMsgList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupLastMsgList", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).BatchGetGroupLastMsgList), varargs...)
}

// ConsumeSentenceCount mocks base method.
func (m *MockAigcSoulmateMiddleClient) ConsumeSentenceCount(ctx context.Context, in *ConsumeSentenceCountReq, opts ...grpc.CallOption) (*ConsumeSentenceCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConsumeSentenceCount", varargs...)
	ret0, _ := ret[0].(*ConsumeSentenceCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConsumeSentenceCount indicates an expected call of ConsumeSentenceCount.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) ConsumeSentenceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConsumeSentenceCount", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).ConsumeSentenceCount), varargs...)
}

// CreateGroupTemplate mocks base method.
func (m *MockAigcSoulmateMiddleClient) CreateGroupTemplate(ctx context.Context, in *CreateGroupTemplateReq, opts ...grpc.CallOption) (*CreateGroupTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateGroupTemplate", varargs...)
	ret0, _ := ret[0].(*CreateGroupTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGroupTemplate indicates an expected call of CreateGroupTemplate.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) CreateGroupTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGroupTemplate", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).CreateGroupTemplate), varargs...)
}

// DeleteGroupTemplate mocks base method.
func (m *MockAigcSoulmateMiddleClient) DeleteGroupTemplate(ctx context.Context, in *DeleteGroupTemplateReq, opts ...grpc.CallOption) (*DeleteGroupTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteGroupTemplate", varargs...)
	ret0, _ := ret[0].(*DeleteGroupTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteGroupTemplate indicates an expected call of DeleteGroupTemplate.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) DeleteGroupTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGroupTemplate", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).DeleteGroupTemplate), varargs...)
}

// GetGroupMsgList mocks base method.
func (m *MockAigcSoulmateMiddleClient) GetGroupMsgList(ctx context.Context, in *GetGroupMsgListReq, opts ...grpc.CallOption) (*GetGroupMsgListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroupMsgList", varargs...)
	ret0, _ := ret[0].(*GetGroupMsgListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupMsgList indicates an expected call of GetGroupMsgList.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) GetGroupMsgList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMsgList", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).GetGroupMsgList), varargs...)
}

// GetGroupTemplateById mocks base method.
func (m *MockAigcSoulmateMiddleClient) GetGroupTemplateById(ctx context.Context, in *GetGroupTemplateByIdReq, opts ...grpc.CallOption) (*GetGroupTemplateByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroupTemplateById", varargs...)
	ret0, _ := ret[0].(*GetGroupTemplateByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupTemplateById indicates an expected call of GetGroupTemplateById.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) GetGroupTemplateById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupTemplateById", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).GetGroupTemplateById), varargs...)
}

// GetGroupTemplateByPage mocks base method.
func (m *MockAigcSoulmateMiddleClient) GetGroupTemplateByPage(ctx context.Context, in *GetGroupTemplateByPageReq, opts ...grpc.CallOption) (*GetGroupTemplateByPageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroupTemplateByPage", varargs...)
	ret0, _ := ret[0].(*GetGroupTemplateByPageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupTemplateByPage indicates an expected call of GetGroupTemplateByPage.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) GetGroupTemplateByPage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupTemplateByPage", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).GetGroupTemplateByPage), varargs...)
}

// GetRoleAllocUserList mocks base method.
func (m *MockAigcSoulmateMiddleClient) GetRoleAllocUserList(ctx context.Context, in *GetRoleAllocUserListRequest, opts ...grpc.CallOption) (*GetRoleAllocUserListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRoleAllocUserList", varargs...)
	ret0, _ := ret[0].(*GetRoleAllocUserListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoleAllocUserList indicates an expected call of GetRoleAllocUserList.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) GetRoleAllocUserList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoleAllocUserList", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).GetRoleAllocUserList), varargs...)
}

// GetSentenceAvailableCount mocks base method.
func (m *MockAigcSoulmateMiddleClient) GetSentenceAvailableCount(ctx context.Context, in *GetSentenceAvailableCountReq, opts ...grpc.CallOption) (*GetSentenceAvailableCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSentenceAvailableCount", varargs...)
	ret0, _ := ret[0].(*GetSentenceAvailableCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSentenceAvailableCount indicates an expected call of GetSentenceAvailableCount.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) GetSentenceAvailableCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSentenceAvailableCount", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).GetSentenceAvailableCount), varargs...)
}

// GetTipsCount mocks base method.
func (m *MockAigcSoulmateMiddleClient) GetTipsCount(ctx context.Context, in *GetTipsCountRequest, opts ...grpc.CallOption) (*GetTipsCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTipsCount", varargs...)
	ret0, _ := ret[0].(*GetTipsCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTipsCount indicates an expected call of GetTipsCount.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) GetTipsCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTipsCount", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).GetTipsCount), varargs...)
}

// GetUserSentenceCount mocks base method.
func (m *MockAigcSoulmateMiddleClient) GetUserSentenceCount(ctx context.Context, in *GetUserSentenceCountReq, opts ...grpc.CallOption) (*GetUserSentenceCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSentenceCount", varargs...)
	ret0, _ := ret[0].(*GetUserSentenceCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSentenceCount indicates an expected call of GetUserSentenceCount.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) GetUserSentenceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSentenceCount", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).GetUserSentenceCount), varargs...)
}

// IsReachLimit mocks base method.
func (m *MockAigcSoulmateMiddleClient) IsReachLimit(ctx context.Context, in *IsReachLimitReq, opts ...grpc.CallOption) (*IsReachLimitResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsReachLimit", varargs...)
	ret0, _ := ret[0].(*IsReachLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsReachLimit indicates an expected call of IsReachLimit.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) IsReachLimit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsReachLimit", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).IsReachLimit), varargs...)
}

// SendGroupImMsg mocks base method.
func (m *MockAigcSoulmateMiddleClient) SendGroupImMsg(ctx context.Context, in *SendGroupImMsgReq, opts ...grpc.CallOption) (*SendGroupImMsgResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendGroupImMsg", varargs...)
	ret0, _ := ret[0].(*SendGroupImMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendGroupImMsg indicates an expected call of SendGroupImMsg.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) SendGroupImMsg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendGroupImMsg", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).SendGroupImMsg), varargs...)
}

// TriggerAIPresentEvent mocks base method.
func (m *MockAigcSoulmateMiddleClient) TriggerAIPresentEvent(ctx context.Context, in *TriggerAIPresentEventRequest, opts ...grpc.CallOption) (*TriggerAIPresentEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerAIPresentEvent", varargs...)
	ret0, _ := ret[0].(*TriggerAIPresentEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerAIPresentEvent indicates an expected call of TriggerAIPresentEvent.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) TriggerAIPresentEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerAIPresentEvent", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).TriggerAIPresentEvent), varargs...)
}

// UpdateGroupTemplate mocks base method.
func (m *MockAigcSoulmateMiddleClient) UpdateGroupTemplate(ctx context.Context, in *UpdateGroupTemplateReq, opts ...grpc.CallOption) (*UpdateGroupTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateGroupTemplate", varargs...)
	ret0, _ := ret[0].(*UpdateGroupTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGroupTemplate indicates an expected call of UpdateGroupTemplate.
func (mr *MockAigcSoulmateMiddleClientMockRecorder) UpdateGroupTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupTemplate", reflect.TypeOf((*MockAigcSoulmateMiddleClient)(nil).UpdateGroupTemplate), varargs...)
}

// MockAigcSoulmateMiddleServer is a mock of AigcSoulmateMiddleServer interface.
type MockAigcSoulmateMiddleServer struct {
	ctrl     *gomock.Controller
	recorder *MockAigcSoulmateMiddleServerMockRecorder
}

// MockAigcSoulmateMiddleServerMockRecorder is the mock recorder for MockAigcSoulmateMiddleServer.
type MockAigcSoulmateMiddleServerMockRecorder struct {
	mock *MockAigcSoulmateMiddleServer
}

// NewMockAigcSoulmateMiddleServer creates a new mock instance.
func NewMockAigcSoulmateMiddleServer(ctrl *gomock.Controller) *MockAigcSoulmateMiddleServer {
	mock := &MockAigcSoulmateMiddleServer{ctrl: ctrl}
	mock.recorder = &MockAigcSoulmateMiddleServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcSoulmateMiddleServer) EXPECT() *MockAigcSoulmateMiddleServerMockRecorder {
	return m.recorder
}

// BatchAllocRoleUser mocks base method.
func (m *MockAigcSoulmateMiddleServer) BatchAllocRoleUser(ctx context.Context, in *BatchAllocRoleUserRequest) (*BatchAllocRoleUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAllocRoleUser", ctx, in)
	ret0, _ := ret[0].(*BatchAllocRoleUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAllocRoleUser indicates an expected call of BatchAllocRoleUser.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) BatchAllocRoleUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAllocRoleUser", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).BatchAllocRoleUser), ctx, in)
}

// BatchDeallocRoleUser mocks base method.
func (m *MockAigcSoulmateMiddleServer) BatchDeallocRoleUser(ctx context.Context, in *BatchDeallocRoleUserRequest) (*BatchDeallocRoleUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeallocRoleUser", ctx, in)
	ret0, _ := ret[0].(*BatchDeallocRoleUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeallocRoleUser indicates an expected call of BatchDeallocRoleUser.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) BatchDeallocRoleUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeallocRoleUser", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).BatchDeallocRoleUser), ctx, in)
}

// BatchGetGroupLastMsgList mocks base method.
func (m *MockAigcSoulmateMiddleServer) BatchGetGroupLastMsgList(ctx context.Context, in *BatchGetGroupLastMsgListReq) (*BatchGetGroupLastMsgListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGroupLastMsgList", ctx, in)
	ret0, _ := ret[0].(*BatchGetGroupLastMsgListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGroupLastMsgList indicates an expected call of BatchGetGroupLastMsgList.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) BatchGetGroupLastMsgList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupLastMsgList", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).BatchGetGroupLastMsgList), ctx, in)
}

// ConsumeSentenceCount mocks base method.
func (m *MockAigcSoulmateMiddleServer) ConsumeSentenceCount(ctx context.Context, in *ConsumeSentenceCountReq) (*ConsumeSentenceCountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConsumeSentenceCount", ctx, in)
	ret0, _ := ret[0].(*ConsumeSentenceCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConsumeSentenceCount indicates an expected call of ConsumeSentenceCount.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) ConsumeSentenceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConsumeSentenceCount", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).ConsumeSentenceCount), ctx, in)
}

// CreateGroupTemplate mocks base method.
func (m *MockAigcSoulmateMiddleServer) CreateGroupTemplate(ctx context.Context, in *CreateGroupTemplateReq) (*CreateGroupTemplateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGroupTemplate", ctx, in)
	ret0, _ := ret[0].(*CreateGroupTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGroupTemplate indicates an expected call of CreateGroupTemplate.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) CreateGroupTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGroupTemplate", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).CreateGroupTemplate), ctx, in)
}

// DeleteGroupTemplate mocks base method.
func (m *MockAigcSoulmateMiddleServer) DeleteGroupTemplate(ctx context.Context, in *DeleteGroupTemplateReq) (*DeleteGroupTemplateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteGroupTemplate", ctx, in)
	ret0, _ := ret[0].(*DeleteGroupTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteGroupTemplate indicates an expected call of DeleteGroupTemplate.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) DeleteGroupTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGroupTemplate", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).DeleteGroupTemplate), ctx, in)
}

// GetGroupMsgList mocks base method.
func (m *MockAigcSoulmateMiddleServer) GetGroupMsgList(ctx context.Context, in *GetGroupMsgListReq) (*GetGroupMsgListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupMsgList", ctx, in)
	ret0, _ := ret[0].(*GetGroupMsgListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupMsgList indicates an expected call of GetGroupMsgList.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) GetGroupMsgList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMsgList", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).GetGroupMsgList), ctx, in)
}

// GetGroupTemplateById mocks base method.
func (m *MockAigcSoulmateMiddleServer) GetGroupTemplateById(ctx context.Context, in *GetGroupTemplateByIdReq) (*GetGroupTemplateByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupTemplateById", ctx, in)
	ret0, _ := ret[0].(*GetGroupTemplateByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupTemplateById indicates an expected call of GetGroupTemplateById.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) GetGroupTemplateById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupTemplateById", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).GetGroupTemplateById), ctx, in)
}

// GetGroupTemplateByPage mocks base method.
func (m *MockAigcSoulmateMiddleServer) GetGroupTemplateByPage(ctx context.Context, in *GetGroupTemplateByPageReq) (*GetGroupTemplateByPageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupTemplateByPage", ctx, in)
	ret0, _ := ret[0].(*GetGroupTemplateByPageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupTemplateByPage indicates an expected call of GetGroupTemplateByPage.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) GetGroupTemplateByPage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupTemplateByPage", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).GetGroupTemplateByPage), ctx, in)
}

// GetRoleAllocUserList mocks base method.
func (m *MockAigcSoulmateMiddleServer) GetRoleAllocUserList(ctx context.Context, in *GetRoleAllocUserListRequest) (*GetRoleAllocUserListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoleAllocUserList", ctx, in)
	ret0, _ := ret[0].(*GetRoleAllocUserListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoleAllocUserList indicates an expected call of GetRoleAllocUserList.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) GetRoleAllocUserList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoleAllocUserList", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).GetRoleAllocUserList), ctx, in)
}

// GetSentenceAvailableCount mocks base method.
func (m *MockAigcSoulmateMiddleServer) GetSentenceAvailableCount(ctx context.Context, in *GetSentenceAvailableCountReq) (*GetSentenceAvailableCountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSentenceAvailableCount", ctx, in)
	ret0, _ := ret[0].(*GetSentenceAvailableCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSentenceAvailableCount indicates an expected call of GetSentenceAvailableCount.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) GetSentenceAvailableCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSentenceAvailableCount", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).GetSentenceAvailableCount), ctx, in)
}

// GetTipsCount mocks base method.
func (m *MockAigcSoulmateMiddleServer) GetTipsCount(ctx context.Context, in *GetTipsCountRequest) (*GetTipsCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTipsCount", ctx, in)
	ret0, _ := ret[0].(*GetTipsCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTipsCount indicates an expected call of GetTipsCount.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) GetTipsCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTipsCount", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).GetTipsCount), ctx, in)
}

// GetUserSentenceCount mocks base method.
func (m *MockAigcSoulmateMiddleServer) GetUserSentenceCount(ctx context.Context, in *GetUserSentenceCountReq) (*GetUserSentenceCountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSentenceCount", ctx, in)
	ret0, _ := ret[0].(*GetUserSentenceCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSentenceCount indicates an expected call of GetUserSentenceCount.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) GetUserSentenceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSentenceCount", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).GetUserSentenceCount), ctx, in)
}

// IsReachLimit mocks base method.
func (m *MockAigcSoulmateMiddleServer) IsReachLimit(ctx context.Context, in *IsReachLimitReq) (*IsReachLimitResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsReachLimit", ctx, in)
	ret0, _ := ret[0].(*IsReachLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsReachLimit indicates an expected call of IsReachLimit.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) IsReachLimit(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsReachLimit", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).IsReachLimit), ctx, in)
}

// SendGroupImMsg mocks base method.
func (m *MockAigcSoulmateMiddleServer) SendGroupImMsg(ctx context.Context, in *SendGroupImMsgReq) (*SendGroupImMsgResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendGroupImMsg", ctx, in)
	ret0, _ := ret[0].(*SendGroupImMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendGroupImMsg indicates an expected call of SendGroupImMsg.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) SendGroupImMsg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendGroupImMsg", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).SendGroupImMsg), ctx, in)
}

// TriggerAIPresentEvent mocks base method.
func (m *MockAigcSoulmateMiddleServer) TriggerAIPresentEvent(ctx context.Context, in *TriggerAIPresentEventRequest) (*TriggerAIPresentEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerAIPresentEvent", ctx, in)
	ret0, _ := ret[0].(*TriggerAIPresentEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerAIPresentEvent indicates an expected call of TriggerAIPresentEvent.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) TriggerAIPresentEvent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerAIPresentEvent", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).TriggerAIPresentEvent), ctx, in)
}

// UpdateGroupTemplate mocks base method.
func (m *MockAigcSoulmateMiddleServer) UpdateGroupTemplate(ctx context.Context, in *UpdateGroupTemplateReq) (*UpdateGroupTemplateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGroupTemplate", ctx, in)
	ret0, _ := ret[0].(*UpdateGroupTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGroupTemplate indicates an expected call of UpdateGroupTemplate.
func (mr *MockAigcSoulmateMiddleServerMockRecorder) UpdateGroupTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupTemplate", reflect.TypeOf((*MockAigcSoulmateMiddleServer)(nil).UpdateGroupTemplate), ctx, in)
}
