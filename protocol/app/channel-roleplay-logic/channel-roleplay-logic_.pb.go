// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel_roleplay_logic/channel-roleplay-logic_.proto

package channel_roleplay_logic // import "golang.52tt.com/protocol/app/channel-roleplay-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 角色类型
type RoleType int32

const (
	RoleType_RoleTypeNil      RoleType = 0
	RoleType_RoleTypeRolePlay RoleType = 1
	RoleType_RoleTypeVest     RoleType = 2
)

var RoleType_name = map[int32]string{
	0: "RoleTypeNil",
	1: "RoleTypeRolePlay",
	2: "RoleTypeVest",
}
var RoleType_value = map[string]int32{
	"RoleTypeNil":      0,
	"RoleTypeRolePlay": 1,
	"RoleTypeVest":     2,
}

func (x RoleType) String() string {
	return proto.EnumName(RoleType_name, int32(x))
}
func (RoleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{0}
}

// 包厢类型
type BoxType int32

const (
	BoxType_BoxTypeRoleplay BoxType = 0
	BoxType_BoxTypeMelee    BoxType = 1
)

var BoxType_name = map[int32]string{
	0: "BoxTypeRoleplay",
	1: "BoxTypeMelee",
}
var BoxType_value = map[string]int32{
	"BoxTypeRoleplay": 0,
	"BoxTypeMelee":    1,
}

func (x BoxType) String() string {
	return proto.EnumName(BoxType_name, int32(x))
}
func (BoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{1}
}

type BoxNotifyType int32

const (
	BoxNotifyType_UserOpType          BoxNotifyType = 0
	BoxNotifyType_ApplyAcceptUserType BoxNotifyType = 1
	BoxNotifyType_ApplyRejectUserType BoxNotifyType = 2
)

var BoxNotifyType_name = map[int32]string{
	0: "UserOpType",
	1: "ApplyAcceptUserType",
	2: "ApplyRejectUserType",
}
var BoxNotifyType_value = map[string]int32{
	"UserOpType":          0,
	"ApplyAcceptUserType": 1,
	"ApplyRejectUserType": 2,
}

func (x BoxNotifyType) String() string {
	return proto.EnumName(BoxNotifyType_name, int32(x))
}
func (BoxNotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{2}
}

type BoxNotifyUserOpType int32

const (
	BoxNotifyUserOpType_EnterNone            BoxNotifyUserOpType = 0
	BoxNotifyUserOpType_EnterBox             BoxNotifyUserOpType = 1
	BoxNotifyUserOpType_ExitBox              BoxNotifyUserOpType = 2
	BoxNotifyUserOpType_EnterBroadcastMicBox BoxNotifyUserOpType = 3
	BoxNotifyUserOpType_ExitBroadcastMicBox  BoxNotifyUserOpType = 4
)

var BoxNotifyUserOpType_name = map[int32]string{
	0: "EnterNone",
	1: "EnterBox",
	2: "ExitBox",
	3: "EnterBroadcastMicBox",
	4: "ExitBroadcastMicBox",
}
var BoxNotifyUserOpType_value = map[string]int32{
	"EnterNone":            0,
	"EnterBox":             1,
	"ExitBox":              2,
	"EnterBroadcastMicBox": 3,
	"ExitBroadcastMicBox":  4,
}

func (x BoxNotifyUserOpType) String() string {
	return proto.EnumName(BoxNotifyUserOpType_name, int32(x))
}
func (BoxNotifyUserOpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{3}
}

type ApplyBoxType int32

const (
	ApplyBoxType_ApplyDefault ApplyBoxType = 0
	ApplyBoxType_ApplyAccept  ApplyBoxType = 1
	ApplyBoxType_ApplyReject  ApplyBoxType = 2
)

var ApplyBoxType_name = map[int32]string{
	0: "ApplyDefault",
	1: "ApplyAccept",
	2: "ApplyReject",
}
var ApplyBoxType_value = map[string]int32{
	"ApplyDefault": 0,
	"ApplyAccept":  1,
	"ApplyReject":  2,
}

func (x ApplyBoxType) String() string {
	return proto.EnumName(ApplyBoxType_name, int32(x))
}
func (ApplyBoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{4}
}

type AudioBoxId int32

const (
	AudioBoxId_BoxIdNone              AudioBoxId = 0
	AudioBoxId_BoxIdKeep              AudioBoxId = 1
	AudioBoxId_SubChannelCommonBoxId  AudioBoxId = 2
	AudioBoxId_MainChannelCommonBoxId AudioBoxId = 3
	AudioBoxId_BroadCastBoxId         AudioBoxId = 4
)

var AudioBoxId_name = map[int32]string{
	0: "BoxIdNone",
	1: "BoxIdKeep",
	2: "SubChannelCommonBoxId",
	3: "MainChannelCommonBoxId",
	4: "BroadCastBoxId",
}
var AudioBoxId_value = map[string]int32{
	"BoxIdNone":              0,
	"BoxIdKeep":              1,
	"SubChannelCommonBoxId":  2,
	"MainChannelCommonBoxId": 3,
	"BroadCastBoxId":         4,
}

func (x AudioBoxId) String() string {
	return proto.EnumName(AudioBoxId_name, int32(x))
}
func (AudioBoxId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{5}
}

type ChannelUserRoleNotify_AuditResult int32

const (
	ChannelUserRoleNotify_AuditResultDefault ChannelUserRoleNotify_AuditResult = 0
	ChannelUserRoleNotify_AuditResultPass    ChannelUserRoleNotify_AuditResult = 1
	ChannelUserRoleNotify_AuditResultReject  ChannelUserRoleNotify_AuditResult = 2
	ChannelUserRoleNotify_AuditResultReview  ChannelUserRoleNotify_AuditResult = 3
)

var ChannelUserRoleNotify_AuditResult_name = map[int32]string{
	0: "AuditResultDefault",
	1: "AuditResultPass",
	2: "AuditResultReject",
	3: "AuditResultReview",
}
var ChannelUserRoleNotify_AuditResult_value = map[string]int32{
	"AuditResultDefault": 0,
	"AuditResultPass":    1,
	"AuditResultReject":  2,
	"AuditResultReview":  3,
}

func (x ChannelUserRoleNotify_AuditResult) String() string {
	return proto.EnumName(ChannelUserRoleNotify_AuditResult_name, int32(x))
}
func (ChannelUserRoleNotify_AuditResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{5, 0}
}

type BoxChangeNotify_Op int32

const (
	BoxChangeNotify_OpNone       BoxChangeNotify_Op = 0
	BoxChangeNotify_OpCreate     BoxChangeNotify_Op = 1
	BoxChangeNotify_OpDelete     BoxChangeNotify_Op = 2
	BoxChangeNotify_OpUpdateMic  BoxChangeNotify_Op = 3
	BoxChangeNotify_OpUpdateName BoxChangeNotify_Op = 4
)

var BoxChangeNotify_Op_name = map[int32]string{
	0: "OpNone",
	1: "OpCreate",
	2: "OpDelete",
	3: "OpUpdateMic",
	4: "OpUpdateName",
}
var BoxChangeNotify_Op_value = map[string]int32{
	"OpNone":       0,
	"OpCreate":     1,
	"OpDelete":     2,
	"OpUpdateMic":  3,
	"OpUpdateName": 4,
}

func (x BoxChangeNotify_Op) String() string {
	return proto.EnumName(BoxChangeNotify_Op_name, int32(x))
}
func (BoxChangeNotify_Op) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{31, 0}
}

type BoxChangeNotify_Result int32

const (
	BoxChangeNotify_ResultNone   BoxChangeNotify_Result = 0
	BoxChangeNotify_ResultPass   BoxChangeNotify_Result = 1
	BoxChangeNotify_ResultReject BoxChangeNotify_Result = 2
)

var BoxChangeNotify_Result_name = map[int32]string{
	0: "ResultNone",
	1: "ResultPass",
	2: "ResultReject",
}
var BoxChangeNotify_Result_value = map[string]int32{
	"ResultNone":   0,
	"ResultPass":   1,
	"ResultReject": 2,
}

func (x BoxChangeNotify_Result) String() string {
	return proto.EnumName(BoxChangeNotify_Result_name, int32(x))
}
func (BoxChangeNotify_Result) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{31, 1}
}

type GetChannelHoldMicUserRoleListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelHoldMicUserRoleListReq) Reset()         { *m = GetChannelHoldMicUserRoleListReq{} }
func (m *GetChannelHoldMicUserRoleListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelHoldMicUserRoleListReq) ProtoMessage()    {}
func (*GetChannelHoldMicUserRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{0}
}
func (m *GetChannelHoldMicUserRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListReq.Unmarshal(m, b)
}
func (m *GetChannelHoldMicUserRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelHoldMicUserRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelHoldMicUserRoleListReq.Merge(dst, src)
}
func (m *GetChannelHoldMicUserRoleListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListReq.Size(m)
}
func (m *GetChannelHoldMicUserRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelHoldMicUserRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelHoldMicUserRoleListReq proto.InternalMessageInfo

func (m *GetChannelHoldMicUserRoleListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelHoldMicUserRoleListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type UserRole struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleName string `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	// 角色信息变更时间戳
	UpdatedAt            uint32   `protobuf:"varint,3,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRole) Reset()         { *m = UserRole{} }
func (m *UserRole) String() string { return proto.CompactTextString(m) }
func (*UserRole) ProtoMessage()    {}
func (*UserRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{1}
}
func (m *UserRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRole.Unmarshal(m, b)
}
func (m *UserRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRole.Marshal(b, m, deterministic)
}
func (dst *UserRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRole.Merge(dst, src)
}
func (m *UserRole) XXX_Size() int {
	return xxx_messageInfo_UserRole.Size(m)
}
func (m *UserRole) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRole.DiscardUnknown(m)
}

var xxx_messageInfo_UserRole proto.InternalMessageInfo

func (m *UserRole) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserRole) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *UserRole) GetUpdatedAt() uint32 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

type GetChannelHoldMicUserRoleListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RoleList             []*UserRole   `protobuf:"bytes,2,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelHoldMicUserRoleListResp) Reset()         { *m = GetChannelHoldMicUserRoleListResp{} }
func (m *GetChannelHoldMicUserRoleListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelHoldMicUserRoleListResp) ProtoMessage()    {}
func (*GetChannelHoldMicUserRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{2}
}
func (m *GetChannelHoldMicUserRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListResp.Unmarshal(m, b)
}
func (m *GetChannelHoldMicUserRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelHoldMicUserRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelHoldMicUserRoleListResp.Merge(dst, src)
}
func (m *GetChannelHoldMicUserRoleListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListResp.Size(m)
}
func (m *GetChannelHoldMicUserRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelHoldMicUserRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelHoldMicUserRoleListResp proto.InternalMessageInfo

func (m *GetChannelHoldMicUserRoleListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelHoldMicUserRoleListResp) GetRoleList() []*UserRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type SetMyChannelRoleReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoleName             string       `protobuf:"bytes,3,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	RoleType             RoleType     `protobuf:"varint,4,opt,name=role_type,json=roleType,proto3,enum=ga.channel_roleplay_logic.RoleType" json:"role_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetMyChannelRoleReq) Reset()         { *m = SetMyChannelRoleReq{} }
func (m *SetMyChannelRoleReq) String() string { return proto.CompactTextString(m) }
func (*SetMyChannelRoleReq) ProtoMessage()    {}
func (*SetMyChannelRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{3}
}
func (m *SetMyChannelRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMyChannelRoleReq.Unmarshal(m, b)
}
func (m *SetMyChannelRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMyChannelRoleReq.Marshal(b, m, deterministic)
}
func (dst *SetMyChannelRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMyChannelRoleReq.Merge(dst, src)
}
func (m *SetMyChannelRoleReq) XXX_Size() int {
	return xxx_messageInfo_SetMyChannelRoleReq.Size(m)
}
func (m *SetMyChannelRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMyChannelRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMyChannelRoleReq proto.InternalMessageInfo

func (m *SetMyChannelRoleReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetMyChannelRoleReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMyChannelRoleReq) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *SetMyChannelRoleReq) GetRoleType() RoleType {
	if m != nil {
		return m.RoleType
	}
	return RoleType_RoleTypeNil
}

type SetMyChannelRoleResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetMyChannelRoleResp) Reset()         { *m = SetMyChannelRoleResp{} }
func (m *SetMyChannelRoleResp) String() string { return proto.CompactTextString(m) }
func (*SetMyChannelRoleResp) ProtoMessage()    {}
func (*SetMyChannelRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{4}
}
func (m *SetMyChannelRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMyChannelRoleResp.Unmarshal(m, b)
}
func (m *SetMyChannelRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMyChannelRoleResp.Marshal(b, m, deterministic)
}
func (dst *SetMyChannelRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMyChannelRoleResp.Merge(dst, src)
}
func (m *SetMyChannelRoleResp) XXX_Size() int {
	return xxx_messageInfo_SetMyChannelRoleResp.Size(m)
}
func (m *SetMyChannelRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMyChannelRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMyChannelRoleResp proto.InternalMessageInfo

func (m *SetMyChannelRoleResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ChannelUserRoleNotify struct {
	// 变更的角色信息
	Role *UserRole `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	// 审核结果
	Result               ChannelUserRoleNotify_AuditResult `protobuf:"varint,2,opt,name=result,proto3,enum=ga.channel_roleplay_logic.ChannelUserRoleNotify_AuditResult" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ChannelUserRoleNotify) Reset()         { *m = ChannelUserRoleNotify{} }
func (m *ChannelUserRoleNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelUserRoleNotify) ProtoMessage()    {}
func (*ChannelUserRoleNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{5}
}
func (m *ChannelUserRoleNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelUserRoleNotify.Unmarshal(m, b)
}
func (m *ChannelUserRoleNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelUserRoleNotify.Marshal(b, m, deterministic)
}
func (dst *ChannelUserRoleNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelUserRoleNotify.Merge(dst, src)
}
func (m *ChannelUserRoleNotify) XXX_Size() int {
	return xxx_messageInfo_ChannelUserRoleNotify.Size(m)
}
func (m *ChannelUserRoleNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelUserRoleNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelUserRoleNotify proto.InternalMessageInfo

func (m *ChannelUserRoleNotify) GetRole() *UserRole {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *ChannelUserRoleNotify) GetResult() ChannelUserRoleNotify_AuditResult {
	if m != nil {
		return m.Result
	}
	return ChannelUserRoleNotify_AuditResultDefault
}

type ChannelRoleChangeMsg struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	RoleName             string   `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRoleChangeMsg) Reset()         { *m = ChannelRoleChangeMsg{} }
func (m *ChannelRoleChangeMsg) String() string { return proto.CompactTextString(m) }
func (*ChannelRoleChangeMsg) ProtoMessage()    {}
func (*ChannelRoleChangeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{6}
}
func (m *ChannelRoleChangeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRoleChangeMsg.Unmarshal(m, b)
}
func (m *ChannelRoleChangeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRoleChangeMsg.Marshal(b, m, deterministic)
}
func (dst *ChannelRoleChangeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRoleChangeMsg.Merge(dst, src)
}
func (m *ChannelRoleChangeMsg) XXX_Size() int {
	return xxx_messageInfo_ChannelRoleChangeMsg.Size(m)
}
func (m *ChannelRoleChangeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRoleChangeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRoleChangeMsg proto.InternalMessageInfo

func (m *ChannelRoleChangeMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ChannelRoleChangeMsg) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

type EnterBoxReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Channelid            uint32       `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32       `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxType              BoxType      `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	AudioBoxid           uint32       `protobuf:"varint,5,opt,name=audio_boxid,json=audioBoxid,proto3" json:"audio_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *EnterBoxReq) Reset()         { *m = EnterBoxReq{} }
func (m *EnterBoxReq) String() string { return proto.CompactTextString(m) }
func (*EnterBoxReq) ProtoMessage()    {}
func (*EnterBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{7}
}
func (m *EnterBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterBoxReq.Unmarshal(m, b)
}
func (m *EnterBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterBoxReq.Marshal(b, m, deterministic)
}
func (dst *EnterBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterBoxReq.Merge(dst, src)
}
func (m *EnterBoxReq) XXX_Size() int {
	return xxx_messageInfo_EnterBoxReq.Size(m)
}
func (m *EnterBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_EnterBoxReq proto.InternalMessageInfo

func (m *EnterBoxReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *EnterBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *EnterBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *EnterBoxReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *EnterBoxReq) GetAudioBoxid() uint32 {
	if m != nil {
		return m.AudioBoxid
	}
	return 0
}

type EnterBoxResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OptTs                uint64        `protobuf:"varint,2,opt,name=opt_ts,json=optTs,proto3" json:"opt_ts,omitempty"`
	IsNeedApply          bool          `protobuf:"varint,3,opt,name=is_need_apply,json=isNeedApply,proto3" json:"is_need_apply,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *EnterBoxResp) Reset()         { *m = EnterBoxResp{} }
func (m *EnterBoxResp) String() string { return proto.CompactTextString(m) }
func (*EnterBoxResp) ProtoMessage()    {}
func (*EnterBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{8}
}
func (m *EnterBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterBoxResp.Unmarshal(m, b)
}
func (m *EnterBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterBoxResp.Marshal(b, m, deterministic)
}
func (dst *EnterBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterBoxResp.Merge(dst, src)
}
func (m *EnterBoxResp) XXX_Size() int {
	return xxx_messageInfo_EnterBoxResp.Size(m)
}
func (m *EnterBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_EnterBoxResp proto.InternalMessageInfo

func (m *EnterBoxResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *EnterBoxResp) GetOptTs() uint64 {
	if m != nil {
		return m.OptTs
	}
	return 0
}

func (m *EnterBoxResp) GetIsNeedApply() bool {
	if m != nil {
		return m.IsNeedApply
	}
	return false
}

type ExitBoxReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Channelid            uint32       `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32       `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxType              BoxType      `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	AudioBoxid           uint32       `protobuf:"varint,5,opt,name=audio_boxid,json=audioBoxid,proto3" json:"audio_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ExitBoxReq) Reset()         { *m = ExitBoxReq{} }
func (m *ExitBoxReq) String() string { return proto.CompactTextString(m) }
func (*ExitBoxReq) ProtoMessage()    {}
func (*ExitBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{9}
}
func (m *ExitBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitBoxReq.Unmarshal(m, b)
}
func (m *ExitBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitBoxReq.Marshal(b, m, deterministic)
}
func (dst *ExitBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitBoxReq.Merge(dst, src)
}
func (m *ExitBoxReq) XXX_Size() int {
	return xxx_messageInfo_ExitBoxReq.Size(m)
}
func (m *ExitBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExitBoxReq proto.InternalMessageInfo

func (m *ExitBoxReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ExitBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ExitBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *ExitBoxReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *ExitBoxReq) GetAudioBoxid() uint32 {
	if m != nil {
		return m.AudioBoxid
	}
	return 0
}

type ExitBoxResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OptTs                uint64        `protobuf:"varint,2,opt,name=opt_ts,json=optTs,proto3" json:"opt_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ExitBoxResp) Reset()         { *m = ExitBoxResp{} }
func (m *ExitBoxResp) String() string { return proto.CompactTextString(m) }
func (*ExitBoxResp) ProtoMessage()    {}
func (*ExitBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{10}
}
func (m *ExitBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitBoxResp.Unmarshal(m, b)
}
func (m *ExitBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitBoxResp.Marshal(b, m, deterministic)
}
func (dst *ExitBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitBoxResp.Merge(dst, src)
}
func (m *ExitBoxResp) XXX_Size() int {
	return xxx_messageInfo_ExitBoxResp.Size(m)
}
func (m *ExitBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExitBoxResp proto.InternalMessageInfo

func (m *ExitBoxResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ExitBoxResp) GetOptTs() uint64 {
	if m != nil {
		return m.OptTs
	}
	return 0
}

type OpeBoxInfo struct {
	OpeType              BoxNotifyUserOpType `protobuf:"varint,1,opt,name=ope_type,json=opeType,proto3,enum=ga.channel_roleplay_logic.BoxNotifyUserOpType" json:"ope_type,omitempty"`
	Boxid                uint32              `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	Uid                  uint32              `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	BoxCnt               uint32              `protobuf:"varint,4,opt,name=box_cnt,json=boxCnt,proto3" json:"box_cnt,omitempty"`
	NotifyType           BoxNotifyType       `protobuf:"varint,5,opt,name=notify_type,json=notifyType,proto3,enum=ga.channel_roleplay_logic.BoxNotifyType" json:"notify_type,omitempty"`
	OptTs                uint64              `protobuf:"varint,6,opt,name=opt_ts,json=optTs,proto3" json:"opt_ts,omitempty"`
	AudioBoxIds          []uint32            `protobuf:"varint,7,rep,packed,name=audio_box_ids,json=audioBoxIds,proto3" json:"audio_box_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *OpeBoxInfo) Reset()         { *m = OpeBoxInfo{} }
func (m *OpeBoxInfo) String() string { return proto.CompactTextString(m) }
func (*OpeBoxInfo) ProtoMessage()    {}
func (*OpeBoxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{11}
}
func (m *OpeBoxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpeBoxInfo.Unmarshal(m, b)
}
func (m *OpeBoxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpeBoxInfo.Marshal(b, m, deterministic)
}
func (dst *OpeBoxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpeBoxInfo.Merge(dst, src)
}
func (m *OpeBoxInfo) XXX_Size() int {
	return xxx_messageInfo_OpeBoxInfo.Size(m)
}
func (m *OpeBoxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OpeBoxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OpeBoxInfo proto.InternalMessageInfo

func (m *OpeBoxInfo) GetOpeType() BoxNotifyUserOpType {
	if m != nil {
		return m.OpeType
	}
	return BoxNotifyUserOpType_EnterNone
}

func (m *OpeBoxInfo) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *OpeBoxInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OpeBoxInfo) GetBoxCnt() uint32 {
	if m != nil {
		return m.BoxCnt
	}
	return 0
}

func (m *OpeBoxInfo) GetNotifyType() BoxNotifyType {
	if m != nil {
		return m.NotifyType
	}
	return BoxNotifyType_UserOpType
}

func (m *OpeBoxInfo) GetOptTs() uint64 {
	if m != nil {
		return m.OptTs
	}
	return 0
}

func (m *OpeBoxInfo) GetAudioBoxIds() []uint32 {
	if m != nil {
		return m.AudioBoxIds
	}
	return nil
}

type OpeBoxNotify struct {
	OpeBoxInfo           []*OpeBoxInfo `protobuf:"bytes,1,rep,name=ope_box_info,json=opeBoxInfo,proto3" json:"ope_box_info,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Msg                  string        `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	RoleName             string        `protobuf:"bytes,4,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	OpeRoleName          string        `protobuf:"bytes,5,opt,name=ope_role_name,json=opeRoleName,proto3" json:"ope_role_name,omitempty"`
	BoxType              BoxType       `protobuf:"varint,6,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OpeBoxNotify) Reset()         { *m = OpeBoxNotify{} }
func (m *OpeBoxNotify) String() string { return proto.CompactTextString(m) }
func (*OpeBoxNotify) ProtoMessage()    {}
func (*OpeBoxNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{12}
}
func (m *OpeBoxNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpeBoxNotify.Unmarshal(m, b)
}
func (m *OpeBoxNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpeBoxNotify.Marshal(b, m, deterministic)
}
func (dst *OpeBoxNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpeBoxNotify.Merge(dst, src)
}
func (m *OpeBoxNotify) XXX_Size() int {
	return xxx_messageInfo_OpeBoxNotify.Size(m)
}
func (m *OpeBoxNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_OpeBoxNotify.DiscardUnknown(m)
}

var xxx_messageInfo_OpeBoxNotify proto.InternalMessageInfo

func (m *OpeBoxNotify) GetOpeBoxInfo() []*OpeBoxInfo {
	if m != nil {
		return m.OpeBoxInfo
	}
	return nil
}

func (m *OpeBoxNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpeBoxNotify) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *OpeBoxNotify) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *OpeBoxNotify) GetOpeRoleName() string {
	if m != nil {
		return m.OpeRoleName
	}
	return ""
}

func (m *OpeBoxNotify) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type ApplyNotify struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Boxid                uint32   `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Msg                  string   `protobuf:"bytes,4,opt,name=msg,proto3" json:"msg,omitempty"`
	RoleName             string   `protobuf:"bytes,5,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyNotify) Reset()         { *m = ApplyNotify{} }
func (m *ApplyNotify) String() string { return proto.CompactTextString(m) }
func (*ApplyNotify) ProtoMessage()    {}
func (*ApplyNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{13}
}
func (m *ApplyNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyNotify.Unmarshal(m, b)
}
func (m *ApplyNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyNotify.Marshal(b, m, deterministic)
}
func (dst *ApplyNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyNotify.Merge(dst, src)
}
func (m *ApplyNotify) XXX_Size() int {
	return xxx_messageInfo_ApplyNotify.Size(m)
}
func (m *ApplyNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyNotify proto.InternalMessageInfo

func (m *ApplyNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ApplyNotify) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *ApplyNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyNotify) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *ApplyNotify) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

type HandleApplyBoxReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Channelid            uint32       `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32       `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	ApplyBoxType         ApplyBoxType `protobuf:"varint,4,opt,name=apply_box_type,json=applyBoxType,proto3,enum=ga.channel_roleplay_logic.ApplyBoxType" json:"apply_box_type,omitempty"`
	Uid                  uint32       `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HandleApplyBoxReq) Reset()         { *m = HandleApplyBoxReq{} }
func (m *HandleApplyBoxReq) String() string { return proto.CompactTextString(m) }
func (*HandleApplyBoxReq) ProtoMessage()    {}
func (*HandleApplyBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{14}
}
func (m *HandleApplyBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleApplyBoxReq.Unmarshal(m, b)
}
func (m *HandleApplyBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleApplyBoxReq.Marshal(b, m, deterministic)
}
func (dst *HandleApplyBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleApplyBoxReq.Merge(dst, src)
}
func (m *HandleApplyBoxReq) XXX_Size() int {
	return xxx_messageInfo_HandleApplyBoxReq.Size(m)
}
func (m *HandleApplyBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleApplyBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandleApplyBoxReq proto.InternalMessageInfo

func (m *HandleApplyBoxReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HandleApplyBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *HandleApplyBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *HandleApplyBoxReq) GetApplyBoxType() ApplyBoxType {
	if m != nil {
		return m.ApplyBoxType
	}
	return ApplyBoxType_ApplyDefault
}

func (m *HandleApplyBoxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type HandleApplyBoxResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Msg                  string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HandleApplyBoxResp) Reset()         { *m = HandleApplyBoxResp{} }
func (m *HandleApplyBoxResp) String() string { return proto.CompactTextString(m) }
func (*HandleApplyBoxResp) ProtoMessage()    {}
func (*HandleApplyBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{15}
}
func (m *HandleApplyBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleApplyBoxResp.Unmarshal(m, b)
}
func (m *HandleApplyBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleApplyBoxResp.Marshal(b, m, deterministic)
}
func (dst *HandleApplyBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleApplyBoxResp.Merge(dst, src)
}
func (m *HandleApplyBoxResp) XXX_Size() int {
	return xxx_messageInfo_HandleApplyBoxResp.Size(m)
}
func (m *HandleApplyBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleApplyBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandleApplyBoxResp proto.InternalMessageInfo

func (m *HandleApplyBoxResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *HandleApplyBoxResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type BoxInfo struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxUserCnt           uint32   `protobuf:"varint,3,opt,name=box_user_cnt,json=boxUserCnt,proto3" json:"box_user_cnt,omitempty"`
	BoxName              string   `protobuf:"bytes,4,opt,name=box_name,json=boxName,proto3" json:"box_name,omitempty"`
	BoxType              BoxType  `protobuf:"varint,5,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	MicCap               uint32   `protobuf:"varint,6,opt,name=mic_cap,json=micCap,proto3" json:"mic_cap,omitempty"`
	PublicMicList        []uint32 `protobuf:"varint,7,rep,packed,name=public_mic_list,json=publicMicList,proto3" json:"public_mic_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxInfo) Reset()         { *m = BoxInfo{} }
func (m *BoxInfo) String() string { return proto.CompactTextString(m) }
func (*BoxInfo) ProtoMessage()    {}
func (*BoxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{16}
}
func (m *BoxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxInfo.Unmarshal(m, b)
}
func (m *BoxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxInfo.Marshal(b, m, deterministic)
}
func (dst *BoxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxInfo.Merge(dst, src)
}
func (m *BoxInfo) XXX_Size() int {
	return xxx_messageInfo_BoxInfo.Size(m)
}
func (m *BoxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BoxInfo proto.InternalMessageInfo

func (m *BoxInfo) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *BoxInfo) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *BoxInfo) GetBoxUserCnt() uint32 {
	if m != nil {
		return m.BoxUserCnt
	}
	return 0
}

func (m *BoxInfo) GetBoxName() string {
	if m != nil {
		return m.BoxName
	}
	return ""
}

func (m *BoxInfo) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *BoxInfo) GetMicCap() uint32 {
	if m != nil {
		return m.MicCap
	}
	return 0
}

func (m *BoxInfo) GetPublicMicList() []uint32 {
	if m != nil {
		return m.PublicMicList
	}
	return nil
}

type GetBoxInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Channelid            uint32       `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	BoxType              BoxType      `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBoxInfoReq) Reset()         { *m = GetBoxInfoReq{} }
func (m *GetBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfoReq) ProtoMessage()    {}
func (*GetBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{17}
}
func (m *GetBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfoReq.Unmarshal(m, b)
}
func (m *GetBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfoReq.Merge(dst, src)
}
func (m *GetBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfoReq.Size(m)
}
func (m *GetBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfoReq proto.InternalMessageInfo

func (m *GetBoxInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBoxInfoReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxInfoReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type GetBoxInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	BoxInfo              []*BoxInfo    `protobuf:"bytes,2,rep,name=box_info,json=boxInfo,proto3" json:"box_info,omitempty"`
	UserBoxid            uint32        `protobuf:"varint,3,opt,name=user_boxid,json=userBoxid,proto3" json:"user_boxid,omitempty"`
	MicTotal             uint32        `protobuf:"varint,4,opt,name=mic_total,json=micTotal,proto3" json:"mic_total,omitempty"`
	UserAudioBoxids      []uint32      `protobuf:"varint,5,rep,packed,name=user_audio_boxids,json=userAudioBoxids,proto3" json:"user_audio_boxids,omitempty"`
	MainCommonBoxid      uint32        `protobuf:"varint,6,opt,name=main_common_boxid,json=mainCommonBoxid,proto3" json:"main_common_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBoxInfoResp) Reset()         { *m = GetBoxInfoResp{} }
func (m *GetBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfoResp) ProtoMessage()    {}
func (*GetBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{18}
}
func (m *GetBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfoResp.Unmarshal(m, b)
}
func (m *GetBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfoResp.Merge(dst, src)
}
func (m *GetBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfoResp.Size(m)
}
func (m *GetBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfoResp proto.InternalMessageInfo

func (m *GetBoxInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBoxInfoResp) GetBoxInfo() []*BoxInfo {
	if m != nil {
		return m.BoxInfo
	}
	return nil
}

func (m *GetBoxInfoResp) GetUserBoxid() uint32 {
	if m != nil {
		return m.UserBoxid
	}
	return 0
}

func (m *GetBoxInfoResp) GetMicTotal() uint32 {
	if m != nil {
		return m.MicTotal
	}
	return 0
}

func (m *GetBoxInfoResp) GetUserAudioBoxids() []uint32 {
	if m != nil {
		return m.UserAudioBoxids
	}
	return nil
}

func (m *GetBoxInfoResp) GetMainCommonBoxid() uint32 {
	if m != nil {
		return m.MainCommonBoxid
	}
	return 0
}

type GetBoxInfosByLimitReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Channelid            uint32       `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32       `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	LastTime             int64        `protobuf:"varint,4,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	GetCnt               int64        `protobuf:"varint,5,opt,name=get_cnt,json=getCnt,proto3" json:"get_cnt,omitempty"`
	BoxType              BoxType      `protobuf:"varint,6,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBoxInfosByLimitReq) Reset()         { *m = GetBoxInfosByLimitReq{} }
func (m *GetBoxInfosByLimitReq) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfosByLimitReq) ProtoMessage()    {}
func (*GetBoxInfosByLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{19}
}
func (m *GetBoxInfosByLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Unmarshal(m, b)
}
func (m *GetBoxInfosByLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfosByLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfosByLimitReq.Merge(dst, src)
}
func (m *GetBoxInfosByLimitReq) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Size(m)
}
func (m *GetBoxInfosByLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfosByLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfosByLimitReq proto.InternalMessageInfo

func (m *GetBoxInfosByLimitReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBoxInfosByLimitReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetLastTime() int64 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetGetCnt() int64 {
	if m != nil {
		return m.GetCnt
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type BoxUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Accunt               string   `protobuf:"bytes,2,opt,name=accunt,proto3" json:"accunt,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Gender               int32    `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxUserInfo) Reset()         { *m = BoxUserInfo{} }
func (m *BoxUserInfo) String() string { return proto.CompactTextString(m) }
func (*BoxUserInfo) ProtoMessage()    {}
func (*BoxUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{20}
}
func (m *BoxUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxUserInfo.Unmarshal(m, b)
}
func (m *BoxUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxUserInfo.Marshal(b, m, deterministic)
}
func (dst *BoxUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxUserInfo.Merge(dst, src)
}
func (m *BoxUserInfo) XXX_Size() int {
	return xxx_messageInfo_BoxUserInfo.Size(m)
}
func (m *BoxUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BoxUserInfo proto.InternalMessageInfo

func (m *BoxUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BoxUserInfo) GetAccunt() string {
	if m != nil {
		return m.Accunt
	}
	return ""
}

func (m *BoxUserInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *BoxUserInfo) GetGender() int32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

type GetBoxInfosByLimitResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	BoxUserInfos         []*BoxUserInfo `protobuf:"bytes,2,rep,name=box_user_infos,json=boxUserInfos,proto3" json:"box_user_infos,omitempty"`
	Channelid            uint32         `protobuf:"varint,3,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32         `protobuf:"varint,4,opt,name=boxid,proto3" json:"boxid,omitempty"`
	LastTime             int64          `protobuf:"varint,5,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetBoxInfosByLimitResp) Reset()         { *m = GetBoxInfosByLimitResp{} }
func (m *GetBoxInfosByLimitResp) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfosByLimitResp) ProtoMessage()    {}
func (*GetBoxInfosByLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{21}
}
func (m *GetBoxInfosByLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Unmarshal(m, b)
}
func (m *GetBoxInfosByLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfosByLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfosByLimitResp.Merge(dst, src)
}
func (m *GetBoxInfosByLimitResp) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Size(m)
}
func (m *GetBoxInfosByLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfosByLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfosByLimitResp proto.InternalMessageInfo

func (m *GetBoxInfosByLimitResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBoxInfosByLimitResp) GetBoxUserInfos() []*BoxUserInfo {
	if m != nil {
		return m.BoxUserInfos
	}
	return nil
}

func (m *GetBoxInfosByLimitResp) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxInfosByLimitResp) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *GetBoxInfosByLimitResp) GetLastTime() int64 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

type GetChannelUserRoleListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32     `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	RoleType             RoleType     `protobuf:"varint,4,opt,name=role_type,json=roleType,proto3,enum=ga.channel_roleplay_logic.RoleType" json:"role_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelUserRoleListReq) Reset()         { *m = GetChannelUserRoleListReq{} }
func (m *GetChannelUserRoleListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelUserRoleListReq) ProtoMessage()    {}
func (*GetChannelUserRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{22}
}
func (m *GetChannelUserRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelUserRoleListReq.Unmarshal(m, b)
}
func (m *GetChannelUserRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelUserRoleListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelUserRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelUserRoleListReq.Merge(dst, src)
}
func (m *GetChannelUserRoleListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelUserRoleListReq.Size(m)
}
func (m *GetChannelUserRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelUserRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelUserRoleListReq proto.InternalMessageInfo

func (m *GetChannelUserRoleListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelUserRoleListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelUserRoleListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetChannelUserRoleListReq) GetRoleType() RoleType {
	if m != nil {
		return m.RoleType
	}
	return RoleType_RoleTypeNil
}

type GetChannelUserRoleListResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RoleList []*UserRole   `protobuf:"bytes,2,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	// 本人审核中的角色
	AuditRole            *UserRole `protobuf:"bytes,3,opt,name=audit_role,json=auditRole,proto3" json:"audit_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetChannelUserRoleListResp) Reset()         { *m = GetChannelUserRoleListResp{} }
func (m *GetChannelUserRoleListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelUserRoleListResp) ProtoMessage()    {}
func (*GetChannelUserRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{23}
}
func (m *GetChannelUserRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelUserRoleListResp.Unmarshal(m, b)
}
func (m *GetChannelUserRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelUserRoleListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelUserRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelUserRoleListResp.Merge(dst, src)
}
func (m *GetChannelUserRoleListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelUserRoleListResp.Size(m)
}
func (m *GetChannelUserRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelUserRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelUserRoleListResp proto.InternalMessageInfo

func (m *GetChannelUserRoleListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelUserRoleListResp) GetRoleList() []*UserRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

func (m *GetChannelUserRoleListResp) GetAuditRole() *UserRole {
	if m != nil {
		return m.AuditRole
	}
	return nil
}

type UpsertBoxInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Box                  *BoxInfo     `protobuf:"bytes,2,opt,name=box,proto3" json:"box,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpsertBoxInfoReq) Reset()         { *m = UpsertBoxInfoReq{} }
func (m *UpsertBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpsertBoxInfoReq) ProtoMessage()    {}
func (*UpsertBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{24}
}
func (m *UpsertBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBoxInfoReq.Unmarshal(m, b)
}
func (m *UpsertBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpsertBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBoxInfoReq.Merge(dst, src)
}
func (m *UpsertBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpsertBoxInfoReq.Size(m)
}
func (m *UpsertBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBoxInfoReq proto.InternalMessageInfo

func (m *UpsertBoxInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpsertBoxInfoReq) GetBox() *BoxInfo {
	if m != nil {
		return m.Box
	}
	return nil
}

type UpsertBoxInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Box                  *BoxInfo      `protobuf:"bytes,2,opt,name=box,proto3" json:"box,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpsertBoxInfoResp) Reset()         { *m = UpsertBoxInfoResp{} }
func (m *UpsertBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpsertBoxInfoResp) ProtoMessage()    {}
func (*UpsertBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{25}
}
func (m *UpsertBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBoxInfoResp.Unmarshal(m, b)
}
func (m *UpsertBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpsertBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBoxInfoResp.Merge(dst, src)
}
func (m *UpsertBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpsertBoxInfoResp.Size(m)
}
func (m *UpsertBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBoxInfoResp proto.InternalMessageInfo

func (m *UpsertBoxInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UpsertBoxInfoResp) GetBox() *BoxInfo {
	if m != nil {
		return m.Box
	}
	return nil
}

type DelBoxInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BoxId                uint32       `protobuf:"varint,3,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DelBoxInfoReq) Reset()         { *m = DelBoxInfoReq{} }
func (m *DelBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelBoxInfoReq) ProtoMessage()    {}
func (*DelBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{26}
}
func (m *DelBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBoxInfoReq.Unmarshal(m, b)
}
func (m *DelBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBoxInfoReq.Merge(dst, src)
}
func (m *DelBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelBoxInfoReq.Size(m)
}
func (m *DelBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBoxInfoReq proto.InternalMessageInfo

func (m *DelBoxInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DelBoxInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DelBoxInfoReq) GetBoxId() uint32 {
	if m != nil {
		return m.BoxId
	}
	return 0
}

type DelBoxInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DelBoxInfoResp) Reset()         { *m = DelBoxInfoResp{} }
func (m *DelBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelBoxInfoResp) ProtoMessage()    {}
func (*DelBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{27}
}
func (m *DelBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBoxInfoResp.Unmarshal(m, b)
}
func (m *DelBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBoxInfoResp.Merge(dst, src)
}
func (m *DelBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelBoxInfoResp.Size(m)
}
func (m *DelBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBoxInfoResp proto.InternalMessageInfo

func (m *DelBoxInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type OpenCommonMicReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BoxType              BoxType      `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	CommonBoxid          uint32       `protobuf:"varint,4,opt,name=common_boxid,json=commonBoxid,proto3" json:"common_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OpenCommonMicReq) Reset()         { *m = OpenCommonMicReq{} }
func (m *OpenCommonMicReq) String() string { return proto.CompactTextString(m) }
func (*OpenCommonMicReq) ProtoMessage()    {}
func (*OpenCommonMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{28}
}
func (m *OpenCommonMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenCommonMicReq.Unmarshal(m, b)
}
func (m *OpenCommonMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenCommonMicReq.Marshal(b, m, deterministic)
}
func (dst *OpenCommonMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenCommonMicReq.Merge(dst, src)
}
func (m *OpenCommonMicReq) XXX_Size() int {
	return xxx_messageInfo_OpenCommonMicReq.Size(m)
}
func (m *OpenCommonMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenCommonMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_OpenCommonMicReq proto.InternalMessageInfo

func (m *OpenCommonMicReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OpenCommonMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpenCommonMicReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *OpenCommonMicReq) GetCommonBoxid() uint32 {
	if m != nil {
		return m.CommonBoxid
	}
	return 0
}

type OpenCommonMicResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OpenCommonMicResp) Reset()         { *m = OpenCommonMicResp{} }
func (m *OpenCommonMicResp) String() string { return proto.CompactTextString(m) }
func (*OpenCommonMicResp) ProtoMessage()    {}
func (*OpenCommonMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{29}
}
func (m *OpenCommonMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenCommonMicResp.Unmarshal(m, b)
}
func (m *OpenCommonMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenCommonMicResp.Marshal(b, m, deterministic)
}
func (dst *OpenCommonMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenCommonMicResp.Merge(dst, src)
}
func (m *OpenCommonMicResp) XXX_Size() int {
	return xxx_messageInfo_OpenCommonMicResp.Size(m)
}
func (m *OpenCommonMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenCommonMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_OpenCommonMicResp proto.InternalMessageInfo

func (m *OpenCommonMicResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 广播
type OpenCommonMicBC struct {
	CommonBoxid          uint32   `protobuf:"varint,1,opt,name=common_boxid,json=commonBoxid,proto3" json:"common_boxid,omitempty"`
	OpeUid               uint32   `protobuf:"varint,2,opt,name=ope_uid,json=opeUid,proto3" json:"ope_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UserName             string   `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	NickName             string   `protobuf:"bytes,5,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Opts                 int64    `protobuf:"varint,6,opt,name=opts,proto3" json:"opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenCommonMicBC) Reset()         { *m = OpenCommonMicBC{} }
func (m *OpenCommonMicBC) String() string { return proto.CompactTextString(m) }
func (*OpenCommonMicBC) ProtoMessage()    {}
func (*OpenCommonMicBC) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{30}
}
func (m *OpenCommonMicBC) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenCommonMicBC.Unmarshal(m, b)
}
func (m *OpenCommonMicBC) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenCommonMicBC.Marshal(b, m, deterministic)
}
func (dst *OpenCommonMicBC) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenCommonMicBC.Merge(dst, src)
}
func (m *OpenCommonMicBC) XXX_Size() int {
	return xxx_messageInfo_OpenCommonMicBC.Size(m)
}
func (m *OpenCommonMicBC) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenCommonMicBC.DiscardUnknown(m)
}

var xxx_messageInfo_OpenCommonMicBC proto.InternalMessageInfo

func (m *OpenCommonMicBC) GetCommonBoxid() uint32 {
	if m != nil {
		return m.CommonBoxid
	}
	return 0
}

func (m *OpenCommonMicBC) GetOpeUid() uint32 {
	if m != nil {
		return m.OpeUid
	}
	return 0
}

func (m *OpenCommonMicBC) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpenCommonMicBC) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *OpenCommonMicBC) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *OpenCommonMicBC) GetOpts() int64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

// 子频道信息变更推送
type BoxChangeNotify struct {
	Op  BoxChangeNotify_Op `protobuf:"varint,1,opt,name=op,proto3,enum=ga.channel_roleplay_logic.BoxChangeNotify_Op" json:"op,omitempty"`
	Box *BoxInfo           `protobuf:"bytes,2,opt,name=box,proto3" json:"box,omitempty"`
	// 修改时间戳(毫秒)
	ChangedAt uint64 `protobuf:"varint,3,opt,name=changed_at,json=changedAt,proto3" json:"changed_at,omitempty"`
	// 子频道创建者
	BoxUid uint32 `protobuf:"varint,4,opt,name=box_uid,json=boxUid,proto3" json:"box_uid,omitempty"`
	// 审核结果, 仅对 uid = box_uid && op = [OpCreate, OpUpdateName] 有效
	Result BoxChangeNotify_Result `protobuf:"varint,5,opt,name=result,proto3,enum=ga.channel_roleplay_logic.BoxChangeNotify_Result" json:"result,omitempty"`
	// 房间内是否还有子频道
	ExistsBox            bool     `protobuf:"varint,6,opt,name=exists_box,json=existsBox,proto3" json:"exists_box,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxChangeNotify) Reset()         { *m = BoxChangeNotify{} }
func (m *BoxChangeNotify) String() string { return proto.CompactTextString(m) }
func (*BoxChangeNotify) ProtoMessage()    {}
func (*BoxChangeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{31}
}
func (m *BoxChangeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxChangeNotify.Unmarshal(m, b)
}
func (m *BoxChangeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxChangeNotify.Marshal(b, m, deterministic)
}
func (dst *BoxChangeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxChangeNotify.Merge(dst, src)
}
func (m *BoxChangeNotify) XXX_Size() int {
	return xxx_messageInfo_BoxChangeNotify.Size(m)
}
func (m *BoxChangeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxChangeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_BoxChangeNotify proto.InternalMessageInfo

func (m *BoxChangeNotify) GetOp() BoxChangeNotify_Op {
	if m != nil {
		return m.Op
	}
	return BoxChangeNotify_OpNone
}

func (m *BoxChangeNotify) GetBox() *BoxInfo {
	if m != nil {
		return m.Box
	}
	return nil
}

func (m *BoxChangeNotify) GetChangedAt() uint64 {
	if m != nil {
		return m.ChangedAt
	}
	return 0
}

func (m *BoxChangeNotify) GetBoxUid() uint32 {
	if m != nil {
		return m.BoxUid
	}
	return 0
}

func (m *BoxChangeNotify) GetResult() BoxChangeNotify_Result {
	if m != nil {
		return m.Result
	}
	return BoxChangeNotify_ResultNone
}

func (m *BoxChangeNotify) GetExistsBox() bool {
	if m != nil {
		return m.ExistsBox
	}
	return false
}

// 获取团战房主房间用户列表
type GetMainChannelUserListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMainChannelUserListReq) Reset()         { *m = GetMainChannelUserListReq{} }
func (m *GetMainChannelUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetMainChannelUserListReq) ProtoMessage()    {}
func (*GetMainChannelUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{32}
}
func (m *GetMainChannelUserListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMainChannelUserListReq.Unmarshal(m, b)
}
func (m *GetMainChannelUserListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMainChannelUserListReq.Marshal(b, m, deterministic)
}
func (dst *GetMainChannelUserListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMainChannelUserListReq.Merge(dst, src)
}
func (m *GetMainChannelUserListReq) XXX_Size() int {
	return xxx_messageInfo_GetMainChannelUserListReq.Size(m)
}
func (m *GetMainChannelUserListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMainChannelUserListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMainChannelUserListReq proto.InternalMessageInfo

func (m *GetMainChannelUserListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMainChannelUserListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMainChannelUserListResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserInfos            []*BoxUserInfo `protobuf:"bytes,2,rep,name=user_infos,json=userInfos,proto3" json:"user_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetMainChannelUserListResp) Reset()         { *m = GetMainChannelUserListResp{} }
func (m *GetMainChannelUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetMainChannelUserListResp) ProtoMessage()    {}
func (*GetMainChannelUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7, []int{33}
}
func (m *GetMainChannelUserListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMainChannelUserListResp.Unmarshal(m, b)
}
func (m *GetMainChannelUserListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMainChannelUserListResp.Marshal(b, m, deterministic)
}
func (dst *GetMainChannelUserListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMainChannelUserListResp.Merge(dst, src)
}
func (m *GetMainChannelUserListResp) XXX_Size() int {
	return xxx_messageInfo_GetMainChannelUserListResp.Size(m)
}
func (m *GetMainChannelUserListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMainChannelUserListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMainChannelUserListResp proto.InternalMessageInfo

func (m *GetMainChannelUserListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMainChannelUserListResp) GetUserInfos() []*BoxUserInfo {
	if m != nil {
		return m.UserInfos
	}
	return nil
}

func init() {
	proto.RegisterType((*GetChannelHoldMicUserRoleListReq)(nil), "ga.channel_roleplay_logic.GetChannelHoldMicUserRoleListReq")
	proto.RegisterType((*UserRole)(nil), "ga.channel_roleplay_logic.UserRole")
	proto.RegisterType((*GetChannelHoldMicUserRoleListResp)(nil), "ga.channel_roleplay_logic.GetChannelHoldMicUserRoleListResp")
	proto.RegisterType((*SetMyChannelRoleReq)(nil), "ga.channel_roleplay_logic.SetMyChannelRoleReq")
	proto.RegisterType((*SetMyChannelRoleResp)(nil), "ga.channel_roleplay_logic.SetMyChannelRoleResp")
	proto.RegisterType((*ChannelUserRoleNotify)(nil), "ga.channel_roleplay_logic.ChannelUserRoleNotify")
	proto.RegisterType((*ChannelRoleChangeMsg)(nil), "ga.channel_roleplay_logic.ChannelRoleChangeMsg")
	proto.RegisterType((*EnterBoxReq)(nil), "ga.channel_roleplay_logic.EnterBoxReq")
	proto.RegisterType((*EnterBoxResp)(nil), "ga.channel_roleplay_logic.EnterBoxResp")
	proto.RegisterType((*ExitBoxReq)(nil), "ga.channel_roleplay_logic.ExitBoxReq")
	proto.RegisterType((*ExitBoxResp)(nil), "ga.channel_roleplay_logic.ExitBoxResp")
	proto.RegisterType((*OpeBoxInfo)(nil), "ga.channel_roleplay_logic.OpeBoxInfo")
	proto.RegisterType((*OpeBoxNotify)(nil), "ga.channel_roleplay_logic.OpeBoxNotify")
	proto.RegisterType((*ApplyNotify)(nil), "ga.channel_roleplay_logic.ApplyNotify")
	proto.RegisterType((*HandleApplyBoxReq)(nil), "ga.channel_roleplay_logic.HandleApplyBoxReq")
	proto.RegisterType((*HandleApplyBoxResp)(nil), "ga.channel_roleplay_logic.HandleApplyBoxResp")
	proto.RegisterType((*BoxInfo)(nil), "ga.channel_roleplay_logic.BoxInfo")
	proto.RegisterType((*GetBoxInfoReq)(nil), "ga.channel_roleplay_logic.GetBoxInfoReq")
	proto.RegisterType((*GetBoxInfoResp)(nil), "ga.channel_roleplay_logic.GetBoxInfoResp")
	proto.RegisterType((*GetBoxInfosByLimitReq)(nil), "ga.channel_roleplay_logic.GetBoxInfosByLimitReq")
	proto.RegisterType((*BoxUserInfo)(nil), "ga.channel_roleplay_logic.BoxUserInfo")
	proto.RegisterType((*GetBoxInfosByLimitResp)(nil), "ga.channel_roleplay_logic.GetBoxInfosByLimitResp")
	proto.RegisterType((*GetChannelUserRoleListReq)(nil), "ga.channel_roleplay_logic.GetChannelUserRoleListReq")
	proto.RegisterType((*GetChannelUserRoleListResp)(nil), "ga.channel_roleplay_logic.GetChannelUserRoleListResp")
	proto.RegisterType((*UpsertBoxInfoReq)(nil), "ga.channel_roleplay_logic.UpsertBoxInfoReq")
	proto.RegisterType((*UpsertBoxInfoResp)(nil), "ga.channel_roleplay_logic.UpsertBoxInfoResp")
	proto.RegisterType((*DelBoxInfoReq)(nil), "ga.channel_roleplay_logic.DelBoxInfoReq")
	proto.RegisterType((*DelBoxInfoResp)(nil), "ga.channel_roleplay_logic.DelBoxInfoResp")
	proto.RegisterType((*OpenCommonMicReq)(nil), "ga.channel_roleplay_logic.OpenCommonMicReq")
	proto.RegisterType((*OpenCommonMicResp)(nil), "ga.channel_roleplay_logic.OpenCommonMicResp")
	proto.RegisterType((*OpenCommonMicBC)(nil), "ga.channel_roleplay_logic.OpenCommonMicBC")
	proto.RegisterType((*BoxChangeNotify)(nil), "ga.channel_roleplay_logic.BoxChangeNotify")
	proto.RegisterType((*GetMainChannelUserListReq)(nil), "ga.channel_roleplay_logic.GetMainChannelUserListReq")
	proto.RegisterType((*GetMainChannelUserListResp)(nil), "ga.channel_roleplay_logic.GetMainChannelUserListResp")
	proto.RegisterEnum("ga.channel_roleplay_logic.RoleType", RoleType_name, RoleType_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.BoxType", BoxType_name, BoxType_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.BoxNotifyType", BoxNotifyType_name, BoxNotifyType_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.BoxNotifyUserOpType", BoxNotifyUserOpType_name, BoxNotifyUserOpType_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.ApplyBoxType", ApplyBoxType_name, ApplyBoxType_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.AudioBoxId", AudioBoxId_name, AudioBoxId_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.ChannelUserRoleNotify_AuditResult", ChannelUserRoleNotify_AuditResult_name, ChannelUserRoleNotify_AuditResult_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.BoxChangeNotify_Op", BoxChangeNotify_Op_name, BoxChangeNotify_Op_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.BoxChangeNotify_Result", BoxChangeNotify_Result_name, BoxChangeNotify_Result_value)
}

func init() {
	proto.RegisterFile("channel_roleplay_logic/channel-roleplay-logic_.proto", fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7)
}

var fileDescriptor_channel_roleplay_logic__e0346dd463fc3aa7 = []byte{
	// 1856 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x59, 0x4f, 0x73, 0x23, 0x39,
	0x15, 0x9f, 0x76, 0xdb, 0x8e, 0xfd, 0x6c, 0x27, 0x1d, 0xe5, 0xcf, 0x78, 0x66, 0xa1, 0xc8, 0x36,
	0xc5, 0x10, 0x52, 0x35, 0x1e, 0x98, 0x5d, 0x8a, 0x2a, 0x60, 0x29, 0xec, 0xcc, 0xd4, 0x6c, 0x8a,
	0x71, 0xbc, 0xdb, 0x9b, 0xa1, 0x28, 0x2e, 0xae, 0x76, 0xb7, 0xc6, 0x2b, 0x68, 0xb7, 0x34, 0x6e,
	0x35, 0xd8, 0x67, 0x2e, 0x9c, 0xb6, 0xf6, 0xc0, 0x99, 0x6f, 0xc1, 0x85, 0x3d, 0x70, 0x85, 0x03,
	0x55, 0x7c, 0x06, 0x4e, 0x7c, 0x06, 0x4e, 0x94, 0x9e, 0xd4, 0xed, 0xb6, 0x93, 0x78, 0xe2, 0x6c,
	0x76, 0x0f, 0xdc, 0xa4, 0xa7, 0x96, 0xde, 0x7b, 0x3f, 0xfd, 0xde, 0x7b, 0x92, 0x1a, 0xde, 0x0f,
	0x3e, 0xf5, 0xe3, 0x98, 0x46, 0xc3, 0x29, 0x8f, 0xa8, 0x88, 0xfc, 0xf9, 0x30, 0xe2, 0x63, 0x16,
	0x3c, 0x31, 0xe2, 0xc7, 0x99, 0xf8, 0x31, 0x8a, 0x87, 0x1d, 0x31, 0xe5, 0x92, 0x93, 0x07, 0x63,
	0xbf, 0x73, 0xf5, 0xc4, 0x87, 0xad, 0xb1, 0x3f, 0x1c, 0xf9, 0x09, 0xd5, 0x5f, 0xba, 0x0c, 0x8e,
	0x5e, 0x50, 0x79, 0xaa, 0xbf, 0xfd, 0x90, 0x47, 0x61, 0x9f, 0x05, 0xaf, 0x12, 0x3a, 0xf5, 0x78,
	0x44, 0x5f, 0xb2, 0x44, 0x7a, 0xf4, 0x0d, 0x79, 0x04, 0x35, 0x35, 0x63, 0x38, 0xa5, 0x6f, 0xda,
	0xd6, 0x91, 0x75, 0xdc, 0x78, 0xda, 0xe8, 0x8c, 0xfd, 0x4e, 0xcf, 0x4f, 0xa8, 0x47, 0xdf, 0x78,
	0x5b, 0x23, 0xdd, 0x20, 0xdf, 0x04, 0xc8, 0x94, 0xb2, 0xb0, 0x5d, 0x3a, 0xb2, 0x8e, 0x5b, 0x5e,
	0xdd, 0x48, 0xce, 0x42, 0xf7, 0x57, 0x50, 0xcb, 0x56, 0x26, 0x0e, 0xd8, 0x29, 0x0b, 0x71, 0xb5,
	0x96, 0xa7, 0x9a, 0xe4, 0x1d, 0xa8, 0x2b, 0x4b, 0x87, 0xb1, 0x3f, 0xa1, 0x38, 0xb7, 0xee, 0xd5,
	0x94, 0xe0, 0xdc, 0x9f, 0x50, 0xb5, 0x72, 0x2a, 0x42, 0x5f, 0xd2, 0x70, 0xe8, 0xcb, 0xb6, 0xad,
	0x57, 0x36, 0x92, 0xae, 0x74, 0x3f, 0xb7, 0xe0, 0xdd, 0xb7, 0x78, 0x91, 0x08, 0xf2, 0x3d, 0xa8,
	0x1b, 0x37, 0x12, 0x61, 0xfc, 0x68, 0x2e, 0xfc, 0x48, 0x84, 0x57, 0x1b, 0x99, 0x16, 0xf9, 0xb9,
	0x31, 0x26, 0x62, 0x89, 0x6c, 0x97, 0x8e, 0xec, 0xe3, 0xc6, 0xd3, 0x6f, 0x77, 0xae, 0xc5, 0xb4,
	0x93, 0xa9, 0xd2, 0x16, 0x2b, 0x85, 0xee, 0x17, 0x16, 0xec, 0x7d, 0x42, 0x65, 0x7f, 0x6e, 0x8c,
	0xc2, 0xe1, 0x3b, 0xc3, 0x72, 0x19, 0x2d, 0x7b, 0x05, 0xad, 0xcc, 0x7a, 0x39, 0x17, 0xb4, 0x5d,
	0x3e, 0xb2, 0x8e, 0xb7, 0xd7, 0x5a, 0xaf, 0x4c, 0xbb, 0x98, 0x0b, 0x63, 0xbd, 0x6a, 0xb9, 0x5d,
	0xd8, 0xbf, 0x6c, 0xfc, 0x46, 0x10, 0xba, 0x9f, 0x97, 0xe0, 0xc0, 0x4c, 0xcf, 0xe0, 0x39, 0xe7,
	0x92, 0xbd, 0x9e, 0x93, 0x1f, 0x41, 0x59, 0x29, 0x32, 0xf3, 0x6f, 0x84, 0x2b, 0x4e, 0x20, 0x17,
	0x50, 0x9d, 0xd2, 0x24, 0x8d, 0x24, 0xe2, 0xb1, 0xfd, 0xf4, 0xa7, 0x6b, 0xa6, 0x5e, 0xa9, 0xba,
	0xd3, 0x4d, 0x43, 0xa6, 0xc8, 0x90, 0x46, 0xd2, 0x33, 0x6b, 0xb9, 0x9f, 0x42, 0xa3, 0x20, 0x26,
	0x87, 0x40, 0x0a, 0xdd, 0x67, 0xf4, 0xb5, 0x9f, 0x46, 0xd2, 0xb9, 0x47, 0xf6, 0x60, 0xa7, 0x20,
	0xff, 0xc8, 0x4f, 0x12, 0xc7, 0x22, 0x07, 0xb0, 0x5b, 0x5c, 0x92, 0xfe, 0x86, 0x06, 0xd2, 0x29,
	0x5d, 0x12, 0xff, 0x8e, 0xd1, 0xdf, 0x3b, 0xb6, 0xfb, 0x02, 0xf6, 0x0b, 0x80, 0xaa, 0xe6, 0x98,
	0xf6, 0x93, 0x31, 0x21, 0x50, 0x96, 0x74, 0x26, 0x11, 0x90, 0xba, 0x87, 0xed, 0xb5, 0xe1, 0xe0,
	0xfe, 0xc3, 0x82, 0xc6, 0xf3, 0x58, 0xd2, 0x69, 0x8f, 0xcf, 0x36, 0x21, 0xd5, 0x37, 0x20, 0xa3,
	0xd0, 0x25, 0x4e, 0xb1, 0x90, 0xec, 0x43, 0x65, 0xc4, 0x67, 0x2c, 0x34, 0xf1, 0xa5, 0x3b, 0xe4,
	0x03, 0xa8, 0x8d, 0xf8, 0xac, 0xc8, 0x25, 0x77, 0x0d, 0xec, 0x3d, 0x3e, 0x43, 0x2a, 0x6d, 0x8d,
	0x74, 0x83, 0x7c, 0x0b, 0x1a, 0x7e, 0x1a, 0x32, 0x3e, 0xd4, 0x4b, 0x57, 0x70, 0x69, 0x40, 0x51,
	0x4f, 0x49, 0x5c, 0x09, 0xcd, 0x85, 0x2b, 0x9b, 0x45, 0xe9, 0x01, 0x54, 0xb9, 0x90, 0x43, 0x99,
	0xa0, 0x2f, 0x65, 0xaf, 0xc2, 0x85, 0xbc, 0x48, 0x88, 0x0b, 0x2d, 0x96, 0x0c, 0x63, 0xaa, 0x92,
	0x85, 0x10, 0xd1, 0x1c, 0xfd, 0xa9, 0x79, 0x0d, 0x96, 0x9c, 0x53, 0x1a, 0x76, 0x95, 0xc8, 0xfd,
	0xbb, 0x05, 0xf0, 0x7c, 0xc6, 0xe4, 0xff, 0x01, 0x80, 0x03, 0x68, 0xe4, 0x9e, 0xdc, 0x05, 0x7e,
	0xee, 0x9f, 0x4b, 0x00, 0x03, 0x41, 0x7b, 0x7c, 0x76, 0x16, 0xbf, 0xe6, 0xe4, 0x0c, 0x6a, 0x5c,
	0x98, 0x64, 0x62, 0xa1, 0xfd, 0x9d, 0xf5, 0xf6, 0xeb, 0x58, 0x53, 0x91, 0x37, 0x10, 0xda, 0x17,
	0x2e, 0x30, 0xad, 0x2c, 0x00, 0x2a, 0x15, 0x01, 0x32, 0xb5, 0xc0, 0x5e, 0xd4, 0x82, 0xfb, 0xa0,
	0xdc, 0x1f, 0x06, 0xb1, 0x44, 0xc4, 0x5a, 0x5e, 0x75, 0xc4, 0x67, 0xa7, 0xb1, 0x24, 0x67, 0xd0,
	0x88, 0x71, 0x75, 0x6d, 0x4e, 0x05, 0xcd, 0x39, 0xbe, 0x89, 0x39, 0x68, 0x08, 0xc4, 0x79, 0xbb,
	0xe0, 0x7c, 0x75, 0x85, 0x3c, 0x39, 0xdc, 0x43, 0x16, 0x26, 0xed, 0xad, 0x23, 0xfb, 0xb8, 0xe5,
	0x35, 0x32, 0xc0, 0xcf, 0xc2, 0xc4, 0xfd, 0x63, 0x09, 0x9a, 0x1a, 0x20, 0x93, 0xd1, 0x5e, 0x40,
	0x53, 0x41, 0x84, 0x53, 0xe2, 0xd7, 0xbc, 0x6d, 0x61, 0xc5, 0xf8, 0xce, 0x1a, 0xbb, 0x16, 0xf8,
	0x7a, 0xc0, 0x17, 0x58, 0xbf, 0x25, 0xeb, 0x3b, 0x60, 0x4f, 0x92, 0xb1, 0xc9, 0xf7, 0xaa, 0xb9,
	0x9c, 0x26, 0xca, 0x2b, 0x75, 0xc0, 0x85, 0x96, 0x32, 0x6b, 0xf1, 0x41, 0x05, 0x3f, 0x68, 0x70,
	0x41, 0xbd, 0xec, 0x9b, 0x22, 0x3b, 0xab, 0x1b, 0xb3, 0xd3, 0xfd, 0x83, 0x05, 0x0d, 0x8c, 0x28,
	0x83, 0xc4, 0xb2, 0x03, 0xd6, 0xaa, 0x03, 0x37, 0x25, 0x80, 0x71, 0xb4, 0x7c, 0x8d, 0xa3, 0x95,
	0x95, 0x7c, 0xf8, 0x2f, 0x0b, 0x76, 0x3f, 0xf4, 0xe3, 0x30, 0xa2, 0x68, 0xcb, 0xd7, 0x10, 0xd4,
	0x7d, 0xd8, 0xc6, 0xdc, 0x32, 0x5c, 0x09, 0xed, 0xef, 0xae, 0x01, 0x2f, 0xb3, 0x0d, 0x11, 0x6c,
	0xfa, 0x85, 0x5e, 0x86, 0x40, 0x25, 0x47, 0xc0, 0xfd, 0x18, 0xc8, 0xaa, 0x47, 0x9b, 0x05, 0xb7,
	0x81, 0xb0, 0x94, 0x43, 0xe8, 0xfe, 0xd7, 0x82, 0xad, 0x8c, 0x68, 0x4b, 0x3e, 0x5b, 0xd7, 0xfa,
	0xbc, 0xb4, 0x4d, 0x47, 0xd0, 0x54, 0xde, 0xa6, 0x09, 0x9d, 0x62, 0x68, 0x6a, 0x40, 0x60, 0xc4,
	0x67, 0x2a, 0xd6, 0x55, 0x78, 0x3e, 0xd0, 0x64, 0x2a, 0x90, 0x51, 0x11, 0xe5, 0x12, 0xcf, 0x2a,
	0x9b, 0x67, 0xc1, 0xfb, 0xb0, 0x35, 0x61, 0xc1, 0x30, 0xf0, 0x05, 0xb2, 0xb4, 0xe5, 0x55, 0x27,
	0x2c, 0x38, 0xf5, 0x05, 0x79, 0x04, 0x3b, 0x22, 0x1d, 0x45, 0x2c, 0x18, 0xaa, 0x71, 0x3c, 0xaf,
	0xe9, 0x88, 0x6d, 0x69, 0x71, 0x9f, 0x05, 0x78, 0x1e, 0xfb, 0x93, 0x05, 0xad, 0x17, 0x54, 0x66,
	0x41, 0x77, 0x67, 0xf4, 0x28, 0xfa, 0x65, 0x6f, 0x1e, 0x3f, 0x9f, 0x95, 0x60, 0xbb, 0x68, 0xd6,
	0x66, 0x7b, 0x6c, 0x94, 0x63, 0xce, 0xd1, 0xa7, 0xd4, 0xb7, 0x28, 0x47, 0x25, 0x4a, 0x79, 0x96,
	0x6d, 0x70, 0x33, 0x8b, 0xfc, 0xae, 0x2b, 0x09, 0x16, 0x16, 0x15, 0x72, 0x0a, 0x53, 0xc9, 0xa5,
	0x1f, 0x99, 0x3c, 0x5c, 0x9b, 0xb0, 0xe0, 0x42, 0xf5, 0xc9, 0x09, 0xec, 0xe2, 0xdc, 0x42, 0x6d,
	0x4a, 0xda, 0x15, 0x44, 0x7e, 0x47, 0x0d, 0x74, 0xf3, 0x02, 0x95, 0xa8, 0x6f, 0x27, 0x3e, 0x8b,
	0x87, 0x01, 0x9f, 0x4c, 0x78, 0x6c, 0xd4, 0xe9, 0x6d, 0xdc, 0x51, 0x03, 0xa7, 0x28, 0xd7, 0xd5,
	0xec, 0x3f, 0x16, 0x1c, 0x2c, 0x00, 0x49, 0x7a, 0xf3, 0x97, 0x6c, 0xc2, 0xe4, 0x57, 0x1d, 0xce,
	0xef, 0x40, 0x3d, 0xf2, 0x13, 0x39, 0x94, 0xcc, 0x30, 0xd7, 0xf6, 0x6a, 0x4a, 0x70, 0xc1, 0x26,
	0xc8, 0xbd, 0x31, 0x95, 0x48, 0xf9, 0x0a, 0x0e, 0x55, 0xc7, 0x54, 0x2a, 0xba, 0x7f, 0xc9, 0xdc,
	0x19, 0x41, 0xa3, 0xa7, 0x63, 0x07, 0x77, 0xe3, 0xf2, 0x95, 0xe8, 0x10, 0xaa, 0x7e, 0x10, 0xa4,
	0xb1, 0x34, 0x51, 0x6c, 0x7a, 0xca, 0xda, 0x98, 0x05, 0xbf, 0x5d, 0x3a, 0xfc, 0x2b, 0x01, 0x06,
	0xda, 0x21, 0x54, 0xc7, 0x34, 0x0e, 0xe9, 0x14, 0xfd, 0xa8, 0x78, 0xa6, 0xe7, 0xfe, 0xdb, 0x82,
	0xc3, 0xab, 0x80, 0xdd, 0x8c, 0x71, 0x2f, 0x61, 0x3b, 0xcf, 0x01, 0x8a, 0x76, 0x89, 0xe1, 0xdd,
	0xa3, 0xf5, 0x8e, 0x67, 0x4e, 0x7a, 0xcd, 0xd1, 0xa2, 0x93, 0x2c, 0x6f, 0x95, 0x7d, 0xed, 0x56,
	0x95, 0xaf, 0xdd, 0xaa, 0xca, 0xf2, 0x56, 0xb9, 0x7f, 0xb3, 0xe0, 0xc1, 0xe2, 0x22, 0xf8, 0xd5,
	0xdc, 0x63, 0x55, 0x96, 0x4b, 0x59, 0xa8, 0x73, 0x8d, 0x8d, 0x8c, 0xdf, 0x4a, 0x59, 0xa8, 0x94,
	0xdc, 0xc1, 0xcd, 0xeb, 0x9f, 0x16, 0x3c, 0xbc, 0xce, 0x83, 0xaf, 0xf9, 0x0e, 0x4b, 0x7a, 0x80,
	0xe7, 0x4c, 0x89, 0x9f, 0xe2, 0xfe, 0xdc, 0x70, 0x89, 0x3a, 0x4e, 0x53, 0x4d, 0x57, 0x80, 0xf3,
	0x4a, 0x24, 0x74, 0x7a, 0x9b, 0xcc, 0xfb, 0x3e, 0xd8, 0x23, 0x3e, 0xc3, 0x0d, 0xb8, 0x59, 0x66,
	0x53, 0x9f, 0xbb, 0x12, 0x76, 0x57, 0x34, 0x6e, 0x86, 0xdb, 0xed, 0xb4, 0x4e, 0xa0, 0xf5, 0x8c,
	0x46, 0xb7, 0x70, 0xf2, 0x2d, 0x64, 0x3b, 0x80, 0xaa, 0x3e, 0x89, 0x16, 0x12, 0xd6, 0x59, 0xe8,
	0xfe, 0x04, 0xb6, 0x8b, 0xea, 0x36, 0xbb, 0x9a, 0x7f, 0x61, 0x81, 0x33, 0x10, 0xd4, 0xe4, 0xdd,
	0x3e, 0x0b, 0xee, 0xd0, 0xde, 0x2f, 0x57, 0x0f, 0xc9, 0xbb, 0xd0, 0x5c, 0xaa, 0x12, 0x3a, 0xf4,
	0x1b, 0x41, 0xa1, 0x42, 0xfc, 0x0c, 0x76, 0x57, 0x8c, 0xdf, 0xcc, 0xfb, 0xbf, 0x5a, 0xb0, 0xb3,
	0xb4, 0x40, 0xef, 0xf4, 0x92, 0x5a, 0xeb, 0x92, 0x5a, 0x55, 0x05, 0xd4, 0x61, 0x3a, 0xcd, 0x9d,
	0xae, 0x72, 0x41, 0x5f, 0xb1, 0x70, 0x05, 0x10, 0xfb, 0x8a, 0x97, 0x1a, 0xcc, 0x96, 0xc5, 0x13,
	0xba, 0x12, 0x60, 0xb2, 0x5e, 0xca, 0xe4, 0x95, 0x95, 0x4c, 0x4e, 0xa0, 0xcc, 0x85, 0xb9, 0x9f,
	0xd8, 0x1e, 0xb6, 0xdd, 0xbf, 0xd8, 0xb0, 0xd3, 0xe3, 0x33, 0xfd, 0x76, 0x60, 0xce, 0xdc, 0x1f,
	0x40, 0x89, 0x0b, 0x73, 0x35, 0x7b, 0xbc, 0x1e, 0xec, 0xe2, 0xbc, 0xce, 0x40, 0x78, 0x25, 0x7e,
	0x4b, 0xbe, 0x67, 0x5e, 0x8f, 0x17, 0x2f, 0x72, 0x65, 0xed, 0xf5, 0x98, 0x86, 0x5d, 0x99, 0xdd,
	0xe0, 0xd2, 0x7c, 0x0b, 0x15, 0x8b, 0x15, 0x5a, 0x67, 0xf9, 0x1b, 0x8e, 0x3e, 0x05, 0xfe, 0x60,
	0x03, 0x83, 0x97, 0x1f, 0x6e, 0x94, 0x09, 0x74, 0xc6, 0x12, 0x99, 0xa8, 0x4d, 0x43, 0x94, 0x6a,
	0x5e, 0x5d, 0x4b, 0x7a, 0x7c, 0xe6, 0x0e, 0xa0, 0x34, 0x10, 0x04, 0xa0, 0x3a, 0x10, 0xe7, 0x3c,
	0xa6, 0xce, 0x3d, 0xd2, 0x84, 0xda, 0x40, 0x9c, 0x4e, 0xa9, 0x2f, 0xa9, 0x63, 0xe9, 0xde, 0x33,
	0x1a, 0x51, 0x49, 0x9d, 0x12, 0xd9, 0x81, 0xc6, 0x40, 0xbc, 0xc2, 0x17, 0xc5, 0x3e, 0x0b, 0x1c,
	0x9b, 0x38, 0xea, 0x8e, 0xa7, 0x05, 0x6a, 0x37, 0x9c, 0xb2, 0xfb, 0x63, 0xa8, 0x9a, 0x37, 0xa2,
	0x6d, 0x00, 0xdd, 0x32, 0x0b, 0xe7, 0x7d, 0xf3, 0x2c, 0xe4, 0x40, 0x73, 0xf9, 0x45, 0xc8, 0x1d,
	0x61, 0x5d, 0xea, 0xab, 0xc3, 0xce, 0x22, 0xb3, 0xdf, 0xf1, 0xfb, 0xea, 0x67, 0xba, 0x74, 0x5c,
	0xa9, 0x64, 0xb3, 0x14, 0xf8, 0xdc, 0x1c, 0x0c, 0x6f, 0x53, 0xe1, 0x91, 0xed, 0x58, 0xde, 0x4f,
	0x4e, 0xa1, 0x96, 0x55, 0x38, 0x85, 0x6f, 0xd6, 0x3e, 0x67, 0x91, 0x73, 0x8f, 0xec, 0x83, 0x93,
	0x97, 0x3f, 0x1e, 0xd1, 0x8f, 0x22, 0x7f, 0x6e, 0x90, 0x33, 0xd2, 0x5f, 0xd2, 0x44, 0x3a, 0xa5,
	0x93, 0xef, 0xe3, 0xa5, 0x05, 0xd7, 0xd8, 0x43, 0xee, 0x67, 0x33, 0x94, 0x7e, 0xe7, 0x9e, 0x9a,
	0x61, 0x84, 0x7d, 0x1a, 0x51, 0xea, 0x58, 0x27, 0x1f, 0x43, 0x6b, 0xe9, 0xda, 0xaf, 0xb6, 0x67,
	0xf1, 0x1a, 0xe1, 0xdc, 0x23, 0xf7, 0x61, 0x0f, 0x6f, 0x55, 0xdd, 0x20, 0xa0, 0x42, 0xaa, 0x21,
	0x1c, 0xb0, 0xf2, 0x01, 0xbd, 0x6d, 0xf9, 0x40, 0xe9, 0x64, 0x0a, 0x7b, 0x57, 0x3c, 0x6c, 0x90,
	0x16, 0xd4, 0xf1, 0xed, 0x6a, 0xc1, 0xaf, 0xec, 0x29, 0xcb, 0xb1, 0x48, 0x03, 0xb6, 0xcc, 0xbb,
	0x8c, 0x53, 0x22, 0x6d, 0xd8, 0xd7, 0x43, 0x53, 0xee, 0x87, 0x81, 0x9f, 0x48, 0x95, 0x77, 0xf8,
	0xcc, 0xb1, 0x95, 0x4e, 0xfc, 0x6c, 0x65, 0xa0, 0x7c, 0xd2, 0x83, 0x66, 0x77, 0xf9, 0x8e, 0xa8,
	0xfb, 0x8b, 0x27, 0xc9, 0x1d, 0x73, 0xf7, 0xd6, 0x7e, 0x38, 0x56, 0x2e, 0xc8, 0x68, 0x77, 0x92,
	0x02, 0x74, 0xf3, 0x87, 0x0b, 0x65, 0x2e, 0x36, 0x8c, 0xb9, 0x59, 0xf7, 0x17, 0x94, 0x0a, 0xc7,
	0x22, 0x0f, 0xe0, 0xe0, 0x93, 0x74, 0x64, 0x98, 0x93, 0x1f, 0xc9, 0xcf, 0x42, 0xa7, 0x44, 0x1e,
	0xc2, 0x61, 0x81, 0x55, 0xc5, 0x31, 0x9b, 0x10, 0xd8, 0x46, 0xdb, 0x4f, 0xfd, 0x44, 0x6a, 0x59,
	0xb9, 0x37, 0x80, 0x76, 0xc0, 0x27, 0x9d, 0x39, 0x9b, 0xf3, 0x54, 0xd1, 0x66, 0xc2, 0x43, 0x1a,
	0xe9, 0x1f, 0x0e, 0xbf, 0x7e, 0x6f, 0xcc, 0x23, 0x3f, 0x1e, 0x77, 0x7e, 0xf8, 0x54, 0xca, 0x4e,
	0xc0, 0x27, 0x4f, 0x50, 0x1c, 0xf0, 0xe8, 0x89, 0x2f, 0xc4, 0x35, 0xbf, 0x35, 0x46, 0x55, 0xfc,
	0xe8, 0xbd, 0xff, 0x05, 0x00, 0x00, 0xff, 0xff, 0x6c, 0x62, 0x48, 0x44, 0x0e, 0x19, 0x00, 0x00,
}
