// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-roleplay-logic_.proto

package channel_roleplay_logic // import "golang.52tt.com/protocol/app/channel-roleplay-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 角色类型
type RoleType int32

const (
	RoleType_RoleTypeNil      RoleType = 0
	RoleType_RoleTypeRolePlay RoleType = 1
	RoleType_RoleTypeVest     RoleType = 2
)

var RoleType_name = map[int32]string{
	0: "RoleTypeNil",
	1: "RoleTypeRolePlay",
	2: "RoleTypeVest",
}
var RoleType_value = map[string]int32{
	"RoleTypeNil":      0,
	"RoleTypeRolePlay": 1,
	"RoleTypeVest":     2,
}

func (x RoleType) String() string {
	return proto.EnumName(RoleType_name, int32(x))
}
func (RoleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{0}
}

// 包厢类型
type BoxType int32

const (
	BoxType_BoxTypeRoleplay BoxType = 0
	BoxType_BoxTypeMelee    BoxType = 1
)

var BoxType_name = map[int32]string{
	0: "BoxTypeRoleplay",
	1: "BoxTypeMelee",
}
var BoxType_value = map[string]int32{
	"BoxTypeRoleplay": 0,
	"BoxTypeMelee":    1,
}

func (x BoxType) String() string {
	return proto.EnumName(BoxType_name, int32(x))
}
func (BoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{1}
}

type BoxNotifyType int32

const (
	BoxNotifyType_UserOpType          BoxNotifyType = 0
	BoxNotifyType_ApplyAcceptUserType BoxNotifyType = 1
	BoxNotifyType_ApplyRejectUserType BoxNotifyType = 2
)

var BoxNotifyType_name = map[int32]string{
	0: "UserOpType",
	1: "ApplyAcceptUserType",
	2: "ApplyRejectUserType",
}
var BoxNotifyType_value = map[string]int32{
	"UserOpType":          0,
	"ApplyAcceptUserType": 1,
	"ApplyRejectUserType": 2,
}

func (x BoxNotifyType) String() string {
	return proto.EnumName(BoxNotifyType_name, int32(x))
}
func (BoxNotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{2}
}

type BoxNotifyUserOpType int32

const (
	BoxNotifyUserOpType_EnterNone            BoxNotifyUserOpType = 0
	BoxNotifyUserOpType_EnterBox             BoxNotifyUserOpType = 1
	BoxNotifyUserOpType_ExitBox              BoxNotifyUserOpType = 2
	BoxNotifyUserOpType_EnterBroadcastMicBox BoxNotifyUserOpType = 3
	BoxNotifyUserOpType_ExitBroadcastMicBox  BoxNotifyUserOpType = 4
)

var BoxNotifyUserOpType_name = map[int32]string{
	0: "EnterNone",
	1: "EnterBox",
	2: "ExitBox",
	3: "EnterBroadcastMicBox",
	4: "ExitBroadcastMicBox",
}
var BoxNotifyUserOpType_value = map[string]int32{
	"EnterNone":            0,
	"EnterBox":             1,
	"ExitBox":              2,
	"EnterBroadcastMicBox": 3,
	"ExitBroadcastMicBox":  4,
}

func (x BoxNotifyUserOpType) String() string {
	return proto.EnumName(BoxNotifyUserOpType_name, int32(x))
}
func (BoxNotifyUserOpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{3}
}

type ApplyBoxType int32

const (
	ApplyBoxType_ApplyDefault ApplyBoxType = 0
	ApplyBoxType_ApplyAccept  ApplyBoxType = 1
	ApplyBoxType_ApplyReject  ApplyBoxType = 2
)

var ApplyBoxType_name = map[int32]string{
	0: "ApplyDefault",
	1: "ApplyAccept",
	2: "ApplyReject",
}
var ApplyBoxType_value = map[string]int32{
	"ApplyDefault": 0,
	"ApplyAccept":  1,
	"ApplyReject":  2,
}

func (x ApplyBoxType) String() string {
	return proto.EnumName(ApplyBoxType_name, int32(x))
}
func (ApplyBoxType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{4}
}

type AudioBoxId int32

const (
	AudioBoxId_BoxIdNone              AudioBoxId = 0
	AudioBoxId_BoxIdKeep              AudioBoxId = 1
	AudioBoxId_SubChannelCommonBoxId  AudioBoxId = 2
	AudioBoxId_MainChannelCommonBoxId AudioBoxId = 3
	AudioBoxId_BroadCastBoxId         AudioBoxId = 4
)

var AudioBoxId_name = map[int32]string{
	0: "BoxIdNone",
	1: "BoxIdKeep",
	2: "SubChannelCommonBoxId",
	3: "MainChannelCommonBoxId",
	4: "BroadCastBoxId",
}
var AudioBoxId_value = map[string]int32{
	"BoxIdNone":              0,
	"BoxIdKeep":              1,
	"SubChannelCommonBoxId":  2,
	"MainChannelCommonBoxId": 3,
	"BroadCastBoxId":         4,
}

func (x AudioBoxId) String() string {
	return proto.EnumName(AudioBoxId_name, int32(x))
}
func (AudioBoxId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{5}
}

type ChannelUserRoleNotify_AuditResult int32

const (
	ChannelUserRoleNotify_AuditResultDefault ChannelUserRoleNotify_AuditResult = 0
	ChannelUserRoleNotify_AuditResultPass    ChannelUserRoleNotify_AuditResult = 1
	ChannelUserRoleNotify_AuditResultReject  ChannelUserRoleNotify_AuditResult = 2
	ChannelUserRoleNotify_AuditResultReview  ChannelUserRoleNotify_AuditResult = 3
)

var ChannelUserRoleNotify_AuditResult_name = map[int32]string{
	0: "AuditResultDefault",
	1: "AuditResultPass",
	2: "AuditResultReject",
	3: "AuditResultReview",
}
var ChannelUserRoleNotify_AuditResult_value = map[string]int32{
	"AuditResultDefault": 0,
	"AuditResultPass":    1,
	"AuditResultReject":  2,
	"AuditResultReview":  3,
}

func (x ChannelUserRoleNotify_AuditResult) String() string {
	return proto.EnumName(ChannelUserRoleNotify_AuditResult_name, int32(x))
}
func (ChannelUserRoleNotify_AuditResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{5, 0}
}

type BoxChangeNotify_Op int32

const (
	BoxChangeNotify_OpNone       BoxChangeNotify_Op = 0
	BoxChangeNotify_OpCreate     BoxChangeNotify_Op = 1
	BoxChangeNotify_OpDelete     BoxChangeNotify_Op = 2
	BoxChangeNotify_OpUpdateMic  BoxChangeNotify_Op = 3
	BoxChangeNotify_OpUpdateName BoxChangeNotify_Op = 4
)

var BoxChangeNotify_Op_name = map[int32]string{
	0: "OpNone",
	1: "OpCreate",
	2: "OpDelete",
	3: "OpUpdateMic",
	4: "OpUpdateName",
}
var BoxChangeNotify_Op_value = map[string]int32{
	"OpNone":       0,
	"OpCreate":     1,
	"OpDelete":     2,
	"OpUpdateMic":  3,
	"OpUpdateName": 4,
}

func (x BoxChangeNotify_Op) String() string {
	return proto.EnumName(BoxChangeNotify_Op_name, int32(x))
}
func (BoxChangeNotify_Op) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{31, 0}
}

type BoxChangeNotify_Result int32

const (
	BoxChangeNotify_ResultNone   BoxChangeNotify_Result = 0
	BoxChangeNotify_ResultPass   BoxChangeNotify_Result = 1
	BoxChangeNotify_ResultReject BoxChangeNotify_Result = 2
)

var BoxChangeNotify_Result_name = map[int32]string{
	0: "ResultNone",
	1: "ResultPass",
	2: "ResultReject",
}
var BoxChangeNotify_Result_value = map[string]int32{
	"ResultNone":   0,
	"ResultPass":   1,
	"ResultReject": 2,
}

func (x BoxChangeNotify_Result) String() string {
	return proto.EnumName(BoxChangeNotify_Result_name, int32(x))
}
func (BoxChangeNotify_Result) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{31, 1}
}

type GetChannelHoldMicUserRoleListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelHoldMicUserRoleListReq) Reset()         { *m = GetChannelHoldMicUserRoleListReq{} }
func (m *GetChannelHoldMicUserRoleListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelHoldMicUserRoleListReq) ProtoMessage()    {}
func (*GetChannelHoldMicUserRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{0}
}
func (m *GetChannelHoldMicUserRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListReq.Unmarshal(m, b)
}
func (m *GetChannelHoldMicUserRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelHoldMicUserRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelHoldMicUserRoleListReq.Merge(dst, src)
}
func (m *GetChannelHoldMicUserRoleListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListReq.Size(m)
}
func (m *GetChannelHoldMicUserRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelHoldMicUserRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelHoldMicUserRoleListReq proto.InternalMessageInfo

func (m *GetChannelHoldMicUserRoleListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelHoldMicUserRoleListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type UserRole struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleName string `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	// 角色信息变更时间戳
	UpdatedAt            uint32   `protobuf:"varint,3,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRole) Reset()         { *m = UserRole{} }
func (m *UserRole) String() string { return proto.CompactTextString(m) }
func (*UserRole) ProtoMessage()    {}
func (*UserRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{1}
}
func (m *UserRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRole.Unmarshal(m, b)
}
func (m *UserRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRole.Marshal(b, m, deterministic)
}
func (dst *UserRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRole.Merge(dst, src)
}
func (m *UserRole) XXX_Size() int {
	return xxx_messageInfo_UserRole.Size(m)
}
func (m *UserRole) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRole.DiscardUnknown(m)
}

var xxx_messageInfo_UserRole proto.InternalMessageInfo

func (m *UserRole) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserRole) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *UserRole) GetUpdatedAt() uint32 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

type GetChannelHoldMicUserRoleListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RoleList             []*UserRole   `protobuf:"bytes,2,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelHoldMicUserRoleListResp) Reset()         { *m = GetChannelHoldMicUserRoleListResp{} }
func (m *GetChannelHoldMicUserRoleListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelHoldMicUserRoleListResp) ProtoMessage()    {}
func (*GetChannelHoldMicUserRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{2}
}
func (m *GetChannelHoldMicUserRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListResp.Unmarshal(m, b)
}
func (m *GetChannelHoldMicUserRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelHoldMicUserRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelHoldMicUserRoleListResp.Merge(dst, src)
}
func (m *GetChannelHoldMicUserRoleListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelHoldMicUserRoleListResp.Size(m)
}
func (m *GetChannelHoldMicUserRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelHoldMicUserRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelHoldMicUserRoleListResp proto.InternalMessageInfo

func (m *GetChannelHoldMicUserRoleListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelHoldMicUserRoleListResp) GetRoleList() []*UserRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type SetMyChannelRoleReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoleName             string       `protobuf:"bytes,3,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	RoleType             RoleType     `protobuf:"varint,4,opt,name=role_type,json=roleType,proto3,enum=ga.channel_roleplay_logic.RoleType" json:"role_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetMyChannelRoleReq) Reset()         { *m = SetMyChannelRoleReq{} }
func (m *SetMyChannelRoleReq) String() string { return proto.CompactTextString(m) }
func (*SetMyChannelRoleReq) ProtoMessage()    {}
func (*SetMyChannelRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{3}
}
func (m *SetMyChannelRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMyChannelRoleReq.Unmarshal(m, b)
}
func (m *SetMyChannelRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMyChannelRoleReq.Marshal(b, m, deterministic)
}
func (dst *SetMyChannelRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMyChannelRoleReq.Merge(dst, src)
}
func (m *SetMyChannelRoleReq) XXX_Size() int {
	return xxx_messageInfo_SetMyChannelRoleReq.Size(m)
}
func (m *SetMyChannelRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMyChannelRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMyChannelRoleReq proto.InternalMessageInfo

func (m *SetMyChannelRoleReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetMyChannelRoleReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMyChannelRoleReq) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *SetMyChannelRoleReq) GetRoleType() RoleType {
	if m != nil {
		return m.RoleType
	}
	return RoleType_RoleTypeNil
}

type SetMyChannelRoleResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetMyChannelRoleResp) Reset()         { *m = SetMyChannelRoleResp{} }
func (m *SetMyChannelRoleResp) String() string { return proto.CompactTextString(m) }
func (*SetMyChannelRoleResp) ProtoMessage()    {}
func (*SetMyChannelRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{4}
}
func (m *SetMyChannelRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMyChannelRoleResp.Unmarshal(m, b)
}
func (m *SetMyChannelRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMyChannelRoleResp.Marshal(b, m, deterministic)
}
func (dst *SetMyChannelRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMyChannelRoleResp.Merge(dst, src)
}
func (m *SetMyChannelRoleResp) XXX_Size() int {
	return xxx_messageInfo_SetMyChannelRoleResp.Size(m)
}
func (m *SetMyChannelRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMyChannelRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMyChannelRoleResp proto.InternalMessageInfo

func (m *SetMyChannelRoleResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ChannelUserRoleNotify struct {
	// 变更的角色信息
	Role *UserRole `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	// 审核结果
	Result               ChannelUserRoleNotify_AuditResult `protobuf:"varint,2,opt,name=result,proto3,enum=ga.channel_roleplay_logic.ChannelUserRoleNotify_AuditResult" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ChannelUserRoleNotify) Reset()         { *m = ChannelUserRoleNotify{} }
func (m *ChannelUserRoleNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelUserRoleNotify) ProtoMessage()    {}
func (*ChannelUserRoleNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{5}
}
func (m *ChannelUserRoleNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelUserRoleNotify.Unmarshal(m, b)
}
func (m *ChannelUserRoleNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelUserRoleNotify.Marshal(b, m, deterministic)
}
func (dst *ChannelUserRoleNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelUserRoleNotify.Merge(dst, src)
}
func (m *ChannelUserRoleNotify) XXX_Size() int {
	return xxx_messageInfo_ChannelUserRoleNotify.Size(m)
}
func (m *ChannelUserRoleNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelUserRoleNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelUserRoleNotify proto.InternalMessageInfo

func (m *ChannelUserRoleNotify) GetRole() *UserRole {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *ChannelUserRoleNotify) GetResult() ChannelUserRoleNotify_AuditResult {
	if m != nil {
		return m.Result
	}
	return ChannelUserRoleNotify_AuditResultDefault
}

type ChannelRoleChangeMsg struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	RoleName             string   `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRoleChangeMsg) Reset()         { *m = ChannelRoleChangeMsg{} }
func (m *ChannelRoleChangeMsg) String() string { return proto.CompactTextString(m) }
func (*ChannelRoleChangeMsg) ProtoMessage()    {}
func (*ChannelRoleChangeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{6}
}
func (m *ChannelRoleChangeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRoleChangeMsg.Unmarshal(m, b)
}
func (m *ChannelRoleChangeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRoleChangeMsg.Marshal(b, m, deterministic)
}
func (dst *ChannelRoleChangeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRoleChangeMsg.Merge(dst, src)
}
func (m *ChannelRoleChangeMsg) XXX_Size() int {
	return xxx_messageInfo_ChannelRoleChangeMsg.Size(m)
}
func (m *ChannelRoleChangeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRoleChangeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRoleChangeMsg proto.InternalMessageInfo

func (m *ChannelRoleChangeMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ChannelRoleChangeMsg) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

type EnterBoxReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Channelid            uint32       `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32       `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxType              BoxType      `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	AudioBoxid           uint32       `protobuf:"varint,5,opt,name=audio_boxid,json=audioBoxid,proto3" json:"audio_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *EnterBoxReq) Reset()         { *m = EnterBoxReq{} }
func (m *EnterBoxReq) String() string { return proto.CompactTextString(m) }
func (*EnterBoxReq) ProtoMessage()    {}
func (*EnterBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{7}
}
func (m *EnterBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterBoxReq.Unmarshal(m, b)
}
func (m *EnterBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterBoxReq.Marshal(b, m, deterministic)
}
func (dst *EnterBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterBoxReq.Merge(dst, src)
}
func (m *EnterBoxReq) XXX_Size() int {
	return xxx_messageInfo_EnterBoxReq.Size(m)
}
func (m *EnterBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_EnterBoxReq proto.InternalMessageInfo

func (m *EnterBoxReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *EnterBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *EnterBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *EnterBoxReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *EnterBoxReq) GetAudioBoxid() uint32 {
	if m != nil {
		return m.AudioBoxid
	}
	return 0
}

type EnterBoxResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OptTs                uint64        `protobuf:"varint,2,opt,name=opt_ts,json=optTs,proto3" json:"opt_ts,omitempty"`
	IsNeedApply          bool          `protobuf:"varint,3,opt,name=is_need_apply,json=isNeedApply,proto3" json:"is_need_apply,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *EnterBoxResp) Reset()         { *m = EnterBoxResp{} }
func (m *EnterBoxResp) String() string { return proto.CompactTextString(m) }
func (*EnterBoxResp) ProtoMessage()    {}
func (*EnterBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{8}
}
func (m *EnterBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterBoxResp.Unmarshal(m, b)
}
func (m *EnterBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterBoxResp.Marshal(b, m, deterministic)
}
func (dst *EnterBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterBoxResp.Merge(dst, src)
}
func (m *EnterBoxResp) XXX_Size() int {
	return xxx_messageInfo_EnterBoxResp.Size(m)
}
func (m *EnterBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_EnterBoxResp proto.InternalMessageInfo

func (m *EnterBoxResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *EnterBoxResp) GetOptTs() uint64 {
	if m != nil {
		return m.OptTs
	}
	return 0
}

func (m *EnterBoxResp) GetIsNeedApply() bool {
	if m != nil {
		return m.IsNeedApply
	}
	return false
}

type ExitBoxReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Channelid            uint32       `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32       `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxType              BoxType      `protobuf:"varint,4,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	AudioBoxid           uint32       `protobuf:"varint,5,opt,name=audio_boxid,json=audioBoxid,proto3" json:"audio_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ExitBoxReq) Reset()         { *m = ExitBoxReq{} }
func (m *ExitBoxReq) String() string { return proto.CompactTextString(m) }
func (*ExitBoxReq) ProtoMessage()    {}
func (*ExitBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{9}
}
func (m *ExitBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitBoxReq.Unmarshal(m, b)
}
func (m *ExitBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitBoxReq.Marshal(b, m, deterministic)
}
func (dst *ExitBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitBoxReq.Merge(dst, src)
}
func (m *ExitBoxReq) XXX_Size() int {
	return xxx_messageInfo_ExitBoxReq.Size(m)
}
func (m *ExitBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExitBoxReq proto.InternalMessageInfo

func (m *ExitBoxReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ExitBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ExitBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *ExitBoxReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *ExitBoxReq) GetAudioBoxid() uint32 {
	if m != nil {
		return m.AudioBoxid
	}
	return 0
}

type ExitBoxResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OptTs                uint64        `protobuf:"varint,2,opt,name=opt_ts,json=optTs,proto3" json:"opt_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ExitBoxResp) Reset()         { *m = ExitBoxResp{} }
func (m *ExitBoxResp) String() string { return proto.CompactTextString(m) }
func (*ExitBoxResp) ProtoMessage()    {}
func (*ExitBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{10}
}
func (m *ExitBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitBoxResp.Unmarshal(m, b)
}
func (m *ExitBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitBoxResp.Marshal(b, m, deterministic)
}
func (dst *ExitBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitBoxResp.Merge(dst, src)
}
func (m *ExitBoxResp) XXX_Size() int {
	return xxx_messageInfo_ExitBoxResp.Size(m)
}
func (m *ExitBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExitBoxResp proto.InternalMessageInfo

func (m *ExitBoxResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ExitBoxResp) GetOptTs() uint64 {
	if m != nil {
		return m.OptTs
	}
	return 0
}

type OpeBoxInfo struct {
	OpeType              BoxNotifyUserOpType `protobuf:"varint,1,opt,name=ope_type,json=opeType,proto3,enum=ga.channel_roleplay_logic.BoxNotifyUserOpType" json:"ope_type,omitempty"`
	Boxid                uint32              `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	Uid                  uint32              `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	BoxCnt               uint32              `protobuf:"varint,4,opt,name=box_cnt,json=boxCnt,proto3" json:"box_cnt,omitempty"`
	NotifyType           BoxNotifyType       `protobuf:"varint,5,opt,name=notify_type,json=notifyType,proto3,enum=ga.channel_roleplay_logic.BoxNotifyType" json:"notify_type,omitempty"`
	OptTs                uint64              `protobuf:"varint,6,opt,name=opt_ts,json=optTs,proto3" json:"opt_ts,omitempty"`
	AudioBoxIds          []uint32            `protobuf:"varint,7,rep,packed,name=audio_box_ids,json=audioBoxIds,proto3" json:"audio_box_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *OpeBoxInfo) Reset()         { *m = OpeBoxInfo{} }
func (m *OpeBoxInfo) String() string { return proto.CompactTextString(m) }
func (*OpeBoxInfo) ProtoMessage()    {}
func (*OpeBoxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{11}
}
func (m *OpeBoxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpeBoxInfo.Unmarshal(m, b)
}
func (m *OpeBoxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpeBoxInfo.Marshal(b, m, deterministic)
}
func (dst *OpeBoxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpeBoxInfo.Merge(dst, src)
}
func (m *OpeBoxInfo) XXX_Size() int {
	return xxx_messageInfo_OpeBoxInfo.Size(m)
}
func (m *OpeBoxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OpeBoxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OpeBoxInfo proto.InternalMessageInfo

func (m *OpeBoxInfo) GetOpeType() BoxNotifyUserOpType {
	if m != nil {
		return m.OpeType
	}
	return BoxNotifyUserOpType_EnterNone
}

func (m *OpeBoxInfo) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *OpeBoxInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OpeBoxInfo) GetBoxCnt() uint32 {
	if m != nil {
		return m.BoxCnt
	}
	return 0
}

func (m *OpeBoxInfo) GetNotifyType() BoxNotifyType {
	if m != nil {
		return m.NotifyType
	}
	return BoxNotifyType_UserOpType
}

func (m *OpeBoxInfo) GetOptTs() uint64 {
	if m != nil {
		return m.OptTs
	}
	return 0
}

func (m *OpeBoxInfo) GetAudioBoxIds() []uint32 {
	if m != nil {
		return m.AudioBoxIds
	}
	return nil
}

type OpeBoxNotify struct {
	OpeBoxInfo           []*OpeBoxInfo `protobuf:"bytes,1,rep,name=ope_box_info,json=opeBoxInfo,proto3" json:"ope_box_info,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Msg                  string        `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	RoleName             string        `protobuf:"bytes,4,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	OpeRoleName          string        `protobuf:"bytes,5,opt,name=ope_role_name,json=opeRoleName,proto3" json:"ope_role_name,omitempty"`
	BoxType              BoxType       `protobuf:"varint,6,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OpeBoxNotify) Reset()         { *m = OpeBoxNotify{} }
func (m *OpeBoxNotify) String() string { return proto.CompactTextString(m) }
func (*OpeBoxNotify) ProtoMessage()    {}
func (*OpeBoxNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{12}
}
func (m *OpeBoxNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpeBoxNotify.Unmarshal(m, b)
}
func (m *OpeBoxNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpeBoxNotify.Marshal(b, m, deterministic)
}
func (dst *OpeBoxNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpeBoxNotify.Merge(dst, src)
}
func (m *OpeBoxNotify) XXX_Size() int {
	return xxx_messageInfo_OpeBoxNotify.Size(m)
}
func (m *OpeBoxNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_OpeBoxNotify.DiscardUnknown(m)
}

var xxx_messageInfo_OpeBoxNotify proto.InternalMessageInfo

func (m *OpeBoxNotify) GetOpeBoxInfo() []*OpeBoxInfo {
	if m != nil {
		return m.OpeBoxInfo
	}
	return nil
}

func (m *OpeBoxNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpeBoxNotify) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *OpeBoxNotify) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *OpeBoxNotify) GetOpeRoleName() string {
	if m != nil {
		return m.OpeRoleName
	}
	return ""
}

func (m *OpeBoxNotify) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type ApplyNotify struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Boxid                uint32   `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Msg                  string   `protobuf:"bytes,4,opt,name=msg,proto3" json:"msg,omitempty"`
	RoleName             string   `protobuf:"bytes,5,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyNotify) Reset()         { *m = ApplyNotify{} }
func (m *ApplyNotify) String() string { return proto.CompactTextString(m) }
func (*ApplyNotify) ProtoMessage()    {}
func (*ApplyNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{13}
}
func (m *ApplyNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyNotify.Unmarshal(m, b)
}
func (m *ApplyNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyNotify.Marshal(b, m, deterministic)
}
func (dst *ApplyNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyNotify.Merge(dst, src)
}
func (m *ApplyNotify) XXX_Size() int {
	return xxx_messageInfo_ApplyNotify.Size(m)
}
func (m *ApplyNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyNotify proto.InternalMessageInfo

func (m *ApplyNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ApplyNotify) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *ApplyNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyNotify) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *ApplyNotify) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

type HandleApplyBoxReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Channelid            uint32       `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32       `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	ApplyBoxType         ApplyBoxType `protobuf:"varint,4,opt,name=apply_box_type,json=applyBoxType,proto3,enum=ga.channel_roleplay_logic.ApplyBoxType" json:"apply_box_type,omitempty"`
	Uid                  uint32       `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HandleApplyBoxReq) Reset()         { *m = HandleApplyBoxReq{} }
func (m *HandleApplyBoxReq) String() string { return proto.CompactTextString(m) }
func (*HandleApplyBoxReq) ProtoMessage()    {}
func (*HandleApplyBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{14}
}
func (m *HandleApplyBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleApplyBoxReq.Unmarshal(m, b)
}
func (m *HandleApplyBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleApplyBoxReq.Marshal(b, m, deterministic)
}
func (dst *HandleApplyBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleApplyBoxReq.Merge(dst, src)
}
func (m *HandleApplyBoxReq) XXX_Size() int {
	return xxx_messageInfo_HandleApplyBoxReq.Size(m)
}
func (m *HandleApplyBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleApplyBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandleApplyBoxReq proto.InternalMessageInfo

func (m *HandleApplyBoxReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HandleApplyBoxReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *HandleApplyBoxReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *HandleApplyBoxReq) GetApplyBoxType() ApplyBoxType {
	if m != nil {
		return m.ApplyBoxType
	}
	return ApplyBoxType_ApplyDefault
}

func (m *HandleApplyBoxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type HandleApplyBoxResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Msg                  string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HandleApplyBoxResp) Reset()         { *m = HandleApplyBoxResp{} }
func (m *HandleApplyBoxResp) String() string { return proto.CompactTextString(m) }
func (*HandleApplyBoxResp) ProtoMessage()    {}
func (*HandleApplyBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{15}
}
func (m *HandleApplyBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleApplyBoxResp.Unmarshal(m, b)
}
func (m *HandleApplyBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleApplyBoxResp.Marshal(b, m, deterministic)
}
func (dst *HandleApplyBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleApplyBoxResp.Merge(dst, src)
}
func (m *HandleApplyBoxResp) XXX_Size() int {
	return xxx_messageInfo_HandleApplyBoxResp.Size(m)
}
func (m *HandleApplyBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleApplyBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandleApplyBoxResp proto.InternalMessageInfo

func (m *HandleApplyBoxResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *HandleApplyBoxResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type BoxInfo struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32   `protobuf:"varint,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	BoxUserCnt           uint32   `protobuf:"varint,3,opt,name=box_user_cnt,json=boxUserCnt,proto3" json:"box_user_cnt,omitempty"`
	BoxName              string   `protobuf:"bytes,4,opt,name=box_name,json=boxName,proto3" json:"box_name,omitempty"`
	BoxType              BoxType  `protobuf:"varint,5,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	MicCap               uint32   `protobuf:"varint,6,opt,name=mic_cap,json=micCap,proto3" json:"mic_cap,omitempty"`
	PublicMicList        []uint32 `protobuf:"varint,7,rep,packed,name=public_mic_list,json=publicMicList,proto3" json:"public_mic_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxInfo) Reset()         { *m = BoxInfo{} }
func (m *BoxInfo) String() string { return proto.CompactTextString(m) }
func (*BoxInfo) ProtoMessage()    {}
func (*BoxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{16}
}
func (m *BoxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxInfo.Unmarshal(m, b)
}
func (m *BoxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxInfo.Marshal(b, m, deterministic)
}
func (dst *BoxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxInfo.Merge(dst, src)
}
func (m *BoxInfo) XXX_Size() int {
	return xxx_messageInfo_BoxInfo.Size(m)
}
func (m *BoxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BoxInfo proto.InternalMessageInfo

func (m *BoxInfo) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *BoxInfo) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *BoxInfo) GetBoxUserCnt() uint32 {
	if m != nil {
		return m.BoxUserCnt
	}
	return 0
}

func (m *BoxInfo) GetBoxName() string {
	if m != nil {
		return m.BoxName
	}
	return ""
}

func (m *BoxInfo) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *BoxInfo) GetMicCap() uint32 {
	if m != nil {
		return m.MicCap
	}
	return 0
}

func (m *BoxInfo) GetPublicMicList() []uint32 {
	if m != nil {
		return m.PublicMicList
	}
	return nil
}

type GetBoxInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Channelid            uint32       `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	BoxType              BoxType      `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBoxInfoReq) Reset()         { *m = GetBoxInfoReq{} }
func (m *GetBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfoReq) ProtoMessage()    {}
func (*GetBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{17}
}
func (m *GetBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfoReq.Unmarshal(m, b)
}
func (m *GetBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfoReq.Merge(dst, src)
}
func (m *GetBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfoReq.Size(m)
}
func (m *GetBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfoReq proto.InternalMessageInfo

func (m *GetBoxInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBoxInfoReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxInfoReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type GetBoxInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	BoxInfo              []*BoxInfo    `protobuf:"bytes,2,rep,name=box_info,json=boxInfo,proto3" json:"box_info,omitempty"`
	UserBoxid            uint32        `protobuf:"varint,3,opt,name=user_boxid,json=userBoxid,proto3" json:"user_boxid,omitempty"`
	MicTotal             uint32        `protobuf:"varint,4,opt,name=mic_total,json=micTotal,proto3" json:"mic_total,omitempty"`
	UserAudioBoxids      []uint32      `protobuf:"varint,5,rep,packed,name=user_audio_boxids,json=userAudioBoxids,proto3" json:"user_audio_boxids,omitempty"`
	MainCommonBoxid      uint32        `protobuf:"varint,6,opt,name=main_common_boxid,json=mainCommonBoxid,proto3" json:"main_common_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBoxInfoResp) Reset()         { *m = GetBoxInfoResp{} }
func (m *GetBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfoResp) ProtoMessage()    {}
func (*GetBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{18}
}
func (m *GetBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfoResp.Unmarshal(m, b)
}
func (m *GetBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfoResp.Merge(dst, src)
}
func (m *GetBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfoResp.Size(m)
}
func (m *GetBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfoResp proto.InternalMessageInfo

func (m *GetBoxInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBoxInfoResp) GetBoxInfo() []*BoxInfo {
	if m != nil {
		return m.BoxInfo
	}
	return nil
}

func (m *GetBoxInfoResp) GetUserBoxid() uint32 {
	if m != nil {
		return m.UserBoxid
	}
	return 0
}

func (m *GetBoxInfoResp) GetMicTotal() uint32 {
	if m != nil {
		return m.MicTotal
	}
	return 0
}

func (m *GetBoxInfoResp) GetUserAudioBoxids() []uint32 {
	if m != nil {
		return m.UserAudioBoxids
	}
	return nil
}

func (m *GetBoxInfoResp) GetMainCommonBoxid() uint32 {
	if m != nil {
		return m.MainCommonBoxid
	}
	return 0
}

type GetBoxInfosByLimitReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Channelid            uint32       `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32       `protobuf:"varint,3,opt,name=boxid,proto3" json:"boxid,omitempty"`
	LastTime             int64        `protobuf:"varint,4,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	GetCnt               int64        `protobuf:"varint,5,opt,name=get_cnt,json=getCnt,proto3" json:"get_cnt,omitempty"`
	BoxType              BoxType      `protobuf:"varint,6,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBoxInfosByLimitReq) Reset()         { *m = GetBoxInfosByLimitReq{} }
func (m *GetBoxInfosByLimitReq) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfosByLimitReq) ProtoMessage()    {}
func (*GetBoxInfosByLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{19}
}
func (m *GetBoxInfosByLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Unmarshal(m, b)
}
func (m *GetBoxInfosByLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfosByLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfosByLimitReq.Merge(dst, src)
}
func (m *GetBoxInfosByLimitReq) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfosByLimitReq.Size(m)
}
func (m *GetBoxInfosByLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfosByLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfosByLimitReq proto.InternalMessageInfo

func (m *GetBoxInfosByLimitReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBoxInfosByLimitReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetLastTime() int64 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetGetCnt() int64 {
	if m != nil {
		return m.GetCnt
	}
	return 0
}

func (m *GetBoxInfosByLimitReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

type BoxUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Accunt               string   `protobuf:"bytes,2,opt,name=accunt,proto3" json:"accunt,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Gender               int32    `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxUserInfo) Reset()         { *m = BoxUserInfo{} }
func (m *BoxUserInfo) String() string { return proto.CompactTextString(m) }
func (*BoxUserInfo) ProtoMessage()    {}
func (*BoxUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{20}
}
func (m *BoxUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxUserInfo.Unmarshal(m, b)
}
func (m *BoxUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxUserInfo.Marshal(b, m, deterministic)
}
func (dst *BoxUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxUserInfo.Merge(dst, src)
}
func (m *BoxUserInfo) XXX_Size() int {
	return xxx_messageInfo_BoxUserInfo.Size(m)
}
func (m *BoxUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BoxUserInfo proto.InternalMessageInfo

func (m *BoxUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BoxUserInfo) GetAccunt() string {
	if m != nil {
		return m.Accunt
	}
	return ""
}

func (m *BoxUserInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *BoxUserInfo) GetGender() int32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

type GetBoxInfosByLimitResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	BoxUserInfos         []*BoxUserInfo `protobuf:"bytes,2,rep,name=box_user_infos,json=boxUserInfos,proto3" json:"box_user_infos,omitempty"`
	Channelid            uint32         `protobuf:"varint,3,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Boxid                uint32         `protobuf:"varint,4,opt,name=boxid,proto3" json:"boxid,omitempty"`
	LastTime             int64          `protobuf:"varint,5,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetBoxInfosByLimitResp) Reset()         { *m = GetBoxInfosByLimitResp{} }
func (m *GetBoxInfosByLimitResp) String() string { return proto.CompactTextString(m) }
func (*GetBoxInfosByLimitResp) ProtoMessage()    {}
func (*GetBoxInfosByLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{21}
}
func (m *GetBoxInfosByLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Unmarshal(m, b)
}
func (m *GetBoxInfosByLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Marshal(b, m, deterministic)
}
func (dst *GetBoxInfosByLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBoxInfosByLimitResp.Merge(dst, src)
}
func (m *GetBoxInfosByLimitResp) XXX_Size() int {
	return xxx_messageInfo_GetBoxInfosByLimitResp.Size(m)
}
func (m *GetBoxInfosByLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBoxInfosByLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBoxInfosByLimitResp proto.InternalMessageInfo

func (m *GetBoxInfosByLimitResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBoxInfosByLimitResp) GetBoxUserInfos() []*BoxUserInfo {
	if m != nil {
		return m.BoxUserInfos
	}
	return nil
}

func (m *GetBoxInfosByLimitResp) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GetBoxInfosByLimitResp) GetBoxid() uint32 {
	if m != nil {
		return m.Boxid
	}
	return 0
}

func (m *GetBoxInfosByLimitResp) GetLastTime() int64 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

type GetChannelUserRoleListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32     `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	RoleType             RoleType     `protobuf:"varint,4,opt,name=role_type,json=roleType,proto3,enum=ga.channel_roleplay_logic.RoleType" json:"role_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelUserRoleListReq) Reset()         { *m = GetChannelUserRoleListReq{} }
func (m *GetChannelUserRoleListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelUserRoleListReq) ProtoMessage()    {}
func (*GetChannelUserRoleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{22}
}
func (m *GetChannelUserRoleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelUserRoleListReq.Unmarshal(m, b)
}
func (m *GetChannelUserRoleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelUserRoleListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelUserRoleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelUserRoleListReq.Merge(dst, src)
}
func (m *GetChannelUserRoleListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelUserRoleListReq.Size(m)
}
func (m *GetChannelUserRoleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelUserRoleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelUserRoleListReq proto.InternalMessageInfo

func (m *GetChannelUserRoleListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelUserRoleListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelUserRoleListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetChannelUserRoleListReq) GetRoleType() RoleType {
	if m != nil {
		return m.RoleType
	}
	return RoleType_RoleTypeNil
}

type GetChannelUserRoleListResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RoleList []*UserRole   `protobuf:"bytes,2,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	// 本人审核中的角色
	AuditRole            *UserRole `protobuf:"bytes,3,opt,name=audit_role,json=auditRole,proto3" json:"audit_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetChannelUserRoleListResp) Reset()         { *m = GetChannelUserRoleListResp{} }
func (m *GetChannelUserRoleListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelUserRoleListResp) ProtoMessage()    {}
func (*GetChannelUserRoleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{23}
}
func (m *GetChannelUserRoleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelUserRoleListResp.Unmarshal(m, b)
}
func (m *GetChannelUserRoleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelUserRoleListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelUserRoleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelUserRoleListResp.Merge(dst, src)
}
func (m *GetChannelUserRoleListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelUserRoleListResp.Size(m)
}
func (m *GetChannelUserRoleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelUserRoleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelUserRoleListResp proto.InternalMessageInfo

func (m *GetChannelUserRoleListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelUserRoleListResp) GetRoleList() []*UserRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

func (m *GetChannelUserRoleListResp) GetAuditRole() *UserRole {
	if m != nil {
		return m.AuditRole
	}
	return nil
}

type UpsertBoxInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Box                  *BoxInfo     `protobuf:"bytes,2,opt,name=box,proto3" json:"box,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpsertBoxInfoReq) Reset()         { *m = UpsertBoxInfoReq{} }
func (m *UpsertBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpsertBoxInfoReq) ProtoMessage()    {}
func (*UpsertBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{24}
}
func (m *UpsertBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBoxInfoReq.Unmarshal(m, b)
}
func (m *UpsertBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpsertBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBoxInfoReq.Merge(dst, src)
}
func (m *UpsertBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpsertBoxInfoReq.Size(m)
}
func (m *UpsertBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBoxInfoReq proto.InternalMessageInfo

func (m *UpsertBoxInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpsertBoxInfoReq) GetBox() *BoxInfo {
	if m != nil {
		return m.Box
	}
	return nil
}

type UpsertBoxInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Box                  *BoxInfo      `protobuf:"bytes,2,opt,name=box,proto3" json:"box,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpsertBoxInfoResp) Reset()         { *m = UpsertBoxInfoResp{} }
func (m *UpsertBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpsertBoxInfoResp) ProtoMessage()    {}
func (*UpsertBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{25}
}
func (m *UpsertBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBoxInfoResp.Unmarshal(m, b)
}
func (m *UpsertBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpsertBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBoxInfoResp.Merge(dst, src)
}
func (m *UpsertBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpsertBoxInfoResp.Size(m)
}
func (m *UpsertBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBoxInfoResp proto.InternalMessageInfo

func (m *UpsertBoxInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UpsertBoxInfoResp) GetBox() *BoxInfo {
	if m != nil {
		return m.Box
	}
	return nil
}

type DelBoxInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BoxId                uint32       `protobuf:"varint,3,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DelBoxInfoReq) Reset()         { *m = DelBoxInfoReq{} }
func (m *DelBoxInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelBoxInfoReq) ProtoMessage()    {}
func (*DelBoxInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{26}
}
func (m *DelBoxInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBoxInfoReq.Unmarshal(m, b)
}
func (m *DelBoxInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBoxInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelBoxInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBoxInfoReq.Merge(dst, src)
}
func (m *DelBoxInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelBoxInfoReq.Size(m)
}
func (m *DelBoxInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBoxInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBoxInfoReq proto.InternalMessageInfo

func (m *DelBoxInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DelBoxInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DelBoxInfoReq) GetBoxId() uint32 {
	if m != nil {
		return m.BoxId
	}
	return 0
}

type DelBoxInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DelBoxInfoResp) Reset()         { *m = DelBoxInfoResp{} }
func (m *DelBoxInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelBoxInfoResp) ProtoMessage()    {}
func (*DelBoxInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{27}
}
func (m *DelBoxInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBoxInfoResp.Unmarshal(m, b)
}
func (m *DelBoxInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBoxInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelBoxInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBoxInfoResp.Merge(dst, src)
}
func (m *DelBoxInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelBoxInfoResp.Size(m)
}
func (m *DelBoxInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBoxInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBoxInfoResp proto.InternalMessageInfo

func (m *DelBoxInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type OpenCommonMicReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BoxType              BoxType      `protobuf:"varint,3,opt,name=box_type,json=boxType,proto3,enum=ga.channel_roleplay_logic.BoxType" json:"box_type,omitempty"`
	CommonBoxid          uint32       `protobuf:"varint,4,opt,name=common_boxid,json=commonBoxid,proto3" json:"common_boxid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OpenCommonMicReq) Reset()         { *m = OpenCommonMicReq{} }
func (m *OpenCommonMicReq) String() string { return proto.CompactTextString(m) }
func (*OpenCommonMicReq) ProtoMessage()    {}
func (*OpenCommonMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{28}
}
func (m *OpenCommonMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenCommonMicReq.Unmarshal(m, b)
}
func (m *OpenCommonMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenCommonMicReq.Marshal(b, m, deterministic)
}
func (dst *OpenCommonMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenCommonMicReq.Merge(dst, src)
}
func (m *OpenCommonMicReq) XXX_Size() int {
	return xxx_messageInfo_OpenCommonMicReq.Size(m)
}
func (m *OpenCommonMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenCommonMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_OpenCommonMicReq proto.InternalMessageInfo

func (m *OpenCommonMicReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OpenCommonMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpenCommonMicReq) GetBoxType() BoxType {
	if m != nil {
		return m.BoxType
	}
	return BoxType_BoxTypeRoleplay
}

func (m *OpenCommonMicReq) GetCommonBoxid() uint32 {
	if m != nil {
		return m.CommonBoxid
	}
	return 0
}

type OpenCommonMicResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OpenCommonMicResp) Reset()         { *m = OpenCommonMicResp{} }
func (m *OpenCommonMicResp) String() string { return proto.CompactTextString(m) }
func (*OpenCommonMicResp) ProtoMessage()    {}
func (*OpenCommonMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{29}
}
func (m *OpenCommonMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenCommonMicResp.Unmarshal(m, b)
}
func (m *OpenCommonMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenCommonMicResp.Marshal(b, m, deterministic)
}
func (dst *OpenCommonMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenCommonMicResp.Merge(dst, src)
}
func (m *OpenCommonMicResp) XXX_Size() int {
	return xxx_messageInfo_OpenCommonMicResp.Size(m)
}
func (m *OpenCommonMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenCommonMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_OpenCommonMicResp proto.InternalMessageInfo

func (m *OpenCommonMicResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 广播
type OpenCommonMicBC struct {
	CommonBoxid          uint32   `protobuf:"varint,1,opt,name=common_boxid,json=commonBoxid,proto3" json:"common_boxid,omitempty"`
	OpeUid               uint32   `protobuf:"varint,2,opt,name=ope_uid,json=opeUid,proto3" json:"ope_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UserName             string   `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	NickName             string   `protobuf:"bytes,5,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Opts                 int64    `protobuf:"varint,6,opt,name=opts,proto3" json:"opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenCommonMicBC) Reset()         { *m = OpenCommonMicBC{} }
func (m *OpenCommonMicBC) String() string { return proto.CompactTextString(m) }
func (*OpenCommonMicBC) ProtoMessage()    {}
func (*OpenCommonMicBC) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{30}
}
func (m *OpenCommonMicBC) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenCommonMicBC.Unmarshal(m, b)
}
func (m *OpenCommonMicBC) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenCommonMicBC.Marshal(b, m, deterministic)
}
func (dst *OpenCommonMicBC) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenCommonMicBC.Merge(dst, src)
}
func (m *OpenCommonMicBC) XXX_Size() int {
	return xxx_messageInfo_OpenCommonMicBC.Size(m)
}
func (m *OpenCommonMicBC) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenCommonMicBC.DiscardUnknown(m)
}

var xxx_messageInfo_OpenCommonMicBC proto.InternalMessageInfo

func (m *OpenCommonMicBC) GetCommonBoxid() uint32 {
	if m != nil {
		return m.CommonBoxid
	}
	return 0
}

func (m *OpenCommonMicBC) GetOpeUid() uint32 {
	if m != nil {
		return m.OpeUid
	}
	return 0
}

func (m *OpenCommonMicBC) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpenCommonMicBC) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *OpenCommonMicBC) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *OpenCommonMicBC) GetOpts() int64 {
	if m != nil {
		return m.Opts
	}
	return 0
}

// 子频道信息变更推送
type BoxChangeNotify struct {
	Op  BoxChangeNotify_Op `protobuf:"varint,1,opt,name=op,proto3,enum=ga.channel_roleplay_logic.BoxChangeNotify_Op" json:"op,omitempty"`
	Box *BoxInfo           `protobuf:"bytes,2,opt,name=box,proto3" json:"box,omitempty"`
	// 修改时间戳(毫秒)
	ChangedAt uint64 `protobuf:"varint,3,opt,name=changed_at,json=changedAt,proto3" json:"changed_at,omitempty"`
	// 子频道创建者
	BoxUid uint32 `protobuf:"varint,4,opt,name=box_uid,json=boxUid,proto3" json:"box_uid,omitempty"`
	// 审核结果, 仅对 uid = box_uid && op = [OpCreate, OpUpdateName] 有效
	Result BoxChangeNotify_Result `protobuf:"varint,5,opt,name=result,proto3,enum=ga.channel_roleplay_logic.BoxChangeNotify_Result" json:"result,omitempty"`
	// 房间内是否还有子频道
	ExistsBox            bool     `protobuf:"varint,6,opt,name=exists_box,json=existsBox,proto3" json:"exists_box,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoxChangeNotify) Reset()         { *m = BoxChangeNotify{} }
func (m *BoxChangeNotify) String() string { return proto.CompactTextString(m) }
func (*BoxChangeNotify) ProtoMessage()    {}
func (*BoxChangeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62, []int{31}
}
func (m *BoxChangeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoxChangeNotify.Unmarshal(m, b)
}
func (m *BoxChangeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoxChangeNotify.Marshal(b, m, deterministic)
}
func (dst *BoxChangeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoxChangeNotify.Merge(dst, src)
}
func (m *BoxChangeNotify) XXX_Size() int {
	return xxx_messageInfo_BoxChangeNotify.Size(m)
}
func (m *BoxChangeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_BoxChangeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_BoxChangeNotify proto.InternalMessageInfo

func (m *BoxChangeNotify) GetOp() BoxChangeNotify_Op {
	if m != nil {
		return m.Op
	}
	return BoxChangeNotify_OpNone
}

func (m *BoxChangeNotify) GetBox() *BoxInfo {
	if m != nil {
		return m.Box
	}
	return nil
}

func (m *BoxChangeNotify) GetChangedAt() uint64 {
	if m != nil {
		return m.ChangedAt
	}
	return 0
}

func (m *BoxChangeNotify) GetBoxUid() uint32 {
	if m != nil {
		return m.BoxUid
	}
	return 0
}

func (m *BoxChangeNotify) GetResult() BoxChangeNotify_Result {
	if m != nil {
		return m.Result
	}
	return BoxChangeNotify_ResultNone
}

func (m *BoxChangeNotify) GetExistsBox() bool {
	if m != nil {
		return m.ExistsBox
	}
	return false
}

func init() {
	proto.RegisterType((*GetChannelHoldMicUserRoleListReq)(nil), "ga.channel_roleplay_logic.GetChannelHoldMicUserRoleListReq")
	proto.RegisterType((*UserRole)(nil), "ga.channel_roleplay_logic.UserRole")
	proto.RegisterType((*GetChannelHoldMicUserRoleListResp)(nil), "ga.channel_roleplay_logic.GetChannelHoldMicUserRoleListResp")
	proto.RegisterType((*SetMyChannelRoleReq)(nil), "ga.channel_roleplay_logic.SetMyChannelRoleReq")
	proto.RegisterType((*SetMyChannelRoleResp)(nil), "ga.channel_roleplay_logic.SetMyChannelRoleResp")
	proto.RegisterType((*ChannelUserRoleNotify)(nil), "ga.channel_roleplay_logic.ChannelUserRoleNotify")
	proto.RegisterType((*ChannelRoleChangeMsg)(nil), "ga.channel_roleplay_logic.ChannelRoleChangeMsg")
	proto.RegisterType((*EnterBoxReq)(nil), "ga.channel_roleplay_logic.EnterBoxReq")
	proto.RegisterType((*EnterBoxResp)(nil), "ga.channel_roleplay_logic.EnterBoxResp")
	proto.RegisterType((*ExitBoxReq)(nil), "ga.channel_roleplay_logic.ExitBoxReq")
	proto.RegisterType((*ExitBoxResp)(nil), "ga.channel_roleplay_logic.ExitBoxResp")
	proto.RegisterType((*OpeBoxInfo)(nil), "ga.channel_roleplay_logic.OpeBoxInfo")
	proto.RegisterType((*OpeBoxNotify)(nil), "ga.channel_roleplay_logic.OpeBoxNotify")
	proto.RegisterType((*ApplyNotify)(nil), "ga.channel_roleplay_logic.ApplyNotify")
	proto.RegisterType((*HandleApplyBoxReq)(nil), "ga.channel_roleplay_logic.HandleApplyBoxReq")
	proto.RegisterType((*HandleApplyBoxResp)(nil), "ga.channel_roleplay_logic.HandleApplyBoxResp")
	proto.RegisterType((*BoxInfo)(nil), "ga.channel_roleplay_logic.BoxInfo")
	proto.RegisterType((*GetBoxInfoReq)(nil), "ga.channel_roleplay_logic.GetBoxInfoReq")
	proto.RegisterType((*GetBoxInfoResp)(nil), "ga.channel_roleplay_logic.GetBoxInfoResp")
	proto.RegisterType((*GetBoxInfosByLimitReq)(nil), "ga.channel_roleplay_logic.GetBoxInfosByLimitReq")
	proto.RegisterType((*BoxUserInfo)(nil), "ga.channel_roleplay_logic.BoxUserInfo")
	proto.RegisterType((*GetBoxInfosByLimitResp)(nil), "ga.channel_roleplay_logic.GetBoxInfosByLimitResp")
	proto.RegisterType((*GetChannelUserRoleListReq)(nil), "ga.channel_roleplay_logic.GetChannelUserRoleListReq")
	proto.RegisterType((*GetChannelUserRoleListResp)(nil), "ga.channel_roleplay_logic.GetChannelUserRoleListResp")
	proto.RegisterType((*UpsertBoxInfoReq)(nil), "ga.channel_roleplay_logic.UpsertBoxInfoReq")
	proto.RegisterType((*UpsertBoxInfoResp)(nil), "ga.channel_roleplay_logic.UpsertBoxInfoResp")
	proto.RegisterType((*DelBoxInfoReq)(nil), "ga.channel_roleplay_logic.DelBoxInfoReq")
	proto.RegisterType((*DelBoxInfoResp)(nil), "ga.channel_roleplay_logic.DelBoxInfoResp")
	proto.RegisterType((*OpenCommonMicReq)(nil), "ga.channel_roleplay_logic.OpenCommonMicReq")
	proto.RegisterType((*OpenCommonMicResp)(nil), "ga.channel_roleplay_logic.OpenCommonMicResp")
	proto.RegisterType((*OpenCommonMicBC)(nil), "ga.channel_roleplay_logic.OpenCommonMicBC")
	proto.RegisterType((*BoxChangeNotify)(nil), "ga.channel_roleplay_logic.BoxChangeNotify")
	proto.RegisterEnum("ga.channel_roleplay_logic.RoleType", RoleType_name, RoleType_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.BoxType", BoxType_name, BoxType_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.BoxNotifyType", BoxNotifyType_name, BoxNotifyType_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.BoxNotifyUserOpType", BoxNotifyUserOpType_name, BoxNotifyUserOpType_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.ApplyBoxType", ApplyBoxType_name, ApplyBoxType_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.AudioBoxId", AudioBoxId_name, AudioBoxId_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.ChannelUserRoleNotify_AuditResult", ChannelUserRoleNotify_AuditResult_name, ChannelUserRoleNotify_AuditResult_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.BoxChangeNotify_Op", BoxChangeNotify_Op_name, BoxChangeNotify_Op_value)
	proto.RegisterEnum("ga.channel_roleplay_logic.BoxChangeNotify_Result", BoxChangeNotify_Result_name, BoxChangeNotify_Result_value)
}

func init() {
	proto.RegisterFile("channel-roleplay-logic_.proto", fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62)
}

var fileDescriptor_channel_roleplay_logic__ae28c50b1f5a6d62 = []byte{
	// 1817 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x58, 0x4f, 0x6f, 0x23, 0x49,
	0x15, 0x4f, 0xbb, 0x6d, 0xc7, 0x7e, 0xb6, 0x93, 0x4e, 0xe5, 0xcf, 0x78, 0x66, 0x59, 0x91, 0x6d,
	0xc4, 0x10, 0x22, 0x8d, 0x07, 0x66, 0x41, 0x48, 0xc0, 0x22, 0xec, 0xcc, 0x6a, 0x36, 0x62, 0x1c,
	0xef, 0xf6, 0x66, 0x10, 0xe2, 0x62, 0xb5, 0xbb, 0x6b, 0xbc, 0x05, 0xed, 0xae, 0x1a, 0x77, 0x35,
	0xd8, 0x67, 0x2e, 0x9c, 0xd0, 0x1e, 0x38, 0xf3, 0x2d, 0xb8, 0xb0, 0x07, 0xae, 0x70, 0x40, 0xe2,
	0x33, 0x70, 0xe2, 0x33, 0x70, 0x42, 0xf5, 0xaa, 0xba, 0xdd, 0x76, 0x12, 0x4f, 0x3c, 0x9b, 0x9d,
	0xc3, 0xde, 0xaa, 0x5e, 0xfd, 0x79, 0xef, 0xfd, 0xea, 0xf7, 0x5e, 0xd5, 0x2b, 0x78, 0x37, 0xf8,
	0xcc, 0x8f, 0x63, 0x1a, 0x3d, 0x9a, 0xf2, 0x88, 0x8a, 0xc8, 0x9f, 0x3f, 0x8a, 0xf8, 0x98, 0x05,
	0xc3, 0x8e, 0x98, 0x72, 0xc9, 0xc9, 0xfd, 0xb1, 0xdf, 0x31, 0x33, 0x86, 0xd9, 0x8c, 0x21, 0xce,
	0x78, 0xd0, 0x1a, 0xfb, 0xc3, 0x91, 0x9f, 0x50, 0x3d, 0xd3, 0x65, 0x70, 0xfc, 0x8c, 0xca, 0x33,
	0x3d, 0xf7, 0x23, 0x1e, 0x85, 0x7d, 0x16, 0xbc, 0x48, 0xe8, 0xd4, 0xe3, 0x11, 0x7d, 0xce, 0x12,
	0xe9, 0xd1, 0x57, 0xe4, 0x21, 0xd4, 0xd4, 0x8a, 0xe1, 0x94, 0xbe, 0x6a, 0x5b, 0xc7, 0xd6, 0x49,
	0xe3, 0x49, 0xa3, 0x33, 0xf6, 0x3b, 0x3d, 0x3f, 0xa1, 0x1e, 0x7d, 0xe5, 0x6d, 0x8f, 0x74, 0x83,
	0xbc, 0x0b, 0x90, 0x29, 0x65, 0x61, 0xbb, 0x74, 0x6c, 0x9d, 0xb4, 0xbc, 0xba, 0x91, 0x9c, 0x87,
	0xee, 0xaf, 0xa0, 0x96, 0xed, 0x4c, 0x1c, 0xb0, 0x53, 0x16, 0xe2, 0x6e, 0x2d, 0x4f, 0x35, 0xc9,
	0x3b, 0x50, 0x57, 0x96, 0x0e, 0x63, 0x7f, 0x42, 0x71, 0x6d, 0xdd, 0xab, 0x29, 0xc1, 0x85, 0x3f,
	0xa1, 0x6a, 0xe7, 0x54, 0x84, 0xbe, 0xa4, 0xe1, 0xd0, 0x97, 0x6d, 0x5b, 0xef, 0x6c, 0x24, 0x5d,
	0xe9, 0x7e, 0x6e, 0xc1, 0x7b, 0xaf, 0xf1, 0x22, 0x11, 0xe4, 0xbb, 0x50, 0x37, 0x6e, 0x24, 0xc2,
	0xf8, 0xd1, 0x5c, 0xf8, 0x91, 0x08, 0xaf, 0x36, 0x32, 0x2d, 0xf2, 0x73, 0x63, 0x4c, 0xc4, 0x12,
	0xd9, 0x2e, 0x1d, 0xdb, 0x27, 0x8d, 0x27, 0xdf, 0xea, 0xdc, 0x88, 0x69, 0x27, 0x53, 0xa5, 0x2d,
	0x56, 0x0a, 0xdd, 0x2f, 0x2c, 0xd8, 0xff, 0x94, 0xca, 0xfe, 0xdc, 0x18, 0x85, 0xc3, 0x77, 0x86,
	0xe5, 0x32, 0x5a, 0xf6, 0x0a, 0x5a, 0x99, 0xf5, 0x72, 0x2e, 0x68, 0xbb, 0x7c, 0x6c, 0x9d, 0xec,
	0xac, 0xb5, 0x5e, 0x99, 0x76, 0x39, 0x17, 0xc6, 0x7a, 0xd5, 0x72, 0xbb, 0x70, 0x70, 0xd5, 0xf8,
	0x8d, 0x20, 0x74, 0x3f, 0x2f, 0xc1, 0xa1, 0x59, 0x9e, 0xc1, 0x73, 0xc1, 0x25, 0x7b, 0x39, 0x27,
	0x3f, 0x82, 0xb2, 0x52, 0x64, 0xd6, 0xdf, 0x0a, 0x57, 0x5c, 0x40, 0x2e, 0xa1, 0x3a, 0xa5, 0x49,
	0x1a, 0x49, 0xc4, 0x63, 0xe7, 0xc9, 0x4f, 0xd7, 0x2c, 0xbd, 0x56, 0x75, 0xa7, 0x9b, 0x86, 0x4c,
	0x91, 0x21, 0x8d, 0xa4, 0x67, 0xf6, 0x72, 0x3f, 0x83, 0x46, 0x41, 0x4c, 0x8e, 0x80, 0x14, 0xba,
	0x4f, 0xe9, 0x4b, 0x3f, 0x8d, 0xa4, 0xb3, 0x45, 0xf6, 0x61, 0xb7, 0x20, 0xff, 0xd8, 0x4f, 0x12,
	0xc7, 0x22, 0x87, 0xb0, 0x57, 0xdc, 0x92, 0xfe, 0x86, 0x06, 0xd2, 0x29, 0x5d, 0x11, 0xff, 0x8e,
	0xd1, 0xdf, 0x3b, 0xb6, 0xfb, 0x0c, 0x0e, 0x0a, 0x80, 0xaa, 0xe6, 0x98, 0xf6, 0x93, 0x31, 0x21,
	0x50, 0x96, 0x74, 0x26, 0x11, 0x90, 0xba, 0x87, 0xed, 0xb5, 0xe1, 0xe0, 0xfe, 0xd3, 0x82, 0xc6,
	0x87, 0xb1, 0xa4, 0xd3, 0x1e, 0x9f, 0x6d, 0x42, 0xaa, 0x6f, 0x40, 0x46, 0xa1, 0x2b, 0x9c, 0x62,
	0x21, 0x39, 0x80, 0xca, 0x88, 0xcf, 0x58, 0x68, 0xe2, 0x4b, 0x77, 0xc8, 0x07, 0x50, 0x1b, 0xf1,
	0x59, 0x91, 0x4b, 0xee, 0x1a, 0xd8, 0x7b, 0x7c, 0x86, 0x54, 0xda, 0x1e, 0xe9, 0x06, 0xf9, 0x26,
	0x34, 0xfc, 0x34, 0x64, 0x7c, 0xa8, 0xb7, 0xae, 0xe0, 0xd6, 0x80, 0xa2, 0x9e, 0x92, 0xb8, 0x12,
	0x9a, 0x0b, 0x57, 0x36, 0x8b, 0xd2, 0x43, 0xa8, 0x72, 0x21, 0x87, 0x32, 0x41, 0x5f, 0xca, 0x5e,
	0x85, 0x0b, 0x79, 0x99, 0x10, 0x17, 0x5a, 0x2c, 0x19, 0xc6, 0x54, 0x25, 0x0b, 0x21, 0xa2, 0x39,
	0xfa, 0x53, 0xf3, 0x1a, 0x2c, 0xb9, 0xa0, 0x34, 0xec, 0x2a, 0x91, 0xfb, 0x0f, 0x0b, 0xe0, 0xc3,
	0x19, 0x93, 0x5f, 0x03, 0x00, 0x07, 0xd0, 0xc8, 0x3d, 0xb9, 0x0b, 0xfc, 0xdc, 0xbf, 0x94, 0x00,
	0x06, 0x82, 0xf6, 0xf8, 0xec, 0x3c, 0x7e, 0xc9, 0xc9, 0x39, 0xd4, 0xb8, 0x30, 0xc9, 0xc4, 0x42,
	0xfb, 0x3b, 0xeb, 0xed, 0xd7, 0xb1, 0xa6, 0x22, 0x6f, 0x20, 0xb4, 0x2f, 0x5c, 0x60, 0x5a, 0x59,
	0x00, 0x54, 0x2a, 0x02, 0x64, 0xee, 0x02, 0x7b, 0x71, 0x17, 0xdc, 0x03, 0xe5, 0xfe, 0x30, 0x88,
	0x25, 0x22, 0xd6, 0xf2, 0xaa, 0x23, 0x3e, 0x3b, 0x8b, 0x25, 0x39, 0x87, 0x46, 0x8c, 0xbb, 0x6b,
	0x73, 0x2a, 0x68, 0xce, 0xc9, 0x6d, 0xcc, 0x41, 0x43, 0x20, 0xce, 0xdb, 0x05, 0xe7, 0xab, 0x2b,
	0xe4, 0xc9, 0xe1, 0x1e, 0xb2, 0x30, 0x69, 0x6f, 0x1f, 0xdb, 0x27, 0x2d, 0xaf, 0x91, 0x01, 0x7e,
	0x1e, 0x26, 0xee, 0x1f, 0x4b, 0xd0, 0xd4, 0x00, 0x99, 0x8c, 0xf6, 0x0c, 0x9a, 0x0a, 0x22, 0x5c,
	0x12, 0xbf, 0xe4, 0x6d, 0x0b, 0x6f, 0x8c, 0x6f, 0xaf, 0xb1, 0x6b, 0x81, 0xaf, 0x07, 0x7c, 0x81,
	0xf5, 0x6b, 0xb2, 0xbe, 0x03, 0xf6, 0x24, 0x19, 0x9b, 0x7c, 0xaf, 0x9a, 0xcb, 0x69, 0xa2, 0xbc,
	0x72, 0x0f, 0xb8, 0xd0, 0x52, 0x66, 0x2d, 0x26, 0x54, 0x70, 0x42, 0x83, 0x0b, 0xea, 0x65, 0x73,
	0x8a, 0xec, 0xac, 0x6e, 0xcc, 0x4e, 0xf7, 0x0f, 0x16, 0x34, 0x30, 0xa2, 0x0c, 0x12, 0xcb, 0x0e,
	0x58, 0xab, 0x0e, 0xdc, 0x96, 0x00, 0xc6, 0xd1, 0xf2, 0x0d, 0x8e, 0x56, 0x56, 0xf2, 0xe1, 0xbf,
	0x2d, 0xd8, 0xfb, 0xc8, 0x8f, 0xc3, 0x88, 0xa2, 0x2d, 0x6f, 0x21, 0xa8, 0xfb, 0xb0, 0x83, 0xb9,
	0x65, 0xb8, 0x12, 0xda, 0xdf, 0x59, 0x03, 0x5e, 0x66, 0x1b, 0x22, 0xd8, 0xf4, 0x0b, 0xbd, 0x0c,
	0x81, 0x4a, 0x8e, 0x80, 0xfb, 0x09, 0x90, 0x55, 0x8f, 0x36, 0x0b, 0x6e, 0x03, 0x61, 0x29, 0x87,
	0xd0, 0xfd, 0x9f, 0x05, 0xdb, 0x19, 0xd1, 0x96, 0x7c, 0xb6, 0x6e, 0xf4, 0x79, 0xe9, 0x98, 0x8e,
	0xa1, 0xa9, 0xbc, 0x4d, 0x13, 0x3a, 0xc5, 0xd0, 0xd4, 0x80, 0xc0, 0x88, 0xcf, 0x54, 0xac, 0xab,
	0xf0, 0xbc, 0xaf, 0xc9, 0x54, 0x20, 0xa3, 0x22, 0xca, 0x15, 0x9e, 0x55, 0x36, 0xcf, 0x82, 0xf7,
	0x60, 0x7b, 0xc2, 0x82, 0x61, 0xe0, 0x0b, 0x64, 0x69, 0xcb, 0xab, 0x4e, 0x58, 0x70, 0xe6, 0x0b,
	0xf2, 0x10, 0x76, 0x45, 0x3a, 0x8a, 0x58, 0x30, 0x54, 0xe3, 0xf8, 0x5e, 0xd3, 0x11, 0xdb, 0xd2,
	0xe2, 0x3e, 0x0b, 0xf0, 0x3d, 0xf6, 0x67, 0x0b, 0x5a, 0xcf, 0xa8, 0xcc, 0x82, 0xee, 0xce, 0xe8,
	0x51, 0xf4, 0xcb, 0xde, 0x3c, 0x7e, 0xfe, 0x54, 0x82, 0x9d, 0xa2, 0x59, 0x9b, 0x9d, 0xb1, 0x51,
	0x8e, 0x39, 0x47, 0xbf, 0x52, 0x5f, 0xa3, 0x1c, 0x95, 0x28, 0xe5, 0x59, 0xb6, 0xc1, 0xc3, 0x2c,
	0xf2, 0xbb, 0xae, 0x24, 0x78, 0xb1, 0xa8, 0x90, 0x53, 0x98, 0x4a, 0x2e, 0xfd, 0xc8, 0xe4, 0xe1,
	0xda, 0x84, 0x05, 0x97, 0xaa, 0x4f, 0x4e, 0x61, 0x0f, 0xd7, 0x16, 0xee, 0xa6, 0xa4, 0x5d, 0x41,
	0xe4, 0x77, 0xd5, 0x40, 0x37, 0xbf, 0xa0, 0x12, 0x35, 0x77, 0xe2, 0xb3, 0x78, 0x18, 0xf0, 0xc9,
	0x84, 0xc7, 0x46, 0x9d, 0x3e, 0xc6, 0x5d, 0x35, 0x70, 0x86, 0x72, 0x7d, 0x9b, 0xfd, 0xd7, 0x82,
	0xc3, 0x05, 0x20, 0x49, 0x6f, 0xfe, 0x9c, 0x4d, 0x98, 0xfc, 0xaa, 0xc3, 0xf9, 0x1d, 0xa8, 0x47,
	0x7e, 0x22, 0x87, 0x92, 0x19, 0xe6, 0xda, 0x5e, 0x4d, 0x09, 0x2e, 0xd9, 0x04, 0xb9, 0x37, 0xa6,
	0x12, 0x29, 0x5f, 0xc1, 0xa1, 0xea, 0x98, 0x4a, 0x45, 0xf7, 0x2f, 0x99, 0x3b, 0x23, 0x68, 0xf4,
	0x74, 0xec, 0xe0, 0x69, 0x5c, 0x2d, 0x89, 0x8e, 0xa0, 0xea, 0x07, 0x41, 0x1a, 0x4b, 0x13, 0xc5,
	0xa6, 0xa7, 0xac, 0x8d, 0x59, 0xf0, 0xdb, 0xa5, 0xc7, 0xbf, 0x12, 0x60, 0xa0, 0x1d, 0x41, 0x75,
	0x4c, 0xe3, 0x90, 0x4e, 0xd1, 0x8f, 0x8a, 0x67, 0x7a, 0xee, 0x7f, 0x2c, 0x38, 0xba, 0x0e, 0xd8,
	0xcd, 0x18, 0xf7, 0x1c, 0x76, 0xf2, 0x1c, 0xa0, 0x68, 0x97, 0x18, 0xde, 0x3d, 0x5c, 0xef, 0x78,
	0xe6, 0xa4, 0xd7, 0x1c, 0x2d, 0x3a, 0xc9, 0xf2, 0x51, 0xd9, 0x37, 0x1e, 0x55, 0xf9, 0xc6, 0xa3,
	0xaa, 0x2c, 0x1f, 0x95, 0xfb, 0x77, 0x0b, 0xee, 0x2f, 0x0a, 0xc1, 0xaf, 0xa6, 0x8e, 0x55, 0x59,
	0x2e, 0x65, 0xa1, 0xce, 0x35, 0x36, 0x32, 0x7e, 0x3b, 0x65, 0xa1, 0x52, 0x72, 0x07, 0x95, 0xd7,
	0xbf, 0x2c, 0x78, 0x70, 0x93, 0x07, 0x6f, 0xb9, 0x86, 0x25, 0x3d, 0xc0, 0x77, 0xa6, 0xc4, 0xa9,
	0x78, 0x3e, 0xb7, 0xdc, 0xa2, 0x8e, 0xcb, 0x54, 0xd3, 0x15, 0xe0, 0xbc, 0x10, 0x09, 0x9d, 0xbe,
	0x49, 0xe6, 0xfd, 0x01, 0xd8, 0x23, 0x3e, 0xc3, 0x03, 0xb8, 0x5d, 0x66, 0x53, 0xd3, 0x5d, 0x09,
	0x7b, 0x2b, 0x1a, 0x37, 0xc3, 0xed, 0xcd, 0xb4, 0x4e, 0xa0, 0xf5, 0x94, 0x46, 0x6f, 0xe0, 0xe4,
	0x6b, 0xc8, 0x76, 0x08, 0x55, 0xfd, 0x12, 0x2d, 0x24, 0xac, 0xf3, 0xd0, 0xfd, 0x09, 0xec, 0x14,
	0xd5, 0x6d, 0x56, 0x9a, 0x7f, 0x61, 0x81, 0x33, 0x10, 0xd4, 0xe4, 0xdd, 0x3e, 0x0b, 0xee, 0xd0,
	0xde, 0x2f, 0x77, 0x1f, 0x92, 0xf7, 0xa0, 0xb9, 0x74, 0x4b, 0xe8, 0xd0, 0x6f, 0x04, 0x85, 0x1b,
	0xe2, 0x67, 0xb0, 0xb7, 0x62, 0xfc, 0x66, 0xde, 0xff, 0xcd, 0x82, 0xdd, 0xa5, 0x0d, 0x7a, 0x67,
	0x57, 0xd4, 0x5a, 0x57, 0xd4, 0xaa, 0x5b, 0x40, 0x3d, 0xa6, 0xd3, 0xdc, 0xe9, 0x2a, 0x17, 0xf4,
	0x05, 0x0b, 0x57, 0x00, 0xb1, 0xaf, 0xf9, 0xa9, 0xc1, 0x6c, 0x59, 0x7c, 0xa1, 0x2b, 0x01, 0x26,
	0xeb, 0xa5, 0x4c, 0x5e, 0x59, 0xc9, 0xe4, 0x04, 0xca, 0x5c, 0x98, 0xfa, 0xc4, 0xf6, 0xb0, 0xed,
	0xfe, 0xd5, 0x86, 0xdd, 0x1e, 0x9f, 0xe9, 0xbf, 0x03, 0xf3, 0xe6, 0xfe, 0x00, 0x4a, 0x5c, 0x98,
	0xd2, 0xec, 0xd1, 0x7a, 0xb0, 0x8b, 0xeb, 0x3a, 0x03, 0xe1, 0x95, 0xf8, 0x1b, 0xf2, 0x3d, 0xf3,
	0x7a, 0xbc, 0xf8, 0x91, 0x2b, 0x6b, 0xaf, 0xc7, 0x34, 0xec, 0xca, 0xac, 0x82, 0x4b, 0xf3, 0x23,
	0x54, 0x2c, 0x56, 0x68, 0x9d, 0xe7, 0x7f, 0x38, 0xfa, 0x15, 0xf8, 0xfd, 0x0d, 0x0c, 0x5e, 0xfe,
	0xb8, 0x51, 0x26, 0xd0, 0x19, 0x4b, 0x64, 0xa2, 0x0e, 0x0d, 0x51, 0xaa, 0x79, 0x75, 0x2d, 0xe9,
	0xf1, 0x99, 0x3b, 0x80, 0xd2, 0x40, 0x10, 0x80, 0xea, 0x40, 0x5c, 0xf0, 0x98, 0x3a, 0x5b, 0xa4,
	0x09, 0xb5, 0x81, 0x38, 0x9b, 0x52, 0x5f, 0x52, 0xc7, 0xd2, 0xbd, 0xa7, 0x34, 0xa2, 0x92, 0x3a,
	0x25, 0xb2, 0x0b, 0x8d, 0x81, 0x78, 0x81, 0x3f, 0x8a, 0x7d, 0x16, 0x38, 0x36, 0x71, 0x54, 0x8d,
	0xa7, 0x05, 0xea, 0x34, 0x9c, 0xb2, 0xfb, 0x63, 0xa8, 0x9a, 0x3f, 0xa2, 0x1d, 0x00, 0xdd, 0x32,
	0x1b, 0xe7, 0x7d, 0xf3, 0x2d, 0xe4, 0x40, 0x73, 0xf9, 0x47, 0xe8, 0xf4, 0x0c, 0x6a, 0x59, 0xb2,
	0x57, 0xaa, 0xb2, 0xf6, 0x05, 0x8b, 0x9c, 0x2d, 0x72, 0x00, 0x4e, 0x7e, 0x13, 0xf0, 0x88, 0x7e,
	0x1c, 0xf9, 0x73, 0xb3, 0x89, 0x91, 0xfe, 0x92, 0x26, 0x6a, 0x93, 0xef, 0xe1, 0xfb, 0x1d, 0xf7,
	0xd8, 0x47, 0x1a, 0x64, 0x2b, 0x14, 0x66, 0xce, 0x96, 0x5a, 0x61, 0x84, 0x7d, 0x1a, 0x51, 0xea,
	0x58, 0xa7, 0x9f, 0x40, 0x6b, 0xa9, 0x02, 0x56, 0x96, 0x2e, 0x0a, 0x73, 0x67, 0x8b, 0xdc, 0x83,
	0x7d, 0x2c, 0x30, 0xba, 0x41, 0x40, 0x85, 0x54, 0x43, 0x38, 0x60, 0xe5, 0x03, 0xda, 0x83, 0x7c,
	0xa0, 0x74, 0x3a, 0x85, 0xfd, 0x6b, 0x6a, 0x7c, 0xd2, 0x82, 0x3a, 0x7e, 0xe3, 0x2c, 0xa0, 0xce,
	0x7e, 0x75, 0x1c, 0x8b, 0x34, 0x60, 0xdb, 0x7c, 0x51, 0x38, 0x25, 0xd2, 0x86, 0x03, 0x3d, 0x34,
	0xe5, 0x7e, 0x18, 0xf8, 0x89, 0x54, 0x21, 0xc8, 0x67, 0x8e, 0xad, 0x74, 0xe2, 0xb4, 0x95, 0x81,
	0xf2, 0x69, 0x0f, 0x9a, 0xdd, 0xe5, 0x72, 0x49, 0xf7, 0x17, 0xbf, 0x73, 0xbb, 0xa6, 0x0c, 0xd5,
	0x7e, 0x38, 0x56, 0x2e, 0xc8, 0x4f, 0x20, 0x05, 0xe8, 0xe6, 0x35, 0xbc, 0x32, 0x17, 0x1b, 0xc6,
	0xdc, 0xac, 0xfb, 0x0b, 0x4a, 0x85, 0x63, 0x91, 0xfb, 0x70, 0xf8, 0x69, 0x3a, 0x32, 0x77, 0x70,
	0xfe, 0x3a, 0x3d, 0x0f, 0x9d, 0x12, 0x79, 0x00, 0x47, 0x7d, 0xf5, 0x64, 0xbd, 0x3a, 0x66, 0x13,
	0x02, 0x3b, 0x68, 0xfb, 0x99, 0x9f, 0x48, 0x2d, 0x2b, 0xf7, 0x06, 0xd0, 0x0e, 0xf8, 0xa4, 0x33,
	0x67, 0x73, 0x9e, 0x2a, 0xaa, 0x4f, 0x78, 0x48, 0x23, 0xfd, 0xf7, 0xfe, 0xeb, 0xf7, 0xc7, 0x3c,
	0xf2, 0xe3, 0x71, 0xe7, 0x87, 0x4f, 0xa4, 0xec, 0x04, 0x7c, 0xf2, 0x18, 0xc5, 0x01, 0x8f, 0x1e,
	0xfb, 0x42, 0x3c, 0xbe, 0xfe, 0x87, 0x7f, 0x54, 0xc5, 0x49, 0xef, 0xff, 0x3f, 0x00, 0x00, 0xff,
	0xff, 0xd0, 0x2b, 0x51, 0x94, 0x02, 0x18, 0x00, 0x00,
}
