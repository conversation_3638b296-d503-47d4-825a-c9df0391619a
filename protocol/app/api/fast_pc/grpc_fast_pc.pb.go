// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/fast_pc_logic/grpc_fast_pc.proto

package fast_pc // import "golang.52tt.com/protocol/app/api/fast_pc"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import fast_pc_logic "golang.52tt.com/protocol/app/fast-pc-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// FastPcLogicClient is the client API for FastPcLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FastPcLogicClient interface {
	// 获取工具箱入口
	GetToolBoxEntrance(ctx context.Context, in *fast_pc_logic.GetToolBoxEntranceReq, opts ...grpc.CallOption) (*fast_pc_logic.GetToolBoxEntranceResp, error)
	// 获取模组分类列表
	GetModTagList(ctx context.Context, in *fast_pc_logic.GetModTagListReq, opts ...grpc.CallOption) (*fast_pc_logic.GetModTagListResp, error)
	// 获取模组列表
	GetModList(ctx context.Context, in *fast_pc_logic.GetModListReq, opts ...grpc.CallOption) (*fast_pc_logic.GetModListResp, error)
}

type fastPcLogicClient struct {
	cc *grpc.ClientConn
}

func NewFastPcLogicClient(cc *grpc.ClientConn) FastPcLogicClient {
	return &fastPcLogicClient{cc}
}

func (c *fastPcLogicClient) GetToolBoxEntrance(ctx context.Context, in *fast_pc_logic.GetToolBoxEntranceReq, opts ...grpc.CallOption) (*fast_pc_logic.GetToolBoxEntranceResp, error) {
	out := new(fast_pc_logic.GetToolBoxEntranceResp)
	err := c.cc.Invoke(ctx, "/ga.api.fast_pc_logic.FastPcLogic/GetToolBoxEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fastPcLogicClient) GetModTagList(ctx context.Context, in *fast_pc_logic.GetModTagListReq, opts ...grpc.CallOption) (*fast_pc_logic.GetModTagListResp, error) {
	out := new(fast_pc_logic.GetModTagListResp)
	err := c.cc.Invoke(ctx, "/ga.api.fast_pc_logic.FastPcLogic/GetModTagList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fastPcLogicClient) GetModList(ctx context.Context, in *fast_pc_logic.GetModListReq, opts ...grpc.CallOption) (*fast_pc_logic.GetModListResp, error) {
	out := new(fast_pc_logic.GetModListResp)
	err := c.cc.Invoke(ctx, "/ga.api.fast_pc_logic.FastPcLogic/GetModList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FastPcLogicServer is the server API for FastPcLogic service.
type FastPcLogicServer interface {
	// 获取工具箱入口
	GetToolBoxEntrance(context.Context, *fast_pc_logic.GetToolBoxEntranceReq) (*fast_pc_logic.GetToolBoxEntranceResp, error)
	// 获取模组分类列表
	GetModTagList(context.Context, *fast_pc_logic.GetModTagListReq) (*fast_pc_logic.GetModTagListResp, error)
	// 获取模组列表
	GetModList(context.Context, *fast_pc_logic.GetModListReq) (*fast_pc_logic.GetModListResp, error)
}

func RegisterFastPcLogicServer(s *grpc.Server, srv FastPcLogicServer) {
	s.RegisterService(&_FastPcLogic_serviceDesc, srv)
}

func _FastPcLogic_GetToolBoxEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(fast_pc_logic.GetToolBoxEntranceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FastPcLogicServer).GetToolBoxEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.fast_pc_logic.FastPcLogic/GetToolBoxEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FastPcLogicServer).GetToolBoxEntrance(ctx, req.(*fast_pc_logic.GetToolBoxEntranceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FastPcLogic_GetModTagList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(fast_pc_logic.GetModTagListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FastPcLogicServer).GetModTagList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.fast_pc_logic.FastPcLogic/GetModTagList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FastPcLogicServer).GetModTagList(ctx, req.(*fast_pc_logic.GetModTagListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FastPcLogic_GetModList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(fast_pc_logic.GetModListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FastPcLogicServer).GetModList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.fast_pc_logic.FastPcLogic/GetModList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FastPcLogicServer).GetModList(ctx, req.(*fast_pc_logic.GetModListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _FastPcLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.fast_pc_logic.FastPcLogic",
	HandlerType: (*FastPcLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetToolBoxEntrance",
			Handler:    _FastPcLogic_GetToolBoxEntrance_Handler,
		},
		{
			MethodName: "GetModTagList",
			Handler:    _FastPcLogic_GetModTagList_Handler,
		},
		{
			MethodName: "GetModList",
			Handler:    _FastPcLogic_GetModList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/fast_pc_logic/grpc_fast_pc.proto",
}

func init() {
	proto.RegisterFile("api/fast_pc_logic/grpc_fast_pc.proto", fileDescriptor_grpc_fast_pc_1ddc36b397883ce7)
}

var fileDescriptor_grpc_fast_pc_1ddc36b397883ce7 = []byte{
	// 297 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x49, 0x2c, 0xc8, 0xd4,
	0x4f, 0x4b, 0x2c, 0x2e, 0x89, 0x2f, 0x48, 0x8e, 0xcf, 0xc9, 0x4f, 0xcf, 0x4c, 0xd6, 0x4f, 0x2f,
	0x2a, 0x48, 0x8e, 0x87, 0x0a, 0xe9, 0x15, 0x14, 0xe5, 0x97, 0xe4, 0x0b, 0x89, 0xa4, 0x27, 0xea,
	0x25, 0x16, 0x64, 0xea, 0xa1, 0x28, 0x94, 0x52, 0x44, 0xd5, 0x87, 0xc2, 0x83, 0x68, 0x94, 0x92,
	0x05, 0x19, 0x9f, 0x5a, 0x51, 0x92, 0x9a, 0x57, 0x9c, 0x99, 0x9f, 0x87, 0x60, 0x41, 0xa4, 0x8d,
	0xae, 0x32, 0x71, 0x71, 0xbb, 0x25, 0x16, 0x97, 0x04, 0x24, 0xfb, 0x80, 0x34, 0x09, 0x15, 0x70,
	0x09, 0xb9, 0xa7, 0x96, 0x84, 0xe4, 0xe7, 0xe7, 0x38, 0xe5, 0x57, 0xb8, 0xe6, 0x95, 0x14, 0x25,
	0xe6, 0x25, 0xa7, 0x0a, 0xa9, 0xeb, 0xa5, 0x27, 0xa2, 0x5a, 0xad, 0x87, 0xa9, 0x2a, 0x28, 0xb5,
	0x50, 0x4a, 0x83, 0x38, 0x85, 0xc5, 0x05, 0x4a, 0xec, 0x4d, 0x0d, 0x0a, 0xcc, 0x1c, 0x6f, 0xcd,
	0x84, 0x12, 0xb9, 0x78, 0xdd, 0x53, 0x4b, 0x7c, 0xf3, 0x53, 0x42, 0x12, 0xd3, 0x7d, 0x32, 0x8b,
	0x4b, 0x84, 0x94, 0xb0, 0x9a, 0x81, 0x50, 0x00, 0xb2, 0x47, 0x99, 0xa0, 0x1a, 0xb8, 0x15, 0xef,
	0xcc, 0x84, 0x22, 0xb8, 0xb8, 0x20, 0xb2, 0x60, 0xf3, 0xe5, 0x71, 0xe9, 0x85, 0x19, 0xae, 0x80,
	0x5f, 0x01, 0xdc, 0xe4, 0xf7, 0x66, 0x52, 0xe2, 0x4d, 0x0d, 0x0a, 0xbc, 0x20, 0xa5, 0xba, 0x05,
	0xc9, 0xba, 0x60, 0xa5, 0x5d, 0x0d, 0x0a, 0x4c, 0xe9, 0xf9, 0x4e, 0x01, 0x5c, 0x62, 0xc9, 0xf9,
	0xb9, 0x7a, 0x85, 0xa5, 0xe5, 0x89, 0x79, 0x7a, 0x25, 0x25, 0x90, 0xd0, 0x06, 0xc5, 0x60, 0x94,
	0x41, 0x7a, 0x7e, 0x4e, 0x62, 0x5e, 0xba, 0x9e, 0xa9, 0x51, 0x49, 0x89, 0x5e, 0x72, 0x7e, 0xae,
	0x3e, 0x58, 0x2a, 0x39, 0x3f, 0x47, 0x3f, 0xb1, 0xa0, 0x40, 0x1f, 0x29, 0x31, 0x58, 0x43, 0xe9,
	0x45, 0x4c, 0xcc, 0x41, 0x01, 0xce, 0x49, 0x6c, 0x60, 0x75, 0xc6, 0x80, 0x00, 0x00, 0x00, 0xff,
	0xff, 0xdf, 0xbb, 0x50, 0xfc, 0x30, 0x02, 0x00, 0x00,
}
