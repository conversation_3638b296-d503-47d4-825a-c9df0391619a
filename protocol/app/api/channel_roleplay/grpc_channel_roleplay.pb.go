// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/channel_roleplay/grpc_channel_roleplay.proto

package channel_roleplay // import "golang.52tt.com/protocol/app/api/channel_roleplay"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import channel_roleplay_logic "golang.52tt.com/protocol/app/channel-roleplay-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelRoleplayLogicClient is the client API for ChannelRoleplayLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelRoleplayLogicClient interface {
	GetChannelHoldMicUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelHoldMicUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelHoldMicUserRoleListResp, error)
	SetMyChannelRole(ctx context.Context, in *channel_roleplay_logic.SetMyChannelRoleReq, opts ...grpc.CallOption) (*channel_roleplay_logic.SetMyChannelRoleResp, error)
	EnterBox(ctx context.Context, in *channel_roleplay_logic.EnterBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.EnterBoxResp, error)
	ExitBox(ctx context.Context, in *channel_roleplay_logic.ExitBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.ExitBoxResp, error)
	HandleApplyBox(ctx context.Context, in *channel_roleplay_logic.HandleApplyBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.HandleApplyBoxResp, error)
	GetBoxInfo(ctx context.Context, in *channel_roleplay_logic.GetBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfoResp, error)
	GetBoxInfosByLimit(ctx context.Context, in *channel_roleplay_logic.GetBoxInfosByLimitReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfosByLimitResp, error)
	GetChannelUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelUserRoleListResp, error)
	UpsertBoxInfo(ctx context.Context, in *channel_roleplay_logic.UpsertBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.UpsertBoxInfoResp, error)
	DelBoxInfo(ctx context.Context, in *channel_roleplay_logic.DelBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.DelBoxInfoResp, error)
	OpenCommonMic(ctx context.Context, in *channel_roleplay_logic.OpenCommonMicReq, opts ...grpc.CallOption) (*channel_roleplay_logic.OpenCommonMicResp, error)
}

type channelRoleplayLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelRoleplayLogicClient(cc *grpc.ClientConn) ChannelRoleplayLogicClient {
	return &channelRoleplayLogicClient{cc}
}

func (c *channelRoleplayLogicClient) GetChannelHoldMicUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelHoldMicUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelHoldMicUserRoleListResp, error) {
	out := new(channel_roleplay_logic.GetChannelHoldMicUserRoleListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelHoldMicUserRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) SetMyChannelRole(ctx context.Context, in *channel_roleplay_logic.SetMyChannelRoleReq, opts ...grpc.CallOption) (*channel_roleplay_logic.SetMyChannelRoleResp, error) {
	out := new(channel_roleplay_logic.SetMyChannelRoleResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/SetMyChannelRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) EnterBox(ctx context.Context, in *channel_roleplay_logic.EnterBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.EnterBoxResp, error) {
	out := new(channel_roleplay_logic.EnterBoxResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/EnterBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) ExitBox(ctx context.Context, in *channel_roleplay_logic.ExitBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.ExitBoxResp, error) {
	out := new(channel_roleplay_logic.ExitBoxResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/ExitBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) HandleApplyBox(ctx context.Context, in *channel_roleplay_logic.HandleApplyBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.HandleApplyBoxResp, error) {
	out := new(channel_roleplay_logic.HandleApplyBoxResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/HandleApplyBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) GetBoxInfo(ctx context.Context, in *channel_roleplay_logic.GetBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfoResp, error) {
	out := new(channel_roleplay_logic.GetBoxInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) GetBoxInfosByLimit(ctx context.Context, in *channel_roleplay_logic.GetBoxInfosByLimitReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfosByLimitResp, error) {
	out := new(channel_roleplay_logic.GetBoxInfosByLimitResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfosByLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) GetChannelUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelUserRoleListResp, error) {
	out := new(channel_roleplay_logic.GetChannelUserRoleListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelUserRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) UpsertBoxInfo(ctx context.Context, in *channel_roleplay_logic.UpsertBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.UpsertBoxInfoResp, error) {
	out := new(channel_roleplay_logic.UpsertBoxInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/UpsertBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) DelBoxInfo(ctx context.Context, in *channel_roleplay_logic.DelBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.DelBoxInfoResp, error) {
	out := new(channel_roleplay_logic.DelBoxInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/DelBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) OpenCommonMic(ctx context.Context, in *channel_roleplay_logic.OpenCommonMicReq, opts ...grpc.CallOption) (*channel_roleplay_logic.OpenCommonMicResp, error) {
	out := new(channel_roleplay_logic.OpenCommonMicResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/OpenCommonMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelRoleplayLogicServer is the server API for ChannelRoleplayLogic service.
type ChannelRoleplayLogicServer interface {
	GetChannelHoldMicUserRoleList(context.Context, *channel_roleplay_logic.GetChannelHoldMicUserRoleListReq) (*channel_roleplay_logic.GetChannelHoldMicUserRoleListResp, error)
	SetMyChannelRole(context.Context, *channel_roleplay_logic.SetMyChannelRoleReq) (*channel_roleplay_logic.SetMyChannelRoleResp, error)
	EnterBox(context.Context, *channel_roleplay_logic.EnterBoxReq) (*channel_roleplay_logic.EnterBoxResp, error)
	ExitBox(context.Context, *channel_roleplay_logic.ExitBoxReq) (*channel_roleplay_logic.ExitBoxResp, error)
	HandleApplyBox(context.Context, *channel_roleplay_logic.HandleApplyBoxReq) (*channel_roleplay_logic.HandleApplyBoxResp, error)
	GetBoxInfo(context.Context, *channel_roleplay_logic.GetBoxInfoReq) (*channel_roleplay_logic.GetBoxInfoResp, error)
	GetBoxInfosByLimit(context.Context, *channel_roleplay_logic.GetBoxInfosByLimitReq) (*channel_roleplay_logic.GetBoxInfosByLimitResp, error)
	GetChannelUserRoleList(context.Context, *channel_roleplay_logic.GetChannelUserRoleListReq) (*channel_roleplay_logic.GetChannelUserRoleListResp, error)
	UpsertBoxInfo(context.Context, *channel_roleplay_logic.UpsertBoxInfoReq) (*channel_roleplay_logic.UpsertBoxInfoResp, error)
	DelBoxInfo(context.Context, *channel_roleplay_logic.DelBoxInfoReq) (*channel_roleplay_logic.DelBoxInfoResp, error)
	OpenCommonMic(context.Context, *channel_roleplay_logic.OpenCommonMicReq) (*channel_roleplay_logic.OpenCommonMicResp, error)
}

func RegisterChannelRoleplayLogicServer(s *grpc.Server, srv ChannelRoleplayLogicServer) {
	s.RegisterService(&_ChannelRoleplayLogic_serviceDesc, srv)
}

func _ChannelRoleplayLogic_GetChannelHoldMicUserRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetChannelHoldMicUserRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetChannelHoldMicUserRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelHoldMicUserRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetChannelHoldMicUserRoleList(ctx, req.(*channel_roleplay_logic.GetChannelHoldMicUserRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_SetMyChannelRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.SetMyChannelRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).SetMyChannelRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/SetMyChannelRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).SetMyChannelRole(ctx, req.(*channel_roleplay_logic.SetMyChannelRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_EnterBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.EnterBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).EnterBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/EnterBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).EnterBox(ctx, req.(*channel_roleplay_logic.EnterBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_ExitBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.ExitBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).ExitBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/ExitBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).ExitBox(ctx, req.(*channel_roleplay_logic.ExitBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_HandleApplyBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.HandleApplyBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).HandleApplyBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/HandleApplyBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).HandleApplyBox(ctx, req.(*channel_roleplay_logic.HandleApplyBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_GetBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfo(ctx, req.(*channel_roleplay_logic.GetBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_GetBoxInfosByLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetBoxInfosByLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfosByLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfosByLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfosByLimit(ctx, req.(*channel_roleplay_logic.GetBoxInfosByLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_GetChannelUserRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetChannelUserRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetChannelUserRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelUserRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetChannelUserRoleList(ctx, req.(*channel_roleplay_logic.GetChannelUserRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_UpsertBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.UpsertBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).UpsertBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/UpsertBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).UpsertBoxInfo(ctx, req.(*channel_roleplay_logic.UpsertBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_DelBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.DelBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).DelBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/DelBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).DelBoxInfo(ctx, req.(*channel_roleplay_logic.DelBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_OpenCommonMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.OpenCommonMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).OpenCommonMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/OpenCommonMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).OpenCommonMic(ctx, req.(*channel_roleplay_logic.OpenCommonMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelRoleplayLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.channel_roleplay.ChannelRoleplayLogic",
	HandlerType: (*ChannelRoleplayLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelHoldMicUserRoleList",
			Handler:    _ChannelRoleplayLogic_GetChannelHoldMicUserRoleList_Handler,
		},
		{
			MethodName: "SetMyChannelRole",
			Handler:    _ChannelRoleplayLogic_SetMyChannelRole_Handler,
		},
		{
			MethodName: "EnterBox",
			Handler:    _ChannelRoleplayLogic_EnterBox_Handler,
		},
		{
			MethodName: "ExitBox",
			Handler:    _ChannelRoleplayLogic_ExitBox_Handler,
		},
		{
			MethodName: "HandleApplyBox",
			Handler:    _ChannelRoleplayLogic_HandleApplyBox_Handler,
		},
		{
			MethodName: "GetBoxInfo",
			Handler:    _ChannelRoleplayLogic_GetBoxInfo_Handler,
		},
		{
			MethodName: "GetBoxInfosByLimit",
			Handler:    _ChannelRoleplayLogic_GetBoxInfosByLimit_Handler,
		},
		{
			MethodName: "GetChannelUserRoleList",
			Handler:    _ChannelRoleplayLogic_GetChannelUserRoleList_Handler,
		},
		{
			MethodName: "UpsertBoxInfo",
			Handler:    _ChannelRoleplayLogic_UpsertBoxInfo_Handler,
		},
		{
			MethodName: "DelBoxInfo",
			Handler:    _ChannelRoleplayLogic_DelBoxInfo_Handler,
		},
		{
			MethodName: "OpenCommonMic",
			Handler:    _ChannelRoleplayLogic_OpenCommonMic_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/channel_roleplay/grpc_channel_roleplay.proto",
}

func init() {
	proto.RegisterFile("api/channel_roleplay/grpc_channel_roleplay.proto", fileDescriptor_grpc_channel_roleplay_0a7f372925f21657)
}

var fileDescriptor_grpc_channel_roleplay_0a7f372925f21657 = []byte{
	// 539 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x95, 0x4f, 0x8b, 0x13, 0x31,
	0x18, 0xc6, 0xe9, 0x56, 0x62, 0x89, 0x28, 0x12, 0xa4, 0x42, 0x71, 0xa1, 0x08, 0xae, 0x8a, 0xdb,
	0xcc, 0xba, 0xee, 0x9e, 0xd6, 0x83, 0xb6, 0x2e, 0xbb, 0x42, 0x8b, 0x52, 0xd9, 0x8b, 0x97, 0x92,
	0xce, 0xc6, 0x31, 0x98, 0x49, 0xb2, 0x33, 0x91, 0x6d, 0x0f, 0x42, 0x59, 0xff, 0x14, 0xbc, 0x08,
	0x7e, 0x02, 0x11, 0x8f, 0xf3, 0x25, 0xfc, 0xef, 0xc7, 0x92, 0x74, 0xda, 0x74, 0x3b, 0x94, 0xec,
	0xb4, 0xb7, 0xe1, 0x7d, 0x7f, 0xcf, 0xf3, 0xbc, 0x21, 0x93, 0x04, 0x6e, 0x10, 0xc5, 0x3c, 0xff,
	0x25, 0x11, 0x82, 0xf2, 0x4e, 0x24, 0x39, 0x55, 0x9c, 0xf4, 0xbd, 0x20, 0x52, 0x7e, 0x27, 0x5b,
	0xc5, 0x2a, 0x92, 0x5a, 0xa2, 0xab, 0x01, 0xc1, 0x44, 0x31, 0x9c, 0x6d, 0x57, 0xb6, 0xb2, 0x95,
	0x0e, 0x97, 0x01, 0xf3, 0x27, 0xee, 0xb5, 0x49, 0xb9, 0x36, 0x2a, 0x77, 0x52, 0xbb, 0xca, 0xaa,
	0x19, 0x80, 0xf6, 0x34, 0x15, 0x31, 0x93, 0x62, 0xfa, 0x95, 0xb6, 0x37, 0xbf, 0x5c, 0x80, 0x57,
	0x1a, 0xa9, 0x41, 0x7b, 0xac, 0x6f, 0x1a, 0x39, 0xfa, 0x56, 0x80, 0xab, 0x7b, 0x54, 0x8f, 0x7b,
	0xfb, 0x92, 0x1f, 0xb6, 0x98, 0x7f, 0x10, 0xd3, 0xc8, 0x60, 0x4d, 0x16, 0x6b, 0xb4, 0x83, 0x03,
	0x82, 0xe7, 0xcf, 0x84, 0x9d, 0xca, 0x36, 0x3d, 0xaa, 0xdc, 0x5f, 0x5e, 0x1c, 0xab, 0xeb, 0xa5,
	0x93, 0x41, 0xf5, 0x5c, 0xe9, 0x7b, 0x02, 0xd0, 0x1b, 0x78, 0xf9, 0x19, 0xd5, 0xad, 0xfe, 0xa9,
	0x35, 0x20, 0xec, 0xf0, 0xce, 0xc2, 0x66, 0x16, 0x6f, 0x21, 0xde, 0xc6, 0xff, 0x48, 0x00, 0xa2,
	0xb0, 0xb4, 0x2b, 0x34, 0x8d, 0xea, 0xb2, 0x87, 0xd6, 0x1c, 0x36, 0x13, 0xc8, 0xc4, 0xdd, 0xcc,
	0xc5, 0xd9, 0x98, 0x9f, 0x09, 0x40, 0x5d, 0x78, 0x7e, 0xb7, 0xc7, 0xb4, 0x49, 0xb9, 0xe1, 0x52,
	0xa7, 0x8c, 0x09, 0x59, 0xcb, 0x83, 0xd9, 0x8c, 0x5f, 0x09, 0x40, 0xc7, 0xf0, 0xd2, 0x3e, 0x11,
	0x87, 0x9c, 0x3e, 0x54, 0x8a, 0xf7, 0x4d, 0xd4, 0xba, 0xc3, 0x63, 0x16, 0x35, 0x89, 0xb5, 0x05,
	0x68, 0x1b, 0xfc, 0x3b, 0x01, 0xe8, 0x15, 0x84, 0x7b, 0xd4, 0x0c, 0xf4, 0x58, 0xbc, 0x90, 0xe8,
	0x96, 0xfb, 0xc7, 0x18, 0x63, 0x26, 0xf0, 0x76, 0x4e, 0xd2, 0x86, 0xfd, 0x49, 0x00, 0x7a, 0x5b,
	0x80, 0x68, 0xda, 0x8c, 0xeb, 0xfd, 0x26, 0x0b, 0x99, 0x46, 0x1b, 0xb9, 0xbc, 0x26, 0xb8, 0x49,
	0xbf, 0xbb, 0xa0, 0xc2, 0x4e, 0xf1, 0x37, 0x01, 0xe8, 0x53, 0x01, 0x96, 0xa7, 0x7f, 0xf9, 0xcc,
	0xa9, 0xda, 0xca, 0x75, 0x30, 0xb2, 0xc7, 0x69, 0x7b, 0x09, 0x95, 0x9d, 0xe8, 0x5f, 0x02, 0x90,
	0x86, 0x17, 0x0f, 0x54, 0x4c, 0x23, 0xbb, 0x0f, 0x77, 0x1c, 0x8e, 0x33, 0xa4, 0x89, 0x5f, 0xcf,
	0x0f, 0xdb, 0xd4, 0xf7, 0xc3, 0xa2, 0xd9, 0xfa, 0x47, 0x94, 0xe7, 0xd9, 0xfa, 0x29, 0x76, 0xd6,
	0xd6, 0x9f, 0x26, 0x6d, 0xd8, 0x87, 0x61, 0xd1, 0x2c, 0xf1, 0x89, 0xa2, 0xa2, 0x21, 0xc3, 0x50,
	0x8a, 0x16, 0xf3, 0x9d, 0x4b, 0x9c, 0x21, 0xcf, 0x5a, 0x62, 0x06, 0xb6, 0xa9, 0xef, 0x86, 0xc5,
	0xca, 0x83, 0x93, 0x41, 0xb5, 0x3c, 0xff, 0x8a, 0xfe, 0x38, 0xa8, 0xae, 0x04, 0xf2, 0xf3, 0xa0,
	0x7a, 0xcd, 0x4b, 0x7d, 0xe6, 0x5d, 0xc4, 0x5e, 0xbd, 0x0b, 0xcb, 0xbe, 0x0c, 0xf1, 0xd1, 0xeb,
	0x63, 0x22, 0xb0, 0xd6, 0xe9, 0xc5, 0x6d, 0x9e, 0x88, 0xe7, 0xf5, 0x40, 0x72, 0x22, 0x02, 0xbc,
	0xbd, 0xa9, 0x35, 0xf6, 0x65, 0xe8, 0x8d, 0x5a, 0xbe, 0xe4, 0x1e, 0x51, 0xca, 0x9b, 0xf7, 0xf2,
	0xec, 0x64, 0x0b, 0x5f, 0x57, 0x8a, 0xed, 0xa7, 0x8d, 0x2e, 0x18, 0x29, 0xef, 0xfd, 0x0f, 0x00,
	0x00, 0xff, 0xff, 0xf2, 0x26, 0x17, 0xe7, 0xaf, 0x06, 0x00, 0x00,
}
