// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/channel_roleplay/grpc_channel_roleplay.proto

package channel_roleplay // import "golang.52tt.com/protocol/app/api/channel_roleplay"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import channel_roleplay_logic "golang.52tt.com/protocol/app/channel-roleplay-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelRoleplayLogicClient is the client API for ChannelRoleplayLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelRoleplayLogicClient interface {
	GetChannelHoldMicUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelHoldMicUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelHoldMicUserRoleListResp, error)
	SetMyChannelRole(ctx context.Context, in *channel_roleplay_logic.SetMyChannelRoleReq, opts ...grpc.CallOption) (*channel_roleplay_logic.SetMyChannelRoleResp, error)
	EnterBox(ctx context.Context, in *channel_roleplay_logic.EnterBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.EnterBoxResp, error)
	ExitBox(ctx context.Context, in *channel_roleplay_logic.ExitBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.ExitBoxResp, error)
	HandleApplyBox(ctx context.Context, in *channel_roleplay_logic.HandleApplyBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.HandleApplyBoxResp, error)
	GetBoxInfo(ctx context.Context, in *channel_roleplay_logic.GetBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfoResp, error)
	GetBoxInfosByLimit(ctx context.Context, in *channel_roleplay_logic.GetBoxInfosByLimitReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfosByLimitResp, error)
	GetChannelUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelUserRoleListResp, error)
	UpsertBoxInfo(ctx context.Context, in *channel_roleplay_logic.UpsertBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.UpsertBoxInfoResp, error)
	DelBoxInfo(ctx context.Context, in *channel_roleplay_logic.DelBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.DelBoxInfoResp, error)
	OpenCommonMic(ctx context.Context, in *channel_roleplay_logic.OpenCommonMicReq, opts ...grpc.CallOption) (*channel_roleplay_logic.OpenCommonMicResp, error)
	GetMainChannelUserList(ctx context.Context, in *channel_roleplay_logic.GetMainChannelUserListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetMainChannelUserListResp, error)
}

type channelRoleplayLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelRoleplayLogicClient(cc *grpc.ClientConn) ChannelRoleplayLogicClient {
	return &channelRoleplayLogicClient{cc}
}

func (c *channelRoleplayLogicClient) GetChannelHoldMicUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelHoldMicUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelHoldMicUserRoleListResp, error) {
	out := new(channel_roleplay_logic.GetChannelHoldMicUserRoleListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelHoldMicUserRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) SetMyChannelRole(ctx context.Context, in *channel_roleplay_logic.SetMyChannelRoleReq, opts ...grpc.CallOption) (*channel_roleplay_logic.SetMyChannelRoleResp, error) {
	out := new(channel_roleplay_logic.SetMyChannelRoleResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/SetMyChannelRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) EnterBox(ctx context.Context, in *channel_roleplay_logic.EnterBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.EnterBoxResp, error) {
	out := new(channel_roleplay_logic.EnterBoxResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/EnterBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) ExitBox(ctx context.Context, in *channel_roleplay_logic.ExitBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.ExitBoxResp, error) {
	out := new(channel_roleplay_logic.ExitBoxResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/ExitBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) HandleApplyBox(ctx context.Context, in *channel_roleplay_logic.HandleApplyBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.HandleApplyBoxResp, error) {
	out := new(channel_roleplay_logic.HandleApplyBoxResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/HandleApplyBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) GetBoxInfo(ctx context.Context, in *channel_roleplay_logic.GetBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfoResp, error) {
	out := new(channel_roleplay_logic.GetBoxInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) GetBoxInfosByLimit(ctx context.Context, in *channel_roleplay_logic.GetBoxInfosByLimitReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfosByLimitResp, error) {
	out := new(channel_roleplay_logic.GetBoxInfosByLimitResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfosByLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) GetChannelUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelUserRoleListResp, error) {
	out := new(channel_roleplay_logic.GetChannelUserRoleListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelUserRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) UpsertBoxInfo(ctx context.Context, in *channel_roleplay_logic.UpsertBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.UpsertBoxInfoResp, error) {
	out := new(channel_roleplay_logic.UpsertBoxInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/UpsertBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) DelBoxInfo(ctx context.Context, in *channel_roleplay_logic.DelBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.DelBoxInfoResp, error) {
	out := new(channel_roleplay_logic.DelBoxInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/DelBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) OpenCommonMic(ctx context.Context, in *channel_roleplay_logic.OpenCommonMicReq, opts ...grpc.CallOption) (*channel_roleplay_logic.OpenCommonMicResp, error) {
	out := new(channel_roleplay_logic.OpenCommonMicResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/OpenCommonMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) GetMainChannelUserList(ctx context.Context, in *channel_roleplay_logic.GetMainChannelUserListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetMainChannelUserListResp, error) {
	out := new(channel_roleplay_logic.GetMainChannelUserListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetMainChannelUserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelRoleplayLogicServer is the server API for ChannelRoleplayLogic service.
type ChannelRoleplayLogicServer interface {
	GetChannelHoldMicUserRoleList(context.Context, *channel_roleplay_logic.GetChannelHoldMicUserRoleListReq) (*channel_roleplay_logic.GetChannelHoldMicUserRoleListResp, error)
	SetMyChannelRole(context.Context, *channel_roleplay_logic.SetMyChannelRoleReq) (*channel_roleplay_logic.SetMyChannelRoleResp, error)
	EnterBox(context.Context, *channel_roleplay_logic.EnterBoxReq) (*channel_roleplay_logic.EnterBoxResp, error)
	ExitBox(context.Context, *channel_roleplay_logic.ExitBoxReq) (*channel_roleplay_logic.ExitBoxResp, error)
	HandleApplyBox(context.Context, *channel_roleplay_logic.HandleApplyBoxReq) (*channel_roleplay_logic.HandleApplyBoxResp, error)
	GetBoxInfo(context.Context, *channel_roleplay_logic.GetBoxInfoReq) (*channel_roleplay_logic.GetBoxInfoResp, error)
	GetBoxInfosByLimit(context.Context, *channel_roleplay_logic.GetBoxInfosByLimitReq) (*channel_roleplay_logic.GetBoxInfosByLimitResp, error)
	GetChannelUserRoleList(context.Context, *channel_roleplay_logic.GetChannelUserRoleListReq) (*channel_roleplay_logic.GetChannelUserRoleListResp, error)
	UpsertBoxInfo(context.Context, *channel_roleplay_logic.UpsertBoxInfoReq) (*channel_roleplay_logic.UpsertBoxInfoResp, error)
	DelBoxInfo(context.Context, *channel_roleplay_logic.DelBoxInfoReq) (*channel_roleplay_logic.DelBoxInfoResp, error)
	OpenCommonMic(context.Context, *channel_roleplay_logic.OpenCommonMicReq) (*channel_roleplay_logic.OpenCommonMicResp, error)
	GetMainChannelUserList(context.Context, *channel_roleplay_logic.GetMainChannelUserListReq) (*channel_roleplay_logic.GetMainChannelUserListResp, error)
}

func RegisterChannelRoleplayLogicServer(s *grpc.Server, srv ChannelRoleplayLogicServer) {
	s.RegisterService(&_ChannelRoleplayLogic_serviceDesc, srv)
}

func _ChannelRoleplayLogic_GetChannelHoldMicUserRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetChannelHoldMicUserRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetChannelHoldMicUserRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelHoldMicUserRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetChannelHoldMicUserRoleList(ctx, req.(*channel_roleplay_logic.GetChannelHoldMicUserRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_SetMyChannelRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.SetMyChannelRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).SetMyChannelRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/SetMyChannelRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).SetMyChannelRole(ctx, req.(*channel_roleplay_logic.SetMyChannelRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_EnterBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.EnterBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).EnterBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/EnterBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).EnterBox(ctx, req.(*channel_roleplay_logic.EnterBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_ExitBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.ExitBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).ExitBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/ExitBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).ExitBox(ctx, req.(*channel_roleplay_logic.ExitBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_HandleApplyBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.HandleApplyBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).HandleApplyBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/HandleApplyBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).HandleApplyBox(ctx, req.(*channel_roleplay_logic.HandleApplyBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_GetBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfo(ctx, req.(*channel_roleplay_logic.GetBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_GetBoxInfosByLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetBoxInfosByLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfosByLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfosByLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfosByLimit(ctx, req.(*channel_roleplay_logic.GetBoxInfosByLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_GetChannelUserRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetChannelUserRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetChannelUserRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelUserRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetChannelUserRoleList(ctx, req.(*channel_roleplay_logic.GetChannelUserRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_UpsertBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.UpsertBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).UpsertBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/UpsertBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).UpsertBoxInfo(ctx, req.(*channel_roleplay_logic.UpsertBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_DelBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.DelBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).DelBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/DelBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).DelBoxInfo(ctx, req.(*channel_roleplay_logic.DelBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_OpenCommonMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.OpenCommonMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).OpenCommonMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/OpenCommonMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).OpenCommonMic(ctx, req.(*channel_roleplay_logic.OpenCommonMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_GetMainChannelUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetMainChannelUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetMainChannelUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_roleplay.ChannelRoleplayLogic/GetMainChannelUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetMainChannelUserList(ctx, req.(*channel_roleplay_logic.GetMainChannelUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelRoleplayLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.channel_roleplay.ChannelRoleplayLogic",
	HandlerType: (*ChannelRoleplayLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelHoldMicUserRoleList",
			Handler:    _ChannelRoleplayLogic_GetChannelHoldMicUserRoleList_Handler,
		},
		{
			MethodName: "SetMyChannelRole",
			Handler:    _ChannelRoleplayLogic_SetMyChannelRole_Handler,
		},
		{
			MethodName: "EnterBox",
			Handler:    _ChannelRoleplayLogic_EnterBox_Handler,
		},
		{
			MethodName: "ExitBox",
			Handler:    _ChannelRoleplayLogic_ExitBox_Handler,
		},
		{
			MethodName: "HandleApplyBox",
			Handler:    _ChannelRoleplayLogic_HandleApplyBox_Handler,
		},
		{
			MethodName: "GetBoxInfo",
			Handler:    _ChannelRoleplayLogic_GetBoxInfo_Handler,
		},
		{
			MethodName: "GetBoxInfosByLimit",
			Handler:    _ChannelRoleplayLogic_GetBoxInfosByLimit_Handler,
		},
		{
			MethodName: "GetChannelUserRoleList",
			Handler:    _ChannelRoleplayLogic_GetChannelUserRoleList_Handler,
		},
		{
			MethodName: "UpsertBoxInfo",
			Handler:    _ChannelRoleplayLogic_UpsertBoxInfo_Handler,
		},
		{
			MethodName: "DelBoxInfo",
			Handler:    _ChannelRoleplayLogic_DelBoxInfo_Handler,
		},
		{
			MethodName: "OpenCommonMic",
			Handler:    _ChannelRoleplayLogic_OpenCommonMic_Handler,
		},
		{
			MethodName: "GetMainChannelUserList",
			Handler:    _ChannelRoleplayLogic_GetMainChannelUserList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/channel_roleplay/grpc_channel_roleplay.proto",
}

func init() {
	proto.RegisterFile("api/channel_roleplay/grpc_channel_roleplay.proto", fileDescriptor_grpc_channel_roleplay_b897993217726b66)
}

var fileDescriptor_grpc_channel_roleplay_b897993217726b66 = []byte{
	// 560 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x95, 0x4f, 0x6b, 0x13, 0x41,
	0x18, 0xc6, 0x49, 0x57, 0xd6, 0x30, 0xa8, 0xc8, 0x20, 0x11, 0x82, 0x85, 0x20, 0x58, 0x15, 0x9b,
	0xd9, 0x5a, 0xdb, 0x53, 0x3d, 0x68, 0x62, 0x69, 0x85, 0x04, 0x25, 0xd2, 0x8b, 0x97, 0x30, 0xd9,
	0x8e, 0xeb, 0xe0, 0xec, 0xcc, 0x74, 0x77, 0xa4, 0xc9, 0x41, 0x08, 0xf5, 0xcf, 0x82, 0x17, 0xc1,
	0x8f, 0x20, 0x1e, 0xf7, 0xe4, 0x37, 0xf0, 0xbf, 0x1f, 0x4b, 0x26, 0x9b, 0x9d, 0x24, 0xcb, 0xb2,
	0xdd, 0xf4, 0xb6, 0xcc, 0xfb, 0x7b, 0x9e, 0xe7, 0x1d, 0xde, 0x1d, 0x5e, 0xb0, 0x81, 0x25, 0x75,
	0xdc, 0x97, 0x98, 0x73, 0xc2, 0xfa, 0x81, 0x60, 0x44, 0x32, 0x3c, 0x72, 0xbc, 0x40, 0xba, 0xfd,
	0xec, 0x29, 0x92, 0x81, 0x50, 0x02, 0x5e, 0xf5, 0x30, 0xc2, 0x92, 0xa2, 0x6c, 0xb9, 0xbe, 0x95,
	0x3d, 0xe9, 0x33, 0xe1, 0x51, 0x37, 0x75, 0x6f, 0xa6, 0xc7, 0xcd, 0xc9, 0x71, 0x3f, 0xb1, 0xab,
	0xaf, 0xea, 0x06, 0xc8, 0x50, 0x11, 0x1e, 0x52, 0xc1, 0x67, 0x5f, 0x49, 0x79, 0xf3, 0xdb, 0x05,
	0x70, 0xa5, 0x9d, 0x18, 0xf4, 0xa6, 0xfa, 0x8e, 0x96, 0xc3, 0xaf, 0x15, 0xb0, 0xba, 0x47, 0xd4,
	0xb4, 0xb6, 0x2f, 0xd8, 0x61, 0x97, 0xba, 0x07, 0x21, 0x09, 0x34, 0xd6, 0xa1, 0xa1, 0x82, 0x3b,
	0xc8, 0xc3, 0x28, 0xbf, 0x27, 0x54, 0xa8, 0xec, 0x91, 0xa3, 0xfa, 0xfd, 0xb3, 0x8b, 0x43, 0x79,
	0xbd, 0x7a, 0x32, 0x6e, 0x9c, 0xab, 0x7e, 0x8f, 0x6d, 0xf8, 0x06, 0x5c, 0x7e, 0x46, 0x54, 0x77,
	0x34, 0x77, 0x07, 0x88, 0x0a, 0xbc, 0xb3, 0xb0, 0xee, 0xc5, 0x59, 0x8a, 0x37, 0xf1, 0x3f, 0x62,
	0x1b, 0x12, 0x50, 0xdd, 0xe5, 0x8a, 0x04, 0x2d, 0x31, 0x84, 0x6b, 0x05, 0x36, 0x29, 0xa4, 0xe3,
	0x6e, 0x96, 0xe2, 0x4c, 0xcc, 0xcf, 0xd8, 0x86, 0x03, 0x70, 0x7e, 0x77, 0x48, 0x95, 0x4e, 0xb9,
	0x51, 0xa4, 0x4e, 0x18, 0x1d, 0xb2, 0x56, 0x06, 0x33, 0x19, 0xbf, 0x62, 0x1b, 0x1e, 0x83, 0x4b,
	0xfb, 0x98, 0x1f, 0x32, 0xf2, 0x50, 0x4a, 0x36, 0xd2, 0x51, 0xeb, 0x05, 0x1e, 0x8b, 0xa8, 0x4e,
	0x6c, 0x2e, 0x41, 0x9b, 0xe0, 0xdf, 0xb1, 0x0d, 0x5f, 0x01, 0xb0, 0x47, 0x74, 0x43, 0x8f, 0xf9,
	0x0b, 0x01, 0x6f, 0x15, 0xff, 0x18, 0x53, 0x4c, 0x07, 0xde, 0x2e, 0x49, 0x9a, 0xb0, 0x3f, 0xb1,
	0x0d, 0xdf, 0x56, 0x00, 0x9c, 0x15, 0xc3, 0xd6, 0xa8, 0x43, 0x7d, 0xaa, 0xe0, 0x46, 0x29, 0xaf,
	0x14, 0xd7, 0xe9, 0x77, 0x97, 0x54, 0x98, 0x2e, 0xfe, 0xc6, 0x36, 0xfc, 0x54, 0x01, 0xb5, 0xd9,
	0x5f, 0xbe, 0xf0, 0xaa, 0xb6, 0x4a, 0x3d, 0x8c, 0xec, 0x73, 0xda, 0x3e, 0x83, 0xca, 0x74, 0xf4,
	0x2f, 0xb6, 0xa1, 0x02, 0x17, 0x0f, 0x64, 0x48, 0x02, 0x33, 0x87, 0x3b, 0x05, 0x8e, 0x0b, 0xa4,
	0x8e, 0x5f, 0x2f, 0x0f, 0x9b, 0xd4, 0xf7, 0x91, 0xa5, 0x47, 0xff, 0x88, 0xb0, 0x32, 0xa3, 0x9f,
	0x61, 0xa7, 0x8d, 0x7e, 0x9e, 0x34, 0x61, 0x1f, 0x22, 0x4b, 0x5f, 0xf1, 0x89, 0x24, 0xbc, 0x2d,
	0x7c, 0x5f, 0xf0, 0x2e, 0x75, 0x0b, 0xaf, 0xb8, 0x40, 0x9e, 0x76, 0xc5, 0x0c, 0x6c, 0x52, 0xdf,
	0x45, 0x56, 0x3a, 0xea, 0x2e, 0xa6, 0x7c, 0x6e, 0x0a, 0x65, 0x46, 0x9d, 0x23, 0x29, 0x31, 0xea,
	0x5c, 0x95, 0xe9, 0x28, 0x8a, 0xac, 0xfa, 0x83, 0x93, 0x71, 0xa3, 0x96, 0xbf, 0x34, 0x3e, 0x8e,
	0x1b, 0x2b, 0x9e, 0xf8, 0x3c, 0x6e, 0x5c, 0x73, 0x12, 0xc3, 0xbc, 0xd5, 0xe0, 0xb4, 0x06, 0xa0,
	0xe6, 0x0a, 0x1f, 0x1d, 0xbd, 0x3e, 0xc6, 0x1c, 0x29, 0x95, 0xac, 0x12, 0xbd, 0xb4, 0x9e, 0xb7,
	0x3c, 0xc1, 0x30, 0xf7, 0xd0, 0xf6, 0xa6, 0x52, 0xc8, 0x15, 0xbe, 0x33, 0x29, 0xb9, 0x82, 0x39,
	0x58, 0x4a, 0x27, 0x6f, 0x17, 0xee, 0x64, 0x0f, 0xbe, 0xac, 0x58, 0xbd, 0xa7, 0xed, 0x81, 0x3d,
	0x51, 0xde, 0xfb, 0x1f, 0x00, 0x00, 0xff, 0xff, 0xe9, 0x88, 0x01, 0xb0, 0x41, 0x07, 0x00, 0x00,
}
