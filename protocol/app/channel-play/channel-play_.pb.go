// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel_play/channel-play_.proto

package channel_play // import "golang.52tt.com/protocol/app/channel-play"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import game_pal_logic "golang.52tt.com/protocol/app/game-pal-logic"
import hobby_channel "golang.52tt.com/protocol/app/hobby-channel"
import topic_channel "golang.52tt.com/protocol/app/topic-channel"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RCMDLabelEnum int32

const (
	RCMDLabelEnum_RCMDLabel_None                RCMDLabelEnum = 0
	RCMDLabelEnum_RCMDLabel_GangUpWithHomeOwner RCMDLabelEnum = 1
	RCMDLabelEnum_RCMDLabel_ChatWithHomeOwner   RCMDLabelEnum = 2
)

var RCMDLabelEnum_name = map[int32]string{
	0: "RCMDLabel_None",
	1: "RCMDLabel_GangUpWithHomeOwner",
	2: "RCMDLabel_ChatWithHomeOwner",
}
var RCMDLabelEnum_value = map[string]int32{
	"RCMDLabel_None":                0,
	"RCMDLabel_GangUpWithHomeOwner": 1,
	"RCMDLabel_ChatWithHomeOwner":   2,
}

func (x RCMDLabelEnum) String() string {
	return proto.EnumName(RCMDLabelEnum_name, int32(x))
}
func (RCMDLabelEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{0}
}

type ChannelLabel int32

const (
	ChannelLabel_ChannelLabelNone    ChannelLabel = 0
	ChannelLabel_ChannelLabelQuality ChannelLabel = 1
	ChannelLabel_ChannelLabelHot     ChannelLabel = 2
)

var ChannelLabel_name = map[int32]string{
	0: "ChannelLabelNone",
	1: "ChannelLabelQuality",
	2: "ChannelLabelHot",
}
var ChannelLabel_value = map[string]int32{
	"ChannelLabelNone":    0,
	"ChannelLabelQuality": 1,
	"ChannelLabelHot":     2,
}

func (x ChannelLabel) String() string {
	return proto.EnumName(ChannelLabel_name, int32(x))
}
func (ChannelLabel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{1}
}

// 男1女2
type RcmdSex int32

const (
	RcmdSex_All    RcmdSex = 0
	RcmdSex_Female RcmdSex = 2
	RcmdSex_Male   RcmdSex = 1
)

var RcmdSex_name = map[int32]string{
	0: "All",
	2: "Female",
	1: "Male",
}
var RcmdSex_value = map[string]int32{
	"All":    0,
	"Female": 2,
	"Male":   1,
}

func (x RcmdSex) String() string {
	return proto.EnumName(RcmdSex_name, int32(x))
}
func (RcmdSex) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{2}
}

// 房间列表入口
type ChannelListEnterSource int32

const (
	ChannelListEnterSource_GangupHomePage       ChannelListEnterSource = 0
	ChannelListEnterSource_MysteryHomePage      ChannelListEnterSource = 1
	ChannelListEnterSource_ScenarioPage         ChannelListEnterSource = 2
	ChannelListEnterSource_MysteryHomeScreen    ChannelListEnterSource = 3
	ChannelListEnterSource_MysteryChannelScreen ChannelListEnterSource = 4
	ChannelListEnterSource_MysteryHomeCarousel  ChannelListEnterSource = 5
	ChannelListEnterSource_MixHomePageSource    ChannelListEnterSource = 6
	ChannelListEnterSource_GameZoneSource       ChannelListEnterSource = 7
	ChannelListEnterSource_MiniGameZoneSource   ChannelListEnterSource = 8
	ChannelListEnterSource_PcFastHomePageSource ChannelListEnterSource = 9
	ChannelListEnterSource_PcFastHallSource     ChannelListEnterSource = 10
)

var ChannelListEnterSource_name = map[int32]string{
	0:  "GangupHomePage",
	1:  "MysteryHomePage",
	2:  "ScenarioPage",
	3:  "MysteryHomeScreen",
	4:  "MysteryChannelScreen",
	5:  "MysteryHomeCarousel",
	6:  "MixHomePageSource",
	7:  "GameZoneSource",
	8:  "MiniGameZoneSource",
	9:  "PcFastHomePageSource",
	10: "PcFastHallSource",
}
var ChannelListEnterSource_value = map[string]int32{
	"GangupHomePage":       0,
	"MysteryHomePage":      1,
	"ScenarioPage":         2,
	"MysteryHomeScreen":    3,
	"MysteryChannelScreen": 4,
	"MysteryHomeCarousel":  5,
	"MixHomePageSource":    6,
	"GameZoneSource":       7,
	"MiniGameZoneSource":   8,
	"PcFastHomePageSource": 9,
	"PcFastHallSource":     10,
}

func (x ChannelListEnterSource) String() string {
	return proto.EnumName(ChannelListEnterSource_name, int32(x))
}
func (ChannelListEnterSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{3}
}

type FilterType int32

const (
	FilterType_Base_Block_Filter             FilterType = 0
	FilterType_Mystery_Game_Condition_Filter FilterType = 1
)

var FilterType_name = map[int32]string{
	0: "Base_Block_Filter",
	1: "Mystery_Game_Condition_Filter",
}
var FilterType_value = map[string]int32{
	"Base_Block_Filter":             0,
	"Mystery_Game_Condition_Filter": 1,
}

func (x FilterType) String() string {
	return proto.EnumName(FilterType_name, int32(x))
}
func (FilterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{4}
}

type ChannelListStyleType int32

const (
	ChannelListStyleType_Default_Group            ChannelListStyleType = 0
	ChannelListStyleType_HuanYou_Experiment_Group ChannelListStyleType = 1
	ChannelListStyleType_Mix_Business_Home_Page   ChannelListStyleType = 2
)

var ChannelListStyleType_name = map[int32]string{
	0: "Default_Group",
	1: "HuanYou_Experiment_Group",
	2: "Mix_Business_Home_Page",
}
var ChannelListStyleType_value = map[string]int32{
	"Default_Group":            0,
	"HuanYou_Experiment_Group": 1,
	"Mix_Business_Home_Page":   2,
}

func (x ChannelListStyleType) String() string {
	return proto.EnumName(ChannelListStyleType_name, int32(x))
}
func (ChannelListStyleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{5}
}

// 首页请求来源
type HomePageHeadConfigReqType int32

const (
	// 不传默认为 6.45版本前的TT新版首页
	HomePageHeadConfigReqType_HOME_PAGE_HEAD_CONFIG_REQ_TYPE_UNSPECIFIED HomePageHeadConfigReqType = 0
	// 6.45版本后的TT新版首页
	HomePageHeadConfigReqType_HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_NEW HomePageHeadConfigReqType = 1
	// 旧版TT首页
	HomePageHeadConfigReqType_HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_OLD HomePageHeadConfigReqType = 2
	// 欢游首页
	HomePageHeadConfigReqType_HOME_PAGE_HEAD_CONFIG_REQ_TYPE_HUANYOU HomePageHeadConfigReqType = 3
	// PC首页
	HomePageHeadConfigReqType_HOME_PAGE_HEAD_CONFIG_REQ_TYPE_PC HomePageHeadConfigReqType = 4
	// 6.56.5版本后的TT首页
	HomePageHeadConfigReqType_HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_656 HomePageHeadConfigReqType = 5
)

var HomePageHeadConfigReqType_name = map[int32]string{
	0: "HOME_PAGE_HEAD_CONFIG_REQ_TYPE_UNSPECIFIED",
	1: "HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_NEW",
	2: "HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_OLD",
	3: "HOME_PAGE_HEAD_CONFIG_REQ_TYPE_HUANYOU",
	4: "HOME_PAGE_HEAD_CONFIG_REQ_TYPE_PC",
	5: "HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_656",
}
var HomePageHeadConfigReqType_value = map[string]int32{
	"HOME_PAGE_HEAD_CONFIG_REQ_TYPE_UNSPECIFIED": 0,
	"HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_NEW":      1,
	"HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_OLD":      2,
	"HOME_PAGE_HEAD_CONFIG_REQ_TYPE_HUANYOU":     3,
	"HOME_PAGE_HEAD_CONFIG_REQ_TYPE_PC":          4,
	"HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_656":      5,
}

func (x HomePageHeadConfigReqType) String() string {
	return proto.EnumName(HomePageHeadConfigReqType_name, int32(x))
}
func (HomePageHeadConfigReqType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{6}
}

type HomePageHeadConfigEnum int32

const (
	HomePageHeadConfigEnum_CONFIG_GAME_TYPE               HomePageHeadConfigEnum = 0
	HomePageHeadConfigEnum_CONFIG_CASUAL_GAME_TYPE        HomePageHeadConfigEnum = 1
	HomePageHeadConfigEnum_CONFIG_ESCAPE_ROOM_TYPE        HomePageHeadConfigEnum = 2
	HomePageHeadConfigEnum_CONFIG_CHAT_TYPE               HomePageHeadConfigEnum = 3
	HomePageHeadConfigEnum_CONFIG_MUSIC_TYPE              HomePageHeadConfigEnum = 4
	HomePageHeadConfigEnum_CONFIG_SOUND_SCENE_TYPE        HomePageHeadConfigEnum = 5
	HomePageHeadConfigEnum_CONFIG_ACTIVITY_CENTER_TYPE    HomePageHeadConfigEnum = 6
	HomePageHeadConfigEnum_CONFIG_COMPETITION_CENTER_TYPE HomePageHeadConfigEnum = 7
	HomePageHeadConfigEnum_CONFIG_ESPORTS_TYPE            HomePageHeadConfigEnum = 8
	HomePageHeadConfigEnum_CONFIG_MUSE_FLASH_CHAT_TYPE    HomePageHeadConfigEnum = 9
	HomePageHeadConfigEnum_CONFIG_ROLE_PLAY_TYPE          HomePageHeadConfigEnum = 10
)

var HomePageHeadConfigEnum_name = map[int32]string{
	0:  "CONFIG_GAME_TYPE",
	1:  "CONFIG_CASUAL_GAME_TYPE",
	2:  "CONFIG_ESCAPE_ROOM_TYPE",
	3:  "CONFIG_CHAT_TYPE",
	4:  "CONFIG_MUSIC_TYPE",
	5:  "CONFIG_SOUND_SCENE_TYPE",
	6:  "CONFIG_ACTIVITY_CENTER_TYPE",
	7:  "CONFIG_COMPETITION_CENTER_TYPE",
	8:  "CONFIG_ESPORTS_TYPE",
	9:  "CONFIG_MUSE_FLASH_CHAT_TYPE",
	10: "CONFIG_ROLE_PLAY_TYPE",
}
var HomePageHeadConfigEnum_value = map[string]int32{
	"CONFIG_GAME_TYPE":               0,
	"CONFIG_CASUAL_GAME_TYPE":        1,
	"CONFIG_ESCAPE_ROOM_TYPE":        2,
	"CONFIG_CHAT_TYPE":               3,
	"CONFIG_MUSIC_TYPE":              4,
	"CONFIG_SOUND_SCENE_TYPE":        5,
	"CONFIG_ACTIVITY_CENTER_TYPE":    6,
	"CONFIG_COMPETITION_CENTER_TYPE": 7,
	"CONFIG_ESPORTS_TYPE":            8,
	"CONFIG_MUSE_FLASH_CHAT_TYPE":    9,
	"CONFIG_ROLE_PLAY_TYPE":          10,
}

func (x HomePageHeadConfigEnum) String() string {
	return proto.EnumName(HomePageHeadConfigEnum_name, int32(x))
}
func (HomePageHeadConfigEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{7}
}

type TagConfigType int32

const (
	TagConfigType_TAG_CONFIG_TYPE_UNSPECIFIED TagConfigType = 0
	TagConfigType_TAG_CONFIG_TYPE_DEFAULT     TagConfigType = 1
	TagConfigType_TAG_CONFIG_TYPE_RICH        TagConfigType = 2
)

var TagConfigType_name = map[int32]string{
	0: "TAG_CONFIG_TYPE_UNSPECIFIED",
	1: "TAG_CONFIG_TYPE_DEFAULT",
	2: "TAG_CONFIG_TYPE_RICH",
}
var TagConfigType_value = map[string]int32{
	"TAG_CONFIG_TYPE_UNSPECIFIED": 0,
	"TAG_CONFIG_TYPE_DEFAULT":     1,
	"TAG_CONFIG_TYPE_RICH":        2,
}

func (x TagConfigType) String() string {
	return proto.EnumName(TagConfigType_name, int32(x))
}
func (TagConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{8}
}

type ButtonEffect int32

const (
	ButtonEffect_BUTTON_INVALID ButtonEffect = 0
	ButtonEffect_BUTTON_MATCH   ButtonEffect = 1
	ButtonEffect_BUTTON_LINK    ButtonEffect = 2
)

var ButtonEffect_name = map[int32]string{
	0: "BUTTON_INVALID",
	1: "BUTTON_MATCH",
	2: "BUTTON_LINK",
}
var ButtonEffect_value = map[string]int32{
	"BUTTON_INVALID": 0,
	"BUTTON_MATCH":   1,
	"BUTTON_LINK":    2,
}

func (x ButtonEffect) String() string {
	return proto.EnumName(ButtonEffect_name, int32(x))
}
func (ButtonEffect) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{9}
}

type HomePageGuideStyle int32

const (
	HomePageGuideStyle_HOME_PAGE_GUIDE_STYLE_UNSPECIFIED HomePageGuideStyle = 0
	HomePageGuideStyle_HOME_PAGE_GUIDE_STYLE_TEXT        HomePageGuideStyle = 1
	HomePageGuideStyle_HOME_PAGE_GUIDE_STYLE_BUTTON      HomePageGuideStyle = 2
)

var HomePageGuideStyle_name = map[int32]string{
	0: "HOME_PAGE_GUIDE_STYLE_UNSPECIFIED",
	1: "HOME_PAGE_GUIDE_STYLE_TEXT",
	2: "HOME_PAGE_GUIDE_STYLE_BUTTON",
}
var HomePageGuideStyle_value = map[string]int32{
	"HOME_PAGE_GUIDE_STYLE_UNSPECIFIED": 0,
	"HOME_PAGE_GUIDE_STYLE_TEXT":        1,
	"HOME_PAGE_GUIDE_STYLE_BUTTON":      2,
}

func (x HomePageGuideStyle) String() string {
	return proto.EnumName(HomePageGuideStyle_name, int32(x))
}
func (HomePageGuideStyle) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{10}
}

// item_id的类型，开黑包括（tab_id, category_id），音乐
type FilterItemType int32

const (
	FilterItemType_UNKNOWN_ITEM_TYPE FilterItemType = 0
	FilterItemType_GAME_TAB          FilterItemType = 1
	FilterItemType_GAME_CATEGORY     FilterItemType = 2
	FilterItemType_MUSIC_ITEM        FilterItemType = 3
	FilterItemType_MIX_CATEGORY      FilterItemType = 4
	FilterItemType_MUSIC_FILTER      FilterItemType = 5
)

var FilterItemType_name = map[int32]string{
	0: "UNKNOWN_ITEM_TYPE",
	1: "GAME_TAB",
	2: "GAME_CATEGORY",
	3: "MUSIC_ITEM",
	4: "MIX_CATEGORY",
	5: "MUSIC_FILTER",
}
var FilterItemType_value = map[string]int32{
	"UNKNOWN_ITEM_TYPE": 0,
	"GAME_TAB":          1,
	"GAME_CATEGORY":     2,
	"MUSIC_ITEM":        3,
	"MIX_CATEGORY":      4,
	"MUSIC_FILTER":      5,
}

func (x FilterItemType) String() string {
	return proto.EnumName(FilterItemType_name, int32(x))
}
func (FilterItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{11}
}

// 区分游戏专区普通玩法和综合频道tab
type GameZoneTabType int32

const (
	GameZoneTabType_GameZoneTabType_Game                 GameZoneTabType = 0
	GameZoneTabType_GameZoneTabType_ComprehensiveChannel GameZoneTabType = 1
)

var GameZoneTabType_name = map[int32]string{
	0: "GameZoneTabType_Game",
	1: "GameZoneTabType_ComprehensiveChannel",
}
var GameZoneTabType_value = map[string]int32{
	"GameZoneTabType_Game":                 0,
	"GameZoneTabType_ComprehensiveChannel": 1,
}

func (x GameZoneTabType) String() string {
	return proto.EnumName(GameZoneTabType_name, int32(x))
}
func (GameZoneTabType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{12}
}

type FilterEntrance int32

const (
	FilterEntrance_UNKNOWN_ENTRANCE           FilterEntrance = 0
	FilterEntrance_HOME_PAGE                  FilterEntrance = 1
	FilterEntrance_GAME_ZONE                  FilterEntrance = 2
	FilterEntrance_MINIGAME_ZONE              FilterEntrance = 3
	FilterEntrance_GAME_PAL                   FilterEntrance = 4
	FilterEntrance_FILTER_ENTRANCE_REGISTER   FilterEntrance = 5
	FilterEntrance_HOME_PAGE_ADD_MUSIC_FILTER FilterEntrance = 6
)

var FilterEntrance_name = map[int32]string{
	0: "UNKNOWN_ENTRANCE",
	1: "HOME_PAGE",
	2: "GAME_ZONE",
	3: "MINIGAME_ZONE",
	4: "GAME_PAL",
	5: "FILTER_ENTRANCE_REGISTER",
	6: "HOME_PAGE_ADD_MUSIC_FILTER",
}
var FilterEntrance_value = map[string]int32{
	"UNKNOWN_ENTRANCE":           0,
	"HOME_PAGE":                  1,
	"GAME_ZONE":                  2,
	"MINIGAME_ZONE":              3,
	"GAME_PAL":                   4,
	"FILTER_ENTRANCE_REGISTER":   5,
	"HOME_PAGE_ADD_MUSIC_FILTER": 6,
}

func (x FilterEntrance) String() string {
	return proto.EnumName(FilterEntrance_name, int32(x))
}
func (FilterEntrance) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{13}
}

type TabItemVersion int32

const (
	TabItemVersion_Default_Version               TabItemVersion = 0
	TabItemVersion_After628_Version              TabItemVersion = 1
	TabItemVersion_TAB_ITEM_VERSION_MULTI_OPTION TabItemVersion = 2
)

var TabItemVersion_name = map[int32]string{
	0: "Default_Version",
	1: "After628_Version",
	2: "TAB_ITEM_VERSION_MULTI_OPTION",
}
var TabItemVersion_value = map[string]int32{
	"Default_Version":               0,
	"After628_Version":              1,
	"TAB_ITEM_VERSION_MULTI_OPTION": 2,
}

func (x TabItemVersion) String() string {
	return proto.EnumName(TabItemVersion_name, int32(x))
}
func (TabItemVersion) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{14}
}

type FeedBackTypeInRoom int32

const (
	FeedBackTypeInRoom_Unknown_FeedBackType     FeedBackTypeInRoom = 0
	FeedBackTypeInRoom_KnockOut_FeedBackType    FeedBackTypeInRoom = 1
	FeedBackTypeInRoom_VisitorList_FeedBackType FeedBackTypeInRoom = 2
	FeedBackTypeInRoom_RoomVisitor_FeedBackType FeedBackTypeInRoom = 3
	// 退房不感兴趣反馈
	FeedBackTypeInRoom_QuitUninterested_FeedBackType FeedBackTypeInRoom = 4
)

var FeedBackTypeInRoom_name = map[int32]string{
	0: "Unknown_FeedBackType",
	1: "KnockOut_FeedBackType",
	2: "VisitorList_FeedBackType",
	3: "RoomVisitor_FeedBackType",
	4: "QuitUninterested_FeedBackType",
}
var FeedBackTypeInRoom_value = map[string]int32{
	"Unknown_FeedBackType":          0,
	"KnockOut_FeedBackType":         1,
	"VisitorList_FeedBackType":      2,
	"RoomVisitor_FeedBackType":      3,
	"QuitUninterested_FeedBackType": 4,
}

func (x FeedBackTypeInRoom) String() string {
	return proto.EnumName(FeedBackTypeInRoom_name, int32(x))
}
func (FeedBackTypeInRoom) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{15}
}

// 房间搭子入口展示状态
type GamePalEntranceStatus int32

const (
	GamePalEntranceStatus_GAME_PAL_ENTRANCE_STATUS_UNSPECIFIED GamePalEntranceStatus = 0
	GamePalEntranceStatus_GAME_PAL_ENTRANCE_STATUS_SHOW        GamePalEntranceStatus = 1
	GamePalEntranceStatus_GAME_PAL_ENTRANCE_STATUS_HIDE        GamePalEntranceStatus = 2
)

var GamePalEntranceStatus_name = map[int32]string{
	0: "GAME_PAL_ENTRANCE_STATUS_UNSPECIFIED",
	1: "GAME_PAL_ENTRANCE_STATUS_SHOW",
	2: "GAME_PAL_ENTRANCE_STATUS_HIDE",
}
var GamePalEntranceStatus_value = map[string]int32{
	"GAME_PAL_ENTRANCE_STATUS_UNSPECIFIED": 0,
	"GAME_PAL_ENTRANCE_STATUS_SHOW":        1,
	"GAME_PAL_ENTRANCE_STATUS_HIDE":        2,
}

func (x GamePalEntranceStatus) String() string {
	return proto.EnumName(GamePalEntranceStatus_name, int32(x))
}
func (GamePalEntranceStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{16}
}

type UgcChannelPlayMode int32

const (
	UgcChannelPlayMode_DEFAULT_VOICE_MODE UgcChannelPlayMode = 0
	UgcChannelPlayMode_WORD_MODE          UgcChannelPlayMode = 1
)

var UgcChannelPlayMode_name = map[int32]string{
	0: "DEFAULT_VOICE_MODE",
	1: "WORD_MODE",
}
var UgcChannelPlayMode_value = map[string]int32{
	"DEFAULT_VOICE_MODE": 0,
	"WORD_MODE":          1,
}

func (x UgcChannelPlayMode) String() string {
	return proto.EnumName(UgcChannelPlayMode_name, int32(x))
}
func (UgcChannelPlayMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{17}
}

// 每日任务完成上报
type DailyTaskType int32

const (
	DailyTaskType_INVALID_TASK_TYPE    DailyTaskType = 0
	DailyTaskType_ENTER_GAME_ZONE_TASK DailyTaskType = 1
	DailyTaskType_CONFIG_TAB_VIEW_DATA DailyTaskType = 2
)

var DailyTaskType_name = map[int32]string{
	0: "INVALID_TASK_TYPE",
	1: "ENTER_GAME_ZONE_TASK",
	2: "CONFIG_TAB_VIEW_DATA",
}
var DailyTaskType_value = map[string]int32{
	"INVALID_TASK_TYPE":    0,
	"ENTER_GAME_ZONE_TASK": 1,
	"CONFIG_TAB_VIEW_DATA": 2,
}

func (x DailyTaskType) String() string {
	return proto.EnumName(DailyTaskType_name, int32(x))
}
func (DailyTaskType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{18}
}

// 按钮跳转类型
type ButtonType int32

const (
	ButtonType_BUTTON_TYPE_UNSPECIFIED ButtonType = 0
	ButtonType_BUTTON_TYPE_ENTER_ZONE  ButtonType = 1
	ButtonType_BUTTON_TYPE_QUICK_MATCH ButtonType = 2
)

var ButtonType_name = map[int32]string{
	0: "BUTTON_TYPE_UNSPECIFIED",
	1: "BUTTON_TYPE_ENTER_ZONE",
	2: "BUTTON_TYPE_QUICK_MATCH",
}
var ButtonType_value = map[string]int32{
	"BUTTON_TYPE_UNSPECIFIED": 0,
	"BUTTON_TYPE_ENTER_ZONE":  1,
	"BUTTON_TYPE_QUICK_MATCH": 2,
}

func (x ButtonType) String() string {
	return proto.EnumName(ButtonType_name, int32(x))
}
func (ButtonType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{19}
}

// 配置类型
type CfgType int32

const (
	CfgType_CFG_TYPE_UNSPECIFIED CfgType = 0
	CfgType_CFG_TYPE_GAME_TAB    CfgType = 1
)

var CfgType_name = map[int32]string{
	0: "CFG_TYPE_UNSPECIFIED",
	1: "CFG_TYPE_GAME_TAB",
}
var CfgType_value = map[string]int32{
	"CFG_TYPE_UNSPECIFIED": 0,
	"CFG_TYPE_GAME_TAB":    1,
}

func (x CfgType) String() string {
	return proto.EnumName(CfgType_name, int32(x))
}
func (CfgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{20}
}

type ListTopicChannelReq_FilterModel int32

const (
	ListTopicChannelReq_FILTER_MODEL_MT ListTopicChannelReq_FilterModel = 0
	ListTopicChannelReq_FILTER_MODE_KH  ListTopicChannelReq_FilterModel = 1
)

var ListTopicChannelReq_FilterModel_name = map[int32]string{
	0: "FILTER_MODEL_MT",
	1: "FILTER_MODE_KH",
}
var ListTopicChannelReq_FilterModel_value = map[string]int32{
	"FILTER_MODEL_MT": 0,
	"FILTER_MODE_KH":  1,
}

func (x ListTopicChannelReq_FilterModel) String() string {
	return proto.EnumName(ListTopicChannelReq_FilterModel_name, int32(x))
}
func (ListTopicChannelReq_FilterModel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{4, 0}
}

type GetSecondaryFilterReq_Mode int32

const (
	GetSecondaryFilterReq_PUBCHANNEL            GetSecondaryFilterReq_Mode = 0
	GetSecondaryFilterReq_MATCHTEAMMATE         GetSecondaryFilterReq_Mode = 1
	GetSecondaryFilterReq_FILTER                GetSecondaryFilterReq_Mode = 2
	GetSecondaryFilterReq_QUESTION              GetSecondaryFilterReq_Mode = 3
	GetSecondaryFilterReq_PubChannelAfter628    GetSecondaryFilterReq_Mode = 4
	GetSecondaryFilterReq_FilterAfter628        GetSecondaryFilterReq_Mode = 5
	GetSecondaryFilterReq_QuestionAfter628      GetSecondaryFilterReq_Mode = 6
	GetSecondaryFilterReq_FilterWithMultiOption GetSecondaryFilterReq_Mode = 7
	GetSecondaryFilterReq_PubChannelAfter640    GetSecondaryFilterReq_Mode = 8
	GetSecondaryFilterReq_PC_HOME_PAGE          GetSecondaryFilterReq_Mode = 9
	GetSecondaryFilterReq_HUAN_YOU_MIX_LABEL    GetSecondaryFilterReq_Mode = 10
)

var GetSecondaryFilterReq_Mode_name = map[int32]string{
	0:  "PUBCHANNEL",
	1:  "MATCHTEAMMATE",
	2:  "FILTER",
	3:  "QUESTION",
	4:  "PubChannelAfter628",
	5:  "FilterAfter628",
	6:  "QuestionAfter628",
	7:  "FilterWithMultiOption",
	8:  "PubChannelAfter640",
	9:  "PC_HOME_PAGE",
	10: "HUAN_YOU_MIX_LABEL",
}
var GetSecondaryFilterReq_Mode_value = map[string]int32{
	"PUBCHANNEL":            0,
	"MATCHTEAMMATE":         1,
	"FILTER":                2,
	"QUESTION":              3,
	"PubChannelAfter628":    4,
	"FilterAfter628":        5,
	"QuestionAfter628":      6,
	"FilterWithMultiOption": 7,
	"PubChannelAfter640":    8,
	"PC_HOME_PAGE":          9,
	"HUAN_YOU_MIX_LABEL":    10,
}

func (x GetSecondaryFilterReq_Mode) String() string {
	return proto.EnumName(GetSecondaryFilterReq_Mode_name, int32(x))
}
func (GetSecondaryFilterReq_Mode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{6, 0}
}

type GetSecondaryFilterReq_Source int32

const (
	GetSecondaryFilterReq_SourceHome   GetSecondaryFilterReq_Source = 0
	GetSecondaryFilterReq_SourceScreen GetSecondaryFilterReq_Source = 1
)

var GetSecondaryFilterReq_Source_name = map[int32]string{
	0: "SourceHome",
	1: "SourceScreen",
}
var GetSecondaryFilterReq_Source_value = map[string]int32{
	"SourceHome":   0,
	"SourceScreen": 1,
}

func (x GetSecondaryFilterReq_Source) String() string {
	return proto.EnumName(GetSecondaryFilterReq_Source_name, int32(x))
}
func (GetSecondaryFilterReq_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{6, 1}
}

type GetSecondaryFilterResp_TabType int32

const (
	GetSecondaryFilterResp_NORMAL    GetSecondaryFilterResp_TabType = 0
	GetSecondaryFilterResp_GAME      GetSecondaryFilterResp_TabType = 1
	GetSecondaryFilterResp_MINI_GAME GetSecondaryFilterResp_TabType = 2
)

var GetSecondaryFilterResp_TabType_name = map[int32]string{
	0: "NORMAL",
	1: "GAME",
	2: "MINI_GAME",
}
var GetSecondaryFilterResp_TabType_value = map[string]int32{
	"NORMAL":    0,
	"GAME":      1,
	"MINI_GAME": 2,
}

func (x GetSecondaryFilterResp_TabType) String() string {
	return proto.EnumName(GetSecondaryFilterResp_TabType_name, int32(x))
}
func (GetSecondaryFilterResp_TabType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{7, 0}
}

type GetSecondaryFilterResp_MatchType int32

const (
	GetSecondaryFilterResp_QUICK          GetSecondaryFilterResp_MatchType = 0
	GetSecondaryFilterResp_TEMPORARYHOUSE GetSecondaryFilterResp_MatchType = 1
)

var GetSecondaryFilterResp_MatchType_name = map[int32]string{
	0: "QUICK",
	1: "TEMPORARYHOUSE",
}
var GetSecondaryFilterResp_MatchType_value = map[string]int32{
	"QUICK":          0,
	"TEMPORARYHOUSE": 1,
}

func (x GetSecondaryFilterResp_MatchType) String() string {
	return proto.EnumName(GetSecondaryFilterResp_MatchType_name, int32(x))
}
func (GetSecondaryFilterResp_MatchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{7, 1}
}

// Mode 是 Block 的选择模式
type BusinessFilterItem_Mode int32

const (
	BusinessFilterItem_SINGLE       BusinessFilterItem_Mode = 0
	BusinessFilterItem_MULTI        BusinessFilterItem_Mode = 1
	BusinessFilterItem_SETUP_NUMBER BusinessFilterItem_Mode = 2
)

var BusinessFilterItem_Mode_name = map[int32]string{
	0: "SINGLE",
	1: "MULTI",
	2: "SETUP_NUMBER",
}
var BusinessFilterItem_Mode_value = map[string]int32{
	"SINGLE":       0,
	"MULTI":        1,
	"SETUP_NUMBER": 2,
}

func (x BusinessFilterItem_Mode) String() string {
	return proto.EnumName(BusinessFilterItem_Mode_name, int32(x))
}
func (BusinessFilterItem_Mode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{10, 0}
}

// Mode 是 Elem 的模式
type BusinessFilterElem_BusinessFilterMode int32

const (
	BusinessFilterElem_NORMAL    BusinessFilterElem_BusinessFilterMode = 0
	BusinessFilterElem_RECOMMEND BusinessFilterElem_BusinessFilterMode = 1
	BusinessFilterElem_INFINITE  BusinessFilterElem_BusinessFilterMode = 2
)

var BusinessFilterElem_BusinessFilterMode_name = map[int32]string{
	0: "NORMAL",
	1: "RECOMMEND",
	2: "INFINITE",
}
var BusinessFilterElem_BusinessFilterMode_value = map[string]int32{
	"NORMAL":    0,
	"RECOMMEND": 1,
	"INFINITE":  2,
}

func (x BusinessFilterElem_BusinessFilterMode) String() string {
	return proto.EnumName(BusinessFilterElem_BusinessFilterMode_name, int32(x))
}
func (BusinessFilterElem_BusinessFilterMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{11, 0}
}

type GetDefaultRoomNameListReq_GetMode int32

const (
	GetDefaultRoomNameListReq_UNSPECIFIED GetDefaultRoomNameListReq_GetMode = 0
	GetDefaultRoomNameListReq_NEXT_PAGE   GetDefaultRoomNameListReq_GetMode = 1
	GetDefaultRoomNameListReq_REFRESH     GetDefaultRoomNameListReq_GetMode = 2
)

var GetDefaultRoomNameListReq_GetMode_name = map[int32]string{
	0: "UNSPECIFIED",
	1: "NEXT_PAGE",
	2: "REFRESH",
}
var GetDefaultRoomNameListReq_GetMode_value = map[string]int32{
	"UNSPECIFIED": 0,
	"NEXT_PAGE":   1,
	"REFRESH":     2,
}

func (x GetDefaultRoomNameListReq_GetMode) String() string {
	return proto.EnumName(GetDefaultRoomNameListReq_GetMode_name, int32(x))
}
func (GetDefaultRoomNameListReq_GetMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{15, 0}
}

type GetDefaultRoomNameListReq_ReqSource int32

const (
	GetDefaultRoomNameListReq_DEFAULT           GetDefaultRoomNameListReq_ReqSource = 0
	GetDefaultRoomNameListReq_AFTER_661_VERSION GetDefaultRoomNameListReq_ReqSource = 1
)

var GetDefaultRoomNameListReq_ReqSource_name = map[int32]string{
	0: "DEFAULT",
	1: "AFTER_661_VERSION",
}
var GetDefaultRoomNameListReq_ReqSource_value = map[string]int32{
	"DEFAULT":           0,
	"AFTER_661_VERSION": 1,
}

func (x GetDefaultRoomNameListReq_ReqSource) String() string {
	return proto.EnumName(GetDefaultRoomNameListReq_ReqSource_name, int32(x))
}
func (GetDefaultRoomNameListReq_ReqSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{15, 1}
}

type GetQuickMiniGamesReq_Source int32

const (
	GetQuickMiniGamesReq_SourceUnknown GetQuickMiniGamesReq_Source = 0
	// 外显
	GetQuickMiniGamesReq_SourceExpose GetQuickMiniGamesReq_Source = 1
	// 更多
	GetQuickMiniGamesReq_SourceMore GetQuickMiniGamesReq_Source = 2
)

var GetQuickMiniGamesReq_Source_name = map[int32]string{
	0: "SourceUnknown",
	1: "SourceExpose",
	2: "SourceMore",
}
var GetQuickMiniGamesReq_Source_value = map[string]int32{
	"SourceUnknown": 0,
	"SourceExpose":  1,
	"SourceMore":    2,
}

func (x GetQuickMiniGamesReq_Source) String() string {
	return proto.EnumName(GetQuickMiniGamesReq_Source_name, int32(x))
}
func (GetQuickMiniGamesReq_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{39, 0}
}

type GameInsertFlowConfigResp_ConfigType int32

const (
	GameInsertFlowConfigResp_QUICK_MATCH GameInsertFlowConfigResp_ConfigType = 0
	GameInsertFlowConfigResp_CREATE_ROOM GameInsertFlowConfigResp_ConfigType = 1
)

var GameInsertFlowConfigResp_ConfigType_name = map[int32]string{
	0: "QUICK_MATCH",
	1: "CREATE_ROOM",
}
var GameInsertFlowConfigResp_ConfigType_value = map[string]int32{
	"QUICK_MATCH": 0,
	"CREATE_ROOM": 1,
}

func (x GameInsertFlowConfigResp_ConfigType) String() string {
	return proto.EnumName(GameInsertFlowConfigResp_ConfigType_name, int32(x))
}
func (GameInsertFlowConfigResp_ConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{42, 0}
}

type GetPlayQuestionsReq_Source int32

const (
	GetPlayQuestionsReq_SOURCE_UNSPECIFIED GetPlayQuestionsReq_Source = 0
	// 房间列表获客人群来源
	GetPlayQuestionsReq_SOURCE_CHANNEL_LIST_NEW GetPlayQuestionsReq_Source = 1
	// 房间列表活跃人群来源
	GetPlayQuestionsReq_SOURCE_CHANNEL_LIST_ACTIVE GetPlayQuestionsReq_Source = 2
	// 房间内反馈
	GetPlayQuestionsReq_SOURCE_FEEDBACK_IN_ROOM GetPlayQuestionsReq_Source = 3
)

var GetPlayQuestionsReq_Source_name = map[int32]string{
	0: "SOURCE_UNSPECIFIED",
	1: "SOURCE_CHANNEL_LIST_NEW",
	2: "SOURCE_CHANNEL_LIST_ACTIVE",
	3: "SOURCE_FEEDBACK_IN_ROOM",
}
var GetPlayQuestionsReq_Source_value = map[string]int32{
	"SOURCE_UNSPECIFIED":         0,
	"SOURCE_CHANNEL_LIST_NEW":    1,
	"SOURCE_CHANNEL_LIST_ACTIVE": 2,
	"SOURCE_FEEDBACK_IN_ROOM":    3,
}

func (x GetPlayQuestionsReq_Source) String() string {
	return proto.EnumName(GetPlayQuestionsReq_Source_name, int32(x))
}
func (GetPlayQuestionsReq_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{56, 0}
}

type ReportNegativeFeedBackInRoomResp_HitType int32

const (
	ReportNegativeFeedBackInRoomResp_HIT_TYPE_UNSPECIFIED ReportNegativeFeedBackInRoomResp_HitType = 0
	ReportNegativeFeedBackInRoomResp_HIT_TYPE_AGE         ReportNegativeFeedBackInRoomResp_HitType = 1
)

var ReportNegativeFeedBackInRoomResp_HitType_name = map[int32]string{
	0: "HIT_TYPE_UNSPECIFIED",
	1: "HIT_TYPE_AGE",
}
var ReportNegativeFeedBackInRoomResp_HitType_value = map[string]int32{
	"HIT_TYPE_UNSPECIFIED": 0,
	"HIT_TYPE_AGE":         1,
}

func (x ReportNegativeFeedBackInRoomResp_HitType) String() string {
	return proto.EnumName(ReportNegativeFeedBackInRoomResp_HitType_name, int32(x))
}
func (ReportNegativeFeedBackInRoomResp_HitType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{75, 0}
}

type RecommendationElem_Type int32

const (
	RecommendationElem_TYPE_UNSPECIFIED RecommendationElem_Type = 0
	RecommendationElem_TYPE_Publish     RecommendationElem_Type = 1
	RecommendationElem_TYPE_ThirdLabel  RecommendationElem_Type = 2
)

var RecommendationElem_Type_name = map[int32]string{
	0: "TYPE_UNSPECIFIED",
	1: "TYPE_Publish",
	2: "TYPE_ThirdLabel",
}
var RecommendationElem_Type_value = map[string]int32{
	"TYPE_UNSPECIFIED": 0,
	"TYPE_Publish":     1,
	"TYPE_ThirdLabel":  2,
}

func (x RecommendationElem_Type) String() string {
	return proto.EnumName(RecommendationElem_Type_name, int32(x))
}
func (RecommendationElem_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{79, 0}
}

type GetTopicChannelCfgInfoResp_TabType int32

const (
	GetTopicChannelCfgInfoResp_NORMAL    GetTopicChannelCfgInfoResp_TabType = 0
	GetTopicChannelCfgInfoResp_GAME      GetTopicChannelCfgInfoResp_TabType = 1
	GetTopicChannelCfgInfoResp_MINI_GAME GetTopicChannelCfgInfoResp_TabType = 2
)

var GetTopicChannelCfgInfoResp_TabType_name = map[int32]string{
	0: "NORMAL",
	1: "GAME",
	2: "MINI_GAME",
}
var GetTopicChannelCfgInfoResp_TabType_value = map[string]int32{
	"NORMAL":    0,
	"GAME":      1,
	"MINI_GAME": 2,
}

func (x GetTopicChannelCfgInfoResp_TabType) String() string {
	return proto.EnumName(GetTopicChannelCfgInfoResp_TabType_name, int32(x))
}
func (GetTopicChannelCfgInfoResp_TabType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{84, 0}
}

type TopicChannelView struct {
	// Types that are valid to be assigned to ChannelView:
	//	*TopicChannelView_ViewDefault
	//	*TopicChannelView_ViewMoba
	//	*TopicChannelView_ViewKtv
	//	*TopicChannelView_ViewMysteryEscape
	//	*TopicChannelView_ViewMarshal
	//	*TopicChannelView_ViewNewMusic
	ChannelView          isTopicChannelView_ChannelView `protobuf_oneof:"channel_view"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *TopicChannelView) Reset()         { *m = TopicChannelView{} }
func (m *TopicChannelView) String() string { return proto.CompactTextString(m) }
func (*TopicChannelView) ProtoMessage()    {}
func (*TopicChannelView) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{0}
}
func (m *TopicChannelView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelView.Unmarshal(m, b)
}
func (m *TopicChannelView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelView.Marshal(b, m, deterministic)
}
func (dst *TopicChannelView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelView.Merge(dst, src)
}
func (m *TopicChannelView) XXX_Size() int {
	return xxx_messageInfo_TopicChannelView.Size(m)
}
func (m *TopicChannelView) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelView.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelView proto.InternalMessageInfo

type isTopicChannelView_ChannelView interface {
	isTopicChannelView_ChannelView()
}

type TopicChannelView_ViewDefault struct {
	ViewDefault *ChannelViewDefault `protobuf:"bytes,1,opt,name=view_default,json=viewDefault,proto3,oneof"`
}

type TopicChannelView_ViewMoba struct {
	ViewMoba *ChannelViewMOBA `protobuf:"bytes,2,opt,name=view_moba,json=viewMoba,proto3,oneof"`
}

type TopicChannelView_ViewKtv struct {
	ViewKtv *ChannelViewKtv `protobuf:"bytes,3,opt,name=view_ktv,json=viewKtv,proto3,oneof"`
}

type TopicChannelView_ViewMysteryEscape struct {
	ViewMysteryEscape *ChannelViewMysteryEscape `protobuf:"bytes,4,opt,name=view_mystery_escape,json=viewMysteryEscape,proto3,oneof"`
}

type TopicChannelView_ViewMarshal struct {
	ViewMarshal *ChannelViewMarshal `protobuf:"bytes,5,opt,name=view_marshal,json=viewMarshal,proto3,oneof"`
}

type TopicChannelView_ViewNewMusic struct {
	ViewNewMusic *ChannelViewNewMusic `protobuf:"bytes,6,opt,name=view_new_music,json=viewNewMusic,proto3,oneof"`
}

func (*TopicChannelView_ViewDefault) isTopicChannelView_ChannelView() {}

func (*TopicChannelView_ViewMoba) isTopicChannelView_ChannelView() {}

func (*TopicChannelView_ViewKtv) isTopicChannelView_ChannelView() {}

func (*TopicChannelView_ViewMysteryEscape) isTopicChannelView_ChannelView() {}

func (*TopicChannelView_ViewMarshal) isTopicChannelView_ChannelView() {}

func (*TopicChannelView_ViewNewMusic) isTopicChannelView_ChannelView() {}

func (m *TopicChannelView) GetChannelView() isTopicChannelView_ChannelView {
	if m != nil {
		return m.ChannelView
	}
	return nil
}

func (m *TopicChannelView) GetViewDefault() *ChannelViewDefault {
	if x, ok := m.GetChannelView().(*TopicChannelView_ViewDefault); ok {
		return x.ViewDefault
	}
	return nil
}

func (m *TopicChannelView) GetViewMoba() *ChannelViewMOBA {
	if x, ok := m.GetChannelView().(*TopicChannelView_ViewMoba); ok {
		return x.ViewMoba
	}
	return nil
}

func (m *TopicChannelView) GetViewKtv() *ChannelViewKtv {
	if x, ok := m.GetChannelView().(*TopicChannelView_ViewKtv); ok {
		return x.ViewKtv
	}
	return nil
}

func (m *TopicChannelView) GetViewMysteryEscape() *ChannelViewMysteryEscape {
	if x, ok := m.GetChannelView().(*TopicChannelView_ViewMysteryEscape); ok {
		return x.ViewMysteryEscape
	}
	return nil
}

func (m *TopicChannelView) GetViewMarshal() *ChannelViewMarshal {
	if x, ok := m.GetChannelView().(*TopicChannelView_ViewMarshal); ok {
		return x.ViewMarshal
	}
	return nil
}

func (m *TopicChannelView) GetViewNewMusic() *ChannelViewNewMusic {
	if x, ok := m.GetChannelView().(*TopicChannelView_ViewNewMusic); ok {
		return x.ViewNewMusic
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*TopicChannelView) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _TopicChannelView_OneofMarshaler, _TopicChannelView_OneofUnmarshaler, _TopicChannelView_OneofSizer, []interface{}{
		(*TopicChannelView_ViewDefault)(nil),
		(*TopicChannelView_ViewMoba)(nil),
		(*TopicChannelView_ViewKtv)(nil),
		(*TopicChannelView_ViewMysteryEscape)(nil),
		(*TopicChannelView_ViewMarshal)(nil),
		(*TopicChannelView_ViewNewMusic)(nil),
	}
}

func _TopicChannelView_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*TopicChannelView)
	// channel_view
	switch x := m.ChannelView.(type) {
	case *TopicChannelView_ViewDefault:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ViewDefault); err != nil {
			return err
		}
	case *TopicChannelView_ViewMoba:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ViewMoba); err != nil {
			return err
		}
	case *TopicChannelView_ViewKtv:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ViewKtv); err != nil {
			return err
		}
	case *TopicChannelView_ViewMysteryEscape:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ViewMysteryEscape); err != nil {
			return err
		}
	case *TopicChannelView_ViewMarshal:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ViewMarshal); err != nil {
			return err
		}
	case *TopicChannelView_ViewNewMusic:
		b.EncodeVarint(6<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ViewNewMusic); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("TopicChannelView.ChannelView has unexpected type %T", x)
	}
	return nil
}

func _TopicChannelView_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*TopicChannelView)
	switch tag {
	case 1: // channel_view.view_default
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ChannelViewDefault)
		err := b.DecodeMessage(msg)
		m.ChannelView = &TopicChannelView_ViewDefault{msg}
		return true, err
	case 2: // channel_view.view_moba
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ChannelViewMOBA)
		err := b.DecodeMessage(msg)
		m.ChannelView = &TopicChannelView_ViewMoba{msg}
		return true, err
	case 3: // channel_view.view_ktv
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ChannelViewKtv)
		err := b.DecodeMessage(msg)
		m.ChannelView = &TopicChannelView_ViewKtv{msg}
		return true, err
	case 4: // channel_view.view_mystery_escape
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ChannelViewMysteryEscape)
		err := b.DecodeMessage(msg)
		m.ChannelView = &TopicChannelView_ViewMysteryEscape{msg}
		return true, err
	case 5: // channel_view.view_marshal
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ChannelViewMarshal)
		err := b.DecodeMessage(msg)
		m.ChannelView = &TopicChannelView_ViewMarshal{msg}
		return true, err
	case 6: // channel_view.view_new_music
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ChannelViewNewMusic)
		err := b.DecodeMessage(msg)
		m.ChannelView = &TopicChannelView_ViewNewMusic{msg}
		return true, err
	default:
		return false, nil
	}
}

func _TopicChannelView_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*TopicChannelView)
	// channel_view
	switch x := m.ChannelView.(type) {
	case *TopicChannelView_ViewDefault:
		s := proto.Size(x.ViewDefault)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *TopicChannelView_ViewMoba:
		s := proto.Size(x.ViewMoba)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *TopicChannelView_ViewKtv:
		s := proto.Size(x.ViewKtv)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *TopicChannelView_ViewMysteryEscape:
		s := proto.Size(x.ViewMysteryEscape)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *TopicChannelView_ViewMarshal:
		s := proto.Size(x.ViewMarshal)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *TopicChannelView_ViewNewMusic:
		s := proto.Size(x.ViewNewMusic)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type PersonalCert struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Color                []string `protobuf:"bytes,3,rep,name=color,proto3" json:"color,omitempty"`
	TextShadowColor      string   `protobuf:"bytes,4,opt,name=text_shadow_color,json=textShadowColor,proto3" json:"text_shadow_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PersonalCert) Reset()         { *m = PersonalCert{} }
func (m *PersonalCert) String() string { return proto.CompactTextString(m) }
func (*PersonalCert) ProtoMessage()    {}
func (*PersonalCert) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{1}
}
func (m *PersonalCert) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PersonalCert.Unmarshal(m, b)
}
func (m *PersonalCert) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PersonalCert.Marshal(b, m, deterministic)
}
func (dst *PersonalCert) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PersonalCert.Merge(dst, src)
}
func (m *PersonalCert) XXX_Size() int {
	return xxx_messageInfo_PersonalCert.Size(m)
}
func (m *PersonalCert) XXX_DiscardUnknown() {
	xxx_messageInfo_PersonalCert.DiscardUnknown(m)
}

var xxx_messageInfo_PersonalCert proto.InternalMessageInfo

func (m *PersonalCert) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *PersonalCert) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *PersonalCert) GetColor() []string {
	if m != nil {
		return m.Color
	}
	return nil
}

func (m *PersonalCert) GetTextShadowColor() string {
	if m != nil {
		return m.TextShadowColor
	}
	return ""
}

type TopicChannelItem struct {
	ChannelId            uint32                                 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string                                 `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	OwnerUid             uint32                                 `protobuf:"varint,3,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	ChannelOwnerUsername string                                 `protobuf:"bytes,4,opt,name=channel_owner_username,json=channelOwnerUsername,proto3" json:"channel_owner_username,omitempty"`
	ChannelOwnerSex      int32                                  `protobuf:"varint,5,opt,name=channel_owner_sex,json=channelOwnerSex,proto3" json:"channel_owner_sex,omitempty"`
	MemberCount          uint32                                 `protobuf:"varint,6,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	TopicChannelView     *TopicChannelView                      `protobuf:"bytes,7,opt,name=topic_channel_view,json=topicChannelView,proto3" json:"topic_channel_view,omitempty"`
	RcmdLabel            RCMDLabelEnum                          `protobuf:"varint,8,opt,name=rcmd_label,json=rcmdLabel,proto3,enum=ga.channel_play.RCMDLabelEnum" json:"rcmd_label,omitempty"`
	GeoInfo              string                                 `protobuf:"bytes,9,opt,name=geo_info,json=geoInfo,proto3" json:"geo_info,omitempty"`
	LevelId              uint32                                 `protobuf:"varint,10,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	TraceInfo            *topic_channel.RecommendationTraceInfo `protobuf:"bytes,11,opt,name=trace_info,json=traceInfo,proto3" json:"trace_info,omitempty"`
	TabId                uint32                                 `protobuf:"varint,12,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string                                 `protobuf:"bytes,13,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	OwnerMismatchVersion bool                                   `protobuf:"varint,14,opt,name=owner_mismatch_version,json=ownerMismatchVersion,proto3" json:"owner_mismatch_version,omitempty"`
	Source               GenViewSource                          `protobuf:"varint,15,opt,name=source,proto3,enum=ga.channel_play.GenViewSource" json:"source,omitempty"`
	PersonCert           []*PersonalCert                        `protobuf:"bytes,16,rep,name=person_cert,json=personCert,proto3" json:"person_cert,omitempty"`
	Label                ChannelLabel                           `protobuf:"varint,17,opt,name=label,proto3,enum=ga.channel_play.ChannelLabel" json:"label,omitempty"`
	UnderHeadImgLabel    string                                 `protobuf:"bytes,18,opt,name=under_head_img_label,json=underHeadImgLabel,proto3" json:"under_head_img_label,omitempty"`
	MiddlePositionLabel  string                                 `protobuf:"bytes,19,opt,name=middle_position_label,json=middlePositionLabel,proto3" json:"middle_position_label,omitempty"`
	InChannelText        string                                 `protobuf:"bytes,20,opt,name=in_channel_text,json=inChannelText,proto3" json:"in_channel_text,omitempty"`
	FriendInfo           *TopicChannelItem_FriendInfo           `protobuf:"bytes,21,opt,name=friend_info,json=friendInfo,proto3" json:"friend_info,omitempty"`
	DeliveryType         string                                 `protobuf:"bytes,22,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	RelationshipLabels   []string                               `protobuf:"bytes,23,rep,name=relationship_labels,json=relationshipLabels,proto3" json:"relationship_labels,omitempty"`
	OnMicMemberCount     uint32                                 `protobuf:"varint,24,opt,name=on_mic_member_count,json=onMicMemberCount,proto3" json:"on_mic_member_count,omitempty"`
	CategoryType         uint32                                 `protobuf:"varint,25,opt,name=category_type,json=categoryType,proto3" json:"category_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *TopicChannelItem) Reset()         { *m = TopicChannelItem{} }
func (m *TopicChannelItem) String() string { return proto.CompactTextString(m) }
func (*TopicChannelItem) ProtoMessage()    {}
func (*TopicChannelItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{2}
}
func (m *TopicChannelItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelItem.Unmarshal(m, b)
}
func (m *TopicChannelItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelItem.Marshal(b, m, deterministic)
}
func (dst *TopicChannelItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelItem.Merge(dst, src)
}
func (m *TopicChannelItem) XXX_Size() int {
	return xxx_messageInfo_TopicChannelItem.Size(m)
}
func (m *TopicChannelItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelItem.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelItem proto.InternalMessageInfo

func (m *TopicChannelItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TopicChannelItem) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *TopicChannelItem) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *TopicChannelItem) GetChannelOwnerUsername() string {
	if m != nil {
		return m.ChannelOwnerUsername
	}
	return ""
}

func (m *TopicChannelItem) GetChannelOwnerSex() int32 {
	if m != nil {
		return m.ChannelOwnerSex
	}
	return 0
}

func (m *TopicChannelItem) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func (m *TopicChannelItem) GetTopicChannelView() *TopicChannelView {
	if m != nil {
		return m.TopicChannelView
	}
	return nil
}

func (m *TopicChannelItem) GetRcmdLabel() RCMDLabelEnum {
	if m != nil {
		return m.RcmdLabel
	}
	return RCMDLabelEnum_RCMDLabel_None
}

func (m *TopicChannelItem) GetGeoInfo() string {
	if m != nil {
		return m.GeoInfo
	}
	return ""
}

func (m *TopicChannelItem) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *TopicChannelItem) GetTraceInfo() *topic_channel.RecommendationTraceInfo {
	if m != nil {
		return m.TraceInfo
	}
	return nil
}

func (m *TopicChannelItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *TopicChannelItem) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *TopicChannelItem) GetOwnerMismatchVersion() bool {
	if m != nil {
		return m.OwnerMismatchVersion
	}
	return false
}

func (m *TopicChannelItem) GetSource() GenViewSource {
	if m != nil {
		return m.Source
	}
	return GenViewSource_FROM_GAME
}

func (m *TopicChannelItem) GetPersonCert() []*PersonalCert {
	if m != nil {
		return m.PersonCert
	}
	return nil
}

func (m *TopicChannelItem) GetLabel() ChannelLabel {
	if m != nil {
		return m.Label
	}
	return ChannelLabel_ChannelLabelNone
}

func (m *TopicChannelItem) GetUnderHeadImgLabel() string {
	if m != nil {
		return m.UnderHeadImgLabel
	}
	return ""
}

func (m *TopicChannelItem) GetMiddlePositionLabel() string {
	if m != nil {
		return m.MiddlePositionLabel
	}
	return ""
}

func (m *TopicChannelItem) GetInChannelText() string {
	if m != nil {
		return m.InChannelText
	}
	return ""
}

func (m *TopicChannelItem) GetFriendInfo() *TopicChannelItem_FriendInfo {
	if m != nil {
		return m.FriendInfo
	}
	return nil
}

func (m *TopicChannelItem) GetDeliveryType() string {
	if m != nil {
		return m.DeliveryType
	}
	return ""
}

func (m *TopicChannelItem) GetRelationshipLabels() []string {
	if m != nil {
		return m.RelationshipLabels
	}
	return nil
}

func (m *TopicChannelItem) GetOnMicMemberCount() uint32 {
	if m != nil {
		return m.OnMicMemberCount
	}
	return 0
}

func (m *TopicChannelItem) GetCategoryType() uint32 {
	if m != nil {
		return m.CategoryType
	}
	return 0
}

type TopicChannelItem_FriendInfo struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicChannelItem_FriendInfo) Reset()         { *m = TopicChannelItem_FriendInfo{} }
func (m *TopicChannelItem_FriendInfo) String() string { return proto.CompactTextString(m) }
func (*TopicChannelItem_FriendInfo) ProtoMessage()    {}
func (*TopicChannelItem_FriendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{2, 0}
}
func (m *TopicChannelItem_FriendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelItem_FriendInfo.Unmarshal(m, b)
}
func (m *TopicChannelItem_FriendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelItem_FriendInfo.Marshal(b, m, deterministic)
}
func (dst *TopicChannelItem_FriendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelItem_FriendInfo.Merge(dst, src)
}
func (m *TopicChannelItem_FriendInfo) XXX_Size() int {
	return xxx_messageInfo_TopicChannelItem_FriendInfo.Size(m)
}
func (m *TopicChannelItem_FriendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelItem_FriendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelItem_FriendInfo proto.InternalMessageInfo

func (m *TopicChannelItem_FriendInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *TopicChannelItem_FriendInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *TopicChannelItem_FriendInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type FilterBlockOption struct {
	BlockId              uint32     `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32     `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	FilterType           FilterType `protobuf:"varint,3,opt,name=filter_type,json=filterType,proto3,enum=ga.channel_play.FilterType" json:"filter_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *FilterBlockOption) Reset()         { *m = FilterBlockOption{} }
func (m *FilterBlockOption) String() string { return proto.CompactTextString(m) }
func (*FilterBlockOption) ProtoMessage()    {}
func (*FilterBlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{3}
}
func (m *FilterBlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterBlockOption.Unmarshal(m, b)
}
func (m *FilterBlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterBlockOption.Marshal(b, m, deterministic)
}
func (dst *FilterBlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterBlockOption.Merge(dst, src)
}
func (m *FilterBlockOption) XXX_Size() int {
	return xxx_messageInfo_FilterBlockOption.Size(m)
}
func (m *FilterBlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterBlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_FilterBlockOption proto.InternalMessageInfo

func (m *FilterBlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *FilterBlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

func (m *FilterBlockOption) GetFilterType() FilterType {
	if m != nil {
		return m.FilterType
	}
	return FilterType_Base_Block_Filter
}

type ListTopicChannelReq struct {
	BaseReq          *app.BaseReq         `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Count            uint32               `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	BlockOption      []*FilterBlockOption `protobuf:"bytes,3,rep,name=block_option,json=blockOption,proto3" json:"block_option,omitempty"`
	TabId            uint32               `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Sex              RcmdSex              `protobuf:"varint,5,opt,name=sex,proto3,enum=ga.channel_play.RcmdSex" json:"sex,omitempty"`
	GetMode          uint32               `protobuf:"varint,6,opt,name=get_mode,json=getMode,proto3" json:"get_mode,omitempty"`
	ChannelPackageId string               `protobuf:"bytes,7,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	NoBrowseList     []uint32             `protobuf:"varint,8,rep,packed,name=no_browse_list,json=noBrowseList,proto3" json:"no_browse_list,omitempty"`
	CategoryIds      []uint32             `protobuf:"varint,9,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	TabIds           []uint32             `protobuf:"varint,10,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	// 用户询问, 喜好游戏及标签
	PrefGames []*topic_channel.PrefGame `protobuf:"bytes,11,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	// 玩法标签数组
	Labels []*topic_channel.GameLabel `protobuf:"bytes,12,rep,name=labels,proto3" json:"labels,omitempty"`
	// 区分首页入口
	ChannelListEnterSource ChannelListEnterSource `protobuf:"varint,13,opt,name=channel_list_enter_source,json=channelListEnterSource,proto3,enum=ga.channel_play.ChannelListEnterSource" json:"channel_list_enter_source,omitempty"`
	// 房间列表样式
	ListStyleType ChannelListStyleType `protobuf:"varint,14,opt,name=list_style_type,json=listStyleType,proto3,enum=ga.channel_play.ChannelListStyleType" json:"list_style_type,omitempty"`
	// 提供view的来源标识，MUSIC代表由音乐业务方提供ktv view，MYSTERY代表由迷境业务方提供mystery view,不填或则填GAME则走旧逻辑所有view由开黑提供
	GenViewSource []GenViewSource `protobuf:"varint,15,rep,packed,name=gen_view_source,json=genViewSource,proto3,enum=ga.channel_play.GenViewSource" json:"gen_view_source,omitempty"`
	// 是否开启热门玩法
	EnableGameLabel bool `protobuf:"varint,16,opt,name=enable_game_label,json=enableGameLabel,proto3" json:"enable_game_label,omitempty"`
	// 兴趣标签
	InterestLabels []string `protobuf:"bytes,17,rep,name=interest_labels,json=interestLabels,proto3" json:"interest_labels,omitempty"`
	DeliveryType   string   `protobuf:"bytes,18,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	// 用户设置的屏蔽词
	ShieldFilterWords []string `protobuf:"bytes,19,rep,name=shield_filter_words,json=shieldFilterWords,proto3" json:"shield_filter_words,omitempty"`
	// 分类标签筛选
	ClassifyLabels []*ClassifyLabelList `protobuf:"bytes,20,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	// 音乐玩法房间列表传参
	FilterId               string                          `protobuf:"bytes,21,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	SubFilterIds           []string                        `protobuf:"bytes,22,rep,name=sub_filter_ids,json=subFilterIds,proto3" json:"sub_filter_ids,omitempty"`
	IsUserLocationAuthOpen bool                            `protobuf:"varint,23,opt,name=is_user_location_auth_open,json=isUserLocationAuthOpen,proto3" json:"is_user_location_auth_open,omitempty"`
	FilterModel            ListTopicChannelReq_FilterModel `protobuf:"varint,24,opt,name=filter_model,json=filterModel,proto3,enum=ga.channel_play.ListTopicChannelReq_FilterModel" json:"filter_model,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                        `json:"-"`
	XXX_unrecognized       []byte                          `json:"-"`
	XXX_sizecache          int32                           `json:"-"`
}

func (m *ListTopicChannelReq) Reset()         { *m = ListTopicChannelReq{} }
func (m *ListTopicChannelReq) String() string { return proto.CompactTextString(m) }
func (*ListTopicChannelReq) ProtoMessage()    {}
func (*ListTopicChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{4}
}
func (m *ListTopicChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListTopicChannelReq.Unmarshal(m, b)
}
func (m *ListTopicChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListTopicChannelReq.Marshal(b, m, deterministic)
}
func (dst *ListTopicChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListTopicChannelReq.Merge(dst, src)
}
func (m *ListTopicChannelReq) XXX_Size() int {
	return xxx_messageInfo_ListTopicChannelReq.Size(m)
}
func (m *ListTopicChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListTopicChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListTopicChannelReq proto.InternalMessageInfo

func (m *ListTopicChannelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListTopicChannelReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListTopicChannelReq) GetBlockOption() []*FilterBlockOption {
	if m != nil {
		return m.BlockOption
	}
	return nil
}

func (m *ListTopicChannelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ListTopicChannelReq) GetSex() RcmdSex {
	if m != nil {
		return m.Sex
	}
	return RcmdSex_All
}

func (m *ListTopicChannelReq) GetGetMode() uint32 {
	if m != nil {
		return m.GetMode
	}
	return 0
}

func (m *ListTopicChannelReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *ListTopicChannelReq) GetNoBrowseList() []uint32 {
	if m != nil {
		return m.NoBrowseList
	}
	return nil
}

func (m *ListTopicChannelReq) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

func (m *ListTopicChannelReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *ListTopicChannelReq) GetPrefGames() []*topic_channel.PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *ListTopicChannelReq) GetLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *ListTopicChannelReq) GetChannelListEnterSource() ChannelListEnterSource {
	if m != nil {
		return m.ChannelListEnterSource
	}
	return ChannelListEnterSource_GangupHomePage
}

func (m *ListTopicChannelReq) GetListStyleType() ChannelListStyleType {
	if m != nil {
		return m.ListStyleType
	}
	return ChannelListStyleType_Default_Group
}

func (m *ListTopicChannelReq) GetGenViewSource() []GenViewSource {
	if m != nil {
		return m.GenViewSource
	}
	return nil
}

func (m *ListTopicChannelReq) GetEnableGameLabel() bool {
	if m != nil {
		return m.EnableGameLabel
	}
	return false
}

func (m *ListTopicChannelReq) GetInterestLabels() []string {
	if m != nil {
		return m.InterestLabels
	}
	return nil
}

func (m *ListTopicChannelReq) GetDeliveryType() string {
	if m != nil {
		return m.DeliveryType
	}
	return ""
}

func (m *ListTopicChannelReq) GetShieldFilterWords() []string {
	if m != nil {
		return m.ShieldFilterWords
	}
	return nil
}

func (m *ListTopicChannelReq) GetClassifyLabels() []*ClassifyLabelList {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

func (m *ListTopicChannelReq) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *ListTopicChannelReq) GetSubFilterIds() []string {
	if m != nil {
		return m.SubFilterIds
	}
	return nil
}

func (m *ListTopicChannelReq) GetIsUserLocationAuthOpen() bool {
	if m != nil {
		return m.IsUserLocationAuthOpen
	}
	return false
}

func (m *ListTopicChannelReq) GetFilterModel() ListTopicChannelReq_FilterModel {
	if m != nil {
		return m.FilterModel
	}
	return ListTopicChannelReq_FILTER_MODEL_MT
}

type ListTopicChannelResp struct {
	BaseResp   *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items      []*TopicChannelItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	LoadFinish bool                `protobuf:"varint,3,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	// 预约开黑相关配置
	GangConf *topic_channel.GangConf `protobuf:"bytes,4,opt,name=gang_conf,json=gangConf,proto3" json:"gang_conf,omitempty"`
	// 用户询问, 喜好游戏及标签
	PrefGames []*topic_channel.PrefGame `protobuf:"bytes,5,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	// 用户喜好游戏询问插入下标
	PrefGamePos          uint32                           `protobuf:"varint,6,opt,name=pref_game_pos,json=prefGamePos,proto3" json:"pref_game_pos,omitempty"`
	ReportData           *ListTopicChannelResp_DataReport `protobuf:"bytes,7,opt,name=report_data,json=reportData,proto3" json:"report_data,omitempty"`
	IsRecommendDoudi     bool                             `protobuf:"varint,8,opt,name=is_recommend_doudi,json=isRecommendDoudi,proto3" json:"is_recommend_doudi,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *ListTopicChannelResp) Reset()         { *m = ListTopicChannelResp{} }
func (m *ListTopicChannelResp) String() string { return proto.CompactTextString(m) }
func (*ListTopicChannelResp) ProtoMessage()    {}
func (*ListTopicChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{5}
}
func (m *ListTopicChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListTopicChannelResp.Unmarshal(m, b)
}
func (m *ListTopicChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListTopicChannelResp.Marshal(b, m, deterministic)
}
func (dst *ListTopicChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListTopicChannelResp.Merge(dst, src)
}
func (m *ListTopicChannelResp) XXX_Size() int {
	return xxx_messageInfo_ListTopicChannelResp.Size(m)
}
func (m *ListTopicChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListTopicChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListTopicChannelResp proto.InternalMessageInfo

func (m *ListTopicChannelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListTopicChannelResp) GetItems() []*TopicChannelItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *ListTopicChannelResp) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

func (m *ListTopicChannelResp) GetGangConf() *topic_channel.GangConf {
	if m != nil {
		return m.GangConf
	}
	return nil
}

func (m *ListTopicChannelResp) GetPrefGames() []*topic_channel.PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *ListTopicChannelResp) GetPrefGamePos() uint32 {
	if m != nil {
		return m.PrefGamePos
	}
	return 0
}

func (m *ListTopicChannelResp) GetReportData() *ListTopicChannelResp_DataReport {
	if m != nil {
		return m.ReportData
	}
	return nil
}

func (m *ListTopicChannelResp) GetIsRecommendDoudi() bool {
	if m != nil {
		return m.IsRecommendDoudi
	}
	return false
}

// 仅用于客户端数据上报字段
type ListTopicChannelResp_DataReport struct {
	Footprint            string   `protobuf:"bytes,1,opt,name=footprint,proto3" json:"footprint,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListTopicChannelResp_DataReport) Reset()         { *m = ListTopicChannelResp_DataReport{} }
func (m *ListTopicChannelResp_DataReport) String() string { return proto.CompactTextString(m) }
func (*ListTopicChannelResp_DataReport) ProtoMessage()    {}
func (*ListTopicChannelResp_DataReport) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{5, 0}
}
func (m *ListTopicChannelResp_DataReport) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListTopicChannelResp_DataReport.Unmarshal(m, b)
}
func (m *ListTopicChannelResp_DataReport) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListTopicChannelResp_DataReport.Marshal(b, m, deterministic)
}
func (dst *ListTopicChannelResp_DataReport) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListTopicChannelResp_DataReport.Merge(dst, src)
}
func (m *ListTopicChannelResp_DataReport) XXX_Size() int {
	return xxx_messageInfo_ListTopicChannelResp_DataReport.Size(m)
}
func (m *ListTopicChannelResp_DataReport) XXX_DiscardUnknown() {
	xxx_messageInfo_ListTopicChannelResp_DataReport.DiscardUnknown(m)
}

var xxx_messageInfo_ListTopicChannelResp_DataReport proto.InternalMessageInfo

func (m *ListTopicChannelResp_DataReport) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

type GetSecondaryFilterReq struct {
	BaseReq              *app.BaseReq                 `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32                       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Mode                 GetSecondaryFilterReq_Mode   `protobuf:"varint,3,opt,name=mode,proto3,enum=ga.channel_play.GetSecondaryFilterReq_Mode" json:"mode,omitempty"`
	Source               GetSecondaryFilterReq_Source `protobuf:"varint,4,opt,name=source,proto3,enum=ga.channel_play.GetSecondaryFilterReq_Source" json:"source,omitempty"`
	BrowseLabels         *topic_channel.BrowseLabel   `protobuf:"bytes,5,opt,name=browse_labels,json=browseLabels,proto3" json:"browse_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetSecondaryFilterReq) Reset()         { *m = GetSecondaryFilterReq{} }
func (m *GetSecondaryFilterReq) String() string { return proto.CompactTextString(m) }
func (*GetSecondaryFilterReq) ProtoMessage()    {}
func (*GetSecondaryFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{6}
}
func (m *GetSecondaryFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSecondaryFilterReq.Unmarshal(m, b)
}
func (m *GetSecondaryFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSecondaryFilterReq.Marshal(b, m, deterministic)
}
func (dst *GetSecondaryFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSecondaryFilterReq.Merge(dst, src)
}
func (m *GetSecondaryFilterReq) XXX_Size() int {
	return xxx_messageInfo_GetSecondaryFilterReq.Size(m)
}
func (m *GetSecondaryFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSecondaryFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSecondaryFilterReq proto.InternalMessageInfo

func (m *GetSecondaryFilterReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSecondaryFilterReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetSecondaryFilterReq) GetMode() GetSecondaryFilterReq_Mode {
	if m != nil {
		return m.Mode
	}
	return GetSecondaryFilterReq_PUBCHANNEL
}

func (m *GetSecondaryFilterReq) GetSource() GetSecondaryFilterReq_Source {
	if m != nil {
		return m.Source
	}
	return GetSecondaryFilterReq_SourceHome
}

func (m *GetSecondaryFilterReq) GetBrowseLabels() *topic_channel.BrowseLabel {
	if m != nil {
		return m.BrowseLabels
	}
	return nil
}

type GetSecondaryFilterResp struct {
	BaseResp          *app.BaseResp                    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Blocks            []*topic_channel.Block           `protobuf:"bytes,2,rep,name=blocks,proto3" json:"blocks,omitempty"`
	TabId             uint32                           `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabType           GetSecondaryFilterResp_TabType   `protobuf:"varint,4,opt,name=tab_type,json=tabType,proto3,enum=ga.channel_play.GetSecondaryFilterResp_TabType" json:"tab_type,omitempty"`
	MatchType         GetSecondaryFilterResp_MatchType `protobuf:"varint,5,opt,name=match_type,json=matchType,proto3,enum=ga.channel_play.GetSecondaryFilterResp_MatchType" json:"match_type,omitempty"`
	ChannelSource     uint32                           `protobuf:"varint,6,opt,name=channel_source,json=channelSource,proto3" json:"channel_source,omitempty"`
	TabVersion        uint32                           `protobuf:"varint,7,opt,name=tab_version,json=tabVersion,proto3" json:"tab_version,omitempty"`
	FreshOption       bool                             `protobuf:"varint,8,opt,name=fresh_option,json=freshOption,proto3" json:"fresh_option,omitempty"`
	IsHiddenGeoOption bool                             `protobuf:"varint,9,opt,name=is_hidden_geo_option,json=isHiddenGeoOption,proto3" json:"is_hidden_geo_option,omitempty"`
	BusinessBlocks    []*BusinessFilterItem            `protobuf:"bytes,10,rep,name=business_blocks,json=businessBlocks,proto3" json:"business_blocks,omitempty"`
	NeedHighlight     bool                             `protobuf:"varint,11,opt,name=need_highlight,json=needHighlight,proto3" json:"need_highlight,omitempty"`
	DisplayBlockInfos []*DisplayBlockInfo              `protobuf:"bytes,12,rep,name=display_block_infos,json=displayBlockInfos,proto3" json:"display_block_infos,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	GuidingForNewBie string `protobuf:"bytes,13,opt,name=GuidingForNewBie,proto3" json:"GuidingForNewBie,omitempty"`
	// 玩法标签数组
	Labels    []*topic_channel.GameLabel `protobuf:"bytes,14,rep,name=labels,proto3" json:"labels,omitempty"`
	NoticeMsg string                     `protobuf:"bytes,15,opt,name=notice_msg,json=noticeMsg,proto3" json:"notice_msg,omitempty"`
	// 是否展示用户设置的屏蔽词
	ShowFilterWords      bool                 `protobuf:"varint,16,opt,name=show_filter_words,json=showFilterWords,proto3" json:"show_filter_words,omitempty"`
	ClassifyLabels       []*ClassifyLabelList `protobuf:"bytes,17,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetSecondaryFilterResp) Reset()         { *m = GetSecondaryFilterResp{} }
func (m *GetSecondaryFilterResp) String() string { return proto.CompactTextString(m) }
func (*GetSecondaryFilterResp) ProtoMessage()    {}
func (*GetSecondaryFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{7}
}
func (m *GetSecondaryFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSecondaryFilterResp.Unmarshal(m, b)
}
func (m *GetSecondaryFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSecondaryFilterResp.Marshal(b, m, deterministic)
}
func (dst *GetSecondaryFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSecondaryFilterResp.Merge(dst, src)
}
func (m *GetSecondaryFilterResp) XXX_Size() int {
	return xxx_messageInfo_GetSecondaryFilterResp.Size(m)
}
func (m *GetSecondaryFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSecondaryFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSecondaryFilterResp proto.InternalMessageInfo

func (m *GetSecondaryFilterResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSecondaryFilterResp) GetBlocks() []*topic_channel.Block {
	if m != nil {
		return m.Blocks
	}
	return nil
}

func (m *GetSecondaryFilterResp) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetSecondaryFilterResp) GetTabType() GetSecondaryFilterResp_TabType {
	if m != nil {
		return m.TabType
	}
	return GetSecondaryFilterResp_NORMAL
}

func (m *GetSecondaryFilterResp) GetMatchType() GetSecondaryFilterResp_MatchType {
	if m != nil {
		return m.MatchType
	}
	return GetSecondaryFilterResp_QUICK
}

func (m *GetSecondaryFilterResp) GetChannelSource() uint32 {
	if m != nil {
		return m.ChannelSource
	}
	return 0
}

func (m *GetSecondaryFilterResp) GetTabVersion() uint32 {
	if m != nil {
		return m.TabVersion
	}
	return 0
}

func (m *GetSecondaryFilterResp) GetFreshOption() bool {
	if m != nil {
		return m.FreshOption
	}
	return false
}

func (m *GetSecondaryFilterResp) GetIsHiddenGeoOption() bool {
	if m != nil {
		return m.IsHiddenGeoOption
	}
	return false
}

func (m *GetSecondaryFilterResp) GetBusinessBlocks() []*BusinessFilterItem {
	if m != nil {
		return m.BusinessBlocks
	}
	return nil
}

func (m *GetSecondaryFilterResp) GetNeedHighlight() bool {
	if m != nil {
		return m.NeedHighlight
	}
	return false
}

func (m *GetSecondaryFilterResp) GetDisplayBlockInfos() []*DisplayBlockInfo {
	if m != nil {
		return m.DisplayBlockInfos
	}
	return nil
}

func (m *GetSecondaryFilterResp) GetGuidingForNewBie() string {
	if m != nil {
		return m.GuidingForNewBie
	}
	return ""
}

func (m *GetSecondaryFilterResp) GetLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *GetSecondaryFilterResp) GetNoticeMsg() string {
	if m != nil {
		return m.NoticeMsg
	}
	return ""
}

func (m *GetSecondaryFilterResp) GetShowFilterWords() bool {
	if m != nil {
		return m.ShowFilterWords
	}
	return false
}

func (m *GetSecondaryFilterResp) GetClassifyLabels() []*ClassifyLabelList {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

// 发布二级字段绑定的一级字段id
type ElemBindBlockInfo struct {
	ElemId               uint32                 `protobuf:"varint,1,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	BindBlocks           []*topic_channel.Block `protobuf:"bytes,2,rep,name=bind_blocks,json=bindBlocks,proto3" json:"bind_blocks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ElemBindBlockInfo) Reset()         { *m = ElemBindBlockInfo{} }
func (m *ElemBindBlockInfo) String() string { return proto.CompactTextString(m) }
func (*ElemBindBlockInfo) ProtoMessage()    {}
func (*ElemBindBlockInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{8}
}
func (m *ElemBindBlockInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ElemBindBlockInfo.Unmarshal(m, b)
}
func (m *ElemBindBlockInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ElemBindBlockInfo.Marshal(b, m, deterministic)
}
func (dst *ElemBindBlockInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ElemBindBlockInfo.Merge(dst, src)
}
func (m *ElemBindBlockInfo) XXX_Size() int {
	return xxx_messageInfo_ElemBindBlockInfo.Size(m)
}
func (m *ElemBindBlockInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ElemBindBlockInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ElemBindBlockInfo proto.InternalMessageInfo

func (m *ElemBindBlockInfo) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

func (m *ElemBindBlockInfo) GetBindBlocks() []*topic_channel.Block {
	if m != nil {
		return m.BindBlocks
	}
	return nil
}

// 客户端外显发布字段关系
type DisplayBlockInfo struct {
	Block                *topic_channel.Block `protobuf:"bytes,1,opt,name=block,proto3" json:"block,omitempty"`
	ElemBindBlockInfos   []*ElemBindBlockInfo `protobuf:"bytes,2,rep,name=elem_bind_block_infos,json=elemBindBlockInfos,proto3" json:"elem_bind_block_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *DisplayBlockInfo) Reset()         { *m = DisplayBlockInfo{} }
func (m *DisplayBlockInfo) String() string { return proto.CompactTextString(m) }
func (*DisplayBlockInfo) ProtoMessage()    {}
func (*DisplayBlockInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{9}
}
func (m *DisplayBlockInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisplayBlockInfo.Unmarshal(m, b)
}
func (m *DisplayBlockInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisplayBlockInfo.Marshal(b, m, deterministic)
}
func (dst *DisplayBlockInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisplayBlockInfo.Merge(dst, src)
}
func (m *DisplayBlockInfo) XXX_Size() int {
	return xxx_messageInfo_DisplayBlockInfo.Size(m)
}
func (m *DisplayBlockInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DisplayBlockInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DisplayBlockInfo proto.InternalMessageInfo

func (m *DisplayBlockInfo) GetBlock() *topic_channel.Block {
	if m != nil {
		return m.Block
	}
	return nil
}

func (m *DisplayBlockInfo) GetElemBindBlockInfos() []*ElemBindBlockInfo {
	if m != nil {
		return m.ElemBindBlockInfos
	}
	return nil
}

type BusinessFilterItem struct {
	FilterType           FilterType              `protobuf:"varint,1,opt,name=filter_type,json=filterType,proto3,enum=ga.channel_play.FilterType" json:"filter_type,omitempty"`
	Id                   uint32                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Title                string                  `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Mode                 BusinessFilterItem_Mode `protobuf:"varint,4,opt,name=mode,proto3,enum=ga.channel_play.BusinessFilterItem_Mode" json:"mode,omitempty"`
	Elems                []*BusinessFilterElem   `protobuf:"bytes,5,rep,name=elems,proto3" json:"elems,omitempty"`
	MostSelectNum        uint32                  `protobuf:"varint,6,opt,name=most_select_num,json=mostSelectNum,proto3" json:"most_select_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BusinessFilterItem) Reset()         { *m = BusinessFilterItem{} }
func (m *BusinessFilterItem) String() string { return proto.CompactTextString(m) }
func (*BusinessFilterItem) ProtoMessage()    {}
func (*BusinessFilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{10}
}
func (m *BusinessFilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessFilterItem.Unmarshal(m, b)
}
func (m *BusinessFilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessFilterItem.Marshal(b, m, deterministic)
}
func (dst *BusinessFilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessFilterItem.Merge(dst, src)
}
func (m *BusinessFilterItem) XXX_Size() int {
	return xxx_messageInfo_BusinessFilterItem.Size(m)
}
func (m *BusinessFilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessFilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessFilterItem proto.InternalMessageInfo

func (m *BusinessFilterItem) GetFilterType() FilterType {
	if m != nil {
		return m.FilterType
	}
	return FilterType_Base_Block_Filter
}

func (m *BusinessFilterItem) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BusinessFilterItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *BusinessFilterItem) GetMode() BusinessFilterItem_Mode {
	if m != nil {
		return m.Mode
	}
	return BusinessFilterItem_SINGLE
}

func (m *BusinessFilterItem) GetElems() []*BusinessFilterElem {
	if m != nil {
		return m.Elems
	}
	return nil
}

func (m *BusinessFilterItem) GetMostSelectNum() uint32 {
	if m != nil {
		return m.MostSelectNum
	}
	return 0
}

// 业务筛选器Elem 元素，多个元素构成一个栏目
type BusinessFilterElem struct {
	Id                   uint32                                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string                                `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Mode                 BusinessFilterElem_BusinessFilterMode `protobuf:"varint,3,opt,name=mode,proto3,enum=ga.channel_play.BusinessFilterElem_BusinessFilterMode" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *BusinessFilterElem) Reset()         { *m = BusinessFilterElem{} }
func (m *BusinessFilterElem) String() string { return proto.CompactTextString(m) }
func (*BusinessFilterElem) ProtoMessage()    {}
func (*BusinessFilterElem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{11}
}
func (m *BusinessFilterElem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessFilterElem.Unmarshal(m, b)
}
func (m *BusinessFilterElem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessFilterElem.Marshal(b, m, deterministic)
}
func (dst *BusinessFilterElem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessFilterElem.Merge(dst, src)
}
func (m *BusinessFilterElem) XXX_Size() int {
	return xxx_messageInfo_BusinessFilterElem.Size(m)
}
func (m *BusinessFilterElem) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessFilterElem.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessFilterElem proto.InternalMessageInfo

func (m *BusinessFilterElem) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BusinessFilterElem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *BusinessFilterElem) GetMode() BusinessFilterElem_BusinessFilterMode {
	if m != nil {
		return m.Mode
	}
	return BusinessFilterElem_NORMAL
}

type GetSecondaryFilterByCategoryReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CategoryId           uint32       `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	ChannelPkg           string       `protobuf:"bytes,3,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSecondaryFilterByCategoryReq) Reset()         { *m = GetSecondaryFilterByCategoryReq{} }
func (m *GetSecondaryFilterByCategoryReq) String() string { return proto.CompactTextString(m) }
func (*GetSecondaryFilterByCategoryReq) ProtoMessage()    {}
func (*GetSecondaryFilterByCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{12}
}
func (m *GetSecondaryFilterByCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSecondaryFilterByCategoryReq.Unmarshal(m, b)
}
func (m *GetSecondaryFilterByCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSecondaryFilterByCategoryReq.Marshal(b, m, deterministic)
}
func (dst *GetSecondaryFilterByCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSecondaryFilterByCategoryReq.Merge(dst, src)
}
func (m *GetSecondaryFilterByCategoryReq) XXX_Size() int {
	return xxx_messageInfo_GetSecondaryFilterByCategoryReq.Size(m)
}
func (m *GetSecondaryFilterByCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSecondaryFilterByCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSecondaryFilterByCategoryReq proto.InternalMessageInfo

func (m *GetSecondaryFilterByCategoryReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSecondaryFilterByCategoryReq) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *GetSecondaryFilterByCategoryReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

type GetSecondaryFilterByCategoryResp struct {
	BaseResp             *app.BaseResp                           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items                []*hobby_channel.GameHomePageFilterItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	BusinessBlocks       []*BusinessFilterItem                   `protobuf:"bytes,3,rep,name=business_blocks,json=businessBlocks,proto3" json:"business_blocks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *GetSecondaryFilterByCategoryResp) Reset()         { *m = GetSecondaryFilterByCategoryResp{} }
func (m *GetSecondaryFilterByCategoryResp) String() string { return proto.CompactTextString(m) }
func (*GetSecondaryFilterByCategoryResp) ProtoMessage()    {}
func (*GetSecondaryFilterByCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{13}
}
func (m *GetSecondaryFilterByCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSecondaryFilterByCategoryResp.Unmarshal(m, b)
}
func (m *GetSecondaryFilterByCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSecondaryFilterByCategoryResp.Marshal(b, m, deterministic)
}
func (dst *GetSecondaryFilterByCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSecondaryFilterByCategoryResp.Merge(dst, src)
}
func (m *GetSecondaryFilterByCategoryResp) XXX_Size() int {
	return xxx_messageInfo_GetSecondaryFilterByCategoryResp.Size(m)
}
func (m *GetSecondaryFilterByCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSecondaryFilterByCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSecondaryFilterByCategoryResp proto.InternalMessageInfo

func (m *GetSecondaryFilterByCategoryResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSecondaryFilterByCategoryResp) GetItems() []*hobby_channel.GameHomePageFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetSecondaryFilterByCategoryResp) GetBusinessBlocks() []*BusinessFilterItem {
	if m != nil {
		return m.BusinessBlocks
	}
	return nil
}

type DefaultRoomNameConfig struct {
	Name   string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ElemId uint32 `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	// 透传给客户端埋点上报
	Source               uint32   `protobuf:"varint,3,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DefaultRoomNameConfig) Reset()         { *m = DefaultRoomNameConfig{} }
func (m *DefaultRoomNameConfig) String() string { return proto.CompactTextString(m) }
func (*DefaultRoomNameConfig) ProtoMessage()    {}
func (*DefaultRoomNameConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{14}
}
func (m *DefaultRoomNameConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DefaultRoomNameConfig.Unmarshal(m, b)
}
func (m *DefaultRoomNameConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DefaultRoomNameConfig.Marshal(b, m, deterministic)
}
func (dst *DefaultRoomNameConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DefaultRoomNameConfig.Merge(dst, src)
}
func (m *DefaultRoomNameConfig) XXX_Size() int {
	return xxx_messageInfo_DefaultRoomNameConfig.Size(m)
}
func (m *DefaultRoomNameConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_DefaultRoomNameConfig.DiscardUnknown(m)
}

var xxx_messageInfo_DefaultRoomNameConfig proto.InternalMessageInfo

func (m *DefaultRoomNameConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DefaultRoomNameConfig) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

func (m *DefaultRoomNameConfig) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type GetDefaultRoomNameListReq struct {
	BaseReq   *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId     uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	RoomName  string       `protobuf:"bytes,3,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	PageIndex uint32       `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	// 请求推荐用:1-加载更多,2-刷新
	GetMode              uint32   `protobuf:"varint,5,opt,name=get_mode,json=getMode,proto3" json:"get_mode,omitempty"`
	ReqSource            uint32   `protobuf:"varint,6,opt,name=req_source,json=reqSource,proto3" json:"req_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDefaultRoomNameListReq) Reset()         { *m = GetDefaultRoomNameListReq{} }
func (m *GetDefaultRoomNameListReq) String() string { return proto.CompactTextString(m) }
func (*GetDefaultRoomNameListReq) ProtoMessage()    {}
func (*GetDefaultRoomNameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{15}
}
func (m *GetDefaultRoomNameListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDefaultRoomNameListReq.Unmarshal(m, b)
}
func (m *GetDefaultRoomNameListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDefaultRoomNameListReq.Marshal(b, m, deterministic)
}
func (dst *GetDefaultRoomNameListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDefaultRoomNameListReq.Merge(dst, src)
}
func (m *GetDefaultRoomNameListReq) XXX_Size() int {
	return xxx_messageInfo_GetDefaultRoomNameListReq.Size(m)
}
func (m *GetDefaultRoomNameListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDefaultRoomNameListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDefaultRoomNameListReq proto.InternalMessageInfo

func (m *GetDefaultRoomNameListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetDefaultRoomNameListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetDefaultRoomNameListReq) GetRoomName() string {
	if m != nil {
		return m.RoomName
	}
	return ""
}

func (m *GetDefaultRoomNameListReq) GetPageIndex() uint32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetDefaultRoomNameListReq) GetGetMode() uint32 {
	if m != nil {
		return m.GetMode
	}
	return 0
}

func (m *GetDefaultRoomNameListReq) GetReqSource() uint32 {
	if m != nil {
		return m.ReqSource
	}
	return 0
}

type GetDefaultRoomNameListResp struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DefaultRoomNameList  []*DefaultRoomNameConfig `protobuf:"bytes,2,rep,name=default_room_name_list,json=defaultRoomNameList,proto3" json:"default_room_name_list,omitempty"`
	NextPageIndex        uint32                   `protobuf:"varint,3,opt,name=next_page_index,json=nextPageIndex,proto3" json:"next_page_index,omitempty"`
	LoadFinish           bool                     `protobuf:"varint,4,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetDefaultRoomNameListResp) Reset()         { *m = GetDefaultRoomNameListResp{} }
func (m *GetDefaultRoomNameListResp) String() string { return proto.CompactTextString(m) }
func (*GetDefaultRoomNameListResp) ProtoMessage()    {}
func (*GetDefaultRoomNameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{16}
}
func (m *GetDefaultRoomNameListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDefaultRoomNameListResp.Unmarshal(m, b)
}
func (m *GetDefaultRoomNameListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDefaultRoomNameListResp.Marshal(b, m, deterministic)
}
func (dst *GetDefaultRoomNameListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDefaultRoomNameListResp.Merge(dst, src)
}
func (m *GetDefaultRoomNameListResp) XXX_Size() int {
	return xxx_messageInfo_GetDefaultRoomNameListResp.Size(m)
}
func (m *GetDefaultRoomNameListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDefaultRoomNameListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDefaultRoomNameListResp proto.InternalMessageInfo

func (m *GetDefaultRoomNameListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetDefaultRoomNameListResp) GetDefaultRoomNameList() []*DefaultRoomNameConfig {
	if m != nil {
		return m.DefaultRoomNameList
	}
	return nil
}

func (m *GetDefaultRoomNameListResp) GetNextPageIndex() uint32 {
	if m != nil {
		return m.NextPageIndex
	}
	return 0
}

func (m *GetDefaultRoomNameListResp) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

// 发布房间
type PublishGangupChannelReq struct {
	BaseReq              *app.BaseReq                 `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32                       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*topic_channel.BlockOption `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	ChannelName          string                       `protobuf:"bytes,5,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	IsWantFresh          bool                         `protobuf:"varint,6,opt,name=is_want_fresh,json=isWantFresh,proto3" json:"is_want_fresh,omitempty"`
	IsShowGeoInfo        bool                         `protobuf:"varint,7,opt,name=is_show_geo_info,json=isShowGeoInfo,proto3" json:"is_show_geo_info,omitempty"`
	DiyHobbyName         string                       `protobuf:"bytes,12,opt,name=diy_hobby_name,json=diyHobbyName,proto3" json:"diy_hobby_name,omitempty"`
	AllSelectedBids      []uint32                     `protobuf:"varint,13,rep,packed,name=all_selected_bids,json=allSelectedBids,proto3" json:"all_selected_bids,omitempty"`
	NeedCheckChannelName bool                         `protobuf:"varint,14,opt,name=need_check_channel_name,json=needCheckChannelName,proto3" json:"need_check_channel_name,omitempty"`
	UgcChannelPlayMode   UgcChannelPlayMode           `protobuf:"varint,15,opt,name=ugc_channel_play_mode,json=ugcChannelPlayMode,proto3,enum=ga.channel_play.UgcChannelPlayMode" json:"ugc_channel_play_mode,omitempty"`
	GameLabels           []*topic_channel.GameLabel   `protobuf:"bytes,16,rep,name=game_labels,json=gameLabels,proto3" json:"game_labels,omitempty"`
	// 发布block后续带的按钮处理
	BlockButtonOpts      []*BlockButtonOpt `protobuf:"bytes,17,rep,name=block_button_opts,json=blockButtonOpts,proto3" json:"block_button_opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PublishGangupChannelReq) Reset()         { *m = PublishGangupChannelReq{} }
func (m *PublishGangupChannelReq) String() string { return proto.CompactTextString(m) }
func (*PublishGangupChannelReq) ProtoMessage()    {}
func (*PublishGangupChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{17}
}
func (m *PublishGangupChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishGangupChannelReq.Unmarshal(m, b)
}
func (m *PublishGangupChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishGangupChannelReq.Marshal(b, m, deterministic)
}
func (dst *PublishGangupChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishGangupChannelReq.Merge(dst, src)
}
func (m *PublishGangupChannelReq) XXX_Size() int {
	return xxx_messageInfo_PublishGangupChannelReq.Size(m)
}
func (m *PublishGangupChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishGangupChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_PublishGangupChannelReq proto.InternalMessageInfo

func (m *PublishGangupChannelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PublishGangupChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PublishGangupChannelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *PublishGangupChannelReq) GetBlockOptions() []*topic_channel.BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *PublishGangupChannelReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PublishGangupChannelReq) GetIsWantFresh() bool {
	if m != nil {
		return m.IsWantFresh
	}
	return false
}

func (m *PublishGangupChannelReq) GetIsShowGeoInfo() bool {
	if m != nil {
		return m.IsShowGeoInfo
	}
	return false
}

func (m *PublishGangupChannelReq) GetDiyHobbyName() string {
	if m != nil {
		return m.DiyHobbyName
	}
	return ""
}

func (m *PublishGangupChannelReq) GetAllSelectedBids() []uint32 {
	if m != nil {
		return m.AllSelectedBids
	}
	return nil
}

func (m *PublishGangupChannelReq) GetNeedCheckChannelName() bool {
	if m != nil {
		return m.NeedCheckChannelName
	}
	return false
}

func (m *PublishGangupChannelReq) GetUgcChannelPlayMode() UgcChannelPlayMode {
	if m != nil {
		return m.UgcChannelPlayMode
	}
	return UgcChannelPlayMode_DEFAULT_VOICE_MODE
}

func (m *PublishGangupChannelReq) GetGameLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.GameLabels
	}
	return nil
}

func (m *PublishGangupChannelReq) GetBlockButtonOpts() []*BlockButtonOpt {
	if m != nil {
		return m.BlockButtonOpts
	}
	return nil
}

type BlockButtonOpt struct {
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	BlockId              uint32   `protobuf:"varint,3,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               []uint32 `protobuf:"varint,4,rep,packed,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockButtonOpt) Reset()         { *m = BlockButtonOpt{} }
func (m *BlockButtonOpt) String() string { return proto.CompactTextString(m) }
func (*BlockButtonOpt) ProtoMessage()    {}
func (*BlockButtonOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{18}
}
func (m *BlockButtonOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockButtonOpt.Unmarshal(m, b)
}
func (m *BlockButtonOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockButtonOpt.Marshal(b, m, deterministic)
}
func (dst *BlockButtonOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockButtonOpt.Merge(dst, src)
}
func (m *BlockButtonOpt) XXX_Size() int {
	return xxx_messageInfo_BlockButtonOpt.Size(m)
}
func (m *BlockButtonOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockButtonOpt.DiscardUnknown(m)
}

var xxx_messageInfo_BlockButtonOpt proto.InternalMessageInfo

func (m *BlockButtonOpt) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BlockButtonOpt) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockButtonOpt) GetElemId() []uint32 {
	if m != nil {
		return m.ElemId
	}
	return nil
}

type PublishGangupChannelResp struct {
	BaseResp             *app.BaseResp                                     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChangeCoolDown       uint32                                            `protobuf:"varint,2,opt,name=change_cool_down,json=changeCoolDown,proto3" json:"change_cool_down,omitempty"`
	FreezeDuration       uint32                                            `protobuf:"varint,3,opt,name=freeze_duration,json=freezeDuration,proto3" json:"freeze_duration,omitempty"`
	AutoDismissDuration  uint32                                            `protobuf:"varint,4,opt,name=auto_dismiss_duration,json=autoDismissDuration,proto3" json:"auto_dismiss_duration,omitempty"`
	SecondaryItem        []*topic_channel.ShowTopicChannelTabSecondaryItem `protobuf:"bytes,5,rep,name=secondary_item,json=secondaryItem,proto3" json:"secondary_item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *PublishGangupChannelResp) Reset()         { *m = PublishGangupChannelResp{} }
func (m *PublishGangupChannelResp) String() string { return proto.CompactTextString(m) }
func (*PublishGangupChannelResp) ProtoMessage()    {}
func (*PublishGangupChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{19}
}
func (m *PublishGangupChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishGangupChannelResp.Unmarshal(m, b)
}
func (m *PublishGangupChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishGangupChannelResp.Marshal(b, m, deterministic)
}
func (dst *PublishGangupChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishGangupChannelResp.Merge(dst, src)
}
func (m *PublishGangupChannelResp) XXX_Size() int {
	return xxx_messageInfo_PublishGangupChannelResp.Size(m)
}
func (m *PublishGangupChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishGangupChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_PublishGangupChannelResp proto.InternalMessageInfo

func (m *PublishGangupChannelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PublishGangupChannelResp) GetChangeCoolDown() uint32 {
	if m != nil {
		return m.ChangeCoolDown
	}
	return 0
}

func (m *PublishGangupChannelResp) GetFreezeDuration() uint32 {
	if m != nil {
		return m.FreezeDuration
	}
	return 0
}

func (m *PublishGangupChannelResp) GetAutoDismissDuration() uint32 {
	if m != nil {
		return m.AutoDismissDuration
	}
	return 0
}

func (m *PublishGangupChannelResp) GetSecondaryItem() []*topic_channel.ShowTopicChannelTabSecondaryItem {
	if m != nil {
		return m.SecondaryItem
	}
	return nil
}

// 房间发布取消
type CancelGangupChannelPublishReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CancelGangupChannelPublishReq) Reset()         { *m = CancelGangupChannelPublishReq{} }
func (m *CancelGangupChannelPublishReq) String() string { return proto.CompactTextString(m) }
func (*CancelGangupChannelPublishReq) ProtoMessage()    {}
func (*CancelGangupChannelPublishReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{20}
}
func (m *CancelGangupChannelPublishReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelGangupChannelPublishReq.Unmarshal(m, b)
}
func (m *CancelGangupChannelPublishReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelGangupChannelPublishReq.Marshal(b, m, deterministic)
}
func (dst *CancelGangupChannelPublishReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelGangupChannelPublishReq.Merge(dst, src)
}
func (m *CancelGangupChannelPublishReq) XXX_Size() int {
	return xxx_messageInfo_CancelGangupChannelPublishReq.Size(m)
}
func (m *CancelGangupChannelPublishReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelGangupChannelPublishReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelGangupChannelPublishReq proto.InternalMessageInfo

func (m *CancelGangupChannelPublishReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelGangupChannelPublishReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CancelGangupChannelPublishReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type CancelGangupChannelPublishResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CancelGangupChannelPublishResp) Reset()         { *m = CancelGangupChannelPublishResp{} }
func (m *CancelGangupChannelPublishResp) String() string { return proto.CompactTextString(m) }
func (*CancelGangupChannelPublishResp) ProtoMessage()    {}
func (*CancelGangupChannelPublishResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{21}
}
func (m *CancelGangupChannelPublishResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelGangupChannelPublishResp.Unmarshal(m, b)
}
func (m *CancelGangupChannelPublishResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelGangupChannelPublishResp.Marshal(b, m, deterministic)
}
func (dst *CancelGangupChannelPublishResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelGangupChannelPublishResp.Merge(dst, src)
}
func (m *CancelGangupChannelPublishResp) XXX_Size() int {
	return xxx_messageInfo_CancelGangupChannelPublishResp.Size(m)
}
func (m *CancelGangupChannelPublishResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelGangupChannelPublishResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelGangupChannelPublishResp proto.InternalMessageInfo

func (m *CancelGangupChannelPublishResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 首页金刚区请求
type HomePageHeadConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SelfGameIds          []uint32     `protobuf:"varint,2,rep,packed,name=self_game_ids,json=selfGameIds,proto3" json:"self_game_ids,omitempty"`
	ActiveTabIds         []uint32     `protobuf:"varint,3,rep,packed,name=active_tab_ids,json=activeTabIds,proto3" json:"active_tab_ids,omitempty"`
	ReqType              uint32       `protobuf:"varint,4,opt,name=req_type,json=reqType,proto3" json:"req_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HomePageHeadConfigReq) Reset()         { *m = HomePageHeadConfigReq{} }
func (m *HomePageHeadConfigReq) String() string { return proto.CompactTextString(m) }
func (*HomePageHeadConfigReq) ProtoMessage()    {}
func (*HomePageHeadConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{22}
}
func (m *HomePageHeadConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageHeadConfigReq.Unmarshal(m, b)
}
func (m *HomePageHeadConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageHeadConfigReq.Marshal(b, m, deterministic)
}
func (dst *HomePageHeadConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageHeadConfigReq.Merge(dst, src)
}
func (m *HomePageHeadConfigReq) XXX_Size() int {
	return xxx_messageInfo_HomePageHeadConfigReq.Size(m)
}
func (m *HomePageHeadConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageHeadConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageHeadConfigReq proto.InternalMessageInfo

func (m *HomePageHeadConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HomePageHeadConfigReq) GetSelfGameIds() []uint32 {
	if m != nil {
		return m.SelfGameIds
	}
	return nil
}

func (m *HomePageHeadConfigReq) GetActiveTabIds() []uint32 {
	if m != nil {
		return m.ActiveTabIds
	}
	return nil
}

func (m *HomePageHeadConfigReq) GetReqType() uint32 {
	if m != nil {
		return m.ReqType
	}
	return 0
}

type HomePageHeadConfigResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Configs              []*HomePageHeadConfigItem `protobuf:"bytes,2,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *HomePageHeadConfigResp) Reset()         { *m = HomePageHeadConfigResp{} }
func (m *HomePageHeadConfigResp) String() string { return proto.CompactTextString(m) }
func (*HomePageHeadConfigResp) ProtoMessage()    {}
func (*HomePageHeadConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{23}
}
func (m *HomePageHeadConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageHeadConfigResp.Unmarshal(m, b)
}
func (m *HomePageHeadConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageHeadConfigResp.Marshal(b, m, deterministic)
}
func (dst *HomePageHeadConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageHeadConfigResp.Merge(dst, src)
}
func (m *HomePageHeadConfigResp) XXX_Size() int {
	return xxx_messageInfo_HomePageHeadConfigResp.Size(m)
}
func (m *HomePageHeadConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageHeadConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageHeadConfigResp proto.InternalMessageInfo

func (m *HomePageHeadConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *HomePageHeadConfigResp) GetConfigs() []*HomePageHeadConfigItem {
	if m != nil {
		return m.Configs
	}
	return nil
}

type HomePageHeadConfigItems struct {
	Items                []*HomePageHeadConfigItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *HomePageHeadConfigItems) Reset()         { *m = HomePageHeadConfigItems{} }
func (m *HomePageHeadConfigItems) String() string { return proto.CompactTextString(m) }
func (*HomePageHeadConfigItems) ProtoMessage()    {}
func (*HomePageHeadConfigItems) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{24}
}
func (m *HomePageHeadConfigItems) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageHeadConfigItems.Unmarshal(m, b)
}
func (m *HomePageHeadConfigItems) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageHeadConfigItems.Marshal(b, m, deterministic)
}
func (dst *HomePageHeadConfigItems) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageHeadConfigItems.Merge(dst, src)
}
func (m *HomePageHeadConfigItems) XXX_Size() int {
	return xxx_messageInfo_HomePageHeadConfigItems.Size(m)
}
func (m *HomePageHeadConfigItems) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageHeadConfigItems.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageHeadConfigItems proto.InternalMessageInfo

func (m *HomePageHeadConfigItems) GetItems() []*HomePageHeadConfigItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type HomePageHeadConfigItem struct {
	// 配置id，用于识别新用户引导匹配
	ConfigId string `protobuf:"bytes,1,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	// 专区类型
	ConfigType HomePageHeadConfigEnum `protobuf:"varint,2,opt,name=config_type,json=configType,proto3,enum=ga.channel_play.HomePageHeadConfigEnum" json:"config_type,omitempty"`
	// 主标题
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// 副标题
	SubTitle string `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// 底图， 旧版代表展示两个一行的大图
	Background string `protobuf:"bytes,5,opt,name=background,proto3" json:"background,omitempty"`
	// 动画效果配置
	Vap *Vap `protobuf:"bytes,6,opt,name=vap,proto3" json:"vap,omitempty"`
	// 广告标签，字数-最长配置7个字
	AdTag string `protobuf:"bytes,7,opt,name=ad_tag,json=adTag,proto3" json:"ad_tag,omitempty"`
	// 游戏专区入口取房间当前组队用户头像，成组切换，提示xxx场组队
	GameOverview *GameOverview `protobuf:"bytes,8,opt,name=game_overview,json=gameOverview,proto3" json:"game_overview,omitempty"`
	// 专区客户端跳转短链
	JumpLink string `protobuf:"bytes,9,opt,name=jump_link,json=jumpLink,proto3" json:"jump_link,omitempty"`
	// 是否可以被替换赛事中心，6.45废弃
	IsReplace bool `protobuf:"varint,10,opt,name=is_replace,json=isReplace,proto3" json:"is_replace,omitempty"`
	// 金刚区收起时展示的小图， 旧版代表展示三个一行的小图
	SmallBackground string `protobuf:"bytes,11,opt,name=small_background,json=smallBackground,proto3" json:"small_background,omitempty"`
	// 根据用户的游戏标签，轮播对应配置文案
	TagConfigInfo []*TagConfigInfo `protobuf:"bytes,12,rep,name=tag_config_info,json=tagConfigInfo,proto3" json:"tag_config_info,omitempty"`
	// 是否需要检查入口
	CheckEnter bool `protobuf:"varint,13,opt,name=check_enter,json=checkEnter,proto3" json:"check_enter,omitempty"`
	// 6.56.5版本后的TT首页，轮播配置
	RichTagConfigInfo *RichTagConfigInfo `protobuf:"bytes,14,opt,name=rich_tag_config_info,json=richTagConfigInfo,proto3" json:"rich_tag_config_info,omitempty"`
	// 轮播配置类型，见TagConfigType
	TagConfigType uint32 `protobuf:"varint,15,opt,name=tag_config_type,json=tagConfigType,proto3" json:"tag_config_type,omitempty"`
	// Types that are valid to be assigned to ExtraData:
	//	*HomePageHeadConfigItem_CompetitionConfig
	//	*HomePageHeadConfigItem_FlashChatConfig
	//	*HomePageHeadConfigItem_MultipleBannersConfig
	ExtraData            isHomePageHeadConfigItem_ExtraData `protobuf_oneof:"extra_data"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *HomePageHeadConfigItem) Reset()         { *m = HomePageHeadConfigItem{} }
func (m *HomePageHeadConfigItem) String() string { return proto.CompactTextString(m) }
func (*HomePageHeadConfigItem) ProtoMessage()    {}
func (*HomePageHeadConfigItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{25}
}
func (m *HomePageHeadConfigItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageHeadConfigItem.Unmarshal(m, b)
}
func (m *HomePageHeadConfigItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageHeadConfigItem.Marshal(b, m, deterministic)
}
func (dst *HomePageHeadConfigItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageHeadConfigItem.Merge(dst, src)
}
func (m *HomePageHeadConfigItem) XXX_Size() int {
	return xxx_messageInfo_HomePageHeadConfigItem.Size(m)
}
func (m *HomePageHeadConfigItem) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageHeadConfigItem.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageHeadConfigItem proto.InternalMessageInfo

func (m *HomePageHeadConfigItem) GetConfigId() string {
	if m != nil {
		return m.ConfigId
	}
	return ""
}

func (m *HomePageHeadConfigItem) GetConfigType() HomePageHeadConfigEnum {
	if m != nil {
		return m.ConfigType
	}
	return HomePageHeadConfigEnum_CONFIG_GAME_TYPE
}

func (m *HomePageHeadConfigItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *HomePageHeadConfigItem) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *HomePageHeadConfigItem) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *HomePageHeadConfigItem) GetVap() *Vap {
	if m != nil {
		return m.Vap
	}
	return nil
}

func (m *HomePageHeadConfigItem) GetAdTag() string {
	if m != nil {
		return m.AdTag
	}
	return ""
}

func (m *HomePageHeadConfigItem) GetGameOverview() *GameOverview {
	if m != nil {
		return m.GameOverview
	}
	return nil
}

func (m *HomePageHeadConfigItem) GetJumpLink() string {
	if m != nil {
		return m.JumpLink
	}
	return ""
}

func (m *HomePageHeadConfigItem) GetIsReplace() bool {
	if m != nil {
		return m.IsReplace
	}
	return false
}

func (m *HomePageHeadConfigItem) GetSmallBackground() string {
	if m != nil {
		return m.SmallBackground
	}
	return ""
}

func (m *HomePageHeadConfigItem) GetTagConfigInfo() []*TagConfigInfo {
	if m != nil {
		return m.TagConfigInfo
	}
	return nil
}

func (m *HomePageHeadConfigItem) GetCheckEnter() bool {
	if m != nil {
		return m.CheckEnter
	}
	return false
}

func (m *HomePageHeadConfigItem) GetRichTagConfigInfo() *RichTagConfigInfo {
	if m != nil {
		return m.RichTagConfigInfo
	}
	return nil
}

func (m *HomePageHeadConfigItem) GetTagConfigType() uint32 {
	if m != nil {
		return m.TagConfigType
	}
	return 0
}

type isHomePageHeadConfigItem_ExtraData interface {
	isHomePageHeadConfigItem_ExtraData()
}

type HomePageHeadConfigItem_CompetitionConfig struct {
	CompetitionConfig *HomePageHeadCompetitionConfig `protobuf:"bytes,50,opt,name=competition_config,json=competitionConfig,proto3,oneof"`
}

type HomePageHeadConfigItem_FlashChatConfig struct {
	FlashChatConfig *HomePageHeadFlashChatConfig `protobuf:"bytes,51,opt,name=flash_chat_config,json=flashChatConfig,proto3,oneof"`
}

type HomePageHeadConfigItem_MultipleBannersConfig struct {
	MultipleBannersConfig *HomePageHeadMultipleBannersConfig `protobuf:"bytes,52,opt,name=multiple_banners_config,json=multipleBannersConfig,proto3,oneof"`
}

func (*HomePageHeadConfigItem_CompetitionConfig) isHomePageHeadConfigItem_ExtraData() {}

func (*HomePageHeadConfigItem_FlashChatConfig) isHomePageHeadConfigItem_ExtraData() {}

func (*HomePageHeadConfigItem_MultipleBannersConfig) isHomePageHeadConfigItem_ExtraData() {}

func (m *HomePageHeadConfigItem) GetExtraData() isHomePageHeadConfigItem_ExtraData {
	if m != nil {
		return m.ExtraData
	}
	return nil
}

func (m *HomePageHeadConfigItem) GetCompetitionConfig() *HomePageHeadCompetitionConfig {
	if x, ok := m.GetExtraData().(*HomePageHeadConfigItem_CompetitionConfig); ok {
		return x.CompetitionConfig
	}
	return nil
}

func (m *HomePageHeadConfigItem) GetFlashChatConfig() *HomePageHeadFlashChatConfig {
	if x, ok := m.GetExtraData().(*HomePageHeadConfigItem_FlashChatConfig); ok {
		return x.FlashChatConfig
	}
	return nil
}

func (m *HomePageHeadConfigItem) GetMultipleBannersConfig() *HomePageHeadMultipleBannersConfig {
	if x, ok := m.GetExtraData().(*HomePageHeadConfigItem_MultipleBannersConfig); ok {
		return x.MultipleBannersConfig
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*HomePageHeadConfigItem) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _HomePageHeadConfigItem_OneofMarshaler, _HomePageHeadConfigItem_OneofUnmarshaler, _HomePageHeadConfigItem_OneofSizer, []interface{}{
		(*HomePageHeadConfigItem_CompetitionConfig)(nil),
		(*HomePageHeadConfigItem_FlashChatConfig)(nil),
		(*HomePageHeadConfigItem_MultipleBannersConfig)(nil),
	}
}

func _HomePageHeadConfigItem_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*HomePageHeadConfigItem)
	// extra_data
	switch x := m.ExtraData.(type) {
	case *HomePageHeadConfigItem_CompetitionConfig:
		b.EncodeVarint(50<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.CompetitionConfig); err != nil {
			return err
		}
	case *HomePageHeadConfigItem_FlashChatConfig:
		b.EncodeVarint(51<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.FlashChatConfig); err != nil {
			return err
		}
	case *HomePageHeadConfigItem_MultipleBannersConfig:
		b.EncodeVarint(52<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.MultipleBannersConfig); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("HomePageHeadConfigItem.ExtraData has unexpected type %T", x)
	}
	return nil
}

func _HomePageHeadConfigItem_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*HomePageHeadConfigItem)
	switch tag {
	case 50: // extra_data.competition_config
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(HomePageHeadCompetitionConfig)
		err := b.DecodeMessage(msg)
		m.ExtraData = &HomePageHeadConfigItem_CompetitionConfig{msg}
		return true, err
	case 51: // extra_data.flash_chat_config
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(HomePageHeadFlashChatConfig)
		err := b.DecodeMessage(msg)
		m.ExtraData = &HomePageHeadConfigItem_FlashChatConfig{msg}
		return true, err
	case 52: // extra_data.multiple_banners_config
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(HomePageHeadMultipleBannersConfig)
		err := b.DecodeMessage(msg)
		m.ExtraData = &HomePageHeadConfigItem_MultipleBannersConfig{msg}
		return true, err
	default:
		return false, nil
	}
}

func _HomePageHeadConfigItem_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*HomePageHeadConfigItem)
	// extra_data
	switch x := m.ExtraData.(type) {
	case *HomePageHeadConfigItem_CompetitionConfig:
		s := proto.Size(x.CompetitionConfig)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *HomePageHeadConfigItem_FlashChatConfig:
		s := proto.Size(x.FlashChatConfig)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *HomePageHeadConfigItem_MultipleBannersConfig:
		s := proto.Size(x.MultipleBannersConfig)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type HomePageHeadMultipleBannersConfig struct {
	Data                 []byte   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HomePageHeadMultipleBannersConfig) Reset()         { *m = HomePageHeadMultipleBannersConfig{} }
func (m *HomePageHeadMultipleBannersConfig) String() string { return proto.CompactTextString(m) }
func (*HomePageHeadMultipleBannersConfig) ProtoMessage()    {}
func (*HomePageHeadMultipleBannersConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{26}
}
func (m *HomePageHeadMultipleBannersConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageHeadMultipleBannersConfig.Unmarshal(m, b)
}
func (m *HomePageHeadMultipleBannersConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageHeadMultipleBannersConfig.Marshal(b, m, deterministic)
}
func (dst *HomePageHeadMultipleBannersConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageHeadMultipleBannersConfig.Merge(dst, src)
}
func (m *HomePageHeadMultipleBannersConfig) XXX_Size() int {
	return xxx_messageInfo_HomePageHeadMultipleBannersConfig.Size(m)
}
func (m *HomePageHeadMultipleBannersConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageHeadMultipleBannersConfig.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageHeadMultipleBannersConfig proto.InternalMessageInfo

func (m *HomePageHeadMultipleBannersConfig) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type Vap struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Vap) Reset()         { *m = Vap{} }
func (m *Vap) String() string { return proto.CompactTextString(m) }
func (*Vap) ProtoMessage()    {}
func (*Vap) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{27}
}
func (m *Vap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Vap.Unmarshal(m, b)
}
func (m *Vap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Vap.Marshal(b, m, deterministic)
}
func (dst *Vap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Vap.Merge(dst, src)
}
func (m *Vap) XXX_Size() int {
	return xxx_messageInfo_Vap.Size(m)
}
func (m *Vap) XXX_DiscardUnknown() {
	xxx_messageInfo_Vap.DiscardUnknown(m)
}

var xxx_messageInfo_Vap proto.InternalMessageInfo

func (m *Vap) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Vap) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type GameOverview struct {
	Account              []string `protobuf:"bytes,1,rep,name=account,proto3" json:"account,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameOverview) Reset()         { *m = GameOverview{} }
func (m *GameOverview) String() string { return proto.CompactTextString(m) }
func (*GameOverview) ProtoMessage()    {}
func (*GameOverview) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{28}
}
func (m *GameOverview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameOverview.Unmarshal(m, b)
}
func (m *GameOverview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameOverview.Marshal(b, m, deterministic)
}
func (dst *GameOverview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameOverview.Merge(dst, src)
}
func (m *GameOverview) XXX_Size() int {
	return xxx_messageInfo_GameOverview.Size(m)
}
func (m *GameOverview) XXX_DiscardUnknown() {
	xxx_messageInfo_GameOverview.DiscardUnknown(m)
}

var xxx_messageInfo_GameOverview proto.InternalMessageInfo

func (m *GameOverview) GetAccount() []string {
	if m != nil {
		return m.Account
	}
	return nil
}

func (m *GameOverview) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type TagConfigInfo struct {
	TagTitle    string `protobuf:"bytes,3,opt,name=tag_title,json=tagTitle,proto3" json:"tag_title,omitempty"`
	TagSubTitle string `protobuf:"bytes,4,opt,name=tag_sub_title,json=tagSubTitle,proto3" json:"tag_sub_title,omitempty"`
	Pic         string `protobuf:"bytes,5,opt,name=pic,proto3" json:"pic,omitempty"`
	TabId       uint32 `protobuf:"varint,6,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// bool need_link_tab_position = 7; // 首页跳转后是否需要定位到对应的玩法
	ConfigType           uint32   `protobuf:"varint,8,opt,name=config_type,json=configType,proto3" json:"config_type,omitempty"`
	ButtonText           string   `protobuf:"bytes,9,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagConfigInfo) Reset()         { *m = TagConfigInfo{} }
func (m *TagConfigInfo) String() string { return proto.CompactTextString(m) }
func (*TagConfigInfo) ProtoMessage()    {}
func (*TagConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{29}
}
func (m *TagConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagConfigInfo.Unmarshal(m, b)
}
func (m *TagConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagConfigInfo.Marshal(b, m, deterministic)
}
func (dst *TagConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagConfigInfo.Merge(dst, src)
}
func (m *TagConfigInfo) XXX_Size() int {
	return xxx_messageInfo_TagConfigInfo.Size(m)
}
func (m *TagConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TagConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TagConfigInfo proto.InternalMessageInfo

func (m *TagConfigInfo) GetTagTitle() string {
	if m != nil {
		return m.TagTitle
	}
	return ""
}

func (m *TagConfigInfo) GetTagSubTitle() string {
	if m != nil {
		return m.TagSubTitle
	}
	return ""
}

func (m *TagConfigInfo) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *TagConfigInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *TagConfigInfo) GetConfigType() uint32 {
	if m != nil {
		return m.ConfigType
	}
	return 0
}

func (m *TagConfigInfo) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

type RichTagInfo struct {
	BackgroundColor      string   `protobuf:"bytes,1,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	LabelText            string   `protobuf:"bytes,2,opt,name=label_text,json=labelText,proto3" json:"label_text,omitempty"`
	MainTagText          string   `protobuf:"bytes,3,opt,name=main_tag_text,json=mainTagText,proto3" json:"main_tag_text,omitempty"`
	TabId                uint32   `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ConfigTabId          string   `protobuf:"bytes,5,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RichTagInfo) Reset()         { *m = RichTagInfo{} }
func (m *RichTagInfo) String() string { return proto.CompactTextString(m) }
func (*RichTagInfo) ProtoMessage()    {}
func (*RichTagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{30}
}
func (m *RichTagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RichTagInfo.Unmarshal(m, b)
}
func (m *RichTagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RichTagInfo.Marshal(b, m, deterministic)
}
func (dst *RichTagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RichTagInfo.Merge(dst, src)
}
func (m *RichTagInfo) XXX_Size() int {
	return xxx_messageInfo_RichTagInfo.Size(m)
}
func (m *RichTagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RichTagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RichTagInfo proto.InternalMessageInfo

func (m *RichTagInfo) GetBackgroundColor() string {
	if m != nil {
		return m.BackgroundColor
	}
	return ""
}

func (m *RichTagInfo) GetLabelText() string {
	if m != nil {
		return m.LabelText
	}
	return ""
}

func (m *RichTagInfo) GetMainTagText() string {
	if m != nil {
		return m.MainTagText
	}
	return ""
}

func (m *RichTagInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *RichTagInfo) GetConfigTabId() string {
	if m != nil {
		return m.ConfigTabId
	}
	return ""
}

type RichTagConfigInfo struct {
	RichTagInfo          []*RichTagInfo `protobuf:"bytes,1,rep,name=rich_tag_info,json=richTagInfo,proto3" json:"rich_tag_info,omitempty"`
	ConfigType           uint32         `protobuf:"varint,2,opt,name=config_type,json=configType,proto3" json:"config_type,omitempty"`
	ButtonText           string         `protobuf:"bytes,3,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RichTagConfigInfo) Reset()         { *m = RichTagConfigInfo{} }
func (m *RichTagConfigInfo) String() string { return proto.CompactTextString(m) }
func (*RichTagConfigInfo) ProtoMessage()    {}
func (*RichTagConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{31}
}
func (m *RichTagConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RichTagConfigInfo.Unmarshal(m, b)
}
func (m *RichTagConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RichTagConfigInfo.Marshal(b, m, deterministic)
}
func (dst *RichTagConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RichTagConfigInfo.Merge(dst, src)
}
func (m *RichTagConfigInfo) XXX_Size() int {
	return xxx_messageInfo_RichTagConfigInfo.Size(m)
}
func (m *RichTagConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RichTagConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RichTagConfigInfo proto.InternalMessageInfo

func (m *RichTagConfigInfo) GetRichTagInfo() []*RichTagInfo {
	if m != nil {
		return m.RichTagInfo
	}
	return nil
}

func (m *RichTagConfigInfo) GetConfigType() uint32 {
	if m != nil {
		return m.ConfigType
	}
	return 0
}

func (m *RichTagConfigInfo) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

type HomePageHeadCompetitionConfig struct {
	// 小副标题，旧版tt首页特有，旧版代表展示三个一行时展示
	SmallSubTitle        string                         `protobuf:"bytes,1,opt,name=small_sub_title,json=smallSubTitle,proto3" json:"small_sub_title,omitempty"`
	CompetitionList      []*HomePageHeadCompetitionList `protobuf:"bytes,3,rep,name=competition_list,json=competitionList,proto3" json:"competition_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *HomePageHeadCompetitionConfig) Reset()         { *m = HomePageHeadCompetitionConfig{} }
func (m *HomePageHeadCompetitionConfig) String() string { return proto.CompactTextString(m) }
func (*HomePageHeadCompetitionConfig) ProtoMessage()    {}
func (*HomePageHeadCompetitionConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{32}
}
func (m *HomePageHeadCompetitionConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageHeadCompetitionConfig.Unmarshal(m, b)
}
func (m *HomePageHeadCompetitionConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageHeadCompetitionConfig.Marshal(b, m, deterministic)
}
func (dst *HomePageHeadCompetitionConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageHeadCompetitionConfig.Merge(dst, src)
}
func (m *HomePageHeadCompetitionConfig) XXX_Size() int {
	return xxx_messageInfo_HomePageHeadCompetitionConfig.Size(m)
}
func (m *HomePageHeadCompetitionConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageHeadCompetitionConfig.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageHeadCompetitionConfig proto.InternalMessageInfo

func (m *HomePageHeadCompetitionConfig) GetSmallSubTitle() string {
	if m != nil {
		return m.SmallSubTitle
	}
	return ""
}

func (m *HomePageHeadCompetitionConfig) GetCompetitionList() []*HomePageHeadCompetitionList {
	if m != nil {
		return m.CompetitionList
	}
	return nil
}

type HomePageHeadCompetitionList struct {
	// 赛事主标题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// 赛事副标题
	Subtitle string `protobuf:"bytes,2,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// 赛事背景图
	Background string `protobuf:"bytes,3,opt,name=background,proto3" json:"background,omitempty"`
	// 跳转链接
	Link string `protobuf:"bytes,4,opt,name=link,proto3" json:"link,omitempty"`
	// 赛事标签词语
	TagText string `protobuf:"bytes,5,opt,name=tag_text,json=tagText,proto3" json:"tag_text,omitempty"`
	// 赛事标签颜色
	TagColor             string   `protobuf:"bytes,6,opt,name=tag_color,json=tagColor,proto3" json:"tag_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HomePageHeadCompetitionList) Reset()         { *m = HomePageHeadCompetitionList{} }
func (m *HomePageHeadCompetitionList) String() string { return proto.CompactTextString(m) }
func (*HomePageHeadCompetitionList) ProtoMessage()    {}
func (*HomePageHeadCompetitionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{33}
}
func (m *HomePageHeadCompetitionList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageHeadCompetitionList.Unmarshal(m, b)
}
func (m *HomePageHeadCompetitionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageHeadCompetitionList.Marshal(b, m, deterministic)
}
func (dst *HomePageHeadCompetitionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageHeadCompetitionList.Merge(dst, src)
}
func (m *HomePageHeadCompetitionList) XXX_Size() int {
	return xxx_messageInfo_HomePageHeadCompetitionList.Size(m)
}
func (m *HomePageHeadCompetitionList) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageHeadCompetitionList.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageHeadCompetitionList proto.InternalMessageInfo

func (m *HomePageHeadCompetitionList) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *HomePageHeadCompetitionList) GetSubtitle() string {
	if m != nil {
		return m.Subtitle
	}
	return ""
}

func (m *HomePageHeadCompetitionList) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *HomePageHeadCompetitionList) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *HomePageHeadCompetitionList) GetTagText() string {
	if m != nil {
		return m.TagText
	}
	return ""
}

func (m *HomePageHeadCompetitionList) GetTagColor() string {
	if m != nil {
		return m.TagColor
	}
	return ""
}

type HomePageHeadFlashChatConfig struct {
	// 目前都是MT那边接口返回数据，为了跨团队合作方便，使用字节流处理，使用 muse_interest_hub_logic.proto HomePageHeadMuseExtraInfo解析
	Data                 []byte   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HomePageHeadFlashChatConfig) Reset()         { *m = HomePageHeadFlashChatConfig{} }
func (m *HomePageHeadFlashChatConfig) String() string { return proto.CompactTextString(m) }
func (*HomePageHeadFlashChatConfig) ProtoMessage()    {}
func (*HomePageHeadFlashChatConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{34}
}
func (m *HomePageHeadFlashChatConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageHeadFlashChatConfig.Unmarshal(m, b)
}
func (m *HomePageHeadFlashChatConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageHeadFlashChatConfig.Marshal(b, m, deterministic)
}
func (dst *HomePageHeadFlashChatConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageHeadFlashChatConfig.Merge(dst, src)
}
func (m *HomePageHeadFlashChatConfig) XXX_Size() int {
	return xxx_messageInfo_HomePageHeadFlashChatConfig.Size(m)
}
func (m *HomePageHeadFlashChatConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageHeadFlashChatConfig.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageHeadFlashChatConfig proto.InternalMessageInfo

func (m *HomePageHeadFlashChatConfig) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type HotMiniGame struct {
	// 玩法id
	TabId uint32 `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// 外显游戏名
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// 介绍文案
	IntroText string `protobuf:"bytes,3,opt,name=intro_text,json=introText,proto3" json:"intro_text,omitempty"`
	// 背景图 url
	BgUrl string `protobuf:"bytes,4,opt,name=bg_url,json=bgUrl,proto3" json:"bg_url,omitempty"`
	// 背景颜色
	BgColor string `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// 按钮文案
	ButtonText string `protobuf:"bytes,6,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	// 按钮颜色
	ButtonColor string `protobuf:"bytes,7,opt,name=button_color,json=buttonColor,proto3" json:"button_color,omitempty"`
	// 按钮跳转
	ButtonLink string `protobuf:"bytes,8,opt,name=button_link,json=buttonLink,proto3" json:"button_link,omitempty"`
	// 小游戏配置
	Config *topic_channel.MiniGameConfig `protobuf:"bytes,9,opt,name=config,proto3" json:"config,omitempty"`
	// 动图 lotties url
	LottieUrl string `protobuf:"bytes,10,opt,name=lottie_url,json=lottieUrl,proto3" json:"lottie_url,omitempty"`
	// 按钮颜色组
	ButtonColors         []string `protobuf:"bytes,11,rep,name=button_colors,json=buttonColors,proto3" json:"button_colors,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HotMiniGame) Reset()         { *m = HotMiniGame{} }
func (m *HotMiniGame) String() string { return proto.CompactTextString(m) }
func (*HotMiniGame) ProtoMessage()    {}
func (*HotMiniGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{35}
}
func (m *HotMiniGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HotMiniGame.Unmarshal(m, b)
}
func (m *HotMiniGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HotMiniGame.Marshal(b, m, deterministic)
}
func (dst *HotMiniGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HotMiniGame.Merge(dst, src)
}
func (m *HotMiniGame) XXX_Size() int {
	return xxx_messageInfo_HotMiniGame.Size(m)
}
func (m *HotMiniGame) XXX_DiscardUnknown() {
	xxx_messageInfo_HotMiniGame.DiscardUnknown(m)
}

var xxx_messageInfo_HotMiniGame proto.InternalMessageInfo

func (m *HotMiniGame) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *HotMiniGame) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *HotMiniGame) GetIntroText() string {
	if m != nil {
		return m.IntroText
	}
	return ""
}

func (m *HotMiniGame) GetBgUrl() string {
	if m != nil {
		return m.BgUrl
	}
	return ""
}

func (m *HotMiniGame) GetBgColor() string {
	if m != nil {
		return m.BgColor
	}
	return ""
}

func (m *HotMiniGame) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

func (m *HotMiniGame) GetButtonColor() string {
	if m != nil {
		return m.ButtonColor
	}
	return ""
}

func (m *HotMiniGame) GetButtonLink() string {
	if m != nil {
		return m.ButtonLink
	}
	return ""
}

func (m *HotMiniGame) GetConfig() *topic_channel.MiniGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *HotMiniGame) GetLottieUrl() string {
	if m != nil {
		return m.LottieUrl
	}
	return ""
}

func (m *HotMiniGame) GetButtonColors() []string {
	if m != nil {
		return m.ButtonColors
	}
	return nil
}

type GetHotMiniGamesReq struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 渠道包
	ChannelPkg           string   `protobuf:"bytes,2,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHotMiniGamesReq) Reset()         { *m = GetHotMiniGamesReq{} }
func (m *GetHotMiniGamesReq) String() string { return proto.CompactTextString(m) }
func (*GetHotMiniGamesReq) ProtoMessage()    {}
func (*GetHotMiniGamesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{36}
}
func (m *GetHotMiniGamesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHotMiniGamesReq.Unmarshal(m, b)
}
func (m *GetHotMiniGamesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHotMiniGamesReq.Marshal(b, m, deterministic)
}
func (dst *GetHotMiniGamesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHotMiniGamesReq.Merge(dst, src)
}
func (m *GetHotMiniGamesReq) XXX_Size() int {
	return xxx_messageInfo_GetHotMiniGamesReq.Size(m)
}
func (m *GetHotMiniGamesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHotMiniGamesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHotMiniGamesReq proto.InternalMessageInfo

func (m *GetHotMiniGamesReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetHotMiniGamesReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

type GetHotMiniGamesResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 区域名
	AreaName string `protobuf:"bytes,2,opt,name=area_name,json=areaName,proto3" json:"area_name,omitempty"`
	// 默认的玩法id
	DefaultTabId uint32 `protobuf:"varint,3,opt,name=default_tab_id,json=defaultTabId,proto3" json:"default_tab_id,omitempty"`
	// 重点小游戏列表
	List                 []*HotMiniGame `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetHotMiniGamesResp) Reset()         { *m = GetHotMiniGamesResp{} }
func (m *GetHotMiniGamesResp) String() string { return proto.CompactTextString(m) }
func (*GetHotMiniGamesResp) ProtoMessage()    {}
func (*GetHotMiniGamesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{37}
}
func (m *GetHotMiniGamesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHotMiniGamesResp.Unmarshal(m, b)
}
func (m *GetHotMiniGamesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHotMiniGamesResp.Marshal(b, m, deterministic)
}
func (dst *GetHotMiniGamesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHotMiniGamesResp.Merge(dst, src)
}
func (m *GetHotMiniGamesResp) XXX_Size() int {
	return xxx_messageInfo_GetHotMiniGamesResp.Size(m)
}
func (m *GetHotMiniGamesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHotMiniGamesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHotMiniGamesResp proto.InternalMessageInfo

func (m *GetHotMiniGamesResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetHotMiniGamesResp) GetAreaName() string {
	if m != nil {
		return m.AreaName
	}
	return ""
}

func (m *GetHotMiniGamesResp) GetDefaultTabId() uint32 {
	if m != nil {
		return m.DefaultTabId
	}
	return 0
}

func (m *GetHotMiniGamesResp) GetList() []*HotMiniGame {
	if m != nil {
		return m.List
	}
	return nil
}

type QuickMiniGame struct {
	// 玩法id
	TabId uint32 `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// tab对应的游戏icon
	GameIcon string `protobuf:"bytes,2,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	// 游戏名
	GameName string `protobuf:"bytes,3,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	// 背景色值
	BgColors []string `protobuf:"bytes,4,rep,name=bg_colors,json=bgColors,proto3" json:"bg_colors,omitempty"`
	// 小游戏配置
	Config *topic_channel.MiniGameConfig `protobuf:"bytes,5,opt,name=config,proto3" json:"config,omitempty"`
	// 按钮效果，快速匹配or短链,见ButtonEffect
	ButtonEffect uint32 `protobuf:"varint,6,opt,name=button_effect,json=buttonEffect,proto3" json:"button_effect,omitempty"`
	// 跳转短链，当button_effect=2时有效
	JumpLink             string   `protobuf:"bytes,7,opt,name=jump_link,json=jumpLink,proto3" json:"jump_link,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuickMiniGame) Reset()         { *m = QuickMiniGame{} }
func (m *QuickMiniGame) String() string { return proto.CompactTextString(m) }
func (*QuickMiniGame) ProtoMessage()    {}
func (*QuickMiniGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{38}
}
func (m *QuickMiniGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickMiniGame.Unmarshal(m, b)
}
func (m *QuickMiniGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickMiniGame.Marshal(b, m, deterministic)
}
func (dst *QuickMiniGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickMiniGame.Merge(dst, src)
}
func (m *QuickMiniGame) XXX_Size() int {
	return xxx_messageInfo_QuickMiniGame.Size(m)
}
func (m *QuickMiniGame) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickMiniGame.DiscardUnknown(m)
}

var xxx_messageInfo_QuickMiniGame proto.InternalMessageInfo

func (m *QuickMiniGame) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *QuickMiniGame) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *QuickMiniGame) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *QuickMiniGame) GetBgColors() []string {
	if m != nil {
		return m.BgColors
	}
	return nil
}

func (m *QuickMiniGame) GetConfig() *topic_channel.MiniGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *QuickMiniGame) GetButtonEffect() uint32 {
	if m != nil {
		return m.ButtonEffect
	}
	return 0
}

func (m *QuickMiniGame) GetJumpLink() string {
	if m != nil {
		return m.JumpLink
	}
	return ""
}

type GetQuickMiniGamesReq struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 请求来源
	Src GetQuickMiniGamesReq_Source `protobuf:"varint,2,opt,name=src,proto3,enum=ga.channel_play.GetQuickMiniGamesReq_Source" json:"src,omitempty"`
	// 渠道包
	ChannelPkg           string   `protobuf:"bytes,3,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickMiniGamesReq) Reset()         { *m = GetQuickMiniGamesReq{} }
func (m *GetQuickMiniGamesReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickMiniGamesReq) ProtoMessage()    {}
func (*GetQuickMiniGamesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{39}
}
func (m *GetQuickMiniGamesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickMiniGamesReq.Unmarshal(m, b)
}
func (m *GetQuickMiniGamesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickMiniGamesReq.Marshal(b, m, deterministic)
}
func (dst *GetQuickMiniGamesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickMiniGamesReq.Merge(dst, src)
}
func (m *GetQuickMiniGamesReq) XXX_Size() int {
	return xxx_messageInfo_GetQuickMiniGamesReq.Size(m)
}
func (m *GetQuickMiniGamesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickMiniGamesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickMiniGamesReq proto.InternalMessageInfo

func (m *GetQuickMiniGamesReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetQuickMiniGamesReq) GetSrc() GetQuickMiniGamesReq_Source {
	if m != nil {
		return m.Src
	}
	return GetQuickMiniGamesReq_SourceUnknown
}

func (m *GetQuickMiniGamesReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

type GetQuickMiniGamesResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 快速匹配小游戏玩法列表
	List []*QuickMiniGame `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	// 快速匹配区域名
	AreaName             string   `protobuf:"bytes,3,opt,name=area_name,json=areaName,proto3" json:"area_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickMiniGamesResp) Reset()         { *m = GetQuickMiniGamesResp{} }
func (m *GetQuickMiniGamesResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickMiniGamesResp) ProtoMessage()    {}
func (*GetQuickMiniGamesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{40}
}
func (m *GetQuickMiniGamesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickMiniGamesResp.Unmarshal(m, b)
}
func (m *GetQuickMiniGamesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickMiniGamesResp.Marshal(b, m, deterministic)
}
func (dst *GetQuickMiniGamesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickMiniGamesResp.Merge(dst, src)
}
func (m *GetQuickMiniGamesResp) XXX_Size() int {
	return xxx_messageInfo_GetQuickMiniGamesResp.Size(m)
}
func (m *GetQuickMiniGamesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickMiniGamesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickMiniGamesResp proto.InternalMessageInfo

func (m *GetQuickMiniGamesResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetQuickMiniGamesResp) GetList() []*QuickMiniGame {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetQuickMiniGamesResp) GetAreaName() string {
	if m != nil {
		return m.AreaName
	}
	return ""
}

type GameInsertFlowConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GameInsertFlowConfigReq) Reset()         { *m = GameInsertFlowConfigReq{} }
func (m *GameInsertFlowConfigReq) String() string { return proto.CompactTextString(m) }
func (*GameInsertFlowConfigReq) ProtoMessage()    {}
func (*GameInsertFlowConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{41}
}
func (m *GameInsertFlowConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInsertFlowConfigReq.Unmarshal(m, b)
}
func (m *GameInsertFlowConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInsertFlowConfigReq.Marshal(b, m, deterministic)
}
func (dst *GameInsertFlowConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInsertFlowConfigReq.Merge(dst, src)
}
func (m *GameInsertFlowConfigReq) XXX_Size() int {
	return xxx_messageInfo_GameInsertFlowConfigReq.Size(m)
}
func (m *GameInsertFlowConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInsertFlowConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GameInsertFlowConfigReq proto.InternalMessageInfo

func (m *GameInsertFlowConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GameInsertFlowConfigResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	QuickMatchConf       *QuickMatchConfig `protobuf:"bytes,2,opt,name=quick_match_conf,json=quickMatchConf,proto3" json:"quick_match_conf,omitempty"`
	CreateRoomConf       *CreateRoomConfig `protobuf:"bytes,3,opt,name=create_room_conf,json=createRoomConf,proto3" json:"create_room_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GameInsertFlowConfigResp) Reset()         { *m = GameInsertFlowConfigResp{} }
func (m *GameInsertFlowConfigResp) String() string { return proto.CompactTextString(m) }
func (*GameInsertFlowConfigResp) ProtoMessage()    {}
func (*GameInsertFlowConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{42}
}
func (m *GameInsertFlowConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInsertFlowConfigResp.Unmarshal(m, b)
}
func (m *GameInsertFlowConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInsertFlowConfigResp.Marshal(b, m, deterministic)
}
func (dst *GameInsertFlowConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInsertFlowConfigResp.Merge(dst, src)
}
func (m *GameInsertFlowConfigResp) XXX_Size() int {
	return xxx_messageInfo_GameInsertFlowConfigResp.Size(m)
}
func (m *GameInsertFlowConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInsertFlowConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GameInsertFlowConfigResp proto.InternalMessageInfo

func (m *GameInsertFlowConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GameInsertFlowConfigResp) GetQuickMatchConf() *QuickMatchConfig {
	if m != nil {
		return m.QuickMatchConf
	}
	return nil
}

func (m *GameInsertFlowConfigResp) GetCreateRoomConf() *CreateRoomConfig {
	if m != nil {
		return m.CreateRoomConf
	}
	return nil
}

type QuickMatchConfig struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Background           string   `protobuf:"bytes,3,opt,name=background,proto3" json:"background,omitempty"`
	ButtonText           string   `protobuf:"bytes,4,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuickMatchConfig) Reset()         { *m = QuickMatchConfig{} }
func (m *QuickMatchConfig) String() string { return proto.CompactTextString(m) }
func (*QuickMatchConfig) ProtoMessage()    {}
func (*QuickMatchConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{43}
}
func (m *QuickMatchConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickMatchConfig.Unmarshal(m, b)
}
func (m *QuickMatchConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickMatchConfig.Marshal(b, m, deterministic)
}
func (dst *QuickMatchConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickMatchConfig.Merge(dst, src)
}
func (m *QuickMatchConfig) XXX_Size() int {
	return xxx_messageInfo_QuickMatchConfig.Size(m)
}
func (m *QuickMatchConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickMatchConfig.DiscardUnknown(m)
}

var xxx_messageInfo_QuickMatchConfig proto.InternalMessageInfo

func (m *QuickMatchConfig) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *QuickMatchConfig) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *QuickMatchConfig) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *QuickMatchConfig) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

type CreateRoomConfig struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Background           string   `protobuf:"bytes,2,opt,name=background,proto3" json:"background,omitempty"`
	ButtonText           string   `protobuf:"bytes,3,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateRoomConfig) Reset()         { *m = CreateRoomConfig{} }
func (m *CreateRoomConfig) String() string { return proto.CompactTextString(m) }
func (*CreateRoomConfig) ProtoMessage()    {}
func (*CreateRoomConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{44}
}
func (m *CreateRoomConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRoomConfig.Unmarshal(m, b)
}
func (m *CreateRoomConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRoomConfig.Marshal(b, m, deterministic)
}
func (dst *CreateRoomConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRoomConfig.Merge(dst, src)
}
func (m *CreateRoomConfig) XXX_Size() int {
	return xxx_messageInfo_CreateRoomConfig.Size(m)
}
func (m *CreateRoomConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRoomConfig.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRoomConfig proto.InternalMessageInfo

func (m *CreateRoomConfig) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CreateRoomConfig) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *CreateRoomConfig) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

// 3091
type GetHomePageGuideReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	HomePageConfigId     string       `protobuf:"bytes,2,opt,name=home_page_config_id,json=homePageConfigId,proto3" json:"home_page_config_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetHomePageGuideReq) Reset()         { *m = GetHomePageGuideReq{} }
func (m *GetHomePageGuideReq) String() string { return proto.CompactTextString(m) }
func (*GetHomePageGuideReq) ProtoMessage()    {}
func (*GetHomePageGuideReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{45}
}
func (m *GetHomePageGuideReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomePageGuideReq.Unmarshal(m, b)
}
func (m *GetHomePageGuideReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomePageGuideReq.Marshal(b, m, deterministic)
}
func (dst *GetHomePageGuideReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomePageGuideReq.Merge(dst, src)
}
func (m *GetHomePageGuideReq) XXX_Size() int {
	return xxx_messageInfo_GetHomePageGuideReq.Size(m)
}
func (m *GetHomePageGuideReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomePageGuideReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomePageGuideReq proto.InternalMessageInfo

func (m *GetHomePageGuideReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetHomePageGuideReq) GetHomePageConfigId() string {
	if m != nil {
		return m.HomePageConfigId
	}
	return ""
}

type GuideContent struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Style                uint32   `protobuf:"varint,3,opt,name=style,proto3" json:"style,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuideContent) Reset()         { *m = GuideContent{} }
func (m *GuideContent) String() string { return proto.CompactTextString(m) }
func (*GuideContent) ProtoMessage()    {}
func (*GuideContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{46}
}
func (m *GuideContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuideContent.Unmarshal(m, b)
}
func (m *GuideContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuideContent.Marshal(b, m, deterministic)
}
func (dst *GuideContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuideContent.Merge(dst, src)
}
func (m *GuideContent) XXX_Size() int {
	return xxx_messageInfo_GuideContent.Size(m)
}
func (m *GuideContent) XXX_DiscardUnknown() {
	xxx_messageInfo_GuideContent.DiscardUnknown(m)
}

var xxx_messageInfo_GuideContent proto.InternalMessageInfo

func (m *GuideContent) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GuideContent) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GuideContent) GetStyle() uint32 {
	if m != nil {
		return m.Style
	}
	return 0
}

type HomePageSubmodule struct {
	GuideContent         *GuideContent `protobuf:"bytes,1,opt,name=guide_content,json=guideContent,proto3" json:"guide_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HomePageSubmodule) Reset()         { *m = HomePageSubmodule{} }
func (m *HomePageSubmodule) String() string { return proto.CompactTextString(m) }
func (*HomePageSubmodule) ProtoMessage()    {}
func (*HomePageSubmodule) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{47}
}
func (m *HomePageSubmodule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageSubmodule.Unmarshal(m, b)
}
func (m *HomePageSubmodule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageSubmodule.Marshal(b, m, deterministic)
}
func (dst *HomePageSubmodule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageSubmodule.Merge(dst, src)
}
func (m *HomePageSubmodule) XXX_Size() int {
	return xxx_messageInfo_HomePageSubmodule.Size(m)
}
func (m *HomePageSubmodule) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageSubmodule.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageSubmodule proto.InternalMessageInfo

func (m *HomePageSubmodule) GetGuideContent() *GuideContent {
	if m != nil {
		return m.GuideContent
	}
	return nil
}

type GetHomePageGuideResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HomePageConfigId     string             `protobuf:"bytes,2,opt,name=home_page_config_id,json=homePageConfigId,proto3" json:"home_page_config_id,omitempty"`
	GuideContent         *GuideContent      `protobuf:"bytes,3,opt,name=guide_content,json=guideContent,proto3" json:"guide_content,omitempty"`
	Submodule            *HomePageSubmodule `protobuf:"bytes,4,opt,name=submodule,proto3" json:"submodule,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetHomePageGuideResp) Reset()         { *m = GetHomePageGuideResp{} }
func (m *GetHomePageGuideResp) String() string { return proto.CompactTextString(m) }
func (*GetHomePageGuideResp) ProtoMessage()    {}
func (*GetHomePageGuideResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{48}
}
func (m *GetHomePageGuideResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomePageGuideResp.Unmarshal(m, b)
}
func (m *GetHomePageGuideResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomePageGuideResp.Marshal(b, m, deterministic)
}
func (dst *GetHomePageGuideResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomePageGuideResp.Merge(dst, src)
}
func (m *GetHomePageGuideResp) XXX_Size() int {
	return xxx_messageInfo_GetHomePageGuideResp.Size(m)
}
func (m *GetHomePageGuideResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomePageGuideResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomePageGuideResp proto.InternalMessageInfo

func (m *GetHomePageGuideResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetHomePageGuideResp) GetHomePageConfigId() string {
	if m != nil {
		return m.HomePageConfigId
	}
	return ""
}

func (m *GetHomePageGuideResp) GetGuideContent() *GuideContent {
	if m != nil {
		return m.GuideContent
	}
	return nil
}

func (m *GetHomePageGuideResp) GetSubmodule() *HomePageSubmodule {
	if m != nil {
		return m.Submodule
	}
	return nil
}

// 3092
type GetMoreTabConfigReq struct {
	BaseReq        *app.BaseReq   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FilterEntrance FilterEntrance `protobuf:"varint,2,opt,name=filter_entrance,json=filterEntrance,proto3,enum=ga.channel_play.FilterEntrance" json:"filter_entrance,omitempty"`
	ChannelPkg     string         `protobuf:"bytes,3,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	//  repeated uint32 self_game_list = 4; //手机内有装游戏的列表
	IsNeedMinorityGame   bool     `protobuf:"varint,4,opt,name=is_need_minority_game,json=isNeedMinorityGame,proto3" json:"is_need_minority_game,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMoreTabConfigReq) Reset()         { *m = GetMoreTabConfigReq{} }
func (m *GetMoreTabConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetMoreTabConfigReq) ProtoMessage()    {}
func (*GetMoreTabConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{49}
}
func (m *GetMoreTabConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMoreTabConfigReq.Unmarshal(m, b)
}
func (m *GetMoreTabConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMoreTabConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetMoreTabConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMoreTabConfigReq.Merge(dst, src)
}
func (m *GetMoreTabConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetMoreTabConfigReq.Size(m)
}
func (m *GetMoreTabConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMoreTabConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMoreTabConfigReq proto.InternalMessageInfo

func (m *GetMoreTabConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMoreTabConfigReq) GetFilterEntrance() FilterEntrance {
	if m != nil {
		return m.FilterEntrance
	}
	return FilterEntrance_UNKNOWN_ENTRANCE
}

func (m *GetMoreTabConfigReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

func (m *GetMoreTabConfigReq) GetIsNeedMinorityGame() bool {
	if m != nil {
		return m.IsNeedMinorityGame
	}
	return false
}

type GetMoreTabConfigResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items                []*MoreTabItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetMoreTabConfigResp) Reset()         { *m = GetMoreTabConfigResp{} }
func (m *GetMoreTabConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetMoreTabConfigResp) ProtoMessage()    {}
func (*GetMoreTabConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{50}
}
func (m *GetMoreTabConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMoreTabConfigResp.Unmarshal(m, b)
}
func (m *GetMoreTabConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMoreTabConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetMoreTabConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMoreTabConfigResp.Merge(dst, src)
}
func (m *GetMoreTabConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetMoreTabConfigResp.Size(m)
}
func (m *GetMoreTabConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMoreTabConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMoreTabConfigResp proto.InternalMessageInfo

func (m *GetMoreTabConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMoreTabConfigResp) GetItems() []*MoreTabItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type MoreTabItem struct {
	CommonBusinessFlag   *CommonBusinessFlag `protobuf:"bytes,1,opt,name=common_business_flag,json=commonBusinessFlag,proto3" json:"common_business_flag,omitempty"`
	CategoryInfo         *CategoryInfo       `protobuf:"bytes,2,opt,name=category_info,json=categoryInfo,proto3" json:"category_info,omitempty"`
	MusicInfo            *MoreTabMusicInfo   `protobuf:"bytes,3,opt,name=music_info,json=musicInfo,proto3" json:"music_info,omitempty"`
	MixInfo              *MixInfo            `protobuf:"bytes,4,opt,name=mix_info,json=mixInfo,proto3" json:"mix_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *MoreTabItem) Reset()         { *m = MoreTabItem{} }
func (m *MoreTabItem) String() string { return proto.CompactTextString(m) }
func (*MoreTabItem) ProtoMessage()    {}
func (*MoreTabItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{51}
}
func (m *MoreTabItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoreTabItem.Unmarshal(m, b)
}
func (m *MoreTabItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoreTabItem.Marshal(b, m, deterministic)
}
func (dst *MoreTabItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoreTabItem.Merge(dst, src)
}
func (m *MoreTabItem) XXX_Size() int {
	return xxx_messageInfo_MoreTabItem.Size(m)
}
func (m *MoreTabItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MoreTabItem.DiscardUnknown(m)
}

var xxx_messageInfo_MoreTabItem proto.InternalMessageInfo

func (m *MoreTabItem) GetCommonBusinessFlag() *CommonBusinessFlag {
	if m != nil {
		return m.CommonBusinessFlag
	}
	return nil
}

func (m *MoreTabItem) GetCategoryInfo() *CategoryInfo {
	if m != nil {
		return m.CategoryInfo
	}
	return nil
}

func (m *MoreTabItem) GetMusicInfo() *MoreTabMusicInfo {
	if m != nil {
		return m.MusicInfo
	}
	return nil
}

func (m *MoreTabItem) GetMixInfo() *MixInfo {
	if m != nil {
		return m.MixInfo
	}
	return nil
}

type CategoryInfo struct {
	CategoryId           uint32                           `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	CategoryName         string                           `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	CategoryInfoItem     []*CategoryInfo_CategoryInfoItem `protobuf:"bytes,3,rep,name=category_info_item,json=categoryInfoItem,proto3" json:"category_info_item,omitempty"`
	CanSelectNum         uint32                           `protobuf:"varint,4,opt,name=can_select_num,json=canSelectNum,proto3" json:"can_select_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *CategoryInfo) Reset()         { *m = CategoryInfo{} }
func (m *CategoryInfo) String() string { return proto.CompactTextString(m) }
func (*CategoryInfo) ProtoMessage()    {}
func (*CategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{52}
}
func (m *CategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CategoryInfo.Unmarshal(m, b)
}
func (m *CategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CategoryInfo.Marshal(b, m, deterministic)
}
func (dst *CategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CategoryInfo.Merge(dst, src)
}
func (m *CategoryInfo) XXX_Size() int {
	return xxx_messageInfo_CategoryInfo.Size(m)
}
func (m *CategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CategoryInfo proto.InternalMessageInfo

func (m *CategoryInfo) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *CategoryInfo) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *CategoryInfo) GetCategoryInfoItem() []*CategoryInfo_CategoryInfoItem {
	if m != nil {
		return m.CategoryInfoItem
	}
	return nil
}

func (m *CategoryInfo) GetCanSelectNum() uint32 {
	if m != nil {
		return m.CanSelectNum
	}
	return 0
}

type CategoryInfo_CategoryInfoItem struct {
	CommonBusinessFlag   *CommonBusinessFlag `protobuf:"bytes,1,opt,name=common_business_flag,json=commonBusinessFlag,proto3" json:"common_business_flag,omitempty"`
	TabDetail            *topic_channel.Tab  `protobuf:"bytes,2,opt,name=tab_detail,json=tabDetail,proto3" json:"tab_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *CategoryInfo_CategoryInfoItem) Reset()         { *m = CategoryInfo_CategoryInfoItem{} }
func (m *CategoryInfo_CategoryInfoItem) String() string { return proto.CompactTextString(m) }
func (*CategoryInfo_CategoryInfoItem) ProtoMessage()    {}
func (*CategoryInfo_CategoryInfoItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{52, 0}
}
func (m *CategoryInfo_CategoryInfoItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CategoryInfo_CategoryInfoItem.Unmarshal(m, b)
}
func (m *CategoryInfo_CategoryInfoItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CategoryInfo_CategoryInfoItem.Marshal(b, m, deterministic)
}
func (dst *CategoryInfo_CategoryInfoItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CategoryInfo_CategoryInfoItem.Merge(dst, src)
}
func (m *CategoryInfo_CategoryInfoItem) XXX_Size() int {
	return xxx_messageInfo_CategoryInfo_CategoryInfoItem.Size(m)
}
func (m *CategoryInfo_CategoryInfoItem) XXX_DiscardUnknown() {
	xxx_messageInfo_CategoryInfo_CategoryInfoItem.DiscardUnknown(m)
}

var xxx_messageInfo_CategoryInfo_CategoryInfoItem proto.InternalMessageInfo

func (m *CategoryInfo_CategoryInfoItem) GetCommonBusinessFlag() *CommonBusinessFlag {
	if m != nil {
		return m.CommonBusinessFlag
	}
	return nil
}

func (m *CategoryInfo_CategoryInfoItem) GetTabDetail() *topic_channel.Tab {
	if m != nil {
		return m.TabDetail
	}
	return nil
}

type MoreTabMusicInfo struct {
	ItemId               string   `protobuf:"bytes,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	CanSelectNum         uint32   `protobuf:"varint,2,opt,name=can_select_num,json=canSelectNum,proto3" json:"can_select_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoreTabMusicInfo) Reset()         { *m = MoreTabMusicInfo{} }
func (m *MoreTabMusicInfo) String() string { return proto.CompactTextString(m) }
func (*MoreTabMusicInfo) ProtoMessage()    {}
func (*MoreTabMusicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{53}
}
func (m *MoreTabMusicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoreTabMusicInfo.Unmarshal(m, b)
}
func (m *MoreTabMusicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoreTabMusicInfo.Marshal(b, m, deterministic)
}
func (dst *MoreTabMusicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoreTabMusicInfo.Merge(dst, src)
}
func (m *MoreTabMusicInfo) XXX_Size() int {
	return xxx_messageInfo_MoreTabMusicInfo.Size(m)
}
func (m *MoreTabMusicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MoreTabMusicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MoreTabMusicInfo proto.InternalMessageInfo

func (m *MoreTabMusicInfo) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *MoreTabMusicInfo) GetCanSelectNum() uint32 {
	if m != nil {
		return m.CanSelectNum
	}
	return 0
}

type MixInfo struct {
	Id                   string                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	MixItem              []*MixInfo_MixCategoryItem `protobuf:"bytes,3,rep,name=mix_item,json=mixItem,proto3" json:"mix_item,omitempty"`
	CanSelectNum         uint32                     `protobuf:"varint,4,opt,name=can_select_num,json=canSelectNum,proto3" json:"can_select_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *MixInfo) Reset()         { *m = MixInfo{} }
func (m *MixInfo) String() string { return proto.CompactTextString(m) }
func (*MixInfo) ProtoMessage()    {}
func (*MixInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{54}
}
func (m *MixInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MixInfo.Unmarshal(m, b)
}
func (m *MixInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MixInfo.Marshal(b, m, deterministic)
}
func (dst *MixInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MixInfo.Merge(dst, src)
}
func (m *MixInfo) XXX_Size() int {
	return xxx_messageInfo_MixInfo.Size(m)
}
func (m *MixInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MixInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MixInfo proto.InternalMessageInfo

func (m *MixInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MixInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MixInfo) GetMixItem() []*MixInfo_MixCategoryItem {
	if m != nil {
		return m.MixItem
	}
	return nil
}

func (m *MixInfo) GetCanSelectNum() uint32 {
	if m != nil {
		return m.CanSelectNum
	}
	return 0
}

type MixInfo_MixCategoryItem struct {
	CommonBusinessFlag   *CommonBusinessFlag `protobuf:"bytes,1,opt,name=common_business_flag,json=commonBusinessFlag,proto3" json:"common_business_flag,omitempty"`
	ItemId               string              `protobuf:"bytes,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	TabDetail            *topic_channel.Tab  `protobuf:"bytes,3,opt,name=tab_detail,json=tabDetail,proto3" json:"tab_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *MixInfo_MixCategoryItem) Reset()         { *m = MixInfo_MixCategoryItem{} }
func (m *MixInfo_MixCategoryItem) String() string { return proto.CompactTextString(m) }
func (*MixInfo_MixCategoryItem) ProtoMessage()    {}
func (*MixInfo_MixCategoryItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{54, 0}
}
func (m *MixInfo_MixCategoryItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MixInfo_MixCategoryItem.Unmarshal(m, b)
}
func (m *MixInfo_MixCategoryItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MixInfo_MixCategoryItem.Marshal(b, m, deterministic)
}
func (dst *MixInfo_MixCategoryItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MixInfo_MixCategoryItem.Merge(dst, src)
}
func (m *MixInfo_MixCategoryItem) XXX_Size() int {
	return xxx_messageInfo_MixInfo_MixCategoryItem.Size(m)
}
func (m *MixInfo_MixCategoryItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MixInfo_MixCategoryItem.DiscardUnknown(m)
}

var xxx_messageInfo_MixInfo_MixCategoryItem proto.InternalMessageInfo

func (m *MixInfo_MixCategoryItem) GetCommonBusinessFlag() *CommonBusinessFlag {
	if m != nil {
		return m.CommonBusinessFlag
	}
	return nil
}

func (m *MixInfo_MixCategoryItem) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *MixInfo_MixCategoryItem) GetTabDetail() *topic_channel.Tab {
	if m != nil {
		return m.TabDetail
	}
	return nil
}

// 玩法问题
type PlayQuestion struct {
	Title                string                     `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Labels               []*topic_channel.GameLabel `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PlayQuestion) Reset()         { *m = PlayQuestion{} }
func (m *PlayQuestion) String() string { return proto.CompactTextString(m) }
func (*PlayQuestion) ProtoMessage()    {}
func (*PlayQuestion) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{55}
}
func (m *PlayQuestion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayQuestion.Unmarshal(m, b)
}
func (m *PlayQuestion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayQuestion.Marshal(b, m, deterministic)
}
func (dst *PlayQuestion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayQuestion.Merge(dst, src)
}
func (m *PlayQuestion) XXX_Size() int {
	return xxx_messageInfo_PlayQuestion.Size(m)
}
func (m *PlayQuestion) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayQuestion.DiscardUnknown(m)
}

var xxx_messageInfo_PlayQuestion proto.InternalMessageInfo

func (m *PlayQuestion) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PlayQuestion) GetLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

type GetPlayQuestionsReq struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 玩法id
	TabId uint32 `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// 来源
	Source               uint32   `protobuf:"varint,3,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlayQuestionsReq) Reset()         { *m = GetPlayQuestionsReq{} }
func (m *GetPlayQuestionsReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayQuestionsReq) ProtoMessage()    {}
func (*GetPlayQuestionsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{56}
}
func (m *GetPlayQuestionsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayQuestionsReq.Unmarshal(m, b)
}
func (m *GetPlayQuestionsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayQuestionsReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayQuestionsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayQuestionsReq.Merge(dst, src)
}
func (m *GetPlayQuestionsReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayQuestionsReq.Size(m)
}
func (m *GetPlayQuestionsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayQuestionsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayQuestionsReq proto.InternalMessageInfo

func (m *GetPlayQuestionsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPlayQuestionsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetPlayQuestionsReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type GetPlayQuestionsResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 玩法问题
	Questions []*PlayQuestion `protobuf:"bytes,2,rep,name=questions,proto3" json:"questions,omitempty"`
	// 玩法发布属性
	Blocks []*topic_channel.Block `protobuf:"bytes,3,rep,name=blocks,proto3" json:"blocks,omitempty"`
	// 年龄人群标签
	AgeGroupLabels       []string `protobuf:"bytes,4,rep,name=age_group_labels,json=ageGroupLabels,proto3" json:"age_group_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlayQuestionsResp) Reset()         { *m = GetPlayQuestionsResp{} }
func (m *GetPlayQuestionsResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayQuestionsResp) ProtoMessage()    {}
func (*GetPlayQuestionsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{57}
}
func (m *GetPlayQuestionsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayQuestionsResp.Unmarshal(m, b)
}
func (m *GetPlayQuestionsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayQuestionsResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayQuestionsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayQuestionsResp.Merge(dst, src)
}
func (m *GetPlayQuestionsResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayQuestionsResp.Size(m)
}
func (m *GetPlayQuestionsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayQuestionsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayQuestionsResp proto.InternalMessageInfo

func (m *GetPlayQuestionsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPlayQuestionsResp) GetQuestions() []*PlayQuestion {
	if m != nil {
		return m.Questions
	}
	return nil
}

func (m *GetPlayQuestionsResp) GetBlocks() []*topic_channel.Block {
	if m != nil {
		return m.Blocks
	}
	return nil
}

func (m *GetPlayQuestionsResp) GetAgeGroupLabels() []string {
	if m != nil {
		return m.AgeGroupLabels
	}
	return nil
}

// 标识业务
type CommonBusinessFlag struct {
	GameBusinessId       uint32         `protobuf:"varint,1,opt,name=game_business_id,json=gameBusinessId,proto3" json:"game_business_id,omitempty"`
	MusicBusinessId      string         `protobuf:"bytes,2,opt,name=music_business_id,json=musicBusinessId,proto3" json:"music_business_id,omitempty"`
	FilterItemType       FilterItemType `protobuf:"varint,3,opt,name=filter_item_type,json=filterItemType,proto3,enum=ga.channel_play.FilterItemType" json:"filter_item_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CommonBusinessFlag) Reset()         { *m = CommonBusinessFlag{} }
func (m *CommonBusinessFlag) String() string { return proto.CompactTextString(m) }
func (*CommonBusinessFlag) ProtoMessage()    {}
func (*CommonBusinessFlag) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{58}
}
func (m *CommonBusinessFlag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonBusinessFlag.Unmarshal(m, b)
}
func (m *CommonBusinessFlag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonBusinessFlag.Marshal(b, m, deterministic)
}
func (dst *CommonBusinessFlag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonBusinessFlag.Merge(dst, src)
}
func (m *CommonBusinessFlag) XXX_Size() int {
	return xxx_messageInfo_CommonBusinessFlag.Size(m)
}
func (m *CommonBusinessFlag) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonBusinessFlag.DiscardUnknown(m)
}

var xxx_messageInfo_CommonBusinessFlag proto.InternalMessageInfo

func (m *CommonBusinessFlag) GetGameBusinessId() uint32 {
	if m != nil {
		return m.GameBusinessId
	}
	return 0
}

func (m *CommonBusinessFlag) GetMusicBusinessId() string {
	if m != nil {
		return m.MusicBusinessId
	}
	return ""
}

func (m *CommonBusinessFlag) GetFilterItemType() FilterItemType {
	if m != nil {
		return m.FilterItemType
	}
	return FilterItemType_UNKNOWN_ITEM_TYPE
}

type FilterItem struct {
	CommonBusinessFlag   *CommonBusinessFlag `protobuf:"bytes,1,opt,name=common_business_flag,json=commonBusinessFlag,proto3" json:"common_business_flag,omitempty"`
	DisplayName          string              `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	TabFilterItem        *TabItem            `protobuf:"bytes,3,opt,name=tab_filter_item,json=tabFilterItem,proto3" json:"tab_filter_item,omitempty"`
	CategoryFilterItem   *CategoryItem       `protobuf:"bytes,4,opt,name=category_filter_item,json=categoryFilterItem,proto3" json:"category_filter_item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *FilterItem) Reset()         { *m = FilterItem{} }
func (m *FilterItem) String() string { return proto.CompactTextString(m) }
func (*FilterItem) ProtoMessage()    {}
func (*FilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{59}
}
func (m *FilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterItem.Unmarshal(m, b)
}
func (m *FilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterItem.Marshal(b, m, deterministic)
}
func (dst *FilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterItem.Merge(dst, src)
}
func (m *FilterItem) XXX_Size() int {
	return xxx_messageInfo_FilterItem.Size(m)
}
func (m *FilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_FilterItem proto.InternalMessageInfo

func (m *FilterItem) GetCommonBusinessFlag() *CommonBusinessFlag {
	if m != nil {
		return m.CommonBusinessFlag
	}
	return nil
}

func (m *FilterItem) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *FilterItem) GetTabFilterItem() *TabItem {
	if m != nil {
		return m.TabFilterItem
	}
	return nil
}

func (m *FilterItem) GetCategoryFilterItem() *CategoryItem {
	if m != nil {
		return m.CategoryFilterItem
	}
	return nil
}

// 游戏专区，玩法筛选器
type GameLabelFilter struct {
	LabelDisplayName     string                     `protobuf:"bytes,1,opt,name=label_display_name,json=labelDisplayName,proto3" json:"label_display_name,omitempty"`
	Labels               []*topic_channel.GameLabel `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty"`
	ShowHot              bool                       `protobuf:"varint,3,opt,name=show_hot,json=showHot,proto3" json:"show_hot,omitempty"`
	IsRcmd               bool                       `protobuf:"varint,4,opt,name=is_rcmd,json=isRcmd,proto3" json:"is_rcmd,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GameLabelFilter) Reset()         { *m = GameLabelFilter{} }
func (m *GameLabelFilter) String() string { return proto.CompactTextString(m) }
func (*GameLabelFilter) ProtoMessage()    {}
func (*GameLabelFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{60}
}
func (m *GameLabelFilter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameLabelFilter.Unmarshal(m, b)
}
func (m *GameLabelFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameLabelFilter.Marshal(b, m, deterministic)
}
func (dst *GameLabelFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameLabelFilter.Merge(dst, src)
}
func (m *GameLabelFilter) XXX_Size() int {
	return xxx_messageInfo_GameLabelFilter.Size(m)
}
func (m *GameLabelFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_GameLabelFilter.DiscardUnknown(m)
}

var xxx_messageInfo_GameLabelFilter proto.InternalMessageInfo

func (m *GameLabelFilter) GetLabelDisplayName() string {
	if m != nil {
		return m.LabelDisplayName
	}
	return ""
}

func (m *GameLabelFilter) GetLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *GameLabelFilter) GetShowHot() bool {
	if m != nil {
		return m.ShowHot
	}
	return false
}

func (m *GameLabelFilter) GetIsRcmd() bool {
	if m != nil {
		return m.IsRcmd
	}
	return false
}

type TabItem struct {
	TabId                uint32                     `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string                     `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Blocks               []*topic_channel.Block     `protobuf:"bytes,3,rep,name=blocks,proto3" json:"blocks,omitempty"`
	Labels               []*topic_channel.GameLabel `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty"`
	Enable               bool                       `protobuf:"varint,5,opt,name=enable,proto3" json:"enable,omitempty"`
	GameLabelFilter      []*GameLabelFilter         `protobuf:"bytes,6,rep,name=game_label_filter,json=gameLabelFilter,proto3" json:"game_label_filter,omitempty"`
	TabDisplayImageUrl   string                     `protobuf:"bytes,7,opt,name=tab_display_image_url,json=tabDisplayImageUrl,proto3" json:"tab_display_image_url,omitempty"`
	GameZoneTabType      GameZoneTabType            `protobuf:"varint,8,opt,name=game_zone_tab_type,json=gameZoneTabType,proto3,enum=ga.channel_play.GameZoneTabType" json:"game_zone_tab_type,omitempty"`
	CategoryType         uint32                     `protobuf:"varint,9,opt,name=category_type,json=categoryType,proto3" json:"category_type,omitempty"`
	ClassifyLabels       []*ClassifyLabelList       `protobuf:"bytes,10,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	TabIds               []uint32                   `protobuf:"varint,11,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *TabItem) Reset()         { *m = TabItem{} }
func (m *TabItem) String() string { return proto.CompactTextString(m) }
func (*TabItem) ProtoMessage()    {}
func (*TabItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{61}
}
func (m *TabItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabItem.Unmarshal(m, b)
}
func (m *TabItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabItem.Marshal(b, m, deterministic)
}
func (dst *TabItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabItem.Merge(dst, src)
}
func (m *TabItem) XXX_Size() int {
	return xxx_messageInfo_TabItem.Size(m)
}
func (m *TabItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TabItem.DiscardUnknown(m)
}

var xxx_messageInfo_TabItem proto.InternalMessageInfo

func (m *TabItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *TabItem) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *TabItem) GetBlocks() []*topic_channel.Block {
	if m != nil {
		return m.Blocks
	}
	return nil
}

func (m *TabItem) GetLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *TabItem) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *TabItem) GetGameLabelFilter() []*GameLabelFilter {
	if m != nil {
		return m.GameLabelFilter
	}
	return nil
}

func (m *TabItem) GetTabDisplayImageUrl() string {
	if m != nil {
		return m.TabDisplayImageUrl
	}
	return ""
}

func (m *TabItem) GetGameZoneTabType() GameZoneTabType {
	if m != nil {
		return m.GameZoneTabType
	}
	return GameZoneTabType_GameZoneTabType_Game
}

func (m *TabItem) GetCategoryType() uint32 {
	if m != nil {
		return m.CategoryType
	}
	return 0
}

func (m *TabItem) GetClassifyLabels() []*ClassifyLabelList {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

func (m *TabItem) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type ClassifyLabelList struct {
	ClassifyName         string                     `protobuf:"bytes,1,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
	ClassifyLabels       []*topic_channel.GameLabel `protobuf:"bytes,2,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *ClassifyLabelList) Reset()         { *m = ClassifyLabelList{} }
func (m *ClassifyLabelList) String() string { return proto.CompactTextString(m) }
func (*ClassifyLabelList) ProtoMessage()    {}
func (*ClassifyLabelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{62}
}
func (m *ClassifyLabelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClassifyLabelList.Unmarshal(m, b)
}
func (m *ClassifyLabelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClassifyLabelList.Marshal(b, m, deterministic)
}
func (dst *ClassifyLabelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClassifyLabelList.Merge(dst, src)
}
func (m *ClassifyLabelList) XXX_Size() int {
	return xxx_messageInfo_ClassifyLabelList.Size(m)
}
func (m *ClassifyLabelList) XXX_DiscardUnknown() {
	xxx_messageInfo_ClassifyLabelList.DiscardUnknown(m)
}

var xxx_messageInfo_ClassifyLabelList proto.InternalMessageInfo

func (m *ClassifyLabelList) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

func (m *ClassifyLabelList) GetClassifyLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

type NewMusicFilterItem struct {
	FilterId             string                     `protobuf:"bytes,1,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	Blocks               []*topic_channel.Block     `protobuf:"bytes,2,rep,name=blocks,proto3" json:"blocks,omitempty"`
	Labels               []*topic_channel.GameLabel `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"`
	IsLabelOn            bool                       `protobuf:"varint,4,opt,name=is_label_on,json=isLabelOn,proto3" json:"is_label_on,omitempty"`
	ClassifyLabels       []*ClassifyLabelList       `protobuf:"bytes,5,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	TabIds               []uint32                   `protobuf:"varint,6,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *NewMusicFilterItem) Reset()         { *m = NewMusicFilterItem{} }
func (m *NewMusicFilterItem) String() string { return proto.CompactTextString(m) }
func (*NewMusicFilterItem) ProtoMessage()    {}
func (*NewMusicFilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{63}
}
func (m *NewMusicFilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewMusicFilterItem.Unmarshal(m, b)
}
func (m *NewMusicFilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewMusicFilterItem.Marshal(b, m, deterministic)
}
func (dst *NewMusicFilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewMusicFilterItem.Merge(dst, src)
}
func (m *NewMusicFilterItem) XXX_Size() int {
	return xxx_messageInfo_NewMusicFilterItem.Size(m)
}
func (m *NewMusicFilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_NewMusicFilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_NewMusicFilterItem proto.InternalMessageInfo

func (m *NewMusicFilterItem) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *NewMusicFilterItem) GetBlocks() []*topic_channel.Block {
	if m != nil {
		return m.Blocks
	}
	return nil
}

func (m *NewMusicFilterItem) GetLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *NewMusicFilterItem) GetIsLabelOn() bool {
	if m != nil {
		return m.IsLabelOn
	}
	return false
}

func (m *NewMusicFilterItem) GetClassifyLabels() []*ClassifyLabelList {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

func (m *NewMusicFilterItem) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type CategoryItem struct {
	CategoryId           uint32     `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	CategoryName         string     `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	TabItems             []*TabItem `protobuf:"bytes,3,rep,name=tab_items,json=tabItems,proto3" json:"tab_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CategoryItem) Reset()         { *m = CategoryItem{} }
func (m *CategoryItem) String() string { return proto.CompactTextString(m) }
func (*CategoryItem) ProtoMessage()    {}
func (*CategoryItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{64}
}
func (m *CategoryItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CategoryItem.Unmarshal(m, b)
}
func (m *CategoryItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CategoryItem.Marshal(b, m, deterministic)
}
func (dst *CategoryItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CategoryItem.Merge(dst, src)
}
func (m *CategoryItem) XXX_Size() int {
	return xxx_messageInfo_CategoryItem.Size(m)
}
func (m *CategoryItem) XXX_DiscardUnknown() {
	xxx_messageInfo_CategoryItem.DiscardUnknown(m)
}

var xxx_messageInfo_CategoryItem proto.InternalMessageInfo

func (m *CategoryItem) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *CategoryItem) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *CategoryItem) GetTabItems() []*TabItem {
	if m != nil {
		return m.TabItems
	}
	return nil
}

// 命令号3093
type GetFilterItemByEntranceReq struct {
	BaseReq              *app.BaseReq                          `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SelfGameIds          []uint32                              `protobuf:"varint,2,rep,packed,name=self_game_ids,json=selfGameIds,proto3" json:"self_game_ids,omitempty"`
	ActiveFilterItem     []*CommonBusinessFlag                 `protobuf:"bytes,3,rep,name=active_filter_item,json=activeFilterItem,proto3" json:"active_filter_item,omitempty"`
	FilterEntrance       FilterEntrance                        `protobuf:"varint,4,opt,name=filter_entrance,json=filterEntrance,proto3,enum=ga.channel_play.FilterEntrance" json:"filter_entrance,omitempty"`
	ChannelPkg           string                                `protobuf:"bytes,5,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	TabItemVersion       TabItemVersion                        `protobuf:"varint,6,opt,name=tab_item_version,json=tabItemVersion,proto3,enum=ga.channel_play.TabItemVersion" json:"tab_item_version,omitempty"`
	BrowseLabelsMap      map[uint32]*topic_channel.BrowseLabel `protobuf:"bytes,7,rep,name=browse_labels_map,json=browseLabelsMap,proto3" json:"browse_labels_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *GetFilterItemByEntranceReq) Reset()         { *m = GetFilterItemByEntranceReq{} }
func (m *GetFilterItemByEntranceReq) String() string { return proto.CompactTextString(m) }
func (*GetFilterItemByEntranceReq) ProtoMessage()    {}
func (*GetFilterItemByEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{65}
}
func (m *GetFilterItemByEntranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterItemByEntranceReq.Unmarshal(m, b)
}
func (m *GetFilterItemByEntranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterItemByEntranceReq.Marshal(b, m, deterministic)
}
func (dst *GetFilterItemByEntranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterItemByEntranceReq.Merge(dst, src)
}
func (m *GetFilterItemByEntranceReq) XXX_Size() int {
	return xxx_messageInfo_GetFilterItemByEntranceReq.Size(m)
}
func (m *GetFilterItemByEntranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterItemByEntranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterItemByEntranceReq proto.InternalMessageInfo

func (m *GetFilterItemByEntranceReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFilterItemByEntranceReq) GetSelfGameIds() []uint32 {
	if m != nil {
		return m.SelfGameIds
	}
	return nil
}

func (m *GetFilterItemByEntranceReq) GetActiveFilterItem() []*CommonBusinessFlag {
	if m != nil {
		return m.ActiveFilterItem
	}
	return nil
}

func (m *GetFilterItemByEntranceReq) GetFilterEntrance() FilterEntrance {
	if m != nil {
		return m.FilterEntrance
	}
	return FilterEntrance_UNKNOWN_ENTRANCE
}

func (m *GetFilterItemByEntranceReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

func (m *GetFilterItemByEntranceReq) GetTabItemVersion() TabItemVersion {
	if m != nil {
		return m.TabItemVersion
	}
	return TabItemVersion_Default_Version
}

func (m *GetFilterItemByEntranceReq) GetBrowseLabelsMap() map[uint32]*topic_channel.BrowseLabel {
	if m != nil {
		return m.BrowseLabelsMap
	}
	return nil
}

type GetFilterItemByEntranceResp struct {
	BaseResp           *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items              []*FilterItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	ComprehensiveItems []*FilterItem `protobuf:"bytes,3,rep,name=comprehensive_items,json=comprehensiveItems,proto3" json:"comprehensive_items,omitempty"`
	// 是否展示用户设置的屏蔽词
	ShowFilterWords      bool     `protobuf:"varint,4,opt,name=show_filter_words,json=showFilterWords,proto3" json:"show_filter_words,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterItemByEntranceResp) Reset()         { *m = GetFilterItemByEntranceResp{} }
func (m *GetFilterItemByEntranceResp) String() string { return proto.CompactTextString(m) }
func (*GetFilterItemByEntranceResp) ProtoMessage()    {}
func (*GetFilterItemByEntranceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{66}
}
func (m *GetFilterItemByEntranceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterItemByEntranceResp.Unmarshal(m, b)
}
func (m *GetFilterItemByEntranceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterItemByEntranceResp.Marshal(b, m, deterministic)
}
func (dst *GetFilterItemByEntranceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterItemByEntranceResp.Merge(dst, src)
}
func (m *GetFilterItemByEntranceResp) XXX_Size() int {
	return xxx_messageInfo_GetFilterItemByEntranceResp.Size(m)
}
func (m *GetFilterItemByEntranceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterItemByEntranceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterItemByEntranceResp proto.InternalMessageInfo

func (m *GetFilterItemByEntranceResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFilterItemByEntranceResp) GetItems() []*FilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetFilterItemByEntranceResp) GetComprehensiveItems() []*FilterItem {
	if m != nil {
		return m.ComprehensiveItems
	}
	return nil
}

func (m *GetFilterItemByEntranceResp) GetShowFilterWords() bool {
	if m != nil {
		return m.ShowFilterWords
	}
	return false
}

// 命令号3094
type SetDIYFilterByEntranceReq struct {
	BaseReq              *app.BaseReq          `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FilterEntrance       FilterEntrance        `protobuf:"varint,2,opt,name=filter_entrance,json=filterEntrance,proto3,enum=ga.channel_play.FilterEntrance" json:"filter_entrance,omitempty"`
	Items                []*CommonBusinessFlag `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SetDIYFilterByEntranceReq) Reset()         { *m = SetDIYFilterByEntranceReq{} }
func (m *SetDIYFilterByEntranceReq) String() string { return proto.CompactTextString(m) }
func (*SetDIYFilterByEntranceReq) ProtoMessage()    {}
func (*SetDIYFilterByEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{67}
}
func (m *SetDIYFilterByEntranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDIYFilterByEntranceReq.Unmarshal(m, b)
}
func (m *SetDIYFilterByEntranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDIYFilterByEntranceReq.Marshal(b, m, deterministic)
}
func (dst *SetDIYFilterByEntranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDIYFilterByEntranceReq.Merge(dst, src)
}
func (m *SetDIYFilterByEntranceReq) XXX_Size() int {
	return xxx_messageInfo_SetDIYFilterByEntranceReq.Size(m)
}
func (m *SetDIYFilterByEntranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDIYFilterByEntranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDIYFilterByEntranceReq proto.InternalMessageInfo

func (m *SetDIYFilterByEntranceReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetDIYFilterByEntranceReq) GetFilterEntrance() FilterEntrance {
	if m != nil {
		return m.FilterEntrance
	}
	return FilterEntrance_UNKNOWN_ENTRANCE
}

func (m *SetDIYFilterByEntranceReq) GetItems() []*CommonBusinessFlag {
	if m != nil {
		return m.Items
	}
	return nil
}

type SetDIYFilterByEntranceResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsInAbtest           bool          `protobuf:"varint,2,opt,name=is_in_abtest,json=isInAbtest,proto3" json:"is_in_abtest,omitempty"`
	NeedJumpTabId        uint32        `protobuf:"varint,3,opt,name=need_jump_tab_id,json=needJumpTabId,proto3" json:"need_jump_tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetDIYFilterByEntranceResp) Reset()         { *m = SetDIYFilterByEntranceResp{} }
func (m *SetDIYFilterByEntranceResp) String() string { return proto.CompactTextString(m) }
func (*SetDIYFilterByEntranceResp) ProtoMessage()    {}
func (*SetDIYFilterByEntranceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{68}
}
func (m *SetDIYFilterByEntranceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDIYFilterByEntranceResp.Unmarshal(m, b)
}
func (m *SetDIYFilterByEntranceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDIYFilterByEntranceResp.Marshal(b, m, deterministic)
}
func (dst *SetDIYFilterByEntranceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDIYFilterByEntranceResp.Merge(dst, src)
}
func (m *SetDIYFilterByEntranceResp) XXX_Size() int {
	return xxx_messageInfo_SetDIYFilterByEntranceResp.Size(m)
}
func (m *SetDIYFilterByEntranceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDIYFilterByEntranceResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDIYFilterByEntranceResp proto.InternalMessageInfo

func (m *SetDIYFilterByEntranceResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SetDIYFilterByEntranceResp) GetIsInAbtest() bool {
	if m != nil {
		return m.IsInAbtest
	}
	return false
}

func (m *SetDIYFilterByEntranceResp) GetNeedJumpTabId() uint32 {
	if m != nil {
		return m.NeedJumpTabId
	}
	return 0
}

// 命令号3095
type GetDIYFilterByEntranceReq struct {
	BaseReq              *app.BaseReq   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FilterEntrance       FilterEntrance `protobuf:"varint,2,opt,name=filter_entrance,json=filterEntrance,proto3,enum=ga.channel_play.FilterEntrance" json:"filter_entrance,omitempty"`
	ChannelPkg           string         `protobuf:"bytes,3,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetDIYFilterByEntranceReq) Reset()         { *m = GetDIYFilterByEntranceReq{} }
func (m *GetDIYFilterByEntranceReq) String() string { return proto.CompactTextString(m) }
func (*GetDIYFilterByEntranceReq) ProtoMessage()    {}
func (*GetDIYFilterByEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{69}
}
func (m *GetDIYFilterByEntranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDIYFilterByEntranceReq.Unmarshal(m, b)
}
func (m *GetDIYFilterByEntranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDIYFilterByEntranceReq.Marshal(b, m, deterministic)
}
func (dst *GetDIYFilterByEntranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDIYFilterByEntranceReq.Merge(dst, src)
}
func (m *GetDIYFilterByEntranceReq) XXX_Size() int {
	return xxx_messageInfo_GetDIYFilterByEntranceReq.Size(m)
}
func (m *GetDIYFilterByEntranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDIYFilterByEntranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDIYFilterByEntranceReq proto.InternalMessageInfo

func (m *GetDIYFilterByEntranceReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetDIYFilterByEntranceReq) GetFilterEntrance() FilterEntrance {
	if m != nil {
		return m.FilterEntrance
	}
	return FilterEntrance_UNKNOWN_ENTRANCE
}

func (m *GetDIYFilterByEntranceReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

type GetDIYFilterByEntranceResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DiyItem              []*DIYItem    `protobuf:"bytes,2,rep,name=diy_item,json=diyItem,proto3" json:"diy_item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetDIYFilterByEntranceResp) Reset()         { *m = GetDIYFilterByEntranceResp{} }
func (m *GetDIYFilterByEntranceResp) String() string { return proto.CompactTextString(m) }
func (*GetDIYFilterByEntranceResp) ProtoMessage()    {}
func (*GetDIYFilterByEntranceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{70}
}
func (m *GetDIYFilterByEntranceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDIYFilterByEntranceResp.Unmarshal(m, b)
}
func (m *GetDIYFilterByEntranceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDIYFilterByEntranceResp.Marshal(b, m, deterministic)
}
func (dst *GetDIYFilterByEntranceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDIYFilterByEntranceResp.Merge(dst, src)
}
func (m *GetDIYFilterByEntranceResp) XXX_Size() int {
	return xxx_messageInfo_GetDIYFilterByEntranceResp.Size(m)
}
func (m *GetDIYFilterByEntranceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDIYFilterByEntranceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDIYFilterByEntranceResp proto.InternalMessageInfo

func (m *GetDIYFilterByEntranceResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetDIYFilterByEntranceResp) GetDiyItem() []*DIYItem {
	if m != nil {
		return m.DiyItem
	}
	return nil
}

type DIYItem struct {
	DiyItem              *CommonBusinessFlag `protobuf:"bytes,1,opt,name=diy_item,json=diyItem,proto3" json:"diy_item,omitempty"`
	DisplayName          string              `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *DIYItem) Reset()         { *m = DIYItem{} }
func (m *DIYItem) String() string { return proto.CompactTextString(m) }
func (*DIYItem) ProtoMessage()    {}
func (*DIYItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{71}
}
func (m *DIYItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DIYItem.Unmarshal(m, b)
}
func (m *DIYItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DIYItem.Marshal(b, m, deterministic)
}
func (dst *DIYItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DIYItem.Merge(dst, src)
}
func (m *DIYItem) XXX_Size() int {
	return xxx_messageInfo_DIYItem.Size(m)
}
func (m *DIYItem) XXX_DiscardUnknown() {
	xxx_messageInfo_DIYItem.DiscardUnknown(m)
}

var xxx_messageInfo_DIYItem proto.InternalMessageInfo

func (m *DIYItem) GetDiyItem() *CommonBusinessFlag {
	if m != nil {
		return m.DiyItem
	}
	return nil
}

func (m *DIYItem) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

// 房间内负反馈列表
type GetNegativeFeedBackInRoomReq struct {
	BaseReq              *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FeedBackType         FeedBackTypeInRoom `protobuf:"varint,2,opt,name=feed_back_type,json=feedBackType,proto3,enum=ga.channel_play.FeedBackTypeInRoom" json:"feed_back_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetNegativeFeedBackInRoomReq) Reset()         { *m = GetNegativeFeedBackInRoomReq{} }
func (m *GetNegativeFeedBackInRoomReq) String() string { return proto.CompactTextString(m) }
func (*GetNegativeFeedBackInRoomReq) ProtoMessage()    {}
func (*GetNegativeFeedBackInRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{72}
}
func (m *GetNegativeFeedBackInRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNegativeFeedBackInRoomReq.Unmarshal(m, b)
}
func (m *GetNegativeFeedBackInRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNegativeFeedBackInRoomReq.Marshal(b, m, deterministic)
}
func (dst *GetNegativeFeedBackInRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNegativeFeedBackInRoomReq.Merge(dst, src)
}
func (m *GetNegativeFeedBackInRoomReq) XXX_Size() int {
	return xxx_messageInfo_GetNegativeFeedBackInRoomReq.Size(m)
}
func (m *GetNegativeFeedBackInRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNegativeFeedBackInRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNegativeFeedBackInRoomReq proto.InternalMessageInfo

func (m *GetNegativeFeedBackInRoomReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNegativeFeedBackInRoomReq) GetFeedBackType() FeedBackTypeInRoom {
	if m != nil {
		return m.FeedBackType
	}
	return FeedBackTypeInRoom_Unknown_FeedBackType
}

type GetNegativeFeedBackInRoomResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Reasons              []string      `protobuf:"bytes,2,rep,name=reasons,proto3" json:"reasons,omitempty"`
	MaskTitle            string        `protobuf:"bytes,3,opt,name=mask_title,json=maskTitle,proto3" json:"mask_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetNegativeFeedBackInRoomResp) Reset()         { *m = GetNegativeFeedBackInRoomResp{} }
func (m *GetNegativeFeedBackInRoomResp) String() string { return proto.CompactTextString(m) }
func (*GetNegativeFeedBackInRoomResp) ProtoMessage()    {}
func (*GetNegativeFeedBackInRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{73}
}
func (m *GetNegativeFeedBackInRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNegativeFeedBackInRoomResp.Unmarshal(m, b)
}
func (m *GetNegativeFeedBackInRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNegativeFeedBackInRoomResp.Marshal(b, m, deterministic)
}
func (dst *GetNegativeFeedBackInRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNegativeFeedBackInRoomResp.Merge(dst, src)
}
func (m *GetNegativeFeedBackInRoomResp) XXX_Size() int {
	return xxx_messageInfo_GetNegativeFeedBackInRoomResp.Size(m)
}
func (m *GetNegativeFeedBackInRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNegativeFeedBackInRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNegativeFeedBackInRoomResp proto.InternalMessageInfo

func (m *GetNegativeFeedBackInRoomResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNegativeFeedBackInRoomResp) GetReasons() []string {
	if m != nil {
		return m.Reasons
	}
	return nil
}

func (m *GetNegativeFeedBackInRoomResp) GetMaskTitle() string {
	if m != nil {
		return m.MaskTitle
	}
	return ""
}

// 上报房间内负反馈
type ReportNegativeFeedBackInRoomReq struct {
	BaseReq      *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FeedBackType FeedBackTypeInRoom `protobuf:"varint,2,opt,name=feed_back_type,json=feedBackType,proto3,enum=ga.channel_play.FeedBackTypeInRoom" json:"feed_back_type,omitempty"`
	ReporterUid  uint32             `protobuf:"varint,3,opt,name=reporter_uid,json=reporterUid,proto3" json:"reporter_uid,omitempty"`
	BlackUid     uint32             `protobuf:"varint,4,opt,name=black_uid,json=blackUid,proto3" json:"black_uid,omitempty"`
	Reasons      []string           `protobuf:"bytes,5,rep,name=reasons,proto3" json:"reasons,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ChannelId            uint32   `protobuf:"varint,6,opt,name=channelId,proto3" json:"channelId,omitempty"`
	EnableFilter         bool     `protobuf:"varint,7,opt,name=enable_filter,json=enableFilter,proto3" json:"enable_filter,omitempty"`
	TabId                uint32   `protobuf:"varint,8,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportNegativeFeedBackInRoomReq) Reset()         { *m = ReportNegativeFeedBackInRoomReq{} }
func (m *ReportNegativeFeedBackInRoomReq) String() string { return proto.CompactTextString(m) }
func (*ReportNegativeFeedBackInRoomReq) ProtoMessage()    {}
func (*ReportNegativeFeedBackInRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{74}
}
func (m *ReportNegativeFeedBackInRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportNegativeFeedBackInRoomReq.Unmarshal(m, b)
}
func (m *ReportNegativeFeedBackInRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportNegativeFeedBackInRoomReq.Marshal(b, m, deterministic)
}
func (dst *ReportNegativeFeedBackInRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportNegativeFeedBackInRoomReq.Merge(dst, src)
}
func (m *ReportNegativeFeedBackInRoomReq) XXX_Size() int {
	return xxx_messageInfo_ReportNegativeFeedBackInRoomReq.Size(m)
}
func (m *ReportNegativeFeedBackInRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportNegativeFeedBackInRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportNegativeFeedBackInRoomReq proto.InternalMessageInfo

func (m *ReportNegativeFeedBackInRoomReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportNegativeFeedBackInRoomReq) GetFeedBackType() FeedBackTypeInRoom {
	if m != nil {
		return m.FeedBackType
	}
	return FeedBackTypeInRoom_Unknown_FeedBackType
}

func (m *ReportNegativeFeedBackInRoomReq) GetReporterUid() uint32 {
	if m != nil {
		return m.ReporterUid
	}
	return 0
}

func (m *ReportNegativeFeedBackInRoomReq) GetBlackUid() uint32 {
	if m != nil {
		return m.BlackUid
	}
	return 0
}

func (m *ReportNegativeFeedBackInRoomReq) GetReasons() []string {
	if m != nil {
		return m.Reasons
	}
	return nil
}

func (m *ReportNegativeFeedBackInRoomReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportNegativeFeedBackInRoomReq) GetEnableFilter() bool {
	if m != nil {
		return m.EnableFilter
	}
	return false
}

func (m *ReportNegativeFeedBackInRoomReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type ReportNegativeFeedBackInRoomResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HitType              uint32        `protobuf:"varint,2,opt,name=hit_type,json=hitType,proto3" json:"hit_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportNegativeFeedBackInRoomResp) Reset()         { *m = ReportNegativeFeedBackInRoomResp{} }
func (m *ReportNegativeFeedBackInRoomResp) String() string { return proto.CompactTextString(m) }
func (*ReportNegativeFeedBackInRoomResp) ProtoMessage()    {}
func (*ReportNegativeFeedBackInRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{75}
}
func (m *ReportNegativeFeedBackInRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportNegativeFeedBackInRoomResp.Unmarshal(m, b)
}
func (m *ReportNegativeFeedBackInRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportNegativeFeedBackInRoomResp.Marshal(b, m, deterministic)
}
func (dst *ReportNegativeFeedBackInRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportNegativeFeedBackInRoomResp.Merge(dst, src)
}
func (m *ReportNegativeFeedBackInRoomResp) XXX_Size() int {
	return xxx_messageInfo_ReportNegativeFeedBackInRoomResp.Size(m)
}
func (m *ReportNegativeFeedBackInRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportNegativeFeedBackInRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportNegativeFeedBackInRoomResp proto.InternalMessageInfo

func (m *ReportNegativeFeedBackInRoomResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ReportNegativeFeedBackInRoomResp) GetHitType() uint32 {
	if m != nil {
		return m.HitType
	}
	return 0
}

// 房间发布选项引导
type GetPublishOptionGuideReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	RoomName             string       `protobuf:"bytes,4,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPublishOptionGuideReq) Reset()         { *m = GetPublishOptionGuideReq{} }
func (m *GetPublishOptionGuideReq) String() string { return proto.CompactTextString(m) }
func (*GetPublishOptionGuideReq) ProtoMessage()    {}
func (*GetPublishOptionGuideReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{76}
}
func (m *GetPublishOptionGuideReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublishOptionGuideReq.Unmarshal(m, b)
}
func (m *GetPublishOptionGuideReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublishOptionGuideReq.Marshal(b, m, deterministic)
}
func (dst *GetPublishOptionGuideReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublishOptionGuideReq.Merge(dst, src)
}
func (m *GetPublishOptionGuideReq) XXX_Size() int {
	return xxx_messageInfo_GetPublishOptionGuideReq.Size(m)
}
func (m *GetPublishOptionGuideReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublishOptionGuideReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublishOptionGuideReq proto.InternalMessageInfo

func (m *GetPublishOptionGuideReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPublishOptionGuideReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPublishOptionGuideReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetPublishOptionGuideReq) GetRoomName() string {
	if m != nil {
		return m.RoomName
	}
	return ""
}

type GetPublishOptionGuideResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 6.60及之前版本的发布选项引导
	PublishOptionElems []*PublishOptionElem `protobuf:"bytes,2,rep,name=publish_option_elems,json=publishOptionElems,proto3" json:"publish_option_elems,omitempty"`
	// 6.61之后版本推荐选择标签
	RcmdOptionElems []*RecommendationElem `protobuf:"bytes,3,rep,name=rcmd_option_elems,json=rcmdOptionElems,proto3" json:"rcmd_option_elems,omitempty"`
	// 6.61之后版本推荐填写标签
	RcmdInputElems       []*RecommendationElem `protobuf:"bytes,4,rep,name=rcmd_input_elems,json=rcmdInputElems,proto3" json:"rcmd_input_elems,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPublishOptionGuideResp) Reset()         { *m = GetPublishOptionGuideResp{} }
func (m *GetPublishOptionGuideResp) String() string { return proto.CompactTextString(m) }
func (*GetPublishOptionGuideResp) ProtoMessage()    {}
func (*GetPublishOptionGuideResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{77}
}
func (m *GetPublishOptionGuideResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublishOptionGuideResp.Unmarshal(m, b)
}
func (m *GetPublishOptionGuideResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublishOptionGuideResp.Marshal(b, m, deterministic)
}
func (dst *GetPublishOptionGuideResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublishOptionGuideResp.Merge(dst, src)
}
func (m *GetPublishOptionGuideResp) XXX_Size() int {
	return xxx_messageInfo_GetPublishOptionGuideResp.Size(m)
}
func (m *GetPublishOptionGuideResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublishOptionGuideResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublishOptionGuideResp proto.InternalMessageInfo

func (m *GetPublishOptionGuideResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPublishOptionGuideResp) GetPublishOptionElems() []*PublishOptionElem {
	if m != nil {
		return m.PublishOptionElems
	}
	return nil
}

func (m *GetPublishOptionGuideResp) GetRcmdOptionElems() []*RecommendationElem {
	if m != nil {
		return m.RcmdOptionElems
	}
	return nil
}

func (m *GetPublishOptionGuideResp) GetRcmdInputElems() []*RecommendationElem {
	if m != nil {
		return m.RcmdInputElems
	}
	return nil
}

type PublishOptionElem struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	ElemTitle            string   `protobuf:"bytes,3,opt,name=elem_title,json=elemTitle,proto3" json:"elem_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PublishOptionElem) Reset()         { *m = PublishOptionElem{} }
func (m *PublishOptionElem) String() string { return proto.CompactTextString(m) }
func (*PublishOptionElem) ProtoMessage()    {}
func (*PublishOptionElem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{78}
}
func (m *PublishOptionElem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishOptionElem.Unmarshal(m, b)
}
func (m *PublishOptionElem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishOptionElem.Marshal(b, m, deterministic)
}
func (dst *PublishOptionElem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishOptionElem.Merge(dst, src)
}
func (m *PublishOptionElem) XXX_Size() int {
	return xxx_messageInfo_PublishOptionElem.Size(m)
}
func (m *PublishOptionElem) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishOptionElem.DiscardUnknown(m)
}

var xxx_messageInfo_PublishOptionElem proto.InternalMessageInfo

func (m *PublishOptionElem) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *PublishOptionElem) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

func (m *PublishOptionElem) GetElemTitle() string {
	if m != nil {
		return m.ElemTitle
	}
	return ""
}

type RecommendationElem struct {
	Type uint32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	// Types that are valid to be assigned to Data:
	//	*RecommendationElem_PublishOptionElem
	//	*RecommendationElem_GameLabel
	Data                 isRecommendationElem_Data `protobuf_oneof:"data"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *RecommendationElem) Reset()         { *m = RecommendationElem{} }
func (m *RecommendationElem) String() string { return proto.CompactTextString(m) }
func (*RecommendationElem) ProtoMessage()    {}
func (*RecommendationElem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{79}
}
func (m *RecommendationElem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendationElem.Unmarshal(m, b)
}
func (m *RecommendationElem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendationElem.Marshal(b, m, deterministic)
}
func (dst *RecommendationElem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendationElem.Merge(dst, src)
}
func (m *RecommendationElem) XXX_Size() int {
	return xxx_messageInfo_RecommendationElem.Size(m)
}
func (m *RecommendationElem) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendationElem.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendationElem proto.InternalMessageInfo

func (m *RecommendationElem) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type isRecommendationElem_Data interface {
	isRecommendationElem_Data()
}

type RecommendationElem_PublishOptionElem struct {
	PublishOptionElem *PublishOptionElem `protobuf:"bytes,2,opt,name=publish_option_elem,json=publishOptionElem,proto3,oneof"`
}

type RecommendationElem_GameLabel struct {
	GameLabel *topic_channel.GameLabel `protobuf:"bytes,3,opt,name=game_label,json=gameLabel,proto3,oneof"`
}

func (*RecommendationElem_PublishOptionElem) isRecommendationElem_Data() {}

func (*RecommendationElem_GameLabel) isRecommendationElem_Data() {}

func (m *RecommendationElem) GetData() isRecommendationElem_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *RecommendationElem) GetPublishOptionElem() *PublishOptionElem {
	if x, ok := m.GetData().(*RecommendationElem_PublishOptionElem); ok {
		return x.PublishOptionElem
	}
	return nil
}

func (m *RecommendationElem) GetGameLabel() *topic_channel.GameLabel {
	if x, ok := m.GetData().(*RecommendationElem_GameLabel); ok {
		return x.GameLabel
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*RecommendationElem) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _RecommendationElem_OneofMarshaler, _RecommendationElem_OneofUnmarshaler, _RecommendationElem_OneofSizer, []interface{}{
		(*RecommendationElem_PublishOptionElem)(nil),
		(*RecommendationElem_GameLabel)(nil),
	}
}

func _RecommendationElem_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*RecommendationElem)
	// data
	switch x := m.Data.(type) {
	case *RecommendationElem_PublishOptionElem:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.PublishOptionElem); err != nil {
			return err
		}
	case *RecommendationElem_GameLabel:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.GameLabel); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("RecommendationElem.Data has unexpected type %T", x)
	}
	return nil
}

func _RecommendationElem_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*RecommendationElem)
	switch tag {
	case 2: // data.publish_option_elem
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(PublishOptionElem)
		err := b.DecodeMessage(msg)
		m.Data = &RecommendationElem_PublishOptionElem{msg}
		return true, err
	case 3: // data.game_label
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(topic_channel.GameLabel)
		err := b.DecodeMessage(msg)
		m.Data = &RecommendationElem_GameLabel{msg}
		return true, err
	default:
		return false, nil
	}
}

func _RecommendationElem_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*RecommendationElem)
	// data
	switch x := m.Data.(type) {
	case *RecommendationElem_PublishOptionElem:
		s := proto.Size(x.PublishOptionElem)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *RecommendationElem_GameLabel:
		s := proto.Size(x.GameLabel)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type NewQuickMatchConfig struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	ButtonText           string   `protobuf:"bytes,4,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	Position             uint32   `protobuf:"varint,5,opt,name=position,proto3" json:"position,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewQuickMatchConfig) Reset()         { *m = NewQuickMatchConfig{} }
func (m *NewQuickMatchConfig) String() string { return proto.CompactTextString(m) }
func (*NewQuickMatchConfig) ProtoMessage()    {}
func (*NewQuickMatchConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{80}
}
func (m *NewQuickMatchConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewQuickMatchConfig.Unmarshal(m, b)
}
func (m *NewQuickMatchConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewQuickMatchConfig.Marshal(b, m, deterministic)
}
func (dst *NewQuickMatchConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewQuickMatchConfig.Merge(dst, src)
}
func (m *NewQuickMatchConfig) XXX_Size() int {
	return xxx_messageInfo_NewQuickMatchConfig.Size(m)
}
func (m *NewQuickMatchConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_NewQuickMatchConfig.DiscardUnknown(m)
}

var xxx_messageInfo_NewQuickMatchConfig proto.InternalMessageInfo

func (m *NewQuickMatchConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *NewQuickMatchConfig) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *NewQuickMatchConfig) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *NewQuickMatchConfig) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

func (m *NewQuickMatchConfig) GetPosition() uint32 {
	if m != nil {
		return m.Position
	}
	return 0
}

type GetNewQuickMatchConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetNewQuickMatchConfigReq) Reset()         { *m = GetNewQuickMatchConfigReq{} }
func (m *GetNewQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetNewQuickMatchConfigReq) ProtoMessage()    {}
func (*GetNewQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{81}
}
func (m *GetNewQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *GetNewQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetNewQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewQuickMatchConfigReq.Merge(dst, src)
}
func (m *GetNewQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetNewQuickMatchConfigReq.Size(m)
}
func (m *GetNewQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewQuickMatchConfigReq proto.InternalMessageInfo

func (m *GetNewQuickMatchConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNewQuickMatchConfigReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetNewQuickMatchConfigResp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Config               *NewQuickMatchConfig `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetNewQuickMatchConfigResp) Reset()         { *m = GetNewQuickMatchConfigResp{} }
func (m *GetNewQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetNewQuickMatchConfigResp) ProtoMessage()    {}
func (*GetNewQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{82}
}
func (m *GetNewQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *GetNewQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetNewQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewQuickMatchConfigResp.Merge(dst, src)
}
func (m *GetNewQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetNewQuickMatchConfigResp.Size(m)
}
func (m *GetNewQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewQuickMatchConfigResp proto.InternalMessageInfo

func (m *GetNewQuickMatchConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNewQuickMatchConfigResp) GetConfig() *NewQuickMatchConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type GetTopicChannelCfgInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32       `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTopicChannelCfgInfoReq) Reset()         { *m = GetTopicChannelCfgInfoReq{} }
func (m *GetTopicChannelCfgInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicChannelCfgInfoReq) ProtoMessage()    {}
func (*GetTopicChannelCfgInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{83}
}
func (m *GetTopicChannelCfgInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicChannelCfgInfoReq.Unmarshal(m, b)
}
func (m *GetTopicChannelCfgInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicChannelCfgInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicChannelCfgInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicChannelCfgInfoReq.Merge(dst, src)
}
func (m *GetTopicChannelCfgInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicChannelCfgInfoReq.Size(m)
}
func (m *GetTopicChannelCfgInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicChannelCfgInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicChannelCfgInfoReq proto.InternalMessageInfo

func (m *GetTopicChannelCfgInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetTopicChannelCfgInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetTopicChannelCfgInfoReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetTopicChannelCfgInfoResp struct {
	BaseResp       *app.BaseResp                      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId      uint32                             `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId          uint32                             `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName        string                             `protobuf:"bytes,4,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	IsInGround     bool                               `protobuf:"varint,5,opt,name=is_in_ground,json=isInGround,proto3" json:"is_in_ground,omitempty"`
	PlayingOption  []uint32                           `protobuf:"varint,6,rep,packed,name=playing_option,json=playingOption,proto3" json:"playing_option,omitempty"`
	SwitchPlayInfo *topic_channel.SwitchPlayInfo      `protobuf:"bytes,7,opt,name=switch_play_info,json=switchPlayInfo,proto3" json:"switch_play_info,omitempty"`
	TabType        GetTopicChannelCfgInfoResp_TabType `protobuf:"varint,8,opt,name=tab_type,json=tabType,proto3,enum=ga.channel_play.GetTopicChannelCfgInfoResp_TabType" json:"tab_type,omitempty"`
	WelcomeText    string                             `protobuf:"bytes,9,opt,name=welcome_text,json=welcomeText,proto3" json:"welcome_text,omitempty"`
	// 从房间内跳到指定外部第三方游戏相关配置(即下载游戏)
	ThirdPartyGame        *topic_channel.ThirdPartyGame `protobuf:"bytes,10,opt,name=third_party_game,json=thirdPartyGame,proto3" json:"third_party_game,omitempty"`
	TeamDesc              string                        `protobuf:"bytes,11,opt,name=team_desc,json=teamDesc,proto3" json:"team_desc,omitempty"`
	ShowTeamDesc          bool                          `protobuf:"varint,12,opt,name=show_team_desc,json=showTeamDesc,proto3" json:"show_team_desc,omitempty"`
	ShowPublishButton     bool                          `protobuf:"varint,13,opt,name=show_publish_button,json=showPublishButton,proto3" json:"show_publish_button,omitempty"`
	CategoryType          uint32                        `protobuf:"varint,14,opt,name=category_type,json=categoryType,proto3" json:"category_type,omitempty"`
	MiniGameId            uint32                        `protobuf:"varint,15,opt,name=mini_game_id,json=miniGameId,proto3" json:"mini_game_id,omitempty"`
	GameCardId            uint32                        `protobuf:"varint,16,opt,name=game_card_id,json=gameCardId,proto3" json:"game_card_id,omitempty"`
	TagId                 uint32                        `protobuf:"varint,17,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	ChannelPlayMode       UgcChannelPlayMode            `protobuf:"varint,18,opt,name=channel_play_mode,json=channelPlayMode,proto3,enum=ga.channel_play.UgcChannelPlayMode" json:"channel_play_mode,omitempty"` // Deprecated: Do not use.
	GamePalEntranceStatus uint32                        `protobuf:"varint,19,opt,name=game_pal_entrance_status,json=gamePalEntranceStatus,proto3" json:"game_pal_entrance_status,omitempty"`
	FastPcCfgTabInfo      *FastPcCfgTabInfo             `protobuf:"bytes,20,opt,name=fast_pc_cfg_tab_info,json=fastPcCfgTabInfo,proto3" json:"fast_pc_cfg_tab_info,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                      `json:"-"`
	XXX_unrecognized      []byte                        `json:"-"`
	XXX_sizecache         int32                         `json:"-"`
}

func (m *GetTopicChannelCfgInfoResp) Reset()         { *m = GetTopicChannelCfgInfoResp{} }
func (m *GetTopicChannelCfgInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicChannelCfgInfoResp) ProtoMessage()    {}
func (*GetTopicChannelCfgInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{84}
}
func (m *GetTopicChannelCfgInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicChannelCfgInfoResp.Unmarshal(m, b)
}
func (m *GetTopicChannelCfgInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicChannelCfgInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicChannelCfgInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicChannelCfgInfoResp.Merge(dst, src)
}
func (m *GetTopicChannelCfgInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicChannelCfgInfoResp.Size(m)
}
func (m *GetTopicChannelCfgInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicChannelCfgInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicChannelCfgInfoResp proto.InternalMessageInfo

func (m *GetTopicChannelCfgInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetTopicChannelCfgInfoResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetTopicChannelCfgInfoResp) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetTopicChannelCfgInfoResp) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *GetTopicChannelCfgInfoResp) GetIsInGround() bool {
	if m != nil {
		return m.IsInGround
	}
	return false
}

func (m *GetTopicChannelCfgInfoResp) GetPlayingOption() []uint32 {
	if m != nil {
		return m.PlayingOption
	}
	return nil
}

func (m *GetTopicChannelCfgInfoResp) GetSwitchPlayInfo() *topic_channel.SwitchPlayInfo {
	if m != nil {
		return m.SwitchPlayInfo
	}
	return nil
}

func (m *GetTopicChannelCfgInfoResp) GetTabType() GetTopicChannelCfgInfoResp_TabType {
	if m != nil {
		return m.TabType
	}
	return GetTopicChannelCfgInfoResp_NORMAL
}

func (m *GetTopicChannelCfgInfoResp) GetWelcomeText() string {
	if m != nil {
		return m.WelcomeText
	}
	return ""
}

func (m *GetTopicChannelCfgInfoResp) GetThirdPartyGame() *topic_channel.ThirdPartyGame {
	if m != nil {
		return m.ThirdPartyGame
	}
	return nil
}

func (m *GetTopicChannelCfgInfoResp) GetTeamDesc() string {
	if m != nil {
		return m.TeamDesc
	}
	return ""
}

func (m *GetTopicChannelCfgInfoResp) GetShowTeamDesc() bool {
	if m != nil {
		return m.ShowTeamDesc
	}
	return false
}

func (m *GetTopicChannelCfgInfoResp) GetShowPublishButton() bool {
	if m != nil {
		return m.ShowPublishButton
	}
	return false
}

func (m *GetTopicChannelCfgInfoResp) GetCategoryType() uint32 {
	if m != nil {
		return m.CategoryType
	}
	return 0
}

func (m *GetTopicChannelCfgInfoResp) GetMiniGameId() uint32 {
	if m != nil {
		return m.MiniGameId
	}
	return 0
}

func (m *GetTopicChannelCfgInfoResp) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

func (m *GetTopicChannelCfgInfoResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

// Deprecated: Do not use.
func (m *GetTopicChannelCfgInfoResp) GetChannelPlayMode() UgcChannelPlayMode {
	if m != nil {
		return m.ChannelPlayMode
	}
	return UgcChannelPlayMode_DEFAULT_VOICE_MODE
}

func (m *GetTopicChannelCfgInfoResp) GetGamePalEntranceStatus() uint32 {
	if m != nil {
		return m.GamePalEntranceStatus
	}
	return 0
}

func (m *GetTopicChannelCfgInfoResp) GetFastPcCfgTabInfo() *FastPcCfgTabInfo {
	if m != nil {
		return m.FastPcCfgTabInfo
	}
	return nil
}

type FastPcCfgTabInfo struct {
	FastPcPublishDesc []*FastPcCfgTabInfo_FastPcPublishDesc `protobuf:"bytes,1,rep,name=fast_pc_publish_desc,json=fastPcPublishDesc,proto3" json:"fast_pc_publish_desc,omitempty"`
	// 新增PC极速版大厅房间背景图
	RoomBackgroundImgUrl string `protobuf:"bytes,2,opt,name=room_background_img_url,json=roomBackgroundImgUrl,proto3" json:"room_background_img_url,omitempty"`
	// 房间发布信息修改cd
	ChangeCoolDown uint32 `protobuf:"varint,3,opt,name=change_cool_down,json=changeCoolDown,proto3" json:"change_cool_down,omitempty"`
	// 房间发布信息cd
	FreezeDuration uint32 `protobuf:"varint,4,opt,name=freeze_duration,json=freezeDuration,proto3" json:"freeze_duration,omitempty"`
	// 自动取消时长
	AutoDismissDuration  uint32   `protobuf:"varint,5,opt,name=auto_dismiss_duration,json=autoDismissDuration,proto3" json:"auto_dismiss_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FastPcCfgTabInfo) Reset()         { *m = FastPcCfgTabInfo{} }
func (m *FastPcCfgTabInfo) String() string { return proto.CompactTextString(m) }
func (*FastPcCfgTabInfo) ProtoMessage()    {}
func (*FastPcCfgTabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{85}
}
func (m *FastPcCfgTabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FastPcCfgTabInfo.Unmarshal(m, b)
}
func (m *FastPcCfgTabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FastPcCfgTabInfo.Marshal(b, m, deterministic)
}
func (dst *FastPcCfgTabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FastPcCfgTabInfo.Merge(dst, src)
}
func (m *FastPcCfgTabInfo) XXX_Size() int {
	return xxx_messageInfo_FastPcCfgTabInfo.Size(m)
}
func (m *FastPcCfgTabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FastPcCfgTabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FastPcCfgTabInfo proto.InternalMessageInfo

func (m *FastPcCfgTabInfo) GetFastPcPublishDesc() []*FastPcCfgTabInfo_FastPcPublishDesc {
	if m != nil {
		return m.FastPcPublishDesc
	}
	return nil
}

func (m *FastPcCfgTabInfo) GetRoomBackgroundImgUrl() string {
	if m != nil {
		return m.RoomBackgroundImgUrl
	}
	return ""
}

func (m *FastPcCfgTabInfo) GetChangeCoolDown() uint32 {
	if m != nil {
		return m.ChangeCoolDown
	}
	return 0
}

func (m *FastPcCfgTabInfo) GetFreezeDuration() uint32 {
	if m != nil {
		return m.FreezeDuration
	}
	return 0
}

func (m *FastPcCfgTabInfo) GetAutoDismissDuration() uint32 {
	if m != nil {
		return m.AutoDismissDuration
	}
	return 0
}

type FastPcCfgTabInfo_FastPcPublishDesc struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	ElemName             string   `protobuf:"bytes,2,opt,name=elem_name,json=elemName,proto3" json:"elem_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FastPcCfgTabInfo_FastPcPublishDesc) Reset()         { *m = FastPcCfgTabInfo_FastPcPublishDesc{} }
func (m *FastPcCfgTabInfo_FastPcPublishDesc) String() string { return proto.CompactTextString(m) }
func (*FastPcCfgTabInfo_FastPcPublishDesc) ProtoMessage()    {}
func (*FastPcCfgTabInfo_FastPcPublishDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{85, 0}
}
func (m *FastPcCfgTabInfo_FastPcPublishDesc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FastPcCfgTabInfo_FastPcPublishDesc.Unmarshal(m, b)
}
func (m *FastPcCfgTabInfo_FastPcPublishDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FastPcCfgTabInfo_FastPcPublishDesc.Marshal(b, m, deterministic)
}
func (dst *FastPcCfgTabInfo_FastPcPublishDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FastPcCfgTabInfo_FastPcPublishDesc.Merge(dst, src)
}
func (m *FastPcCfgTabInfo_FastPcPublishDesc) XXX_Size() int {
	return xxx_messageInfo_FastPcCfgTabInfo_FastPcPublishDesc.Size(m)
}
func (m *FastPcCfgTabInfo_FastPcPublishDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_FastPcCfgTabInfo_FastPcPublishDesc.DiscardUnknown(m)
}

var xxx_messageInfo_FastPcCfgTabInfo_FastPcPublishDesc proto.InternalMessageInfo

func (m *FastPcCfgTabInfo_FastPcPublishDesc) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *FastPcCfgTabInfo_FastPcPublishDesc) GetElemName() string {
	if m != nil {
		return m.ElemName
	}
	return ""
}

// 设置房间玩法模式（语音，文字）
type SetUgcChannelPlayModeReq struct {
	BaseReq              *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32             `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelPlayMode      UgcChannelPlayMode `protobuf:"varint,3,opt,name=channel_play_mode,json=channelPlayMode,proto3,enum=ga.channel_play.UgcChannelPlayMode" json:"channel_play_mode,omitempty"`
	Uid                  uint32             `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32             `protobuf:"varint,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SetUgcChannelPlayModeReq) Reset()         { *m = SetUgcChannelPlayModeReq{} }
func (m *SetUgcChannelPlayModeReq) String() string { return proto.CompactTextString(m) }
func (*SetUgcChannelPlayModeReq) ProtoMessage()    {}
func (*SetUgcChannelPlayModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{86}
}
func (m *SetUgcChannelPlayModeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUgcChannelPlayModeReq.Unmarshal(m, b)
}
func (m *SetUgcChannelPlayModeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUgcChannelPlayModeReq.Marshal(b, m, deterministic)
}
func (dst *SetUgcChannelPlayModeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUgcChannelPlayModeReq.Merge(dst, src)
}
func (m *SetUgcChannelPlayModeReq) XXX_Size() int {
	return xxx_messageInfo_SetUgcChannelPlayModeReq.Size(m)
}
func (m *SetUgcChannelPlayModeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUgcChannelPlayModeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUgcChannelPlayModeReq proto.InternalMessageInfo

func (m *SetUgcChannelPlayModeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUgcChannelPlayModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetUgcChannelPlayModeReq) GetChannelPlayMode() UgcChannelPlayMode {
	if m != nil {
		return m.ChannelPlayMode
	}
	return UgcChannelPlayMode_DEFAULT_VOICE_MODE
}

func (m *SetUgcChannelPlayModeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUgcChannelPlayModeReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type SetUgcChannelPlayModeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ToastTitle           string        `protobuf:"bytes,2,opt,name=toast_title,json=toastTitle,proto3" json:"toast_title,omitempty"` // Deprecated: Do not use.
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUgcChannelPlayModeResp) Reset()         { *m = SetUgcChannelPlayModeResp{} }
func (m *SetUgcChannelPlayModeResp) String() string { return proto.CompactTextString(m) }
func (*SetUgcChannelPlayModeResp) ProtoMessage()    {}
func (*SetUgcChannelPlayModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{87}
}
func (m *SetUgcChannelPlayModeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUgcChannelPlayModeResp.Unmarshal(m, b)
}
func (m *SetUgcChannelPlayModeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUgcChannelPlayModeResp.Marshal(b, m, deterministic)
}
func (dst *SetUgcChannelPlayModeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUgcChannelPlayModeResp.Merge(dst, src)
}
func (m *SetUgcChannelPlayModeResp) XXX_Size() int {
	return xxx_messageInfo_SetUgcChannelPlayModeResp.Size(m)
}
func (m *SetUgcChannelPlayModeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUgcChannelPlayModeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUgcChannelPlayModeResp proto.InternalMessageInfo

func (m *SetUgcChannelPlayModeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// Deprecated: Do not use.
func (m *SetUgcChannelPlayModeResp) GetToastTitle() string {
	if m != nil {
		return m.ToastTitle
	}
	return ""
}

// 文字房用户输入状态广播
type TypingStatusBroadcastReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32       `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TypingStatusBroadcastReq) Reset()         { *m = TypingStatusBroadcastReq{} }
func (m *TypingStatusBroadcastReq) String() string { return proto.CompactTextString(m) }
func (*TypingStatusBroadcastReq) ProtoMessage()    {}
func (*TypingStatusBroadcastReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{88}
}
func (m *TypingStatusBroadcastReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TypingStatusBroadcastReq.Unmarshal(m, b)
}
func (m *TypingStatusBroadcastReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TypingStatusBroadcastReq.Marshal(b, m, deterministic)
}
func (dst *TypingStatusBroadcastReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TypingStatusBroadcastReq.Merge(dst, src)
}
func (m *TypingStatusBroadcastReq) XXX_Size() int {
	return xxx_messageInfo_TypingStatusBroadcastReq.Size(m)
}
func (m *TypingStatusBroadcastReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TypingStatusBroadcastReq.DiscardUnknown(m)
}

var xxx_messageInfo_TypingStatusBroadcastReq proto.InternalMessageInfo

func (m *TypingStatusBroadcastReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *TypingStatusBroadcastReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TypingStatusBroadcastReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type TypingStatusBroadcastResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *TypingStatusBroadcastResp) Reset()         { *m = TypingStatusBroadcastResp{} }
func (m *TypingStatusBroadcastResp) String() string { return proto.CompactTextString(m) }
func (*TypingStatusBroadcastResp) ProtoMessage()    {}
func (*TypingStatusBroadcastResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{89}
}
func (m *TypingStatusBroadcastResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TypingStatusBroadcastResp.Unmarshal(m, b)
}
func (m *TypingStatusBroadcastResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TypingStatusBroadcastResp.Marshal(b, m, deterministic)
}
func (dst *TypingStatusBroadcastResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TypingStatusBroadcastResp.Merge(dst, src)
}
func (m *TypingStatusBroadcastResp) XXX_Size() int {
	return xxx_messageInfo_TypingStatusBroadcastResp.Size(m)
}
func (m *TypingStatusBroadcastResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TypingStatusBroadcastResp.DiscardUnknown(m)
}

var xxx_messageInfo_TypingStatusBroadcastResp proto.InternalMessageInfo

func (m *TypingStatusBroadcastResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 文字房工具栏引导气泡文案
type GetChannelPlayModeGuideReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Version              uint32       `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	TabId                uint32       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelPlayModeGuideReq) Reset()         { *m = GetChannelPlayModeGuideReq{} }
func (m *GetChannelPlayModeGuideReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelPlayModeGuideReq) ProtoMessage()    {}
func (*GetChannelPlayModeGuideReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{90}
}
func (m *GetChannelPlayModeGuideReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPlayModeGuideReq.Unmarshal(m, b)
}
func (m *GetChannelPlayModeGuideReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPlayModeGuideReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelPlayModeGuideReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPlayModeGuideReq.Merge(dst, src)
}
func (m *GetChannelPlayModeGuideReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelPlayModeGuideReq.Size(m)
}
func (m *GetChannelPlayModeGuideReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPlayModeGuideReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPlayModeGuideReq proto.InternalMessageInfo

func (m *GetChannelPlayModeGuideReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelPlayModeGuideReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *GetChannelPlayModeGuideReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetChannelPlayModeGuideResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Version              uint32        `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	GuideWords           string        `protobuf:"bytes,3,opt,name=guide_words,json=guideWords,proto3" json:"guide_words,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelPlayModeGuideResp) Reset()         { *m = GetChannelPlayModeGuideResp{} }
func (m *GetChannelPlayModeGuideResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelPlayModeGuideResp) ProtoMessage()    {}
func (*GetChannelPlayModeGuideResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{91}
}
func (m *GetChannelPlayModeGuideResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPlayModeGuideResp.Unmarshal(m, b)
}
func (m *GetChannelPlayModeGuideResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPlayModeGuideResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelPlayModeGuideResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPlayModeGuideResp.Merge(dst, src)
}
func (m *GetChannelPlayModeGuideResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelPlayModeGuideResp.Size(m)
}
func (m *GetChannelPlayModeGuideResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPlayModeGuideResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPlayModeGuideResp proto.InternalMessageInfo

func (m *GetChannelPlayModeGuideResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelPlayModeGuideResp) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *GetChannelPlayModeGuideResp) GetGuideWords() string {
	if m != nil {
		return m.GuideWords
	}
	return ""
}

// 输入状态推送
type TypeStatusPushMsg struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TypeStatusPushMsg) Reset()         { *m = TypeStatusPushMsg{} }
func (m *TypeStatusPushMsg) String() string { return proto.CompactTextString(m) }
func (*TypeStatusPushMsg) ProtoMessage()    {}
func (*TypeStatusPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{92}
}
func (m *TypeStatusPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TypeStatusPushMsg.Unmarshal(m, b)
}
func (m *TypeStatusPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TypeStatusPushMsg.Marshal(b, m, deterministic)
}
func (dst *TypeStatusPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TypeStatusPushMsg.Merge(dst, src)
}
func (m *TypeStatusPushMsg) XXX_Size() int {
	return xxx_messageInfo_TypeStatusPushMsg.Size(m)
}
func (m *TypeStatusPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_TypeStatusPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_TypeStatusPushMsg proto.InternalMessageInfo

func (m *TypeStatusPushMsg) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *TypeStatusPushMsg) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

// 房间模式变化推送
type UgcChannelPlayModeChangeMsg struct {
	ChannelMode          UgcChannelPlayMode `protobuf:"varint,1,opt,name=channel_mode,json=channelMode,proto3,enum=ga.channel_play.UgcChannelPlayMode" json:"channel_mode,omitempty"`
	ChangeTime           uint64             `protobuf:"varint,2,opt,name=change_time,json=changeTime,proto3" json:"change_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UgcChannelPlayModeChangeMsg) Reset()         { *m = UgcChannelPlayModeChangeMsg{} }
func (m *UgcChannelPlayModeChangeMsg) String() string { return proto.CompactTextString(m) }
func (*UgcChannelPlayModeChangeMsg) ProtoMessage()    {}
func (*UgcChannelPlayModeChangeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{93}
}
func (m *UgcChannelPlayModeChangeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UgcChannelPlayModeChangeMsg.Unmarshal(m, b)
}
func (m *UgcChannelPlayModeChangeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UgcChannelPlayModeChangeMsg.Marshal(b, m, deterministic)
}
func (dst *UgcChannelPlayModeChangeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UgcChannelPlayModeChangeMsg.Merge(dst, src)
}
func (m *UgcChannelPlayModeChangeMsg) XXX_Size() int {
	return xxx_messageInfo_UgcChannelPlayModeChangeMsg.Size(m)
}
func (m *UgcChannelPlayModeChangeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UgcChannelPlayModeChangeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UgcChannelPlayModeChangeMsg proto.InternalMessageInfo

func (m *UgcChannelPlayModeChangeMsg) GetChannelMode() UgcChannelPlayMode {
	if m != nil {
		return m.ChannelMode
	}
	return UgcChannelPlayMode_DEFAULT_VOICE_MODE
}

func (m *UgcChannelPlayModeChangeMsg) GetChangeTime() uint64 {
	if m != nil {
		return m.ChangeTime
	}
	return 0
}

// 活动任务上报
type ReportDailyTaskReq struct {
	BaseReq              *app.BaseReq  `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TaskType             DailyTaskType `protobuf:"varint,3,opt,name=task_type,json=taskType,proto3,enum=ga.channel_play.DailyTaskType" json:"task_type,omitempty"`
	TabId                uint32        `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ConfigTabId          string        `protobuf:"bytes,5,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"`
	StayDuration         uint32        `protobuf:"varint,6,opt,name=stay_duration,json=stayDuration,proto3" json:"stay_duration,omitempty"`
	ViewType             uint32        `protobuf:"varint,7,opt,name=view_type,json=viewType,proto3" json:"view_type,omitempty"`
	ViewCount            uint32        `protobuf:"varint,8,opt,name=view_count,json=viewCount,proto3" json:"view_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportDailyTaskReq) Reset()         { *m = ReportDailyTaskReq{} }
func (m *ReportDailyTaskReq) String() string { return proto.CompactTextString(m) }
func (*ReportDailyTaskReq) ProtoMessage()    {}
func (*ReportDailyTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{94}
}
func (m *ReportDailyTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportDailyTaskReq.Unmarshal(m, b)
}
func (m *ReportDailyTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportDailyTaskReq.Marshal(b, m, deterministic)
}
func (dst *ReportDailyTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportDailyTaskReq.Merge(dst, src)
}
func (m *ReportDailyTaskReq) XXX_Size() int {
	return xxx_messageInfo_ReportDailyTaskReq.Size(m)
}
func (m *ReportDailyTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportDailyTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportDailyTaskReq proto.InternalMessageInfo

func (m *ReportDailyTaskReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportDailyTaskReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportDailyTaskReq) GetTaskType() DailyTaskType {
	if m != nil {
		return m.TaskType
	}
	return DailyTaskType_INVALID_TASK_TYPE
}

func (m *ReportDailyTaskReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ReportDailyTaskReq) GetConfigTabId() string {
	if m != nil {
		return m.ConfigTabId
	}
	return ""
}

func (m *ReportDailyTaskReq) GetStayDuration() uint32 {
	if m != nil {
		return m.StayDuration
	}
	return 0
}

func (m *ReportDailyTaskReq) GetViewType() uint32 {
	if m != nil {
		return m.ViewType
	}
	return 0
}

func (m *ReportDailyTaskReq) GetViewCount() uint32 {
	if m != nil {
		return m.ViewCount
	}
	return 0
}

type ReportDailyTaskResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportDailyTaskResp) Reset()         { *m = ReportDailyTaskResp{} }
func (m *ReportDailyTaskResp) String() string { return proto.CompactTextString(m) }
func (*ReportDailyTaskResp) ProtoMessage()    {}
func (*ReportDailyTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{95}
}
func (m *ReportDailyTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportDailyTaskResp.Unmarshal(m, b)
}
func (m *ReportDailyTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportDailyTaskResp.Marshal(b, m, deterministic)
}
func (dst *ReportDailyTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportDailyTaskResp.Merge(dst, src)
}
func (m *ReportDailyTaskResp) XXX_Size() int {
	return xxx_messageInfo_ReportDailyTaskResp.Size(m)
}
func (m *ReportDailyTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportDailyTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportDailyTaskResp proto.InternalMessageInfo

func (m *ReportDailyTaskResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetCacheReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCacheReq) Reset()         { *m = GetCacheReq{} }
func (m *GetCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetCacheReq) ProtoMessage()    {}
func (*GetCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{96}
}
func (m *GetCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCacheReq.Unmarshal(m, b)
}
func (m *GetCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCacheReq.Merge(dst, src)
}
func (m *GetCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetCacheReq.Size(m)
}
func (m *GetCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCacheReq proto.InternalMessageInfo

func (m *GetCacheReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type CacheData struct {
	Ids                  []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CacheData) Reset()         { *m = CacheData{} }
func (m *CacheData) String() string { return proto.CompactTextString(m) }
func (*CacheData) ProtoMessage()    {}
func (*CacheData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{97}
}
func (m *CacheData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CacheData.Unmarshal(m, b)
}
func (m *CacheData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CacheData.Marshal(b, m, deterministic)
}
func (dst *CacheData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CacheData.Merge(dst, src)
}
func (m *CacheData) XXX_Size() int {
	return xxx_messageInfo_CacheData.Size(m)
}
func (m *CacheData) XXX_DiscardUnknown() {
	xxx_messageInfo_CacheData.DiscardUnknown(m)
}

var xxx_messageInfo_CacheData proto.InternalMessageInfo

func (m *CacheData) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type CacheBlockData struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemIds              []uint32 `protobuf:"varint,2,rep,packed,name=elem_ids,json=elemIds,proto3" json:"elem_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CacheBlockData) Reset()         { *m = CacheBlockData{} }
func (m *CacheBlockData) String() string { return proto.CompactTextString(m) }
func (*CacheBlockData) ProtoMessage()    {}
func (*CacheBlockData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{98}
}
func (m *CacheBlockData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CacheBlockData.Unmarshal(m, b)
}
func (m *CacheBlockData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CacheBlockData.Marshal(b, m, deterministic)
}
func (dst *CacheBlockData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CacheBlockData.Merge(dst, src)
}
func (m *CacheBlockData) XXX_Size() int {
	return xxx_messageInfo_CacheBlockData.Size(m)
}
func (m *CacheBlockData) XXX_DiscardUnknown() {
	xxx_messageInfo_CacheBlockData.DiscardUnknown(m)
}

var xxx_messageInfo_CacheBlockData proto.InternalMessageInfo

func (m *CacheBlockData) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *CacheBlockData) GetElemIds() []uint32 {
	if m != nil {
		return m.ElemIds
	}
	return nil
}

type CacheTabData struct {
	TabId                uint32            `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockDatas           []*CacheBlockData `protobuf:"bytes,2,rep,name=block_datas,json=blockDatas,proto3" json:"block_datas,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CacheTabData) Reset()         { *m = CacheTabData{} }
func (m *CacheTabData) String() string { return proto.CompactTextString(m) }
func (*CacheTabData) ProtoMessage()    {}
func (*CacheTabData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{99}
}
func (m *CacheTabData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CacheTabData.Unmarshal(m, b)
}
func (m *CacheTabData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CacheTabData.Marshal(b, m, deterministic)
}
func (dst *CacheTabData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CacheTabData.Merge(dst, src)
}
func (m *CacheTabData) XXX_Size() int {
	return xxx_messageInfo_CacheTabData.Size(m)
}
func (m *CacheTabData) XXX_DiscardUnknown() {
	xxx_messageInfo_CacheTabData.DiscardUnknown(m)
}

var xxx_messageInfo_CacheTabData proto.InternalMessageInfo

func (m *CacheTabData) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *CacheTabData) GetBlockDatas() []*CacheBlockData {
	if m != nil {
		return m.BlockDatas
	}
	return nil
}

type GetCacheResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	CategoryIds          []uint32              `protobuf:"varint,2,rep,packed,name=categoryIds,proto3" json:"categoryIds,omitempty"`
	CategoryTabsMap      map[uint32]*CacheData `protobuf:"bytes,3,rep,name=category_tabs_map,json=categoryTabsMap,proto3" json:"category_tabs_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TabIds               []uint32              `protobuf:"varint,4,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	MinGameTabIds        []uint32              `protobuf:"varint,5,rep,packed,name=min_game_tab_ids,json=minGameTabIds,proto3" json:"min_game_tab_ids,omitempty"`
	TabDisplayBlockIds   []*CacheTabData       `protobuf:"bytes,6,rep,name=tab_display_block_ids,json=tabDisplayBlockIds,proto3" json:"tab_display_block_ids,omitempty"`
	TabBlockIds          []*CacheTabData       `protobuf:"bytes,7,rep,name=tab_block_ids,json=tabBlockIds,proto3" json:"tab_block_ids,omitempty"`
	TabBaseBlockIds      []*CacheTabData       `protobuf:"bytes,8,rep,name=tab_base_block_ids,json=tabBaseBlockIds,proto3" json:"tab_base_block_ids,omitempty"`
	GameCardTabsMap      map[uint32]*CacheData `protobuf:"bytes,9,rep,name=game_card_tabs_map,json=gameCardTabsMap,proto3" json:"game_card_tabs_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetCacheResp) Reset()         { *m = GetCacheResp{} }
func (m *GetCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetCacheResp) ProtoMessage()    {}
func (*GetCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{100}
}
func (m *GetCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCacheResp.Unmarshal(m, b)
}
func (m *GetCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCacheResp.Merge(dst, src)
}
func (m *GetCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetCacheResp.Size(m)
}
func (m *GetCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCacheResp proto.InternalMessageInfo

func (m *GetCacheResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCacheResp) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

func (m *GetCacheResp) GetCategoryTabsMap() map[uint32]*CacheData {
	if m != nil {
		return m.CategoryTabsMap
	}
	return nil
}

func (m *GetCacheResp) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *GetCacheResp) GetMinGameTabIds() []uint32 {
	if m != nil {
		return m.MinGameTabIds
	}
	return nil
}

func (m *GetCacheResp) GetTabDisplayBlockIds() []*CacheTabData {
	if m != nil {
		return m.TabDisplayBlockIds
	}
	return nil
}

func (m *GetCacheResp) GetTabBlockIds() []*CacheTabData {
	if m != nil {
		return m.TabBlockIds
	}
	return nil
}

func (m *GetCacheResp) GetTabBaseBlockIds() []*CacheTabData {
	if m != nil {
		return m.TabBaseBlockIds
	}
	return nil
}

func (m *GetCacheResp) GetGameCardTabsMap() map[uint32]*CacheData {
	if m != nil {
		return m.GameCardTabsMap
	}
	return nil
}

// 专区入口检查，目前只有电竞有这个检查
// CMD_HomePageHeadConfigEnterCheck = 3105;   //首页专区是否能进入检查判断
type HomePageHeadConfigEnterCheckReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ConfigId             string       `protobuf:"bytes,2,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HomePageHeadConfigEnterCheckReq) Reset()         { *m = HomePageHeadConfigEnterCheckReq{} }
func (m *HomePageHeadConfigEnterCheckReq) String() string { return proto.CompactTextString(m) }
func (*HomePageHeadConfigEnterCheckReq) ProtoMessage()    {}
func (*HomePageHeadConfigEnterCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{101}
}
func (m *HomePageHeadConfigEnterCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageHeadConfigEnterCheckReq.Unmarshal(m, b)
}
func (m *HomePageHeadConfigEnterCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageHeadConfigEnterCheckReq.Marshal(b, m, deterministic)
}
func (dst *HomePageHeadConfigEnterCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageHeadConfigEnterCheckReq.Merge(dst, src)
}
func (m *HomePageHeadConfigEnterCheckReq) XXX_Size() int {
	return xxx_messageInfo_HomePageHeadConfigEnterCheckReq.Size(m)
}
func (m *HomePageHeadConfigEnterCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageHeadConfigEnterCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageHeadConfigEnterCheckReq proto.InternalMessageInfo

func (m *HomePageHeadConfigEnterCheckReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HomePageHeadConfigEnterCheckReq) GetConfigId() string {
	if m != nil {
		return m.ConfigId
	}
	return ""
}

type HomePageHeadConfigEnterCheckResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsCanEnter           bool          `protobuf:"varint,2,opt,name=is_can_enter,json=isCanEnter,proto3" json:"is_can_enter,omitempty"`
	Msg                  string        `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HomePageHeadConfigEnterCheckResp) Reset()         { *m = HomePageHeadConfigEnterCheckResp{} }
func (m *HomePageHeadConfigEnterCheckResp) String() string { return proto.CompactTextString(m) }
func (*HomePageHeadConfigEnterCheckResp) ProtoMessage()    {}
func (*HomePageHeadConfigEnterCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{102}
}
func (m *HomePageHeadConfigEnterCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageHeadConfigEnterCheckResp.Unmarshal(m, b)
}
func (m *HomePageHeadConfigEnterCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageHeadConfigEnterCheckResp.Marshal(b, m, deterministic)
}
func (dst *HomePageHeadConfigEnterCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageHeadConfigEnterCheckResp.Merge(dst, src)
}
func (m *HomePageHeadConfigEnterCheckResp) XXX_Size() int {
	return xxx_messageInfo_HomePageHeadConfigEnterCheckResp.Size(m)
}
func (m *HomePageHeadConfigEnterCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageHeadConfigEnterCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageHeadConfigEnterCheckResp proto.InternalMessageInfo

func (m *HomePageHeadConfigEnterCheckResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *HomePageHeadConfigEnterCheckResp) GetIsCanEnter() bool {
	if m != nil {
		return m.IsCanEnter
	}
	return false
}

func (m *HomePageHeadConfigEnterCheckResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type GetChannelListGuideConfigsReq struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// Types that are valid to be assigned to GuideReq:
	//	*GetChannelListGuideConfigsReq_HomePageGuideReq_
	GuideReq             isGetChannelListGuideConfigsReq_GuideReq `protobuf_oneof:"guide_req"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *GetChannelListGuideConfigsReq) Reset()         { *m = GetChannelListGuideConfigsReq{} }
func (m *GetChannelListGuideConfigsReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelListGuideConfigsReq) ProtoMessage()    {}
func (*GetChannelListGuideConfigsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{103}
}
func (m *GetChannelListGuideConfigsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListGuideConfigsReq.Unmarshal(m, b)
}
func (m *GetChannelListGuideConfigsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListGuideConfigsReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelListGuideConfigsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListGuideConfigsReq.Merge(dst, src)
}
func (m *GetChannelListGuideConfigsReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelListGuideConfigsReq.Size(m)
}
func (m *GetChannelListGuideConfigsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListGuideConfigsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListGuideConfigsReq proto.InternalMessageInfo

func (m *GetChannelListGuideConfigsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type isGetChannelListGuideConfigsReq_GuideReq interface {
	isGetChannelListGuideConfigsReq_GuideReq()
}

type GetChannelListGuideConfigsReq_HomePageGuideReq_ struct {
	HomePageGuideReq *GetChannelListGuideConfigsReq_HomePageGuideReq `protobuf:"bytes,2,opt,name=home_page_guide_req,json=homePageGuideReq,proto3,oneof"`
}

func (*GetChannelListGuideConfigsReq_HomePageGuideReq_) isGetChannelListGuideConfigsReq_GuideReq() {}

func (m *GetChannelListGuideConfigsReq) GetGuideReq() isGetChannelListGuideConfigsReq_GuideReq {
	if m != nil {
		return m.GuideReq
	}
	return nil
}

func (m *GetChannelListGuideConfigsReq) GetHomePageGuideReq() *GetChannelListGuideConfigsReq_HomePageGuideReq {
	if x, ok := m.GetGuideReq().(*GetChannelListGuideConfigsReq_HomePageGuideReq_); ok {
		return x.HomePageGuideReq
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GetChannelListGuideConfigsReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GetChannelListGuideConfigsReq_OneofMarshaler, _GetChannelListGuideConfigsReq_OneofUnmarshaler, _GetChannelListGuideConfigsReq_OneofSizer, []interface{}{
		(*GetChannelListGuideConfigsReq_HomePageGuideReq_)(nil),
	}
}

func _GetChannelListGuideConfigsReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GetChannelListGuideConfigsReq)
	// guide_req
	switch x := m.GuideReq.(type) {
	case *GetChannelListGuideConfigsReq_HomePageGuideReq_:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.HomePageGuideReq); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GetChannelListGuideConfigsReq.GuideReq has unexpected type %T", x)
	}
	return nil
}

func _GetChannelListGuideConfigsReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GetChannelListGuideConfigsReq)
	switch tag {
	case 2: // guide_req.home_page_guide_req
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GetChannelListGuideConfigsReq_HomePageGuideReq)
		err := b.DecodeMessage(msg)
		m.GuideReq = &GetChannelListGuideConfigsReq_HomePageGuideReq_{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GetChannelListGuideConfigsReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GetChannelListGuideConfigsReq)
	// guide_req
	switch x := m.GuideReq.(type) {
	case *GetChannelListGuideConfigsReq_HomePageGuideReq_:
		s := proto.Size(x.HomePageGuideReq)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type GetChannelListGuideConfigsReq_HomePageGuideReq struct {
	TabId                        uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	IsSatisfyExposeStayCondition bool     `protobuf:"varint,2,opt,name=is_satisfy_expose_stay_condition,json=isSatisfyExposeStayCondition,proto3" json:"is_satisfy_expose_stay_condition,omitempty"`
	PlayBlackAbTest              string   `protobuf:"bytes,3,opt,name=play_black_ab_test,json=playBlackAbTest,proto3" json:"play_black_ab_test,omitempty"`
	GamePalAbTest                string   `protobuf:"bytes,4,opt,name=game_pal_ab_test,json=gamePalAbTest,proto3" json:"game_pal_ab_test,omitempty"`
	QuickMatchCfgExposeCount     uint32   `protobuf:"varint,5,opt,name=quick_match_cfg_expose_count,json=quickMatchCfgExposeCount,proto3" json:"quick_match_cfg_expose_count,omitempty"`
	XXX_NoUnkeyedLiteral         struct{} `json:"-"`
	XXX_unrecognized             []byte   `json:"-"`
	XXX_sizecache                int32    `json:"-"`
}

func (m *GetChannelListGuideConfigsReq_HomePageGuideReq) Reset() {
	*m = GetChannelListGuideConfigsReq_HomePageGuideReq{}
}
func (m *GetChannelListGuideConfigsReq_HomePageGuideReq) String() string {
	return proto.CompactTextString(m)
}
func (*GetChannelListGuideConfigsReq_HomePageGuideReq) ProtoMessage() {}
func (*GetChannelListGuideConfigsReq_HomePageGuideReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{103, 0}
}
func (m *GetChannelListGuideConfigsReq_HomePageGuideReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListGuideConfigsReq_HomePageGuideReq.Unmarshal(m, b)
}
func (m *GetChannelListGuideConfigsReq_HomePageGuideReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListGuideConfigsReq_HomePageGuideReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelListGuideConfigsReq_HomePageGuideReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListGuideConfigsReq_HomePageGuideReq.Merge(dst, src)
}
func (m *GetChannelListGuideConfigsReq_HomePageGuideReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelListGuideConfigsReq_HomePageGuideReq.Size(m)
}
func (m *GetChannelListGuideConfigsReq_HomePageGuideReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListGuideConfigsReq_HomePageGuideReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListGuideConfigsReq_HomePageGuideReq proto.InternalMessageInfo

func (m *GetChannelListGuideConfigsReq_HomePageGuideReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetChannelListGuideConfigsReq_HomePageGuideReq) GetIsSatisfyExposeStayCondition() bool {
	if m != nil {
		return m.IsSatisfyExposeStayCondition
	}
	return false
}

func (m *GetChannelListGuideConfigsReq_HomePageGuideReq) GetPlayBlackAbTest() string {
	if m != nil {
		return m.PlayBlackAbTest
	}
	return ""
}

func (m *GetChannelListGuideConfigsReq_HomePageGuideReq) GetGamePalAbTest() string {
	if m != nil {
		return m.GamePalAbTest
	}
	return ""
}

func (m *GetChannelListGuideConfigsReq_HomePageGuideReq) GetQuickMatchCfgExposeCount() uint32 {
	if m != nil {
		return m.QuickMatchCfgExposeCount
	}
	return 0
}

type GetChannelListGuideConfigsResp struct {
	BaseResp             *app.BaseResp                               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GuideConfig          *GetChannelListGuideConfigsResp_GuideConfig `protobuf:"bytes,2,opt,name=guide_config,json=guideConfig,proto3" json:"guide_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                    `json:"-"`
	XXX_unrecognized     []byte                                      `json:"-"`
	XXX_sizecache        int32                                       `json:"-"`
}

func (m *GetChannelListGuideConfigsResp) Reset()         { *m = GetChannelListGuideConfigsResp{} }
func (m *GetChannelListGuideConfigsResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelListGuideConfigsResp) ProtoMessage()    {}
func (*GetChannelListGuideConfigsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{104}
}
func (m *GetChannelListGuideConfigsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListGuideConfigsResp.Unmarshal(m, b)
}
func (m *GetChannelListGuideConfigsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListGuideConfigsResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelListGuideConfigsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListGuideConfigsResp.Merge(dst, src)
}
func (m *GetChannelListGuideConfigsResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelListGuideConfigsResp.Size(m)
}
func (m *GetChannelListGuideConfigsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListGuideConfigsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListGuideConfigsResp proto.InternalMessageInfo

func (m *GetChannelListGuideConfigsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelListGuideConfigsResp) GetGuideConfig() *GetChannelListGuideConfigsResp_GuideConfig {
	if m != nil {
		return m.GuideConfig
	}
	return nil
}

type GetChannelListGuideConfigsResp_GuideConfig struct {
	// Types that are valid to be assigned to Config:
	//	*GetChannelListGuideConfigsResp_GuideConfig_NewQuickMatchConfig
	//	*GetChannelListGuideConfigsResp_GuideConfig_PalCardGuideConfig
	//	*GetChannelListGuideConfigsResp_GuideConfig_GameContentGuideConfig
	Config               isGetChannelListGuideConfigsResp_GuideConfig_Config `protobuf_oneof:"config"`
	XXX_NoUnkeyedLiteral struct{}                                            `json:"-"`
	XXX_unrecognized     []byte                                              `json:"-"`
	XXX_sizecache        int32                                               `json:"-"`
}

func (m *GetChannelListGuideConfigsResp_GuideConfig) Reset() {
	*m = GetChannelListGuideConfigsResp_GuideConfig{}
}
func (m *GetChannelListGuideConfigsResp_GuideConfig) String() string {
	return proto.CompactTextString(m)
}
func (*GetChannelListGuideConfigsResp_GuideConfig) ProtoMessage() {}
func (*GetChannelListGuideConfigsResp_GuideConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{104, 0}
}
func (m *GetChannelListGuideConfigsResp_GuideConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListGuideConfigsResp_GuideConfig.Unmarshal(m, b)
}
func (m *GetChannelListGuideConfigsResp_GuideConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListGuideConfigsResp_GuideConfig.Marshal(b, m, deterministic)
}
func (dst *GetChannelListGuideConfigsResp_GuideConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListGuideConfigsResp_GuideConfig.Merge(dst, src)
}
func (m *GetChannelListGuideConfigsResp_GuideConfig) XXX_Size() int {
	return xxx_messageInfo_GetChannelListGuideConfigsResp_GuideConfig.Size(m)
}
func (m *GetChannelListGuideConfigsResp_GuideConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListGuideConfigsResp_GuideConfig.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListGuideConfigsResp_GuideConfig proto.InternalMessageInfo

type isGetChannelListGuideConfigsResp_GuideConfig_Config interface {
	isGetChannelListGuideConfigsResp_GuideConfig_Config()
}

type GetChannelListGuideConfigsResp_GuideConfig_NewQuickMatchConfig struct {
	NewQuickMatchConfig *NewQuickMatchConfig `protobuf:"bytes,1,opt,name=new_quick_match_config,json=newQuickMatchConfig,proto3,oneof"`
}

type GetChannelListGuideConfigsResp_GuideConfig_PalCardGuideConfig struct {
	PalCardGuideConfig *PalCardGuideConfig `protobuf:"bytes,2,opt,name=pal_card_guide_config,json=palCardGuideConfig,proto3,oneof"`
}

type GetChannelListGuideConfigsResp_GuideConfig_GameContentGuideConfig struct {
	GameContentGuideConfig *GameContentGuideConfig `protobuf:"bytes,3,opt,name=game_content_guide_config,json=gameContentGuideConfig,proto3,oneof"`
}

func (*GetChannelListGuideConfigsResp_GuideConfig_NewQuickMatchConfig) isGetChannelListGuideConfigsResp_GuideConfig_Config() {
}

func (*GetChannelListGuideConfigsResp_GuideConfig_PalCardGuideConfig) isGetChannelListGuideConfigsResp_GuideConfig_Config() {
}

func (*GetChannelListGuideConfigsResp_GuideConfig_GameContentGuideConfig) isGetChannelListGuideConfigsResp_GuideConfig_Config() {
}

func (m *GetChannelListGuideConfigsResp_GuideConfig) GetConfig() isGetChannelListGuideConfigsResp_GuideConfig_Config {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *GetChannelListGuideConfigsResp_GuideConfig) GetNewQuickMatchConfig() *NewQuickMatchConfig {
	if x, ok := m.GetConfig().(*GetChannelListGuideConfigsResp_GuideConfig_NewQuickMatchConfig); ok {
		return x.NewQuickMatchConfig
	}
	return nil
}

func (m *GetChannelListGuideConfigsResp_GuideConfig) GetPalCardGuideConfig() *PalCardGuideConfig {
	if x, ok := m.GetConfig().(*GetChannelListGuideConfigsResp_GuideConfig_PalCardGuideConfig); ok {
		return x.PalCardGuideConfig
	}
	return nil
}

func (m *GetChannelListGuideConfigsResp_GuideConfig) GetGameContentGuideConfig() *GameContentGuideConfig {
	if x, ok := m.GetConfig().(*GetChannelListGuideConfigsResp_GuideConfig_GameContentGuideConfig); ok {
		return x.GameContentGuideConfig
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GetChannelListGuideConfigsResp_GuideConfig) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GetChannelListGuideConfigsResp_GuideConfig_OneofMarshaler, _GetChannelListGuideConfigsResp_GuideConfig_OneofUnmarshaler, _GetChannelListGuideConfigsResp_GuideConfig_OneofSizer, []interface{}{
		(*GetChannelListGuideConfigsResp_GuideConfig_NewQuickMatchConfig)(nil),
		(*GetChannelListGuideConfigsResp_GuideConfig_PalCardGuideConfig)(nil),
		(*GetChannelListGuideConfigsResp_GuideConfig_GameContentGuideConfig)(nil),
	}
}

func _GetChannelListGuideConfigsResp_GuideConfig_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GetChannelListGuideConfigsResp_GuideConfig)
	// config
	switch x := m.Config.(type) {
	case *GetChannelListGuideConfigsResp_GuideConfig_NewQuickMatchConfig:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.NewQuickMatchConfig); err != nil {
			return err
		}
	case *GetChannelListGuideConfigsResp_GuideConfig_PalCardGuideConfig:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.PalCardGuideConfig); err != nil {
			return err
		}
	case *GetChannelListGuideConfigsResp_GuideConfig_GameContentGuideConfig:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.GameContentGuideConfig); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GetChannelListGuideConfigsResp_GuideConfig.Config has unexpected type %T", x)
	}
	return nil
}

func _GetChannelListGuideConfigsResp_GuideConfig_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GetChannelListGuideConfigsResp_GuideConfig)
	switch tag {
	case 1: // config.new_quick_match_config
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(NewQuickMatchConfig)
		err := b.DecodeMessage(msg)
		m.Config = &GetChannelListGuideConfigsResp_GuideConfig_NewQuickMatchConfig{msg}
		return true, err
	case 2: // config.pal_card_guide_config
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(PalCardGuideConfig)
		err := b.DecodeMessage(msg)
		m.Config = &GetChannelListGuideConfigsResp_GuideConfig_PalCardGuideConfig{msg}
		return true, err
	case 3: // config.game_content_guide_config
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GameContentGuideConfig)
		err := b.DecodeMessage(msg)
		m.Config = &GetChannelListGuideConfigsResp_GuideConfig_GameContentGuideConfig{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GetChannelListGuideConfigsResp_GuideConfig_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GetChannelListGuideConfigsResp_GuideConfig)
	// config
	switch x := m.Config.(type) {
	case *GetChannelListGuideConfigsResp_GuideConfig_NewQuickMatchConfig:
		s := proto.Size(x.NewQuickMatchConfig)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GetChannelListGuideConfigsResp_GuideConfig_PalCardGuideConfig:
		s := proto.Size(x.PalCardGuideConfig)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GetChannelListGuideConfigsResp_GuideConfig_GameContentGuideConfig:
		s := proto.Size(x.GameContentGuideConfig)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type PalCardGuideConfig struct {
	Position             []uint32                                   `protobuf:"varint,1,rep,packed,name=position,proto3" json:"position,omitempty"`
	GamePalItems         []*PalCardGuideConfig_GamePalCardGuideInfo `protobuf:"bytes,2,rep,name=game_pal_items,json=gamePalItems,proto3" json:"game_pal_items,omitempty"`
	GamePalItem          []*game_pal_logic.GamePalItem              `protobuf:"bytes,3,rep,name=game_pal_item,json=gamePalItem,proto3" json:"game_pal_item,omitempty"` // Deprecated: Do not use.
	GamePalListInfo      []*game_pal_logic.GamePalItem              `protobuf:"bytes,4,rep,name=game_pal_list_info,json=gamePalListInfo,proto3" json:"game_pal_list_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *PalCardGuideConfig) Reset()         { *m = PalCardGuideConfig{} }
func (m *PalCardGuideConfig) String() string { return proto.CompactTextString(m) }
func (*PalCardGuideConfig) ProtoMessage()    {}
func (*PalCardGuideConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{105}
}
func (m *PalCardGuideConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PalCardGuideConfig.Unmarshal(m, b)
}
func (m *PalCardGuideConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PalCardGuideConfig.Marshal(b, m, deterministic)
}
func (dst *PalCardGuideConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PalCardGuideConfig.Merge(dst, src)
}
func (m *PalCardGuideConfig) XXX_Size() int {
	return xxx_messageInfo_PalCardGuideConfig.Size(m)
}
func (m *PalCardGuideConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PalCardGuideConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PalCardGuideConfig proto.InternalMessageInfo

func (m *PalCardGuideConfig) GetPosition() []uint32 {
	if m != nil {
		return m.Position
	}
	return nil
}

func (m *PalCardGuideConfig) GetGamePalItems() []*PalCardGuideConfig_GamePalCardGuideInfo {
	if m != nil {
		return m.GamePalItems
	}
	return nil
}

// Deprecated: Do not use.
func (m *PalCardGuideConfig) GetGamePalItem() []*game_pal_logic.GamePalItem {
	if m != nil {
		return m.GamePalItem
	}
	return nil
}

func (m *PalCardGuideConfig) GetGamePalListInfo() []*game_pal_logic.GamePalItem {
	if m != nil {
		return m.GamePalListInfo
	}
	return nil
}

type PalCardGuideConfig_GamePalCardGuideInfo struct {
	CardId               string   `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	DisplayLabels        []string `protobuf:"bytes,4,rep,name=display_labels,json=displayLabels,proto3" json:"display_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PalCardGuideConfig_GamePalCardGuideInfo) Reset() {
	*m = PalCardGuideConfig_GamePalCardGuideInfo{}
}
func (m *PalCardGuideConfig_GamePalCardGuideInfo) String() string { return proto.CompactTextString(m) }
func (*PalCardGuideConfig_GamePalCardGuideInfo) ProtoMessage()    {}
func (*PalCardGuideConfig_GamePalCardGuideInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{105, 0}
}
func (m *PalCardGuideConfig_GamePalCardGuideInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PalCardGuideConfig_GamePalCardGuideInfo.Unmarshal(m, b)
}
func (m *PalCardGuideConfig_GamePalCardGuideInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PalCardGuideConfig_GamePalCardGuideInfo.Marshal(b, m, deterministic)
}
func (dst *PalCardGuideConfig_GamePalCardGuideInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PalCardGuideConfig_GamePalCardGuideInfo.Merge(dst, src)
}
func (m *PalCardGuideConfig_GamePalCardGuideInfo) XXX_Size() int {
	return xxx_messageInfo_PalCardGuideConfig_GamePalCardGuideInfo.Size(m)
}
func (m *PalCardGuideConfig_GamePalCardGuideInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PalCardGuideConfig_GamePalCardGuideInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PalCardGuideConfig_GamePalCardGuideInfo proto.InternalMessageInfo

func (m *PalCardGuideConfig_GamePalCardGuideInfo) GetCardId() string {
	if m != nil {
		return m.CardId
	}
	return ""
}

func (m *PalCardGuideConfig_GamePalCardGuideInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PalCardGuideConfig_GamePalCardGuideInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PalCardGuideConfig_GamePalCardGuideInfo) GetDisplayLabels() []string {
	if m != nil {
		return m.DisplayLabels
	}
	return nil
}

type GameContentGuideConfig struct {
	Position             []uint32                                    `protobuf:"varint,1,rep,packed,name=position,proto3" json:"position,omitempty"`
	Feeds                []*GameContentGuideConfig_GameFeedGuideInfo `protobuf:"bytes,2,rep,name=feeds,proto3" json:"feeds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                    `json:"-"`
	XXX_unrecognized     []byte                                      `json:"-"`
	XXX_sizecache        int32                                       `json:"-"`
}

func (m *GameContentGuideConfig) Reset()         { *m = GameContentGuideConfig{} }
func (m *GameContentGuideConfig) String() string { return proto.CompactTextString(m) }
func (*GameContentGuideConfig) ProtoMessage()    {}
func (*GameContentGuideConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{106}
}
func (m *GameContentGuideConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameContentGuideConfig.Unmarshal(m, b)
}
func (m *GameContentGuideConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameContentGuideConfig.Marshal(b, m, deterministic)
}
func (dst *GameContentGuideConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameContentGuideConfig.Merge(dst, src)
}
func (m *GameContentGuideConfig) XXX_Size() int {
	return xxx_messageInfo_GameContentGuideConfig.Size(m)
}
func (m *GameContentGuideConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_GameContentGuideConfig.DiscardUnknown(m)
}

var xxx_messageInfo_GameContentGuideConfig proto.InternalMessageInfo

func (m *GameContentGuideConfig) GetPosition() []uint32 {
	if m != nil {
		return m.Position
	}
	return nil
}

func (m *GameContentGuideConfig) GetFeeds() []*GameContentGuideConfig_GameFeedGuideInfo {
	if m != nil {
		return m.Feeds
	}
	return nil
}

type GameContentGuideConfig_GameFeedGuideInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	ImgUrls              []string `protobuf:"bytes,3,rep,name=img_urls,json=imgUrls,proto3" json:"img_urls,omitempty"`
	ConfigTabId          string   `protobuf:"bytes,4,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"`
	Account              string   `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string   `protobuf:"bytes,6,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Uid                  uint32   `protobuf:"varint,7,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameContentGuideConfig_GameFeedGuideInfo) Reset() {
	*m = GameContentGuideConfig_GameFeedGuideInfo{}
}
func (m *GameContentGuideConfig_GameFeedGuideInfo) String() string { return proto.CompactTextString(m) }
func (*GameContentGuideConfig_GameFeedGuideInfo) ProtoMessage()    {}
func (*GameContentGuideConfig_GameFeedGuideInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{106, 0}
}
func (m *GameContentGuideConfig_GameFeedGuideInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameContentGuideConfig_GameFeedGuideInfo.Unmarshal(m, b)
}
func (m *GameContentGuideConfig_GameFeedGuideInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameContentGuideConfig_GameFeedGuideInfo.Marshal(b, m, deterministic)
}
func (dst *GameContentGuideConfig_GameFeedGuideInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameContentGuideConfig_GameFeedGuideInfo.Merge(dst, src)
}
func (m *GameContentGuideConfig_GameFeedGuideInfo) XXX_Size() int {
	return xxx_messageInfo_GameContentGuideConfig_GameFeedGuideInfo.Size(m)
}
func (m *GameContentGuideConfig_GameFeedGuideInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameContentGuideConfig_GameFeedGuideInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameContentGuideConfig_GameFeedGuideInfo proto.InternalMessageInfo

func (m *GameContentGuideConfig_GameFeedGuideInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GameContentGuideConfig_GameFeedGuideInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GameContentGuideConfig_GameFeedGuideInfo) GetImgUrls() []string {
	if m != nil {
		return m.ImgUrls
	}
	return nil
}

func (m *GameContentGuideConfig_GameFeedGuideInfo) GetConfigTabId() string {
	if m != nil {
		return m.ConfigTabId
	}
	return ""
}

func (m *GameContentGuideConfig_GameFeedGuideInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GameContentGuideConfig_GameFeedGuideInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *GameContentGuideConfig_GameFeedGuideInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetTabInfosReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabIds               []uint32     `protobuf:"varint,2,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTabInfosReq) Reset()         { *m = GetTabInfosReq{} }
func (m *GetTabInfosReq) String() string { return proto.CompactTextString(m) }
func (*GetTabInfosReq) ProtoMessage()    {}
func (*GetTabInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{107}
}
func (m *GetTabInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabInfosReq.Unmarshal(m, b)
}
func (m *GetTabInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabInfosReq.Marshal(b, m, deterministic)
}
func (dst *GetTabInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabInfosReq.Merge(dst, src)
}
func (m *GetTabInfosReq) XXX_Size() int {
	return xxx_messageInfo_GetTabInfosReq.Size(m)
}
func (m *GetTabInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabInfosReq proto.InternalMessageInfo

func (m *GetTabInfosReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetTabInfosReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type GetTabInfosResp struct {
	BaseResp             *app.BaseResp                 `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TabInfos             map[uint32]*topic_channel.Tab `protobuf:"bytes,2,rep,name=tab_infos,json=tabInfos,proto3" json:"tab_infos,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetTabInfosResp) Reset()         { *m = GetTabInfosResp{} }
func (m *GetTabInfosResp) String() string { return proto.CompactTextString(m) }
func (*GetTabInfosResp) ProtoMessage()    {}
func (*GetTabInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{108}
}
func (m *GetTabInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabInfosResp.Unmarshal(m, b)
}
func (m *GetTabInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabInfosResp.Marshal(b, m, deterministic)
}
func (dst *GetTabInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabInfosResp.Merge(dst, src)
}
func (m *GetTabInfosResp) XXX_Size() int {
	return xxx_messageInfo_GetTabInfosResp.Size(m)
}
func (m *GetTabInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabInfosResp proto.InternalMessageInfo

func (m *GetTabInfosResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetTabInfosResp) GetTabInfos() map[uint32]*topic_channel.Tab {
	if m != nil {
		return m.TabInfos
	}
	return nil
}

// 推荐游戏配置信息
type GameTabConfig struct {
	TabId                uint32    `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string    `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	BackgroundImg        string    `protobuf:"bytes,3,opt,name=background_img,json=backgroundImg,proto3" json:"background_img,omitempty"`
	IconBeginColor       string    `protobuf:"bytes,4,opt,name=icon_begin_color,json=iconBeginColor,proto3" json:"icon_begin_color,omitempty"`
	IconEndColor         string    `protobuf:"bytes,5,opt,name=icon_end_color,json=iconEndColor,proto3" json:"icon_end_color,omitempty"`
	IconText             string    `protobuf:"bytes,6,opt,name=icon_text,json=iconText,proto3" json:"icon_text,omitempty"`
	ShowTeamText         string    `protobuf:"bytes,7,opt,name=show_team_text,json=showTeamText,proto3" json:"show_team_text,omitempty"`
	Accounts             []string  `protobuf:"bytes,8,rep,name=accounts,proto3" json:"accounts,omitempty"`
	Buttons              []*Button `protobuf:"bytes,9,rep,name=buttons,proto3" json:"buttons,omitempty"`
	MaskLayer            string    `protobuf:"bytes,10,opt,name=mask_layer,json=maskLayer,proto3" json:"mask_layer,omitempty"`
	CardsImgUrl          string    `protobuf:"bytes,11,opt,name=cards_img_url,json=cardsImgUrl,proto3" json:"cards_img_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GameTabConfig) Reset()         { *m = GameTabConfig{} }
func (m *GameTabConfig) String() string { return proto.CompactTextString(m) }
func (*GameTabConfig) ProtoMessage()    {}
func (*GameTabConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{109}
}
func (m *GameTabConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTabConfig.Unmarshal(m, b)
}
func (m *GameTabConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTabConfig.Marshal(b, m, deterministic)
}
func (dst *GameTabConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTabConfig.Merge(dst, src)
}
func (m *GameTabConfig) XXX_Size() int {
	return xxx_messageInfo_GameTabConfig.Size(m)
}
func (m *GameTabConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTabConfig.DiscardUnknown(m)
}

var xxx_messageInfo_GameTabConfig proto.InternalMessageInfo

func (m *GameTabConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameTabConfig) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *GameTabConfig) GetBackgroundImg() string {
	if m != nil {
		return m.BackgroundImg
	}
	return ""
}

func (m *GameTabConfig) GetIconBeginColor() string {
	if m != nil {
		return m.IconBeginColor
	}
	return ""
}

func (m *GameTabConfig) GetIconEndColor() string {
	if m != nil {
		return m.IconEndColor
	}
	return ""
}

func (m *GameTabConfig) GetIconText() string {
	if m != nil {
		return m.IconText
	}
	return ""
}

func (m *GameTabConfig) GetShowTeamText() string {
	if m != nil {
		return m.ShowTeamText
	}
	return ""
}

func (m *GameTabConfig) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *GameTabConfig) GetButtons() []*Button {
	if m != nil {
		return m.Buttons
	}
	return nil
}

func (m *GameTabConfig) GetMaskLayer() string {
	if m != nil {
		return m.MaskLayer
	}
	return ""
}

func (m *GameTabConfig) GetCardsImgUrl() string {
	if m != nil {
		return m.CardsImgUrl
	}
	return ""
}

type Button struct {
	ButtonType           uint32   `protobuf:"varint,1,opt,name=button_type,json=buttonType,proto3" json:"button_type,omitempty"`
	ButtonText           string   `protobuf:"bytes,2,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Button) Reset()         { *m = Button{} }
func (m *Button) String() string { return proto.CompactTextString(m) }
func (*Button) ProtoMessage()    {}
func (*Button) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{110}
}
func (m *Button) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Button.Unmarshal(m, b)
}
func (m *Button) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Button.Marshal(b, m, deterministic)
}
func (dst *Button) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Button.Merge(dst, src)
}
func (m *Button) XXX_Size() int {
	return xxx_messageInfo_Button.Size(m)
}
func (m *Button) XXX_DiscardUnknown() {
	xxx_messageInfo_Button.DiscardUnknown(m)
}

var xxx_messageInfo_Button proto.InternalMessageInfo

func (m *Button) GetButtonType() uint32 {
	if m != nil {
		return m.ButtonType
	}
	return 0
}

func (m *Button) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

type RecommendConfig struct {
	ConfigType           uint32         `protobuf:"varint,1,opt,name=config_type,json=configType,proto3" json:"config_type,omitempty"`
	GameTabItem          *GameTabConfig `protobuf:"bytes,2,opt,name=game_tab_item,json=gameTabItem,proto3" json:"game_tab_item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RecommendConfig) Reset()         { *m = RecommendConfig{} }
func (m *RecommendConfig) String() string { return proto.CompactTextString(m) }
func (*RecommendConfig) ProtoMessage()    {}
func (*RecommendConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{111}
}
func (m *RecommendConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendConfig.Unmarshal(m, b)
}
func (m *RecommendConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendConfig.Marshal(b, m, deterministic)
}
func (dst *RecommendConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendConfig.Merge(dst, src)
}
func (m *RecommendConfig) XXX_Size() int {
	return xxx_messageInfo_RecommendConfig.Size(m)
}
func (m *RecommendConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendConfig.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendConfig proto.InternalMessageInfo

func (m *RecommendConfig) GetConfigType() uint32 {
	if m != nil {
		return m.ConfigType
	}
	return 0
}

func (m *RecommendConfig) GetGameTabItem() *GameTabConfig {
	if m != nil {
		return m.GameTabItem
	}
	return nil
}

// 获取官方推荐游戏
type GetRecommendGamesReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRecommendGamesReq) Reset()         { *m = GetRecommendGamesReq{} }
func (m *GetRecommendGamesReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendGamesReq) ProtoMessage()    {}
func (*GetRecommendGamesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{112}
}
func (m *GetRecommendGamesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendGamesReq.Unmarshal(m, b)
}
func (m *GetRecommendGamesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendGamesReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendGamesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendGamesReq.Merge(dst, src)
}
func (m *GetRecommendGamesReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendGamesReq.Size(m)
}
func (m *GetRecommendGamesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendGamesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendGamesReq proto.InternalMessageInfo

func (m *GetRecommendGamesReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetRecommendGamesResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Configs              []*RecommendConfig `protobuf:"bytes,2,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetRecommendGamesResp) Reset()         { *m = GetRecommendGamesResp{} }
func (m *GetRecommendGamesResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendGamesResp) ProtoMessage()    {}
func (*GetRecommendGamesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{113}
}
func (m *GetRecommendGamesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendGamesResp.Unmarshal(m, b)
}
func (m *GetRecommendGamesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendGamesResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendGamesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendGamesResp.Merge(dst, src)
}
func (m *GetRecommendGamesResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendGamesResp.Size(m)
}
func (m *GetRecommendGamesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendGamesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendGamesResp proto.InternalMessageInfo

func (m *GetRecommendGamesResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecommendGamesResp) GetConfigs() []*RecommendConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

// 更新标签缓存
type RefreshGameLabelReq struct {
	BaseReq              *app.BaseReq               `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32                     `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Labels               []*topic_channel.GameLabel `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"`
	FilterId             string                     `protobuf:"bytes,4,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *RefreshGameLabelReq) Reset()         { *m = RefreshGameLabelReq{} }
func (m *RefreshGameLabelReq) String() string { return proto.CompactTextString(m) }
func (*RefreshGameLabelReq) ProtoMessage()    {}
func (*RefreshGameLabelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{114}
}
func (m *RefreshGameLabelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshGameLabelReq.Unmarshal(m, b)
}
func (m *RefreshGameLabelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshGameLabelReq.Marshal(b, m, deterministic)
}
func (dst *RefreshGameLabelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshGameLabelReq.Merge(dst, src)
}
func (m *RefreshGameLabelReq) XXX_Size() int {
	return xxx_messageInfo_RefreshGameLabelReq.Size(m)
}
func (m *RefreshGameLabelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshGameLabelReq.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshGameLabelReq proto.InternalMessageInfo

func (m *RefreshGameLabelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *RefreshGameLabelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *RefreshGameLabelReq) GetLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *RefreshGameLabelReq) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

type RefreshGameLabelResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Labels               []*topic_channel.GameLabel `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *RefreshGameLabelResp) Reset()         { *m = RefreshGameLabelResp{} }
func (m *RefreshGameLabelResp) String() string { return proto.CompactTextString(m) }
func (*RefreshGameLabelResp) ProtoMessage()    {}
func (*RefreshGameLabelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{115}
}
func (m *RefreshGameLabelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshGameLabelResp.Unmarshal(m, b)
}
func (m *RefreshGameLabelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshGameLabelResp.Marshal(b, m, deterministic)
}
func (dst *RefreshGameLabelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshGameLabelResp.Merge(dst, src)
}
func (m *RefreshGameLabelResp) XXX_Size() int {
	return xxx_messageInfo_RefreshGameLabelResp.Size(m)
}
func (m *RefreshGameLabelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshGameLabelResp.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshGameLabelResp proto.InternalMessageInfo

func (m *RefreshGameLabelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *RefreshGameLabelResp) GetLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

type GetSupportTabListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSupportTabListReq) Reset()         { *m = GetSupportTabListReq{} }
func (m *GetSupportTabListReq) String() string { return proto.CompactTextString(m) }
func (*GetSupportTabListReq) ProtoMessage()    {}
func (*GetSupportTabListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{116}
}
func (m *GetSupportTabListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSupportTabListReq.Unmarshal(m, b)
}
func (m *GetSupportTabListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSupportTabListReq.Marshal(b, m, deterministic)
}
func (dst *GetSupportTabListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSupportTabListReq.Merge(dst, src)
}
func (m *GetSupportTabListReq) XXX_Size() int {
	return xxx_messageInfo_GetSupportTabListReq.Size(m)
}
func (m *GetSupportTabListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSupportTabListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSupportTabListReq proto.InternalMessageInfo

func (m *GetSupportTabListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetSupportTabListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TabIds               []uint32      `protobuf:"varint,2,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSupportTabListResp) Reset()         { *m = GetSupportTabListResp{} }
func (m *GetSupportTabListResp) String() string { return proto.CompactTextString(m) }
func (*GetSupportTabListResp) ProtoMessage()    {}
func (*GetSupportTabListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{117}
}
func (m *GetSupportTabListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSupportTabListResp.Unmarshal(m, b)
}
func (m *GetSupportTabListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSupportTabListResp.Marshal(b, m, deterministic)
}
func (dst *GetSupportTabListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSupportTabListResp.Merge(dst, src)
}
func (m *GetSupportTabListResp) XXX_Size() int {
	return xxx_messageInfo_GetSupportTabListResp.Size(m)
}
func (m *GetSupportTabListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSupportTabListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSupportTabListResp proto.InternalMessageInfo

func (m *GetSupportTabListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSupportTabListResp) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type GetChannelMicVolSetReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelMicVolSetReq) Reset()         { *m = GetChannelMicVolSetReq{} }
func (m *GetChannelMicVolSetReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMicVolSetReq) ProtoMessage()    {}
func (*GetChannelMicVolSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{118}
}
func (m *GetChannelMicVolSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMicVolSetReq.Unmarshal(m, b)
}
func (m *GetChannelMicVolSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMicVolSetReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMicVolSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMicVolSetReq.Merge(dst, src)
}
func (m *GetChannelMicVolSetReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMicVolSetReq.Size(m)
}
func (m *GetChannelMicVolSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMicVolSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMicVolSetReq proto.InternalMessageInfo

func (m *GetChannelMicVolSetReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelMicVolSetReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type ChannelMicVolSetItem struct {
	MicId                uint32   `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	MaxVol               uint32   `protobuf:"varint,3,opt,name=max_vol,json=maxVol,proto3" json:"max_vol,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMicVolSetItem) Reset()         { *m = ChannelMicVolSetItem{} }
func (m *ChannelMicVolSetItem) String() string { return proto.CompactTextString(m) }
func (*ChannelMicVolSetItem) ProtoMessage()    {}
func (*ChannelMicVolSetItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{119}
}
func (m *ChannelMicVolSetItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMicVolSetItem.Unmarshal(m, b)
}
func (m *ChannelMicVolSetItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMicVolSetItem.Marshal(b, m, deterministic)
}
func (dst *ChannelMicVolSetItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMicVolSetItem.Merge(dst, src)
}
func (m *ChannelMicVolSetItem) XXX_Size() int {
	return xxx_messageInfo_ChannelMicVolSetItem.Size(m)
}
func (m *ChannelMicVolSetItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMicVolSetItem.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMicVolSetItem proto.InternalMessageInfo

func (m *ChannelMicVolSetItem) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *ChannelMicVolSetItem) GetMaxVol() uint32 {
	if m != nil {
		return m.MaxVol
	}
	return 0
}

type GetChannelMicVolSetResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MicVolSet            []*ChannelMicVolSetItem `protobuf:"bytes,2,rep,name=mic_vol_set,json=micVolSet,proto3" json:"mic_vol_set,omitempty"`
	SyncZeroMicSet       bool                    `protobuf:"varint,3,opt,name=sync_zero_mic_set,json=syncZeroMicSet,proto3" json:"sync_zero_mic_set,omitempty"`
	Cid                  uint32                  `protobuf:"varint,4,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetChannelMicVolSetResp) Reset()         { *m = GetChannelMicVolSetResp{} }
func (m *GetChannelMicVolSetResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMicVolSetResp) ProtoMessage()    {}
func (*GetChannelMicVolSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{120}
}
func (m *GetChannelMicVolSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMicVolSetResp.Unmarshal(m, b)
}
func (m *GetChannelMicVolSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMicVolSetResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMicVolSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMicVolSetResp.Merge(dst, src)
}
func (m *GetChannelMicVolSetResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMicVolSetResp.Size(m)
}
func (m *GetChannelMicVolSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMicVolSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMicVolSetResp proto.InternalMessageInfo

func (m *GetChannelMicVolSetResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelMicVolSetResp) GetMicVolSet() []*ChannelMicVolSetItem {
	if m != nil {
		return m.MicVolSet
	}
	return nil
}

func (m *GetChannelMicVolSetResp) GetSyncZeroMicSet() bool {
	if m != nil {
		return m.SyncZeroMicSet
	}
	return false
}

func (m *GetChannelMicVolSetResp) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type SetChannelMicVolReq struct {
	BaseReq              *app.BaseReq            `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	MicVolSet            []*ChannelMicVolSetItem `protobuf:"bytes,2,rep,name=mic_vol_set,json=micVolSet,proto3" json:"mic_vol_set,omitempty"`
	SyncZeroMicSet       bool                    `protobuf:"varint,3,opt,name=sync_zero_mic_set,json=syncZeroMicSet,proto3" json:"sync_zero_mic_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SetChannelMicVolReq) Reset()         { *m = SetChannelMicVolReq{} }
func (m *SetChannelMicVolReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicVolReq) ProtoMessage()    {}
func (*SetChannelMicVolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{121}
}
func (m *SetChannelMicVolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicVolReq.Unmarshal(m, b)
}
func (m *SetChannelMicVolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicVolReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicVolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicVolReq.Merge(dst, src)
}
func (m *SetChannelMicVolReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicVolReq.Size(m)
}
func (m *SetChannelMicVolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicVolReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicVolReq proto.InternalMessageInfo

func (m *SetChannelMicVolReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetChannelMicVolReq) GetMicVolSet() []*ChannelMicVolSetItem {
	if m != nil {
		return m.MicVolSet
	}
	return nil
}

func (m *SetChannelMicVolReq) GetSyncZeroMicSet() bool {
	if m != nil {
		return m.SyncZeroMicSet
	}
	return false
}

type SetChannelMicVolResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetChannelMicVolResp) Reset()         { *m = SetChannelMicVolResp{} }
func (m *SetChannelMicVolResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicVolResp) ProtoMessage()    {}
func (*SetChannelMicVolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{122}
}
func (m *SetChannelMicVolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicVolResp.Unmarshal(m, b)
}
func (m *SetChannelMicVolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicVolResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicVolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicVolResp.Merge(dst, src)
}
func (m *SetChannelMicVolResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicVolResp.Size(m)
}
func (m *SetChannelMicVolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicVolResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicVolResp proto.InternalMessageInfo

func (m *SetChannelMicVolResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ChannelMicVolSetChangePushMsg struct {
	MicVolSet            []*ChannelMicVolSetItem `protobuf:"bytes,1,rep,name=mic_vol_set,json=micVolSet,proto3" json:"mic_vol_set,omitempty"`
	SyncZeroMicSet       bool                    `protobuf:"varint,2,opt,name=sync_zero_mic_set,json=syncZeroMicSet,proto3" json:"sync_zero_mic_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ChannelMicVolSetChangePushMsg) Reset()         { *m = ChannelMicVolSetChangePushMsg{} }
func (m *ChannelMicVolSetChangePushMsg) String() string { return proto.CompactTextString(m) }
func (*ChannelMicVolSetChangePushMsg) ProtoMessage()    {}
func (*ChannelMicVolSetChangePushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{123}
}
func (m *ChannelMicVolSetChangePushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMicVolSetChangePushMsg.Unmarshal(m, b)
}
func (m *ChannelMicVolSetChangePushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMicVolSetChangePushMsg.Marshal(b, m, deterministic)
}
func (dst *ChannelMicVolSetChangePushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMicVolSetChangePushMsg.Merge(dst, src)
}
func (m *ChannelMicVolSetChangePushMsg) XXX_Size() int {
	return xxx_messageInfo_ChannelMicVolSetChangePushMsg.Size(m)
}
func (m *ChannelMicVolSetChangePushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMicVolSetChangePushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMicVolSetChangePushMsg proto.InternalMessageInfo

func (m *ChannelMicVolSetChangePushMsg) GetMicVolSet() []*ChannelMicVolSetItem {
	if m != nil {
		return m.MicVolSet
	}
	return nil
}

func (m *ChannelMicVolSetChangePushMsg) GetSyncZeroMicSet() bool {
	if m != nil {
		return m.SyncZeroMicSet
	}
	return false
}

// 麦位用户扩展信息
type MicUserExtInfo struct {
	// ai账号信息
	AiAccount            *MicUserExtInfo_AIAccount `protobuf:"bytes,1,opt,name=ai_account,json=aiAccount,proto3" json:"ai_account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *MicUserExtInfo) Reset()         { *m = MicUserExtInfo{} }
func (m *MicUserExtInfo) String() string { return proto.CompactTextString(m) }
func (*MicUserExtInfo) ProtoMessage()    {}
func (*MicUserExtInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{124}
}
func (m *MicUserExtInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicUserExtInfo.Unmarshal(m, b)
}
func (m *MicUserExtInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicUserExtInfo.Marshal(b, m, deterministic)
}
func (dst *MicUserExtInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicUserExtInfo.Merge(dst, src)
}
func (m *MicUserExtInfo) XXX_Size() int {
	return xxx_messageInfo_MicUserExtInfo.Size(m)
}
func (m *MicUserExtInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicUserExtInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicUserExtInfo proto.InternalMessageInfo

func (m *MicUserExtInfo) GetAiAccount() *MicUserExtInfo_AIAccount {
	if m != nil {
		return m.AiAccount
	}
	return nil
}

type MicUserExtInfo_AIAccount struct {
	// 标识
	Identity string `protobuf:"bytes,1,opt,name=identity,proto3" json:"identity,omitempty"`
	// 说明
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicUserExtInfo_AIAccount) Reset()         { *m = MicUserExtInfo_AIAccount{} }
func (m *MicUserExtInfo_AIAccount) String() string { return proto.CompactTextString(m) }
func (*MicUserExtInfo_AIAccount) ProtoMessage()    {}
func (*MicUserExtInfo_AIAccount) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play__33ecab187915ce66, []int{124, 0}
}
func (m *MicUserExtInfo_AIAccount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicUserExtInfo_AIAccount.Unmarshal(m, b)
}
func (m *MicUserExtInfo_AIAccount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicUserExtInfo_AIAccount.Marshal(b, m, deterministic)
}
func (dst *MicUserExtInfo_AIAccount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicUserExtInfo_AIAccount.Merge(dst, src)
}
func (m *MicUserExtInfo_AIAccount) XXX_Size() int {
	return xxx_messageInfo_MicUserExtInfo_AIAccount.Size(m)
}
func (m *MicUserExtInfo_AIAccount) XXX_DiscardUnknown() {
	xxx_messageInfo_MicUserExtInfo_AIAccount.DiscardUnknown(m)
}

var xxx_messageInfo_MicUserExtInfo_AIAccount proto.InternalMessageInfo

func (m *MicUserExtInfo_AIAccount) GetIdentity() string {
	if m != nil {
		return m.Identity
	}
	return ""
}

func (m *MicUserExtInfo_AIAccount) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func init() {
	proto.RegisterType((*TopicChannelView)(nil), "ga.channel_play.TopicChannelView")
	proto.RegisterType((*PersonalCert)(nil), "ga.channel_play.PersonalCert")
	proto.RegisterType((*TopicChannelItem)(nil), "ga.channel_play.TopicChannelItem")
	proto.RegisterType((*TopicChannelItem_FriendInfo)(nil), "ga.channel_play.TopicChannelItem.FriendInfo")
	proto.RegisterType((*FilterBlockOption)(nil), "ga.channel_play.FilterBlockOption")
	proto.RegisterType((*ListTopicChannelReq)(nil), "ga.channel_play.ListTopicChannelReq")
	proto.RegisterType((*ListTopicChannelResp)(nil), "ga.channel_play.ListTopicChannelResp")
	proto.RegisterType((*ListTopicChannelResp_DataReport)(nil), "ga.channel_play.ListTopicChannelResp.DataReport")
	proto.RegisterType((*GetSecondaryFilterReq)(nil), "ga.channel_play.GetSecondaryFilterReq")
	proto.RegisterType((*GetSecondaryFilterResp)(nil), "ga.channel_play.GetSecondaryFilterResp")
	proto.RegisterType((*ElemBindBlockInfo)(nil), "ga.channel_play.ElemBindBlockInfo")
	proto.RegisterType((*DisplayBlockInfo)(nil), "ga.channel_play.DisplayBlockInfo")
	proto.RegisterType((*BusinessFilterItem)(nil), "ga.channel_play.BusinessFilterItem")
	proto.RegisterType((*BusinessFilterElem)(nil), "ga.channel_play.BusinessFilterElem")
	proto.RegisterType((*GetSecondaryFilterByCategoryReq)(nil), "ga.channel_play.GetSecondaryFilterByCategoryReq")
	proto.RegisterType((*GetSecondaryFilterByCategoryResp)(nil), "ga.channel_play.GetSecondaryFilterByCategoryResp")
	proto.RegisterType((*DefaultRoomNameConfig)(nil), "ga.channel_play.DefaultRoomNameConfig")
	proto.RegisterType((*GetDefaultRoomNameListReq)(nil), "ga.channel_play.GetDefaultRoomNameListReq")
	proto.RegisterType((*GetDefaultRoomNameListResp)(nil), "ga.channel_play.GetDefaultRoomNameListResp")
	proto.RegisterType((*PublishGangupChannelReq)(nil), "ga.channel_play.PublishGangupChannelReq")
	proto.RegisterType((*BlockButtonOpt)(nil), "ga.channel_play.BlockButtonOpt")
	proto.RegisterType((*PublishGangupChannelResp)(nil), "ga.channel_play.PublishGangupChannelResp")
	proto.RegisterType((*CancelGangupChannelPublishReq)(nil), "ga.channel_play.CancelGangupChannelPublishReq")
	proto.RegisterType((*CancelGangupChannelPublishResp)(nil), "ga.channel_play.CancelGangupChannelPublishResp")
	proto.RegisterType((*HomePageHeadConfigReq)(nil), "ga.channel_play.HomePageHeadConfigReq")
	proto.RegisterType((*HomePageHeadConfigResp)(nil), "ga.channel_play.HomePageHeadConfigResp")
	proto.RegisterType((*HomePageHeadConfigItems)(nil), "ga.channel_play.HomePageHeadConfigItems")
	proto.RegisterType((*HomePageHeadConfigItem)(nil), "ga.channel_play.HomePageHeadConfigItem")
	proto.RegisterType((*HomePageHeadMultipleBannersConfig)(nil), "ga.channel_play.HomePageHeadMultipleBannersConfig")
	proto.RegisterType((*Vap)(nil), "ga.channel_play.Vap")
	proto.RegisterType((*GameOverview)(nil), "ga.channel_play.GameOverview")
	proto.RegisterType((*TagConfigInfo)(nil), "ga.channel_play.TagConfigInfo")
	proto.RegisterType((*RichTagInfo)(nil), "ga.channel_play.RichTagInfo")
	proto.RegisterType((*RichTagConfigInfo)(nil), "ga.channel_play.RichTagConfigInfo")
	proto.RegisterType((*HomePageHeadCompetitionConfig)(nil), "ga.channel_play.HomePageHeadCompetitionConfig")
	proto.RegisterType((*HomePageHeadCompetitionList)(nil), "ga.channel_play.HomePageHeadCompetitionList")
	proto.RegisterType((*HomePageHeadFlashChatConfig)(nil), "ga.channel_play.HomePageHeadFlashChatConfig")
	proto.RegisterType((*HotMiniGame)(nil), "ga.channel_play.HotMiniGame")
	proto.RegisterType((*GetHotMiniGamesReq)(nil), "ga.channel_play.GetHotMiniGamesReq")
	proto.RegisterType((*GetHotMiniGamesResp)(nil), "ga.channel_play.GetHotMiniGamesResp")
	proto.RegisterType((*QuickMiniGame)(nil), "ga.channel_play.QuickMiniGame")
	proto.RegisterType((*GetQuickMiniGamesReq)(nil), "ga.channel_play.GetQuickMiniGamesReq")
	proto.RegisterType((*GetQuickMiniGamesResp)(nil), "ga.channel_play.GetQuickMiniGamesResp")
	proto.RegisterType((*GameInsertFlowConfigReq)(nil), "ga.channel_play.GameInsertFlowConfigReq")
	proto.RegisterType((*GameInsertFlowConfigResp)(nil), "ga.channel_play.GameInsertFlowConfigResp")
	proto.RegisterType((*QuickMatchConfig)(nil), "ga.channel_play.QuickMatchConfig")
	proto.RegisterType((*CreateRoomConfig)(nil), "ga.channel_play.CreateRoomConfig")
	proto.RegisterType((*GetHomePageGuideReq)(nil), "ga.channel_play.GetHomePageGuideReq")
	proto.RegisterType((*GuideContent)(nil), "ga.channel_play.GuideContent")
	proto.RegisterType((*HomePageSubmodule)(nil), "ga.channel_play.HomePageSubmodule")
	proto.RegisterType((*GetHomePageGuideResp)(nil), "ga.channel_play.GetHomePageGuideResp")
	proto.RegisterType((*GetMoreTabConfigReq)(nil), "ga.channel_play.GetMoreTabConfigReq")
	proto.RegisterType((*GetMoreTabConfigResp)(nil), "ga.channel_play.GetMoreTabConfigResp")
	proto.RegisterType((*MoreTabItem)(nil), "ga.channel_play.MoreTabItem")
	proto.RegisterType((*CategoryInfo)(nil), "ga.channel_play.CategoryInfo")
	proto.RegisterType((*CategoryInfo_CategoryInfoItem)(nil), "ga.channel_play.CategoryInfo.CategoryInfoItem")
	proto.RegisterType((*MoreTabMusicInfo)(nil), "ga.channel_play.MoreTabMusicInfo")
	proto.RegisterType((*MixInfo)(nil), "ga.channel_play.MixInfo")
	proto.RegisterType((*MixInfo_MixCategoryItem)(nil), "ga.channel_play.MixInfo.MixCategoryItem")
	proto.RegisterType((*PlayQuestion)(nil), "ga.channel_play.PlayQuestion")
	proto.RegisterType((*GetPlayQuestionsReq)(nil), "ga.channel_play.GetPlayQuestionsReq")
	proto.RegisterType((*GetPlayQuestionsResp)(nil), "ga.channel_play.GetPlayQuestionsResp")
	proto.RegisterType((*CommonBusinessFlag)(nil), "ga.channel_play.CommonBusinessFlag")
	proto.RegisterType((*FilterItem)(nil), "ga.channel_play.FilterItem")
	proto.RegisterType((*GameLabelFilter)(nil), "ga.channel_play.GameLabelFilter")
	proto.RegisterType((*TabItem)(nil), "ga.channel_play.TabItem")
	proto.RegisterType((*ClassifyLabelList)(nil), "ga.channel_play.ClassifyLabelList")
	proto.RegisterType((*NewMusicFilterItem)(nil), "ga.channel_play.NewMusicFilterItem")
	proto.RegisterType((*CategoryItem)(nil), "ga.channel_play.CategoryItem")
	proto.RegisterType((*GetFilterItemByEntranceReq)(nil), "ga.channel_play.GetFilterItemByEntranceReq")
	proto.RegisterMapType((map[uint32]*topic_channel.BrowseLabel)(nil), "ga.channel_play.GetFilterItemByEntranceReq.BrowseLabelsMapEntry")
	proto.RegisterType((*GetFilterItemByEntranceResp)(nil), "ga.channel_play.GetFilterItemByEntranceResp")
	proto.RegisterType((*SetDIYFilterByEntranceReq)(nil), "ga.channel_play.SetDIYFilterByEntranceReq")
	proto.RegisterType((*SetDIYFilterByEntranceResp)(nil), "ga.channel_play.SetDIYFilterByEntranceResp")
	proto.RegisterType((*GetDIYFilterByEntranceReq)(nil), "ga.channel_play.GetDIYFilterByEntranceReq")
	proto.RegisterType((*GetDIYFilterByEntranceResp)(nil), "ga.channel_play.GetDIYFilterByEntranceResp")
	proto.RegisterType((*DIYItem)(nil), "ga.channel_play.DIYItem")
	proto.RegisterType((*GetNegativeFeedBackInRoomReq)(nil), "ga.channel_play.GetNegativeFeedBackInRoomReq")
	proto.RegisterType((*GetNegativeFeedBackInRoomResp)(nil), "ga.channel_play.GetNegativeFeedBackInRoomResp")
	proto.RegisterType((*ReportNegativeFeedBackInRoomReq)(nil), "ga.channel_play.ReportNegativeFeedBackInRoomReq")
	proto.RegisterType((*ReportNegativeFeedBackInRoomResp)(nil), "ga.channel_play.ReportNegativeFeedBackInRoomResp")
	proto.RegisterType((*GetPublishOptionGuideReq)(nil), "ga.channel_play.GetPublishOptionGuideReq")
	proto.RegisterType((*GetPublishOptionGuideResp)(nil), "ga.channel_play.GetPublishOptionGuideResp")
	proto.RegisterType((*PublishOptionElem)(nil), "ga.channel_play.PublishOptionElem")
	proto.RegisterType((*RecommendationElem)(nil), "ga.channel_play.RecommendationElem")
	proto.RegisterType((*NewQuickMatchConfig)(nil), "ga.channel_play.NewQuickMatchConfig")
	proto.RegisterType((*GetNewQuickMatchConfigReq)(nil), "ga.channel_play.GetNewQuickMatchConfigReq")
	proto.RegisterType((*GetNewQuickMatchConfigResp)(nil), "ga.channel_play.GetNewQuickMatchConfigResp")
	proto.RegisterType((*GetTopicChannelCfgInfoReq)(nil), "ga.channel_play.GetTopicChannelCfgInfoReq")
	proto.RegisterType((*GetTopicChannelCfgInfoResp)(nil), "ga.channel_play.GetTopicChannelCfgInfoResp")
	proto.RegisterType((*FastPcCfgTabInfo)(nil), "ga.channel_play.FastPcCfgTabInfo")
	proto.RegisterType((*FastPcCfgTabInfo_FastPcPublishDesc)(nil), "ga.channel_play.FastPcCfgTabInfo.FastPcPublishDesc")
	proto.RegisterType((*SetUgcChannelPlayModeReq)(nil), "ga.channel_play.SetUgcChannelPlayModeReq")
	proto.RegisterType((*SetUgcChannelPlayModeResp)(nil), "ga.channel_play.SetUgcChannelPlayModeResp")
	proto.RegisterType((*TypingStatusBroadcastReq)(nil), "ga.channel_play.TypingStatusBroadcastReq")
	proto.RegisterType((*TypingStatusBroadcastResp)(nil), "ga.channel_play.TypingStatusBroadcastResp")
	proto.RegisterType((*GetChannelPlayModeGuideReq)(nil), "ga.channel_play.GetChannelPlayModeGuideReq")
	proto.RegisterType((*GetChannelPlayModeGuideResp)(nil), "ga.channel_play.GetChannelPlayModeGuideResp")
	proto.RegisterType((*TypeStatusPushMsg)(nil), "ga.channel_play.TypeStatusPushMsg")
	proto.RegisterType((*UgcChannelPlayModeChangeMsg)(nil), "ga.channel_play.UgcChannelPlayModeChangeMsg")
	proto.RegisterType((*ReportDailyTaskReq)(nil), "ga.channel_play.ReportDailyTaskReq")
	proto.RegisterType((*ReportDailyTaskResp)(nil), "ga.channel_play.ReportDailyTaskResp")
	proto.RegisterType((*GetCacheReq)(nil), "ga.channel_play.GetCacheReq")
	proto.RegisterType((*CacheData)(nil), "ga.channel_play.CacheData")
	proto.RegisterType((*CacheBlockData)(nil), "ga.channel_play.CacheBlockData")
	proto.RegisterType((*CacheTabData)(nil), "ga.channel_play.CacheTabData")
	proto.RegisterType((*GetCacheResp)(nil), "ga.channel_play.GetCacheResp")
	proto.RegisterMapType((map[uint32]*CacheData)(nil), "ga.channel_play.GetCacheResp.CategoryTabsMapEntry")
	proto.RegisterMapType((map[uint32]*CacheData)(nil), "ga.channel_play.GetCacheResp.GameCardTabsMapEntry")
	proto.RegisterType((*HomePageHeadConfigEnterCheckReq)(nil), "ga.channel_play.HomePageHeadConfigEnterCheckReq")
	proto.RegisterType((*HomePageHeadConfigEnterCheckResp)(nil), "ga.channel_play.HomePageHeadConfigEnterCheckResp")
	proto.RegisterType((*GetChannelListGuideConfigsReq)(nil), "ga.channel_play.GetChannelListGuideConfigsReq")
	proto.RegisterType((*GetChannelListGuideConfigsReq_HomePageGuideReq)(nil), "ga.channel_play.GetChannelListGuideConfigsReq.HomePageGuideReq")
	proto.RegisterType((*GetChannelListGuideConfigsResp)(nil), "ga.channel_play.GetChannelListGuideConfigsResp")
	proto.RegisterType((*GetChannelListGuideConfigsResp_GuideConfig)(nil), "ga.channel_play.GetChannelListGuideConfigsResp.GuideConfig")
	proto.RegisterType((*PalCardGuideConfig)(nil), "ga.channel_play.PalCardGuideConfig")
	proto.RegisterType((*PalCardGuideConfig_GamePalCardGuideInfo)(nil), "ga.channel_play.PalCardGuideConfig.GamePalCardGuideInfo")
	proto.RegisterType((*GameContentGuideConfig)(nil), "ga.channel_play.GameContentGuideConfig")
	proto.RegisterType((*GameContentGuideConfig_GameFeedGuideInfo)(nil), "ga.channel_play.GameContentGuideConfig.GameFeedGuideInfo")
	proto.RegisterType((*GetTabInfosReq)(nil), "ga.channel_play.GetTabInfosReq")
	proto.RegisterType((*GetTabInfosResp)(nil), "ga.channel_play.GetTabInfosResp")
	proto.RegisterMapType((map[uint32]*topic_channel.Tab)(nil), "ga.channel_play.GetTabInfosResp.TabInfosEntry")
	proto.RegisterType((*GameTabConfig)(nil), "ga.channel_play.GameTabConfig")
	proto.RegisterType((*Button)(nil), "ga.channel_play.Button")
	proto.RegisterType((*RecommendConfig)(nil), "ga.channel_play.RecommendConfig")
	proto.RegisterType((*GetRecommendGamesReq)(nil), "ga.channel_play.GetRecommendGamesReq")
	proto.RegisterType((*GetRecommendGamesResp)(nil), "ga.channel_play.GetRecommendGamesResp")
	proto.RegisterType((*RefreshGameLabelReq)(nil), "ga.channel_play.RefreshGameLabelReq")
	proto.RegisterType((*RefreshGameLabelResp)(nil), "ga.channel_play.RefreshGameLabelResp")
	proto.RegisterType((*GetSupportTabListReq)(nil), "ga.channel_play.GetSupportTabListReq")
	proto.RegisterType((*GetSupportTabListResp)(nil), "ga.channel_play.GetSupportTabListResp")
	proto.RegisterType((*GetChannelMicVolSetReq)(nil), "ga.channel_play.GetChannelMicVolSetReq")
	proto.RegisterType((*ChannelMicVolSetItem)(nil), "ga.channel_play.ChannelMicVolSetItem")
	proto.RegisterType((*GetChannelMicVolSetResp)(nil), "ga.channel_play.GetChannelMicVolSetResp")
	proto.RegisterType((*SetChannelMicVolReq)(nil), "ga.channel_play.SetChannelMicVolReq")
	proto.RegisterType((*SetChannelMicVolResp)(nil), "ga.channel_play.SetChannelMicVolResp")
	proto.RegisterType((*ChannelMicVolSetChangePushMsg)(nil), "ga.channel_play.ChannelMicVolSetChangePushMsg")
	proto.RegisterType((*MicUserExtInfo)(nil), "ga.channel_play.MicUserExtInfo")
	proto.RegisterType((*MicUserExtInfo_AIAccount)(nil), "ga.channel_play.MicUserExtInfo.AIAccount")
	proto.RegisterEnum("ga.channel_play.RCMDLabelEnum", RCMDLabelEnum_name, RCMDLabelEnum_value)
	proto.RegisterEnum("ga.channel_play.ChannelLabel", ChannelLabel_name, ChannelLabel_value)
	proto.RegisterEnum("ga.channel_play.RcmdSex", RcmdSex_name, RcmdSex_value)
	proto.RegisterEnum("ga.channel_play.ChannelListEnterSource", ChannelListEnterSource_name, ChannelListEnterSource_value)
	proto.RegisterEnum("ga.channel_play.FilterType", FilterType_name, FilterType_value)
	proto.RegisterEnum("ga.channel_play.ChannelListStyleType", ChannelListStyleType_name, ChannelListStyleType_value)
	proto.RegisterEnum("ga.channel_play.HomePageHeadConfigReqType", HomePageHeadConfigReqType_name, HomePageHeadConfigReqType_value)
	proto.RegisterEnum("ga.channel_play.HomePageHeadConfigEnum", HomePageHeadConfigEnum_name, HomePageHeadConfigEnum_value)
	proto.RegisterEnum("ga.channel_play.TagConfigType", TagConfigType_name, TagConfigType_value)
	proto.RegisterEnum("ga.channel_play.ButtonEffect", ButtonEffect_name, ButtonEffect_value)
	proto.RegisterEnum("ga.channel_play.HomePageGuideStyle", HomePageGuideStyle_name, HomePageGuideStyle_value)
	proto.RegisterEnum("ga.channel_play.FilterItemType", FilterItemType_name, FilterItemType_value)
	proto.RegisterEnum("ga.channel_play.GameZoneTabType", GameZoneTabType_name, GameZoneTabType_value)
	proto.RegisterEnum("ga.channel_play.FilterEntrance", FilterEntrance_name, FilterEntrance_value)
	proto.RegisterEnum("ga.channel_play.TabItemVersion", TabItemVersion_name, TabItemVersion_value)
	proto.RegisterEnum("ga.channel_play.FeedBackTypeInRoom", FeedBackTypeInRoom_name, FeedBackTypeInRoom_value)
	proto.RegisterEnum("ga.channel_play.GamePalEntranceStatus", GamePalEntranceStatus_name, GamePalEntranceStatus_value)
	proto.RegisterEnum("ga.channel_play.UgcChannelPlayMode", UgcChannelPlayMode_name, UgcChannelPlayMode_value)
	proto.RegisterEnum("ga.channel_play.DailyTaskType", DailyTaskType_name, DailyTaskType_value)
	proto.RegisterEnum("ga.channel_play.ButtonType", ButtonType_name, ButtonType_value)
	proto.RegisterEnum("ga.channel_play.CfgType", CfgType_name, CfgType_value)
	proto.RegisterEnum("ga.channel_play.ListTopicChannelReq_FilterModel", ListTopicChannelReq_FilterModel_name, ListTopicChannelReq_FilterModel_value)
	proto.RegisterEnum("ga.channel_play.GetSecondaryFilterReq_Mode", GetSecondaryFilterReq_Mode_name, GetSecondaryFilterReq_Mode_value)
	proto.RegisterEnum("ga.channel_play.GetSecondaryFilterReq_Source", GetSecondaryFilterReq_Source_name, GetSecondaryFilterReq_Source_value)
	proto.RegisterEnum("ga.channel_play.GetSecondaryFilterResp_TabType", GetSecondaryFilterResp_TabType_name, GetSecondaryFilterResp_TabType_value)
	proto.RegisterEnum("ga.channel_play.GetSecondaryFilterResp_MatchType", GetSecondaryFilterResp_MatchType_name, GetSecondaryFilterResp_MatchType_value)
	proto.RegisterEnum("ga.channel_play.BusinessFilterItem_Mode", BusinessFilterItem_Mode_name, BusinessFilterItem_Mode_value)
	proto.RegisterEnum("ga.channel_play.BusinessFilterElem_BusinessFilterMode", BusinessFilterElem_BusinessFilterMode_name, BusinessFilterElem_BusinessFilterMode_value)
	proto.RegisterEnum("ga.channel_play.GetDefaultRoomNameListReq_GetMode", GetDefaultRoomNameListReq_GetMode_name, GetDefaultRoomNameListReq_GetMode_value)
	proto.RegisterEnum("ga.channel_play.GetDefaultRoomNameListReq_ReqSource", GetDefaultRoomNameListReq_ReqSource_name, GetDefaultRoomNameListReq_ReqSource_value)
	proto.RegisterEnum("ga.channel_play.GetQuickMiniGamesReq_Source", GetQuickMiniGamesReq_Source_name, GetQuickMiniGamesReq_Source_value)
	proto.RegisterEnum("ga.channel_play.GameInsertFlowConfigResp_ConfigType", GameInsertFlowConfigResp_ConfigType_name, GameInsertFlowConfigResp_ConfigType_value)
	proto.RegisterEnum("ga.channel_play.GetPlayQuestionsReq_Source", GetPlayQuestionsReq_Source_name, GetPlayQuestionsReq_Source_value)
	proto.RegisterEnum("ga.channel_play.ReportNegativeFeedBackInRoomResp_HitType", ReportNegativeFeedBackInRoomResp_HitType_name, ReportNegativeFeedBackInRoomResp_HitType_value)
	proto.RegisterEnum("ga.channel_play.RecommendationElem_Type", RecommendationElem_Type_name, RecommendationElem_Type_value)
	proto.RegisterEnum("ga.channel_play.GetTopicChannelCfgInfoResp_TabType", GetTopicChannelCfgInfoResp_TabType_name, GetTopicChannelCfgInfoResp_TabType_value)
}

func init() {
	proto.RegisterFile("channel_play/channel-play_.proto", fileDescriptor_channel_play__33ecab187915ce66)
}

var fileDescriptor_channel_play__33ecab187915ce66 = []byte{
	// 9789 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x7d, 0x6d, 0x8c, 0x23, 0x49,
	0x96, 0x50, 0xa5, 0x5d, 0x1f, 0xf6, 0xf3, 0x47, 0x65, 0x45, 0x55, 0x77, 0xbb, 0xab, 0xa6, 0xa7,
	0x7b, 0x72, 0x76, 0x66, 0x7a, 0x6a, 0x77, 0xaa, 0x77, 0x7a, 0x76, 0x66, 0xbf, 0x66, 0x67, 0xc7,
	0xe5, 0x72, 0x55, 0x79, 0xba, 0xec, 0xaa, 0x4e, 0xbb, 0xba, 0xa7, 0x67, 0x8f, 0x4d, 0xa5, 0x33,
	0xc3, 0xae, 0xa4, 0xec, 0x4c, 0x8f, 0x33, 0xdd, 0xdd, 0xb5, 0xba, 0x3b, 0x09, 0x9d, 0xee, 0x0e,
	0x38, 0x21, 0x38, 0x40, 0x5a, 0x40, 0x42, 0x8b, 0x04, 0xe2, 0xee, 0x17, 0x48, 0xc0, 0x81, 0x10,
	0xfc, 0x42, 0x42, 0x5a, 0x21, 0x84, 0x10, 0x3f, 0x90, 0xd0, 0x0a, 0x84, 0xf6, 0x17, 0x7f, 0x90,
	0xf8, 0xc3, 0x21, 0x40, 0x02, 0x14, 0x2f, 0x22, 0xd2, 0x99, 0x76, 0xba, 0xba, 0xdc, 0xd3, 0x8b,
	0xee, 0x97, 0x1d, 0x2f, 0x22, 0x5e, 0x44, 0xbc, 0x78, 0xf1, 0xe2, 0xc5, 0x8b, 0xf7, 0x22, 0xe1,
	0x8e, 0x75, 0x66, 0xba, 0x2e, 0xed, 0x19, 0x83, 0x9e, 0x79, 0x71, 0x4f, 0x24, 0xde, 0x63, 0x09,
	0x63, 0x67, 0x30, 0xf4, 0x02, 0x8f, 0xac, 0x76, 0xcd, 0x9d, 0x68, 0xa1, 0xcd, 0x42, 0xd7, 0x34,
	0xda, 0xa6, 0x4f, 0x79, 0xfe, 0xa6, 0x16, 0x78, 0x03, 0xc7, 0x32, 0x44, 0x91, 0x7b, 0xb1, 0x94,
	0xc0, 0xb1, 0xf9, 0xd6, 0xcc, 0x56, 0xde, 0x7b, 0xea, 0xd0, 0x67, 0xb2, 0x98, 0x76, 0xe6, 0xb5,
	0xdb, 0x17, 0x21, 0x2a, 0x4c, 0xbd, 0x37, 0x81, 0xea, 0xcd, 0xae, 0xd9, 0xa7, 0xc6, 0xc0, 0xec,
	0x19, 0x3d, 0xaf, 0xeb, 0x58, 0xf7, 0xe2, 0x49, 0x5e, 0x48, 0xfb, 0xcf, 0x69, 0x50, 0x5b, 0xac,
	0x23, 0x15, 0x5e, 0xf9, 0x91, 0x43, 0x9f, 0x91, 0x43, 0xc8, 0x63, 0x63, 0x36, 0xed, 0x98, 0xa3,
	0x5e, 0x50, 0x52, 0xee, 0x28, 0x77, 0x73, 0xf7, 0xdf, 0xdc, 0x99, 0x18, 0xdf, 0x4e, 0xa4, 0xce,
	0x1e, 0x2f, 0x7a, 0xb8, 0xa0, 0xe7, 0x9e, 0x8e, 0x93, 0xe4, 0x87, 0x90, 0x45, 0x4c, 0x7d, 0xaf,
	0x6d, 0x96, 0x52, 0x88, 0xe6, 0xce, 0x65, 0x68, 0xea, 0xc7, 0xbb, 0xe5, 0xc3, 0x05, 0x3d, 0xc3,
	0x2a, 0xd5, 0xbd, 0xb6, 0x49, 0x3e, 0x06, 0xfc, 0x6f, 0x9c, 0x07, 0x4f, 0x4b, 0x69, 0xac, 0x7f,
	0xfb, 0xb2, 0xfa, 0x0f, 0x82, 0xa7, 0x87, 0x0b, 0xfa, 0xca, 0x53, 0xfe, 0x97, 0xfc, 0x08, 0xd6,
	0x79, 0xf3, 0x17, 0x7e, 0x40, 0x87, 0x17, 0x06, 0xf5, 0x2d, 0x73, 0x40, 0x4b, 0x8b, 0x88, 0xe8,
	0xdd, 0x4b, 0x3b, 0xc2, 0x6b, 0x54, 0xb1, 0xc2, 0xe1, 0x82, 0xbe, 0xf6, 0x74, 0x12, 0x18, 0x52,
	0xa9, 0x6f, 0x0e, 0xfd, 0x33, 0xb3, 0x57, 0x5a, 0x7a, 0x31, 0x95, 0xea, 0xbc, 0xa8, 0xa4, 0x92,
	0x48, 0x92, 0x23, 0x28, 0x22, 0x26, 0x97, 0x61, 0x1b, 0xf9, 0x8e, 0x55, 0x5a, 0x46, 0x5c, 0x5f,
	0xbb, 0x0c, 0x57, 0x83, 0x3e, 0xab, 0xb3, 0xb2, 0x87, 0x0b, 0x3a, 0xf6, 0x43, 0xa6, 0x77, 0x8b,
	0x90, 0x97, 0x75, 0x18, 0x5c, 0x7b, 0x0e, 0xf9, 0x13, 0x3a, 0xf4, 0x3d, 0xd7, 0xec, 0x55, 0xe8,
	0x30, 0x20, 0x04, 0x16, 0x1d, 0xcb, 0x73, 0x71, 0x56, 0xb3, 0x3a, 0xfe, 0x67, 0xb0, 0x80, 0x3e,
	0x0f, 0x70, 0x8a, 0xb2, 0x3a, 0xfe, 0x27, 0x1b, 0xb0, 0x64, 0x79, 0x3d, 0x6f, 0x58, 0x4a, 0xdf,
	0x49, 0xdf, 0xcd, 0xea, 0x3c, 0x41, 0xb6, 0x61, 0x8d, 0xe5, 0x1a, 0xfe, 0x99, 0x69, 0x7b, 0xcf,
	0x0c, 0x5e, 0x62, 0x11, 0xab, 0xad, 0xb2, 0x8c, 0x26, 0xc2, 0x2b, 0x0c, 0xac, 0xfd, 0x8f, 0x6c,
	0x9c, 0xb9, 0x6a, 0x01, 0xed, 0x93, 0x5b, 0x00, 0xb2, 0x7b, 0x8e, 0x8d, 0x9d, 0x28, 0xe8, 0x59,
	0x01, 0xa9, 0xd9, 0xe4, 0x8d, 0x71, 0xef, 0x5d, 0xb3, 0x4f, 0x45, 0x8f, 0x72, 0x02, 0xd6, 0x30,
	0xfb, 0x94, 0x6c, 0x41, 0xd6, 0x7b, 0xe6, 0xd2, 0xa1, 0x31, 0x72, 0x6c, 0x64, 0x8a, 0x82, 0x9e,
	0x41, 0xc0, 0xa9, 0x63, 0x93, 0x6f, 0xc1, 0x75, 0x59, 0x5f, 0x14, 0xf2, 0xe9, 0x10, 0x31, 0xf1,
	0x4e, 0x6e, 0x88, 0xdc, 0x63, 0xac, 0x20, 0xf2, 0xd8, 0xa8, 0xe2, 0xb5, 0x7c, 0xfa, 0x1c, 0x27,
	0x74, 0x49, 0x5f, 0x8d, 0x56, 0x68, 0xd2, 0xe7, 0xac, 0x87, 0x7d, 0xda, 0x6f, 0xd3, 0xa1, 0x61,
	0x79, 0x23, 0x37, 0xc0, 0xb9, 0x2a, 0xe8, 0x39, 0x0e, 0xab, 0x30, 0x10, 0x39, 0x06, 0x12, 0x5f,
	0xdd, 0x6c, 0x22, 0x4a, 0x2b, 0x38, 0xa9, 0x6f, 0x4c, 0x4d, 0xea, 0xe4, 0xfa, 0xd3, 0xd5, 0x60,
	0x72, 0x45, 0xfe, 0x00, 0x60, 0x68, 0xf5, 0x6d, 0xa3, 0x67, 0xb6, 0x69, 0xaf, 0x94, 0xb9, 0xa3,
	0xdc, 0x2d, 0xde, 0x7f, 0x7d, 0x0a, 0x91, 0x5e, 0xa9, 0xef, 0x1d, 0xb1, 0x12, 0x55, 0x77, 0xd4,
	0xd7, 0xb3, 0xac, 0x06, 0x26, 0xc9, 0x4d, 0xc8, 0x74, 0xa9, 0x67, 0x38, 0x6e, 0xc7, 0x2b, 0x65,
	0x91, 0x0c, 0x2b, 0x5d, 0xea, 0xd5, 0xdc, 0x8e, 0xc7, 0xb2, 0x7a, 0xf4, 0x29, 0x9f, 0x0c, 0xc0,
	0x91, 0xac, 0x60, 0xba, 0x66, 0x93, 0x43, 0x80, 0x60, 0x68, 0x5a, 0x94, 0xd7, 0xcb, 0x8d, 0x17,
	0x4d, 0x6c, 0x6c, 0x3b, 0x3a, 0xb5, 0xbc, 0x7e, 0x9f, 0xba, 0xb6, 0x19, 0x38, 0x9e, 0xdb, 0x62,
	0x35, 0x18, 0x66, 0x3d, 0x1b, 0xc8, 0xbf, 0xe4, 0x1a, 0x2c, 0x07, 0x66, 0x9b, 0x35, 0x91, 0xc7,
	0x26, 0x96, 0x02, 0xb3, 0x5d, 0xb3, 0x59, 0xdb, 0x0c, 0x8c, 0xb3, 0x53, 0xe0, 0xdd, 0x0a, 0xcc,
	0x36, 0xce, 0xf1, 0xb7, 0xe0, 0x3a, 0x9f, 0x88, 0xbe, 0xe3, 0xf7, 0xcd, 0xc0, 0x3a, 0x33, 0x9e,
	0xd2, 0xa1, 0xef, 0x78, 0x6e, 0xa9, 0x78, 0x47, 0xb9, 0x9b, 0xd1, 0x37, 0x30, 0xb7, 0x2e, 0x32,
	0x1f, 0xf1, 0x3c, 0xf2, 0x11, 0x2c, 0xfb, 0xde, 0x68, 0x68, 0xd1, 0xd2, 0xea, 0x0c, 0x12, 0x1d,
	0x50, 0x97, 0x11, 0xb4, 0x89, 0xa5, 0x74, 0x51, 0x9a, 0x7c, 0x02, 0xb9, 0x01, 0x2e, 0x11, 0xc3,
	0xa2, 0xc3, 0xa0, 0xa4, 0xde, 0x49, 0xdf, 0xcd, 0xdd, 0xbf, 0x35, 0x55, 0x39, 0xba, 0x8c, 0x74,
	0xe0, 0x35, 0x70, 0x49, 0x7d, 0x00, 0x4b, 0x7c, 0x66, 0xd6, 0xb0, 0xd9, 0x5b, 0xb3, 0xd6, 0x2d,
	0xce, 0x86, 0xce, 0xcb, 0x92, 0x7b, 0xb0, 0x31, 0x72, 0x6d, 0x3a, 0x34, 0xce, 0xa8, 0x69, 0x1b,
	0x4e, 0xbf, 0x2b, 0x66, 0x97, 0x20, 0x25, 0xd6, 0x30, 0xef, 0x90, 0x9a, 0x76, 0xad, 0xdf, 0xe5,
	0xb3, 0x78, 0x1f, 0xae, 0xf5, 0x1d, 0xdb, 0xee, 0x51, 0x63, 0xe0, 0xf9, 0x0e, 0x23, 0xb6, 0xa8,
	0xb1, 0x8e, 0x35, 0xd6, 0x79, 0xe6, 0x89, 0xc8, 0xe3, 0x75, 0xde, 0x86, 0x55, 0xc7, 0x0d, 0xd9,
	0x10, 0xd7, 0xf8, 0x06, 0x96, 0x2e, 0x38, 0xae, 0xe8, 0x54, 0x8b, 0x2d, 0xf6, 0x3a, 0xe4, 0x3a,
	0x43, 0x87, 0xba, 0x36, 0x9f, 0xec, 0x6b, 0x38, 0xd9, 0xdf, 0xb8, 0x94, 0x55, 0xd9, 0x6a, 0xde,
	0xd9, 0xc7, 0x4a, 0x38, 0xdf, 0xd0, 0x09, 0xff, 0x93, 0x37, 0xa1, 0x60, 0xd3, 0x9e, 0xf3, 0x94,
	0x09, 0xdd, 0xe0, 0x62, 0x40, 0x4b, 0xd7, 0xb1, 0xd1, 0xbc, 0x04, 0xb6, 0x2e, 0x06, 0x94, 0xdc,
	0x83, 0xf5, 0x21, 0xed, 0x21, 0xd7, 0xf8, 0x67, 0xce, 0x80, 0x0f, 0xc6, 0x2f, 0xdd, 0x40, 0x71,
	0x43, 0xa2, 0x59, 0x38, 0x16, 0x9f, 0xbc, 0x07, 0xeb, 0x9e, 0x6b, 0xf4, 0x1d, 0xcb, 0x88, 0x2d,
	0xc0, 0x12, 0xf2, 0x94, 0xea, 0xb9, 0x75, 0xc7, 0xaa, 0x47, 0x56, 0xe1, 0x9b, 0x50, 0xb0, 0xcc,
	0x80, 0x76, 0x3d, 0xd9, 0x89, 0x9b, 0x58, 0x30, 0x2f, 0x81, 0xac, 0x13, 0x9b, 0x2d, 0x80, 0xf1,
	0x18, 0x48, 0x09, 0x56, 0x4c, 0x8b, 0x63, 0xe5, 0xe2, 0x51, 0x26, 0xc9, 0x26, 0x64, 0x5c, 0xc7,
	0x3a, 0x8f, 0xc8, 0xa4, 0x30, 0x4d, 0x54, 0x48, 0x8f, 0x45, 0x11, 0xfb, 0xab, 0xfd, 0xb6, 0x02,
	0x6b, 0xfb, 0x4e, 0x2f, 0xa0, 0xc3, 0xdd, 0x9e, 0x67, 0x9d, 0x1f, 0x0f, 0xd8, 0x48, 0x18, 0xbf,
	0xb7, 0x59, 0x72, 0x2c, 0xf8, 0x56, 0x30, 0x5d, 0xb3, 0xc9, 0x0d, 0x58, 0xa1, 0x3d, 0xda, 0x67,
	0x39, 0x29, 0xcc, 0x59, 0x66, 0xc9, 0x9a, 0x4d, 0x3e, 0x86, 0x5c, 0x07, 0x11, 0xf1, 0x21, 0xa4,
	0x91, 0xc1, 0xb6, 0xa6, 0x26, 0x86, 0x37, 0xc6, 0x46, 0xa4, 0x43, 0x27, 0xfc, 0xaf, 0xfd, 0x71,
	0x16, 0xd6, 0x8f, 0x1c, 0x3f, 0x88, 0xce, 0x9b, 0x4e, 0xbf, 0x24, 0x6f, 0x43, 0x86, 0x29, 0x26,
	0xc6, 0x90, 0x7e, 0x29, 0x76, 0xf7, 0x1c, 0x43, 0xb9, 0x6b, 0xfa, 0x54, 0xa7, 0x5f, 0xea, 0x2b,
	0x6d, 0xfe, 0x87, 0xef, 0x01, 0x8c, 0x1a, 0xbc, 0x53, 0x3c, 0x41, 0xaa, 0x90, 0xe7, 0xe3, 0xf0,
	0x70, 0x5c, 0xb8, 0x41, 0xe4, 0xee, 0x6b, 0x33, 0x3a, 0x15, 0xa1, 0x80, 0x9e, 0x6b, 0x47, 0xc8,
	0x31, 0x96, 0x0a, 0x8b, 0x51, 0xa9, 0xb0, 0x0d, 0x69, 0x29, 0x7d, 0x8b, 0xf7, 0x4b, 0xd3, 0x42,
	0xce, 0xea, 0xdb, 0x4d, 0xfa, 0x5c, 0x67, 0x85, 0xb8, 0x60, 0x0b, 0x8c, 0xbe, 0x67, 0x53, 0x21,
	0x87, 0x57, 0xba, 0x34, 0xa8, 0x7b, 0x36, 0x25, 0xdf, 0x00, 0x12, 0xd6, 0x33, 0xad, 0x73, 0xb3,
	0x4b, 0x59, 0x4b, 0x2b, 0x38, 0x75, 0xaa, 0xc8, 0x39, 0xe1, 0x19, 0x35, 0x9b, 0x7c, 0x0d, 0x8a,
	0xae, 0x67, 0xb4, 0x87, 0xde, 0x33, 0x9f, 0x1a, 0x3d, 0xc7, 0x0f, 0x4a, 0x99, 0x3b, 0x69, 0xc6,
	0x2c, 0xae, 0xb7, 0x8b, 0x40, 0x46, 0x45, 0xdc, 0x9c, 0x24, 0x47, 0x39, 0xb6, 0x5f, 0xca, 0x62,
	0x99, 0x9c, 0x84, 0xd5, 0x6c, 0x9f, 0x4d, 0x24, 0x1f, 0x94, 0x5f, 0x02, 0xcc, 0x5d, 0xc6, 0x51,
	0xf9, 0xe4, 0xbb, 0x00, 0x83, 0x21, 0xed, 0x18, 0x4c, 0x0d, 0xf3, 0x4b, 0x39, 0x24, 0xd9, 0xe6,
	0xb4, 0x34, 0x3d, 0x19, 0xd2, 0xce, 0x81, 0xd9, 0xa7, 0x7a, 0x76, 0x20, 0xfe, 0xf9, 0xe4, 0x03,
	0x58, 0x16, 0x6b, 0x23, 0x8f, 0xd5, 0xb6, 0xa6, 0xab, 0xb1, 0x82, 0x5c, 0xba, 0x88, 0xa2, 0xa4,
	0x0d, 0x37, 0xe5, 0xf8, 0xd9, 0x78, 0x0c, 0xea, 0x32, 0x26, 0x12, 0xe2, 0xb1, 0x80, 0xc4, 0x7d,
	0x67, 0xa6, 0x9c, 0x72, 0xfc, 0xa0, 0xca, 0xca, 0x0b, 0x39, 0x29, 0xb7, 0xd4, 0x09, 0x38, 0xa9,
	0xc3, 0x2a, 0xe2, 0xf6, 0x83, 0x8b, 0x1e, 0xe5, 0x0c, 0x5a, 0x44, 0xcc, 0x6f, 0x5d, 0x86, 0xb9,
	0xc9, 0x4a, 0x23, 0xab, 0x16, 0x7a, 0xd1, 0x24, 0xd9, 0x87, 0xd5, 0x2e, 0x75, 0x71, 0xb3, 0x34,
	0x42, 0x39, 0x9e, 0xbe, 0x82, 0x1c, 0x2f, 0x74, 0xa3, 0x49, 0xb6, 0x9b, 0x53, 0xd7, 0x6c, 0xf7,
	0x28, 0x12, 0x5b, 0x08, 0x49, 0x15, 0xf7, 0x8d, 0x55, 0x9e, 0x11, 0x92, 0x8b, 0xbc, 0xc3, 0x04,
	0x64, 0x40, 0x87, 0xd4, 0x0f, 0xa4, 0x00, 0x5a, 0x43, 0x01, 0x54, 0x94, 0x60, 0x21, 0x7c, 0xa6,
	0x44, 0x1a, 0x49, 0x10, 0x69, 0x3b, 0xb0, 0xee, 0x9f, 0x39, 0xb4, 0x67, 0x1b, 0x62, 0xd1, 0x3e,
	0xf3, 0x86, 0xb6, 0x5f, 0x5a, 0x47, 0x8c, 0x6b, 0x3c, 0x8b, 0xaf, 0x8a, 0xc7, 0x2c, 0x83, 0x3c,
	0x80, 0x55, 0xab, 0x67, 0xfa, 0xbe, 0xd3, 0xb9, 0x90, 0xad, 0x6f, 0xcc, 0x58, 0x4c, 0x15, 0x51,
	0x0e, 0xbb, 0xc3, 0xc8, 0xa8, 0x17, 0xad, 0x28, 0xc8, 0x67, 0x7a, 0x91, 0x68, 0xd5, 0xb1, 0x51,
	0x82, 0x67, 0xf5, 0x0c, 0x07, 0x70, 0x06, 0xf7, 0x47, 0x6d, 0x23, 0x2c, 0xe0, 0x97, 0xae, 0x63,
	0xa7, 0xf2, 0xfe, 0xa8, 0xbd, 0x2f, 0x0a, 0xf9, 0xe4, 0x7b, 0xb0, 0xe9, 0xf8, 0xa8, 0x32, 0x19,
	0x3d, 0xcf, 0x42, 0xf9, 0x6b, 0x98, 0xa3, 0xe0, 0xcc, 0xf0, 0x06, 0xd4, 0x2d, 0xdd, 0x40, 0x12,
	0x5e, 0x77, 0x7c, 0xa6, 0x37, 0x1d, 0x89, 0xfc, 0xf2, 0x28, 0x38, 0x3b, 0x1e, 0x50, 0x97, 0x34,
	0x21, 0x2f, 0xb0, 0xb3, 0xe5, 0xd8, 0x43, 0xb1, 0x5c, 0xbc, 0xff, 0xcd, 0xa9, 0x81, 0x24, 0xc8,
	0x23, 0x21, 0x29, 0xd8, 0xba, 0xed, 0xe9, 0x42, 0xde, 0x61, 0x42, 0xfb, 0x08, 0x72, 0x91, 0x3c,
	0xb2, 0x0e, 0xab, 0xfb, 0xb5, 0xa3, 0x56, 0x55, 0x37, 0xea, 0xc7, 0x7b, 0xd5, 0x23, 0xa3, 0xde,
	0x52, 0x17, 0x08, 0x81, 0x62, 0x04, 0x68, 0x3c, 0x38, 0x54, 0x15, 0x76, 0xae, 0xd9, 0x98, 0x6e,
	0xc8, 0x1f, 0x90, 0x77, 0x21, 0x2b, 0x24, 0x9f, 0x3f, 0x10, 0xa2, 0x2f, 0x3f, 0x16, 0x7d, 0xfe,
	0x40, 0xcf, 0xb4, 0xc5, 0x3f, 0xf2, 0x6d, 0x58, 0x72, 0x02, 0xda, 0xf7, 0x4b, 0x29, 0x9c, 0x92,
	0x37, 0x5e, 0xb8, 0x1b, 0xea, 0xbc, 0x3c, 0xb9, 0x0d, 0xb9, 0x9e, 0x67, 0x32, 0x1e, 0x70, 0x1d,
	0xff, 0x0c, 0x65, 0x76, 0x46, 0x07, 0x06, 0xda, 0x47, 0x08, 0xf9, 0x36, 0x64, 0xbb, 0xa6, 0xdb,
	0x35, 0x2c, 0xcf, 0xed, 0x88, 0xd3, 0xc8, 0x66, 0xd2, 0x9a, 0x76, 0xbb, 0x15, 0xcf, 0xed, 0xe8,
	0x99, 0xae, 0xf8, 0x37, 0x21, 0x44, 0x96, 0xe6, 0x11, 0x22, 0x1a, 0x14, 0xc2, 0xaa, 0x4c, 0x81,
	0x90, 0x7a, 0xab, 0x2c, 0x71, 0xe2, 0xf9, 0xe4, 0x21, 0xe4, 0x86, 0x74, 0xe0, 0x0d, 0x03, 0xc3,
	0x36, 0x03, 0x53, 0x28, 0xac, 0x57, 0x99, 0x41, 0x7f, 0xb0, 0xb3, 0x67, 0x06, 0xa6, 0x8e, 0x95,
	0x75, 0xe0, 0x48, 0x18, 0x84, 0x89, 0x61, 0xc7, 0x37, 0x86, 0x52, 0x47, 0x34, 0x6c, 0x6f, 0x64,
	0x3b, 0xa8, 0xc1, 0x66, 0x74, 0xd5, 0xf1, 0x43, 0xe5, 0x71, 0x8f, 0xc1, 0x37, 0xb7, 0x01, 0xc6,
	0x78, 0xc8, 0x6b, 0x90, 0xed, 0x78, 0x5e, 0x30, 0x18, 0x3a, 0xe1, 0x7e, 0x3c, 0x06, 0x68, 0xff,
	0x70, 0x11, 0xae, 0x1d, 0xd0, 0xa0, 0x49, 0x2d, 0xcf, 0xb5, 0xcd, 0xe1, 0x05, 0xe7, 0x93, 0x79,
	0x76, 0xb7, 0xf1, 0x06, 0x94, 0x8a, 0x6e, 0x40, 0x3f, 0x84, 0x45, 0xdc, 0x50, 0xf8, 0x5e, 0xfb,
	0xf5, 0x04, 0xd9, 0x93, 0xd0, 0xe8, 0x0e, 0x63, 0x50, 0x1d, 0x2b, 0x92, 0x6a, 0xa8, 0x86, 0x2e,
	0x22, 0x8a, 0xf7, 0xae, 0x88, 0x62, 0x42, 0x2b, 0xdd, 0x85, 0x82, 0xdc, 0x90, 0xb8, 0x68, 0xe0,
	0x27, 0xcc, 0x5b, 0xd3, 0xf3, 0x2d, 0xb6, 0x28, 0x94, 0xff, 0xf9, 0xf6, 0x38, 0xe1, 0x6b, 0xbf,
	0x54, 0x60, 0x11, 0xb7, 0xc3, 0x22, 0xc0, 0xc9, 0xe9, 0x6e, 0xe5, 0xb0, 0xdc, 0x68, 0x54, 0x8f,
	0xd4, 0x05, 0xb2, 0x06, 0x85, 0x7a, 0xb9, 0x55, 0x39, 0x6c, 0x55, 0xcb, 0xf5, 0x7a, 0xb9, 0x55,
	0x55, 0x15, 0x02, 0xb0, 0xcc, 0xd7, 0x91, 0x9a, 0x22, 0x79, 0xc8, 0x3c, 0x3c, 0xad, 0x36, 0x5b,
	0xb5, 0xe3, 0x86, 0x9a, 0x26, 0xd7, 0x81, 0x9c, 0x8c, 0xda, 0x62, 0xb6, 0xcb, 0x9d, 0x80, 0x0e,
	0x3f, 0xba, 0xff, 0x1d, 0x75, 0x11, 0x57, 0x1e, 0xf6, 0x3e, 0x84, 0x2d, 0x91, 0x0d, 0x50, 0x1f,
	0x8e, 0xa8, 0x8f, 0xa2, 0x41, 0x42, 0x97, 0xc9, 0x4d, 0xb8, 0x26, 0xe4, 0x9e, 0x13, 0x9c, 0xd5,
	0x47, 0xbd, 0xc0, 0xe1, 0x4a, 0x80, 0xba, 0x92, 0x84, 0xfc, 0x5b, 0xdf, 0x54, 0x33, 0x44, 0x85,
	0xfc, 0x49, 0xc5, 0x38, 0x3c, 0xae, 0x57, 0x8d, 0x93, 0xf2, 0x41, 0x55, 0xcd, 0xb2, 0x92, 0x87,
	0xa7, 0xe5, 0x86, 0xf1, 0xe4, 0xf8, 0xd4, 0xa8, 0xd7, 0x3e, 0x37, 0x8e, 0xca, 0xbb, 0xd5, 0x23,
	0x15, 0xb4, 0x6d, 0x58, 0x16, 0x92, 0xbf, 0x08, 0xc0, 0xff, 0x1d, 0x7a, 0x7d, 0xaa, 0x2e, 0x30,
	0x1c, 0x3c, 0xdd, 0xb4, 0x86, 0x94, 0xba, 0xaa, 0xa2, 0xfd, 0x72, 0x05, 0xae, 0x27, 0x51, 0x7f,
	0x3e, 0xd1, 0x70, 0x0f, 0x96, 0x51, 0x93, 0x91, 0xb2, 0xe1, 0x46, 0xc2, 0x9c, 0xb0, 0x7c, 0x5d,
	0x14, 0x8b, 0xb0, 0x5a, 0x3a, 0xca, 0x6a, 0x9f, 0xf1, 0x13, 0x10, 0xee, 0x27, 0x9c, 0x57, 0xee,
	0x5d, 0x89, 0x57, 0xfc, 0xc1, 0x4e, 0xcb, 0x6c, 0xe3, 0x1e, 0xca, 0xd4, 0x0d, 0xdc, 0x7b, 0x4e,
	0x00, 0xf8, 0x49, 0x09, 0xb1, 0x71, 0xf5, 0xe9, 0xfd, 0xab, 0x62, 0xab, 0xb3, 0x9a, 0x88, 0x2f,
	0xdb, 0x97, 0x7f, 0xc9, 0x5b, 0x50, 0x94, 0x75, 0x05, 0x3f, 0x73, 0x99, 0x51, 0x10, 0x50, 0x41,
	0xf4, 0xdb, 0x90, 0x63, 0x83, 0x90, 0x07, 0xb4, 0x15, 0x2c, 0x03, 0x81, 0xd9, 0x96, 0xc7, 0xb2,
	0x37, 0x20, 0xdf, 0x19, 0x52, 0xff, 0x4c, 0xea, 0x8b, 0x7c, 0xf5, 0xe7, 0x10, 0x26, 0x74, 0xc1,
	0x7b, 0xb0, 0xe1, 0xf8, 0xc6, 0x99, 0x63, 0xdb, 0xd4, 0x35, 0xd8, 0x59, 0x55, 0x14, 0xcd, 0x62,
	0xd1, 0x35, 0xc7, 0x3f, 0xc4, 0xac, 0x03, 0xea, 0x89, 0x0a, 0x47, 0xb0, 0xda, 0x1e, 0xf9, 0x8e,
	0x4b, 0x7d, 0xdf, 0x10, 0x53, 0x01, 0x38, 0x15, 0xd3, 0x06, 0x98, 0x5d, 0x51, 0x4e, 0x6c, 0x73,
	0x4c, 0x50, 0x17, 0x65, 0xdd, 0x5d, 0x3e, 0x3d, 0x6f, 0x41, 0xd1, 0xa5, 0xd4, 0x36, 0xce, 0x9c,
	0xee, 0x59, 0xcf, 0xe9, 0x9e, 0x05, 0x78, 0xdc, 0xcd, 0xe8, 0x05, 0x06, 0x3d, 0x94, 0x40, 0xf2,
	0x10, 0xd6, 0x6d, 0xc7, 0x47, 0x9b, 0x9f, 0x50, 0xe4, 0xdd, 0x8e, 0x27, 0xb5, 0xb2, 0xe9, 0xfd,
	0x61, 0x8f, 0x97, 0xc5, 0x36, 0xf0, 0x88, 0xb4, 0x66, 0x4f, 0x40, 0x7c, 0xb2, 0x0d, 0xea, 0xc1,
	0xc8, 0xb1, 0x1d, 0xb7, 0xbb, 0xef, 0x0d, 0x1b, 0xf4, 0xd9, 0xae, 0x23, 0xcf, 0xc2, 0x53, 0xf0,
	0x88, 0x1e, 0x58, 0xbc, 0xba, 0x1e, 0x78, 0x0b, 0xc0, 0xf5, 0x02, 0xc7, 0xa2, 0x46, 0xdf, 0xef,
	0xe2, 0xb9, 0x38, 0xab, 0x67, 0x39, 0xa4, 0xee, 0x77, 0x99, 0xae, 0xe4, 0x9f, 0x79, 0xcf, 0xe2,
	0xfa, 0x8a, 0xd0, 0x95, 0x58, 0xc6, 0x0b, 0xb4, 0x95, 0xb5, 0x97, 0xd5, 0x56, 0xb4, 0x1d, 0x58,
	0x11, 0x2c, 0xcc, 0x04, 0x4f, 0xe3, 0x58, 0xaf, 0x97, 0x99, 0x5c, 0xca, 0xc0, 0xe2, 0x41, 0xb9,
	0xce, 0xc4, 0x51, 0x01, 0xb2, 0xf5, 0x5a, 0xa3, 0x66, 0x60, 0x32, 0xa5, 0x6d, 0x43, 0x36, 0x64,
	0x52, 0x92, 0x85, 0xa5, 0x87, 0xa7, 0xb5, 0xca, 0x03, 0xbe, 0xfb, 0xb7, 0xaa, 0xf5, 0x93, 0x63,
	0xbd, 0xac, 0x3f, 0x39, 0x3c, 0x3e, 0x6d, 0x56, 0x55, 0x45, 0xeb, 0xc0, 0x5a, 0xb5, 0x47, 0xfb,
	0xbb, 0x8e, 0x6b, 0x87, 0xa4, 0x8e, 0x1e, 0xb1, 0x94, 0xd8, 0x11, 0xeb, 0x3b, 0x90, 0x6b, 0x3b,
	0xae, 0x6d, 0x5c, 0x6d, 0x45, 0x43, 0x5b, 0x62, 0xf5, 0xb5, 0xbf, 0xa6, 0x80, 0x3a, 0x39, 0xc9,
	0xe4, 0x3d, 0x58, 0x42, 0x4c, 0x42, 0x84, 0xcc, 0x44, 0xc4, 0x4b, 0x91, 0x53, 0xb8, 0x86, 0xdd,
	0x1a, 0x77, 0x41, 0x70, 0x55, 0x6a, 0x06, 0x69, 0xa7, 0x46, 0xa6, 0x13, 0x3a, 0x09, 0xf2, 0xb5,
	0x7f, 0x95, 0x02, 0x32, 0xcd, 0xf8, 0x93, 0xc7, 0x49, 0x65, 0xae, 0xe3, 0x24, 0x29, 0x42, 0x2a,
	0xdc, 0x2c, 0x53, 0x8e, 0xcd, 0x8e, 0x87, 0x81, 0x13, 0xf4, 0xf8, 0x56, 0x99, 0xd5, 0x79, 0x82,
	0x7c, 0x2c, 0xf6, 0x4f, 0x2e, 0xd0, 0xee, 0x5e, 0x61, 0x3d, 0x46, 0x37, 0xcf, 0xef, 0xc2, 0x12,
	0x1b, 0x8e, 0xd4, 0x6e, 0x5e, 0xb4, 0x9c, 0x19, 0x35, 0x74, 0x5e, 0x83, 0xbc, 0x0d, 0xab, 0x7d,
	0x8f, 0x1d, 0x47, 0x68, 0x8f, 0x5a, 0x81, 0xe1, 0x8e, 0xfa, 0x52, 0x60, 0x31, 0x70, 0x13, 0xa1,
	0x8d, 0x51, 0x5f, 0xbb, 0x27, 0xf6, 0x44, 0x80, 0xe5, 0x66, 0xad, 0x71, 0x70, 0x54, 0x55, 0x17,
	0x18, 0x47, 0xd5, 0x4f, 0x8f, 0x5a, 0x35, 0x55, 0xc1, 0x4d, 0xa3, 0xda, 0x3a, 0x3d, 0x31, 0x1a,
	0xa7, 0xf5, 0x5d, 0xb6, 0x1b, 0x6a, 0xff, 0x52, 0x99, 0x24, 0x26, 0x6b, 0x56, 0x90, 0x43, 0x99,
	0x26, 0x47, 0x2a, 0x4a, 0x8e, 0xcf, 0x62, 0xea, 0xc4, 0x47, 0x57, 0x18, 0xcf, 0x04, 0x68, 0x4c,
	0x1c, 0xed, 0x07, 0x93, 0xfd, 0x90, 0xe3, 0x08, 0xd7, 0x4f, 0x01, 0xb2, 0x7a, 0xb5, 0x72, 0x5c,
	0xaf, 0x57, 0x1b, 0x7b, 0xaa, 0xc2, 0xf6, 0xf1, 0x5a, 0x63, 0xbf, 0xd6, 0xa8, 0xb5, 0xd8, 0x1a,
	0xfa, 0x3d, 0x05, 0x6e, 0x4f, 0x6f, 0x00, 0xbb, 0x17, 0x15, 0x71, 0x80, 0x9d, 0x47, 0x79, 0xba,
	0x0d, 0xb9, 0xc8, 0x59, 0x58, 0x30, 0x05, 0x8c, 0x8f, 0xc2, 0x58, 0x40, 0x8e, 0xf3, 0xbc, 0x2b,
	0x58, 0x44, 0xda, 0x7e, 0x4f, 0xce, 0xbb, 0xda, 0x2f, 0x14, 0xb8, 0x73, 0x79, 0x6f, 0xe6, 0xdb,
	0x94, 0x3f, 0x89, 0xeb, 0xeb, 0xc8, 0x78, 0xb1, 0x7b, 0x12, 0x94, 0x8e, 0x4c, 0x33, 0x38, 0x31,
	0xbb, 0x34, 0xb2, 0x1b, 0x08, 0xb5, 0x3d, 0x61, 0x4b, 0x49, 0xbf, 0xf4, 0x96, 0xa2, 0xfd, 0x1a,
	0x5c, 0x13, 0xb7, 0x20, 0xba, 0xe7, 0xf5, 0x1b, 0x66, 0x9f, 0x32, 0x0d, 0xde, 0xe9, 0x12, 0x02,
	0x8b, 0x68, 0x45, 0x12, 0xf6, 0x77, 0xb4, 0x20, 0xcd, 0x34, 0xff, 0x5c, 0x0f, 0x55, 0x49, 0xae,
	0x37, 0x88, 0x94, 0xf6, 0x4f, 0x53, 0x70, 0xf3, 0x80, 0x06, 0x13, 0x2d, 0xa0, 0xa0, 0xfd, 0xea,
	0x0a, 0xf0, 0x16, 0x64, 0x87, 0x9e, 0xd7, 0xe7, 0x86, 0x59, 0x3e, 0x6f, 0x99, 0xa1, 0x68, 0x82,
	0xed, 0x27, 0x03, 0x34, 0xa6, 0xb8, 0x36, 0x7d, 0x2e, 0x2c, 0x37, 0x59, 0x06, 0xa9, 0x31, 0x40,
	0xcc, 0x22, 0xb3, 0x14, 0xb7, 0xc8, 0xdc, 0x02, 0x18, 0xd2, 0x2f, 0xe3, 0xaa, 0x44, 0x76, 0x48,
	0xbf, 0xe4, 0x6a, 0x84, 0xf6, 0x11, 0xac, 0x1c, 0x88, 0x92, 0xab, 0x90, 0x3b, 0x6d, 0x34, 0x4f,
	0xaa, 0x95, 0xda, 0x7e, 0xad, 0xba, 0xc7, 0xb9, 0xba, 0x51, 0xfd, 0xbc, 0xc5, 0x15, 0x41, 0x85,
	0xe4, 0x60, 0x45, 0xaf, 0xee, 0xeb, 0xd5, 0xe6, 0xa1, 0x9a, 0xd2, 0xee, 0x41, 0x56, 0x97, 0x48,
	0x58, 0xce, 0x5e, 0x75, 0xbf, 0x7c, 0x7a, 0xc4, 0x0e, 0x86, 0xd7, 0x60, 0xad, 0xbc, 0xcf, 0xce,
	0x85, 0x1f, 0x7d, 0xf4, 0xbe, 0xf1, 0xa8, 0xaa, 0x37, 0x99, 0x36, 0xab, 0x68, 0xff, 0x55, 0x81,
	0xcd, 0x59, 0xb4, 0x9b, 0x8f, 0xe3, 0x7e, 0x04, 0xd7, 0xc5, 0x1d, 0x99, 0x11, 0x12, 0x8c, 0x5b,
	0x8f, 0x38, 0x0b, 0xbe, 0x3d, 0xad, 0x12, 0x24, 0xb1, 0x84, 0xbe, 0x6e, 0x4f, 0xf7, 0x85, 0x49,
	0x33, 0x97, 0x3e, 0x0f, 0x8c, 0x08, 0xb5, 0x39, 0x0f, 0x14, 0x18, 0xf8, 0x24, 0xa4, 0xf8, 0xc4,
	0x69, 0x73, 0x71, 0xf2, 0xb4, 0xa9, 0xfd, 0x74, 0x09, 0x6e, 0x9c, 0x8c, 0xda, 0x3d, 0xc7, 0x3f,
	0x63, 0x47, 0xca, 0xd1, 0xe0, 0x25, 0x0c, 0x81, 0xf1, 0x5b, 0x9b, 0xd4, 0xe4, 0xad, 0xcd, 0x0c,
	0xf5, 0x96, 0x9d, 0x60, 0x22, 0x86, 0x42, 0xbf, 0xb4, 0x38, 0xb6, 0xac, 0x27, 0x6c, 0x89, 0xc2,
	0x48, 0x98, 0x8f, 0x18, 0x09, 0xfd, 0xa9, 0x0b, 0xa1, 0xa5, 0xe9, 0x0b, 0x21, 0x0d, 0x0a, 0x8e,
	0x6f, 0x3c, 0x33, 0xdd, 0xc0, 0x40, 0x9d, 0x12, 0x79, 0x2b, 0xa3, 0xe7, 0x1c, 0xff, 0xb1, 0xe9,
	0x06, 0xfb, 0x0c, 0x44, 0xde, 0x01, 0xd5, 0xf1, 0x0d, 0x54, 0x75, 0xc2, 0xab, 0x90, 0x15, 0xae,
	0xe3, 0x39, 0x7e, 0xf3, 0xcc, 0x7b, 0x76, 0x20, 0x2e, 0x44, 0xbe, 0x06, 0x45, 0xdb, 0xb9, 0x30,
	0xb8, 0xe0, 0xc0, 0x16, 0xf3, 0xc2, 0xd0, 0xe3, 0x5c, 0x1c, 0x32, 0x60, 0x43, 0x5c, 0x18, 0x99,
	0xbd, 0x9e, 0xd8, 0x69, 0xa8, 0x6d, 0xb4, 0x1d, 0xdb, 0x2f, 0x15, 0xd0, 0xe0, 0xb7, 0x6a, 0xf6,
	0x7a, 0x4d, 0x01, 0xdf, 0x75, 0x6c, 0x9f, 0x7c, 0x08, 0x37, 0x50, 0xb9, 0xb4, 0xce, 0xa8, 0x75,
	0x6e, 0xc4, 0x06, 0x23, 0x2e, 0x33, 0x58, 0x76, 0x85, 0xe5, 0x56, 0x22, 0xa3, 0x7a, 0x04, 0xd7,
	0x46, 0xdd, 0xf1, 0x15, 0x12, 0x6a, 0x9d, 0xb8, 0xac, 0xf8, 0xdd, 0xc6, 0xb4, 0x50, 0x3a, 0xed,
	0xca, 0x03, 0xf9, 0x49, 0xcf, 0xbc, 0xc0, 0x5d, 0x83, 0x8c, 0xa6, 0x60, 0x4c, 0x05, 0x18, 0x9b,
	0xc5, 0x7c, 0x71, 0xd9, 0x71, 0xa9, 0x2a, 0x09, 0x5d, 0xf9, 0x97, 0xe9, 0x80, 0x6b, 0x7c, 0x4a,
	0xdb, 0xa3, 0x20, 0xf0, 0x5c, 0x36, 0xb3, 0x52, 0x0b, 0x9c, 0xbe, 0x99, 0xc5, 0x59, 0xdd, 0xc5,
	0x82, 0xc7, 0x83, 0x40, 0x5f, 0x6d, 0xc7, 0xd2, 0xbe, 0xf6, 0x39, 0x14, 0xe3, 0x45, 0xf0, 0x22,
	0x92, 0x29, 0x26, 0x9c, 0xc3, 0xf0, 0x7f, 0xcc, 0x6c, 0x9e, 0x9e, 0x69, 0x36, 0x5f, 0xe4, 0xd6,
	0x56, 0x2e, 0x37, 0xb5, 0x3f, 0x48, 0x41, 0x29, 0x99, 0xe7, 0xe7, 0x5b, 0xe1, 0x77, 0x01, 0x6d,
	0xc5, 0x5d, 0x6a, 0x58, 0x9e, 0xd7, 0x33, 0x6c, 0xef, 0x99, 0x2b, 0xfa, 0x56, 0xe4, 0xf0, 0x8a,
	0xe7, 0xf5, 0xf6, 0xbc, 0x67, 0x2e, 0x79, 0x07, 0x56, 0x3b, 0x43, 0x4a, 0x7f, 0x42, 0x0d, 0x7b,
	0x34, 0x34, 0x85, 0x5d, 0x1c, 0x0b, 0x72, 0xf0, 0x9e, 0x80, 0x92, 0xfb, 0x70, 0xcd, 0x1c, 0x05,
	0x9e, 0x61, 0x3b, 0x7e, 0xdf, 0xf1, 0xfd, 0x71, 0x71, 0x2e, 0x4b, 0xd7, 0x59, 0xe6, 0x1e, 0xcf,
	0x0b, 0xeb, 0x3c, 0x81, 0xa2, 0x2f, 0xb7, 0x49, 0x83, 0xed, 0x56, 0x42, 0x3b, 0xba, 0x3f, 0x3d,
	0x6d, 0x8c, 0x97, 0xa3, 0xc6, 0x99, 0x96, 0xd9, 0x0e, 0x77, 0x58, 0xdc, 0xa8, 0x0a, 0x7e, 0x34,
	0xa9, 0xfd, 0x06, 0xdc, 0xaa, 0x98, 0xae, 0x45, 0x7b, 0x31, 0x3a, 0x09, 0xda, 0xfd, 0xca, 0x45,
	0x84, 0xf6, 0x00, 0x5e, 0xbf, 0xac, 0xf9, 0xb9, 0x66, 0x4b, 0xfb, 0x3b, 0x0a, 0x5c, 0x93, 0xfb,
	0xfb, 0x21, 0x35, 0x6d, 0x21, 0x5e, 0xe7, 0x18, 0x84, 0x06, 0x05, 0x9f, 0xf6, 0x84, 0x95, 0x8c,
	0xad, 0xe9, 0x14, 0x37, 0xf1, 0x33, 0x20, 0x5b, 0x10, 0x35, 0xdb, 0x67, 0x12, 0xc2, 0xb4, 0x02,
	0xe7, 0x29, 0x35, 0xa4, 0xa5, 0x3f, 0xcd, 0xef, 0x0a, 0x38, 0xb4, 0xc5, 0xed, 0xfd, 0x37, 0x21,
	0xc3, 0x76, 0xbb, 0xf0, 0x68, 0x5f, 0xd0, 0x57, 0x86, 0xf4, 0x4b, 0xbc, 0x95, 0xf9, 0x1d, 0x05,
	0xae, 0x27, 0x75, 0x73, 0x3e, 0xd6, 0x2c, 0xc3, 0x8a, 0x85, 0x15, 0xa5, 0xc2, 0x33, 0x6d, 0xce,
	0x9f, 0x6e, 0x04, 0x39, 0x40, 0xd6, 0xd3, 0x3e, 0x87, 0x1b, 0xc9, 0x45, 0x7c, 0xf2, 0x03, 0xa9,
	0x4c, 0x29, 0xf3, 0xe1, 0xe6, 0xb5, 0xb4, 0x5f, 0xac, 0x24, 0x0d, 0x11, 0x8f, 0x20, 0x5b, 0x90,
	0xe5, 0xed, 0xcb, 0x93, 0x58, 0x56, 0xcf, 0x70, 0x00, 0xde, 0x39, 0xe7, 0x44, 0x66, 0x28, 0x06,
	0x8a, 0x57, 0x6a, 0x1c, 0xaf, 0xbc, 0x81, 0xd7, 0xc5, 0xb3, 0x4a, 0xf2, 0xd9, 0x64, 0x0b, 0xb2,
	0xfe, 0xa8, 0x6d, 0xf0, 0x1c, 0xee, 0x11, 0x90, 0xf1, 0x47, 0xed, 0x16, 0x66, 0xbe, 0x0e, 0xd0,
	0x36, 0xad, 0xf3, 0xee, 0xd0, 0x1b, 0xb9, 0xb6, 0xd8, 0x68, 0x22, 0x10, 0xf2, 0x36, 0xa4, 0x9f,
	0x9a, 0x03, 0xe1, 0x9c, 0xb1, 0x31, 0xd5, 0xa9, 0x47, 0xe6, 0x40, 0x67, 0x05, 0x18, 0xab, 0x9b,
	0xb6, 0x11, 0x98, 0x5d, 0x71, 0xdd, 0xb4, 0x64, 0xda, 0x2d, 0xb3, 0xcb, 0x76, 0x43, 0x64, 0x2b,
	0xef, 0x29, 0x1d, 0xa2, 0x43, 0x40, 0x66, 0x6c, 0xcf, 0x8b, 0xdb, 0x68, 0xcc, 0x3e, 0x3d, 0x16,
	0x85, 0xf4, 0x7c, 0x37, 0x92, 0x62, 0xfd, 0xff, 0xd3, 0xa3, 0xfe, 0xc0, 0xe8, 0x39, 0xee, 0xb9,
	0xb8, 0xca, 0xcf, 0x30, 0xc0, 0x91, 0xe3, 0x9e, 0xb3, 0x15, 0x88, 0xb6, 0xd6, 0x41, 0xcf, 0xb4,
	0x28, 0xde, 0xe6, 0x67, 0xf4, 0xac, 0xe3, 0xeb, 0x1c, 0x40, 0xde, 0x05, 0xd5, 0xef, 0xb3, 0x5d,
	0x2b, 0x32, 0xc8, 0x1c, 0xf7, 0xdc, 0x40, 0xf8, 0xee, 0x78, 0xa4, 0xfb, 0xb0, 0x1a, 0x98, 0xdc,
	0x3e, 0xcd, 0xe6, 0x89, 0x6d, 0x96, 0xdc, 0xc8, 0x31, 0x7d, 0x13, 0xd3, 0x32, 0xbb, 0x62, 0x72,
	0xd9, 0x51, 0xb4, 0x10, 0x44, 0x93, 0xfc, 0x0c, 0xc0, 0x76, 0x3d, 0xbc, 0x7d, 0x42, 0xc3, 0x46,
	0x86, 0x9d, 0x01, 0xa8, 0x75, 0x8e, 0xf7, 0x48, 0xa4, 0x09, 0x1b, 0x43, 0xc7, 0x3a, 0x33, 0x26,
	0x5b, 0x2b, 0x22, 0x69, 0xa6, 0x0f, 0xbf, 0xba, 0x63, 0x9d, 0xc5, 0x5b, 0x5c, 0x1b, 0x4e, 0x82,
	0x98, 0xe6, 0x14, 0xc1, 0x87, 0x8c, 0xb4, 0xca, 0x35, 0xa7, 0xb0, 0x77, 0xc8, 0x22, 0x06, 0x10,
	0xcb, 0xeb, 0x0f, 0x68, 0xc0, 0x2f, 0xd3, 0x79, 0xf9, 0xd2, 0x7d, 0x6c, 0x7a, 0xe7, 0x05, 0x3c,
	0x17, 0x56, 0xe3, 0xf8, 0x0e, 0x17, 0xf4, 0x35, 0x6b, 0x12, 0x48, 0xbe, 0x80, 0xb5, 0x4e, 0xcf,
	0xf4, 0xcf, 0x98, 0x70, 0x0e, 0x24, 0xfe, 0x0f, 0x66, 0xdc, 0xad, 0x47, 0xf1, 0xef, 0xb3, 0x5a,
	0x95, 0x33, 0x33, 0x08, 0xb1, 0xaf, 0x76, 0xe2, 0x20, 0xd2, 0x83, 0x1b, 0xfd, 0x51, 0x2f, 0x70,
	0x06, 0x3d, 0x6a, 0xb4, 0x19, 0x9a, 0xa1, 0x2f, 0x5b, 0xf8, 0x16, 0xb6, 0x70, 0xff, 0xd2, 0x16,
	0xea, 0xa2, 0xee, 0x2e, 0xaf, 0x1a, 0xb6, 0x73, 0xad, 0x9f, 0x94, 0xb1, 0x9b, 0x07, 0xa0, 0xcf,
	0x83, 0xa1, 0x89, 0x17, 0x03, 0xda, 0xb7, 0xe1, 0x8d, 0x17, 0xe2, 0x62, 0x5b, 0x39, 0xde, 0x22,
	0xb0, 0x25, 0x9e, 0xd7, 0xf1, 0xbf, 0xf6, 0x2e, 0xa4, 0x1f, 0x99, 0x03, 0xbc, 0x30, 0x1f, 0xf6,
	0xc4, 0xe2, 0x67, 0x7f, 0x19, 0xa4, 0x6f, 0x7f, 0x28, 0x0e, 0xce, 0xec, 0xaf, 0xf6, 0x31, 0xe4,
	0xa3, 0xeb, 0x20, 0x7e, 0x35, 0x9f, 0x8e, 0x5e, 0xcd, 0x27, 0x38, 0x2f, 0x69, 0xff, 0x4c, 0x81,
	0x42, 0x9c, 0x29, 0xb6, 0x20, 0xcb, 0x98, 0x22, 0x2a, 0x13, 0x32, 0x81, 0xd9, 0xe5, 0x2b, 0x5f,
	0x03, 0xc6, 0x1a, 0xc6, 0xa4, 0x68, 0xc8, 0x05, 0x66, 0xb7, 0x29, 0xa5, 0x83, 0x0a, 0xe9, 0x81,
	0x63, 0x09, 0xb1, 0xc0, 0xfe, 0x46, 0xb6, 0xb4, 0xe5, 0xa8, 0xd6, 0x7b, 0x3b, 0x2e, 0xc3, 0x32,
	0xe2, 0x64, 0x3c, 0xe6, 0xbb, 0xdb, 0x90, 0x13, 0xda, 0x13, 0xf6, 0x3b, 0x2b, 0x04, 0x0d, 0x82,
	0x5a, 0xac, 0xf7, 0xff, 0x58, 0x81, 0x9c, 0xe0, 0x74, 0xec, 0xfb, 0xbb, 0xa0, 0x8e, 0xd7, 0xac,
	0xf0, 0xb9, 0xe2, 0xc4, 0x5b, 0x1d, 0xc3, 0xd1, 0xe7, 0x8a, 0xc9, 0x00, 0x54, 0xec, 0x8c, 0x08,
	0x49, 0xb2, 0x08, 0x41, 0x3f, 0x0f, 0x0d, 0x0a, 0x7d, 0xd3, 0x71, 0x71, 0xbd, 0x61, 0x09, 0x4e,
	0x89, 0x1c, 0x03, 0xb6, 0xcc, 0x2e, 0x96, 0x99, 0x71, 0x2f, 0xaf, 0x41, 0x41, 0x0e, 0x8b, 0xe7,
	0x4a, 0x4d, 0x9c, 0x0f, 0x0c, 0x77, 0xf3, 0x9f, 0x2a, 0xb0, 0x36, 0xb5, 0x44, 0xc9, 0xa7, 0x50,
	0x08, 0x17, 0x39, 0xae, 0x6e, 0xbe, 0xa7, 0xbc, 0x36, 0x6b, 0x75, 0xe3, 0xba, 0xce, 0x0d, 0x23,
	0x04, 0xb8, 0x3d, 0xbd, 0x2d, 0x5c, 0x4a, 0xd2, 0xf4, 0x14, 0x49, 0xff, 0x96, 0x02, 0xb7, 0x2e,
	0x5d, 0xc1, 0x4c, 0x6a, 0x70, 0xf1, 0x38, 0xe6, 0x02, 0x4e, 0xe3, 0x02, 0x82, 0x43, 0x3e, 0x78,
	0x0c, 0x6a, 0x54, 0x6a, 0xe0, 0x71, 0x8f, 0xdb, 0x09, 0xbe, 0x71, 0x55, 0x99, 0x81, 0x67, 0xcd,
	0x55, 0x2b, 0x0e, 0x60, 0x3c, 0xbb, 0x75, 0x49, 0x85, 0xf1, 0x8e, 0xa6, 0x44, 0x77, 0xb4, 0x4d,
	0x60, 0x1b, 0x58, 0xd4, 0xee, 0x14, 0xa6, 0x27, 0x36, 0xb4, 0xf4, 0xd4, 0x86, 0x46, 0x60, 0x11,
	0x37, 0x12, 0xce, 0xed, 0xf8, 0x9f, 0x3b, 0x65, 0x09, 0xe6, 0x58, 0x92, 0x4e, 0x59, 0x9c, 0x31,
	0xc4, 0x12, 0xe2, 0xfc, 0xb7, 0x1c, 0x2e, 0x21, 0xee, 0xec, 0xf7, 0x7e, 0xbc, 0xf3, 0x13, 0x12,
	0x2c, 0x51, 0x1a, 0xfc, 0xef, 0x14, 0xe4, 0x0e, 0xbd, 0xa0, 0xee, 0xb8, 0x0e, 0x5b, 0xea, 0x11,
	0xc6, 0x53, 0xa2, 0x8c, 0xf7, 0x06, 0xe4, 0xa5, 0xd5, 0x3d, 0xea, 0x12, 0x28, 0x60, 0xd2, 0x28,
	0xe1, 0xb8, 0xc1, 0xd0, 0x8b, 0xce, 0x7e, 0x16, 0x21, 0x92, 0xa3, 0xdb, 0x5d, 0x83, 0x89, 0x1c,
	0x3e, 0xd2, 0xa5, 0x76, 0xf7, 0x74, 0x88, 0x6e, 0x71, 0x6d, 0x39, 0x1c, 0x31, 0xd4, 0x36, 0x1f,
	0xcd, 0x24, 0x3f, 0x2d, 0x4f, 0xf2, 0x13, 0xeb, 0x94, 0x28, 0xc0, 0xeb, 0xf3, 0x9d, 0x5e, 0x54,
	0x9a, 0xc4, 0x81, 0x44, 0xce, 0x44, 0x71, 0xe0, 0x7e, 0xfd, 0x1d, 0x58, 0x16, 0x12, 0x3b, 0x3b,
	0x76, 0x8d, 0x8d, 0x6b, 0xf3, 0x92, 0x36, 0x42, 0x45, 0x14, 0xe5, 0x71, 0x95, 0x7b, 0x41, 0xe0,
	0x50, 0x1c, 0x14, 0x88, 0x55, 0x8e, 0x10, 0x36, 0xb0, 0x37, 0xa1, 0x10, 0xed, 0x1c, 0x77, 0x37,
	0xc9, 0xea, 0xf9, 0x48, 0xef, 0x7c, 0xed, 0x4f, 0x01, 0x39, 0xa0, 0x41, 0x84, 0xfe, 0xfe, 0xbc,
	0xe6, 0xbf, 0x88, 0x75, 0x2f, 0x35, 0x65, 0xdd, 0xfb, 0x23, 0x05, 0xd6, 0xa7, 0xf0, 0xcf, 0xa7,
	0xe1, 0x6e, 0x41, 0xd6, 0x1c, 0x52, 0x33, 0x3a, 0xeb, 0x19, 0x06, 0xc0, 0x29, 0x67, 0xe7, 0x74,
	0x61, 0x7b, 0x89, 0x9d, 0x2b, 0xf2, 0x02, 0x8a, 0x02, 0x89, 0x7c, 0x93, 0x71, 0xb8, 0x1f, 0x08,
	0xc3, 0xc3, 0x6b, 0x09, 0x0b, 0x34, 0xec, 0x9e, 0x8e, 0x25, 0xb5, 0xff, 0xa9, 0x40, 0xe1, 0xe1,
	0xc8, 0xb1, 0xce, 0x5f, 0xc4, 0x96, 0x5b, 0x90, 0xe5, 0xa7, 0x04, 0xcb, 0x73, 0x65, 0xef, 0x18,
	0xa0, 0x66, 0x79, 0x6e, 0x98, 0x19, 0x35, 0xa1, 0x31, 0x80, 0x74, 0x60, 0x95, 0x7c, 0xc7, 0x4d,
	0x22, 0x59, 0x3d, 0x23, 0x18, 0xcf, 0x8f, 0x30, 0xc5, 0xd2, 0x9c, 0x4c, 0x31, 0x9e, 0x75, 0xda,
	0xe9, 0x50, 0x4b, 0x7a, 0xa6, 0x8a, 0x59, 0xaf, 0x22, 0x2c, 0xae, 0x40, 0xae, 0xc4, 0x15, 0x48,
	0xed, 0x3f, 0x29, 0xb0, 0x71, 0x40, 0x83, 0xd8, 0xf0, 0xe7, 0xe2, 0x8a, 0x4f, 0x20, 0xed, 0x0f,
	0x2d, 0xa1, 0xb6, 0x7f, 0x23, 0xe9, 0xf2, 0x71, 0x0a, 0xb7, 0xbc, 0xf5, 0x66, 0x15, 0x5f, 0x6c,
	0x33, 0xfe, 0x41, 0x78, 0xd5, 0xbb, 0x06, 0x05, 0xfe, 0xef, 0xd4, 0x3d, 0x77, 0xbd, 0x67, 0x6e,
	0xf4, 0xb6, 0xb7, 0xfa, 0x7c, 0xe0, 0xf9, 0x54, 0x55, 0xc6, 0xf7, 0xc1, 0x75, 0x6f, 0x48, 0xd5,
	0x94, 0xf6, 0x57, 0x14, 0xf4, 0x19, 0x98, 0xec, 0xc4, 0x7c, 0x6c, 0x79, 0x5f, 0xf0, 0x54, 0x6a,
	0x86, 0x46, 0x1c, 0xc3, 0xce, 0xb9, 0x2a, 0xce, 0xca, 0xe9, 0x38, 0x2b, 0x6b, 0x65, 0xb8, 0x81,
	0x67, 0x4b, 0xd7, 0xa7, 0xc3, 0x60, 0xbf, 0xe7, 0x3d, 0x9b, 0xfb, 0xdc, 0xaa, 0xfd, 0xc5, 0x14,
	0x94, 0x92, 0x71, 0xcc, 0x37, 0xb6, 0x07, 0xa0, 0x7e, 0xc9, 0xba, 0x6f, 0xf0, 0xab, 0x64, 0x74,
	0x50, 0x49, 0xcd, 0xf0, 0x5b, 0xe6, 0xe3, 0x64, 0xe5, 0x44, 0x5b, 0xc5, 0x2f, 0x63, 0x10, 0x86,
	0xcc, 0x1a, 0x52, 0x33, 0xa0, 0xdc, 0x3a, 0x8a, 0xc8, 0xd2, 0x33, 0x90, 0x55, 0xb0, 0xa0, 0xee,
	0x79, 0x7d, 0x89, 0xcc, 0x8a, 0x41, 0xb4, 0x1d, 0x80, 0x88, 0xea, 0xbe, 0x0a, 0x39, 0xbc, 0x00,
	0x34, 0xd0, 0x89, 0x41, 0x5d, 0x60, 0x80, 0x8a, 0x5e, 0x2d, 0xb7, 0xaa, 0x86, 0x7e, 0x7c, 0x5c,
	0x57, 0x15, 0x76, 0xc8, 0x56, 0x27, 0x7b, 0x38, 0x63, 0x0b, 0x8d, 0x1d, 0x0a, 0x53, 0x97, 0x1e,
	0x0a, 0xa7, 0xf7, 0xd0, 0x89, 0x9d, 0x62, 0x71, 0x4a, 0xf3, 0x70, 0x40, 0x9d, 0x1c, 0xdc, 0x8c,
	0x7e, 0xc4, 0x9b, 0x4a, 0xbd, 0xa8, 0xa9, 0x69, 0x25, 0xa7, 0x27, 0x44, 0x2e, 0xdf, 0x86, 0x0f,
	0x46, 0x8e, 0x4d, 0xe7, 0x59, 0xbd, 0xef, 0xc1, 0xfa, 0x99, 0x87, 0x41, 0x22, 0x68, 0xef, 0x92,
	0x67, 0x74, 0xde, 0x11, 0xf5, 0x4c, 0xa0, 0x15, 0x8a, 0x9d, 0xad, 0x3d, 0x86, 0x3c, 0x36, 0x51,
	0xf1, 0xdc, 0x80, 0xba, 0xc1, 0xcb, 0x10, 0x77, 0x03, 0x96, 0xd0, 0x77, 0x50, 0xda, 0x84, 0x30,
	0xa1, 0x3d, 0x86, 0x35, 0x39, 0x86, 0xe6, 0xa8, 0xdd, 0xf7, 0xec, 0x51, 0x0f, 0xbd, 0x61, 0xba,
	0xac, 0x35, 0xd6, 0x31, 0xd6, 0x9c, 0x18, 0x49, 0xc2, 0xe9, 0x39, 0xd2, 0x27, 0x3d, 0xdf, 0x8d,
	0xa4, 0xb4, 0x3f, 0xe6, 0xf2, 0x6d, 0x82, 0x40, 0xf3, 0xad, 0x90, 0xf9, 0x88, 0x34, 0xdd, 0xed,
	0xf4, 0xdc, 0xdd, 0x26, 0x9f, 0x22, 0x09, 0x39, 0x1d, 0x84, 0xbb, 0x98, 0x36, 0x53, 0xd5, 0x0c,
	0x29, 0xa6, 0x8f, 0x2b, 0x69, 0xbf, 0xe0, 0x9b, 0x31, 0x93, 0x82, 0x2d, 0xb3, 0x3d, 0xbf, 0x59,
	0xec, 0x10, 0x56, 0xc5, 0xb5, 0x31, 0x75, 0x83, 0xa1, 0xe9, 0x5a, 0xd2, 0x34, 0x73, 0x7b, 0xc6,
	0xd5, 0x71, 0x55, 0x14, 0xd3, 0x8b, 0x9d, 0x58, 0xfa, 0x85, 0x12, 0x9e, 0xbc, 0x0f, 0xd7, 0x1c,
	0xdf, 0x40, 0x83, 0x79, 0xdf, 0x71, 0xbd, 0xa1, 0x13, 0x5c, 0xa0, 0x35, 0x4e, 0x5c, 0x6c, 0x10,
	0xc7, 0x6f, 0x50, 0x6a, 0xd7, 0x45, 0x16, 0x93, 0x79, 0xda, 0x08, 0x67, 0x75, 0x62, 0x70, 0xf3,
	0xca, 0xf4, 0xd8, 0xdd, 0xe1, 0xb4, 0xa2, 0x20, 0xb0, 0x47, 0x6d, 0x5c, 0x3f, 0x4b, 0x41, 0x2e,
	0x02, 0x26, 0xa7, 0xb0, 0x61, 0x79, 0xfd, 0xbe, 0xe7, 0x1a, 0xe1, 0x35, 0x62, 0xa7, 0x67, 0x76,
	0x67, 0x87, 0x4f, 0x61, 0xe1, 0xf0, 0x2a, 0xb1, 0x67, 0x76, 0x75, 0x62, 0x4d, 0xc1, 0x18, 0x07,
	0x8d, 0x2f, 0x5a, 0xd9, 0xe9, 0x29, 0x35, 0x83, 0x83, 0xe4, 0xbd, 0x29, 0x1e, 0x9f, 0x42, 0x47,
	0x65, 0x71, 0x02, 0x03, 0x0c, 0x2c, 0xe2, 0x08, 0x66, 0xc9, 0x60, 0x31, 0x18, 0x0c, 0x23, 0xe2,
	0x21, 0x1c, 0x7d, 0xf9, 0x97, 0x7c, 0x00, 0x99, 0xbe, 0xf3, 0x9c, 0xd7, 0xe7, 0x2c, 0x38, 0xed,
	0x9a, 0x5d, 0x77, 0x9e, 0x63, 0xb5, 0x95, 0x3e, 0xff, 0xa3, 0xfd, 0xaf, 0x14, 0xe4, 0xa3, 0xbd,
	0x9a, 0xbc, 0x34, 0x56, 0xa6, 0x2e, 0x8d, 0xa3, 0x3e, 0xfb, 0x11, 0xb5, 0x2f, 0x1c, 0x0d, 0xea,
	0x4f, 0xbf, 0x06, 0x24, 0x46, 0x11, 0x6e, 0x11, 0xe7, 0x67, 0xb0, 0x9d, 0x4b, 0xc9, 0x12, 0x4b,
	0xe0, 0x5c, 0xaa, 0xd6, 0x04, 0x84, 0x29, 0x96, 0x96, 0xe9, 0x46, 0x9d, 0x08, 0x16, 0x65, 0xdc,
	0x80, 0x1b, 0xfa, 0x10, 0x6c, 0xfe, 0x4c, 0x01, 0x75, 0x12, 0xd9, 0xaf, 0x8a, 0x03, 0xbe, 0x05,
	0xc0, 0x14, 0x50, 0x9b, 0x06, 0xa6, 0xd3, 0x13, 0xd3, 0x7f, 0x6d, 0x5a, 0x2d, 0x6c, 0x99, 0x6d,
	0x3d, 0x1b, 0x98, 0xed, 0x3d, 0x2c, 0xa7, 0x3d, 0x04, 0x75, 0x72, 0x42, 0xc9, 0x0d, 0x58, 0x61,
	0xb4, 0x1a, 0x5b, 0x5e, 0x97, 0x59, 0x92, 0xbb, 0x07, 0x4f, 0x0c, 0x3a, 0x35, 0x3d, 0x68, 0xed,
	0xdf, 0xa5, 0x60, 0x45, 0x4c, 0x72, 0xc4, 0xf9, 0x21, 0x8b, 0xce, 0x0f, 0xf2, 0x5a, 0x3b, 0x15,
	0xb9, 0xd6, 0xae, 0x08, 0xa6, 0x19, 0x4f, 0xcf, 0xdd, 0x59, 0x4c, 0xc3, 0x7e, 0x43, 0x7a, 0xa2,
	0x91, 0x9a, 0x31, 0xd1, 0xd5, 0xe7, 0xe3, 0x9f, 0x28, 0xb0, 0x3a, 0x81, 0xe2, 0x57, 0x35, 0x1d,
	0x11, 0x22, 0xa6, 0x62, 0x44, 0x8c, 0xcf, 0x53, 0xfa, 0x8a, 0xf3, 0xf4, 0x04, 0xf2, 0x27, 0x3d,
	0xf3, 0x42, 0x3a, 0x4d, 0xce, 0xd8, 0x46, 0xc7, 0xbe, 0x5f, 0xa9, 0x2b, 0xfb, 0x7e, 0x69, 0xff,
	0x85, 0x8b, 0xfd, 0x28, 0x7a, 0xff, 0x15, 0xf8, 0x07, 0xcc, 0x72, 0x4a, 0xf8, 0x49, 0xa8, 0x9c,
	0x5f, 0x07, 0xd2, 0x3c, 0x3e, 0xd5, 0x2b, 0x55, 0x23, 0x7e, 0x8f, 0xbf, 0x05, 0x37, 0x04, 0x5c,
	0x78, 0xa2, 0x1a, 0x47, 0xb5, 0x66, 0xcb, 0x68, 0x54, 0x1f, 0xab, 0x0a, 0x79, 0x1d, 0x36, 0x93,
	0x32, 0xcb, 0x95, 0x56, 0xed, 0x51, 0x55, 0x4d, 0x45, 0x2a, 0xef, 0x57, 0xab, 0x7b, 0xbb, 0xe5,
	0xca, 0x03, 0xa3, 0xd6, 0xe0, 0xea, 0x5e, 0x5a, 0xfb, 0x0f, 0x7c, 0x6b, 0x9f, 0x18, 0xea, 0x7c,
	0x9b, 0xc0, 0xf7, 0x21, 0xfb, 0xa5, 0xac, 0x2b, 0xc8, 0x9c, 0x10, 0x04, 0x16, 0x69, 0x41, 0x1f,
	0x97, 0x8f, 0xb8, 0x84, 0xa6, 0xaf, 0xe6, 0x12, 0x7a, 0x17, 0x54, 0xa6, 0x42, 0x30, 0xdd, 0x2e,
	0x8c, 0x7d, 0xe2, 0x87, 0xc1, 0x22, 0xd3, 0x4d, 0x18, 0x58, 0xb8, 0xca, 0xfd, 0x91, 0x02, 0x64,
	0x9a, 0x37, 0x19, 0x02, 0x3c, 0x63, 0x86, 0xcc, 0x1d, 0x4a, 0xd4, 0x22, 0x83, 0xcb, 0xb2, 0x18,
	0x52, 0xb3, 0xc6, 0xc5, 0x7f, 0xb4, 0x28, 0xe7, 0xdd, 0x55, 0xcc, 0x88, 0x94, 0xad, 0x81, 0x2a,
	0x83, 0x04, 0x18, 0x93, 0x47, 0xa2, 0x8e, 0x66, 0xed, 0xf5, 0x6c, 0xad, 0xa1, 0xab, 0x98, 0xd8,
	0xeb, 0x65, 0x5a, 0xfb, 0xeb, 0x29, 0x80, 0x88, 0xef, 0xd9, 0xaf, 0x68, 0x39, 0x5e, 0xc1, 0x3c,
	0xf4, 0x29, 0xac, 0x32, 0x3e, 0x8e, 0x8c, 0x4b, 0xac, 0xce, 0x52, 0xc2, 0x75, 0x06, 0xdf, 0xe3,
	0x0b, 0x81, 0xd9, 0x8e, 0xf4, 0xfd, 0x18, 0x36, 0xc2, 0x2d, 0x27, 0x8a, 0x66, 0xf1, 0x45, 0x7b,
	0x31, 0xc3, 0x15, 0xee, 0x56, 0x63, 0x84, 0xda, 0x1f, 0x2a, 0xb0, 0x1a, 0x2e, 0x58, 0x0e, 0x27,
	0xdf, 0x00, 0xc2, 0x6d, 0xb7, 0xb1, 0xf1, 0x70, 0x31, 0xa0, 0x62, 0xce, 0x5e, 0x64, 0x50, 0x2f,
	0x23, 0x11, 0xc8, 0x4d, 0xc8, 0xa0, 0x0f, 0xc4, 0x99, 0x17, 0x88, 0xb8, 0x84, 0x15, 0x96, 0x3e,
	0xf4, 0x02, 0x14, 0x6b, 0xbe, 0x31, 0xb4, 0xfa, 0xb6, 0x50, 0xb5, 0x96, 0x1d, 0x5f, 0xb7, 0xfa,
	0xb6, 0xf6, 0x57, 0x17, 0xd1, 0x55, 0x13, 0xe9, 0x30, 0xc3, 0x16, 0x12, 0x8d, 0xe4, 0x4c, 0xc5,
	0x23, 0x39, 0xe7, 0x5e, 0x17, 0xe3, 0x71, 0x2d, 0x5e, 0x7d, 0x5c, 0xd7, 0x61, 0x99, 0x47, 0xf6,
	0xa0, 0xd5, 0x24, 0xa3, 0x8b, 0x14, 0x39, 0x82, 0xb5, 0xb1, 0xb3, 0x83, 0x98, 0xb9, 0xd2, 0x32,
	0xe2, 0xbd, 0x93, 0x78, 0xef, 0x16, 0x99, 0x0f, 0x7d, 0xb5, 0x3b, 0x31, 0x41, 0xef, 0xc3, 0x35,
	0x14, 0xf0, 0x62, 0x7a, 0x9c, 0x3e, 0x5b, 0xc0, 0xa3, 0x61, 0x4f, 0x18, 0x52, 0x08, 0x13, 0xea,
	0x3c, 0xaf, 0xc6, 0xb2, 0x4e, 0x87, 0x3d, 0x52, 0x07, 0x82, 0x1d, 0xf8, 0x89, 0xe7, 0xf2, 0xfb,
	0xe2, 0xf0, 0x4e, 0xa0, 0x38, 0xa3, 0x07, 0x5f, 0x78, 0x2e, 0x95, 0xce, 0xdd, 0xd8, 0x83, 0x08,
	0x60, 0x3a, 0xa6, 0x31, 0x3b, 0x1d, 0xd3, 0x98, 0xe4, 0xa7, 0x0b, 0x2f, 0x1d, 0x55, 0x14, 0x09,
	0x68, 0xcb, 0x45, 0x03, 0xda, 0xb4, 0xdf, 0x84, 0xb5, 0xa9, 0xda, 0xd8, 0x3f, 0xd9, 0x74, 0x84,
	0x7b, 0xf3, 0x12, 0x88, 0x2c, 0xb1, 0x37, 0xdd, 0xbf, 0x2b, 0xb0, 0xf0, 0xa4, 0x03, 0xf1, 0xdf,
	0x4c, 0x01, 0x91, 0x41, 0xef, 0x91, 0x95, 0x1a, 0x8b, 0x82, 0x52, 0x26, 0xa2, 0xa0, 0xe6, 0xf6,
	0xdb, 0x1f, 0x33, 0x63, 0xfa, 0xea, 0xcc, 0xf8, 0x3a, 0xe4, 0x1c, 0x5f, 0xb0, 0x9c, 0xf0, 0xeb,
	0xc0, 0x8b, 0x58, 0x2c, 0x75, 0xec, 0x26, 0xcd, 0xcf, 0xd2, 0xab, 0x98, 0x9f, 0xe5, 0xd8, 0xfc,
	0xfc, 0x9e, 0x12, 0x51, 0xbe, 0x19, 0x65, 0x5e, 0x8d, 0xf2, 0xfd, 0x21, 0x64, 0xb1, 0x3d, 0x3c,
	0x2d, 0x71, 0xa2, 0xcc, 0x96, 0xa2, 0x4c, 0x2a, 0xa0, 0x3f, 0x81, 0xf6, 0x2f, 0x16, 0xd1, 0xe9,
	0x6e, 0x3c, 0x51, 0xbb, 0x17, 0xe1, 0x19, 0xf1, 0x15, 0xfb, 0x67, 0x3c, 0x04, 0x22, 0xfc, 0x33,
	0xe2, 0x02, 0x3f, 0x7d, 0xd5, 0x5d, 0x46, 0xe5, 0xd5, 0x23, 0x4c, 0x95, 0x70, 0xfe, 0x5d, 0x7c,
	0x25, 0xe7, 0xdf, 0xa5, 0xa9, 0xf3, 0x6f, 0x0d, 0x54, 0x49, 0xdf, 0x30, 0xa4, 0x62, 0x79, 0x46,
	0x5b, 0x82, 0xcc, 0x22, 0xce, 0x42, 0x2f, 0x06, 0xb1, 0x34, 0xe9, 0xc1, 0x5a, 0x2c, 0x80, 0xc8,
	0xe8, 0x9b, 0x83, 0xd2, 0x0a, 0xd2, 0xe1, 0xd3, 0x24, 0xdb, 0xec, 0x8c, 0xc9, 0x89, 0x86, 0x17,
	0xf9, 0x75, 0x73, 0xc0, 0xb2, 0x2e, 0xf4, 0xd5, 0x76, 0x1c, 0xba, 0x69, 0xc2, 0x46, 0x52, 0x41,
	0xa2, 0x42, 0xfa, 0x9c, 0x5e, 0x08, 0x76, 0x63, 0x7f, 0xc9, 0x07, 0xb0, 0xf4, 0xd4, 0xec, 0x8d,
	0x68, 0xf4, 0x24, 0x3b, 0x3b, 0xa0, 0x89, 0x97, 0xfd, 0x5e, 0xea, 0x3b, 0x8a, 0xf6, 0xdf, 0x15,
	0xd8, 0x9a, 0xd9, 0xcf, 0xf9, 0x74, 0xbd, 0xf7, 0xe3, 0x07, 0xfe, 0xad, 0x4b, 0x74, 0x9b, 0xb1,
	0x7f, 0xf0, 0xba, 0xe5, 0xf5, 0x07, 0x43, 0x7a, 0x46, 0x5d, 0x9f, 0xb1, 0x57, 0x74, 0x0d, 0x5c,
	0x8a, 0x80, 0xc4, 0xea, 0x71, 0x07, 0x9b, 0xc4, 0xc0, 0x8b, 0xc5, 0xc4, 0xc0, 0x0b, 0xed, 0xe7,
	0x0a, 0xdc, 0x6c, 0xd2, 0x60, 0xaf, 0xf6, 0x44, 0xfa, 0x48, 0xbf, 0xcc, 0xda, 0x79, 0x75, 0x46,
	0x9c, 0xef, 0x4a, 0xe2, 0xcd, 0xb1, 0xa8, 0x84, 0xd1, 0xe4, 0x2f, 0x2b, 0xb0, 0x39, 0x6b, 0x28,
	0xf3, 0xcd, 0xe0, 0x1d, 0xc8, 0x3b, 0xbe, 0xe1, 0xb8, 0x86, 0xd9, 0x0e, 0xa8, 0xcf, 0xef, 0xba,
	0x33, 0x3a, 0x38, 0x7e, 0xcd, 0x2d, 0x23, 0x84, 0xbc, 0x03, 0x2a, 0xda, 0x91, 0xf0, 0xc2, 0x23,
	0x76, 0x49, 0x84, 0x71, 0x3d, 0x9f, 0x8d, 0xfa, 0x03, 0x7e, 0x6d, 0xfd, 0xf7, 0x14, 0xee, 0x4d,
	0xfd, 0x27, 0x85, 0xbe, 0x2f, 0xbc, 0x06, 0xf9, 0x75, 0xee, 0xc1, 0xfc, 0xd5, 0x89, 0xf8, 0x01,
	0x64, 0x6c, 0x47, 0xb8, 0x14, 0xa6, 0x66, 0x08, 0xf3, 0xbd, 0xda, 0x13, 0x7e, 0x22, 0xb7, 0x1d,
	0xee, 0x32, 0xd8, 0x83, 0x15, 0x01, 0x23, 0x9f, 0x44, 0xea, 0xcf, 0xa1, 0xc7, 0x4b, 0x54, 0x57,
	0x50, 0xde, 0xb5, 0xdf, 0x57, 0xe0, 0xb5, 0x03, 0x1a, 0x34, 0x68, 0xd7, 0x44, 0xa9, 0x4c, 0xa9,
	0xbd, 0x6b, 0x5a, 0xe7, 0x35, 0x57, 0xf7, 0xbc, 0xfe, 0x3c, 0xf3, 0x53, 0x83, 0x62, 0x87, 0xb1,
	0x43, 0xdb, 0xb4, 0xce, 0xa3, 0xee, 0x65, 0xd3, 0x3d, 0x96, 0x6d, 0x30, 0x6d, 0x4a, 0xb4, 0x93,
	0xef, 0x44, 0x60, 0xda, 0x6f, 0x29, 0x70, 0xeb, 0x92, 0x3e, 0xcd, 0x37, 0x07, 0x25, 0x58, 0x19,
	0x52, 0xd3, 0x97, 0x87, 0xce, 0xac, 0x2e, 0x93, 0xe4, 0x16, 0x40, 0xdf, 0xf4, 0xcf, 0x63, 0x4e,
	0x2b, 0x59, 0x06, 0x41, 0xeb, 0xb9, 0xf6, 0xf3, 0x14, 0xdc, 0xe6, 0xa1, 0xb2, 0x7f, 0x92, 0x88,
	0xc3, 0xe6, 0x94, 0x07, 0x00, 0xc7, 0x9e, 0xe8, 0xc9, 0x49, 0xd8, 0xa9, 0x83, 0x77, 0xa7, 0xed,
	0x1e, 0x6b, 0x69, 0x14, 0x7a, 0x99, 0x64, 0x10, 0xc0, 0x32, 0x23, 0xf4, 0x58, 0x8a, 0xd3, 0xe3,
	0x35, 0x18, 0x3b, 0x94, 0xca, 0x00, 0x82, 0xb1, 0x87, 0xe9, 0x9b, 0x50, 0x10, 0x61, 0xff, 0x42,
	0xcf, 0xe7, 0xfe, 0xdd, 0x79, 0x0e, 0x14, 0x2a, 0xfc, 0xf8, 0x00, 0x93, 0x89, 0xba, 0xa1, 0xfe,
	0x4c, 0x81, 0x3b, 0x97, 0x93, 0x72, 0xbe, 0x39, 0xbd, 0x09, 0x99, 0x33, 0x27, 0x88, 0x7a, 0xab,
	0xac, 0x9c, 0x39, 0x01, 0xf2, 0xce, 0x87, 0xb0, 0x72, 0xc8, 0xff, 0x92, 0x12, 0x6c, 0x1c, 0xd6,
	0x5a, 0x46, 0xeb, 0xc9, 0xc9, 0xa4, 0xa1, 0x44, 0x85, 0x7c, 0x98, 0x83, 0x31, 0x0f, 0xda, 0x9f,
	0x55, 0xa0, 0x74, 0x40, 0x03, 0xe1, 0x19, 0xcb, 0xbd, 0xe3, 0xe7, 0xbe, 0xe1, 0x11, 0x2f, 0x95,
	0xa4, 0xc2, 0x97, 0x4a, 0x66, 0x79, 0xee, 0xc7, 0x42, 0x40, 0x16, 0xe3, 0x21, 0x20, 0xda, 0x3f,
	0xe7, 0xc1, 0x27, 0x49, 0x5d, 0x99, 0x8f, 0x4a, 0x2d, 0xd8, 0x18, 0x70, 0x24, 0x22, 0x42, 0xc0,
	0xe0, 0xa1, 0x5f, 0xb3, 0x42, 0xdf, 0x62, 0x2d, 0x62, 0xe4, 0x17, 0x19, 0x4c, 0x82, 0x7c, 0x72,
	0x0c, 0x6b, 0xf8, 0x58, 0x52, 0x0c, 0xe5, 0xac, 0x9d, 0x2a, 0xfe, 0x7a, 0x11, 0xe2, 0x5c, 0x65,
	0xb5, 0xa3, 0x08, 0xeb, 0xa0, 0x22, 0x42, 0xc7, 0x1d, 0x8c, 0x02, 0x81, 0x6f, 0xf1, 0xea, 0xf8,
	0x8a, 0xac, 0x72, 0x8d, 0xd5, 0x45, 0x74, 0x5a, 0x07, 0xd6, 0xa6, 0x06, 0xf2, 0x52, 0x6f, 0xc3,
	0xdc, 0x02, 0xc0, 0x8c, 0x98, 0x78, 0x60, 0x10, 0x2e, 0x1e, 0xfe, 0x5c, 0x0a, 0xc8, 0x74, 0x77,
	0x42, 0x17, 0x7b, 0x25, 0xe2, 0x62, 0xdf, 0x82, 0xf5, 0x84, 0x89, 0x10, 0xfa, 0xd9, 0x15, 0xe6,
	0xe1, 0x70, 0x41, 0x5f, 0x9b, 0x9a, 0x09, 0xf2, 0x31, 0x40, 0xe4, 0x01, 0x0e, 0x6e, 0x71, 0xb9,
	0xec, 0x00, 0x75, 0xb8, 0xa0, 0x67, 0xc3, 0x23, 0xb7, 0x56, 0x81, 0x45, 0xe1, 0xc8, 0xab, 0x26,
	0x2f, 0x10, 0x84, 0x8a, 0xae, 0xa8, 0x0a, 0x59, 0x87, 0x55, 0x84, 0xb4, 0xce, 0x9c, 0x21, 0x7f,
	0xf7, 0x4a, 0x4d, 0xed, 0x2e, 0x73, 0xb7, 0x23, 0xed, 0x6f, 0x28, 0xb0, 0xde, 0xa0, 0xcf, 0xa6,
	0x2e, 0x84, 0xe7, 0xb7, 0x67, 0x24, 0xfb, 0x15, 0xbf, 0xe8, 0x16, 0x98, 0x6c, 0x42, 0x46, 0xbe,
	0xda, 0x24, 0xe2, 0xa2, 0xc2, 0xb4, 0xf6, 0x05, 0x2e, 0xa7, 0x84, 0xee, 0x7d, 0x75, 0x5b, 0xad,
	0xf6, 0xdb, 0x3c, 0xd8, 0x29, 0x11, 0xf9, 0x7c, 0x8b, 0xf5, 0xe3, 0xd0, 0x31, 0x25, 0x35, 0xe3,
	0x75, 0xba, 0xa4, 0x46, 0x44, 0x1d, 0xd6, 0x0f, 0x36, 0xc8, 0x68, 0x64, 0x42, 0xa5, 0xc3, 0x3d,
	0xfd, 0x5e, 0x5d, 0x8c, 0x41, 0x24, 0x56, 0x28, 0xb4, 0x5b, 0x16, 0xc2, 0x58, 0x21, 0x94, 0xbe,
	0xff, 0x6d, 0x05, 0xe9, 0x91, 0xd8, 0x8f, 0xf9, 0xe8, 0xf1, 0x72, 0x21, 0x51, 0x51, 0xce, 0x5a,
	0x8c, 0x73, 0x56, 0xa8, 0xd0, 0x46, 0x1c, 0xd0, 0x85, 0x42, 0x7b, 0xc0, 0x1d, 0x00, 0xde, 0x82,
	0x22, 0x23, 0xb4, 0xe3, 0x76, 0x65, 0x7c, 0x3c, 0x3f, 0xf2, 0x17, 0x04, 0x54, 0xc4, 0xc6, 0x7f,
	0x06, 0xaa, 0xff, 0xcc, 0x09, 0xac, 0x33, 0x1e, 0x34, 0x14, 0xc6, 0x3a, 0x25, 0x3a, 0x13, 0x35,
	0xb1, 0xe4, 0x49, 0xcf, 0xe4, 0xf7, 0x86, 0x45, 0x3f, 0x96, 0x26, 0x8d, 0xc8, 0x0b, 0x05, 0xdc,
	0x6a, 0xf5, 0x41, 0xd2, 0xd1, 0x71, 0x06, 0x3d, 0xa7, 0x5f, 0x29, 0x78, 0x03, 0xf2, 0xcf, 0x68,
	0xcf, 0xf2, 0xfa, 0x34, 0xea, 0xfc, 0x9a, 0x13, 0x30, 0x5c, 0x2a, 0x9f, 0x81, 0x1a, 0xb0, 0x35,
	0x6c, 0x0c, 0xcc, 0xa1, 0xbc, 0xfc, 0x85, 0x59, 0xdd, 0xc7, 0xd5, 0x7e, 0xc2, 0x0a, 0xa2, 0xb7,
	0x4d, 0x31, 0x88, 0xa5, 0xd1, 0x65, 0x91, 0x9a, 0x7d, 0xc3, 0xa6, 0xbe, 0x25, 0x9c, 0xdd, 0x33,
	0x0c, 0xb0, 0x47, 0x7d, 0x0b, 0xdf, 0xc4, 0x61, 0x47, 0xb0, 0x71, 0x89, 0x3c, 0xd7, 0x18, 0x18,
	0xb4, 0x25, 0x4b, 0xe1, 0x9b, 0x3e, 0xde, 0x33, 0x43, 0x0a, 0x48, 0xbe, 0xa8, 0x85, 0x2f, 0x3b,
	0x9e, 0xe1, 0x84, 0x0c, 0xe2, 0x51, 0x4c, 0xd3, 0x26, 0xba, 0x62, 0x82, 0x89, 0xee, 0x0e, 0xe4,
	0xfb, 0x8e, 0xeb, 0x48, 0x3b, 0x86, 0xf0, 0x4f, 0x87, 0xbe, 0xf0, 0x1a, 0xaa, 0xd9, 0xac, 0x04,
	0x66, 0x5a, 0xe6, 0xd0, 0x66, 0x25, 0x54, 0x5e, 0x82, 0xc1, 0x2a, 0xe6, 0xd0, 0x96, 0x1c, 0x86,
	0xce, 0x07, 0x6b, 0x92, 0xc3, 0xba, 0x35, 0x9b, 0x34, 0xc7, 0x6f, 0x19, 0x8e, 0x63, 0xc6, 0xc8,
	0x95, 0x63, 0xc6, 0x76, 0x53, 0x25, 0x25, 0x7c, 0xf4, 0x30, 0x0c, 0x1a, 0xfb, 0x36, 0x94, 0xc2,
	0xf7, 0x43, 0xe5, 0xe9, 0xc6, 0xf0, 0x03, 0x33, 0x18, 0xf9, 0xf8, 0xfc, 0x5c, 0x41, 0xbf, 0xc6,
	0xf2, 0x4f, 0xcc, 0x9e, 0x3c, 0x89, 0x34, 0x31, 0x93, 0x3c, 0x84, 0x8d, 0x8e, 0xe9, 0x07, 0xc6,
	0xc0, 0x32, 0xac, 0x8e, 0x70, 0x1d, 0x66, 0xfc, 0xb8, 0x31, 0xe3, 0x0e, 0x7a, 0xdf, 0xf4, 0x83,
	0x13, 0xab, 0xd2, 0x41, 0x87, 0x62, 0xc6, 0x45, 0x6a, 0x67, 0x02, 0x32, 0xf7, 0xcb, 0x01, 0xff,
	0x27, 0x05, 0xea, 0x24, 0x5a, 0x62, 0x8f, 0xfb, 0x25, 0x27, 0x16, 0x39, 0x80, 0xbb, 0x26, 0x7f,
	0xf0, 0xc2, 0x7e, 0x09, 0x80, 0x98, 0x7a, 0xc6, 0x28, 0xfa, 0x5a, 0x67, 0x12, 0x44, 0x3e, 0x84,
	0x1b, 0xa8, 0x46, 0x45, 0xbc, 0xb7, 0x9d, 0x3e, 0xf7, 0x44, 0xe5, 0xdb, 0xca, 0x06, 0xcb, 0x1e,
	0x07, 0x5e, 0xd4, 0xfa, 0xe8, 0x98, 0x9a, 0x14, 0x75, 0x96, 0xbe, 0x6a, 0xd4, 0xd9, 0xe2, 0x7c,
	0x51, 0x67, 0x4b, 0x33, 0xa3, 0xce, 0x36, 0xf7, 0x61, 0x6d, 0x6a, 0x94, 0xb3, 0xbd, 0x7c, 0x50,
	0x15, 0x89, 0xba, 0x6a, 0x32, 0x00, 0xea, 0x8b, 0xbf, 0x50, 0xa0, 0xd4, 0xa4, 0x41, 0x42, 0x7c,
	0xe2, 0xab, 0x13, 0xfd, 0xc7, 0x49, 0x5c, 0x9f, 0xbe, 0x7a, 0xa4, 0xe4, 0x14, 0xc7, 0x0b, 0x55,
	0x79, 0x31, 0x49, 0x55, 0x5e, 0x8a, 0xee, 0xb0, 0xe7, 0x68, 0x9b, 0x49, 0x1a, 0xdc, 0x7c, 0xfb,
	0xc9, 0x9b, 0x90, 0x0b, 0x3c, 0xc6, 0x92, 0x11, 0x57, 0x29, 0x5c, 0x8c, 0x80, 0x60, 0xae, 0xd3,
	0x75, 0xa0, 0xd4, 0xba, 0x18, 0x38, 0x6e, 0x97, 0x2f, 0xaf, 0xdd, 0xa1, 0x67, 0xda, 0x96, 0x39,
	0x5f, 0xd4, 0xf7, 0xf4, 0x21, 0x40, 0x85, 0xb4, 0x35, 0x7e, 0xc0, 0xd0, 0x72, 0x6c, 0x6d, 0x1f,
	0x6e, 0xce, 0x68, 0x67, 0xbe, 0x88, 0xbc, 0x11, 0xee, 0xb6, 0x13, 0x94, 0x99, 0xfb, 0xd8, 0x52,
	0x82, 0x15, 0x69, 0x0a, 0x15, 0x87, 0x29, 0x91, 0x9c, 0x15, 0x55, 0xf8, 0x5b, 0xdc, 0x50, 0x98,
	0xdc, 0xee, 0xdc, 0xa7, 0xf3, 0x19, 0x6d, 0xdf, 0x86, 0x1c, 0x77, 0xed, 0xe2, 0xb6, 0x3b, 0x61,
	0xa5, 0x41, 0x10, 0x37, 0xdb, 0x7d, 0x1b, 0xd6, 0x98, 0x94, 0xe2, 0x24, 0x3c, 0x19, 0xf9, 0x67,
	0x75, 0xbf, 0x2b, 0xa9, 0xaf, 0xe0, 0xc6, 0x1d, 0xa5, 0x7e, 0x6a, 0x4c, 0xfd, 0xdf, 0x51, 0x60,
	0x6b, 0x9a, 0xa1, 0x2a, 0xb8, 0xf4, 0x19, 0x8e, 0xfd, 0xb1, 0x9e, 0x83, 0x7c, 0xae, 0x5c, 0x9d,
	0xcf, 0xa5, 0x32, 0x84, 0x3c, 0x2e, 0xec, 0x4c, 0x5d, 0x6a, 0x04, 0x8e, 0x58, 0xb7, 0x8b, 0xdc,
	0xce, 0xd4, 0xa5, 0x2d, 0xa7, 0x4f, 0xb5, 0x3f, 0xc0, 0x23, 0x04, 0x7f, 0xcc, 0xcb, 0xe9, 0x5d,
	0xb4, 0x4c, 0xff, 0xfc, 0xab, 0x71, 0xda, 0xf7, 0x21, 0x1b, 0xa0, 0x45, 0x63, 0x7c, 0xad, 0x3c,
	0xed, 0x40, 0x1b, 0xb6, 0x85, 0xaa, 0x43, 0x26, 0x10, 0xff, 0xbe, 0x42, 0x60, 0x0a, 0xdb, 0x94,
	0xfd, 0xc0, 0xbc, 0x18, 0x8b, 0x3d, 0xe1, 0x1b, 0xcd, 0x80, 0xa1, 0x8c, 0xdc, 0x12, 0xaf, 0x55,
	0x63, 0xe7, 0xf8, 0x33, 0x46, 0xf8, 0xfa, 0x34, 0x36, 0x7e, 0x0b, 0x00, 0x33, 0x79, 0x08, 0x12,
	0x37, 0x1e, 0x60, 0x71, 0x7c, 0x6c, 0x54, 0xfb, 0x14, 0xd6, 0xa7, 0x08, 0x35, 0xdf, 0x52, 0xf9,
	0x10, 0x72, 0x8c, 0x65, 0x4d, 0xeb, 0x6c, 0x9e, 0xb5, 0xa1, 0xdd, 0x82, 0x2c, 0xd6, 0xc1, 0xd7,
	0xd6, 0x54, 0x48, 0x3b, 0xb6, 0x2f, 0x99, 0xcb, 0xb1, 0x7d, 0x6d, 0x1f, 0x8a, 0x98, 0x8d, 0xd7,
	0x5a, 0x58, 0xe6, 0x92, 0x93, 0xe6, 0x4d, 0xc8, 0x88, 0x93, 0xa6, 0xbc, 0x58, 0x59, 0xe1, 0x47,
	0x4d, 0x5f, 0xeb, 0x42, 0x1e, 0xf1, 0xb4, 0xcc, 0x36, 0x62, 0x99, 0x71, 0x70, 0xfa, 0x14, 0xf8,
	0x13, 0x9f, 0x18, 0x27, 0x26, 0x0f, 0xf2, 0x09, 0x4f, 0x76, 0xc7, 0xba, 0xa4, 0x43, 0x5b, 0xfe,
	0xf5, 0xb5, 0xff, 0xbb, 0x04, 0xf9, 0x31, 0x1d, 0xe6, 0x35, 0x09, 0x47, 0xdf, 0xe2, 0x94, 0x77,
	0x43, 0xd1, 0xe7, 0x39, 0x7f, 0x0c, 0x6b, 0x63, 0xe5, 0xcc, 0x6c, 0xf3, 0x2b, 0x91, 0xf4, 0x38,
	0x96, 0x7a, 0x52, 0xaf, 0x0d, 0xbb, 0x11, 0xde, 0xe8, 0xb7, 0xcc, 0x76, 0xe4, 0x12, 0xc4, 0x8a,
	0x43, 0xa3, 0xb7, 0x71, 0x8b, 0xb1, 0xe7, 0x3f, 0xdf, 0x01, 0xb5, 0xcf, 0x54, 0x7b, 0xa6, 0x44,
	0xc9, 0x12, 0x4b, 0x5c, 0x79, 0xef, 0x3b, 0x2e, 0xd3, 0xf9, 0x44, 0xdc, 0xf0, 0x49, 0xfc, 0x8e,
	0x59, 0x4e, 0x95, 0x2f, 0x6e, 0xad, 0x6f, 0x25, 0xd3, 0x52, 0x4c, 0x4b, 0xf4, 0x0a, 0x9a, 0x3f,
	0x06, 0x64, 0xfb, 0xa4, 0x0c, 0x05, 0x86, 0x71, 0x8c, 0x69, 0xe5, 0x2a, 0x98, 0x72, 0x81, 0xd9,
	0x0e, 0x51, 0x7c, 0x06, 0x04, 0x51, 0xb0, 0x79, 0x18, 0xe3, 0xc9, 0x5c, 0x05, 0xcf, 0x2a, 0xc3,
	0x63, 0xfa, 0x34, 0xc4, 0x65, 0x88, 0x1b, 0x71, 0x54, 0x6c, 0xc3, 0x39, 0xc8, 0x5e, 0x65, 0x0e,
	0x0e, 0x84, 0xf2, 0x1b, 0x9f, 0x83, 0x6e, 0x1c, 0xba, 0xf9, 0x63, 0xd8, 0x48, 0x9a, 0xac, 0x84,
	0x8b, 0xa8, 0x6f, 0xc6, 0x2f, 0xa2, 0x36, 0x93, 0x47, 0x82, 0xc3, 0x18, 0xdf, 0x42, 0x31, 0xfc,
	0x49, 0x1d, 0x79, 0x55, 0xf8, 0xb5, 0x0e, 0xdc, 0x4e, 0x8a, 0x6f, 0x0e, 0xe8, 0x10, 0xdf, 0x88,
	0x98, 0x47, 0x00, 0xc7, 0x62, 0xad, 0x53, 0xf1, 0x58, 0x6b, 0xed, 0xcf, 0x28, 0x70, 0xe7, 0xf2,
	0x86, 0x5e, 0xe6, 0x42, 0xc6, 0x32, 0x5d, 0x11, 0xed, 0x1b, 0x5e, 0xc8, 0x54, 0x4c, 0x97, 0x47,
	0xfb, 0xaa, 0x90, 0xee, 0xfb, 0xf2, 0x3e, 0x83, 0xfd, 0xd5, 0xfe, 0x6d, 0x1a, 0x0d, 0xe9, 0x91,
	0xd7, 0x61, 0xa5, 0x1f, 0x74, 0xc7, 0xe9, 0xce, 0xe5, 0xab, 0x36, 0x88, 0xfa, 0x65, 0xf3, 0x7d,
	0x99, 0x55, 0xe1, 0x94, 0xff, 0x61, 0x22, 0x5f, 0xcd, 0x6c, 0x74, 0x67, 0xd2, 0x85, 0xfe, 0x70,
	0x61, 0xec, 0xda, 0x2d, 0x61, 0x9b, 0xbf, 0x9b, 0x02, 0x75, 0xca, 0xd7, 0x7e, 0x86, 0x5c, 0xdc,
	0x87, 0x3b, 0x8e, 0x6f, 0xf8, 0x66, 0xe0, 0xf8, 0x9d, 0x0b, 0x83, 0x62, 0x7c, 0x8a, 0x81, 0x3b,
	0x92, 0xe5, 0xb9, 0x36, 0x37, 0x0b, 0x71, 0x7a, 0xbd, 0xe6, 0xf8, 0x4d, 0x5e, 0x8c, 0x47, 0xb1,
	0x34, 0x03, 0xf3, 0xa2, 0x22, 0xcb, 0x90, 0xaf, 0x03, 0x11, 0x62, 0xc1, 0xb4, 0xce, 0x0d, 0x76,
	0x30, 0xa7, 0xbe, 0x8c, 0x04, 0x58, 0xe5, 0xab, 0xde, 0xb4, 0xce, 0xcb, 0xed, 0x96, 0xb8, 0xff,
	0x0a, 0x0f, 0x6d, 0xb2, 0x28, 0xb7, 0x39, 0x14, 0xc4, 0x61, 0x4d, 0x14, 0xfc, 0x04, 0x5e, 0x8b,
	0x45, 0x7d, 0x74, 0xba, 0xb2, 0x8b, 0x7c, 0xb7, 0xe3, 0xfa, 0x6e, 0x29, 0x12, 0xde, 0xd1, 0xe9,
	0xf2, 0xde, 0xe1, 0xe6, 0xb7, 0x9b, 0x83, 0x6c, 0x48, 0x71, 0xed, 0x97, 0x69, 0x78, 0xfd, 0x32,
	0xea, 0xce, 0xc7, 0x54, 0x3f, 0x86, 0x7c, 0xe8, 0x3f, 0x3f, 0xb6, 0x3d, 0x7d, 0x7f, 0xae, 0xf9,
	0x64, 0x92, 0x63, 0x0c, 0xd0, 0x73, 0xdd, 0x71, 0x62, 0xf3, 0x1f, 0xa5, 0x20, 0x17, 0xc9, 0x24,
	0x3f, 0x82, 0xeb, 0x2e, 0x7d, 0x66, 0x4c, 0x06, 0xc1, 0x38, 0xd2, 0x4d, 0xed, 0x4a, 0x56, 0xaf,
	0xc3, 0x05, 0x7d, 0xdd, 0x4d, 0xb0, 0x36, 0x7e, 0x0e, 0xd7, 0xd8, 0x5c, 0xa0, 0xe4, 0x4b, 0x18,
	0xd5, 0xb4, 0x02, 0x77, 0x62, 0xf6, 0x98, 0x98, 0x89, 0x74, 0xf0, 0x70, 0x41, 0x27, 0x83, 0x29,
	0x28, 0xb1, 0xe1, 0x26, 0x17, 0xaa, 0x3c, 0x64, 0x20, 0x8e, 0x9d, 0x5b, 0x5e, 0xdf, 0x49, 0xf4,
	0x36, 0x12, 0x31, 0x06, 0xf1, 0x16, 0xae, 0x77, 0x13, 0x73, 0x76, 0x33, 0xd2, 0x04, 0xa8, 0xfd,
	0x83, 0x34, 0x90, 0xe9, 0xce, 0xc5, 0xac, 0x9c, 0x5c, 0x09, 0x09, 0xd3, 0xe4, 0xc7, 0x50, 0x0c,
	0xb9, 0x31, 0x7a, 0xf5, 0xfe, 0x9d, 0x2b, 0x8c, 0x1a, 0xbb, 0x1a, 0x05, 0x73, 0x1f, 0x77, 0xc1,
	0xc5, 0xfc, 0x42, 0x7d, 0x5f, 0x3c, 0xaf, 0x20, 0xf1, 0x8b, 0x6d, 0x1d, 0xd5, 0xcb, 0x89, 0x6f,
	0x9f, 0x1c, 0x8c, 0xeb, 0xe1, 0x09, 0x2b, 0x17, 0x41, 0x44, 0x1e, 0x88, 0xfd, 0x09, 0x8b, 0x3b,
	0x7e, 0x20, 0x7d, 0xde, 0xaf, 0x80, 0x8c, 0xef, 0x45, 0x27, 0x26, 0xf2, 0x20, 0xeb, 0xe2, 0xe6,
	0x9f, 0x57, 0xf8, 0x66, 0x31, 0xd9, 0x77, 0xa6, 0x28, 0x48, 0xcb, 0x8e, 0xf0, 0xc4, 0xb6, 0xb8,
	0x55, 0x27, 0x12, 0xe7, 0x9e, 0x8a, 0x3f, 0x41, 0x5f, 0xc2, 0x07, 0x3f, 0xc2, 0x20, 0x92, 0xac,
	0x2e, 0x93, 0xe4, 0x2d, 0x28, 0x4a, 0x7d, 0x21, 0xe6, 0x48, 0x5a, 0x10, 0x50, 0xe1, 0x31, 0xf5,
	0xef, 0x53, 0x70, 0x3d, 0x79, 0xce, 0x2f, 0x9d, 0xb8, 0x63, 0x58, 0xea, 0x50, 0x6a, 0xcb, 0xf9,
	0xfa, 0xee, 0x15, 0xf9, 0x08, 0xc1, 0xfb, 0x94, 0x46, 0x26, 0x8c, 0xe3, 0xd9, 0xfc, 0x37, 0x0a,
	0xac, 0x4d, 0x65, 0x32, 0x8a, 0x0c, 0x3c, 0x46, 0xee, 0x90, 0x22, 0x2c, 0xc9, 0x29, 0x22, 0xc7,
	0x9d, 0x8a, 0x8f, 0xfb, 0x26, 0x64, 0x84, 0x39, 0xc5, 0x17, 0x5f, 0x29, 0x59, 0x71, 0xd0, 0x82,
	0xe2, 0x4f, 0x1f, 0x0a, 0x16, 0xa7, 0x0f, 0x05, 0x11, 0x52, 0x2f, 0xc5, 0x49, 0xbd, 0x05, 0x59,
	0x97, 0x09, 0x00, 0x34, 0x67, 0x2c, 0x8f, 0x9f, 0xfb, 0x6f, 0x44, 0x9e, 0xfb, 0x5f, 0x19, 0x3f,
	0xf7, 0xff, 0x10, 0x8a, 0x07, 0x34, 0x10, 0x96, 0xa1, 0xb9, 0x76, 0xad, 0x88, 0xbe, 0x98, 0x8a,
	0x79, 0x6f, 0xfd, 0x47, 0x05, 0x56, 0x63, 0x38, 0xe7, 0x8d, 0xe3, 0xcb, 0x4a, 0x53, 0x9b, 0x9c,
	0xb7, 0x9d, 0x44, 0xbb, 0x6d, 0x04, 0xff, 0x8e, 0x4c, 0x70, 0xbd, 0x0a, 0x7d, 0xb7, 0x58, 0x72,
	0x53, 0x87, 0x42, 0x2c, 0x2b, 0x41, 0xd3, 0xf9, 0x7a, 0x5c, 0xd3, 0x99, 0xe1, 0xf5, 0x1e, 0x51,
	0x72, 0xfe, 0x42, 0x1a, 0x0a, 0x42, 0xeb, 0x7d, 0xe9, 0xab, 0x98, 0xb7, 0xa0, 0x18, 0x37, 0xac,
	0x89, 0x75, 0x51, 0x68, 0x47, 0x0d, 0x6a, 0xe4, 0x2e, 0xa8, 0x8e, 0xe5, 0xb9, 0x46, 0x9b, 0x76,
	0x1d, 0x37, 0xf6, 0xc5, 0x9a, 0x22, 0x83, 0xef, 0x32, 0x30, 0x8f, 0xd8, 0xfe, 0x1a, 0x20, 0xc4,
	0xa0, 0xe1, 0x2b, 0x0b, 0x9c, 0x2f, 0xf2, 0x0c, 0x5a, 0x95, 0x4f, 0x2c, 0x6c, 0x41, 0x16, 0x4b,
	0x45, 0x22, 0xc3, 0x33, 0x0c, 0x80, 0xc6, 0xeb, 0x98, 0x4d, 0x19, 0x4b, 0x70, 0xdf, 0xd0, 0xd0,
	0xa6, 0x2c, 0x6f, 0x83, 0x04, 0xab, 0x71, 0x2d, 0x3a, 0xab, 0x87, 0x69, 0xf2, 0x3e, 0xac, 0x70,
	0x13, 0xb3, 0x2f, 0x94, 0xe2, 0x1b, 0x09, 0xcf, 0x0f, 0xb2, 0x7c, 0x5d, 0x96, 0x0b, 0xfd, 0x04,
	0x7a, 0xe6, 0x05, 0x1d, 0xca, 0x70, 0x70, 0x06, 0x39, 0x62, 0x00, 0x5c, 0x0b, 0xe6, 0xd0, 0xf6,
	0x43, 0xdb, 0x63, 0x4e, 0xac, 0x05, 0x06, 0xe4, 0x26, 0x47, 0xed, 0x33, 0x58, 0x16, 0xf6, 0xeb,
	0xc8, 0x55, 0xd6, 0xf8, 0x9a, 0x50, 0x5e, 0x65, 0x25, 0xbc, 0xb5, 0x90, 0x9a, 0x0a, 0x43, 0x7c,
	0x0a, 0xab, 0xe1, 0xbd, 0xa3, 0x98, 0xdc, 0x89, 0x07, 0x1c, 0x94, 0xa9, 0x07, 0x1c, 0xe4, 0xe3,
	0x38, 0xd2, 0xf7, 0x4d, 0x30, 0xd2, 0xeb, 0x89, 0xc2, 0x66, 0x1c, 0xe6, 0x85, 0x92, 0x5b, 0xb8,
	0xc1, 0x69, 0x9f, 0x60, 0x08, 0x40, 0xd8, 0xf4, 0xbc, 0xd1, 0xcb, 0xda, 0x6f, 0x62, 0x70, 0xf0,
	0x64, 0xfd, 0xf9, 0x16, 0xde, 0xf7, 0x26, 0x5f, 0x65, 0xba, 0x33, 0xfb, 0x8a, 0x58, 0x8c, 0x21,
	0x7c, 0x8e, 0xe9, 0xef, 0x2a, 0xb0, 0xae, 0x53, 0x7c, 0xc2, 0x6e, 0xec, 0x54, 0xfa, 0xd5, 0xc3,
	0x35, 0x5e, 0xca, 0x87, 0x35, 0xe6, 0x46, 0xbb, 0x18, 0x77, 0xa3, 0xd5, 0x9e, 0xc2, 0xc6, 0x74,
	0x3f, 0xe7, 0x75, 0x3c, 0x7a, 0x89, 0x78, 0x16, 0x3e, 0xc1, 0xcd, 0xd1, 0x60, 0xe0, 0x0d, 0x99,
	0xe8, 0x9a, 0xf3, 0xbd, 0x4b, 0xed, 0x47, 0xfc, 0xc5, 0xf8, 0x89, 0xfa, 0xf3, 0x75, 0x7c, 0xa6,
	0xc4, 0xd6, 0xf1, 0x61, 0x71, 0xa1, 0x84, 0xd6, 0x1d, 0xeb, 0x91, 0xd7, 0x6b, 0xd2, 0x79, 0x0d,
	0xb3, 0x13, 0x86, 0xc0, 0x7d, 0xd8, 0x98, 0x44, 0x28, 0xdd, 0xf0, 0xfb, 0x8e, 0x15, 0x91, 0x95,
	0x7d, 0xc7, 0xe2, 0x9e, 0x02, 0x7d, 0xf3, 0xb9, 0xf1, 0xd4, 0xeb, 0xc9, 0xc8, 0x9c, 0xbe, 0xf9,
	0xfc, 0x91, 0xd7, 0xd3, 0x7e, 0xae, 0xc0, 0x8d, 0xc4, 0xce, 0xcd, 0x37, 0xf6, 0x2a, 0xe4, 0x58,
	0xb3, 0x4f, 0xbd, 0x9e, 0xe1, 0x53, 0x19, 0x00, 0x3f, 0xf3, 0x5b, 0x1f, 0xb1, 0x2e, 0xeb, 0xd9,
	0xbe, 0x4c, 0x92, 0x77, 0x61, 0xcd, 0xbf, 0x70, 0x2d, 0xe3, 0x27, 0x74, 0xe8, 0xe1, 0xe7, 0x7c,
	0x18, 0x32, 0x1e, 0x8d, 0x50, 0x64, 0x19, 0x5f, 0xd0, 0xa1, 0x57, 0x77, 0x2c, 0x56, 0x54, 0x90,
	0x64, 0x71, 0x4c, 0x92, 0xbf, 0xaf, 0xc0, 0x7a, 0x73, 0x62, 0x28, 0xf3, 0x10, 0xf9, 0xff, 0xfb,
	0x18, 0xb4, 0x32, 0x6c, 0x4c, 0x77, 0x78, 0x3e, 0xdb, 0xe0, 0xef, 0x2b, 0x70, 0x6b, 0xb2, 0x47,
	0xdc, 0x1c, 0x2c, 0xcd, 0xca, 0x13, 0xc3, 0x52, 0x5e, 0xe5, 0xb0, 0x52, 0x89, 0xc3, 0xfa, 0xa9,
	0x02, 0xc5, 0xba, 0x63, 0x9d, 0xfa, 0x74, 0x58, 0x7d, 0x8e, 0xda, 0x2e, 0x39, 0x04, 0x30, 0x1d,
	0x23, 0xfa, 0x05, 0xa5, 0xa4, 0xcf, 0xec, 0xc5, 0x2b, 0xed, 0x94, 0x6b, 0x65, 0x5e, 0x41, 0xcf,
	0x9a, 0x8e, 0xf8, 0xbb, 0xf9, 0x7d, 0xc8, 0x86, 0x70, 0xb6, 0x5b, 0x3a, 0x36, 0x75, 0x03, 0x27,
	0xb8, 0x90, 0x1e, 0xfd, 0x32, 0x8d, 0xef, 0xca, 0x50, 0xdf, 0x92, 0x61, 0x87, 0xec, 0xff, 0x76,
	0x17, 0x0a, 0xb1, 0x4f, 0xa1, 0x11, 0x02, 0xc5, 0x10, 0x60, 0x34, 0x3c, 0x97, 0xaa, 0x0b, 0xe4,
	0x0d, 0xb8, 0x35, 0x86, 0x1d, 0x98, 0x6e, 0xf7, 0x74, 0xf0, 0xd8, 0x09, 0xce, 0xd8, 0x81, 0x1e,
	0x3f, 0xf5, 0xa6, 0x2a, 0xe4, 0x36, 0x6c, 0x8d, 0x8b, 0x54, 0xce, 0xcc, 0x20, 0x5e, 0x20, 0xb5,
	0xad, 0x43, 0x3e, 0xfa, 0x65, 0x2f, 0xb2, 0x01, 0x6a, 0x34, 0x2d, 0x5a, 0xba, 0x01, 0xeb, 0x51,
	0xe8, 0xc3, 0x91, 0xd9, 0x73, 0x82, 0x0b, 0xee, 0xb1, 0x12, 0xcd, 0x38, 0xf4, 0x02, 0x35, 0xb5,
	0x7d, 0x17, 0x56, 0xc4, 0x27, 0x8e, 0xc8, 0x0a, 0xa4, 0xcb, 0xbd, 0x9e, 0xba, 0x80, 0x5f, 0x66,
	0xa0, 0x7d, 0xb3, 0x47, 0xd5, 0x14, 0xc9, 0xc0, 0x62, 0x9d, 0xfd, 0x53, 0xb6, 0xff, 0x52, 0x0a,
	0xae, 0x27, 0x7f, 0xb0, 0x87, 0x0d, 0x98, 0xbf, 0xa7, 0x28, 0xed, 0x13, 0xea, 0x02, 0x6b, 0x4d,
	0x7c, 0xc0, 0x30, 0x04, 0xf2, 0xb7, 0xae, 0x2d, 0xea, 0x9a, 0x43, 0xc7, 0x43, 0x48, 0x8a, 0x5c,
	0x83, 0xb5, 0x48, 0x31, 0xf1, 0xdd, 0x84, 0x34, 0x29, 0xc1, 0x86, 0x00, 0x8b, 0x26, 0x45, 0xce,
	0x22, 0x1b, 0x5e, 0xa4, 0x42, 0xc5, 0x1c, 0x7a, 0x23, 0x9f, 0xf6, 0xd4, 0x25, 0xc4, 0xe4, 0x3c,
	0x0f, 0xe3, 0xd2, 0xb1, 0x67, 0xea, 0x32, 0xef, 0x1b, 0x8f, 0x6a, 0x11, 0x30, 0xfc, 0x06, 0x84,
	0x7c, 0x30, 0x23, 0x02, 0xcf, 0xb0, 0x56, 0x4f, 0xac, 0x7d, 0xd3, 0x0f, 0x26, 0xb0, 0x64, 0x19,
	0xa9, 0x45, 0x8e, 0xd9, 0x13, 0x1f, 0x22, 0x50, 0x61, 0x7b, 0x5f, 0x06, 0x9c, 0x89, 0xfb, 0x82,
	0x35, 0xb6, 0x96, 0x0c, 0x34, 0x3d, 0x1a, 0x3c, 0x83, 0xcf, 0xbc, 0xe8, 0xb0, 0xc1, 0x1a, 0x34,
	0x42, 0xeb, 0x8a, 0x2c, 0xa2, 0x6c, 0x5b, 0xa1, 0xdc, 0x8d, 0x7d, 0xb0, 0x88, 0xac, 0x41, 0x41,
	0xbc, 0xe0, 0x6b, 0x60, 0x80, 0x9e, 0xba, 0x40, 0x5e, 0x83, 0xd2, 0xe1, 0xc8, 0x74, 0x9f, 0x78,
	0x23, 0xa3, 0xfa, 0x7c, 0x40, 0x87, 0x4e, 0x9f, 0x9d, 0xbf, 0x79, 0xae, 0x42, 0x36, 0xe1, 0x7a,
	0xdd, 0x79, 0x6e, 0x48, 0xe7, 0x57, 0x83, 0x8d, 0xc3, 0xe0, 0x94, 0xde, 0xfe, 0x59, 0x0a, 0x6e,
	0x26, 0xbe, 0x56, 0x29, 0x3e, 0x25, 0xb4, 0x1d, 0x7e, 0xfb, 0xc2, 0x38, 0xac, 0x96, 0xf7, 0x8c,
	0xca, 0x71, 0x63, 0xbf, 0x76, 0x60, 0xe8, 0xd5, 0x87, 0x49, 0x1e, 0x83, 0xef, 0xc2, 0x5b, 0x2f,
	0x28, 0xdf, 0x92, 0x81, 0x96, 0x57, 0x2a, 0x7a, 0x7c, 0xb4, 0xa7, 0xa6, 0xc8, 0x36, 0xbc, 0xfd,
	0x82, 0xa2, 0x87, 0xa7, 0xe5, 0xc6, 0x93, 0xe3, 0x53, 0x35, 0x4d, 0xde, 0x82, 0x37, 0x5e, 0x50,
	0xf6, 0xa4, 0xa2, 0x2e, 0x5e, 0xad, 0xf5, 0x8f, 0x3e, 0xfc, 0x48, 0x5d, 0xda, 0xfe, 0xd7, 0xa9,
	0xa4, 0x57, 0x24, 0x71, 0x49, 0xb3, 0xa5, 0xc6, 0xab, 0x1d, 0x94, 0xeb, 0x55, 0xac, 0xc7, 0xe3,
	0x4b, 0x05, 0xb4, 0x52, 0x6e, 0x9e, 0x96, 0x8f, 0x22, 0x99, 0x4a, 0x24, 0xb3, 0xda, 0xac, 0x94,
	0x4f, 0xf8, 0x4b, 0x21, 0x3c, 0x33, 0x15, 0xc1, 0x57, 0x39, 0x2c, 0x73, 0xc7, 0x4b, 0x35, 0xcd,
	0x38, 0x48, 0x40, 0xeb, 0xa7, 0xcd, 0x5a, 0x85, 0x83, 0x17, 0x23, 0x98, 0x9a, 0xc7, 0xa7, 0x8d,
	0x3d, 0xa3, 0x59, 0xa9, 0x36, 0x44, 0x33, 0x4b, 0x4c, 0x6a, 0x88, 0x4c, 0x8c, 0x5c, 0xad, 0xb5,
	0x9e, 0x18, 0x95, 0x6a, 0xa3, 0x55, 0xd5, 0x79, 0x81, 0x65, 0xa2, 0xc1, 0xeb, 0xb2, 0xa9, 0xe3,
	0xfa, 0x49, 0xb5, 0x55, 0x6b, 0xd5, 0x8e, 0x1b, 0xb1, 0x32, 0x2b, 0x28, 0x33, 0x64, 0x5f, 0x4f,
	0x8e, 0xf5, 0x56, 0x93, 0x67, 0x64, 0x22, 0xd8, 0xeb, 0xa7, 0xcd, 0xaa, 0xb1, 0x7f, 0x54, 0x6e,
	0x1e, 0x46, 0xba, 0x9c, 0x25, 0x37, 0xe1, 0x9a, 0xa4, 0xe7, 0xf1, 0x51, 0xd5, 0x38, 0x39, 0x2a,
	0x3f, 0xe1, 0x59, 0xc0, 0xe4, 0x62, 0x2b, 0xf6, 0x00, 0xe2, 0x6d, 0xd8, 0x6a, 0x95, 0x0f, 0x24,
	0xfd, 0x13, 0x98, 0x6a, 0x0b, 0x6e, 0x4c, 0x16, 0x90, 0xcf, 0x6b, 0x2b, 0x6c, 0x71, 0x4e, 0x66,
	0xea, 0xb5, 0xca, 0xa1, 0x9a, 0xda, 0xae, 0x42, 0x7e, 0x37, 0xfa, 0xe8, 0x10, 0x81, 0xe2, 0xee,
	0x69, 0xab, 0x75, 0xdc, 0x30, 0x6a, 0x8d, 0x47, 0xe5, 0xa3, 0x9a, 0x70, 0xe0, 0x13, 0x30, 0xfe,
	0x84, 0x8b, 0x42, 0x56, 0x21, 0x27, 0x20, 0x47, 0xb5, 0xc6, 0x03, 0x35, 0xb5, 0xfd, 0x1b, 0x40,
	0x62, 0xf6, 0x55, 0x5c, 0x87, 0x71, 0x36, 0x3b, 0x38, 0xad, 0xed, 0x55, 0x8d, 0x66, 0xeb, 0xc9,
	0xd1, 0x64, 0xd7, 0x5f, 0x87, 0xcd, 0xe4, 0x62, 0xad, 0xea, 0xe7, 0xac, 0xf7, 0x77, 0xe0, 0xb5,
	0xe4, 0x7c, 0xde, 0x07, 0x35, 0xb5, 0xfd, 0xeb, 0xf2, 0xeb, 0x36, 0x32, 0xa0, 0x95, 0xb1, 0xc3,
	0x69, 0xe3, 0x41, 0xe3, 0xf8, 0x71, 0xc3, 0xa8, 0xb5, 0xaa, 0x75, 0xc9, 0x75, 0x79, 0xc8, 0x70,
	0x3e, 0x2b, 0xef, 0xaa, 0x0a, 0x93, 0x11, 0x98, 0xaa, 0x94, 0x5b, 0xd5, 0x83, 0x63, 0xfd, 0x89,
	0x9a, 0x22, 0x45, 0x00, 0xce, 0x3f, 0xac, 0x96, 0x9a, 0x66, 0x63, 0xaf, 0xd7, 0x3e, 0x1f, 0x97,
	0x58, 0x44, 0x08, 0x96, 0x10, 0x5f, 0xe0, 0x59, 0xda, 0x3e, 0xe5, 0xf1, 0xa1, 0xd1, 0xe0, 0xbf,
	0x12, 0xb7, 0x25, 0x45, 0x40, 0x28, 0xc0, 0xd4, 0x05, 0x72, 0x17, 0xbe, 0x36, 0x99, 0x53, 0x89,
	0x86, 0x9c, 0x08, 0x61, 0xa6, 0x2a, 0xdb, 0x7f, 0x5b, 0x91, 0xa3, 0x0a, 0xa3, 0x0d, 0x36, 0x40,
	0x95, 0xa3, 0xaa, 0x36, 0x5a, 0x7a, 0xb9, 0x51, 0xa9, 0xf2, 0x27, 0xd7, 0xc7, 0xdf, 0xde, 0x41,
	0x9f, 0x1a, 0x1c, 0xd5, 0x17, 0xc7, 0x0d, 0xb6, 0x5c, 0xd6, 0xa0, 0x50, 0xaf, 0x35, 0x6a, 0x63,
	0x50, 0x3a, 0xa4, 0xc2, 0x49, 0xf9, 0x48, 0x5d, 0x64, 0x62, 0x51, 0x7c, 0x94, 0x4b, 0xe2, 0x34,
	0xf4, 0xea, 0x41, 0xad, 0x89, 0x83, 0x8b, 0x4f, 0x4e, 0x79, 0x6f, 0xcf, 0x88, 0x0d, 0x7e, 0x79,
	0xfb, 0xc7, 0x50, 0x8c, 0xc7, 0x36, 0xb1, 0xdd, 0x4b, 0x4a, 0x5e, 0x01, 0x52, 0x17, 0x58, 0xcf,
	0xe5, 0x37, 0x86, 0x42, 0xa8, 0xc2, 0xe4, 0x7b, 0xab, 0xbc, 0xcb, 0x67, 0x48, 0xbc, 0xfa, 0x6e,
	0xe0, 0xb7, 0x1d, 0x8c, 0xe3, 0x13, 0xfc, 0xa0, 0x51, 0x6a, 0xfb, 0x0f, 0x15, 0x20, 0xd3, 0x7e,
	0xec, 0x8c, 0xc0, 0xe2, 0x35, 0x29, 0x23, 0x9a, 0xab, 0x2e, 0xb0, 0x55, 0xf5, 0xc0, 0xf5, 0xac,
	0xf3, 0xe3, 0x51, 0x10, 0xcf, 0x52, 0xd8, 0x48, 0x1f, 0x39, 0xbe, 0x13, 0x78, 0x43, 0xb6, 0x57,
	0xc4, 0x73, 0x53, 0x2c, 0x97, 0xa1, 0x16, 0x25, 0xe2, 0xb9, 0x69, 0xd6, 0xd5, 0x87, 0x23, 0x27,
	0x38, 0x75, 0xe5, 0xc7, 0xe6, 0xa8, 0x1d, 0x2f, 0xb2, 0xb8, 0xfd, 0xbb, 0x0a, 0x5c, 0x3b, 0x48,
	0x74, 0xad, 0x62, 0x93, 0x2e, 0x08, 0x3e, 0x26, 0x72, 0xb3, 0x55, 0x6e, 0x9d, 0x36, 0x27, 0xd6,
	0xc2, 0x1b, 0x70, 0x6b, 0x66, 0xc9, 0xe6, 0xe1, 0xf1, 0x63, 0x4e, 0xb4, 0x99, 0x45, 0x0e, 0x6b,
	0x7b, 0x55, 0x35, 0xb5, 0xfd, 0x7d, 0x20, 0xd3, 0x0e, 0x05, 0x6c, 0xeb, 0x16, 0x22, 0xc1, 0x78,
	0x74, 0x5c, 0xab, 0x54, 0xf1, 0x23, 0x6c, 0x9c, 0x7f, 0x1e, 0x1f, 0xeb, 0x7b, 0x3c, 0xa9, 0x6c,
	0x7f, 0x01, 0x85, 0xd8, 0xb5, 0x3e, 0x5b, 0x4b, 0x42, 0x18, 0x18, 0xad, 0x72, 0xf3, 0x81, 0x5c,
	0x4b, 0x25, 0xd8, 0xe0, 0x82, 0x30, 0x64, 0x2d, 0xcc, 0xe6, 0xe2, 0x46, 0x8a, 0x9a, 0xf2, 0xae,
	0xf1, 0xa8, 0x56, 0x7d, 0x6c, 0xec, 0x95, 0x5b, 0x65, 0x35, 0xb5, 0xdd, 0x06, 0xd8, 0x1d, 0x9b,
	0x27, 0xb6, 0xe0, 0x86, 0x10, 0x23, 0x09, 0x02, 0x6d, 0x13, 0xae, 0x47, 0x33, 0x79, 0x53, 0xc8,
	0xc0, 0xca, 0x64, 0xc5, 0xe8, 0xfb, 0x52, 0xa9, 0xed, 0xef, 0xc1, 0x4a, 0xa5, 0xd3, 0x95, 0xcb,
	0xb0, 0xb2, 0x9f, 0x28, 0x2e, 0xd9, 0x76, 0x21, 0x73, 0xc6, 0x12, 0x61, 0xb7, 0x0a, 0x25, 0xcb,
	0xeb, 0xef, 0x5c, 0x38, 0x17, 0xde, 0x88, 0x69, 0xc3, 0xf8, 0x7d, 0x3c, 0xfe, 0x01, 0xee, 0x2f,
	0xde, 0xed, 0x7a, 0x3d, 0xd3, 0xed, 0xee, 0x7c, 0x78, 0x3f, 0x08, 0x76, 0x2c, 0xaf, 0x7f, 0x0f,
	0xc1, 0x96, 0xd7, 0xbb, 0x67, 0x0e, 0x06, 0xb1, 0x0f, 0x80, 0xb7, 0x97, 0x31, 0xeb, 0x83, 0xff,
	0x17, 0x00, 0x00, 0xff, 0xff, 0x1b, 0xcb, 0x64, 0xf4, 0x8a, 0x7c, 0x00, 0x00,
}
