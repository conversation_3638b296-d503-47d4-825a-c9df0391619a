// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel_play/channel-play-view_.proto

package channel_play // import "golang.52tt.com/protocol/app/channel-play"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelViewType int32

const (
	ChannelViewType_ChannelView_Default       ChannelViewType = 0
	ChannelViewType_ChannelView_MOBA          ChannelViewType = 1
	ChannelViewType_ChannelView_Ktv           ChannelViewType = 2
	ChannelViewType_ChannelView_MysteryEscape ChannelViewType = 3
	ChannelViewType_ChanenlView_Marshal       ChannelViewType = 4
	ChannelViewType_ChannelView_Music         ChannelViewType = 5
)

var ChannelViewType_name = map[int32]string{
	0: "ChannelView_Default",
	1: "ChannelView_MOBA",
	2: "ChannelView_Ktv",
	3: "ChannelView_MysteryEscape",
	4: "ChanenlView_Marshal",
	5: "ChannelView_Music",
}
var ChannelViewType_value = map[string]int32{
	"ChannelView_Default":       0,
	"ChannelView_MOBA":          1,
	"ChannelView_Ktv":           2,
	"ChannelView_MysteryEscape": 3,
	"ChanenlView_Marshal":       4,
	"ChannelView_Music":         5,
}

func (x ChannelViewType) String() string {
	return proto.EnumName(ChannelViewType_name, int32(x))
}
func (ChannelViewType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{0}
}

type MusicDescIcon int32

const (
	MusicDescIcon_DescIcon_None  MusicDescIcon = 0
	MusicDescIcon_DescIcon_MUSIC MusicDescIcon = 1
	MusicDescIcon_DescIcon_KTV   MusicDescIcon = 2
)

var MusicDescIcon_name = map[int32]string{
	0: "DescIcon_None",
	1: "DescIcon_MUSIC",
	2: "DescIcon_KTV",
}
var MusicDescIcon_value = map[string]int32{
	"DescIcon_None":  0,
	"DescIcon_MUSIC": 1,
	"DescIcon_KTV":   2,
}

func (x MusicDescIcon) String() string {
	return proto.EnumName(MusicDescIcon_name, int32(x))
}
func (MusicDescIcon) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{1}
}

type MarshalType int32

const (
	MarshalType_UNKNOWN_TYPE MarshalType = 0
	MarshalType_JSON_TYPE    MarshalType = 1
	MarshalType_PROTO_TYPE   MarshalType = 2
)

var MarshalType_name = map[int32]string{
	0: "UNKNOWN_TYPE",
	1: "JSON_TYPE",
	2: "PROTO_TYPE",
}
var MarshalType_value = map[string]int32{
	"UNKNOWN_TYPE": 0,
	"JSON_TYPE":    1,
	"PROTO_TYPE":   2,
}

func (x MarshalType) String() string {
	return proto.EnumName(MarshalType_name, int32(x))
}
func (MarshalType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{2}
}

// 提供view的来源标识
type GenViewSource int32

const (
	GenViewSource_FROM_GAME    GenViewSource = 0
	GenViewSource_FROM_MUSIC   GenViewSource = 1
	GenViewSource_FROM_MYSTERY GenViewSource = 2
	GenViewSource_FROM_REVENUE GenViewSource = 3
	GenViewSource_FROM_MT_GAME GenViewSource = 4
)

var GenViewSource_name = map[int32]string{
	0: "FROM_GAME",
	1: "FROM_MUSIC",
	2: "FROM_MYSTERY",
	3: "FROM_REVENUE",
	4: "FROM_MT_GAME",
}
var GenViewSource_value = map[string]int32{
	"FROM_GAME":    0,
	"FROM_MUSIC":   1,
	"FROM_MYSTERY": 2,
	"FROM_REVENUE": 3,
	"FROM_MT_GAME": 4,
}

func (x GenViewSource) String() string {
	return proto.EnumName(GenViewSource_name, int32(x))
}
func (GenViewSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{3}
}

// 默认
type ChannelViewDefault struct {
	DescList             []string          `protobuf:"bytes,1,rep,name=desc_list,json=descList,proto3" json:"desc_list,omitempty"`
	TabName              string            `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	CornerImg            string            `protobuf:"bytes,3,opt,name=corner_img,json=cornerImg,proto3" json:"corner_img,omitempty"`
	CornerBgImg          string            `protobuf:"bytes,4,opt,name=corner_bg_img,json=cornerBgImg,proto3" json:"corner_bg_img,omitempty"`
	RoomCondition        string            `protobuf:"bytes,5,opt,name=room_condition,json=roomCondition,proto3" json:"room_condition,omitempty"`
	AudienceInfo         []*UserInfoForImg `protobuf:"bytes,6,rep,name=audience_info,json=audienceInfo,proto3" json:"audience_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChannelViewDefault) Reset()         { *m = ChannelViewDefault{} }
func (m *ChannelViewDefault) String() string { return proto.CompactTextString(m) }
func (*ChannelViewDefault) ProtoMessage()    {}
func (*ChannelViewDefault) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{0}
}
func (m *ChannelViewDefault) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewDefault.Unmarshal(m, b)
}
func (m *ChannelViewDefault) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewDefault.Marshal(b, m, deterministic)
}
func (dst *ChannelViewDefault) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewDefault.Merge(dst, src)
}
func (m *ChannelViewDefault) XXX_Size() int {
	return xxx_messageInfo_ChannelViewDefault.Size(m)
}
func (m *ChannelViewDefault) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewDefault.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewDefault proto.InternalMessageInfo

func (m *ChannelViewDefault) GetDescList() []string {
	if m != nil {
		return m.DescList
	}
	return nil
}

func (m *ChannelViewDefault) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ChannelViewDefault) GetCornerImg() string {
	if m != nil {
		return m.CornerImg
	}
	return ""
}

func (m *ChannelViewDefault) GetCornerBgImg() string {
	if m != nil {
		return m.CornerBgImg
	}
	return ""
}

func (m *ChannelViewDefault) GetRoomCondition() string {
	if m != nil {
		return m.RoomCondition
	}
	return ""
}

func (m *ChannelViewDefault) GetAudienceInfo() []*UserInfoForImg {
	if m != nil {
		return m.AudienceInfo
	}
	return nil
}

// 带区服模式人数的游戏
type ChannelViewMOBA struct {
	DescFirst            []*ChannelViewMOBA_DescAndColor `protobuf:"bytes,1,rep,name=desc_first,json=descFirst,proto3" json:"desc_first,omitempty"`
	DescList             []string                        `protobuf:"bytes,2,rep,name=desc_list,json=descList,proto3" json:"desc_list,omitempty"`
	TabName              string                          `protobuf:"bytes,3,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	CornerImg            string                          `protobuf:"bytes,4,opt,name=corner_img,json=cornerImg,proto3" json:"corner_img,omitempty"`
	CornerBgImg          string                          `protobuf:"bytes,5,opt,name=corner_bg_img,json=cornerBgImg,proto3" json:"corner_bg_img,omitempty"`
	RoomCondition        string                          `protobuf:"bytes,6,opt,name=room_condition,json=roomCondition,proto3" json:"room_condition,omitempty"`
	AudienceInfo         []*UserInfoForImg               `protobuf:"bytes,8,rep,name=audience_info,json=audienceInfo,proto3" json:"audience_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *ChannelViewMOBA) Reset()         { *m = ChannelViewMOBA{} }
func (m *ChannelViewMOBA) String() string { return proto.CompactTextString(m) }
func (*ChannelViewMOBA) ProtoMessage()    {}
func (*ChannelViewMOBA) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{1}
}
func (m *ChannelViewMOBA) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewMOBA.Unmarshal(m, b)
}
func (m *ChannelViewMOBA) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewMOBA.Marshal(b, m, deterministic)
}
func (dst *ChannelViewMOBA) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewMOBA.Merge(dst, src)
}
func (m *ChannelViewMOBA) XXX_Size() int {
	return xxx_messageInfo_ChannelViewMOBA.Size(m)
}
func (m *ChannelViewMOBA) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewMOBA.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewMOBA proto.InternalMessageInfo

func (m *ChannelViewMOBA) GetDescFirst() []*ChannelViewMOBA_DescAndColor {
	if m != nil {
		return m.DescFirst
	}
	return nil
}

func (m *ChannelViewMOBA) GetDescList() []string {
	if m != nil {
		return m.DescList
	}
	return nil
}

func (m *ChannelViewMOBA) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ChannelViewMOBA) GetCornerImg() string {
	if m != nil {
		return m.CornerImg
	}
	return ""
}

func (m *ChannelViewMOBA) GetCornerBgImg() string {
	if m != nil {
		return m.CornerBgImg
	}
	return ""
}

func (m *ChannelViewMOBA) GetRoomCondition() string {
	if m != nil {
		return m.RoomCondition
	}
	return ""
}

func (m *ChannelViewMOBA) GetAudienceInfo() []*UserInfoForImg {
	if m != nil {
		return m.AudienceInfo
	}
	return nil
}

type ChannelViewMOBA_DescAndColor struct {
	Desc                 string   `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
	MarkBlue             bool     `protobuf:"varint,2,opt,name=mark_blue,json=markBlue,proto3" json:"mark_blue,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelViewMOBA_DescAndColor) Reset()         { *m = ChannelViewMOBA_DescAndColor{} }
func (m *ChannelViewMOBA_DescAndColor) String() string { return proto.CompactTextString(m) }
func (*ChannelViewMOBA_DescAndColor) ProtoMessage()    {}
func (*ChannelViewMOBA_DescAndColor) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{1, 0}
}
func (m *ChannelViewMOBA_DescAndColor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewMOBA_DescAndColor.Unmarshal(m, b)
}
func (m *ChannelViewMOBA_DescAndColor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewMOBA_DescAndColor.Marshal(b, m, deterministic)
}
func (dst *ChannelViewMOBA_DescAndColor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewMOBA_DescAndColor.Merge(dst, src)
}
func (m *ChannelViewMOBA_DescAndColor) XXX_Size() int {
	return xxx_messageInfo_ChannelViewMOBA_DescAndColor.Size(m)
}
func (m *ChannelViewMOBA_DescAndColor) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewMOBA_DescAndColor.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewMOBA_DescAndColor proto.InternalMessageInfo

func (m *ChannelViewMOBA_DescAndColor) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ChannelViewMOBA_DescAndColor) GetMarkBlue() bool {
	if m != nil {
		return m.MarkBlue
	}
	return false
}

// 一起K歌,扩列聊天
type ChannelViewKtv struct {
	OrderSongCount       uint32            `protobuf:"varint,1,opt,name=order_song_count,json=orderSongCount,proto3" json:"order_song_count,omitempty"`
	SingSongName         string            `protobuf:"bytes,2,opt,name=sing_song_name,json=singSongName,proto3" json:"sing_song_name,omitempty"`
	TabName              string            `protobuf:"bytes,3,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	CornerImg            string            `protobuf:"bytes,4,opt,name=corner_img,json=cornerImg,proto3" json:"corner_img,omitempty"`
	CornerBgImg          string            `protobuf:"bytes,5,opt,name=corner_bg_img,json=cornerBgImg,proto3" json:"corner_bg_img,omitempty"`
	RoomCondition        string            `protobuf:"bytes,6,opt,name=room_condition,json=roomCondition,proto3" json:"room_condition,omitempty"`
	MusicIcon            MusicDescIcon     `protobuf:"varint,7,opt,name=music_icon,json=musicIcon,proto3,enum=ga.channel_play.MusicDescIcon" json:"music_icon,omitempty"`
	DescList             []string          `protobuf:"bytes,8,rep,name=desc_list,json=descList,proto3" json:"desc_list,omitempty"`
	AudienceInfo         []*UserInfoForImg `protobuf:"bytes,9,rep,name=audience_info,json=audienceInfo,proto3" json:"audience_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChannelViewKtv) Reset()         { *m = ChannelViewKtv{} }
func (m *ChannelViewKtv) String() string { return proto.CompactTextString(m) }
func (*ChannelViewKtv) ProtoMessage()    {}
func (*ChannelViewKtv) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{2}
}
func (m *ChannelViewKtv) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewKtv.Unmarshal(m, b)
}
func (m *ChannelViewKtv) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewKtv.Marshal(b, m, deterministic)
}
func (dst *ChannelViewKtv) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewKtv.Merge(dst, src)
}
func (m *ChannelViewKtv) XXX_Size() int {
	return xxx_messageInfo_ChannelViewKtv.Size(m)
}
func (m *ChannelViewKtv) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewKtv.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewKtv proto.InternalMessageInfo

func (m *ChannelViewKtv) GetOrderSongCount() uint32 {
	if m != nil {
		return m.OrderSongCount
	}
	return 0
}

func (m *ChannelViewKtv) GetSingSongName() string {
	if m != nil {
		return m.SingSongName
	}
	return ""
}

func (m *ChannelViewKtv) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ChannelViewKtv) GetCornerImg() string {
	if m != nil {
		return m.CornerImg
	}
	return ""
}

func (m *ChannelViewKtv) GetCornerBgImg() string {
	if m != nil {
		return m.CornerBgImg
	}
	return ""
}

func (m *ChannelViewKtv) GetRoomCondition() string {
	if m != nil {
		return m.RoomCondition
	}
	return ""
}

func (m *ChannelViewKtv) GetMusicIcon() MusicDescIcon {
	if m != nil {
		return m.MusicIcon
	}
	return MusicDescIcon_DescIcon_None
}

func (m *ChannelViewKtv) GetDescList() []string {
	if m != nil {
		return m.DescList
	}
	return nil
}

func (m *ChannelViewKtv) GetAudienceInfo() []*UserInfoForImg {
	if m != nil {
		return m.AudienceInfo
	}
	return nil
}

type ChannelViewNewMusic struct {
	Song                 string                              `protobuf:"bytes,1,opt,name=song,proto3" json:"song,omitempty"`
	Topic                string                              `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	TopicIcon            string                              `protobuf:"bytes,3,opt,name=topic_icon,json=topicIcon,proto3" json:"topic_icon,omitempty"`
	TopicType            int32                               `protobuf:"varint,4,opt,name=topic_type,json=topicType,proto3" json:"topic_type,omitempty"`
	Status               string                              `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	TabId                uint32                              `protobuf:"varint,6,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Glory                *KtvGloryData                       `protobuf:"bytes,7,opt,name=glory,proto3" json:"glory,omitempty"`
	MusicSocial          *MusicSocialData                    `protobuf:"bytes,8,opt,name=music_social,json=musicSocial,proto3" json:"music_social,omitempty"`
	TabIcon              string                              `protobuf:"bytes,9,opt,name=tab_icon,json=tabIcon,proto3" json:"tab_icon,omitempty"`
	TabDesc              string                              `protobuf:"bytes,10,opt,name=tab_desc,json=tabDesc,proto3" json:"tab_desc,omitempty"`
	DescFirst            []*ChannelViewNewMusic_DescAndColor `protobuf:"bytes,11,rep,name=desc_first,json=descFirst,proto3" json:"desc_first,omitempty"`
	DescList             []string                            `protobuf:"bytes,12,rep,name=desc_list,json=descList,proto3" json:"desc_list,omitempty"`
	Accounts             []string                            `protobuf:"bytes,13,rep,name=accounts,proto3" json:"accounts,omitempty"`
	Logo                 string                              `protobuf:"bytes,14,opt,name=logo,proto3" json:"logo,omitempty"`
	Interest             *MusicInterestData                  `protobuf:"bytes,15,opt,name=interest,proto3" json:"interest,omitempty"`
	SameCity             *ChannelMusicSameCityInfo           `protobuf:"bytes,16,opt,name=same_city,json=sameCity,proto3" json:"same_city,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *ChannelViewNewMusic) Reset()         { *m = ChannelViewNewMusic{} }
func (m *ChannelViewNewMusic) String() string { return proto.CompactTextString(m) }
func (*ChannelViewNewMusic) ProtoMessage()    {}
func (*ChannelViewNewMusic) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{3}
}
func (m *ChannelViewNewMusic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewNewMusic.Unmarshal(m, b)
}
func (m *ChannelViewNewMusic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewNewMusic.Marshal(b, m, deterministic)
}
func (dst *ChannelViewNewMusic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewNewMusic.Merge(dst, src)
}
func (m *ChannelViewNewMusic) XXX_Size() int {
	return xxx_messageInfo_ChannelViewNewMusic.Size(m)
}
func (m *ChannelViewNewMusic) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewNewMusic.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewNewMusic proto.InternalMessageInfo

func (m *ChannelViewNewMusic) GetSong() string {
	if m != nil {
		return m.Song
	}
	return ""
}

func (m *ChannelViewNewMusic) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ChannelViewNewMusic) GetTopicIcon() string {
	if m != nil {
		return m.TopicIcon
	}
	return ""
}

func (m *ChannelViewNewMusic) GetTopicType() int32 {
	if m != nil {
		return m.TopicType
	}
	return 0
}

func (m *ChannelViewNewMusic) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *ChannelViewNewMusic) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ChannelViewNewMusic) GetGlory() *KtvGloryData {
	if m != nil {
		return m.Glory
	}
	return nil
}

func (m *ChannelViewNewMusic) GetMusicSocial() *MusicSocialData {
	if m != nil {
		return m.MusicSocial
	}
	return nil
}

func (m *ChannelViewNewMusic) GetTabIcon() string {
	if m != nil {
		return m.TabIcon
	}
	return ""
}

func (m *ChannelViewNewMusic) GetTabDesc() string {
	if m != nil {
		return m.TabDesc
	}
	return ""
}

func (m *ChannelViewNewMusic) GetDescFirst() []*ChannelViewNewMusic_DescAndColor {
	if m != nil {
		return m.DescFirst
	}
	return nil
}

func (m *ChannelViewNewMusic) GetDescList() []string {
	if m != nil {
		return m.DescList
	}
	return nil
}

func (m *ChannelViewNewMusic) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *ChannelViewNewMusic) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *ChannelViewNewMusic) GetInterest() *MusicInterestData {
	if m != nil {
		return m.Interest
	}
	return nil
}

func (m *ChannelViewNewMusic) GetSameCity() *ChannelMusicSameCityInfo {
	if m != nil {
		return m.SameCity
	}
	return nil
}

type ChannelViewNewMusic_DescAndColor struct {
	Desc                 string   `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
	MarkBlue             bool     `protobuf:"varint,2,opt,name=mark_blue,json=markBlue,proto3" json:"mark_blue,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelViewNewMusic_DescAndColor) Reset()         { *m = ChannelViewNewMusic_DescAndColor{} }
func (m *ChannelViewNewMusic_DescAndColor) String() string { return proto.CompactTextString(m) }
func (*ChannelViewNewMusic_DescAndColor) ProtoMessage()    {}
func (*ChannelViewNewMusic_DescAndColor) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{3, 0}
}
func (m *ChannelViewNewMusic_DescAndColor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewNewMusic_DescAndColor.Unmarshal(m, b)
}
func (m *ChannelViewNewMusic_DescAndColor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewNewMusic_DescAndColor.Marshal(b, m, deterministic)
}
func (dst *ChannelViewNewMusic_DescAndColor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewNewMusic_DescAndColor.Merge(dst, src)
}
func (m *ChannelViewNewMusic_DescAndColor) XXX_Size() int {
	return xxx_messageInfo_ChannelViewNewMusic_DescAndColor.Size(m)
}
func (m *ChannelViewNewMusic_DescAndColor) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewNewMusic_DescAndColor.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewNewMusic_DescAndColor proto.InternalMessageInfo

func (m *ChannelViewNewMusic_DescAndColor) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ChannelViewNewMusic_DescAndColor) GetMarkBlue() bool {
	if m != nil {
		return m.MarkBlue
	}
	return false
}

type MusicInterestData struct {
	Topic                string   `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicInterestData) Reset()         { *m = MusicInterestData{} }
func (m *MusicInterestData) String() string { return proto.CompactTextString(m) }
func (*MusicInterestData) ProtoMessage()    {}
func (*MusicInterestData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{4}
}
func (m *MusicInterestData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicInterestData.Unmarshal(m, b)
}
func (m *MusicInterestData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicInterestData.Marshal(b, m, deterministic)
}
func (dst *MusicInterestData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicInterestData.Merge(dst, src)
}
func (m *MusicInterestData) XXX_Size() int {
	return xxx_messageInfo_MusicInterestData.Size(m)
}
func (m *MusicInterestData) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicInterestData.DiscardUnknown(m)
}

var xxx_messageInfo_MusicInterestData proto.InternalMessageInfo

func (m *MusicInterestData) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

type ChannelMusicSameCityInfo struct {
	CityName             string                 `protobuf:"bytes,1,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	UserCityList         []*ChannelUserCityInfo `protobuf:"bytes,2,rep,name=user_city_list,json=userCityList,proto3" json:"user_city_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ChannelMusicSameCityInfo) Reset()         { *m = ChannelMusicSameCityInfo{} }
func (m *ChannelMusicSameCityInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelMusicSameCityInfo) ProtoMessage()    {}
func (*ChannelMusicSameCityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{5}
}
func (m *ChannelMusicSameCityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMusicSameCityInfo.Unmarshal(m, b)
}
func (m *ChannelMusicSameCityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMusicSameCityInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelMusicSameCityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMusicSameCityInfo.Merge(dst, src)
}
func (m *ChannelMusicSameCityInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelMusicSameCityInfo.Size(m)
}
func (m *ChannelMusicSameCityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMusicSameCityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMusicSameCityInfo proto.InternalMessageInfo

func (m *ChannelMusicSameCityInfo) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *ChannelMusicSameCityInfo) GetUserCityList() []*ChannelUserCityInfo {
	if m != nil {
		return m.UserCityList
	}
	return nil
}

type ChannelUserCityInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CityName             string   `protobuf:"bytes,2,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelUserCityInfo) Reset()         { *m = ChannelUserCityInfo{} }
func (m *ChannelUserCityInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelUserCityInfo) ProtoMessage()    {}
func (*ChannelUserCityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{6}
}
func (m *ChannelUserCityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelUserCityInfo.Unmarshal(m, b)
}
func (m *ChannelUserCityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelUserCityInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelUserCityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelUserCityInfo.Merge(dst, src)
}
func (m *ChannelUserCityInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelUserCityInfo.Size(m)
}
func (m *ChannelUserCityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelUserCityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelUserCityInfo proto.InternalMessageInfo

func (m *ChannelUserCityInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelUserCityInfo) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

type KtvGloryData struct {
	GloryName            string   `protobuf:"bytes,1,opt,name=glory_name,json=gloryName,proto3" json:"glory_name,omitempty"`
	GloryImg             string   `protobuf:"bytes,2,opt,name=glory_img,json=gloryImg,proto3" json:"glory_img,omitempty"`
	GloryBgImg           string   `protobuf:"bytes,3,opt,name=glory_bg_img,json=gloryBgImg,proto3" json:"glory_bg_img,omitempty"`
	GloryRank            uint32   `protobuf:"varint,4,opt,name=glory_rank,json=gloryRank,proto3" json:"glory_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KtvGloryData) Reset()         { *m = KtvGloryData{} }
func (m *KtvGloryData) String() string { return proto.CompactTextString(m) }
func (*KtvGloryData) ProtoMessage()    {}
func (*KtvGloryData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{7}
}
func (m *KtvGloryData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KtvGloryData.Unmarshal(m, b)
}
func (m *KtvGloryData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KtvGloryData.Marshal(b, m, deterministic)
}
func (dst *KtvGloryData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KtvGloryData.Merge(dst, src)
}
func (m *KtvGloryData) XXX_Size() int {
	return xxx_messageInfo_KtvGloryData.Size(m)
}
func (m *KtvGloryData) XXX_DiscardUnknown() {
	xxx_messageInfo_KtvGloryData.DiscardUnknown(m)
}

var xxx_messageInfo_KtvGloryData proto.InternalMessageInfo

func (m *KtvGloryData) GetGloryName() string {
	if m != nil {
		return m.GloryName
	}
	return ""
}

func (m *KtvGloryData) GetGloryImg() string {
	if m != nil {
		return m.GloryImg
	}
	return ""
}

func (m *KtvGloryData) GetGloryBgImg() string {
	if m != nil {
		return m.GloryBgImg
	}
	return ""
}

func (m *KtvGloryData) GetGloryRank() uint32 {
	if m != nil {
		return m.GloryRank
	}
	return 0
}

type MusicSocialData struct {
	MemberLabelBg        string                            `protobuf:"bytes,2,opt,name=member_label_bg,json=memberLabelBg,proto3" json:"member_label_bg,omitempty"`
	MemberLabelText      string                            `protobuf:"bytes,3,opt,name=member_label_text,json=memberLabelText,proto3" json:"member_label_text,omitempty"`
	RankSignInfo         *MusicSocialRankHonorSignInfoData `protobuf:"bytes,4,opt,name=rank_sign_info,json=rankSignInfo,proto3" json:"rank_sign_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *MusicSocialData) Reset()         { *m = MusicSocialData{} }
func (m *MusicSocialData) String() string { return proto.CompactTextString(m) }
func (*MusicSocialData) ProtoMessage()    {}
func (*MusicSocialData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{8}
}
func (m *MusicSocialData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicSocialData.Unmarshal(m, b)
}
func (m *MusicSocialData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicSocialData.Marshal(b, m, deterministic)
}
func (dst *MusicSocialData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicSocialData.Merge(dst, src)
}
func (m *MusicSocialData) XXX_Size() int {
	return xxx_messageInfo_MusicSocialData.Size(m)
}
func (m *MusicSocialData) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicSocialData.DiscardUnknown(m)
}

var xxx_messageInfo_MusicSocialData proto.InternalMessageInfo

func (m *MusicSocialData) GetMemberLabelBg() string {
	if m != nil {
		return m.MemberLabelBg
	}
	return ""
}

func (m *MusicSocialData) GetMemberLabelText() string {
	if m != nil {
		return m.MemberLabelText
	}
	return ""
}

func (m *MusicSocialData) GetRankSignInfo() *MusicSocialRankHonorSignInfoData {
	if m != nil {
		return m.RankSignInfo
	}
	return nil
}

type MusicSocialRankHonorSignInfoData struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	StyleColorList       []string `protobuf:"bytes,2,rep,name=style_color_list,json=styleColorList,proto3" json:"style_color_list,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicSocialRankHonorSignInfoData) Reset()         { *m = MusicSocialRankHonorSignInfoData{} }
func (m *MusicSocialRankHonorSignInfoData) String() string { return proto.CompactTextString(m) }
func (*MusicSocialRankHonorSignInfoData) ProtoMessage()    {}
func (*MusicSocialRankHonorSignInfoData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{9}
}
func (m *MusicSocialRankHonorSignInfoData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicSocialRankHonorSignInfoData.Unmarshal(m, b)
}
func (m *MusicSocialRankHonorSignInfoData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicSocialRankHonorSignInfoData.Marshal(b, m, deterministic)
}
func (dst *MusicSocialRankHonorSignInfoData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicSocialRankHonorSignInfoData.Merge(dst, src)
}
func (m *MusicSocialRankHonorSignInfoData) XXX_Size() int {
	return xxx_messageInfo_MusicSocialRankHonorSignInfoData.Size(m)
}
func (m *MusicSocialRankHonorSignInfoData) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicSocialRankHonorSignInfoData.DiscardUnknown(m)
}

var xxx_messageInfo_MusicSocialRankHonorSignInfoData proto.InternalMessageInfo

func (m *MusicSocialRankHonorSignInfoData) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MusicSocialRankHonorSignInfoData) GetStyleColorList() []string {
	if m != nil {
		return m.StyleColorList
	}
	return nil
}

func (m *MusicSocialRankHonorSignInfoData) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type UserInfoForImg struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Username             string   `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfoForImg) Reset()         { *m = UserInfoForImg{} }
func (m *UserInfoForImg) String() string { return proto.CompactTextString(m) }
func (*UserInfoForImg) ProtoMessage()    {}
func (*UserInfoForImg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{10}
}
func (m *UserInfoForImg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoForImg.Unmarshal(m, b)
}
func (m *UserInfoForImg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoForImg.Marshal(b, m, deterministic)
}
func (dst *UserInfoForImg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoForImg.Merge(dst, src)
}
func (m *UserInfoForImg) XXX_Size() int {
	return xxx_messageInfo_UserInfoForImg.Size(m)
}
func (m *UserInfoForImg) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoForImg.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoForImg proto.InternalMessageInfo

func (m *UserInfoForImg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfoForImg) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *UserInfoForImg) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 密室逃脱
type ChannelViewMysteryEscape struct {
	PlayerInfo           *UserInfoForImg   `protobuf:"bytes,1,opt,name=player_info,json=playerInfo,proto3" json:"player_info,omitempty"`
	Process              string            `protobuf:"bytes,2,opt,name=process,proto3" json:"process,omitempty"`
	AudienceInfo         []*UserInfoForImg `protobuf:"bytes,3,rep,name=audience_info,json=audienceInfo,proto3" json:"audience_info,omitempty"`
	RoomCondition        string            `protobuf:"bytes,4,opt,name=room_condition,json=roomCondition,proto3" json:"room_condition,omitempty"`
	RoomConditionFlag    uint32            `protobuf:"varint,5,opt,name=room_condition_flag,json=roomConditionFlag,proto3" json:"room_condition_flag,omitempty"`
	RoomMode             uint32            `protobuf:"varint,6,opt,name=room_mode,json=roomMode,proto3" json:"room_mode,omitempty"`
	CornerImg            string            `protobuf:"bytes,7,opt,name=corner_img,json=cornerImg,proto3" json:"corner_img,omitempty"`
	AudienceCount        uint32            `protobuf:"varint,8,opt,name=audience_count,json=audienceCount,proto3" json:"audience_count,omitempty"`
	BackgroundImg        string            `protobuf:"bytes,9,opt,name=background_img,json=backgroundImg,proto3" json:"background_img,omitempty"`
	AvatarToast          string            `protobuf:"bytes,10,opt,name=avatar_toast,json=avatarToast,proto3" json:"avatar_toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChannelViewMysteryEscape) Reset()         { *m = ChannelViewMysteryEscape{} }
func (m *ChannelViewMysteryEscape) String() string { return proto.CompactTextString(m) }
func (*ChannelViewMysteryEscape) ProtoMessage()    {}
func (*ChannelViewMysteryEscape) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{11}
}
func (m *ChannelViewMysteryEscape) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewMysteryEscape.Unmarshal(m, b)
}
func (m *ChannelViewMysteryEscape) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewMysteryEscape.Marshal(b, m, deterministic)
}
func (dst *ChannelViewMysteryEscape) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewMysteryEscape.Merge(dst, src)
}
func (m *ChannelViewMysteryEscape) XXX_Size() int {
	return xxx_messageInfo_ChannelViewMysteryEscape.Size(m)
}
func (m *ChannelViewMysteryEscape) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewMysteryEscape.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewMysteryEscape proto.InternalMessageInfo

func (m *ChannelViewMysteryEscape) GetPlayerInfo() *UserInfoForImg {
	if m != nil {
		return m.PlayerInfo
	}
	return nil
}

func (m *ChannelViewMysteryEscape) GetProcess() string {
	if m != nil {
		return m.Process
	}
	return ""
}

func (m *ChannelViewMysteryEscape) GetAudienceInfo() []*UserInfoForImg {
	if m != nil {
		return m.AudienceInfo
	}
	return nil
}

func (m *ChannelViewMysteryEscape) GetRoomCondition() string {
	if m != nil {
		return m.RoomCondition
	}
	return ""
}

func (m *ChannelViewMysteryEscape) GetRoomConditionFlag() uint32 {
	if m != nil {
		return m.RoomConditionFlag
	}
	return 0
}

func (m *ChannelViewMysteryEscape) GetRoomMode() uint32 {
	if m != nil {
		return m.RoomMode
	}
	return 0
}

func (m *ChannelViewMysteryEscape) GetCornerImg() string {
	if m != nil {
		return m.CornerImg
	}
	return ""
}

func (m *ChannelViewMysteryEscape) GetAudienceCount() uint32 {
	if m != nil {
		return m.AudienceCount
	}
	return 0
}

func (m *ChannelViewMysteryEscape) GetBackgroundImg() string {
	if m != nil {
		return m.BackgroundImg
	}
	return ""
}

func (m *ChannelViewMysteryEscape) GetAvatarToast() string {
	if m != nil {
		return m.AvatarToast
	}
	return ""
}

// 2023.4.4 首页改版后使用，音乐view由音乐业务方提供
type ChannelViewMarshal struct {
	MarshalType          MarshalType `protobuf:"varint,1,opt,name=marshal_type,json=marshalType,proto3,enum=ga.channel_play.MarshalType" json:"marshal_type,omitempty"`
	Content              []byte      `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ChannelViewMarshal) Reset()         { *m = ChannelViewMarshal{} }
func (m *ChannelViewMarshal) String() string { return proto.CompactTextString(m) }
func (*ChannelViewMarshal) ProtoMessage()    {}
func (*ChannelViewMarshal) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_view__4b7f1c05e772b728, []int{12}
}
func (m *ChannelViewMarshal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelViewMarshal.Unmarshal(m, b)
}
func (m *ChannelViewMarshal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelViewMarshal.Marshal(b, m, deterministic)
}
func (dst *ChannelViewMarshal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelViewMarshal.Merge(dst, src)
}
func (m *ChannelViewMarshal) XXX_Size() int {
	return xxx_messageInfo_ChannelViewMarshal.Size(m)
}
func (m *ChannelViewMarshal) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelViewMarshal.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelViewMarshal proto.InternalMessageInfo

func (m *ChannelViewMarshal) GetMarshalType() MarshalType {
	if m != nil {
		return m.MarshalType
	}
	return MarshalType_UNKNOWN_TYPE
}

func (m *ChannelViewMarshal) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

func init() {
	proto.RegisterType((*ChannelViewDefault)(nil), "ga.channel_play.ChannelViewDefault")
	proto.RegisterType((*ChannelViewMOBA)(nil), "ga.channel_play.ChannelViewMOBA")
	proto.RegisterType((*ChannelViewMOBA_DescAndColor)(nil), "ga.channel_play.ChannelViewMOBA.DescAndColor")
	proto.RegisterType((*ChannelViewKtv)(nil), "ga.channel_play.ChannelViewKtv")
	proto.RegisterType((*ChannelViewNewMusic)(nil), "ga.channel_play.ChannelViewNewMusic")
	proto.RegisterType((*ChannelViewNewMusic_DescAndColor)(nil), "ga.channel_play.ChannelViewNewMusic.DescAndColor")
	proto.RegisterType((*MusicInterestData)(nil), "ga.channel_play.MusicInterestData")
	proto.RegisterType((*ChannelMusicSameCityInfo)(nil), "ga.channel_play.ChannelMusicSameCityInfo")
	proto.RegisterType((*ChannelUserCityInfo)(nil), "ga.channel_play.ChannelUserCityInfo")
	proto.RegisterType((*KtvGloryData)(nil), "ga.channel_play.KtvGloryData")
	proto.RegisterType((*MusicSocialData)(nil), "ga.channel_play.MusicSocialData")
	proto.RegisterType((*MusicSocialRankHonorSignInfoData)(nil), "ga.channel_play.MusicSocialRankHonorSignInfoData")
	proto.RegisterType((*UserInfoForImg)(nil), "ga.channel_play.UserInfoForImg")
	proto.RegisterType((*ChannelViewMysteryEscape)(nil), "ga.channel_play.ChannelViewMysteryEscape")
	proto.RegisterType((*ChannelViewMarshal)(nil), "ga.channel_play.ChannelViewMarshal")
	proto.RegisterEnum("ga.channel_play.ChannelViewType", ChannelViewType_name, ChannelViewType_value)
	proto.RegisterEnum("ga.channel_play.MusicDescIcon", MusicDescIcon_name, MusicDescIcon_value)
	proto.RegisterEnum("ga.channel_play.MarshalType", MarshalType_name, MarshalType_value)
	proto.RegisterEnum("ga.channel_play.GenViewSource", GenViewSource_name, GenViewSource_value)
}

func init() {
	proto.RegisterFile("channel_play/channel-play-view_.proto", fileDescriptor_channel_play_view__4b7f1c05e772b728)
}

var fileDescriptor_channel_play_view__4b7f1c05e772b728 = []byte{
	// 1415 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0x4d, 0x6f, 0xdb, 0x46,
	0x13, 0x8e, 0x2c, 0xc9, 0x96, 0x46, 0x22, 0x4d, 0x6f, 0x92, 0xf7, 0x65, 0x92, 0xa6, 0x75, 0x85,
	0xb8, 0x70, 0x0c, 0x44, 0x41, 0x1c, 0xf4, 0xd8, 0xa4, 0xf1, 0x57, 0xe2, 0x38, 0xfe, 0x00, 0x2d,
	0x27, 0x48, 0x2f, 0xc4, 0x8a, 0x5a, 0x33, 0x84, 0xc9, 0x5d, 0x81, 0x5c, 0x39, 0xd1, 0xb9, 0x87,
	0x16, 0xe8, 0xdf, 0xe8, 0xef, 0x28, 0x7a, 0xe8, 0xcf, 0xea, 0xa1, 0x98, 0x59, 0x4a, 0x26, 0x6d,
	0xc7, 0x09, 0xd2, 0x4b, 0x6f, 0x3b, 0xcf, 0xce, 0x2e, 0x77, 0x9e, 0x79, 0x76, 0x66, 0x09, 0x4b,
	0xc1, 0x3b, 0x2e, 0xa5, 0x88, 0xfd, 0x61, 0xcc, 0xc7, 0x0f, 0x73, 0xe3, 0x01, 0x1a, 0x0f, 0x4e,
	0x23, 0xf1, 0xde, 0xef, 0x0e, 0x53, 0xa5, 0x15, 0x9b, 0x0f, 0x79, 0xb7, 0xe8, 0xd9, 0xf9, 0xbb,
	0x02, 0x6c, 0xdd, 0x00, 0xaf, 0x23, 0xf1, 0x7e, 0x43, 0x1c, 0xf3, 0x51, 0xac, 0xd9, 0x1d, 0x68,
	0x0e, 0x44, 0x16, 0xf8, 0x71, 0x94, 0x69, 0xb7, 0xb2, 0x58, 0x5d, 0x6e, 0x7a, 0x0d, 0x04, 0x5e,
	0x45, 0x99, 0x66, 0xb7, 0xa0, 0xa1, 0x79, 0xdf, 0x97, 0x3c, 0x11, 0xee, 0xcc, 0x62, 0x65, 0xb9,
	0xe9, 0xcd, 0x69, 0xde, 0xdf, 0xe3, 0x89, 0x60, 0x77, 0x01, 0x02, 0x95, 0x4a, 0x91, 0xfa, 0x51,
	0x12, 0xba, 0x55, 0x9a, 0x6c, 0x1a, 0x64, 0x3b, 0x09, 0x59, 0x07, 0xac, 0x7c, 0xba, 0x1f, 0x92,
	0x47, 0x8d, 0x3c, 0x5a, 0x06, 0x5c, 0x0b, 0xd1, 0x67, 0x09, 0xec, 0x54, 0xa9, 0xc4, 0x0f, 0x94,
	0x1c, 0x44, 0x3a, 0x52, 0xd2, 0xad, 0x93, 0x93, 0x85, 0xe8, 0xfa, 0x04, 0x64, 0x1b, 0x60, 0xf1,
	0xd1, 0x20, 0x12, 0x32, 0x10, 0x7e, 0x24, 0x8f, 0x95, 0x3b, 0xbb, 0x58, 0x5d, 0x6e, 0xad, 0x7e,
	0xd3, 0x3d, 0x17, 0x61, 0xf7, 0x28, 0x13, 0xe9, 0xb6, 0x3c, 0x56, 0x5b, 0x0a, 0x8f, 0xe0, 0xb5,
	0x27, 0xab, 0x10, 0xeb, 0xfc, 0x5a, 0x85, 0xf9, 0x42, 0xf8, 0xbb, 0xfb, 0x6b, 0xcf, 0xd8, 0x2b,
	0x00, 0x8a, 0xfd, 0x38, 0x4a, 0xf3, 0xe0, 0x5b, 0xab, 0x0f, 0x2e, 0x6c, 0x7b, 0x6e, 0x55, 0x77,
	0x43, 0x64, 0xc1, 0x33, 0x39, 0x58, 0x57, 0xb1, 0x4a, 0x3d, 0x22, 0x6f, 0x0b, 0xd7, 0x97, 0x99,
	0x9c, 0xb9, 0x82, 0xc9, 0xea, 0x55, 0x4c, 0xd6, 0x3e, 0xc9, 0x64, 0xfd, 0x73, 0x98, 0x9c, 0xfd,
	0x2c, 0x26, 0x1b, 0x5f, 0xc0, 0xe4, 0xed, 0xa7, 0xd0, 0x2e, 0x52, 0xc0, 0x18, 0xd4, 0x30, 0x4c,
	0xb7, 0x42, 0x9f, 0xa4, 0x31, 0x72, 0x91, 0xf0, 0xf4, 0xc4, 0xef, 0xc7, 0x23, 0xa3, 0x9c, 0x86,
	0xd7, 0x40, 0x60, 0x2d, 0x1e, 0x89, 0xce, 0x2f, 0x55, 0xb0, 0x0b, 0xa4, 0xee, 0xe8, 0x53, 0xb6,
	0x0c, 0x8e, 0x4a, 0x07, 0x22, 0xf5, 0x33, 0x25, 0x43, 0x3f, 0x50, 0x23, 0xa9, 0x69, 0x3f, 0xcb,
	0xb3, 0x09, 0x3f, 0x54, 0x32, 0x5c, 0x47, 0x94, 0xdd, 0x03, 0x3b, 0x8b, 0x64, 0x68, 0x1c, 0x0b,
	0xc2, 0x6c, 0x23, 0x8a, 0x6e, 0xc4, 0xe9, 0x7f, 0x82, 0xee, 0x1f, 0x00, 0x92, 0x51, 0x16, 0x05,
	0x7e, 0x14, 0x28, 0xe9, 0xce, 0x2d, 0x56, 0x96, 0xed, 0xd5, 0xaf, 0x2f, 0x70, 0xbd, 0x8b, 0x2e,
	0x48, 0xe8, 0x76, 0xa0, 0xa4, 0xd7, 0xa4, 0x15, 0x38, 0x2c, 0xeb, 0xa9, 0x71, 0x4e, 0x4f, 0x17,
	0x52, 0xd9, 0xfc, 0x92, 0x4b, 0xf1, 0x57, 0x1d, 0xae, 0x17, 0x32, 0xb1, 0x27, 0xde, 0xd3, 0x69,
	0x30, 0xa5, 0xc8, 0xef, 0x24, 0xa5, 0x38, 0x66, 0x37, 0xa0, 0xae, 0xd5, 0x30, 0x0a, 0x72, 0xbe,
	0x8d, 0x81, 0x6c, 0xd2, 0xc0, 0xc4, 0x98, 0x97, 0x01, 0x42, 0x28, 0x86, 0xe9, 0xb4, 0x1e, 0x0f,
	0x05, 0x91, 0x5d, 0xcf, 0xa7, 0x7b, 0xe3, 0xa1, 0x60, 0xff, 0x83, 0xd9, 0x4c, 0x73, 0x3d, 0xca,
	0x72, 0x96, 0x73, 0x8b, 0xdd, 0x84, 0x59, 0x4c, 0x5f, 0x34, 0x20, 0x62, 0x2d, 0xaf, 0xae, 0x79,
	0x7f, 0x7b, 0xc0, 0x1e, 0x43, 0x3d, 0x8c, 0x55, 0x3a, 0x26, 0x2e, 0x5b, 0xab, 0x77, 0x2f, 0x04,
	0xbb, 0xa3, 0x4f, 0x9f, 0xa3, 0xc3, 0x06, 0xd7, 0xdc, 0x33, 0xbe, 0x6c, 0x1d, 0xda, 0x26, 0x0b,
	0x99, 0x0a, 0x22, 0x1e, 0xbb, 0x0d, 0x5a, 0xbb, 0x78, 0x79, 0x1e, 0x0e, 0xc9, 0x87, 0x96, 0xb7,
	0x92, 0x33, 0x60, 0xa2, 0x27, 0x0a, 0xb2, 0x39, 0xd5, 0x13, 0x85, 0x98, 0x4f, 0xd1, 0x15, 0x80,
	0xe9, 0x14, 0x26, 0x94, 0x1d, 0x94, 0xea, 0x4b, 0x8b, 0x32, 0xf4, 0xe8, 0xaa, 0xfa, 0x32, 0x49,
	0xc0, 0xe7, 0xd5, 0x98, 0xf6, 0x39, 0x4d, 0xdc, 0x86, 0x06, 0x0f, 0xe8, 0xee, 0x64, 0xae, 0x65,
	0xe6, 0x26, 0x36, 0x66, 0x34, 0x56, 0xa1, 0x72, 0x6d, 0x93, 0x51, 0x1c, 0xb3, 0x27, 0xd0, 0x88,
	0xa4, 0x16, 0xa9, 0xc8, 0xb4, 0x3b, 0x4f, 0xac, 0x74, 0x2e, 0x67, 0x65, 0x3b, 0xf7, 0x22, 0x5e,
	0xa6, 0x6b, 0xd8, 0x16, 0x34, 0x33, 0x9e, 0x08, 0x3f, 0x88, 0xf4, 0xd8, 0x75, 0x68, 0x83, 0xfb,
	0x1f, 0x8b, 0xce, 0xb0, 0xcb, 0x13, 0xb1, 0x1e, 0xe9, 0x31, 0x6a, 0xcf, 0x6b, 0x64, 0xb9, 0xf5,
	0xef, 0x0b, 0xca, 0x7d, 0x58, 0xb8, 0x70, 0xce, 0xcb, 0xf5, 0xda, 0xf9, 0xb9, 0x02, 0xee, 0xc7,
	0x8e, 0x84, 0x1f, 0xc1, 0x58, 0x4c, 0xd9, 0x30, 0x5f, 0x6f, 0x20, 0x40, 0x75, 0xe3, 0x25, 0xd8,
	0xa3, 0x4c, 0xa4, 0x14, 0xed, 0x59, 0x8d, 0x6f, 0xad, 0xde, 0xfb, 0x58, 0xc8, 0x78, 0xf3, 0xa6,
	0xd1, 0xb6, 0x47, 0xb9, 0x85, 0x99, 0xea, 0x6c, 0x4c, 0xaf, 0x5d, 0xd1, 0x89, 0x39, 0x50, 0x1d,
	0x45, 0x83, 0xbc, 0xf0, 0xe1, 0xb0, 0x7c, 0xa2, 0x99, 0xf2, 0x89, 0x3a, 0xbf, 0x55, 0xa0, 0x5d,
	0x54, 0x3c, 0xde, 0x36, 0xd2, 0x7c, 0x31, 0x80, 0x26, 0x21, 0x14, 0xc1, 0x1d, 0x30, 0x06, 0x95,
	0xb5, 0x7c, 0x33, 0x02, 0xb0, 0xa6, 0x2d, 0x42, 0xdb, 0x4c, 0xe6, 0x65, 0xcf, 0x5c, 0x65, 0xb3,
	0x9f, 0xa9, 0x7a, 0xd3, 0xdd, 0x53, 0x2e, 0x4f, 0xe8, 0x2e, 0x5b, 0xf9, 0xee, 0x1e, 0x97, 0x27,
	0x9d, 0x3f, 0x2a, 0x30, 0x7f, 0xee, 0x0e, 0xb1, 0xef, 0x60, 0x3e, 0x11, 0x49, 0x5f, 0xa4, 0x7e,
	0xcc, 0xfb, 0x22, 0xf6, 0xfb, 0x93, 0xef, 0x5a, 0x06, 0x7e, 0x85, 0xe8, 0x5a, 0xc8, 0x56, 0x60,
	0xa1, 0xe4, 0xa7, 0xc5, 0x07, 0x9d, 0x9f, 0x60, 0xbe, 0xe0, 0xd9, 0x13, 0x1f, 0x34, 0x7b, 0x03,
	0x36, 0x1e, 0xc0, 0xcf, 0xa2, 0x50, 0x9a, 0xd2, 0x57, 0x23, 0xe9, 0x3d, 0xba, 0xea, 0x46, 0xe3,
	0x09, 0x5f, 0x28, 0xa9, 0xd2, 0xc3, 0x28, 0x94, 0xc8, 0x35, 0x49, 0xb9, 0x8d, 0x1b, 0x4d, 0x90,
	0x8e, 0x86, 0xc5, 0x4f, 0xad, 0x40, 0x69, 0x52, 0x0d, 0xc8, 0xa5, 0x89, 0x63, 0xec, 0x5d, 0x99,
	0x1e, 0xc7, 0xc2, 0x0f, 0x50, 0xbd, 0xc5, 0xf6, 0x6f, 0x13, 0x4e, 0xa2, 0xa6, 0x0b, 0xca, 0xa0,
	0x56, 0x88, 0x8c, 0xc6, 0x9d, 0x03, 0xb0, 0xcb, 0x25, 0xfa, 0x12, 0x15, 0xdc, 0x86, 0x06, 0xca,
	0xa7, 0x28, 0x82, 0x89, 0x8d, 0xde, 0x99, 0xf8, 0x40, 0x5b, 0x5a, 0x1e, 0x0e, 0x3b, 0x7f, 0x56,
	0xa7, 0x12, 0xa7, 0x37, 0xcb, 0x38, 0xd3, 0x22, 0x1d, 0x6f, 0x66, 0x01, 0x1f, 0x0a, 0xf6, 0x23,
	0xb4, 0x90, 0x1b, 0xec, 0x7e, 0x48, 0x5d, 0x85, 0xa8, 0xfb, 0x64, 0xd7, 0x00, 0xb3, 0x86, 0x44,
	0xea, 0xc2, 0xdc, 0x30, 0x55, 0x81, 0xc8, 0xb2, 0xc9, 0x93, 0x30, 0x37, 0x2f, 0xf6, 0xa4, 0xea,
	0x17, 0xf4, 0xa4, 0x4b, 0x9a, 0x6b, 0xed, 0xb2, 0xe6, 0xda, 0x85, 0xeb, 0x65, 0x37, 0xff, 0x38,
	0xe6, 0xa6, 0x5b, 0x5b, 0xde, 0x42, 0xc9, 0x77, 0x2b, 0xe6, 0x21, 0x8a, 0x9f, 0xfc, 0x13, 0x35,
	0x10, 0x79, 0x57, 0x69, 0x20, 0xb0, 0xab, 0x06, 0xe7, 0xdf, 0x04, 0x73, 0xe7, 0xdf, 0x04, 0x4b,
	0x60, 0x4f, 0x03, 0x33, 0x6f, 0x93, 0x06, 0x6d, 0x30, 0x0d, 0xd7, 0x3c, 0x4d, 0x96, 0xc0, 0xee,
	0xf3, 0xe0, 0x24, 0x4c, 0xd5, 0x48, 0x0e, 0x68, 0x27, 0xd3, 0x2a, 0xac, 0x33, 0x14, 0x77, 0xfb,
	0x16, 0xda, 0xfc, 0x94, 0x6b, 0x9e, 0xfa, 0x5a, 0xf1, 0x4c, 0xe7, 0x4d, 0xa3, 0x65, 0xb0, 0x1e,
	0x42, 0x1d, 0x55, 0x7a, 0xaa, 0xef, 0xf2, 0x34, 0x7b, 0xc7, 0x63, 0xf6, 0x14, 0xda, 0x89, 0x19,
	0x9a, 0x76, 0x5a, 0xa1, 0x17, 0xc5, 0x57, 0x17, 0x75, 0x6f, 0x9c, 0xb0, 0xc3, 0x7a, 0xad, 0xe4,
	0xcc, 0xc0, 0xd4, 0x05, 0x4a, 0x6a, 0x21, 0x35, 0xa5, 0xae, 0xed, 0x4d, 0xcc, 0x95, 0xdf, 0x2b,
	0xa5, 0xd7, 0x31, 0x79, 0xff, 0xbf, 0xf4, 0x36, 0xf0, 0xf3, 0x1f, 0x06, 0xe7, 0x1a, 0xbb, 0x01,
	0x4e, 0x71, 0x02, 0x1f, 0xc5, 0x4e, 0x85, 0x5d, 0x2f, 0xed, 0xe0, 0xef, 0xe8, 0x53, 0x67, 0x86,
	0xdd, 0x85, 0x5b, 0x25, 0xd7, 0xa2, 0x16, 0x9d, 0xea, 0xe4, 0x13, 0x42, 0xe6, 0xd3, 0xe6, 0xac,
	0x4e, 0x8d, 0xdd, 0x84, 0x85, 0xd2, 0x3a, 0xbc, 0x97, 0x4e, 0x7d, 0xe5, 0x05, 0x58, 0xa5, 0xe7,
	0x12, 0x5b, 0x00, 0x6b, 0x32, 0xf6, 0xf7, 0x94, 0x14, 0xce, 0x35, 0xc6, 0xc0, 0x9e, 0x42, 0xbb,
	0x47, 0x87, 0xdb, 0xeb, 0x4e, 0x85, 0x39, 0xa6, 0xc3, 0x10, 0xb6, 0xd3, 0x7b, 0xed, 0xcc, 0xac,
	0x3c, 0x81, 0x56, 0x81, 0x26, 0x74, 0x38, 0xda, 0xdb, 0xd9, 0xdb, 0x7f, 0xb3, 0xe7, 0xf7, 0xde,
	0x1e, 0x6c, 0x3a, 0xd7, 0x98, 0x05, 0xcd, 0x97, 0x87, 0xfb, 0xb9, 0x59, 0x61, 0x36, 0xc0, 0x81,
	0xb7, 0xdf, 0xdb, 0x37, 0xf6, 0xcc, 0xca, 0x00, 0xac, 0xe7, 0x42, 0xe2, 0xe1, 0x0e, 0xd5, 0x28,
	0x0d, 0x04, 0xfa, 0x6f, 0x79, 0xfb, 0xbb, 0xfe, 0xf3, 0x67, 0xbb, 0xb8, 0xdc, 0x06, 0x20, 0xb3,
	0x70, 0x02, 0x63, 0xbf, 0x3d, 0xec, 0x6d, 0x7a, 0x6f, 0x9d, 0x99, 0x29, 0xe2, 0x6d, 0xbe, 0xde,
	0xdc, 0x3b, 0xda, 0x74, 0xaa, 0x67, 0x3e, 0x3d, 0xb3, 0x4b, 0x6d, 0x6d, 0x13, 0xdc, 0x40, 0x25,
	0xdd, 0x71, 0x34, 0x56, 0x23, 0x4c, 0x33, 0x8a, 0x37, 0x36, 0x3f, 0x78, 0x3f, 0xdd, 0x0f, 0x55,
	0xcc, 0x65, 0xd8, 0xfd, 0x7e, 0x55, 0xeb, 0x6e, 0xa0, 0x92, 0x87, 0x04, 0x07, 0x2a, 0x7e, 0xc8,
	0x87, 0xc3, 0xd2, 0x7f, 0x61, 0x7f, 0x96, 0xa6, 0x1e, 0xff, 0x13, 0x00, 0x00, 0xff, 0xff, 0x3a,
	0x25, 0x6e, 0x6a, 0x3b, 0x0e, 0x00, 0x00,
}
