// Code generated by protoc-gen-go. DO NOT EDIT.
// source: fast_pc_logic/fast_pc_logic.proto

package fast_pc_logic // import "golang.52tt.com/protocol/app/fast-pc-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ToolBoxEntranceType int32

const (
	ToolBoxEntranceType_TOOL_BOX_ENTRANCE_TYPE_UNSPECIFIED ToolBoxEntranceType = 0
	ToolBoxEntranceType_TOOL_BOX_ENTRANCE_TYPE_MOD         ToolBoxEntranceType = 1
	ToolBoxEntranceType_TOOL_BOX_ENTRANCE_TYPE_JUMP_LINK   ToolBoxEntranceType = 2
)

var ToolBoxEntranceType_name = map[int32]string{
	0: "TOOL_BOX_ENTRANCE_TYPE_UNSPECIFIED",
	1: "TOOL_BOX_ENTRANCE_TYPE_MOD",
	2: "TOOL_BOX_ENTRANCE_TYPE_JUMP_LINK",
}
var ToolBoxEntranceType_value = map[string]int32{
	"TOOL_BOX_ENTRANCE_TYPE_UNSPECIFIED": 0,
	"TOOL_BOX_ENTRANCE_TYPE_MOD":         1,
	"TOOL_BOX_ENTRANCE_TYPE_JUMP_LINK":   2,
}

func (x ToolBoxEntranceType) String() string {
	return proto.EnumName(ToolBoxEntranceType_name, int32(x))
}
func (ToolBoxEntranceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fast_pc_logic_8f508040cbbeba53, []int{0}
}

type ToolBoxEntrance struct {
	// 名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 图标
	Icon string `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	// 入口类型 see enum ToolBoxEntranceType
	EntranceType uint32 `protobuf:"varint,3,opt,name=entrance_type,json=entranceType,proto3" json:"entrance_type,omitempty"`
	// 客户端跳转短链
	TtLink               string   `protobuf:"bytes,4,opt,name=tt_link,json=ttLink,proto3" json:"tt_link,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ToolBoxEntrance) Reset()         { *m = ToolBoxEntrance{} }
func (m *ToolBoxEntrance) String() string { return proto.CompactTextString(m) }
func (*ToolBoxEntrance) ProtoMessage()    {}
func (*ToolBoxEntrance) Descriptor() ([]byte, []int) {
	return fileDescriptor_fast_pc_logic_8f508040cbbeba53, []int{0}
}
func (m *ToolBoxEntrance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ToolBoxEntrance.Unmarshal(m, b)
}
func (m *ToolBoxEntrance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ToolBoxEntrance.Marshal(b, m, deterministic)
}
func (dst *ToolBoxEntrance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ToolBoxEntrance.Merge(dst, src)
}
func (m *ToolBoxEntrance) XXX_Size() int {
	return xxx_messageInfo_ToolBoxEntrance.Size(m)
}
func (m *ToolBoxEntrance) XXX_DiscardUnknown() {
	xxx_messageInfo_ToolBoxEntrance.DiscardUnknown(m)
}

var xxx_messageInfo_ToolBoxEntrance proto.InternalMessageInfo

func (m *ToolBoxEntrance) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ToolBoxEntrance) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ToolBoxEntrance) GetEntranceType() uint32 {
	if m != nil {
		return m.EntranceType
	}
	return 0
}

func (m *ToolBoxEntrance) GetTtLink() string {
	if m != nil {
		return m.TtLink
	}
	return ""
}

type GetToolBoxEntranceReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetToolBoxEntranceReq) Reset()         { *m = GetToolBoxEntranceReq{} }
func (m *GetToolBoxEntranceReq) String() string { return proto.CompactTextString(m) }
func (*GetToolBoxEntranceReq) ProtoMessage()    {}
func (*GetToolBoxEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fast_pc_logic_8f508040cbbeba53, []int{1}
}
func (m *GetToolBoxEntranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetToolBoxEntranceReq.Unmarshal(m, b)
}
func (m *GetToolBoxEntranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetToolBoxEntranceReq.Marshal(b, m, deterministic)
}
func (dst *GetToolBoxEntranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetToolBoxEntranceReq.Merge(dst, src)
}
func (m *GetToolBoxEntranceReq) XXX_Size() int {
	return xxx_messageInfo_GetToolBoxEntranceReq.Size(m)
}
func (m *GetToolBoxEntranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetToolBoxEntranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetToolBoxEntranceReq proto.InternalMessageInfo

func (m *GetToolBoxEntranceReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetToolBoxEntranceResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Entrances            []*ToolBoxEntrance `protobuf:"bytes,2,rep,name=entrances,proto3" json:"entrances,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetToolBoxEntranceResp) Reset()         { *m = GetToolBoxEntranceResp{} }
func (m *GetToolBoxEntranceResp) String() string { return proto.CompactTextString(m) }
func (*GetToolBoxEntranceResp) ProtoMessage()    {}
func (*GetToolBoxEntranceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fast_pc_logic_8f508040cbbeba53, []int{2}
}
func (m *GetToolBoxEntranceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetToolBoxEntranceResp.Unmarshal(m, b)
}
func (m *GetToolBoxEntranceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetToolBoxEntranceResp.Marshal(b, m, deterministic)
}
func (dst *GetToolBoxEntranceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetToolBoxEntranceResp.Merge(dst, src)
}
func (m *GetToolBoxEntranceResp) XXX_Size() int {
	return xxx_messageInfo_GetToolBoxEntranceResp.Size(m)
}
func (m *GetToolBoxEntranceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetToolBoxEntranceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetToolBoxEntranceResp proto.InternalMessageInfo

func (m *GetToolBoxEntranceResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetToolBoxEntranceResp) GetEntrances() []*ToolBoxEntrance {
	if m != nil {
		return m.Entrances
	}
	return nil
}

type GetModTagListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetModTagListReq) Reset()         { *m = GetModTagListReq{} }
func (m *GetModTagListReq) String() string { return proto.CompactTextString(m) }
func (*GetModTagListReq) ProtoMessage()    {}
func (*GetModTagListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fast_pc_logic_8f508040cbbeba53, []int{3}
}
func (m *GetModTagListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetModTagListReq.Unmarshal(m, b)
}
func (m *GetModTagListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetModTagListReq.Marshal(b, m, deterministic)
}
func (dst *GetModTagListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModTagListReq.Merge(dst, src)
}
func (m *GetModTagListReq) XXX_Size() int {
	return xxx_messageInfo_GetModTagListReq.Size(m)
}
func (m *GetModTagListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModTagListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetModTagListReq proto.InternalMessageInfo

func (m *GetModTagListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetModTagListResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 标签列表，不包括【全部】这个标签
	Tags []string `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
	// qq咨询群链接
	QueryChatLink        string   `protobuf:"bytes,3,opt,name=query_chat_link,json=queryChatLink,proto3" json:"query_chat_link,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetModTagListResp) Reset()         { *m = GetModTagListResp{} }
func (m *GetModTagListResp) String() string { return proto.CompactTextString(m) }
func (*GetModTagListResp) ProtoMessage()    {}
func (*GetModTagListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fast_pc_logic_8f508040cbbeba53, []int{4}
}
func (m *GetModTagListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetModTagListResp.Unmarshal(m, b)
}
func (m *GetModTagListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetModTagListResp.Marshal(b, m, deterministic)
}
func (dst *GetModTagListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModTagListResp.Merge(dst, src)
}
func (m *GetModTagListResp) XXX_Size() int {
	return xxx_messageInfo_GetModTagListResp.Size(m)
}
func (m *GetModTagListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModTagListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetModTagListResp proto.InternalMessageInfo

func (m *GetModTagListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetModTagListResp) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GetModTagListResp) GetQueryChatLink() string {
	if m != nil {
		return m.QueryChatLink
	}
	return ""
}

type ModDetail struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// 图片
	ImgUrl string `protobuf:"bytes,3,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	// 作者
	Author string `protobuf:"bytes,4,opt,name=author,proto3" json:"author,omitempty"`
	// 游戏名称
	GameName string `protobuf:"bytes,5,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	// 发布时间，单位秒
	UpdateTime uint32 `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 详情描述
	Desc string `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	// mod文件下载链接
	FileUrl string `protobuf:"bytes,8,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	// mod文件大小，单位KB
	FileSize uint32 `protobuf:"varint,9,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	// 游戏短称，用于创建客户端文件夹
	GameShortName        string   `protobuf:"bytes,10,opt,name=game_short_name,json=gameShortName,proto3" json:"game_short_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModDetail) Reset()         { *m = ModDetail{} }
func (m *ModDetail) String() string { return proto.CompactTextString(m) }
func (*ModDetail) ProtoMessage()    {}
func (*ModDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_fast_pc_logic_8f508040cbbeba53, []int{5}
}
func (m *ModDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModDetail.Unmarshal(m, b)
}
func (m *ModDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModDetail.Marshal(b, m, deterministic)
}
func (dst *ModDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModDetail.Merge(dst, src)
}
func (m *ModDetail) XXX_Size() int {
	return xxx_messageInfo_ModDetail.Size(m)
}
func (m *ModDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ModDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ModDetail proto.InternalMessageInfo

func (m *ModDetail) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ModDetail) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ModDetail) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *ModDetail) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *ModDetail) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *ModDetail) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ModDetail) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ModDetail) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *ModDetail) GetFileSize() uint32 {
	if m != nil {
		return m.FileSize
	}
	return 0
}

func (m *ModDetail) GetGameShortName() string {
	if m != nil {
		return m.GameShortName
	}
	return ""
}

type GetModListReq struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 搜索内容
	Search string `protobuf:"bytes,2,opt,name=search,proto3" json:"search,omitempty"`
	// modId查询
	Id string `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	// tag查询
	Tag                  string   `protobuf:"bytes,4,opt,name=tag,proto3" json:"tag,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32   `protobuf:"varint,6,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetModListReq) Reset()         { *m = GetModListReq{} }
func (m *GetModListReq) String() string { return proto.CompactTextString(m) }
func (*GetModListReq) ProtoMessage()    {}
func (*GetModListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fast_pc_logic_8f508040cbbeba53, []int{6}
}
func (m *GetModListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetModListReq.Unmarshal(m, b)
}
func (m *GetModListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetModListReq.Marshal(b, m, deterministic)
}
func (dst *GetModListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModListReq.Merge(dst, src)
}
func (m *GetModListReq) XXX_Size() int {
	return xxx_messageInfo_GetModListReq.Size(m)
}
func (m *GetModListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetModListReq proto.InternalMessageInfo

func (m *GetModListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetModListReq) GetSearch() string {
	if m != nil {
		return m.Search
	}
	return ""
}

func (m *GetModListReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetModListReq) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *GetModListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetModListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type GetModListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ModList              []*ModDetail  `protobuf:"bytes,2,rep,name=mod_list,json=modList,proto3" json:"mod_list,omitempty"`
	Total                uint32        `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetModListResp) Reset()         { *m = GetModListResp{} }
func (m *GetModListResp) String() string { return proto.CompactTextString(m) }
func (*GetModListResp) ProtoMessage()    {}
func (*GetModListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fast_pc_logic_8f508040cbbeba53, []int{7}
}
func (m *GetModListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetModListResp.Unmarshal(m, b)
}
func (m *GetModListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetModListResp.Marshal(b, m, deterministic)
}
func (dst *GetModListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModListResp.Merge(dst, src)
}
func (m *GetModListResp) XXX_Size() int {
	return xxx_messageInfo_GetModListResp.Size(m)
}
func (m *GetModListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetModListResp proto.InternalMessageInfo

func (m *GetModListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetModListResp) GetModList() []*ModDetail {
	if m != nil {
		return m.ModList
	}
	return nil
}

func (m *GetModListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func init() {
	proto.RegisterType((*ToolBoxEntrance)(nil), "ga.fast_pc_logic.ToolBoxEntrance")
	proto.RegisterType((*GetToolBoxEntranceReq)(nil), "ga.fast_pc_logic.GetToolBoxEntranceReq")
	proto.RegisterType((*GetToolBoxEntranceResp)(nil), "ga.fast_pc_logic.GetToolBoxEntranceResp")
	proto.RegisterType((*GetModTagListReq)(nil), "ga.fast_pc_logic.GetModTagListReq")
	proto.RegisterType((*GetModTagListResp)(nil), "ga.fast_pc_logic.GetModTagListResp")
	proto.RegisterType((*ModDetail)(nil), "ga.fast_pc_logic.ModDetail")
	proto.RegisterType((*GetModListReq)(nil), "ga.fast_pc_logic.GetModListReq")
	proto.RegisterType((*GetModListResp)(nil), "ga.fast_pc_logic.GetModListResp")
	proto.RegisterEnum("ga.fast_pc_logic.ToolBoxEntranceType", ToolBoxEntranceType_name, ToolBoxEntranceType_value)
}

func init() {
	proto.RegisterFile("fast_pc_logic/fast_pc_logic.proto", fileDescriptor_fast_pc_logic_8f508040cbbeba53)
}

var fileDescriptor_fast_pc_logic_8f508040cbbeba53 = []byte{
	// 682 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x54, 0xcf, 0x4f, 0xdb, 0x4a,
	0x10, 0x7e, 0x4e, 0x20, 0x89, 0x27, 0x04, 0xf2, 0xf6, 0xbd, 0xc7, 0x73, 0x41, 0x6a, 0x43, 0x5a,
	0x21, 0x8a, 0x84, 0x91, 0xa8, 0xda, 0x43, 0x2f, 0xa8, 0x81, 0x80, 0x68, 0xf3, 0x03, 0x19, 0x23,
	0xb5, 0xbd, 0xac, 0x36, 0xf6, 0xe2, 0xac, 0xb0, 0xbd, 0xc6, 0xde, 0xa8, 0x0d, 0xd7, 0xf6, 0xd2,
	0x7b, 0x2f, 0xfd, 0x6f, 0xab, 0xfd, 0x01, 0x94, 0xb4, 0x1c, 0x72, 0x9b, 0xf9, 0xc6, 0x3b, 0xfb,
	0xcd, 0x37, 0xdf, 0x1a, 0x36, 0x2e, 0x48, 0x21, 0x70, 0x16, 0xe0, 0x98, 0x47, 0x2c, 0xd8, 0xbd,
	0x97, 0xb9, 0x59, 0xce, 0x05, 0x47, 0xcd, 0x88, 0xb8, 0xf7, 0xf0, 0xb5, 0x46, 0x44, 0xf0, 0x88,
	0x14, 0x54, 0x7f, 0xd0, 0xfe, 0x04, 0x2b, 0x3e, 0xe7, 0x71, 0x87, 0x7f, 0xee, 0xa6, 0x22, 0x27,
	0x69, 0x40, 0x11, 0x82, 0x85, 0x94, 0x24, 0xd4, 0xb1, 0x5a, 0xd6, 0x96, 0xed, 0xa9, 0x58, 0x62,
	0x2c, 0xe0, 0xa9, 0x53, 0xd2, 0x98, 0x8c, 0xd1, 0x53, 0x68, 0x50, 0x73, 0x06, 0x8b, 0x69, 0x46,
	0x9d, 0x72, 0xcb, 0xda, 0x6a, 0x78, 0x4b, 0x37, 0xa0, 0x3f, 0xcd, 0x28, 0xfa, 0x1f, 0xaa, 0x42,
	0xe0, 0x98, 0xa5, 0x97, 0xce, 0x82, 0x3a, 0x5b, 0x11, 0xa2, 0xc7, 0xd2, 0xcb, 0xf6, 0x3e, 0xfc,
	0x77, 0x4c, 0xc5, 0xcc, 0xdd, 0x1e, 0xbd, 0x42, 0x9b, 0x50, 0x93, 0xfc, 0x70, 0x4e, 0xaf, 0x14,
	0x85, 0xfa, 0x5e, 0xdd, 0x8d, 0x88, 0xdb, 0x21, 0x85, 0x2c, 0x7b, 0xd5, 0x91, 0x0e, 0xda, 0x5f,
	0x2d, 0x58, 0xfd, 0x53, 0x87, 0x22, 0x43, 0xcf, 0xc1, 0x36, 0x2d, 0x8a, 0xcc, 0xf4, 0x58, 0xba,
	0xeb, 0x51, 0x64, 0x5e, 0x6d, 0x64, 0x22, 0xb4, 0x0f, 0xf6, 0x0d, 0xdf, 0xc2, 0x29, 0xb5, 0xca,
	0x5b, 0xf5, 0xbd, 0x0d, 0x77, 0x56, 0x34, 0x77, 0xf6, 0x92, 0xbb, 0x33, 0xed, 0xd7, 0xd0, 0x3c,
	0xa6, 0xa2, 0xcf, 0x43, 0x9f, 0x44, 0x3d, 0x56, 0x88, 0x79, 0x46, 0xb8, 0x86, 0xbf, 0x67, 0xce,
	0xce, 0x47, 0x1e, 0xc1, 0x82, 0x20, 0x91, 0xe6, 0x6d, 0x7b, 0x2a, 0x46, 0x9b, 0xb0, 0x72, 0x35,
	0xa1, 0xf9, 0x14, 0x07, 0x63, 0x62, 0x84, 0x2f, 0x2b, 0xe1, 0x1b, 0x0a, 0x3e, 0x18, 0x13, 0xad,
	0xff, 0xf7, 0x12, 0xd8, 0x7d, 0x1e, 0x1e, 0x52, 0x41, 0x58, 0x8c, 0x96, 0xa1, 0xc4, 0x42, 0xb3,
	0xf1, 0x12, 0x0b, 0xd1, 0xbf, 0xb0, 0x28, 0x98, 0x88, 0xa9, 0x59, 0xb8, 0x4e, 0xe4, 0x32, 0x59,
	0x12, 0xe1, 0x49, 0x1e, 0x9b, 0x9e, 0x15, 0x96, 0x44, 0xe7, 0x79, 0x8c, 0x56, 0xa1, 0x42, 0x26,
	0x62, 0xcc, 0xf3, 0x9b, 0x25, 0xeb, 0x0c, 0xad, 0x83, 0x1d, 0x91, 0x84, 0x62, 0xe5, 0xa7, 0x45,
	0x55, 0xaa, 0x49, 0x60, 0x20, 0x3d, 0xf5, 0x04, 0xea, 0x93, 0x2c, 0x24, 0x82, 0x62, 0xc1, 0x12,
	0xea, 0x54, 0x94, 0x7b, 0x40, 0x43, 0x3e, 0xd3, 0xa6, 0x0b, 0x69, 0x11, 0x38, 0x55, 0x6d, 0x3a,
	0x19, 0xa3, 0x47, 0x50, 0xbb, 0x60, 0x31, 0x55, 0x1c, 0x6a, 0x0a, 0xaf, 0xca, 0x5c, 0x92, 0x58,
	0x07, 0x5b, 0x95, 0x0a, 0x76, 0x4d, 0x1d, 0x5b, 0x75, 0x53, 0xdf, 0x9e, 0xb1, 0x6b, 0x2a, 0x65,
	0x51, 0x4c, 0x8a, 0x31, 0xcf, 0x85, 0xe6, 0x03, 0x5a, 0x16, 0x09, 0x9f, 0x49, 0x54, 0x92, 0x6a,
	0xff, 0xb0, 0xa0, 0xa1, 0x77, 0x32, 0xe7, 0x32, 0xa5, 0x06, 0x05, 0x25, 0x79, 0x30, 0x36, 0x9a,
	0x99, 0xcc, 0x48, 0x5b, 0xbe, 0x95, 0xb6, 0x09, 0x65, 0x41, 0x22, 0x23, 0x94, 0x0c, 0xe5, 0x9c,
	0x19, 0x89, 0xb4, 0x40, 0x0d, 0x4f, 0xc5, 0x12, 0x53, 0x73, 0x68, 0x55, 0x54, 0xdc, 0xfe, 0x66,
	0xc1, 0xf2, 0xaf, 0xdc, 0xe6, 0x33, 0xcb, 0x2b, 0xa8, 0x25, 0x3c, 0xc4, 0x31, 0x2b, 0x84, 0x31,
	0xfa, 0xfa, 0xef, 0x46, 0xbf, 0x75, 0x84, 0x57, 0x4d, 0xf4, 0x35, 0xca, 0x0a, 0x5c, 0x90, 0xd8,
	0x3c, 0x6f, 0x9d, 0x6c, 0x7f, 0xb1, 0xe0, 0x9f, 0x99, 0x57, 0xa1, 0xde, 0xfb, 0x26, 0xb4, 0xfd,
	0xe1, 0xb0, 0x87, 0x3b, 0xc3, 0xf7, 0xb8, 0x3b, 0xf0, 0xbd, 0x37, 0x83, 0x83, 0x2e, 0xf6, 0x3f,
	0x9c, 0x76, 0xf1, 0xf9, 0xe0, 0xec, 0xb4, 0x7b, 0x70, 0x72, 0x74, 0xd2, 0x3d, 0x6c, 0xfe, 0x85,
	0x1e, 0xc3, 0xda, 0x03, 0xdf, 0xf5, 0x87, 0x87, 0x4d, 0x0b, 0x3d, 0x83, 0xd6, 0x03, 0xf5, 0xb7,
	0xe7, 0xfd, 0x53, 0xdc, 0x3b, 0x19, 0xbc, 0x6b, 0x96, 0x3a, 0x47, 0xe0, 0x04, 0x3c, 0x71, 0xa7,
	0x6c, 0xca, 0x27, 0x72, 0x98, 0x84, 0x87, 0x34, 0xd6, 0x7f, 0xb6, 0x8f, 0xdb, 0x11, 0x8f, 0x49,
	0x1a, 0xb9, 0x2f, 0xf7, 0x84, 0x70, 0x03, 0x9e, 0xec, 0x2a, 0x38, 0xe0, 0xf1, 0x2e, 0xc9, 0x32,
	0xf5, 0xb3, 0xdc, 0xc9, 0x82, 0x1d, 0x35, 0xf6, 0xa8, 0xa2, 0x6a, 0x2f, 0x7e, 0x06, 0x00, 0x00,
	0xff, 0xff, 0xa9, 0xb0, 0xd6, 0xd6, 0x52, 0x05, 0x00, 0x00,
}
