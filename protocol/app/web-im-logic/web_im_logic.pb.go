// Code generated by protoc-gen-go. DO NOT EDIT.
// source: web_im_logic/web_im_logic.proto

package web_im_logic // import "golang.52tt.com/protocol/app/web-im-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 业务唯一标识
type WebImCmd int32

const (
	WebImCmd_WEB_IM_CMD_UNSPECIFIED WebImCmd = 0
	WebImCmd_WEB_IM_CMD_AI_PARTNER  WebImCmd = 1
	WebImCmd_WEB_IM_CMD_AI_GROUP    WebImCmd = 2
)

var WebImCmd_name = map[int32]string{
	0: "WEB_IM_CMD_UNSPECIFIED",
	1: "WEB_IM_CMD_AI_PARTNER",
	2: "WEB_IM_CMD_AI_GROUP",
}
var WebImCmd_value = map[string]int32{
	"WEB_IM_CMD_UNSPECIFIED": 0,
	"WEB_IM_CMD_AI_PARTNER":  1,
	"WEB_IM_CMD_AI_GROUP":    2,
}

func (x WebImCmd) String() string {
	return proto.EnumName(WebImCmd_name, int32(x))
}
func (WebImCmd) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{0}
}

type SpecialMsgType int32

const (
	// 普通消息
	SpecialMsgType_SPECIAL_MSG_TYPE_NORMAL SpecialMsgType = 0
	// 指定用户可见
	SpecialMsgType_SPECIAL_MSG_TYPE_SPECIFIED_USER_VISIBLE SpecialMsgType = 1
)

var SpecialMsgType_name = map[int32]string{
	0: "SPECIAL_MSG_TYPE_NORMAL",
	1: "SPECIAL_MSG_TYPE_SPECIFIED_USER_VISIBLE",
}
var SpecialMsgType_value = map[string]int32{
	"SPECIAL_MSG_TYPE_NORMAL":                 0,
	"SPECIAL_MSG_TYPE_SPECIFIED_USER_VISIBLE": 1,
}

func (x SpecialMsgType) String() string {
	return proto.EnumName(SpecialMsgType_name, int32(x))
}
func (SpecialMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{1}
}

// 多人群组开始起用
// 内容类型
type ImMsgContentType int32

const (
	ImMsgContentType_IM_MSG_CONTENT_TYPE_UNSPECIFIED ImMsgContentType = 0
	// 文本
	ImMsgContentType_IM_MSG_CONTENT_TYPE_TEXT ImMsgContentType = 1
	// 文本+语音
	ImMsgContentType_IM_MSG_CONTENT_TYPE_TEXT_VOICE ImMsgContentType = 2
	// 表情
	ImMsgContentType_IM_MSG_CONTENT_TYPE_EMOTION ImMsgContentType = 3
	// 图片
	ImMsgContentType_IM_MSG_CONTENT_TYPE_IMAGE ImMsgContentType = 4
)

var ImMsgContentType_name = map[int32]string{
	0: "IM_MSG_CONTENT_TYPE_UNSPECIFIED",
	1: "IM_MSG_CONTENT_TYPE_TEXT",
	2: "IM_MSG_CONTENT_TYPE_TEXT_VOICE",
	3: "IM_MSG_CONTENT_TYPE_EMOTION",
	4: "IM_MSG_CONTENT_TYPE_IMAGE",
}
var ImMsgContentType_value = map[string]int32{
	"IM_MSG_CONTENT_TYPE_UNSPECIFIED": 0,
	"IM_MSG_CONTENT_TYPE_TEXT":        1,
	"IM_MSG_CONTENT_TYPE_TEXT_VOICE":  2,
	"IM_MSG_CONTENT_TYPE_EMOTION":     3,
	"IM_MSG_CONTENT_TYPE_IMAGE":       4,
}

func (x ImMsgContentType) String() string {
	return proto.EnumName(ImMsgContentType_name, int32(x))
}
func (ImMsgContentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{2}
}

// 业务类型
type ImBusiType int32

const (
	// 未知类型
	ImBusiType_IM_BUSI_TYPE_UNSPECIFIED ImBusiType = 0
	// 单人群聊
	ImBusiType_IM_BUSI_TYPE_SINGLE_GROUP ImBusiType = 1
	// 多人群聊
	ImBusiType_IM_BUSI_TYPE_MULTI_GROUP ImBusiType = 2
	// 多角色聊天
	ImBusiType_IM_BUSI_TYPE_MULTI_ROLE ImBusiType = 3
)

var ImBusiType_name = map[int32]string{
	0: "IM_BUSI_TYPE_UNSPECIFIED",
	1: "IM_BUSI_TYPE_SINGLE_GROUP",
	2: "IM_BUSI_TYPE_MULTI_GROUP",
	3: "IM_BUSI_TYPE_MULTI_ROLE",
}
var ImBusiType_value = map[string]int32{
	"IM_BUSI_TYPE_UNSPECIFIED":  0,
	"IM_BUSI_TYPE_SINGLE_GROUP": 1,
	"IM_BUSI_TYPE_MULTI_GROUP":  2,
	"IM_BUSI_TYPE_MULTI_ROLE":   3,
}

func (x ImBusiType) String() string {
	return proto.EnumName(ImBusiType_name, int32(x))
}
func (ImBusiType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{3}
}

// 命令类型 非内容消息需要指定业务类型前缀
type ImCmdType int32

const (
	// 未知类型
	ImCmdType_IM_CMD_TYPE_UNSPECIFIED ImCmdType = 0
	// 内容消息
	ImCmdType_IM_CMD_TYPE_CONTENT_MSG ImCmdType = 1
	// 句数提示
	ImCmdType_IM_CMD_TYPE_SENTENCE_TIP ImCmdType = 2
	// 进群提示消息
	ImCmdType_IM_CMD_TYPE_JOIN_GROUP_TIP ImCmdType = 3
	// 欢迎语消息
	ImCmdType_IM_CMD_TYPE_WELCOME_MSG ImCmdType = 4
	// 用户送礼消息
	ImCmdType_IM_CMD_TYPE_USER_PRESENT ImCmdType = 5
	// ai送礼消息
	ImCmdType_IM_CMD_TYPE_AI_PRESENT ImCmdType = 6
	// 用户送礼引导消息
	ImCmdType_IM_CMD_TYPE_USER_PRESENT_GUIDE ImCmdType = 7
)

var ImCmdType_name = map[int32]string{
	0: "IM_CMD_TYPE_UNSPECIFIED",
	1: "IM_CMD_TYPE_CONTENT_MSG",
	2: "IM_CMD_TYPE_SENTENCE_TIP",
	3: "IM_CMD_TYPE_JOIN_GROUP_TIP",
	4: "IM_CMD_TYPE_WELCOME_MSG",
	5: "IM_CMD_TYPE_USER_PRESENT",
	6: "IM_CMD_TYPE_AI_PRESENT",
	7: "IM_CMD_TYPE_USER_PRESENT_GUIDE",
}
var ImCmdType_value = map[string]int32{
	"IM_CMD_TYPE_UNSPECIFIED":        0,
	"IM_CMD_TYPE_CONTENT_MSG":        1,
	"IM_CMD_TYPE_SENTENCE_TIP":       2,
	"IM_CMD_TYPE_JOIN_GROUP_TIP":     3,
	"IM_CMD_TYPE_WELCOME_MSG":        4,
	"IM_CMD_TYPE_USER_PRESENT":       5,
	"IM_CMD_TYPE_AI_PRESENT":         6,
	"IM_CMD_TYPE_USER_PRESENT_GUIDE": 7,
}

func (x ImCmdType) String() string {
	return proto.EnumName(ImCmdType_name, int32(x))
}
func (ImCmdType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{4}
}

type H5PushType int32

const (
	H5PushType_H5_PUSH_TYPE_UNSPECIFIED H5PushType = 0
	// data为json.Marshal后的game_red_dot_logic.proto RedDotUpdateNotify
	H5PushType_H5_PUSH_TYPE_RED_DOT H5PushType = 1
	// 亲密度升级
	H5PushType_H5_PUSH_TYPE_INTIMACY_LEVEL_UP H5PushType = 2
	// 剧本匹配确认
	H5PushType_H5_PUSH_TYPE_SCRIPT_MATCH_CONFIRM H5PushType = 3
	// 剧本匹配成功
	H5PushType_H5_PUSH_TYPE_SCRIPT_MATCH_FOUND H5PushType = 4
	// 社区发帖引导
	H5PushType_H5_PUSH_TYPE_PUBLISH_POST_GUIDE H5PushType = 5
	// 社区发帖任务完成
	H5PushType_H5_PUSH_TYPE_POST_TASK_FINISH H5PushType = 6
	// 群内关注引导
	H5PushType_H5_PUSH_TYPE_GROUP_FOLLOW_GUIDE H5PushType = 7
	// 用户入群
	H5PushType_H5_PUSH_TYPE_GROUP_MEMBER_JOINED H5PushType = 8
	// 用户退群
	H5PushType_H5_PUSH_TYPE_GROUP_MEMBER_LEFT H5PushType = 9
)

var H5PushType_name = map[int32]string{
	0: "H5_PUSH_TYPE_UNSPECIFIED",
	1: "H5_PUSH_TYPE_RED_DOT",
	2: "H5_PUSH_TYPE_INTIMACY_LEVEL_UP",
	3: "H5_PUSH_TYPE_SCRIPT_MATCH_CONFIRM",
	4: "H5_PUSH_TYPE_SCRIPT_MATCH_FOUND",
	5: "H5_PUSH_TYPE_PUBLISH_POST_GUIDE",
	6: "H5_PUSH_TYPE_POST_TASK_FINISH",
	7: "H5_PUSH_TYPE_GROUP_FOLLOW_GUIDE",
	8: "H5_PUSH_TYPE_GROUP_MEMBER_JOINED",
	9: "H5_PUSH_TYPE_GROUP_MEMBER_LEFT",
}
var H5PushType_value = map[string]int32{
	"H5_PUSH_TYPE_UNSPECIFIED":          0,
	"H5_PUSH_TYPE_RED_DOT":              1,
	"H5_PUSH_TYPE_INTIMACY_LEVEL_UP":    2,
	"H5_PUSH_TYPE_SCRIPT_MATCH_CONFIRM": 3,
	"H5_PUSH_TYPE_SCRIPT_MATCH_FOUND":   4,
	"H5_PUSH_TYPE_PUBLISH_POST_GUIDE":   5,
	"H5_PUSH_TYPE_POST_TASK_FINISH":     6,
	"H5_PUSH_TYPE_GROUP_FOLLOW_GUIDE":   7,
	"H5_PUSH_TYPE_GROUP_MEMBER_JOINED":  8,
	"H5_PUSH_TYPE_GROUP_MEMBER_LEFT":    9,
}

func (x H5PushType) String() string {
	return proto.EnumName(H5PushType_name, int32(x))
}
func (H5PushType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{5}
}

type GroupSendType int32

const (
	GroupSendType_GROUP_SEND_TYPE_UNSPECIFIED GroupSendType = 0
	// 用户上发
	GroupSendType_GROUP_SEND_TYPE_USER2AI GroupSendType = 1
	// AI下发
	GroupSendType_GROUP_SEND_TYPE_AI2USER GroupSendType = 2
)

var GroupSendType_name = map[int32]string{
	0: "GROUP_SEND_TYPE_UNSPECIFIED",
	1: "GROUP_SEND_TYPE_USER2AI",
	2: "GROUP_SEND_TYPE_AI2USER",
}
var GroupSendType_value = map[string]int32{
	"GROUP_SEND_TYPE_UNSPECIFIED": 0,
	"GROUP_SEND_TYPE_USER2AI":     1,
	"GROUP_SEND_TYPE_AI2USER":     2,
}

func (x GroupSendType) String() string {
	return proto.EnumName(GroupSendType_name, int32(x))
}
func (GroupSendType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{6}
}

type ImMsg_Type int32

const (
	ImMsg_TYPE_UNSPECIFIED ImMsg_Type = 0
	// 文本
	ImMsg_TYPE_TEXT ImMsg_Type = 1
	// 表情
	ImMsg_TYPE_EMOTICON ImMsg_Type = 3
	// 沉默(询问用户是否切换AI伴侣到活跃状态)
	ImMsg_TYPE_SILENCE ImMsg_Type = 4
	// AI伴侣统一消息通道
	ImMsg_TYPE_AI_PARTNER ImMsg_Type = 6
)

var ImMsg_Type_name = map[int32]string{
	0: "TYPE_UNSPECIFIED",
	1: "TYPE_TEXT",
	3: "TYPE_EMOTICON",
	4: "TYPE_SILENCE",
	6: "TYPE_AI_PARTNER",
}
var ImMsg_Type_value = map[string]int32{
	"TYPE_UNSPECIFIED": 0,
	"TYPE_TEXT":        1,
	"TYPE_EMOTICON":    3,
	"TYPE_SILENCE":     4,
	"TYPE_AI_PARTNER":  6,
}

func (x ImMsg_Type) String() string {
	return proto.EnumName(ImMsg_Type_name, int32(x))
}
func (ImMsg_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{3, 0}
}

type CommonH5PushMsg_MarshalType int32

const (
	CommonH5PushMsg_MARSHAL_TYPE_UNSPECIFIED CommonH5PushMsg_MarshalType = 0
	CommonH5PushMsg_MARSHAL_TYPE_JSON        CommonH5PushMsg_MarshalType = 1
)

var CommonH5PushMsg_MarshalType_name = map[int32]string{
	0: "MARSHAL_TYPE_UNSPECIFIED",
	1: "MARSHAL_TYPE_JSON",
}
var CommonH5PushMsg_MarshalType_value = map[string]int32{
	"MARSHAL_TYPE_UNSPECIFIED": 0,
	"MARSHAL_TYPE_JSON":        1,
}

func (x CommonH5PushMsg_MarshalType) String() string {
	return proto.EnumName(CommonH5PushMsg_MarshalType_name, int32(x))
}
func (CommonH5PushMsg_MarshalType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{21, 0}
}

// IM消息推送
type ImMsgPush struct {
	// 业务唯一标识
	Cmd WebImCmd `protobuf:"varint,1,opt,name=cmd,proto3,enum=ga.web_im_logic.WebImCmd" json:"cmd,omitempty"`
	// 接收消息的用户id
	Uid uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// 发送方唯一标识
	Source string `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	// 消息内容
	Msg *ImMsg `protobuf:"bytes,4,opt,name=msg,proto3" json:"msg,omitempty"`
	// 业务自定义扩展内容
	Ext []byte `protobuf:"bytes,5,opt,name=ext,proto3" json:"ext,omitempty"`
	// 发送时间
	SentAt               int64    `protobuf:"varint,6,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	IsReadHeartEntrance  bool     `protobuf:"varint,7,opt,name=is_read_heart_entrance,json=isReadHeartEntrance,proto3" json:"is_read_heart_entrance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImMsgPush) Reset()         { *m = ImMsgPush{} }
func (m *ImMsgPush) String() string { return proto.CompactTextString(m) }
func (*ImMsgPush) ProtoMessage()    {}
func (*ImMsgPush) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{0}
}
func (m *ImMsgPush) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImMsgPush.Unmarshal(m, b)
}
func (m *ImMsgPush) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImMsgPush.Marshal(b, m, deterministic)
}
func (dst *ImMsgPush) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImMsgPush.Merge(dst, src)
}
func (m *ImMsgPush) XXX_Size() int {
	return xxx_messageInfo_ImMsgPush.Size(m)
}
func (m *ImMsgPush) XXX_DiscardUnknown() {
	xxx_messageInfo_ImMsgPush.DiscardUnknown(m)
}

var xxx_messageInfo_ImMsgPush proto.InternalMessageInfo

func (m *ImMsgPush) GetCmd() WebImCmd {
	if m != nil {
		return m.Cmd
	}
	return WebImCmd_WEB_IM_CMD_UNSPECIFIED
}

func (m *ImMsgPush) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ImMsgPush) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *ImMsgPush) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *ImMsgPush) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *ImMsgPush) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *ImMsgPush) GetIsReadHeartEntrance() bool {
	if m != nil {
		return m.IsReadHeartEntrance
	}
	return false
}

type TransparentMsgExtInfo struct {
	ReqSeqId             uint32   `protobuf:"varint,1,opt,name=req_seq_id,json=reqSeqId,proto3" json:"req_seq_id,omitempty"`
	ReqMsgId             string   `protobuf:"bytes,2,opt,name=req_msg_id,json=reqMsgId,proto3" json:"req_msg_id,omitempty"`
	SpecialMsgType       uint32   `protobuf:"varint,3,opt,name=special_msg_type,json=specialMsgType,proto3" json:"special_msg_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransparentMsgExtInfo) Reset()         { *m = TransparentMsgExtInfo{} }
func (m *TransparentMsgExtInfo) String() string { return proto.CompactTextString(m) }
func (*TransparentMsgExtInfo) ProtoMessage()    {}
func (*TransparentMsgExtInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{1}
}
func (m *TransparentMsgExtInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransparentMsgExtInfo.Unmarshal(m, b)
}
func (m *TransparentMsgExtInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransparentMsgExtInfo.Marshal(b, m, deterministic)
}
func (dst *TransparentMsgExtInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransparentMsgExtInfo.Merge(dst, src)
}
func (m *TransparentMsgExtInfo) XXX_Size() int {
	return xxx_messageInfo_TransparentMsgExtInfo.Size(m)
}
func (m *TransparentMsgExtInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TransparentMsgExtInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TransparentMsgExtInfo proto.InternalMessageInfo

func (m *TransparentMsgExtInfo) GetReqSeqId() uint32 {
	if m != nil {
		return m.ReqSeqId
	}
	return 0
}

func (m *TransparentMsgExtInfo) GetReqMsgId() string {
	if m != nil {
		return m.ReqMsgId
	}
	return ""
}

func (m *TransparentMsgExtInfo) GetSpecialMsgType() uint32 {
	if m != nil {
		return m.SpecialMsgType
	}
	return 0
}

func (m *TransparentMsgExtInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ImGroupMsgPush struct {
	// 业务唯一标识
	Cmd WebImCmd `protobuf:"varint,1,opt,name=cmd,proto3,enum=ga.web_im_logic.WebImCmd" json:"cmd,omitempty"`
	// 接收消息的用户id
	Uid             uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GroupId         uint32 `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupTemplateId uint32 `protobuf:"varint,4,opt,name=group_template_id,json=groupTemplateId,proto3" json:"group_template_id,omitempty"`
	RoleId          uint32 `protobuf:"varint,5,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 消息内容
	Msg     *ImMsg `protobuf:"bytes,6,opt,name=msg,proto3" json:"msg,omitempty"`
	JumpUrl string `protobuf:"bytes,7,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	// 发送时间
	SentAt               int64         `protobuf:"varint,8,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	Avatar               string        `protobuf:"bytes,9,opt,name=avatar,proto3" json:"avatar,omitempty"`
	GroupName            string        `protobuf:"bytes,10,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	ImTabTag             string        `protobuf:"bytes,11,opt,name=im_tab_tag,json=imTabTag,proto3" json:"im_tab_tag,omitempty"`
	IsTrigger            bool          `protobuf:"varint,12,opt,name=is_trigger,json=isTrigger,proto3" json:"is_trigger,omitempty"`
	SenderNickName       string        `protobuf:"bytes,13,opt,name=sender_nick_name,json=senderNickName,proto3" json:"sender_nick_name,omitempty"`
	AtUids               []uint32      `protobuf:"varint,14,rep,packed,name=at_uids,json=atUids,proto3" json:"at_uids,omitempty"`
	AtRoleIds            []uint32      `protobuf:"varint,15,rep,packed,name=at_role_ids,json=atRoleIds,proto3" json:"at_role_ids,omitempty"`
	SenderUid            uint32        `protobuf:"varint,16,opt,name=sender_uid,json=senderUid,proto3" json:"sender_uid,omitempty"`
	GroupSendType        GroupSendType `protobuf:"varint,17,opt,name=group_send_type,json=groupSendType,proto3,enum=ga.web_im_logic.GroupSendType" json:"group_send_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ImGroupMsgPush) Reset()         { *m = ImGroupMsgPush{} }
func (m *ImGroupMsgPush) String() string { return proto.CompactTextString(m) }
func (*ImGroupMsgPush) ProtoMessage()    {}
func (*ImGroupMsgPush) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{2}
}
func (m *ImGroupMsgPush) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImGroupMsgPush.Unmarshal(m, b)
}
func (m *ImGroupMsgPush) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImGroupMsgPush.Marshal(b, m, deterministic)
}
func (dst *ImGroupMsgPush) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImGroupMsgPush.Merge(dst, src)
}
func (m *ImGroupMsgPush) XXX_Size() int {
	return xxx_messageInfo_ImGroupMsgPush.Size(m)
}
func (m *ImGroupMsgPush) XXX_DiscardUnknown() {
	xxx_messageInfo_ImGroupMsgPush.DiscardUnknown(m)
}

var xxx_messageInfo_ImGroupMsgPush proto.InternalMessageInfo

func (m *ImGroupMsgPush) GetCmd() WebImCmd {
	if m != nil {
		return m.Cmd
	}
	return WebImCmd_WEB_IM_CMD_UNSPECIFIED
}

func (m *ImGroupMsgPush) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ImGroupMsgPush) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *ImGroupMsgPush) GetGroupTemplateId() uint32 {
	if m != nil {
		return m.GroupTemplateId
	}
	return 0
}

func (m *ImGroupMsgPush) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *ImGroupMsgPush) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *ImGroupMsgPush) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *ImGroupMsgPush) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *ImGroupMsgPush) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *ImGroupMsgPush) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *ImGroupMsgPush) GetImTabTag() string {
	if m != nil {
		return m.ImTabTag
	}
	return ""
}

func (m *ImGroupMsgPush) GetIsTrigger() bool {
	if m != nil {
		return m.IsTrigger
	}
	return false
}

func (m *ImGroupMsgPush) GetSenderNickName() string {
	if m != nil {
		return m.SenderNickName
	}
	return ""
}

func (m *ImGroupMsgPush) GetAtUids() []uint32 {
	if m != nil {
		return m.AtUids
	}
	return nil
}

func (m *ImGroupMsgPush) GetAtRoleIds() []uint32 {
	if m != nil {
		return m.AtRoleIds
	}
	return nil
}

func (m *ImGroupMsgPush) GetSenderUid() uint32 {
	if m != nil {
		return m.SenderUid
	}
	return 0
}

func (m *ImGroupMsgPush) GetGroupSendType() GroupSendType {
	if m != nil {
		return m.GroupSendType
	}
	return GroupSendType_GROUP_SEND_TYPE_UNSPECIFIED
}

type ImMsg struct {
	// 消息类型
	Type uint32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	// 消息内容
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	// 消息扩展
	Ext []byte `protobuf:"bytes,3,opt,name=ext,proto3" json:"ext,omitempty"`
	// 消息发送的服务端时间(毫秒)
	SentAt int64 `protobuf:"varint,4,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	// 消息唯一标识
	MsgId string `protobuf:"bytes,5,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	// 来自文案
	ComeFrom             string   `protobuf:"bytes,6,opt,name=come_from,json=comeFrom,proto3" json:"come_from,omitempty"`
	SeqId                uint32   `protobuf:"varint,7,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	ImBusiType           uint32   `protobuf:"varint,8,opt,name=im_busi_type,json=imBusiType,proto3" json:"im_busi_type,omitempty"`
	ContentType          uint32   `protobuf:"varint,9,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	ImCmdType            uint32   `protobuf:"varint,10,opt,name=im_cmd_type,json=imCmdType,proto3" json:"im_cmd_type,omitempty"`
	TransparentExtMsg    string   `protobuf:"bytes,11,opt,name=transparent_ext_msg,json=transparentExtMsg,proto3" json:"transparent_ext_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImMsg) Reset()         { *m = ImMsg{} }
func (m *ImMsg) String() string { return proto.CompactTextString(m) }
func (*ImMsg) ProtoMessage()    {}
func (*ImMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{3}
}
func (m *ImMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImMsg.Unmarshal(m, b)
}
func (m *ImMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImMsg.Marshal(b, m, deterministic)
}
func (dst *ImMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImMsg.Merge(dst, src)
}
func (m *ImMsg) XXX_Size() int {
	return xxx_messageInfo_ImMsg.Size(m)
}
func (m *ImMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ImMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ImMsg proto.InternalMessageInfo

func (m *ImMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ImMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ImMsg) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *ImMsg) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *ImMsg) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *ImMsg) GetComeFrom() string {
	if m != nil {
		return m.ComeFrom
	}
	return ""
}

func (m *ImMsg) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *ImMsg) GetImBusiType() uint32 {
	if m != nil {
		return m.ImBusiType
	}
	return 0
}

func (m *ImMsg) GetContentType() uint32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *ImMsg) GetImCmdType() uint32 {
	if m != nil {
		return m.ImCmdType
	}
	return 0
}

func (m *ImMsg) GetTransparentExtMsg() string {
	if m != nil {
		return m.TransparentExtMsg
	}
	return ""
}

// 沉默的扩展消息
type ImMsg_ExtSilence struct {
	// 摸摸头，不是哦(需要调接口，AI伴侣由沉默变成活跃)
	ActiveText string `protobuf:"bytes,1,opt,name=active_text,json=activeText,proto3" json:"active_text,omitempty"`
	// 随便你(不需要调接口，AI伴侣已是沉默状态)
	SilentText           string   `protobuf:"bytes,2,opt,name=silent_text,json=silentText,proto3" json:"silent_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImMsg_ExtSilence) Reset()         { *m = ImMsg_ExtSilence{} }
func (m *ImMsg_ExtSilence) String() string { return proto.CompactTextString(m) }
func (*ImMsg_ExtSilence) ProtoMessage()    {}
func (*ImMsg_ExtSilence) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{3, 0}
}
func (m *ImMsg_ExtSilence) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImMsg_ExtSilence.Unmarshal(m, b)
}
func (m *ImMsg_ExtSilence) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImMsg_ExtSilence.Marshal(b, m, deterministic)
}
func (dst *ImMsg_ExtSilence) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImMsg_ExtSilence.Merge(dst, src)
}
func (m *ImMsg_ExtSilence) XXX_Size() int {
	return xxx_messageInfo_ImMsg_ExtSilence.Size(m)
}
func (m *ImMsg_ExtSilence) XXX_DiscardUnknown() {
	xxx_messageInfo_ImMsg_ExtSilence.DiscardUnknown(m)
}

var xxx_messageInfo_ImMsg_ExtSilence proto.InternalMessageInfo

func (m *ImMsg_ExtSilence) GetActiveText() string {
	if m != nil {
		return m.ActiveText
	}
	return ""
}

func (m *ImMsg_ExtSilence) GetSilentText() string {
	if m != nil {
		return m.SilentText
	}
	return ""
}

type WebImMsg struct {
	Target               string   `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	Msg                  *ImMsg   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WebImMsg) Reset()         { *m = WebImMsg{} }
func (m *WebImMsg) String() string { return proto.CompactTextString(m) }
func (*WebImMsg) ProtoMessage()    {}
func (*WebImMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{4}
}
func (m *WebImMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WebImMsg.Unmarshal(m, b)
}
func (m *WebImMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WebImMsg.Marshal(b, m, deterministic)
}
func (dst *WebImMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WebImMsg.Merge(dst, src)
}
func (m *WebImMsg) XXX_Size() int {
	return xxx_messageInfo_WebImMsg.Size(m)
}
func (m *WebImMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_WebImMsg.DiscardUnknown(m)
}

var xxx_messageInfo_WebImMsg proto.InternalMessageInfo

func (m *WebImMsg) GetTarget() string {
	if m != nil {
		return m.Target
	}
	return ""
}

func (m *WebImMsg) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type SendWebImMsgRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 业务唯一标识
	Cmd WebImCmd `protobuf:"varint,2,opt,name=cmd,proto3,enum=ga.web_im_logic.WebImCmd" json:"cmd,omitempty"`
	// 接收方唯一标识
	Target string `protobuf:"bytes,4,opt,name=target,proto3" json:"target,omitempty"`
	// 消息内容
	Msg                  *ImMsg   `protobuf:"bytes,5,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendWebImMsgRequest) Reset()         { *m = SendWebImMsgRequest{} }
func (m *SendWebImMsgRequest) String() string { return proto.CompactTextString(m) }
func (*SendWebImMsgRequest) ProtoMessage()    {}
func (*SendWebImMsgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{5}
}
func (m *SendWebImMsgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWebImMsgRequest.Unmarshal(m, b)
}
func (m *SendWebImMsgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWebImMsgRequest.Marshal(b, m, deterministic)
}
func (dst *SendWebImMsgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWebImMsgRequest.Merge(dst, src)
}
func (m *SendWebImMsgRequest) XXX_Size() int {
	return xxx_messageInfo_SendWebImMsgRequest.Size(m)
}
func (m *SendWebImMsgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWebImMsgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendWebImMsgRequest proto.InternalMessageInfo

func (m *SendWebImMsgRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendWebImMsgRequest) GetCmd() WebImCmd {
	if m != nil {
		return m.Cmd
	}
	return WebImCmd_WEB_IM_CMD_UNSPECIFIED
}

func (m *SendWebImMsgRequest) GetTarget() string {
	if m != nil {
		return m.Target
	}
	return ""
}

func (m *SendWebImMsgRequest) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type SendWebImMsgResponse struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 发送时间(毫秒)
	SentAt int64 `protobuf:"varint,2,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	// 消息唯一标识
	MsgId                string   `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendWebImMsgResponse) Reset()         { *m = SendWebImMsgResponse{} }
func (m *SendWebImMsgResponse) String() string { return proto.CompactTextString(m) }
func (*SendWebImMsgResponse) ProtoMessage()    {}
func (*SendWebImMsgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{6}
}
func (m *SendWebImMsgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWebImMsgResponse.Unmarshal(m, b)
}
func (m *SendWebImMsgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWebImMsgResponse.Marshal(b, m, deterministic)
}
func (dst *SendWebImMsgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWebImMsgResponse.Merge(dst, src)
}
func (m *SendWebImMsgResponse) XXX_Size() int {
	return xxx_messageInfo_SendWebImMsgResponse.Size(m)
}
func (m *SendWebImMsgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWebImMsgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendWebImMsgResponse proto.InternalMessageInfo

func (m *SendWebImMsgResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SendWebImMsgResponse) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *SendWebImMsgResponse) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type GetWebImMsgListRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 业务唯一标识
	Cmd WebImCmd `protobuf:"varint,2,opt,name=cmd,proto3,enum=ga.web_im_logic.WebImCmd" json:"cmd,omitempty"`
	// 对话中对方的唯一标识
	Target string `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
	// 拉取大于id的消息
	MsgId                string   `protobuf:"bytes,4,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWebImMsgListRequest) Reset()         { *m = GetWebImMsgListRequest{} }
func (m *GetWebImMsgListRequest) String() string { return proto.CompactTextString(m) }
func (*GetWebImMsgListRequest) ProtoMessage()    {}
func (*GetWebImMsgListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{7}
}
func (m *GetWebImMsgListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWebImMsgListRequest.Unmarshal(m, b)
}
func (m *GetWebImMsgListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWebImMsgListRequest.Marshal(b, m, deterministic)
}
func (dst *GetWebImMsgListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWebImMsgListRequest.Merge(dst, src)
}
func (m *GetWebImMsgListRequest) XXX_Size() int {
	return xxx_messageInfo_GetWebImMsgListRequest.Size(m)
}
func (m *GetWebImMsgListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWebImMsgListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWebImMsgListRequest proto.InternalMessageInfo

func (m *GetWebImMsgListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWebImMsgListRequest) GetCmd() WebImCmd {
	if m != nil {
		return m.Cmd
	}
	return WebImCmd_WEB_IM_CMD_UNSPECIFIED
}

func (m *GetWebImMsgListRequest) GetTarget() string {
	if m != nil {
		return m.Target
	}
	return ""
}

func (m *GetWebImMsgListRequest) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type GetWebImMsgListResponse struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 返回的消息列表(按msg_id排序)
	Msgs                 []*ImMsg `protobuf:"bytes,2,rep,name=msgs,proto3" json:"msgs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWebImMsgListResponse) Reset()         { *m = GetWebImMsgListResponse{} }
func (m *GetWebImMsgListResponse) String() string { return proto.CompactTextString(m) }
func (*GetWebImMsgListResponse) ProtoMessage()    {}
func (*GetWebImMsgListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{8}
}
func (m *GetWebImMsgListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWebImMsgListResponse.Unmarshal(m, b)
}
func (m *GetWebImMsgListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWebImMsgListResponse.Marshal(b, m, deterministic)
}
func (dst *GetWebImMsgListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWebImMsgListResponse.Merge(dst, src)
}
func (m *GetWebImMsgListResponse) XXX_Size() int {
	return xxx_messageInfo_GetWebImMsgListResponse.Size(m)
}
func (m *GetWebImMsgListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWebImMsgListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWebImMsgListResponse proto.InternalMessageInfo

func (m *GetWebImMsgListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWebImMsgListResponse) GetMsgs() []*ImMsg {
	if m != nil {
		return m.Msgs
	}
	return nil
}

type ReadWebImMsgRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 业务唯一标识
	Cmd WebImCmd `protobuf:"varint,2,opt,name=cmd,proto3,enum=ga.web_im_logic.WebImCmd" json:"cmd,omitempty"`
	// 对话中对方的唯一标识
	Target string `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
	// 标记小于等于id的消息已读
	MsgId                string   `protobuf:"bytes,4,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadWebImMsgRequest) Reset()         { *m = ReadWebImMsgRequest{} }
func (m *ReadWebImMsgRequest) String() string { return proto.CompactTextString(m) }
func (*ReadWebImMsgRequest) ProtoMessage()    {}
func (*ReadWebImMsgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{9}
}
func (m *ReadWebImMsgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadWebImMsgRequest.Unmarshal(m, b)
}
func (m *ReadWebImMsgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadWebImMsgRequest.Marshal(b, m, deterministic)
}
func (dst *ReadWebImMsgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadWebImMsgRequest.Merge(dst, src)
}
func (m *ReadWebImMsgRequest) XXX_Size() int {
	return xxx_messageInfo_ReadWebImMsgRequest.Size(m)
}
func (m *ReadWebImMsgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadWebImMsgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReadWebImMsgRequest proto.InternalMessageInfo

func (m *ReadWebImMsgRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReadWebImMsgRequest) GetCmd() WebImCmd {
	if m != nil {
		return m.Cmd
	}
	return WebImCmd_WEB_IM_CMD_UNSPECIFIED
}

func (m *ReadWebImMsgRequest) GetTarget() string {
	if m != nil {
		return m.Target
	}
	return ""
}

func (m *ReadWebImMsgRequest) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type ReadWebImMsgResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReadWebImMsgResponse) Reset()         { *m = ReadWebImMsgResponse{} }
func (m *ReadWebImMsgResponse) String() string { return proto.CompactTextString(m) }
func (*ReadWebImMsgResponse) ProtoMessage()    {}
func (*ReadWebImMsgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{10}
}
func (m *ReadWebImMsgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadWebImMsgResponse.Unmarshal(m, b)
}
func (m *ReadWebImMsgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadWebImMsgResponse.Marshal(b, m, deterministic)
}
func (dst *ReadWebImMsgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadWebImMsgResponse.Merge(dst, src)
}
func (m *ReadWebImMsgResponse) XXX_Size() int {
	return xxx_messageInfo_ReadWebImMsgResponse.Size(m)
}
func (m *ReadWebImMsgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadWebImMsgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReadWebImMsgResponse proto.InternalMessageInfo

func (m *ReadWebImMsgResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetUserWebImMsgListRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 业务唯一标识
	Cmd WebImCmd `protobuf:"varint,2,opt,name=cmd,proto3,enum=ga.web_im_logic.WebImCmd" json:"cmd,omitempty"`
	// 拉取消息的用户id
	Uid uint32 `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	// 拉取大于id的消息
	MsgId string `protobuf:"bytes,4,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	// 一次拉取的数量
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserWebImMsgListRequest) Reset()         { *m = GetUserWebImMsgListRequest{} }
func (m *GetUserWebImMsgListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserWebImMsgListRequest) ProtoMessage()    {}
func (*GetUserWebImMsgListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{11}
}
func (m *GetUserWebImMsgListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWebImMsgListRequest.Unmarshal(m, b)
}
func (m *GetUserWebImMsgListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWebImMsgListRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserWebImMsgListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWebImMsgListRequest.Merge(dst, src)
}
func (m *GetUserWebImMsgListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserWebImMsgListRequest.Size(m)
}
func (m *GetUserWebImMsgListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWebImMsgListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWebImMsgListRequest proto.InternalMessageInfo

func (m *GetUserWebImMsgListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserWebImMsgListRequest) GetCmd() WebImCmd {
	if m != nil {
		return m.Cmd
	}
	return WebImCmd_WEB_IM_CMD_UNSPECIFIED
}

func (m *GetUserWebImMsgListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserWebImMsgListRequest) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *GetUserWebImMsgListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserWebImMsgListResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MsgList              []*WebImMsg   `protobuf:"bytes,2,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserWebImMsgListResponse) Reset()         { *m = GetUserWebImMsgListResponse{} }
func (m *GetUserWebImMsgListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserWebImMsgListResponse) ProtoMessage()    {}
func (*GetUserWebImMsgListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{12}
}
func (m *GetUserWebImMsgListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWebImMsgListResponse.Unmarshal(m, b)
}
func (m *GetUserWebImMsgListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWebImMsgListResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserWebImMsgListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWebImMsgListResponse.Merge(dst, src)
}
func (m *GetUserWebImMsgListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserWebImMsgListResponse.Size(m)
}
func (m *GetUserWebImMsgListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWebImMsgListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWebImMsgListResponse proto.InternalMessageInfo

func (m *GetUserWebImMsgListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserWebImMsgListResponse) GetMsgList() []*WebImMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type SendWebImMsgCommonReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Target               string   `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	Msg                  *ImMsg   `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendWebImMsgCommonReq) Reset()         { *m = SendWebImMsgCommonReq{} }
func (m *SendWebImMsgCommonReq) String() string { return proto.CompactTextString(m) }
func (*SendWebImMsgCommonReq) ProtoMessage()    {}
func (*SendWebImMsgCommonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{13}
}
func (m *SendWebImMsgCommonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWebImMsgCommonReq.Unmarshal(m, b)
}
func (m *SendWebImMsgCommonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWebImMsgCommonReq.Marshal(b, m, deterministic)
}
func (dst *SendWebImMsgCommonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWebImMsgCommonReq.Merge(dst, src)
}
func (m *SendWebImMsgCommonReq) XXX_Size() int {
	return xxx_messageInfo_SendWebImMsgCommonReq.Size(m)
}
func (m *SendWebImMsgCommonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWebImMsgCommonReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendWebImMsgCommonReq proto.InternalMessageInfo

func (m *SendWebImMsgCommonReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendWebImMsgCommonReq) GetTarget() string {
	if m != nil {
		return m.Target
	}
	return ""
}

func (m *SendWebImMsgCommonReq) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type SendWebImMsgCommonResp struct {
	MsgId                string   `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	SentAt               int64    `protobuf:"varint,2,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendWebImMsgCommonResp) Reset()         { *m = SendWebImMsgCommonResp{} }
func (m *SendWebImMsgCommonResp) String() string { return proto.CompactTextString(m) }
func (*SendWebImMsgCommonResp) ProtoMessage()    {}
func (*SendWebImMsgCommonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{14}
}
func (m *SendWebImMsgCommonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWebImMsgCommonResp.Unmarshal(m, b)
}
func (m *SendWebImMsgCommonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWebImMsgCommonResp.Marshal(b, m, deterministic)
}
func (dst *SendWebImMsgCommonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWebImMsgCommonResp.Merge(dst, src)
}
func (m *SendWebImMsgCommonResp) XXX_Size() int {
	return xxx_messageInfo_SendWebImMsgCommonResp.Size(m)
}
func (m *SendWebImMsgCommonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWebImMsgCommonResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendWebImMsgCommonResp proto.InternalMessageInfo

func (m *SendWebImMsgCommonResp) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *SendWebImMsgCommonResp) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

type GetWebImMsgCommonReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Target               string   `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	MsgId                string   `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWebImMsgCommonReq) Reset()         { *m = GetWebImMsgCommonReq{} }
func (m *GetWebImMsgCommonReq) String() string { return proto.CompactTextString(m) }
func (*GetWebImMsgCommonReq) ProtoMessage()    {}
func (*GetWebImMsgCommonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{15}
}
func (m *GetWebImMsgCommonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWebImMsgCommonReq.Unmarshal(m, b)
}
func (m *GetWebImMsgCommonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWebImMsgCommonReq.Marshal(b, m, deterministic)
}
func (dst *GetWebImMsgCommonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWebImMsgCommonReq.Merge(dst, src)
}
func (m *GetWebImMsgCommonReq) XXX_Size() int {
	return xxx_messageInfo_GetWebImMsgCommonReq.Size(m)
}
func (m *GetWebImMsgCommonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWebImMsgCommonReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWebImMsgCommonReq proto.InternalMessageInfo

func (m *GetWebImMsgCommonReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWebImMsgCommonReq) GetTarget() string {
	if m != nil {
		return m.Target
	}
	return ""
}

func (m *GetWebImMsgCommonReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type GetWebImMsgCommonResp struct {
	MsgList              []*ImMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWebImMsgCommonResp) Reset()         { *m = GetWebImMsgCommonResp{} }
func (m *GetWebImMsgCommonResp) String() string { return proto.CompactTextString(m) }
func (*GetWebImMsgCommonResp) ProtoMessage()    {}
func (*GetWebImMsgCommonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{16}
}
func (m *GetWebImMsgCommonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWebImMsgCommonResp.Unmarshal(m, b)
}
func (m *GetWebImMsgCommonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWebImMsgCommonResp.Marshal(b, m, deterministic)
}
func (dst *GetWebImMsgCommonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWebImMsgCommonResp.Merge(dst, src)
}
func (m *GetWebImMsgCommonResp) XXX_Size() int {
	return xxx_messageInfo_GetWebImMsgCommonResp.Size(m)
}
func (m *GetWebImMsgCommonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWebImMsgCommonResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWebImMsgCommonResp proto.InternalMessageInfo

func (m *GetWebImMsgCommonResp) GetMsgList() []*ImMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type ReadWebImMsgCommonReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Target               string   `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	MsgId                string   `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadWebImMsgCommonReq) Reset()         { *m = ReadWebImMsgCommonReq{} }
func (m *ReadWebImMsgCommonReq) String() string { return proto.CompactTextString(m) }
func (*ReadWebImMsgCommonReq) ProtoMessage()    {}
func (*ReadWebImMsgCommonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{17}
}
func (m *ReadWebImMsgCommonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadWebImMsgCommonReq.Unmarshal(m, b)
}
func (m *ReadWebImMsgCommonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadWebImMsgCommonReq.Marshal(b, m, deterministic)
}
func (dst *ReadWebImMsgCommonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadWebImMsgCommonReq.Merge(dst, src)
}
func (m *ReadWebImMsgCommonReq) XXX_Size() int {
	return xxx_messageInfo_ReadWebImMsgCommonReq.Size(m)
}
func (m *ReadWebImMsgCommonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadWebImMsgCommonReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReadWebImMsgCommonReq proto.InternalMessageInfo

func (m *ReadWebImMsgCommonReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReadWebImMsgCommonReq) GetTarget() string {
	if m != nil {
		return m.Target
	}
	return ""
}

func (m *ReadWebImMsgCommonReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type ReadWebImMsgCommonResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadWebImMsgCommonResp) Reset()         { *m = ReadWebImMsgCommonResp{} }
func (m *ReadWebImMsgCommonResp) String() string { return proto.CompactTextString(m) }
func (*ReadWebImMsgCommonResp) ProtoMessage()    {}
func (*ReadWebImMsgCommonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{18}
}
func (m *ReadWebImMsgCommonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadWebImMsgCommonResp.Unmarshal(m, b)
}
func (m *ReadWebImMsgCommonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadWebImMsgCommonResp.Marshal(b, m, deterministic)
}
func (dst *ReadWebImMsgCommonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadWebImMsgCommonResp.Merge(dst, src)
}
func (m *ReadWebImMsgCommonResp) XXX_Size() int {
	return xxx_messageInfo_ReadWebImMsgCommonResp.Size(m)
}
func (m *ReadWebImMsgCommonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadWebImMsgCommonResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReadWebImMsgCommonResp proto.InternalMessageInfo

type GetUserWebImMsgCommonReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MsgId                string   `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserWebImMsgCommonReq) Reset()         { *m = GetUserWebImMsgCommonReq{} }
func (m *GetUserWebImMsgCommonReq) String() string { return proto.CompactTextString(m) }
func (*GetUserWebImMsgCommonReq) ProtoMessage()    {}
func (*GetUserWebImMsgCommonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{19}
}
func (m *GetUserWebImMsgCommonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWebImMsgCommonReq.Unmarshal(m, b)
}
func (m *GetUserWebImMsgCommonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWebImMsgCommonReq.Marshal(b, m, deterministic)
}
func (dst *GetUserWebImMsgCommonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWebImMsgCommonReq.Merge(dst, src)
}
func (m *GetUserWebImMsgCommonReq) XXX_Size() int {
	return xxx_messageInfo_GetUserWebImMsgCommonReq.Size(m)
}
func (m *GetUserWebImMsgCommonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWebImMsgCommonReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWebImMsgCommonReq proto.InternalMessageInfo

func (m *GetUserWebImMsgCommonReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserWebImMsgCommonReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *GetUserWebImMsgCommonReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserWebImMsgCommonResp struct {
	MsgList              []*WebImMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetUserWebImMsgCommonResp) Reset()         { *m = GetUserWebImMsgCommonResp{} }
func (m *GetUserWebImMsgCommonResp) String() string { return proto.CompactTextString(m) }
func (*GetUserWebImMsgCommonResp) ProtoMessage()    {}
func (*GetUserWebImMsgCommonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{20}
}
func (m *GetUserWebImMsgCommonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWebImMsgCommonResp.Unmarshal(m, b)
}
func (m *GetUserWebImMsgCommonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWebImMsgCommonResp.Marshal(b, m, deterministic)
}
func (dst *GetUserWebImMsgCommonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWebImMsgCommonResp.Merge(dst, src)
}
func (m *GetUserWebImMsgCommonResp) XXX_Size() int {
	return xxx_messageInfo_GetUserWebImMsgCommonResp.Size(m)
}
func (m *GetUserWebImMsgCommonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWebImMsgCommonResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWebImMsgCommonResp proto.InternalMessageInfo

func (m *GetUserWebImMsgCommonResp) GetMsgList() []*WebImMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

// GAME_COMMON_H5_PUSH = 188; // 开黑通用H5推送
type CommonH5PushMsg struct {
	// 推送类型, see enum H5PushType
	PushType uint32 `protobuf:"varint,1,opt,name=push_type,json=pushType,proto3" json:"push_type,omitempty"`
	// 推送内容
	Data []byte `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	// 数据解析类型, see enum MarshalType
	MarshalType          uint32   `protobuf:"varint,3,opt,name=marshal_type,json=marshalType,proto3" json:"marshal_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonH5PushMsg) Reset()         { *m = CommonH5PushMsg{} }
func (m *CommonH5PushMsg) String() string { return proto.CompactTextString(m) }
func (*CommonH5PushMsg) ProtoMessage()    {}
func (*CommonH5PushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{21}
}
func (m *CommonH5PushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonH5PushMsg.Unmarshal(m, b)
}
func (m *CommonH5PushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonH5PushMsg.Marshal(b, m, deterministic)
}
func (dst *CommonH5PushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonH5PushMsg.Merge(dst, src)
}
func (m *CommonH5PushMsg) XXX_Size() int {
	return xxx_messageInfo_CommonH5PushMsg.Size(m)
}
func (m *CommonH5PushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonH5PushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_CommonH5PushMsg proto.InternalMessageInfo

func (m *CommonH5PushMsg) GetPushType() uint32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *CommonH5PushMsg) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *CommonH5PushMsg) GetMarshalType() uint32 {
	if m != nil {
		return m.MarshalType
	}
	return 0
}

type SendGroupImMsgRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 业务唯一标识
	Cmd             WebImCmd `protobuf:"varint,2,opt,name=cmd,proto3,enum=ga.web_im_logic.WebImCmd" json:"cmd,omitempty"`
	GroupId         uint32   `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupTemplateId uint32   `protobuf:"varint,4,opt,name=group_template_id,json=groupTemplateId,proto3" json:"group_template_id,omitempty"`
	RoleIds         []uint32 `protobuf:"varint,5,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	// 消息内容
	Msg                  *ImMsg   `protobuf:"bytes,6,opt,name=msg,proto3" json:"msg,omitempty"`
	AtUids               []uint32 `protobuf:"varint,7,rep,packed,name=at_uids,json=atUids,proto3" json:"at_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGroupImMsgRequest) Reset()         { *m = SendGroupImMsgRequest{} }
func (m *SendGroupImMsgRequest) String() string { return proto.CompactTextString(m) }
func (*SendGroupImMsgRequest) ProtoMessage()    {}
func (*SendGroupImMsgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{22}
}
func (m *SendGroupImMsgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGroupImMsgRequest.Unmarshal(m, b)
}
func (m *SendGroupImMsgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGroupImMsgRequest.Marshal(b, m, deterministic)
}
func (dst *SendGroupImMsgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGroupImMsgRequest.Merge(dst, src)
}
func (m *SendGroupImMsgRequest) XXX_Size() int {
	return xxx_messageInfo_SendGroupImMsgRequest.Size(m)
}
func (m *SendGroupImMsgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGroupImMsgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGroupImMsgRequest proto.InternalMessageInfo

func (m *SendGroupImMsgRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendGroupImMsgRequest) GetCmd() WebImCmd {
	if m != nil {
		return m.Cmd
	}
	return WebImCmd_WEB_IM_CMD_UNSPECIFIED
}

func (m *SendGroupImMsgRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *SendGroupImMsgRequest) GetGroupTemplateId() uint32 {
	if m != nil {
		return m.GroupTemplateId
	}
	return 0
}

func (m *SendGroupImMsgRequest) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

func (m *SendGroupImMsgRequest) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *SendGroupImMsgRequest) GetAtUids() []uint32 {
	if m != nil {
		return m.AtUids
	}
	return nil
}

type SendGroupImMsgResponse struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 发送时间(毫秒)
	SentAt int64 `protobuf:"varint,2,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	// 消息唯一标识
	SeqId                uint32   `protobuf:"varint,3,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGroupImMsgResponse) Reset()         { *m = SendGroupImMsgResponse{} }
func (m *SendGroupImMsgResponse) String() string { return proto.CompactTextString(m) }
func (*SendGroupImMsgResponse) ProtoMessage()    {}
func (*SendGroupImMsgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{23}
}
func (m *SendGroupImMsgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGroupImMsgResponse.Unmarshal(m, b)
}
func (m *SendGroupImMsgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGroupImMsgResponse.Marshal(b, m, deterministic)
}
func (dst *SendGroupImMsgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGroupImMsgResponse.Merge(dst, src)
}
func (m *SendGroupImMsgResponse) XXX_Size() int {
	return xxx_messageInfo_SendGroupImMsgResponse.Size(m)
}
func (m *SendGroupImMsgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGroupImMsgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendGroupImMsgResponse proto.InternalMessageInfo

func (m *SendGroupImMsgResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SendGroupImMsgResponse) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *SendGroupImMsgResponse) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

// 拉取群组的最新消息,拉取红点，拉取最新一条消息都用这个接口
type BatchGetGroupLastMsgRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 业务唯一标识
	Cmd                  WebImCmd `protobuf:"varint,2,opt,name=cmd,proto3,enum=ga.web_im_logic.WebImCmd" json:"cmd,omitempty"`
	GroupIds             []uint32 `protobuf:"varint,3,rep,packed,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGroupLastMsgRequest) Reset()         { *m = BatchGetGroupLastMsgRequest{} }
func (m *BatchGetGroupLastMsgRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetGroupLastMsgRequest) ProtoMessage()    {}
func (*BatchGetGroupLastMsgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{24}
}
func (m *BatchGetGroupLastMsgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGroupLastMsgRequest.Unmarshal(m, b)
}
func (m *BatchGetGroupLastMsgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGroupLastMsgRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetGroupLastMsgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGroupLastMsgRequest.Merge(dst, src)
}
func (m *BatchGetGroupLastMsgRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetGroupLastMsgRequest.Size(m)
}
func (m *BatchGetGroupLastMsgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGroupLastMsgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGroupLastMsgRequest proto.InternalMessageInfo

func (m *BatchGetGroupLastMsgRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchGetGroupLastMsgRequest) GetCmd() WebImCmd {
	if m != nil {
		return m.Cmd
	}
	return WebImCmd_WEB_IM_CMD_UNSPECIFIED
}

func (m *BatchGetGroupLastMsgRequest) GetGroupIds() []uint32 {
	if m != nil {
		return m.GroupIds
	}
	return nil
}

type GroupBaseInfo struct {
	UnreadRedDotCnt      uint32        `protobuf:"varint,1,opt,name=unread_red_dot_cnt,json=unreadRedDotCnt,proto3" json:"unread_red_dot_cnt,omitempty"`
	Avatar               string        `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	JumpUrl              string        `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	GroupName            string        `protobuf:"bytes,4,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	ImTabTag             string        `protobuf:"bytes,5,opt,name=im_tab_tag,json=imTabTag,proto3" json:"im_tab_tag,omitempty"`
	SenderNickName       string        `protobuf:"bytes,6,opt,name=sender_nick_name,json=senderNickName,proto3" json:"sender_nick_name,omitempty"`
	MemberAvatars        []*AvatarInfo `protobuf:"bytes,7,rep,name=member_avatars,json=memberAvatars,proto3" json:"member_avatars,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GroupBaseInfo) Reset()         { *m = GroupBaseInfo{} }
func (m *GroupBaseInfo) String() string { return proto.CompactTextString(m) }
func (*GroupBaseInfo) ProtoMessage()    {}
func (*GroupBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{25}
}
func (m *GroupBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupBaseInfo.Unmarshal(m, b)
}
func (m *GroupBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupBaseInfo.Marshal(b, m, deterministic)
}
func (dst *GroupBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupBaseInfo.Merge(dst, src)
}
func (m *GroupBaseInfo) XXX_Size() int {
	return xxx_messageInfo_GroupBaseInfo.Size(m)
}
func (m *GroupBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GroupBaseInfo proto.InternalMessageInfo

func (m *GroupBaseInfo) GetUnreadRedDotCnt() uint32 {
	if m != nil {
		return m.UnreadRedDotCnt
	}
	return 0
}

func (m *GroupBaseInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GroupBaseInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *GroupBaseInfo) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *GroupBaseInfo) GetImTabTag() string {
	if m != nil {
		return m.ImTabTag
	}
	return ""
}

func (m *GroupBaseInfo) GetSenderNickName() string {
	if m != nil {
		return m.SenderNickName
	}
	return ""
}

func (m *GroupBaseInfo) GetMemberAvatars() []*AvatarInfo {
	if m != nil {
		return m.MemberAvatars
	}
	return nil
}

type AvatarInfo struct {
	MemberType           uint32   `protobuf:"varint,1,opt,name=member_type,json=memberType,proto3" json:"member_type,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Avatar               string   `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AvatarInfo) Reset()         { *m = AvatarInfo{} }
func (m *AvatarInfo) String() string { return proto.CompactTextString(m) }
func (*AvatarInfo) ProtoMessage()    {}
func (*AvatarInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{26}
}
func (m *AvatarInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AvatarInfo.Unmarshal(m, b)
}
func (m *AvatarInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AvatarInfo.Marshal(b, m, deterministic)
}
func (dst *AvatarInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AvatarInfo.Merge(dst, src)
}
func (m *AvatarInfo) XXX_Size() int {
	return xxx_messageInfo_AvatarInfo.Size(m)
}
func (m *AvatarInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AvatarInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AvatarInfo proto.InternalMessageInfo

func (m *AvatarInfo) GetMemberType() uint32 {
	if m != nil {
		return m.MemberType
	}
	return 0
}

func (m *AvatarInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AvatarInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

type LastGroupMsg struct {
	GroupId              uint32            `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupBaseInfo        *GroupBaseInfo    `protobuf:"bytes,2,opt,name=group_base_info,json=groupBaseInfo,proto3" json:"group_base_info,omitempty"`
	TimeLineMsg          *GroupTimeLineMsg `protobuf:"bytes,3,opt,name=time_line_msg,json=timeLineMsg,proto3" json:"time_line_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *LastGroupMsg) Reset()         { *m = LastGroupMsg{} }
func (m *LastGroupMsg) String() string { return proto.CompactTextString(m) }
func (*LastGroupMsg) ProtoMessage()    {}
func (*LastGroupMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{27}
}
func (m *LastGroupMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LastGroupMsg.Unmarshal(m, b)
}
func (m *LastGroupMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LastGroupMsg.Marshal(b, m, deterministic)
}
func (dst *LastGroupMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LastGroupMsg.Merge(dst, src)
}
func (m *LastGroupMsg) XXX_Size() int {
	return xxx_messageInfo_LastGroupMsg.Size(m)
}
func (m *LastGroupMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_LastGroupMsg.DiscardUnknown(m)
}

var xxx_messageInfo_LastGroupMsg proto.InternalMessageInfo

func (m *LastGroupMsg) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *LastGroupMsg) GetGroupBaseInfo() *GroupBaseInfo {
	if m != nil {
		return m.GroupBaseInfo
	}
	return nil
}

func (m *LastGroupMsg) GetTimeLineMsg() *GroupTimeLineMsg {
	if m != nil {
		return m.TimeLineMsg
	}
	return nil
}

type BatchGetGroupLastMsgResponse struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LastGroupMsgs        []*LastGroupMsg `protobuf:"bytes,2,rep,name=last_group_msgs,json=lastGroupMsgs,proto3" json:"last_group_msgs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetGroupLastMsgResponse) Reset()         { *m = BatchGetGroupLastMsgResponse{} }
func (m *BatchGetGroupLastMsgResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetGroupLastMsgResponse) ProtoMessage()    {}
func (*BatchGetGroupLastMsgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{28}
}
func (m *BatchGetGroupLastMsgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGroupLastMsgResponse.Unmarshal(m, b)
}
func (m *BatchGetGroupLastMsgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGroupLastMsgResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetGroupLastMsgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGroupLastMsgResponse.Merge(dst, src)
}
func (m *BatchGetGroupLastMsgResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetGroupLastMsgResponse.Size(m)
}
func (m *BatchGetGroupLastMsgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGroupLastMsgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGroupLastMsgResponse proto.InternalMessageInfo

func (m *BatchGetGroupLastMsgResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatchGetGroupLastMsgResponse) GetLastGroupMsgs() []*LastGroupMsg {
	if m != nil {
		return m.LastGroupMsgs
	}
	return nil
}

type GetGroupMsgListRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 业务唯一标识
	Cmd     WebImCmd `protobuf:"varint,2,opt,name=cmd,proto3,enum=ga.web_im_logic.WebImCmd" json:"cmd,omitempty"`
	GroupId uint32   `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	SeqId   uint32   `protobuf:"varint,4,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	// 一次拉取的数量
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupMsgListRequest) Reset()         { *m = GetGroupMsgListRequest{} }
func (m *GetGroupMsgListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupMsgListRequest) ProtoMessage()    {}
func (*GetGroupMsgListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{29}
}
func (m *GetGroupMsgListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMsgListRequest.Unmarshal(m, b)
}
func (m *GetGroupMsgListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMsgListRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroupMsgListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMsgListRequest.Merge(dst, src)
}
func (m *GetGroupMsgListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupMsgListRequest.Size(m)
}
func (m *GetGroupMsgListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMsgListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMsgListRequest proto.InternalMessageInfo

func (m *GetGroupMsgListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGroupMsgListRequest) GetCmd() WebImCmd {
	if m != nil {
		return m.Cmd
	}
	return WebImCmd_WEB_IM_CMD_UNSPECIFIED
}

func (m *GetGroupMsgListRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GetGroupMsgListRequest) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *GetGroupMsgListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetGroupMsgListResponse struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TimeLineMsgs         []*GroupTimeLineMsg `protobuf:"bytes,2,rep,name=time_line_msgs,json=timeLineMsgs,proto3" json:"time_line_msgs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetGroupMsgListResponse) Reset()         { *m = GetGroupMsgListResponse{} }
func (m *GetGroupMsgListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupMsgListResponse) ProtoMessage()    {}
func (*GetGroupMsgListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{30}
}
func (m *GetGroupMsgListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMsgListResponse.Unmarshal(m, b)
}
func (m *GetGroupMsgListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMsgListResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroupMsgListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMsgListResponse.Merge(dst, src)
}
func (m *GetGroupMsgListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupMsgListResponse.Size(m)
}
func (m *GetGroupMsgListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMsgListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMsgListResponse proto.InternalMessageInfo

func (m *GetGroupMsgListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGroupMsgListResponse) GetTimeLineMsgs() []*GroupTimeLineMsg {
	if m != nil {
		return m.TimeLineMsgs
	}
	return nil
}

type GroupTimeLineMsg struct {
	// 群实例id
	GroupId         uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupTemplateId uint32   `protobuf:"varint,2,opt,name=group_template_id,json=groupTemplateId,proto3" json:"group_template_id,omitempty"`
	RoleIds         []uint32 `protobuf:"varint,3,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	// 用户id
	Uid                  uint32        `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Msg                  *ImMsg        `protobuf:"bytes,5,opt,name=msg,proto3" json:"msg,omitempty"`
	GroupSendType        GroupSendType `protobuf:"varint,6,opt,name=group_send_type,json=groupSendType,proto3,enum=ga.web_im_logic.GroupSendType" json:"group_send_type,omitempty"`
	AtUids               []uint32      `protobuf:"varint,7,rep,packed,name=at_uids,json=atUids,proto3" json:"at_uids,omitempty"`
	AtRoleIds            []uint32      `protobuf:"varint,8,rep,packed,name=at_role_ids,json=atRoleIds,proto3" json:"at_role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GroupTimeLineMsg) Reset()         { *m = GroupTimeLineMsg{} }
func (m *GroupTimeLineMsg) String() string { return proto.CompactTextString(m) }
func (*GroupTimeLineMsg) ProtoMessage()    {}
func (*GroupTimeLineMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_web_im_logic_8ad06310be9834dc, []int{31}
}
func (m *GroupTimeLineMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupTimeLineMsg.Unmarshal(m, b)
}
func (m *GroupTimeLineMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupTimeLineMsg.Marshal(b, m, deterministic)
}
func (dst *GroupTimeLineMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTimeLineMsg.Merge(dst, src)
}
func (m *GroupTimeLineMsg) XXX_Size() int {
	return xxx_messageInfo_GroupTimeLineMsg.Size(m)
}
func (m *GroupTimeLineMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTimeLineMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTimeLineMsg proto.InternalMessageInfo

func (m *GroupTimeLineMsg) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupTimeLineMsg) GetGroupTemplateId() uint32 {
	if m != nil {
		return m.GroupTemplateId
	}
	return 0
}

func (m *GroupTimeLineMsg) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

func (m *GroupTimeLineMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GroupTimeLineMsg) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *GroupTimeLineMsg) GetGroupSendType() GroupSendType {
	if m != nil {
		return m.GroupSendType
	}
	return GroupSendType_GROUP_SEND_TYPE_UNSPECIFIED
}

func (m *GroupTimeLineMsg) GetAtUids() []uint32 {
	if m != nil {
		return m.AtUids
	}
	return nil
}

func (m *GroupTimeLineMsg) GetAtRoleIds() []uint32 {
	if m != nil {
		return m.AtRoleIds
	}
	return nil
}

func init() {
	proto.RegisterType((*ImMsgPush)(nil), "ga.web_im_logic.ImMsgPush")
	proto.RegisterType((*TransparentMsgExtInfo)(nil), "ga.web_im_logic.TransparentMsgExtInfo")
	proto.RegisterType((*ImGroupMsgPush)(nil), "ga.web_im_logic.ImGroupMsgPush")
	proto.RegisterType((*ImMsg)(nil), "ga.web_im_logic.ImMsg")
	proto.RegisterType((*ImMsg_ExtSilence)(nil), "ga.web_im_logic.ImMsg.ExtSilence")
	proto.RegisterType((*WebImMsg)(nil), "ga.web_im_logic.WebImMsg")
	proto.RegisterType((*SendWebImMsgRequest)(nil), "ga.web_im_logic.SendWebImMsgRequest")
	proto.RegisterType((*SendWebImMsgResponse)(nil), "ga.web_im_logic.SendWebImMsgResponse")
	proto.RegisterType((*GetWebImMsgListRequest)(nil), "ga.web_im_logic.GetWebImMsgListRequest")
	proto.RegisterType((*GetWebImMsgListResponse)(nil), "ga.web_im_logic.GetWebImMsgListResponse")
	proto.RegisterType((*ReadWebImMsgRequest)(nil), "ga.web_im_logic.ReadWebImMsgRequest")
	proto.RegisterType((*ReadWebImMsgResponse)(nil), "ga.web_im_logic.ReadWebImMsgResponse")
	proto.RegisterType((*GetUserWebImMsgListRequest)(nil), "ga.web_im_logic.GetUserWebImMsgListRequest")
	proto.RegisterType((*GetUserWebImMsgListResponse)(nil), "ga.web_im_logic.GetUserWebImMsgListResponse")
	proto.RegisterType((*SendWebImMsgCommonReq)(nil), "ga.web_im_logic.SendWebImMsgCommonReq")
	proto.RegisterType((*SendWebImMsgCommonResp)(nil), "ga.web_im_logic.SendWebImMsgCommonResp")
	proto.RegisterType((*GetWebImMsgCommonReq)(nil), "ga.web_im_logic.GetWebImMsgCommonReq")
	proto.RegisterType((*GetWebImMsgCommonResp)(nil), "ga.web_im_logic.GetWebImMsgCommonResp")
	proto.RegisterType((*ReadWebImMsgCommonReq)(nil), "ga.web_im_logic.ReadWebImMsgCommonReq")
	proto.RegisterType((*ReadWebImMsgCommonResp)(nil), "ga.web_im_logic.ReadWebImMsgCommonResp")
	proto.RegisterType((*GetUserWebImMsgCommonReq)(nil), "ga.web_im_logic.GetUserWebImMsgCommonReq")
	proto.RegisterType((*GetUserWebImMsgCommonResp)(nil), "ga.web_im_logic.GetUserWebImMsgCommonResp")
	proto.RegisterType((*CommonH5PushMsg)(nil), "ga.web_im_logic.CommonH5PushMsg")
	proto.RegisterType((*SendGroupImMsgRequest)(nil), "ga.web_im_logic.SendGroupImMsgRequest")
	proto.RegisterType((*SendGroupImMsgResponse)(nil), "ga.web_im_logic.SendGroupImMsgResponse")
	proto.RegisterType((*BatchGetGroupLastMsgRequest)(nil), "ga.web_im_logic.BatchGetGroupLastMsgRequest")
	proto.RegisterType((*GroupBaseInfo)(nil), "ga.web_im_logic.GroupBaseInfo")
	proto.RegisterType((*AvatarInfo)(nil), "ga.web_im_logic.AvatarInfo")
	proto.RegisterType((*LastGroupMsg)(nil), "ga.web_im_logic.LastGroupMsg")
	proto.RegisterType((*BatchGetGroupLastMsgResponse)(nil), "ga.web_im_logic.BatchGetGroupLastMsgResponse")
	proto.RegisterType((*GetGroupMsgListRequest)(nil), "ga.web_im_logic.GetGroupMsgListRequest")
	proto.RegisterType((*GetGroupMsgListResponse)(nil), "ga.web_im_logic.GetGroupMsgListResponse")
	proto.RegisterType((*GroupTimeLineMsg)(nil), "ga.web_im_logic.GroupTimeLineMsg")
	proto.RegisterEnum("ga.web_im_logic.WebImCmd", WebImCmd_name, WebImCmd_value)
	proto.RegisterEnum("ga.web_im_logic.SpecialMsgType", SpecialMsgType_name, SpecialMsgType_value)
	proto.RegisterEnum("ga.web_im_logic.ImMsgContentType", ImMsgContentType_name, ImMsgContentType_value)
	proto.RegisterEnum("ga.web_im_logic.ImBusiType", ImBusiType_name, ImBusiType_value)
	proto.RegisterEnum("ga.web_im_logic.ImCmdType", ImCmdType_name, ImCmdType_value)
	proto.RegisterEnum("ga.web_im_logic.H5PushType", H5PushType_name, H5PushType_value)
	proto.RegisterEnum("ga.web_im_logic.GroupSendType", GroupSendType_name, GroupSendType_value)
	proto.RegisterEnum("ga.web_im_logic.ImMsg_Type", ImMsg_Type_name, ImMsg_Type_value)
	proto.RegisterEnum("ga.web_im_logic.CommonH5PushMsg_MarshalType", CommonH5PushMsg_MarshalType_name, CommonH5PushMsg_MarshalType_value)
}

func init() {
	proto.RegisterFile("web_im_logic/web_im_logic.proto", fileDescriptor_web_im_logic_8ad06310be9834dc)
}

var fileDescriptor_web_im_logic_8ad06310be9834dc = []byte{
	// 2208 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x59, 0xcd, 0x72, 0xdb, 0xc8,
	0xf1, 0x5f, 0x10, 0xfc, 0x6c, 0x8a, 0x12, 0x0c, 0x7d, 0x18, 0xb2, 0x6c, 0x4b, 0xc6, 0xff, 0x9f,
	0x44, 0xab, 0xad, 0x95, 0x2b, 0xda, 0xec, 0x03, 0x50, 0x14, 0x44, 0xc1, 0xe1, 0x87, 0x02, 0x80,
	0xf6, 0xda, 0x97, 0x29, 0x90, 0x1c, 0xd3, 0x58, 0x13, 0x00, 0x85, 0x19, 0xee, 0xda, 0x97, 0x9c,
	0x72, 0x4d, 0x25, 0x39, 0x26, 0x95, 0x17, 0x48, 0x55, 0x0e, 0xc9, 0x21, 0x55, 0xc9, 0x33, 0xe4,
	0x51, 0x92, 0x7b, 0x6e, 0xa9, 0xd4, 0xcc, 0x80, 0x24, 0x40, 0x42, 0xb2, 0xbc, 0x65, 0xfb, 0xc6,
	0xfe, 0x40, 0x4f, 0xcf, 0xaf, 0xbb, 0x67, 0xba, 0x87, 0xb0, 0xff, 0x3d, 0xee, 0x23, 0xcf, 0x47,
	0xe3, 0x70, 0xe4, 0x0d, 0x1e, 0x27, 0x89, 0xe3, 0x49, 0x14, 0xd2, 0x50, 0xdd, 0x18, 0xb9, 0xc7,
	0x49, 0xf6, 0xbd, 0xda, 0xc8, 0x45, 0x7d, 0x97, 0x60, 0x21, 0xd7, 0xff, 0x2d, 0x41, 0xc5, 0xf4,
	0xdb, 0x64, 0x74, 0x39, 0x25, 0xaf, 0xd4, 0x2f, 0x40, 0x1e, 0xf8, 0x43, 0x4d, 0x3a, 0x90, 0x0e,
	0xd7, 0x4f, 0x76, 0x8f, 0x97, 0xbe, 0x3d, 0x7e, 0x86, 0xfb, 0xa6, 0xdf, 0xf0, 0x87, 0x16, 0xd3,
	0x52, 0x15, 0x90, 0xa7, 0xde, 0x50, 0xcb, 0x1d, 0x48, 0x87, 0x35, 0x8b, 0xfd, 0x54, 0x77, 0xa0,
	0x48, 0xc2, 0x69, 0x34, 0xc0, 0x9a, 0x7c, 0x20, 0x1d, 0x56, 0xac, 0x98, 0x52, 0x0f, 0x41, 0xf6,
	0xc9, 0x48, 0xcb, 0x1f, 0x48, 0x87, 0xd5, 0x93, 0x9d, 0x15, 0xb3, 0x7c, 0x7d, 0x8b, 0xa9, 0x30,
	0x9b, 0xf8, 0x0d, 0xd5, 0x0a, 0x07, 0xd2, 0xe1, 0x9a, 0xc5, 0x7e, 0xaa, 0x77, 0xa1, 0x44, 0x70,
	0x40, 0x91, 0x4b, 0xb5, 0xe2, 0x81, 0x74, 0x28, 0x5b, 0x45, 0x46, 0xd6, 0xa9, 0xfa, 0x15, 0xec,
	0x78, 0x04, 0x45, 0xd8, 0x1d, 0xa2, 0x57, 0xd8, 0x8d, 0x28, 0xc2, 0x01, 0x8d, 0xdc, 0x60, 0x80,
	0xb5, 0xd2, 0x81, 0x74, 0x58, 0xb6, 0x36, 0x3d, 0x62, 0x61, 0x77, 0x78, 0xc1, 0x64, 0x46, 0x2c,
	0xd2, 0x7f, 0x23, 0xc1, 0xb6, 0x13, 0xb9, 0x01, 0x99, 0xb8, 0x11, 0x0e, 0x68, 0x9b, 0x8c, 0x8c,
	0x37, 0xd4, 0x0c, 0x5e, 0x86, 0xea, 0x7d, 0x80, 0x08, 0x5f, 0x21, 0x82, 0xaf, 0x90, 0x27, 0x10,
	0xa8, 0x59, 0xe5, 0x08, 0x5f, 0xd9, 0xf8, 0xca, 0x1c, 0xce, 0xa4, 0x3e, 0x19, 0xa1, 0x78, 0xcb,
	0x15, 0x2e, 0x6d, 0x93, 0x91, 0x39, 0x54, 0x0f, 0x41, 0x21, 0x13, 0x3c, 0xf0, 0xdc, 0x31, 0xd7,
	0xa0, 0x6f, 0x27, 0x02, 0x81, 0x9a, 0xb5, 0x1e, 0xf3, 0xdb, 0x64, 0xe4, 0xbc, 0x9d, 0xe0, 0x19,
	0x66, 0xf9, 0x39, 0x66, 0xfa, 0x3f, 0xf3, 0xb0, 0x6e, 0xfa, 0xcd, 0x28, 0x9c, 0x4e, 0x3e, 0x50,
	0x14, 0x76, 0xa1, 0x3c, 0x62, 0xe6, 0x98, 0xa7, 0xc2, 0x8b, 0x12, 0xa7, 0xcd, 0xa1, 0x7a, 0x04,
	0x77, 0x84, 0x88, 0x62, 0x7f, 0x32, 0x76, 0x29, 0x46, 0x73, 0x67, 0x36, 0xb8, 0xc0, 0x89, 0xf9,
	0xe6, 0x90, 0x01, 0x1f, 0x85, 0x63, 0xae, 0x51, 0xe0, 0x1a, 0x45, 0x46, 0xf2, 0xdd, 0xf2, 0x68,
	0x16, 0xdf, 0x1d, 0xcd, 0x5d, 0x28, 0x7f, 0x3b, 0xf5, 0x27, 0x68, 0x1a, 0x8d, 0x79, 0x50, 0x2a,
	0x56, 0x89, 0xd1, 0xbd, 0x68, 0x9c, 0x0c, 0x6b, 0x39, 0x15, 0xd6, 0x1d, 0x28, 0xba, 0xdf, 0xb9,
	0xd4, 0x8d, 0xb4, 0x8a, 0xc8, 0x21, 0x41, 0xa9, 0x0f, 0x00, 0x84, 0xeb, 0x81, 0xeb, 0x63, 0x0d,
	0xb8, 0xac, 0xc2, 0x39, 0x1d, 0xd7, 0xc7, 0x2c, 0x40, 0x9e, 0x8f, 0xa8, 0xdb, 0x47, 0xd4, 0x1d,
	0x69, 0x55, 0x11, 0x20, 0xcf, 0x77, 0xdc, 0xbe, 0xe3, 0x8e, 0xd8, 0xc7, 0x1e, 0x41, 0x34, 0xf2,
	0x46, 0x23, 0x1c, 0x69, 0x6b, 0x3c, 0x3f, 0x2a, 0x1e, 0x71, 0x04, 0x83, 0xc7, 0x0f, 0x07, 0x43,
	0x1c, 0xa1, 0xc0, 0x1b, 0xbc, 0x16, 0x2b, 0xd4, 0xb8, 0x89, 0x75, 0xc1, 0xef, 0x78, 0x83, 0xd7,
	0x7c, 0x99, 0xbb, 0x50, 0x72, 0x29, 0x9a, 0x7a, 0x43, 0xa2, 0xad, 0x1f, 0xc8, 0x0c, 0x14, 0x97,
	0xf6, 0xbc, 0x21, 0x51, 0x1f, 0x42, 0xd5, 0xa5, 0x28, 0x06, 0x8c, 0x68, 0x1b, 0x5c, 0x58, 0x71,
	0xa9, 0xc5, 0x31, 0x23, 0xcc, 0x83, 0x78, 0x09, 0x16, 0x2d, 0x85, 0x03, 0x5a, 0x11, 0x9c, 0x9e,
	0x37, 0x54, 0xcf, 0x41, 0xe0, 0x8f, 0x18, 0x4b, 0x24, 0xd0, 0x1d, 0x1e, 0xfe, 0x87, 0x2b, 0xf8,
	0xf2, 0x54, 0xb1, 0x71, 0x30, 0x64, 0x09, 0x65, 0xd5, 0x46, 0x49, 0x52, 0xff, 0x8f, 0x0c, 0x05,
	0x1e, 0x00, 0x55, 0x85, 0x3c, 0x37, 0x23, 0x32, 0x99, 0xff, 0x56, 0x35, 0x28, 0x0d, 0xc2, 0x80,
	0xe2, 0x80, 0xc6, 0x29, 0x3c, 0x23, 0x67, 0x75, 0x27, 0x67, 0xd6, 0x5d, 0x3e, 0x15, 0xa0, 0x6d,
	0x28, 0xc6, 0x65, 0x50, 0xe0, 0x36, 0x0a, 0x3e, 0xaf, 0x81, 0x3d, 0xa8, 0x0c, 0x42, 0x1f, 0xa3,
	0x97, 0x51, 0xe8, 0xf3, 0xdc, 0xa8, 0x58, 0x65, 0xc6, 0x38, 0x8f, 0x42, 0x9f, 0x7d, 0x13, 0x17,
	0x56, 0x89, 0xbb, 0x53, 0x20, 0xbc, 0xaa, 0x0e, 0x60, 0xcd, 0xf3, 0x51, 0x7f, 0x4a, 0x3c, 0xb1,
	0xe5, 0x32, 0x17, 0x82, 0xe7, 0x9f, 0x4e, 0x89, 0xc7, 0xeb, 0xe5, 0x11, 0xac, 0xc5, 0x2e, 0x0a,
	0x8d, 0x0a, 0xd7, 0xa8, 0xc6, 0x3c, 0xae, 0xf2, 0x10, 0xaa, 0x9e, 0x8f, 0x06, 0x7e, 0x0c, 0x1b,
	0x08, 0x68, 0x3d, 0x56, 0x21, 0x5c, 0x7e, 0x0c, 0x9b, 0x74, 0x51, 0xf1, 0x08, 0xbf, 0xa1, 0xac,
	0x48, 0xe3, 0x14, 0xb9, 0x93, 0x10, 0x19, 0x6f, 0xd8, 0x79, 0x70, 0xaf, 0x03, 0x60, 0xbc, 0xa1,
	0xb6, 0x37, 0xc6, 0xc1, 0x00, 0xab, 0xfb, 0x50, 0x75, 0x07, 0xd4, 0xfb, 0x0e, 0x23, 0xca, 0x00,
	0x92, 0xf8, 0x57, 0x20, 0x58, 0x0e, 0xc3, 0x69, 0x1f, 0xaa, 0x84, 0xe9, 0x52, 0xa1, 0x20, 0x70,
	0x05, 0xc1, 0x62, 0x0a, 0x3a, 0x86, 0x3c, 0xf7, 0x63, 0x0b, 0x14, 0xe7, 0xf9, 0xa5, 0x81, 0x7a,
	0x1d, 0xfb, 0xd2, 0x68, 0x98, 0xe7, 0xa6, 0x71, 0xa6, 0x7c, 0xa6, 0xd6, 0xa0, 0xc2, 0xb9, 0x8e,
	0xf1, 0x8d, 0xa3, 0x48, 0xea, 0x1d, 0xa8, 0x71, 0xd2, 0x68, 0x77, 0x1d, 0xb3, 0xd1, 0xed, 0x28,
	0xb2, 0xaa, 0xc0, 0x1a, 0x67, 0xd9, 0x66, 0xcb, 0xe8, 0x34, 0x0c, 0x25, 0xaf, 0x6e, 0xc2, 0x06,
	0xe7, 0xd4, 0x4d, 0x74, 0x59, 0xb7, 0x9c, 0x8e, 0x61, 0x29, 0x45, 0xbd, 0x05, 0x65, 0x7e, 0x30,
	0xb0, 0xd8, 0xef, 0x40, 0x91, 0xba, 0xd1, 0x08, 0xcf, 0xfc, 0x8d, 0xa9, 0x59, 0xe5, 0xe6, 0xde,
	0x59, 0xb9, 0xfa, 0x9f, 0x25, 0xd8, 0x64, 0x49, 0x35, 0x33, 0x69, 0xe1, 0xab, 0x29, 0x26, 0x54,
	0xfd, 0x31, 0x94, 0xd9, 0xe5, 0x81, 0x22, 0x7c, 0xc5, 0x6d, 0x57, 0x4f, 0xaa, 0xcc, 0xcc, 0xa9,
	0x4b, 0xb0, 0x85, 0xaf, 0xac, 0x52, 0x5f, 0xfc, 0x98, 0x1d, 0x61, 0xb9, 0x5b, 0x1d, 0x61, 0x0b,
	0x77, 0xf3, 0x59, 0xee, 0x16, 0xde, 0xed, 0xee, 0x15, 0x6c, 0xa5, 0xbd, 0x25, 0x93, 0x30, 0x20,
	0x58, 0xfd, 0x1c, 0x2a, 0xb1, 0xbb, 0x64, 0x12, 0xfb, 0xbb, 0xb6, 0xf0, 0x97, 0x4c, 0xac, 0x72,
	0x3f, 0xfe, 0x95, 0xcc, 0xf7, 0xdc, 0x35, 0xf9, 0x2e, 0x27, 0xf2, 0x5d, 0xff, 0xa3, 0x04, 0x3b,
	0x4d, 0x4c, 0x67, 0x4b, 0xb6, 0x3c, 0x42, 0x3f, 0x11, 0x48, 0x72, 0x0a, 0xa4, 0x85, 0x7b, 0xf9,
	0xa4, 0x7b, 0x13, 0xb8, 0xbb, 0xe2, 0xdd, 0xfb, 0x83, 0x72, 0x04, 0x79, 0x9f, 0x8c, 0x88, 0x96,
	0x3b, 0x90, 0x6f, 0x08, 0x01, 0xd7, 0xd1, 0x7f, 0x2f, 0xc1, 0x26, 0xbb, 0x70, 0x3f, 0x71, 0xca,
	0xdc, 0x0a, 0x8d, 0x3a, 0x6c, 0xa5, 0x5d, 0x7b, 0x6f, 0x28, 0xf4, 0xbf, 0x48, 0x70, 0xaf, 0x89,
	0x69, 0x8f, 0xe0, 0xe8, 0x93, 0xc5, 0x3c, 0xbe, 0xdb, 0xe5, 0xc5, 0xdd, 0x9e, 0xbd, 0x3f, 0x75,
	0x0b, 0x0a, 0x63, 0xcf, 0xf7, 0x68, 0x7c, 0x53, 0x0b, 0x42, 0xff, 0x25, 0xec, 0x65, 0x7a, 0xfc,
	0xfe, 0x79, 0xf0, 0x33, 0x28, 0xb3, 0x65, 0xc7, 0x1e, 0xa1, 0x71, 0x2e, 0x5c, 0xe3, 0x3a, 0x03,
	0xb7, 0xe4, 0x8b, 0x85, 0xf4, 0xd7, 0xb0, 0x9d, 0xac, 0xca, 0x46, 0xe8, 0xfb, 0x61, 0xc0, 0x40,
	0x88, 0xf7, 0x25, 0xa5, 0x3a, 0xc7, 0x38, 0x9e, 0xb9, 0xac, 0x23, 0x40, 0x7e, 0xf7, 0x11, 0x70,
	0x01, 0x3b, 0x59, 0x8b, 0x91, 0x49, 0x02, 0x33, 0x29, 0x89, 0xd9, 0x75, 0x05, 0xaf, 0x3f, 0x83,
	0xad, 0x44, 0xe9, 0xfc, 0x10, 0xaf, 0xaf, 0x39, 0x32, 0x9e, 0xc0, 0x76, 0x86, 0x61, 0x32, 0x51,
	0x7f, 0x9a, 0x80, 0x57, 0xba, 0xb1, 0xd4, 0xe6, 0xd8, 0x7e, 0x03, 0xdb, 0xc9, 0x8c, 0xfe, 0x80,
	0x5e, 0x6a, 0xb0, 0x93, 0x65, 0x99, 0x4c, 0xf4, 0xe7, 0xa0, 0x2d, 0xe5, 0xd3, 0x4d, 0xcb, 0x66,
	0x9b, 0x5f, 0xa4, 0x6a, 0x3e, 0x99, 0xaa, 0xbf, 0x80, 0xdd, 0x6b, 0x4c, 0x2f, 0x65, 0x9f, 0x74,
	0xeb, 0xec, 0xfb, 0x93, 0x04, 0x1b, 0xc2, 0xc8, 0xc5, 0xd7, 0xac, 0xad, 0x66, 0x17, 0xe3, 0x1e,
	0x54, 0x26, 0x53, 0xf2, 0x0a, 0x25, 0x3a, 0xa3, 0x32, 0x63, 0xf0, 0x0b, 0x5a, 0x85, 0xfc, 0xd0,
	0xa5, 0x2e, 0x47, 0x69, 0xcd, 0xe2, 0xbf, 0x59, 0xff, 0xe1, 0xbb, 0x11, 0x79, 0xe5, 0x8e, 0x93,
	0x5d, 0x7d, 0x35, 0xe6, 0xf1, 0x96, 0xeb, 0x14, 0xaa, 0xed, 0x05, 0xa9, 0xde, 0x07, 0xad, 0x5d,
	0xb7, 0xec, 0x8b, 0x7a, 0x0b, 0x65, 0x5c, 0xf7, 0xdb, 0x70, 0x27, 0x25, 0x7d, 0x62, 0x77, 0x3b,
	0x8a, 0xa4, 0xff, 0x2e, 0x27, 0x4a, 0x85, 0xf7, 0x76, 0x1f, 0xff, 0xf4, 0xfc, 0x40, 0x13, 0xc2,
	0x2e, 0x94, 0xe7, 0x0d, 0x6f, 0x81, 0x37, 0xbc, 0xa5, 0x28, 0x6e, 0x77, 0x6f, 0x3f, 0x23, 0x24,
	0x3a, 0xea, 0x52, 0xb2, 0xa3, 0xd6, 0x89, 0x28, 0xe8, 0x24, 0x24, 0x1f, 0xf6, 0x56, 0x8f, 0x3b,
	0x52, 0x39, 0xd1, 0x91, 0xb2, 0xf9, 0x70, 0xef, 0xd4, 0xa5, 0x83, 0x57, 0x4d, 0x4c, 0xf9, 0xca,
	0x2d, 0x97, 0xd0, 0x8f, 0x1d, 0x8e, 0x3d, 0xa8, 0xcc, 0xc2, 0x41, 0x34, 0x99, 0x83, 0x50, 0x8e,
	0xe3, 0x41, 0xf4, 0x3f, 0xe4, 0xa0, 0xc6, 0x3d, 0x61, 0x6b, 0xf0, 0x49, 0xf5, 0x0b, 0x50, 0xa7,
	0x01, 0x9f, 0x7b, 0x23, 0x3c, 0x44, 0xc3, 0x90, 0xa2, 0x41, 0x40, 0xe3, 0x6c, 0xde, 0x10, 0x12,
	0x0b, 0x0f, 0xcf, 0x42, 0xda, 0x08, 0x92, 0xe3, 0x54, 0x2e, 0x35, 0x4e, 0x25, 0x47, 0x33, 0x39,
	0x3d, 0x9a, 0xa5, 0x27, 0xad, 0xfc, 0xcd, 0x93, 0x56, 0x61, 0x69, 0xd2, 0xca, 0x1a, 0xa5, 0x8a,
	0x99, 0xa3, 0xd4, 0x29, 0xac, 0xfb, 0xd8, 0xef, 0xe3, 0x08, 0x09, 0x97, 0x44, 0xfc, 0xab, 0x27,
	0x7b, 0x2b, 0x68, 0xd5, 0xb9, 0x9c, 0xed, 0xdd, 0xaa, 0x89, 0x4f, 0x04, 0x87, 0xe8, 0x08, 0x60,
	0x21, 0x64, 0xad, 0x78, 0x6c, 0x31, 0x51, 0xdf, 0x20, 0x58, 0x4e, 0x3c, 0xff, 0xb8, 0x83, 0x41,
	0x38, 0x5d, 0xcc, 0x3f, 0x31, 0x99, 0x80, 0x49, 0x4e, 0xc2, 0xa4, 0xff, 0x5d, 0x82, 0x35, 0x96,
	0x02, 0xb3, 0xf9, 0x3c, 0x55, 0x3a, 0x52, 0xba, 0x74, 0xe6, 0x33, 0x1c, 0xcf, 0x10, 0x2f, 0x78,
	0x19, 0xc6, 0x9d, 0xf6, 0x35, 0x33, 0xdc, 0x2c, 0xa0, 0xf1, 0x0c, 0x37, 0x8f, 0xaf, 0x01, 0x35,
	0xea, 0xf9, 0x18, 0x8d, 0xbd, 0x00, 0xa3, 0xc5, 0xed, 0xf7, 0x28, 0xdb, 0x8a, 0xe3, 0xf9, 0xb8,
	0xe5, 0x05, 0x98, 0x25, 0x69, 0x95, 0x2e, 0x08, 0xfd, 0xb7, 0x12, 0xdc, 0xcf, 0x4e, 0xe5, 0xf7,
	0x2f, 0x23, 0x03, 0x36, 0xc6, 0x2e, 0xa1, 0x48, 0xec, 0x2f, 0xd1, 0x12, 0x3e, 0x58, 0x71, 0x2a,
	0x89, 0x96, 0x55, 0x1b, 0x27, 0x28, 0xa2, 0xff, 0x4d, 0xf4, 0xcc, 0x33, 0xc6, 0x47, 0xef, 0x9f,
	0x6e, 0x38, 0xe7, 0x16, 0xf5, 0x9f, 0x4f, 0x4e, 0xa4, 0xd9, 0x8d, 0xd4, 0xaf, 0x25, 0xde, 0x4d,
	0xa7, 0xfd, 0x7e, 0x7f, 0x14, 0x9b, 0xb0, 0x9e, 0x0a, 0xec, 0x0c, 0xc4, 0x5b, 0x44, 0x76, 0x2d,
	0x11, 0x59, 0xa2, 0xff, 0x35, 0x07, 0xca, 0xb2, 0xca, 0x4d, 0x99, 0x99, 0x79, 0xa8, 0xe7, 0xde,
	0x7d, 0xa8, 0xcb, 0xe9, 0x43, 0x7d, 0xe5, 0xf1, 0xea, 0xf6, 0x13, 0x5a, 0xd6, 0x03, 0x47, 0xf1,
	0x07, 0x3c, 0x70, 0x5c, 0x7b, 0x5d, 0x2c, 0x3f, 0xc0, 0x94, 0x97, 0x1e, 0x60, 0x8e, 0x5e, 0xc4,
	0xf3, 0x71, 0xc3, 0x1f, 0xaa, 0xf7, 0x60, 0xe7, 0x99, 0x71, 0x8a, 0xcc, 0x36, 0x6a, 0xb4, 0xcf,
	0x96, 0x6e, 0xe8, 0x5d, 0xd8, 0x4e, 0xc8, 0x12, 0x23, 0xb6, 0xa4, 0xde, 0x85, 0xcd, 0xb4, 0xa8,
	0x69, 0x75, 0x7b, 0x97, 0x4a, 0xee, 0xe8, 0x05, 0xac, 0xdb, 0xe9, 0x77, 0xbe, 0x3d, 0xb8, 0xcb,
	0x8d, 0xd6, 0x5b, 0xa8, 0x6d, 0x37, 0xc5, 0x5d, 0xdf, 0xe9, 0x5a, 0xed, 0x7a, 0x4b, 0xf9, 0x4c,
	0xfd, 0x02, 0x7e, 0xb2, 0x22, 0x9c, 0xbb, 0x80, 0x7a, 0xb6, 0x61, 0xa1, 0xa7, 0xa6, 0x6d, 0x9e,
	0xb6, 0x0c, 0x45, 0x3a, 0xfa, 0x87, 0x04, 0x4a, 0xdc, 0x10, 0x2d, 0xde, 0x3c, 0xfe, 0x0f, 0xf6,
	0xcd, 0x36, 0xff, 0xb8, 0xd1, 0xed, 0x38, 0x46, 0xc7, 0xc9, 0xea, 0x35, 0xee, 0x83, 0x96, 0xa5,
	0x14, 0xbf, 0x34, 0xe8, 0xf0, 0xf0, 0x3a, 0x29, 0x7a, 0xda, 0x35, 0x1b, 0x86, 0x92, 0x53, 0xf7,
	0x61, 0x2f, 0x4b, 0x87, 0x3f, 0x4e, 0xf0, 0xb7, 0x89, 0x07, 0xb0, 0x9b, 0xa5, 0x60, 0xb6, 0xeb,
	0x4d, 0x43, 0xc9, 0x1f, 0xfd, 0x4a, 0x02, 0x30, 0x17, 0x8f, 0x39, 0xc2, 0xa1, 0xd3, 0x9e, 0x6d,
	0x66, 0xb9, 0x2b, 0x6c, 0x2d, 0xa4, 0xb6, 0xd9, 0x69, 0xb6, 0x8c, 0x18, 0x63, 0x69, 0xe5, 0xe3,
	0x76, 0xaf, 0xe5, 0xcc, 0x23, 0xc0, 0xf0, 0xce, 0x90, 0x5a, 0xdd, 0x96, 0xa1, 0xc8, 0x47, 0xff,
	0xe5, 0x6f, 0xdc, 0xb3, 0xf7, 0x20, 0xa1, 0xca, 0x22, 0x98, 0xe1, 0xc4, 0x92, 0x70, 0xb6, 0xab,
	0xb6, 0xdd, 0x9c, 0xbb, 0x30, 0x17, 0xda, 0x06, 0x13, 0x36, 0x0c, 0xe4, 0x98, 0xcc, 0x85, 0x87,
	0x70, 0x2f, 0x29, 0x7d, 0xd2, 0x35, 0x3b, 0xc2, 0x3d, 0x2e, 0x97, 0x97, 0x4d, 0x3f, 0x33, 0x5a,
	0x8d, 0x6e, 0xdb, 0xe0, 0xa6, 0xf3, 0xcb, 0xa6, 0x79, 0x0e, 0x5c, 0x5a, 0x06, 0x5b, 0x42, 0x29,
	0xb0, 0x7c, 0x4d, 0x4a, 0x59, 0x52, 0xc6, 0xb2, 0x62, 0x1c, 0xc7, 0xcc, 0x2f, 0x51, 0xb3, 0x67,
	0x9e, 0x19, 0x4a, 0xe9, 0xe8, 0x5f, 0x39, 0x00, 0xd1, 0x04, 0xcf, 0xe2, 0x70, 0xf1, 0x35, 0xba,
	0xec, 0xd9, 0x17, 0x59, 0x10, 0x68, 0xb0, 0x95, 0x92, 0x5a, 0xc6, 0x19, 0x3a, 0xeb, 0xc6, 0x29,
	0x93, 0x92, 0x98, 0x1d, 0xc7, 0x6c, 0xd7, 0x1b, 0xcf, 0x51, 0xcb, 0x78, 0x6a, 0xb4, 0x10, 0x0f,
	0xc4, 0x8f, 0xe0, 0x51, 0x4a, 0xc7, 0x6e, 0x58, 0xe6, 0xa5, 0x83, 0xda, 0x75, 0xa7, 0x71, 0xc1,
	0xe0, 0x3c, 0x37, 0xad, 0xb6, 0x22, 0xb3, 0x04, 0xbe, 0x5e, 0xed, 0xbc, 0xdb, 0xeb, 0x9c, 0x29,
	0xf9, 0x15, 0xa5, 0xcb, 0xde, 0x69, 0xcb, 0xb4, 0x2f, 0xd0, 0x65, 0xd7, 0x9e, 0xed, 0xad, 0xa0,
	0x3e, 0x82, 0x07, 0x69, 0x25, 0x26, 0x74, 0xea, 0xf6, 0xcf, 0xd1, 0xb9, 0xd9, 0x31, 0xed, 0x0b,
	0xa5, 0xb8, 0x62, 0x47, 0x44, 0xe5, 0xbc, 0xdb, 0x6a, 0x75, 0x9f, 0xcd, 0x30, 0x52, 0xff, 0x1f,
	0x0e, 0x32, 0x94, 0xda, 0x46, 0xfb, 0xd4, 0xb0, 0x78, 0x30, 0x8d, 0x33, 0xa5, 0xbc, 0x02, 0x41,
	0x4a, 0xab, 0x65, 0x9c, 0x3b, 0x4a, 0xe5, 0xe8, 0xdb, 0xb8, 0x61, 0x9b, 0x9f, 0x59, 0xfb, 0xb0,
	0x27, 0xf4, 0x6c, 0xa3, 0x73, 0x5d, 0xd6, 0xad, 0x28, 0xd8, 0x86, 0x75, 0x52, 0x37, 0x15, 0x29,
	0x4b, 0x58, 0x37, 0x4f, 0x98, 0x5c, 0xc9, 0x9d, 0x1a, 0xa0, 0x0d, 0x42, 0xff, 0xf8, 0xad, 0xf7,
	0x36, 0x9c, 0xb2, 0x83, 0xd4, 0x0f, 0x87, 0x78, 0x2c, 0xfe, 0xda, 0x79, 0xf1, 0xf9, 0x28, 0x1c,
	0xbb, 0xc1, 0xe8, 0xf8, 0xeb, 0x13, 0x4a, 0x8f, 0x07, 0xa1, 0xff, 0x98, 0xb3, 0x07, 0xe1, 0xf8,
	0xb1, 0x3b, 0x99, 0x3c, 0xfe, 0x1e, 0xf7, 0xbf, 0xf4, 0xfc, 0x2f, 0xf9, 0x91, 0xdb, 0x2f, 0x72,
	0xd1, 0x57, 0xff, 0x0b, 0x00, 0x00, 0xff, 0xff, 0xa6, 0x73, 0x0c, 0x2e, 0x4f, 0x1a, 0x00, 0x00,
}
