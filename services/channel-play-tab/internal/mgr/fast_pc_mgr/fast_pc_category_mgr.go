package fast_pc_mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-play-tab"
	"golang.52tt.com/services/channel-play-tab/internal/cache"
	"golang.52tt.com/services/channel-play-tab/internal/dao"
	"strings"
)

type FastPcCategoryMgr struct {
	mongoDao dao.IMongoDao
}

func NewFastPcCategoryMgr(mongoDao dao.IMongoDao) *FastPcCategoryMgr {
	return &FastPcCategoryMgr{
		mongoDao: mongoDao,
	}
}

func (m *FastPcCategoryMgr) GetFastPCCategoryConfig(ctx context.Context) ([]*dao.FastPCCategoryConfig, error) {
	data, err := m.mongoDao.GetFastPCCategoryConfig(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFastPCCategoryConfig failed, mongoDao.GetFastPCCategoryConfig err:%v", err)
		return nil, err
	}
	return data, nil
}

func (m *FastPcCategoryMgr) GetNewFastPCCategoryId(ctx context.Context) (uint32, error) {
	newId, err := m.mongoDao.CreateNewTabId(ctx, dao.FastPcCategoryIdCreatorType)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewFastPCCategoryId mongoDao.CreateNewTabId err:%v", err)
		return 0, err
	}
	return newId, nil
}

func (m *FastPcCategoryMgr) UpsertFastPCCategoryConfig(ctx context.Context, conf *dao.FastPCCategoryConfig) error {
	if err := m.checkTabIdsDuplicate(ctx, conf); err != nil {
		return err
	}

	err := m.mongoDao.UpsertFastPCCategoryConfig(ctx, conf)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpsertFastPCCategoryConfig mongoDao.UpsertFastPCCategoryConfig err:%v, conf:%v", err, conf)
		return err
	}
	return nil
}

func (m *FastPcCategoryMgr) DeleteFastPCCategoryConfig(ctx context.Context, id uint32) error {
	err := m.mongoDao.DeleteFastPCCategoryConfig(ctx, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteTab mongoDao.DeleteTab err:%v, id:%s", err, id)
		return err
	}
	return nil
}

func (m *FastPcCategoryMgr) ResortFastPCCategoryConfig(ctx context.Context, ids []uint32) error {
	err := m.mongoDao.ResortFastPCCategoryConfig(ctx, ids)
	if err != nil {
		log.ErrorWithCtx(ctx, "ResortFastPCCategoryConfig fail, err:%v ids:%v", err, ids)
		return err
	}
	return nil
}

// checkTabIdsDuplicate 检查小游戏分类配置中的 tabIds 是否与其他分类的 tabIds 重复
func (m *FastPcCategoryMgr) checkTabIdsDuplicate(ctx context.Context, conf *dao.FastPCCategoryConfig) error {
	if conf == nil || len(conf.TabIds) == 0 {
		return nil
	}

	allCategoryConfs, err := m.mongoDao.GetFastPCCategoryConfig(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkTabIdsDuplicate GetFastPCCategoryConfig err:%v", err)
		return err
	}

	var (
		duplicateTabIds     = make([]uint32, 0)
		currentTabIds       = make(map[uint32]bool)
		deduplicateTabIdMap = make(map[uint32]bool)
	)

	for _, tabId := range conf.TabIds {
		currentTabIds[tabId] = true
	}

	for _, otherConf := range allCategoryConfs {
		switch conf.CategoryType {
		case pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MINI_GAME:
			// 如果当前是小游戏分类或团战分类，则需要检查其他分类的 tabIds 是否与当前分类的 tabIds 重复
			if otherConf.CategoryType == conf.CategoryType {
				continue
			}

		default:
			// 如果当前是其他分类，则需要检查小游戏分类以及团战的 tabIds 是否与当前分类的 tabIds 重复
			if otherConf.CategoryType != pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MINI_GAME &&
				otherConf.CategoryType != pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MELEE {
				continue
			}
		}
		for _, tabId := range otherConf.TabIds {
			if currentTabIds[tabId] {
				if _, exist := deduplicateTabIdMap[tabId]; !exist {
					duplicateTabIds = append(duplicateTabIds, tabId)
					deduplicateTabIdMap[tabId] = true
				}
			}
		}
	}

	if len(duplicateTabIds) > 0 {
		var errMsg strings.Builder
		for _, tabId := range duplicateTabIds {
			tabInfo := cache.GetTabInfoById(tabId)
			errMsg.WriteString(fmt.Sprintf("%s(%d),", tabInfo.GetName(), tabInfo.GetId()))
		}
		if conf.CategoryType == pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MINI_GAME || conf.CategoryType == pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MELEE{
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("以下玩法已被其他分类绑定:%s不可重复配置", errMsg.String()))
		} else {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("以下玩法已被小游戏或团战分类绑定:%s不可重复配置", errMsg.String()))
		}
	}

	return nil
}
