package server

import (
	"context"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_ext"
	"golang.52tt.com/clients/channel"
	channel_open_game "golang.52tt.com/clients/channel-open-game"
	channel_scheme_conf_mgr "golang.52tt.com/clients/channel-scheme-conf-mgr"
	"golang.52tt.com/clients/configserver"
	gameServerV2Client "golang.52tt.com/clients/game-server-v2"
	mysteryplace "golang.52tt.com/clients/mystery-place"
	presence "golang.52tt.com/clients/presence/v2"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	"golang.52tt.com/protocol/services/muse-social-community"
	"golang.52tt.com/protocol/services/music-nest"

	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	"golang.52tt.com/protocol/services/rcmd/rcmd_channel_name"
	"golang.52tt.com/protocol/services/satoshi"
	"math/rand"
	"time"

	channelMsgApi "golang.52tt.com/clients/channel-msg-api"
	channel_play_tab "golang.52tt.com/clients/channel-play-tab"
	device_info_service "golang.52tt.com/clients/datacenter/device-info-service"
	"golang.52tt.com/clients/entertainmentrecommendback"
	game_pal "golang.52tt.com/clients/game-pal"
	game_ugc_content "golang.52tt.com/clients/game-ugc-content"
	game_user_rate "golang.52tt.com/clients/game-user-rate"
	newbiepage "golang.52tt.com/clients/newbie-page"
	rcmd_game_pal_card "golang.52tt.com/clients/rcmd/rcmd-game-pal-card"
	rcmd_statistics_query "golang.52tt.com/clients/rcmd/rcmd-statistics-query"
	riskMngApi "golang.52tt.com/clients/risk-mng-api"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	ttc_proxy "golang.52tt.com/clients/ttc-proxy"
	"golang.52tt.com/clients/ugc/content"
	"golang.52tt.com/pkg/abtest"
	pkgConfig "golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/kafka"
	eventlink "golang.52tt.com/pkg/kafka_eventlink"
	"golang.52tt.com/pkg/speedlimit"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	channel_play_index "golang.52tt.com/protocol/services/channel-play-index"
	channel_play_middle "golang.52tt.com/protocol/services/channel-play-middle"
	channel_scheme_middle "golang.52tt.com/protocol/services/channel-scheme-middle"
	"golang.52tt.com/protocol/services/demo/echo"
	revenue_recommend_svr "golang.52tt.com/protocol/services/revenue-recommend-svr"
	"golang.52tt.com/services/channel-play-logic/internal/cache/cache_client"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/game_task_mgr"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/negative_feedback"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/pub_supervision"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/tab_list"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/word_channel_mgr"
	"golang.52tt.com/services/channel-play-logic/internal/rpc"

	accountCpp "golang.52tt.com/clients/account"
	account "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	channel_scheme "golang.52tt.com/clients/channel-scheme"
	"golang.52tt.com/clients/channelapi"
	channelIm "golang.52tt.com/clients/channelim"
	"golang.52tt.com/clients/channelmic"
	channelol_stat_go "golang.52tt.com/clients/channelol-stat-go"
	"golang.52tt.com/clients/expsvr"
	gameCard "golang.52tt.com/clients/game-card"
	gangup_channel_cli "golang.52tt.com/clients/gangup-channel"
	hobby_channel_cli "golang.52tt.com/clients/hobby-channel"
	new_user_reception "golang.52tt.com/clients/new-user-reception"
	tcChannel "golang.52tt.com/clients/topic-channel/channel"
	rcmdChannelLabel "golang.52tt.com/clients/topic-channel/rcmd-channel-label"
	recommendation_gen "golang.52tt.com/clients/topic-channel/recommendation-gen"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/channel-play-logic/internal/breaker"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/block_mgr"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list"
	manager "golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_operation"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/game_filter_mgr"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/home_page_head_mgr"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/minigame"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/mutientry_filter_mgr"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/play_guide"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/room_config_mgr"
	grpcOP "google.golang.org/grpc"
)

const abtestUrl = "http://abtestlogic.commonservice.svc.cluster.local:8000/AbtestLogicService/GetUsersAbtestByTag"

type ChannelPlayLogic struct {
	ChannelListMgr     *manager.ChannelListMgr
	SecondaryFilterMgr block_mgr.ISecondaryFilterMgr
	RoomConfigMgr      *room_config_mgr.RoomConfigMgr
	GameFilterMgr      *game_filter_mgr.GameFilterMgr

	ChannelOperationMgr   *channel_operation.ChannelOperationMgr
	MutiEntryFilterMgr    *mutientry_filter_mgr.MutiEntryFilterMgr
	HomePageHeadConfigMgr *home_page_head_mgr.HomePageHeadMgr
	MoreTabMgr            *mutientry_filter_mgr.MoreTabMgr

	miniGameMgr  *minigame.Manager
	playGuideMgr *play_guide.Manager

	negativeFeedbackMgr *negative_feedback.NegativeFeedbackMgr

	wordChannelMgr *word_channel_mgr.WordChannelMgr

	gameTaskMgr *game_task_mgr.GameTaskMgr

	tabListMgr    *tab_list.TabListMgr
	errOverInst   *speedlimit.ErrorOver
	revenueClient *revenue_recommend_svr.Client

	rcmdLabelClient         *rcmdChannelLabel.Client
	musicTopicChannelClient music_topic_channel.MusicChannelClient

	channelListCoroutineTimeOut int

	channelMicMgr *channel_operation.ChannelMicMgr

	satoshiClient satoshi.SatoshiClient
}

type StartConfig struct {
	// [optional] from startup arguments
	Environment string `json:"environment"`
	// from config file
	GameActivityTaskKafka *pkgConfig.KafkaConfig `json:"game_activity_task_kafka"`

	ChannelStatKafka *pkgConfig.KafkaConfig `json:"channel_stat_kafka"`

	RcmdNilDoudiRatio uint32 `json:"rcmd_nil_doudi_ratio"` //推荐列表返回空比例时走兜底

	ChannelListCoroutineTimeOut int `json:"channel_list_coroutine_time_out"`

	ChannelListRcmdTimeOut int `json:"channel_list_rcmd_time_out"`
}

func (s *StartConfig) GetChannelStatKafka() *pkgConfig.KafkaConfig {
	return s.ChannelStatKafka
}

func (s *StartConfig) GetChannelListRcmdTimeOut() int {
	if s.ChannelListRcmdTimeOut == 0 {
		return 900
	}
	return s.ChannelListRcmdTimeOut
}

func (s *StartConfig) GetChannelListCoroutineTimeOut() int {
	if s.ChannelListCoroutineTimeOut == 0 {
		return 2500
	}
	return s.ChannelListCoroutineTimeOut
}

func (s *ChannelPlayLogic) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func NewChannelPlayLogic(ctx context.Context, cfg *StartConfig) (*ChannelPlayLogic, error) {
	s := &ChannelPlayLogic{
		channelListCoroutineTimeOut: cfg.GetChannelListCoroutineTimeOut(),
	}
	err := conf.Parse(ctx, cfg.Environment)
	if err != nil {
		log.ErrorWithCtx(ctx, "Setup config fail: %v ", err)
		return s, err
	}
	opts := []grpcOP.DialOption{grpcOP.WithBlock()}
	tCChannelClient, err := tcChannel.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "new topic-channel-client err: %v ", err)
		return s, err
	}
	tCTabClient, _ := tcTab.NewClient(opts...)
	channelClient := channel.NewClient(opts...)
	genRecommendationClient, _ := recommendation_gen.NewClient(opts...)
	accountClient, _ := account.NewClient(opts...)
	channelOlStatCli, _ := channelol_stat_go.NewClient()
	gangupChannelClient, err := gangup_channel_cli.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "gangupChannelClient err %v", err)
		return s, err
	}
	channelMsgApiClient, err := channelMsgApi.NewIClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelMsgApiClient err %v", err)
		return s, err
	}
	expClient := expsvr.NewClient(opts...)
	realNameClient, _ := ttc_proxy.NewClient(opts...)
	censoringClient := censoring_proxy.NewClient(opts...)
	//darkClient, err := darkserver.NewClient(opts...)
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "darkserver.NewClient err %v", err)
	//	return s, err
	//}
	musicTopicChannelClient, err := music_topic_channel.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "new musicTopicChannelClient err(%v)", err)
		return s, err
	}
	s.musicTopicChannelClient = musicTopicChannelClient

	gameServerClient, _ := gameServerV2Client.NewClient(opts...)
	mysteryPlaceClient, err := mysteryplace.NewClient(opts...)
	if err != nil {
		log.Errorf("Setup mysteryplace.NewClient err: %v", err)
		return s, err
	}
	channelOpenGameClient, err := channel_open_game.NewClient(opts...)
	if err != nil {
		log.Errorf("Setup channel_open_game.NewClient err: %v", err)
		return s, err
	}

	channelSchemeConfClient := channel_scheme_conf_mgr.NewClient(opts...)
	rcmdChannelLabelClient, err := rcmdChannelLabel.NewClient(opts...)
	if err != nil {
		log.Errorf("Setup rcmdChannelLabel.NewClient err: %v", err)
		return s, err
	}
	configServerClient, err := configserver.NewClient(opts...)
	if err != nil {
		log.Errorf("Setup configserver.NewClient err: %v", err)
		return s, err
	}
	channelPlayTabClient, err := channel_play_tab.NewClient(opts...)
	if err != nil {
		log.Errorf("Setup channel_play_tab.NewClient err: %v", err)
		return s, err
	}
	gameUgcContentClient, _ := game_ugc_content.NewClient()
	gamePalClient, err := game_pal.NewClient()
	gameCardClient, err := gameCard.NewClient(opts...)
	if err != nil {
		return s, err
	}
	cacheClient := &cache_client.CacheClients{
		TCTabClient:             tCTabClient,
		GameServerClient:        gameServerClient,
		MysteryPlaceClient:      mysteryPlaceClient,
		ChannelOpenGameClient:   channelOpenGameClient,
		ChannelSchemeConfClient: channelSchemeConfClient,
		RcmdChannelLabelClient:  rcmdChannelLabelClient,
		ConfigServerClient:      configServerClient,
		ChannelPlayTabClient:    channelPlayTabClient,
		GameUgcContentClient:    gameUgcContentClient,
		GamePalClient:           gamePalClient,
		GameCardClient:          gameCardClient,
	}
	err = cache.NewCache(ctx, cacheClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "new cache error %v", err)
		return s, err
	}

	//greenBabaClient := greenbaba.NewClient()
	//antispamAccountClient := antispamaccount.NewClient(opts...)
	channelFilterMgr := pub_supervision.NewPubChannelFilterManager(tCChannelClient)
	userFilterMgr := pub_supervision.NewUserFilterManager(expClient)
	accountCppClient, err := accountCpp.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "accountCpp.NewClient err(%v)", err)
		return s, err
	}

	pubSupervision := pub_supervision.NewPubSupervisory(channelFilterMgr, userFilterMgr)

	supervisorInst, err := supervision.NewSupervisory(accountClient, expClient, realNameClient, censoringClient,
		accountCppClient, cache_client.ChannelPlayTabClient, conf.GetEnv())
	if err != nil {
		return s, err
	}
	satoshiClient, err := satoshi.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "satoshi.NewClient(ctx) err :%v", err)
		return s, err
	}
	s.satoshiClient = satoshiClient
	revenueClient, err := revenue_recommend_svr.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "revenue_recommend_svr.NewClient err(%v)", err)
		return s, err
	}
	s.revenueClient = revenueClient

	channelMicClient := channelmic.NewClient(opts...)

	s.rcmdLabelClient = rcmdChannelLabelClient

	channelSchemeClient := channel_scheme.NewClient(opts...)

	errOverInst := speedlimit.NewErrorOver(20, cfg.RcmdNilDoudiRatio)

	channelPlayMiddleCli, err := channel_play_middle.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "channel_play_middle.NewClient() err(%v)", err)
		return s, err
	}
	channelGoClient, err := channel_go.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "channel_go.NewClient() err(%v)", err)
		return s, err
	}
	musicNestClient, err := music_nest.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "music_nest.NewClient() err(%v)", err)
		return s, err
	}
	museSocialCommunityClient, err := muse_social_community.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "muse_social_community.NewClient() err(%v)", err)
		return s, err
	}
	mgrParams := &channel_list.ChannelListMgrParam{
		TCChannelClient:           tCChannelClient,
		ChannelClient:             channelGoClient,
		GenRecommendationClient:   genRecommendationClient,
		SupervisorInst:            supervisorInst,
		ChannelPlayTabClient:      channelPlayTabClient,
		GangupChannelClient:       gangupChannelClient,
		ErrOverInst:               errOverInst,
		ChannelPlayMiddleClient:   channelPlayMiddleCli,
		MusicTopicClient:          musicTopicChannelClient,
		MusicNestClient:           musicNestClient,
		MuseSocialCommunityClient: museSocialCommunityClient,
		ChannelListRcmdTimeOut:    cfg.GetChannelListRcmdTimeOut(),
	}

	mgr, err := channel_list.NewChannelListMgr(mgrParams)
	if err != nil {
		log.ErrorWithCtx(ctx, "new mgr err %v", err)
		return s, err
	}
	s.ChannelListMgr = mgr

	baseBlockMgr := block_mgr.NewBaseBlockHandler(rcmdChannelLabelClient, tCTabClient)

	businessBlockMgr := block_mgr.NewBusinessBlockFilterMgr()

	newUserReceptionClient, err := new_user_reception.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "new_user_reception.NewClient err %v", err)
		return s, err
	}

	deviceInfoServiceCli, err := device_info_service.NewClientToDeviceService()
	if err != nil {
		log.ErrorWithCtx(ctx, "device_info_service.NewClient() fail, err:%v", err)
		return nil, err
	}
	abtestClient := abtest.NewABTestClient(abtestUrl, uint32(abtest.APPID_TTyuyin), "")
	secondaryFilterMgr, err := block_mgr.NewSecondaryFilterMgr(baseBlockMgr, businessBlockMgr, supervisorInst, rcmdChannelLabelClient, newUserReceptionClient, expClient, deviceInfoServiceCli, accountClient, abtestClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "new blockMgr err %v", err)
		return s, err
	}
	s.SecondaryFilterMgr = secondaryFilterMgr

	hobbyChannelClient, err := hobby_channel_cli.NewClient(opts...)
	if err != nil {
		return s, err
	}

	diyFilterSourceInst, err := game_filter_mgr.NewDIYFilterSource(gangupChannelClient, hobbyChannelClient)
	if err != nil {
		return s, err
	}

	configFilterSourceInst, err := game_filter_mgr.NewConfigFilterSource(gameCardClient)
	if err != nil {
		return s, err
	}
	rcmdChannelNameClient := rcmd_channel_name.MustNewClientTo(ctx, "rcmd-entrance-server.rcmd-gateway.svc:80")
	roomConfigMgr, err := room_config_mgr.NewRoomConfigMgr(tCTabClient, rcmdChannelNameClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "new blockMgr err %v", err)
		return s, err
	}
	s.RoomConfigMgr = roomConfigMgr

	gameFilterMgr, err := game_filter_mgr.NewGameFilterMgr(supervisorInst, diyFilterSourceInst, configFilterSourceInst)
	if err != nil {
		log.ErrorWithCtx(ctx, "new NewGameFilterModel err %v", err)
		return s, err
	}
	s.GameFilterMgr = gameFilterMgr

	// 接入熔断
	err = breaker.Setup(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Setup breaker err(%v)", err)
		return s, err
	}

	channelApiClient := channelapi.NewClient(opts...)

	riskMngApiClient, err := riskMngApi.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "new riskMngApi.NewClient err(%v)", err)
		return s, err
	}
	channelPlayIndexClient, err := channel_play_index.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "channel_play_index.NewClient fail, err:%s", err.Error())
		return nil, err
	}
	micExtCli, err := channel_mic_ext.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelMicExt.NewClient() fail, err:%s", err.Error())
		return nil, err
	}

	publisher, err := channel_operation.NewPublisher(gangupChannelClient, tCChannelClient, channelMicClient,
		channelApiClient, rcmdChannelLabelClient, channelPlayIndexClient, micExtCli, musicTopicChannelClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "new NewPublisher err(%v)", err)
		return s, err
	}
	//musicTopicChannelClient, err := music_channel.NewClient(opts...)
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "new musicTopicChannelClient err(%v)", err)
	//	return s, err
	//}
	//schemeClient := channel_scheme.NewClient(opts...)
	creator, err := channel_operation.NewCreator(supervisorInst, tCChannelClient, pubSupervision)
	if err != nil {
		log.ErrorWithCtx(ctx, "new NewCreator err(%v)", err)
		return s, err
	}
	cancelPublisher, err := channel_operation.NewCancelPublisher(gangupChannelClient, channelClient, musicTopicChannelClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "new NewCancelPublisher err(%v)", err)
		return s, err
	}
	commonHandler, err := channel_operation.NewCommonHandle(channelClient, channelSchemeClient, supervisorInst,
		channelApiClient, channelOlStatCli, accountClient, riskMngApiClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "new NewCommonHandle err(%v)", err)
		return s, err
	}
	channelImClient := channelIm.NewClient(opts...)

	pusher, err := channel_operation.NewPusher(channelImClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "new channel_operation.NewPusher err %v", err)
		return s, err
	}

	// 百灵数据统计 初始化
	bylinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to new kfk collector: %v", err)
		return s, err
	}
	bylink.InitGlobalCollector(bylinkCollect)

	if cfg.GetChannelStatKafka() != nil {
		rpc.ChannelStatPro, err = eventlink.NewEventLinkKafkaProducer(cfg.GetChannelStatKafka().ClientID, cfg.GetChannelStatKafka().Topics, cfg.GetChannelStatKafka().BrokerList())
		if err != nil {
			log.ErrorWithCtx(ctx, "event.NewEventLinkKafkaProducer kafkaBrokers(%v) topic(%v) err(%v)", cfg.GetChannelStatKafka().BrokerList(), cfg.GetChannelStatKafka().Topics, err)
			return nil, err
		}
	}

	entertainmentRecommendClient := entertainmentrecommendback.NewClient()
	topicChannelCfgInfoHandle, _ := channel_operation.NewTopicChannelCfgInfo(tCChannelClient, channelMicClient, tCTabClient,
		entertainmentRecommendClient, channelSchemeClient)

	channelOperationMgr, err := channel_operation.NewChannelOperationMgr(supervisorInst, creator, publisher,
		cancelPublisher, commonHandler, pusher, topicChannelCfgInfoHandle, pubSupervision)
	if err != nil {
		log.ErrorWithCtx(ctx, "new NewChannelOperationMgr err %v", err)
		return s, err
	}
	s.ChannelOperationMgr = channelOperationMgr

	// 新版首页金刚区
	s.HomePageHeadConfigMgr, err = home_page_head_mgr.NewHomePageHeadMgr(accountClient, gangupChannelClient, tCTabClient,
		gameCardClient, abtestClient, channelPlayTabClient, supervisorInst)
	if err != nil {
		log.ErrorWithCtx(ctx, "new NewHomePageHeadMgr err %v", err)
		return s, err
	}

	// 新版更多玩法管理
	s.MoreTabMgr = mutientry_filter_mgr.NewMoreTabMgr(supervisorInst, gameUgcContentClient)

	//新版首页筛选器

	mixCommonHandler := mutientry_filter_mgr.NewCommonHandler(gangupChannelClient, gameCardClient, musicTopicChannelClient)
	newbiepageClient, err := newbiepage.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "new biepage.NewClient err(%v)", err)
		return s, err
	}
	mixHomePageFilter := mutientry_filter_mgr.NewMixHomePageFilter(mixCommonHandler, musicTopicChannelClient, tCChannelClient, newbiepageClient)
	gameZoneFilter := mutientry_filter_mgr.NewGameZoneFilter(mixCommonHandler)
	miniGameZoneFilter := mutientry_filter_mgr.NewMiniGameZoneFilter(mixCommonHandler)

	mutiEntryFilterMgr := mutientry_filter_mgr.NewMutiEntryFilterMgr(supervisorInst, mixHomePageFilter, gameZoneFilter, miniGameZoneFilter,
		rcmdChannelLabelClient, baseBlockMgr, mixCommonHandler, abtestClient, deviceInfoServiceCli, accountClient, musicTopicChannelClient)
	s.MutiEntryFilterMgr = mutiEntryFilterMgr

	s.miniGameMgr = minigame.NewManger(supervisorInst, channelPlayTabClient)
	ugcContent, err := content.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "content.NewClient() failed err(%v)", err)
		return nil, err
	}
	rcmdGamePalClient, err := rcmd_game_pal_card.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "rcmd_game_pal_card.NewClient fail, err:%s", err.Error())
		return nil, err
	}

	gameUserRateClient, err := game_user_rate.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "game_user_rate.NewClient() fail, err:%s", err.Error())
		return nil, err
	}

	rcmdStatisticsQueryClient, err := rcmd_statistics_query.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "rcmd_statistics_query.NewClient() fail, err:%s", err.Error())
		return nil, err
	}

	presenceClient, _ := presence.NewClient()

	s.playGuideMgr = play_guide.NewManager(
		baseBlockMgr,
		rcmdChannelLabelClient,
		abtestClient,
		channelPlayTabClient,
		gameUgcContentClient,
		ugcContent,
		rcmdGamePalClient,
		gamePalClient,
		gameUserRateClient,
		accountClient,
		channelPlayIndexClient,
		rcmdStatisticsQueryClient,
		deviceInfoServiceCli,
		tCTabClient,
		rcmdChannelLabelClient,
		presenceClient,
	)

	s.negativeFeedbackMgr = negative_feedback.NewNegativeFeedbackMgr(gangupChannelClient, tCTabClient, accountClient, deviceInfoServiceCli)
	rand.Seed(time.Now().UnixNano())
	var channelSchemeMiddleClient *channel_scheme_middle.Client
	channelSchemeMiddleClient, err = channel_scheme_middle.NewClient(ctx, opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "channel_scheme_middle.NewClient failed err(%v)", err)
		return nil, err
	}

	s.wordChannelMgr = word_channel_mgr.NewWordChannelMgr(gangupChannelClient, accountClient, commonHandler, channelMsgApiClient, channelSchemeMiddleClient)
	gameActiveTaskKafkacfg := cfg.GameActivityTaskKafka
	gameActivityTaskProducer, err := kafka.NewPublisher("channel_play_logic", gameActiveTaskKafkacfg)

	if err != nil {
		log.ErrorWithCtx(ctx, "NewChannelServer NewEventLinkKafkaProducer cfg(%v) err(%v)", gameActiveTaskKafkacfg, err)
		return nil, err
	}
	s.gameTaskMgr = game_task_mgr.NewGameTaskMgr(gameActivityTaskProducer)
	s.tabListMgr = tab_list.NewTabListMgr(supervisorInst, channelPlayTabClient, channelPlayMiddleCli)

	s.channelMicMgr = channel_operation.NewChannelMicMgr(gangupChannelClient, channelMsgApiClient, channelGoClient)
	return s, nil
}

func (s *ChannelPlayLogic) GetCache(ctx context.Context, in *channel_play.GetCacheReq) (out *channel_play.GetCacheResp, err error) {
	out = &channel_play.GetCacheResp{
		CategoryTabsMap: make(map[uint32]*channel_play.CacheData),
		GameCardTabsMap: make(map[uint32]*channel_play.CacheData),
	}
	channelPlayLogicCache := cache.GetTabInfoCache()
	//category
	categoryMap := channelPlayLogicCache.GetCategoryMap()
	categoryIdMap := channelPlayLogicCache.GetCategoryIdMap()
	//tab
	tabIdMap := channelPlayLogicCache.GetTabIdCache()
	//小众游戏项
	minorityGameTabMap := channelPlayLogicCache.GetMinorityGameTab()
	//发布
	releaseConditionMap := channelPlayLogicCache.GetReleaseConditionCache()

	//gamecard
	gameCardIdTabMap := channelPlayLogicCache.GetGameCardIdTabMap()
	//block项
	baseBlocksMap := channelPlayLogicCache.GetBaseBlocksMap()

	for id := range categoryIdMap {
		out.CategoryIds = append(out.CategoryIds, id)
	}
	for cid, infos := range categoryMap {
		var tabIds []uint32
		for _, tabInfo := range infos {
			tabIds = append(tabIds, tabInfo.GetId())
		}
		out.CategoryTabsMap[cid] = &channel_play.CacheData{
			Ids: tabIds,
		}
	}
	for id := range tabIdMap {
		out.TabIds = append(out.TabIds, id)
	}
	for _, info := range minorityGameTabMap {
		out.MinGameTabIds = append(out.MinGameTabIds, info.GetId())
	}

	releaseLen := len(releaseConditionMap)
	for _, info := range releaseConditionMap {
		blockDatas := make([]*channel_play.CacheBlockData, 0, releaseLen)
		for _, b := range info.GetBlocks() {
			var elemIds []uint32
			for _, e := range b.GetElems() {
				elemIds = append(elemIds, e.GetId())
			}
			blockData := &channel_play.CacheBlockData{
				BlockId: b.GetId(),
				ElemIds: elemIds,
			}
			blockDatas = append(blockDatas, blockData)
		}
		if len(blockDatas) != 0 {
			out.TabBlockIds = append(out.TabBlockIds, &channel_play.CacheTabData{
				TabId:      info.GetTabId(),
				BlockDatas: blockDatas,
			})
		}

		displayBlockDatas := make([]*channel_play.CacheBlockData, 0, releaseLen)
		for _, b := range info.GetDisplayBlockInfos() {
			var elemIds []uint32
			for _, e := range b.GetElemBindBlockInfos() {
				elemIds = append(elemIds, e.GetElemId())
			}
			blockData := &channel_play.CacheBlockData{
				BlockId: b.BlockId,
				ElemIds: elemIds,
			}
			displayBlockDatas = append(displayBlockDatas, blockData)
		}
		if len(displayBlockDatas) != 0 {
			out.TabDisplayBlockIds = append(out.TabDisplayBlockIds, &channel_play.CacheTabData{
				TabId:      info.GetTabId(),
				BlockDatas: displayBlockDatas,
			})
		}
	}

	for tabId, baseBlocks := range baseBlocksMap {
		baseBlockDatas := make([]*channel_play.CacheBlockData, 0, releaseLen)
		for _, b := range baseBlocks {
			var elemIds []uint32
			for _, e := range b.GetElems() {
				elemIds = append(elemIds, e.GetId())
			}
			blockData := &channel_play.CacheBlockData{
				BlockId: b.GetId(),
				ElemIds: elemIds,
			}
			baseBlockDatas = append(baseBlockDatas, blockData)
		}
		if len(baseBlockDatas) != 0 {
			out.TabBaseBlockIds = append(out.TabBaseBlockIds, &channel_play.CacheTabData{
				TabId:      tabId,
				BlockDatas: baseBlockDatas,
			})
		}
	}

	for tabId, infos := range gameCardIdTabMap {
		var ids []uint32
		for _, info := range infos {
			ids = append(ids, info.GetId())
		}
		out.GameCardTabsMap[tabId] = &channel_play.CacheData{
			Ids: ids,
		}
	}

	log.Infof("getcache channel-play-logic:%d,%d,%d,%d,%d,%d,%d,%d", len(out.GetCategoryIds()), len(out.GetCategoryTabsMap()), len(out.GetTabIds()), len(out.GetMinGameTabIds()), len(out.GetTabDisplayBlockIds()), len(out.GetTabBlockIds()), len(out.GetMinGameTabIds()), len(out.GetTabBaseBlockIds()))
	return out, nil
}

func (s *ChannelPlayLogic) ShutDown() {
	s.gameTaskMgr.ShutDown()
	log.DebugWithCtx(context.Background(), "channelPlayLogic shutdown")
}
