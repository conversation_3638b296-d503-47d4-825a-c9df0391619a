package server

import (
	"context"
	"fmt"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/monkey-send-chat/monkey_sender"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/protocol/common/status"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	revenue_recommend_svr "golang.52tt.com/protocol/services/revenue-recommend-svr"
	"golang.52tt.com/protocol/services/satoshi"
	"golang.52tt.com/services/channel-play-logic/internal/breaker"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/convert"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/data"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/param"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"time"
)

const (
	getUserInfoErrorMsg = "获取用户信息失败"
)

func (s *ChannelPlayLogic) ListTopicChannel(ctx context.Context, in *channel_play.ListTopicChannelReq) (out *channel_play.ListTopicChannelResp, err error) {
	out = &channel_play.ListTopicChannelResp{}
	startTime := time.Now()
	utils.PrintCtxDeadLineLog(ctx, "ListTopicChannel_logic")
	if conf.ChannelPlayLogicConfig.GetIsSentinelTest(breaker.ChannelList) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, breaker.ChannelList+" sentinel test")
	}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	if in.Count == 0 {
		in.Count = 10
	}

	if protocol.IsFastPcClientType(uint32(serviceInfo.ClientType)) && in.GetTabId() == conf.PublicSwitchConfig.GetFastPcChatTabId() {
		// 扩列玩法，极速PC传过来的是开黑的tabId，底层给的是MT的扩列id
		in.TabId = conf.PublicSwitchConfig.GetMuseChatTabId()
	}

	if protocol.IsFastPcClientType(uint32(serviceInfo.ClientType)) && len(in.GetCategoryIds()) > 0 {
		// 把极速PC的分类id转换成tabId
		in.TabIds = cache.GetTabInfoCache().GetTabIdsByFastPCCategoryIds(in.GetCategoryIds())
		log.DebugWithCtx(ctx, "ListTopicChannel fast pc categoryIds:%v convert to tabIds:%v", in.GetCategoryIds(), in.GetTabIds())
		in.CategoryIds = []uint32{}
	}

	// 未成年监管过滤配置
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      conf.PublicSwitchConfig.GetRegisterLimitTime(),
		UserLevelLimit:         conf.PublicSwitchConfig.GetUserLevelLimit(),
		RealNameStandardStatus: conf.PublicSwitchConfig.GetRealNameStandardStatus(),
	}
	tabFilter, categoryFilter := s.ChannelListMgr.SupervisorInst.GetFilterMap(ctx, serviceInfo, in.GetChannelPackageId(), supConfInst)

	var confTabs []*music_topic_channel.FilterIdToConfTabResp_ConfTab
	if len(in.GetFilterId()) > 0 {
		confTabs, err = s.ChannelListMgr.FilterIdToTab(ctx, in.GetFilterId(), in.GetSubFilterIds())
		if err != nil {
			log.WarnWithCtx(ctx, "ListTopicChannel FilterIdToTab in:%s err:%v", in.String(), err)
		}
	}

	// 参数处理
	paramData := param.NewParamData(ctx, serviceInfo, in, tabFilter, categoryFilter, confTabs)

	if paramData.PlayItems == nil {
		out.LoadFinish = true
		//log.DebugWithCtx(ctx, "NewPlayItems is nil, in:%+v, serviceInfo:%+v", in, serviceInfo)
		param.FilterReport(ctx, serviceInfo.UserID, in)
		return out, nil
	}
	defer func() {
		if paramData.CostReporter != nil && len(paramData.CostReporter.TimeCostList) > 0 {
			go paramData.CostReporter.ReportByHistogram(grpc.NewContextWithInfo(ctx))
		}
	}()

	satoshiRsp, err := s.satoshiClient.Supervise(ctx, &satoshi.SuperviseReq{
		SuperviseType: "home_page_channel_list",
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListTopicChannel in:%s satoshiClient.Supervise err:%v", in.String(), err)
	} else if satoshiRsp.GetForbiddenMsg().GetForbiddenCode() < 0 {
		//监管需求，屏蔽房间列表
		out.LoadFinish = true
		//log.DebugWithCtx(ctx, "ListTopicChannel 开启监管 marketid %v, uid %v", serviceInfo.MarketID, serviceInfo.UserID)
		paramData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_MarketId_Filter)
		blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, paramData.ReportData)
		return out, nil
	}

	//if conf.ChannelPlayLogicConfig != nil && conf.PublicSwitchConfig.GetTopicChannelHomePageSwitch()[serviceInfo.MarketID] {
	//	//监管需求，屏蔽房间列表
	//	out.LoadFinish = true
	//	//log.DebugWithCtx(ctx, "ListTopicChannel 开启监管 marketid %v, uid %v", serviceInfo.MarketID, serviceInfo.UserID)
	//	paramData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_MarketId_Filter)
	//	blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, paramData.ReportData)
	//	return out, nil
	//}

	// 房间列表处理
	var revenueItemMap map[uint32]*channel_play.TopicChannelItem
	var rcmdSourceData *data.RcmdDataResp
	fillChannelInfoTask := []utils.CoroutineTask{
		// 推荐房间列表
		func(ctx *context.Context, bCancelLog bool) (error, bool) {

			rcmdSourceData, err = s.ChannelListMgr.ListTopicChannel(*ctx, paramData)
			if err != nil {
				log.ErrorWithCtx(*ctx, "ListTopicChannel service info:%s, err %v", paramData.ServiceInfo.String(), err)
			} else {
				paramData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Suc)
				blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(*ctx, paramData.ReportData)
			}
			if rcmdSourceData != nil {
				out.ReportData = rcmdSourceData.ReportData
				out.LoadFinish = rcmdSourceData.LoadFinish
				out.IsRecommendDoudi = rcmdSourceData.IsRecommendDoudi
				if rcmdSourceData.IsRefresh {
					out.BaseResp = new(app.BaseResp)
					out.BaseResp.SuccessMsg = "刷新成功~正在努力寻找新房间"
				}
				if rcmdSourceData.GenResp != nil {
					//用户偏好
					out.PrefGames = convert.PrefGamesRecommend2Pb(*ctx, rcmdSourceData.GenResp.GetPrefGames(), cache.GetTabInfoCache().GetTabIdCache())
					out.PrefGamePos = rcmdSourceData.GenResp.GetInsertPos()
				}
			}
			return nil, false
		},
		//强插营收房
		func(ctx *context.Context, bCancelLog bool) (error, bool) {
			revTaskStartTime := time.Now()
			if !utils.IsHigherVersion(paramData.ServiceInfo.ClientVersion, "6.51.3") {
				return nil, false
			}
			tabPositionMap := conf.ChannelPlayLogicConfig.GetRevenueRoomTabInsertPostionMap()
			tmpTabId := paramData.PlayItems.TabId
			if !paramData.PlayItems.IsMusicTab && paramData.In.GetChannelListEnterSource() == channel_play.ChannelListEnterSource_MixHomePageSource &&
				paramData.ServiceInfo.MarketID == uint32(app.BaseReq_MARKET_NONE) && paramData.IsRefresh && !paramData.IsSelect &&
				len(tabPositionMap) != 0 && tmpTabId != 0 {
				if positonList, ok := tabPositionMap[tmpTabId]; ok && len(positonList) > 0 {
					revCtx, revCancel := grpc.NewContextWithInfoTimeout(*ctx, 500*time.Millisecond)
					defer revCancel()
					revenueItemMap, err = s.fillRevenueRoomlItemInfo(revCtx, paramData.ServiceInfo.UserID, tmpTabId, positonList)
					if err != nil {
						log.ErrorWithCtx(*ctx, "fillRevenueRoomlItemInfo fail, err:%v, param:%+v", err, paramData)
					}
				}

				log.InfoWithCtx(*ctx, "RevenueRoom info, revenueItemMap:%v, uid:%d, isrefresh:%t, isSelect:%t, tabPositionMap:%v,"+
					" tabids:%v costTime:%d ms", revenueItemMap, paramData.ServiceInfo.UserID, paramData.IsRefresh, paramData.IsSelect,
					conf.ChannelPlayLogicConfig.GetRevenueRoomTabInsertPostionMap(), paramData.PlayItems.TabIds,
					time.Since(revTaskStartTime).Milliseconds())
			}
			return nil, false
		},
	}
	newCtx, cancel := context.WithTimeout(ctx, time.Duration(s.channelListCoroutineTimeOut)*time.Millisecond)
	defer cancel()
	err, bRet := utils.CoroutineDoWithSingleResult("", newCtx, uint32(len(fillChannelInfoTask)), fillChannelInfoTask...)
	if err != nil {
		log.ErrorWithCtx(ctx, "CoroutineDoWithSingleResult len(fillChannelInfoTask):%d, err:%v", len(fillChannelInfoTask), err)
		if bRet {
			return out, err
		}
	}

	out.Items = s.reSortChannelList(rcmdSourceData, revenueItemMap)

	// 加上耗时告警
	if time.Since(startTime).Milliseconds() > int64(time.Millisecond*1500) {
		sendErr := monkey_sender.GetNumMsgSenderByChatId(conf.PublicSwitchConfig.GetFallBackChatId(), conf.PublicSwitchConfig.GetFallBackWarnDuration()).
			SendMsg("开黑房间列表", fmt.Sprintf("推荐耗时超过1500ms，uid:%d, in:%s", serviceInfo.UserID, in.String()))
		if sendErr != nil {
			log.ErrorWithCtx(ctx, "ChannelListMgr.ListTopicChannel monkey_sender SendMsg err:%v", sendErr)
		}
	}
	log.InfoWithCtx(ctx, "ListTopicChannel uid %v, req:%s len(out):%d, len(revenueItemMap):%d costReporter:%s",
		serviceInfo.UserID, in.String(), len(out.GetItems()), len(revenueItemMap), paramData.CostReporter.GetString())
	return out, err

}

func (s *ChannelPlayLogic) reSortChannelList(channelData *data.RcmdDataResp, revenueRoomItems map[uint32]*channel_play.TopicChannelItem) (
	rspItems []*channel_play.TopicChannelItem) {
	var cids []uint32
	rspItems = make([]*channel_play.TopicChannelItem, 0, len(cids))
	var allChannelItems map[uint32]*channel_play.TopicChannelItem
	if channelData != nil {
		cids = channelData.GetOriginChannelIds()
		allChannelItems = channelData.AllChannelItems
	} else {
		return rspItems
	}

	for _, cid := range cids {
		if revenueRoomItems != nil {
			rspLen := len(rspItems)
			if revenueItem, ok := revenueRoomItems[uint32(rspLen)]; ok {
				rspItems = append(rspItems, revenueItem)
			}

		}
		if v, ok := allChannelItems[cid]; ok {
			rspItems = append(rspItems, v)
			continue
		}
	}
	return rspItems
}

func (s *ChannelPlayLogic) fillRevenueRoomlItemInfo(ctx context.Context, uid, tabId uint32, insertPos []uint32) (
	itemMap map[uint32]*channel_play.TopicChannelItem, err error) {
	itemMap = make(map[uint32]*channel_play.TopicChannelItem)
	posLen := len(insertPos)
	if posLen == 0 {
		return itemMap, nil
	}
	req := &revenue_recommend_svr.GetRevenueRecListReq{
		ReqSource: uint32(revenue_recommend_svr.GetRevenueRecListReq_Req_Source_Ugc_King),
		Uid:       uid,
		Count:     uint32(posLen),
		TabId:     tabId,
	}
	revenueRsp, err := s.revenueClient.GetRevenueRecList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "fillRevenueRoomlItemInfo revenueClient.GetRevenueRecList err: %v, req:%s", err, req.String())
		return itemMap, err
	}
	//log.DebugWithCtx(ctx, "revenueClient.GetRevenueRecList: req:%s, rsp:%s", req.String(), revenueRsp.String())
	for i, info := range revenueRsp.GetInfoList() {
		if i < posLen && info.GetIsShow() {
			item := &channel_play.TopicChannelItem{
				Source: channel_play.GenViewSource_FROM_REVENUE,
				TopicChannelView: &channel_play.TopicChannelView{
					ChannelView: &channel_play.TopicChannelView_ViewMarshal{
						ViewMarshal: &channel_play.ChannelViewMarshal{
							MarshalType: channel_play.MarshalType(info.GetMarshalType()),
							Content:     info.GetData(),
						},
					},
				},
			}
			itemMap[insertPos[i]] = item
		} else {
			log.InfoWithCtx(ctx, "fillRevenueRoomlItemInfo revenueClient.GetRevenueRecList item not show: %v", info)
		}

	}
	return
}

func (s *ChannelPlayLogic) GetNewQuickMatchConfig(ctx context.Context, in *channel_play.GetNewQuickMatchConfigReq) (out *channel_play.GetNewQuickMatchConfigResp, err error) {
	out = &channel_play.GetNewQuickMatchConfigResp{}

	configInfo, err := s.ChannelListMgr.GetNewQuickMatchConfig(ctx, in.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewQuickMatchConfig err: %v, in: %+v", err, in)
		return out, err
	}
	out.Config = configInfo
	log.InfoWithCtx(ctx, "GetNewQuickMatchConfig in: %+v, out: %+v", in, out)
	return out, err
}
