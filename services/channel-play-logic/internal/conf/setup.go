package conf

import (
	"context"
	"os/user"
	"path"
	"runtime"

	"golang.52tt.com/pkg/log"
)

const (
	Debug      = "debug"
	Testing    = "testing"
	Staging    = "staging"
	Production = "production"
)

var (
	MiniGameConfig         *MiniGameConfigLoader
	ThirdPartyGameConf     *ThirdPartyGameConfigLoader
	ChannelPlayLogicConfig *ChannelPlayLogicConfigLoader
	HomePageConfigs        *HomePageConfigLoader
	PublicSwitchConfig     *PublicSwitchConfigLoader
	GuideCfg               *GuideConfigLoader
	PCHomePageConfigs      *PCHomePageConfigLoader
	Environment            = Testing
)

func ttConfPath(name string) string {
	var ttConfDir string
	if runtime.GOOS == "windows" {
		if u, err := user.Current(); err == nil {
			ttConfDir = path.Join(u.HomeDir, "/data/oss/conf-center/tt/")
		}
	} else {
		ttConfDir = "/data/oss/conf-center/tt/"
	}
	return path.Join(ttConfDir, name)
}

func Parse(ctx context.Context, environment string) (err error) {
	if ChannelPlayLogicConfig, err = NewChannelPlayLogicConfigLoader(ttConfPath("channel-play-logic.json")); err != nil {
		log.ErrorWithCtx(ctx, "NewChannelPlayLogicConfigLoader err %v")
		return err
	}

	if ThirdPartyGameConf, err = NewThirdPartyGameConfigLoader(ttConfPath("third_party_game_config.json")); err != nil {
		log.ErrorWithCtx(ctx, "NewThirdPartyGameConfigLoader err %v")
		return err
	}

	if MiniGameConfig, err = NewMiniGameConfigLoader(ttConfPath("channel-play-minigame.json")); err != nil {
		log.ErrorWithCtx(ctx, "NewMiniGameConfigLoader err %v")
		return err
	}

	if HomePageConfigs, err = NewHomePageConfigLoader(ttConfPath("home_page_head_configs.json")); err != nil {
		log.ErrorWithCtx(ctx, "NewHomePageConfigLoader err %v")
		return err
	}

	if PublicSwitchConfig, err = NewPublicSwitchConfigLoader(ttConfPath("topic-channel-switch-option.json")); err != nil {
		log.ErrorWithCtx(ctx, "NewPublicSwitchConfigLoader err %v")
		return err
	}

	if GuideCfg, err = NewGuideConfigLoader(ttConfPath("channel_play_guide_config.json")); err != nil {
		log.ErrorWithCtx(ctx, "NewGuideConfigLoader err %v")
		return err
	}

	if PCHomePageConfigs, err = NewPCHomePageConfigLoader(ttConfPath("pc_home_page_head_configs.json")); err != nil {
		log.ErrorWithCtx(ctx, "NewPCHomePageConfigLoader err %v")
		return err
	}


	Environment = environment
	log.InfoWithCtx(ctx, "ChannelPlayLogicConfig :%+v", ChannelPlayLogicConfig.LoadConfig())
	log.InfoWithCtx(ctx, "ThirdPartyGameConf :%+v", ThirdPartyGameConf.LoadConfig())
	log.InfoWithCtx(ctx, "MiniGameConfig :%+v", MiniGameConfig.loadConfig())
	log.InfoWithCtx(ctx, "HomePageConfigs :%+v", HomePageConfigs.loadConfig())
	log.InfoWithCtx(ctx, "PublicSwitchConfig :%+v", PublicSwitchConfig.loadConfig())
	log.InfoWithCtx(ctx, "GuideCfg :%+v", GuideCfg.loadConfig())
	log.InfoWithCtx(ctx, "PCHomePageConfigs :%+v", PCHomePageConfigs.loadConfig())

	return nil
}

func GetEnv() string {
	return Environment
}
