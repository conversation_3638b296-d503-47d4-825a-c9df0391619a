package conf

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
	"time"
)

const (
	defaultMuseChatTabId   = 564
	defaultFastPcChatTabId = 609
)

type PublicSwitchConfigLoader struct {
	configLoader *pkg.ConfigLoader
}

func NewPublicSwitchConfigLoader(filename string) (loader *PublicSwitchConfigLoader, err error) {
	loader = &PublicSwitchConfigLoader{}

	loader.configLoader, err = pkg.NewConfigLoaderV2(context.Background(), filename, &publicSwitchConfig{}, false, 30*time.Second)
	if err != nil {
		log.Errorf("NewPublicSwitchConfigLoader NewConfigLoaderV2 filename(%s) err: %v", filename, err)
		return nil, err
	}

	return
}

func (loader *PublicSwitchConfigLoader) loadConfig() (cfg *publicSwitchConfig) {
	cfg = &publicSwitchConfig{}

	if loader.configLoader == nil {
		log.Warnf("PublicSwitchConfigLoader LoadConfig configLoader nil")
		return
	}

	e := loader.configLoader.Get()
	if e == nil {
		log.Warnf("PublicSwitchConfigLoader LoadConfig Get nil")
		return
	}

	var ok bool
	cfg, ok = e.(*publicSwitchConfig)
	if !ok {
		log.Warnf("PublicSwitchConfigLoader LoadConfig publicSwitchConfig nil")
		return
	}

	return
}

func (loader *PublicSwitchConfigLoader) GetTimeoutUidList() []uint32 {
	return loader.loadConfig().TimeoutUidList
}

func (loader *PublicSwitchConfigLoader) GetRecommendNilUidList() []uint32 {
	return loader.loadConfig().RecommendNilUidList
}

func (loader *PublicSwitchConfigLoader) GetCheckPunish() uint32 {
	return loader.loadConfig().CheckPunish
}

func (loader *PublicSwitchConfigLoader) GetRegisterLimitTime() uint32 {
	return loader.loadConfig().RegisterLimitTime
}

func (loader *PublicSwitchConfigLoader) GetUserLevelLimit() uint32 {
	return loader.loadConfig().UserLevelLimit
}

func (loader *PublicSwitchConfigLoader) IsKTVTab(tabId uint32) bool {
	return tabId == loader.loadConfig().KTVTabId
}

func (loader *PublicSwitchConfigLoader) IsSensitiveProvince(province string) bool {
	sensitiveArea := loader.loadConfig().SensitiveArea
	if len(sensitiveArea.Province) == 0 {
		return false
	}
	for _, v := range sensitiveArea.Province {
		if v == province {
			return true
		}
	}
	return false
}

func (loader *PublicSwitchConfigLoader) IsSensitiveCity(city string) bool {
	sensitiveArea := loader.loadConfig().SensitiveArea
	if len(sensitiveArea.City) == 0 {
		return false
	}
	for _, v := range sensitiveArea.City {
		if v == city {
			return true
		}
	}
	return false
}

func (loader *PublicSwitchConfigLoader) IsRealName(realNameStatus int32) bool {
	realNameStandard := loader.loadConfig().RealNameStandard
	if len(realNameStandard) == 0 {
		log.Debugf("PublicSwitchConfigLoader RealNameStandard not found")
		return false
	}
	for _, v := range realNameStandard {
		if realNameStatus == v {
			return true
		}
	}
	return false
}

func (loader *PublicSwitchConfigLoader) GetRealNameStandardStatus() (realNameStandard []int32) {
	realNameStandard = loader.loadConfig().RealNameStandard
	return realNameStandard
}

func (loader *PublicSwitchConfigLoader) IsHiddenGeoOption() bool {
	return loader.loadConfig().IsHiddenGeoOption
}

func (loader *PublicSwitchConfigLoader) IsHiddenGeoOptTest() bool {
	return loader.loadConfig().IsHiddenGeoOptTest

}

func (loader *PublicSwitchConfigLoader) GetFreshOption() bool {
	return loader.loadConfig().FreshOption
}

func (loader *PublicSwitchConfigLoader) GetTopicChannelHomePageSwitch() map[uint32]bool {
	return loader.loadConfig().TopicChannelHomePageSwitch
}

func (loader *PublicSwitchConfigLoader) GetDefaultTab() uint32 {
	return loader.loadConfig().DefaultTab
}

func (loader *PublicSwitchConfigLoader) GetChatTabId() uint32 {
	return loader.loadConfig().ChatTabId
}

func (loader *PublicSwitchConfigLoader) GetFallBackChatId() string {
	chatId := loader.loadConfig().FallBackChatId
	if len(chatId) == 0 {
		chatId = "oc_95df9effdb0b94012f566df379c53b58"
	}
	return chatId
}

func (loader *PublicSwitchConfigLoader) GetFallBackWarnDuration() int64 {
	duration := loader.loadConfig().FallBackWarnDuration
	if duration == 0 {
		duration = 60
	}
	return duration
}

func (loader *PublicSwitchConfigLoader) GetChannelListNoFilterTabSwitch(tabId uint32) bool {
	switchMap := loader.loadConfig().ChannelListNoFilterTabSwitch
	if switchMap != nil {
		return switchMap[tabId]
	}
	return false
}

// publicSwitchConfig 核心服务公共配置
type publicSwitchConfig struct {
	TimeoutUidList             []uint32        `json:"timeout_uid_list"` //超时名单
	CheckPunish                uint32          `json:"check_punish"`
	RegisterLimitTime          uint32          `json:"register_limit_time"`
	UserLevelLimit             uint32          `json:"user_level_limit"`
	KTVTabId                   uint32          `json:"ktv_tab_id"`
	SensitiveArea              AreaInfo        `json:"sensitive_area"`
	RealNameStandard           []int32         `json:"real_name_standard"`
	IsHiddenGeoOption          bool            `json:"is_hidden_geo_option"`
	IsHiddenGeoOptTest         bool            `json:"is_hidden_geo_opt_test"`
	FreshOption                bool            `json:"fresh_option"`
	TopicChannelHomePageSwitch map[uint32]bool `json:"topic_channel_home_page_switch"` //2022/12/5监管需求，快速匹配，房间列表开关
	DefaultTab                 uint32          `json:"default_tab"`                    // 房间信息未选择时，默认tab_id，之前旧字段，没映射
	ChatTabId                  uint32          `json:"chat_tab_id"`                    // 扩列聊天tab_id，之前旧字段，没映射
	FallBackChatId             string          `json:"fall_back_chat_id"`              //兜底告警群id
	FallBackWarnDuration       int64           `json:"fall_back_warn_duration"`        //兜底告警周期，单位s
	//特殊玩法（新旧扩列）
	ChannelListNoFilterTabSwitch map[uint32]bool `json:"channel_list_no_filter_tab_switch"`
	RecommendNilUidList          []uint32        `json:"recommend_nil_uid_list"` //返回空名单

	IsChannelPushStat bool     `json:"is_channenl_push_stat"` //房间下发统计写kfk
	IsBlReportClose   bool     `json:"is_bl_report_close"`
	DoudiTabList      []uint32 `json:"doudi_tab_list"`  //tab列表为空兜底
	DoudiNilRatio     uint32   `json:"doudi_nil_ratio"` //空比例多少时兜底

	MuseChatTabId                uint32 `json:"muse_chat_tab_id"`
	FastPcChatTabId              uint32 `json:"fast_pc_chat_tab_id"`
	OpenFastPcChatTabDoubleWrite bool   `json:"open_fast_pc_chat_tab_double_write"`
}

func (cfg *publicSwitchConfig) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, cfg)
	if err != nil {
		log.Errorf("publicSwitchConfig UnmarshalBinary data(%s) err: %v", data, err)
		return err
	}

	return nil
}

func (loader *PublicSwitchConfigLoader) GetIsChannelStatPush() bool {
	config := loader.loadConfig()
	if config != nil {
		return config.IsChannelPushStat
	}
	return false
}

func (loader *PublicSwitchConfigLoader) GetIsBlReportOpen() bool {
	config := loader.loadConfig()
	if config != nil {
		return !config.IsBlReportClose
	}
	return false
}

func (loader *PublicSwitchConfigLoader) GetDoudiTabMap() map[uint32]bool {
	doudiTabIdMap := make(map[uint32]bool)
	config := loader.loadConfig()
	if config != nil {
		if len(config.DoudiTabList) != 0 {
			for _, tabId := range config.DoudiTabList {
				doudiTabIdMap[tabId] = true
			}
		}
		return doudiTabIdMap
	}
	return doudiTabIdMap
}

func (loader *PublicSwitchConfigLoader) GetDoudiNilRatio() uint32 {
	config := loader.loadConfig()
	if config != nil {
		return config.DoudiNilRatio
	}
	return 0
}

func (loader *PublicSwitchConfigLoader) GetFastPcChatTabId() uint32 {
	config := loader.loadConfig()
	if config != nil {
		return config.FastPcChatTabId
	}
	return defaultFastPcChatTabId
}

func (loader *PublicSwitchConfigLoader) GetMuseChatTabId() uint32 {
	config := loader.loadConfig()
	if config != nil {
		return config.MuseChatTabId
	}
	return defaultMuseChatTabId
}

func (loader *PublicSwitchConfigLoader) IsOpenFastPcChatTabDoubleWrite() bool {
	config := loader.loadConfig()
	if config == nil {
		return true
	}
	return config.OpenFastPcChatTabDoubleWrite
}
