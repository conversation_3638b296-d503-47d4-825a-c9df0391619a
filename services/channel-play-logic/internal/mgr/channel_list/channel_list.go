package channel_list

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata"
	channel_play_tab "golang.52tt.com/clients/channel-play-tab"
	gangup_channel "golang.52tt.com/clients/gangup-channel"
	tcChannel "golang.52tt.com/clients/topic-channel/channel"
	recommendation_gen "golang.52tt.com/clients/topic-channel/recommendation-gen"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/speedlimit"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	channel_play_middle "golang.52tt.com/protocol/services/channel-play-middle"
	channelPlayTabPb "golang.52tt.com/protocol/services/channel-play-tab"
	gangup_channel_pb "golang.52tt.com/protocol/services/gangup-channel"
	"golang.52tt.com/protocol/services/muse-social-community"
	"golang.52tt.com/protocol/services/music-nest"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/data"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/filter"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/param"
	"google.golang.org/protobuf/runtime/protoimpl"
	"time"
)

type ChannelListMgr struct {
	TCChannelClient           tcChannel.IClient
	ChannelClient             channel_go.ChannelGoClient
	GenRecommendationClient   recommendation_gen.IClient
	SupervisorInst            *supervision.Supervisory
	ChannelPlayTabClient      *channel_play_tab.Client
	GangupChannelClient       *gangup_channel.Client
	errOverInst               *speedlimit.ErrorOver
	channelPlayMiddleClient   channel_play_middle.ChannelPlayMiddleClient
	musicTopicClient          music_topic_channel.MusicChannelClient
	MusicNestClient           music_nest.MusicNestClient
	MuseSocialCommunityClient muse_social_community.MuseSocialCommunityClient
	channelListRcmdTimeOut    int
}

type ChannelListMgrParam struct {
	TCChannelClient           tcChannel.IClient
	ChannelClient             channel_go.ChannelGoClient
	GenRecommendationClient   recommendation_gen.IClient
	SupervisorInst            *supervision.Supervisory
	ChannelPlayTabClient      *channel_play_tab.Client
	GangupChannelClient       *gangup_channel.Client
	ErrOverInst               *speedlimit.ErrorOver
	ChannelPlayMiddleClient   channel_play_middle.ChannelPlayMiddleClient
	MusicTopicClient          music_topic_channel.MusicChannelClient
	MusicNestClient           music_nest.MusicNestClient
	MuseSocialCommunityClient muse_social_community.MuseSocialCommunityClient
	ChannelListRcmdTimeOut    int
}

type InsertItemType struct {
	item        *channel_play.TopicChannelItem
	insertIndex int
}

func NewChannelListMgr(mgrParams *ChannelListMgrParam) (listMgr *ChannelListMgr, err error) {
	listMgr = &ChannelListMgr{
		TCChannelClient:           mgrParams.TCChannelClient,
		ChannelClient:             mgrParams.ChannelClient,
		GenRecommendationClient:   mgrParams.GenRecommendationClient,
		SupervisorInst:            mgrParams.SupervisorInst,
		ChannelPlayTabClient:      mgrParams.ChannelPlayTabClient,
		GangupChannelClient:       mgrParams.GangupChannelClient,
		errOverInst:               mgrParams.ErrOverInst,
		channelPlayMiddleClient:   mgrParams.ChannelPlayMiddleClient,
		musicTopicClient:          mgrParams.MusicTopicClient,
		MusicNestClient:           mgrParams.MusicNestClient,
		MuseSocialCommunityClient: mgrParams.MuseSocialCommunityClient,
		channelListRcmdTimeOut:    mgrParams.ChannelListRcmdTimeOut,
	}
	return listMgr, nil
}

func (m *ChannelListMgr) ListTopicChannel(ctx context.Context, param *param.ParamData) (channelData *data.RcmdDataResp, err error) {
	userId := param.ServiceInfo.UserID
	var fallBackHandle data.FallBack
	var sourceData data.SourceData
	if param.PlayItems.IsMusicTab {
		fallBackHandle = data.NewMusicFallBack(m.musicTopicClient, param)
		sourceData = data.NewMusicRcmdData(param, fallBackHandle, &data.ClientParams{
			GenRecommendationClient:   m.GenRecommendationClient,
			ChannelClient:             m.ChannelClient,
			TCChannelClient:           m.TCChannelClient,
			MusicNestClient:           m.MusicNestClient,
			MuseSocialCommunityClient: m.MuseSocialCommunityClient,
			MusicTopicChannelClient:   m.musicTopicClient,
		}, m.channelListRcmdTimeOut)
	} else {
		fallBackHandle = data.NewDefaultFallBack(m.TCChannelClient, param.In, param.Options, userId)
		sourceData = data.NewRcmdData(param, fallBackHandle, &data.ClientParams{
			GenRecommendationClient: m.GenRecommendationClient,
			ChannelClient:           m.ChannelClient,
			TCChannelClient:         m.TCChannelClient,
		}, m.errOverInst, m.channelListRcmdTimeOut)
	}

	// 获取数据(包括兜底数据)
	channelData, err = sourceData.GetChannelData(ctx)
	//log.DebugWithCtx(ctx, "ListTopicChannel GetData, channelData:%+v", channelData)
	if err != nil {
		return channelData, err
	}

	// 上报筛选记录
	if !param.PlayItems.IsMusicTab && len(channelData.GenResp.GetLabels()) != 0 && param.IsRefresh {
		reportCtx := metadata.NewContext(ctx)
		go m.HandleChannelListTagRecord(reportCtx, channelData.GenResp.GetLabels(), param)
	} else {
		if len(param.GenOption) != 0 && len(param.In.GetLabels()) != 0 {
			log.WarnWithCtx(ctx, "label err: nil, labels:%+v, param.IsRefresh:%t, req option:%+v, label:%v",
				channelData.GenResp.GetLabels(), param.IsRefresh, param.GenOption, param.In.GetLabels())
		}
	}

	if len(channelData.GetOriginChannelIds()) == 0 {
		log.InfoWithCtx(ctx, "ListTopicChannel GetData, no data param:%+v", param)
		//out.LoadFinish = true 里面判断了
		return channelData, nil
	}

	var allChannelItems map[uint32]*channel_play.TopicChannelItem

	if len(channelData.GetOriginChannelIds()) > 0 {
		// 组装ugc返回信息
		allChannelItems, err = m.fillAllChannelRspItemInfo(ctx, param, channelData)
		if err != nil {
			log.ErrorWithCtx(ctx, "FillUgcRspItemInfo fail, err:%v, param:%+v, channelData:%v", err, param, channelData)
			return channelData, err

		}
	} else {
		log.WarnWithCtx(ctx, "ListTopicChannel ugcChannelIds is empty, param:%+v, channelData:%+v", param, channelData)
	}

	channelData.AllChannelItems = allChannelItems

	// 源数据有，输出没有，加上告警日志
	outLen := len(channelData.AllChannelItems)
	if len(channelData.GetOriginChannelIds()) > 0 && outLen == 0 {
		log.ErrorWithCtx(ctx, "UserLine Err: ListTopicChannel data response error, param:%+v, channelData:%+v", param, channelData)
	}

	log.InfoWithCtx(ctx, "ListTopicChannel out, reqtab:%s, uid(%d),  OriginChannelIdsLen(%d), outLen(%d)",
		param.PlayItems.GetReportDataAllStr(ctx), userId, len(channelData.GetOriginChannelIds()), outLen)
	return channelData, nil
}

func (m *ChannelListMgr) fillAllChannelRspItemInfo(ctx context.Context, param *param.ParamData, dataInfo *data.RcmdDataResp) (allItemsMap map[uint32]*channel_play.TopicChannelItem, err error) {
	allItemsMap = make(map[uint32]*channel_play.TopicChannelItem, len(dataInfo.GetOriginChannelIds()))

	viewReq := &channel_play_middle.BatGetGameChannelViewMapReq{
		ReqSource:  uint32(channel_play_middle.ReqSource_REQ_SOURCE_LIST),
		ChannelIds: dataInfo.GetOriginChannelIds(),

		ListParams: &channel_play_middle.SourceListParams{
			ChannelListEnterSource: uint32(param.In.GetChannelListEnterSource()),
			ListStyleType:          uint32(param.In.GetListStyleType()),
			ChannelPackageId:       param.In.GetChannelPackageId(),
		},
	}
	if dataInfo.RcmdInfo != nil {
		viewReq.RecommendInfo = &channel_play_middle.RcmdInfo{
			SelfLocation:   dataInfo.RcmdInfo.RcmdLoc,
			ChannelInfoMap: dataInfo.RcmdInfo.RcmdChannelInfoMap,
		}
	}
	if param.ReportData != nil {
		viewReq.ListParams.TabId = param.ReportData.TabId
		viewReq.ListParams.TabIdsStr = param.ReportData.TabIds
		viewReq.ListParams.CategoryIdsStr = param.ReportData.CategoryIds
	}

	genViewSource := make([]uint32, 0, len(param.In.GenViewSource))
	for _, v := range param.In.GenViewSource {
		genViewSource = append(genViewSource, uint32(v))
	}
	viewReq.ListParams.GenViewSource = genViewSource
	viewResp, err := m.channelPlayMiddleClient.BatGetGameChannelViewMap(ctx, viewReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetGameChannelViewMap in:%s, viewReq:%s, err:%+v", param.In.String(), viewReq.String(), err)
		return nil, err
	}
	channelFilter := filter.NewChannelFilter(param)
	for cid, anyInfo := range viewResp.GetChannelViewMap() {
		item := &channel_play.TopicChannelItem{}
		err = anyInfo.UnmarshalTo(protoimpl.X.ProtoMessageV2Of(item))
		if err != nil {
			log.ErrorWithCtx(ctx, "fillAllChannelRspItemInfo msg：%s umMarshal err:%+v", anyInfo.String(), err)
			continue
		}
		switch item.GetTopicChannelView().GetChannelView().(type) {
		case *channel_play.TopicChannelView_ViewMarshal, *channel_play.TopicChannelView_ViewNewMusic:
		default:
			ret := channelFilter.DoFilter(ctx, item.GetTabId())
			if ret != 0 {
				log.ErrorWithCtx(ctx, "fillAllChannelRspItemInfo DoFilter ret:%d item:%s ", ret, item.String())
				param.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Req_TabId_Not_Match)
				blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, param.ReportData)
				continue
			}
		}

		allItemsMap[cid] = item
	}
	return allItemsMap, nil
}

func (m *ChannelListMgr) fillOtherChannelItemInfo(ctx context.Context, otherChannelIds []uint32) (items map[uint32]*channel_play.TopicChannelItem, err error) {
	items = make(map[uint32]*channel_play.TopicChannelItem, 0)
	marshalResp, err := m.musicTopicClient.ListMusicChannelViewPbs(ctx, &music_topic_channel.ListMusicChannelViewPbsReq{
		ChannelIds: otherChannelIds,
	})
	//log.DebugWithCtx(ctx, "ListTopicChannel fillOtherChannelItemInfo, ListMusicChannelViewPbs, err:%v, otherChannelIds:%v, ChannelViews:%+v",
	//	otherChannelIds, marshalResp.GetChannelViews())

	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelFollowCommonHandle ListMusicChannelViewPbs, marshalCids:%v, err %v", otherChannelIds, err)
		return
	}
	for k, v := range marshalResp.GetChannelViews() {
		item := &channel_play.TopicChannelItem{
			ChannelId: k,
			Source:    channel_play.GenViewSource_FROM_MUSIC,
			TopicChannelView: &channel_play.TopicChannelView{
				ChannelView: &channel_play.TopicChannelView_ViewMarshal{
					ViewMarshal: &channel_play.ChannelViewMarshal{
						MarshalType: channel_play.MarshalType_PROTO_TYPE,
						Content:     v,
					},
				},
			},
		}
		items[k] = item
	}
	return
}

func (m *ChannelListMgr) reSortChannelList(cids []uint32, ugcChannelItems, otherChannelItems map[uint32]*channel_play.TopicChannelItem, revenueRoomItems map[uint32]*channel_play.TopicChannelItem) (rspItems []*channel_play.TopicChannelItem) {
	rspItems = make([]*channel_play.TopicChannelItem, 0, len(cids))
	for i, cid := range cids {
		if revenueRoomItems != nil {
			if revenueItem, ok := revenueRoomItems[uint32(i)]; ok {
				rspItems = append(rspItems, revenueItem)
			}
		}
		if v, ok := ugcChannelItems[cid]; ok {
			rspItems = append(rspItems, v)
			continue
		}
		if v, ok := otherChannelItems[cid]; ok {
			rspItems = append(rspItems, v)
		}
	}
	return
}

// GetNewQuickMatchConfig 获取新版本快速匹配入口配置
func (m *ChannelListMgr) GetNewQuickMatchConfig(ctx context.Context, tabId uint32) (*channel_play.NewQuickMatchConfig, error) {

	resp, err := m.ChannelPlayTabClient.GetNewQuickMatchConfig(ctx, &channelPlayTabPb.GetNewQuickMatchConfigReq{
		TabId: tabId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewQuickMatchConfig ChannelPlayTabClient.GetNewQuickMatchConfig tabId:%d err: %v", tabId, err)
		return nil, err
	}
	if resp.GetConfig() == nil {
		return nil, nil
	}
	return &channel_play.NewQuickMatchConfig{
		TabId:      resp.GetConfig().GetTabId(),
		TabName:    resp.GetConfig().GetTabName(),
		Title:      resp.GetConfig().GetTitle(),
		ButtonText: resp.GetConfig().GetButtonText(),
		Position:   resp.GetConfig().GetPosition(),
	}, nil
}

func (m *ChannelListMgr) HandleChannelListTagRecord(ctx context.Context, gameLabels []*rcmd_channel_label.GameLabel, paramInst *param.ParamData) {
	//log.DebugWithCtx(ctx, "HandleChannelListTagRecord labels:%+v paramInst:%+v", gameLabels, paramInst)
	ctx, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()
	defer func() {
		err := recover()
		if err != nil {
			log.ErrorWithCtx(ctx, "handleChannelListTagRecord panic, err:%v ", err)
		}
	}()
	in := paramInst.In
	tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(in.GetTabId())
	if topic_channel.CategoryType(tabInfo.GetCategoryMapping()) != topic_channel.CategoryType_Gangup_type ||
		(len(in.GetLabels()) == 0 && len(paramInst.GenOption) == 0) {
		log.InfoWithCtx(ctx, "HandleChannelListTagRecord tabId:%d, in:%+v", in.GetTabId(), in)
		return
	}

	records := make([]*gangup_channel_pb.UpdateChannelListFilterRecordReq_LabelRecord, 0, len(gameLabels))
	for _, label := range gameLabels {
		record := &gangup_channel_pb.UpdateChannelListFilterRecordReq_LabelRecord{
			DisplayTitle:    label.GetDisplayName(),
			RelatedLabelVal: label.GetVal(),
		}
		records = append(records, record)
		//log.InfoWithCtx(ctx, "HandleChannelListTagRecord record:%+v paramInst:%+v", record, paramInst)
	}
	if len(records) == 0 {
		return
	}

	recordReq := &gangup_channel_pb.UpdateChannelListFilterRecordReq{
		Uid:         paramInst.ServiceInfo.UserID,
		TabId:       in.GetTabId(),
		LabelRecord: records,
	}
	_, err := m.GangupChannelClient.UpdateChannelListFilterRecord(ctx, recordReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateChannelListFilterRecord in:%s err:%v", in.String(), err)
	}
	log.InfoWithCtx(ctx, "HandleChannelListTagRecord recordReq:%+v", recordReq)
}

func (m *ChannelListMgr) FilterIdToTab(ctx context.Context, filterId string, filterSubIds []string) ([]*music_topic_channel.FilterIdToConfTabResp_ConfTab, error) {
	filterIds := []string{filterId}
	if len(filterSubIds) > 0 {
		log.DebugWithCtx(ctx, "FilterIdToTab filterSubIds:%v", filterSubIds)
		filterIds = filterSubIds
	}

	rsp, err := m.musicTopicClient.FilterIdToConfTab(ctx, &music_topic_channel.FilterIdToConfTabReq{
		FilterIds: filterIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "FilterIdToTab musicTopicClient.FilterIdToConfTab filterIds:%v err:%v", filterIds, err)
		return nil, err
	}

	return rsp.GetConfTabs(), nil
}
