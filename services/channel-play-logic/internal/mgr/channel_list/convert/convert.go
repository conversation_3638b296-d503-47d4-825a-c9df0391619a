package convert

import (
	"context"
	"golang.52tt.com/pkg/log"
	gaChannelPB "golang.52tt.com/protocol/app/channel"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	genPB "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"math"
)

const (
	InfElemID uint32 = math.MaxUint32
)

func ConvertBlockBlockOption(ctx context.Context, reqOption []*channel_play.FilterBlockOption) (
	options []*channelPB.BlockOption, genOption []*genPB.BlockOption, businessOption map[channel_play.FilterType][]*channel_play.FilterBlockOption) {
	businessOption = make(map[channel_play.FilterType][]*channel_play.FilterBlockOption)
	for _, o := range reqOption {
		if o.FilterType == channel_play.FilterType_Base_Block_Filter {
			if o.ElemId > 0 && o.ElemId != InfElemID {
				options = append(options, &channelPB.BlockOption{BlockId: o.BlockId, ElemId: o.ElemId})
				genOption = append(genOption, &genPB.BlockOption{BlockId: o.BlockId, ElemId: o.ElemId})
			}
		} else if o.FilterType == channel_play.FilterType_Mystery_Game_Condition_Filter {
			if _, ok := businessOption[o.FilterType]; ok {
				businessOption[o.FilterType] = append(businessOption[o.FilterType], o)
			} else {
				businessOption[o.FilterType] = make([]*channel_play.FilterBlockOption, 0)
				businessOption[o.FilterType] = append(businessOption[o.FilterType], o)
			}
		} else {
			log.WarnWithCtx(ctx, "ListTopicChannel convertBlockBlockOption err filterType %v option %v", o.FilterType, o)
			continue
		}
	}

	return
}

// 将gamelabel转化为推荐协议类型
func Convert2RcmdGameLabel(labels []*topic_channel.GameLabel) []*rcmd_channel_label.GameLabel {
	res := make([]*rcmd_channel_label.GameLabel, 0)

	for _, v := range labels {
		res = append(res, &rcmd_channel_label.GameLabel{
			Val:         v.GetValValue(),
			DisplayName: v.GetDisplayName(),
			Type:        rcmd_channel_label.GameLabelType(v.GetType()),
			LabelType:   v.GetLabelType(),
		})
	}
	return res
}

func ConvertChannelEnterSource(source channel_play.ChannelListEnterSource) gaChannelPB.ChannelEnterReq_EChannelEnterSource {
	//请求来源
	var result gaChannelPB.ChannelEnterReq_EChannelEnterSource
	switch source {
	case channel_play.ChannelListEnterSource_MysteryHomePage: // 秘境首页
		result = gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_MYSTERY_CHANNEL_LIST
	case channel_play.ChannelListEnterSource_ScenarioPage: // 剧本页
		result = gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_SCENARIO_DETAIL
	case channel_play.ChannelListEnterSource_MysteryHomeScreen: // 谜境主页半屏
		result = gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_MYSTERY_HOME_SCREEN
	case channel_play.ChannelListEnterSource_MysteryChannelScreen: // 谜境房间半屏
		result = gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_MYSTERY_CHANNEL_SCREEN
	case channel_play.ChannelListEnterSource_MysteryHomeCarousel: // 谜境主页房主头像轮播
		result = gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_MYSTERY_HOME_CAROUSEL
	case channel_play.ChannelListEnterSource_MixHomePageSource:
		result = gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_MIX_HOME_PAGE
	case channel_play.ChannelListEnterSource_GameZoneSource:
		result = gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_GAME_ZONE
	case channel_play.ChannelListEnterSource_MiniGameZoneSource:
		result = gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_MINI_GAME_ARENA
	case channel_play.ChannelListEnterSource_PcFastHomePageSource:
		result = gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_PC_FAST_HOME_PAGE
	case channel_play.ChannelListEnterSource_PcFastHallSource:
		result = gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_PC_FAST_HALL

	default: // 默认开黑首页
		result = gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_FINDPLAY_TAB
	}

	return result
}

func ConvertSexToGenSex(sex channel_play.RcmdSex) uint32 {
	if sex == channel_play.RcmdSex_Female {
		return 1
	} else if sex == channel_play.RcmdSex_Male {
		return 2
	} else {
		return 0
	}
}

func PrefGamesRecommend2Pb(ctx context.Context, prefGames []*genPB.PrefGame, tabMap map[uint32]*tabPB.Tab) []*topic_channel.PrefGame {
	const defaultTabImg = "https://ga-album-cdnqn.52tt.com/prod-yunying/e9d0-17f2f93e79e.png"

	list := make([]*topic_channel.PrefGame, 0)
	for _, prefGame := range prefGames {
		tab := tabMap[prefGame.GetTabId()]
		if tab == nil || tab.GetId() == 0 {
			log.ErrorWithCtx(ctx, "prefGamesRecommend2Pb tab %d not found", prefGame.GetTabId())
			continue
		}

		tabImg := tab.GetSmallCardUrl()
		if tabImg == "" {
			tabImg = defaultTabImg
		}

		pg := &topic_channel.PrefGame{TabId: tab.GetId(), TabName: tab.GetName(), TabImg: tabImg}
		for _, label := range prefGame.GetLabels() {
			l := &topic_channel.PrefGame_Label{Id: label.GetId(), ValValue: label.GetVal()}
			if label.GetType() == genPB.PrefGameLabelType_SYSTEM {
				l.Type = topic_channel.PrefGame_Label_TypeSystem
			} else if label.GetType() == genPB.PrefGameLabelType_CUSTOM {
				l.Type = topic_channel.PrefGame_Label_TypeCustom
			} else {
				log.ErrorWithCtx(ctx, "prefGamesRecommend2Pb invalid label type")
				continue
			}

			pg.Labels = append(pg.Labels, l)
		}

		list = append(list, pg)
	}

	//log.DebugWithCtx(ctx, "prefGamesRecommend2Pb list: %v", list)
	return list
}

func ConvertChannelTopic(confTabs []*music_topic_channel.FilterIdToConfTabResp_ConfTab) []*genPB.ChannelTopic {
	res := make([]*genPB.ChannelTopic, 0, len(confTabs))
	if len(confTabs) == 0 {
		return res
	}
	for _, tab := range confTabs {
		blocks := make([]*genPB.BlockOption, 0, len(tab.Blocks))
		for _, block := range tab.Blocks {
			blocks = append(blocks, &genPB.BlockOption{
				BlockId: block.BlockId,
				ElemId:  block.ElemId,
			})
		}
		res = append(res, &genPB.ChannelTopic{
			TabId:        tab.TabId,
			BlockOptions: blocks,
		})
	}
	return res
}
