package data

import (
	"context"
	"fmt"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/monkey-send-chat/monkey_sender"
	"golang.52tt.com/pkg/speedlimit"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	configserverPB "golang.52tt.com/protocol/services/configserver"
	genPB "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/mgr/channel_list/param"
	"strings"
	"time"
)

const (
	//房间模式blockId，写死
	MysteryBussRoomModeBlock = 1
	//对局状态blockId, 写死
	MysteryBussBGameConditionBlock = 2
	// 半屏也对局状态blockId，写死
	MysteryBussBGameScreenConditionBlock = 3

	//房间模式Business Elem
	MysteryBussRoomModeNoLimit = 1
	MysteryBussRoomModeSingle  = 2
	MysteryBussRoomModeDouble  = 3

	//对局状态Business Elem
	MysteryBussGameConditionNoLimit = 4
	MysteryBussGameConditionWaiting = 5

	// 半屏页对战状态Business Elem
	MysteryBussGameScreenConditionWaiting = 6
	MysteryBussGameScreenConditionStarted = 7
)

var rcmdBussBlockMap = map[uint32]genPB.BusinessBlockEnum{
	MysteryBussRoomModeBlock:             genPB.BusinessBlockEnum_RoomMode,
	MysteryBussBGameConditionBlock:       genPB.BusinessBlockEnum_GameCondition,
	MysteryBussBGameScreenConditionBlock: genPB.BusinessBlockEnum_GameCondition,
}

var rcmdBussElemMap = map[uint32]genPB.BusinessElemEnum{
	MysteryBussRoomModeNoLimit:            genPB.BusinessElemEnum_NoLimit,
	MysteryBussRoomModeSingle:             genPB.BusinessElemEnum_Single,
	MysteryBussRoomModeDouble:             genPB.BusinessElemEnum_Double,
	MysteryBussGameConditionNoLimit:       genPB.BusinessElemEnum_NoLimit,
	MysteryBussGameConditionWaiting:       genPB.BusinessElemEnum_Waiting,
	MysteryBussGameScreenConditionWaiting: genPB.BusinessElemEnum_Waiting,
	MysteryBussGameScreenConditionStarted: genPB.BusinessElemEnum_Started,
}

type RcmdData struct {
	errOverInst *speedlimit.ErrorOver
	rcmdTimeOut int

	*BaseDataHandle
}

type RcmdDataResp struct {
	originChannelIds        []uint32 // 源数据，第一次获取后不可再修改
	Appointment             bool
	IsUgcChannelCntLessFive bool
	GenResp                 *genPB.GetRecommendationListResp
	RcmdInfo                *RcmdInfo
	LoadFinish              bool
	ReportData              *channel_play.ListTopicChannelResp_DataReport
	IsRefresh               bool
	IsCallFallBack          bool
	IsRecommendDoudi        bool

	AllChannelItems map[uint32]*channel_play.TopicChannelItem
}

func (p *RcmdDataResp) GetOriginChannelIds() []uint32 {
	return p.originChannelIds
}

func (p *RcmdDataResp) SetOriginChannelIds(cids []uint32) {
	p.originChannelIds = cids
}

func NewRcmdData(param *param.ParamData, fallBack FallBack, clientParams *ClientParams,
	errOverInst *speedlimit.ErrorOver, rcmdTimeOut int) *RcmdData {
	return &RcmdData{
		errOverInst: errOverInst,
		rcmdTimeOut: rcmdTimeOut,

		BaseDataHandle: NewBaseDataHandle(param, clientParams, fallBack),
	}
}

func (m *RcmdData) DoudiAndFallBack(ctx context.Context, rsp *RcmdDataResp) (err error) {
	isDoudi, isRecommendDoudi := m.param.IsDoudi(ctx)
	if isDoudi {
		err = m.FallBackHandle(ctx, rsp)
		if err != nil {
			return
		}
	} else {
		//不走兜底，以推荐的loadfinish为准
		rsp.LoadFinish = rsp.GenResp.GetBottomReached()
	}
	if len(rsp.GetOriginChannelIds()) != 0 { //兜底也不为空时才展示
		rsp.IsRecommendDoudi = isRecommendDoudi
	}
	return err
}

// GetChannelData 获取房间列表数据（整合后）
func (m *RcmdData) GetChannelData(ctx context.Context) (rsp *RcmdDataResp, err error) {
	rsp = &RcmdDataResp{
		RcmdInfo: &RcmdInfo{},
	}
	userId := m.param.ServiceInfo.UserID
	if cache.GetSwitchStatusByType(configserverPB.SwitchBusinessType_GAME_MIX_CHANNEL_LIST) {
		err = m.FallBackHandle(ctx, rsp)
		if err != nil {
			return
		}

	} else {
		genReq, _ := m.GenParam(ctx)
		m.param.CostReporter.Tick("gen_req_by_filter")
		m.errOverInst.AddTotalCnt(1)
		timeOutTime := m.rcmdTimeOut
		rcmdCtx, cancel := context.WithTimeout(ctx, time.Duration(timeOutTime)*time.Millisecond)
		defer cancel()
		rsp.GenResp, err = m.GetData(rcmdCtx, genReq)
		m.param.CostReporter.Tick("rcmd_cost_time")

		if err != nil || rsp.GenResp == nil { //推荐失败走兜底
			m.param.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Rcmd_Err)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, m.param.ReportData)
			if err != nil && strings.Contains(err.Error(), "context deadline exceeded") && conf.GetEnv() == conf.Production {
				sendErr := monkey_sender.GetNumMsgSenderByChatId(conf.PublicSwitchConfig.GetFallBackChatId(), conf.PublicSwitchConfig.GetFallBackWarnDuration()).
					SendMsg("混推房间列表", fmt.Sprintf("推荐耗时超过%dms，uid:%d, rcmdReq:%s", timeOutTime, userId, genReq.String()))
				if sendErr != nil {
					log.ErrorWithCtx(ctx, "ListTopicChannel monkey_sender fallback SendMsg err:%v", sendErr)
				}
			}
			err = m.FallBackHandle(ctx, rsp)
			if err != nil {
				return
			}

		} else {
			rsp.SetOriginChannelIds(rsp.GenResp.GetChannelId())
			if len(rsp.GetOriginChannelIds()) == 0 {
				rsp.LoadFinish = rsp.GenResp.GetBottomReached()
				m.errOverInst.AddErrorCnt(1)
				m.param.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Rcmd_Nil)
				blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, m.param.ReportData)
				log.WarnWithCtx(ctx, "UserLine Err: ListTopicChannel rcmd genResp get no data in(%v) genReq(%v) genResp(%v)", m.param.In, genReq, rsp.GenResp)
				rsp.LoadFinish = true
				if m.errOverInst.IsOverByRatio(ctx, conf.PublicSwitchConfig.GetDoudiNilRatio()) {
					err = m.DoudiAndFallBack(ctx, rsp)
					if err != nil {
						return
					}
				} else {
					if len(conf.PublicSwitchConfig.GetDoudiTabMap()) != 0 {
						if _, ok := conf.PublicSwitchConfig.GetDoudiTabMap()[m.param.PlayItems.TabId]; ok {
							err = m.DoudiAndFallBack(ctx, rsp)
							if err != nil {
								return
							}
							log.DebugWithCtx(ctx, "ListTopicChannel DoudiTabMap fall back, tabId:%d, rsp:%+v", m.param.PlayItems.TabId, rsp)
						}
					}
				}
			}
			//var otherChannelIds []uint32
			//rsp.UgcChannelIds, otherChannelIds, err = m.getCidToUserMap(ctx, userId, rsp.GetOriginChannelIds())
			//if err != nil {
			//	return
			//}
			//rsp.SetOtherChannelIds(otherChannelIds)

			//拼接客户端数据上报字段
			rsp.ReportData = &channel_play.ListTopicChannelResp_DataReport{
				Footprint: rsp.GenResp.GetFootprint(),
			}
			rsp.Appointment, rsp.IsRefresh = getGenRespNotify(rsp.GenResp.GetNotifyList())
			rsp.LoadFinish = rsp.GenResp.GetBottomReached()
			rsp.RcmdInfo.RcmdChannelInfoMap = rsp.GenResp.GetCommonChannelInfoMap()
			rsp.RcmdInfo.RcmdLoc = rsp.GenResp.GetSelfLoc()
			rsp.RcmdInfo.Labels = rsp.GenResp.GetLabels()
			
		}
	}
	return
}

func getGenRespNotify(notifyTypes []genPB.GetRecommendationListResp_NotifyType) (appointment, refresh bool) {
	for _, notifyType := range notifyTypes {
		if notifyType == genPB.GetRecommendationListResp_APPOINTMENT {
			appointment = true
		}
		if notifyType == genPB.GetRecommendationListResp_RefreshSucc {
			refresh = true
		}
	}
	return
}
