package cache

import (
	"context"
	"golang.52tt.com/pkg/log"
	configserverPB "golang.52tt.com/protocol/services/configserver"
	clients "golang.52tt.com/services/channel-play-logic/internal/cache/cache_client"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
)

type switchCache struct {
	fallBackSwitchMap map[int32]bool
}

var switchInfoCache = &switchCache{}

func setSwitchInfoCache(tmpCache *switchCache) {
	switchInfoCache = tmpCache
}

func timerRefreshSwitchCache(ctx context.Context) {
	defer func() {
		err := recover()
		if err != nil {
			log.Errorf("timerRefreshSwitchCache refreshSwitchCache err = %v", err)
		}
	}()
	if err := refreshSwitchCache(ctx); err != nil {
		log.ErrorWithCtx(ctx, "refreshSwitchCache err:%v", err)
	}
}

func refreshSwitchCache(ctx context.Context) (err error) {
	var env = conf.Environment
	dyConfEnv := conf.ChannelPlayLogicConfig.GetTestConfEnv()
	var switchEnv configserverPB.SwitchEnv
	if env != conf.Production && dyConfEnv == conf.Staging {
		switchEnv = configserverPB.SwitchEnv_SWITCH_STAGING
	} else {
		switchEnv = configserverPB.SwitchEnv_SWITCH_DEFAULT
	}

	switchReq := configserverPB.GetFallBackSwitchByTypesReq{
		Types: []configserverPB.SwitchBusinessType{configserverPB.SwitchBusinessType_GAME_MIX_CHANNEL_LIST,
			configserverPB.SwitchBusinessType_MUSIC_CHANNEL_LIST},
		ReqSource: "channel-play-logic",
		SwitchEnv: switchEnv,
	}
	resp, err := clients.ConfigServerClient.GetFallBackSwitchByTypes(ctx, switchReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshSwitchCache ConfigServerClient.GetFallBackSwitchByTypes err:%v", err)
		return
	}
	tmpCache := &switchCache{
		fallBackSwitchMap: resp.GetSwitchMap(),
	}
	setSwitchInfoCache(tmpCache)
	log.InfoWithCtx(ctx, "refreshSwitchCache success switchMap:%v, req:%+v, env:%s, dyConfEnv:%s", tmpCache.fallBackSwitchMap, switchReq, env, dyConfEnv)
	return nil
}

func GetSwitchStatusByType(businessType configserverPB.SwitchBusinessType) bool {
	return switchInfoCache.fallBackSwitchMap[int32(businessType)]
}
