package config

import (
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/config/ttconfig"
	"sync/atomic"
)

type FastPcConfig struct {
	CertificationActivity *CertificationActivityConfig `json:"certification_activity_time"`
}

type CertificationActivityConfig struct {
	IsOpen                bool   `json:"is_open"`                   // 是否开启认证活动
	StayInRoomTriggerTime uint32 `json:"stay_in_room_trigger_time"` // 满足进房多久完成任务,单位秒
	CertTypeId            string `json:"cert_type_id"`              // 认证标类型ID
	CertExpireDay         uint32 `json:"cert_expire_day"`           // 认证过期时间,单位天
	CertText              string `json:"cert_text"`                 // 认证长文案
	CertShortText         string `json:"cert_short_text"`           // 认证短文案
	CertOperator          string `json:"cert_operator"`             // 认证操作人
	TTHelperText          string `json:"tt_helper_text"`            // tt助手文案
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *FastPcConfig) Format() error {
	return nil
}

var (
	atomicFastPcConfig *atomic.Value
)

//func init() {
//	if err := InitFastPcConfig(); err != nil {
//	    panic(err)
//	}
//}

func SetFastPcConfig(cfg *FastPcConfig) {
	if atomicFastPcConfig == nil {
		atomicFastPcConfig = &atomic.Value{}
	}
	if cfg == nil {
		cfg = &FastPcConfig{}
	}
	atomicFastPcConfig.Store(cfg)
}

// InitFastPcConfig
// 可以选择外部初始化或者直接init函数初始化
func InitFastPcConfig() error {
	cfg := &FastPcConfig{}
	atomCfg, err := ttconfig.AtomLoad("/data/cicd-dy-conf/ser/fast-pc.json", cfg)
	if nil != err {
		return err
	}
	atomicFastPcConfig = atomCfg
	return nil
}

func GetFastPcConfig() *FastPcConfig {
	return atomicFastPcConfig.Load().(*FastPcConfig)
}

func (s *FastPcConfig) GetCertificationActivityConfig() *CertificationActivityConfig {
	if s == nil {
		return &CertificationActivityConfig{}
	}
	return s.CertificationActivity
}
