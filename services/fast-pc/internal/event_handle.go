package internal

import (
	"context"
	context_info "golang.52tt.com/pkg/context-info"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/channel"
	game_fre_server "golang.52tt.com/protocol/services/game-fre-server"
	personal_certification_pb "golang.52tt.com/protocol/services/personalcertification"
	userOnlinePb "golang.52tt.com/protocol/services/user-online"
	config "golang.52tt.com/services/fast-pc/internal/config/ttconfig/fast_pc"
	"time"

	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	"gitlab.ttyuyin.com/tyr/x/log"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_channelol"
)

const oneDaySeconds = 24 * 60 * 60

// onChannelEvent 处理进出房事件
func (s *Server) onChannelEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {

	channelEvent := &kafka_channelol.ChannelEvent{}
	if err := proto.Unmarshal(msg.Value, channelEvent); err != nil {
		log.ErrorWithCtx(ctx, "onChannelEvent unmarshal failed: %v", err)
		return err, false
	}

	log.DebugWithCtx(ctx, "onChannelEvent channelEvent: %+v", channelEvent)

	switch channelEvent.GetEvent().(type) {
	case *kafka_channelol.ChannelEvent_LeaveEvent:
		// 只处理UGC退房事件
		if channel.ChannelType(channelEvent.GetLeaveEvent().GetBaseInfo().GetChannelType()) != channel.ChannelType_USER_CHANNEL_TYPE {
			return nil, false
		}
		_ = s.handlePcCertificationActivity(ctx, channelEvent.GetLeaveEvent())
	case *kafka_channelol.ChannelEvent_EnterEvent:
		return nil, false
	default:
		log.WarnWithCtx(ctx, "onChannelEvent unknown event type")
		return nil, false
	}

	return nil, false
}

func (s *Server) handlePcCertificationActivity(ctx context.Context, leaveEvent *kafka_channelol.ChannelLeaveEvent) error {
	ctx, cancel := context.WithTimeout(ctx, 1*time.Second)
	defer cancel()
	ctx = context_info.GenReqId(ctx)

	if leaveEvent == nil {
		log.WarnWithCtx(ctx, "handlePcCertificationActivity leaveEvent is nil")
		return nil
	}

	// 如果不在活动期间，直接返回
	activityConfig := config.GetFastPcConfig().GetCertificationActivityConfig()
	if !activityConfig.IsOpen {
		log.InfoWithCtx(ctx, "handlePcCertificationActivity activity is not open")
		return nil
	}

	baseInfo := leaveEvent.GetBaseInfo()
	log.InfoWithCtx(ctx, "handlePcCertificationActivity processing leave event, uid: %d, chId: %d, channelType: %d, stayInRoomSecond: %d",
		baseInfo.GetUid(), baseInfo.GetChId(), baseInfo.GetChannelType(), leaveEvent.GetOnlineSecond())

	// 检查是否满足在房10分钟
	if leaveEvent.GetOnlineSecond() > activityConfig.StayInRoomTriggerTime {
		// 是否开黑PC或者WEB端在线
		multiOnlineInfo, err := s.clients.UserOnlineCli.GetMultiOnlineInfo(ctx, baseInfo.GetUid(), nil)
		if err != nil {
			log.ErrorWithCtx(ctx, "handlePcCertificationActivity GetMultiOnlineInfo err: %v", err)
			return err
		}
		log.DebugWithCtx(ctx, "handlePcCertificationActivity multiOnlineInfo: %+v", multiOnlineInfo)
		for _, onlineInfo := range multiOnlineInfo.GetOnlineInfoList() {
			if onlineInfo.GetOnlineType() == userOnlinePb.OnlineType_ONLINE_TYPE_ONLINE && protocol.IsFastPcClientType(onlineInfo.GetClientType()) {
				// 下发认证标
				err := s.sendCertificationReward(ctx, baseInfo.GetUid(), activityConfig.CertTypeId, activityConfig.CertExpireDay)
				if err != nil {
					log.ErrorWithCtx(ctx, "handlePcCertificationActivity sendCertificationReward err: %v", err)
					return err
				}
				break
			}
		}
	}

	return nil
}

func (s *Server) sendCertificationReward(ctx context.Context, uid uint32, certTypeId string, expireDay uint32) error {
	now := time.Now()

	// 今天已经发过认证标了
	countResp, err := s.clients.GameFreCli.GetFreCountBySource(ctx, &game_fre_server.GetFreCountBySourceReq{
		FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_FAST_PC_SEND_CERTIFICATION_LIMIT),
		Suffix:         now.Format("20060102"),
		UserId:         uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "sendCertificationReward GetFreCountBySource failed, uid: %d, err: %v", uid, err)
		return err
	}

	if countResp.GetCount() >= 1 {
		log.InfoWithCtx(ctx, "sendCertificationReward already sent certification reward today, uid: %d", uid)
		return nil
	}

	activityConfig := config.GetFastPcConfig().GetCertificationActivityConfig()

	certTypeResp, err := s.clients.PersonalCertCli.GetCertTypeById(ctx, certTypeId)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendCertificationReward GetCertTypeById(certTypeId:%s) err: %v", certTypeId, err)
		return err
	}
	certType := certTypeResp.GetCert()
	certReq := &personal_certification_pb.SetUserCertReq{
		CertList: []*personal_certification_pb.UserCertInfo{
			{
				Uid:          uid,
				CertTypeId:   certType.GetId(),
				CertTypeName: certType.GetName(),
				Text:         activityConfig.CertText,
				ShortText:    activityConfig.CertShortText,
				BeginTime:    uint32(now.Unix()),
				EndTime:      uint32(now.Unix()) + (expireDay * oneDaySeconds),
				UpdateTime:   uint32(now.Unix()),
				Operator:     activityConfig.CertOperator,
			},
		},
	}
	certResp, err := s.clients.PersonalCertCli.SetUserCert(ctx, certReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendCertificationReward SetUserCert failed, uid: %d, err: %v", err)
		return err
	}

	_, err = s.clients.GameFreCli.IncFreCountBySource(ctx, &game_fre_server.IncFreCountBySourceReq{
		FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_FAST_PC_SEND_CERTIFICATION_LIMIT),
		Suffix:         now.Format("20060102"),
		UserId:         uid,
		ExpireTime:     uint64(time.Hour * 24),
	})
	if err != nil {
		log.WarnWithCtx(ctx, "sendCertificationReward IncFreCountBySource failed, uid: %d, err: %v", uid, err)
	}

	if activityConfig.TTHelperText != "" {
		_ = s.sendTTHelperMsg(ctx, uid, activityConfig.TTHelperText)
	}

	log.InfoWithCtx(ctx, "sendCertificationReward SetUserCert success, uid: %d, certReq: %+v, certResp: %+v",
		uid, certReq, certResp)
	return nil
}

func (s *Server) sendTTHelperMsg(ctx context.Context, uid uint32, msg string) error {
	err := s.clients.ApiCenterClient.SendTTHelperMsg(ctx, uid, msg)
	if err != nil {
		log.WarnWithCtx(ctx, "sendTTHelperMsg SendTTHelperMsg failed, uid:%d msg:%s", uid, msg)
	}
	log.InfoWithCtx(ctx, "sendTTHelperMsg success, uid: %d, msg: %s", uid, msg)
	return nil
}
