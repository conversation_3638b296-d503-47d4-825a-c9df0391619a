package internal

import (
    "context"
    config "golang.52tt.com/services/fast-pc/internal/config/ttconfig/fast_pc"
    "golang.52tt.com/services/fast-pc/internal/rpc"

    middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"
    "gitlab.ttyuyin.com/tyr/x/log"
    "golang.52tt.com/services/fast-pc/internal/event"
    "golang.52tt.com/services/fast-pc/internal/event/eventlink"
)

type StartConfig struct {
    // from config file
    EventLink *middleware_event.Options `json:"event_link"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    clients, err := rpc.NewClients()
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer rpc.NewClients failed: %v", err)
        return nil, err
    }

    err = config.InitFastPcConfig()
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer config.InitFastPcConfig failed: %v", err)
        return nil, err
    }

    eventBus, err := eventlink.NewEventBus(ctx, cfg.EventLink)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer create eventBus failed: %v", err)
        return nil, err
    }

    s := &Server{
        eventBus: eventBus,
        clients:  clients,
    }

    // 进出房事件
    if err := eventBus.Subscribe(ctx, event.SubNameChannelEvent, s.onChannelEvent); err != nil {
        log.ErrorWithCtx(ctx, "NewServer Subscribe name(%s) err: %v", event.SubNameChannelEvent, err)
        eventBus.Close()
        return nil, err
    }

    return s, nil
}

type Server struct {
    eventBus event.EventBus
    clients  *rpc.Clients
}

func (s *Server) ShutDown() {
    if s.eventBus != nil {
        s.eventBus.Close()
    }
}
