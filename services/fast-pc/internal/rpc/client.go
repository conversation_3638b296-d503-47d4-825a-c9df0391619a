package rpc

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	personal_cli "golang.52tt.com/clients/personal-certification"
	user_online "golang.52tt.com/clients/user-online"
	game_fre_server "golang.52tt.com/protocol/services/game-fre-server"
)

type Clients struct {
	PersonalCertCli personal_cli.IClient
	UserOnlineCli   user_online.IClient
	GameFreCli      game_fre_server.GameFreServerClient
	ApiCenterClient apicenter.IClient
}

func NewClients() (*Clients, error) {
	c := new(Clients)

	var (
		ctx = context.Background()
		err error
	)

	c.PersonalCertCli = personal_cli.NewIClient()
	c.UserOnlineCli = user_online.NewIClient()
	c.GameFreCli, err = game_fre_server.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients game_fre_server.NewClient failed: %v", err)
		return nil, err
	}
	c.ApiCenterClient = apicenter.NewIClient()

	return c, nil
}
