package internal

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	apicenter_mocks "golang.52tt.com/clients/mocks/apicenter/apiserver"
	personal_cert_mocks "golang.52tt.com/clients/mocks/personal-certification"
	user_online_mocks "golang.52tt.com/clients/mocks/user-online"
	"golang.52tt.com/protocol/app/channel"
	game_fre_server "golang.52tt.com/protocol/services/game-fre-server"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_channelol"
	personal_certification_pb "golang.52tt.com/protocol/services/personalcertification"
	user_online_pb "golang.52tt.com/protocol/services/user-online"
	config "golang.52tt.com/services/fast-pc/internal/config/ttconfig/fast_pc"
	"golang.52tt.com/services/fast-pc/internal/event/mocks"
	"golang.52tt.com/services/fast-pc/internal/rpc"
)

func init() {
	// 设置默认配置，使用2025年的时间范围
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600, // 10分钟
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1, // 1天
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})
}

func genMockClients(ctrl *gomock.Controller) *rpc.Clients {
	return &rpc.Clients{
		PersonalCertCli: personal_cert_mocks.NewMockIClient(ctrl),
		UserOnlineCli:   user_online_mocks.NewMockIClient(ctrl),
		GameFreCli:      game_fre_server.NewMockGameFreServerClient(ctrl),
		ApiCenterClient: apicenter_mocks.NewMockIClient(ctrl),
	}
}

func TestServer_onChannelEvent_LeaveEvent_UGC(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	mockEventBus := mocks.NewMockEventBus(ctrl)
	server := &Server{
		eventBus: mockEventBus,
	}

	// 创建UGC退房事件（USER_CHANNEL_TYPE）- 根据代码逻辑，这种事件会被忽略
	leaveEvent := &kafka_channelol.ChannelLeaveEvent{
		BaseInfo: &kafka_channelol.ChannelEventBase{
			Uid:         12345,
			ChId:        123,
			ChannelType: uint32(channel.ChannelType_USER_CHANNEL_TYPE), // UGC房间
		},
		OnlineSecond: 300,
	}

	channelEvent := &kafka_channelol.ChannelEvent{
		Event: &kafka_channelol.ChannelEvent_LeaveEvent{
			LeaveEvent: leaveEvent,
		},
	}

	data, err := proto.Marshal(channelEvent)
	assert.NoError(t, err)

	msg := &subscriber.ConsumerMessage{
		Topic:     "channel_event",
		Partition: 0,
		Offset:    100,
		Value:     data,
	}

	ctx := context.Background()
	err, retry := server.onChannelEvent(ctx, msg)

	// UGC退房事件应该被忽略，返回 nil, false
	assert.NoError(t, err)
	assert.False(t, retry)
}

func TestServer_onChannelEvent_LeaveEvent_NonUGC(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	mockEventBus := mocks.NewMockEventBus(ctrl)

	server := &Server{
		eventBus: mockEventBus,
	}

	// 创建非UGC退房事件（GUILD_HOME_CHANNEL_TYPE）- 根据代码逻辑，这种事件会被忽略
	leaveEvent := &kafka_channelol.ChannelLeaveEvent{
		BaseInfo: &kafka_channelol.ChannelEventBase{
			Uid:         12345,
			ChId:        123,
			ChannelType: uint32(channel.ChannelType_GUILD_HOME_CHANNEL_TYPE),
		},
		OnlineSecond: 700, // 超过10分钟
	}

	channelEvent := &kafka_channelol.ChannelEvent{
		Event: &kafka_channelol.ChannelEvent_LeaveEvent{
			LeaveEvent: leaveEvent,
		},
	}

	data, err := proto.Marshal(channelEvent)
	assert.NoError(t, err)

	msg := &subscriber.ConsumerMessage{
		Topic:     "channel_event",
		Partition: 0,
		Offset:    100,
		Value:     data,
	}

	ctx := context.Background()
	err, retry := server.onChannelEvent(ctx, msg)

	// 非UGC退房事件应该被忽略，返回 nil, false
	assert.NoError(t, err)
	assert.False(t, retry)
}

func TestServer_onChannelEvent_EnterEvent(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	mockEventBus := mocks.NewMockEventBus(ctrl)
	server := &Server{
		eventBus: mockEventBus,
	}

	enterEvent := &kafka_channelol.ChannelEnterEvent{
		BaseInfo: &kafka_channelol.ChannelEventBase{
			Uid:         12345,
			ChId:        123,
			ChannelType: uint32(channel.ChannelType_USER_CHANNEL_TYPE),
		},
	}

	channelEvent := &kafka_channelol.ChannelEvent{
		Event: &kafka_channelol.ChannelEvent_EnterEvent{
			EnterEvent: enterEvent,
		},
	}

	data, err := proto.Marshal(channelEvent)
	assert.NoError(t, err)

	msg := &subscriber.ConsumerMessage{
		Topic:     "channel_event",
		Partition: 0,
		Offset:    100,
		Value:     data,
	}

	ctx := context.Background()
	err, retry := server.onChannelEvent(ctx, msg)

	assert.NoError(t, err)
	assert.False(t, retry)
}

func TestServer_onChannelEvent_InvalidMessage(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	mockEventBus := mocks.NewMockEventBus(ctrl)
	server := &Server{
		eventBus: mockEventBus,
	}

	msg := &subscriber.ConsumerMessage{
		Topic:     "channel_event",
		Partition: 0,
		Offset:    100,
		Value:     []byte("invalid message"),
	}

	ctx := context.Background()
	err, retry := server.onChannelEvent(ctx, msg)

	assert.Error(t, err)
	assert.False(t, retry)
}

func TestServer_onChannelEvent_UnknownEventType(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	mockEventBus := mocks.NewMockEventBus(ctrl)
	server := &Server{
		eventBus: mockEventBus,
	}

	// 创建一个空的ChannelEvent（没有具体事件类型）
	channelEvent := &kafka_channelol.ChannelEvent{}

	data, err := proto.Marshal(channelEvent)
	assert.NoError(t, err)

	msg := &subscriber.ConsumerMessage{
		Topic:     "channel_event",
		Partition: 0,
		Offset:    100,
		Value:     data,
	}

	ctx := context.Background()
	err, retry := server.onChannelEvent(ctx, msg)

	assert.NoError(t, err)
	assert.False(t, retry)
}

func TestServer_handlePcCertificationActivity_NilLeaveEvent(t *testing.T) {
	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	server := &Server{}

	ctx := context.Background()
	err := server.handlePcCertificationActivity(ctx, nil)

	assert.NoError(t, err)
}

func TestServer_handlePcCertificationActivity_NotInActivityTime(t *testing.T) {
	// 设置活动时间为过去的时间
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                false,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
		},
	})

	server := &Server{}

	leaveEvent := &kafka_channelol.ChannelLeaveEvent{
		BaseInfo: &kafka_channelol.ChannelEventBase{
			Uid:         12345,
			ChId:        123,
			ChannelType: uint32(channel.ChannelType_USER_CHANNEL_TYPE),
		},
		OnlineSecond: 700,
	}

	ctx := context.Background()
	err := server.handlePcCertificationActivity(ctx, leaveEvent)

	// 根据代码第77行，不在活动时间内时返回 nil
	assert.NoError(t, err)
}

func TestServer_handlePcCertificationActivity_OnlineTimeNotEnough(t *testing.T) {
	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	server := &Server{}

	leaveEvent := &kafka_channelol.ChannelLeaveEvent{
		BaseInfo: &kafka_channelol.ChannelEventBase{
			Uid:         12345,
			ChId:        123,
			ChannelType: uint32(channel.ChannelType_USER_CHANNEL_TYPE),
		},
		OnlineSecond: 300, // 5分钟，不足10分钟
	}

	ctx := context.Background()
	err := server.handlePcCertificationActivity(ctx, leaveEvent)

	// 根据代码逻辑，在线时长不足时，不会进入if分支，最终返回 nil
	assert.NoError(t, err)
}

func TestServer_handlePcCertificationActivity_GetMultiOnlineInfoError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	clients := genMockClients(ctrl)
	mockUserOnlineCli := clients.UserOnlineCli.(*user_online_mocks.MockIClient)
	server := &Server{
		clients: clients,
	}

	leaveEvent := &kafka_channelol.ChannelLeaveEvent{
		BaseInfo: &kafka_channelol.ChannelEventBase{
			Uid:         12345,
			ChId:        123,
			ChannelType: uint32(channel.ChannelType_USER_CHANNEL_TYPE), // 使用UGC类型
		},
		OnlineSecond: 700,
	}

	// Mock GetMultiOnlineInfo 返回错误
	mockUserOnlineCli.EXPECT().
		GetMultiOnlineInfo(gomock.Any(), uint32(12345), gomock.Any()).
		Return(nil, errors.New("network error"))

	ctx := context.Background()
	err := server.handlePcCertificationActivity(ctx, leaveEvent)

	// 根据代码第84行，GetMultiOnlineInfo错误时返回 err
	assert.Error(t, err)
}

func TestServer_handlePcCertificationActivity_NoPcClientOnline(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	clients := genMockClients(ctrl)
	mockUserOnlineCli := clients.UserOnlineCli.(*user_online_mocks.MockIClient)
	server := &Server{
		clients: clients,
	}

	leaveEvent := &kafka_channelol.ChannelLeaveEvent{
		BaseInfo: &kafka_channelol.ChannelEventBase{
			Uid:         12345,
			ChId:        123,
			ChannelType: uint32(channel.ChannelType_USER_CHANNEL_TYPE), // 使用UGC类型
		},
		OnlineSecond: 700,
	}

	// Mock GetMultiOnlineInfo 返回非PC客户端
	mockUserOnlineCli.EXPECT().
		GetMultiOnlineInfo(gomock.Any(), uint32(12345), gomock.Any()).
		Return(&user_online_pb.MultiOnlineInfo{
			OnlineInfoList: []*user_online_pb.OnlineInfo{
				{
					OnlineType: user_online_pb.OnlineType_ONLINE_TYPE_ONLINE,
					ClientType: 1, // 手机客户端
				},
				{
					OnlineType: user_online_pb.OnlineType_ONLINE_TYPE_OFFLINE,
					ClientType: 1, // pc
				},
			},
		}, nil)

	ctx := context.Background()
	err := server.handlePcCertificationActivity(ctx, leaveEvent)

	assert.NoError(t, err)
}

func TestServer_sendCertificationReward_GetCertTypeByIdError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	clients := genMockClients(ctrl)
	mockPersonalCertCli := clients.PersonalCertCli.(*personal_cert_mocks.MockIClient)
	mockGameFreCli := clients.GameFreCli.(*game_fre_server.MockGameFreServerClient)
	server := &Server{
		clients: clients,
	}

	// Mock GetFreCountBySource 成功
	mockGameFreCli.EXPECT().
		GetFreCountBySource(gomock.Any(), gomock.Any()).
		Return(&game_fre_server.GetFreCountBySourceResp{Count: 0}, nil)

	// Mock GetCertTypeById 返回错误
	mockPersonalCertCli.EXPECT().
		GetCertTypeById(gomock.Any(), "test_cert_type_id").
		Return(nil, errors.New("cert type not found"))

	ctx := context.Background()
	err := server.sendCertificationReward(ctx, 12345, "test_cert_type_id", 86400)

	// GetCertTypeById错误时直接返回error
	assert.Error(t, err)
}

func TestServer_sendCertificationReward_SetUserCertError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	clients := genMockClients(ctrl)
	mockPersonalCertCli := clients.PersonalCertCli.(*personal_cert_mocks.MockIClient)
	mockGameFreCli := clients.GameFreCli.(*game_fre_server.MockGameFreServerClient)
	server := &Server{
		clients: clients,
	}

	// Mock GetFreCountBySource 成功
	mockGameFreCli.EXPECT().
		GetFreCountBySource(gomock.Any(), gomock.Any()).
		Return(&game_fre_server.GetFreCountBySourceResp{Count: 0}, nil)

	// Mock GetCertTypeById 成功
	mockPersonalCertCli.EXPECT().
		GetCertTypeById(gomock.Any(), "test_cert_type_id").
		Return(&personal_certification_pb.GetCertTypeByIdResp{
			Cert: &personal_certification_pb.CertTypeInfo{
				Id:   "test_cert_type_id",
				Name: "PC开黑认证",
			},
		}, nil)

	// Mock SetUserCert 返回错误
	mockPersonalCertCli.EXPECT().
		SetUserCert(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("set cert failed"))

	ctx := context.Background()
	err := server.sendCertificationReward(ctx, 12345, "test_cert_type_id", 86400)

	// SetUserCert错误时直接返回error
	assert.Error(t, err)
}

func TestServer_sendCertificationReward_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	clients := genMockClients(ctrl)
	mockPersonalCertCli := clients.PersonalCertCli.(*personal_cert_mocks.MockIClient)
	mockGameFreCli := clients.GameFreCli.(*game_fre_server.MockGameFreServerClient)
	mockApiCenterCli := clients.ApiCenterClient.(*apicenter_mocks.MockIClient)
	server := &Server{
		clients: clients,
	}

	// Mock GetFreCountBySource 成功
	mockGameFreCli.EXPECT().
		GetFreCountBySource(gomock.Any(), gomock.Any()).
		Return(&game_fre_server.GetFreCountBySourceResp{Count: 0}, nil)

	// Mock GetCertTypeById 成功
	mockPersonalCertCli.EXPECT().
		GetCertTypeById(gomock.Any(), "test_cert_type_id").
		Return(&personal_certification_pb.GetCertTypeByIdResp{
			Cert: &personal_certification_pb.CertTypeInfo{
				Id:   "test_cert_type_id",
				Name: "PC开黑认证",
			},
		}, nil)

	// Mock SetUserCert 成功
	mockPersonalCertCli.EXPECT().
		SetUserCert(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, req *personal_certification_pb.SetUserCertReq) (*personal_certification_pb.SetUserCertResp, error) {
			// 验证请求参数
			assert.Len(t, req.CertList, 1)
			cert := req.CertList[0]
			assert.Equal(t, uint32(12345), cert.Uid)
			assert.Equal(t, "test_cert_type_id", cert.CertTypeId)
			assert.Equal(t, "PC开黑认证", cert.CertTypeName)
			assert.Equal(t, "pc开黑认证标", cert.Text)
			assert.Equal(t, "pc开黑认证标", cert.ShortText)
			assert.Equal(t, "pc开黑自动发认证标", cert.Operator)
			assert.True(t, cert.BeginTime > 0)
			assert.True(t, cert.EndTime > cert.BeginTime)
			assert.True(t, cert.UpdateTime > 0)
			return &personal_certification_pb.SetUserCertResp{}, nil
		})

	// Mock IncFreCountBySource 成功
	mockGameFreCli.EXPECT().
		IncFreCountBySource(gomock.Any(), gomock.Any()).
		Return(&game_fre_server.IncFreCountBySourceResp{}, nil)

	mockApiCenterCli.EXPECT().SendTTHelperMsg(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	ctx := context.Background()
	err := server.sendCertificationReward(ctx, 12345, "test_cert_type_id", 1)

	assert.NoError(t, err)
}

func TestServer_sendCertificationReward_AlreadySentToday(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	clients := genMockClients(ctrl)
	mockGameFreCli := clients.GameFreCli.(*game_fre_server.MockGameFreServerClient)
	server := &Server{
		clients: clients,
	}

	// Mock GetFreCountBySource 返回已经发过了
	mockGameFreCli.EXPECT().
		GetFreCountBySource(gomock.Any(), gomock.Any()).
		Return(&game_fre_server.GetFreCountBySourceResp{Count: 2}, nil)

	ctx := context.Background()
	err := server.sendCertificationReward(ctx, 12345, "test_cert_type_id", 86400)

	// 已经发过认证标，应该返回 nil
	assert.NoError(t, err)
}

func TestServer_sendCertificationReward_GetFreCountError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 重置配置确保测试隔离
	config.SetFastPcConfig(&config.FastPcConfig{
		CertificationActivity: &config.CertificationActivityConfig{
			IsOpen:                true,
			StayInRoomTriggerTime: 600,
			CertTypeId:            "test_cert_type_id",
			CertExpireDay:         1,
			CertText:              "pc开黑认证标",
			CertShortText:         "pc开黑认证标",
			CertOperator:          "pc开黑自动发认证标",
			TTHelperText:          "恭喜您获得了PC开黑认证标，快去个人中心查看吧~",
		},
	})

	clients := genMockClients(ctrl)
	mockGameFreCli := clients.GameFreCli.(*game_fre_server.MockGameFreServerClient)
	server := &Server{
		clients: clients,
	}

	// Mock GetFreCountBySource 返回错误
	mockGameFreCli.EXPECT().
		GetFreCountBySource(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("network error"))

	ctx := context.Background()
	err := server.sendCertificationReward(ctx, 12345, "test_cert_type_id", 86400)

	// GetFreCountBySource错误时直接返回error
	assert.Error(t, err)
}

/*// 边界测试用例
func TestServer_handlePcCertificationActivity_EdgeCases(t *testing.T) {
	tests := []struct {
		name       string
		leaveEvent *kafka_channelol.ChannelLeaveEvent
		setupMocks func(*personal_cert_mocks.MockIClient, *user_online_mocks.MockIClient, *game_fre_server.MockGameFreServerClient)
		wantErr    bool
	}{
		{
			name: "nil base info",
			leaveEvent: &kafka_channelol.ChannelLeaveEvent{
				BaseInfo:     nil,
				OnlineSecond: 700,
			},
			setupMocks: func(mockPersonalCertCli *personal_cert_mocks.MockIClient, mockUserOnlineCli *user_online_mocks.MockIClient, mockGameFreCli *game_fre_server.MockGameFreServerClient) {
				// 当baseInfo为nil时，GetUid()返回0，仍然会调用GetMultiOnlineInfo
				mockUserOnlineCli.EXPECT().
					GetMultiOnlineInfo(gomock.Any(), uint32(0), gomock.Any()).
					Return(&user_online_pb.MultiOnlineInfo{
						OnlineInfoList: []*user_online_pb.OnlineInfo{
							{ClientType: 1}, // 手机客户端，不会触发认证
						},
					}, nil)
			},
			wantErr: false,
		},
		{
			name: "exactly trigger time",
			leaveEvent: &kafka_channelol.ChannelLeaveEvent{
				BaseInfo: &kafka_channelol.ChannelEventBase{
					Uid:         12345,
					ChId:        123,
					ChannelType: uint32(channel.ChannelType_GUILD_HOME_CHANNEL_TYPE), // 使用非UGC类型
				},
				OnlineSecond: 600, // 正好10分钟
			},
			setupMocks: func(mockPersonalCertCli *personal_cert_mocks.MockIClient, mockUserOnlineCli *user_online_mocks.MockIClient, mockGameFreCli *game_fre_server.MockGameFreServerClient) {
				// 不会触发，因为是 > 而不是 >=
			},
			wantErr: false,
		},
		{
			name: "multiple client types with PC",
			leaveEvent: &kafka_channelol.ChannelLeaveEvent{
				BaseInfo: &kafka_channelol.ChannelEventBase{
					Uid:         12345,
					ChId:        123,
					ChannelType: uint32(channel.ChannelType_USER_CHANNEL_TYPE), // 使用UGC类型
				},
				OnlineSecond: 700,
			},
			setupMocks: func(mockPersonalCertCli *personal_cert_mocks.MockIClient, mockUserOnlineCli *user_online_mocks.MockIClient, mockGameFreCli *game_fre_server.MockGameFreServerClient) {
				mockUserOnlineCli.EXPECT().
					GetMultiOnlineInfo(gomock.Any(), uint32(12345), gomock.Any()).
					Return(&user_online_pb.MultiOnlineInfo{
						OnlineInfoList: []*user_online_pb.OnlineInfo{
							{ClientType: 1},  // 手机
							{ClientType: 9},  // PC
							{ClientType: 10}, // PC Web
						},
					}, nil)

				mockGameFreCli.EXPECT().
					GetFreCountBySource(gomock.Any(), gomock.Any()).
					Return(&game_fre_server.GetFreCountBySourceResp{Count: 0}, nil)

				mockPersonalCertCli.EXPECT().
					GetCertTypeById(gomock.Any(), "test_cert_type_id").
					Return(&personal_certification_pb.GetCertTypeByIdResp{
						Cert: &personal_certification_pb.CertTypeInfo{
							Id:   "test_cert_type_id",
							Name: "PC开黑认证",
						},
					}, nil)

				mockPersonalCertCli.EXPECT().
					SetUserCert(gomock.Any(), gomock.Any()).
					Return(&personal_certification_pb.SetUserCertResp{}, nil)

				mockGameFreCli.EXPECT().
					IncFreCountBySource(gomock.Any(), gomock.Any()).
					Return(&game_fre_server.IncFreCountBySourceResp{}, nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// 设置默认配置
			config.SetFastPcConfig(&config.FastPcConfig{
				CertificationActivity: &config.CertificationActivityConfig{
					StartTime:               "2025-01-01 00:00:00",
					EndTime:                 "2025-12-31 23:59:59",
					StayInRoomTriggerTime:   600,
					CertTypeId:     "test_cert_type_id",
					CertExpireDay: 1,
				},
			})

			mockPersonalCertCli := personal_cert_mocks.NewMockIClient(ctrl)
			mockUserOnlineCli := user_online_mocks.NewMockIClient(ctrl)
			mockGameFreCli := game_fre_server.NewMockGameFreServerClient(ctrl)

			server := &Server{
				clients: &rpc.Clients{
					PersonalCertCli: mockPersonalCertCli,
					UserOnlineCli:   mockUserOnlineCli,
					GameFreCli:      mockGameFreCli,
				},
			}

			tt.setupMocks(mockPersonalCertCli, mockUserOnlineCli, mockGameFreCli)

			ctx := context.Background()
			err := server.handlePcCertificationActivity(ctx, tt.leaveEvent)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
*/
