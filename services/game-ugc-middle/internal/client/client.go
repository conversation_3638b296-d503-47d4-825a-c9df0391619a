package client

import (
	"context"
	accountGo "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/account_go/v1"
	"golang.52tt.com/clients/banuser"
	cybros_arbiter "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/darkserver"
	"golang.52tt.com/clients/frequence"
	game_card "golang.52tt.com/clients/game-card"
	game_pal "golang.52tt.com/clients/game-pal"
	game_ugc_content "golang.52tt.com/clients/game-ugc-content"
	iop "golang.52tt.com/clients/iop-proxy"
	rcmd_game_post "golang.52tt.com/clients/rcmd/rcmd-game-post"
	risk_mng_api "golang.52tt.com/clients/risk-mng-api"
	topic_channel "golang.52tt.com/clients/topic-channel/channel"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/clients/ugc/activity_stream"
	"golang.52tt.com/clients/ugc/attitude"
	"golang.52tt.com/clients/ugc/content"
	"golang.52tt.com/clients/ugc/following_stream"
	opeconfig "golang.52tt.com/clients/ugc/ope_config"
	"golang.52tt.com/clients/ugc/topic"
	"golang.52tt.com/clients/ugc/vote"
	user_online "golang.52tt.com/clients/user-online"
	"golang.52tt.com/pkg/log"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
)

var (
	AccountGoCli            accountGo.AccountGoClient
	FrequencyClient         frequence.IClient
	ContentClient           content.IClient
	VoteCli                 vote.IClient
	CensorsProxyClient      cybros_arbiter.IClient
	DarkCli                 darkserver.IClient
	FollowStreamBreakClient following_stream.IClient
	AttitudeClient          attitude.IClient
	ActivityStreamClient    activity_stream.IClient
	BanuserClient           banuser.IClient
	RcmdPostClient          rcmd_game_post.IClient
	GameCardClient          game_card.IClient
	TopicChannelClient      topic_channel.IClient
	UserOlClient            user_online.IClient
	TCTabClient             tcTab.IClient
	GameUgcContentClient    game_ugc_content.IClient
	IopCli                  iop.IClient
	OpeConfigClient         opeconfig.IClient
	GamePalClient           game_pal.IClient
	TopicClient             topic.IClient
	ChannelPlayTabClient    channel_play_tab.ChannelPlayTabClient
	RiskMngApiClient        risk_mng_api.IClient
)

func InitClients(ctx context.Context) {
	var err error
	AccountGoCli, err = accountGo.NewClient(ctx)
	if err != nil {
		log.WarnWithCtx(ctx, "accountGo.NewClient err: %v", err)
	}
	FrequencyClient = frequence.NewClient()
	ContentClient, _ = content.NewClient()
	VoteCli, _ = vote.NewClient()
	CensorsProxyClient = cybros_arbiter.NewIClient()
	DarkCli = darkserver.NewIClient()
	FollowStreamBreakClient, _ = following_stream.NewClient()
	AttitudeClient, _ = attitude.NewClient()
	ActivityStreamClient, _ = activity_stream.NewClient()
	BanuserClient = banuser.NewClient()
	RcmdPostClient, _ = rcmd_game_post.NewClient()
	GameCardClient, _ = game_card.NewClient()
	TopicChannelClient, _ = topic_channel.NewClient()
	UserOlClient, _ = user_online.NewClient()
	TCTabClient, err = tcTab.NewClient()
	if err != nil {
		log.Errorf("TCTabClient init fail")
	}
	GameUgcContentClient = game_ugc_content.NewIClient()
	IopCli = iop.NewIClient()
	OpeConfigClient, _ = opeconfig.NewClient()
	GamePalClient, _ = game_pal.NewClient()
	TopicClient, _ = topic.NewClient()
	ChannelPlayTabClient, _ = channel_play_tab.NewClient(ctx)
	RiskMngApiClient = risk_mng_api.NewIClient()
}
