package risk

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	game_ugc_middle "golang.52tt.com/protocol/services/game-ugc-middle"
	risk_mng_api "golang.52tt.com/protocol/services/risk-mng-api"
	contentPB "golang.52tt.com/protocol/services/ugc/content"
	"golang.52tt.com/services/game-ugc-middle/internal/client"
	"golang.52tt.com/services/game-ugc-middle/internal/common/contentfmt"
	"strings"
)

const (
	UgcPostRiskContentTypeText  = "1" // 文本
	UgcPostRiskContentTypeImage = "2" // 图片
	UgcPostRiskContentTypeVideo = "3" // 视频
	UgcPostRiskContentTypeVote  = "4" // 投票
	UgcPostRiskContentTypeLink  = "5" // 链接

	PostSourceGameDistrict = "1" // 开黑专区发帖:1
	PostSourceActivity     = "2" // 活动发帖:2
	HasPostPicture         = "1" // 包含图片或视频
	NoPostPicture          = "0" // 不包含图片或视频

	AddPostAction = "ADD_POST_ACTION" // 发帖行为风控检查场景
)

// PostRiskCheck 发帖实时风控检查
func PostRiskCheck(ctx context.Context, in *game_ugc_middle.PostPostRequest, serviceInfo metainfo.ServiceInfoReader, postId string) (riskInfo *contentPB.RiskCheckInfo, err error) {
	customParams, err := buildPostRiskParams(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "PostRiskCheck BuildPostRiskParams err: %v, postId: %s", err, postId)
		return
	}

	checkReq := &risk_mng_api.CheckReq{
		Scene: AddPostAction,
		SourceEntity: &risk_mng_api.Entity{
			Uid:          serviceInfo.UserID(),
			DeviceIdRaw:  serviceInfo.DeviceID(),
			TerminalType: serviceInfo.TerminalType(),
			ClientIp:     serviceInfo.ClientIPAddr().String(),
		},
		CustomParams: customParams,
	}
	checkResp, err := client.RiskMngApiClient.Check(ctx, checkReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "PostRiskCheck risk_mng_api.Check err: (%v), req: %+v, postId:%v", err, checkReq, postId)
		return nil, err
	}
	log.InfoWithCtx(ctx, "PostRiskCheck risk_mng_api.Check req: %+v, resp: %+v, postId:%v", checkReq, checkResp, postId)

	riskInfo = &contentPB.RiskCheckInfo{
		RiskToken: checkResp.GetRiskToken(),
		ErrCode:   checkResp.GetErrCode(),
		ErrMsg:    checkResp.GetErrMsg(),
	}

	return riskInfo, err
}

func buildPostRiskParams(ctx context.Context, post *game_ugc_middle.PostPostRequest) (map[string]string, error) {
	params := make(map[string]string)
	contentTypes := make([]string, 0)
	if post.GetOrigin() == game_ugc_middle.PostOrigin_POST_ORIGIN_GAME_ACTIVITY {
		params["post_source"] = PostSourceActivity
	} else {
		params["post_source"] = PostSourceGameDistrict
	}
	plainText := contentfmt.FormatPlainText(post.GetContent())
	if post.GetTitle() != "" {
		plainText += ";"
		plainText += post.GetTitle()
	}
	if len(plainText) > 0 {
		contentTypes = append(contentTypes, UgcPostRiskContentTypeText)
		params["post_text"] = plainText
	}

	hasImage := post.GetAttachmentImageCount() > 0
	hasVideo := post.GetAttachmentVideoCount() > 0
	if hasImage {
		contentTypes = append(contentTypes, UgcPostRiskContentTypeImage)
	}
	if hasVideo {
		contentTypes = append(contentTypes, UgcPostRiskContentTypeVideo)
	}

	if hasImage || hasVideo {
		params["post_picture"] = HasPostPicture // 包含图片或视频
	} else {
		params["post_picture"] = NoPostPicture // 不包含图片或视频
	}

	// 话题
	if post.GetTopicId() != "" {
		topic, err := client.GameUgcContentClient.GetTopicById(ctx, post.GetTopicId())
		if err != nil {
			log.ErrorWithCtx(ctx, "buildPostRiskParams GameUgcContentClient.GetTopicById err: %v, topicId: %s", err, post.GetTopicId())
			return nil, err
		}
		if topic.GetDisplayDistrictName() != "" {
			params["post_text_ext"] = fmt.Sprintf("#%s#", topic.GetDisplayDistrictName())
		}
	}

	// 投票
	if post.GetVote() != nil {
		contentTypes = append(contentTypes, UgcPostRiskContentTypeVote)
		var builder strings.Builder
		builder.WriteString(post.GetVote().GetTitle())
		for _, option := range post.GetVote().GetOptions() {
			builder.WriteString(fmt.Sprintf(";%s", option.GetContent()))
		}

		params["post_vote"] = builder.String() // 投票内容
	}
	// link_url: 预留字段 社团帖子才有
	params["link_url"] = ""

	params["content_type"] = strings.Join(contentTypes, ",")

	return params, nil
}
