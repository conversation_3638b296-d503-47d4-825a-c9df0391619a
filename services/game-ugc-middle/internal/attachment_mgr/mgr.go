package attachment_mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	account_go "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/account_go/v1"
	cybros_arbiter "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	censoring_proxy "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	pb "golang.52tt.com/protocol/services/game-ugc-middle"
	contentPB "golang.52tt.com/protocol/services/ugc/content"
	topicPb "golang.52tt.com/protocol/services/ugc/topic"
	votePb "golang.52tt.com/protocol/services/ugc/vote"
	"golang.52tt.com/services/game-ugc-middle/internal/blcheck"
	"golang.52tt.com/services/game-ugc-middle/internal/client"
	"golang.52tt.com/services/game-ugc-middle/internal/common/contentfmt"
	"golang.52tt.com/services/user/user-httplogic/models/metrics"
	"strings"
	"time"
)

const (
	timeLayout = "2006-01-02 15:04:05"

	ugcObsImageScope  = "ugc-image"
	ugcObsAudioScope  = "ugc-audio"
	ugcObsVideoScope  = "ugc-video"
	defaultVideoTrans = "videos-dst"
)

type AttachmentMgr struct {
	defaultObsPrefix string
	videoTrans       string
	blCheck          *blcheck.BlCheck
}

func NewAttachmentMgr(ctx context.Context, defaultObsPrefix, videoTrans string, isBlOpen bool) *AttachmentMgr {
	// 百灵数据统计 初始化
	var blCheck *blcheck.BlCheck
	bylinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewAttachmentMgr bylink.NewKfkCollector failed: %v", err)
	} else {
		bylink.InitGlobalCollector(bylinkCollect)
		blCheck = blcheck.NewBlCheck(isBlOpen)
	}

	if videoTrans == "" {
		videoTrans = defaultVideoTrans
	}

	return &AttachmentMgr{
		defaultObsPrefix: defaultObsPrefix,
		videoTrans:       videoTrans,
		blCheck:          blCheck,
	}
}

func (s *AttachmentMgr) MarkPostAttachmentUploaded(ctx context.Context, in *pb.MarkPostAttachmentUploadedRequest) error {
	attachments := make([]*contentPB.AttachmentInfo, len(in.GetAttachmentList()))
	for i, a := range in.GetAttachmentList() {
		attachments[i] = &contentPB.AttachmentInfo{
			Key:   a.GetAttachmentKey(),
			Type:  contentPB.AttachmentInfo_AttachmentType(uint32(a.GetAttachmentType())),
			Extra: a.GetExtraInfo(),
		}
	}

	req := &contentPB.MarkAttachmentUploadedReq{
		PostId:             in.GetPostId(),
		CommentId:          in.GetCommentId(),
		AttachmentInfoList: attachments,
	}
	_, err := client.ContentClient.MarkAttachmentUploaded(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "MarkPostAttachmentUploaded ContentClient.MarkAttachmentUploaded failed: %v", err)
		return err
	}

	uid := in.GetUid()
	audioAttachments := make([]string, 0, 100)
	audioInfo := ""
	// 鉴视频
	videoAttachments := make([]string, 0, 100)
	// 鉴图片
	imageAttachments := make([]string, 0)
	for _, attachment := range in.GetAttachmentList() {
		if attachment.AttachmentType == pb.AttachmentType_ATTACHMENT_TYPE_IMAGE ||
			attachment.AttachmentType == pb.AttachmentType_ATTACHMENT_TYPE_GIF {

			if strings.HasPrefix(attachment.GetAttachmentKey(), "images-shumei/") ||
				strings.HasPrefix(attachment.GetAttachmentKey(), "images-shumei-test/") {
				imageAttachments = append(imageAttachments, attachment.GetAttachmentKey())
			}

		} else if attachment.AttachmentType == pb.AttachmentType_ATTACHMENT_TYPE_VIDEO {
			if strings.HasPrefix(attachment.GetAttachmentKey(), "videos-shumei/") ||
				strings.HasPrefix(attachment.GetAttachmentKey(), "videos-shumei-test/") {
				videoAttachments = append(videoAttachments, attachment.GetAttachmentKey())
			}
		} else if attachment.AttachmentType == pb.AttachmentType_ATTACHMENT_TYPE_AUDIO {
			if strings.HasPrefix(attachment.GetAttachmentKey(), "audios-shumei/") ||
				strings.HasPrefix(attachment.GetAttachmentKey(), "audios-shumei-test/") {
				audioAttachments = append(audioAttachments, attachment.GetAttachmentKey())
				audioInfo = attachment.GetExtraInfo()
			}
		}
	}

	var riskErr error
	var riskCheckInfo *contentPB.RiskCheckInfo
	var getPostByIdResp *contentPB.PostInfo
	if in.GetCommentId() == "" {
		getPostByIdResp, err = client.ContentClient.GetPostById(ctx, in.GetPostId())
		if err != nil {
			log.ErrorWithCtx(ctx, "MarkPostAttachmentUploaded GetPostById err: (%v)", err)
			return err
		}
		riskCheckInfo = getPostByIdResp.GetRiskCheckInfo()
		if riskCheckInfo.GetErrCode() < 0 {
			riskErr = protocol.NewExactServerError(nil, int(riskCheckInfo.GetErrCode()), riskCheckInfo.GetErrMsg())
		}
	}
	go func() {
		tmpCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, 10*time.Second)
		defer cancel()

		if len(videoAttachments) != 0 {
			metrics.IncQiNiuCode(metrics.VideoPost)
			log.WarnWithCtx(tmpCtx, "come with video, req:%+v, postid:%v", in, in.GetPostId())
			return
		}

		user, rpcErr := client.AccountGoCli.GetUserByUid(tmpCtx, &account_go.UidReq{Uid: uid})
		if rpcErr != nil {
			log.ErrorWithCtx(tmpCtx, "MarkPostAttachmentUploaded AccountGoCli.GetUserByUid err: (%v), postid:%v", err, in.GetPostId())
			return
		}

		reqAudit := &censoring_proxy.CensoringReq{
			Context: &censoring_proxy.TaskContext{
				AppId:       cybros_arbiter.AppId,
				BelongObjId: user.GetAlias(),
				Scenes: []censoring_proxy.Scene{
					censoring_proxy.Scene_SCENE_DEFAULT,
				},
				Source: &censoring_proxy.SourceInfo{},
				UserInfo: &censoring_proxy.User{
					Id:       uint64(uid),
					Alias:    user.GetAlias(),
					Nickname: user.GetNickname(),
				},
				SceneCreateTime: time.Now().Format(timeLayout),
				RiskToken:       riskCheckInfo.GetRiskToken(),
			},
		}
		reqAudit.Callback = &censoring_proxy.Callback{
			Params: make(map[string]string),
		}
		reqAudit.Callback.Params["quicksilver"] = "new"

		var audioJson string
		if audioInfo != "" {
			extraInfo := make(map[string]interface{})
			if err := json.Unmarshal([]byte(audioInfo), &extraInfo); err != nil {
				log.ErrorWithCtx(tmpCtx, "MarkPostAttachmentUploaded Unmarshal err: (%v)", err)
			} else {
				audioJson, _ = extraInfo["audio_json"].(string)
				log.WarnWithCtx(tmpCtx, "MarkPostAttachmentUploaded audiojson : %v", audioJson)
			}
		}

		var isImage, isVideo, isAudio bool
		//构造机审人审数据
		if len(imageAttachments) != 0 {
			s.asyncImageExamine(tmpCtx, imageAttachments, uid, req.GetCommentId() != "", in.GetPostId(), in.GetCommentId(), reqAudit, in.GetIsObs())
			isImage = true
		}
		if len(videoAttachments) != 0 {
			s.asyncVideoExamine(tmpCtx, videoAttachments, uid, in.GetPostId(), reqAudit, in.GetIsObs())
			isVideo = true
		}
		if len(audioAttachments) != 0 {
			s.asyncAudioExamine(tmpCtx, audioAttachments, uid, in.GetPostId(), audioJson, reqAudit, in.GetIsObs())
			isAudio = true
		}
		if isImage {
			reqAudit.DataType = censoring_proxy.MixDataType_DATA_TYPE_IMAGE
		} else if isAudio {
			reqAudit.DataType = censoring_proxy.MixDataType_DATA_TYPE_AUDIO
		} else if isVideo {
			reqAudit.DataType = censoring_proxy.MixDataType_DATA_TYPE_VIDEO
		}

		if in.GetCommentId() == "" {
			reqAudit.Context.Source.Id = in.GetPostId()
			reqAudit.Context.BizRecordId = in.GetPostId()
			getPostByIdResp, serr := client.ContentClient.GetPostById(tmpCtx, in.GetPostId())
			if serr != nil {
				log.ErrorWithCtx(tmpCtx, "MarkPostAttachmentUploaded GetPostById err: (%v)", serr)
				return
			}

			var checkText string
			var plainText = contentfmt.FormatPlainText(getPostByIdResp.GetContent())
			checkText += plainText
			if audioJson != "" {
				checkText += audioJson
			}

			var titleAll string
			if len(getPostByIdResp.GetDiyTopicIds()) > 0 {
				log.InfoWithCtx(tmpCtx, "MarkPostAttachmentUploaded postid: %s GetTopics: %v", in.GetPostId(), getPostByIdResp.GetDiyTopicIds())
				getTopicsReq := &topicPb.GetTopicsReq{
					TopicIds: getPostByIdResp.GetDiyTopicIds(),
					Option:   topicPb.TopicQueryOptionEx_ExQueryAll,
				}
				var titles []string

				getTopicsResp, err := client.TopicClient.GetTopics(tmpCtx, getTopicsReq)
				if err != nil {
					log.ErrorWithCtx(tmpCtx, "MarkPostAttachmentUploaded GetTopics err: %v", err)
					return
				}
				for _, tmpTopicInfo := range getTopicsResp.GetTopicInfos() {
					titles = append(titles, tmpTopicInfo.Name)
				}
				titleAll = strings.Join(titles, ";")
			}
			//加完topic 加plainText
			if titleAll != "" {
				checkText += ";"
				checkText += titleAll
			}
			if getPostByIdResp.GetTitle() != "" {
				checkText += ";"
				checkText += getPostByIdResp.GetTitle()
			}

			reqAudit.Context.Category = cybros_arbiter.CategoryUgcPost
			reqAudit.Context.SourceInfo = cybros_arbiter.CategoryUgcPost
			reqAudit.TextData = append(reqAudit.TextData, &censoring_proxy.TextData{
				Metadata: &censoring_proxy.Metadata{
					DataId: getPostByIdResp.GetPostId(),
					Name:   "post",
				},
				Content: checkText,
			})

			if getPostByIdResp.GetIsVote() {
				voteResp, err := client.VoteCli.GetVote(tmpCtx, &votePb.GetVoteReq{PostId: getPostByIdResp.GetPostId()})
				if err != nil {
					log.ErrorWithCtx(tmpCtx, "MarkPostAttachmentUploaded GetVote uid(%d) postId(%s) err: %v", uid, getPostByIdResp.GetPostId(), err)
					return
				}

				multiData := &censoring_proxy.MultiMediaData{
					TextDatas: []*censoring_proxy.TextData{
						{
							Metadata: &censoring_proxy.Metadata{DataId: fmt.Sprintf("vote:%s", getPostByIdResp.GetPostId())},
							Content:  voteResp.GetVote().GetTitle(),
						},
					},
				}

				for _, option := range voteResp.GetVote().GetOptions() {
					multiData.TextDatas = append(multiData.TextDatas, &censoring_proxy.TextData{
						Metadata: &censoring_proxy.Metadata{DataId: fmt.Sprintf("option:%s", option.GetId())},
						Content:  option.GetContent(),
					})
				}

				reqAudit.DataType = censoring_proxy.MixDataType_DATA_TYPE_MULTI_MEDIA
				reqAudit.MultiMediaData = multiData
			}
		} else {
			reqAudit.Context.Source.Id = in.GetCommentId()
			reqAudit.Context.BizRecordId = in.GetCommentId()
			commentInfo, err := client.ContentClient.GetCommentById(tmpCtx, in.GetCommentId())
			if err != nil {
				return
			}
			reqAudit.Context.Category = cybros_arbiter.CategoryUgcComment
			reqAudit.Context.SourceInfo = cybros_arbiter.CategoryUgcComment
			reqAudit.TextData = append(reqAudit.TextData, &censoring_proxy.TextData{
				Metadata: &censoring_proxy.Metadata{
					DataId: in.GetCommentId(),
					Name:   "comment",
				},
				Content: contentfmt.FormatPlainText(commentInfo.GetContent()),
			})
			reqAudit.Callback.Params["postid"] = commentInfo.GetPostId()
		}
		_, err := client.CensorsProxyClient.Censoring().AsyncScanMix(tmpCtx, reqAudit)
		if err != nil {
			s.blCheck.SendPostAuditFail(uid, in.GetPostId())
			log.ErrorWithCtx(tmpCtx, "AsyncScanMix err:%v, req:%+v", err, reqAudit)
			return
		}
		if reqAudit.Context.Category == cybros_arbiter.CategoryUgcPost {
			if reqAudit.DataType == censoring_proxy.MixDataType_DATA_TYPE_IMAGE {
				metrics.IncAuditCode(metrics.AuditPostImage)
			} else if reqAudit.DataType == censoring_proxy.MixDataType_DATA_TYPE_AUDIO {
				metrics.IncAuditCode(metrics.AuditPostAudio)
			}
		}
		log.InfoWithCtx(tmpCtx, "AsyncScanMix suc, req:%+v", reqAudit)
	}()

	log.InfoWithCtx(ctx, "MarkPostAttachmentUploaded, uid: (%v), post id: (%v), isobs:%t", uid, in.GetPostId(), in.GetIsObs())

	if riskErr != nil {
		return riskErr
	}
	return nil
}

// asyncVideoExamine
func (s *AttachmentMgr) asyncVideoExamine(ctx context.Context, videos []string, uid uint32, postId string, reqAudit *censoring_proxy.CensoringReq, isObs bool) {
	log.Debugf("asyncVideoExamine videos(%s) uid(%v)", strings.Join(videos, ","), uid)

	for _, v := range videos {
		var dstVideoUrl string
		i := strings.Index(v, "/")
		if i == -1 {
			dstVideoUrl = v
		} else {
			dstVideoUrl = s.videoTrans + v[i:]
		}
		log.DebugWithCtx(ctx, "asyncVideoExamine video url before trans: (%v)", v)
		log.DebugWithCtx(ctx, "asyncVideoExamine video url: (%v)", dstVideoUrl)
		reqAudit.VideoData = &censoring_proxy.VideoData{
			Metadata: &censoring_proxy.Metadata{
				DataId: dstVideoUrl,
				Name:   "ugc-post-video",
			},
			VideoData: &censoring_proxy.VideoData_Url{
				Url: fmt.Sprintf("https://tt-ugc-v-cdnqn.52tt.com/%s", dstVideoUrl),
			},
		}
		if isObs {
			reqAudit.VideoData.VideoData = &censoring_proxy.VideoData_Url{
				Url: fmt.Sprintf("%s%s/%s", s.defaultObsPrefix, ugcObsVideoScope, dstVideoUrl),
			}
		}
	}
}

func (s *AttachmentMgr) asyncImageExamine(ctx context.Context, keys []string, uid uint32, isComment bool,
	postId string, commentId string, reqAudit *censoring_proxy.CensoringReq, isObs bool) {
	log.DebugWithCtx(ctx, "asyncImageExamine keys(%s) isComment(%v)", strings.Join(keys, ","), isComment)

	reqAudit.Context.Source.Id = postId
	if isComment {
		// IsObs IOS版本[6.10.0,6.36.0) 广场图片评论IOS客户端修复无法正常送审的异常问题，服务端兼容6.10.0之后的版本处理
		serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
		log.DebugWithCtx(ctx, "serviceInfo:%s,type:%d,version:%d", serviceInfo, serviceInfo.ClientType, serviceInfo.ClientVersion)
		if checkIsObsFixVersion(serviceInfo) {
			isObs = false
		}
		reqAudit.Context.Source.Id = commentId
	}

	imageDatas := make([]*censoring_proxy.ImageData, 0, len(keys))
	for _, key := range keys {
		imageData := &censoring_proxy.ImageData{
			Metadata: &censoring_proxy.Metadata{
				DataId: key,
				Name:   "ugc-post-image",
			},

			ImageData: &censoring_proxy.ImageData_Url{
				Url: fmt.Sprintf("https://tt-ugc-cdnqn.52tt.com/%s", key),
			},
		}
		if isObs {
			imageData.ImageData = &censoring_proxy.ImageData_Url{
				Url: fmt.Sprintf("%s%s/%s", s.defaultObsPrefix, ugcObsImageScope, key),
			}
		}
		imageDatas = append(imageDatas, imageData)

	}
	reqAudit.ImageDatas = imageDatas
}

func (s *AttachmentMgr) asyncAudioExamine(ctx context.Context, audios []string, uid uint32, postId, audioJson string, reqAudit *censoring_proxy.CensoringReq, isObs bool) {
	log.DebugWithCtx(ctx, "asyncAudioExamine audios(%s) uid(%v)", strings.Join(audios, ","), uid)

	for _, v := range audios {
		reqAudit.AudioData = &censoring_proxy.AudioData{
			Metadata: &censoring_proxy.Metadata{
				DataId: v,
				Name:   "ugc-post-audio",
			},
			AudioData: &censoring_proxy.AudioData_Url{
				Url: fmt.Sprintf("https://tt-ugc-cdnqn.52tt.com/%s", v),
			},
		}
		if isObs {
			reqAudit.AudioData.AudioData = &censoring_proxy.AudioData_Url{
				Url: fmt.Sprintf("%s%s/%s", s.defaultObsPrefix, ugcObsAudioScope, v),
			}
		}
		if audioJson != "" {
			reqAudit.AudioData.AudioText = audioJson
		}
	}
}

func checkIsObsFixVersion(serviceInfo *protogrpc.ServiceInfo) bool {
	if serviceInfo.ClientType != protocol.ClientTypeIOS {
		return false
	}
	if serviceInfo.ClientVersion >= protocol.FormatClientVersion(6, 10, 0) &&
		serviceInfo.ClientVersion < protocol.FormatClientVersion(6, 35, 0) {
		return true
	}
	return false
}
