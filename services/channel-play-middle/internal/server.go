package internal

import (
	"context"
	"github.com/golang/protobuf/ptypes/any"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	accountCpp "golang.52tt.com/clients/account"
	account "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/channel"
	channel_ktv_client "golang.52tt.com/clients/channel-ktv"
	channel_level_cli "golang.52tt.com/clients/channel-level"
	channel_open_game_controller "golang.52tt.com/clients/channel-open-game-controller"
	channel_play_tab "golang.52tt.com/clients/channel-play-tab"
	channel_scheme "golang.52tt.com/clients/channel-scheme"
	"golang.52tt.com/clients/channelmic"
	music_client "golang.52tt.com/clients/channelmusic"
	channelol_stat_go "golang.52tt.com/clients/channelol-stat-go"
	"golang.52tt.com/clients/expsvr"
	mystery_place "golang.52tt.com/clients/mystery-place"
	personal_cli "golang.52tt.com/clients/personal-certification"
	punishuser "golang.52tt.com/clients/punish-user"
	tcChannel "golang.52tt.com/clients/topic-channel/channel"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	ttc_proxy "golang.52tt.com/clients/ttc-proxy"
	userol "golang.52tt.com/clients/user-online"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/bylink"
	pkgConfig "golang.52tt.com/pkg/config"
	eventlink "golang.52tt.com/pkg/kafka_eventlink"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	permissionChecker "golang.52tt.com/protocol/services/channel-operate-permission-checker"
	pb "golang.52tt.com/protocol/services/channel-play-middle"
	"golang.52tt.com/protocol/services/demo/echo"
	music_channel "golang.52tt.com/protocol/services/music-topic-channel"
	"golang.52tt.com/services/channel-play-middle/conf"
	"golang.52tt.com/services/channel-play-middle/internal/cache"
	"golang.52tt.com/services/channel-play-middle/internal/cache/cache_client"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/filter"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/common_handle"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/common_tab_filter"
	grpcOP "google.golang.org/grpc"
)

type StartConfig struct {
	// from config file
	Env string `json:"env"`

	RcmdNilDoudiRatio uint32 `json:"rcmd_nil_doudi_ratio"` //推荐列表返回空比例时走兜底

	ChannelStatKafka *pkgConfig.KafkaConfig `json:"channel_stat_kafka"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)
	s := &Server{}
	err := conf.Parse(ctx, cfg.Env)
	if err != nil {
		log.ErrorWithCtx(ctx, "Setup config fail: %v ", err)
		return s, err
	}
	// 百灵数据统计 初始化
	bylinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to new kfk collector: %v", err)
		return s, err
	}
	bylink.InitGlobalCollector(bylinkCollect)

	opts := []grpcOP.DialOption{grpcOP.WithBlock()} // #gosec
	tCChannelClient, err := tcChannel.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "new topic-channel-client err: %v ", err)
		return s, err
	}
	tCTabClient, _ := tcTab.NewClient(opts...)
	channelClient := channel.NewClient(opts...)
	userOLClient, _ := userol.NewClient(opts...)
	punishUserCli, _ := punishuser.NewClient(opts...)
	accountClient, _ := account.NewClient(opts...)
	channelLevelClient, _ := channel_level_cli.NewClient()
	channelOlStatCli, _ := channelol_stat_go.NewClient()
	channelOpenGameCli, _ := channel_open_game_controller.NewClient()
	mysteryClient, _ := mystery_place.NewClient(opts...)

	expClient := expsvr.NewClient(opts...)
	realNameClient, _ := ttc_proxy.NewClient(opts...)
	censoringClient := censoring_proxy.NewClient(opts...)

	musicTopicChannelClient, err := music_channel.NewClient(ctx, opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "new musicTopicChannelClient err(%v)", err)
		return s, err
	}
	//greenBabaClient := greenbaba.NewClient()
	//antispamAccountClient := antispamaccount.NewClient(opts...)
	accountCppClient, err := accountCpp.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "accountCpp.NewClient err(%v)", err)
		return s, err
	}
	channelPlayTabClient, err := channel_play_tab.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "channel_play_tab.NewClient() err(%v)", err)
		return s, err
	}
	err = cache.NewCache(ctx, &cache_client.Client{
		TCTabClient:          tCTabClient,
		MysteryPlaceClient:   mysteryClient,
		ChannelPlayTabClient: channelPlayTabClient,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "cache.NewCache err(%v)", err)
		return s, err
	}
	supervisorInst, err := supervision.NewSupervisory(accountClient, expClient, realNameClient,
		censoringClient, accountCppClient, channelPlayTabClient, cfg.Env)
	if err != nil {
		return s, err
	}

	commonTabFilter := common_tab_filter.NewCommonTabFilter(supervisorInst)

	personalClient, err := personal_cli.NewClient(opts...)
	if err != nil {
		log.ErrorWithCtx(ctx, "personal_cli.NewClient err %v", err)
		return s, err
	}
	channelMicClient := channelmic.NewClient(opts...)

	channelSchemeClient := channel_scheme.NewClient(opts...)
	ktvClient, _ := channel_ktv_client.NewClient(opts...)
	channelMusicClient := music_client.NewClient()
	cdParams := &common_handle.InitCommonHandleParams{
		SupervisorInst:      supervisorInst,
		PunishUserCli:       punishUserCli,
		AccountClient:       accountClient,
		ChannelLevelClient:  channelLevelClient,
		UserOLClient:        userOLClient,
		MusicTopicClient:    musicTopicChannelClient,
		PersonalClient:      personalClient,
		ChannelMicClient:    channelMicClient,
		ChannelOlStatClient: channelOlStatCli,
		ChannelOpenGameCli:  channelOpenGameCli,
		MysteryClient:       mysteryClient,
		ChannelMusicClient:  channelMusicClient,
		KtvClient:           ktvClient,
		ChannelSchemeClient: channelSchemeClient,
		TcChannelClient:     tCChannelClient,
		ChannelClient:       channelClient,
	}
	channelCommonHandleInst := common_handle.NewChannelCommonHandle(cdParams)

	itemFilter := filter.NewItemFilter(supervisorInst)

	channelStatPro, err := eventlink.NewEventLinkKafkaProducer(cfg.ChannelStatKafka.ClientID, cfg.ChannelStatKafka.Topics, cfg.ChannelStatKafka.BrokerList())
	if err != nil {
		log.ErrorWithCtx(ctx, "event.NewEventLinkKafkaProducer kafkaBrokers(%v) topic(%v) err(%v)",
			cfg.ChannelStatKafka.BrokerList(), cfg.ChannelStatKafka.Topics, err)
		return s, err
	}

	channelItemMgr := channel_list.NewChannelItemMgr(accountClient, itemFilter, channelStatPro)

	s.tCChannelClient = tCChannelClient
	s.channelClient = channelClient
	s.channelItemMgr = channelItemMgr
	s.cd = channelCommonHandleInst
	s.commonTabFilter = commonTabFilter
	return s, nil
}

type Server struct {
	channelItemMgr  *channel_list.ChannelItemMgr
	cd              *common_handle.ChannelCommonHandle
	commonTabFilter *common_tab_filter.CommonTabFilter

	tCChannelClient tcChannel.IClient
	channelClient   channel.IClient
}

func (s *Server) ShutDown() {

}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) BatGetGameChannelViewMap(ctx context.Context, req *pb.BatGetGameChannelViewMapReq) (*pb.BatGetGameChannelViewMapResp, error) {
	out := &pb.BatGetGameChannelViewMapResp{
		ChannelViewMap: make(map[uint32]*any.Any, len(req.GetChannelIds())),
	}
	//log.InfoWithCtx(ctx, "BatGetGameChannelViewMap reqSource: %d cids:%d", req.GetReqSource(), req.GetChannelIds())
	if len(req.GetChannelIds()) == 0 {
		return out, nil
	}
	svrInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "BatGetGameChannelViewMap grpc.ServiceInfoFromContext err")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	// 1, 并发获取房间需要的数据
	commonData, err := s.cd.ChannelFollowCommonHandle(ctx, req, svrInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetGameChannelViewMap ChannelFollowCommonHandle req:%s err:%v", req.String(), err)
		if commonData != nil && commonData.SwitchOpts != nil && commonData.SwitchOpts.NeedReportData {
			commonData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_Common_Info_Err)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, commonData.ReportData)
		}
		return out, err
	}

	// 2, 生成item信息
	//房间viewMap
	out.ChannelViewMap = s.channelItemMgr.BatchGetChannelItem(ctx, svrInfo, commonData)

	log.InfoWithCtx(ctx, "BatGetGameChannelViewMap success req:%s len(out):%d", req.String(), len(out.GetChannelViewMap()))
	return out, nil
}

func (s *Server) FilterTab(ctx context.Context, req *pb.FilterTabReq) (*pb.FilterTabResp, error) {
	//log.InfoWithCtx(ctx, "FilterTab req")
	out := &pb.FilterTabResp{}
	baseInfo := &grpc.ServiceInfo{}
	baseInfo.ClientType = uint16(req.GetClientType())
	baseInfo.TerminalType = req.GetTerminalType()
	baseInfo.UserID = req.GetUid()
	baseInfo.ClientVersion = req.GetClientVersion()
	baseInfo.MarketID = req.GetMarketId()

	tabIds, catIds := s.commonTabFilter.Filter(ctx, baseInfo, req.GetChannelPkgId(), req.GetTabIds(), req.GetCategoryIds())
	out.TabIds = tabIds
	out.CategoryIds = catIds
	log.InfoWithCtx(ctx, "FilterTab req:%s tabIds:%v catIds:%v, serviceinfo:%s", req.String(), tabIds, catIds, baseInfo.String())
	return out, nil
}

// GetFastPcSupportTabList 获取房间支持的tab列表
func (s *Server) GetFastPcSupportTabList(ctx context.Context, req *pb.GetFastPcSupportTabListReq) (*pb.GetFastPcSupportTabListResp, error) {
	out := &pb.GetFastPcSupportTabListResp{}

	svrInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetFastPcSupportTabList grpc.ServiceInfoFromContext err")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	supportTabs, err := s.commonTabFilter.GetFastPcSupportTabList(ctx, req.GetNeedTabFilter(), svrInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFastPcSupportTabList GetFastPcSupportTabList err:%v", err)
		return out, err
	}
	out.Tabs = supportTabs

	return out, nil
}

// CheckOperatePermission PC实现进房管理判断
func (s *Server) CheckOperatePermission(ctx context.Context,
	req *permissionChecker.CheckOperatePermissionReq) (out *permissionChecker.CheckOperatePermissionResp, err error) {
	out = &permissionChecker.CheckOperatePermissionResp{}

	svrInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CheckOperatePermission grpc.ServiceInfoFromContext err, req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	if protocol.IsFastPcClientType(uint32(svrInfo.ClientType)) {
		out.PermissionErrCode, out.PermissionErrMsg, err = s.commonTabFilter.FastPcEnterRoomCheck(ctx, svrInfo, req)
	}

	log.DebugWithCtx(ctx, "CheckOperatePermission req:%+v, out:%+v", req, out)
	return
}
