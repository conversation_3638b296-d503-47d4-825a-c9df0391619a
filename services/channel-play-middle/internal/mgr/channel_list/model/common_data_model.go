package model

import (
	"golang.52tt.com/clients/account-go"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/channel-play"
	music_topic_channel_logic "golang.52tt.com/protocol/app/music-topic-channel"
	"golang.52tt.com/protocol/common/status"
	ktvpb "golang.52tt.com/protocol/services/channel-ktv"
	"golang.52tt.com/protocol/services/channel-level"
	gamePb "golang.52tt.com/protocol/services/channel-open-game-controller"
	pb "golang.52tt.com/protocol/services/channel-play-middle"
	"golang.52tt.com/protocol/services/channel-scheme"
	channelPB "golang.52tt.com/protocol/services/channelsvr"
	"golang.52tt.com/protocol/services/music-topic-channel"
	mystery_place "golang.52tt.com/protocol/services/mystery-place"
	"golang.52tt.com/protocol/services/personalcertification"
	tcPb "golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/protocol/services/user-online"
	"golang.52tt.com/services/channel-play-middle/conf"
)

// 构建密室逃脱view需要的信息
type MysteryViewInfo struct {
	//密室逃脱游戏状态
	GameStatusInfoMap map[uint32]*gamePb.ChannelGameStatusInfo
	//密室逃脱游戏进度
	RoomGamePercentMap map[uint32]*mystery_place.RoomGamePercent
}

// 构建ktv View需要的信息
type KtvViewInfo struct {
	//房间播放音乐信息
	PlayingMusicList map[uint32]string
	//一起k歌房间歌单信息
	KtvPlayMusicStatus map[uint32]*ktvpb.PlayListStatus
}

// 构建音乐marshalView需要的信息
type MarshalViewInfo struct {
	//marshalView内容
	MarshalChannelInfoMap map[uint32][]byte
}

type ChannelCommonHandleData struct {
	ReqSource     pb.ReqSource
	ListStyle     channel_play.ChannelListStyleType
	EnterSource   channel_play.ChannelListEnterSource
	GenViewSource []uint32
	SwitchOpts    *GenViewSwitch
	ReportData    *blreport.ChannelList
	ChannelPkg    string

	ChannelInfoMap        map[uint32]*channelPB.ChannelSimpleInfo
	TcInfoMap             map[uint32]*tcPb.ChannelInfo
	RcmdInfo              *RcmdInfo
	UgcChannelIds         []uint32
	MarshalViewChannelIds []uint32                                //音乐marshalView房间
	MobaViewChannelIds    []uint32                                //mobaView房间
	DefaultViewChannelIds []uint32                                //defaultView房间
	MysteryViewChannelIds []uint32                                //密室逃脱房间
	KtvViewChannelIds     []uint32                                //ktvView房间
	MusicViewChannelIds   []uint32                                //musicView房间
	ChannelViewMap        map[uint32]channel_play.ChannelViewType // key 房间id value viewType
	OtherChannelIds       []uint32                                // 非ugc房间id列表

	PunishMap map[uint32]bool
	UserMap   map[uint32]*account_go.User
	LevelInfo map[uint32]*channel_level.GetChannelInfoResp
	MemberMap map[uint32]uint32
	UserOLMap map[uint32]*user_online.OnlineInfo
	//用户个人认证标签
	PersonalCertMap map[uint32]*personalcertification.PersonalCertificationInfoList
	//房间标签（优质房间，热门房间）
	ChannelLabelMap map[uint32]music_topic_channel.QualityType

	//卡片外显用户头像信息
	UserOnMicImgMap map[uint32][]*channel_play.UserInfoForImg
	//房间麦上人数key cid value 人数
	UserOnMicNumMap map[uint32]uint32

	//房间玩法类型信息
	ChannelSchemeInfoMap map[uint32]*channel_scheme.ChannelSchemeInfo
	//房间id外显文案
	IdDescMap map[uint32]string

	//构建密室逃脱view需要的信息
	MysteryInfo *MysteryViewInfo
	//构建ktv View需要的信息
	KtvInfo *KtvViewInfo
	//构建marshalView需要的信息
	MarshalViewInfo *MarshalViewInfo

	// 音乐兴趣社群房需要的信息
	MusicChannelInfoMap map[uint32]*music_topic_channel_logic.MusicChannel

	//tab未成年过滤配置,key:tabId value:是否过滤
	TabFilter map[uint32]bool
	//分类未成年过滤配置,key:categoryId value:是否过滤
	CategoryFilter map[uint32]bool
}

func (u *ChannelCommonHandleData) GetNeedInfoChannels() []uint32 {
	//source包含音乐，不需要获取音乐房间信息
	needInfoChannels := make([]uint32, 0, len(u.DefaultViewChannelIds)+len(u.MysteryViewChannelIds)+
		len(u.MobaViewChannelIds)+len(u.KtvViewChannelIds))
	needInfoChannels = append(needInfoChannels, u.DefaultViewChannelIds...)
	needInfoChannels = append(needInfoChannels, u.MysteryViewChannelIds...)
	needInfoChannels = append(needInfoChannels, u.MobaViewChannelIds...)
	needInfoChannels = append(needInfoChannels, u.KtvViewChannelIds...)
	needInfoChannels = append(needInfoChannels, u.MusicViewChannelIds...)
	return needInfoChannels
}

func (u *ChannelCommonHandleData) GetCreators() []uint32 {
	creators := make([]uint32, 0, len(u.ChannelInfoMap))
	for _, channelInfo := range u.ChannelInfoMap {
		creators = append(creators, channelInfo.GetBindId())
	}
	return creators
}

func (u *ChannelCommonHandleData) GetCid2CreatorMap() map[uint32]uint32 {
	cid2CreatorMap := make(map[uint32]uint32, len(u.ChannelInfoMap))
	for _, channelInfo := range u.ChannelInfoMap {
		cid2CreatorMap[channelInfo.GetChannelId()] = channelInfo.GetBindId()
	}
	return cid2CreatorMap
}

func (u *ChannelCommonHandleData) GetChannelTabIds() (map[uint32]uint32, error) {
	channelTabIdMap := make(map[uint32]uint32)

	if u.TcInfoMap == nil {
		return nil, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}
	for _, info := range u.TcInfoMap {
		channelTabIdMap[info.GetId()] = info.GetTabId()
	}
	return channelTabIdMap, nil
}

func (u *ChannelCommonHandleData) GetMusicIds() []uint32 {
	findMusicInfoChannelIds := make([]uint32, 0)
	for _, cid := range u.GetNeedInfoChannels() {
		info, ok := u.TcInfoMap[cid]
		if !ok {
			continue
		}
		if conf.ChannelPlayConfig.LoadConfig().ShowMusicInfoTab[info.GetTabId()] {
			findMusicInfoChannelIds = append(findMusicInfoChannelIds, info.GetId())
		}
	}
	return findMusicInfoChannelIds
}

func (u *ChannelCommonHandleData) GetKtvIds() []uint32 {
	findKTVInfoChannelIds := make([]uint32, 0)
	for _, cid := range u.GetNeedInfoChannels() {
		info, ok := u.TcInfoMap[cid]
		if !ok {
			continue
		}
		if conf.PublicSwitchConfig.IsKTVTab(info.GetTabId()) {
			findKTVInfoChannelIds = append(findKTVInfoChannelIds, info.GetId())
		}
	}
	return findKTVInfoChannelIds
}

func (u *ChannelCommonHandleData) GetMarshalViewChannelIdMap() map[uint32]bool {
	marshalViewChannelIdMap := make(map[uint32]bool, len(u.MarshalViewChannelIds))
	for _, channelId := range u.MarshalViewChannelIds {
		marshalViewChannelIdMap[channelId] = true
	}
	return marshalViewChannelIdMap
}

func (u *ChannelCommonHandleData) GetUgcChannelIdMap() map[uint32]bool {
	ugcChannelIdMap := make(map[uint32]bool, len(u.UgcChannelIds))
	for _, channelId := range u.UgcChannelIds {
		ugcChannelIdMap[channelId] = true
	}
	return ugcChannelIdMap
}
