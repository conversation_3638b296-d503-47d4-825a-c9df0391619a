package view

import (
	"context"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	channel_scheme "golang.52tt.com/protocol/app/channel-scheme"
	channelschemepb "golang.52tt.com/protocol/services/channel-scheme"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-middle/conf"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/model"
)

const (
	BlockNoLimit        = "不限"
	InPutModeMaxElemNum = 4
)

type BaseViewHandlerI interface {
	GenChannelView(ctx context.Context, channelId uint32, svrInfo *grpc.ServiceInfo,
		commonData *model.ChannelCommonHandleData) (*channel_play.TopicChannelView, uint32)
}

func GetRoomCondition(blockOptions []*channelPB.BlockOption, tabInfo *tabPB.Tab) string {
	var condition string
	specialCondition := tabInfo.SpecialChannelCondition
	if specialCondition == nil {
		return tabInfo.DefaultChannelCondition
	}
	//用户选择了的字段
	userSelectedBlockMap := initUserChoseBlockIdMap(blockOptions)
	for blockId, elemMap := range userSelectedBlockMap {
		conditionMap, ok := specialCondition[blockId]
		if !ok {
			continue
		}
		if len(elemMap) > 1 {
			//多选,展示默认状态
			condition = tabInfo.DefaultChannelCondition
			return condition
		} else {
			for elemId := range elemMap {
				specialCon, ok := conditionMap.Condition[elemId]
				if ok {
					//用户选的elem，有配置个性化状态
					condition = specialCon
					return condition
				}
			}
		}
		//找到个性化状态，退出for
		break
	}
	//没有找到个性化状态，返回默认状态
	if condition == "" {
		condition = tabInfo.DefaultChannelCondition
	}
	return condition
}

// GenDescFirst 根据运营后台配置置顶字段生成第一行展示信息
func GenDescFirst(tabInfo *tabPB.Tab, vBlockHandler *VBlockHandler,
	commonData *model.ChannelCommonHandleData, schemeInfo *channelschemepb.ChannelSchemeInfo) (
	[]*channel_play.ChannelViewMOBA_DescAndColor, string) {
	style := commonData.ListStyle
	blocks := vBlockHandler.blocks
	enterSource := commonData.EnterSource
	var tabNameLabel string
	var wordChannelLabel *channel_play.ChannelViewMOBA_DescAndColor
	//mobaTitleMap := vBlockHandler.mobaTitleMap
	userSelectedMap := vBlockHandler.userSelectedBlockMap
	topTitles := vBlockHandler.topTitles
	if channel_scheme.SchemeDetailType(schemeInfo.GetSchemeDetailType()) == channel_scheme.SchemeDetailType_SCHEME_DETAIL_TYPE_GAME_WORD {
		wordChannelLabel = conf.ChannelPlayConfig.GetWordChannelLabel()
	}
	//qufu := v.genDescAndColor(playServer, mobaTitleMap[playServer], style)
	//mode := v.genDescAndColor(playMode, mobaTitleMap[playMode], style)
	//renshu := v.genDescAndColor(playerNum, mobaTitleMap[playerNum], style)

	var (
		topDesc         = make([]*channel_play.ChannelViewMOBA_DescAndColor, 0, len(topTitles))
		allTopDesc      = make([]*channel_play.ChannelViewMOBA_DescAndColor, 0, len(topTitles)+1)
		afterTabNameVal string
	)

	topDesc = append(topDesc, wordChannelLabel)
	allTopDesc = append(allTopDesc, wordChannelLabel)
	for _, title := range topTitles {
		switch title.ShowPosition {
		case tabPB.Block_TopAfterTabName:
			afterTabNameVal = title.Val
			allTopDesc = append(allTopDesc, genDescAndColor(title.Key, title.Val, style, enterSource))
		case tabPB.Block_Top:
			topDesc = append(topDesc, genDescAndColor(title.Key, title.Val, style, enterSource))
			allTopDesc = append(allTopDesc, genDescAndColor(title.Key, title.Val, style, enterSource))
		}
	}

	// 加上新增字段的展示
	userInputDescFirst := GetUserInputDescFirst(blocks, userSelectedMap, style, enterSource)

	switch commonData.EnterSource {
	case channel_play.ChannelListEnterSource_MixHomePageSource, channel_play.ChannelListEnterSource_PcFastHallSource,
		channel_play.ChannelListEnterSource_PcFastHomePageSource:

		tabNameLabel = tabInfo.GetTabAliasName()
		if afterTabNameVal != "" {
			tabNameLabel = tabInfo.GetTabAliasName() + "·" + afterTabNameVal
		}
		desc := append(topDesc, userInputDescFirst...)
		return genDesc(desc...), tabNameLabel
		//if value := mobaTitleMap[playerNum]; value != "" {
		//   tabNameLabel = tabInfo.GetTabAliasName() + "·" + value
		//}
		//desc := []*channel_play.ChannelViewMOBA_DescAndColor{wordChannelLabel, qufu, mode}
		//desc = append(desc, userInputDescFirst...)
		//return v.genDesc(desc...), tabNameLabel
	case channel_play.ChannelListEnterSource_GameZoneSource:
		tabNameLabel = ""
		if tabInfo.GetIsMinorityGame() {
			tabNameLabel = tabInfo.GetName()
		}
		//desc := []*channel_play.ChannelViewMOBA_DescAndColor{wordChannelLabel, qufu, mode, renshu}
		//desc = append(desc, userInputDescFirst...)
		desc := append(allTopDesc, userInputDescFirst...)
		return genDesc(desc...), tabNameLabel
	default:
		tabNameLabel = tabInfo.GetName()
		//desc := []*channel_play.ChannelViewMOBA_DescAndColor{wordChannelLabel, qufu, mode, renshu}
		//desc = append(desc, userInputDescFirst...)
		desc := append(allTopDesc, userInputDescFirst...)
		return genDesc(desc...), tabNameLabel
	}

}

func genDesc(mobaElems ...*channel_play.ChannelViewMOBA_DescAndColor) (
	res []*channel_play.ChannelViewMOBA_DescAndColor) {
	for _, elem := range mobaElems {
		if elem != nil {
			res = append(res, elem)
		}
	}
	return
}

func handleMobaDescValue(key, val string) string {
	if val == "不限" {
		val += key
	}
	return val
}

func genDescAndColor(key, val string, style channel_play.ChannelListStyleType,
	enterSource channel_play.ChannelListEnterSource) (res *channel_play.ChannelViewMOBA_DescAndColor) {
	if val == "" {
		return nil
	}
	res = &channel_play.ChannelViewMOBA_DescAndColor{
		Desc: handleMobaDescValue(key, val),
	}
	// 欢游置顶标签全都高亮
	if enterSource == channel_play.ChannelListEnterSource_PcFastHomePageSource ||
		enterSource == channel_play.ChannelListEnterSource_PcFastHallSource ||
		style != channel_play.ChannelListStyleType_Mix_Business_Home_Page /* && key != playerNum */ {
		res.MarkBlue = true
	} else {
		res.MarkBlue = false
	}
	return
}

func GetUserInputDescFirst(blocks []*tabPB.Block, userSelectedMap map[uint32]map[uint32]BlockOptionVal,
	style channel_play.ChannelListStyleType, enterSource channel_play.ChannelListEnterSource) []*channel_play.ChannelViewMOBA_DescAndColor {
	// 加上新增字段的展示
	userInputDescFirst := make([]*channel_play.ChannelViewMOBA_DescAndColor, 0, 2)
	for _, b := range blocks {
		if b.GetMode() != tabPB.Block_USER_INPUT {
			continue
		}
		if userSelectedMap[b.GetId()] == nil {
			continue
		}

		for _, e := range b.GetElems() {
			if !userSelectedMap[b.GetId()][e.GetId()].IsSelectEd || userSelectedMap[b.GetId()][e.GetId()].Val == "" {
				continue
			}
			for _, f := range e.GetPublicFlags() {
				//log.Debugf("GetUserInputDescFirst, blockId:%d, elemId:%d, flag:%v", b.GetId(), e.GetId(), f)
				if f == tabPB.Elem_PublishFlagTopCardMode {
					userInputDescFirst = append(userInputDescFirst, genDescAndColor(e.GetTitle(),
						e.GetTitle()+":"+userSelectedMap[b.GetId()][e.GetId()].Val, style, enterSource))
					break
				}
			}
		}
	}
	return userInputDescFirst
}
