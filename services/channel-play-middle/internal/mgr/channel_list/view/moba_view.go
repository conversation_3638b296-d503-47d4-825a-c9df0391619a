package view

import (
	"context"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	channel_play_middle "golang.52tt.com/protocol/services/channel-play-middle"
	"golang.52tt.com/services/channel-play-middle/internal/cache"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/model"
)

const (
	playServer = "区服"
	playMode   = "模式"
	playerNum  = "人数"
)

type MobaView struct {
}

func NewMobaView() *MobaView {
	mobaView := &MobaView{}
	return mobaView
}
func (v *MobaView) GenChannelView(ctx context.Context, channelId uint32, svrInfo *grpc.ServiceInfo,
	commonData *model.ChannelCommonHandleData) (*channel_play.TopicChannelView, uint32) {
	channelTabMap, err := commonData.GetChannelTabIds()
	if err != nil {
		return nil, uint32(blreport.F_ID_ChannelPush_ChannelTab_NotExist)
	}

	channelInfo, ok := commonData.TcInfoMap[channelId]
	if !ok {
		log.WarnWithCtx(ctx, "ListTopicChannel defaultView GenViewInfo channel id %v channelInfo no find", channelId)
		return nil, uint32(blreport.F_ID_ChannelPush_GetChannelInfo_Fail)
	}

	tabId, ok := channelTabMap[channelId]
	if !ok {
		log.WarnWithCtx(ctx, "ListTopicChannel mobaView GenViewInfo channel id %v tabInfo no find", channelId)
		return nil, uint32(blreport.F_ID_ChannelPush_ChannelTab_NotExist)
	}

	tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(tabId)
	if tabInfo == nil {
		log.WarnWithCtx(ctx, "ListTopicChannel mobaView GenViewInfo GetTabInfoCacheById id %v tabInfo no find", channelId)
		return nil, uint32(blreport.F_ID_ChannelPush_TabCache_NotExist)
	}

	mobaView := &channel_play.ChannelViewMOBA{
		//运营后台全局配置最近在玩配图
		CornerImg:   tabInfo.GetSmallCardUrl(),
		CornerBgImg: tabInfo.GetMaskLayer(),
	}
	//房间状态
	mobaView.RoomCondition = GetRoomCondition(channelInfo.GetBlockOptions(), tabInfo)

	blocks := cache.GetTabInfoCache().GetBaseBlocksMap()[tabInfo.GetId()]
	vBlockHandler := NewVBlockHandler(
		channelInfo.GetBlockOptions(),
		blocks,
		channel_play.ChannelViewType_ChannelView_MOBA,
		commonData.ListStyle,
		svrInfo)

	// 搜索来源发布中的才显示发布信息
	if (commonData.ReqSource == channel_play_middle.ReqSource_REQ_SOURCE_SEARCH_BY_NAME ||
		commonData.ReqSource == channel_play_middle.ReqSource_REQ_SOURCE_SEARCH_BY_ID) && !IsDisplayChannel(channelInfo.GetDisplayType()) {
		mobaView.TabName = tabInfo.GetTabAliasName()
		if desc, ok := commonData.IdDescMap[channelId]; ok {
			mobaView.DescList = []string{desc}
		}
	} else {
		//获取block 展示文案
		//生成卡片第一行的信息（区分enterSource,listStyleType，包括TabName， 区服段位人数
		mobaView.DescFirst, mobaView.TabName = GenDescFirst(tabInfo, vBlockHandler,
			commonData, commonData.ChannelSchemeInfoMap[channelId])
		mobaView.DescList = vBlockHandler.GetDescListByBusiness(tabInfo, commonData.IdDescMap[channelId])
	}
	//log.InfoWithCtx(ctx, "ListTopicChannel mobaView GenViewInfo channelId:%d, tabId:%d, tabName:%s, descFirst:%v, descList:%v",
	//	channelId, tabId, mobaView.TabName, mobaView.DescFirst, mobaView.DescList)

	mobaView.AudienceInfo = commonData.UserOnMicImgMap[channelId]

	return &channel_play.TopicChannelView{
		ChannelView: &channel_play.TopicChannelView_ViewMoba{ViewMoba: mobaView},
	}, uint32(blreport.F_ID_ChannelPush_Suc)

}
