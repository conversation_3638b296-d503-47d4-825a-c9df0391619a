package channel_item

import (
	"context"
	"golang.52tt.com/pkg/log"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	rcmdCommon "golang.52tt.com/protocol/services/rcmd/common"
	tcPb "golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/services/channel-play-middle/conf"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/model"
	"regexp"
)

const (
	locationPrefix   = "同在"
	locationIpPrefix = "ip:"

	gangUpWithHomeOwner = "一起玩过"
	chatWithHomeOwner   = "一起聊过"
	ownerIsFollowed     = "关注的人"
)

type RcmdInfoAssembler struct {
}

func NewRcmdInfoAssembler() *RcmdInfoAssembler {
	return &RcmdInfoAssembler{}
}

type RcmdItemInfo struct {
	traceInfo           *topic_channel.RecommendationTraceInfo
	rcmdLabel           channel_play.RCMDLabelEnum
	underHeadImgLabel   string
	middlePositionLabel string
	geoInfo             string
}

func (c *RcmdInfoAssembler) GenRcmdItemInfo(ctx context.Context, channelId uint32,
	commonData *model.ChannelCommonHandleData) *RcmdItemInfo {
	res := &RcmdItemInfo{}

	//推荐传的值
	info := commonData.RcmdInfo.GetRcmdChannelInfo(channelId)
	if info == nil {
		return res
	}
	cinfo := commonData.TcInfoMap[channelId]

	res.traceInfo = &topic_channel.RecommendationTraceInfo{}
	res.traceInfo.RecallFlag = info.GetRecallFlag()
	res.rcmdLabel = c.getOldRcmdLabelInfo(info.GetRcmdLabels())
	res.underHeadImgLabel = c.getUnderHeadImgLabel(ctx, info, cinfo, commonData.RcmdInfo)
	res.middlePositionLabel = c.getMiddlePositionLabel(channelId, commonData.RcmdInfo)

	res.geoInfo = c.genGeoInfo(ctx, info, cinfo, commonData.RcmdInfo)
	log.DebugWithCtx(ctx, "fillCommonInfo cid:%d RcmdLabel:%v UnderHeadImgLabel:%s MiddlePositionLabel:%s item.GeoInfo:%s", channelId,
		res.rcmdLabel, res.underHeadImgLabel, res.middlePositionLabel, res.geoInfo)

	return res
}

func (c *RcmdInfoAssembler) getMiddlePositionLabel(channelId uint32, rcmdInfo *model.RcmdInfo) string {
	return rcmdInfo.GetRcmdChannelInfo(channelId).GetDisplayContent()

}

func (c *RcmdInfoAssembler) getOldRcmdLabelInfo(rcmdLabel []rcmdCommon.RCMDLabel) (oldLabel channel_play.RCMDLabelEnum) {
	for _, v := range rcmdLabel {
		//开黑标签优先展示
		if v == rcmdCommon.RCMDLabel_GangUpWithHomeOwner {
			oldLabel = channel_play.RCMDLabelEnum_RCMDLabel_GangUpWithHomeOwner
			break
		} else if v == rcmdCommon.RCMDLabel_ChatWithHomeOwner {
			oldLabel = channel_play.RCMDLabelEnum_RCMDLabel_ChatWithHomeOwner
		} else {
			continue
		}
	}
	return oldLabel
}

func (c *RcmdInfoAssembler) getUnderHeadImgLabel(ctx context.Context, info *rcmdCommon.ChannelInfo, cinfo *tcPb.ChannelInfo,
	rcmdInfo *model.RcmdInfo) (labelText string) {
	//rcmdLabels := info.GetRcmdLabels()
	//priorityMap := conf.ChannelPlayConfig.GetRcmdLabelPriorityMap()
	//var label rcmdCommon.RCMDLabel
	//var priority int
	//for _, v := range rcmdLabels {
	//	if priorityMap[v] > priority {
	//		label = v
	//		priority = priorityMap[v]
	//	}
	//}
	//if label == rcmdCommon.RCMDLabel_LocShow {
	//	labelText = c.genGeoInfo(ctx, info, cinfo, rcmdInfo)
	//} else {
	//	// 头像下面只保留IP位置信息
	//	labelText = ""
	//	//labelText = genDisplayLabelExceptLoc(label)
	//}
	//
	//return labelText
	return c.genGeoInfo(ctx, info, cinfo, rcmdInfo)
}

var locationReg = regexp.MustCompile(`省$|市$|特别行政区$|地区$|维吾尔自治区$|壮族自治区$|回族自治区$|自治区$`)

func trimLocationInfo(locationInfo string) string {
	return locationReg.ReplaceAllString(locationInfo, "")
}

func printLocationInfo(ctx context.Context, userLocation, channelLocation *rcmdCommon.LocationInfo, locShowType rcmdCommon.ChannelInfo_LocShowType) string {
	log.DebugWithCtx(ctx, "printLocationInfo locShowType(%s) userLocation(%v) channelLocation(%v)", locShowType, userLocation, channelLocation)
	isSensitiveProvince := conf.PublicSwitchConfig.IsSensitiveProvince(channelLocation.GetProvince())
	isSensitiveCity := conf.PublicSwitchConfig.IsSensitiveCity(channelLocation.GetCity())

	if locShowType == rcmdCommon.ChannelInfo_LocShowType_DEFAULT {
		//if userLocation.GetCityCode() != 0 && userLocation.GetCityCode() == channelLocation.GetCityCode() {
		//	if !isSensitiveCity && !isSensitiveProvince {
		//		return locationPrefix + userLocation.GetCity()
		//	}
		//}
		//if userLocation.GetProvinceCode() != 0 && userLocation.GetProvinceCode() == channelLocation.GetProvinceCode() {
		//	if !isSensitiveProvince {
		//		return locationPrefix + userLocation.GetProvince()
		//	}
		//}

		// 直接不外显
		return ""
	}
	if locShowType == rcmdCommon.ChannelInfo_LocShowType_PROVINCE {
		if channelLocation.GetProvinceCode() != 0 && !isSensitiveProvince {
			return locationIpPrefix + channelLocation.GetProvince()
		}
	}
	if locShowType == rcmdCommon.ChannelInfo_LocShowType_CITY {
		if channelLocation.GetCityCode() != 0 && !isSensitiveCity && !isSensitiveProvince {
			return locationIpPrefix + channelLocation.GetCity()
		}
	}

	return ""
}

func (c *RcmdInfoAssembler) genGeoInfo(ctx context.Context, info *rcmdCommon.ChannelInfo, cinfo *tcPb.ChannelInfo, rcmdInfo *model.RcmdInfo) string {
	var geoInfo string
	if info.GetIsNewUserUndertake() { // 萌新承接房标识
		geoInfo = conf.ChannelPlayConfig.GetNewBieChannelText()
	} else if cinfo.GetShowGeoInfo() { // 地理位置信息
		geoText := printLocationInfo(ctx, rcmdInfo.GetRcmdLoc(), info.GetLoc(), info.GetLocShowType())
		geoInfo = trimLocationInfo(geoText)
	} else {
		log.DebugWithCtx(ctx, "fillCommonInfo GeoInfo empty")
	}

	return geoInfo
}

func genDisplayLabelExceptLoc(label rcmdCommon.RCMDLabel) string {

	switch label {
	case rcmdCommon.RCMDLabel_GangUpWithHomeOwner:
		return gangUpWithHomeOwner
	case rcmdCommon.RCMDLabel_ChatWithHomeOwner:
		return chatWithHomeOwner
	case rcmdCommon.RCMDLabel_FollowUserInChannel:
		return ownerIsFollowed
	default:
		return ""
	}

}
