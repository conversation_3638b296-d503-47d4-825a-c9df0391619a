package channel_item

import (
	"context"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	"golang.52tt.com/protocol/services/personalcertification"
	"golang.52tt.com/services/channel-play-middle/conf"
	"golang.52tt.com/services/channel-play-middle/internal/cache"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/model"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/view"
	"golang.52tt.com/services/channel-play-middle/internal/utils"
)

type CommonInfoAssembler struct {
	viewFactory *view.ViewFactory
}

func NewBaseInfoAssembler(viewFactory *view.ViewFactory) *CommonInfoAssembler {
	return &CommonInfoAssembler{
		viewFactory: viewFactory,
	}
}

func (c *CommonInfoAssembler) GenPgcBaseItem(ctx context.Context, commonData *model.ChannelCommonHandleData,
	cid uint32, svrInfo *grpc.ServiceInfo) (*channel_play.TopicChannelItem, uint32) {
	item := &channel_play.TopicChannelItem{
		ChannelId: cid,
	}
	viewInfo, ret := c.viewFactory.GetHandler(commonData.ChannelViewMap[cid]).GenChannelView(ctx, cid, svrInfo, commonData)
	if ret != uint32(blreport.F_ID_ChannelPush_Suc) {
		log.ErrorWithCtx(ctx, "GenChannelView cid:%d viewInfo not found ret: %v", cid, ret)
		if commonData.SwitchOpts.NeedReportData {
			commonData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_View_Err)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, commonData.ReportData)
		}
		return nil, ret
	}
	item.Source = channel_play.GenViewSource_FROM_MUSIC
	item.TopicChannelView = viewInfo
	return item, 0
}

func (c *CommonInfoAssembler) GenBaseItem(ctx context.Context, commonData *model.ChannelCommonHandleData,
	cid uint32, svrInfo *grpc.ServiceInfo) (*channel_play.TopicChannelItem, uint32) {
	item := &channel_play.TopicChannelItem{
		ChannelId: cid,
	}
	channelId := item.GetChannelId()
	item.OwnerUid = commonData.ChannelInfoMap[channelId].GetBindId()
	cinfo, ok := commonData.TcInfoMap[channelId]
	if !ok {
		log.WarnWithCtx(ctx, "ListTopicChannel fillTopicChannelItemInfo filter channel %v no channelInfo", channelId)
		return item, uint32(blreport.F_ID_ChannelPush_GetChannelInfo_Fail)
	}
	item.TabId = cinfo.GetTabId()
	item.CategoryType = cache.GetTabInfoCache().GetTabInfoCacheById(cinfo.GetTabId()).GetCategoryMapping()

	viewInfo, ret := c.viewFactory.GetHandler(commonData.ChannelViewMap[cid]).GenChannelView(ctx, cid, svrInfo, commonData)
	if ret != uint32(blreport.F_ID_ChannelPush_Suc) {
		log.ErrorWithCtx(ctx, "GenChannelView cid:%d viewInfo not found ret: %v", cid, ret)
		if commonData.SwitchOpts.NeedReportData {
			commonData.ReportData.SetFilterlType(blreport.F_ID_ChannelList_View_Err)
			blreport.GetBlInst(conf.PublicSwitchConfig.GetIsBlReportOpen()).SendChannelListInfo(ctx, commonData.ReportData)
		}
		return nil, ret
	}
	item.TopicChannelView = viewInfo

	switch item.GetTopicChannelView().GetChannelView().(type) {
	case *channel_play.TopicChannelView_ViewMarshal:
		item.Source = channel_play.GenViewSource_FROM_MUSIC
	case *channel_play.TopicChannelView_ViewNewMusic:
		item.Source = channel_play.GenViewSource_FROM_MT_GAME
	default:
		item.Source = channel_play.GenViewSource_FROM_GAME
	}

	if l, ok := commonData.LevelInfo[channelId]; ok {
		item.LevelId = l.GetLevel().GetId()
	}

	// 房间麦上人数
	if commonData.UserOnMicNumMap != nil {
		item.OnMicMemberCount = commonData.UserOnMicNumMap[channelId]
		//switch item.GetTopicChannelView().GetChannelView().(type) {
		//case *channel_play.TopicChannelView_ViewMoba, *channel_play.TopicChannelView_ViewNewMusic:
		//	item.OnMicMemberCount = commonData.UserOnMicNumMap[channelId]
		//default:
		//	if protocol.IsFastPcClientType(uint32(svrInfo.ClientType)) {
		//		item.OnMicMemberCount = commonData.UserOnMicNumMap[channelId]
		//	}
		//}
	}

	if item.GetSource() == channel_play.GenViewSource_FROM_MUSIC {
		return item, 0
	}
	//房间信息
	item.TabName = c.genTabName(item, commonData.ListStyle)
	item.ChannelName = commonData.ChannelInfoMap[channelId].GetName()
	//房主信息
	ownerInfo, ok := commonData.UserMap[item.GetOwnerUid()]
	if !ok {
		log.WarnWithCtx(ctx, "ListTopicChannel fillTopicChannelItemInfo filter channel %v no owner %d info",
			channelId, item.GetOwnerUid())
		return item, uint32(blreport.F_ID_ChannelPush_GetChannelInfo_Fail)
	}
	item.ChannelOwnerSex = utils.TransferSex(ownerInfo.GetSex())
	item.ChannelOwnerUsername = ownerInfo.GetUsername()

	// 谜境弹窗房间列表-他们在等人一起玩, 房主版本判断
	if commonData.UserOLMap != nil &&
		commonData.UserOLMap[item.GetOwnerUid()].GetClientVersion() < protocol.FormatClientVersion(6, 15, 0) {
		item.OwnerMismatchVersion = true
	}

	//房间人数
	if commonData.MemberMap != nil {
		item.MemberCount = commonData.MemberMap[channelId]
	}
	if item.MemberCount == 0 {
		//房间人数为0打印日志
		log.WarnWithCtx(ctx, "fillTopicChannelItemInfo memberCount=0 cid:%d ownerId:%d tabId:%d", channelId, item.GetOwnerUid(), item.GetTabId())
	}

	//房间标签
	item.Label = c.genChannelLabel(commonData.ChannelLabelMap, channelId)

	//个人认证标签
	item.PersonCert = c.genPersonalCert(commonData.PersonalCertMap, item.GetOwnerUid(), channelId, commonData.RcmdInfo)
	return item, 0
}

func (c *CommonInfoAssembler) genTabName(item *channel_play.TopicChannelItem, listStyle channel_play.ChannelListStyleType) string {
	tabName := cache.GetTabInfoCache().GetTabNameById(item.GetTabId())
	if listStyle == channel_play.ChannelListStyleType_Mix_Business_Home_Page {
		if mysteryView, ok := item.GetTopicChannelView().GetChannelView().(*channel_play.TopicChannelView_ViewMysteryEscape); ok {
			var roomMode string
			if mysteryView.ViewMysteryEscape.GetRoomMode() == model.RoomModeSingle {
				roomMode = "单人"
			} else if mysteryView.ViewMysteryEscape.GetRoomMode() == model.RoomModeDouble {
				roomMode = "双人"
			} else {
				return cache.GetTabInfoCache().GetTabAliasNameById(item.GetTabId())
			}
			tabName = cache.GetTabInfoCache().GetTabAliasNameById(item.GetTabId()) + "·" + roomMode
		}
	}
	return tabName
}

func (c *CommonInfoAssembler) genPersonalCert(personalMap map[uint32]*personalcertification.PersonalCertificationInfoList,
	uid, channelId uint32, rcmdInfo *model.RcmdInfo) []*channel_play.PersonalCert {
	res := make([]*channel_play.PersonalCert, 0, 1)

	isNewbie := rcmdInfo.GetRcmdChannelInfo(channelId).GetIsNewUserUndertake()
	// 萌新军认证
	if isNewbie {
		pc := conf.ChannelPlayConfig.GetNewBiePersonalCert()
		return append(res, &pc)
	}

	if personalMap == nil || personalMap[uid] == nil || len(personalMap[uid].GetInfoList()) == 0 {
		return res
	}

	list := personalMap[uid].GetInfoList()
	//for _, l := range list {
	//	temp := &channel_play.PersonalCert{
	//		Icon:            l.GetCommon().GetIcon(),
	//		Text:            l.GetCommon().GetText(),
	//		Color:           l.GetCommon().GetColor(),
	//		TextShadowColor: l.GetCommon().GetTextShadowColor(),
	//	}
	//	res = append(res, temp)
	//}
	//返回第一个就好
	if len(list) > 0 {
		temp := &channel_play.PersonalCert{
			Icon:            list[0].GetCommon().GetIcon(),
			Text:            list[0].GetCommon().GetText(),
			Color:           list[0].GetCommon().GetColor(),
			TextShadowColor: list[0].GetCommon().GetTextShadowColor(),
		}
		res = append(res, temp)
	}
	return res

}

func (c *CommonInfoAssembler) genChannelLabel(channelLabelMap map[uint32]music_topic_channel.QualityType, channelId uint32) channel_play.ChannelLabel {

	if channelLabelMap != nil {
		label, ok := channelLabelMap[channelId]
		if ok {
			if label == music_topic_channel.QualityType_High_Quality {
				return channel_play.ChannelLabel_ChannelLabelQuality
			} else if label == music_topic_channel.QualityType_Quality_Hot {
				return channel_play.ChannelLabel_ChannelLabelHot
			}
		}
	}
	return channel_play.ChannelLabel_ChannelLabelNone
}
