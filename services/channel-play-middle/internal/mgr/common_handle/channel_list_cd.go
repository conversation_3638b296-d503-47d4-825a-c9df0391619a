package common_handle

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/clients/channel"
	channel_ktv_client "golang.52tt.com/clients/channel-ktv"
	channel_level_cli "golang.52tt.com/clients/channel-level"
	channel_open_game_controller "golang.52tt.com/clients/channel-open-game-controller"
	channel_scheme "golang.52tt.com/clients/channel-scheme"
	"golang.52tt.com/clients/channelmic"
	music_client "golang.52tt.com/clients/channelmusic"
	channelol_stat_go "golang.52tt.com/clients/channelol-stat-go"
	mystery_place_cli "golang.52tt.com/clients/mystery-place"
	punishuser "golang.52tt.com/clients/punish-user"
	tcChannel "golang.52tt.com/clients/topic-channel/channel"
	userol "golang.52tt.com/clients/user-online"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/kaihei-pkg/coroutine"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	simpleChannelPB "golang.52tt.com/protocol/app/channel"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	gamePb "golang.52tt.com/protocol/services/channel-open-game-controller"
	pb "golang.52tt.com/protocol/services/channel-play-middle"
	channelschemepb "golang.52tt.com/protocol/services/channel-scheme"
	channel_mic "golang.52tt.com/protocol/services/channelmicsvr"
	channelmusic "golang.52tt.com/protocol/services/channelmusicsvr"
	channelPB "golang.52tt.com/protocol/services/channelsvr"
	music_cli "golang.52tt.com/protocol/services/music-topic-channel"
	music_topic_channel "golang.52tt.com/protocol/services/music-topic-channel"
	mystery_place "golang.52tt.com/protocol/services/mystery-place"
	"golang.52tt.com/protocol/services/personalcertification"
	tcPb "golang.52tt.com/protocol/services/topic_channel/channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-middle/conf"
	"golang.52tt.com/services/channel-play-middle/internal/cache"
	"golang.52tt.com/services/channel-play-middle/internal/mgr/channel_list/model"
	"golang.52tt.com/services/channel-play-middle/internal/utils"
	"time"

	personal_cli "golang.52tt.com/clients/personal-certification"
	ktvpb "golang.52tt.com/protocol/services/channel-ktv"
)

type InitCommonHandleParams struct {
	SupervisorInst *supervision.Supervisory

	PunishUserCli       punishuser.IClient
	AccountClient       account.IClient
	ChannelLevelClient  channel_level_cli.IClient
	UserOLClient        userol.IClient
	MusicTopicClient    *music_cli.Client
	PersonalClient      *personal_cli.Client
	ChannelMicClient    *channelmic.Client
	ChannelOlStatClient *channelol_stat_go.Client
	ChannelOpenGameCli  *channel_open_game_controller.Client
	MysteryClient       *mystery_place_cli.Client
	ChannelMusicClient  music_client.IClient
	KtvClient           *channel_ktv_client.Client
	ChannelSchemeClient *channel_scheme.Client
	TcChannelClient     tcChannel.IClient
	ChannelClient       channel.IClient
}

func NewChannelCommonHandle(params *InitCommonHandleParams) *ChannelCommonHandle {
	return &ChannelCommonHandle{
		supervisorInst:      params.SupervisorInst,
		punishUserCli:       params.PunishUserCli,
		accountClient:       params.AccountClient,
		channelLevelClient:  params.ChannelLevelClient,
		userOLClient:        params.UserOLClient,
		musicTopicClient:    params.MusicTopicClient,
		personalClient:      params.PersonalClient,
		channelMicClient:    params.ChannelMicClient,
		channelOlStatClient: params.ChannelOlStatClient,
		channelMusicClient:  params.ChannelMusicClient,
		channelOpenGameCli:  params.ChannelOpenGameCli,
		mysteryClient:       params.MysteryClient,
		ktvClient:           params.KtvClient,
		channelSchemeClient: params.ChannelSchemeClient,
		channelClient:       params.ChannelClient,
		tcChannelClient:     params.TcChannelClient,
	}
}

type ChannelCommonHandle struct {
	supervisorInst *supervision.Supervisory

	punishUserCli       punishuser.IClient
	accountClient       account.IClient
	channelLevelClient  channel_level_cli.IClient
	userOLClient        userol.IClient
	musicTopicClient    *music_cli.Client
	personalClient      *personal_cli.Client
	channelMicClient    *channelmic.Client
	channelOlStatClient *channelol_stat_go.Client
	channelOpenGameCli  *channel_open_game_controller.Client
	mysteryClient       *mystery_place_cli.Client
	channelMusicClient  music_client.IClient
	ktvClient           *channel_ktv_client.Client
	channelSchemeClient *channel_scheme.Client
	tcChannelClient     tcChannel.IClient
	channelClient       channel.IClient
}

func (cd *ChannelCommonHandle) genViewSwitchOptions(ctx context.Context, listStyle channel_play.ChannelListStyleType,
	enterSource channel_play.ChannelListEnterSource, reqSource pb.ReqSource) *model.GenViewSwitch {

	switchOpts := &model.GenViewSwitch{}

	switch reqSource {
	case pb.ReqSource_REQ_SOURCE_SEARCH_BY_ID:
		switchOpts.NeedIdDesc = true
	case pb.ReqSource_REQ_SOURCE_LIST:
		switchOpts.NeedReportData = true
		switchOpts.NeedDoTabFilter = true
		switchOpts.NeedDoPublishFilter = true
		switchOpts.NeedChannelPwdFilter = true
	default:
	}

	if enterSource == channel_play.ChannelListEnterSource_MysteryChannelScreen {
		switchOpts.NeedUserOLInfo = true
	}

	switch listStyle {
	case channel_play.ChannelListStyleType_Default_Group:

		switchOpts.NeedKtvPlayMusicStatus = true

		switchOpts.NeedPlayingMusicList = true

	case channel_play.ChannelListStyleType_HuanYou_Experiment_Group:
		switchOpts.NeedKtvPlayMusicStatus = true
		switchOpts.NeedPlayingMusicList = true
		switchOpts.NeedUserOnMicNum = true

		switchOpts.NeedHuanYouChannelDisplayUserInfo = true
	case channel_play.ChannelListStyleType_Mix_Business_Home_Page:
		switchOpts.NeedChannelLabel = true
		switchOpts.NeedPersonalCert = true
		switchOpts.NeedUserOnMicNum = true
	default:
		log.WarnWithCtx(ctx, "genSwitchOption unknown list style type: %d", listStyle)
	}
	return switchOpts
}

func (cd *ChannelCommonHandle) GenBaseCommonData(ctx context.Context, uid uint32,
	req *pb.BatGetGameChannelViewMapReq) (*model.ChannelCommonHandleData, error) {
	cids := req.GetChannelIds()
	commonData := &model.ChannelCommonHandleData{
		ReqSource:       pb.ReqSource(req.GetReqSource()),
		MysteryInfo:     &model.MysteryViewInfo{},
		KtvInfo:         &model.KtvViewInfo{},
		MarshalViewInfo: &model.MarshalViewInfo{},
		RcmdInfo: &model.RcmdInfo{
			RcmdChannelInfoMap: req.GetRecommendInfo().GetChannelInfoMap(),
			RcmdLoc:            req.GetRecommendInfo().GetSelfLocation(),
		},
		ReportData: &blreport.ChannelList{
			TabId:       req.GetListParams().GetTabId(),
			TabIds:      req.GetListParams().GetTabIdsStr(),
			CategoryIds: req.GetListParams().GetCategoryIdsStr(),
			UserId:      uid,
		},
	}

	var enterSource channel_play.ChannelListEnterSource
	var listStyle channel_play.ChannelListStyleType
	var genViewSource []uint32
	switch pb.ReqSource(req.GetReqSource()) {
	case pb.ReqSource_REQ_SOURCE_SEARCH_BY_ID, pb.ReqSource_REQ_SOURCE_SEARCH_BY_NAME:
		enterSource = channel_play.ChannelListEnterSource_MixHomePageSource
		listStyle = channel_play.ChannelListStyleType_Mix_Business_Home_Page
		genViewSource = req.GetListParams().GetGenViewSource()
	case pb.ReqSource_REQ_SOURCE_LIST:
		enterSource = channel_play.ChannelListEnterSource(req.GetListParams().GetChannelListEnterSource())
		listStyle = channel_play.ChannelListStyleType(req.GetListParams().GetListStyleType())
		genViewSource = req.GetListParams().GetGenViewSource()
		commonData.ChannelPkg = req.GetListParams().GetChannelPackageId()
	default:
	}
	commonData.ListStyle = listStyle
	commonData.EnterSource = enterSource
	commonData.GenViewSource = genViewSource

	// 2, 根据请求来源等参数，生成需要查询的数据开关
	commonData.SwitchOpts = cd.genViewSwitchOptions(ctx, commonData.ListStyle, commonData.EnterSource, commonData.ReqSource)

	channelInfoMap, err := cd.channelClient.BatchGetChannelSimpleInfo(ctx, uid, cids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenBaseCommonData BatchGetChannelSimpleInfo channelIds:%v,err:%v", cids, err)
		return commonData, err
	}
	simpleInfoMap := make(map[uint32]*channelPB.ChannelSimpleInfo, len(channelInfoMap))
	ugcChannelIds := make([]uint32, 0, len(channelInfoMap))
	var otherCids []uint32
	for cid, info := range channelInfoMap {
		// 收集所有房间信息
		simpleInfoMap[cid] = info
		if info.GetChannelType() == uint32(simpleChannelPB.ChannelType_USER_CHANNEL_TYPE) {
			ugcChannelIds = append(ugcChannelIds, info.GetChannelId())
		} else {
			otherCids = append(otherCids, cid)
		}
	}
	if len(otherCids) > 0 {
		log.InfoWithCtx(ctx, "GenBaseCommonData no ugc Cids:%v", otherCids)
	}
	tcResp, err := cd.tcChannelClient.GetChannelByIds(ctx, &tcPb.GetChannelByIdsReq{
		Ids:       ugcChannelIds,
		ReturnAll: true,
		Source:    "channelPB-play-middle/BatGetGameChannelViewMap",
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GenBaseCommonData GetChannelByIds failed cids:%+v, err is %v", cids, err)
		return commonData, err
	}

	tcInfoMap := make(map[uint32]*tcPb.ChannelInfo, len(tcResp.GetInfo()))
	for _, v := range tcResp.GetInfo() {
		if protocol.IsFastPcClientType(v.ClientType) && v.GetTabId() == conf.PublicSwitchConfig.GetMuseChatTabId() {
			v.TabId = conf.PublicSwitchConfig.GetFastPcChatTabId()
		}
		tcInfoMap[v.GetId()] = v
	}

	commonData.UgcChannelIds = ugcChannelIds
	commonData.ChannelInfoMap = simpleInfoMap
	commonData.TcInfoMap = tcInfoMap
	commonData.OtherChannelIds = otherCids
	return commonData, nil
}

func (cd *ChannelCommonHandle) ChannelFollowCommonHandle(ctx context.Context, req *pb.BatGetGameChannelViewMapReq,
	serviceInfo *grpc.ServiceInfo) (commonData *model.ChannelCommonHandleData, err error) {
	// 1, 房间基础信息
	commonData, err = cd.GenBaseCommonData(ctx, serviceInfo.UserID, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelFollowCommonHandle GenBaseCommonData req:%s err:%v", req.String(), err)
		return commonData, err
	}

	cd.spiltChannelByView(ctx, commonData)

	//// 2, 根据请求来源等参数，生成需要查询的数据开关
	//commonData.SwitchOpts = cd.genViewSwitchOptions(ctx, commonData.ListStyle, commonData.EnterSource, commonData.ReqSource)

	if commonData.SwitchOpts.NeedIdDesc {
		commonData.IdDescMap = make(map[uint32]string)
		for cid, info := range commonData.ChannelInfoMap {
			if info.GetChannelViewId() != "" {
				commonData.IdDescMap[cid] = fmt.Sprintf("房间ID：%s", info.GetChannelViewId())
			} else {
				commonData.IdDescMap[cid] = fmt.Sprintf("房间ID：%d", info.GetDisplayId())
			}
		}
	}

	var task []coroutine.CoroutineTask

	baseTask := cd.genBaseTask(commonData, serviceInfo)
	if len(baseTask) > 0 {
		task = append(task, baseTask...)
	}

	taskBySwitchOpts := cd.genTaskByOpts(commonData, serviceInfo)
	if len(taskBySwitchOpts) > 0 {
		task = append(task, taskBySwitchOpts...)
	}

	if len(task) > 0 {
		coroutineCtx, coroutineCancel := context.WithTimeout(ctx, 500*time.Millisecond)
		err, bRet := coroutine.CoroutineDoWithSingleResult("", coroutineCtx, uint32(len(task)), task...)
		if err != nil {
			if bRet {
				coroutineCancel()
				return commonData, err
			}
		}
		coroutineCancel()
	}
	log.InfoWithCtx(ctx, "ChannelFollowCommonHandle ReqSource:%s ListStyle:%s EnterSource:%s UgcChannelIds:%+v "+
		"MobaViewChannelIds:%+v DefaultViewChannelIds:%+v KtvViewChannelIds:%+v MysteryViewChannelIds:%+v MarshalViewChannelIds:%+v"+
		"switchOpts:%+v ChannelViewMap:%+v PunishMap:%+v, MusicChannelInfoMap: %+v, MarshalChannelInfoMap: %+v",
		commonData.ReqSource.String(), commonData.ListStyle.String(), commonData.EnterSource.String(),
		commonData.UgcChannelIds, commonData.MobaViewChannelIds, commonData.DefaultViewChannelIds,
		commonData.KtvViewChannelIds, commonData.MysteryViewChannelIds, commonData.MarshalViewChannelIds,
		commonData.SwitchOpts, commonData.ChannelViewMap, commonData.PunishMap, commonData.MusicChannelInfoMap, commonData.MarshalViewInfo.MarshalChannelInfoMap)

	return commonData, err
}

func (cd *ChannelCommonHandle) genBaseTask(commonData *model.ChannelCommonHandleData, serviceInfo *grpc.ServiceInfo) []coroutine.CoroutineTask {
	uid := serviceInfo.UserID
	needChannelInfoIds := commonData.GetNeedInfoChannels()
	creators := commonData.GetCreators()
	task := []coroutine.CoroutineTask{
		//func(ctx *context.Context, bCancelLog bool) (error, bool) {
		//	punishUsers, err := cd.getPunishUsers(*ctx, creators)
		//	if err != nil {
		//		if !bCancelLog {
		//			log.ErrorWithCtx(*ctx, "ListTopicChannel CheckPunishUser by ids(%v) err(%v)", creators, err)
		//		}
		//		return err, false
		//	}
		//	commonData.PunishMap = punishUsers
		//	return nil, false
		//},
		func(ctx *context.Context, bCancelLog bool) (error, bool) {
			userResp, err := cd.accountClient.GetUsersMap(*ctx, creators)
			if err != nil {
				if !bCancelLog {
					log.ErrorWithCtx(*ctx, "ListTopicChannel GetUsersMap by ids(%v) err(%v)", creators, err)
				}
				return err, true
			}
			commonData.UserMap = userResp
			return nil, false
		},
	}
	if len(needChannelInfoIds) > 0 {
		needInfoTask := []coroutine.CoroutineTask{
			func(ctx *context.Context, bCancelLog bool) (error, bool) {
				levelResp, err := cd.channelLevelClient.BatchGetChannelInfo(*ctx, needChannelInfoIds)
				if err != nil {
					//报错了不返回就算了，接口不报错
					if !bCancelLog {
						log.ErrorWithCtx(*ctx, "ListTopicChannel BatchGetChannelLevelInfo userId(%v) ids(%v) err(%v)",
							uid, needChannelInfoIds, err)
					}
					return err, false
				} else {
					commonData.LevelInfo = levelResp
				}
				return nil, false
			},
			func(ctx *context.Context, bCancelLog bool) (error, bool) {
				memberMap, err := cd.channelOlStatClient.BatchGetChannelMemberSize(*ctx, uid, needChannelInfoIds)
				if err != nil {
					//报错了不返回就算了，接口不报错
					if !bCancelLog {
						log.ErrorWithCtx(*ctx, "ChannelFollowCommonHandle BatchGetChannelMemberSize userId(%v) cids(%v) err(%v)",
							uid, needChannelInfoIds, err)
					}
					return err, false
				}
				commonData.MemberMap = memberMap
				return nil, false
			},
			//获取需要展示的用户头像信息
			func(ctx *context.Context, bCancelLog bool) (error, bool) {
				userInfoMap, err := cd.getChannelDisplayUserInfo(*ctx, needChannelInfoIds, commonData.RcmdInfo)
				if err != nil {
					//报错了不返回就算了，接口不报错
					if !bCancelLog {
						log.ErrorWithCtx(*ctx, "ListTopicChannel getChannelDisplayUserInfo userId(%v) uids(%v) err(%v)",
							uid, needChannelInfoIds, err)
					}
					return err, false
				}
				commonData.UserOnMicImgMap = userInfoMap
				return nil, false
			},
		}
		task = append(task, needInfoTask...)
	}

	if len(commonData.MarshalViewChannelIds) > 0 {
		marshalTask := []coroutine.CoroutineTask{
			func(ctx *context.Context, bCancelLog bool) (error, bool) {
				marshalResp, err := cd.musicTopicClient.ListMusicChannelViews(*ctx, &music_topic_channel.ListMusicChannelViewsReq{
					ChannelIds: commonData.MarshalViewChannelIds,
					IsAll:      true,
				})
				if err != nil {
					if !bCancelLog {
						log.ErrorWithCtx(*ctx, "ChannelFollowCommonHandle ListMusicChannelViewPbs uid:%v, marshalCids:%v, err %v",
							uid, commonData.MarshalViewChannelIds, err)
					}
					return err, false
				}
				commonData.MarshalViewInfo.MarshalChannelInfoMap = make(map[uint32][]byte, len(marshalResp.GetChannelViews()))
				for k, newView := range marshalResp.GetChannelViews() {
					ret, err := proto.Marshal(newView)
					if err != nil {
						log.WarnWithCtx(*ctx, "convertViewToPbMarshal newView:%v err:%v", newView, err)
						continue
					}
					commonData.MarshalViewInfo.MarshalChannelInfoMap[k] = ret
				}
				return nil, false
			},
		}
		task = append(task, marshalTask...)
	}

	if len(commonData.MobaViewChannelIds) > 0 {
		mobaTask := []coroutine.CoroutineTask{
			func(ctx *context.Context, bCancelLog bool) (error, bool) {
				resp, err := cd.channelSchemeClient.BatGetUgcChannelSchemeInfo(*ctx, &channelschemepb.BatGetUgcChannelSchemeInfoReq{
					CidList: commonData.MobaViewChannelIds,
				})
				if err != nil {
					//报错了不返回就算了，接口不报错
					if !bCancelLog {
						log.ErrorWithCtx(*ctx, "ChannelFollowCommonHandle BatGetCurChannelSchemeInfo userId(%v) cids(%v) err(%v)", uid,
							commonData.MobaViewChannelIds, err)
					}
					return err, false
				} else {
					commonData.ChannelSchemeInfoMap = resp.GetSchemeInfoList()
				}
				return nil, false
			},
		}
		task = append(task, mobaTask...)
	}

	// 音乐房数需要补充的数据
	if len(commonData.MusicViewChannelIds) > 0 {
		musicViewTask := []coroutine.CoroutineTask{
			func(ctx *context.Context, bCancelLog bool) (error, bool) {
				musicViewResp, err := cd.musicTopicClient.ListMusicChannelViews(*ctx, &music_topic_channel.ListMusicChannelViewsReq{
					ChannelIds: commonData.MusicViewChannelIds,
					IsAll:      true,
				})
				if err != nil {
					if !bCancelLog {
						log.ErrorWithCtx(*ctx, "ChannelFollowCommonHandle ListMusicChannelViewPbs uid:%v, marshalCids:%v, err %v",
							uid, commonData.MusicViewChannelIds, err)
					}
					return err, false
				}

				commonData.MusicChannelInfoMap = musicViewResp.GetChannelViews()
				return nil, false
			},
			func(ctx *context.Context, bCancelLog bool) (error, bool) {
				resp, err := cd.channelSchemeClient.BatGetUgcChannelSchemeInfo(*ctx, &channelschemepb.BatGetUgcChannelSchemeInfoReq{
					CidList: commonData.MusicViewChannelIds,
				})
				if err != nil {
					//报错了不返回就算了，接口不报错
					if !bCancelLog {
						log.ErrorWithCtx(*ctx, "ChannelFollowCommonHandle BatGetCurChannelSchemeInfo userId(%v) cids(%v) err(%v)", uid,
							commonData.MusicViewChannelIds, err)
					}
					return err, false
				}

				commonData.ChannelSchemeInfoMap = resp.GetSchemeInfoList()
				return nil, false
			},
		}
		task = append(task, musicViewTask...)
	}

	if len(commonData.MysteryViewChannelIds) > 0 {
		mysteryTask := []coroutine.CoroutineTask{
			func(ctx *context.Context, bCancelLog bool) (error, bool) {
				gameStatusRsp, err := cd.channelOpenGameCli.BatchGetChannelGameStatusInfo(*ctx, &gamePb.BatchGetChannelGameStatusInfoReq{
					ChannelIdList: commonData.MysteryViewChannelIds,
				})
				if err != nil {
					if !bCancelLog {
						log.ErrorWithCtx(*ctx, "ChannelFollowCommonHandle BatchGetChannelGameStatusInfo cids:%v, err:%v",
							commonData.MysteryViewChannelIds, err)
					}
					return err, false
				}
				commonData.MysteryInfo.GameStatusInfoMap = gameStatusRsp.GetChannelGameStatusInfoMap()
				return nil, false
			},
			func(ctx *context.Context, bCancelLog bool) (error, bool) {
				//批量进度
				processInfos, err := cd.mysteryClient.GetGamePercentMapByChannelIds(*ctx, &mystery_place.BatchGetGamePercentByChannelIdsReq{
					ChannelIds: commonData.MysteryViewChannelIds,
				})
				log.DebugWithCtx(*ctx, "processInfos %v", processInfos)
				if err != nil {
					if !bCancelLog {
						log.ErrorWithCtx(*ctx, "ChannelFollowCommonHandle GetGamePercentMapByChannelIds uid %v cids:%v, get process err %v", uid,
							commonData.MysteryViewChannelIds, err)
					}
					return err, false
				}
				commonData.MysteryInfo.RoomGamePercentMap = processInfos
				return nil, false
			},
		}
		task = append(task, mysteryTask...)
	}

	return task
}

func (cd *ChannelCommonHandle) genTaskByOpts(commonData *model.ChannelCommonHandleData, serviceInfo *grpc.ServiceInfo) []coroutine.CoroutineTask {

	uid := serviceInfo.UserID
	var task []coroutine.CoroutineTask
	needChannelInfoIds := commonData.GetNeedInfoChannels()
	if protocol.IsFastPcClientType(uint32(serviceInfo.ClientType)) {
		needChannelInfoIds = append(needChannelInfoIds, commonData.MarshalViewChannelIds...)
	}
	creators := commonData.GetCreators()
	switchOpts := commonData.SwitchOpts

	// 玩法过滤配置
	if switchOpts.NeedDoTabFilter {
		task = append(task, func(ctx *context.Context, bCancelLog bool) (error, bool) {
			// 未成年监管过滤配置
			supConfInst := &supervision.SupervisoryConf{
				RegisterLimitTime:      conf.PublicSwitchConfig.GetRegisterLimitTime(),
				UserLevelLimit:         conf.PublicSwitchConfig.GetUserLevelLimit(),
				RealNameStandardStatus: conf.PublicSwitchConfig.GetRealNameStandardStatus(),
			}
			commonData.TabFilter, commonData.CategoryFilter = cd.supervisorInst.GetFilterMap(*ctx, serviceInfo, commonData.ChannelPkg, supConfInst)
			return nil, false
		})
	}

	// 谜境密逃房半屏 && 等待中
	if switchOpts.NeedUserOLInfo {
		task = append(task, func(ctx *context.Context, bCancelLog bool) (error, bool) {
			userOLMap, err := cd.userOLClient.BatchGetLastMobileOnlineInfo(*ctx, creators)
			if err != nil {
				if !bCancelLog {
					log.ErrorWithCtx(*ctx, "ListTopicChannel BatchGetLastMobileOnlineInfo userId(%d) creators(%v) err: %v",
						uid, creators, err)
				}
				return err, false
			}

			commonData.UserOLMap = userOLMap
			return nil, false
		})
	}

	if switchOpts.NeedChannelLabel {
		task = append(task, func(ctx *context.Context, bCancelLog bool) (error, bool) {
			channelTabIdMap, err := commonData.GetChannelTabIds()
			if err != nil {
				return err, false
			}
			channelLabelMap, err := cd.getChannelLabel(*ctx, channelTabIdMap)
			if err != nil {
				//报错了不返回就算了，接口不报错
				if !bCancelLog {
					log.ErrorWithCtx(*ctx, "ListTopicChannel getChannelLabel userId(%v) ids(%v) err(%v)",
						uid, needChannelInfoIds, err)
				}
				return err, false
			}
			commonData.ChannelLabelMap = channelLabelMap

			return nil, false
		})
	}

	if switchOpts.NeedPersonalCert {
		task = append(task, func(ctx *context.Context, bCancelLog bool) (error, bool) {
			if len(creators) > 0 {
				personalMap, err := cd.getPersonalCert(*ctx, creators)
				if err != nil {
					//报错了不返回就算了，接口不报错
					if !bCancelLog {
						log.ErrorWithCtx(*ctx, "ListTopicChannel getPersonalCert userId(%v) uids(%v) err(%v)",
							uid, creators, err)
					}
					return err, false
				}
				commonData.PersonalCertMap = personalMap
			}
			return nil, false
		})
	}

	needMusicIds := commonData.GetMusicIds()
	if len(needMusicIds) > 0 && switchOpts.NeedPlayingMusicList {

		commonData.KtvInfo.PlayingMusicList = make(map[uint32]string)

		ktvTask := func(ctx *context.Context, bCancelLog bool) (error, bool) {
			musicResp, err := cd.channelMusicClient.BatGetChannelPlayingMusicInfo(*ctx, uid, &channelmusic.BatGetChannelPlayingMusicInfoReq{
				ChannelIdList: needMusicIds,
			})
			if err != nil {
				//报错了不返回就算了，接口不报错
				if !bCancelLog {
					log.ErrorWithCtx(*ctx, "ChannelFollowCommonHandle BatGetChannelPlayingMusicInfo by ids(%v) err(%v)",
						needMusicIds, err)
				}
				return err, false
			}
			for _, item := range musicResp.GetMusicList() {
				commonData.KtvInfo.PlayingMusicList[item.ChannelId] = item.MusicName + "-" + item.MusicAuthor
			}
			return nil, false
		}
		task = append(task, ktvTask)
	}
	needKtvIds := commonData.GetKtvIds()
	if len(needKtvIds) > 0 && switchOpts.NeedKtvPlayMusicStatus {

		ktvTask := func(ctx *context.Context, bCancelLog bool) (error, bool) {
			ktvResp, err := cd.ktvClient.BatchGetPlayListStatus(*ctx, &ktvpb.GetPlayListStatusReq{ChannelIds: needKtvIds})
			if err != nil {
				//报错了不返回就算了，接口不报错
				if !bCancelLog {
					log.ErrorWithCtx(*ctx, "ChannelFollowCommonHandle BatchGetPlayListStatus userId(%v) ids(%v) err(%v)", uid,
						needKtvIds, err)
				}
				return err, false
			}
			commonData.KtvInfo.KtvPlayMusicStatus = ktvResp.GetStatusesMapping()

			return nil, false
		}
		task = append(task, ktvTask)
	}

	if len(needChannelInfoIds) > 0 && switchOpts.NeedUserOnMicNum {
		t := func(ctx *context.Context, bCancelLog bool) (error, bool) {
			numMap, err := cd.getOnMicUserNum(*ctx, uid, needChannelInfoIds)
			if err != nil {
				//报错了不返回就算了，接口不报错
				if !bCancelLog {
					log.ErrorWithCtx(*ctx, "ChannelFollowCommonHandle getOnMicUserNum userId(%v) ids(%v) err(%v)", uid,
						needChannelInfoIds, err)
				}
				return err, false
			}
			commonData.UserOnMicNumMap = numMap
			return nil, false
		}
		task = append(task, t)
	}

	return task

}

func (cd *ChannelCommonHandle) getOnMicUserNum(ctx context.Context, uid uint32, needChannelInfoIds []uint32) (map[uint32]uint32, error) {
	micListResp, err := cd.channelMicClient.BatGetMicrList(ctx, uid, &channel_mic.BatGetMicrListReq{ChannelIdList: needChannelInfoIds})
	if err != nil {
		return nil, err
	}
	numMap := make(map[uint32]uint32, len(micListResp.GetMicDataList()))
	for _, micInfo := range micListResp.GetMicDataList() {
		var num uint32
		for _, m := range micInfo.GetAllMicList() {
			if m.MicUid > 0 {
				num++
			}
		}
		numMap[micInfo.GetChannelId()] = num
	}
	return numMap, nil

}

// 获取房间标签
func (cd *ChannelCommonHandle) getChannelLabel(ctx context.Context, channelTabMap map[uint32]uint32) (
	map[uint32]music_topic_channel.QualityType, error) {
	channelLabelMap := make(map[uint32]music_topic_channel.QualityType)

	resp, err := cd.musicTopicClient.BatchHighQualityChannels(ctx, &music_topic_channel.BatchHighQualityChannelsReq{
		TabChannelMap: channelTabMap,
	})
	if err != nil {
		return channelLabelMap, err
	}
	return resp.GetChannelQualityMap(), nil
}

// 获取个人认证标签
func (cd *ChannelCommonHandle) getPersonalCert(ctx context.Context, creators []uint32) (
	map[uint32]*personalcertification.PersonalCertificationInfoList, error) {
	resp, err := cd.personalClient.BatGetPersonalCertification(ctx, &personalcertification.BatGetPersonalCertificationReq{
		Uid:             creators,
		PresentPosition: uint32(personalcertification.PresentPosition_RCMD_CARD),
	})
	if err != nil {
		return nil, err
	}
	return resp.GetUserCertMap(), nil
}

// 处理被惩罚用户 2021-12-02
func (cd *ChannelCommonHandle) getPunishUsers(ctx context.Context, userIds []uint32) (map[uint32]bool, error) {
	if conf.PublicSwitchConfig.GetCheckPunish() == 0 {
		return nil, nil
	}

	punishResp, err := cd.punishUserCli.CheckPunishUser(ctx, userIds, 0)
	if err != nil {
		return nil, err
	}
	return punishResp.UserPunishResult, nil
}

func (cd *ChannelCommonHandle) getChannelDisplayUserInfo(ctx context.Context, channelIds []uint32, rcmdInfo *model.RcmdInfo) (
	map[uint32][]*channel_play.UserInfoForImg, error) {
	res := make(map[uint32][]*channel_play.UserInfoForImg, len(channelIds))
	uidList := make([]uint32, 0)
	for _, channelInfo := range rcmdInfo.RcmdChannelInfoMap {
		uidList = append(uidList, channelInfo.GetDisplayUidList()...)
	}

	if len(uidList) == 0 {
		return res, nil
	}
	if len(uidList) > 100 {
		log.WarnWithCtx(ctx, "getChannelDisplayUserInfo 查询uid过多，截断uids:%+v", uidList[100:])
		//最多查询100个uid
		uidList = uidList[:100]
	}
	userMap, err := cd.accountClient.GetUsersMap(ctx, uidList)
	if err != nil {
		log.WarnWithCtx(ctx, "ChannelCommonHandle getChannelDisplayUserInfo AccountGoClient.BatGetUserByUid  err:%v, randomUids:%v", err, uidList)
		return res, err
	}
	for channelId, channelInfo := range rcmdInfo.RcmdChannelInfoMap {
		for _, uid := range channelInfo.GetDisplayUidList() {
			userInfo, ok := userMap[uid]
			if !ok {
				continue
			}
			res[channelId] = append(res[channelId], &channel_play.UserInfoForImg{
				Uid:      userInfo.GetUid(),
				Username: userInfo.GetUsername(),
				Sex:      uint32(utils.TransferSex(userInfo.GetSex())),
			})
		}
	}
	return res, nil
}

func (cd *ChannelCommonHandle) spiltChannelByView(ctx context.Context, commonData *model.ChannelCommonHandleData) {
	defaultViewChannel := make([]uint32, 0)
	mysteryViewChannel := make([]uint32, 0)
	mobaViewChannel := make([]uint32, 0)
	ktvViewChannel := make([]uint32, 0)
	marshalViewChannel := make([]uint32, 0)
	musicViewChannel := make([]uint32, 0)
	channelInfoMap := commonData.TcInfoMap
	channelViewMap := make(map[uint32]channel_play.ChannelViewType, len(channelInfoMap))
	needMarshalView, needMusicView := isMusicGenView(commonData.GenViewSource)
	for cid, info := range channelInfoMap {
		tabInfo, ok := cache.GetTabInfoCache().GetTabIdCache()[info.GetTabId()]
		if !ok {
			log.ErrorWithCtx(ctx, "ChannelList ChannelItemMgr channelInfo(%v) get no tab", info.String())
			continue
		}
		vType := channel_play.ChannelViewType(tabInfo.GetTopicChannelViewType())
		if needMarshalView && tabInfo.GetHomePageType() == tabPB.HomePageType_HomePageTypeMUSIC {
			vType = channel_play.ChannelViewType_ChanenlView_Marshal
		}

		if needMusicView && tabInfo.GetHomePageType() == tabPB.HomePageType_HomePageTypeMUSIC {
			vType = channel_play.ChannelViewType_ChannelView_Music
		}

		channelViewMap[cid] = vType
		switch vType {
		case channel_play.ChannelViewType_ChannelView_Default:
			defaultViewChannel = append(defaultViewChannel, cid)
		case channel_play.ChannelViewType_ChannelView_Ktv:
			ktvViewChannel = append(ktvViewChannel, cid)
		case channel_play.ChannelViewType_ChannelView_MOBA:
			mobaViewChannel = append(mobaViewChannel, cid)
		case channel_play.ChannelViewType_ChannelView_MysteryEscape:
			mysteryViewChannel = append(mysteryViewChannel, cid)
		case channel_play.ChannelViewType_ChanenlView_Marshal:
			marshalViewChannel = append(marshalViewChannel, cid)
		case channel_play.ChannelViewType_ChannelView_Music:
			musicViewChannel = append(musicViewChannel, cid)
		default:
			log.ErrorWithCtx(ctx, "SpiltChannelByView channelId %d err viewType %v", cid, vType)
		}
	}

	// 非ugc房（社群/语音直播房），统一走marshal view
	for _, cid := range commonData.OtherChannelIds {
		channelViewMap[cid] = channel_play.ChannelViewType_ChanenlView_Marshal
		marshalViewChannel = append(marshalViewChannel, cid)
	}

	commonData.DefaultViewChannelIds = defaultViewChannel
	commonData.MarshalViewChannelIds = marshalViewChannel
	commonData.MobaViewChannelIds = mobaViewChannel
	commonData.MysteryViewChannelIds = mysteryViewChannel
	commonData.KtvViewChannelIds = ktvViewChannel
	commonData.MusicViewChannelIds = musicViewChannel
	commonData.ChannelViewMap = channelViewMap
}

func isMusicGenView(source []uint32) (bool, bool) {
	var isMusic, isMtGame bool
	for _, s := range source {
		if channel_play.GenViewSource(s) == channel_play.GenViewSource_FROM_MUSIC {
			isMusic = true
		}
		if channel_play.GenViewSource(s) == channel_play.GenViewSource_FROM_MT_GAME {
			isMtGame = true
		}
	}
	return isMusic, isMtGame
}
