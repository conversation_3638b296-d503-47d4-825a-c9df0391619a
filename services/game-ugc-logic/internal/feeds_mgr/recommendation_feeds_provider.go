package feeds_mgr

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	send "golang.52tt.com/pkg/monkey-send-chat"
	"golang.52tt.com/pkg/monkey-send-chat/monkey_sender"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/speedlimit"
	game_ugc "golang.52tt.com/protocol/app/game-ugc"
	game_ugc_content "golang.52tt.com/protocol/services/game-ugc-content"
	rcmdPB "golang.52tt.com/protocol/services/rcmd/rcmd_game_post"
	"golang.52tt.com/services/game-ugc-logic/internal/client"
	config "golang.52tt.com/services/game-ugc-logic/internal/config/ttconfig/feeds"
)

type recommendationFeedsProvider struct {
	cd          IFeedCommonHandle
	errOverInst *speedlimit.ErrorOver
	in          *game_ugc.GetGameNewsFeedsReq
	serviceInfo *protogrpc.ServiceInfo
}

func newRecommendationFeedsProvider(serviceInfo *protogrpc.ServiceInfo, in *game_ugc.GetGameNewsFeedsReq,
	cd IFeedCommonHandle, errOverInst *speedlimit.ErrorOver) feedsProvider {
	return &recommendationFeedsProvider{
		cd:          cd,
		errOverInst: errOverInst,
		serviceInfo: serviceInfo,
		in:          in,
	}
}

func (p *recommendationFeedsProvider) GetActivitiesFeeds(ctx context.Context) ([]*game_ugc.GameFeed, bool, error) {
	userId := p.serviceInfo.UserID
	req := p.in
	serviceInfo := p.serviceInfo
	var bottomReach bool
	p.errOverInst.AddTotalCnt(1)
	rcmdReq, err := p.genRcmdRequest(ctx, p.in)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetActivitiesFeeds GetRcmdGamePost uid %d in:%s, svInfo:%s, err: %v", userId,
			req.String(), serviceInfo.String(), err)
		return nil, bottomReach, err
	}
	// 话题列表为空不请求推荐
	if len(rcmdReq.GetTopicIdList()) == 0 {
		log.WarnWithCtx(ctx, "GetActivitiesFeeds GetRcmdGamePost topicIdList is empty, don't request rcmd, uid:%d, in:%s, svInfo:%s", userId,
			req.String(), serviceInfo.String())
		return nil, bottomReach, nil
	}
	rcmdResp, err := client.RcmdPostClient.GetRcmdGamePost(ctx, rcmdReq)
	if err != nil || config.GetFeedsConfig().GetTestRcmdWarn() {
		log.ErrorWithCtx(ctx, "GetActivitiesFeeds GetRcmdGamePost uid %d in:%s, rcmdReq:%s, err: %v", userId,
			req.String(), rcmdReq.String(), err)
		send.GetMonkey().SendToChat("开黑专区动态", fmt.Sprintf("推荐流返回error，uid:%d", userId))
		return nil, bottomReach, err
	}

	log.InfoWithCtx(ctx, "GetActivitiesFeeds GetRcmdGamePost uid %d in:%s, rcmdReq:%s, len(postList):%d bottomReached %t",
		userId, req.String(), rcmdReq.String(), len(rcmdResp.GetPostIdList()), rcmdResp.GetBottomReached())
	postIdList := rcmdResp.GetPostIdList()
	bottomReach = rcmdResp.GetBottomReached()

	log.DebugWithCtx(ctx, "uid: %d, postIdList: %v", userId, postIdList)

	if len(postIdList) == 0 {
		p.errOverInst.AddErrorCnt(1)
		log.WarnWithCtx(ctx, "GetRcmdGamePost no post uid:%d, in:%s, sv:%s", userId, req.String(), serviceInfo.String())
		if p.errOverInst.IsOverWithMin(ctx, config.GetFeedsConfig().GetMinEmptyListWarnReqNum()) {
			go func() {
				errCnt, totalCnt, errRate := p.errOverInst.GetErrRate()
				sendErr := monkey_sender.GetQueueMsgSenderByChatId(config.GetFeedsConfig().GetWarnChatId(), 100).
					SendMsg("专区帖子列表", fmt.Sprintf("推荐返回空列表请求数量：%d 请求总数：%d 比例为:%.2f%% overRate:%d%% pod_name:%s",
						errCnt, totalCnt, errRate, p.errOverInst.GetOverRate(), config.PodName))
				if sendErr != nil {
					log.ErrorWithCtx(ctx, "GetInviteListData monkey_sender SendMsg err:%v", sendErr)
				}
			}()
		}
		return nil, bottomReach, nil
	}

	resp, err := client.GameUgcContentClient.BatchGetGameFeedPbs(ctx, &game_ugc_content.BatchGetGameFeedPbsReq{
		PostId:     postIdList,
		ReqSource:  uint32(game_ugc_content.ReqSource_GAME_RCMD_SOURCE),
		ReqUid:     userId,
		MarketId:   p.serviceInfo.MarketID,
		ClientType: uint32(p.serviceInfo.ClientType),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetActivitiesFeeds uid:%d, postId:%v, in:%s, serviceInfo:%s err:%v", userId, postIdList,
			req.String(), serviceInfo.String(), err)
		return nil, bottomReach, err
	}
	outFeeds := make([]*game_ugc.GameFeed, 0, len(resp.GetGameFeedContent()))
	if len(resp.GetGameFeedContent()) == 0 {
		log.ErrorWithCtx(ctx, "GetActivitiesFeeds len(outFeeds)=0, uid:%d, in:%s, serviceInfo:%s, rcmdReq:%s, rcmdResp:%s",
			userId, req.String(), serviceInfo.String(), rcmdReq.String(), rcmdResp.String())
		return outFeeds, bottomReach, nil
	}
	for _, content := range resp.GetGameFeedContent() {
		tempFeeds := &game_ugc.GameFeed{}
		if content.GetUnmarshalType() == uint32(game_ugc_content.UnmarshalType_UNMARSHAL_TYPE_PROTOBUF) {
			err = proto.Unmarshal(content.GetContent(), tempFeeds)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetActivitiesFeeds uid:%d, proto.Unmarshal postId:%v, in:%s, serviceInfo:%s err:%v", userId, postIdList,
					req.String(), serviceInfo.String(), err)
				continue
			}
		}
		outFeeds = append(outFeeds, tempFeeds)
	}

	return outFeeds, bottomReach, nil
}

func (p *recommendationFeedsProvider) genRcmdRequest(ctx context.Context, req *game_ugc.GetGameNewsFeedsReq) (
	*rcmdPB.GetRcmdGamePostReq, error) {

	list := req.GetGameRecommendationStreamReq().GetTopicIds()
	if len(list) == 0 {
		resp, err := client.GameUgcContentClient.GetConfigTabInfoById(ctx, &game_ugc_content.GetConfigTabInfoByIdReq{
			ConfigTabId: req.GetGameRecommendationStreamReq().GetConfigTabSetId(),
		})
		if err != nil {
			return nil, err
		}
		list = make([]string, 0, len(resp.GetConfigTabInfo().GetBindTopics()))
		for _, topic := range resp.GetConfigTabInfo().GetBindTopics() {
			list = append(list, topic.GetTopicId())
		}
	}
	log.DebugWithCtx(ctx, "GetActivitiesFeeds uid:%d, configTabId:%s, topicList:%+v",
		p.serviceInfo.UserID, p.in.GetGameRecommendationStreamReq().GetConfigTabSetId(), list)

	rcmdReq := &rcmdPB.GetRcmdGamePostReq{
		Uid:                p.serviceInfo.UserID,
		TabId:              p.in.GetGameRecommendationStreamReq().GetTabId(),
		TopicIdList:        list,
		GetMode:            rcmdPB.GetRcmdGamePostReq_GetListMode(req.GetGetMode()),
		NoBrowsePostIdList: req.GetBrowseList(),
		Limit:              config.GetFeedsConfig().GetRcmdPostPageSize(),
		PostTabId:          p.in.GetGameRecommendationStreamReq().GetConfigTabSetId(),
		RecommendType:      req.GetRecRuleType(),
	}

	return rcmdReq, nil
}
