package mongo

import (
	"context"
	"time"

	"gitlab.ttyuyin.com/tyr/x/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	pb "golang.52tt.com/protocol/services/aigc/aigc-game"
	"golang.52tt.com/services/aigc/aigc-game/internal/entity"
	"golang.52tt.com/services/aigc/aigc-game/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-game/internal/store"
)

type playGamesStore struct {
	playGames *mongo.Collection
}

func NewPlayGamesStore(ctx context.Context, db *db.MongoDB) (store.PlayGamesStore, error) {
	st := &playGamesStore{
		playGames: db.Database().Collection("play_games"),
	}

	_, err := st.playGames.Indexes().CreateMany(ctx, (&playGame{}).indexes())
	if err != nil {
		log.ErrorWithCtx(ctx, "NewPlayGamesStore collection(play_games) CreateMany err: %v", err)
		return nil, err
	}

	return st, nil
}

func (s *playGamesStore) Save(ctx context.Context, playGame *entity.PlayGame) error {
	model := playGameModel(playGame)
	_, err := s.playGames.UpdateOne(
		ctx,
		bson.M{
			"role_id": model.RoleId,
			"game_id": model.GameId,
			"uid":     model.Uid,
		},
		bson.M{
			"$set": model,
		},
		options.Update().SetUpsert(true),
	)
	return err
}

func (s *playGamesStore) Top(ctx context.Context, roleId, topicId uint32, lastTime time.Time, src pb.GameSource, limit uint32) (entity.TopPlayGameList, error) {
	matchVal := bson.D{
		{Key: "role_id", Value: roleId},
		{Key: "last_time", Value: bson.D{{Key: "$gte", Value: lastTime.Unix()}}},
	}
	if topicId > 0 {
		matchVal = append(matchVal, bson.E{Key: "topic_id", Value: topicId})
	}
	if src > 0 {
		matchVal = append(matchVal, bson.E{Key: "creator", Value: src})
	}

	pipeline := mongo.Pipeline{
		// match stage
		bson.D{
			{
				Key:   "$match",
				Value: matchVal,
			},
		},
		// group stage
		bson.D{
			{
				Key: "$group",
				Value: bson.D{
					{Key: "_id", Value: "$game_id"},
					{Key: "user_num", Value: bson.D{{Key: "$sum", Value: 1}}},
				},
			},
		},
		// sort stage
		bson.D{
			{
				Key:   "$sort",
				Value: bson.D{{Key: "user_num", Value: -1}},
			},
		},
		// limit stage
		bson.D{
			{Key: "$limit", Value: limit},
		},
	}

	cursor, err := s.playGames.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}

	var results []*topPlayGame
	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	list := make(entity.TopPlayGameList, 0, len(results))
	for _, result := range results {
		list = append(list, topPlayGameEntity(result))
	}

	return list, nil
}

type playGame struct {
	Id primitive.ObjectID `bson:"_id,omitempty"`

	Uid     uint32 `bson:"uid"`
	RoleId  uint32 `bson:"role_id"`
	TopicId uint32 `bson:"topic_id"`

	GameId  string `bson:"game_id"`
	Creator int32  `bson:"creator"`

	LastTime int64 `bson:"last_time"`
}

func (g *playGame) indexes() []mongo.IndexModel {
	return []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "role_id", Value: 1},
				{Key: "game_id", Value: 1},
				{Key: "uid", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			// 用于查询最近最多人玩的玩法
			Keys: bson.D{
				{Key: "role_id", Value: 1},
				{Key: "last_time", Value: -1},
			},
		},
	}
}

type topPlayGame struct {
	GameId  string `bson:"_id"`
	UserNum uint32 `bson:"user_num"`
}

func playGameModel(en *entity.PlayGame) *playGame {
	model := &playGame{
		Uid:     en.Uid,
		RoleId:  en.RoleId,
		TopicId: en.TopicId,

		GameId:  en.GameId,
		Creator: int32(en.Source),

		LastTime: en.LastTime.Unix(),
	}

	return model
}

func topPlayGameEntity(model *topPlayGame) *entity.TopPlayGame {
	en := &entity.TopPlayGame{
		GameId:  model.GameId,
		UserNum: model.UserNum,
	}

	return en
}
