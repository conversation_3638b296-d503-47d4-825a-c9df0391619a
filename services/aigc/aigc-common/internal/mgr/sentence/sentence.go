package sentence

import (
	"context"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	"golang.52tt.com/services/aigc/aigc-common/internal/cache"
	"golang.52tt.com/services/aigc/aigc-common/internal/cache/redis"
	"golang.52tt.com/services/aigc/aigc-common/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-common/internal/store"
)

type sentenceMgr struct {
	sentenceCache      cache.SentenceCache
	sentenceMongoStore store.SentenceStore
}

func NewSentenceMgr(sentenceCache cache.SentenceCache, sentenceMongoStore store.SentenceStore) mgr.SentenceMgr {
	return &sentenceMgr{
		sentenceCache:      sentenceCache,
		sentenceMongoStore: sentenceMongoStore,
	}
}

func (m *sentenceMgr) AddSentenceCount(ctx context.Context, uid uint32, entityInst *pb.Entity, sentenceType uint32, count uint32) error {
	return m.sentenceCache.IncrTodaySpecifiedSentenceCount(ctx, uid, redis.EncodeSpecifiedSentenceField(sentenceType, entityInst), count)
}

func (m *sentenceMgr) GetSentenceCountByTypes(ctx context.Context, uid uint32, sentenceTypes []uint32, entityInst *pb.Entity) (map[string]uint32, error) {
	ids := make([]string, 0, len(sentenceTypes))
	for _, sentenceType := range sentenceTypes {
		ids = append(ids, redis.EncodeSpecifiedSentenceField(sentenceType, entityInst))
	}
	return m.sentenceCache.BatGetTodaySpecifiedSentenceCount(ctx, uid, ids...)
}

func (m *sentenceMgr) GetUserCount(ctx context.Context, uid uint32, entityType uint32, sentenceType uint32) (uint32, error) {
	return m.sentenceCache.GetTodayUserCount(ctx, redis.EncodeUserCountField(uid, entityType, sentenceType))
}

func (m *sentenceMgr) AddUserCount(ctx context.Context, uid uint32, entityType uint32, sentenceType uint32) error {
	return m.sentenceCache.IncrTodayUserCount(ctx, redis.EncodeUserCountField(uid, entityType, sentenceType), 1)
}

func (m *sentenceMgr) AddBussCount(ctx context.Context, uid, businessType,
	sentenceType uint32, count int64) (uint32, error) {
	return m.sentenceCache.IncrOrDecrTodayBussCount(ctx, uid, sentenceType, redis.EncodeBussCountField(businessType), count)

}

func (m *sentenceMgr) BatAddBussCount(ctx context.Context, uids []uint32,
	businessType, sentenceType uint32, count int64) (map[uint32]uint32, error) {
	return m.sentenceCache.BatIncrOrDecrTodayBussCount(ctx, uids, sentenceType, redis.EncodeBussCountField(businessType), count)

}

func (m *sentenceMgr) GetTotalAwardCountByTypes(ctx context.Context, uid, businessType uint32,
	rewardTypes []uint32) (map[uint32]uint32, error) {
	return m.sentenceMongoStore.GetTotalAwardCountByTypes(ctx, uid, businessType, rewardTypes)
}
