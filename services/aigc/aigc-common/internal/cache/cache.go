package cache

import (
	"context"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	"golang.52tt.com/services/aigc/aigc-common/internal/entity"
)

type SentenceCache interface {
	IncrTodaySpecifiedSentenceCount(ctx context.Context, uid uint32, id string, incr uint32) error
	BatGetTodaySpecifiedSentenceCount(ctx context.Context, uid uint32, ids ...string) (map[string]uint32, error)
	IncrTodayUserCount(ctx context.Context, id string, incr uint32) error
	GetTodayUserCount(ctx context.Context, ids string) (uint32, error)

	IncrOrDecrTodayBussCount(ctx context.Context, uid, sentenceType uint32, id string, incr int64) (uint32, error)
	BatIncrOrDecrTodayBussCount(ctx context.Context, uids []uint32, sentenceType uint32,
		id string, incr int64) (map[uint32]uint32, error)
	BatUpdateTodayCount(ctx context.Context, params []*entity.SentenceCountParams) ([]uint32, error)
	BatGetTodayCount(ctx context.Context, uid, businessType, roleType uint32, sentenceTypes []uint32, entityInst *pb.Entity) (
		map[uint32]uint32, error)
	GetTotalCountByType(ctx context.Context, uid, sentenceType, businessType uint32) (uint32, error)
	BatCoverTodayCount(ctx context.Context, params []*entity.SentenceCountParams) ([]uint32, error)
}

type DelayMsgCache interface {
	AddToPool(ctx context.Context, pushTo pb.PushTo, shouldSendTime int64, taskId string) error
	GetTaskIdsFromPool(ctx context.Context, pushTo pb.PushTo, count int64) ([]string, error)
	AddTaskRecord(ctx context.Context, record *entity.DelayRecord) error
	BatGetTaskRecord(ctx context.Context, taskId ...string) ([]*entity.DelayRecord, error)
	BatDeleteTaskRecord(ctx context.Context, taskId ...string) error
	BatDelFromPool(ctx context.Context, pushTo pb.PushTo, taskIds ...string) error
	BatAddToPool(ctx context.Context, taskIds []string, pushTo pb.PushTo, delayTime int64) error
}
