package redis

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	aigc_soulmate_middle "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	"golang.52tt.com/services/aigc/aigc-common/internal/entity"
	"strconv"
	"time"
)

/*
	Field
	除桌宠外今日句数: default
	桌宠今日句数：${roleType}
	专属句数：default:${entityType}:${entityId}
	额外句数：default

*/

func (c *sentenceRedisCache) encodeTodayCountField(sentenceType, roleType uint32, en *pb.Entity) string {
	field := "default"
	if aigc_soulmate_middle.SentenceType(sentenceType) == aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED &&
		en != nil {
		field = fmt.Sprintf("%s:%d:%d", field, en.GetType(), en.GetId())
	} else if aigc_soulmate_middle.SentenceType(sentenceType) == aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_CUR_DAY {
		if aigc_soulmate.AIRoleType(roleType) == aigc_soulmate.AIRoleType_AIRoleTypePet {
			field = field + ":" + "pet"
		}
	}

	return field
}

/*
	Key
	今日句数，专属句数:sentence:today:${uid}:${sentenceType}:${businessType}:${timestamp}
*/
func (c *sentenceRedisCache) keyOfTodayCount(uid, sentenceType, businessType uint32) (key string) {
	return fmt.Sprintf("sentence:today:%d:%d:%d:%s", uid, sentenceType, businessType, time.Now().Format("2006-01-02"))
}

func (c *sentenceRedisCache) BatUpdateTodayCount(ctx context.Context, params []*entity.SentenceCountParams) ([]uint32, error) {
	var incrResList []*redis.IntCmd
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		for _, param := range params {
			key := c.keyOfTodayCount(param.Uid, param.SentenceType, param.BusinessType)
			field := c.encodeTodayCountField(param.SentenceType, param.RoleType, param.En)
			incrResList = append(incrResList, pl.HIncrBy(ctx, key, field, param.Incr))
			_ = pl.Expire(ctx, key, sentenceCountRecordTTL)
		}
		return nil
	}); err != nil {
		return nil, err
	}
	res := make([]uint32, 0, len(params))
	for i, incrRes := range incrResList {
		uid := params[i].Uid
		curNum, err := incrRes.Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "BatUpdateTodayCount uid:%d params:%v err:%v",
				uid, params[uid], err)
			continue
		}
		if curNum < 0 {
			log.WarnWithCtx(ctx, "BatUpdateTodayCount curNum < 0, uid:%d param:%v curNum:%d",
				uid, params[uid], curNum)
			curNum = 0
		}
		res = append(res, uint32(curNum))
	}

	return res, nil
}

func (c *sentenceRedisCache) BatGetTodayCount(ctx context.Context, uid, businessType, roleType uint32, sentenceTypes []uint32, entityInst *pb.Entity) (
	map[uint32]uint32, error) {
	res := make(map[uint32]uint32, len(sentenceTypes))
	hGetList := make([]*redis.StringCmd, 0, len(sentenceTypes))
	c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		for _, sentenceType := range sentenceTypes {
			key := c.keyOfTodayCount(uid, sentenceType, businessType)
			field := c.encodeTodayCountField(sentenceType, roleType, entityInst)
			hGetList = append(hGetList, pl.HGet(ctx, key, field))
		}
		return nil
	})
	for i, sentenceType := range sentenceTypes {
		val, err := hGetList[i].Result()
		if err != nil {
			if redis.IsNil(err) {
				res[sentenceType] = 0

			} else {
				log.ErrorWithCtx(ctx, "BatGetTodayCount uid:%d sentenceType:%d businessType:%d err:%v",
					uid, sentenceType, businessType, err)
			}
			continue
		}
		if val == "" {
			res[sentenceTypes[i]] = 0
			continue
		}
		num, _ := strconv.Atoi(val)
		res[sentenceTypes[i]] = uint32(num)
	}
	return res, nil
}

func (c *sentenceRedisCache) GetTotalCountByType(ctx context.Context, uid, sentenceType, businessType uint32) (uint32, error) {
	key := c.keyOfTodayCount(uid, sentenceType, businessType)
	var cursor uint64
	var allFields = make(map[string]string)

	for {
		// 每次迭代获取部分字段
		fields, newCursor, err := c.cmder.HScan(ctx, key, cursor, "", 100).Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "getRoleSpecifiedTotal uid:%d HScan error: %v", uid, err)
			break
		}

		// 将本次迭代获取的字段添加到结果 map 中
		for i := 0; i < len(fields); i += 2 {
			allFields[fields[i]] = fields[i+1]
		}

		// 当游标返回 0 时，表示扫描完成
		if newCursor == 0 {
			break
		}
		cursor = newCursor
	}
	totalCount := 0
	for _, v := range allFields {
		count, err := strconv.Atoi(v)
		if err != nil {
			log.ErrorWithCtx(ctx, "getRoleSpecifiedTotal uid:%d strconv.Atoi error: %v", uid, err)
			continue
		}
		if count <= 0 {
			log.WarnWithCtx(ctx, "getRoleSpecifiedTotal uid:%d count <= 0, count:%d", uid, count)
			continue
		}
		totalCount += count
	}

	return uint32(totalCount), nil
}

func (c *sentenceRedisCache) BatCoverTodayCount(ctx context.Context, params []*entity.SentenceCountParams) ([]uint32, error) {
	var incrResList []*redis.IntCmd
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		for _, param := range params {
			key := c.keyOfTodayCount(param.Uid, param.SentenceType, param.BusinessType)
			field := c.encodeTodayCountField(param.SentenceType, param.RoleType, param.En)
			incrResList = append(incrResList, pl.HSet(ctx, key, field, param.Incr))
			_ = pl.Expire(ctx, key, sentenceCountRecordTTL)
		}
		return nil
	}); err != nil {
		return nil, err
	}
	res := make([]uint32, 0, len(params))
	for i, incrRes := range incrResList {
		uid := params[i].Uid
		curNum, err := incrRes.Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "BatUpdateTodayCount uid:%d params:%v err:%v",
				uid, params[uid], err)
			continue
		}
		if curNum < 0 {
			log.WarnWithCtx(ctx, "BatUpdateTodayCount curNum < 0, uid:%d param:%v curNum:%d",
				uid, params[uid], curNum)
			curNum = 0
		}
		res = append(res, uint32(curNum))
	}

	return res, nil
}