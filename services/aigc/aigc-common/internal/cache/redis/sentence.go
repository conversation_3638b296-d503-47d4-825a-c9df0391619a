package redis

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tyr/x/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	"golang.52tt.com/services/aigc/aigc-common/internal/cache"
	"golang.52tt.com/services/aigc/aigc-common/internal/infra/db"
	"strconv"
	"strings"
	"time"

	"github.com/gookit/goutil/strutil"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
)

const (
	sentenceCountRecordTTL = 24 * time.Hour
)

type sentenceRedisCache struct {
	cmder *db.RedisDB
}

func NewSentenceRedisCache(database *db.RedisDB) cache.SentenceCache {
	c := &sentenceRedisCache{
		cmder: database,
	}

	return c
}

func (c *sentenceRedisCache) IncrTodaySpecifiedSentenceCount(ctx context.Context, uid uint32, id string, incr uint32) error {
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		key := c.keyOfTodaySpecifiedCount(uid)
		_ = pl.HIncrBy(ctx, key, id, int64(incr))
		_ = pl.Expire(ctx, key, sentenceCountRecordTTL)
		return nil
	}); err != nil {
		return err
	}

	return nil
}

func (c *sentenceRedisCache) BatGetTodaySpecifiedSentenceCount(ctx context.Context, uid uint32, ids ...string) (map[string]uint32, error) {
	vals, err := c.cmder.HMGet(ctx, c.keyOfTodaySpecifiedCount(uid), ids...).Result()
	if err != nil {
		return nil, err
	}
	res := make(map[string]uint32, len(ids))
	for index, id := range ids {
		if val := vals[index]; val != nil {
			if strCnt, ok := val.(string); ok {
				count, err := strconv.Atoi(strCnt)
				if err != nil {
					log.ErrorWithCtx(ctx, "GetTodaySpecifiedSentenceCount HMGet type err, info:%v, index:%d", val, index)
					continue
				}
				res[id] = uint32(count)
			} else {
				log.ErrorWithCtx(ctx, "GetTodaySpecifiedSentenceCount HMGet type err, info:%v, index:%d", val, index)
			}
		}
	}
	return res, nil
}

func (c *sentenceRedisCache) keyOfTodaySpecifiedCount(uid uint32) string {
	return fmt.Sprintf("sentence:specified:%d:%s", uid, time.Now().Format("2006-01-02"))
}

func EncodeSpecifiedSentenceField(sentenceType uint32, en *pb.Entity) string {
	id := fmt.Sprintf("%d:%d:%d", en.GetType(), en.GetId(), sentenceType)
	return id
}

func DecodeSpecifiedSentenceField(id string) (uid uint32, sentenceType uint32, en *pb.Entity) {
	en = new(pb.Entity)
	seg := strings.Split(id, ":")
	switch len(seg) {
	case 3:
		en.Type = pb.Entity_Type(strutil.SafeUint(seg[0]))
		en.Id = uint32(strutil.SafeUint(seg[1]))
		sentenceType = uint32(strutil.SafeUint(seg[2]))
	case 4:
		uid = uint32(strutil.SafeUint(seg[0]))
		en.Type = pb.Entity_Type(strutil.SafeUint(seg[1]))
		en.Id = uint32(strutil.SafeUint(seg[2]))
		sentenceType = uint32(strutil.SafeUint(seg[3]))
	}

	return
}

func (c *sentenceRedisCache) keyOfTodayUserCount() string {
	return fmt.Sprintf("sentence:total:%s", time.Now().Format("2006-01-02"))
}

func (c *sentenceRedisCache) IncrTodayUserCount(ctx context.Context, id string, incr uint32) error {
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		key := c.keyOfTodayUserCount()
		_ = pl.HIncrBy(ctx, key, id, int64(incr))
		_ = pl.Expire(ctx, key, sentenceCountRecordTTL)
		return nil
	}); err != nil {
		return err
	}

	return nil
}

func (c *sentenceRedisCache) GetTodayUserCount(ctx context.Context, ids string) (uint32, error) {
	val, err := c.cmder.HGet(ctx, c.keyOfTodayUserCount(), ids).Result()
	if err != nil {
		if redis.IsNil(err) {
			return 0, nil
		}
		return 0, err
	}
	res, err := strconv.Atoi(val)
	if err != nil {
		return 0, err
	}
	return uint32(res), nil
}

// EncodeUserCountField uid:entityType:sentenceType
func EncodeUserCountField(uid uint32, entityType uint32, sentenceType uint32) string {
	return fmt.Sprintf("%d:%d:%d", uid, entityType, sentenceType)
}

func (c *sentenceRedisCache) keyOfTodayBussCount(uid uint32, sentenceType uint32) string {
	return fmt.Sprintf("sentence:buss:%d:%d:%s", uid, sentenceType, time.Now().Format("2006-01-02"))
}

func (c *sentenceRedisCache) IncrOrDecrTodayBussCount(ctx context.Context, uid, sentenceType uint32, id string, incr int64) (uint32, error) {
	var incrRes *redis.IntCmd
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		key := c.keyOfTodayBussCount(uid, sentenceType)
		incrRes = pl.HIncrBy(ctx, key, id, incr)
		_ = pl.Expire(ctx, key, sentenceCountRecordTTL)
		return nil
	}); err != nil {
		return 0, err
	}

	curNum, err := incrRes.Result()
	if curNum < 0 {
		log.WarnWithCtx(ctx, "IncrOrDecrTodayBussCount curNum < 0, uid:%d sentencedType:%d id:%s, incr:%d curNum:%d",
			uid, sentenceType, id, incr, curNum)
		curNum = 0
	}
	return uint32(curNum), err
}

func EncodeBussCountField(businessType uint32) string {
	return strconv.Itoa(int(businessType))
}

func (c *sentenceRedisCache) BatIncrOrDecrTodayBussCount(ctx context.Context, uids []uint32, sentenceType uint32,
	id string, incr int64) (map[uint32]uint32, error) {
	var incrResList []*redis.IntCmd
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		for _, uid := range uids {
			key := c.keyOfTodayBussCount(uid, sentenceType)
			incrResList = append(incrResList, pl.HIncrBy(ctx, key, id, incr))
			_ = pl.Expire(ctx, key, sentenceCountRecordTTL)
		}
		return nil
	}); err != nil {
		return nil, err
	}
	resMap := make(map[uint32]uint32, len(uids))
	for i, incrRes := range incrResList {
		uid := uids[i]
		curNum, err := incrRes.Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "BatIncrOrDecrTodayBussCount uid:%d sentenceType:%d id:%s, incr:%d err:%v",
				uid, sentenceType, id, incr, err)
			continue
		}
		if curNum < 0 {
			log.WarnWithCtx(ctx, "BatIncrOrDecrTodayBussCount curNum < 0, uid:%d sentencedType:%d id:%s, incr:%d curNum:%d",
				uid, sentenceType, id, incr, curNum)
			curNum = 0
		}
		resMap[uid] = uint32(curNum)
	}

	return resMap, nil
}
