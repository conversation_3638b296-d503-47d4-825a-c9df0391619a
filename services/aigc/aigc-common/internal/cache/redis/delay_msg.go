package redis

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	"golang.52tt.com/services/aigc/aigc-common/internal/cache"
	"golang.52tt.com/services/aigc/aigc-common/internal/entity"
	"golang.52tt.com/services/aigc/aigc-common/internal/infra/db"
)

const day = 24 * 60 * 60

type delayMsgRedisCache struct {
	cmder *db.RedisDB
}

func NewDelayMsgRedisCache(database *db.RedisDB) cache.DelayMsgCache {
	c := &delayMsgRedisCache{
		cmder: database,
	}

	return c
}

func (c *delayMsgRedisCache) keyOfMsgPool(pushTo pb.PushTo) string {
	return "delay_pool:" + pushTo.String()
}

func (c *delayMsgRedisCache) keyOfTask(taskId string) string {
	return "delay_task:" + taskId
}

func (c *delayMsgRedisCache) AddToPool(ctx context.Context, pushTo pb.PushTo, shouldSendTime int64, taskId string) error {
	key := c.keyOfMsgPool(pushTo)
	_, err := c.cmder.ZAdd(ctx, key, &redis.Z{
		Score:  float64(shouldSendTime),
		Member: taskId,
	}).Result()
	if err != nil {
		return err
	}
	return nil
}

func (c *delayMsgRedisCache) GetTaskIdsFromPool(ctx context.Context, pushTo pb.PushTo, count int64) ([]string, error) {
	key := c.keyOfMsgPool(pushTo)
	vals, err := c.cmder.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min:    "-inf",
		Max:    strconv.FormatInt(time.Now().Unix(), 10),
		Offset: 0,
		Count:  count,
	}).Result()
	if err != nil {
		return nil, err
	}
	return vals, nil
}

func (c *delayMsgRedisCache) AddTaskRecord(ctx context.Context, record *entity.DelayRecord) error {
	key := c.keyOfTask(record.TaskId)
	marshalByte, err := json.Marshal(record)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddTaskRecord json.Marshal failed, taskId:%s, err:%v", record.TaskId, err)
		return err
	}
	_, err = c.cmder.Set(ctx, key, marshalByte, time.Duration(record.DelayTime+60)*time.Second).Result()
	if err != nil {
		return err
	}
	return nil
}

func (c *delayMsgRedisCache) BatGetTaskRecord(ctx context.Context, taskId ...string) ([]*entity.DelayRecord, error) {
	keys := make([]string, 0, len(taskId))
	for _, id := range taskId {
		keys = append(keys, c.keyOfTask(id))
	}
	vals, err := c.cmder.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, err
	}
	res := make([]*entity.DelayRecord, 0, len(vals))
	for i := range vals {
		if vals[i] == nil {
			log.ErrorWithCtx(ctx, "BatGetTaskRecord taskId:%s not found", taskId[i])
			continue
		}
		var record entity.DelayRecord
		err = json.Unmarshal([]byte(vals[i].(string)), &record)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatGetTaskRecord json.Unmarshal failed, taskId:%s, err:%v", taskId[i], err)
			continue
		}
		res = append(res, &record)
	}
	return res, nil
}

func (c *delayMsgRedisCache) BatDeleteTaskRecord(ctx context.Context, taskId ...string) error {
	keys := make([]string, 0, len(taskId))
	for _, id := range taskId {
		keys = append(keys, c.keyOfTask(id))
	}
	_, err := c.cmder.Del(ctx, keys...).Result()
	if err != nil {
		return err
	}
	return nil
}

func (c *delayMsgRedisCache) BatDelFromPool(ctx context.Context, pushTo pb.PushTo, taskIds ...string) error {
	key := c.keyOfMsgPool(pushTo)
	_, err := c.cmder.ZRem(ctx, key, taskIds).Result()
	if err != nil {
		return err
	}
	return nil
}

func (c *delayMsgRedisCache) BatAddToPool(ctx context.Context, taskIds []string, pushTo pb.PushTo, delayTime int64) error {
	if len(taskIds) == 0 {
		log.WarnWithCtx(ctx, "BatAddToPool taskIds is empty")
		return nil
	}
	members := make([]*redis.Z, 0, len(taskIds))
	curTime := time.Now().Unix()
	for _, taskId := range taskIds {
		members = append(members, &redis.Z{
			Score:  float64(curTime + delayTime),
			Member: taskId,
		})
	}
	key := c.keyOfMsgPool(pushTo)

	_, err := c.cmder.ZAdd(ctx, key, members...).Result()
	if err != nil {
		return err
	}
	return nil
}
