package mgr

import (
	"context"

	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	"golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
)

//go:generate mockgen -destination=mocks/msg.go -package=mocks golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/mgr MsgManager
type MsgManger interface {
	SendSingleMsg(ctx context.Context, uid, partnerId uint32, msg *chat_bot.SendingMsg, opt *chat_bot.SendOption) error
	SendSpecialSingleMsg(ctx context.Context, uid, partnerId uint32, specialMsgType rcmd_ai_partner.ReceiveSpecialMsgFromUserReq_SpecialMsgType) error
}

//go:generate mockgen -destination=mocks/gift.go -package=mocks golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/mgr GiftManager
type GiftManager interface {
	PickPartnerGift(ctx context.Context, uid uint32, partner *aigc_soulmate.AIPartner, price float64, isFirst bool) (AIGift, error)
	GivePartnerChatSentence(ctx context.Context, uid uint32, partner *aigc_soulmate.AIPartner, num uint32) error
	GivePartnerChatBackground(ctx context.Context, uid uint32, partner *aigc_soulmate.AIPartner, background *aigc_intimacy.ChatBackground) error
}
