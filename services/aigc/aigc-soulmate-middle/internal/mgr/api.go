package mgr

import (
	"context"

	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
)

//go:generate mockgen -destination=mocks/gift.go -package=mocks golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/mgr GiftManager
type GiftManager interface {
	PickPartnerGift(ctx context.Context, uid uint32, partner *aigc_soulmate.AIPartner, price float64, isFirst bool) (AIGift, error)
	SendPartnerThankPresentMsg(ctx context.Context, uid, partnerId uint32, presentConfig *userpresent_go.PresentBaseConfig) error
	GivePartnerChatSentence(ctx context.Context, uid uint32, partner *aigc_soulmate.AIPartner, num uint32) error
	GivePartnerChatBackground(ctx context.Context, uid uint32, partner *aigc_soulmate.<PERSON><PERSON><PERSON><PERSON>, background *aigc_intimacy.ChatBackground) error
}
