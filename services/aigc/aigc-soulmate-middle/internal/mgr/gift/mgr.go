package gift

import (
	"crypto/rand"
	"math/big"
	"sort"

	"gitlab.ttyuyin.com/tyr/x/log"
	"golang.52tt.com/kaihei-pkg/aigc/msg"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
	config "golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/config/ttconfig/aigc_soulmate_middle"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/report"
)

type manager struct {
	sender   msg.Sender
	reporter report.Reporter

	aigcCommonClient    aigc_common.AigcCommonClient
	aigcIntimacyClient  aigc_intimacy.AigcIntimacyClient
	rcmdAIPartnerClient rcmd_ai_partner.RCMDAIPartnerClient
}

func New(
	sender msg.Sender,
	reporter report.Reporter,

	aigcCommonClient aigc_common.AigcCommonClient,
	aigcIntimacyClient aigc_intimacy.AigcIntimacyClient,
	rcmdAIPartnerClient rcmd_ai_partner.RCMDAIPartnerClient,
) *manager {
	return &manager{
		sender:   sender,
		reporter: reporter,

		aigcCommonClient:    aigcCommonClient,
		aigcIntimacyClient:  aigcIntimacyClient,
		rcmdAIPartnerClient: rcmdAIPartnerClient,
	}
}

// isHitProbability 是否命中概率
func isHitProbability(probability int) bool {
	n, err := rand.Int(rand.Reader, big.NewInt(100))
	if err != nil {
		log.Errorf("isHitProbability rand.Int err: %v", err)
		return false
	}

	log.Debugf("isHitProbability number:%d probability: %d", n, probability)
	return int(n.Int64()) < probability
}

// searchReturnGiftConfig 查找回礼配置
func searchReturnGiftConfig(price float64) (cfg config.ReturnGiftConfig, ok bool) {
	if price == 0 {
		return
	}

	configs := config.GetAigcSoulmateMiddleConfig().GetReturnGiftConfigs()
	if len(configs) == 0 {
		return
	}

	sort.Slice(configs, func(i int, j int) bool {
		return configs[i].Price < configs[j].Price
	})

	i := sort.Search(len(configs), func(i int) bool {
		return configs[i].Price >= price
	})
	if i >= len(configs) {
		i = len(configs) - 1
	}

	return configs[i], true
}
