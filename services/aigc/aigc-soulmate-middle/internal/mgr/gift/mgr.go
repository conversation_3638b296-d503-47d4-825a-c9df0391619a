package gift

import (
	"context"
	"math/rand"
	"sort"
	"time"

	"github.com/pkg/errors"
	"gitlab.ttyuyin.com/tyr/x/log"
	"gitlab.ttyuyin.com/tyr/x/metadata"

	"golang.52tt.com/kaihei-pkg/aigc/msg/builder"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	aigc_trigger "golang.52tt.com/protocol/services/aigc/aigc-trigger"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	config "golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/config/ttconfig/aigc_soulmate_middle"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/report"
)

type manager struct {
	msgMgr mgr.MsgManger

	reporter report.Reporter

	aigcCommonClient   aigc_common.AigcCommonClient
	aigcIntimacyClient aigc_intimacy.AigcIntimacyClient

	rand *rand.Rand
}

func New(
	msgMgr mgr.MsgManger,

	reporter report.Reporter,

	aigcCommonClient aigc_common.AigcCommonClient,
	aigcIntimacyClient aigc_intimacy.AigcIntimacyClient,
) *manager {
	return &manager{
		msgMgr: msgMgr,

		reporter: reporter,

		aigcCommonClient:   aigcCommonClient,
		aigcIntimacyClient: aigcIntimacyClient,

		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// PickPartnerGift 挑选partner回礼
func (m *manager) PickPartnerGift(ctx context.Context, uid uint32, partner *aigc_soulmate.AIPartner, price float64, isFirst bool) (mgr.AIGift, error) {
	gift := mgr.AIGift{
		Type: mgr.AIGiftTypeNone,
	}

	// 根据送礼金额查找回礼配置
	returnGiftConfig, ok := searchReturnGiftConfig(price)
	if !ok {
		log.WarnWithCtx(ctx, "PickPartnerGift searchReturnGiftConfig price(%f) not found", price)
		return gift, nil
	}

	log.InfoWithCtx(ctx, "PickPartnerGift uid(%d) partner(%d) returnGiftConfig: %+v", uid, partner.GetId(), returnGiftConfig)

	// 判断是否要回礼
	if !isFirst && !m.isHitProbability(returnGiftConfig.Probability) {
		return gift, nil
	}

	todayRemainSentence, err := m.getSingleChatTodayRemainSentence(ctx, uid, uint32(partner.GetRole().GetType()))
	if err != nil {
		return gift, err
	}

	// 今日剩余句数充足并且随机到了聊天背景，尝试获取未解锁背景作为回礼
	if todayRemainSentence > config.GetAigcSoulmateMiddleConfig().GetReturnGiftRemainSentence() &&
		m.isHitProbability(config.GetAigcSoulmateMiddleConfig().GetReturnGiftChatBackgroundProbability()) {
		getBackgroundReq := &aigc_intimacy.GetAvailableBackgroundRequest{
			Uid: uid,
			Entity: &aigc_intimacy.Entity{
				Id:   partner.GetId(),
				Type: aigc_intimacy.Entity_TYPE_PARTNER,
			},
			BindEntity: &aigc_intimacy.BindEntity{
				Id:   partner.GetRole().GetId(),
				Type: aigc_intimacy.BindEntity_ENTITY_TYPE_ROLE,
			},
		}
		getBackgroundResp, err := m.aigcIntimacyClient.GetAvailableBackground(ctx, getBackgroundReq)
		if err != nil {
			return gift, errors.WithMessagef(err, "GetAvailableBackground req(%+v)", getBackgroundReq)
		}

		// 筛选未解锁的送礼背景
		var lockBackgrounds []*aigc_intimacy.ChatBackground
		for _, background := range getBackgroundResp.GetLockBackgrounds() {
			if background.GetType() == aigc_intimacy.ChatBackgroundType_CHAT_BACKGROUND_TYPE_PRESENT {
				lockBackgrounds = append(lockBackgrounds, background)
			}
		}

		// 随机选择一个未解锁的背景
		if len(lockBackgrounds) > 0 {
			gift.Type = mgr.AIGiftTypeChatBackground
			gift.ChatBackground = lockBackgrounds[rand.Intn(len(lockBackgrounds))]
			return gift, nil
		} else {
			log.InfoWithCtx(ctx, "PickPartnerGift uid(%d) role(%d) lockBackground empty", uid, partner.GetRole().GetId())
		}
	}

	// 今日句数不足或者已解锁所有送礼背景，抽取句数作为回礼
	gift.Type = mgr.AIGiftTypeSentence
	gift.SentenceNum = returnGiftConfig.ExtraSentences[rand.Intn(len(returnGiftConfig.ExtraSentences))]

	return gift, nil
}

// GivePartnerChatBackground 发放partner聊天背景
func (m *manager) GivePartnerChatBackground(ctx context.Context, uid uint32, partner *aigc_soulmate.AIPartner, background *aigc_intimacy.ChatBackground) error {
	grantReq := &aigc_intimacy.GrantChatBackgroundRequest{
		Uid:  uid,
		BgId: background.GetId(),
		Entity: &aigc_intimacy.BindEntity{
			Id:   partner.GetRole().GetId(),
			Type: aigc_intimacy.BindEntity_ENTITY_TYPE_ROLE,
		},
	}
	if _, err := m.aigcIntimacyClient.GrantChatBackground(ctx, grantReq); err != nil {
		return errors.WithMessagef(err, "GrantChatBackground req(%+v)", grantReq)
	}

	// 发送ai背景回礼消息
	m.sendGiftMsg(
		ctx,
		uid,
		partner.GetId(),
		builder.BuildChatBackgroundGiftMsg(partner.GetName(), background.GetName(), background.GetImageUrl()),
	)

	// 数据上报
	m.reporter.ReportReturnGift(ctx, report.ReturnGiftData{
		Uid: uid,

		RoleId:    partner.GetRole().GetId(),
		PartnerId: partner.GetId(),

		GiftNum:  1,
		GiftName: "聊天背景卡",

		BgId:   background.GetId(),
		BgName: background.GetName(),
	})

	return nil
}

// GivePartnerChatSentence 发放partner聊天句数
func (m *manager) GivePartnerChatSentence(ctx context.Context, uid uint32, partner *aigc_soulmate.AIPartner, num uint32) error {
	addReq := &aigc_common.AddExtraCountRequest{
		Uid:          uid,
		ExtraCount:   num,
		ExpireTime:   int64(config.GetAigcSoulmateMiddleConfig().GetReturnGiftSentenceTTL()),
		BusinessType: uint32(aigc_trigger.BusinessType_BUSINESS_TYPE_SINGLE_CHAT),
		RewardType:   uint32(aigc_common.RewardType_REWARD_TYPE_PRESENT),
	}
	if _, err := m.aigcCommonClient.AddExtraCount(ctx, addReq); err != nil {
		return errors.WithMessagef(err, "AddExtraCount req(%+v)", addReq)
	}

	// 发送ai句数回礼消息
	m.sendGiftMsg(
		ctx,
		uid,
		partner.GetId(),
		builder.BuildSentenceGiftMsg(partner.GetName(), num),
	)

	// 数据上报
	m.reporter.ReportReturnGift(ctx, report.ReturnGiftData{
		Uid: uid,

		RoleId:    partner.GetRole().GetId(),
		PartnerId: partner.GetId(),

		GiftNum:  num,
		GiftName: "句数礼包",
	})

	return nil
}

// getSingleChatTodayRemainSentence 获取单聊今日剩余句数
func (m *manager) getSingleChatTodayRemainSentence(ctx context.Context, uid, roleType uint32) (uint32, error) {
	countReq := &aigc_common.BatGetCurSentenceCountRequest{
		Uid:          uid,
		RoleType:     roleType,
		BusinessType: uint32(aigc_trigger.BusinessType_BUSINESS_TYPE_SINGLE_CHAT),
		SentenceType: []uint32{
			uint32(pb.SentenceType_SENTENCE_TYPE_CUR_DAY),
		},
		NeedAvailableExtraCount: true,
	}
	countResp, err := m.aigcCommonClient.BatGetCurSentenceCount(ctx, countReq)
	if err != nil {
		return 0, errors.WithMessagef(err, "BatGetCurSentenceCount req(%+v)", countReq)
	}

	remain := countResp.GetAvailableExtraNum()
	dayUsed := countResp.GetCurNumMap()[uint32(pb.SentenceType_SENTENCE_TYPE_CUR_DAY)]

	if dayLimit := config.GetAigcSoulmateMiddleConfig().
		GetCurDayCfgCount(uint32(aigc_trigger.BusinessType_BUSINESS_TYPE_SINGLE_CHAT), roleType); dayLimit > dayUsed {
		remain += dayLimit - dayUsed
	}

	return remain, nil
}

// searchReturnGiftConfig 查找回礼配置
func searchReturnGiftConfig(price float64) (cfg config.ReturnGiftConfig, ok bool) {
	if price == 0 {
		return
	}

	configs := config.GetAigcSoulmateMiddleConfig().GetReturnGiftConfigs()
	if len(configs) == 0 {
		return
	}

	sort.Slice(configs, func(i int, j int) bool {
		return configs[i].Price < configs[j].Price
	})

	i := sort.Search(len(configs), func(i int) bool {
		return configs[i].Price >= price
	})
	if i >= len(configs) {
		i = len(configs) - 1
	}

	return configs[i], true
}

// isHitProbability 是否命中概率
func (m *manager) isHitProbability(probability int) bool {
	return m.rand.Perm(100)[m.rand.Intn(100)] < probability
}

func (m *manager) sendGiftMsg(ctx context.Context, uid, partnerId uint32, msg *chat_bot.SendingMsg) {
	time.AfterFunc(config.GetAigcSoulmateMiddleConfig().GetReturnGiftMsgDelay(), func() {
		ctx, cancel := context.WithTimeout(metadata.NewContext(ctx), time.Second)
		defer cancel()

		_ = m.msgMgr.SendSingleMsg(
			ctx,
			uid,
			partnerId,
			msg,
			&chat_bot.SendOption{},
		)
	})
}
