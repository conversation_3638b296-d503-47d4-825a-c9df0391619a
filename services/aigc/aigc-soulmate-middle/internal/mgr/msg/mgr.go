package msg

import (
	"context"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/tyr/x/log"

	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	"golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/event"
)

type manager struct {
	eventBus event.Bus

	rcmdAIPartnerClient rcmd_ai_partner.RCMDAIPartnerClient
}

func New(eventBus event.Bus, rcmdAIPartnerClient rcmd_ai_partner.RCMDAIPartnerClient) *manager {
	return &manager{
		eventBus: eventBus,

		rcmdAIPartnerClient: rcmdAIPartnerClient,
	}
}

func (m *manager) SendSingleMsg(ctx context.Context, uid, partnerId uint32, msg *chat_bot.SendingMsg, opt *chat_bot.SendOption) error {
	return m.eventBus.Publish(
		ctx,
		event.PubNameMsg,
		event.TopicSendSingleMsg,
		strconv.Itoa(int(partnerId)),
		&chat_bot.SingleMsgSendEvent{
			Uid:       uid,
			PartnerId: partnerId,

			SendingMsg: msg,

			Opt: opt,
		},
	)
}

func (m *manager) SendSpecialSingleMsg(ctx context.Context, uid, partnerId uint32, specialMsgType rcmd_ai_partner.ReceiveSpecialMsgFromUserReq_SpecialMsgType) error {
	rpcCtx, rpcCancel := context.WithTimeout(ctx, 2*time.Second)
	defer rpcCancel()

	receiveReq := &rcmd_ai_partner.ReceiveSpecialMsgFromUserReq{
		Uid:       uid,
		PartnerId: partnerId,

		MsgType: specialMsgType,
	}
	if _, err := m.rcmdAIPartnerClient.ReceiveSpecialMsgFromUser(rpcCtx, receiveReq); err != nil {
		log.ErrorWithCtx(ctx, "SendSpecialSingleMsg ReceiveSpecialMsgFromUser req(%+v) err: %v", receiveReq, err)
		return err
	}

	log.InfoWithCtx(ctx, "SendSpecialSingleMsg ReceiveSpecialMsgFromUser req(%+v) finished", receiveReq)
	return nil
}
