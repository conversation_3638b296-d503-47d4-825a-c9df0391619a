package server

import (
	"context"

	subscriber "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/kaihei-pkg/aigc/msg/builder"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/mgr"
)

// TriggerAIPresentEvent 触发AI送礼事件
func (s *Server) TriggerAIPresentEvent(ctx context.Context, req *pb.TriggerAIPresentEventRequest) (*pb.TriggerAIPresentEventResponse, error) {
	resp := &pb.TriggerAIPresentEventResponse{}
	log.InfoWithCtx(ctx, "TriggerAIPresentEvent req: %+v", req)

	ev := req.GetEvent()
	if ev.GetFromUid() == 0 || ev.GetTargetRoleId() == 0 || ev.GetTargetPartnerId() == 0 || ev.GetItemId() == 0 || ev.GetItemCount() == 0 || ev.GetTotalPrice() == 0 {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	err, _ := s.handleAIPresentEvent(ctx, &userpresent_go.AiPresentEvent{
		FromUid:         ev.GetFromUid(),
		TargetRoleId:    ev.GetTargetRoleId(),
		TargetPartnerId: ev.GetTargetPartnerId(),
		ItemId:          ev.GetItemId(),
		ItemCount:       ev.GetItemCount(),
		TotalPrice:      ev.GetTotalPrice(),
		IsFirst:         ev.GetIsFirst(),
	})

	return resp, err
}

// onAIReceiveUserPresent 处理用户给ai送礼
func (s *Server) onAIReceiveUserPresent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	var ev userpresent_go.AiPresentEvent
	if err := proto.Unmarshal(msg.Value, &ev); err != nil {
		log.ErrorWithCtx(ctx, "onAIReceiveUserPresent Unmarshal err: %v", err)
		return err, false
	}

	log.InfoWithCtx(ctx, "onAIReceiveUserPresent ev: %+v", ev)
	return s.handleAIPresentEvent(ctx, &ev)
}

func (s *Server) handleAIPresentEvent(ctx context.Context, ev *userpresent_go.AiPresentEvent) (error, bool) {
	// 查询partner
	partnerReq := &aigc_soulmate.GetAIPartnerReq{
		Id: ev.GetTargetPartnerId(),
	}
	partnerResp, err := s.soulmateClient.GetAIPartner(ctx, partnerReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAIPresentEvent GetAIPartner req(%+v) err: %v", partnerReq, err)
		return err, true
	}
	partner := partnerResp.GetPartner()
	if partner == nil {
		log.ErrorWithCtx(ctx, "handleAIPresentEvent GetAIPartner req(%+v) not found", partnerReq)
		return nil, false
	}

	// 查询礼物配置
	presentConfigReq := &userpresent_go.GetPresentConfigByIdReq{
		ItemId: ev.GetItemId(),
	}
	presentConfigResp, err := s.userPresentClient.GetPresentConfigByIdNew(ctx, presentConfigReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAIPresentEvent GetPresentConfigByIdNew req(%+v) err: %v", presentConfigReq, err)
		return err, true
	}
	presentConfig := presentConfigResp.GetItemConfig().GetBaseConfig()
	if presentConfig == nil || userpresent_go.PresentPriceType(presentConfig.GetPriceType()) != userpresent_go.PresentPriceType_PRESENT_PRICE_TBEAN {
		log.WarnWithCtx(ctx, "handleAIPresentEvent unsupported present: %+v", presentConfig)
		return nil, false
	}

	// 发送送礼消息
	_ = s.sender.SendSingleMsg(ctx, ev.GetFromUid(), ev.GetTargetPartnerId(), builder.BuildUserPresentMsg(presentConfig), &chat_bot.SendOption{})
	// 发送谢礼消息
	_ = s.giftMgr.SendPartnerThankPresentMsg(ctx, ev.GetFromUid(), ev.GetTargetPartnerId(), presentConfig)

	// 挑选回礼
	rmb := float64(ev.GetTotalPrice()) / 100 // 转换为人民币价值
	gift, err := s.giftMgr.PickPartnerGift(ctx, ev.GetFromUid(), partner, rmb, ev.GetIsFirst())
	if err != nil {
		log.ErrorWithCtx(
			ctx,
			"handleAIPresentEvent PickPartnerGift uid(%d) partner(%d) price(%f) isFirst(%t) err: %v",
			ev.GetFromUid(), partner.GetId(), rmb, ev.GetIsFirst(), err,
		)
		return err, true
	}

	// 发放回礼
	switch gift.Type {
	case mgr.AIGiftTypeSentence: // 聊天句数
		if err := s.giftMgr.GivePartnerChatSentence(ctx, ev.GetFromUid(), partner, gift.SentenceNum); err != nil {
			log.ErrorWithCtx(ctx, "handleAIPresentEvent GivePartnerChatSentence uid(%d) partner(%d) num(%d) err: %v", ev.GetFromUid(), partner.GetId(), gift.SentenceNum, err)
			return err, true
		}
	case mgr.AIGiftTypeChatBackground: // 聊天背景
		if err := s.giftMgr.GivePartnerChatBackground(ctx, ev.GetFromUid(), partner, gift.ChatBackground); err != nil {
			log.ErrorWithCtx(ctx, "handleAIPresentEvent GivePartnerChatBackground uid(%d) partner(%d) background(%s) err: %v", ev.GetFromUid(), partner.GetId(), gift.ChatBackground.GetId(), err)
			return err, true
		}
	}

	log.InfoWithCtx(ctx, "handleAIPresentEvent ev(%+v) gift: %+v", ev, gift)
	return nil, false
}
