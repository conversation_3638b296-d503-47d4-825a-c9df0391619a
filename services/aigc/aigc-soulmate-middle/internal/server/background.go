package server

import (
	"context"
	"encoding/json"
	"errors"
	"net/url"

	subscriber "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"

	"golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/pkg/log"
	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/rcmd_mt_proxy"
)

// onChatBackgroundCallback 处理生成聊天背景回调
func (s *Server) onChatBackgroundCallback(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	const (
		obsApp   = "tt"
		obsScope = "aigc-bg"
	)

	var asyncRsp rcmd_mt_proxy.AsyncRsp
	if err := proto.Unmarshal(msg.Value, &asyncRsp); err != nil {
		log.ErrorWithCtx(ctx, "onChatBackgroundCallback Unmarshal err: %v", err)
		return err, false
	}

	log.InfoWithCtx(ctx, "onChatBackgroundCallback asyncRsp: %+v", asyncRsp)

	cbData := asyncRsp.GetCallbackData()
	if len(cbData) == 0 {
		log.ErrorWithCtx(ctx, "onChatBackgroundCallback cbData: %+v", cbData)
		return nil, false
	}
	bgData := cbData["background"]
	if len(bgData) == 0 {
		log.ErrorWithCtx(ctx, "onChatBackgroundCallback invalid bgData: %+v", bgData)
		return nil, false
	}

	var bizRsp rcmd_mt_proxy.GenSoulmateGiveGiftRoleImageRsp
	if err := json.Unmarshal([]byte(asyncRsp.GetData()), &bizRsp); err != nil {
		log.ErrorWithCtx(ctx, "onChatBackgroundCallback Unmarshal data(%s) err: %v", asyncRsp.GetData(), err)
		return err, false
	}
	if bizRsp.GetErrMsg() != "" || bizRsp.GetRoleId() == 0 || bizRsp.GetImageUrl() == "" {
		log.ErrorWithCtx(ctx, "onChatBackgroundCallback invalid bizRsp: %+v", bizRsp)
		return nil, false
	}
	if _, err := url.Parse(bizRsp.GetImageUrl()); err != nil {
		log.ErrorWithCtx(ctx, "onChatBackgroundCallback invalid ImageUrl: %s", bizRsp.GetImageUrl())
		return errors.New("invalid ImageUrl"), false
	}

	var bg aigc_intimacy.ChatBackground
	if err := json.Unmarshal([]byte(bgData), &bg); err != nil {
		log.ErrorWithCtx(ctx, "onChatBackgroundCallback Unmarshal bgData(%s) err: %v", bgData, err)
		return err, false
	}

	// 背景图转存obs
	_, _, bgUrl, _, err := s.obsGatewayClient.CopyUrl(ctx, bizRsp.GetImageUrl(), obsApp, obsScope, obsgateway.WithNoReview(), obsgateway.WithReturnUrl())
	if err != nil {
		log.ErrorWithCtx(ctx, "onChatBackgroundCallback CopyUrl srcUrl(%s) err: %v", bizRsp.GetImageUrl(), err)
		return err, true
	}

	log.InfoWithCtx(ctx, "onChatBackgroundCallback CopyUrl srcUrl(%s) bgUrl(%s) finished", bizRsp.GetImageUrl(), bgUrl)

	// 创建聊天背景
	bg.ImageUrl = bgUrl
	createBackgroundReq := &aigc_intimacy.CreateChatBackgroundRequest{
		Background: &bg,
	}
	if _, err := s.aigcIntimacyClient.CreateChatBackground(ctx, createBackgroundReq); err != nil {
		log.ErrorWithCtx(ctx, "onChatBackgroundCallback CreateChatBackground req(%+v) err: %v", createBackgroundReq, err)
		return err, false
	}

	log.InfoWithCtx(ctx, "onChatBackgroundCallback CreateChatBackground req(%+v) finished", createBackgroundReq)
	return nil, false
}
