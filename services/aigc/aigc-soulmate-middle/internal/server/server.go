package server

import (
	"context"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"

	account_go "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/clients/seqgen/v2"
	userpresent_go "golang.52tt.com/clients/userpresent-go"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/protocol/services/demo/echo"
	game_red_dot "golang.52tt.com/protocol/services/game-red-dot"
	"golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
	timeline_v2 "golang.52tt.com/protocol/services/timeline-v2"
	config "golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/config/ttconfig/aigc_soulmate_middle"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/event"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/event/eventlink"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/mgr/gift"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/mgr/msg"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/report"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/report/bylink"
)

type StartConfig struct {
	// from config file
	EventLink *middleware_event.Options `json:"event_link"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)
	err := config.InitAigcSoulmateMiddleConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer config.InitAigcSoulmateMiddleConfig err: %v", err)
		return nil, err
	}
	seqgenV2Client := seqgen.NewIClient()
	timelineV2Client, err := timeline_v2.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer timeline_v2.NewClient err: %v", err)
		return nil, err
	}

	censoringProxy := censoring_proxy.NewClient()

	accountGoClient, err := account_go.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer account_go.NewClient err: %v", err)
		return nil, err
	}

	groupClient, err := aigc_group.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer aigc_group.NewClient err: %v", err)
		return nil, err
	}
	auditInst := NewAudit(censoringProxy)

	soulmateClient, err := aigc_soulmate.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer aigc_soulmate.NewClient err: %v", err)
		return nil, err
	}

	redDotClient, err := game_red_dot.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer game_red_dot.NewClient err: %v", err)
		return nil, err
	}

	aigcCommonClient, err := aigc_common.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer aigc_common.NewClient err: %v", err)
		return nil, err
	}

	aigcIntimacyClient, err := aigc_intimacy.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer aigc_intimacy.NewClient err: %v", err)
		return nil, err
	}

	obsGatewayClient, err := obsgateway.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer obsgateway.NewClient err: %v", err)
		return nil, err
	}

	rcmdAIPartnerClient, err := rcmd_ai_partner.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer rcmd_ai_partner.NewClient err: %v", err)
		return nil, err
	}

	userPresentClient := userpresent_go.NewClient()

	reporter, err := bylink.NewBylinkReporter()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer bylink.NewBylinkReporter err: %v", err)
		return nil, err
	}

	eventBus, err := eventlink.New(ctx, cfg.EventLink)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer eventlink.New cfg(%+v) err: %v", cfg.EventLink, err)
		return nil, err
	}

	msgMgr := msg.New(eventBus, rcmdAIPartnerClient)
	giftMgr := gift.New(msgMgr, reporter, aigcCommonClient, aigcIntimacyClient)

	s := &Server{
		SeqgenV2Client:      seqgenV2Client,
		TimelineV2Client:    timelineV2Client,
		CensoringProxy:      censoringProxy,
		AuditInst:           auditInst,
		AccountGo:           accountGoClient,
		groupClient:         groupClient,
		soulmateClient:      soulmateClient,
		redDotClient:        redDotClient,
		aigcCommonClient:    aigcCommonClient,
		aigcIntimacyClient:  aigcIntimacyClient,
		obsGatewayClient:    obsGatewayClient,
		userPresentClient:   userPresentClient,
		rcmdAIPartnerClient: rcmdAIPartnerClient,

		reporter: reporter,

		eventBus: eventBus,

		msgMgr:  msgMgr,
		giftMgr: giftMgr,
	}

	if err := eventBus.Subscribe(ctx, event.SubNameChatBackgroundCallback, s.onChatBackgroundCallback); err != nil {
		log.ErrorWithCtx(ctx, "NewServer Subscribe name(%s) err: %v", event.SubNameChatBackgroundCallback, err)
		return nil, err
	}
	if err := eventBus.Subscribe(ctx, event.SubNameAIReturnGift, s.onAIReceiveUserPresent); err != nil {
		log.ErrorWithCtx(ctx, "NewServer Subscribe name(%s) err: %v", event.SubNameAIReturnGift, err)
		return nil, err
	}

	return s, nil
}

type Server struct {
	SeqgenV2Client      seqgen.IClient
	TimelineV2Client    *timeline_v2.Client
	CensoringProxy      censoring_proxy.IClient
	AuditInst           *Audit
	AccountGo           account_go.IClient
	groupClient         aigc_group.AigcGroupClient
	soulmateClient      aigc_soulmate.AigcSoulmateClient
	redDotClient        game_red_dot.GameRedDotClient
	aigcCommonClient    aigc_common.AigcCommonClient
	aigcIntimacyClient  aigc_intimacy.AigcIntimacyClient
	obsGatewayClient    obsgateway.IClient
	userPresentClient   userpresent_go.IClient
	rcmdAIPartnerClient rcmd_ai_partner.RCMDAIPartnerClient

	reporter report.Reporter

	eventBus event.Bus

	msgMgr  mgr.MsgManger
	giftMgr mgr.GiftManager
}

func (s *Server) ShutDown() {
	if s.eventBus != nil {
		s.eventBus.Close()
	}
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}
