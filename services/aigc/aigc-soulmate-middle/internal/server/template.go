package server

import (
	"context"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
)

func (s *Server) CreateGroupTemplate(ctx context.Context, req *pb.CreateGroupTemplateReq) (*pb.CreateGroupTemplateResp, error) {
	out := &pb.CreateGroupTemplateResp{}
	log.InfoWithCtx(ctx, "CreateGroupTemplate req: %+v", req)
	// 1，先创建群模板角色，然后创建群模板。模板创建失败顶多冗余角色数据
	if len(req.GetGroupTemplate().GetTemplateRoles()) == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "模板角色不能为空")
	}
	roleIds, err := s.createGroupRole(ctx, req.GetGroupTemplate().GetCategoryId(), req.GetGroupTemplate().GetTemplateRoles())
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateGroupTemplate createGroupRole req:%s err:%v", req.String(), err)
		return out, err
	}

	// 2，创建群模板
	resp, err := s.groupClient.CreateGroupTemplate(ctx, &aigc_group.CreateGroupTemplateRequest{
		GroupTemplate: convertTemplatePb(req.GetGroupTemplate(), roleIds),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateGroupTemplate CreateGroupTemplate req:%s err:%v", req.String(), err)
		return out, err
	}
	out.TemplateId = resp.GetId()
	log.InfoWithCtx(ctx, "CreateGroupTemplate success req:%s resp: %s", req.String(), out.String())
	return out, nil
}

func (s *Server) createGroupRole(ctx context.Context, categoryId string, groupRole []*pb.GroupTemplateInfo_TemplateRole) ([]uint32, error) {
	serverRole := make([]*aigc_soulmate.CreateAIRoleReq_Role, 0, len(groupRole))
	for _, role := range groupRole {
		serverRole = append(serverRole, convertRoleToPbCreateRole(categoryId, role))
	}
	resp, err := s.soulmateClient.BatchCreateRole(ctx, &aigc_soulmate.BatchCreateRoleReq{
		RoleList: serverRole,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "createGroupRole BatchCreateRole req:%s err:%v", groupRole, err)
		return nil, err
	}
	return resp.GetRoleIds(), nil
}

func convertRoleToPbCreateRole(categoryId string, groupRole *pb.GroupTemplateInfo_TemplateRole) *aigc_soulmate.CreateAIRoleReq_Role {
	role := &aigc_soulmate.CreateAIRoleReq_Role{
		Type:       aigc_soulmate.AIRoleType_AIRoleTypeGroup,
		Source:     aigc_soulmate.AIRoleSource_AIRoleSourceOfficial,
		Sex:        groupRole.GetSex(),
		Name:       groupRole.GetName(),
		Avatar:     groupRole.GetAvatar(),
		State:      aigc_soulmate.AIRoleState_AIRoleStatePublic,
		Character:  groupRole.GetDisplayCharacter(),
		CategoryId: categoryId,
		Timbre:     groupRole.GetTimbre(),
		GroupRoleConfig: &aigc_soulmate.GroupRoleConfig{
			ChatCharacter:     groupRole.GetAiCharacter(),
			RelationCharacter: groupRole.GetRelationCharacter(),
			Desc:              groupRole.GetChatRoleDesc(),
		},
	}
	for _, prologue := range groupRole.GetPrologues() {
		role.GroupRoleConfig.Prologues = append(role.GroupRoleConfig.Prologues, &aigc_soulmate.AIGroupPrologue{
			Text:     prologue.GetText(),
			Audio:    prologue.GetAudio(),
			Priority: prologue.GetPriority(),
		})
	}
	for _, prologue := range groupRole.GetWelcomePrologues() {
		role.GroupRoleConfig.WelcomePrologues = append(role.GroupRoleConfig.WelcomePrologues, &aigc_soulmate.AIGroupPrologue{
			Text:     prologue.GetText(),
			Audio:    prologue.GetAudio(),
			Priority: prologue.GetPriority(),
		})
	}
	return role
}

func (s *Server) UpdateGroupTemplate(ctx context.Context, req *pb.UpdateGroupTemplateReq) (*pb.UpdateGroupTemplateResp, error) {
	out := &pb.UpdateGroupTemplateResp{}
	log.InfoWithCtx(ctx, "UpdateGroupTemplate req: %+v", req)

	oldInfo, err := s.groupClient.GetGroupTemplateByIds(ctx, &aigc_group.GetGroupTemplateByIdsRequest{
		Ids: []uint32{req.GetGroupTemplate().GetId()},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateGroupTemplate GetGroupTemplateByIds req:%s err:%v", req.String(), err)
		return out, err
	}

	if len(oldInfo.GetList()) == 0 {
		log.ErrorWithCtx(ctx, "UpdateGroupTemplate template not  exist req:%s", req.String())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "template not exist")
	}

	oldRoleIds := oldInfo.GetList()[0].GetRoleIds()
	// 更新群模板角色信息
	roleIds, err := s.updateTemplateRole(ctx, req.GetGroupTemplate().GetCategoryId(), req.GetGroupTemplate().GetTemplateRoles(), oldRoleIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateGroupTemplate updateTemplateRole req:%s err:%v", req.String(), err)
		return out, err
	}

	// 更新群模板信息
	_, err = s.groupClient.UpdateGroupTemplate(ctx, &aigc_group.UpdateGroupTemplateRequest{
		GroupTemplate: convertTemplatePb(req.GetGroupTemplate(), roleIds),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateGroupTemplate GetGroupTemplateByIds req:%s err:%v", req.String(), err)
		return out, err
	}
	return out, nil
}

func (s *Server) updateTemplateRole(ctx context.Context, categoryId string, roles []*pb.GroupTemplateInfo_TemplateRole,
	oldRoleIds []uint32) ([]uint32, error) {
	// 对比更新前的角色id，找出新增、删除、修改的角色
	var deleteRoleIds []uint32
	var updateRoles []*aigc_soulmate.UpdateAIRoleReq_Role
	var newRoles []*aigc_soulmate.CreateAIRoleReq_Role
	var err error
	var newRoleIds []uint32

	existIdMap := make(map[uint32]bool, len(roles))
	for _, role := range roles {
		if role.GetRoleId() == 0 {
			newRoles = append(newRoles, convertRoleToPbCreateRole(categoryId, role))
		} else {
			updateRoles = append(updateRoles, convertRoleTOPbUpdateRole(categoryId, role))
			newRoleIds = append(newRoleIds, role.GetRoleId())
			existIdMap[role.GetRoleId()] = true
		}
	}
	for _, roleId := range oldRoleIds {
		if !existIdMap[roleId] {
			deleteRoleIds = append(deleteRoleIds, roleId)
		}
	}

	if len(newRoles) > 0 {
		resp, err := s.soulmateClient.BatchCreateRole(ctx, &aigc_soulmate.BatchCreateRoleReq{
			RoleList: newRoles,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "updateTemplateRole BatchCreateRole newRoles:%v err:%v", newRoles, err)
			return newRoleIds, err
		}
		newRoleIds = append(newRoleIds, resp.GetRoleIds()...)
	}
	if len(updateRoles) > 0 {
		_, err = s.soulmateClient.BatchUpdateRole(ctx, &aigc_soulmate.BatchUpdateRoleReq{
			RoleList: updateRoles,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "updateTemplateRole BatchUpdateRole updateRoles:%v err:%v", updateRoles, err)
			return newRoleIds, err
		}
	}
	if len(deleteRoleIds) > 0 {
		_, err = s.soulmateClient.BatchDeleteUserRole(ctx, &aigc_soulmate.BatchDeleteUserRoleReq{
			RoleIdList: deleteRoleIds,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "updateTemplateRole BatchDeleteRole deleteRoleIds:%v err:%v", deleteRoleIds, err)
			return newRoleIds, err
		}
	}
	return newRoleIds, nil
}

func convertRoleTOPbUpdateRole(categoryId string, groupRole *pb.GroupTemplateInfo_TemplateRole) *aigc_soulmate.UpdateAIRoleReq_Role {
	role := &aigc_soulmate.UpdateAIRoleReq_Role{
		Id:         groupRole.GetRoleId(),
		Sex:        groupRole.GetSex(),
		Name:       groupRole.GetName(),
		Avatar:     groupRole.GetAvatar(),
		State:      aigc_soulmate.AIRoleState_AIRoleStatePublic,
		Character:  groupRole.GetDisplayCharacter(),
		CategoryId: categoryId,
		Timbre:     groupRole.GetTimbre(),
		GroupRoleConfig: &aigc_soulmate.GroupRoleConfig{
			ChatCharacter:     groupRole.GetAiCharacter(),
			RelationCharacter: groupRole.GetRelationCharacter(),
			Desc:              groupRole.GetChatRoleDesc(),
		},
	}
	for _, prologue := range groupRole.GetPrologues() {
		role.GroupRoleConfig.Prologues = append(role.GroupRoleConfig.Prologues, &aigc_soulmate.AIGroupPrologue{
			Text:     prologue.GetText(),
			Audio:    prologue.GetAudio(),
			Priority: prologue.GetPriority(),
		})
	}
	for _, prologue := range groupRole.GetWelcomePrologues() {
		role.GroupRoleConfig.WelcomePrologues = append(role.GroupRoleConfig.WelcomePrologues, &aigc_soulmate.AIGroupPrologue{
			Text:     prologue.GetText(),
			Audio:    prologue.GetAudio(),
			Priority: prologue.GetPriority(),
		})
	}
	return role
}

func (s *Server) DeleteGroupTemplate(ctx context.Context, req *pb.DeleteGroupTemplateReq) (*pb.DeleteGroupTemplateResp, error) {
	out := &pb.DeleteGroupTemplateResp{}
	log.InfoWithCtx(ctx, "DeleteGroupTemplate req: %+v", req)
	resp, err := s.groupClient.DeleteGroupTemplate(ctx, &aigc_group.DeleteGroupTemplateRequest{
		Id: req.GetId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteGroupTemplate DeleteGroupTemplate req:%s err:%v", req.String(), err)
		return out, err
	}

	if len(resp.GetRoleIds()) > 0 {
		_, err = s.soulmateClient.BatchDeleteUserRole(ctx, &aigc_soulmate.BatchDeleteUserRoleReq{
			RoleIdList: resp.GetRoleIds(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "DeleteGroupTemplate BatchDeleteUserRole req:%s err:%v", req.String(), err)
			return out, err
		}
	}
	return out, nil
}

func (s *Server) GetGroupTemplateById(ctx context.Context, req *pb.GetGroupTemplateByIdReq) (*pb.GetGroupTemplateByIdResp, error) {
	out := &pb.GetGroupTemplateByIdResp{}

	if req.GetId() == 0 {
		log.WarnWithCtx(ctx, "GetGroupTemplateByIds empty req")
		return out, nil
	}

	groupResp, err := s.groupClient.GetGroupTemplateByIds(ctx, &aigc_group.GetGroupTemplateByIdsRequest{
		Ids: []uint32{req.GetId()},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupTemplateByIds GetGroupTemplateByIds req:%s err:%v", req.String(), err)
		return out, err
	}
	if len(groupResp.GetList()) == 0 {
		log.WarnWithCtx(ctx, "GetGroupTemplateByIds req:%s groupResp empty", req.String())
		return out, nil
	}
	roleResp, err := s.soulmateClient.GetAIRoleList(ctx, &aigc_soulmate.GetAIRoleListReq{
		RoleIdList: groupResp.GetList()[0].GetRoleIds(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupTemplateByIds GetAIRoleList req:%s err:%v", req.String(), err)
		return out, err
	}
	out.TemplateInfo = convertTemplateRespPb(groupResp.GetList()[0], roleResp.GetRoleList())
	log.InfoWithCtx(ctx, "GetGroupTemplateByIds success req:%s resp:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetGroupTemplateByPage(ctx context.Context, req *pb.GetGroupTemplateByPageReq) (*pb.GetGroupTemplateByPageResp, error) {
	out := &pb.GetGroupTemplateByPageResp{}
	if req.GetPage() <= 0 || req.GetSize() <= 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	groupResp, err := s.groupClient.GetGroupTemplateByPage(ctx, &aigc_group.GetGroupTemplateByPageRequest{
		Page:      req.GetPage(),
		Size:      req.GetSize(),
		GroupType: convertGroupType(req.GetGroupType()),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupTemplateByPage GetGroupTemplateByPage req:%s err:%v", req.String(), err)
		return out, err
	}
	out.Total = groupResp.GetTotal()

	if len(groupResp.GetList()) == 0 {
		return out, nil
	}

	roleMap, err := s.batGetRoleInfoMap(ctx, groupResp.GetList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupTemplateByPage batGetRoleInfoMap req:%s err:%v", req.String(), err)
		return out, err
	}
	for _, template := range groupResp.GetList() {
		out.List = append(out.List, convertTemplateRespPb(template, roleMap[template.GetId()]))
	}

	log.InfoWithCtx(ctx, "GetGroupTemplateByPage success req:%s total:%d len(resp):%d", req.String(), out.GetTotal(), len(out.GetList()))
	return out, nil

}

func convertGroupType(groupTypes []uint32) []aigc_group.GroupType {
	res := make([]aigc_group.GroupType, 0, len(groupTypes))
	for _, groupType := range groupTypes {
		res = append(res, aigc_group.GroupType(groupType))
	}
	return res
}

func (s *Server) batGetRoleInfoMap(ctx context.Context, templates []*aigc_group.GroupTemplate) (map[uint32][]*aigc_soulmate.AIRole, error) {
	var roleIds []uint32
	repeatedMap := make(map[uint32]bool)
	res := make(map[uint32][]*aigc_soulmate.AIRole)
	for _, template := range templates {
		for _, roleId := range template.GetRoleIds() {
			if repeatedMap[roleId] {
				continue
			}
			repeatedMap[roleId] = true
			roleIds = append(roleIds, roleId)
		}
	}
	roleResp, err := s.soulmateClient.GetAIRoleList(ctx, &aigc_soulmate.GetAIRoleListReq{
		RoleIdList: roleIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupTemplateByPage GetAIRoleList len(templates):%d err:%v", len(templates), err)
		return res, err
	}
	roleMap := make(map[uint32]*aigc_soulmate.AIRole)
	for _, role := range roleResp.GetRoleList() {
		roleMap[role.GetId()] = role
	}
	for _, template := range templates {
		var roles []*aigc_soulmate.AIRole
		for _, roleId := range template.GetRoleIds() {
			roles = append(roles, roleMap[roleId])
		}
		res[template.GetId()] = roles
	}
	return res, nil

}

func convertTemplatePb(groupTemplate *pb.GroupTemplateInfo, roleIds []uint32) *aigc_group.GroupTemplate {
	template := &aigc_group.GroupTemplate{
		Id:                groupTemplate.GetId(),
		Name:              groupTemplate.GetName(),
		Character:         groupTemplate.GetCharacter(),
		Tags:              groupTemplate.GetTags(),
		Avatar:            groupTemplate.GetAvatar(),
		Sex:               groupTemplate.GetSex(),
		ChatBackgroundImg: groupTemplate.GetChatBackgroundImg(),
		HomeBackgroundImg: groupTemplate.GetHomeBackgroundImg(),
		GroupIcon:         groupTemplate.GetGroupIcon(),
		Exposed:           groupTemplate.GetExposed(),
		InsertPos:         groupTemplate.GetInsertPos(),
		CornerIcon:        groupTemplate.GetCornerIcon(),
		CategoryId:        groupTemplate.GetCategoryId(),
		ConfigLikeNum:     groupTemplate.GetConfigLikeNum(),
		RoleIds:           roleIds,
		ImTabTags:         groupTemplate.GetImTabTags(),
		GroupType:         aigc_group.GroupType(groupTemplate.GetGroupType()),
		SuitableSex:       groupTemplate.GetSuitableSex(),
		DefaultPrologues:  convertPrologueToPb(groupTemplate.GetDefaultPrologues()),
		QuickSpeakTexts:   groupTemplate.GetQuickSpeakTexts(),
		LeaveStrategy:     aigc_group.LeaveStrategy(groupTemplate.GetLeaveStrategy()),
	}
	if groupTemplate.GetScriptInfo() != nil {
		template.ScriptInfo = &aigc_group.ScriptInfo{
			ButtonDisplayText: groupTemplate.GetScriptInfo().GetButtonDisplayText(),
			BackgroundMusic:   groupTemplate.GetScriptInfo().GetBackgroundMusic(),
			UserNum:           groupTemplate.GetScriptInfo().GetUserNum(),
			AnyNum:            groupTemplate.GetScriptInfo().GetAnyNum(),
			MaleNum:           groupTemplate.GetScriptInfo().GetMaleNum(),
			FemaleNum:         groupTemplate.GetScriptInfo().GetFemaleNum(),
			Sort:              groupTemplate.GetScriptInfo().GetSort(),
			MatchStrategy:     aigc_group.MatchStrategy(groupTemplate.GetScriptInfo().GetMatchStrategy()),
		}
		if template.ScriptInfo.MatchStrategy == aigc_group.MatchStrategy_MATCH_STRATEGY_UNSPECIFIED {
			template.ScriptInfo.MatchStrategy = aigc_group.MatchStrategy_MATCH_STRATEGY_POOL
		}
		if len(groupTemplate.GetScriptInfo().GetPlayRoles()) > 0 {
			for _, role := range groupTemplate.GetScriptInfo().GetPlayRoles() {
				template.ScriptInfo.PlayRoles = append(template.ScriptInfo.PlayRoles, &aigc_group.PlayRole{
					Id:              role.GetId(),
					Avatar:          role.GetAvatar(),
					Name:            role.GetName(),
					SuitableSex:     role.GetSuitableSex(),
					Character:       role.GetCharacter(),
					QuickSpeakTexts: role.GetQuickSpeakTexts(),
				})
			}
		}
	}
	return template
}

func convertPrologueToPb(prologue []*pb.GroupTemplateInfo_TemplateRole_AIGroupPrologue) []*aigc_group.AIGroupPrologue {
	res := make([]*aigc_group.AIGroupPrologue, 0, len(prologue))
	for _, item := range prologue {
		res = append(res, &aigc_group.AIGroupPrologue{
			Text:     item.GetText(),
			Audio:    item.GetAudio(),
			Priority: item.GetPriority(),
		})
	}
	return res
}

func convertTemplateRespPb(groupTemplate *aigc_group.GroupTemplate, roles []*aigc_soulmate.AIRole) *pb.GroupTemplateInfo {
	template := &pb.GroupTemplateInfo{
		Id:                groupTemplate.GetId(),
		Name:              groupTemplate.GetName(),
		Character:         groupTemplate.GetCharacter(),
		Tags:              groupTemplate.GetTags(),
		Avatar:            groupTemplate.GetAvatar(),
		Sex:               groupTemplate.GetSex(),
		ChatBackgroundImg: groupTemplate.GetChatBackgroundImg(),
		HomeBackgroundImg: groupTemplate.GetHomeBackgroundImg(),
		GroupIcon:         groupTemplate.GetGroupIcon(),
		Exposed:           groupTemplate.GetExposed(),
		InsertPos:         groupTemplate.GetInsertPos(),
		CornerIcon:        groupTemplate.GetCornerIcon(),
		CategoryId:        groupTemplate.GetCategoryId(),
		ConfigLikeNum:     groupTemplate.GetConfigLikeNum(),
		ImTabTags:         groupTemplate.GetImTabTags(),
		GroupType:         uint32(groupTemplate.GetGroupType()),
		SuitableSex:       groupTemplate.GetSuitableSex(),
		DefaultPrologues:  convertPrologueToPbResp(groupTemplate.GetDefaultPrologues()),
		QuickSpeakTexts:   groupTemplate.GetQuickSpeakTexts(),
		LeaveStrategy:     uint32(groupTemplate.GetLeaveStrategy()),
	}
	for _, role := range roles {
		if role.GetType() != aigc_soulmate.AIRoleType_AIRoleTypeGroup {
			continue
		}
		template.TemplateRoles = append(template.TemplateRoles, convertRoleRespPb(role))
	}

	if groupTemplate.ScriptInfo != nil {
		matchStrategy := groupTemplate.GetScriptInfo().GetMatchStrategy()
		if matchStrategy == aigc_group.MatchStrategy_MATCH_STRATEGY_UNSPECIFIED {
			matchStrategy = aigc_group.MatchStrategy_MATCH_STRATEGY_POOL
		}

		template.ScriptInfo = &pb.ScriptInfo{
			ButtonDisplayText: groupTemplate.GetScriptInfo().GetButtonDisplayText(),
			BackgroundMusic:   groupTemplate.GetScriptInfo().GetBackgroundMusic(),
			UserNum:           groupTemplate.GetScriptInfo().GetUserNum(),
			AnyNum:            groupTemplate.GetScriptInfo().GetAnyNum(),
			MaleNum:           groupTemplate.GetScriptInfo().GetMaleNum(),
			FemaleNum:         groupTemplate.GetScriptInfo().GetFemaleNum(),
			Sort:              groupTemplate.GetScriptInfo().GetSort(),
			MatchStrategy:     uint32(matchStrategy),
		}
		if len(groupTemplate.GetScriptInfo().GetPlayRoles()) > 0 {
			for _, role := range groupTemplate.GetScriptInfo().GetPlayRoles() {
				template.ScriptInfo.PlayRoles = append(template.GetScriptInfo().GetPlayRoles(), &pb.PlayRole{
					Id:              role.GetId(),
					Avatar:          role.GetAvatar(),
					Name:            role.GetName(),
					SuitableSex:     role.GetSuitableSex(),
					Character:       role.GetCharacter(),
					QuickSpeakTexts: role.GetQuickSpeakTexts(),
				})
			}

		}
	}
	return template
}

func convertPrologueToPbResp(prologue []*aigc_group.AIGroupPrologue) []*pb.GroupTemplateInfo_TemplateRole_AIGroupPrologue {
	res := make([]*pb.GroupTemplateInfo_TemplateRole_AIGroupPrologue, 0, len(prologue))
	for _, item := range prologue {
		res = append(res, &pb.GroupTemplateInfo_TemplateRole_AIGroupPrologue{
			Text:     item.GetText(),
			Audio:    item.GetAudio(),
			Priority: item.GetPriority(),
		})
	}
	return res
}

func convertRoleRespPb(role *aigc_soulmate.AIRole) *pb.GroupTemplateInfo_TemplateRole {

	rolePb := &pb.GroupTemplateInfo_TemplateRole{
		RoleId:            role.GetId(),
		Name:              role.GetName(),
		Avatar:            role.GetAvatar(),
		Timbre:            role.GetTimbre(),
		Sex:               role.GetSex(),
		DisplayCharacter:  role.GetCharacter(),
		AiCharacter:       role.GetGroupRoleConfig().GetChatCharacter(),
		ChatRoleDesc:      role.GetGroupRoleConfig().GetDesc(),
		RelationCharacter: role.GetGroupRoleConfig().GetRelationCharacter(),
	}
	for _, prologue := range role.GetGroupRoleConfig().GetPrologues() {
		rolePb.Prologues = append(rolePb.Prologues, &pb.GroupTemplateInfo_TemplateRole_AIGroupPrologue{
			Text:     prologue.GetText(),
			Audio:    prologue.GetAudio(),
			Priority: prologue.GetPriority(),
		})
	}
	for _, prologue := range role.GetGroupRoleConfig().GetWelcomePrologues() {
		rolePb.WelcomePrologues = append(rolePb.WelcomePrologues, &pb.GroupTemplateInfo_TemplateRole_AIGroupPrologue{
			Text:     prologue.GetText(),
			Audio:    prologue.GetAudio(),
			Priority: prologue.GetPriority(),
		})
	}
	return rolePb
}
