package eventlink

import (
	"context"
	"fmt"

	middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"
	middleware_publisher "gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	middleware_subscriber "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/event"
)

type eventLink struct {
	factory *middleware_event.Factory

	publishers  map[string]middleware_publisher.Publisher
	subscribers map[string]middleware_subscriber.Subscriber
}

func New(ctx context.Context, options *middleware_event.Options) (event.Bus, error) {
	factory, err := middleware_event.NewEventFactory(options)
	if err != nil {
		log.ErrorWithCtx(ctx, "New NewEventFactory options(%+v) err: %v", options, err)
		return nil, err
	}

	el := &eventLink{
		factory: factory,

		publishers:  make(map[string]middleware_publisher.Publisher),
		subscribers: make(map[string]middleware_subscriber.Subscriber),
	}

	for name := range options.Publisher {
		syncPub, err := factory.NewSyncPublisher(name)
		if err != nil {
			log.ErrorWithCtx(ctx, "NewEventBus NewSyncPublisher name(%s) err: %v", name, err)
			return nil, err
		}

		el.publishers[name] = syncPub
		log.InfoWithCtx(ctx, "New NewSyncPublisher name(%s) finished", name)
	}

	log.InfoWithCtx(ctx, "New options(%+v) finished", options)
	return el, nil
}

func (el *eventLink) Close() {
	for _, pub := range el.publishers {
		_ = pub.Close()
	}
	for _, sub := range el.subscribers {
		_ = sub.Stop()
	}
}

func (el *eventLink) Subscribe(ctx context.Context, name string, process middleware_subscriber.ProcessorContextFunc) error {
	if _, ok := el.subscribers[name]; ok {
		log.WarnWithCtx(ctx, "Subscribe name(%s) duplicate, skip", name)
		return nil
	}

	sub, err := el.factory.NewSubscriber(name, nil, process)
	if err != nil {
		log.ErrorWithCtx(ctx, "Subscribe NewSubscriber name(%s) err: %v", name, err)
		return err
	}

	el.subscribers[string(name)] = sub

	log.InfoWithCtx(ctx, "Subscribe name(%s) finished", name)
	return nil
}

func (el *eventLink) Publish(ctx context.Context, name, topic, key string, msg any) error {
	publisher, ok := el.publishers[name]
	if !ok {
		return fmt.Errorf("name %s not found", name)
	}

	bin, err := proto.Marshal(msg)
	if err != nil {
		return err
	}

	pubMsg := &middleware_publisher.ProducerMessage{
		Topic: topic,

		Key:   middleware_publisher.StringEncoder(key),
		Value: middleware_publisher.ByteEncoder(bin),
	}
	if result := publisher.Publish(ctx, pubMsg); result.Err != nil {
		return result.Err
	}

	log.InfoWithCtx(ctx, "Publish name(%s) topic(%s) key(%s) msg(%+v) finished", name, topic, key, msg)
	return nil
}
