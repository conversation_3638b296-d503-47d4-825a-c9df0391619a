package event

import (
	"context"

	middleware_subscriber "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
)

const (
	SubNameAIReturnGift           = "ai-return-gift"
	SubNameChatBackgroundCallback = "chat-background-callback"

	PubNameMsg = "msg"

	TopicSendSingleMsg = "aigc_send_single_msg"
)

//go:generate mockgen -destination=mocks/bus.go -package=mocks golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/event Bus
type Bus interface {
	Close()

	Publish(ctx context.Context, name, topic, key string, msg any) error
	Subscribe(ctx context.Context, name string, process middleware_subscriber.ProcessorContextFunc) error
}
