package bylink

import (
	"context"
	"encoding/base64"
	"encoding/json"

	web_im_logic "golang.52tt.com/protocol/app/web-im-logic"

	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/pkg/bylink"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	"golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/report"
)

type bylinkReporter struct {
	collector bylink.Bylink
}

func NewBylinkReporter() (report.Reporter, error) {
	collector, err := bylink.NewKfkCollector()
	if err != nil {
		return nil, err
	}

	bylink.InitGlobalCollector(collector)

	return &bylinkReporter{
		collector: collector,
	}, nil
}

func (r *bylinkReporter) Close() {
	r.collector.Flush()
	r.collector.Close()
}

func (r *bylinkReporter) ReportGroupMsg(ctx context.Context, data report.GroupMsgData) {
	if data.BizType != pb.ImBusiType_IM_BUSI_TYPE_MULTI_GROUP {
		return
	}

	var (
		userIdentifier uint64

		m = map[string]any{
			"partner_id": data.GroupId,
			"group_id":   data.GroupTemplateId,
			"content":    data.Content,
			"msg_type":   data.MsgType,
			"group_type": uint32(data.GroupTemplateType),
		}
	)
	transparentExtMsg := &web_im_logic.TransparentMsgExtInfo{}
	if len(data.TransparentExtMsg) > 0 {
		extByte, err := base64.StdEncoding.DecodeString(data.TransparentExtMsg)
		if err != nil {
			log.ErrorWithCtx(ctx, "writeSpecialMsg DecodeString TransparentExtMsg err:%v, msg:%s", err, data.TransparentExtMsg)
		}
		err = json.Unmarshal(extByte, transparentExtMsg)
		if err != nil {
			log.ErrorWithCtx(ctx, "writeSpecialMsg Unmarshal TransparentExtMsg err:%v, msg:%s", err, data.TransparentExtMsg)
		}
	}

	if data.IsTrigger {
		m["scene"] = "AI主动回复(多人群聊)"
	} else if data.CmdType == pb.ImCmdType_IM_CMD_TYPE_SENTENCE_TIP || data.CmdType == pb.ImCmdType_IM_CMD_TYPE_JOIN_GROUP_TIP ||
		transparentExtMsg.SpecialMsgType == uint32(web_im_logic.SpecialMsgType_SPECIAL_MSG_TYPE_SPECIFIED_USER_VISIBLE) {
		m["scene"] = "系统消息"
	} else {
		m["scene"] = "用户回复"
	}

	m["trigger_me_type"] = r.ToCmdChinese(data.CmdType, data.IsUseUp, data.IsTrigger, transparentExtMsg.GetSpecialMsgType())
	switch data.SenderType {
	case pb.GroupSendType_GroupSendTypeUser2AI:
		userIdentifier = uint64(data.Uid)

		m["uid"] = data.Uid
		m["user_type"] = "user"
	case pb.GroupSendType_GroupSendTypeAI2User:
		userIdentifier = uint64(data.RoleId)

		m["role_id"] = data.RoleId
		m["user_type"] = "ai"

	default:
		log.WarnWithCtx(ctx, "ReportGroupMsg invalid SenderType: %d", data.SenderType)
		return
	}

	if err := bylink.Track(ctx, userIdentifier, "aigc_community_multiplayer_im_log", m, false); err != nil {
		log.ErrorWithCtx(ctx, "ReportGroupMsg Track err: %v", err)
		return
	}

	bylink.Flush()
	log.InfoWithCtx(ctx, "ReportGroupMsg data: %+v", m)
}

func (r *bylinkReporter) ToCmdChinese(cmdType pb.ImCmdType, isUseUp, isTrigger bool, specialMsgType uint32) string {
	if isTrigger {
		return "AI主动回复(多人群聊)"
	}
	if isUseUp {
		return "到达上限后消息"
	}
	if specialMsgType == uint32(web_im_logic.SpecialMsgType_SPECIAL_MSG_TYPE_SPECIFIED_USER_VISIBLE) {
		return "模拟打招呼"
	}
	switch cmdType {
	case pb.ImCmdType_IM_CMD_TYPE_CONTENT_MSG:
		return "用户正常回复"
	case pb.ImCmdType_IM_CMD_TYPE_SENTENCE_TIP:
		return "句数提示"
	case pb.ImCmdType_IM_CMD_TYPE_JOIN_GROUP_TIP:
		return "进群提示"
	case pb.ImCmdType_IM_CMD_TYPE_WELCOME_MSG:
		return "欢迎语"
	default:
		return cmdType.String()
	}
}

func (r *bylinkReporter) ReportReturnGift(ctx context.Context, data report.ReturnGiftData) {
	m := map[string]any{
		"uid": data.Uid,

		"role_id":    data.RoleId,
		"target_uid": data.PartnerId,

		"content":   data.GiftName,
		"total_num": data.GiftNum,

		"background_id":   data.BgId,
		"background_name": data.BgName,
	}

	if err := bylink.Track(ctx, uint64(data.Uid), "aigc_return_salute_log", m, false); err != nil {
		log.ErrorWithCtx(ctx, "ReportReturnGift Track data(%+v) err: %v", m, err)
		return
	}

	log.InfoWithCtx(ctx, "ReportReturnGift properties: %+v", m)
}
