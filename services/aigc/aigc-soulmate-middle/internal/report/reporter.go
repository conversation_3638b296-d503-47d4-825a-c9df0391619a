package report

import (
	"context"

	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"

	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
)

type (
	GroupMsgData struct {
		Uid        uint32
		RoleId     uint32
		SenderType pb.GroupSendType

		BizType pb.ImBusiType
		MsgType pb.ImMsgContentType
		Content string

		GroupId           uint32
		GroupTemplateId   uint32
		CmdType           pb.ImCmdType
		IsTrigger         bool
		IsUseUp           bool
		TransparentExtMsg string
		GroupTemplateType aigc_group.GroupType
	}

	// ReturnGiftData 回礼上报
	ReturnGiftData struct {
		Uid uint32

		RoleId    uint32
		PartnerId uint32

		GiftNum  uint32
		GiftName string

		BgId   string
		BgName string
	}
)

//go:generate mockgen -destination=mocks/reporter.go -package=mocks golang.52tt.com/services/aigc/aigc-soulmate-middle/internal/report Reporter
type Reporter interface {
	Close()

	ReportGroupMsg(ctx context.Context, data GroupMsgData)
	ReportReturnGift(ctx context.Context, data ReturnGiftData)
}
