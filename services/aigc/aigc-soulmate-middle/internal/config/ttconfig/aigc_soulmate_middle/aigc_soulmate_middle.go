package config

import (
	"encoding/json"
	"math"
	"sync/atomic"
	"time"

	"github.com/gookit/goutil/timex"
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"

	"golang.52tt.com/pkg/log"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	aigc_trigger "golang.52tt.com/protocol/services/aigc/aigc-trigger"
)

const (
	CurDayPet = "pet"
	CurDay    = "other"
)

type AigcSoulmateMiddleConfig struct {
	SpecifiedSentenceSwitch       bool     `json:"specified_sentence_switch"` // true 需要走白名单
	SpecifiedSentenceWhiteUidList []uint32 `json:"specified_sentence_white_uid_list"`

	CurDayCountCfgMap map[uint32]interface{} `json:"cur_day_count_cfg_map"` // key:businessType value:今日句数配置

	// ai回礼
	ReturnGiftConfigs                   []ReturnGiftConfig `json:"return_gift_configs"`                     // 回礼配置
	ReturnGiftMsgDelay                  int64              `json:"return_gift_msg_delay"`                   // 回礼消息延迟，单位:秒
	ReturnGiftSentenceTTL               int64              `json:"return_gift_ttl"`                         // 回礼句数的过期时间，单位:秒
	ReturnGiftRemainSentence            uint32             `json:"return_gift_remain_sentence"`             // 回礼剩余句数
	ReturnGiftChatBackgroundProbability int                `json:"return_gift_chat_background_probability"` // 回礼聊天背景概率 0~100
}

type RoleCurDayCfg struct {
	RoleTypeCountMap map[string]uint32 `json:"role_type_count_map"` // key:roleType value:count
}

type GroupCurDayCfg struct {
	Count uint32 `json:"count"`
}

// ReturnPresentConfig 回礼配置
type ReturnGiftConfig struct {
	Price          float64  `json:"price"`           // 送礼金额
	Probability    int      `json:"probability"`     // 回礼概率 0~100
	ExtraSentences []uint32 `json:"extra_sentences"` // 额外句数
}

// defaultReturnGiftConfigs 默认回礼配置
var defaultReturnGiftConfigs = []ReturnGiftConfig{
	{
		Price:          0.1,
		Probability:    20,
		ExtraSentences: []uint32{10, 12, 13, 15, 16},
	},
	{
		Price:          0.5,
		Probability:    35,
		ExtraSentences: []uint32{46, 49, 52, 53, 55, 56},
	},
	{
		Price:          4.9,
		Probability:    50,
		ExtraSentences: []uint32{88, 89, 99, 100, 101, 111, 122, 123, 129, 131, 133},
	},
	{
		Price:          5,
		Probability:    60,
		ExtraSentences: []uint32{99, 100, 101, 111, 122, 129, 133, 152, 166, 168, 188, 191, 199, 200},
	},
	{
		Price:          math.MaxUint32,
		Probability:    75,
		ExtraSentences: []uint32{99, 100, 101, 111, 122, 129, 133, 152, 166, 168, 188, 191, 199, 200},
	},
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *AigcSoulmateMiddleConfig) Format() error {
	log.Infof("Format config: %+v", s)
	return nil
}

var (
	atomicAigcSoulmateMiddleConfig *atomic.Value
)

func init() {
	//if err := InitAigcSoulmateMiddleConfig(); err != nil {
	//    panic(err)
	//}
}

// InitAigcSoulmateMiddleConfig
// 可以选择外部初始化或者直接init函数初始化
func InitAigcSoulmateMiddleConfig() error {
	cfg := &AigcSoulmateMiddleConfig{}
	atomCfg, err := ttconfig.AtomLoad("aigc-soulmate-middle", cfg)
	if nil != err {
		return err
	}
	atomicAigcSoulmateMiddleConfig = atomCfg
	return nil
}

func GetAigcSoulmateMiddleConfig() *AigcSoulmateMiddleConfig {
	return atomicAigcSoulmateMiddleConfig.Load().(*AigcSoulmateMiddleConfig)
}

func (s *AigcSoulmateMiddleConfig) IsInWhiteList(uid uint32) bool {
	if s == nil || len(s.SpecifiedSentenceWhiteUidList) == 0 {
		return false
	}
	for _, u := range s.SpecifiedSentenceWhiteUidList {
		if u == uid {
			return true
		}
	}
	return false
}

func (s *AigcSoulmateMiddleConfig) GetSpecifiedSentenceSwitch() bool {
	if s == nil {
		return false
	}
	return s.SpecifiedSentenceSwitch
}

func (s *AigcSoulmateMiddleConfig) GetCurDayCfgCount(businessType, roleType uint32) uint32 {
	if s == nil || len(s.CurDayCountCfgMap) == 0 {
		return 0
	}
	if _, exists := s.CurDayCountCfgMap[businessType]; !exists {
		log.Errorf("GetCurDayCfgCount businessType:%d not exists", businessType)
		return 0
	}
	// 2. 将 interface{} 转回 JSON 字节
	data, err := json.Marshal(s.CurDayCountCfgMap[businessType])
	if err != nil {
		log.Errorf("序列化失败: %v", err)
		return 0
	}
	switch aigc_trigger.BusinessType(businessType) {
	case aigc_trigger.BusinessType_BUSINESS_TYPE_MUTI_PLAYER:
		var groupCfg GroupCurDayCfg
		if err := json.Unmarshal(data, &groupCfg); err != nil {
			log.Errorf("反序列化失败: %v", err)
			return 0
		}
		return groupCfg.Count

	case aigc_trigger.BusinessType_BUSINESS_TYPE_SINGLE_CHAT:
		// 3. 直接反序列化为目标结构体
		var roleCfg RoleCurDayCfg
		if err := json.Unmarshal(data, &roleCfg); err != nil {
			log.Errorf("反序列化失败: %v", err)
			return 0
		}
		if aigc_soulmate.AIRoleType(roleType) == aigc_soulmate.AIRoleType_AIRoleTypePet {
			return roleCfg.RoleTypeCountMap[CurDayPet]
		} else {
			return roleCfg.RoleTypeCountMap[CurDay]
		}
	default:
		return 0
	}

}

func (s *AigcSoulmateMiddleConfig) GetReturnGiftConfigs() []ReturnGiftConfig {
	var configs []ReturnGiftConfig
	if s == nil || len(s.ReturnGiftConfigs) == 0 {
		configs = defaultReturnGiftConfigs
	} else {
		configs = s.ReturnGiftConfigs
	}

	list := make([]ReturnGiftConfig, len(configs))
	copy(list, configs)

	return list
}

func (s *AigcSoulmateMiddleConfig) GetReturnGiftMsgDelay() time.Duration {
	if s == nil {
		return 5 * time.Second
	}
	return time.Duration(s.ReturnGiftMsgDelay) * time.Second
}

func (s *AigcSoulmateMiddleConfig) GetReturnGiftSentenceTTL() int64 {
	if s == nil || s.ReturnGiftSentenceTTL == 0 {
		return 7 * int64(timex.Day.Seconds())
	}
	return s.ReturnGiftSentenceTTL
}

func (s *AigcSoulmateMiddleConfig) GetReturnGiftRemainSentence() uint32 {
	if s == nil || s.ReturnGiftRemainSentence == 0 {
		return 200
	}
	return s.ReturnGiftRemainSentence
}

func (s *AigcSoulmateMiddleConfig) GetReturnGiftChatBackgroundProbability() int {
	if s == nil {
		return 50
	}
	return s.ReturnGiftChatBackgroundProbability
}
