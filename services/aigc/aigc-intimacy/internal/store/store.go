package store

import (
	"context"

	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
)

//go:generate mockgen -destination=mocks/background.go -package=mocks golang.52tt.com/services/aigc/aigc-intimacy/internal/store ChatBackgroundStore
type ChatBackgroundStore interface {
	Save(ctx context.Context, background *entity.ChatBackground) error
	Delete(ctx context.Context, id string) error
	Get(ctx context.Context, id string) (*entity.ChatBackground, error)
	BatchGet(ctx context.Context, ids []string) ([]*entity.ChatBackground, error)
	GetByPage(ctx context.Context, page, pageSize int64, needCount bool, name string, typ pb.ChatBackgroundType) ([]*entity.ChatBackground, int64, error)
	GetByBindRoleId(ctx context.Context, roleId uint32, typ pb.ChatBackgroundType) ([]*entity.ChatBackground, error)
	CountByBindRoleId(ctx context.Context, roleId uint32) (int64, error)

	UpsertBackgroundBinding(ctx context.Context, binding *entity.ObjectBackgroundBinding) error
	GetBackgroundBinding(ctx context.Context, entityInst *pb.Entity) (*entity.ObjectBackgroundBinding, error)
	DeleteBackgroundBinding(ctx context.Context, entityInst *pb.Entity) error
}

//go:generate mockgen -destination=mocks/relation.go -package=mocks golang.52tt.com/services/aigc/aigc-intimacy/internal/store RelationshipStore
type RelationshipStore interface {
	Save(ctx context.Context, relationship *entity.Relationship) error
	Delete(ctx context.Context, id string) error
	Get(ctx context.Context, id string) (*entity.Relationship, error)
	BatchGet(ctx context.Context, ids []string) ([]*entity.Relationship, error)

	UpsertRelationBinding(ctx context.Context, binding *entity.ObjectRelationBinding) error
	GetRelationBinding(ctx context.Context, entityInst *pb.Entity) (*entity.ObjectRelationBinding, error)
	DeleteRelationBinding(ctx context.Context, entityInst *pb.Entity) error
	BatchGetRelationBinding(ctx context.Context, entityInsts []*pb.Entity) (map[string]*entity.ObjectRelationBinding, error)
}

type IntimacyLevelConfStore interface {
	Save(ctx context.Context, level *entity.IntimacyLevelConf) error
	Delete(ctx context.Context, id uint32) error
	Get(ctx context.Context, id uint32) (*entity.IntimacyLevelConf, error)
	BatchGet(ctx context.Context, ids []uint32) ([]*entity.IntimacyLevelConf, error)
	GetLessThan(ctx context.Context, level uint32) ([]*entity.IntimacyLevelConf, error)
}

type IntimacyConditionStore interface {
	Save(ctx context.Context, condition *entity.IntimacyCondition) error
	Delete(ctx context.Context, id string) error
	Get(ctx context.Context, id string) (*entity.IntimacyCondition, error)
	BatchGet(ctx context.Context, ids []string) ([]*entity.IntimacyCondition, error)
}

//go:generate mockgen -destination=mocks/level.go -package=mocks golang.52tt.com/services/aigc/aigc-intimacy/internal/store LevelStore
type LevelStore interface {
	CreateOrIncrValue(ctx context.Context, level *entity.Level, value uint32, serialNo, reason string) (*entity.Level, bool, error)
	UpdateLevel(ctx context.Context, id string, oLv, oVal, nLv, nVal uint32) (*entity.Level, error)

	Get(ctx context.Context, id string) (*entity.Level, error)
}

//go:generate mockgen -destination=mocks/user_role_bg.go -package=mocks golang.52tt.com/services/aigc/aigc-intimacy/internal/store UserRoleBackgroundStore
type UserBackgroundStore interface {
	Add(ctx context.Context, bg *entity.UserBackground) error
	FindByUidAndBindIdAndBgIds(ctx context.Context, uid uint32, bindType pb.BindEntity_EntityType, bindId uint32, bgIds []string) (entity.UserBackgrounds, error)
}
