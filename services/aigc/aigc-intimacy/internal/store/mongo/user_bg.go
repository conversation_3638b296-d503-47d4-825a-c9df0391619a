package mongo

import (
	"context"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store"
)

type userBackgroundStore struct {
	col *mongo.Collection
}

func NewUserBackgroundStore(ctx context.Context, mongoDB *db.MongoDB) store.UserBackgroundStore {
	st := &userBackgroundStore{
		col: mongoDB.Database().Collection("user_bg"),
	}
	_, err := st.col.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "uid", Value: 1},
				{Key: "bind_type", Value: 1},
				{Key: "bind_id", Value: 1},
				{Key: "bg_id", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "NewUserBackgroundStore collection(user_bg) Indexes.CreateMany err: %v", err)
	}

	return st
}

func (s *userBackgroundStore) Add(ctx context.Context, bg *entity.UserBackground) error {
	_, err := s.col.InsertOne(ctx, bg)
	return err
}

func (s *userBackgroundStore) FindByUidAndBindIdAndBgIds(ctx context.Context, uid uint32, bindType pb.BindEntity_EntityType, bindId uint32, bgIds []string) (entity.UserBackgrounds, error) {
	cursor, err := s.col.Find(ctx, bson.M{"uid": uid, "bind_type": bindType, "bind_id": bindId, "bg_id": bson.M{"$in": bgIds}})
	if err != nil {
		return nil, err
	}

	var list entity.UserBackgrounds
	if err := cursor.All(ctx, &list); err != nil {
		return nil, err
	}

	return list, nil
}
