package mongo

import (
	"context"
	"time"

	"gitlab.ttyuyin.com/tyr/x/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store"
)

type levelMongoStore struct {
	logs   *mongo.Collection
	levels *mongo.Collection
}

func NewLevelMongoStore(ctx context.Context, database *db.MongoDB) store.LevelStore {
	st := &levelMongoStore{
		logs:   database.Database().Collection("growth_logs"),
		levels: database.Database().Collection("levels"),
	}

	_, err := st.levels.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			// 用于查询跟用户关联的partner亲密度等级
			Keys: bson.D{
				{Key: "uid", Value: 1},
			},
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "NewLevelMongoStore collection(levels) CreateMany err: %v", err)
	}

	_, err = st.logs.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			// 用于查询用户亲密度增长流水
			Keys: bson.D{
				{Key: "uid", Value: 1},
				{Key: "created_at", Value: 1},
			},
		},
		{
			// 用于清理过期数据
			Keys: bson.D{
				{Key: "created_at", Value: 1},
			},
			Options: options.Index().SetExpireAfterSeconds(30 * 24 * 3600),
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "NewLevelMongoStore collection(growth_logs) CreateMany err: %v", err)
	}

	return st
}

func (s *levelMongoStore) CreateOrIncrValue(ctx context.Context, level *entity.Level, value uint32, serialNo, reason string) (*entity.Level, bool, error) {
	if _, err := s.logs.InsertOne(ctx, bson.M{
		"_id":        serialNo,
		"created_at": time.Now(),

		"uid":   level.Uid,
		"value": value,

		"reason": reason,
	}); err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return nil, false, nil
		}

		return nil, false, err
	}

	result := s.levels.FindOneAndUpdate(
		ctx,
		bson.M{"_id": level.ID},
		bson.M{
			"$setOnInsert": bson.M{
				"_id":        level.ID,
				"created_at": level.CreatedAt,

				"uid": level.Uid,

				"level": level.Level,
			},
			"$inc": bson.M{
				"sum":   value,
				"value": value,
			},
		},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After),
	)
	if err := result.Err(); err != nil {
		return nil, false, err
	}

	var l entity.Level
	return &l, true, result.Decode(&l)
}

func (s *levelMongoStore) UpdateLevel(ctx context.Context, id string, oLv, oVal, nLv, nVal uint32) (*entity.Level, error) {
	result := s.levels.FindOneAndUpdate(
		ctx,
		bson.M{
			"_id":   id,
			"level": oLv,
			"value": oVal,
		},
		bson.M{
			"$set": bson.M{
				"level": nLv,
				"value": nVal,
			},
		},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	)
	if err := result.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}

		return nil, err
	}

	var l entity.Level
	return &l, result.Decode(&l)
}

func (s *levelMongoStore) Get(ctx context.Context, id string) (*entity.Level, error) {
	result := s.levels.FindOne(ctx, bson.M{"_id": id})
	if err := result.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}

		return nil, err
	}

	var level entity.Level
	return &level, result.Decode(&level)
}
