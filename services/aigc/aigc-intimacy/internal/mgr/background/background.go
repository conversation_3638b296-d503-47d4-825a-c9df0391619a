package background

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"

	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	config "golang.52tt.com/services/aigc/aigc-intimacy/internal/config/ttconfig/aigc_intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store"
)

type manager struct {
	backgroundStore store.ChatBackgroundStore
	userBgStore     store.UserBackgroundStore
}

func NewManager(backgroundStore store.ChatBackgroundStore, userBgStore store.UserBackgroundStore) mgr.BackgroundManager {

	return &manager{
		backgroundStore: backgroundStore,
		userBgStore:     userBgStore,
	}
}

func (m *manager) UpsertChatBackground(ctx context.Context, background *entity.ChatBackground) error {
	return m.backgroundStore.Save(ctx, background)
}

func (m *manager) DeleteChatBackground(ctx context.Context, id string) error {
	return m.backgroundStore.Delete(ctx, id)
}

func (m *manager) GetChatBackground(ctx context.Context, id string) (*entity.ChatBackground, error) {
	return m.backgroundStore.Get(ctx, id)
}

func (m *manager) GetChatBackgroundByPage(ctx context.Context, page, pageSize int64, needCount bool, name string, typ pb.ChatBackgroundType) ([]*entity.ChatBackground, int64, error) {
	if page == 0 {
		page = 1
	}
	if pageSize == 0 || pageSize > 100 {
		pageSize = 100
	}
	return m.backgroundStore.GetByPage(ctx, page, pageSize, needCount, name, typ)
}

func (m *manager) BatchGetChatBackground(ctx context.Context, ids []string) ([]*entity.ChatBackground, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	return m.backgroundStore.BatchGet(ctx, ids)
}

func (m *manager) GetChatBackgroundMap(ctx context.Context, ids []string) (map[string]*entity.ChatBackground, error) {
	backgrounds, err := m.BatchGetChatBackground(ctx, ids)
	if err != nil {
		return nil, err
	}
	backgroundMap := make(map[string]*entity.ChatBackground, len(backgrounds))
	for _, background := range backgrounds {
		backgroundMap[background.ID] = background
	}

	return backgroundMap, nil
}

func (m *manager) GetChatBackgroundByBindEntity(ctx context.Context, bindEntity *pb.BindEntity, typ pb.ChatBackgroundType) ([]*entity.ChatBackground, error) {
	var backgrounds []*entity.ChatBackground
	var err error

	switch bindEntity.GetType() {
	case pb.BindEntity_ENTITY_TYPE_ROLE:
		backgrounds, err = m.backgroundStore.GetByBindRoleId(ctx, bindEntity.GetId(), typ)
	default:
		log.WarnWithCtx(ctx, "GetChatBackgroundByBindEntity invalid bind entity type:%v", bindEntity.GetType())
	}
	return backgrounds, err
}

func (m *manager) IsReachBindEntityChatBackgroundLimit(ctx context.Context, bindEntity *pb.BindEntity, add uint32) (bool, error) {
	switch bindEntity.GetType() {
	case pb.BindEntity_ENTITY_TYPE_ROLE:
		count, err := m.backgroundStore.CountByBindRoleId(ctx, bindEntity.GetId())
		if err != nil {
			log.ErrorWithCtx(ctx, "IsReachBindEntityChatBackgroundLimit CountByBindRoleId roleId(%d) err: %v", bindEntity.GetId(), err)
			return false, err
		}
		if limit := config.GetAigcIntimacyConfig().GetRoleChatBackgroundLimit(); uint32(count)+add > limit {
			log.WarnWithCtx(ctx, "IsReachBindEntityChatBackgroundLimit roleId(%d) count(%d) + add(%d) >= limit(%d)", bindEntity.GetId(), add, count, limit)
			return true, nil
		}
	}

	return false, nil
}

func (m *manager) AddUserBackground(ctx context.Context, uid uint32, en pb.BindEntity, bgId string) error {
	bg := &entity.UserBackground{
		Id:        primitive.NewObjectID().Hex(),
		CreatedAt: time.Now(),

		Uid:  uid,
		BgId: bgId,

		BindId:   en.GetId(),
		BindType: en.GetType(),
	}
	return m.userBgStore.Add(ctx, bg)
}

func (m *manager) BatchGetUserBackgroundId(ctx context.Context, uid uint32, en pb.BindEntity, bgIds []string) (map[string]bool, error) {
	list, err := m.userBgStore.FindByUidAndBindIdAndBgIds(ctx, uid, en.GetType(), en.GetId(), bgIds)
	if err != nil {
		return nil, err
	}

	bgIdMap := make(map[string]bool)

	if len(list) == 0 {
		return bgIdMap, nil
	}

	for _, v := range list {
		bgIdMap[v.BgId] = true
	}

	return bgIdMap, nil
}
