package config

import (
	"sync/atomic"
	"time"

	"github.com/gookit/goutil/timex"
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
)

type AigcIntimacyConfig struct {
	DaySec int64 `json:"day_sec` // 模拟天的秒数

	RoleChatBackgroundLimit uint32 `json:"role_chat_background_limit"`
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *AigcIntimacyConfig) Format() error {
	return nil
}

var (
	atomicAigcIntimacyConfig *atomic.Value
)

// InitAigcIntimacyConfig
// 可以选择外部初始化或者直接init函数初始化
func InitAigcIntimacyConfig() error {
	cfg := &AigcIntimacyConfig{}
	atomCfg, err := ttconfig.AtomLoad("aigc-intimacy", cfg)
	if nil != err {
		return err
	}
	atomicAigcIntimacyConfig = atomCfg
	return nil
}

func GetAigcIntimacyConfig() *AigcIntimacyConfig {
	return atomicAigcIntimacyConfig.Load().(*AigcIntimacyConfig)
}

func (s *AigcIntimacyConfig) GetDayDuration() time.Duration {
	if s == nil || s.DaySec <= 0 {
		return timex.Day
	}

	return time.Duration(s.DaySec) * time.Second
}

func (s *AigcIntimacyConfig) GetRoleChatBackgroundLimit() uint32 {
	if s == nil {
		return 100
	}
	return s.RoleChatBackgroundLimit
}
