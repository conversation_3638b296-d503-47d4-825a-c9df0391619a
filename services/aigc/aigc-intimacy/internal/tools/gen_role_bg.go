package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"time"

	"github.com/spf13/cobra"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"golang.52tt.com/clients/obsgateway"
	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
)

func newGenRoleBgCommand() *cobra.Command {
	type BackgroundImages struct {
		Images []struct {
			RoleId uint32 `json:"role_id"`

			Url  string `json:"url"`
			Name string `json:"name"`
		} `json:"images"`
	}

	cmd := &cobra.Command{
		Use:   "gen-role-bg",
		Short: "生成背景",
		Run: func(cmd *cobra.Command, args []string) {
			ctx := context.Background()

			env, _ := cmd.Flags().GetString("env")
			path, _ := cmd.Flags().GetString("file")
			interactive, _ := cmd.Flags().GetBool("interactive")

			mongoConfig := envMongoConfig[env]
			database, err := db.NewMongoDB(ctx, mongoConfig)
			if err != nil {
				log.Fatalf("NewMongoDB(%+v) err: %v", mongoConfig, err)
			}

			collection := database.Database().Collection("chat_background")

			obs, err := obsgateway.NewClient()
			if err != nil {
				log.Fatalf("obsgateway.NewClient err: %v", err)
			}

			file, err := os.Open(path)
			if err != nil {
				log.Fatalf("Open(%s) err: %v", path, err)
			}

			data, err := io.ReadAll(file)
			if err != nil {
				log.Fatalf("ReadAll(%s) err: %v", path, err)
			}

			var images BackgroundImages
			if err := json.Unmarshal(data, &images); err != nil {
				log.Fatalf("Unmarshal(%s) err: %v", path, err)
			}

			for _, image := range images.Images {
				_, _, url, _, err := obs.CopyUrl(ctx, image.Url, "tt", "aigc-bg", obsgateway.WithNoReview(), obsgateway.WithReturnUrl())
				if err != nil {
					log.Fatalf("CopyUrl srcUrl(%s) err: %v", image.Url, err)
				}

				now := time.Now()
				bg := &entity.ChatBackground{
					ID:              primitive.NewObjectID().Hex(),
					CreatedAt:       now,
					UpdatedAt:       now,
					Type:            aigc_intimacy.ChatBackgroundType_CHAT_BACKGROUND_TYPE_PRESENT,
					Name:            image.Name,
					ImageUrl:        url,
					ShowAvatar:      true,
					DefaultUnlock:   false,
					UnlockCondition: "送礼后有概率掉落",
					BindEntityType:  aigc_intimacy.ChatBackground_BIND_ENTITY_TYPE_ROLE_LIST,
					BindEntityIds:   []uint32{image.RoleId},
				}
				if interactive {
					data, _ := json.MarshalIndent(bg, "\t", "\t")
					fmt.Printf("%s\n", data)
					fmt.Print("确认要保存背景吗？(y/n): ")

					var confirm string
					if _, err := fmt.Scanln(&confirm); err != nil {
						log.Fatalf("Scanln err: %v", err)
					}
					if confirm != "y" {
						continue
					}
				}
				if _, err := collection.InsertOne(ctx, bg); err != nil {
					log.Fatalf("InsertOne bg(%+v) err: %v", bg, err)
				}

				log.Printf("InsertOne bg(%+v) finished", bg)
			}
		},
	}

	cmd.Flags().Bool("interactive", false, "--interactive")
	cmd.Flags().String("file", "", "--file=<file>")

	return cmd
}
