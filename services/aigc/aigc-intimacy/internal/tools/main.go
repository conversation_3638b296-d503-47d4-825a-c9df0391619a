package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"golang.52tt.com/pkg/config"
)

var envMongoConfig = map[string]*config.MongoConfig{
	"test": {
		Addrs:       "10.34.6.51:27017",
		Database:    "aigc_intimacy",
		MaxPoolSize: 1,
		UserName:    "aigc_intimacy_rw",
		Password:    "5JH*URWSabeIvEq",
	},
	"prod": {
		Addrs:       "aigc-intimacy-quicksilver-mongo-node-2.external.se.cluster.local:27017,aigc-intimacy-quicksilver-mongo-node-1.external.se.cluster.local:27017",
		Database:    "aigc_intimacy",
		MaxPoolSize: 1,
		UserName:    "aigc_intimacy_rw",
		Password:    "0XGEJb5vtr*gdHF",
	},
}

func main() {
	cmd := &cobra.Command{
		Use:   "aigc-intimacy-tool",
		Short: "aigc-intimacy tool",
	}

	cmd.PersistentFlags().String("env", "", "--env=<环境:test|prod>")
	cmd.AddCommand(newGenRoleBgCommand())

	if err := cmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "error: %+v\n", err)
		os.Exit(1)
	}
}
