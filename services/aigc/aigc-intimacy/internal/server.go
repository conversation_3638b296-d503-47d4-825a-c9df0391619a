package internal

import (
	"context"
	"time"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	redis_connect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"

	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/cache/redis"
	ttconfig "golang.52tt.com/services/aigc/aigc-intimacy/internal/config/ttconfig/aigc_intimacy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/event"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/event/eventlink"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr/background"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr/level"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr/levelup"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/mgr/relation"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/report"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/report/bylink"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/store/mongo"
)

type StartConfig struct {
	// from config file
	// from config file
	Mongo     *config.MongoConfig        `json:"mongo"`
	Redis     *redis_connect.RedisConfig `json:"redis"`
	EventLink *middleware_event.Options  `json:"event_link"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.InfoWithCtx(ctx, "server startup with cfg: %+v", *cfg)

	if err := ttconfig.InitAigcIntimacyConfig(); err != nil {
		log.ErrorWithCtx(ctx, "NewServer InitAigcIntimacyConfig err: %v", err)
		return nil, err
	}

	eventBus, err := eventlink.NewEventBus(ctx, cfg.EventLink)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewEventBus cfg(%+v) err: %v", cfg, err)
		return nil, err
	}

	mongoDB, err := db.NewMongoDB(ctx, cfg.Mongo)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewMongoDB cfg(%+v) err", cfg, err)
		return nil, err
	}

	redisDB, err := db.NewRedisDB(ctx, cfg.Redis)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewRedisDB cfg(%+v) err", cfg, err)
		return nil, err
	}

	pushClient, _ := push.NewClient()

	reporter, err := bylink.NewBylinkReporter()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewBylinkReporter err: %v", err)
		return nil, err
	}

	relationMongo := mongo.NewRelationshipMongoStore(ctx, mongoDB)
	relationMgr := relation.NewManager(relationMongo, eventBus)

	backgroundMongo := mongo.NewBackgroundMongoStore(ctx, mongoDB)
	userBgMongo := mongo.NewUserBackgroundStore(ctx, mongoDB)
	backgroundMgr := background.NewManager(backgroundMongo, userBgMongo)

	levelMongo := mongo.NewLevelMongoStore(ctx, mongoDB)
	levelConfMongo := mongo.NewIntimacyLevelConfMongoStore(ctx, mongoDB)
	growthCache := redis.NewGrowthRedisCache(ctx, redisDB)
	levelMgr := level.NewManager(levelMongo, levelConfMongo, growthCache, eventBus, pushClient, reporter)

	conditionMongo := mongo.NewIntimacyConditionMongoStore(ctx, mongoDB)
	chatCache := redis.NewChatRedisCache(ctx, redisDB)
	progressCache := redis.NewLevelUpCondProgressRedisCache(ctx, redisDB)
	levelUpMgr := levelup.NewManager(conditionMongo, chatCache, progressCache)

	s := &Server{
		mongoDB: mongoDB,
		redisDB: redisDB,

		eventBus: eventBus,

		reporter: reporter,

		levelMgr:      levelMgr,
		levelUpMgr:    levelUpMgr,
		relationMgr:   relationMgr,
		backgroundMgr: backgroundMgr,
	}

	if err = eventBus.Subscribe(ctx, event.SubNameChatLevelUp, s.handleChatLevelUpCondtion); err != nil {
		log.ErrorWithCtx(ctx, "NewServer Subscribe(%s) err: %v", event.SubNameChatLevelUp, err)
		return nil, err
	}

	return s, nil
}

type Server struct {
	mongoDB *db.MongoDB
	redisDB *db.RedisDB

	eventBus event.EventBus

	reporter report.Reporter

	levelMgr      mgr.LevelManager
	levelUpMgr    mgr.LevelUpManager
	relationMgr   mgr.RelationManager
	backgroundMgr mgr.BackgroundManager
}

func (s *Server) ShutDown() {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if s.mongoDB != nil {
		_ = s.mongoDB.Close(ctx)
	}
	if s.redisDB != nil {
		_ = s.redisDB.Close()
	}
	if s.eventBus != nil {
		s.eventBus.Close()
	}
	if s.reporter != nil {
		s.reporter.Close()
	}
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) GetReadHeartCount(ctx context.Context, req *pb.GetReadHeartCountRequest) (*pb.GetReadHeartCountResponse, error) {
	out := &pb.GetReadHeartCountResponse{}
	if req.GetUid() == 0 ||
		req.GetEntity() == nil ||
		req.GetEntity().GetType() == pb.Entity_TYPE_UNSPECIFIED ||
		req.GetEntity().GetId() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	levelEntity, err := s.levelMgr.GetIntimacyLevel(ctx, req.GetUid(), req.GetEntity())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReadHeartCount GetIntimacyLevel uid(%d) entity(%+v) err: %v", req.GetUid(), req.GetEntity(), err)
		return out, err
	}
	if levelEntity == nil {
		log.ErrorWithCtx(ctx, "GetReadHeartCount GetIntimacyLevel uid(%d) entity(%+v) levelEntity is nil", req.GetUid(), req.GetEntity())
		return out, nil
	}
	levelConf, err := s.levelMgr.GetIntimacyLevelConf(ctx, levelEntity.Level)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReadHeartCount GetIntimacyLevelConf uid(%d) entity(%+v) err: %v", req.GetUid(), req.GetEntity(), err)
		return out, err
	}
	if levelConf != nil {
		out.ReadHeartCount = levelConf.Benefits.ExtraReadMindCount
	}
	log.InfoWithCtx(ctx, "GetReadHeartCount uid(%d) entity(%+v) readHeartCount(%d)", req.GetUid(), req.GetEntity(), out.ReadHeartCount)
	return out, nil
}

func (s *Server) GetBenefitConfig(ctx context.Context, req *pb.GetBenefitConfigRequest) (*pb.GetBenefitConfigResponse, error) {
	out := &pb.GetBenefitConfigResponse{}
	if req.GetUid() == 0 ||
		req.GetEntity() == nil ||
		req.GetEntity().GetType() == pb.Entity_TYPE_UNSPECIFIED ||
		req.GetEntity().GetId() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	levelEntity, err := s.levelMgr.GetIntimacyLevel(ctx, req.GetUid(), req.GetEntity())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBenefitConfig GetIntimacyLevel uid(%d) entity(%+v) err: %v", req.GetUid(), req.GetEntity(), err)
		return out, err
	}
	if levelEntity == nil {
		log.ErrorWithCtx(ctx, "GetBenefitConfig GetIntimacyLevel uid(%d) entity(%+v) levelEntity is nil", req.GetUid(), req.GetEntity())
		return out, nil
	}
	out.CurLevel = levelEntity.Level
	levelConf, err := s.levelMgr.GetIntimacyLevelConf(ctx, levelEntity.Level)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBenefitConfig GetIntimacyLevelConf uid(%d) entity(%+v) err: %v", req.GetUid(), req.GetEntity(), err)
		return out, err
	}
	if levelConf != nil {
		out.Benefits = assembleLevelBenefits(levelConf.Benefits)
	}
	log.InfoWithCtx(ctx, "GetBenefitConfig uid(%d) entity(%+v) out(%s)", req.GetUid(), req.GetEntity(), out.String())
	return out, nil
}
