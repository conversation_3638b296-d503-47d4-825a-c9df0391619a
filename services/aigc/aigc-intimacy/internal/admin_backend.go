package internal

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/rcmd_mt_proxy"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/entity"
	"golang.52tt.com/services/aigc/aigc-intimacy/internal/event"
)

// ========== 运营后台 ==========
// 聊天背景配置
func (s *Server) checkChatBackground(ctx context.Context, background *pb.ChatBackground) error {
	if background.GetName() == "" || background.GetImageUrl() == "" {
		log.ErrorWithCtx(ctx, "checkChatBackground name or url is empty")
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	if background.GetBindEntityType() != pb.ChatBackground_BIND_ENTITY_TYPE_ALL_ROLE &&
		background.GetBindEntityType() != pb.ChatBackground_BIND_ENTITY_TYPE_ROLE_LIST {
		log.ErrorWithCtx(ctx, "checkChatBackground invalid bindEntityType")
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	if background.GetBindEntityType() == pb.ChatBackground_BIND_ENTITY_TYPE_ROLE_LIST && len(background.GetBindEntityIds()) == 0 {
		log.ErrorWithCtx(ctx, "checkChatBackground bindEntityIds is empty")
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	return nil

}
func (s *Server) CreateChatBackground(ctx context.Context, req *pb.CreateChatBackgroundRequest) (*pb.CreateChatBackgroundResponse, error) {
	resp := &pb.CreateChatBackgroundResponse{}
	log.InfoWithCtx(ctx, "CreateChatBackground req: %+v", req)

	if err := s.checkChatBackground(ctx, req.GetBackground()); err != nil {
		return resp, err
	}

	background := entity.NewChatBackground(req.GetBackground())
	err := s.backgroundMgr.UpsertChatBackground(ctx, background)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateChatBackground err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "CreateChatBackground success, req: %+v, id: %s", req, background.ID)
	return resp, nil
}

func (s *Server) UpdateChatBackground(ctx context.Context, req *pb.UpdateChatBackgroundRequest) (*pb.UpdateChatBackgroundResponse, error) {
	resp := &pb.UpdateChatBackgroundResponse{}
	log.InfoWithCtx(ctx, "UpdateChatBackground req: %+v", req)

	if err := s.checkChatBackground(ctx, req.GetBackground()); err != nil {
		return resp, err
	}

	background, err := s.backgroundMgr.GetChatBackground(ctx, req.GetBackground().GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateChatBackground GetChatBackground err: %v", err)
		return resp, err
	}
	if background == nil {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("id(%s)对应的聊天背景不存在", req.GetBackground().GetId()))
	}

	background.Update(req.GetBackground())
	err = s.backgroundMgr.UpsertChatBackground(ctx, background)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateChatBackground UpsertChatBackground err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "UpdateChatBackground success, req: %+v", req)
	return resp, nil
}

func (s *Server) DeleteChatBackground(ctx context.Context, req *pb.DeleteChatBackgroundRequest) (*pb.DeleteChatBackgroundResponse, error) {
	resp := &pb.DeleteChatBackgroundResponse{}
	log.InfoWithCtx(ctx, "DeleteChatBackground req: %+v", req)

	if req.GetId() == "" {
		return resp, nil
	}
	err := s.backgroundMgr.DeleteChatBackground(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteChatBackground err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "DeleteChatBackground success, id: %s", req.GetId())
	return resp, nil
}

func (s *Server) GetChatBackgroundById(ctx context.Context, req *pb.GetChatBackgroundByIdRequest) (*pb.GetChatBackgroundByIdResponse, error) {
	resp := &pb.GetChatBackgroundByIdResponse{}

	if len(req.GetIdList()) == 0 {
		log.InfoWithCtx(ctx, "GetChatBackgroundById req.IdList is empty")
		return resp, nil
	}
	backgrounds, err := s.backgroundMgr.BatchGetChatBackground(ctx, req.GetIdList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChatBackgroundById BatchGetChatBackground req: %+v, err: %v", req, err)
		return resp, err
	}

	resp.Backgrounds = assembleBackgroundList(backgrounds)
	log.InfoWithCtx(ctx, "GetChatBackgroundById req: %+v, len(resp.Backgrounds): %d", req, len(resp.Backgrounds))
	return resp, nil
}

func (s *Server) GetPageChatBackground(ctx context.Context, req *pb.GetPageChatBackgroundRequest) (*pb.GetPageChatBackgroundResponse, error) {
	resp := &pb.GetPageChatBackgroundResponse{}

	backgrounds, total, err := s.backgroundMgr.GetChatBackgroundByPage(ctx, int64(req.GetPage()), int64(req.GetSize()), req.GetNeedCount(), req.GetName(), req.GetType())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPageChatBackground GetChatBackgroundByPage req: %+v, err: %v", req, err)
		return resp, err
	}

	resp.Backgrounds = assembleBackgroundList(backgrounds)
	resp.Total = total
	log.InfoWithCtx(ctx, "GetPageChatBackground req: %+v, total: %d, len(resp.Backgrounds): %d", req, total, len(resp.Backgrounds))
	return resp, nil
}

func (s *Server) GetBindChatBackground(ctx context.Context, req *pb.GetBindChatBackgroundRequest) (*pb.GetBindChatBackgroundResponse, error) {
	resp := &pb.GetBindChatBackgroundResponse{}

	backgrounds, err := s.backgroundMgr.GetChatBackgroundByBindEntity(ctx, req.GetEntity(), req.GetType())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBindChatBackground GetChatBackgroundByBindEntity req: %+v, err: %v", req, err)
		return resp, err
	}

	resp.Backgrounds = assembleBackgroundList(backgrounds)
	log.InfoWithCtx(ctx, "GetBindChatBackground req: %+v, len(resp.Backgrounds): %d", req, len(resp.Backgrounds))
	return resp, nil
}

// 关系配置
func (s *Server) checkRelationship(ctx context.Context, relationship *pb.Relationship) error {
	if relationship.GetName() == "" || relationship.GetAigcParams() == "" ||
		relationship.GetIcon() == "" || relationship.GetIntimacyBackgroundImg() == "" {
		log.ErrorWithCtx(ctx, "checkRelationship require param is empty")
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	if relationship.GetBindBackgroundId() != "" {
		background, err := s.backgroundMgr.GetChatBackground(ctx, relationship.GetBindBackgroundId())
		if err != nil {
			log.ErrorWithCtx(ctx, "checkRelationship GetChatBackground err: %v", err)
			return err
		}
		if background == nil {
			// 如果传上来的背景图id不存在，则清空绑定背景图id
			relationship.BindBackgroundId = ""
		} else {
			if background.DefaultUnlock {
				log.WarnWithCtx(ctx, "checkRelationship 不可绑定默认解锁背景图: %s(%s)", background.Name, background.ID)
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("不可绑定默认解锁背景图: %s", background.Name))
			}
		}
	}
	return nil
}

func (s *Server) CreateRelationship(ctx context.Context, req *pb.CreateRelationshipRequest) (*pb.CreateRelationshipResponse, error) {
	resp := &pb.CreateRelationshipResponse{}
	log.InfoWithCtx(ctx, "CreateRelationship req: %+v", req)

	reqRelationship := req.GetRelationship()
	if err := s.checkRelationship(ctx, reqRelationship); err != nil {
		return resp, err
	}

	relationship := entity.NewRelationship(reqRelationship)
	err := s.relationMgr.UpsertRelationship(ctx, relationship)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateRelationship err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "CreateRelationship success, reqRelationship: %+v, id: %s", reqRelationship, relationship.ID)
	return resp, nil
}

func (s *Server) UpdateRelationship(ctx context.Context, req *pb.UpdateRelationshipRequest) (*pb.UpdateRelationshipResponse, error) {
	resp := &pb.UpdateRelationshipResponse{}
	log.InfoWithCtx(ctx, "UpdateRelationship req: %+v", req)

	reqRelationship := req.GetRelationship()
	if err := s.checkRelationship(ctx, reqRelationship); err != nil {
		return resp, err
	}

	relationship, err := s.relationMgr.GetRelationship(ctx, reqRelationship.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateRelationship GetRelationship err: %v", err)
		return resp, err
	}
	if relationship == nil {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("id(%s)对应的关系配置不存在", reqRelationship.GetId()))
	}

	relationship.Update(reqRelationship)
	err = s.relationMgr.UpsertRelationship(ctx, relationship)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateRelationship UpsertRelationship err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "UpdateRelationship success, reqRelationship: %+v", reqRelationship)
	return resp, nil
}

func (s *Server) DeleteRelationship(ctx context.Context, req *pb.DeleteRelationshipRequest) (*pb.DeleteRelationshipResponse, error) {
	resp := &pb.DeleteRelationshipResponse{}
	log.InfoWithCtx(ctx, "DeleteRelationship req: %+v", req)

	if req.GetId() == "" {
		return resp, nil
	}

	err := s.relationMgr.DeleteRelationship(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteRelationship err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "DeleteRelationship success, id: %s", req.GetId())
	return resp, nil
}

func (s *Server) GetRelationshipList(ctx context.Context, req *pb.GetRelationshipListRequest) (*pb.GetRelationshipListResponse, error) {
	resp := &pb.GetRelationshipListResponse{}

	if !req.GetGetAll() && len(req.GetIdList()) == 0 {
		log.InfoWithCtx(ctx, "GetRelationshipList req.GetAll is false && req.IdList is empty")
		return resp, nil
	}

	var relationships []*entity.Relationship
	var err error
	if req.GetGetAll() {
		relationships, err = s.relationMgr.GetAllRelationship(ctx)
	} else {
		relationships, err = s.relationMgr.BatchGetRelationship(ctx, req.GetIdList())
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRelationshipList BatchGetRelationship req: %+v, err: %v", req, err)
		return resp, err
	}

	resp.Relationships = assembleRelationshipList(relationships)

	if req.GetGetBindInfo() {
		backgroundIds := make([]string, 0, len(relationships))
		for _, r := range relationships {
			if r.BindBackgroundID != "" {
				backgroundIds = append(backgroundIds, r.BindBackgroundID)
			}
		}
		backgrounds, err := s.backgroundMgr.BatchGetChatBackground(ctx, backgroundIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRelationshipList BatchGetChatBackground err: %v", err)
			return resp, err
		}
		resp.BackgroundInfoMap = assembleBackgroundMap(backgrounds)
	}

	log.InfoWithCtx(ctx, "GetRelationshipList req: %+v, len(resp.Relationships): %d, len(resp.BackgroundInfoMap): %d", req, len(resp.Relationships), len(resp.BackgroundInfoMap))
	return resp, nil
}

// 亲密度等级配置
func (s *Server) checkIntimacyLevelConf(ctx context.Context, levelConf *pb.IntimacyLevelConf, isCreate bool) error {
	if levelConf.GetLevel() == 0 || (levelConf.GetLevel() > 1 && levelConf.GetRequireValue() == 0) {
		log.ErrorWithCtx(ctx, "checkIntimacyLevelConf level or requireValue is 0")
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "等级>1时升级所需亲密值不能为0")
	}

	if len(levelConf.GetBenefits().GetUnlockBackgrounds()) != 0 {
		backgroundMap, err := s.backgroundMgr.GetChatBackgroundMap(ctx, levelConf.GetBenefits().GetUnlockBackgrounds())
		if err != nil {
			log.ErrorWithCtx(ctx, "checkIntimacyLevelConf GetChatBackgroundMap err: %v", err)
			return err
		}

		validBackgroundIds := make([]string, 0)
		defaultBackgroundIds := make([]string, 0)
		defaultBackgroundNames := make([]string, 0)
		for _, id := range levelConf.GetBenefits().GetUnlockBackgrounds() {
			if b, ok := backgroundMap[id]; ok {
				if b.DefaultUnlock {
					defaultBackgroundIds = append(defaultBackgroundIds, b.ID)
					defaultBackgroundNames = append(defaultBackgroundNames, b.Name)
				}
				validBackgroundIds = append(validBackgroundIds, id)
			}
		}
		levelConf.Benefits.UnlockBackgrounds = validBackgroundIds
		if len(defaultBackgroundNames) > 0 {
			warnMsg := fmt.Sprintf("不可绑定默认解锁背景图: [%s]", strings.Join(defaultBackgroundNames, ","))
			log.WarnWithCtx(ctx, "checkIntimacyLevelConf warnMsg: %s, defaultBackgroundIds: %v", warnMsg, defaultBackgroundIds)
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, warnMsg)
		}
	}
	if len(levelConf.GetBenefits().GetUnlockRelationships()) != 0 {
		relationshipMap, err := s.relationMgr.GetRelationshipMap(ctx, levelConf.GetBenefits().GetUnlockRelationships())
		if err != nil {
			log.ErrorWithCtx(ctx, "checkIntimacyLevelConf GetRelationshipMap err: %v", err)
			return err
		}

		validRelationshipIds := make([]string, 0)
		for _, id := range levelConf.GetBenefits().GetUnlockRelationships() {
			if _, ok := relationshipMap[id]; ok {
				validRelationshipIds = append(validRelationshipIds, id)
			}
		}
		levelConf.Benefits.UnlockRelationships = validRelationshipIds
	}

	if isCreate {
		config, err := s.levelMgr.GetIntimacyLevelConf(ctx, levelConf.GetLevel())
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateIntimacyLevelConf checkIntimacyLevelConf GetIntimacyLevelConfByLevel err: %v", err)
			return err
		}
		if config != nil {
			log.WarnWithCtx(ctx, "CreateIntimacyLevelConf checkIntimacyLevelConf level(%d) already exists", levelConf.GetLevel())
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "同等级亲密度配置已存在")
		}
	}

	return nil
}

func (s *Server) CreateIntimacyLevelConf(ctx context.Context, req *pb.CreateIntimacyLevelConfRequest) (*pb.CreateIntimacyLevelConfResponse, error) {
	resp := &pb.CreateIntimacyLevelConfResponse{}
	log.InfoWithCtx(ctx, "CreateIntimacyLevelConf req: %+v", req)

	reqLevelConf := req.GetLevelConfig()
	if err := s.checkIntimacyLevelConf(ctx, reqLevelConf, true); err != nil {
		return resp, err
	}

	intimacyLevelConf := entity.NewIntimacyLevelConf(reqLevelConf)
	err := s.levelMgr.UpsertIntimacyLevelConf(ctx, intimacyLevelConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateIntimacyLevelConf err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "CreateIntimacyLevelConf success, reqLevelConf: %+v, id: %s", reqLevelConf, intimacyLevelConf.ID)
	return resp, nil
}

func (s *Server) UpdateIntimacyLevelConf(ctx context.Context, req *pb.UpdateIntimacyLevelConfRequest) (*pb.UpdateIntimacyLevelConfResponse, error) {
	resp := &pb.UpdateIntimacyLevelConfResponse{}
	log.InfoWithCtx(ctx, "UpdateIntimacyLevelConf req: %+v", req)

	reqLevelConf := req.GetLevelConfig()
	if err := s.checkIntimacyLevelConf(ctx, reqLevelConf, false); err != nil {
		return resp, err
	}

	levelConf, err := s.levelMgr.GetIntimacyLevelConf(ctx, reqLevelConf.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateIntimacyLevelConf GetIntimacyLevelConf err: %v", err)
		return resp, err
	}
	if levelConf == nil {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("id(%d)对应的亲密度等级配置不存在", reqLevelConf.GetId()))
	}

	levelConf.Update(reqLevelConf)
	err = s.levelMgr.UpsertIntimacyLevelConf(ctx, levelConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateIntimacyLevelConf UpsertIntimacyLevelConf err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "UpdateIntimacyLevelConf success, reqLevelConf: %+v", reqLevelConf)
	return resp, nil
}

func (s *Server) DeleteIntimacyLevelConf(ctx context.Context, req *pb.DeleteIntimacyLevelConfRequest) (*pb.DeleteIntimacyLevelConfResponse, error) {
	resp := &pb.DeleteIntimacyLevelConfResponse{}
	log.InfoWithCtx(ctx, "DeleteIntimacyLevelConf req: %+v", req)

	if req.GetId() == 0 {
		return resp, nil
	}

	err := s.levelMgr.DeleteIntimacyLevelConf(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteIntimacyLevelConf err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "DeleteIntimacyLevelConf success, id: %s", req.GetId())
	return resp, nil
}

func (s *Server) GetIntimacyLevelConfList(ctx context.Context, req *pb.GetIntimacyLevelConfListRequest) (*pb.GetIntimacyLevelConfListResponse, error) {
	resp := &pb.GetIntimacyLevelConfListResponse{}

	if !req.GetGetAll() && len(req.GetIdList()) == 0 {
		log.InfoWithCtx(ctx, "GetIntimacyLevelConfList req.GetAll is false && req.IdList is empty")
		return resp, nil
	}

	var levelConfs []*entity.IntimacyLevelConf
	var err error
	if req.GetGetAll() {
		levelConfs, err = s.levelMgr.GetAllIntimacyLevelConf(ctx)
	} else {
		levelConfs, err = s.levelMgr.BatchGetIntimacyLevelConf(ctx, req.GetIdList())
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIntimacyLevelConfList BatchGetIntimacyLevelConf req: %+v, err: %v", req, err)
		return resp, err
	}

	resp.ConfigList = assembleIntimacyLevelConfList(levelConfs)

	if req.GetGetBindInfo() {
		backgroundIds := make([]string, 0)
		relationshipIds := make([]string, 0)
		for _, l := range levelConfs {
			if l.Benefits == nil {
				continue
			}
			if len(l.Benefits.UnlockBackgrounds) > 0 {
				backgroundIds = append(backgroundIds, l.Benefits.UnlockBackgrounds...)
			}
			if len(l.Benefits.UnlockRelationships) > 0 {
				relationshipIds = append(relationshipIds, l.Benefits.UnlockRelationships...)
			}
		}
		backgrounds, err := s.backgroundMgr.BatchGetChatBackground(ctx, backgroundIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIntimacyLevelConfList BatchGetChatBackground err: %v", err)
			return resp, err
		}
		relationships, err := s.relationMgr.BatchGetRelationship(ctx, relationshipIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIntimacyLevelConfList BatchGetRelationship err: %v", err)
			return resp, err
		}

		resp.BackgroundInfoMap = assembleBackgroundMap(backgrounds)
		resp.RelationshipInfoMap = assembleRelationshipMap(relationships)
	}

	log.InfoWithCtx(ctx, "GetIntimacyLevelConfList req: %+v, len(resp.ConfigList): %d, len(resp.BackgroundInfoMap): %d, len(resp.RelationshipInfoMap): %d",
		req, len(resp.ConfigList), len(resp.BackgroundInfoMap), len(resp.RelationshipInfoMap))
	return resp, nil
}

// 亲密度升级条件配置
func (s *Server) checkIntimacyCondition(ctx context.Context, condition *pb.IntimacyCondition) error {
	if condition.GetRequireCount() == 0 || condition.GetValueAdd() == 0 || condition.GetDailyLimit() == 0 ||
		condition.GetTitle() == "" || condition.GetDesc() == "" {
		log.ErrorWithCtx(ctx, "checkIntimacyCondition require param is empty")
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	if condition.GetConditionType() != pb.ConditionType_CONDITION_TYPE_CHAT_DAY && condition.GetConditionType() != pb.ConditionType_CONDITION_TYPE_CHAT_MSG_COUNT {
		log.ErrorWithCtx(ctx, "checkIntimacyCondition conditionType is invalid")
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	return nil
}

func (s *Server) CreateIntimacyCondition(ctx context.Context, req *pb.CreateIntimacyConditionRequest) (*pb.CreateIntimacyConditionResponse, error) {
	resp := &pb.CreateIntimacyConditionResponse{}
	log.InfoWithCtx(ctx, "CreateIntimacyCondition req: %+v", req)

	if err := s.checkIntimacyCondition(ctx, req.GetCondition()); err != nil {
		return resp, err
	}

	condition := entity.NewIntimacyCondition(req.GetCondition())
	err := s.levelUpMgr.UpsertIntimacyCondition(ctx, condition)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateIntimacyCondition err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "CreateIntimacyCondition success, req: %+v, id: %s", req, condition.ID)
	return resp, nil
}

func (s *Server) UpdateIntimacyCondition(ctx context.Context, req *pb.UpdateIntimacyConditionRequest) (*pb.UpdateIntimacyConditionResponse, error) {
	resp := &pb.UpdateIntimacyConditionResponse{}
	log.InfoWithCtx(ctx, "UpdateIntimacyCondition req: %+v", req)

	if err := s.checkIntimacyCondition(ctx, req.GetCondition()); err != nil {
		return resp, err
	}

	condition, err := s.levelUpMgr.GetIntimacyCondition(ctx, req.GetCondition().GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateIntimacyCondition GetIntimacyCondition err: %v", err)
		return resp, err
	}
	if condition == nil {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("id(%s)对应的亲密度升级条件配置不存在", req.GetCondition().GetId()))
	}

	condition.Update(req.GetCondition())
	err = s.levelUpMgr.UpsertIntimacyCondition(ctx, condition)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateIntimacyCondition UpsertIntimacyCondition err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "UpdateIntimacyCondition success, req: %+v", req)
	return resp, nil
}

func (s *Server) DeleteIntimacyCondition(ctx context.Context, req *pb.DeleteIntimacyConditionRequest) (*pb.DeleteIntimacyConditionResponse, error) {
	resp := &pb.DeleteIntimacyConditionResponse{}
	log.InfoWithCtx(ctx, "DeleteIntimacyCondition req: %+v", req)

	if req.GetId() == "" {
		return resp, nil
	}

	err := s.levelUpMgr.DeleteIntimacyCondition(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteIntimacyCondition err: %v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "DeleteIntimacyCondition success, id: %s", req.GetId())
	return resp, nil
}

func (s *Server) GetIntimacyConditionList(ctx context.Context, req *pb.GetIntimacyConditionListRequest) (*pb.GetIntimacyConditionListResponse, error) {
	resp := &pb.GetIntimacyConditionListResponse{}

	if !req.GetGetAll() && len(req.GetIdList()) == 0 {
		log.InfoWithCtx(ctx, "GetIntimacyConditionList req.GetAll is false && req.IdList is empty")
		return resp, nil
	}

	var conditions []*entity.IntimacyCondition
	var err error
	if req.GetGetAll() {
		conditions, err = s.levelUpMgr.GetAllIntimacyCondition(ctx)
	} else {
		conditions, err = s.levelUpMgr.BatchGetIntimacyCondition(ctx, req.GetIdList())
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIntimacyConditionList BatchGetIntimacyCondition req: %+v, err: %v", req, err)
		return resp, err
	}

	resp.Conditions = assembleIntimacyConditionList(conditions)

	log.InfoWithCtx(ctx, "GetIntimacyConditionList req: %+v, len(resp.Conditions): %d", req, len(resp.Conditions))
	return resp, nil
}

// GenerateChatBackground 生成聊天背景图
func (s *Server) GenerateChatBackground(ctx context.Context, req *pb.GenerateChatBackgroundRequest) (*pb.GenerateChatBackgroundResponse, error) {
	resp := &pb.GenerateChatBackgroundResponse{}
	log.InfoWithCtx(ctx, "GenerateChatBackground req: %+v", req)

	bindEntity := req.GetEntity()
	if bindEntity.GetId() == 0 || bindEntity.GetType() != pb.BindEntity_ENTITY_TYPE_ROLE || req.GetEntityBg() == "" || len(req.GetBackgrounds()) == 0 {
		log.ErrorWithCtx(ctx, "GenerateChatBackground invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	isReach, err := s.backgroundMgr.IsReachBindEntityChatBackgroundLimit(ctx, bindEntity, uint32(len(req.GetBackgrounds())))
	if err != nil {
		log.ErrorWithCtx(ctx, "GenerateChatBackground CountChatBackgroundByBindEntity bindEntity(%+v) err: %v", bindEntity, err)
		return resp, err
	}
	if isReach {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "数量超过上限")
	}

	for _, background := range req.GetBackgrounds() {
		bg := &pb.ChatBackground{
			Type:            pb.ChatBackgroundType_CHAT_BACKGROUND_TYPE_PRESENT,
			Name:            background.GetName(),
			ShowAvatar:      background.GetShowAvatar(),
			UnlockCondition: background.GetUnlockCondition(),
			BindEntityType:  pb.ChatBackground_BIND_ENTITY_TYPE_ROLE_LIST,
			BindEntityIds:   []uint32{bindEntity.GetId()},
		}
		bgData, err := json.Marshal(bg)
		if err != nil {
			log.ErrorWithCtx(ctx, "GenerateChatBackground Marshal bg(%+v) err: %v", bg, err)
			return resp, err
		}

		bizReq := &rcmd_mt_proxy.GenSoulmateGiveGiftRoleImageReq{
			RoleId:            bindEntity.GetId(),
			StyleId:           background.GetStyleId(),
			RoleBackgroundUrl: req.GetEntityBg(),
		}
		bizData, err := json.Marshal(bizReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "GenerateChatBackground Marshal bizReq(%+v) err: %v", bizReq, err)
			return resp, err
		}

		asyncReq := &rcmd_mt_proxy.AsyncReq{
			Uid:   int64(bindEntity.GetId()),
			CtxId: primitive.NewObjectID().Hex(),

			Cmd:  "GenSoulmateGiveGiftRoleImage",
			Data: string(bizData),
			CallbackData: map[string]string{
				"background": string(bgData),
			},
		}
		if err := s.eventBus.Publish(ctx, event.PubEventAigcProxy, strconv.Itoa(int(bindEntity.GetId())), asyncReq); err != nil {
			log.ErrorWithCtx(ctx, "GenerateChatBackground Publish msg(%+v) err: %v", asyncReq, err)
			return resp, err
		}

		log.InfoWithCtx(ctx, "GenerateChatBackground Publish msg(%+v) finished", asyncReq)
	}

	return resp, nil
}
