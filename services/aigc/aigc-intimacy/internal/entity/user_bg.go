package entity

import (
	"time"

	pb "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
)

type UserBackgrounds []*UserBackground

type UserBackground struct {
	Id        string    `bson:"_id"`
	CreatedAt time.Time `bson:"created_at"` // 创建时间

	Uid  uint32 `bson:"uid"`   // 用户ID
	BgId string `bson:"bg_id"` // 背景ID

	BindId   uint32                   `bson:"bind_id"`   // 绑定ID
	BindType pb.BindEntity_EntityType `bson:"bind_type"` // 绑定类型
}
