package mgr

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"gitlab.ttyuyin.com/tyr/x/log"
	context_info "golang.52tt.com/pkg/context-info"
	pb "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_voice_room"
	zegoPb "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/db/entity"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"io"
	"strconv"
	"strings"
	"sync"
	"time"
)

const (
	ZegoStreamUpdateTypeAdd = "add"
	ZegoStreamUpdateTypeDel = "del"
)

type StreamClients struct {
	aigcStreamCli   pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient
	zegoStreamCli   zegoPb.ZegoStream_StreamTransferClient
	streamId        string
	uid             uint32
	closeStream<PERSON>han chan struct{}
	sMgr            *StreamManager
}

func (s *StreamClients) closeChan() {
	_ = s.zegoStreamCli.CloseSend()
	_ = s.aigcStreamCli.CloseSend()
	close(s.closeStreamChan)
}

func (m *StreamClients) handleRecvError(ctx context.Context, streamId string, err error) bool {
	if err == io.EOF {
		log.InfoWithCtx(ctx, "handleRecvError streamId:%s Stream  ended normally", streamId)
		return false
	}

	// 检查 gRPC 状态码
	if status, ok := status.FromError(err); ok {
		switch status.Code() {
		case codes.Canceled:
			log.InfoWithCtx(ctx, "handleRecvError streamId:%s Stream canceled: %v", streamId, status.Message())
			return false

		case codes.DeadlineExceeded:
			log.InfoWithCtx(ctx, "handleRecvError streamId:%s  Stream timeout, will retry: %v", streamId, status.Message())
			return true // 可以重试

		case codes.Unavailable, codes.ResourceExhausted:
			log.InfoWithCtx(ctx, "handleRecvError streamId:%s  Temporary error, will retry: %v", streamId, status.Message())
			time.Sleep(1 * time.Second) // 等待后重试
			return true

		case codes.Unimplemented, codes.PermissionDenied:
			log.InfoWithCtx(ctx, "handleRecvError streamId:%s  Permanent error: %v", streamId, status.Message())
			return false

		default:
			log.InfoWithCtx(ctx, "handleRecvError streamId:%s  gRPC Code [%v]: message:%s", streamId, status.Code(), status.Message())
			return false
		}
	}

	log.InfoWithCtx(ctx, "handleRecvError streamId:%s  Non-gRPC error: %v", streamId, err)
	return false
}

func (m *StreamClients) handleStream(channelId uint32, streamId string, streamInterface interface{}, uid uint32) {

	msgChan := make(chan *CommonMsg, 100) // 带缓冲的channel

	// 在函数内初始化sync.Once（确保每个handleStream实例独立）
	var msgChanCloseOnce sync.Once

	ctx := context_info.GenReqId(context.Background())
	workerCount := 3
	var wg sync.WaitGroup
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func(workerId int) {
			defer wg.Done()
			for msg := range msgChan {
				m.ProcessResponse(msg)
			}
			log.InfoWithCtx(ctx, "handleStream Worker:%d finished", workerId)
		}(i)
	}

	for {
		select {
		case <-m.closeStreamChan:
			msgChanCloseOnce.Do(func() {
				close(msgChan)
			}) // 关闭channel，通知工作者没有新消息了
			wg.Wait() // 等待所有工作者处理完剩余消息
			return    // 全局关闭
		default:
			if stream, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
				resp, err := stream.Recv()
				if err != nil {
					if m.handleRecvError(ctx, streamId, err) {
						log.InfoWithCtx(ctx, "handleStream Stream %s error: %v, reconnecting...", streamId, err)
						if !m.sMgr.reconnectStream(ctx, entity.AigcStreamType, uid, resp.GetChannelId(), streamId) {
							//TODO 重试失败，退出并写kafka
							return
						} else {
							log.ErrorWithCtx(ctx, "handleStream aigc Stream %s error: %+v", streamId, errors.WithStack(err))
							continue // 可恢复错误，继续尝试
						}
					}

					msgChanCloseOnce.Do(func() {
						close(msgChan)
					}) // 关闭channel，通知工作者没有新消息了
					wg.Wait() // 等待所有工作者处理完剩余消息
					return
				}

				if streamId != strconv.Itoa(int(resp.GetChannelId())) {
					log.ErrorWithCtx(ctx, "handleStream Stream ID mismatch: expected %s, got %s", streamId, resp.GetChannelId())
				}
				commonMsg := &CommonMsg{
					StreamId: streamId,
					AigcMsg:  resp,
					MsgType:  entity.AigcStreamType,
				}
				msgChan <- commonMsg

				log.Infof("aigc [%s] Received: %+v", streamId, resp.String())
			} else if stream, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
				resp, err := stream.Recv()
				if err != nil {
					if m.handleRecvError(ctx, streamId, err) {
						log.InfoWithCtx(ctx, "handleStream Stream %s error: %v, reconnecting...", streamId, err)
						if !m.sMgr.reconnectStream(ctx, entity.AigcStreamType, uid, channelId, streamId) {
							//TODO 重试失败，退出并写kafka
							return
						} else {
							log.ErrorWithCtx(ctx, "handleStream aigc Stream %s error: %+v", streamId, errors.WithStack(err))
							continue // 可恢复错误，继续尝试
						}
					}
					msgChanCloseOnce.Do(func() {
						close(msgChan)
					}) // 关闭channel，通知工作者没有新消息了
					wg.Wait() // 等待所有工作者处理完剩余消息
					return
				}
				commonMsg := &CommonMsg{
					StreamId: streamId,
					ZegoMsg:  resp,
					MsgType:  entity.ZegoStreamType,
				}
				msgChan <- commonMsg
				log.InfoWithCtx(ctx, "handleStream zego streamId:%s aiUid:%d Received: %+v", streamId, uid, resp.String())
			}
		}
	}

}

func (m *StreamClients) monitorStream(channelId uint32, streamId string, streamInterface interface{}, uid uint32) {

	heartbeatTicker := time.NewTicker(10 * time.Second)
	defer heartbeatTicker.Stop()
	ctx := context_info.GenReqId(context.Background())
	for {
		select {
		case <-m.closeStreamChan:
			return
		case <-heartbeatTicker.C:
			if stream, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
				if err := stream.Send(&pb.RecvAudioStreamInRoomReq{
					ChannelId: channelId,
					Uid:       uid,
					//UserAudio: []byte("ping"),
					Ping: true,
				}); err != nil {
					log.ErrorWithCtx(ctx, "monitorStream aigc stream.Send err: %v, aiuid:%d streamId:%s", err, uid, streamId)
					//m.streamRecord.HeartbeatFail(context.Background(), streamId, entity.AigcStreamType)
					continue
				}
				log.InfoWithCtx(ctx, "monitorStream aigc heartbeat sent, aiuid:%d streamId:%s", uid, streamId)
			} else if stream, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
				if err := stream.Send(&zegoPb.EventWrap{Data: []byte("ping")}); err != nil {
					log.ErrorWithCtx(ctx, "monitorStream zego stream.Send err: %v,  aiuid:%d streamId:%s", err, uid, streamId)
					continue
				}
				log.InfoWithCtx(ctx, "monitorStream zego heartbeat sent, aiuid:%d streamId:%s", uid, streamId)
			} else {
				log.ErrorWithCtx(ctx, "monitorStream stream type err, aiuid:%d streamId:%s", uid, streamId)
				return
			}
		}
	}
}

func (m *StreamClients) ProcessResponse(commonMsg *CommonMsg) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	ctx = context_info.GenReqId(ctx)
	if commonMsg.MsgType == entity.AigcStreamType {
		if commonMsg.AigcMsg.GetPong() {
			log.InfoWithCtx(ctx, "ProcessResponse aigc received Pong [%s] AigcMsg:%s, commonMsg: %+v", commonMsg.AigcMsg.GetChannelId(), commonMsg.AigcMsg.String(), commonMsg)
		} else {
			log.InfoWithCtx(ctx, "ProcessResponse aigc receive Msg [%s] AigcMsg:%s, commonMsg: %+v", commonMsg.AigcMsg.GetChannelId(), commonMsg.AigcMsg.String(), commonMsg)
			_ = m.sMgr.SendAudioData(ctx, commonMsg.StreamId, commonMsg.AigcMsg.GetTtsAudio())
		}
	} else if commonMsg.MsgType == entity.ZegoStreamType {
		msg := commonMsg.ZegoMsg
		emptyAudio := 0
		switch msg.Event {
		case zegoPb.EventType_Heartbeat:

		case zegoPb.EventType_LoginResult:
			log.InfoWithCtx(ctx, "ProcessResponse zego recv login result, commonMsg: %+v, zegoMsg:%s", commonMsg, commonMsg.ZegoMsg.String())
			//失败需要重试
		case zegoPb.EventType_StreamUpdate:
			event := new(streamUpdateEvent)
			err := json.Unmarshal([]byte(msg.Json), event)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse zego streamId:%s json.Unmarshal err:%v", commonMsg.StreamId, err)
				return
			}
			if event.UpdateType == ZegoStreamUpdateTypeAdd {
				for _, streamId := range event.StreamList {
					if strings.HasPrefix(streamId, "91") {
						err := m.sMgr.startPullStream(ctx, streamId)
						if err != nil {
							log.ErrorWithCtx(ctx, "ProcessResponse zego startPullStream err: %v, commonMsg: %+v, event:%+v", err, commonMsg, event)
						}
						//pullStream = streamId
					}
				}
			} else if event.UpdateType == ZegoStreamUpdateTypeDel {
				for _, streamId := range event.StreamList {
					err := m.sMgr.stopPullStream(ctx, streamId)
					if err != nil {
						log.ErrorWithCtx(ctx, "ProcessResponse zego stopPullStream err: %v, commonMsg: %+v, event:%+v", err, commonMsg, event)
					}
				}
			} else {
				log.ErrorWithCtx(ctx, "ProcessResponse zego updatetype err, commonMsg: %+v, event:%+v", commonMsg, event)
			}

		case zegoPb.EventType_Audio:
			if sumAudioData(msg.Data) > 0 {
				err := m.sMgr.sendAigcStream(ctx, commonMsg.StreamId, msg.Data)
				if err != nil {
					log.ErrorWithCtx(ctx, "ProcessResponse zego stopPullStream err: %v, commonMsg: %+v", err, commonMsg)
				}
				emptyAudio = 0
			} else {
				emptyAudio++
				log.ErrorWithCtx(ctx, "ProcessResponse zego empty audio:%d, commonMsg: %+v", emptyAudio, commonMsg)
			}

		}
	}

}
