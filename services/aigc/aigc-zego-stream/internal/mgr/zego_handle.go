package mgr

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_voice_room"
	zegoPb "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"
	"strings"
	"time"
)

type streamUpdateEvent struct {
	RoomId     string   `json:"room_id"`
	UpdateType string   `json:"update_type"`
	StreamList []string `json:"stream_list"`
}

type ZegoHandleT struct {
	streamMgr *StreamManager

	streamRecord *StreamRecord
}

func NewZegoHandle(streamMgr *StreamManager, streamRecord *StreamRecord) *ZegoHandleT {
	return &ZegoHandleT{
		streamMgr:    streamMgr,
		streamRecord: streamRecord,
	}
}

func sumAudioData(data []byte) int {
	sum := 0
	for _, v := range data {
		sum += int(v)
	}
	return sum
}

func (z *ZegoHandleT) ProcessResponse(commonMsg *CommonMsg) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if commonMsg.MsgType == AigcStreamType {
		z.SendAudioData(ctx, commonMsg.StreamId, commonMsg.AigcMsg.GetTtsAudio())
	} else if commonMsg.MsgType == ZegoStreamType {
		msg := commonMsg.ZegoMsg
		emptyAudio := 0
		switch msg.Event {
		case zegoPb.EventType_LoginResult:
			log.InfoWithCtx(ctx, "ProcessResponse recv login result, commonMsg: %+v, zegoMsg:%s", commonMsg, commonMsg.ZegoMsg.String())
			//失败需要重试
		case zegoPb.EventType_StreamUpdate:
			event := new(streamUpdateEvent)
			json.Unmarshal([]byte(msg.Json), event)
			if event.UpdateType == ZegoStreamUpdateTypeAdd {
				for _, streamId := range event.StreamList {
					if strings.HasPrefix(streamId, "91") {
						err := z.startPullStream(ctx, streamId)
						if err != nil {
							log.ErrorWithCtx(ctx, "startPullStream err: %v, commonMsg: %+v, event:%+v", err, commonMsg, event)
						}
						//pullStream = streamId
					}
				}
			} else if event.UpdateType == ZegoStreamUpdateTypeDel {
				for _, streamId := range event.StreamList {
					err := z.stopPullStream(ctx, streamId)
					if err != nil {
						log.ErrorWithCtx(ctx, "stopPullStream err: %v, commonMsg: %+v, event:%+v", err, commonMsg, event)
					}
				}
			} else {
				log.ErrorWithCtx(ctx, "ProcessResponse updatetype err, commonMsg: %+v, event:%+v", commonMsg, event)
			}

		case zegoPb.EventType_Audio:
			if sumAudioData(msg.Data) > 0 {
				err := z.sendAigcStream(ctx, commonMsg.StreamId, msg.Data)
				if err != nil {
					log.ErrorWithCtx(ctx, "stopPullStream err: %v, commonMsg: %+v", err, commonMsg)
				}
				emptyAudio = 0
			} else {
				emptyAudio++
				log.Errorf("empty audio:%d, commonMsg: %+v", emptyAudio, commonMsg)
			}

		}
	}

}

func (z *ZegoHandleT) loginRoom(ctx context.Context, appId string, channelId, userId, streamId, streamName string) error {
	jsonRaw, err := json.Marshal(map[string]any{"app_id": appId, "room_id": channelId, "user_id": userId, "stream_id": streamName})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, appId: %s, channelId: %s, userId: %s, streamId: %s", err, appId, channelId, userId, streamId)
		return err
	}

	streamClis := z.streamMgr.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "loginRoom streamClis is nil, streamId: %s", streamId)
		return nil
	}

	err = streamClis.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Login, Json: string(jsonRaw)})
	if err != nil {
		log.ErrorWithCtx(ctx, "loginRoom zegoStreamCli.Send err, streamId: %s", err, streamId)
		return err
	}

	return nil
}

func (z *ZegoHandleT) startPullStream(ctx context.Context, streamId string) error {
	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamId})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, streamId: %s", err, streamId)
		return err
	}

	streamClis := z.streamMgr.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "startPullStream streamClis is nil, streamId: %s", streamId)
		return nil
	}
	err = streamClis.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_StartPullStream, Json: string(jsonRaw)})
	if err != nil {
		log.ErrorWithCtx(ctx, "startPullStream zegoStreamCli.Send err: %v, streamId: %s", err, streamId)
		return err
	}

	return nil
}

func (z *ZegoHandleT) stopPullStream(ctx context.Context, streamId string) error {
	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamId})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, streamId: %s", err, streamId)
		return err
	}
	streamClis := z.streamMgr.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s", streamId)
		return nil
	}
	err = streamClis.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_StopPullStream, Json: string(jsonRaw)})
	if err != nil {
		log.ErrorWithCtx(ctx, "stopPullStream zegoStreamCli.Send err: %v, streamId: %s", err, streamId)
		return err
	}

	return nil
}

func (z *ZegoHandleT) sendAigcStream(ctx context.Context, streamId string, data []byte) error {
	streamClis := z.streamMgr.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s", streamId)
		return nil
	}
	err := streamClis.aigcStreamCli.Send(&pb.RecvAudioStreamInRoomReq{UserAudio: data})
	if err != nil {
		log.ErrorWithCtx(ctx, "sendAigcStream error: %v, streamId: %s", err, streamId)
		return err
	}

	return nil
}

func (z *ZegoHandleT) SendAudioData(ctx context.Context, streamId string, data []byte) error {
	streamClis := z.streamMgr.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s", streamId)
		return nil
	}
	err := streamClis.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Audio, Data: data})
	if err != nil {
		log.ErrorWithCtx(ctx, "sendAudioData error: %v, streamId: %s", err, streamId)
		return err
	}
	return nil
}
