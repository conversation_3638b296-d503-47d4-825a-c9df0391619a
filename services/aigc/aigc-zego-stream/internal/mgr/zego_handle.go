package mgr

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	zegoPb "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"
)

type streamUpdateEvent struct {
	RoomId     string   `json:"room_id"`
	UpdateType string   `json:"update_type"`
	StreamList []string `json:"stream_list"`
}

type ZegoHandleT struct {
	streamMgr *StreamManager

	streamRecord *StreamRecord
}

func NewZegoHandle(streamMgr *StreamManager, streamRecord *StreamRecord) *ZegoHandleT {
	return &ZegoHandleT{
		streamMgr:    streamMgr,
		streamRecord: streamRecord,
	}
}

func sumAudioData(data []byte) int {
	sum := 0
	for _, v := range data {
		sum += int(v)
	}
	return sum
}

func (z *ZegoHandleT) loginRoom(ctx context.Context, appId string, channelId, userId, streamId, streamName string) error {
	jsonRaw, err := json.Marshal(map[string]any{"app_id": appId, "room_id": channelId, "user_id": userId, "stream_id": streamName})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, appId: %s, channelId: %s, userId: %s, streamId: %s", err, appId, channelId, userId, streamId)
		return err
	}

	streamClis := z.streamMgr.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "loginRoom streamClis is nil, streamId: %s", streamId)
		return nil
	}

	err = streamClis.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Login, Json: string(jsonRaw)})
	if err != nil {
		log.ErrorWithCtx(ctx, "loginRoom zegoStreamCli.Send err, streamId: %s", err, streamId)
		return err
	}

	return nil
}
