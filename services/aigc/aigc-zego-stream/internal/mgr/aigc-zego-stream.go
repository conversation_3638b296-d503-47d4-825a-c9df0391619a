package mgr

import (
	"context"
	"github.com/pkg/errors"
	"gitlab.ttyuyin.com/tyr/x/log"
	pb "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_voice_room"
	zegoPb "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"sync"
	"time"
)

const (
	AigcStreamType = 0
	ZegoStreamType = 1

	ZegoStreamUpdateTypeAdd = "add"
	ZegoStreamUpdateTypeDel = "del"
)

type StreamClients struct {
	aigcStreamCli pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient
	zegoStreamCli zegoPb.ZegoStream_StreamTransferClient
	streamId      string
	uid           uint32
}

type CommonMsg struct {
	StreamId string
	ZegoMsg  *zegoPb.EventWrap
	AigcMsg  *pb.RecvAudioStreamInRoomResp
	MsgType  uint32
}

type StreamManager struct {
	aigcClient      *pb.Client
	zegoProxyClient *zegoPb.Client
	streams         sync.Map // key: streamId
	closeChan       chan struct{}
	zegoHandle      *ZegoHandleT
}

func (m *StreamManager) GetSteamCli(streamId string) *StreamClients {
	if value, exists := m.streams.Load(streamId); !exists {
		return nil
	} else {
		return value.(*StreamClients)
	}
}

func NewStreamManager(ctx context.Context) (*StreamManager, error) {

	aigcClient, err := pb.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewStreamManager pb.NewClient err: %v", err)
		return nil, err
	}

	zegoProxyClient, err := zegoPb.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewStreamManager zegoPb.NewClient err: %v", err)
		return nil, err
	}
	return &StreamManager{
		aigcClient:      aigcClient,
		zegoProxyClient: zegoProxyClient,
		closeChan:       make(chan struct{}),
	}, nil
}

func (m *StreamManager) CreateStream(ctx context.Context, streamType uint32, streamId string, uid uint32) error {

	if streamType == AigcStreamType {
		stream, err := m.aigcClient.RecvAudioStreamInRoom(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateStream RecvAudioStreamInRoom err: %v, streamId:%d uid:%d", err, streamId, uid)
			return err
		}

		if streamInfo, ok := m.streams.Load(streamId); ok {
			log.InfoWithCtx(ctx, "CreateStream Stream:%s already exists", streamId)
			streamInfo.(*StreamClients).aigcStreamCli = stream
		} else {
			log.InfoWithCtx(ctx, "Creating new stream: %s", streamId)
			m.streams.Store(streamId, &StreamClients{
				aigcStreamCli: stream,
				streamId:      streamId,
				uid:           uid,
			})
		}
		go m.monitorStream(streamId, stream, uid) // 启动监控协程
		go m.handleStream(streamId, stream, uid)
	} else if streamType == ZegoStreamType {
		stream, err := m.zegoProxyClient.StreamTransfer(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateStream StreamTransfer err: %v, streamId:%s", err, streamId)
			return err
		}

		if streamInfo, ok := m.streams.Load(streamId); ok {
			log.Infof("CreateStream Stream:%s already exists", streamId)
			streamInfo.(*StreamClients).zegoStreamCli = stream
		} else {
			log.Infof("Creating new stream: %s", streamId)
			m.streams.Store(streamId, &StreamClients{
				zegoStreamCli: stream,
				streamId:      streamId,
				uid:           uid,
			})
		}
		go m.monitorStream(streamId, stream, uid) // 启动监控协程
		go m.handleStream(streamId, stream, uid)
	}

	return nil
}

func (m *StreamManager) handleStream(streamId string, streamInterface interface{}, uid uint32) {

	//heartbeatTicker := time.NewTicker(10 * time.Second)
	//defer heartbeatTicker.Stop()

	msgChan := make(chan *CommonMsg, 100) // 带缓冲的channel

	// 在函数内初始化sync.Once（确保每个handleStream实例独立）
	var msgChanCloseOnce sync.Once

	defer func() {
		m.streams.Delete(streamId)
	}()

	workerCount := 3
	var wg sync.WaitGroup
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func(workerId int) {
			defer wg.Done()
			for msg := range msgChan {
				m.zegoHandle.ProcessResponse(msg)
			}
			log.Infof("handleStream Worker:%d finished", workerId)
		}(i)
	}

	for {
		select {
		case <-m.closeChan:
			msgChanCloseOnce.Do(func() {
				close(msgChan)
			})        // 关闭channel，通知工作者没有新消息了
			wg.Wait() // 等待所有工作者处理完剩余消息
			return    // 全局关闭
		default:
			if stream, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
				resp, err := stream.Recv()
				if err != nil {
					if status.Code(err) != codes.Canceled {
						log.Infof("Stream %s error: %v, reconnecting...", streamId, err)
						// 自动重连
						if !m.reconnectStream(AigcStreamType, uid, streamId) {
							continue
						}
					} else {
						// 检查错误是否是因为上下文取消（这是优雅退出的常见情况）
						// TODO 测试环境验证
						log.Errorf("aigc Stream %s error: %+v, reconnecting...", streamId, errors.WithStack(err))
					}
					msgChanCloseOnce.Do(func() {
						close(msgChan)
					})        // 关闭channel，通知工作者没有新消息了
					wg.Wait() // 等待所有工作者处理完剩余消息
					return
				}

				if streamId != resp.GetChannelId() {
					log.Errorf("Stream ID mismatch: expected %s, got %s", streamId, resp.GetChannelId())
				}
				if resp.GetPong() {
					log.Infof("Pong [%s] Received: %+v", streamId, resp.String())
					continue
				}
				commonMsg := &CommonMsg{
					StreamId: streamId,
					AigcMsg:  resp,
					MsgType:  AigcStreamType,
				}
				msgChan <- commonMsg
				log.Infof("aigc [%s] Received: %+v", streamId, resp.String())
			} else if stream, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
				resp, err := stream.Recv()
				if err != nil {
					if status.Code(err) != codes.Canceled {
						log.Infof("Stream %s error: %v, reconnecting...", streamId, err)
						// 自动重连
						if !m.reconnectStream(ZegoStreamType, uid, streamId) {
							continue
						}
					} else {
						// 检查错误是否是因为上下文取消（这是优雅退出的常见情况）
						// TODO 测试环境验证
						log.Errorf("zego Stream %s error: %+v, reconnecting...", streamId, errors.WithStack(err))

					}
					msgChanCloseOnce.Do(func() {
						close(msgChan)
					})        // 关闭channel，通知工作者没有新消息了
					wg.Wait() // 等待所有工作者处理完剩余消息
					return
				}
				commonMsg := &CommonMsg{
					StreamId: streamId,
					ZegoMsg:  resp,
					MsgType:  ZegoStreamType,
				}
				msgChan <- commonMsg
				log.Infof("zego [%s] Received: %+v", streamId, resp.String())
			}
		}
	}

}

func (m *StreamManager) monitorStream(streamId string, streamInterface interface{}, uid uint32) {
	defer func() {
		m.streams.Delete(streamId)
	}()

	heartbeatTicker := time.NewTicker(10 * time.Second)
	defer heartbeatTicker.Stop()

	for {
		select {
		case <-m.closeChan:
			return
		case <-heartbeatTicker.C:
			if stream, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
				if err := stream.Send(&pb.RecvAudioStreamInRoomReq{
					ChannelId: streamId,
					Uid:       uid,
					UserAudio: []byte("ping"),
					Ping:      true,
				}); err != nil {
					log.Errorf("monitorStream aigc stream.Send err: %v, streamId:%d", err, streamId)
					return
				}
			} else if stream, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
				if err := stream.Send(&zegoPb.EventWrap{Data: []byte("ping")}); err != nil {
					log.Errorf("monitorStream zego stream.Send err: %v, streamId:%d", err, streamId)
					return
				}
			}

		default:
			//if stream, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
			//	resp, err := stream.Recv()
			//	if err != nil {
			//		if status.Code(err) != codes.Canceled {
			//			log.Errorf("stream.Recv err: %v, streamId:%d, reconnecting...", err, streamId)
			//			m.reconnectStream(AigcStreamType, uid, streamId) // 自动重连
			//		}
			//		return
			//	}
			//	log.Infof("[%s] Received: %+v", streamId, resp.String())
			//} else if stream, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
			//	resp, err := stream.Recv()
			//	if err != nil {
			//		if status.Code(err) != codes.Canceled {
			//			log.Errorf("stream.Recv err: %v, streamId:%d, reconnecting...", err, streamId)
			//			m.reconnectStream(ZegoStreamType, uid, streamId) // 自动重连
			//		}
			//		return
			//	}
			//	log.Infof("[%s] Received: %+v", streamId, resp.String())
			//}
		}
	}
}

func (m *StreamManager) reconnectStream(streamType, uid uint32, streamId string) (isSuccess bool) {
	// 指数退避重试逻辑
	backoff := time.Second
	for i := 0; i < 3; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		if err := m.CreateStream(ctx, streamType, streamId, uid); err == nil {
			cancel()
			log.Infof("reconnect stream %s err:after retries:%d times", streamId, i+1)
			return true
		}
		cancel()
		time.Sleep(backoff)
		backoff *= 2
	}
	log.Infof("reconnect stream fail %s err:after retries", streamId)
	return false
}

func (m *StreamManager) DeleteStreamId(ctx context.Context, streamId string) {
	if streamInfo, ok := m.streams.Load(streamId); ok {
		log.Infof("CreateStream Stream:%s already exists", streamId)
		streamInfo.(*StreamClients).zegoStreamCli.CloseSend()
		streamInfo.(*StreamClients).aigcStreamCli.CloseSend()
		m.streams.Delete(streamId) // 删除指定的 Stream
	} else {
		log.ErrorWithCtx(ctx, "DeleteStreamId StreamId:%s err: not exist", streamId)
	}
}

func (m *StreamManager) Close() {
	close(m.closeChan) // 通知所有监控协程退出

	// 关闭所有 Stream
	m.streams.Range(func(key, value interface{}) bool {
		if err := value.(*StreamClients).aigcStreamCli.CloseSend(); err != nil {
			log.Infof("CloseSend err: %v, id: %v", err, key)
		}
		if err := value.(*StreamClients).zegoStreamCli.CloseSend(); err != nil {
			log.Infof("CloseSend err: %v, id: %v", err, key)
		}
		m.streams.Delete(key)
		return true
	})

}
