package mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/grpc/client/interceptor"
	"gitlab.ttyuyin.com/tyr/x/log"
	pb "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_voice_room"
	zegoPb "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/db/entity"
	"google.golang.org/grpc"
	"strconv"
	"sync"
	"time"
)

type streamUpdateEvent struct {
	RoomId     string   `json:"room_id"`
	UpdateType string   `json:"update_type"`
	StreamList []string `json:"stream_list"`
}

func sumAudioData(data []byte) int {
	sum := 0
	for _, v := range data {
		sum += int(v)
	}
	return sum
}

type CommonMsg struct {
	StreamId string
	ZegoMsg  *zegoPb.EventWrap
	AigcMsg  *pb.RecvAudioStreamInRoomResp
	MsgType  uint32
}

type StreamManager struct {
	aigcClient      *pb.Client
	zegoProxyClient *zegoPb.Client
	streams         sync.Map // key: streamId
	streamRecord    *StreamRecord
	zegoAppId       string
}

func (m *StreamManager) GetSteamCli(streamId string) *StreamClients {
	if value, exists := m.streams.Load(streamId); !exists {
		return nil
	} else {
		return value.(*StreamClients)
	}
}

func NewStreamManager(ctx context.Context, streamRecord *StreamRecord, zegoAppId string) (*StreamManager, error) {

	aigcClient := pb.MustNewClientTo(ctx, "aigc-voice-room.rcmd-tt.svc:8000",
		grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()))

	zegoProxyClient := zegoPb.MustNewClientTo(ctx, "zego-proxy.rcmd-tt.svc.cluster.local:8000",
		grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()))

	return &StreamManager{
		aigcClient:      aigcClient,
		zegoProxyClient: zegoProxyClient,
		streamRecord:    streamRecord,
		zegoAppId:       zegoAppId,
	}, nil
}

func GetStreamName(uid, channelId uint32) string {
	return fmt.Sprintf("server-%d-Baigcroom-C%d-%d", uid, channelId, time.Now().UnixMilli())
}

func (m *StreamManager) CreateStream(ctx context.Context, streamType uint32, streamId string, uid, channelId uint32) error {
	if streamType == entity.AigcStreamType {
		stream, err := m.aigcClient.RecvAudioStreamInRoom(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateStream RecvAudioStreamInRoom err: %v, streamId:%d uid:%d", err, streamId, uid)
			return err
		}

		if s, ok := m.streams.Load(streamId); ok {
			s.(*StreamClients).closeChan()

			log.InfoWithCtx(ctx, "CreateStream Stream:%s already exists", streamId)
			s.(*StreamClients).closeStreamChan = make(chan struct{})
			s.(*StreamClients).aigcStreamCli = stream
			s.(*StreamClients).streamId = streamId
			s.(*StreamClients).uid = uid
			s.(*StreamClients).sMgr = m
			go s.(*StreamClients).monitorStream(channelId, streamId, stream, uid)
			go s.(*StreamClients).handleStream(channelId, streamId, stream, uid)
		} else {
			log.InfoWithCtx(ctx, "Creating new stream: %s", streamId)
			sTmp := &StreamClients{
				closeStreamChan: make(chan struct{}),
				aigcStreamCli:   stream,
				streamId:        streamId,
				uid:             uid,
				sMgr:            m,
			}
			m.streams.Store(streamId, sTmp)
			go sTmp.monitorStream(channelId, streamId, stream, uid) // 启动监控协程
			go sTmp.handleStream(channelId, streamId, stream, uid)
		}

	} else if streamType == entity.ZegoStreamType {
		stream, err := m.zegoProxyClient.StreamTransfer(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateStream StreamTransfer err: %v, streamId:%s", err, streamId)
			return err
		}

		if streamInfo, ok := m.streams.Load(streamId); ok {
			log.Infof("CreateStream Stream:%s already exists", streamId)
			streamInfo.(*StreamClients).closeStreamChan = make(chan struct{})
			streamInfo.(*StreamClients).zegoStreamCli = stream
			streamInfo.(*StreamClients).streamId = streamId
			streamInfo.(*StreamClients).uid = uid
			streamInfo.(*StreamClients).sMgr = m
		} else {
			log.Infof("Creating new stream: %s", streamId)
			sTmp := &StreamClients{
				closeStreamChan: make(chan struct{}),
				zegoStreamCli:   stream,
				streamId:        streamId,
				uid:             uid,
				sMgr:            m,
			}
			m.streams.Store(streamId, sTmp)
			go sTmp.monitorStream(channelId, streamId, stream, uid) // 启动监控协程
			go sTmp.handleStream(channelId, streamId, stream, uid)
		}
	}

	return nil
}
func (m *StreamManager) reconnectStream(ctx context.Context, streamType, uid, channelId uint32, streamId string) (isSuccess bool) {
	isSuc := false
	// 指数退避重试逻辑
	backoff := time.Second
	for i := 0; i < 3; i++ {
		if err := m.CreateStream(ctx, streamType, streamId, channelId, uid); err == nil {
			log.Infof("reconnect stream %s err:after retries:%d times", streamId, i+1)
			isSuc = true
			break
		}
		time.Sleep(backoff)
		backoff *= 2
	}

	if isSuc {
		streamName := GetStreamName(uid, channelId)
		m.LoginRoom(ctx, streamId, strconv.FormatUint(uint64(uid), 10), streamName)
	}
	log.Infof("reconnect stream fail %s err:after retries", streamId)
	return false
}

func (m *StreamManager) DeleteStreamId(ctx context.Context, streamId string) {
	if streamInfo, ok := m.streams.Load(streamId); ok {
		log.Infof("CreateStream Stream:%s already exists", streamId)
		streamInfo.(*StreamClients).closeChan()
		m.streams.Delete(streamId) // 删除指定的 Stream
	} else {
		log.ErrorWithCtx(ctx, "DeleteStreamId StreamId:%s err: not exist", streamId)
	}
}

func (m *StreamManager) Close() {
	// 关闭所有 Stream
	m.streams.Range(func(key, value interface{}) bool {
		value.(*StreamClients).closeChan()
		m.streams.Delete(key)
		return true
	})

}

func (m *StreamManager) startPullStream(ctx context.Context, streamId string) error {
	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamId})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, streamId: %s", err, streamId)
		return err
	}

	streamClis := m.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "startPullStream streamClis is nil, streamId: %s", streamId)
		return nil
	}
	err = streamClis.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_StartPullStream, Json: string(jsonRaw)})
	if err != nil {
		log.ErrorWithCtx(ctx, "startPullStream zegoStreamCli.Send err: %v, streamId: %s", err, streamId)
		return err
	}

	return nil
}

func (m *StreamManager) stopPullStream(ctx context.Context, streamId string) error {
	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamId})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, streamId: %s", err, streamId)
		return err
	}
	streamClis := m.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s", streamId)
		return nil
	}
	err = streamClis.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_StopPullStream, Json: string(jsonRaw)})
	if err != nil {
		log.ErrorWithCtx(ctx, "stopPullStream zegoStreamCli.Send err: %v, streamId: %s", err, streamId)
		return err
	}

	return nil
}

func (m *StreamManager) sendAigcStream(ctx context.Context, streamId string, data []byte) error {
	streamClis := m.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s", streamId)
		return nil
	}
	err := streamClis.aigcStreamCli.Send(&pb.RecvAudioStreamInRoomReq{UserAudio: data})
	if err != nil {
		log.ErrorWithCtx(ctx, "sendAigcStream error: %v, streamId: %s", err, streamId)
		return err
	}

	return nil
}

func (m *StreamManager) SendAudioData(ctx context.Context, streamId string, data []byte) error {
	streamClis := m.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s", streamId)
		return nil
	}
	err := streamClis.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Audio, Data: data})
	if err != nil {
		log.ErrorWithCtx(ctx, "sendAudioData error: %v, streamId: %s", err, streamId)
		return err
	}
	return nil
}

func (m *StreamManager) LoginRoom(ctx context.Context, streamId, userId, streamName string) error {
	jsonRaw, err := json.Marshal(map[string]any{"app_id": m.zegoAppId, "room_id": streamId, "user_id": userId, "stream_id": streamName})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, appId: %s,userId: %s, streamId: %s", err, m.zegoAppId, userId, streamId)
		return err
	}

	streamClis := m.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s", streamId)
		return nil
	}

	err = streamClis.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Login, Json: string(jsonRaw)})
	if err != nil {
		log.ErrorWithCtx(ctx, "loginRoom zegoStreamCli.Send err, streamId: %s", err, streamId)
		return err
	}
	return nil
}
