package mgr

import (
	"context"
	"fmt"
	accountGo "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"os"
	"strconv"
	"time"
)

func NewEnterRoom(streamMgr *StreamManager, accountCli *accountGo.Client, aigcAccount aigc_account.AigcAccountClient, streamRecord *StreamRecord, appId string, zegoHandle *ZegoHandleT) *EnterRoom {
	return &EnterRoom{
		streamMgr:    streamMgr,
		accountCli:   accountCli,
		aigcAccount:  aigcAccount,
		appId:        appId,
		streamRecord: streamRecord,
		zegoHandle:   zegoHandle,
	}
}

type EnterRoom struct {
	accountCli   *accountGo.Client
	aigcAccount  aigc_account.AigcAccountClient
	streamMgr    *StreamManager
	streamRecord *StreamRecord
	appId        string
	zegoHandle   *ZegoHandleT
}

func (z *EnterRoom) isAiAccount(ctx context.Context, uid uint32) (bool, error) {
	userInfo, err := z.accountCli.GetUserByUid(ctx, uid)
	if err != nil {
		return false, err
	}

	if accountPB.USER_TYPE(userInfo.GetUserType()) != accountPB.USER_TYPE_USER_TYPE_ROBOT {
		return false, nil
	}

	aiAccountRsp, accountErr := z.aigcAccount.GetAIAccount(ctx, &aigc_account.GetAIAccountRequest{
		Uid: uid,
	})

	if accountErr != nil {
		log.ErrorWithCtx(ctx, "Login aigcAccount.GetAIAccount err:%v, uid:%d", err, uid)
		return false, accountErr
	}
	if aiAccountRsp.GetAccount() == nil || aiAccountRsp.GetAccount().GetIsUnregister() {
		// AI账号不存在或已注销
		log.InfoWithCtx(ctx, "aiAccount not found or unregister, uid:%d", uid)
		return false, nil
	}
	return true, nil
}

func (z *EnterRoom) getStreamName(uid, channelId uint32) string {
	return fmt.Sprintf("server-%d-Baigcroom-C%d-%d", uid, channelId, time.Now().UnixMilli())
}

func (z *EnterRoom) Login(ctx context.Context, channelId, uid uint32) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	isAiAccount, err := z.isAiAccount(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Login isAiAccount err: %v, channelId:%d, uid:%d", err, channelId, uid)
		return err
	}

	if !isAiAccount {
		return nil
	}

	streamId := strconv.FormatInt(int64(channelId), 10)
	err = z.streamMgr.CreateStream(ctx, AigcStreamType, streamId, uid)
	if err != nil {
		return err
	}
	err = z.streamMgr.CreateStream(ctx, ZegoStreamType, streamId, uid)
	if err != nil {
		return err
	}

	streamName := z.getStreamName(uid, channelId)
	err = z.zegoHandle.loginRoom(ctx, z.appId, strconv.FormatUint(uint64(channelId), 10), strconv.FormatUint(uint64(uid), 10), streamId, streamName)
	return err
}

func (z *EnterRoom) Logout(ctx context.Context, channelId, uid uint32) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	isAiAccount, err := z.isAiAccount(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Login isAiAccount err: %v, channelId:%d, uid:%d", err, channelId, uid)
		return err
	}

	if !isAiAccount {
		return nil
	}

	err = z.streamRecord.UpdateStreamByExitRoom(ctx, channelId)
	return err
}

func GetPodName() string {
	name := os.Getenv("MY_POD_NAME")
	return name
}

func (z *EnterRoom) DeleteStreamByExitRoom(ctx context.Context) error {
	podName := GetPodName()
	if podName == "" {
		log.Warnf("NewGaugeCounter: pod name is empty")
	}
	roomRecords, err := z.streamRecord.GetStreamItemsLimitByExitRoom(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteStreamByExitRoom GetStreamItemsLimitByExitRoom err: %v", err)
		return err
	}
	for _, record := range roomRecords {
		if podName == record.PodName {
			z.streamMgr.DeleteStreamId(ctx, strconv.FormatUint(uint64(record.ChannelId), 10))
		}
	}
	return nil
}
