package mgr

import (
	"context"
	accountGo "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/db/entity"
	"os"
	"strconv"
	"time"
)

func NewEnterRoom(streamMgr *StreamManager, accountCli *accountGo.Client, aigcAccount aigc_account.AigcAccountClient, streamRecord *StreamRecord) *EnterRoom {
	return &EnterRoom{
		streamMgr:    streamMgr,
		accountCli:   accountCli,
		aigcAccount:  aigcAccount,
		streamRecord: streamRecord,
	}
}

type EnterRoom struct {
	accountCli   *accountGo.Client
	aigcAccount  aigc_account.AigcAccountClient
	streamMgr    *StreamManager
	streamRecord *StreamRecord
}

func (z *EnterRoom) isAiAccount(ctx context.Context, uid uint32) (bool, error) {
	userInfo, err := z.accountCli.GetUserByUid(ctx, uid)
	if err != nil {
		return false, err
	}

	if accountPB.USER_TYPE(userInfo.GetUserType()) != accountPB.USER_TYPE_USER_TYPE_ROBOT {
		return false, nil
	}

	aiAccountRsp, accountErr := z.aigcAccount.GetAIAccount(ctx, &aigc_account.GetAIAccountRequest{
		Uid: uid,
	})

	if accountErr != nil {
		log.ErrorWithCtx(ctx, "Login aigcAccount.GetAIAccount err:%v, uid:%d", accountErr, uid)
		return false, accountErr
	}
	if aiAccountRsp.GetAccount() == nil || aiAccountRsp.GetAccount().GetIsUnregister() {
		// AI账号不存在或已注销
		log.InfoWithCtx(ctx, "aiAccount not found or unregister, uid:%d", uid)
		return false, nil
	}
	return true, nil
}

func (z *EnterRoom) Login(ctx context.Context, channelId, uid uint32) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	isAiAccount, err := z.isAiAccount(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Login isAiAccount err: %v, channelId:%d, uid:%d", err, channelId, uid)
		return err
	}

	if !isAiAccount {
		return nil
	}

	streamId := strconv.FormatInt(int64(channelId), 10)
	err = z.streamMgr.CreateStream(ctx, entity.AigcStreamType, streamId, uid, channelId)
	if err != nil {
		return err
	}
	err = z.streamMgr.CreateStream(ctx, entity.ZegoStreamType, streamId, uid, channelId)
	if err != nil {
		return err
	}

	streamName := GetStreamName(uid, channelId)
	err = z.streamMgr.LoginRoom(ctx, streamId, strconv.FormatUint(uint64(uid), 10), streamName)
	if err != nil {
		log.ErrorWithCtx(ctx, "Login loginRoom err: %v, channelId:%d, uid:%d", err, channelId, uid)
		return err
	}

	err = z.streamRecord.UpsertStreamItem(ctx, &entity.StreamItem{
		StreamId:         streamId,
		PodName:          GetPodName(),
		ZegoStreamStatus: entity.ZegoStreamStatusOk,
		AigcStreamStatus: entity.AigcStreamStatusOK,
		IsExitRoom:       false,
		CreateTime:       time.Now(),
		UpdateTime:       time.Now(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Login UpsertStreamItem err: %v, channelId:%d, uid:%d", err, channelId, uid)
		return err
	}
	return nil
}

func (z *EnterRoom) Logout(ctx context.Context, channelId, uid uint32) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	isAiAccount, err := z.isAiAccount(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Login isAiAccount err: %v, channelId:%d, uid:%d", err, channelId, uid)
		return err
	}

	if !isAiAccount {
		return nil
	}
	streamId := strconv.FormatInt(int64(channelId), 10)
	err = z.streamRecord.UpdateStreamByExitRoom(ctx, streamId)
	return err
}

func GetPodName() string {
	name := os.Getenv("MY_POD_NAME")
	return name
}

func (z *EnterRoom) DeleteStreamByExitRoom(ctx context.Context) error {
	podName := GetPodName()
	if podName == "" {
		log.Warnf("NewGaugeCounter: pod name is empty")
	}
	roomRecords, err := z.streamRecord.GetStreamItemsLimitByExitRoom(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteStreamByExitRoom GetStreamItemsLimitByExitRoom err: %v", err)
		return err
	}
	for _, record := range roomRecords {
		if podName == record.PodName {
			z.streamMgr.DeleteStreamId(ctx, record.StreamId)
		}
	}
	return nil
}
