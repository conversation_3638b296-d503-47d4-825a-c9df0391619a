package mgr

import (
	"context"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/db/entity"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/db/mongo"
)

type StreamRecord struct {
	mDao *mongo.MongoDao
}

func NewStreamRecord(mDao *mongo.MongoDao) *StreamRecord {
	return &StreamRecord{
		mDao: mDao,
	}
}

func (s *StreamRecord) UpdateStreamByExitRoom(ctx context.Context, streamId string) error {
	return s.mDao.UpdateStreamByExitRoom(ctx, streamId)
}

func (s *StreamRecord) GetStreamItemsLimitByExitRoom(ctx context.Context) ([]*entity.StreamItem, error) {
	return s.mDao.GetStreamItemsLimitByExitRoom(ctx)
}

func (s *StreamRecord) UpsertStreamItem(ctx context.Context, item *entity.StreamItem) error {
	return s.mDao.UpsertStreamItem(ctx, item)
}

func (s *StreamRecord) HeartbeatFail(ctx context.Context, streamId string, streamType uint32) error {
	return s.mDao.HeartbeatFail(ctx, streamId, streamType)
}
