package mgr

import (
	"context"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/db/entity"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/db/mongo"
)

type StreamRecord struct {
	mDao *mongo.MongoDao
}

func NewStreamRecord(mDao *mongo.MongoDao) *StreamRecord {
	return &StreamRecord{
		mDao: mDao,
	}
}

func (s *StreamRecord) UpdateStreamByExitRoom(ctx context.Context, channelId uint32) error {
	return s.mDao.UpdateStreamByExitRoom(ctx, channelId)
}

func (s *StreamRecord) GetStreamItemsLimitByExitRoom(ctx context.Context) ([]*entity.StreamItem, error) {
	return s.mDao.GetStreamItemsLimitByExitRoom(ctx)
}
