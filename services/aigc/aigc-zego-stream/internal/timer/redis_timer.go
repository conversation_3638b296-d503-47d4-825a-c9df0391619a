package timer

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/pkg/timer"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/mgr"
	tt_config "golang.52tt.com/services/aigc/aigc-zego-stream/internal/ttconfig"
	"os"
	"time"
)

func NewRedisTimer(streamManager *mgr.StreamManager, enterRoomMgr *mgr.EnterRoom) *RedisTimer {
	data, err := os.ReadFile("img/test.wav")
	if err != nil {
		log.Errorf("ReadFile err:%v", err)
		return nil
	}
	demoAudioData := data[44:]
	log.Infof("test file len:%d", len(demoAudioData))
	//var demoAudioData []byte
	return &RedisTimer{
		streamManager: streamManager,
		demoAudioData: demoAudioData,
		enterRoomMgr:  enterRoomMgr,
	}
}

type RedisTimer struct {
	timerD        *timer.Timer
	demoAudioData []byte
	streamManager *mgr.StreamManager
	enterRoomMgr  *mgr.EnterRoom
}

func (t *RedisTimer) Start() error {

	ctx, cancelFunc := context.WithCancel(context.Background())
	defer cancelFunc()

	timerD, err := timer.NewTimerD(ctx, "aigc-zego-stream")
	if err != nil {
		log.Errorf("NewTimerD err:%v", err)
		return err
	}
	t.timerD = timerD

	every30Sec := "@every 10s"
	err = timerD.AddTask(every30Sec, "HandleTimeOutStream", timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.HandleTimeOutStream(ctx)
	}))
	if err != nil {
		log.Errorf("redis timer DeleteStreamByExitRoom start err:%v", err)
		return err
	}

	timerD.Start()
	return nil
}

func (t *RedisTimer) Stop() {
	t.timerD.Stop()
}

func (t *RedisTimer) HandleTimeOutStream(ctx context.Context) error {
	//TODO timeout的连接放kafka
	return nil
}

func (t *RedisTimer) TestSend(ctx context.Context) error {
	_ = t.streamManager.SendAudioData(ctx, tt_config.GetAigcZegoStreamConfig().GetTestStreamId(), t.demoAudioData)
	return nil
}

func (t *RedisTimer) TestTimer() error {
	go func() {
		ticker := time.NewTicker(time.Second * 10)
		defer func() {
			ticker.Stop()
		}()

		for range ticker.C {
			ctx, cancelFunc := context.WithCancel(context.Background())
			_ = t.enterRoomMgr.DeleteStreamByExitRoom(ctx)
			_ = t.TestSend(ctx)
			cancelFunc()
		}

	}()
	return nil
}
