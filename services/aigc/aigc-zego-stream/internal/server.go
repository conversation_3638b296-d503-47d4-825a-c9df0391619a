package internal

import (
	"context"
	middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"
	"gitlab.ttyuyin.com/tyr/x/log"
	account_go "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/config"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	pb "golang.52tt.com/protocol/services/aigc/aigc-zego-stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/db/mongo"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/event"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/event/eventlink"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/timer"
	tt_config "golang.52tt.com/services/aigc/aigc-zego-stream/internal/ttconfig"
)

type StartConfig struct {
	// from config file
	ZegoAppId          string              `json:"zego_app_id"`
	MongoConf          *config.MongoConfig `json:"mongo"`

	EventLink *middleware_event.Options `json:"event_link"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	err := tt_config.InitAigcZegoStreamConfig()
	if err != nil {
		log.Errorf("InitAigcZegoStreamConfig err: %v", err)
		return nil, err
	}
	streamMgr, err := mgr.NewStreamManager(ctx)
	if err != nil {
		log.Errorf("mgr.NewStreamManager err: %v", err)
		return nil, err
	}

	accountCli, err := account_go.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "account_go.NewClient err: %v", err)
		return nil, err
	}

	aigcAccount, err := aigc_account.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "aigc_account.NewClient err: %v", err)
		return nil, err
	}

	mongoDao, err := mongo.NewMongoDao(ctx, cfg.MongoConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "mongo.NewMongoDao err: %v", err)
		return nil, err
	}

	streamRcord := mgr.NewStreamRecord(mongoDao)

	zegoHandle := mgr.NewZegoHandle(streamMgr, streamRcord)

	enterRoomMgr := mgr.NewEnterRoom(streamMgr, accountCli, aigcAccount, streamRcord, cfg.ZegoAppId, zegoHandle)

	eventBus, err := eventlink.NewEventBus(ctx, cfg.EventLink)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewEventBus cfg(%+v) err: %v", cfg, err)
		return nil, err
	}
	kafkaSub, err := event.NewSimpleMicEventSubscriber(ctx, eventBus, enterRoomMgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewSimpleMicEventSubscriber err %s ", err.Error())
		return nil, err
	}

	timerInst := timer.NewRedisTimer(zegoHandle, enterRoomMgr)
	err = timerInst.Start()
	if err != nil {
		log.ErrorWithCtx(ctx, "timer.NewRedisTimer err %s ", err.Error())
		return nil, err
	}
	timerInst.TestTimer()

	s := &Server{
		streamMgr: streamMgr,
		roomSub:   kafkaSub,
	}

	return s, nil
}

type Server struct {
	streamMgr *mgr.StreamManager
	roomSub   *event.SimpleMicEventSubscriber
	eventBus  event.EventBus
}

func (s *Server) Login(ctx context.Context, req *pb.LoginReq) (*pb.LoginResp, error) {
	resp := &pb.LoginResp{}
	log.InfoWithCtx(ctx, "Login LoginReq: %s, loginResp:%s", req.String(), resp.String())
	return resp, nil
}

func (s *Server) ShutDown() {
	s.streamMgr.Close()
	if s.eventBus != nil {
		s.eventBus.Close()
	}
}
