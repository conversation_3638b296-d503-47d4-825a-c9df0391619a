package entity

import "time"

type StreamItem struct {
	Id                 string    `bson:"_id"`
	PodName            string    `bson:"pod_name,omitempty"`
	ChannelId          uint32    `bson:"channel_id,omitempty"`
	ZegoPodName        string    `bson:"zego_pod_name,omitempty"`
	ZegoStreamStatus   uint32    `bson:"zego_stream_status,omitempty"`
	ZegoStreamFailTime int64     `bson:"zego_stream_fail_time,omitempty"`
	AigcPodName        string    `bson:"aigc_pod_name,omitempty"`
	AigcStreamStatus   uint32    `bson:"aigc_stream_status,omitempty"`
	AigcStreamFailTime int64     `bson:"aigc_stream_fail_time,omitempty"`
	IsExitRoom         bool      `bson:"is_exit_room,omitempty"` // 是否退出房间
	CreateTime         time.Time `bson:"create_time,omitempty"`
	UpdateTime         time.Time `bson:"update_time,omitempty"`
}
