package entity

import "time"

const (
	ZegoStreamStatusOk      = 1 // zego流状态正常
	ZegoStreamStatusFail    = 2 // zego流状态异常
	ZegoStreamStatusWaiting = 3 // zego流状态等待中
	AigcStreamStatusOK      = 1 // aigc流状态正常
	AigcStreamStatusFail    = 2 // aigc流状态异常

	AigcStreamType = 0
	ZegoStreamType = 1
)

type StreamItem struct {
	//Id                 string    `bson:"_id"`
	StreamId           string    `bson:"_id,omitempty"`
	PodName            string    `bson:"pod_name,omitempty"`
	ZegoPodName        string    `bson:"zego_pod_name,omitempty"`
	ZegoStreamStatus   uint32    `bson:"zego_stream_status,omitempty"`
	ZegoStreamFailTime int64     `bson:"zego_stream_fail_time,omitempty"`
	AigcPodName        string    `bson:"aigc_pod_name,omitempty"`
	AigcStreamStatus   uint32    `bson:"aigc_stream_status,omitempty"`
	AigcStreamFailTime int64     `bson:"aigc_stream_fail_time,omitempty"`
	IsExitRoom         bool      `bson:"is_exit_room,omitempty"` // 是否退出房间
	CreateTime         time.Time `bson:"create_time,omitempty"`
	UpdateTime         time.Time `bson:"update_time,omitempty"`
}
