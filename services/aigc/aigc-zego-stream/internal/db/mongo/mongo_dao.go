package mongo

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/db/entity"
	"time"
)

type MongoDao struct {
	mongoCli       *mongo.Client
	streamInfoPool *mongo.Collection
}

func NewMongoDao(ctx context.Context, cfg *config.MongoConfig) (*MongoDao, error) {
	//client := &mongo.Client{}
	client, err := mongo.NewClient(cfg.OptionsForPrimaryPreferred())
	if err != nil {
		log.ErrorWithCtx(ctx, "NewMongoDao NewClient err: (%v)", err)
		return nil, err
	}

	if err = client.Connect(ctx); err != nil {
		log.ErrorWithCtx(ctx, "newServer Connect err: (%v)", err)
		return nil, err
	}

	if err = client.Ping(ctx, readpref.Primary()); err != nil {
		log.ErrorWithCtx(ctx, "newServer Ping err: (%v)", err)
		return nil, err
	}

	streamInfoPool := client.Database(cfg.Database).Collection("streaminfo")

	return &MongoDao{
		mongoCli:       client,
		streamInfoPool: streamInfoPool,
	}, nil
}

func (m *MongoDao) Close() error {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	err := m.mongoCli.Disconnect(ctx)
	cancel()
	if err != nil {
		log.Errorf("mongoCli.Disconnect err:%v", err)
		return err
	}
	return nil
}

func (m *MongoDao) CreateIndexes() error {
	var err error
	opts := options.CreateIndexes().SetMaxTime(10 * time.Second)
	_, err = m.streamInfoPool.Indexes().CreateMany(
		context.Background(),
		[]mongo.IndexModel{
			{
				Keys:    bson.D{primitive.E{Key: "channelid", Value: 1}},
				Options: options.Index().SetUnique(true),
			}, {
				Keys:    bson.D{primitive.E{Key: "zego_stream_status", Value: 1}},
				Options: options.Index().SetUnique(true),
			}, {
				Keys:    bson.D{primitive.E{Key: "is_exit_room", Value: 1}},
				Options: options.Index().SetUnique(true),
			},
		},
		opts,
	)
	if err != nil {
		log.Errorf("createIndexs channelBoxPool err:%v", err)
	}

	return nil
}

const (
	zego_stream_status_ok      = 1 // zego流状态正常
	zego_stream_status_fail    = 2 // zego流状态异常
	zego_stream_status_waiting = 3 // zego流状态等待中
	aigc_stream_status_ok      = 1 // aigc流状态正常
	aigc_stream_status_fail    = 2 // aigc流状态异常
)

func (m *MongoDao) GetStreamItemsLimitByExitRoom(ctx context.Context) ([]*entity.StreamItem, error) {
	var filter = bson.M{"is_exit_room": true}

	cursor, err := m.streamInfoPool.Find(ctx, filter, options.Find().SetLimit(50))
	if err != nil {
		log.ErrorWithCtx(ctx, "Getentity.StreamItemsLimit Find err:%v", err)
		return nil, err
	}
	items := make([]*entity.StreamItem, 0)
	for cursor.Next(ctx) {
		info := &entity.StreamItem{}
		if err := cursor.Decode(info); err != nil {
			log.ErrorWithCtx(ctx, "Getentity.StreamItemsLimit cursor.Decode err:%v", err)
			return items, err
		}
		items = append(items, info)
	}
	if err := cursor.Err(); err != nil {
		log.ErrorWithCtx(ctx, "Getentity.StreamItemsLimit cursor err:%v", err)
		return items, err
	}

	if err := cursor.Close(ctx); err != nil {
		log.ErrorWithCtx(ctx, "Getentity.StreamItemsLimit cursor.Close err:%v", err)
	}
	return items, nil
}

func (m *MongoDao) UpdateStreamByExitRoom(ctx context.Context, channelId uint32) error {
	updates := bson.M{"is_exit_room": true, "update_time": time.Now()}

	res := m.streamInfoPool.FindOneAndUpdate(ctx, bson.M{"channel_id": channelId, "is_exit_room": false}, bson.M{"$set": updates},
		options.FindOneAndUpdate().SetReturnDocument(options.Before))
	if err := res.Err(); err != nil {
		log.ErrorWithCtx(ctx, "UpdateStreamByExitRoom streamInfoPool.FindOneAndUpdate err: %v, channelId:%d", err, channelId)
		return err
	}

	/*var bi entity.StreamItem
	err := res.Decode(&bi)
	if err != nil {
		log.ErrorWithCtx(ctx, "Mongo.UpdateBox res.Decode err: %v", err)
		return nil, err
	}*/

	log.DebugWithCtx(ctx, "MongoDao.UpdateBoxInfo result: %v", res)
	return nil
}

func (m *MongoDao) UpdateStreamByChannelIds(ctx context.Context, channelIds []uint32, zegoStatus uint32) (*entity.StreamItem, error) {
	var updates = bson.M{"zego_stream_status": zegoStatus}

	res := m.streamInfoPool.FindOneAndUpdate(ctx, bson.M{"channel_id": bson.M{"$in": channelIds}}, bson.M{"$set": updates},
		options.FindOneAndUpdate().SetReturnDocument(options.Before))
	if err := res.Err(); err != nil {
		log.ErrorWithCtx(ctx, "Mongo.UpdateBox boxPool.FindOneAndUpdate err: %v, channelIds:%v", err, channelIds)
		return nil, err
	}

	var bi entity.StreamItem
	err := res.Decode(&bi)
	if err != nil {
		log.ErrorWithCtx(ctx, "Mongo.UpdateBox res.Decode err: %v", err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "MongoDao.UpdateBoxInfo result: %v", res)
	return &bi, nil
}

func (m *MongoDao) GetStreamItemsLimit(ctx context.Context, zegoStreamStatus uint32) ([]*entity.StreamItem, error) {
	var filter = bson.M{"zego_stream_status": zegoStreamStatus}

	cursor, err := m.streamInfoPool.Find(ctx, filter, options.Find().SetLimit(20))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetStreamItemsLimit Find err:%v", err)
		return nil, err
	}
	items := make([]*entity.StreamItem, 0)
	for cursor.Next(ctx) {
		info := &entity.StreamItem{}
		if err := cursor.Decode(info); err != nil {
			log.ErrorWithCtx(ctx, "GetStreamItemsLimit cursor.Decode err:%v", err)
			return items, err
		}
		items = append(items, info)
	}
	if err := cursor.Err(); err != nil {
		log.ErrorWithCtx(ctx, "GetStreamItemsLimit cursor err:%v", err)
		return items, err
	}

	if err := cursor.Close(ctx); err != nil {
		log.ErrorWithCtx(ctx, "GetStreamItemsLimit cursor.Close err:%v", err)
	}
	return items, nil
}

func (m *MongoDao) AddStream(ctx context.Context, item *entity.StreamItem) error {
	res, err := m.streamInfoPool.InsertOne(ctx, item)
	if err != nil {
		log.ErrorWithCtx(ctx, "MongoDao.AddStream (%v) err: %v", item, err)
		return err
	}

	log.DebugWithCtx(ctx, "MongoDao.AddStream result: %v", res)
	return nil
}

func (m *MongoDao) UpdateStream(ctx context.Context, item *entity.StreamItem) (*entity.StreamItem, error) {
	updates := bson.M{"pod_name": item.PodName, "zego_pod_name": item.ZegoPodName, "zego_stream_status": item.ZegoStreamStatus,
		"zego_stream_fail_time": item.ZegoStreamFailTime, "aigc_pod_name": item.AigcPodName, "aigc_stream_status": item.AigcStreamStatus,
		"aigc_stream_fail_time": item.AigcStreamFailTime, "is_exit_room": item.IsExitRoom, "update_time": time.Now()}

	res := m.streamInfoPool.FindOneAndUpdate(ctx, bson.M{"channel_id": item.ChannelId}, bson.M{"$set": updates},
		options.FindOneAndUpdate().SetReturnDocument(options.Before))
	if err := res.Err(); err != nil {
		log.ErrorWithCtx(ctx, "Mongo.UpdateBox boxPool.FindOneAndUpdate item(%v) err: %v", item, err)
		return nil, err
	}

	var bi entity.StreamItem
	err := res.Decode(&bi)
	if err != nil {
		log.ErrorWithCtx(ctx, "Mongo.UpdateBox res.Decode err: %v", err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "MongoDao.UpdateBoxInfo result: %v", res)

	return &bi, nil
}
