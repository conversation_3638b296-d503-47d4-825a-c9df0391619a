package tt_config

import (
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"golang.52tt.com/pkg/log"
	"sync/atomic"
)

type AigcZegoStreamConfig struct {
	TestStreamId string `json:"test_stream_id"`
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (g *AigcZegoStreamConfig) Format() error {
	return nil
}

var (
	atomicInitGameUserRateConfig *atomic.Value
)

func init() {
	//if err := InitGameUserRateConfig(); err != nil {
	//    panic(err)
	//}
}

// 可以选择外部初始化或者直接init函数初始化
func InitAigcZegoStreamConfig() error {
	cfg := &AigcZegoStreamConfig{}
	atomCfg, err := ttconfig.AtomLoad("game-user-rate", cfg)
	if nil != err {
		return err
	}
	atomicInitGameUserRateConfig = atomCfg
	return nil
}

var defaultRateConfig = &AigcZegoStreamConfig{}

func GetAigcZegoStreamConfig() *AigcZegoStreamConfig {
	if atomicInitGameUserRateConfig == nil {
		log.Errorf("GameUserRateConfig is nil")
		return defaultRateConfig
	}
	return atomicInitGameUserRateConfig.Load().(*AigcZegoStreamConfig)
}

func (g *AigcZegoStreamConfig) GetTestStreamId() string {
	if g == nil || g.TestStreamId == "" {
		return ""
	}
	return g.TestStreamId
}
