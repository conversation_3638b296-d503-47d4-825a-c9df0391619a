package event

import (
	"context"
	"fmt"
	"strings"

	middleware_subscriber "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
)

type SubName string
type PubEvent string

const (
	SubNameTTSimpleMicEv        SubName = "tt_simple_mic_ev"
)

//go:generate mockgen -destination=mocks/event_bus.go -package=mocks golang.52tt.com/services/aigc/aigc-zego-stream/internal/event EventBus
type EventBus interface {
	Close()

	Subscribe(ctx context.Context, name SubName, process middleware_subscriber.ProcessorContextFunc) error

	Publish(ctx context.Context, e PubEvent, key string, msg any) error
}

func SplitPubEvent(e PubEvent) (name, topic string, err error) {
	ss := strings.Split(string(e), ":")
	if len(ss) != 2 {
		return "", "", fmt.Errorf("invalid event %s", e)
	}

	return ss[0], ss[1], nil
}
