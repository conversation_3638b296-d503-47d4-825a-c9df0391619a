package event

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	context_info "golang.52tt.com/pkg/context-info"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/mgr"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"

	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkasimplemic"
)

type SimpleMicEventSubscriber struct {
	eventBus     EventBus
	enterRoomMgr *mgr.EnterRoom
}

func NewSimpleMicEventSubscriber(ctx context.Context, eventBus EventBus, enterRoomMgr *mgr.EnterRoom) (*SimpleMicEventSubscriber, error) {

	sub := &SimpleMicEventSubscriber{
		enterRoomMgr: enterRoomMgr,
		eventBus:     eventBus,
	}

	err := eventBus.Subscribe(ctx, SubNameTTSimpleMicEv, sub.handlerMicEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewSimpleMicEventSubscriber Subscribe err: %v", err)
		return nil, err
	}
	return sub, nil
}

func (s *SimpleMicEventSubscriber) handlerMicEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	ctx = context_info.GenReqId(ctx)
	simpleMicEvent := &kafkasimplemic.SimpleMicEvent{}
	err := proto.Unmarshal(msg.Value, simpleMicEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerMicEvent Failed to proto.Unmarshal err:%+v", err)
		return err, false
	}

	log.DebugWithCtx(ctx, "handlerMicEvent:%v, pid:%d, offset:%d", simpleMicEvent.String(), msg.Partition, msg.Offset)

	if simpleMicEvent.GetChannelType() != uint32(channel.ChannelType_USER_CHANNEL_TYPE) {
		log.InfoWithCtx(ctx, "handlerMicEvent unsport channeltype %d", simpleMicEvent.GetChannelType())
		return nil, false
	}

	err = s.handlerSimpleMicEventMsg(simpleMicEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerSimpleMicEventMsg err:%v, simpleMicEvent:%s", err, simpleMicEvent.String())
		return err, false
	}
	return nil, false
}

func (s *SimpleMicEventSubscriber) handlerSimpleMicEventMsg(simpleMicEvent *kafkasimplemic.SimpleMicEvent) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	ctx = context_info.GenReqId(ctx)
	log.InfoWithCtx(ctx, "handlerSimpleMicEventMsg:%v", simpleMicEvent.String())

	if simpleMicEvent.GetEventType() == uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_HOLD) {
		err := s.enterRoomMgr.Login(ctx, simpleMicEvent.GetChId(), simpleMicEvent.GetMicUserId())
		if err != nil {
			log.ErrorWithCtx(ctx, "handlerSimpleMicEventMsg Login err:%v, simpleMicEvent:%+v", err, simpleMicEvent)
			return err
		}
		return nil
	} else if simpleMicEvent.GetEventType() == uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_RELEASE) {
		err := s.enterRoomMgr.Logout(ctx, simpleMicEvent.GetChId(), simpleMicEvent.GetMicUserId())
		if err != nil {
			log.ErrorWithCtx(ctx, "handlerSimpleMicEventMsg Logout err:%v, simpleMicEvent:%+v", err, simpleMicEvent)
			return err
		}
		return nil
	}

	return nil
}
