// Code generated by ifacemaker; DO NOT EDIT.

package mgr

import (
	"context"

	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/partner/entity"
)

// IPartnerManager ...
type IPartnerManager interface {
	CreatePartner(ctx context.Context, partner *entity.Partner) error
	TryCreatePartner(ctx context.Context, partner *entity.Partner) error
	UpdatePartner(ctx context.Context, partner *entity.Partner) error
	ChangePartnerRole(ctx context.Context, partner *entity.Partner) error
	GetPartner(ctx context.Context, id uint32) (*entity.Partner, error)
	GetUserPartnerByRoleID(ctx context.Context, uid, roleId uint32) (*entity.Partner, error)
	GetUserPartnerByRoleType(ctx context.Context, uid uint32, roleType pb.AIRoleType) (*entity.Partner, error)
	GetUserPartnerList(ctx context.Context, uid uint32, roleTypes []pb.AIRoleType) ([]*entity.Partner, error)
	DeletePartner(ctx context.Context, id uint32) error
	// DeleteBindRolePartners 分批删除指定角色绑定的partner
	DeleteBindRolePartners(ctx context.Context, roleIdList ...uint32) error
	BatGetPartnerByIds(ctx context.Context, ids []uint32) ([]*entity.Partner, error)
}
