package partner

import (
	"context"

	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/aigc/aigc-soulmate/internal/config/ttconfig"
	"golang.52tt.com/services/aigc/aigc-soulmate/internal/event/pub"
	"golang.52tt.com/services/aigc/aigc-soulmate/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/partner/entity"
	"golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/partner/store"
	"golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/partner/store/mongo"
)

type ManagerOption func(context.Context, *Manager) error

//go:generate ifacemaker -f *.go -s Manager -p mgr -i IPartnerManager -o ../partner_mgr_api.go
//go:generate mockgen -destination=../mocks/partner_mgr.go -package=mocks -mock_names=IPartnerManager=MockIPartnerManager golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr IPartnerManager
type Manager struct {
	pub   pub.IEventPublisher
	store store.IStore
}

func NewManager(ctx context.Context, opts ...ManagerOption) (*Manager, error) {
	mgr := &Manager{}
	for _, opt := range opts {
		err := opt(ctx, mgr)
		if err != nil {
			return nil, err
		}
	}

	return mgr, nil
}

func WithStoreOption(database *db.MongoDB) ManagerOption {
	return func(ctx context.Context, mgr *Manager) error {
		var err error
		mgr.store, err = mongo.NewStore(ctx, database)
		return err
	}
}

func WithEventPublisherOption(pub pub.IEventPublisher) ManagerOption {
	return func(ctx context.Context, mgr *Manager) error {
		mgr.pub = pub
		return nil
	}
}

func (m *Manager) CreatePartner(ctx context.Context, partner *entity.Partner) error {
	err := m.TryCreatePartner(ctx, partner)
	if err != nil {
		return err
	}

	_, err = m.store.SavePartner(ctx, partner)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreatePartner SavePartner partner(%+v) err: %v", partner, err)
		return err
	}

	return nil
}

func (m *Manager) TryCreatePartner(ctx context.Context, partner *entity.Partner) error {
	uid := metainfo.GetServiceInfo(ctx).UserID()

	switch pb.AIPartnerSource(partner.Source) {
	case pb.AIPartnerSource_AIPartnerSourceUser, pb.AIPartnerSource_AIPartnerSourceDeRole:
		// 旧版本来源,只能有一个partner
		rp, err := m.store.GetPartner(ctx, store.NewQueryOption().WithUid(uid).WithRoleType(pb.AIRoleType_AIRoleTypePartner))
		if err != nil {
			log.ErrorWithCtx(ctx, "TryCreatePartner GetPartner uid(%d) err: %v", uid, err)
			return err
		}
		if rp != nil {
			log.WarnWithCtx(ctx, "TryCreatePartner user(%d) source(%d) partner already exists", uid, partner.Source)
			return protocol.NewExactServerError(nil, status.ErrChatBotAiPartnerExists)
		}
	case pb.AIPartnerSource_AIPartnerSourceMultiRole:
		// 多角色来源,限制创建数量
		num, err := m.store.CountPartner(ctx, store.NewQueryOption().WithUid(uid).WithRoleType(pb.AIRoleType_AIRoleTypePartner, pb.AIRoleType_AIRoleTypeGame))
		if err != nil {
			log.ErrorWithCtx(ctx, "TryCreatePartner CountPartner uid(%d) err: %v", uid, err)
			return err
		}

		limit := ttconfig.Config.GetAIGCSoulmateConfig().GetCreatePartnerLimit()
		if num >= limit {
			log.WarnWithCtx(ctx, "TryCreatePartner user(%d) partner num(%d) >= limit(%d)", uid, num, limit)
			return protocol.NewExactServerError(nil, status.ErrChatBotCreateAiPartnerLimit)
		}
	case pb.AIPartnerSource_AIPartnerSourcePet:
		// 桌宠来源，限制一个桌宠
		rp, err := m.store.GetPartner(ctx, store.NewQueryOption().WithUid(uid).WithRoleType(pb.AIRoleType_AIRoleTypePet))
		if err != nil {
			log.ErrorWithCtx(ctx, "TryCreatePartner GetPartner uid(%d) err: %v", uid, pb.AIRoleType_AIRoleTypePet, err)
			return err
		}
		if rp != nil {
			log.WarnWithCtx(ctx, "TryCreatePartner user(%d) source(%d) partner already exists", uid, partner.Source)
			return protocol.NewExactServerError(nil, status.ErrChatBotAiPartnerExists)
		}
	}

	if partner.ID == 0 {
		var err error
		partner.ID, err = m.store.NextPartnerID(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "TryCreatePartner NextPartnerID err: %v", err)
			return err
		}
	}

	return nil
}

func (m *Manager) UpdatePartner(ctx context.Context, partner *entity.Partner) error {
	_, err := m.store.SavePartner(ctx, partner)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdatePartner SavePartner partner(%+v) err: %v", partner, err)
		return err
	}

	return nil
}

func (m *Manager) ChangePartnerRole(ctx context.Context, partner *entity.Partner) error {
	rp, err := m.store.GetPartner(ctx, store.NewQueryOption().WithUid(partner.Uid).WithRoleId(partner.RoleId))
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangePartnerRole GetPartner uid(%d) roleId(%d) err: %v", partner.Uid, partner.RoleId, err)
		return err
	}

	// 要切换角色已存在,删除原来的,为了兼容多角色版本切回树洞版本
	if rp != nil {
		err = m.store.DeletePartner(ctx, rp.ID)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChangePartnerRole DeletePartner id(%d) err: %v", rp.ID, err)
			return err
		}

		err = m.pub.PublishSwitchRolePartnerDeleted(ctx, rp.Uid, rp.ID)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChangePartnerRole PublishSwitchRolePartnerDeleted id(%d) err: %v", rp.ID, err)
		}
	}

	_, err = m.store.SavePartner(ctx, partner)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangePartnerRole SavePartner partner(%+v) err: %v", partner, err)
		return err
	}

	return nil
}

func (m *Manager) GetPartner(ctx context.Context, id uint32) (*entity.Partner, error) {
	if id == 0 {
		return nil, nil
	}

	partner, err := m.store.GetPartner(ctx, store.NewQueryOption().WithID(id))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPartner GetPartner id(%d) err: %v", id, err)
		return nil, err
	}
	if partner == nil {
		return nil, nil
	}

	return partner, nil
}

func (m *Manager) GetUserPartnerByRoleID(ctx context.Context, uid, roleId uint32) (*entity.Partner, error) {
	if uid == 0 {
		return nil, nil
	}

	partner, err := m.store.GetPartner(ctx, store.NewQueryOption().WithUid(uid).WithRoleId(roleId))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserPartnerByRoleID GetPartner uid(%d) roleId(%d) err: %v", uid, roleId, err)
		return nil, err
	}
	if partner == nil {
		return nil, nil
	}

	return partner, nil
}

func (m *Manager) GetUserPartnerByRoleType(ctx context.Context, uid uint32, roleType pb.AIRoleType) (*entity.Partner, error) {
	if uid == 0 {
		return nil, nil
	}

	partner, err := m.store.GetPartner(ctx, store.NewQueryOption().WithUid(uid).WithRoleType(roleType))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserPartnerByRoleType GetPartner uid(%d) roleType(%d) err: %v", uid, roleType, err)
		return nil, err
	}
	if partner == nil {
		return nil, nil
	}

	return partner, nil
}

func (m *Manager) GetUserPartnerList(ctx context.Context, uid uint32, roleTypes []pb.AIRoleType) ([]*entity.Partner, error) {
	if uid == 0 {
		return nil, nil
	}

	partners, err := m.store.ListPartner(ctx, store.NewQueryOption().WithUid(uid).WithRoleType(roleTypes...))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserPartnerList uid(%d) err: %v", uid, err)
		return nil, err
	}
	if len(partners) == 0 {
		return nil, nil
	}

	return partners, nil
}

func (m *Manager) DeletePartner(ctx context.Context, id uint32) error {
	partner, err := m.store.GetPartner(ctx, store.NewQueryOption().WithID(id))
	if err != nil {
		log.ErrorWithCtx(ctx, "DeletePartner GetPartner id(%d) err: %v", id, err)
		return err
	}
	if partner == nil {
		log.WarnWithCtx(ctx, "DeletePartner partner %d not found", id)
		return nil
	}

	err = m.store.DeletePartner(ctx, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeletePartner DeletePartner id(%d) err: %+v", id, err)
		return err
	}

	err = m.pub.PublishPartnerDeleted(ctx, partner.Uid, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeletePartner PublishRoleDeleted id(%d) err: %v", id, err)
		return err
	}

	return nil
}

// DeleteBindRolePartners 分批删除指定角色绑定的partner
func (m *Manager) DeleteBindRolePartners(ctx context.Context, roleIdList ...uint32) error {
	const limit = 1000

	if len(roleIdList) == 0 {
		log.WarnWithCtx(ctx, "DeleteBindRolePartners roleIdList empty")
		return nil
	}

	var deleteNum int
	for {
		partners, err := m.store.ListPartnerIDByRoleID(ctx, roleIdList, limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "DeleteBindRolePartners ListPartnerIDByRoleID roleIdList(%+v) err: %v", roleIdList, err)
			return err
		}
		if len(partners) == 0 {
			break
		}

		err = m.store.DeletePartner(ctx, partners...)
		if err != nil {
			log.ErrorWithCtx(ctx, "DeleteBindRolePartners DeletePartnerByID err: %v", err)
			return err
		}

		deleteNum += len(partners)
		log.InfoWithCtx(ctx, "DeleteBindRolePartners partners: %+v", partners)

		if len(partners) < limit {
			break
		}
	}

	log.InfoWithCtx(ctx, "DeleteBindRolePartners roleIdList(%+v) deleteNum: %d", roleIdList, deleteNum)
	return nil
}

func (m *Manager) BatGetPartnerByIds(ctx context.Context, ids []uint32) ([]*entity.Partner, error) {
	partners, err := m.store.ListPartner(ctx, store.NewQueryOption().WithID(ids...))
	if err != nil {
		return nil, err
	}
	return partners, nil
}