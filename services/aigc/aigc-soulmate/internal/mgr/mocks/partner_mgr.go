// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr (interfaces: IPartnerManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	entity "golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/partner/entity"
)

// MockIPartnerManager is a mock of IPartnerManager interface.
type MockIPartnerManager struct {
	ctrl     *gomock.Controller
	recorder *MockIPartnerManagerMockRecorder
}

// MockIPartnerManagerMockRecorder is the mock recorder for MockIPartnerManager.
type MockIPartnerManagerMockRecorder struct {
	mock *MockIPartnerManager
}

// NewMockIPartnerManager creates a new mock instance.
func NewMockIPartnerManager(ctrl *gomock.Controller) *MockIPartnerManager {
	mock := &MockIPartnerManager{ctrl: ctrl}
	mock.recorder = &MockIPartnerManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPartnerManager) EXPECT() *MockIPartnerManagerMockRecorder {
	return m.recorder
}

// BatGetPartnerByIds mocks base method.
func (m *MockIPartnerManager) BatGetPartnerByIds(arg0 context.Context, arg1 []uint32) ([]*entity.Partner, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetPartnerByIds", arg0, arg1)
	ret0, _ := ret[0].([]*entity.Partner)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetPartnerByIds indicates an expected call of BatGetPartnerByIds.
func (mr *MockIPartnerManagerMockRecorder) BatGetPartnerByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetPartnerByIds", reflect.TypeOf((*MockIPartnerManager)(nil).BatGetPartnerByIds), arg0, arg1)
}

// ChangePartnerRole mocks base method.
func (m *MockIPartnerManager) ChangePartnerRole(arg0 context.Context, arg1 *entity.Partner) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangePartnerRole", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ChangePartnerRole indicates an expected call of ChangePartnerRole.
func (mr *MockIPartnerManagerMockRecorder) ChangePartnerRole(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangePartnerRole", reflect.TypeOf((*MockIPartnerManager)(nil).ChangePartnerRole), arg0, arg1)
}

// CreatePartner mocks base method.
func (m *MockIPartnerManager) CreatePartner(arg0 context.Context, arg1 *entity.Partner) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePartner", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePartner indicates an expected call of CreatePartner.
func (mr *MockIPartnerManagerMockRecorder) CreatePartner(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePartner", reflect.TypeOf((*MockIPartnerManager)(nil).CreatePartner), arg0, arg1)
}

// DeleteBindRolePartners mocks base method.
func (m *MockIPartnerManager) DeleteBindRolePartners(arg0 context.Context, arg1 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteBindRolePartners", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBindRolePartners indicates an expected call of DeleteBindRolePartners.
func (mr *MockIPartnerManagerMockRecorder) DeleteBindRolePartners(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBindRolePartners", reflect.TypeOf((*MockIPartnerManager)(nil).DeleteBindRolePartners), varargs...)
}

// DeletePartner mocks base method.
func (m *MockIPartnerManager) DeletePartner(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePartner", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePartner indicates an expected call of DeletePartner.
func (mr *MockIPartnerManagerMockRecorder) DeletePartner(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePartner", reflect.TypeOf((*MockIPartnerManager)(nil).DeletePartner), arg0, arg1)
}

// GetPartner mocks base method.
func (m *MockIPartnerManager) GetPartner(arg0 context.Context, arg1 uint32) (*entity.Partner, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPartner", arg0, arg1)
	ret0, _ := ret[0].(*entity.Partner)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPartner indicates an expected call of GetPartner.
func (mr *MockIPartnerManagerMockRecorder) GetPartner(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartner", reflect.TypeOf((*MockIPartnerManager)(nil).GetPartner), arg0, arg1)
}

// GetUserPartnerByRoleID mocks base method.
func (m *MockIPartnerManager) GetUserPartnerByRoleID(arg0 context.Context, arg1, arg2 uint32) (*entity.Partner, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPartnerByRoleID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*entity.Partner)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPartnerByRoleID indicates an expected call of GetUserPartnerByRoleID.
func (mr *MockIPartnerManagerMockRecorder) GetUserPartnerByRoleID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPartnerByRoleID", reflect.TypeOf((*MockIPartnerManager)(nil).GetUserPartnerByRoleID), arg0, arg1, arg2)
}

// GetUserPartnerByRoleType mocks base method.
func (m *MockIPartnerManager) GetUserPartnerByRoleType(arg0 context.Context, arg1 uint32, arg2 aigc_soulmate.AIRoleType) (*entity.Partner, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPartnerByRoleType", arg0, arg1, arg2)
	ret0, _ := ret[0].(*entity.Partner)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPartnerByRoleType indicates an expected call of GetUserPartnerByRoleType.
func (mr *MockIPartnerManagerMockRecorder) GetUserPartnerByRoleType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPartnerByRoleType", reflect.TypeOf((*MockIPartnerManager)(nil).GetUserPartnerByRoleType), arg0, arg1, arg2)
}

// GetUserPartnerList mocks base method.
func (m *MockIPartnerManager) GetUserPartnerList(arg0 context.Context, arg1 uint32, arg2 []aigc_soulmate.AIRoleType) ([]*entity.Partner, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPartnerList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*entity.Partner)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPartnerList indicates an expected call of GetUserPartnerList.
func (mr *MockIPartnerManagerMockRecorder) GetUserPartnerList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPartnerList", reflect.TypeOf((*MockIPartnerManager)(nil).GetUserPartnerList), arg0, arg1, arg2)
}

// TryCreatePartner mocks base method.
func (m *MockIPartnerManager) TryCreatePartner(arg0 context.Context, arg1 *entity.Partner) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TryCreatePartner", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// TryCreatePartner indicates an expected call of TryCreatePartner.
func (mr *MockIPartnerManagerMockRecorder) TryCreatePartner(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryCreatePartner", reflect.TypeOf((*MockIPartnerManager)(nil).TryCreatePartner), arg0, arg1)
}

// UpdatePartner mocks base method.
func (m *MockIPartnerManager) UpdatePartner(arg0 context.Context, arg1 *entity.Partner) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePartner", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePartner indicates an expected call of UpdatePartner.
func (mr *MockIPartnerManagerMockRecorder) UpdatePartner(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePartner", reflect.TypeOf((*MockIPartnerManager)(nil).UpdatePartner), arg0, arg1)
}
