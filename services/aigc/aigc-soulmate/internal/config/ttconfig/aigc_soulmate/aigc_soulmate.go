package config

import (
	"fmt"
	"sync/atomic"

	"golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/role/entity"

	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"golang.52tt.com/pkg/log"
)

//go:generate ifacemaker -f aigc_soulmate.go -s AigcSoulmateConfig -p config -i IAigcSoulmateConfig -o aigc_soulmate_api.go
//go:generate mockgen -destination=../mocks/aigc_soulmate.go -package=mocks golang.52tt.com/services/aigc/aigc-soulmate/internal/config/ttconfig/aigc_soulmate IAigcSoulmateConfig
type AigcSoulmateConfig struct {
	ShareDailyLimit    uint32 `json:"share_daily_limit"`     // 分享每日次数上限
	ShareKeyTimeout    uint32 `json:"share_key_timeout"`     // 分享key有效期(秒)
	ShareKeyAllocLimit uint32 `json:"share_key_alloc_limit"` // 分享key分配人数上限

	CreateRoleLimit    uint32 `json:"create_role_limit"`    // 创建角色上限
	ChangeRoleLimit    uint32 `json:"change_role_limit"`    // 修改树洞形象次数上限
	CreateGameLimit    uint32 `json:"create_game_limit"`    // 创建互动玩法上限
	CreatePartnerLimit uint32 `json:"create_partner_limit"` // 创建伴侣上限

	DefaultSoulmateName string `json:"default_soulmate_name"` // 默认灵魂伴侣名称

	// 举报等级特殊处理配置，key:等级_二级标签名，value:处理方式(见auditHandleMethod) map中不存在的默认按audit_no_pass处理
	ReportLevelHandleMap map[string]entity.ReportHandleMethod `json:"report_level_handle_map"`

	MachineSpecialReasonMap map[string]entity.ReportHandleMethod `json:"machine_special_reason_map"` // 机审特殊原因配置
	ReadHeartMaxInterval    uint32                               `json:"read_heart_max_interval"`    // 读心最大次数

	// 默认partner的的头像
	DefaultPartnerAvatar string `json:"default_partner_avatar"` // 默认伴侣头像
	// 默认partner的昵称
	DefaultPartnerName string `json:"default_partner_name"` // 默认伴侣昵称
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (c *AigcSoulmateConfig) Format() error {
	log.Infof("AigcSoulmateConfig.Format config: %+v", c)
	return nil
}

var (
	atomicAigcSoulmateConfig *atomic.Value
)

// InitAigcSoulmateConfig
// 可以选择外部初始化或者直接init函数初始化
func InitAigcSoulmateConfig() error {
	cfg := &AigcSoulmateConfig{}
	atomCfg, err := ttconfig.AtomLoad("aigc-soulmate", cfg)
	if nil != err {
		return err
	}
	atomicAigcSoulmateConfig = atomCfg
	return nil
}

func GetAigcSoulmateConfig() *AigcSoulmateConfig {
	return atomicAigcSoulmateConfig.Load().(*AigcSoulmateConfig)
}

func (c *AigcSoulmateConfig) GetShareDailyLimit() uint32 {
	const defaultLimit = 20

	if c == nil || c.ShareDailyLimit == 0 {
		return defaultLimit
	}

	return c.ShareDailyLimit
}

func (c *AigcSoulmateConfig) GetShareKeyTimeout() uint32 {
	const defaultTimeout = 48 * 3600 // 48h

	if c == nil || c.ShareKeyTimeout == 0 {
		return defaultTimeout

	}

	return c.ShareKeyTimeout
}

func (c *AigcSoulmateConfig) GetShareKeyAllocLimit() uint32 {
	const defaultLimit = 20

	if c == nil || c.ShareKeyAllocLimit == 0 {
		return defaultLimit
	}

	return c.ShareKeyAllocLimit
}

func (c *AigcSoulmateConfig) GetDefaultSoulmateName() string {
	const defaultName = "灵魂Soulmate"

	if c == nil || c.DefaultSoulmateName == "" {
		return defaultName
	}

	return c.DefaultSoulmateName
}

func (c *AigcSoulmateConfig) GetCreateRoleLimit() uint32 {
	const defaultLimit = 5

	if c == nil || c.CreateRoleLimit == 0 {
		return defaultLimit
	}

	return c.CreateRoleLimit
}

func (c *AigcSoulmateConfig) GetCreatePartnerLimit() uint32 {
	const defaultLimit = 100

	if c == nil || c.CreatePartnerLimit == 0 {
		return defaultLimit
	}

	return c.CreatePartnerLimit
}

func (c *AigcSoulmateConfig) GetChangeRoleLimit() uint32 {
	const defaultLimit = 10

	if c == nil || c.ChangeRoleLimit == 0 {
		return defaultLimit
	}

	return c.ChangeRoleLimit
}

func (c *AigcSoulmateConfig) GetReportLevelHandle(level string, labelId uint32) entity.ReportHandleMethod {

	if c == nil || len(c.ReportLevelHandleMap) == 0 {
		return ""
	}

	return c.ReportLevelHandleMap[fmt.Sprintf("%s_%d", level, labelId)]
}

func (c *AigcSoulmateConfig) GetMachineReasonHandle(reason string) entity.ReportHandleMethod {

	if c == nil || len(c.MachineSpecialReasonMap) == 0 {
		return ""
	}

	return c.MachineSpecialReasonMap[reason]
}

func (c *AigcSoulmateConfig) GetCreateGameLimit() uint32 {
	const defaultLimit = 100

	if c == nil || c.CreateGameLimit == 0 {
		return defaultLimit
	}

	return c.CreateGameLimit
}

func (c *AigcSoulmateConfig) GetReadHeartMaxInterval() uint32 {
	const defaultInterval = 15
	const defaultMaxInterval = 33

	if c == nil || c.ReadHeartMaxInterval == 0 {
		return defaultInterval
	}

	if c.ReadHeartMaxInterval > 33 {
		return defaultMaxInterval
	}
	return c.ReadHeartMaxInterval
}

func (c *AigcSoulmateConfig) GetDefaultPartnerAvatar() string {
	if c == nil {
		return ""
	}
	return c.DefaultPartnerAvatar
}

func (c *AigcSoulmateConfig) GetDefaultPartnerName() string {
	if c == nil {
		return ""
	}
	return c.DefaultPartnerName
}
