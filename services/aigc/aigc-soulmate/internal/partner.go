package internal

import (
	"context"
	config "golang.52tt.com/services/aigc/aigc-soulmate/internal/config/ttconfig/aigc_soulmate"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/partner/entity"
)

func (s *Server) TryCreateAIPartner(ctx context.Context, req *pb.CreateAIPartnerReq) (*pb.CreateAIPartnerResp, error) {
	resp := new(pb.CreateAIPartnerResp)
	log.InfoWithCtx(ctx, "TryCreateAIPartner req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.WarnWithCtx(ctx, "TryCreateAIPartner miss uid")
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	err := req.Validate()
	if err != nil {
		log.WarnWithCtx(ctx, "TryCreateAIPartner Validate req(%+v) err: %v", req, err)
		return resp, err
	}

	reqPartner := req.GetPartner()
	role, err := s.role.GetRole(ctx, reqPartner.GetRoleId())
	if err != nil {
		log.ErrorWithCtx(ctx, "TryCreateAIPartner GetRole id(%d) err: %v", reqPartner.GetRoleId(), err)
		return resp, err
	}
	if role == nil {
		log.WarnWithCtx(ctx, "TryCreateAIPartner GetRole id(%d) not found", reqPartner.GetRoleId())
		return resp, protocol.NewExactServerError(nil, status.ErrChatBotAiRoleNotFound)
	}

	partner, err := entity.CreatePartner(uid, *reqPartner, role.Type, role.Name)
	if err != nil {
		log.ErrorWithCtx(ctx, "TryCreateAIPartner entity.CreatePartner partner(%+v) err: %v", reqPartner, err)
		return resp, err
	}

	err = s.partner.TryCreatePartner(ctx, partner)
	if err != nil {
		log.ErrorWithCtx(ctx, "TryCreateAIPartner TryCreatePartner partner(%+v) err: %v", reqPartner, err)
		return resp, err
	}

	resp.Partner, err = s.assembleAIPartner(ctx, partner)
	if err != nil {
		return resp, err
	}

	log.InfoWithCtx(ctx, "TryCreateAIPartner resp: %+v", resp)
	return resp, nil
}

func (s *Server) CreateAIPartner(ctx context.Context, req *pb.CreateAIPartnerReq) (*pb.CreateAIPartnerResp, error) {
	resp := new(pb.CreateAIPartnerResp)
	log.InfoWithCtx(ctx, "CreateAIPartner req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.WarnWithCtx(ctx, "CreateAIPartner miss uid")
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	err := req.Validate()
	if err != nil {
		log.WarnWithCtx(ctx, "CreateAIPartner Validate req(%+v) err: %v", req, err)
		return resp, err
	}

	reqPartner := req.GetPartner()
	role, err := s.role.GetRole(ctx, reqPartner.GetRoleId())
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIPartner GetRole id(%d) err: %v", reqPartner.GetRoleId(), err)
		return resp, err
	}
	if role == nil {
		log.WarnWithCtx(ctx, "CreateAIPartner GetRole id(%d) not found", reqPartner.GetRoleId())
		return resp, protocol.NewExactServerError(nil, status.ErrChatBotAiRoleNotFound)
	}

	partner, err := entity.CreatePartner(uid, *reqPartner, role.Type, role.Name)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIPartner entity.CreatePartner partner(%+v) err: %v", reqPartner, err)
		return resp, err
	}

	err = s.partner.CreatePartner(ctx, partner)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIPartner CreatePartner partner(%+v) err: %v", partner, err)
		return resp, err
	}

	resp.Partner, err = s.assembleAIPartner(ctx, partner)
	if err != nil {
		return resp, err
	}

	log.InfoWithCtx(ctx, "CreateAIPartner resp: %+v", resp)
	return resp, nil
}

func (s *Server) UpdateAIPartner(ctx context.Context, req *pb.UpdateAIPartnerReq) (*pb.UpdateAIPartnerResp, error) {
	resp := new(pb.UpdateAIPartnerResp)
	log.InfoWithCtx(ctx, "UpdateAIPartner req: %+v", req)

	err := req.Validate()
	if err != nil {
		log.WarnWithCtx(ctx, "UpdateAIPartner Validate req(%+v) err: %v", req, err)
		return resp, err
	}

	reqPartner := req.GetPartner()
	partner, err := s.partner.GetPartner(ctx, reqPartner.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIPartner GetPartner id(%d) err: %v", reqPartner.GetId(), err)
		return resp, err
	}

	err = partner.Update(*reqPartner)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIPartner Update partner(%+v) err: %v", reqPartner, err)
		return resp, err
	}

	err = s.partner.UpdatePartner(ctx, partner)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIPartner UpdatePartner partner(%+v) err: %v", partner, err)
		return resp, err
	}

	resp.Partner, err = s.assembleAIPartner(ctx, partner)
	if err != nil {
		return resp, err
	}

	log.InfoWithCtx(ctx, "UpdateAIPartner resp: %+v", resp)
	return resp, nil
}

func (s *Server) DeleteAIPartner(ctx context.Context, req *pb.DeleteAIPartnerReq) (*pb.DeleteAIPartnerResp, error) {
	resp := new(pb.DeleteAIPartnerResp)
	log.InfoWithCtx(ctx, "DeleteAIPartner req: %+v", req)

	if req.GetId() == 0 {
		log.WarnWithCtx(ctx, "DeleteAIPartner invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	err := s.partner.DeletePartner(ctx, req.GetId())
	if err != nil {
		log.WarnWithCtx(ctx, "DeleteAIPartner DeleteAIPartner id(%d) err: %v", req.GetId(), err)
		return resp, err
	}

	return resp, nil
}

func (s *Server) ChangeAIPartnerRole(ctx context.Context, req *pb.ChangeAIPartnerRoleReq) (*pb.ChangeAIPartnerRoleResp, error) {
	resp := new(pb.ChangeAIPartnerRoleResp)
	log.InfoWithCtx(ctx, "ChangeAIPartnerRole req: %+v", req)

	if req.GetId() == 0 || req.GetRoleId() == 0 {
		log.WarnWithCtx(ctx, "ChangeAIPartnerRole invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	partner, err := s.partner.GetPartner(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangeAIPartnerRole GetPartner id(%d) err: %v", req.GetId())
		return resp, err
	}
	if partner == nil {
		log.WarnWithCtx(ctx, "ChangeAIPartnerRole GetPartner id(%d) not found", req.GetId())
		return resp, protocol.NewExactServerError(nil, status.ErrChatBotAiPartnerNotFound)
	}

	role, err := s.role.GetRole(ctx, req.GetRoleId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangeAIPartnerRole GetRole id(%d) err: %v", req.GetRoleId())
		return resp, err
	}
	if role == nil {
		log.WarnWithCtx(ctx, "ChangeAIPartnerRole role %d not found", req.GetRoleId())
		return resp, protocol.NewExactServerError(nil, status.ErrChatBotAiRoleNotFound)
	}

	err = partner.ChangeRole(role.ID, role.Type)
	if err != nil {
		return resp, err
	}

	err = s.partner.ChangePartnerRole(ctx, partner)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangeAIPartnerRole ChangePartnerRole partner(%+v) err: %v", partner, err)
		return resp, err
	}

	resp.Partner, err = s.assembleAIPartner(ctx, partner)
	if err != nil {
		return resp, err
	}

	log.InfoWithCtx(ctx, "ChangeAIPartnerRole resp: %+v", resp)
	return resp, nil
}

func (s *Server) UpdateAIPartnerChatState(ctx context.Context, req *pb.UpdateAIPartnerChatStateReq) (*pb.UpdateAIPartnerChatStateResp, error) {
	resp := new(pb.UpdateAIPartnerChatStateResp)
	log.InfoWithCtx(ctx, "UpdateAIPartnerChatState req: %+v", req)

	if req.GetId() == 0 {
		log.WarnWithCtx(ctx, "UpdateAIPartnerChatState invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	partner, err := s.partner.GetPartner(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIPartnerChatState GetPartner id(%d) err: %v", req.GetId(), err)
		return resp, err
	}
	if partner == nil {
		log.ErrorWithCtx(ctx, "UpdateAIPartnerChatState GetPartner id(%d) not found", req.GetId())
		return resp, protocol.NewExactServerError(nil, status.ErrChatBotAiPartnerNotFound)
	}

	switched, err := partner.SwitchSilent(req.GetSilent())
	if err != nil {
		return resp, err
	}
	if switched {
		err = s.partner.UpdatePartner(ctx, partner)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIPartnerChatState UpdatePartner partner(%+v) err: %v", partner, err)
			return resp, err
		}
	}

	return resp, nil
}

func (s *Server) GetAIPartner(ctx context.Context, req *pb.GetAIPartnerReq) (*pb.GetAIPartnerResp, error) {
	resp := new(pb.GetAIPartnerResp)
	log.DebugWithCtx(ctx, "GetAIPartner req: %+v", req)

	if req.GetId() == 0 {
		log.WarnWithCtx(ctx, "GetAIPartner invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	partner, err := s.partner.GetPartner(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIPartner GetAIPartner id(%d) err: %v", req.GetId(), err)
		return resp, err
	}
	if partner == nil {
		log.WarnWithCtx(ctx, "GetAIPartner partner %d not found", req.GetId())
		return resp, nil
	}

	resp.Partner, err = s.assembleAIPartner(ctx, partner)
	if err != nil {
		return resp, err
	}

	log.DebugWithCtx(ctx, "GetAIPartner resp: %+v", resp)
	return resp, nil
}

func (s *Server) GetUserAIPartner(ctx context.Context, req *pb.GetUserAIPartnerReq) (*pb.GetUserAIPartnerResp, error) {
	resp := new(pb.GetUserAIPartnerResp)
	log.DebugWithCtx(ctx, "GetUserAIPartner req: %+v", req)

	if req.GetUid() == 0 {
		log.WarnWithCtx(ctx, "GetUserAIPartner invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	var (
		err     error
		partner *entity.Partner
	)
	if req.GetRoleId() > 0 {
		partner, err = s.partner.GetUserPartnerByRoleID(ctx, req.GetUid(), req.GetRoleId())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserAIPartner GetUserAIPartnerByRoleID uid(%d) roleId(%d) err: %v", req.GetUid(), req.GetRoleId(), err)
			return resp, err
		}
	} else {
		partner, err = s.partner.GetUserPartnerByRoleType(ctx, req.GetUid(), req.GetRoleType())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserAIPartner GetUserAIPartnerByRoleType uid(%d) roleType(%d) err: %v", req.GetUid(), req.GetRoleType(), err)
			return resp, err
		}
	}
	if partner == nil {
		return resp, nil
	}

	resp.Partner, err = s.assembleAIPartner(ctx, partner)
	if err != nil {
		return resp, err
	}

	log.DebugWithCtx(ctx, "GetUserAIPartner resp: %+v", resp)
	return resp, nil
}

func (s *Server) GetUserAIPartnerList(ctx context.Context, req *pb.GetUserAIPartnerListReq) (*pb.GetUserAIPartnerListResp, error) {
	resp := new(pb.GetUserAIPartnerListResp)
	log.DebugWithCtx(ctx, "GetUserAIPartnerList req: %+v", req)

	if req.GetUid() == 0 {
		log.WarnWithCtx(ctx, "GetUserAIPartnerList invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	roleTypeList := req.GetRoleTypes()
	if len(roleTypeList) == 0 {
		// 兼容旧逻辑没有传roleType，桌宠需求上线后可去掉
		roleTypeList = []pb.AIRoleType{pb.AIRoleType_AIRoleTypePartner, pb.AIRoleType_AIRoleTypeGame}
	}

	partners, err := s.partner.GetUserPartnerList(ctx, req.GetUid(), roleTypeList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAIPartnerList GetUserAIPartnerList uid(%d) err: %v", req.GetUid(), err)
		return resp, err
	}
	if len(partners) == 0 {
		return resp, nil
	}

	resp.PartnerList, err = s.assembleAIPartnerList(ctx, partners)
	if err != nil {
		return resp, err
	}

	log.DebugWithCtx(ctx, "GetUserAIPartnerList resp: %+v", resp)
	return resp, nil
}

// BatchGetAIPartner 批量获取AI伙伴，如果传入的ID列表中有不存在的伙伴ID，则返回默认的AI伙伴信息（默认头像、默认名称，但没id）
func (s *Server) BatchGetAIPartner(ctx context.Context, req *pb.BatchGetAIPartnerReq) (*pb.BatchGetAIPartnerResp, error) {
	resp := new(pb.BatchGetAIPartnerResp)
	if len(req.GetIds()) == 0 {
		log.WarnWithCtx(ctx, "BatchGetAIPartner invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	partners, err := s.partner.BatGetPartnerByIds(ctx, req.GetIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAIPartner BatGetPartnerByIds ids(%v) err: %v", req.GetIds(), err)
		return resp, err
	}

	data, err := s.assembleAIPartnerList(ctx, partners)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAIPartner assembleAIPartnerList, req:%v, partners(%+v) err: %v", req, partners, err)
		return resp, err
	}
	assiMap := make(map[uint32]*pb.AIPartner, len(data))
	for _, partner := range data {
		assiMap[partner.GetId()] = partner
	}
	resp.PartnerMap = make(map[uint32]*pb.AIPartner, len(req.GetIds()))
	for _, id := range req.GetIds() {
		if id == 0 {
			log.WarnWithCtx(ctx, "BatchGetAIPartner invalid partner id: %d", id)
			continue
		}
		partner, ok := assiMap[id]
		if ok {
			resp.PartnerMap[id] = partner

		} else {
			log.WarnWithCtx(ctx, "BatchGetAIPartner partner id(%d) not found, use default partner", id)
			partner = &pb.AIPartner{
				Name: config.GetAigcSoulmateConfig().GetDefaultPartnerName(),
				Role: &pb.AIRole{
					Avatar: config.GetAigcSoulmateConfig().GetDefaultPartnerAvatar(),
					Name:   config.GetAigcSoulmateConfig().GetDefaultPartnerName(),
				},
			}
		}
		resp.PartnerMap[id] = partner
	}

	log.InfoWithCtx(ctx, "BatchGetAIPartner req:%v, resp: %+v", req, resp)
	return resp, nil
}
