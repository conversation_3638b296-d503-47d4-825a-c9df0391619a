package mgr

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/protocol"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/monkey-send-chat/monkey_sender"
	"golang.52tt.com/pkg/speedlimit"
	channelPb "golang.52tt.com/protocol/app/channel"
	inviteroomlogic "golang.52tt.com/protocol/app/invite-room-logic"
	accountGoPb "golang.52tt.com/protocol/services/account-go"
	gameCardPb "golang.52tt.com/protocol/services/game-card"
	gamePalPb "golang.52tt.com/protocol/services/game-pal"
	gangupPb "golang.52tt.com/protocol/services/gangup-channel"
	inviteRoomPb "golang.52tt.com/protocol/services/invite-room"
	"golang.52tt.com/protocol/services/rcmd/rcmd_invite_user"
	topicChannelPb "golang.52tt.com/protocol/services/topic_channel/channel"
	tabPb "golang.52tt.com/protocol/services/topic_channel/tab"
	friendPb "golang.52tt.com/protocol/services/ugc/friendship"
	config "golang.52tt.com/services/invite-room-logic/internal/config/ttconfig/invite_room_logic"
	"golang.52tt.com/services/invite-room-logic/internal/rpc"
	"golang.52tt.com/services/invite-room-logic/internal/utils"
	"math"
	"strconv"
	"strings"
	"time"
)

const (
	unLimitElemId   = math.MaxUint32
	unLimitElemName = "不限"

	InviteListReqLimit = 20

	Minute = 60
	Hour   = 3600
	Day    = 86400
	Month  = 2592000

	selectTagsNormalLimit = 8
)

type InviteListMgr struct {
	inviteListReq             *InviteListReq
	accountInfoMap            map[uint32]*accountGoPb.UserResp
	tabInfoMap                map[uint32]*tabPb.Tab
	userStatusMap             map[uint32]uint32
	userFollowInfoMap         map[uint32]followInfo
	userRoomInfoMap           map[uint32]roomInfo
	gamePalTagsMap            map[uint32]gamePalInfo
	selectChannelLabelTagsMap map[uint32][]string
	gameCardLabelTagsMap      map[uint32][]string
	userInviteStatusMap       map[uint32]uint32
	tabGameCardImgMap         map[uint32]gameCardImgInfo
	errOverInst               speedlimit.IErrorOver
}

type InviteListReq struct {
	Uid uint32
	// 用户房间玩法
	TabId uint32
	// 筛选标签
	Labels []string
	// 强推uid,邀请入口点击用户头像需带上该用户
	ForceUid []uint32
	// 上次请求未曝光id列表
	NoBrowseList []uint32
	// 1-请求下一页(上滑刷新)，2-刷新
	ReqType uint32
	// 客户端版本
	ClientVersion uint32
}

type InviteListResp struct {
	LoadFinish bool
	Items      []*InviteListItem
}

type InviteListItem struct {
	TabName        string
	TabImg         string
	Title          string
	InviteUserInfo *InviteRoomUserInfo
	UserTags       []string
}

type InviteRoomUserInfo struct {
	Uid            uint32
	Account        string
	Nickname       string
	Sex            uint32
	Status         uint32
	FollowText     string
	PlayText       string
	InviteRateText string
	InviteStatus   uint32
	FriendRemark   string
}

type roomInfo struct {
	Cid          uint32
	Name         string // 房间标题
	IsPublishing bool
	DescFirst    []string
	DescList     []string
}

type followInfo struct {
	FollowText   string
	FriendRemark string
}

type gamePalInfo struct {
	Title string
	Tags  []string
}

type gameCardImgInfo struct {
	BackgroundImg      string
	BackgroundColorNum uint32
}

func NewInviteListMgr(inviteListReq *InviteListReq, errOverInst *speedlimit.ErrorOver) *InviteListMgr {
	return &InviteListMgr{
		inviteListReq:             inviteListReq,
		accountInfoMap:            make(map[uint32]*accountGoPb.UserResp),
		tabInfoMap:                make(map[uint32]*tabPb.Tab),
		userStatusMap:             make(map[uint32]uint32),
		userFollowInfoMap:         make(map[uint32]followInfo),
		userRoomInfoMap:           make(map[uint32]roomInfo),
		gamePalTagsMap:            make(map[uint32]gamePalInfo),
		selectChannelLabelTagsMap: make(map[uint32][]string),
		userInviteStatusMap:       make(map[uint32]uint32),
		tabGameCardImgMap:         make(map[uint32]gameCardImgInfo),
		errOverInst:               errOverInst,
	}
}

func (m *InviteListMgr) GetInviteListData(ctx context.Context) (*InviteListResp, error) {
	resp := &InviteListResp{}
	m.errOverInst.AddTotalCnt(1)
	loadFinish, originDatas, err := m.getInviteListOriginData(ctx, m.inviteListReq)
	resp.LoadFinish = loadFinish
	if err != nil {
		return resp, err
	}
	if len(originDatas) == 0 {
		m.errOverInst.AddErrorCnt(1)
		if m.errOverInst.IsOverWithMin(ctx, config.GetInviteRoomLogicConfig().GetMinEmptyListWarnReqNum()) {
			go func() {
				errCnt, totalCnt, errRate := m.errOverInst.GetErrRate()
				sendErr := monkey_sender.GetQueueMsgSenderByChatId(config.GetInviteRoomLogicConfig().GetWarnChatId(), 100).
					SendMsg("邀请进房列表", fmt.Sprintf("推荐返回空列表请求数量：%d 请求总数：%d 请求比例为:%.2f%% overRate:%d%% pod_name:%s",
						errCnt, totalCnt, errRate, m.errOverInst.GetOverRate(), config.PodName))
				if sendErr != nil {
					log.ErrorWithCtx(ctx, "GetInviteListData monkey_sender SendMsg err:%v", sendErr)
				}
			}()
		}
		log.InfoWithCtx(ctx, "GetInviteListData, originDatas is empty, inviteListReq:%+v", m.inviteListReq)
		return resp, nil
	}

	err = m.getInviteListOtherData(ctx, originDatas)
	if err != nil {
		return resp, err
	}

	resp.Items = make([]*InviteListItem, 0, len(originDatas))
	for _, originData := range originDatas {
		resItem, fillErr := m.fillUserData(ctx, originData)
		if fillErr != nil {
			log.ErrorWithCtx(ctx, "fillUserData fail, err:%s, originData:%+v, inviteListReq:%+v", fillErr.Error(), originData, m.inviteListReq)
			continue
		}
		if resItem == nil {
			continue
		}
		resp.Items = append(resp.Items, resItem)
	}
	return resp, nil
}

func (m *InviteListMgr) getInviteListOriginData(ctx context.Context, inviteListReq *InviteListReq) (bool, []*rcmd_invite_user.UserInfo, error) {
	newCtx, cancel := context.WithTimeout(ctx, time.Millisecond*2000)
	defer cancel()

	rsp, err := rpc.RcmdInviteUserCli.GetInviteStrangerUser(newCtx, &rcmd_invite_user.GetInviteStrangerUserReq{
		Uid:          inviteListReq.Uid,
		TabId:        inviteListReq.TabId,
		InviteSource: rcmd_invite_user.GetInviteStrangerUserReq_List,
		ReqType:      rcmd_invite_user.GetInviteStrangerUserReq_ReqType(inviteListReq.ReqType),
		Limit:        InviteListReqLimit,
		Labels:       inviteListReq.Labels,
		ForceUid:     inviteListReq.ForceUid,
		NoBrowseList: inviteListReq.NoBrowseList,
	})
	if err != nil {
		log.ErrorWithCtx(newCtx, "GetInviteStrangerUser fail, err:%s, req:%+v", err.Error(), inviteListReq)
		return true, nil, err
	}
	log.InfoWithCtx(newCtx, "GetInviteStrangerUser, req:%+v, rsp:%+v", inviteListReq, rsp)

	return rsp.GetBottomReached(), rsp.GetStrangerList(), err
}

// 组装数据
func (m *InviteListMgr) getInviteListOtherData(ctx context.Context, originDatas []*rcmd_invite_user.UserInfo) error {
	// 获取各种数据，看情况是否需要并发去取数据
	tabIds := make([]uint32, 0, len(originDatas)+1)
	tabIds = append(tabIds, m.inviteListReq.TabId)
	showUids := make([]uint32, 0, len(originDatas))
	publishingUids := make([]uint32, 0, len(originDatas))
	gamePalUids := make([]uint32, 0, len(originDatas))
	selectLabelUids := make([]uint32, 0, len(originDatas))
	gameCardLabelUids := make([]uint32, 0, len(originDatas))
	for _, originData := range originDatas {
		if originData.GetShowTabId() > 0 {
			tabIds = append(tabIds, originData.GetShowTabId())
		}
		if originData.GetDetail().GetLastPlayTabId() > 0 {
			tabIds = append(tabIds, originData.GetDetail().GetLastPlayTabId())
		}
		showUids = append(showUids, originData.GetUid())
		switch originData.GetTitleShowType() {
		case rcmd_invite_user.UserInfo_Channel:
			publishingUids = append(publishingUids, originData.GetUid())
		case rcmd_invite_user.UserInfo_SelectLabel:
			selectLabelUids = append(selectLabelUids, originData.GetUid())
		case rcmd_invite_user.UserInfo_PalCard:
			gamePalUids = append(gamePalUids, originData.GetUid())
		case rcmd_invite_user.UserInfo_GameCard:
			gameCardLabelUids = append(gameCardLabelUids, originData.GetUid())
		}
	}

	// 并发读取数据， 部分数据不需要必须返回，错误也不需提前返回
	labelTask := []func(*context.Context, bool) (error, bool){
		func(ctx *context.Context, bCancelLog bool) (error, bool) {
			err := m.getTabInfoMap(*ctx, tabIds)
			if err != nil {
				return err, true
			}
			return nil, false
		},
		func(ctx *context.Context, bCancelLog bool) (error, bool) {
			err := m.getUserAccountInfo(*ctx, showUids)
			if err != nil {
				return err, true
			}
			return nil, false
		},
		func(ctx *context.Context, bCancelLog bool) (error, bool) {
			_ = m.getUserStatus(*ctx, m.inviteListReq.Uid, showUids)
			return nil, false
		},
		func(ctx *context.Context, bCancelLog bool) (error, bool) {
			_ = m.getUserFollowText(*ctx, m.inviteListReq.Uid, showUids)
			return nil, false
		},
		func(ctx *context.Context, bCancelLog bool) (error, bool) {
			_ = m.getUserInviteStatus(*ctx, m.inviteListReq.Uid, showUids)
			return nil, false
		},
		func(ctx *context.Context, bCancelLog bool) (error, bool) {
			if len(selectLabelUids) > 0 {
				_ = m.getChannelSelectTags(*ctx, selectLabelUids)
			}
			return nil, false
		},
		func(ctx *context.Context, bCancelLog bool) (error, bool) {
			if len(gamePalUids) > 0 {
				_ = m.getGamePalTags(*ctx, gamePalUids)
			}
			return nil, false
		},
	}
	err, _ := utils.CoroutineDoWithSingleResult("", ctx, uint32(len(labelTask)), labelTask...)
	if err != nil {
		log.WarnWithCtx(ctx, "CoroutineDoWithSingleResult len(labelTask):%d, err:%v", len(labelTask), err)
		return err
	}

	// 获取游戏卡数据依赖tab信息，不能一起并发获取
	if len(gameCardLabelUids) > 0 {
		_ = m.getGameCardLabel(ctx, gameCardLabelUids)
	}

	return nil
}

// 组装数据
func (m *InviteListMgr) fillUserData(ctx context.Context, originData *rcmd_invite_user.UserInfo) (*InviteListItem, error) {
	res := &InviteListItem{
		TabName: m.tabInfoMap[originData.ShowTabId].GetName(),
		TabImg:  m.tabInfoMap[originData.ShowTabId].GetSmallCardUrl(),
		Title:   "",
		InviteUserInfo: &InviteRoomUserInfo{
			Uid:            originData.GetUid(),
			Account:        m.accountInfoMap[originData.GetUid()].GetUsername(),
			Nickname:       m.accountInfoMap[originData.GetUid()].GetNickname(),
			Sex:            uint32(m.accountInfoMap[originData.GetUid()].GetSex()),
			Status:         m.userStatusMap[originData.GetUid()],
			FollowText:     m.userFollowInfoMap[originData.GetUid()].FollowText,
			PlayText:       m.getUserPlayText(ctx, originData.GetDetail().GetLastPlayTabId(), originData.GetDetail().GetLastPlayTs()),
			InviteRateText: originData.GetDetail().GetInviteRateText(),
			InviteStatus:   m.userInviteStatusMap[originData.GetUid()],
			FriendRemark:   m.userFollowInfoMap[originData.GetUid()].FriendRemark,
		},
		UserTags: nil,
	}

	switch originData.TitleShowType {
	case rcmd_invite_user.UserInfo_Channel:
		// 非发布中的过滤掉
		if !m.userRoomInfoMap[originData.GetUid()].IsPublishing {
			log.InfoWithCtx(ctx, "fillUserData, not publishing, uid:%d", originData.GetUid())
			return nil, nil
		}
		res.Title = m.userRoomInfoMap[originData.GetUid()].Name
		res.UserTags = append(m.userRoomInfoMap[originData.GetUid()].DescFirst, m.userRoomInfoMap[originData.GetUid()].DescList...)

	case rcmd_invite_user.UserInfo_SelectLabel:
		res.UserTags = m.selectChannelLabelTagsMap[originData.GetUid()]

	case rcmd_invite_user.UserInfo_PalCard:
		res.Title = m.gamePalTagsMap[originData.GetUid()].Title
		res.UserTags = m.gamePalTagsMap[originData.GetUid()].Tags

	case rcmd_invite_user.UserInfo_GameCard:
		res.UserTags = m.gameCardLabelTagsMap[originData.GetUid()]

	default:
		log.DebugWithCtx(ctx, "fillUserData, default TitleShowType originData:%+v", originData)
	}

	return res, nil
}

// 获取玩法信息
func (m *InviteListMgr) getTabInfoMap(ctx context.Context, tabIds []uint32) error {
	newCtx, cancel := context.WithTimeout(ctx, time.Millisecond*500)
	defer cancel()

	tabInfoMap, err := rpc.TCTabClient.GetTabsByIds(newCtx, tabIds)
	if err != nil {
		log.ErrorWithCtx(newCtx, "GetTabsByIds fail, err:%s, tabIds:%+v", err.Error(), tabIds)
		return err
	}
	m.tabInfoMap = tabInfoMap
	return nil
}

// 获取用户信息
func (m *InviteListMgr) getUserAccountInfo(ctx context.Context, showUids []uint32) error {
	newCtx, cancel := context.WithTimeout(ctx, time.Millisecond*500)
	defer cancel()

	resp, err := rpc.AccountClient.GetUsersByUids(newCtx, showUids)
	if err != nil {
		log.ErrorWithCtx(newCtx, "GetUsersByUids fail, err:%s, uids:%+v", err.Error(), showUids)
		return err
	}
	accountInfoMap := make(map[uint32]*accountGoPb.UserResp, len(resp.GetUserList()))
	for _, user := range resp.GetUserList() {
		accountInfoMap[user.GetUid()] = user
	}
	m.accountInfoMap = accountInfoMap
	return nil
}

// 发布中>在线>离线
func (m *InviteListMgr) getUserStatus(ctx context.Context, uid uint32, showUids []uint32) error {
	newCtx, cancel := context.WithTimeout(ctx, time.Millisecond*1000)
	defer cancel()

	// 获取房间数据
	err := m.getUserRoomInfo(newCtx, uid, showUids, true)
	if err != nil {
		return err
	}

	// 在线状态
	presenceMap, err := rpc.PresenceClient.BatchGetUserPres(newCtx, showUids)
	if err != nil {
		log.ErrorWithCtx(newCtx, "getUserOnlineStatus BatchGetUserPres fail, err:%v, uids:%v", err, showUids)
	}

	userStatusMap := make(map[uint32]uint32, len(showUids))
	for _, showUid := range showUids {
		if v, ok := m.userRoomInfoMap[showUid]; ok && v.IsPublishing {
			userStatusMap[showUid] = uint32(inviteroomlogic.InviteRoomUserInfo_ONLINE_STATUS_PUBLISHING)

		} else if _, ok := m.userRoomInfoMap[showUid]; ok && m.inviteListReq.ClientVersion >= protocol.FormatClientVersion(6, 52, 5) {
			// 新增的状态展示，要兼容旧版本
			userStatusMap[showUid] = uint32(inviteroomlogic.InviteRoomUserInfo_ONLINE_STATUS_IN_ROOM)

		} else if v1, ok1 := presenceMap[showUid]; ok1 && v1 != nil {
			userStatusMap[showUid] = uint32(inviteroomlogic.InviteRoomUserInfo_ONLINE_STATUS_ONLINE)

		} else {
			userStatusMap[showUid] = uint32(inviteroomlogic.InviteRoomUserInfo_ONLINE_STATUS_UNSPECIFIED)
		}
	}
	m.userStatusMap = userStatusMap
	return nil
}

// 获取房间信息
func (m *InviteListMgr) getUserRoomInfo(ctx context.Context, uid uint32, showUids []uint32, isInvite bool) error {
	// 用户在房信息
	userChannelMap, err := rpc.ChannelOl.BatchGetUserChannelId(ctx, uid, showUids...)
	if err != nil {
		log.ErrorWithCtx(ctx, "getUserRoomInfo BatchGetUserChannelId fail, err:%v, uids:%v", err, showUids)
		return err
	}
	cids := make([]uint32, 0, len(userChannelMap))
	for _, cid := range userChannelMap {
		if cid == 0 {
			continue
		}
		cids = append(cids, cid)
	}
	// 获取房间信息
	channelId2Info, err := rpc.ChannelCli.BatchGetChannelSimpleInfo(ctx, uid, cids)
	if nil != err {
		log.ErrorWithCtx(ctx, "getUserRoomInfo BatchGetChannelSimpleInfo err:%v", err)
		return err
	}

	ugcChannelIds := make([]uint32, 0, len(channelId2Info))
	for _, cfgInfo := range channelId2Info {
		if cfgInfo.GetChannelType() == uint32(channelPb.ChannelType_USER_CHANNEL_TYPE) {
			ugcChannelIds = append(ugcChannelIds, cfgInfo.GetChannelId())
		}
	}
	// 只返回发布中的房间
	tcResp, err := rpc.TopicClient.GetChannelByIds(ctx, &topicChannelPb.GetChannelByIdsReq{Ids: ugcChannelIds})
	if err != nil {
		log.ErrorWithCtx(ctx, "getUserRoomInfo GetChannelByIds fail, err:%v, cids:%v", err, ugcChannelIds)
		return err
	}
	publishChannelMap := make(map[uint32]*topicChannelPb.ChannelInfo)
	for _, t := range tcResp.GetInfo() {
		publishChannelMap[t.GetId()] = t
	}

	// 获取发布标签
	blockInfo, err := rpc.TCTabClient.BatchGetBlocks(ctx, &tabPb.BatchGetBlocksReq{TabId: []uint32{m.inviteListReq.TabId}})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabInfoCache genBaseBlocksMap err %v", err)
		return err
	}

	userRoomInfoMap := make(map[uint32]roomInfo, len(showUids))
	for showUid, cid := range userChannelMap {
		if cid == 0 {
			continue
		}
		info := roomInfo{
			Cid: cid,
		}
		// 发布中数据
		if v, ok := publishChannelMap[cid]; ok && v != nil {
			// 房间信息
			if showUid == channelId2Info[v.GetId()].GetCreaterUid() {
				// 玩法相同才展示房间标签
				if v.GetTabId() == m.inviteListReq.TabId {
					info.Name = channelId2Info[v.GetId()].GetName()
					confBlockInfo := blockInfo.GetData()[v.GetTabId()].GetBlocks()
					info.DescFirst = GenDescFirst(v.GetBlockOptions(), confBlockInfo, isInvite)
					info.DescList = GetDescListByBusiness(v.GetBlockOptions(), confBlockInfo, isInvite)
				}
				// 房主才展示发布中
				info.IsPublishing = true
			}
		}
		userRoomInfoMap[showUid] = info
	}
	m.userRoomInfoMap = userRoomInfoMap
	return nil
}

// 关注的人/我的玩伴
func (m *InviteListMgr) getUserFollowText(ctx context.Context, uid uint32, showUids []uint32) error {
	newCtx, cancel := context.WithTimeout(ctx, time.Millisecond*1000)
	defer cancel()

	// 关注状态信息
	userFollow, followUser, err := rpc.FriendshipCli.BatchGetBiFollowingWithCache(newCtx, uid, showUids, true, true)
	if err != nil {
		log.ErrorWithCtx(newCtx, "getUserFollowText BatchGetBiFollowingWithCache fail, err:%v, uids:%v", err, showUids)
		return err
	}
	userFollowInfoMap := make(map[uint32]followInfo, len(showUids))

	friendPair := make([]*friendPb.BatchGetFriendInfoReq_FriendPair, 0, len(showUids))

	for _, showUid := range showUids {
		var userFollowInfo followInfo
		if userFollow[showUid] {
			userFollowInfo.FollowText = "关注的人"
		}
		if userFollow[showUid] && followUser[showUid] {
			friendPair = append(friendPair, &friendPb.BatchGetFriendInfoReq_FriendPair{
				Uid:       uid,
				FriendUid: showUid,
			})
			userFollowInfo.FollowText = "我的玩伴"
		}
		userFollowInfoMap[showUid] = userFollowInfo
	}

	if len(friendPair) > 0 {
		friendInfo, friendInfoErr := rpc.FriendshipCli.BatchGetFriendInfo(ctx, &friendPb.BatchGetFriendInfoReq{
			FriendPairs: friendPair,
		})
		if friendInfoErr != nil {
			log.ErrorWithCtx(ctx, "getUserFollowText BatchGetFriendInfo fail, err:%v, friendPair:%v", friendInfoErr, friendPair)
			return friendInfoErr
		}
		for _, info := range friendInfo.GetFriendInfos() {
			showUid := info.GetFriendInfo().GetFriendUid()
			if v, ok := userFollowInfoMap[showUid]; ok {
				v.FriendRemark = info.GetFriendInfo().GetRemark()
				userFollowInfoMap[showUid] = v
			}
		}
	}

	m.userFollowInfoMap = userFollowInfoMap
	return nil
}

// 一起玩信息
func (m *InviteListMgr) getUserPlayText(ctx context.Context, lastPlayTabId uint32, lastPlayTs int64) string {
	playText := ""
	if lastPlayTabId == 0 || lastPlayTs <= 0 {
		return playText
	}

	tabInfo := m.tabInfoMap[lastPlayTabId]
	if tabInfo == nil {
		log.WarnWithCtx(ctx, "getUserPlayText, tabInfo not found, lastPlayTabId:%d", lastPlayTabId)
		return playText
	}
	interval := time.Now().Unix() - lastPlayTs
	if interval < Minute {
		playText = fmt.Sprintf("刚刚一起玩过%s", tabInfo.GetName())
	} else if interval < Hour {
		playText = fmt.Sprintf("%d分钟前一起玩过%s", interval/Minute, tabInfo.GetName())
	} else if interval < Day {
		playText = fmt.Sprintf("%d小时前一起玩过%s", interval/Hour, tabInfo.GetName())
	} else if interval < Month {
		playText = fmt.Sprintf("%d天前一起玩过%s", interval/Day, tabInfo.GetName())
	} else {
		playText = fmt.Sprintf("1个月前一起玩过%s", tabInfo.GetName())
	}

	return playText
}

// 获取搭子标签
func (m *InviteListMgr) getGamePalTags(ctx context.Context, showUids []uint32) error {
	newCtx, cancel := context.WithTimeout(ctx, time.Millisecond*500)
	defer cancel()

	gamePalIds := make([]string, 0, len(showUids))
	for _, showUid := range showUids {
		gamePalIds = append(gamePalIds, fmt.Sprintf("%d_%d", showUid, m.inviteListReq.TabId))
	}
	gamePalRsp, err := rpc.GamePalClient.GetGamePalCardList(newCtx, &gamePalPb.GetGamePalCardListReq{
		IdList: gamePalIds,
	})
	if err != nil {
		log.ErrorWithCtx(newCtx, "GetGamePalCardList fail, err:%v, gamePalIds:%v", err, gamePalIds)
		return err
	}
	gamePalMap := make(map[uint32]gamePalInfo, len(gamePalRsp.GetCards()))
	for _, v := range gamePalRsp.GetCards() {
		if v.GetState() == gamePalPb.GamePalCardState_GamePalCardStateLit {
			gamePalMap[v.GetUid()] = gamePalInfo{
				Title: v.GetSocialDecl(),
				Tags:  getGamePalBlockText(v.GetProps()),
			}
		} else {
			log.DebugWithCtx(newCtx, "GetGamePalCardList gamePalTags filter, uid:%d, state:%d", v.GetUid(), v.GetState())
		}
	}

	m.gamePalTagsMap = gamePalMap
	return nil
}

func getGamePalBlockText(userProp []*gamePalPb.GamePalBlock) []string {
	var gamePalTags []string
	if len(userProp) == 0 {
		return gamePalTags
	}
	var purposeTags []string
	var playTimeTags []string
	var normalTags []string
	for _, p := range userProp {
		switch p.GetType() {
		case gamePalPb.GamePalBlock_Purpose:
			purpose := ""
			for _, e := range p.GetElems() {
				purpose += e.Name + "/"
			}
			purpose = strings.TrimSuffix(purpose, "/")
			if len(purpose) > 0 {
				if len(purposeTags) == 0 {
					purpose = "想找：" + purpose
				}
				purposeTags = append(purposeTags, purpose)
			}

		case gamePalPb.GamePalBlock_PlayTime:
			playTimeStr := ""
			for _, e := range p.GetElems() {
				playTimeStr += e.Name + "/"
			}
			playTimeStr = strings.TrimSuffix(playTimeStr, "/")
			if len(playTimeStr) > 0 {
				playTimeTags = append(playTimeTags, p.Name+"："+playTimeStr)
			}

		case gamePalPb.GamePalBlock_TypeDefault:
			tempElem := ""
			isUnLimit := false
			for _, e := range p.GetElems() {
				if e.Id == unLimitElemId {
					isUnLimit = true
					break
				}
				tempElem += e.Name + "/"
			}
			if isUnLimit {
				tempElem = p.Name + unLimitElemName
			} else {
				tempElem = strings.TrimSuffix(tempElem, "/")
			}
			if len(tempElem) > 0 {
				if len(normalTags) == 0 && !isUnLimit {
					tempElem = "我是：" + tempElem
				}

				normalTags = append(normalTags, tempElem)
			}
		}
	}
	if len(purposeTags) > 0 {
		gamePalTags = append(gamePalTags, purposeTags...)
	}
	if len(playTimeTags) > 0 {
		gamePalTags = append(gamePalTags, playTimeTags...)
	}
	if len(normalTags) > 0 {
		gamePalTags = append(gamePalTags, normalTags...)
	}
	log.DebugWithCtx(context.Background(), "purpose:%s, playTimeStr:%s, otherPublishInfo:%s", purposeTags, playTimeTags, normalTags)
	return gamePalTags
}

// 获取用户房间筛选标签
func (m *InviteListMgr) getChannelSelectTags(ctx context.Context, showUids []uint32) error {
	newCtx, cancel := context.WithTimeout(ctx, time.Millisecond*500)
	defer cancel()

	rsp, err := rpc.GangupChannelClient.GetChannelListFilterRecord(newCtx, &gangupPb.GetChannelListFilterRecordReq{
		UidList: showUids,
		TabId:   m.inviteListReq.TabId,
	})
	if err != nil {
		log.ErrorWithCtx(newCtx, "getChannelSelectTags GetChannelListFilterRecord fail, err:%v, showUids:%v, tabId:%d", err, showUids, m.inviteListReq.TabId)
		return err
	}
	selectChannelLabelTagsMap := make(map[uint32][]string, len(rsp.GetRecordMap()))
	for showUid, v := range rsp.GetRecordMap() {
		if len(v.ElemTitle) > selectTagsNormalLimit {
			v.ElemTitle = v.ElemTitle[:selectTagsNormalLimit]
		}
		selectLabel := strings.Join(v.ElemTitle, "/")
		if len(selectLabel) == 0 {
			continue
		}
		selectChannelLabelTagsMap[showUid] = append(selectChannelLabelTagsMap[showUid], "想找："+selectLabel)
	}
	m.selectChannelLabelTagsMap = selectChannelLabelTagsMap
	return nil
}

// 获取游戏卡标签
func (m *InviteListMgr) getGameCardLabel(ctx context.Context, showUids []uint32) error {
	newCtx, cancel := context.WithTimeout(ctx, time.Millisecond*500)
	defer cancel()

	tabInfo := m.tabInfoMap[m.inviteListReq.TabId]
	if tabInfo == nil {
		log.InfoWithCtx(newCtx, "getGameCardLabel tabInfo is nil, tabId:%d", m.inviteListReq.TabId)
		return nil
	}
	// 获取游戏卡配置
	cardConfResp, err := rpc.GameCardClient.GetGameCardConfByCardIdFromCache(newCtx, []uint32{tabInfo.GetGameInfo().GetGameCardId()})
	if err != nil {
		log.ErrorWithCtx(newCtx, "getGameCardLabel GetGameCardConfByCardIdFromCache fail, err:%v, gameCardId:%d", err, tabInfo.GetGameInfo().GetGameCardId())
		return err
	}
	if len(cardConfResp) == 0 {
		log.InfoWithCtx(newCtx, "getGameCardLabel cardConfResp is empty, gameCardId:%d", tabInfo.GetGameInfo().GetGameCardId())
		return nil
	}
	gameCardConf := cardConfResp[0]

	// 用户游戏卡
	gameCardInfosMap, err := rpc.GameCardClient.BatGetGameCardMap(newCtx, showUids)
	if err != nil {
		log.ErrorWithCtx(newCtx, "getGameCardLabel BatGetGameCardMap fail, err:%v, uid:%d", err, showUids)
		return err
	}
	gameCardLabelTagsMap := make(map[uint32][]string, len(gameCardInfosMap))
	for showUid, gameCardInfos := range gameCardInfosMap {
		var gameCardInfo *gameCardPb.GameCardInfo
		for _, tempInfo := range gameCardInfos {
			if tempInfo.GetGameCardId() == tabInfo.GetGameInfo().GetGameCardId() {
				gameCardInfo = tempInfo
				break
			}
		}
		if gameCardInfo == nil {
			continue
		}

		optMap := make(map[uint32]*gameCardPb.GameCardOpt, len(gameCardInfo.GetOptList()))
		for _, opt := range gameCardInfo.GetOptList() {
			optMap[opt.GetOptId()] = opt
		}

		tags := make([]string, 0, len(gameCardInfo.GetOptList()))
		for _, opt := range gameCardConf.GetOptConfList() {
			userOpt, ok := optMap[opt.GetOptId()]
			if !ok {
				log.DebugWithCtx(newCtx, "getGameCardLabel, optId:%d not found", opt.GetOptId())
				continue
			}
			var tag string
			if opt.GetOptType() == gameCardPb.GameCardOptType_GAME_CARD_OPT_INPUT {
				var tmp string
				confInputMap := make(map[string]bool, len(opt.GetInputConfValueList()))
				for _, v := range opt.GetInputConfValueList() {
					confInputMap[v.Title] = true
				}
				for _, v := range userOpt.GetInputVal() {
					// 输入类型，用户保存的标题名称和配置的标题名称不一致，不展示
					if !confInputMap[v.GetElemTitle()] {
						continue
					}
					tmp += v.GetElemTitle() + ":" + transInputVal(v.GetElemVal()) + "/"
				}
				if len(tmp) == 0 {
					continue
				}
				tmp = strings.TrimSuffix(tmp, "/")
				tag = opt.GetOptName() + ":" + tmp

			} else {
				if len(userOpt.GetValueList()) == 0 {
					continue
				}
				tag = opt.GetOptName() + ":" + strings.Join(userOpt.GetValueList(), "/")
			}
			if len(tag) == 0 {
				continue
			}
			tags = append(tags, tag)
		}
		gameCardLabelTagsMap[showUid] = tags
	}
	log.DebugWithCtx(newCtx, "getGameCardLabel, gameCardLabelTagsMap:%v", gameCardLabelTagsMap)
	m.gameCardLabelTagsMap = gameCardLabelTagsMap
	return nil
}

func transInputVal(val string) string {
	if len(val) == 0 {
		return ""
	}
	num, err := strconv.Atoi(val)
	if err != nil {
		return ""
	}
	if num < 1000 {
		return val

	} else if num >= 1000 && num <= 9999 {
		m := num % 1000
		if m < 100 {
			return fmt.Sprintf("%dk", num/1000)
		} else {
			return fmt.Sprintf("%d.%dk", num/1000, m/100)
		}

	} else if num >= 10000 && num <= 99999 {
		m := num % 10000
		if m < 1000 {
			return fmt.Sprintf("%dw", num/10000)
		} else {
			return fmt.Sprintf("%d.%dw", num/10000, m/1000)
		}

	} else {
		return "10w+"
	}
}

// 当天列表用户是否已经拒绝过/邀请过
func (m *InviteListMgr) getUserInviteStatus(ctx context.Context, uid uint32, showUids []uint32) error {
	newCtx, cancel := context.WithTimeout(ctx, time.Millisecond*500)
	defer cancel()

	rsp, err := rpc.InviteRoomClient.GetUserInviteStatus(newCtx, &inviteRoomPb.GetUserInviteStatusRequest{
		Uid:        uid,
		InviteUids: showUids,
	})
	if err != nil {
		log.ErrorWithCtx(newCtx, "getUserInviteStatus fail, err:%s, showUids:%d", err.Error(), showUids)
		return err
	}
	userInviteStatusMap := make(map[uint32]uint32)
	for showUid, v := range rsp.GetStatusMap() {
		userInviteStatusMap[showUid] = uint32(v)
	}
	m.userInviteStatusMap = userInviteStatusMap
	return nil
}

// 获取游戏卡信息
func (m *InviteListMgr) getGameCardImg(ctx context.Context, tabIds []uint32) error {
	newCtx, cancel := context.WithTimeout(ctx, time.Millisecond*500)
	defer cancel()
	err := m.getTabInfoMap(newCtx, tabIds)
	if err != nil {
		return err
	}
	gameCardIds := make([]uint32, 0, len(tabIds))
	for _, tabId := range tabIds {
		gameCardIds = append(gameCardIds, m.tabInfoMap[tabId].GetGameInfo().GetGameCardId())
	}

	gameCardInfos, err := rpc.GameCardClient.GetGameCardConfByCardIdFromCache(newCtx, gameCardIds)
	if err != nil {
		log.ErrorWithCtx(newCtx, "getGameCardImg GetGameCardConfByCardIdFromCache fail, err:%v, gameCardIds:%v", err, gameCardIds)
		return err
	}

	gameCardImgMap := make(map[uint32]gameCardImgInfo, len(gameCardInfos))
	for i, gameCardInfo := range gameCardInfos {
		gameCardImgMap[tabIds[i]] = gameCardImgInfo{
			BackgroundImg:      gameCardInfo.GetGameBackImgUrl(),
			BackgroundColorNum: gameCardInfo.GetGameBackColorNum(),
		}
	}
	m.tabGameCardImgMap = gameCardImgMap
	log.InfoWithCtx(newCtx, "getGameCardImg, len(tabIds):%d, len(tabGameCardImgMap):%d", len(tabIds), len(m.tabGameCardImgMap))
	return nil
}
