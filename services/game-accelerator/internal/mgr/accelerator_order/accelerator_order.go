package accelerator_order

import (
	"context"
	"errors"
	account_go "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/monkey-send-chat/monkey_sender"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/game-accelerator"
	"golang.52tt.com/services/game-accelerator/internal/infra/db"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_order/entity"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_order/mongo"
	third_open_api "golang.52tt.com/services/game-accelerator/internal/third-open-api"
	"time"
)

//go:generate ifacemaker -f *.go -s AcceleratorOrderMgr -p accelerator_order -o ./accelerator_order_api.go -i IAcceleratorOrderMgr
//go:generate mockgen -destination=../mocks/accelerator_order.go -package=mgr -source ./accelerator_order_api.go IAcceleratorOrderMgr
type AcceleratorOrderMgr struct {
	appId     string
	appSecret string
	appUrl    string

	orderStore   mongo.IAcceleratorOrderStore
	thirdOpenApi third_open_api.IThirdOpenApi
	accountGoCli account_go.IClient
}

func NewAcceleratorOrderMgr(ctx context.Context, appId, appSecret, appUrl string, mongoDB *db.MongoDB, accountGoCli account_go.IClient,
	thirdOpenApi third_open_api.IThirdOpenApi) (*AcceleratorOrderMgr, error) {

	acceleratorOrderStore, err := mongo.NewAcceleratorOrderStore(ctx, mongoDB)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewAcceleratorOrderStore err: %v", err)
		return nil, err
	}

	return &AcceleratorOrderMgr{
		orderStore:   acceleratorOrderStore,
		appId:        appId,
		appSecret:    appSecret,
		appUrl:       appUrl,
		thirdOpenApi: thirdOpenApi,
		accountGoCli: accountGoCli,
	}, nil
}

func (m *AcceleratorOrderMgr) CreateOrder(ctx context.Context, order *entity.AcceleratorOrder) (string, int, time.Time, error) {
	userInfo, uErr := m.accountGoCli.GetUserByUid(ctx, order.Uid)
	if uErr != nil {
		log.ErrorWithCtx(ctx, "GetUserByUid failed: %v, uid: %d", uErr, order.Uid)
		return "", 0, time.Time{}, third_open_api.UserNotFoundError
	}
	// 初始化订单信息
	order.TTId = userInfo.GetUsername()
	orderId, err := m.orderStore.CreateOrder(ctx, order)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateOrder failed: %v, order: %+v", err, order)
		return "", 0, time.Time{}, third_open_api.CreateOrderError
	}
	// 调用授权接口
	authCode, createTime, err := m.Authorize(ctx, userInfo.GetUsername(), order.ExpireTime.In(time.Local).Format(third_open_api.TimeFormat))
	if err != nil {
		log.ErrorWithCtx(ctx, "Authorize failed: %v, order: %+v", err, order)
		return "", 0, time.Time{}, err
	}

	log.InfoWithCtx(ctx, "CreateOrder success, authCode:%d, createTime:%v, order:%+v", authCode, createTime, order)
	return orderId, authCode, createTime, nil
}

func (m *AcceleratorOrderMgr) UpdateOrderStatus(ctx context.Context, orderId string, authCodeId int,
	thirdCreateTime time.Time, status pb.AcceleratorOrderStatus, remark string) error {

	if orderId == "" {
		return errors.New("order_id cannot be empty")
	}
	switch status {
	case pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_SUCCESS:
		err := m.orderStore.UpdateOrderSuccessStatus(ctx, orderId, authCodeId, thirdCreateTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateOrderStatus failed: %v, order_id: %s, status: %s", err, orderId, status.String())
			return err
		}
	case pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_FAIL, pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_INVALID:
		err := m.orderStore.UpdateOrderFailStatus(ctx, orderId, status, remark)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateOrderStatus failed: %v, order_id: %s, status: %s", err, orderId, status.String())
			return err
		}
	}

	return nil
}

func (m *AcceleratorOrderMgr) Authorize(ctx context.Context, imei, expireTime string) (int, time.Time, error) {
	request := &third_open_api.AuthorizeRequest{
		ApiBaseRequest: third_open_api.ApiBaseRequest{
			Appid:     m.appId,
			Appsecret: m.appSecret,
			OsType:    uint32(third_open_api.Windows),
			Imei:      imei,
		},
		ExpireTime: expireTime,
	}
	rsp, err := m.thirdOpenApi.Authorize(ctx, request)
	if err != nil {
		log.ErrorWithCtx(ctx, "authorize response fail, imei:%s, expireTime:%s, rsp:%+v", imei, expireTime, rsp)
		return 0, time.Time{}, err
	}

	if rsp.Code != third_open_api.SuccessCode {
		log.WarnWithCtx(ctx, "authorize response error code, imei:%s, expireTime:%s, rsp:%+v", imei, expireTime, rsp)
		return rsp.Data.OrgAuthCodeId, time.Time{}, protocol.NewExactServerError(nil, status.ErrAcceleratorAuthorizeFail, rsp.Message)
	}

	if rsp.Data.OrgAuthCodeId <= 0 {
		log.ErrorWithCtx(ctx, "authorize fail, invalid orgAuthCodeId , imei:%s, expireTime:%s, rsp:%+v", imei, expireTime, rsp)
		return 0, time.Time{}, protocol.NewExactServerError(nil, status.ErrAcceleratorAuthorizeFail, "org_auth_code_id is invalid")
	}

	var createTime time.Time
	if rsp.Data.CreatedAt != "" {
		tCreateTime, err := time.ParseInLocation(third_open_api.TimeFormat, rsp.Data.CreatedAt, time.Local)
		if err != nil {
			log.ErrorWithCtx(ctx, "Parse createAt fail, err:%v, rsp:%+v", err, rsp)
			return rsp.Data.OrgAuthCodeId, time.Time{}, err
		}
		createTime = tCreateTime
	}
	log.InfoWithCtx(ctx, "authorize success, imei:%s, expireTime:%s, rsp:%+v", imei, expireTime, rsp)
	return rsp.Data.OrgAuthCodeId, createTime, nil
}

// GetOrder 根据订单ID获取订单
func (m *AcceleratorOrderMgr) GetOrder(ctx context.Context, orderId string) (*entity.AcceleratorOrder, error) {
	if orderId == "" {
		return nil, errors.New("order_id cannot be empty")
	}
	return m.orderStore.GetOrder(ctx, orderId)
}

// GetNewbieOrderByUid 根据uid获取用户的新人套餐订单
func (m *AcceleratorOrderMgr) GetNewbieOrderByUid(ctx context.Context, uid uint32) (*entity.AcceleratorOrder, error) {
	return m.orderStore.GetOrderByUidAndOrderType(ctx, uid, pb.AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_NEWBIE)
}

func (m *AcceleratorOrderMgr) GetNewbieOrderByUids(ctx context.Context, uids []uint32) ([]*entity.AcceleratorOrder, error) {
	orders, err := m.orderStore.GetOrderByUidsAndOrderType(ctx, uids, pb.AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_NEWBIE)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewbieOrderByUids GetOrderByUidAndOrderType error: %v, uid: %d", err, uids)
		return nil, err
	}
	return orders, nil
}

func (m *AcceleratorOrderMgr) GetOrdersByAdmin(ctx context.Context, req *pb.GetAcceleratorOrderReq) ([]*entity.AcceleratorOrder, int64, error) {
	// 设置默认分页参数
	limit := req.GetPageSize()
	if limit <= 0 || limit > 100 {
		limit = 20
	}
	skip := (req.GetPage() - 1) * limit
	if skip < 0 {
		skip = 0
	}
	orders, err := m.orderStore.GetOrdersByAdmin(ctx, req.GetThirdPartyOrderId(), req.GetUid(), req.GetStatus(),
		req.GetStartTime(), req.GetEndTime(), int64(limit), int64(skip))
	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	total, err := m.orderStore.CountOrdersByAdmin(ctx, req.GetThirdPartyOrderId(), req.GetUid(), req.GetStatus(), req.GetStartTime(), req.GetEndTime())
	if err != nil {
		return nil, 0, nil
	}
	return orders, total, nil
}

func (m *AcceleratorOrderMgr) GetFailOrders(ctx context.Context) ([]*entity.AcceleratorOrder, error) {
	orders, err := m.orderStore.GetFailOrders(ctx)
	if err != nil {
		return nil, err
	}
	return orders, nil
}

// SendWarningMsg 实时告警
func (m *AcceleratorOrderMgr) SendWarningMsg(ctx context.Context, msg string) error {
	// 用户线告警群
	log.InfoWithCtx(ctx, "sendWarningMsg", msg)
	err := monkey_sender.GetNumMsgSenderByChatId("oc_011ac28df0198ead399462f6e64cca51", 10).SendMsg("加速器授权下单报错", msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendWarningMsg monkey_sender fallback SendMsg err:%v, msg:%s", err, msg)
		return err
	}
	return nil
}
