package mongo

import (
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/writeconcern"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/game-accelerator"
	"strconv"

	"golang.52tt.com/services/game-accelerator/internal/infra/db"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_order/entity"
	"time"
)

const (
	orderCollection = "accelerator_orders"
)

//go:generate ifacemaker -f *.go -s AcceleratorOrderStore -p mongo -o ./accelerator_order_mongo_api.go -i IAcceleratorOrderStore
//go:generate mockgen -destination=../mocks/accelerator_order_mongo.go -package=accelerator_order -source ./accelerator_order_mongo_api.go IAcceleratorOrderStore
type AcceleratorOrderStore struct {
	order *mongo.Collection
}

func NewAcceleratorOrderStore(ctx context.Context, db *db.MongoDB) (*AcceleratorOrderStore, error) {

	colOption := options.Collection().SetWriteConcern(writeconcern.New(writeconcern.WMajority(), writeconcern.J(true)))
	st := &AcceleratorOrderStore{
		order: db.Database().Collection(orderCollection, colOption),
	}
	// 创建索引
	if err := st.createIndexes(ctx); err != nil {
		log.ErrorWithCtx(ctx, "NewAcceleratorOrderStore createIndexes failed: %v", err)
		return nil, err
	}

	return st, nil
}

// createIndexes 创建索引
func (s *AcceleratorOrderStore) createIndexes(ctx context.Context) error {
	indexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				primitive.E{Key: "third_create_time", Value: -1},
			},
		},
		{
			Keys: bson.D{
				primitive.E{Key: "status", Value: 1},
				primitive.E{Key: "third_create_time", Value: -1},
			},
		},
		{
			Keys: bson.D{
				primitive.E{Key: "org_auth_code_id", Value: 1},
			},
			Options: options.Index(),
		},
		{
			Keys: bson.D{
				primitive.E{Key: "uid", Value: 1},
			},
			Options: options.Index(),
		},
		{
			Keys: bson.D{
				primitive.E{Key: "out_trade_order_id", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
	}

	_, err := s.order.Indexes().CreateMany(ctx, indexes)
	return err
}

func (s *AcceleratorOrderStore) CreateOrder(ctx context.Context, order *entity.AcceleratorOrder) (string, error) {
	res, err := s.order.InsertOne(ctx, order)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateOrder failed: %v, order: %+v", err, order)
		return "", err
	}
	return res.InsertedID.(string), nil
}

// GetOrder 根据订单ID获取订单
func (s *AcceleratorOrderStore) GetOrder(ctx context.Context, orderId string) (*entity.AcceleratorOrder, error) {
	var order entity.AcceleratorOrder
	err := s.order.FindOne(ctx, bson.M{"_id": orderId}).Decode(&order)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetOrder failed: %v, orderId: %s", err, orderId)
		return nil, err
	}

	return &order, nil
}

func (s *AcceleratorOrderStore) GetOrderByUidAndOrderType(ctx context.Context, uid uint32, orderType pb.AcceleratorOrderType) (*entity.AcceleratorOrder, error) {
	var order entity.AcceleratorOrder
	err := s.order.FindOne(ctx, bson.M{"uid": uid, "order_type": orderType}).Decode(&order)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetOrderByUidAndOrderType failed: %v, uid: %d, orderType: %v", err, uid, orderType)
		return nil, err
	}

	return &order, nil
}

func (s *AcceleratorOrderStore) GetOrderByUidsAndOrderType(ctx context.Context, uids []uint32, orderType pb.AcceleratorOrderType) ([]*entity.AcceleratorOrder, error) {
	cursor, err := s.order.Find(ctx, bson.M{"uid": bson.M{"$in": uids}, "order_type": orderType})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderByUidsAndOrderType failed: %v", err)
		return nil, err
	}
	var orders []*entity.AcceleratorOrder
	err = cursor.All(ctx, &orders)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderByUidsAndOrderType decode failed: %v", err)
		return nil, err
	}
	return orders, nil
}

func generateFilterByStatus(thirdOrderId string, uid uint32, status pb.AcceleratorOrderStatus, startTimeUx, endTimeUx int64) bson.M {
	filter := bson.M{}
	if thirdOrderId == "" && uid == 0 {
		filter = bson.M{
			"third_create_time": bson.M{
				"$gte": time.Now().AddDate(0, 0, -7), // 最近7天
			},
		}
	}

	if startTimeUx > 0 {
		startTime := time.Unix(startTimeUx, 0)
		filter["third_create_time"] = bson.M{"$gte": startTime}
	}
	if endTimeUx > 0 {
		endTime := time.Unix(endTimeUx, 0)
		filter["third_create_time"].(bson.M)["$lte"] = endTime
	}

	if thirdOrderId != "" {
		orgAuthCodeId, err := strconv.Atoi(thirdOrderId)
		if err != nil {
			log.ErrorWithCtx(context.Background(), "generateFilterByStatus failed to convert thirdOrderId to int: %v, thirdOrderId: %s", err, thirdOrderId)
		} else {
			filter["org_auth_code_id"] = orgAuthCodeId
		}
	}
	if uid > 0 {
		filter["uid"] = uid
	}
	if status != pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_UNSPECIFIED {
		filter["status"] = status
	}
	return filter
}

func (s *AcceleratorOrderStore) CountOrdersByAdmin(ctx context.Context, thirdOrderId string, uid uint32, status pb.AcceleratorOrderStatus,
	startTime, endTime int64) (int64, error) {

	filter := generateFilterByStatus(thirdOrderId, uid, status, startTime, endTime)
	count, err := s.order.CountDocuments(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "CountOrdersByAdmin failed: %v, orderId: %s, uid: %d, status: %v", err, thirdOrderId, uid, status)
		return 0, err
	}
	return count, nil
}

func (s *AcceleratorOrderStore) GetOrdersByAdmin(ctx context.Context, thirdOrderId string, uid uint32, status pb.AcceleratorOrderStatus,
	startTime, endTime, limit, skip int64) ([]*entity.AcceleratorOrder, error) {

	filter := generateFilterByStatus(thirdOrderId, uid, status, startTime, endTime)

	opts := options.Find().SetSort(bson.M{"third_create_time": -1}).SetLimit(limit).SetSkip(skip)
	cursor, err := s.order.Find(ctx, filter, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrdersByUid failed: %v, uid: %d", err, uid)
		return nil, err
	}
	var orders []*entity.AcceleratorOrder
	if err := cursor.All(ctx, &orders); err != nil {
		log.ErrorWithCtx(ctx, "GetOrdersByUid decode failed: %v, uid: %d", err, uid)
		return nil, err
	}
	return orders, nil
}

func (s *AcceleratorOrderStore) UpdateOrderSuccessStatus(ctx context.Context, orderId string, orgAuthCodeId int, thirdCreateTime time.Time) error {

	filter := bson.M{"_id": orderId, "status": bson.M{"$in": []pb.AcceleratorOrderStatus{pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_FAIL,
		pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_PENDING}}}

	update := bson.M{"$set": bson.M{
		"status":           pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_SUCCESS,
		"org_auth_code_id": orgAuthCodeId,
	}}
	if !thirdCreateTime.IsZero() {
		update["$set"].(bson.M)["third_create_time"] = thirdCreateTime
	}
	_, err := s.order.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s *AcceleratorOrderStore) UpdateOrderFailStatus(ctx context.Context, orderId string,
	status pb.AcceleratorOrderStatus, remark string) error {

	filter := bson.M{"_id": orderId, "status": bson.M{"$in": []pb.AcceleratorOrderStatus{pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_FAIL,
		pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_PENDING}}}
	update := bson.M{"$set": bson.M{
		"status": status,
	}}
	if remark != "" {
		update["$set"].(bson.M)["remark"] = remark
	}
	_, err := s.order.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s *AcceleratorOrderStore) GetFailOrders(ctx context.Context) ([]*entity.AcceleratorOrder, error) {
	filter := bson.M{
		"status": bson.M{"$in": []pb.AcceleratorOrderStatus{pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_FAIL,
			pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_PENDING}},
		"third_create_time": bson.M{
			"$gte": time.Now().AddDate(0, 0, -3),
			"$lt":  time.Now().Add(-time.Second * 30), // 最近3天内创建的订单，且不超过30秒前的订单
		},
	}
	cursor, err := s.order.Find(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFailOrders failed: %v", err)
		return nil, err
	}
	var orders []*entity.AcceleratorOrder
	if err := cursor.All(ctx, &orders); err != nil {
		log.ErrorWithCtx(ctx, "GetFailOrders decode failed: %v", err)
		return nil, err
	}
	return orders, nil
}
