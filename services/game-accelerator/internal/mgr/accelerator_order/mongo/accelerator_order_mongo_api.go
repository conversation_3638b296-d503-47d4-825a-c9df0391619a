// Code generated by ifacemaker; DO NOT EDIT.

package mongo

import (
	"context"
	"time"

	pb "golang.52tt.com/protocol/services/game-accelerator"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_order/entity"
)

// IAcceleratorOrderStore ...
type IAcceleratorOrderStore interface {
	CreateOrder(ctx context.Context, order *entity.AcceleratorOrder) (string, error)
	// GetOrder 根据订单ID获取订单
	GetOrder(ctx context.Context, orderId string) (*entity.AcceleratorOrder, error)
	GetOrderByUidAndOrderType(ctx context.Context, uid uint32, orderType pb.AcceleratorOrderType) (*entity.AcceleratorOrder, error)
	GetOrderByUidsAndOrderType(ctx context.Context, uids []uint32, orderType pb.AcceleratorOrderType) ([]*entity.AcceleratorOrder, error)
	CountOrdersByAdmin(ctx context.Context, thirdOrderId string, uid uint32, status pb.AcceleratorOrderStatus, startTime, endTime int64) (int64, error)
	GetOrdersByAdmin(ctx context.Context, thirdOrderId string, uid uint32, status pb.AcceleratorOrderStatus, startTime, endTime, limit, skip int64) ([]*entity.AcceleratorOrder, error)
	UpdateOrderSuccessStatus(ctx context.Context, orderId string, orgAuthCodeId int, thirdCreateTime time.Time) error
	UpdateOrderFailStatus(ctx context.Context, orderId string, status pb.AcceleratorOrderStatus, remark string) error
	GetFailOrders(ctx context.Context) ([]*entity.AcceleratorOrder, error)
}
