// Code generated by ifacemaker; DO NOT EDIT.

package accelerator_order

import (
	"context"
	"time"

	pb "golang.52tt.com/protocol/services/game-accelerator"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_order/entity"
)

// IAcceleratorOrderMgr ...
type IAcceleratorOrderMgr interface {
	CreateOrder(ctx context.Context, order *entity.AcceleratorOrder) (string, int, time.Time, error)
	UpdateOrderStatus(ctx context.Context, orderId string, authCodeId int, thirdCreateTime time.Time, status pb.AcceleratorOrderStatus, remark string) error
	Authorize(ctx context.Context, imei, expireTime string) (int, time.Time, error)
	// GetOrder 根据订单ID获取订单
	GetOrder(ctx context.Context, orderId string) (*entity.AcceleratorOrder, error)
	// GetNewbieOrderByUid 根据uid获取用户的新人套餐订单
	GetNewbieOrderByUid(ctx context.Context, uid uint32) (*entity.AcceleratorOrder, error)
	GetNewbieOrderByUids(ctx context.Context, uids []uint32) ([]*entity.AcceleratorOrder, error)
	GetOrdersByAdmin(ctx context.Context, req *pb.GetAcceleratorOrderReq) ([]*entity.AcceleratorOrder, int64, error)
	GetFailOrders(ctx context.Context) ([]*entity.AcceleratorOrder, error)
	// SendWarningMsg 实时告警
	SendWarningMsg(ctx context.Context, msg string) error
}
