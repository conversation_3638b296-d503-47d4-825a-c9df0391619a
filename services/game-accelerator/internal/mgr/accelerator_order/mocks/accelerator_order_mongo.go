// Code generated by MockGen. DO NOT EDIT.
// Source: ./accelerator_order_mongo_api.go

// Package accelerator_order is a generated GoMock package.
package accelerator_order

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	game_accelerator "golang.52tt.com/protocol/services/game-accelerator"
	entity "golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_order/entity"
)

// MockIAcceleratorOrderStore is a mock of IAcceleratorOrderStore interface.
type MockIAcceleratorOrderStore struct {
	ctrl     *gomock.Controller
	recorder *MockIAcceleratorOrderStoreMockRecorder
}

// MockIAcceleratorOrderStoreMockRecorder is the mock recorder for MockIAcceleratorOrderStore.
type MockIAcceleratorOrderStoreMockRecorder struct {
	mock *MockIAcceleratorOrderStore
}

// NewMockIAcceleratorOrderStore creates a new mock instance.
func NewMockIAcceleratorOrderStore(ctrl *gomock.Controller) *MockIAcceleratorOrderStore {
	mock := &MockIAcceleratorOrderStore{ctrl: ctrl}
	mock.recorder = &MockIAcceleratorOrderStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAcceleratorOrderStore) EXPECT() *MockIAcceleratorOrderStoreMockRecorder {
	return m.recorder
}

// CountOrdersByAdmin mocks base method.
func (m *MockIAcceleratorOrderStore) CountOrdersByAdmin(ctx context.Context, thirdOrderId string, uid uint32, status game_accelerator.AcceleratorOrderStatus, startTime, endTime int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountOrdersByAdmin", ctx, thirdOrderId, uid, status, startTime, endTime)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountOrdersByAdmin indicates an expected call of CountOrdersByAdmin.
func (mr *MockIAcceleratorOrderStoreMockRecorder) CountOrdersByAdmin(ctx, thirdOrderId, uid, status, startTime, endTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountOrdersByAdmin", reflect.TypeOf((*MockIAcceleratorOrderStore)(nil).CountOrdersByAdmin), ctx, thirdOrderId, uid, status, startTime, endTime)
}

// CreateOrder mocks base method.
func (m *MockIAcceleratorOrderStore) CreateOrder(ctx context.Context, order *entity.AcceleratorOrder) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrder", ctx, order)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrder indicates an expected call of CreateOrder.
func (mr *MockIAcceleratorOrderStoreMockRecorder) CreateOrder(ctx, order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrder", reflect.TypeOf((*MockIAcceleratorOrderStore)(nil).CreateOrder), ctx, order)
}

// GetFailOrders mocks base method.
func (m *MockIAcceleratorOrderStore) GetFailOrders(ctx context.Context) ([]*entity.AcceleratorOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFailOrders", ctx)
	ret0, _ := ret[0].([]*entity.AcceleratorOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFailOrders indicates an expected call of GetFailOrders.
func (mr *MockIAcceleratorOrderStoreMockRecorder) GetFailOrders(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFailOrders", reflect.TypeOf((*MockIAcceleratorOrderStore)(nil).GetFailOrders), ctx)
}

// GetOrder mocks base method.
func (m *MockIAcceleratorOrderStore) GetOrder(ctx context.Context, orderId string) (*entity.AcceleratorOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrder", ctx, orderId)
	ret0, _ := ret[0].(*entity.AcceleratorOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrder indicates an expected call of GetOrder.
func (mr *MockIAcceleratorOrderStoreMockRecorder) GetOrder(ctx, orderId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrder", reflect.TypeOf((*MockIAcceleratorOrderStore)(nil).GetOrder), ctx, orderId)
}

// GetOrderByUidAndOrderType mocks base method.
func (m *MockIAcceleratorOrderStore) GetOrderByUidAndOrderType(ctx context.Context, uid uint32, orderType game_accelerator.AcceleratorOrderType) (*entity.AcceleratorOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderByUidAndOrderType", ctx, uid, orderType)
	ret0, _ := ret[0].(*entity.AcceleratorOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderByUidAndOrderType indicates an expected call of GetOrderByUidAndOrderType.
func (mr *MockIAcceleratorOrderStoreMockRecorder) GetOrderByUidAndOrderType(ctx, uid, orderType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderByUidAndOrderType", reflect.TypeOf((*MockIAcceleratorOrderStore)(nil).GetOrderByUidAndOrderType), ctx, uid, orderType)
}

// GetOrderByUidsAndOrderType mocks base method.
func (m *MockIAcceleratorOrderStore) GetOrderByUidsAndOrderType(ctx context.Context, uids []uint32, orderType game_accelerator.AcceleratorOrderType) ([]*entity.AcceleratorOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderByUidsAndOrderType", ctx, uids, orderType)
	ret0, _ := ret[0].([]*entity.AcceleratorOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderByUidsAndOrderType indicates an expected call of GetOrderByUidsAndOrderType.
func (mr *MockIAcceleratorOrderStoreMockRecorder) GetOrderByUidsAndOrderType(ctx, uids, orderType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderByUidsAndOrderType", reflect.TypeOf((*MockIAcceleratorOrderStore)(nil).GetOrderByUidsAndOrderType), ctx, uids, orderType)
}

// GetOrdersByAdmin mocks base method.
func (m *MockIAcceleratorOrderStore) GetOrdersByAdmin(ctx context.Context, thirdOrderId string, uid uint32, status game_accelerator.AcceleratorOrderStatus, startTime, endTime, limit, skip int64) ([]*entity.AcceleratorOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrdersByAdmin", ctx, thirdOrderId, uid, status, startTime, endTime, limit, skip)
	ret0, _ := ret[0].([]*entity.AcceleratorOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrdersByAdmin indicates an expected call of GetOrdersByAdmin.
func (mr *MockIAcceleratorOrderStoreMockRecorder) GetOrdersByAdmin(ctx, thirdOrderId, uid, status, startTime, endTime, limit, skip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrdersByAdmin", reflect.TypeOf((*MockIAcceleratorOrderStore)(nil).GetOrdersByAdmin), ctx, thirdOrderId, uid, status, startTime, endTime, limit, skip)
}

// UpdateOrderFailStatus mocks base method.
func (m *MockIAcceleratorOrderStore) UpdateOrderFailStatus(ctx context.Context, orderId string, status game_accelerator.AcceleratorOrderStatus, remark string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrderFailStatus", ctx, orderId, status, remark)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrderFailStatus indicates an expected call of UpdateOrderFailStatus.
func (mr *MockIAcceleratorOrderStoreMockRecorder) UpdateOrderFailStatus(ctx, orderId, status, remark interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderFailStatus", reflect.TypeOf((*MockIAcceleratorOrderStore)(nil).UpdateOrderFailStatus), ctx, orderId, status, remark)
}

// UpdateOrderSuccessStatus mocks base method.
func (m *MockIAcceleratorOrderStore) UpdateOrderSuccessStatus(ctx context.Context, orderId string, orgAuthCodeId int, thirdCreateTime time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrderSuccessStatus", ctx, orderId, orgAuthCodeId, thirdCreateTime)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrderSuccessStatus indicates an expected call of UpdateOrderSuccessStatus.
func (mr *MockIAcceleratorOrderStoreMockRecorder) UpdateOrderSuccessStatus(ctx, orderId, orgAuthCodeId, thirdCreateTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderSuccessStatus", reflect.TypeOf((*MockIAcceleratorOrderStore)(nil).UpdateOrderSuccessStatus), ctx, orderId, orgAuthCodeId, thirdCreateTime)
}
