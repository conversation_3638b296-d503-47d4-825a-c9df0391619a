package entity

import (
	pb "golang.52tt.com/protocol/services/game-accelerator"
	"strconv"
	"time"
)

type AcceleratorOrder struct {
	OrderId         string                     `bson:"_id"`
	Uid             uint32                     `bson:"uid"`                // 用户id
	TTId            string                     `bson:"tt_id"`              // 用户TTID，授权的唯一设备标识
	OrgAuthCodeId   int                        `bson:"org_auth_code_id"`   // 第三方授权码订单ID
	OrderType       pb.AcceleratorOrderType    `bson:"order_type"`         // 订单类型
	OrderCount      uint32                     `bson:"order_count"`        // 申请数量
	Status          pb.AcceleratorOrderStatus  `bson:"status"`             // 授权结果状态
	PlatformType    pb.AcceleratorPlatformType `bson:"platform_type"`      // 平台类型
	Operator        string                     `bson:"operator"`           // 人工操作：显示工号; 系统操作：显示业务ID
	Remark          string                     `bson:"remark"`             // 申请授权填写的原因
	OutTradeOrderId string                     `bson:"out_trade_order_id"` // 业务调用方的订单号
	ExpireTime      time.Time                  `bson:"expire_time"`        // 订单到期时间
	ThirdCreateTime time.Time                  `bson:"third_create_time"`  // 创建时间, 订单成功的话使用的是第三方返回的创建时间
	CreateTime      time.Time                  `bson:"create_time"`        // 创建时间
}

func (order *AcceleratorOrder) ToPB() *pb.AcceleratorOrderItem {
	return &pb.AcceleratorOrderItem{
		OrderId:           order.OrderId,
		Uid:               order.Uid,
		OrderType:         order.OrderType,
		OrderCount:        order.OrderCount,
		Status:            order.Status,
		CreateTime:        order.ThirdCreateTime.Unix(),
		ExpireTime:        order.ExpireTime.Unix(),
		PlatformType:      order.PlatformType,
		Operator:          order.Operator,
		Remark:            order.Remark,
		OutTradeOrderId:   order.OutTradeOrderId,
		ThirdPartyOrderId: strconv.Itoa(order.OrgAuthCodeId),
	}
}