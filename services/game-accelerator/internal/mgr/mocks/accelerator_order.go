// Code generated by MockGen. DO NOT EDIT.
// Source: ./accelerator_order_api.go

// Package mgr is a generated GoMock package.
package mgr

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	game_accelerator "golang.52tt.com/protocol/services/game-accelerator"
	entity "golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_order/entity"
)

// MockIAcceleratorOrderMgr is a mock of IAcceleratorOrderMgr interface.
type MockIAcceleratorOrderMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIAcceleratorOrderMgrMockRecorder
}

// MockIAcceleratorOrderMgrMockRecorder is the mock recorder for MockIAcceleratorOrderMgr.
type MockIAcceleratorOrderMgrMockRecorder struct {
	mock *MockIAcceleratorOrderMgr
}

// NewMockIAcceleratorOrderMgr creates a new mock instance.
func NewMockIAcceleratorOrderMgr(ctrl *gomock.Controller) *MockIAcceleratorOrderMgr {
	mock := &MockIAcceleratorOrderMgr{ctrl: ctrl}
	mock.recorder = &MockIAcceleratorOrderMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAcceleratorOrderMgr) EXPECT() *MockIAcceleratorOrderMgrMockRecorder {
	return m.recorder
}

// Authorize mocks base method.
func (m *MockIAcceleratorOrderMgr) Authorize(ctx context.Context, imei, expireTime string) (int, time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Authorize", ctx, imei, expireTime)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(time.Time)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Authorize indicates an expected call of Authorize.
func (mr *MockIAcceleratorOrderMgrMockRecorder) Authorize(ctx, imei, expireTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Authorize", reflect.TypeOf((*MockIAcceleratorOrderMgr)(nil).Authorize), ctx, imei, expireTime)
}

// CreateOrder mocks base method.
func (m *MockIAcceleratorOrderMgr) CreateOrder(ctx context.Context, order *entity.AcceleratorOrder) (string, int, time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrder", ctx, order)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(time.Time)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// CreateOrder indicates an expected call of CreateOrder.
func (mr *MockIAcceleratorOrderMgrMockRecorder) CreateOrder(ctx, order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrder", reflect.TypeOf((*MockIAcceleratorOrderMgr)(nil).CreateOrder), ctx, order)
}

// GetFailOrders mocks base method.
func (m *MockIAcceleratorOrderMgr) GetFailOrders(ctx context.Context) ([]*entity.AcceleratorOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFailOrders", ctx)
	ret0, _ := ret[0].([]*entity.AcceleratorOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFailOrders indicates an expected call of GetFailOrders.
func (mr *MockIAcceleratorOrderMgrMockRecorder) GetFailOrders(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFailOrders", reflect.TypeOf((*MockIAcceleratorOrderMgr)(nil).GetFailOrders), ctx)
}

// GetNewbieOrderByUid mocks base method.
func (m *MockIAcceleratorOrderMgr) GetNewbieOrderByUid(ctx context.Context, uid uint32) (*entity.AcceleratorOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewbieOrderByUid", ctx, uid)
	ret0, _ := ret[0].(*entity.AcceleratorOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieOrderByUid indicates an expected call of GetNewbieOrderByUid.
func (mr *MockIAcceleratorOrderMgrMockRecorder) GetNewbieOrderByUid(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieOrderByUid", reflect.TypeOf((*MockIAcceleratorOrderMgr)(nil).GetNewbieOrderByUid), ctx, uid)
}

// GetNewbieOrderByUids mocks base method.
func (m *MockIAcceleratorOrderMgr) GetNewbieOrderByUids(ctx context.Context, uids []uint32) ([]*entity.AcceleratorOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewbieOrderByUids", ctx, uids)
	ret0, _ := ret[0].([]*entity.AcceleratorOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewbieOrderByUids indicates an expected call of GetNewbieOrderByUids.
func (mr *MockIAcceleratorOrderMgrMockRecorder) GetNewbieOrderByUids(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewbieOrderByUids", reflect.TypeOf((*MockIAcceleratorOrderMgr)(nil).GetNewbieOrderByUids), ctx, uids)
}

// GetOrder mocks base method.
func (m *MockIAcceleratorOrderMgr) GetOrder(ctx context.Context, orderId string) (*entity.AcceleratorOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrder", ctx, orderId)
	ret0, _ := ret[0].(*entity.AcceleratorOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrder indicates an expected call of GetOrder.
func (mr *MockIAcceleratorOrderMgrMockRecorder) GetOrder(ctx, orderId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrder", reflect.TypeOf((*MockIAcceleratorOrderMgr)(nil).GetOrder), ctx, orderId)
}

// GetOrdersByAdmin mocks base method.
func (m *MockIAcceleratorOrderMgr) GetOrdersByAdmin(ctx context.Context, req *game_accelerator.GetAcceleratorOrderReq) ([]*entity.AcceleratorOrder, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrdersByAdmin", ctx, req)
	ret0, _ := ret[0].([]*entity.AcceleratorOrder)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetOrdersByAdmin indicates an expected call of GetOrdersByAdmin.
func (mr *MockIAcceleratorOrderMgrMockRecorder) GetOrdersByAdmin(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrdersByAdmin", reflect.TypeOf((*MockIAcceleratorOrderMgr)(nil).GetOrdersByAdmin), ctx, req)
}

// SendWarningMsg mocks base method.
func (m *MockIAcceleratorOrderMgr) SendWarningMsg(ctx context.Context, msg string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendWarningMsg", ctx, msg)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendWarningMsg indicates an expected call of SendWarningMsg.
func (mr *MockIAcceleratorOrderMgrMockRecorder) SendWarningMsg(ctx, msg interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendWarningMsg", reflect.TypeOf((*MockIAcceleratorOrderMgr)(nil).SendWarningMsg), ctx, msg)
}

// UpdateOrderStatus mocks base method.
func (m *MockIAcceleratorOrderMgr) UpdateOrderStatus(ctx context.Context, orderId string, authCodeId int, thirdCreateTime time.Time, status game_accelerator.AcceleratorOrderStatus, remark string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrderStatus", ctx, orderId, authCodeId, thirdCreateTime, status, remark)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrderStatus indicates an expected call of UpdateOrderStatus.
func (mr *MockIAcceleratorOrderMgrMockRecorder) UpdateOrderStatus(ctx, orderId, authCodeId, thirdCreateTime, status, remark interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderStatus", reflect.TypeOf((*MockIAcceleratorOrderMgr)(nil).UpdateOrderStatus), ctx, orderId, authCodeId, thirdCreateTime, status, remark)
}
