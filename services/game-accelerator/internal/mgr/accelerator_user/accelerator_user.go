package accelerator_user

import (
	"context"
	bizLabel "gitlab.ttyuyin.com/bizFund/bizFund/pkg/push-notification"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	pushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	game_accelerator_logic "golang.52tt.com/protocol/app/game-accelerator-logic"
	gapush "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/game-accelerator"
	pushPb "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/game-accelerator/internal/infra/db"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_user/cache"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_user/cache/redis"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_user/entity"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_user/mongo"
	third_open_api "golang.52tt.com/services/game-accelerator/internal/third-open-api"
	"strconv"
	"time"
)

//go:generate ifacemaker -f *.go -s AcceleratorUserMgr -p accelerator_user -o ./accelerator_user_api.go -i IAcceleratorUserMgr
//go:generate mockgen -destination=../mocks/accelerator_user.go -package=mgr -source ./accelerator_user_api.go IAcceleratorUserMgr
type AcceleratorUserMgr struct {
	userStore mongo.IAcceleratorUserStore
	cache     cache.AcceleratorTokenCache

	appId        string
	appSecret    string
	appUrl       string
	thirdOpenApi third_open_api.IThirdOpenApi
	pushClient   *pushNotification.Client
}

func NewAcceleratorUserMgr(ctx context.Context, appId, appSecret, appUrl string, mongoDB *db.MongoDB, redisDB *db.RedisDB, thirdOpenApi third_open_api.IThirdOpenApi) (*AcceleratorUserMgr, error) {
	userStore, err := mongo.NewAcceleratorUserStore(ctx, mongoDB)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewAcceleratorOrderStore err: %v", err)
		return nil, err
	}

	redisCache := redis.NewAcceleratorTokenCache(redisDB)

	pushClient, _ := pushNotification.NewClient()

	return &AcceleratorUserMgr{
		userStore:    userStore,
		cache:        redisCache,
		appId:        appId,
		appSecret:    appSecret,
		appUrl:       appUrl,
		thirdOpenApi: thirdOpenApi,
		pushClient:   pushClient,
	}, nil
}

func (m *AcceleratorUserMgr) UpsertUser(ctx context.Context, data *entity.AcceleratorUser) error {
	err := m.userStore.UpsertUser(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (m *AcceleratorUserMgr) GetUser(ctx context.Context, uid uint32) (*entity.AcceleratorUser, error) {
	info, err := m.userStore.GetUser(ctx, uid)
	if err != nil {
		return nil, err
	}
	return info, nil
}

func (m *AcceleratorUserMgr) GetUsers(ctx context.Context, uids []uint32) ([]*entity.AcceleratorUser, error) {
	info, err := m.userStore.GetUsers(ctx, uids)
	if err != nil {
		return nil, err
	}
	return info, nil
}

func (m *AcceleratorUserMgr) GetExpireUsers(ctx context.Context) ([]*entity.AcceleratorUser, error) {
	info, err := m.userStore.GetExpireUsers(ctx)
	if err != nil {
		return nil, err
	}
	return info, nil
}

// UpdateUserExpireStatus 定时任务更新用户状态
func (m *AcceleratorUserMgr) UpdateUserExpireStatus(ctx context.Context, uids []uint32) error {
	err := m.userStore.UpdateUserExpireStatus(ctx, uids)
	if err != nil {
		return err
	}
	return nil
}

func (m *AcceleratorUserMgr) SearchUsers(ctx context.Context, req *pb.GetAcceleratorUserListReq) ([]*entity.AcceleratorUser, int64, string, error) {
	var (
		limit = 20
		total = int64(0)
		err   error
	)

	if req.GetLastId() == "" {
		total, err = m.userStore.CountDocuments(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "CountDocuments failed: %v", err)
			return nil, 0, "", err
		}
	}

	data, lastSortId, err := m.userStore.SearchUsers(ctx, req, req.GetLastId(), int64(limit))
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchUsers failed: %v", err)
		return nil, 0, "", err
	}
	return data, total, lastSortId, nil
}

// UpdateUserAcceleratorStatus 运营后台操作
func (m *AcceleratorUserMgr) UpdateUserAcceleratorStatus(ctx context.Context, uid uint32, action pb.BanUserUseAcceleratorReq_Action) error {
	user, err := m.userStore.GetUser(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "BanUserUseAccelerator GetUser failed: %v, uid: %d", err, uid)
		return err
	}
	if user == nil {
		log.ErrorWithCtx(ctx, "BanUserUseAccelerator user not found, uid: %d", uid)
		return protocol.NewExactServerError(nil, status.ErrBadRequest, "user not found")
	}

	userStatus := pb.UserStatus_USER_STATUS_BAN
	// 解禁的时候判断用户是否已经过期了，是的话改成过期状态
	if action == pb.BanUserUseAcceleratorReq_ACTION_UNBAN {
		userStatus = pb.UserStatus_USER_STATUS_NORMAL
		if user.MembershipExpireTime.Before(time.Now()) {
			userStatus = pb.UserStatus_USER_STATUS_EXPIRED
		}
	}
	err = m.userStore.UpdateUserAcceleratorStatus(ctx, uid, userStatus)
	if err != nil {
		return err
	}
	return nil
}

func (m *AcceleratorUserMgr) UserAccelerateStatusChangePush(ctx context.Context, uids []uint32,
	status game_accelerator_logic.UserAcceleratorState) error {

	pushMsg := &game_accelerator_logic.UserAccelerateStatusChangeNotify{
		Status: uint32(status),
	}
	content, err := proto.Marshal(pushMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "proto.Marshal failed, err:%v, pushMsg:%+v", err, pushMsg)
		return err
	}
	payload, err := proto.Marshal(&gapush.PushMessage{
		Cmd:     uint32(gapush.PushMessage_USER_ACCELERATOR_STATUS_CHANGE_NOTIFY),
		Content: content,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "proto.Marshal failed, err:%v, pushMsg:%+v", err, pushMsg)
		return err
	}
	err = m.pushClient.PushToUsers(ctx, uids, &pushPb.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypePolicy: pushNotification.DefaultPolicy,
		ProxyNotification: &pushPb.ProxyNotification{
			Type:      uint32(pushPb.ProxyNotification_PUSH),
			Payload:   payload,
			PushLabel: bizLabel.LabelBanUserUseAccelerator,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PushToUsers failed, err:%v, uids:%v", err, uids)
		return err
	}
	log.InfoWithCtx(ctx, "UserAccelerateStatusChangePush success, uids:%v, status:%d", uids, status)
	return nil
}

func (m *AcceleratorUserMgr) GetUserAcceleratorLog(ctx context.Context,
	req *pb.GetUserAcceleratorLogReq) (*third_open_api.LogsResponse, error) {

	startTime, endTime := "", ""
	if req.GetStartTime() > 0 {
		startTime = time.Unix(req.GetStartTime(), 0).In(time.Local).Format("2006-01-02 15:04:05")
	}
	if req.GetEndTime() > 0 {
		endTime = time.Unix(req.GetEndTime(), 0).In(time.Local).Format("2006-01-02 15:04:05")
	}

	request := &third_open_api.LogsRequest{
		ApiBaseRequest: third_open_api.ApiBaseRequest{
			Appid:     m.appId,
			Appsecret: m.appSecret,
			OsType:    uint32(third_open_api.Windows),
			Imei:      req.GetTtid(),
		},
		AppIds:    req.GetAppIds(),
		StartTime: startTime,
		EndTime:   endTime,
		PerPage:   req.GetPageSize(),
		Page:      req.GetPage(),
	}

	rsp, err := m.thirdOpenApi.GetAcceleratorLog(ctx, request)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAcceleratorLog GetAcceleratorLogs failed: %v, req: %+v", err, request)
		return nil, err
	}
	return rsp, nil
}

func (m *AcceleratorUserMgr) getThirdApiToken(ctx context.Context, imei string, appId string) (string, error) {
	request := &third_open_api.TokenRequest{
		ApiBaseRequest: third_open_api.ApiBaseRequest{
			Appid:     m.appId,
			Appsecret: m.appSecret,
			OsType:    uint32(third_open_api.Windows),
			Imei:      imei,
		},
		AppIds: []string{appId},
	}
	rsp, err := m.thirdOpenApi.GetApiToken(ctx, request)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAcceleratorLog GetAcceleratorLogs failed: %v, req: %+v", err, request)
		return "", err
	}
	if rsp.Code != 0 || rsp.Data.Token == "" {
		log.WarnWithCtx(ctx, "GetAcceleratorToken failed, rsp:%+v", rsp)
		if rsp.Code == third_open_api.FailCode && rsp.Message == third_open_api.GetTokenFailMessage {
			return "", protocol.NewExactServerError(nil, status.ErrAcceleratorNotActive, rsp.Message)
		}
		return "", protocol.NewExactServerError(nil, status.ErrAcceleratorGetTokenFail, rsp.Message)
	}
	return rsp.Data.Token, nil
}

func (m *AcceleratorUserMgr) GetAcceleratorToken(ctx context.Context, req *pb.GetAcceleratorTokenReq) (string, error) {
	user, err := m.userStore.GetUser(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAcceleratorToken GetUser failed: %v, uid: %d", err, req.GetUid())
		return "", err
	}
	if user == nil || user.UserStatus == pb.UserStatus_USER_STATUS_UNSPECIFIED {
		return "", protocol.NewExactServerError(nil, status.ErrAcceleratorNotActive)
	}
	if user.UserStatus == pb.UserStatus_USER_STATUS_BAN {
		return "", protocol.NewExactServerError(nil, status.ErrAcceleratorUserBan)
	}
	if user.UserStatus == pb.UserStatus_USER_STATUS_EXPIRED {
		return "", protocol.NewExactServerError(nil, status.ErrAcceleratorAlreadyExpire)
	}

	// 从缓存中取token
	token, err := m.cache.GetUserAcceleratorToken(ctx, req.GetUid(), req.GetAppId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAcceleratorToken GetUserAcceleratorToken failed: %v, uid: %d", err, req.GetUid())
	}
	if token != "" {
		return token, nil
	}

	token, err = m.getThirdApiToken(ctx, req.GetImei(), req.GetAppId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAcceleratorToken getThirdApiToken failed: %v, uid: %d, imei: %s, appId: %s", err, req.GetUid(), req.GetImei(), req.GetAppId())
		return "", err
	}

	// 设置token到缓存中
	expireDuration := time.Until(user.AuthorizeExpireTime)
	if expireDuration < 0 {
		return token, nil
	}
	if expireDuration > time.Hour*24 {
		expireDuration = time.Hour * 24 // 最多缓存一天
	}
	if err = m.cache.SetUserAcceleratorToken(ctx, req.GetUid(), req.GetAppId(), token, expireDuration); err != nil {
		log.ErrorWithCtx(ctx, "GetAcceleratorToken SetUserAcceleratorToken failed: %v, uid: %d", err, req.GetUid())
	}
	return token, nil
}

func (m *AcceleratorUserMgr) StatisticsAcceleratorUse(ttid string, appIds []string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	records := make([]*entity.AcceleratorRecord, 0, len(appIds))
	for _, appId := range appIds {
		a, e := strconv.Atoi(appId)
		if e != nil {
			log.ErrorWithCtx(ctx, "StatisticsAcceleratorUse strconv.Atoi failed: %v, appId: %s", e, appId)
			continue
		}
		records = append(records, &entity.AcceleratorRecord{
			AppId:      uint32(a),
			TTId:       ttid,
			CreateTime: time.Now(),
		})
	}
	return m.userStore.AddAcceleratorRecords(ctx, records)
}

// GetAcceleratorUserCount 获取最近14天每个游戏加速过的人数
func (m *AcceleratorUserMgr) GetAcceleratorUserCount(ctx context.Context, appIds []uint32) (map[uint32]uint32, error) {
	countMap, err := m.userStore.GetAcceleratorUserCount(ctx, appIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAcceleratorUserCount fail: %v, appIds:%v", err, appIds)
		return nil, err
	}
	return countMap, nil
}
