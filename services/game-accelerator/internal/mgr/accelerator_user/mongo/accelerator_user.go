package mongo

import (
	"context"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/writeconcern"
	pb "golang.52tt.com/protocol/services/game-accelerator"
	"golang.52tt.com/services/game-accelerator/internal/infra/db"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_user/entity"
	"strconv"
	"time"
)

const (
	userCollection = "accelerator_users"
	// AcceleratorRecordCollection 用于存储最近14天的用户加速器记录
	acceleratorRecordCollection = "accelerator_record"

	AcceleratorRecordExpireSeconds = 14 * 24 * 60 * 60 // 14天
)

//go:generate ifacemaker -f *.go -s AcceleratorUserStore -p mongo -o ./accelerator_user_mongo_api.go -i IAcceleratorUserStore
//go:generate mockgen -destination=../mocks/accelerator_user_mongo.go -package=mocks -source ./accelerator_user_mongo_api.go IAcceleratorUserStore
type AcceleratorUserStore struct {
	user              *mongo.Collection
	acceleratorRecord *mongo.Collection
}

func NewAcceleratorUserStore(ctx context.Context, db *db.MongoDB) (*AcceleratorUserStore, error) {

	colOption := options.Collection().SetWriteConcern(writeconcern.New(writeconcern.WMajority(), writeconcern.J(true)))
	st := &AcceleratorUserStore{
		user:              db.Database().Collection(userCollection, colOption),
		acceleratorRecord: db.Database().Collection(acceleratorRecordCollection),
	}

	// 创建索引
	if err := st.createIndexes(ctx); err != nil {
		log.ErrorWithCtx(ctx, "NewAcceleratorUserStore createIndexes failed: %v", err)
		return nil, err
	}

	return st, nil
}

// createIndexes 创建索引
func (s *AcceleratorUserStore) createIndexes(ctx context.Context) error {
	indexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "admin_sort", Value: -1},
			},
		},
		{
			Keys: bson.D{
				{Key: "user_status", Value: 1},
				{Key: "admin_sort", Value: -1},
			},
		},
		{
			Keys: bson.D{
				{Key: "membership_expire_time", Value: -1},
				{Key: "user_status", Value: 1},
			},
		},
	}
	_, err := s.user.Indexes().CreateMany(ctx, indexes)
	if err != nil {
		log.ErrorWithCtx(ctx, "createIndexes for accelerator_users failed: %v", err)
	}

	recordIndexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "create_time", Value: 1},
				{Key: "app_id", Value: 1},
				{Key: "ttid", Value: 1},
			},
			Options: options.Index(),
		},
		{
			Keys: bson.D{
				{Key: "app_id", Value: 1},
				{Key: "ttid", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{
				{Key: "create_time", Value: 1},
			},
			Options: options.Index().SetExpireAfterSeconds(AcceleratorRecordExpireSeconds),
		},
	}
	_, err = s.acceleratorRecord.Indexes().CreateMany(ctx, recordIndexes)
	if err != nil {
		log.ErrorWithCtx(ctx, "createIndexes for accelerator_record failed: %v", err)
	}
	return nil
}

func (s *AcceleratorUserStore) UpsertUser(ctx context.Context, user *entity.AcceleratorUser) error {
	filter := bson.M{"_id": user.Id}
	update := bson.M{
		"$set": bson.M{
			"user_identity":          user.UserIdentity,
			"user_status":            user.UserStatus,
			"membership_expire_time": user.MembershipExpireTime,
			"authorize_expire_time":  user.AuthorizeExpireTime,
			"update_time":            user.UpdateTime,
			"admin_sort":             fmt.Sprintf("%s%d", user.UpdateTime.In(time.Local).Format("20060102150405"), user.Id),
		},
		"$setOnInsert": bson.M{
			"create_time": user.CreateTime,
		},
	}

	opts := options.Update().SetUpsert(true)
	_, err := s.user.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpsertUser failed: %v, uid: %d", err, user.Id)
		return err
	}
	return nil
}

func (s *AcceleratorUserStore) CountDocuments(ctx context.Context, req *pb.GetAcceleratorUserListReq) (total int64, err error) {
	filter := bson.M{}
	// 构建查询条件
	if req.GetUid() > 0 {
		filter["_id"] = req.GetUid()
	}
	// 套餐时间范围查询
	if req.GetMembershipStartTime() > 0 || req.GetMembershipEndTime() > 0 {
		membershipTimeFilter := bson.M{}
		if req.GetMembershipStartTime() > 0 {
			membershipTimeFilter["$gte"] = time.Unix(req.GetMembershipStartTime(), 0)
		}
		if req.GetMembershipEndTime() > 0 {
			membershipTimeFilter["$lte"] = time.Unix(req.GetMembershipEndTime(), 0)
		}
		filter["membership_expire_time"] = membershipTimeFilter
	}
	if req.GetUserStatus() != pb.UserStatus_USER_STATUS_UNSPECIFIED {
		filter["user_status"] = req.GetUserStatus()
	}

	// 没有筛选条件，使用预估的总数，性能快很多
	if len(filter) == 0 {
		total, err = s.user.EstimatedDocumentCount(ctx)
	} else {
		// 获取总数
		total, err = s.user.CountDocuments(ctx, filter)
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "CountDocuments failed: %v, filter: %+v", err, filter)
		return 0, err
	}
	return total, nil
}

func (s *AcceleratorUserStore) SearchUsers(ctx context.Context, req *pb.GetAcceleratorUserListReq, lastSortId string, limit int64) ([]*entity.AcceleratorUser, string, error) {
	useMembershipTimeSort := false
	opts := options.Find()
	filter := bson.M{}
	// 构建查询条件
	if req.GetUid() > 0 {
		filter["_id"] = req.GetUid()
	}
	if req.GetUserStatus() != pb.UserStatus_USER_STATUS_UNSPECIFIED {
		filter["user_status"] = req.GetUserStatus()
	}
	// 套餐时间范围查询
	if req.GetMembershipStartTime() > 0 || req.GetMembershipEndTime() > 0 {
		membershipTimeFilter := bson.M{}
		if req.GetMembershipStartTime() > 0 {
			membershipTimeFilter["$gte"] = time.Unix(req.GetMembershipStartTime(), 0)
		}
		if req.GetMembershipEndTime() > 0 {
			membershipTimeFilter["$lte"] = time.Unix(req.GetMembershipEndTime(), 0)
		}
		// 以毫秒时间戳作为排序依据，如果时间戳相同，取下一页数据可能会被过滤掉，先这样简单处理。
		if lastSortId != "" {
			ux, e := strconv.ParseInt(lastSortId, 10, 64)
			if e != nil {
				log.ErrorWithCtx(ctx, "SearchUsers ParseInt lastSortId failed: %v, lastSortId: %s", e, lastSortId)
				return nil, "", e
			}
			sortTime := time.UnixMilli(ux)
			membershipTimeFilter["$lt"] = sortTime
		}
		filter["membership_expire_time"] = membershipTimeFilter

		useMembershipTimeSort = true
		opts.SetSort(bson.M{"membership_expire_time": -1})

	} else {
		opts.SetSort(bson.M{"admin_sort": -1})
		if lastSortId != "" {
			filter["admin_sort"] = bson.M{"$lt": lastSortId}
		}
	}

	// 查询数据
	opts.SetLimit(limit)
	cursor, err := s.user.Find(ctx, filter, opts)
	if err != nil {
		return nil, "", err
	}
	var users []*entity.AcceleratorUser
	if err = cursor.All(ctx, &users); err != nil {
		return nil, "", err
	}
	if len(users) < int(limit) {
		return users, "", nil
	}
	lastData := users[len(users)-1]
	if useMembershipTimeSort {
		lastSortId = fmt.Sprintf("%d", lastData.MembershipExpireTime.UnixMilli())
	} else {
		lastSortId = lastData.AdminSort
	}
	return users, lastSortId, nil
}

func (s *AcceleratorUserStore) GetUser(ctx context.Context, uid uint32) (*entity.AcceleratorUser, error) {
	filter := bson.M{"_id": uid}
	var user entity.AcceleratorUser
	err := s.user.FindOne(ctx, filter).Decode(&user)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}
	return &user, nil
}

func (s *AcceleratorUserStore) GetUsers(ctx context.Context, uids []uint32) ([]*entity.AcceleratorUser, error) {
	filter := bson.M{"_id": bson.M{"$in": uids}}

	cursor, err := s.user.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var users []*entity.AcceleratorUser
	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}
	return users, nil
}

func (s *AcceleratorUserStore) GetExpireUsers(ctx context.Context) ([]*entity.AcceleratorUser, error) {
	filter := bson.M{
		"membership_expire_time": bson.M{
			"$lt": time.Now(),
			"$gt": time.Now().AddDate(0, 0, -3),
		},
		"user_status": pb.UserStatus_USER_STATUS_NORMAL,
	}

	cursor, err := s.user.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var users []*entity.AcceleratorUser
	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}
	return users, nil
}

func (s *AcceleratorUserStore) UpdateUserExpireStatus(ctx context.Context, uids []uint32) error {
	filter := bson.M{
		"_id":         bson.M{"$in": uids},
		"user_status": pb.UserStatus_USER_STATUS_NORMAL,
		"membership_expire_time": bson.M{
			"$lt": time.Now(),
		}}
	update := bson.M{"$set": bson.M{"user_status": pb.UserStatus_USER_STATUS_EXPIRED, "update_time": time.Now()}}
	_, err := s.user.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s *AcceleratorUserStore) UpdateUserAcceleratorStatus(ctx context.Context, uid uint32, userStatus pb.UserStatus) error {
	now := time.Now()
	filter := bson.M{"_id": uid}
	update := bson.M{"$set": bson.M{"user_status": userStatus, "update_time": now,
		"admin_sort": fmt.Sprintf("%s%d", now.In(time.Local).Format("20060102150405"), uid)},
	}
	_, err := s.user.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}
