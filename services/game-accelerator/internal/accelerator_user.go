package internal

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.52tt.com/pkg/protocol"
	game_accelerator_logic "golang.52tt.com/protocol/app/game-accelerator-logic"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/game-accelerator"
	order_entity "golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_order/entity"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_user/entity"
	third_open_api "golang.52tt.com/services/game-accelerator/internal/third-open-api"
	"strconv"
	"time"
)

// AdminAddUserAccelerator 运营后台创建加速器订单
func (s *Server) AdminAddUserAccelerator(ctx context.Context, req *pb.AdminAddUserAcceleratorReq) (*pb.AdminAddUserAcceleratorResp, error) {
	out := &pb.AdminAddUserAcceleratorResp{}
	log.InfoWithCtx(ctx, "AdminAddUserAccelerator request: %+v", req)
	if len(req.GetUids()) > 200 {
		return out, protocol.NewExactServerError(nil, status.ErrAcceleratorAuthorizeFail, "批量添加用户加速器套餐数量不能超过200")
	}
	successNum, failItems, err := s.addUserAccelerator(ctx, req.GetUids(), req.GetOutTradeOrderId(), req.GetOperator(), req.GetAcceleratorOrderType(),
		req.GetOrderCount(), req.GetPlatformType(), req.GetRemark(), pb.AcceleratorBusinessType_ACCELERATOR_BUSINESS_TYPE_ADMIN)
	if err != nil {
		log.ErrorWithCtx(ctx, "AdminAddUserAccelerator addUserAccelerator fail, err:%v, req: %+v", err, req)
		return out, err
	}
	out.SuccessNum = successNum
	out.FailResult = make([]*pb.AddAcceleratorResult, 0, len(failItems))
	for _, item := range failItems {
		if item.err == nil {
			continue
		}
		out.FailResult = append(out.FailResult, &pb.AddAcceleratorResult{
			Uid:     item.uid,
			Code:    status.ErrAcceleratorAuthorizeFail,
			Message: item.err.Error(),
		})
	}
	log.InfoWithCtx(ctx, "AdminAddUserAccelerator success, req: %+v, successNum:%d, failItems:%+v", req, successNum, failItems)
	return out, nil
}

func (s *Server) GetPreviewExpireTime(ctx context.Context, req *pb.GetPreviewExpireTimeReq) (*pb.GetPreviewExpireTimeResp, error) {
	out := &pb.GetPreviewExpireTimeResp{}

	now := time.Now()
	expireTime, err := third_open_api.CalExpireTime(now, req.GetAcceleratorOrderType(), req.GetOrderCount())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPreviewExpireTime CalExpireTime fail, err:%v, req: %+v", err, req)
		return out, err
	}

	if req.GetUid() > 0 {
		// 判断用户是否已经领取过新人加速器套餐
		if req.GetAcceleratorOrderType() == pb.AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_NEWBIE {
			newBieOrder, err := s.acceleratorOrderMgr.GetNewbieOrderByUid(ctx, req.GetUid())
			if err != nil {
				log.ErrorWithCtx(ctx, "GetPreviewExpireTime GetNewbieOrderByUid fail, err:%v, uid: %d", err, req.GetUid())
				return out, err
			}
			if newBieOrder != nil && newBieOrder.Status != pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_INVALID {
				return out, protocol.NewExactServerError(nil, status.ErrAcceleratorNewbieAuthorizeFail)
			}
		}

		user, e := s.acceleratorUserMgr.GetUser(ctx, req.GetUid())
		if e != nil {
			log.ErrorWithCtx(ctx, "GetPreviewExpireTime GetUsers fail, err:%v, req: %+v", e, req)
			return out, err
		}
		if user != nil && user.MembershipExpireTime.After(now) {
			expireTime, e = third_open_api.CalExpireTime(user.MembershipExpireTime, req.GetAcceleratorOrderType(), req.GetOrderCount())
			if e != nil {
				return out, e
			}
		}
	}
	out.ExpireTime = expireTime.In(time.Local).Format(third_open_api.TimeFormat)
	return out, nil
}

type AcceleratorFailItem struct {
	uid uint32
	err error
}

// 添加用户加速器套餐，这里没有使用分布式事务逻辑，采取的是最终一致性处理方式，定时任务处理异常的订单； 同个用户后面有并发授权情况有数据风险
func (s *Server) addUserAccelerator(ctx context.Context, uids []uint32, reqOutTradeOrderId, operator string, orderType pb.AcceleratorOrderType, orderCount uint32,
	platform pb.AcceleratorPlatformType, remark string, businessType pb.AcceleratorBusinessType) (uint32, []AcceleratorFailItem, error) {
	// 验证请求参数
	if len(uids) == 0 {
		return 0, nil, protocol.NewExactServerError(nil, status.ErrAcceleratorAuthorizeFail, "uid is required")
	}
	if reqOutTradeOrderId == "" {
		return 0, nil, protocol.NewExactServerError(nil, status.ErrAcceleratorAuthorizeFail, "out_trade_order_id is required")
	}
	if operator == "" {
		return 0, nil, protocol.NewExactServerError(nil, status.ErrAcceleratorAuthorizeFail, "operator is required")
	}
	if orderCount == 0 {
		return 0, nil, protocol.NewExactServerError(nil, status.ErrAcceleratorAuthorizeFail, "order_count is required")
	}

	now := time.Now()
	successNum := uint32(0)
	failItems := make([]AcceleratorFailItem, 0, len(uids))
	for _, uid := range uids {
		// 判断用户是否已经领取过新人加速器套餐
		if orderType == pb.AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_NEWBIE {
			newBieOrder, err := s.acceleratorOrderMgr.GetNewbieOrderByUid(ctx, uid)
			if err != nil {
				failItems = append(failItems, AcceleratorFailItem{
					uid: uid,
					err: err,
				})
				log.ErrorWithCtx(ctx, "GetNewbieOrderByUid fail, err:%v, uid: %d", err, uid)
				continue
			}
			if newBieOrder != nil && newBieOrder.Status != pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_INVALID {
				failItems = append(failItems, AcceleratorFailItem{
					uid: uid,
					err: protocol.NewExactServerError(nil, status.ErrAcceleratorNewbieAuthorizeFail),
				})
				log.WarnWithCtx(ctx, "User %d has already received newbie accelerator order", uid)
				continue
			}
		}

		calStartTime := now
		acceleratorUser, err := s.acceleratorUserMgr.GetUser(ctx, uid)
		if err != nil {
			failItems = append(failItems, AcceleratorFailItem{
				uid: uid,
				err: err,
			})
			log.ErrorWithCtx(ctx, "GetUser fail, err:%v, uid: %d", err, uid)
			continue
		}
		if acceleratorUser != nil {
			if acceleratorUser.UserStatus == pb.UserStatus_USER_STATUS_BAN {
				log.WarnWithCtx(ctx, "User %d is banned from using accelerator", uid)
				failItems = append(failItems, AcceleratorFailItem{
					uid: uid,
					err: protocol.NewExactServerError(nil, status.ErrAcceleratorAuthorizeFail, "user is banned from using accelerator"),
				})
				continue
			}
			// 当前用户套餐还没过期，则使用当前套餐的过期时间作为计算开始时间
			if acceleratorUser.MembershipExpireTime.After(calStartTime) {
				calStartTime = acceleratorUser.MembershipExpireTime
			}
		}

		expireTime, err := third_open_api.CalExpireTime(calStartTime, orderType, orderCount)
		if err != nil {
			failItems = append(failItems, AcceleratorFailItem{
				uid: uid,
				err: protocol.NewExactServerError(nil, status.ErrAcceleratorAuthorizeFail, err.Error()),
			})
			log.ErrorWithCtx(ctx, "CalExpireTime fail, err:%v, uid: %d, orderType: %s, orderCount: %d", err, uid, orderType.String(), orderCount)
			continue
		}

		var outTradeOrderId string
		switch businessType {
		case pb.AcceleratorBusinessType_ACCELERATOR_BUSINESS_TYPE_ACTIVITY:
			// 活动类型的订单，他们业务保证 OutTradeOrderId 是唯一的
			outTradeOrderId = reqOutTradeOrderId
		case pb.AcceleratorBusinessType_ACCELERATOR_BUSINESS_TYPE_ADMIN:
			// 管理员后台批量添加订单，可能会有多个用户使用同一个 OutTradeOrderId
			outTradeOrderId = fmt.Sprintf("%s_%d", reqOutTradeOrderId, uid)
		}
		order := &order_entity.AcceleratorOrder{
			OrderId:         primitive.NewObjectID().Hex(),
			Uid:             uid,
			OrderType:       orderType,
			OrderCount:      orderCount,
			Status:          pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_PENDING,
			PlatformType:    platform,
			Operator:        operator,
			Remark:          remark,
			OutTradeOrderId: outTradeOrderId,
			ExpireTime:      expireTime,
			CreateTime:      now,
			ThirdCreateTime: now,
		}
		// 调用管理器创建订单
		orderId, orgAuthCodeId, thirdCreateTime, err := s.acceleratorOrderMgr.CreateOrder(ctx, order)
		if err != nil {
			failItems = append(failItems, AcceleratorFailItem{
				uid: uid,
				err: err,
			})
			if third_open_api.IsThirdReturnBusErr(err) {
				// 第三方返回业务错误，可能是订单重复等情况，不需要重试
				errUpdate := s.acceleratorOrderMgr.UpdateOrderStatus(ctx, order.OrderId, orgAuthCodeId, time.Time{}, pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_INVALID, err.Error())
				if errUpdate != nil {
					log.ErrorWithCtx(ctx, "UpdateOrderStatus failed: %v, order_id: %s", errUpdate, order.OrderId)
				}

			} else if third_open_api.CreateOrderIgnoreErr(err) {
				log.WarnWithCtx(ctx, "CreateOrderIgnoreErr fail, err:%v, order:%v", err, order)

			} else {
				// 要重试的订单失败状态
				errUpdate := s.acceleratorOrderMgr.UpdateOrderStatus(ctx, order.OrderId, orgAuthCodeId, time.Time{}, pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_FAIL, "")
				if errUpdate != nil {
					log.ErrorWithCtx(ctx, "UpdateOrderStatus failed: %v, order_id: %s", errUpdate, order.OrderId)
				}
				_ = s.acceleratorOrderMgr.SendWarningMsg(ctx, fmt.Sprintf("uid:%d fail authorize, orderId:%s, errinfo:%v", order.Uid, order.OrderId, err))
			}
			continue
		}
		// 创建或者更新用户状态信息
		err = s.acceleratorUserMgr.UpsertUser(ctx, &entity.AcceleratorUser{
			Id:                   uid,
			UserIdentity:         pb.UserIdentity_USER_IDENTITY_FREE,
			UserStatus:           pb.UserStatus_USER_STATUS_NORMAL,
			MembershipExpireTime: expireTime,
			AuthorizeExpireTime:  expireTime,
			UpdateTime:           now,
			CreateTime:           now,
		})
		if err != nil {
			failItems = append(failItems, AcceleratorFailItem{
				uid: uid,
				err: err,
			})
			log.ErrorWithCtx(ctx, "UpsertUser fail, err:%v, uid: %d, expireTime:%v", err, uid, expireTime)
			continue
		}
		// 更新订单状态
		err = s.acceleratorOrderMgr.UpdateOrderStatus(ctx, orderId, orgAuthCodeId, thirdCreateTime, pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_SUCCESS, "")
		if err != nil {
			failItems = append(failItems, AcceleratorFailItem{
				uid: uid,
				err: err,
			})
			log.ErrorWithCtx(ctx, "UpdateOrderStatus fail, err:%v, order_id:%s, orgAuthCodeId:%d", err, orderId, orgAuthCodeId)
			continue
		}
		successNum++
	}
	return successNum, failItems, nil
}

func (s *Server) GetAcceleratorUserList(ctx context.Context, req *pb.GetAcceleratorUserListReq) (*pb.GetAcceleratorUserListResp, error) {
	out := &pb.GetAcceleratorUserListResp{}
	data, total, lastId, err := s.acceleratorUserMgr.SearchUsers(ctx, req)
	if err != nil {
		return out, err
	}
	out.TotalCount = total
	out.LastId = lastId
	out.UserList = make([]*pb.AcceleratorUserItem, 0, len(data))
	for _, user := range data {
		out.UserList = append(out.UserList, user.ToPB())
	}
	log.InfoWithCtx(ctx, "GetAcceleratorUserList success, req:%+v", req)
	return out, nil
}

func (s *Server) GetUserAcceleratorLog(ctx context.Context, req *pb.GetUserAcceleratorLogReq) (*pb.GetUserAcceleratorLogResp, error) {
	out := &pb.GetUserAcceleratorLogResp{}
	logInfo, err := s.acceleratorUserMgr.GetUserAcceleratorLog(ctx, req)
	if err != nil {
		return out, err
	}
	out.Message = logInfo.Message
	out.Code = logInfo.Code
	logItems := logInfo.Data
	items := make([]*pb.GetUserAcceleratorLogResp_Data_Item, 0, len(logItems.Items))
	for _, item := range logItems.Items {
		items = append(items, &pb.GetUserAcceleratorLogResp_Data_Item{
			Imei:      item.Imei,
			AppIds:    item.AppIds,
			StartTime: item.StartTime,
			EndTime:   item.EndTime,
			FlowUse:   strconv.Itoa(item.FlowUse),
			MaxWidth:  strconv.Itoa(item.MaxWidth),
			MinWidth:  strconv.Itoa(item.MinWidth),
			AvgWidth:  strconv.Itoa(item.AvgWidth),
		})
	}
	out.Data = &pb.GetUserAcceleratorLogResp_Data{
		Items:       items,
		Total:       logItems.Total,
		PerPage:     logItems.PerPage,
		CurrentPage: logItems.CurrentPage,
		ServerTime:  logItems.ServerTime,
	}
	return out, nil
}

func (s *Server) BanUserUseAccelerator(ctx context.Context, req *pb.BanUserUseAcceleratorReq) (*pb.BanUserUseAcceleratorResp, error) {
	out := &pb.BanUserUseAcceleratorResp{}
	err := s.acceleratorUserMgr.UpdateUserAcceleratorStatus(ctx, req.GetUid(), req.GetAction())
	if err != nil {
		log.ErrorWithCtx(ctx, "BanUserUseAccelerator BanUserUseAccelerator fail, err:%v, req: %+v", err, req)
		return out, err
	}
	if req.GetAction() == pb.BanUserUseAcceleratorReq_ACTION_BAN {
		_ = s.acceleratorUserMgr.UserAccelerateStatusChangePush(ctx, []uint32{req.GetUid()}, game_accelerator_logic.UserAcceleratorState_USER_ACCELERATOR_STATE_BANNED)
	}
	log.InfoWithCtx(ctx, "BanUserUseAccelerator success, req: %+v", req)
	return out, nil
}

func (s *Server) GetUserAcceleratorInfo(ctx context.Context, req *pb.GetUserAcceleratorInfoReq) (*pb.GetUserAcceleratorInfoResp, error) {
	out := &pb.GetUserAcceleratorInfoResp{}
	if len(req.GetUids()) == 0 {
		return out, nil
	}
	data, err := s.acceleratorUserMgr.GetUsers(ctx, req.GetUids())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAcceleratorInfo GetUsers fail, err:%v, req: %+v", err, req)
		return out, err
	}
	out.Items = make([]*pb.AcceleratorUserItem, 0, len(data))
	for _, user := range data {
		out.Items = append(out.Items, user.ToPB())
	}
	return out, nil
}

// AddUserAcceleratorAuthorize 添加用户加速器套餐
func (s *Server) AddUserAcceleratorAuthorize(ctx context.Context, req *pb.AddUserAcceleratorAuthorizeReq) (*pb.AddUserAcceleratorAuthorizeResp, error) {
	out := &pb.AddUserAcceleratorAuthorizeResp{}
	log.InfoWithCtx(ctx, "AddUserAcceleratorAuthorize request: %+v", req)
	operator := pb.AcceleratorBusinessType_name[int32(req.GetBusinessId())]
	remark := req.GetRemark()
	if remark == "" {
		remark = operator + "自动发放"
	}
	successNum, failItems, err := s.addUserAccelerator(ctx, []uint32{req.GetUid()}, req.GetOutTradeOrderId(), operator, req.GetAcceleratorOrderType(),
		req.GetOrderCount(), req.GetPlatformType(), remark, req.GetBusinessId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserAcceleratorAuthorize addUserAccelerator fail, err:%v, req: %+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "AddUserAcceleratorAuthorize success, req: %+v, successNum:%d, failItems:%+v", req, successNum, failItems)

	out.SuccessNum = successNum
	if len(failItems) > 0 {
		out.FailUids = []uint32{failItems[0].uid}
		return out, failItems[0].err
	}
	return out, nil
}

func (s *Server) GetAcceleratorToken(ctx context.Context, req *pb.GetAcceleratorTokenReq) (*pb.GetAcceleratorTokenResp, error) {
	out := &pb.GetAcceleratorTokenResp{}
	if req.GetAppId() == "" {
		log.WarnWithCtx(ctx, "GetAcceleratorToken AppId is empty, req: %+v", req)
		return out, nil
	}
	appId, err := strconv.Atoi(req.GetAppId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAcceleratorToken strconv.Atoi fail, err:%v, req: %+v", err, req)
		return out, protocol.NewExactServerError(nil, status.ErrAcceleratorGetTokenFail, "游戏ID错误")
	}
	gameInfo, err := s.acceleratorCfgMgr.GetGameByIds(ctx, []uint32{uint32(appId)})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAcceleratorToken GetGameByIds fail, err:%v, req: %+v", err, req)
		return out, protocol.NewExactServerError(nil, status.ErrAcceleratorGetTokenFail, "服务器错误，请稍后再试")
	}
	if len(gameInfo) == 0 || gameInfo[0].State != pb.AcceleratorGameState_ACCELERATOR_GAME_STATE_NORMAL {
		log.WarnWithCtx(ctx, "GetAcceleratorToken game is not normal, req: %+v, gameInfo:%v", req, gameInfo)
		return out, protocol.NewExactServerError(nil, status.ErrAcceleratorGetTokenFail, "该游戏已屏蔽下架")
	}

	token, err := s.acceleratorUserMgr.GetAcceleratorToken(ctx, req)
	if err != nil {
		return out, err
	}
	out.Token = token

	// 异步统计每款游戏的加速器使用情况
	go func() {
		_ = s.acceleratorUserMgr.StatisticsAcceleratorUse(req.GetImei(), []string{req.GetAppId()})
	}()

	log.InfoWithCtx(ctx, "GetAcceleratorToken success, req: %+v, out:%+v", req, out)
	return out, nil
}

func (s *Server) GetNewbieOrderByUids(ctx context.Context, req *pb.GetNewbieOrderByUidsReq) (*pb.GetNewbieOrderByUidsResp, error) {
	out := &pb.GetNewbieOrderByUidsResp{}

	if len(req.GetUids()) == 0 || len(req.GetUids()) > 500 {
		log.WarnWithCtx(ctx, "GetNewbieOrderByUids Uids is empty or too many, req: %+v", req)
		return out, nil
	}
	// 判断用户是否已经领取过新人加速器套餐
	newBieOrders, err := s.acceleratorOrderMgr.GetNewbieOrderByUids(ctx, req.GetUids())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewbieOrderByUids fail, err:%v, req:%v", err, req)
		return out, err
	}
	out.ItemMap = make(map[uint32]*pb.AcceleratorOrderItem, len(newBieOrders))
	for _, order := range newBieOrders {
		if order != nil && order.Status != pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_INVALID {
			out.ItemMap[order.Uid] = order.ToPB()
		}
	}
	return out, nil
}
