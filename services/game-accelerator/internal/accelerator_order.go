package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/game-accelerator"
)

const (
	// 三个月的秒数
	ThreeMonthsSeconds = 3 * 30 * 24 * 3600
)

func (s *Server) GetAcceleratorOrder(ctx context.Context, req *pb.GetAcceleratorOrderReq) (*pb.GetAcceleratorOrderResp, error) {
	out := &pb.GetAcceleratorOrderResp{}

	if req.GetEndTime()-req.GetStartTime() > ThreeMonthsSeconds {
		log.ErrorWithCtx(ctx, "GetAcceleratorOrder time range exceeds 3 months, req: %v", req)
		return out, protocol.NewExactServerError(nil, status.ErrBadRequest, "time range exceeds 3 months")
	}
	// 调用管理器获取用户订单
	orders, total, err := s.acceleratorOrderMgr.GetOrdersByAdmin(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrdersByAdmin failed: %v, req: %v", err, req)
		return out, err
	}
	out.TotalCount = total
	out.OrderList = make([]*pb.AcceleratorOrderItem, 0, len(orders))
	for _, order := range orders {
		out.OrderList = append(out.OrderList, order.ToPB())
	}
	return out, nil
}
