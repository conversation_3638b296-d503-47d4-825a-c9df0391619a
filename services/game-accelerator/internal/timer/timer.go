package timer

import (
	"context"
	"fmt"
	account_go "golang.52tt.com/clients/account-go"
	context_info "golang.52tt.com/pkg/context-info"
	game_accelerator_logic "golang.52tt.com/protocol/app/game-accelerator-logic"
	pb "golang.52tt.com/protocol/services/game-accelerator"
	"golang.52tt.com/services/game-accelerator/internal/infra/db"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_cfg"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_cfg/entity"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_order"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_user"
	third_open_api "golang.52tt.com/services/game-accelerator/internal/third-open-api"
	"time"

	user_entity "golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_user/entity"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/pkg/timer"
)

type RedisTimer struct {
	timerD *timer.Timer

	accountGoCli account_go.IClient

	acceleratorCfgMgr   accelerator_cfg.IAcceleratorCfgMgr
	acceleratorUserMgr  accelerator_user.IAcceleratorUserMgr
	acceleratorOrderMgr accelerator_order.IAcceleratorOrderMgr
}

func NewRedisTimer(ctx context.Context, redisCli *db.RedisDB, accountGoCli account_go.IClient, acceleratorCfgMgr accelerator_cfg.IAcceleratorCfgMgr,
	acceleratorUserMgr accelerator_user.IAcceleratorUserMgr, acceleratorOrderMgr accelerator_order.IAcceleratorOrderMgr) (*RedisTimer, error) {

	timerD, err := timer.NewTimerD(ctx, "game-accelerator", timer.WithV8RedisCmdable(redisCli))
	if err != nil {
		log.Errorf("NewTimerD err:%v", err)
		return nil, err
	}

	return &RedisTimer{
		timerD:              timerD,
		accountGoCli:        accountGoCli,
		acceleratorCfgMgr:   acceleratorCfgMgr,
		acceleratorUserMgr:  acceleratorUserMgr,
		acceleratorOrderMgr: acceleratorOrderMgr,
	}, nil
}

func (t *RedisTimer) Start() error {

	// 1分钟更新一次游戏列表排序分数
	t.timerD.AddIntervalTask("SyncGameScore", 1*time.Minute, timer.BuildFromLambda(func(ctx context.Context) {
		err := t.SyncGameScore(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "SyncGameScore error: %v", err)
		}
	}))

	// 15分钟同步一次第三方游戏列表
	t.timerD.AddIntervalTask("SyncThirdPartyGameData", 15*time.Minute, timer.BuildFromLambda(func(ctx context.Context) {
		err := t.SyncThirdPartyGameData(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "SyncThirdPartyGameData error: %v", err)
		}
	}))

	// 更新用户加速器过期状态
	t.timerD.AddIntervalTask("UpdateUserExpireStatus", 3*time.Second, timer.BuildFromLambda(func(ctx context.Context) {
		newCtx, cancel := context.WithTimeout(ctx, time.Second*3)
		defer cancel()
		err := t.UpdateUserExpireStatus(newCtx)
		if err != nil {
			log.ErrorWithCtx(newCtx, "UpdateUserExpireStatus error: %v", err)
		}
	}))

	// 兜底补单
	t.timerD.AddIntervalTask("RecoverFailOrder", 3*time.Second, timer.BuildFromLambda(func(ctx context.Context) {
		newCtx, cancel := context.WithTimeout(ctx, time.Second*3)
		defer cancel()
		err := t.RecoverFailOrder(newCtx)
		if err != nil {
			log.ErrorWithCtx(newCtx, "RecoverFailOrder error: %v", err)
		}
	}))

	t.timerD.Start()
	return nil
}

func (t *RedisTimer) Stop() {
	if t != nil && t.timerD != nil {
		t.timerD.Stop()
	}
}

// 定时更新游戏分数
func (t *RedisTimer) SyncGameScore(ctx context.Context) error {
	ctx = context_info.GenReqId(ctx)
	log.InfoWithCtx(ctx, "SyncGameScore begin")

	var lastId string
	for {
		var games []*entity.Game
		var err error
		games, lastId, err = t.acceleratorCfgMgr.GetAcceleratorGameListWithOption(ctx, lastId, 100, &entity.GameSearchOptions{})
		if err != nil {
			log.ErrorWithCtx(ctx, "SyncGameScore GetAcceleratorGameListWithOption error: %v", err)
			return err
		}
		if len(games) == 0 {
			break
		}
		var gameIds []uint32
		for _, game := range games {
			gameIds = append(gameIds, game.Id)
		}
		userCountMap, err := t.acceleratorUserMgr.GetAcceleratorUserCount(ctx, gameIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "SyncGameScore GetAcceleratorUserCount error: %v", err)
			return err
		}

		err = t.acceleratorCfgMgr.SyncGameScores(ctx, games, userCountMap)
		if err != nil {
			log.ErrorWithCtx(ctx, "SyncGameScore SyncGameScores error: %v", err)
			return err
		}
		log.InfoWithCtx(ctx, "SyncGameScores success gameIds:%v len(games):%d lastId:%s", gameIds, len(games), lastId)
		if lastId == "" {
			break
		}
	}
	return nil
}

// SyncThirdPartyGameData 定时同步第三方游戏数据
func (t *RedisTimer) SyncThirdPartyGameData(ctx context.Context) error {
	ctx = context_info.GenReqId(ctx)
	log.InfoWithCtx(ctx, "SyncThirdPartyGameData begin")
	err := t.acceleratorCfgMgr.SyncGames(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SyncThirdPartyGameData SyncGames error: %v", err)
		return err
	}
	return nil
}

// UpdateUserExpireStatus 定时更新用户加速器过期状态
func (t *RedisTimer) UpdateUserExpireStatus(ctx context.Context) error {
	ctx = context_info.GenReqId(ctx)
	now := time.Now()
	defer func() {
		log.InfoWithCtx(ctx, "UpdateUserExpireStatus end, cost: %v", time.Since(now))
	}()
	users, err := t.acceleratorUserMgr.GetExpireUsers(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetExpireUsers error: %v", err)
		return err
	}
	if len(users) == 0 {
		return nil
	}
	num := 0
	uids := make([]uint32, 0, 100)
	for _, user := range users {
		uids = append(uids, user.Id)
		num++
		if num >= 100 {
			num = 0
			uids = make([]uint32, 0, 100)
			// 过期状态推送
			err = t.acceleratorUserMgr.UserAccelerateStatusChangePush(ctx, uids, game_accelerator_logic.UserAcceleratorState_USER_ACCELERATOR_STATE_EXPIRED)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateUserExpireStatus UserAccelerateStatusChangePush error: %v, uids: %v", err, uids)
				continue
			}
			err = t.acceleratorUserMgr.UpdateUserExpireStatus(ctx, uids)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateUserExpireStatus UpdateUserExpireStatus error: %v, uids: %v", err, uids)
				continue
			}
		}
	}
	if len(uids) > 0 {
		// 过期状态推送
		err = t.acceleratorUserMgr.UserAccelerateStatusChangePush(ctx, uids, game_accelerator_logic.UserAcceleratorState_USER_ACCELERATOR_STATE_EXPIRED)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateUserExpireStatus UserAccelerateStatusChangePush err:%v, uids:%v", err, uids)
			return err
		}
		err = t.acceleratorUserMgr.UpdateUserExpireStatus(ctx, uids)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateUserExpireStatus UpdateUserExpireStatus err:%v, uids:%v", err, uids)
			return err
		}
	}
	return nil
}

func (t *RedisTimer) RecoverFailOrder(ctx context.Context) error {
	ctx = context_info.GenReqId(ctx)
	now := time.Now()
	defer func() {
		log.InfoWithCtx(ctx, "RecoverFailOrder end, cost: %v", time.Since(now))
	}()

	orders, oErr := t.acceleratorOrderMgr.GetFailOrders(ctx)
	if oErr != nil {
		log.ErrorWithCtx(ctx, "RecoverFailOrder GetFailOrders error: %v", oErr)
		return oErr
	}
	if len(orders) == 0 {
		return nil
	}

	for _, order := range orders {
		// 判断用户是否已经领取过新人加速器套餐
		if order.OrderType == pb.AcceleratorOrderType_ACCELERATOR_ORDER_TYPE_NEWBIE {
			newBieOrder, err := t.acceleratorOrderMgr.GetNewbieOrderByUid(ctx, order.Uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "RecoverFailOrder GetNewbieOrderByUid failed: %v, order: %v", err, order)
				continue
			}
			if newBieOrder != nil && newBieOrder.Status == pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_SUCCESS {
				log.WarnWithCtx(ctx, "RecoverFailOrder User has already received newbie accelerator package, order:%v", order)

				errUpdate := t.acceleratorOrderMgr.UpdateOrderStatus(ctx, order.OrderId, 0, time.Time{}, pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_INVALID, "")
				if errUpdate != nil {
					log.ErrorWithCtx(ctx, "RecoverFailOrder UpdateOrderStatus failed: %v, order_id: %s", errUpdate, order.OrderId)
				}
				continue
			}
		}

		calStartTime := order.CreateTime
		acceleratorUser, err := t.acceleratorUserMgr.GetUser(ctx, order.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "RecoverFailOrder GetUser failed: %v, order: %v", err, order)
			continue
		}
		if acceleratorUser != nil {
			if acceleratorUser.UserStatus == pb.UserStatus_USER_STATUS_BAN {
				log.WarnWithCtx(ctx, "RecoverFailOrder User is banned from using accelerator, order:%v", order)
				continue
			}
			// 当前用户套餐还没过期，则使用当前套餐的过期时间作为计算开始时间
			if acceleratorUser.MembershipExpireTime.After(calStartTime) {
				calStartTime = acceleratorUser.MembershipExpireTime
			}
		}

		expireTime, err := third_open_api.CalExpireTime(calStartTime, order.OrderType, order.OrderCount)
		if err != nil {
			log.ErrorWithCtx(ctx, "RecoverFailOrder CalExpireTime error: %v, order: %+v", err, order)
			continue
		}

		orgAuthCodeId, thirdCreateTime, err := t.acceleratorOrderMgr.Authorize(ctx, order.TTId, order.ExpireTime.In(time.Local).Format(third_open_api.TimeFormat))
		// 补单时如果是重复订单问题，需要更新订单状态为成功，跟初次下单不同
		if err != nil && err.Error() != third_open_api.RepeatAuthorizeError.Error() {
			log.ErrorWithCtx(ctx, "RecoverFailOrder Authorize error: %v, order: %+v", err, order)
			if third_open_api.IsThirdReturnBusErr(err) {
				errUpdate := t.acceleratorOrderMgr.UpdateOrderStatus(ctx, order.OrderId, orgAuthCodeId, time.Time{}, pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_INVALID, err.Error())
				if errUpdate != nil {
					log.ErrorWithCtx(ctx, "RecoverFailOrder UpdateOrderStatus failed: %v, order_id: %s", errUpdate, order.OrderId)
				}

			} else {
				// 要重试的订单失败状态
				errUpdate := t.acceleratorOrderMgr.UpdateOrderStatus(ctx, order.OrderId, orgAuthCodeId, time.Time{}, pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_FAIL, "")
				if errUpdate != nil {
					log.ErrorWithCtx(ctx, "RecoverFailOrder UpdateOrderStatus failed: %v, order_id: %s", errUpdate, order.OrderId)
				}
				_ = t.acceleratorOrderMgr.SendWarningMsg(ctx, fmt.Sprintf("uid:%d fail authorize, orderId:%s, errinfo:%v", order.Uid, order.OrderId, err))
			}
			continue
		}
		// 创建或者更新用户状态信息
		err = t.acceleratorUserMgr.UpsertUser(ctx, &user_entity.AcceleratorUser{
			Id:                   order.Uid,
			UserIdentity:         pb.UserIdentity_USER_IDENTITY_FREE,
			UserStatus:           pb.UserStatus_USER_STATUS_NORMAL,
			MembershipExpireTime: expireTime,
			AuthorizeExpireTime:  expireTime,
			UpdateTime:           time.Now(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "RecoverFailOrder UpsertUser failed: %v, order:%v", err, order)
			continue
		}
		// 更新订单状态
		err = t.acceleratorOrderMgr.UpdateOrderStatus(ctx, order.OrderId, orgAuthCodeId, thirdCreateTime, pb.AcceleratorOrderStatus_ACCELERATOR_ORDER_STATUS_SUCCESS, "")
		if err != nil {
			log.ErrorWithCtx(ctx, "RecoverFailOrder UpdateOrderStatus failed: %v, order_id:%s, orgAuthCodeId:%d", err, order.OrderId, orgAuthCodeId)
			continue
		}
		log.InfoWithCtx(ctx, "RecoverFailOrder order success, code: %d, order:%+v", orgAuthCodeId, order)
	}

	return nil
}
