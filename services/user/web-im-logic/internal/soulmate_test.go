package internal

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/terrors"

	account_go "golang.52tt.com/clients/account-go"
	mock_account_go "golang.52tt.com/clients/mocks/account-go"
	mock_censoring_proxy "golang.52tt.com/clients/mocks/censoring-proxy"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
)

func getError() terrors.ServerError {
	return protocol.NewExactServerError(nil, status.ErrChatBotInvalidMsgContent)
}

func TestError(t *testing.T) {
	err := getError()
	if err.Error() == protocol.NewExactServerError(nil, status.ErrChatBotInvalidMsgContent).Error() {
		t.Log("success")
	} else {
		t.Error("error")
	}
}

func TestSendMsg(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Mock setup
	mockAccountClient := mock_account_go.NewMockIClient(ctrl)
	mockSoulmateClient := aigc_soulmate.NewMockAigcSoulmateClient(ctrl)
	mockCensoringClient := mock_censoring_proxy.NewMockIClient(ctrl)
	//mockRcmdAIPartnerClient := mock_rcmd_ai_partner.NewMockIClient(ctrl)

	handler := NewSoulmateIMHandler(mockAccountClient, mockSoulmateClient, mockCensoringClient, nil,
		nil, nil, nil, nil, nil,nil, nil) //mockRcmdAIPartnerClient)

	tests := []struct {
		name    string
		setup   func()
		req     *SendReq
		wantErr bool
	}{
		{
			name:  "Invalid target ID",
			setup: func() {},
			req: &SendReq{
				Target: "invalid",
			},
			wantErr: true,
		},
		{
			name: "Failure to get user by UID",
			setup: func() {
				mockAccountClient.EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(nil, protocol.NewExactServerError(nil, status.ErrAccountNotExist))
			},
			req: &SendReq{
				Uid:    123,
				Target: "456",
			},
			wantErr: true,
		},
		{
			name: "Invalid partner",
			setup: func() {
				mockAccountClient.EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(&account_go.User{}, nil)
				mockSoulmateClient.EXPECT().GetAIPartner(gomock.Any(), gomock.Any()).Return(&aigc_soulmate.GetAIPartnerResp{}, nil) // Assuming partner UID does not match
			},
			req: &SendReq{
				Uid:    123,
				Target: "456",
			},
			wantErr: true,
		},
		/*{
			name: "Successful message send with text content",
			setup: func() {
				mockAccountClient.EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(&account_go.User{}, nil)
				mockSoulmateClient.EXPECT().GetAIPartner(gomock.Any(), gomock.Any()).Return(&aigc_soulmate.GetAIPartnerResp{Partner: &aigc_soulmate.AIPartner{Uid: 123}}, nil)
				mockRcmdAIPartnerClient.EXPECT().ReceiveMsgFromUser(gomock.Any(), gomock.Any()).Return(&rcmd_ai_partner.ReceiveMsgFromUserResp{}, nil)
			},
			req: &SendReq{
				Uid:    123,
				Target: "456",
				Msg:    &web_im_logic.ImMsg{Content: "Hello", Type: uint32(web_im_logic.ImMsg_TYPE_EMOTICON)},
			},
			wantErr: false,
		},*/
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			_, err := handler.SendMsg(context.Background(), tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestPullMsg(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAccountClient := mock_account_go.NewMockIClient(ctrl)
	mockSoulmateClient := aigc_soulmate.NewMockAigcSoulmateClient(ctrl)

	handler := NewSoulmateIMHandler(mockAccountClient, mockSoulmateClient, nil, nil,
		nil, nil, nil, nil, nil, nil, nil)

	tests := []struct {
		name    string
		setup   func()
		req     *PullReq
		wantErr bool
	}{
		{
			name:  "Invalid target ID",
			setup: func() {},
			req: &PullReq{
				Target: "invalid",
			},
			wantErr: false,
		},
		{
			name: "Successful message pull",
			setup: func() {
				bin, _ := proto.Marshal(&chat_bot.ImMsg{
					Type:    chat_bot.ImMsgType_ImMsgTypeText,
					Content: "Hello",
					SentAt:  time.Now().UnixMilli(),
					MsgId:   genMsgID(),
				})
				mockSoulmateClient.EXPECT().
					PullAIMessage(gomock.Any(), gomock.Any()).
					Return(&aigc_soulmate.PullAIMessageResp{
						MsgList: []*aigc_soulmate.AIMessage{
							{
								Bin:       bin,
								PartnerId: 123,
							},
						},
					}, nil)
				mockSoulmateClient.EXPECT().ClearAIMessage(gomock.Any(), gomock.Any()).Return(&aigc_soulmate.ClearAIMessageResp{}, nil).AnyTimes()
			},
			req: &PullReq{
				Uid:    123,
				Target: "123",
				MsgID:  "start_msg_id",
				Limit:  10,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			_, err := handler.PullMsg(context.Background(), tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
