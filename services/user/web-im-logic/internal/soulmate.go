package internal

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"golang.52tt.com/kaihei-pkg/aigc/msg/builder"
	game_fre_server "golang.52tt.com/protocol/services/game-fre-server"
	"golang.52tt.com/services/user/web-im-logic/internal/event"

	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	"golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/pkg/abtest"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_ext_content "golang.52tt.com/protocol/services/aigc/aigc-ext-content"
	aigc_game "golang.52tt.com/protocol/services/aigc/aigc-game"
	aigc_push "golang.52tt.com/protocol/services/aigc/aigc-push"
	aigc_soulmate_middle "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	aigc_trigger "golang.52tt.com/protocol/services/aigc/aigc-trigger"
	"golang.52tt.com/protocol/services/rcmd/partner_common"
	"golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner_kafka"
	config "golang.52tt.com/services/user/web-im-logic/internal/config/ttconfig/web_im_logic"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata"
	account_go "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	rcmd_ai_partner "golang.52tt.com/clients/rcmd/ai_partner"
	"golang.52tt.com/pkg/audit"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/web-im-logic"
	"golang.52tt.com/protocol/common/status"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	cybros_arbiter_v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	rcmd_ai_partner_pb "golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
	"gopkg.in/mgo.v2/bson"
)

type SoulmateIMHandler struct {
	accountClient       account_go.IClient
	soulmateClient      aigc_soulmate.AigcSoulmateClient
	censoringClient     censoring_proxy.IClient
	rcmdAIPartnerClient rcmd_ai_partner.IClient
	obsCli              *obsgateway.Client

	aigcSoulmateMiddleClient aigc_soulmate_middle.AigcSoulmateMiddleClient
	aigcCommonClient         aigc_common.AigcCommonClient
	aigcGameClient           aigc_game.AigceGameClient

	abTestClient abtest.IABTestClient

	gameFreCli game_fre_server.GameFreServerClient

	eventBus event.EventBus
}

func NewSoulmateIMHandler(
	accountClient account_go.IClient,
	soulmateClient aigc_soulmate.AigcSoulmateClient,
	censoringClient censoring_proxy.IClient,
	rcmdAIPartnerClient rcmd_ai_partner.IClient,
	obsCli *obsgateway.Client,
	aigcSoulmateMiddleClient aigc_soulmate_middle.AigcSoulmateMiddleClient,
	aigcCommonClient aigc_common.AigcCommonClient,
	abTestClient abtest.IABTestClient,
	aigcGameClient aigc_game.AigceGameClient,
	gameFreCli game_fre_server.GameFreServerClient,
	eventBus event.EventBus,
) *SoulmateIMHandler {
	return &SoulmateIMHandler{
		accountClient:            accountClient,
		soulmateClient:           soulmateClient,
		censoringClient:          censoringClient,
		rcmdAIPartnerClient:      rcmdAIPartnerClient,
		obsCli:                   obsCli,
		aigcSoulmateMiddleClient: aigcSoulmateMiddleClient,
		aigcCommonClient:         aigcCommonClient,
		abTestClient:             abTestClient,
		aigcGameClient:           aigcGameClient,
		gameFreCli:               gameFreCli,
		eventBus:                 eventBus,
	}
}

func (s *SoulmateIMHandler) SendMsg(ctx context.Context, req *SendReq) (*SendRsp, error) {
	rsp := new(SendRsp)
	log.InfoWithCtx(ctx, "SendMsg req: %+v", req)

	id, err := strconv.Atoi(req.Target)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendMsg invalid target: %s", req.Target)
		return rsp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	user, err := s.accountClient.GetUserByUid(ctx, req.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendMsg GetUserByUid uid(%d) err: %v", req.Uid, err)
		return rsp, err
	}

	partnerResp, err := s.soulmateClient.GetAIPartner(ctx, &aigc_soulmate.GetAIPartnerReq{
		Id: uint32(id),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendMsg GetAIPartner id(%d) err: %v", id, err)
		return rsp, err
	}

	partner := partnerResp.GetPartner()
	if partner == nil || partner.GetUid() != req.Uid {
		log.WarnWithCtx(ctx, "SendMsg invalid partner: %+v", partner)
		return rsp, protocol.NewExactServerError(nil, status.ErrChatBotAiPartnerNotFound)
	}
	abtestCtx, cancel := context.WithTimeout(ctx, 200*time.Millisecond)
	defer cancel()
	cfg := config.GetWebImLogicConfig().GetSentenceAbtest()
	var useTTHint bool
	if cfg == nil || cfg.ArgvName == "" {
		useTTHint = false
	} else {
		if value, err := s.abTestClient.GetUidTestArgVal(abtestCtx, req.Uid, cfg.ArgvName); err != nil {
			log.ErrorWithCtx(abtestCtx, "SendMsg GetUidTestArgVal uid(%d) argvName(%s) err: %v", req.Uid, cfg.ArgvName, err)
			useTTHint = false
		} else {
			useTTHint = value == cfg.ExpectValue
		}
	}
	msg := req.Msg
	msg.MsgId = genMsgID()
	msg.SentAt = time.Now().UnixMilli()
	rsp.MsgID = msg.GetMsgId()
	rsp.SentAt = msg.GetSentAt()
	extMap := make(map[string]string)
	needSentenceCount := s.shouldCheckSentenceCount(ctx, req.Msg)

	var isExactlyReachLimit bool
	if useTTHint {
		extMap["soulmate_sentence_refactor_gray_release"] = "tt"
		if needSentenceCount {
			// 扣句数
			sentenceRsp, err := s.aigcSoulmateMiddleClient.ConsumeSentenceCount(ctx, &aigc_soulmate_middle.ConsumeSentenceCountReq{
				Uid: req.Uid,
				Entity: &aigc_soulmate_middle.Entity{
					Id:   partner.GetId(),
					Type: aigc_soulmate_middle.Entity_TYPE_PARTNER,
				},
				RoleType:     uint32(partner.GetRole().GetType()),
				BusinessType: uint32(aigc_trigger.BusinessType_BUSINESS_TYPE_SINGLE_CHAT),
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "SendMsg ConsumeSentenceCount uid:%d err: %v", req.Uid, err)
				return rsp, err
			}
			if !sentenceRsp.GetSuccess() {
				if sentenceRsp.GetNeedExtraTip() {
					// 所有句数消耗完，发提示
					err = s.pushTipMsg(ctx, partner, req.Msg)
					if err != nil {
						log.ErrorWithCtx(ctx, "SendMsg pushTipMsg uid:%d err: %v", req.Uid, err)
					}
				}
				log.InfoWithCtx(ctx, "SendMsg sentenceRsp:%s no count req:(%+v) rsp(%+v)", sentenceRsp.String(), req, rsp)
				return rsp, nil
			}
			var tipType int
			if sentenceRsp.GetNeedExtraTip() {
				tipType = int(aigc_push.TipType_TIP_TYPE_EXTRA)
			} else if sentenceRsp.GetNeedCurDayTip() {
				tipType = int(aigc_push.TipType_TIP_TYPE_CUR_DAY)
			} else if sentenceRsp.GetNeedSpecialTip() {
				tipType = int(aigc_push.TipType_TIP_TYPE_ROLE_SPECIAL)
			} else {
				tipType = int(aigc_push.TipType_TIP_TYPE_UNSPECIFIED)
			}
			extMap[aigc_ext_content.ExtMapKey_EXT_MAP_KEY_SENTENCE_TIP.String()] = strconv.Itoa(tipType)

			extMap["sentence_type"] = sentenceRsp.GetUsedType().String()
			isExactlyReachLimit = sentenceRsp.GetSuccess() && sentenceRsp.GetNeedExtraTip()
		}

	} else {

		extMap["soulmate_sentence_refactor_gray_release"] = "aigc"
		if config.GetWebImLogicConfig().GetSentenceSwitch() && needSentenceCount {
			// 扣句数
			sentenceRsp, err := s.aigcSoulmateMiddleClient.ConsumeSentenceCount(ctx, &aigc_soulmate_middle.ConsumeSentenceCountReq{
				Uid: req.Uid,
				Entity: &aigc_soulmate_middle.Entity{
					Id:   partner.GetId(),
					Type: aigc_soulmate_middle.Entity_TYPE_PARTNER,
				},
				RoleType:     uint32(partner.GetRole().GetType()),
				BusinessType: uint32(aigc_trigger.BusinessType_BUSINESS_TYPE_SINGLE_CHAT),
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "SendMsg ConsumeSentenceCount uid:%d err: %v", req.Uid, err)
			}
			log.InfoWithCtx(ctx, "SendMsg use aigc hint sentenceRsp:%s err:%v", sentenceRsp.String(), err)
		}
	}

	//语音
	if req.Msg.GetContentType() == uint32(pb.ImMsgContentType_IM_MSG_CONTENT_TYPE_TEXT_VOICE) && req.Msg.GetImBusiType() == uint32(pb.ImBusiType_IM_BUSI_TYPE_MULTI_ROLE) {
		//审核
		asrContent := &aigc_ext_content.AsrExtContent{}
		err = json.Unmarshal(req.Msg.GetExt(), asrContent)
		if err != nil {
			log.ErrorWithCtx(ctx, "json.Unmarshal err:%v, asrContent:%+v", err, asrContent)
			return rsp, err
		}
		if msg.GetContent() != "" && asrContent.GetAsrTextType() != uint32(aigc_ext_content.AsrTextType_ASR_TEXT_TYPE_INVALID) {
			err := s.syncScanText(ctx, user, audit.SCENE_CODE_AI_PARTNER_MSG, msg.GetContent())
			if err != nil {
				log.ErrorWithCtx(ctx, "SendMsg syncScanText uid(%d) text(%s) err: %v", req.Uid, msg.GetContent(), err)
				if err.Error() == protocol.NewExactServerError(nil, status.ErrChatBotInvalidMsgContent).Error() && asrContent.GetKey() != "" {
					delErr := s.obsCli.Delete(ctx, "tt", "aigcyuyinasr", asrContent.GetKey()) //这个scope是不审核的
					if delErr != nil {
						log.ErrorWithCtx(ctx, "obsCli.Delete err, key:%s", asrContent.GetKey())
					}
					log.InfoWithCtx(ctx, "Delete key:%s", asrContent.GetKey())
				}
				return rsp, err
			}
		}

		/*asyncCtx, asyncCancel := context.WithTimeout(metadata.NewContext(ctx), 3*time.Second)
		go func() {
			defer asyncCancel()
			err = s.obsCli.Delete(asyncCtx, "tt", "gamescreenshot2", asrContent.GetKey())
			if err != nil {
				log.ErrorWithCtx(ctx, "obsCli.Delete err, key:%s", asrContent.GetKey())
			}
		}()*/
	} else {

		switch pb.ImMsg_Type(msg.GetType()) {
		case pb.ImMsg_TYPE_TEXT, pb.ImMsg_TYPE_AI_PARTNER:
			if msg.GetContent() != "" {
				err := s.syncScanText(ctx, user, audit.SCENE_CODE_AI_PARTNER_MSG, msg.GetContent())
				if err != nil {
					log.ErrorWithCtx(ctx, "SendMsg syncScanText uid(%d) text(%s) err: %v", req.Uid, msg.GetContent(), err)
					return rsp, err
				}
			}
		case pb.ImMsg_TYPE_EMOTICON:
		default:
			log.WarnWithCtx(ctx, "SendMsg invalid msg type %d", msg.GetType())
			return rsp, protocol.NewExactServerError(nil, status.ErrChatBotInvalidMsgType)
		}

	}

	receiveReq := &rcmd_ai_partner_pb.ReceiveMsgFromUserReq{
		Uid:                 partner.GetUid(),
		AiPartnerId:         partner.GetId(),
		MsgType:             rcmd_ai_partner_pb.ReceiveMsgFromUserReq_MsgType(msg.GetType()),
		Content:             msg.GetContent(),
		Ext:                 msg.GetExt(),
		MsgId:               msg.GetMsgId(),
		AiRoleType:          rcmd_ai_partner_pb.AIRoleType(partner.GetRole().GetType()),
		ImBusiType:          msg.GetImBusiType(),
		ImCmdType:           msg.GetImCmdType(),
		ImContentType:       msg.GetContentType(),
		ExtraMap:            extMap,
		IsExactlyReachLimit: isExactlyReachLimit,
	}
	if len(msg.GetExt()) > 0 {
		uniformMsg := &rcmd_ai_partner_kafka.UniformMsg{}
		json.Unmarshal(msg.GetExt(), uniformMsg)
		if uniformMsg.GetCtxInfo().GetGameId() != "" {
			gameReq := &aigc_game.GetGameInfoRequest{
				Id: uniformMsg.GetCtxInfo().GetGameId(),
			}
			gameResp, err := s.aigcGameClient.GetGameInfo(ctx, gameReq)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendMsg GetGameInfo req(%+v) err: %v", gameReq, err)
				return rsp, err
			}

			if game := gameResp.GetInfo(); game != nil {
				receiveReq.InteractiveGameInfo = &partner_common.GameInfo{
					GameId:    gameResp.GetInfo().GetId(),
					Prompt:    game.GetPrompt(),
					SessionId: uniformMsg.GetCtxInfo().GetCtxId(),
				}
			}
		}
	}
	_, err = s.rcmdAIPartnerClient.ReceiveMsgFromUser(ctx, receiveReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendMsg ReceiveMsgFromUser req(%+v) err: %v", receiveReq, err)
		return rsp, err
	}
	go func() {
		asyncCtx, asyncCancel := context.WithTimeout(metadata.NewContext(ctx), 5*time.Second)
		defer asyncCancel()
		_ = s.timeAfterSentGiftTipPush(asyncCtx, partner.GetUid(), partner.GetId(), uint32(partner.GetRole().GetType()))
	}()

	log.InfoWithCtx(ctx, "SendMsg ReceiveMsgFromUser req(%+v) finished", receiveReq)

	log.InfoWithCtx(ctx, "SendMsg useTTHint:%v req(%+v) rsp: %+v", useTTHint, req, rsp)
	return rsp, nil
}

func (s *SoulmateIMHandler) shouldCheckSentenceCount(ctx context.Context, msg *pb.ImMsg) bool {
	if msg.GetType() != uint32(pb.ImMsg_TYPE_AI_PARTNER) {
		return true
	}
	rcmdMsg := &rcmd_ai_partner_kafka.UniformMsg{}
	err := json.Unmarshal(msg.GetExt(), rcmdMsg)
	if err != nil {
		log.WarnWithCtx(ctx, "shouldCheckSentenceCount Unmarshal msg:%s ext err: %v", msg.String(), err)
		return true
	}
	switch rcmdMsg.GetType() {
	case rcmd_ai_partner_kafka.MsgType_Text, rcmd_ai_partner_kafka.MsgType_Meme, rcmd_ai_partner_kafka.MsgType_AnimatedMeme,
		rcmd_ai_partner_kafka.MsgType_TTSMsg, rcmd_ai_partner_kafka.MsgType_QuickReply, rcmd_ai_partner_kafka.MsgType_PetReply,
		rcmd_ai_partner_kafka.MsgType_AIRcmdReply, rcmd_ai_partner_kafka.MsgType_ContinueChat:
		return true
	default:
		return false

	}
}

func (s *SoulmateIMHandler) PullMsg(ctx context.Context, req *PullReq) (*PullRsp, error) {
	rsp := new(PullRsp)
	log.InfoWithCtx(ctx, "PullMsg req: %+v", req)

	var partnerId uint32
	if req.Target != "" {
		id, err := strconv.Atoi(req.Target)
		if err != nil {
			log.WarnWithCtx(ctx, "PullMsg invalid target: %s", req.Target)
			return rsp, nil
		}

		partnerId = uint32(id)
	}

	pullReq := &aigc_soulmate.PullAIMessageReq{
		Uid:       req.Uid,
		PartnerId: partnerId,
		MsgId:     req.MsgID,
		Limit:     req.Limit,
	}
	pullResp, err := s.soulmateClient.PullAIMessage(ctx, pullReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "PullMsg PullAIMessage req(%+v) err: %v", pullReq, err)
		return rsp, err
	}
	if len(pullResp.GetMsgList()) == 0 {
		log.DebugWithCtx(ctx, "PullMsg PullAIMessage uid(%d) partnerId(%d) msg empty", req.Uid, partnerId)
		return rsp, nil
	}

	for _, msg := range pullResp.GetMsgList() {
		var imMsg chat_bot.ImMsg
		err := proto.Unmarshal(msg.GetBin(), &imMsg)
		if err != nil {
			log.WarnWithCtx(ctx, "PullMsg Unmarshal msg bin err: %v", err)
			continue
		}

		rsp.MsgList = append(rsp.MsgList, &pb.WebImMsg{
			Target: strconv.Itoa(int(msg.GetPartnerId())),
			Msg: &pb.ImMsg{
				Type:     uint32(imMsg.GetType()),
				Content:  imMsg.GetContent(),
				Ext:      imMsg.GetExt(),
				SentAt:   imMsg.GetSentAt(),
				MsgId:    imMsg.GetMsgId(),
				ComeFrom: imMsg.GetComeFrom(),

				ImCmdType:   imMsg.GetImCmdType(),
				ImBusiType:  imMsg.GetImBusiType(),
				ContentType: uint32(imMsg.GetContentType()),
			},
		})
	}

	asyncCtx, asyncCancel := context.WithTimeout(metadata.NewContext(ctx), 3*time.Second)
	go func() {
		defer asyncCancel()

		clearReq := &aigc_soulmate.ClearAIMessageReq{
			Uid:       req.Uid,
			PartnerId: partnerId,
			MsgId:     req.MsgID,
		}
		_, err := s.soulmateClient.ClearAIMessage(asyncCtx, clearReq)
		if err != nil {
			log.ErrorWithCtx(asyncCtx, "PullMsg ClearAIMessage req(%+v) err: %v", clearReq, err)
			return
		}
	}()

	log.InfoWithCtx(ctx, "PullMsg req(%+v) rsp: %+v", req, rsp)
	return rsp, nil
}

func (s *SoulmateIMHandler) syncScanText(ctx context.Context, user *account_go.User, scene audit.SceneCodeType, text string) error {
	resp, err := s.censoringClient.Text().SyncScanText(ctx, &cybros_arbiter_v2.SyncTextCheckReq{
		Context: &cybros_arbiter_v2.TaskContext{
			SceneCode:   string(scene),
			DeviceInfo:  &cybros_arbiter_v2.Device{},
			BelongObjId: user.GetAlias(),
			UserInfo: &cybros_arbiter_v2.User{
				Id:       uint64(user.GetUid()),
				Alias:    user.GetAlias(),
				Phone:    user.GetPhone(),
				Nickname: user.GetNickname(),
			},
		},
		Text: text,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "syncScanText SyncScanText uid(%d) sceneCode(%s) text(%s) err: %v", user.GetUid(), scene, text, err)
		return err
	}

	log.InfoWithCtx(ctx, "syncScanText SyncScanText uid(%d) sceneCode(%s) text(%s) result: %d", user.GetUid(), scene, text, resp.GetResult())

	if cybros_arbiter_v2.Suggestion(resp.GetResult()) != cybros_arbiter_v2.Suggestion_PASS {
		return protocol.NewExactServerError(nil, status.ErrChatBotInvalidMsgContent)
	}

	return nil
}

func genMsgID() string {
	return bson.NewObjectId().Hex()
}

func (s *SoulmateIMHandler) pushTipMsg(ctx context.Context, partner *aigc_soulmate.AIPartner, msg *pb.ImMsg) error {
	var (
		role = partner.GetRole()

		pushPartner = &aigc_push.Partner{
			Id:  partner.GetId(),
			Uid: partner.GetUid(),

			Name: partner.GetName(),

			Role: &aigc_push.Partner_Role{
				Id:   role.GetId(),
				Uid:  role.GetUid(),
				Type: uint32(role.GetType()),

				Sex:   role.GetSex(),
				State: uint32(role.GetState()),

				Name:   role.GetName(),
				Avatar: role.GetAvatar(),
			},
		}
	)

	pushReq := &aigc_push.PushSentenceTipToPartnerRequest{
		TipType: aigc_push.TipType_TIP_TYPE_EXTRA,
		Uid:     pushPartner.GetUid(),
		Partner: pushPartner,
	}
	//_, err := s.aigcPushClient.PushSentenceTipToPartner(ctx, pushReq)
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "pushGroupTipMsg PushSentenceTipToPartner req(%s) err: %v", pushReq.String(), err)
	//}
	marshalBytes, err := proto.Marshal(pushReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushGroupTipMsg Marshal pushReq err: %v", err)
		return err
	}

	receiveMsg, err := proto.Marshal(msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushGroupTipMsg Marshal msg pushReq:%s err: %v", pushReq.String(), err)
	}
	// 走延时发送
	_, err = s.aigcCommonClient.AddToDelayQueue(ctx, &aigc_common.AddToDelayQueueRequest{
		DelayTime:  2,
		Data:       marshalBytes,
		PushTo:     aigc_common.PushTo_PUSH_TO_PARTNER,
		ReceiveMsg: receiveMsg,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "pushGroupTipMsg AddToDelayQueue req(%s) err: %v", pushReq.String(), err)
		return err
	}
	log.InfoWithCtx(ctx, "pushMsg AddToDelayQueue pushReq(%s)", pushReq)
	return nil
}

// 统计用户发送的聊天，给用户送礼提示推送
func (s *SoulmateIMHandler) timeAfterSentGiftTipPush(ctx context.Context, uid, partnerId, roleType uint32) error {
	if roleType != uint32(aigc_soulmate.AIRoleType_AIRoleTypeGame) {
		log.DebugWithCtx(ctx, "sentGiftTipPush uid(%d) partnerId(%d) roleType(%d) not game role, no need to push gift tip", uid, partnerId, roleType)
		return nil
	}
	// 获取当前用户的句数
	sentenceRsp, err := s.aigcCommonClient.GetSentenceCountMap(ctx, &aigc_common.GetSentenceCountMapRequest{
		Uid: uid,
		Entity: &aigc_common.Entity{
			Id:   partnerId,
			Type: aigc_common.Entity_TYPE_PARTNER,
		},
		Type: []uint32{
			uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED),
			uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_CUR_DAY),
			uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_EXTRA),
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "sentGiftTipPush BatGetCurSentenceCount uid(%d) partnerId(%d) roleType(%d) err: %v", uid, partnerId, roleType, err)
		return err
	}
	if sentenceRsp.GetCountMap() == nil {
		log.WarnWithCtx(ctx, "sentGiftTipPush BatGetCurSentenceCount uid(%d) partnerId(%d) roleType(%d) no curNumMap", uid, partnerId, roleType)
		return nil
	}
	log.DebugWithCtx(ctx, "sentGiftTipPush BatGetCurSentenceCount uid(%d) partnerId(%d) roleType(%d) curNumMap: %v", uid, partnerId, roleType, sentenceRsp.GetCountMap())
	totalNum := sentenceRsp.GetCountMap()[uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED)] +
		sentenceRsp.GetCountMap()[uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_CUR_DAY)] +
		sentenceRsp.GetCountMap()[uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_EXTRA)]

	if totalNum < config.GetWebImLogicConfig().GetSentenceSendGiftTipNum() {
		log.InfoWithCtx(ctx, "sentGiftTipPush uid(%d) partnerId(%d) roleType(%d) totalNum(%d) less than setNum (%d), no need to push gift tip",
			uid, partnerId, roleType, totalNum, config.GetWebImLogicConfig().GetSentenceSendGiftTipNum())
		return nil
	}

	// 获取用户的频率限制
	userLimitSuffix := time.Now().Format("20060102")
	partnerLimitSuffix := fmt.Sprintf("%s:%d", time.Now().Format("20060102"), partnerId)
	freResp, err := s.gameFreCli.BatGetFreCountBySources(ctx, &game_fre_server.BatGetFreCountBySourcesReq{
		Items: []*game_fre_server.BatGetFreItem{
			{
				FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_USER_LIMIT),
				Suffix:         userLimitSuffix,
				UserId:         uid,
			},
			{
				FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_ROLE_LIMIT),
				UserId:         uid,
				Suffix:         partnerLimitSuffix,
			},
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "sentGiftTipPush BatGetFreCountBySources fail, uid:%d, partnerId:%d, err:%v", uid, partnerId, err)
		return err
	}
	if freResp.GetResMap() == nil {
		log.WarnWithCtx(ctx, "sentGiftTipPush BatGetFreCountBySources uid(%d) partnerId(%d) roleType(%d) no resMap", uid, partnerId, roleType)
		return nil
	}

	userItemKey := fmt.Sprintf("%d_%d_%s", uid, game_fre_server.FreCountSource_FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_USER_LIMIT, userLimitSuffix)
	partnerItemKey := fmt.Sprintf("%d_%d_%s", uid, game_fre_server.FreCountSource_FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_ROLE_LIMIT, partnerLimitSuffix)
	// 用户一天只能触发x次推送
	if freResp.GetResMap()[userItemKey] >= config.GetWebImLogicConfig().GetUserSendGiftPushLimitCount() {
		log.InfoWithCtx(ctx, "sentGiftTipPush uid(%d) partnerId(%d) roleType(%d) totalNum(%d) reach user limit", uid, partnerId, roleType, totalNum)
		return nil
	}

	// 用户对一个角色一天只能触发一次推送
	if freResp.GetResMap()[partnerItemKey] >= 1 {
		log.InfoWithCtx(ctx, "sentGiftTipPush uid(%d) partnerId(%d) roleType(%d) totalNum(%d) reach partner limit", uid, partnerId, roleType, totalNum)
		return nil
	}

	// 设置频率(这里game-fre-server服务这个接口可以优化，直接批量设置然后返回数量，可以减少一次请求)
	_, err = s.gameFreCli.BatIncFreCountBySources(ctx, &game_fre_server.BatIncFreCountBySourcesReq{
		Items: []*game_fre_server.BatIncFreItem{
			{
				FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_USER_LIMIT),
				Suffix:         userLimitSuffix,
				ExpireTime:     uint64(time.Hour * 24),
				UserId:         uid,
			},
			{
				FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_ROLE_LIMIT),
				Suffix:         partnerLimitSuffix,
				ExpireTime:     uint64(time.Hour * 24),
				UserId:         uid,
			},
		}})
	if err != nil {
		log.ErrorWithCtx(ctx, "sentGiftTipPush BatIncFreCountBySources fail, uid:%d,partnerId:%d, err:%v", uid, partnerId, err)
		return err
	}
	log.InfoWithCtx(ctx, "sentGiftTipPush BatIncFreCountBySources success, uid:%d, partnerId:%d, roleType:%d, totalNum:%d, freCountMap:%v",
		uid, partnerId, roleType, totalNum, freResp.GetResMap())

	// 设置延时发送
	select {
	case <-time.After(config.GetWebImLogicConfig().GetSentenceSendGiftPushDelay()):
		// 发送礼物提示
		err = s.sendSingleMsg(ctx, uid, partnerId)
		if err != nil {
			log.ErrorWithCtx(ctx, "sentGiftTipPush sendSingleMsg uid(%d) partnerId(%d) roleType(%d) err: %v", uid, partnerId, roleType, err)
			return err
		}
		log.InfoWithCtx(ctx, "sentGiftTipPush sendSingleMsg uid(%d) partnerId(%d) roleType(%d) success", uid, partnerId, roleType)
	}
	//t := time.AfterFunc(config.GetWebImLogicConfig().GetSentenceSendGiftPushDelay(), func() {})
	//defer t.Stop()

	return nil
}

func (s *SoulmateIMHandler) sendSingleMsg(ctx context.Context, uid, partnerId uint32) error {
	content := config.GetWebImLogicConfig().GetSentenceSendGiftPushContent()
	msg := builder.BuildUserPresentGuideMsg(content)
	return s.eventBus.Publish(ctx, event.PubEventSendSingleMsg, strconv.Itoa(int(partnerId)), &chat_bot.SingleMsgSendEvent{
		Uid:        uid,
		PartnerId:  partnerId,
		SendingMsg: msg,
		Opt:        &chat_bot.SendOption{},
	})
}
