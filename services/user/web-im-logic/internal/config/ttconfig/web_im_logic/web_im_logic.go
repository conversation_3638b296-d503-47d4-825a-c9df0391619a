package config

import (
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"sync/atomic"
	"time"
)

type WebImLogicConfig struct {
	// 群聊消息跳转链接
	AIGroupMsgPushConfig *AiGroupMsgPushConfig `json:"ai_group_msg_push_config"`

	SentenceAbtest *AbtestConfig `json:"sentence_abtest"` // AI伴侣-句数改造实验

	SentenceSwitch bool `json:"sentence_switch"`

	// 送礼提示达到的句数阈值
	SentenceSendGiftTipNum uint32 `json:"sentence_send_gift_tip_num"` // 送礼提示
	// 用户送礼提示推送限制次数
	UserSendGiftPushLimitCount uint32 `json:"user_send_gift_push_limit_count"` // 用户推送限制次数
	// 送礼提示推送内容
	SentenceSendGiftPushContent string `json:"sentence_send_gift_push_content"` // 送礼提示推送内容
	// 延迟多少秒后才推送
	SentenceSendGiftPushDelay uint32 `json:"sentence_send_gift_push_delay"` // 送礼提示推送延迟
}

type AiGroupMsgPushConfig struct {
	WebUrl    string `json:"web_url"`    // H5链接
	ClientUrl string `json:"client_url"` // 客户端短链

}
type AbtestConfig struct {
	ArgvName    string `json:"argv_name"`
	ExpectValue string `json:"expect_value"`
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *WebImLogicConfig) Format() error {
	return nil
}

var (
	atomicWebImLogicConfig *atomic.Value
)

func init() {
	//if err := InitWebImLogicConfig(); err != nil {
	//    panic(err)
	//}
}

// InitWebImLogicConfig
// 可以选择外部初始化或者直接init函数初始化
func InitWebImLogicConfig() error {
	cfg := &WebImLogicConfig{}
	atomCfg, err := ttconfig.AtomLoad("web-im-logic", cfg)
	if nil != err {
		return err
	}
	atomicWebImLogicConfig = atomCfg
	return nil
}

func GetWebImLogicConfig() *WebImLogicConfig {
	return atomicWebImLogicConfig.Load().(*WebImLogicConfig)
}

func (s *WebImLogicConfig) GetAIGroupMsgPushConfig() *AiGroupMsgPushConfig {
	if s == nil {
		return nil
	}
	return s.AIGroupMsgPushConfig
}

func (s *WebImLogicConfig) GetSentenceAbtest() *AbtestConfig {
	if s == nil {
		return nil
	}
	return s.SentenceAbtest
}

func (s *WebImLogicConfig) GetSentenceSwitch() bool {
	if s == nil {
		return false
	}
	return s.SentenceSwitch
}

func (s *WebImLogicConfig) GetSentenceSendGiftTipNum() uint32 {
	if s == nil || s.SentenceSendGiftTipNum == 0 {
		return 100
	}
	return s.SentenceSendGiftTipNum
}

func (s *WebImLogicConfig) GetUserSendGiftPushLimitCount() uint32 {
	if s == nil || s.UserSendGiftPushLimitCount == 0 {
		return 2
	}
	return s.UserSendGiftPushLimitCount
}

func (s *WebImLogicConfig) GetSentenceSendGiftPushContent() string {
	if s == nil || s.SentenceSendGiftPushContent == "" {
		return "听说送礼可能触发神秘回礼哦~"
	}
	return s.SentenceSendGiftPushContent
}

func (s *WebImLogicConfig) GetSentenceSendGiftPushDelay() time.Duration {
	if s == nil || s.SentenceSendGiftPushDelay == 0 {
		return 3 * time.Second
	}
	return time.Second * time.Duration(s.SentenceSendGiftPushDelay)
}
