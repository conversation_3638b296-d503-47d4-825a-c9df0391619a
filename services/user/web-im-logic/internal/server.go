package internal

import (
	"context"
	game_fre_server "golang.52tt.com/protocol/services/game-fre-server"

	"golang.52tt.com/pkg/abtest"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_game "golang.52tt.com/protocol/services/aigc/aigc-game"

	"golang.52tt.com/clients/obsgateway"

	aigc_push "golang.52tt.com/protocol/services/aigc/aigc-push"
	aigc_trigger "golang.52tt.com/protocol/services/aigc/aigc-trigger"
	config "golang.52tt.com/services/user/web-im-logic/internal/config/ttconfig/web_im_logic"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"
	"gitlab.ttyuyin.com/tyr/x/log"
	account_go "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	push "golang.52tt.com/clients/push-notification/v2"
	rcmd_ai_partner "golang.52tt.com/clients/rcmd/ai_partner"
	"golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/web-im-logic"
	"golang.52tt.com/protocol/common/status"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	aigc_soulmate_middle "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	"golang.52tt.com/protocol/services/demo/echo"
	game_red_dot "golang.52tt.com/protocol/services/game-red-dot"
	timeline_v2 "golang.52tt.com/protocol/services/timeline-v2"
	"golang.52tt.com/services/user/web-im-logic/internal/event"
	"golang.52tt.com/services/user/web-im-logic/internal/event/eventlink"
)

type StartConfig struct {
	EventLink *middleware_event.Options `json:"event_link"`
}

const abtestUrl = "http://abtestlogic.commonservice.svc.cluster.local:8000/AbtestLogicService/GetUsersAbtestByTag"

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	eventBus, err := eventlink.NewEventBus(ctx, cfg.EventLink)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewEventBus cfg(%+v) err: %v", cfg.EventLink, err)
		return nil, err
	}

	censoringClient := censoring_proxy.NewClient()

	accountClient, err := account_go.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer account_go.NewClient err: %v", err)
		return nil, err
	}

	soulmateClient, err := aigc_soulmate.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer aigc_soulmate.NewClient err: %v", err)
		return nil, err
	}

	rcmdPartnerClient, err := rcmd_ai_partner.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer rcmd_ai_partner.NewClient err: %v", err)
		return nil, err
	}

	seqgenV2Client := seqgen.NewIClient()
	timelineV2Client, err := timeline_v2.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer timeline_v2.NewClient err: %v", err)
		return nil, err
	}
	groupClient, err := aigc_group.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer aigc_group.NewClient err: %v", err)
		return nil, err
	}

	soulmateMiddleCli, err := aigc_soulmate_middle.NewClient(ctx)

	redDotClient, err := game_red_dot.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer game_red_dot.NewClient err: %v", err)
		return nil, err
	}

	triggleCli, err := aigc_trigger.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer aigc_trigger.NewClient err: %v", err)
		return nil, err
	}

	err = config.InitWebImLogicConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer config.InitWebImLogicConfig err: %v", err)
		return nil, err
	}

	push, err := push.NewClient()
	if err != nil {
		log.Errorf("NewClient new push client err: %v", err)
	}

	obsCli, err := obsgateway.NewClient()
	if err != nil {
		log.Errorf("obsgateway.NewClient err: %v", err)
	}
	pusher, err := aigc_push.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer aigc_push.NewClient err: %v", err)
	}

	aigcCommonClient, err := aigc_common.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer aigc_common.NewClient err: %v", err)
	}

	aigcGameClient, err := aigc_game.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer aigc_game.NewClient err: %v", err)
	}

	abtestClient := abtest.NewABTestClient(abtestUrl, uint32(abtest.APPID_TTyuyin), "")

	gameFreCli, _ := game_fre_server.NewClient(ctx)

	s := &Server{
		SeqgenV2Client:    seqgenV2Client,
		TimelineV2Client:  timelineV2Client,
		soulmateMiddleCli: soulmateMiddleCli,
		aiClient:          rcmdPartnerClient,
		groupClient:       groupClient,
		redDotClient:      redDotClient,
		soulmateClient:    soulmateClient,
		accountClient:     accountClient,
		triggleCli:        triggleCli,
		pushCli:           push,
		obsCli:            obsCli,

		pusher:           pusher,
		aigcCommonClient: aigcCommonClient,
		abTestClient:     abtestClient,

		eventBus: eventBus,
	}
	s.handlers = map[pb.WebImCmd]WebIMHandler{
		pb.WebImCmd_WEB_IM_CMD_AI_PARTNER: NewSoulmateIMHandler(accountClient, soulmateClient, censoringClient,
			rcmdPartnerClient, obsCli, soulmateMiddleCli, aigcCommonClient, abtestClient, aigcGameClient, gameFreCli, eventBus),
	}

	return s, nil
}

type Server struct {
	handlers          map[pb.WebImCmd]WebIMHandler
	SeqgenV2Client    seqgen.IClient
	TimelineV2Client  *timeline_v2.Client
	soulmateMiddleCli aigc_soulmate_middle.AigcSoulmateMiddleClient
	aiClient          rcmd_ai_partner.IClient
	groupClient       aigc_group.AigcGroupClient
	redDotClient      game_red_dot.GameRedDotClient
	soulmateClient    aigc_soulmate.AigcSoulmateClient
	accountClient     account_go.IClient
	triggleCli        aigc_trigger.AigcTriggerClient
	pushCli           push.IClient
	obsCli            *obsgateway.Client

	pusher           aigc_push.AigcPushClient
	aigcCommonClient aigc_common.AigcCommonClient
	abTestClient     abtest.IABTestClient

	eventBus event.EventBus
}

func (s *Server) ShutDown() {}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) SendWebImMsg(ctx context.Context, in *pb.SendWebImMsgRequest) (out *pb.SendWebImMsgResponse, err error) {
	out = new(pb.SendWebImMsgResponse)

	if in.GetTarget() == "" || in.GetMsg() == nil {
		log.WarnWithCtx(ctx, "SendWebImMsg invalid in: %+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.WarnWithCtx(ctx, "SendWebImMsg invalid user")
		return out, nil
	}

	h := s.handlers[in.GetCmd()]
	if h == nil {
		log.WarnWithCtx(ctx, "SendWebImMsg cmd(%d) handler not found", in.GetCmd())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	sendReq := &SendReq{
		Uid:    uid,
		Target: in.GetTarget(),

		Msg: in.GetMsg(),
	}
	sendRsp, err := h.SendMsg(ctx, sendReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendWebImMsg SendMsg cmd(%d) req(%+v) err: %v", in.GetCmd(), sendReq, err)
		return out, err
	}

	out.MsgId = sendRsp.MsgID
	out.SentAt = sendRsp.SentAt

	return
}

func (s *Server) GetWebImMsgList(ctx context.Context, in *pb.GetWebImMsgListRequest) (out *pb.GetWebImMsgListResponse, err error) {
	out = new(pb.GetWebImMsgListResponse)

	if in.GetTarget() == "" {
		log.WarnWithCtx(ctx, "GetWebImMsgList invalid in: %+v", in)
		return out, nil
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.WarnWithCtx(ctx, "GetWebImMsgList invalid user")
		return out, nil
	}

	h := s.handlers[in.GetCmd()]
	if h == nil {
		log.WarnWithCtx(ctx, "GetWebImMsgList cmd(%d) handler not found", in.GetCmd())
		return out, nil
	}

	pullReq := &PullReq{
		Uid:    uid,
		Target: in.GetTarget(),
		MsgID:  in.GetMsgId(),
	}
	pullRsp, err := h.PullMsg(ctx, pullReq)

	if err != nil {
		log.ErrorWithCtx(ctx, "GetWebImMsgList PullMsg cmd(%d) req(%+v) err: %v", in.GetCmd(), pullReq, err)
		return out, err
	}

	for _, msg := range pullRsp.MsgList {
		out.Msgs = append(out.Msgs, msg.GetMsg())
	}

	return
}

func (s *Server) GetUserWebImMsgList(ctx context.Context, in *pb.GetUserWebImMsgListRequest) (*pb.GetUserWebImMsgListResponse, error) {
	out := new(pb.GetUserWebImMsgListResponse)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.WarnWithCtx(ctx, "GetWebImMsgList invalid user")
		return out, nil
	}

	h := s.handlers[in.GetCmd()]
	if h == nil {
		log.WarnWithCtx(ctx, "GetWebImMsgList cmd(%d) handler not found", in.GetCmd())
		return out, nil
	}

	pullReq := &PullReq{
		Uid: uid,

		MsgID: in.GetMsgId(),
		Limit: in.GetLimit(),
	}
	pullRsp, err := h.PullMsg(ctx, pullReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWebImMsgList PullMsg cmd(%d) req(%+v) err: %v", in.GetCmd(), pullReq, err)
		return out, err
	}

	out.MsgList = pullRsp.MsgList
	return out, nil
}

func (s *Server) ReadWebImMsg(ctx context.Context, in *pb.ReadWebImMsgRequest) (out *pb.ReadWebImMsgResponse, err error) {
	out = new(pb.ReadWebImMsgResponse)
	log.InfoWithCtx(ctx, "ReadWebImMsg in: %+v", in)
	log.InfoWithCtx(ctx, "ReadWebImMsg out: %+v", out)
	return
}
