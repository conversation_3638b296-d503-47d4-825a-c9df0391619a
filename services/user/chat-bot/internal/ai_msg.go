package internal

import (
	"context"
	"time"

	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	aigc_soulmate_middle "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	pb "golang.52tt.com/protocol/services/chat-bot"
	"golang.52tt.com/services/user/chat-bot/internal/mgr/ai/msg"
)

func (s *Server) SendImMsgAIToUser(ctx context.Context, in *pb.SendImMsgAIToUserReq) (*pb.SendImMsgAIToUserResp, error) {
	out := &pb.SendImMsgAIToUserResp{Result: pb.AuditResult_AuditResultPass}
	log.InfoWithCtx(ctx, "SendImMsgAIToUser in: %+v", in)

	if in.GetUid() == 0 || in.GetPartnerId() == 0 || in.GetMsg() == nil {
		log.WarnWithCtx(ctx, "SendImMsgAIToUser invalid in: %+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	partnerReq := &aigc_soulmate.GetAIPartnerReq{
		Id: in.GetPartnerId(),
	}
	partnerResp, err := s.clients.Soulmate.GetAIPartner(ctx, partnerReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsgAIToUser GetAIPartner req(%+v) err: %v", partnerReq, err)
		return out, err
	}

	partner := partnerResp.GetPartner()
	if partner == nil || partner.GetUid() != in.GetUid() {
		log.ErrorWithCtx(ctx, "SendImMsgAIToUser GetAIPartner req(%+v) empty", partnerReq)
		return out, protocol.NewExactServerError(nil, status.ErrChatBotAiPartnerNotFound)
	}

	user, rpcErr := s.clients.AccountGo.GetUserByUid(ctx, in.GetUid())
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "SendImMsgAIToUser GetUserByUid uid(%d) err: %v", in.GetUid(), rpcErr)
		return out, rpcErr
	}

	var (
		sender = msg.NewPartner(partner, in.GetExtraPartner())

		singleMsg = msg.NewSingleMsg(
			in.GetMsg(),

			in.GetTriggerMsgType(),
			aigc_soulmate_middle.SentenceType(in.GetSentenceType()),

			in.GetSegmentIndex(),
			in.GetSegmentCnt(),

			in.GetTextWordsNumber(),
			in.GetReqMsgId(),
		)
	)
	sendIn := &msg.SingleSendIn{
		Sender:   sender,
		Receiver: &msg.User{UserResp: *user},

		Msg: singleMsg,
		Opt: in.GetOpt(),
	}
	sendOut, err := s.singleSender.SendMsg(ctx, sendIn)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsgAIToUser SendMsg in(%+v) err: %v", sendIn, err)
		return out, err
	}

	out.MsgId = singleMsg.GetMsgId()
	out.Result = sendOut.AuditResult

	//if in.GetSegmentIndex() == 0 {
	//	// 处理句数记录
	//	s.handleSentenceRecord(ctx, in.GetUid(), in.GetPartnerId(), in.GetSentenceType())
	//}
	if in.GetSegmentIndex()+1 == in.GetSegmentCnt() &&
		in.GetTriggerMsgType() == pb.ImTriggerMsgType_ImTriggerMsgRespUser {
		if v, ok := in.GetExtMap()["soulmate_sentence_refactor_gray_release"]; !ok || v == "aigc" {
			out.NeedSendSentenceHint = s.handleSpecifiedSentenceTip(ctx, in.GetUid(), in.GetPartnerId(), in.GetSentenceType())
		} else {
			err = s.singleSender.HandleTips(ctx, in.GetExtMap(), partner)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendImMsgAIToUser HandleTips in(%+v) err: %v", sendIn, err)
			}
		}

	}

	log.InfoWithCtx(ctx, "SendImMsgAIToUser out: %+v", out)
	return out, nil
}

func (s *Server) handleSentenceRecord(ctx context.Context, uid uint32, partnerId uint32, sentenceType uint32) {
	if aigc_soulmate_middle.SentenceType(sentenceType) != aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED &&
		aigc_soulmate_middle.SentenceType(sentenceType) != aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_CUR_DAY &&
		aigc_soulmate_middle.SentenceType(sentenceType) != aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_EXTRA {
		return
	}

	_, err := s.clients.AigcCommonClient.AddSentenceCount(ctx, &aigc_common.AddSentenceCountRequest{
		Uid: uid,
		Entity: &aigc_common.Entity{
			Id:   partnerId,
			Type: aigc_common.Entity_TYPE_PARTNER,
		},
		Type: sentenceType,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleSentenceRecord AddSentenceCount uid:%d partnerId:%d sentenceType:%d err: %v",
			uid, partnerId, sentenceType, err)
	}

}

func (s *Server) handleSpecifiedSentenceTip(ctx context.Context, uid uint32, partnerId uint32, sentenceType uint32) bool {

	if aigc_soulmate_middle.SentenceType(sentenceType) != aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_ROLE_SPECIFIED {
		return false
	}

	benefitResp, err := s.clients.AigcIntimacyClient.GetBenefitConfig(ctx, &aigc_intimacy.GetBenefitConfigRequest{
		Uid: uid,
		Entity: &aigc_intimacy.Entity{
			Id:   partnerId,
			Type: aigc_intimacy.Entity_TYPE_PARTNER,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleSpecifiedSentenceTip GetBenefitConfig uid:%d partnerId:%d err: %v", uid, partnerId, err)
		return false
	}
	if benefitResp.GetBenefits().GetExtraSendMsgCount() == 0 {
		return false
	}
	countResp, err := s.clients.AigcCommonClient.GetSentenceCountMap(ctx, &aigc_common.GetSentenceCountMapRequest{
		Uid: uid,
		Entity: &aigc_common.Entity{
			Id:   partnerId,
			Type: aigc_common.Entity_TYPE_PARTNER,
		},
		Type: []uint32{sentenceType},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleSpecifiedSentenceTip GetSentenceCountMap uid:%d partnerId:%d err: %v", uid, partnerId, err)
		return false
	}
	if countResp.GetCountMap()[sentenceType] == benefitResp.GetBenefits().GetExtraSendMsgCount() {
		return true
	}
	return false
}

// handleSingleMsgSendEvent 处理单聊消息发送事件
func (s *Server) handleSingleMsgSendEvent(ctx context.Context, message *subscriber.ConsumerMessage) (error, bool) {
	var event pb.SingleMsgSendEvent
	if err := proto.Unmarshal(message.Value, &event); err != nil {
		log.ErrorWithCtx(ctx, "handleSingleMsgSendEvent Unmarshal err: %v", err)
		return err, false
	}

	sendingMsg := event.GetSendingMsg()
	if event.GetUid() == 0 || event.GetPartnerId() == 0 || sendingMsg == nil || sendingMsg.GetMsg() == nil {
		log.ErrorWithCtx(ctx, "handleSingleMsgSendEvent invalid event: %+v", event)
		return nil, false
	}

	log.InfoWithCtx(ctx, "handleSingleMsgSendEvent event: %+v", event)

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	partnerReq := &aigc_soulmate.GetAIPartnerReq{Id: event.GetPartnerId()}
	partnerResp, err := s.clients.Soulmate.GetAIPartner(ctx, partnerReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleSingleMsgSendEvent GetAIPartner req(%+v) err: %v", partnerReq, err)
		return err, false
	}
	partner := partnerResp.GetPartner()
	if partner == nil || partner.GetUid() != event.GetUid() {
		log.ErrorWithCtx(ctx, "handleSingleMsgSendEvent GetAIPartner req(%+v) invalid partner: %+v", partnerReq, partner)
		return nil, false
	}

	user, rpcErr := s.clients.AccountGo.GetUserByUid(ctx, event.GetUid())
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "handleSingleMsgSendEvent GetUserByUid uid(%d) err: %v", event.GetUid(), rpcErr)
		return err, false
	}

	singleMsg := msg.NewSingleMsg(
		sendingMsg.GetMsg(),
		pb.ImTriggerMsgType(sendingMsg.GetTrigType()),
		aigc_soulmate_middle.SentenceType(sendingMsg.GetSentenceType()),
		sendingMsg.GetSegIdx(), sendingMsg.GetSegCnt(), 0, "",
	)
	sendIn := &msg.SingleSendIn{
		Sender:   &msg.Partner{AIPartner: *partner},
		Receiver: &msg.User{UserResp: *user},

		Msg: singleMsg,
		Opt: event.GetOpt(),
	}
	if _, err = s.singleSender.SendMsg(ctx, sendIn); err != nil {
		log.ErrorWithCtx(ctx, "handleSingleMsgSendEvent SendMsg in(%+v) err: %v", sendIn, err)
		return err, false
	}

	// 上报
	s.repoter.ReportSingleMsg(ctx, sendingMsg, partner)
	return nil, false
}
