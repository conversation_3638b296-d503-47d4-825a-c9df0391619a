package internal

import (
	"context"
	"time"

	"gopkg.in/mgo.v2/bson"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"

	"golang.52tt.com/pkg/config"
	pb "golang.52tt.com/protocol/app/chat-bot-logic"
	account_go "golang.52tt.com/protocol/services/account-go"
	Account "golang.52tt.com/protocol/services/accountsvr"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/protocol/services/demo/echo"
	rcmd_business "golang.52tt.com/protocol/services/rcmd/business_ai_partner"
	rcmd_partner "golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
	"golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner_kafka"
	"golang.52tt.com/services/user/chat-bot-logic/internal/config/ttconfig"
	"golang.52tt.com/services/user/chat-bot-logic/internal/event/pub"
	"golang.52tt.com/services/user/chat-bot-logic/internal/mgr/entrance"
	"golang.52tt.com/services/user/chat-bot-logic/internal/rpc"
)

type StartConfig struct {
	Filename string `json:"filename"`

	RcmdPartnerKafka *config.KafkaConfig `json:"rcmd_partner_kafka"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	if err := ttconfig.InitChatBotLogicConfig(cfg.Filename); err != nil {
		log.ErrorWithCtx(ctx, "NewServer InitChatBotLogicConfig filename(%s) err: %v", cfg.Filename, err)
		return nil, err
	}

	clients := rpc.NewClient(ctx)

	eventPub, err := pub.NewEventPublisher(ctx, cfg.RcmdPartnerKafka)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewEventPublisher err: %v", err)
		return nil, err
	}

	enLoader := entrance.NewLoader(
		clients.ABTest,
		clients.RedDotClient,
		clients.AccountCli,

		clients.GroupClient,
		clients.Soulmate,
		clients.IntimacyClient,
		clients.AigcSoulmateMiddle,

		clients.RcmdPartner,
		clients.RcmdBusiness,
	)

	return &Server{
		clients:  clients,
		eventPub: eventPub,

		enLoader: enLoader,
	}, nil
}

type Server struct {
	clients  *rpc.Client
	eventPub pub.IEventPublisher

	enLoader *entrance.Loader
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) ShutDown() {
	log.Infof("Server ShutDown Finish")
}

// GetAIPartnerEntrance 获取AI伴侣入口 客户端版本 < 6.45
func (s *Server) GetAIPartnerEntrance(ctx context.Context, in *pb.GetAIPartnerEntranceRequest) (*pb.GetAIPartnerEntranceResponse, error) {
	out := new(pb.GetAIPartnerEntranceResponse)

	svcInfo := metainfo.GetServiceInfo(ctx)
	if svcInfo.UserID() == 0 {
		log.WarnWithCtx(ctx, "GetAIPartnerEntrance miss uid")
		return out, nil
	}

	triggerCtx, triggerCancel := context.WithTimeout(ctx, time.Second)
	defer triggerCancel()

	triggerReq := &rcmd_partner.NewTriggerReq{
		Uid:        svcInfo.UserID(),
		Source:     uint32(pb.AIPartnerEntranceSource_AI_PARTNER_ENTRANCE_SOURCE_UNSPECIFIED),
		CliVersion: svcInfo.ClientVersion(),
	}
	triggerResp, err := s.clients.RcmdPartner.NewTrigger(triggerCtx, triggerReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIPartnerEntrance NewTrigger req(%+v) err: %v", triggerReq, err)
		return out, nil
	}
	if !triggerResp.GetIsOpen() {
		return out, nil
	}

	entrances, err := s.enLoader.Load(ctx, s.enLoader.TreeHoleLoader(triggerResp.GetIsOpenNoRole()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIPartnerEntrance Load deRole(%t) err: %v", triggerResp.GetIsOpenNoRole())
		return out, err
	}
	if len(entrances) == 0 {
		return out, nil
	}

	entrance := entrances[0]
	out.Enable = true
	out.JumpLink = entrance.GetJumpLink()
	out.Background = entrance.GetBackground()
	if len(entrances[0].GetPartners()) > 0 {
		out.Partner = entrance.GetPartners()[0]
	}

	log.InfoWithCtx(ctx, "GetAIPartnerEntrance svcInfo(%+v） out: %+v", svcInfo, out)
	return out, nil
}

// GetAIPartnerEntranceV2 获取AI伴侣入口 客户端版本 >= 6.45
func (s *Server) GetAIPartnerEntranceV2(ctx context.Context, in *pb.GetAIPartnerEntranceV2Request) (*pb.GetAIPartnerEntranceV2Response, error) {
	out := new(pb.GetAIPartnerEntranceV2Response)

	svcInfo := metainfo.GetServiceInfo(ctx)
	if svcInfo.UserID() == 0 {
		log.WarnWithCtx(ctx, "GetAIPartnerEntranceV2 miss uid")
		return out, nil
	}

	triggerCtx, triggerCancel := context.WithTimeout(ctx, time.Second)
	defer triggerCancel()

	triggerReq := &rcmd_partner.NewTriggerReq{
		Uid:        svcInfo.UserID(),
		Source:     uint32(in.GetSource()),
		CliVersion: svcInfo.ClientVersion(),
	}
	triggerResp, err := s.clients.RcmdPartner.NewTrigger(triggerCtx, triggerReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIPartnerEntranceV2 NewTrigger req(%+v) err: %v", triggerReq, err)
		return out, nil
	}
	if !triggerResp.GetIsOpen() || !triggerResp.GetEnableMultiRoles() {
		return out, nil
	}

	entrances, err := s.enLoader.Load(ctx, s.enLoader.MultiRoleLoader(false))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIPartnerEntranceV2 Load withGroup(%t)")
		return out, nil
	}
	if len(entrances) > 0 {
		out.Entrance = entrances[0]
	}

	log.InfoWithCtx(ctx, "GetAIPartnerEntranceV2 svcInfo(%+v) in(%+v) out: %+v", svcInfo, in, out)
	return out, nil
}

// GetAIPartnerEntranceList 获取AI伴侣入口列表
func (s *Server) GetAIPartnerEntranceList(ctx context.Context, in *pb.GetAIPartnerEntranceListRequest) (*pb.GetAIPartnerEntranceListResponse, error) {
	out := new(pb.GetAIPartnerEntranceListResponse)

	svcInfo := metainfo.GetServiceInfo(ctx)
	if svcInfo.UserID() == 0 {
		log.WarnWithCtx(ctx, "GetAIPartnerEntranceList miss uid")
		return out, nil
	}

	triggerCtx, triggerCancel := context.WithTimeout(ctx, time.Second)
	defer triggerCancel()

	triggerReq := &rcmd_partner.NewTriggerReq{
		Uid:        svcInfo.UserID(),
		Source:     uint32(in.GetSource()),
		CliVersion: svcInfo.ClientVersion(),
	}
	triggerResp, err := s.clients.RcmdPartner.NewTrigger(triggerCtx, triggerReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIPartnerEntranceList NewTrigger req(%+v) err: %v", triggerReq, err)
		return out, err
	}
	if !triggerResp.GetIsOpen() {
		return out, nil
	}

	enMap := make(map[pb.AIPartnerEntranceType]bool)
	for _, t := range in.GetEntranceTypeList() {
		enMap[pb.AIPartnerEntranceType(t)] = true
	}

	var loadFns []entrance.LoadFn
	if triggerResp.GetEnableMultiRoles() {
		loadFns = append(loadFns, s.enLoader.MultiRoleLoader(enMap[pb.AIPartnerEntranceType_AI_PARTNER_ENTRANCE_TYPE_GROUP]))
	}
	if triggerResp.GetEnablePet() {
		loadFns = append(loadFns, s.enLoader.PetLoader())
	}

	if out.EntranceList, err = s.enLoader.Load(ctx, loadFns...); err != nil {
		log.ErrorWithCtx(ctx, "GetAIPartnerEntranceList Load err: %v", err)
		return out, nil
	}

	log.InfoWithCtx(ctx, "GetAIPartnerEntranceList in(%+v) out: %+v", in, out)
	return out, nil
}

func (s *Server) GetAIRoleInteractiveConfig(ctx context.Context, in *pb.GetAIRoleInteractiveConfigRequest) (*pb.GetAIRoleInteractiveConfigResponse, error) {
	out := new(pb.GetAIRoleInteractiveConfigResponse)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.WarnWithCtx(ctx, "GetAIRoleInteractiveConfig miss uid")
		return out, nil
	}

	if in.GetId() == 0 {
		log.WarnWithCtx(ctx, "GetAIRoleInteractiveConfig invalid req: %+v", in)
		return out, nil
	}

	rcmdRoleReq := &rcmd_business.BatchGetRoleInteractiveReq{
		RoleIds: []uint32{in.GetId()},
	}
	rcmdRoleResp, err := s.clients.RcmdBusiness.BatchGetRoleInteractive(ctx, rcmdRoleReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIRoleInteractiveConfig BatchGetRoleInteractive req(%+v) err: %v", rcmdRoleReq, err)
		return out, nil
	}
	if len(rcmdRoleResp.GetRoleInteractiveList()) > 0 {
		config := rcmdRoleResp.GetRoleInteractiveList()[0]
		out.Config = &pb.AIRoleInteractiveConfig{
			Id:  config.GetRoleId(),
			Ext: config.GetExt(),
		}
	}

	log.DebugWithCtx(ctx, "GetAIRoleInteractiveConfig in(%+v) out: %+v", in, out)
	return out, nil
}

func (s *Server) ReportAIPetBehavior(ctx context.Context, in *pb.ReportAIPetBehaviorRequest) (*pb.ReportAIPetBehaviorResponse, error) {
	out := new(pb.ReportAIPetBehaviorResponse)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.WarnWithCtx(ctx, "ReportAIPetBehavior miss uid")
		return out, nil
	}

	out.MsgId = genMsgID()
	out.ReportedAt = time.Now().UnixMilli()

	err := s.eventPub.PublishAIPetActed(ctx, &rcmd_ai_partner_kafka.PetTipReport{
		Uid:       uid,
		PartnerId: in.GetPartnerId(),
		RoleId:    in.GetRoleId(),
		Action:    rcmd_ai_partner_kafka.PetTipReport_Action(in.GetAction()),
		Tip:       in.GetTip(),
		MsgId:     out.GetMsgId(),
		SentAt:    out.GetReportedAt(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportAIPetBehavior PublishAIPetBehavior uid(%d) in(%+v) err: %v", uid, in, err)
		return out, nil // 错误不返回
	}

	log.DebugWithCtx(ctx, "ReportAIPetBehavior in(%+v) out: %+v", in, out)
	return out, nil
}

func (s *Server) BatchGetAIAccount(ctx context.Context, req *pb.BatchGetAIAccountRequest) (*pb.BatchGetAIAccountResponse, error) {
	resp := new(pb.BatchGetAIAccountResponse)

	if len(req.GetAccounts()) == 0 {
		log.WarnWithCtx(ctx, "BatchGetAIAccount invalid req: %+v", req)
		return resp, nil
	}

	accounts := req.GetAccounts()
	if len(accounts) > 100 {
		accounts = accounts[:100] // 限制最多查询100个账号
	}

	svcInfo := metainfo.GetServiceInfo(ctx)
	logAccounts := accounts
	if len(logAccounts) > 10 {
		logAccounts = logAccounts[:10]
	}
	log.InfoWithCtx(ctx, "BatchGetAIAccount svcInfo(%+v) reqSrc(%d) accountsLen(%d) accounts: %+v", svcInfo, req.GetReqSrc(), len(req.GetAccounts()), logAccounts)

	// 通过account查uid
	queryUidReq := &account_go.BatchQueryUidListReq{
		KeyList:   accounts,
		QueryType: uint32(account_go.BatchQueryUidListReq_QUERY_BY_ACCOUNT),
	}
	queryUidResp, err := s.clients.Account.BatchQueryUidList(ctx, queryUidReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAIAccount BatchQueryUidList req(%+v) err: %v", queryUidReq, err)
		return resp, err
	}
	if len(queryUidResp.GetResultList()) == 0 {
		return resp, nil
	}

	uidList := make([]uint32, 0, len(queryUidResp.GetResultList()))
	for _, pair := range queryUidResp.GetResultList() {
		uidList = append(uidList, pair.GetUid())
	}

	// 通过uid查用户信息
	userReq := &account_go.UidsReq{
		UidList: uidList,
	}
	userResp, err := s.clients.Account.GetUsersByUids(ctx, userReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAIAccount GetUsersByUids req(%+v) err: %v", userReq, err)
		return resp, err
	}

	// 过滤出机器人账号
	var (
		robotUidList = make([]uint32, 0, len(userResp.GetUserList()))
		robotUserMap = make(map[uint32]*account_go.UserResp)
	)
	for _, user := range userResp.GetUserList() {
		if user.GetUserType() == uint32(Account.USER_TYPE_USER_TYPE_ROBOT) {
			robotUidList = append(robotUidList, user.GetUid())
			robotUserMap[user.GetUid()] = user
		}
	}
	if len(robotUidList) == 0 {
		return resp, nil
	}

	// 查询ai账号信息
	var reqSrc aigc_account.GetAIAccountSource
	switch pb.BatchGetAIAccountRequest_RequestSource(req.GetReqSrc()) {
	case pb.BatchGetAIAccountRequest_REQUEST_SOURCE_IM_TAB:
		reqSrc = aigc_account.GetAIAccountSource_IM_TAB
	case pb.BatchGetAIAccountRequest_REQUEST_SOURCE_CHANNEL_PERSONAL_PAGE:
		reqSrc = aigc_account.GetAIAccountSource_CHANNEL_PERSONAL_PAGE
	}
	aiAccountReq := &aigc_account.BatchGetAIAccountRequest{
		UidList:   robotUidList,
		ReqSource: reqSrc,
	}
	aiAccountResp, err := s.clients.AigcAccount.BatchGetAIAccount(ctx, aiAccountReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAIAccount BatchGetAIAccount req(%+v) err: %v", aiAccountReq, err)
		return resp, err
	}
	if len(aiAccountResp.GetAccountList()) == 0 {
		return resp, nil
	}

	for _, aiAccount := range aiAccountResp.GetAccountList() {
		resp.List = append(resp.List, &pb.AIAccount{
			Account: robotUserMap[aiAccount.GetUid()].GetUsername(),

			Identity: aiAccount.GetIdentity(),
			Desc:     aiAccount.GetDesc(),
		})
	}

	return resp, nil
}

func genMsgID() string {
	return bson.NewObjectId().Hex()
}
