// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/user/channelbox/db/mongo (interfaces: IMongoDao)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	mongo "golang.52tt.com/services/user/channelbox/internal/db/mongo"
)

// MockIMongoDao is a mock of IMongoDao interface.
type MockIMongoDao struct {
	ctrl     *gomock.Controller
	recorder *MockIMongoDaoMockRecorder
}

// MockIMongoDaoMockRecorder is the mock recorder for MockIMongoDao.
type MockIMongoDaoMockRecorder struct {
	mock *MockIMongoDao
}

// NewMockIMongoDao creates a new mock instance.
func NewMockIMongoDao(ctrl *gomock.Controller) *MockIMongoDao {
	mock := &MockIMongoDao{ctrl: ctrl}
	mock.recorder = &MockIMongoDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMongoDao) EXPECT() *MockIMongoDaoMockRecorder {
	return m.recorder
}

// AddBoxInfo mocks base method.
func (m *MockIMongoDao) AddBoxInfo(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (bool, uint32, uint32, uint32, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBoxInfo", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(uint32)
	ret3, _ := ret[3].(uint32)
	ret4, _ := ret[4].(int64)
	ret5, _ := ret[5].(error)
	return ret0, ret1, ret2, ret3, ret4, ret5
}

// AddBoxInfo indicates an expected call of AddBoxInfo.
func (mr *MockIMongoDaoMockRecorder) AddBoxInfo(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBoxInfo", reflect.TypeOf((*MockIMongoDao)(nil).AddBoxInfo), arg0, arg1, arg2, arg3, arg4)
}

// BatchGetBoxInfos mocks base method.
func (m *MockIMongoDao) BatchGetBoxInfos(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) (map[uint32]*mongo.BoxItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBoxInfos", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(map[uint32]*mongo.BoxItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBoxInfos indicates an expected call of BatchGetBoxInfos.
func (mr *MockIMongoDaoMockRecorder) BatchGetBoxInfos(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBoxInfos", reflect.TypeOf((*MockIMongoDao)(nil).BatchGetBoxInfos), arg0, arg1, arg2, arg3)
}

// CountChannelBox mocks base method.
func (m *MockIMongoDao) CountChannelBox(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountChannelBox", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountChannelBox indicates an expected call of CountChannelBox.
func (mr *MockIMongoDaoMockRecorder) CountChannelBox(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountChannelBox", reflect.TypeOf((*MockIMongoDao)(nil).CountChannelBox), arg0, arg1, arg2)
}

// CreateBox mocks base method.
func (m *MockIMongoDao) CreateBox(arg0 context.Context, arg1 *mongo.BoxInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBox", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBox indicates an expected call of CreateBox.
func (mr *MockIMongoDaoMockRecorder) CreateBox(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBox", reflect.TypeOf((*MockIMongoDao)(nil).CreateBox), arg0, arg1)
}

// CreateIndexes mocks base method.
func (m *MockIMongoDao) CreateIndexes() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIndexes")
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateIndexes indicates an expected call of CreateIndexes.
func (mr *MockIMongoDaoMockRecorder) CreateIndexes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIndexes", reflect.TypeOf((*MockIMongoDao)(nil).CreateIndexes))
}

// DelBox mocks base method.
func (m *MockIMongoDao) DelBox(arg0 context.Context, arg1, arg2 uint32) (*mongo.BoxInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBox", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mongo.BoxInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelBox indicates an expected call of DelBox.
func (mr *MockIMongoDaoMockRecorder) DelBox(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBox", reflect.TypeOf((*MockIMongoDao)(nil).DelBox), arg0, arg1, arg2)
}

// DelBoxInfo mocks base method.
func (m *MockIMongoDao) DelBoxInfo(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBoxInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelBoxInfo indicates an expected call of DelBoxInfo.
func (mr *MockIMongoDaoMockRecorder) DelBoxInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBoxInfo", reflect.TypeOf((*MockIMongoDao)(nil).DelBoxInfo), arg0, arg1, arg2)
}

// DelBoxInfos mocks base method.
func (m *MockIMongoDao) DelBoxInfos(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBoxInfos", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelBoxInfos indicates an expected call of DelBoxInfos.
func (mr *MockIMongoDaoMockRecorder) DelBoxInfos(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBoxInfos", reflect.TypeOf((*MockIMongoDao)(nil).DelBoxInfos), arg0, arg1)
}

// DelBoxInfosByBoxType mocks base method.
func (m *MockIMongoDao) DelBoxInfosByBoxType(arg0 context.Context, arg1, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBoxInfosByBoxType", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelBoxInfosByBoxType indicates an expected call of DelBoxInfosByBoxType.
func (mr *MockIMongoDaoMockRecorder) DelBoxInfosByBoxType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBoxInfosByBoxType", reflect.TypeOf((*MockIMongoDao)(nil).DelBoxInfosByBoxType), arg0, arg1, arg2)
}

// DelChannelUserBox mocks base method.
func (m *MockIMongoDao) DelChannelUserBox(arg0 context.Context, arg1, arg2 uint32) (*mongo.BoxItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChannelUserBox", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mongo.BoxItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelChannelUserBox indicates an expected call of DelChannelUserBox.
func (mr *MockIMongoDaoMockRecorder) DelChannelUserBox(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelUserBox", reflect.TypeOf((*MockIMongoDao)(nil).DelChannelUserBox), arg0, arg1, arg2)
}

// DelJoinBoxInfo mocks base method.
func (m *MockIMongoDao) DelJoinBoxInfo(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelJoinBoxInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelJoinBoxInfo indicates an expected call of DelJoinBoxInfo.
func (mr *MockIMongoDaoMockRecorder) DelJoinBoxInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelJoinBoxInfo", reflect.TypeOf((*MockIMongoDao)(nil).DelJoinBoxInfo), arg0, arg1, arg2)
}

// DelJoinBoxInfos mocks base method.
func (m *MockIMongoDao) DelJoinBoxInfos(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelJoinBoxInfos", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelJoinBoxInfos indicates an expected call of DelJoinBoxInfos.
func (mr *MockIMongoDaoMockRecorder) DelJoinBoxInfos(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelJoinBoxInfos", reflect.TypeOf((*MockIMongoDao)(nil).DelJoinBoxInfos), arg0, arg1)
}

// GetBox mocks base method.
func (m *MockIMongoDao) GetBox(arg0 context.Context, arg1, arg2 uint32) (*mongo.BoxInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBox", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mongo.BoxInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBox indicates an expected call of GetBox.
func (mr *MockIMongoDaoMockRecorder) GetBox(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBox", reflect.TypeOf((*MockIMongoDao)(nil).GetBox), arg0, arg1, arg2)
}

// GetBoxIdByUid mocks base method.
func (m *MockIMongoDao) GetBoxIdByUid(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBoxIdByUid", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxIdByUid indicates an expected call of GetBoxIdByUid.
func (mr *MockIMongoDaoMockRecorder) GetBoxIdByUid(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxIdByUid", reflect.TypeOf((*MockIMongoDao)(nil).GetBoxIdByUid), arg0, arg1, arg2, arg3)
}

// GetBoxInfosLimit mocks base method.
func (m *MockIMongoDao) GetBoxInfosLimit(arg0 context.Context, arg1, arg2, arg3 uint32, arg4, arg5 int64) ([]*mongo.BoxItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBoxInfosLimit", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*mongo.BoxItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxInfosLimit indicates an expected call of GetBoxInfosLimit.
func (mr *MockIMongoDaoMockRecorder) GetBoxInfosLimit(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxInfosLimit", reflect.TypeOf((*MockIMongoDao)(nil).GetBoxInfosLimit), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetBoxUserInfos mocks base method.
func (m *MockIMongoDao) GetBoxUserInfos(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 []uint32) ([]*mongo.BoxItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBoxUserInfos", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*mongo.BoxItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxUserInfos indicates an expected call of GetBoxUserInfos.
func (mr *MockIMongoDaoMockRecorder) GetBoxUserInfos(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxUserInfos", reflect.TypeOf((*MockIMongoDao)(nil).GetBoxUserInfos), arg0, arg1, arg2, arg3, arg4)
}

// GetBoxsCntByChannelId mocks base method.
func (m *MockIMongoDao) GetBoxsCntByChannelId(arg0 context.Context, arg1 uint32) (map[uint32]*mongo.BoxCntItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBoxsCntByChannelId", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*mongo.BoxCntItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxsCntByChannelId indicates an expected call of GetBoxsCntByChannelId.
func (mr *MockIMongoDaoMockRecorder) GetBoxsCntByChannelId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxsCntByChannelId", reflect.TypeOf((*MockIMongoDao)(nil).GetBoxsCntByChannelId), arg0, arg1)
}

// GetChannelAllBox mocks base method.
func (m *MockIMongoDao) GetChannelAllBox(arg0 context.Context, arg1 uint32) ([]*mongo.BoxInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelAllBox", arg0, arg1)
	ret0, _ := ret[0].([]*mongo.BoxInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelAllBox indicates an expected call of GetChannelAllBox.
func (mr *MockIMongoDaoMockRecorder) GetChannelAllBox(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelAllBox", reflect.TypeOf((*MockIMongoDao)(nil).GetChannelAllBox), arg0, arg1)
}

// GetChannelBoxIdByUid mocks base method.
func (m *MockIMongoDao) GetChannelBoxIdByUid(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelBoxIdByUid", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelBoxIdByUid indicates an expected call of GetChannelBoxIdByUid.
func (mr *MockIMongoDaoMockRecorder) GetChannelBoxIdByUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelBoxIdByUid", reflect.TypeOf((*MockIMongoDao)(nil).GetChannelBoxIdByUid), arg0, arg1, arg2)
}

// GetChannelBoxList mocks base method.
func (m *MockIMongoDao) GetChannelBoxList(arg0 context.Context, arg1, arg2 uint32) ([]*mongo.BoxInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelBoxList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*mongo.BoxInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelBoxList indicates an expected call of GetChannelBoxList.
func (mr *MockIMongoDaoMockRecorder) GetChannelBoxList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelBoxList", reflect.TypeOf((*MockIMongoDao)(nil).GetChannelBoxList), arg0, arg1, arg2)
}

// GetOpenMicId mocks base method.
func (m *MockIMongoDao) GetOpenMicId(arg0 context.Context, arg1 uint32) (*mongo.ChannelMic, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOpenMicId", arg0, arg1)
	ret0, _ := ret[0].(*mongo.ChannelMic)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOpenMicId indicates an expected call of GetOpenMicId.
func (mr *MockIMongoDaoMockRecorder) GetOpenMicId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOpenMicId", reflect.TypeOf((*MockIMongoDao)(nil).GetOpenMicId), arg0, arg1)
}

// GetUserChannelBox mocks base method.
func (m *MockIMongoDao) GetUserChannelBox(arg0 context.Context, arg1, arg2, arg3 uint32) (*mongo.BoxItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserChannelBox", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*mongo.BoxItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserChannelBox indicates an expected call of GetUserChannelBox.
func (mr *MockIMongoDaoMockRecorder) GetUserChannelBox(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserChannelBox", reflect.TypeOf((*MockIMongoDao)(nil).GetUserChannelBox), arg0, arg1, arg2, arg3)
}

// IncrKey mocks base method.
func (m *MockIMongoDao) IncrKey(arg0 context.Context, arg1 string, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrKey", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrKey indicates an expected call of IncrKey.
func (mr *MockIMongoDaoMockRecorder) IncrKey(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrKey", reflect.TypeOf((*MockIMongoDao)(nil).IncrKey), arg0, arg1, arg2)
}

// JoinBoxSuc mocks base method.
func (m *MockIMongoDao) JoinBoxSuc(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JoinBoxSuc", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// JoinBoxSuc indicates an expected call of JoinBoxSuc.
func (mr *MockIMongoDaoMockRecorder) JoinBoxSuc(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinBoxSuc", reflect.TypeOf((*MockIMongoDao)(nil).JoinBoxSuc), arg0, arg1, arg2, arg3, arg4)
}

// ModifyBoxCnt mocks base method.
func (m *MockIMongoDao) ModifyBoxCnt(arg0 context.Context, arg1, arg2 uint32, arg3 int) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyBoxCnt", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyBoxCnt indicates an expected call of ModifyBoxCnt.
func (mr *MockIMongoDaoMockRecorder) ModifyBoxCnt(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyBoxCnt", reflect.TypeOf((*MockIMongoDao)(nil).ModifyBoxCnt), arg0, arg1, arg2, arg3)
}

// UpdateBox mocks base method.
func (m *MockIMongoDao) UpdateBox(arg0 context.Context, arg1 *mongo.BoxInfo) (*mongo.BoxInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBox", arg0, arg1)
	ret0, _ := ret[0].(*mongo.BoxInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBox indicates an expected call of UpdateBox.
func (mr *MockIMongoDaoMockRecorder) UpdateBox(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBox", reflect.TypeOf((*MockIMongoDao)(nil).UpdateBox), arg0, arg1)
}

// UpdateBroadcastBoxInfo mocks base method.
func (m *MockIMongoDao) UpdateBroadcastBoxInfo(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBroadcastBoxInfo", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBroadcastBoxInfo indicates an expected call of UpdateBroadcastBoxInfo.
func (mr *MockIMongoDaoMockRecorder) UpdateBroadcastBoxInfo(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBroadcastBoxInfo", reflect.TypeOf((*MockIMongoDao)(nil).UpdateBroadcastBoxInfo), arg0, arg1, arg2, arg3, arg4)
}

// UpdateCommonBoxInfo mocks base method.
func (m *MockIMongoDao) UpdateCommonBoxInfo(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCommonBoxInfo", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCommonBoxInfo indicates an expected call of UpdateCommonBoxInfo.
func (mr *MockIMongoDaoMockRecorder) UpdateCommonBoxInfo(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCommonBoxInfo", reflect.TypeOf((*MockIMongoDao)(nil).UpdateCommonBoxInfo), arg0, arg1, arg2, arg3, arg4)
}

// UpdateDelBoxInfo mocks base method.
func (m *MockIMongoDao) UpdateDelBoxInfo(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDelBoxInfo", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateDelBoxInfo indicates an expected call of UpdateDelBoxInfo.
func (mr *MockIMongoDaoMockRecorder) UpdateDelBoxInfo(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDelBoxInfo", reflect.TypeOf((*MockIMongoDao)(nil).UpdateDelBoxInfo), arg0, arg1, arg2, arg3, arg4)
}

// UpdateOpenMicId mocks base method.
func (m *MockIMongoDao) UpdateOpenMicId(arg0 context.Context, arg1, arg2, arg3 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOpenMicId", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateOpenMicId indicates an expected call of UpdateOpenMicId.
func (mr *MockIMongoDaoMockRecorder) UpdateOpenMicId(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOpenMicId", reflect.TypeOf((*MockIMongoDao)(nil).UpdateOpenMicId), arg0, arg1, arg2, arg3)
}
