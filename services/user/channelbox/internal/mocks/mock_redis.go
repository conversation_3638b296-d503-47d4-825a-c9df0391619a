// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/user/channelbox/internal/db/redis (interfaces: IRedisDao)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	redis "golang.52tt.com/services/user/channelbox/internal/db/redis"
)

// MockIRedisDao is a mock of IRedisDao interface.
type MockIRedisDao struct {
	ctrl     *gomock.Controller
	recorder *MockIRedisDaoMockRecorder
}

// MockIRedisDaoMockRecorder is the mock recorder for MockIRedisDao.
type MockIRedisDaoMockRecorder struct {
	mock *MockIRedisDao
}

// NewMockIRedisDao creates a new mock instance.
func NewMockIRedisDao(ctrl *gomock.Controller) *MockIRedisDao {
	mock := &MockIRedisDao{ctrl: ctrl}
	mock.recorder = &MockIRedisDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRedisDao) EXPECT() *MockIRedisDaoMockRecorder {
	return m.recorder
}

// AddChannelBoxUser mocks base method.
func (m *MockIRedisDao) AddChannelBoxUser(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannelBoxUser", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddChannelBoxUser indicates an expected call of AddChannelBoxUser.
func (mr *MockIRedisDaoMockRecorder) AddChannelBoxUser(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelBoxUser", reflect.TypeOf((*MockIRedisDao)(nil).AddChannelBoxUser), arg0, arg1, arg2, arg3)
}

// AddMainChannelUser mocks base method.
func (m *MockIRedisDao) AddMainChannelUser(arg0 context.Context, arg1 uint32, arg2 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddMainChannelUser", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddMainChannelUser indicates an expected call of AddMainChannelUser.
func (mr *MockIRedisDaoMockRecorder) AddMainChannelUser(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMainChannelUser", reflect.TypeOf((*MockIRedisDao)(nil).AddMainChannelUser), varargs...)
}

// DelChannelBox mocks base method.
func (m *MockIRedisDao) DelChannelBox(arg0 context.Context, arg1 uint32, arg2 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelChannelBox", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelChannelBox indicates an expected call of DelChannelBox.
func (mr *MockIRedisDaoMockRecorder) DelChannelBox(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelBox", reflect.TypeOf((*MockIRedisDao)(nil).DelChannelBox), varargs...)
}

// DelChannelBoxUser mocks base method.
func (m *MockIRedisDao) DelChannelBoxUser(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChannelBoxUser", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelChannelBoxUser indicates an expected call of DelChannelBoxUser.
func (mr *MockIRedisDaoMockRecorder) DelChannelBoxUser(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelBoxUser", reflect.TypeOf((*MockIRedisDao)(nil).DelChannelBoxUser), arg0, arg1, arg2, arg3)
}

// DelChannelBoxs mocks base method.
func (m *MockIRedisDao) DelChannelBoxs(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChannelBoxs", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelChannelBoxs indicates an expected call of DelChannelBoxs.
func (mr *MockIRedisDaoMockRecorder) DelChannelBoxs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelBoxs", reflect.TypeOf((*MockIRedisDao)(nil).DelChannelBoxs), arg0, arg1)
}

// DelMainChannelUser mocks base method.
func (m *MockIRedisDao) DelMainChannelUser(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMainChannelUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelMainChannelUser indicates an expected call of DelMainChannelUser.
func (mr *MockIRedisDaoMockRecorder) DelMainChannelUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMainChannelUser", reflect.TypeOf((*MockIRedisDao)(nil).DelMainChannelUser), arg0, arg1, arg2)
}

// GetBoxsCntByChannelId mocks base method.
func (m *MockIRedisDao) GetBoxsCntByChannelId(arg0 context.Context, arg1 uint32) (map[uint32]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBoxsCntByChannelId", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxsCntByChannelId indicates an expected call of GetBoxsCntByChannelId.
func (mr *MockIRedisDaoMockRecorder) GetBoxsCntByChannelId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxsCntByChannelId", reflect.TypeOf((*MockIRedisDao)(nil).GetBoxsCntByChannelId), arg0, arg1)
}

// GetChannelBoxUserCnt mocks base method.
func (m *MockIRedisDao) GetChannelBoxUserCnt(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelBoxUserCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelBoxUserCnt indicates an expected call of GetChannelBoxUserCnt.
func (mr *MockIRedisDaoMockRecorder) GetChannelBoxUserCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelBoxUserCnt", reflect.TypeOf((*MockIRedisDao)(nil).GetChannelBoxUserCnt), arg0, arg1, arg2)
}

// GetChannelBoxUserNums mocks base method.
func (m *MockIRedisDao) GetChannelBoxUserNums(arg0 context.Context, arg1 uint32, arg2 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelBoxUserNums", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelBoxUserNums indicates an expected call of GetChannelBoxUserNums.
func (mr *MockIRedisDaoMockRecorder) GetChannelBoxUserNums(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelBoxUserNums", reflect.TypeOf((*MockIRedisDao)(nil).GetChannelBoxUserNums), arg0, arg1, arg2)
}

// GetMainChannelUsers mocks base method.
func (m *MockIRedisDao) GetMainChannelUsers(arg0 context.Context, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMainChannelUsers", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMainChannelUsers indicates an expected call of GetMainChannelUsers.
func (mr *MockIRedisDaoMockRecorder) GetMainChannelUsers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMainChannelUsers", reflect.TypeOf((*MockIRedisDao)(nil).GetMainChannelUsers), arg0, arg1)
}

// GetMic mocks base method.
func (m *MockIRedisDao) GetMic(arg0 context.Context, arg1 uint32) (*redis.MicList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMic", arg0, arg1)
	ret0, _ := ret[0].(*redis.MicList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMic indicates an expected call of GetMic.
func (mr *MockIRedisDaoMockRecorder) GetMic(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMic", reflect.TypeOf((*MockIRedisDao)(nil).GetMic), arg0, arg1)
}

// GetMicApplyRecords mocks base method.
func (m *MockIRedisDao) GetMicApplyRecords(arg0 context.Context, arg1 uint32) ([]*redis.MicApplyRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMicApplyRecords", arg0, arg1)
	ret0, _ := ret[0].([]*redis.MicApplyRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMicApplyRecords indicates an expected call of GetMicApplyRecords.
func (mr *MockIRedisDaoMockRecorder) GetMicApplyRecords(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMicApplyRecords", reflect.TypeOf((*MockIRedisDao)(nil).GetMicApplyRecords), arg0, arg1)
}

// Lock mocks base method.
func (m *MockIRedisDao) Lock(arg0 context.Context, arg1, arg2 string, arg3 time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Lock", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Lock indicates an expected call of Lock.
func (mr *MockIRedisDaoMockRecorder) Lock(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lock", reflect.TypeOf((*MockIRedisDao)(nil).Lock), arg0, arg1, arg2, arg3)
}

// SetMic mocks base method.
func (m *MockIRedisDao) SetMic(arg0 context.Context, arg1 uint32, arg2 []uint32, arg3 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMic", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMic indicates an expected call of SetMic.
func (mr *MockIRedisDaoMockRecorder) SetMic(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMic", reflect.TypeOf((*MockIRedisDao)(nil).SetMic), arg0, arg1, arg2, arg3)
}

// SetMicApplyRecords mocks base method.
func (m *MockIRedisDao) SetMicApplyRecords(arg0 context.Context, arg1 uint32, arg2 []*redis.MicApplyRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMicApplyRecords", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMicApplyRecords indicates an expected call of SetMicApplyRecords.
func (mr *MockIRedisDaoMockRecorder) SetMicApplyRecords(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMicApplyRecords", reflect.TypeOf((*MockIRedisDao)(nil).SetMicApplyRecords), arg0, arg1, arg2)
}

// Unlock mocks base method.
func (m *MockIRedisDao) Unlock(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unlock", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unlock indicates an expected call of Unlock.
func (mr *MockIRedisDaoMockRecorder) Unlock(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unlock", reflect.TypeOf((*MockIRedisDao)(nil).Unlock), arg0, arg1, arg2)
}
