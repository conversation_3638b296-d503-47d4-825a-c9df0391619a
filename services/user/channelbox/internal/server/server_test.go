package server

import (
	"context"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/channel-mic-go"
	"golang.52tt.com/clients/channel-msg-express"
	"golang.52tt.com/clients/channelmic"
	mockChannelMic "golang.52tt.com/clients/mocks/channelmic"
	//"golang.52tt.com/pkg/config"
	pb "golang.52tt.com/protocol/services/channelbox"
	"golang.52tt.com/services/user/channelbox/internal/common"
	"golang.52tt.com/services/user/channelbox/internal/conf"
	"golang.52tt.com/services/user/channelbox/internal/db/mongo"
	"golang.52tt.com/services/user/channelbox/internal/db/redis"
	"golang.52tt.com/services/user/channelbox/internal/event"
	"golang.52tt.com/services/user/channelbox/internal/mocks"
	"golang.52tt.com/services/user/channelbox/internal/model"
	"reflect"
	"testing"
	"time"
)

func TestChannelBoxServer_BatchGetBoxInfos(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)

	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.BatchGetBoxInfosReq
	}

	getBoxMap := make(map[uint32]*mongo.BoxItem)
	gomock.InOrder(
		mongoDao.EXPECT().BatchGetBoxInfos(ctx, channelId, boxType, []uint32{uid}).Return(getBoxMap, nil),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.BatchGetBoxInfosResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_BatchGetBoxInfos",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao},
			args: args{ctx: ctx, in: &pb.BatchGetBoxInfosReq{
				UidList:   []uint32{uid},
				Channelid: channelId,
				BoxType:   pb.BoxType_BoxTypeMelee,
			}},
			wantErr: false,
			wantOut: &pb.BatchGetBoxInfosResp{
				BoxBaseInfos: []*pb.BoxBaseInfo{
					{
						Uid: uid,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.BatchGetBoxInfos(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetBoxInfos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("BatchGetBoxInfos() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_ClearJoinBox(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.ClearJoinBoxReq
	}

	gomock.InOrder(
		channelBox.EXPECT().ClearJoinInfo(ctx, channelId, uid).Return(nil),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.ClearJoinBoxResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_ClearJoinBox",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.ClearJoinBoxReq{
				Uid:       uid,
				Channelid: channelId,
				BoxType:   pb.BoxType_BoxTypeMelee,
				Boxid:     boxId,
			}},
			wantErr: false,
			wantOut: &pb.ClearJoinBoxResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.ClearJoinBox(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ClearJoinBox() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("ClearJoinBox() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_DelBoxInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	var boxCnt uint32 = 2
	out := &pb.DelBoxInfoResp{
		BoxNum: boxCnt,
	}
	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.DelBoxInfoReq
	}
	gomock.InOrder(
		channelBox.EXPECT().DelBoxInfo(ctx, channelId, boxId).Return(boxCnt, nil),
	)
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.DelBoxInfoResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_DelBoxInfo",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.DelBoxInfoReq{
				ChannelId: channelId,
				BoxType:   pb.BoxType_BoxTypeMelee,
				BoxId:     boxId,
			}},
			wantErr: false,
			wantOut: out,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.DelBoxInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DelBoxInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DelBoxInfo() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_EnterBox(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	var bcBoxId uint32 = 2
	rspInfo := &common.EnterBoxRspInfo{
		IsSwitch: true,
	}
	var enterTime int64 = time.Now().Unix()
	gomock.InOrder(
		channelBox.EXPECT().EnterBox(ctx, channelId, boxId, uid, boxType).Return(rspInfo, nil),
		channelBox.EXPECT().DoCommonBox(ctx, channelId, bcBoxId, uid, boxType, true).Return(enterTime, nil),
	)
	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.EnterBoxReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.EnterBoxResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_EnterBox1",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.EnterBoxReq{
				Channelid: channelId,
				BoxType:   pb.BoxType_BoxTypeMelee,
				Boxid:     boxId,
				Uid:       uid,
			}},
			wantErr: false,
			wantOut: &pb.EnterBoxResp{
				IsSwitch: true,
				EnterBoxInfos: []*pb.EnterBoxInfo{
					{
						BoxType: pb.EnterResultBoxType_SwitchType,
					},
					{
						Boxid:       boxId,
						AudioBoxIds: []uint32{0, 0},
					},
				},
				Channelid: channelId,
			},
		},
		{name: "TestChannelBoxServer_EnterBox2",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.EnterBoxReq{
				Channelid:      channelId,
				BoxType:        pb.BoxType_BoxTypeMelee,
				Boxid:          0,
				Uid:            uid,
				BroadcastBoxid: 2,
			}},
			wantErr: false,
			wantOut: &pb.EnterBoxResp{
				IsSwitch: false,
				EnterBoxInfos: []*pb.EnterBoxInfo{
					{
						Boxid: bcBoxId,
					},
				},
				Channelid: channelId,
				Opts:      uint64(enterTime),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.EnterBox(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("EnterBox() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("EnterBox() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_ExitBox(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	var bcBoxId uint32 = 2

	var exitTime int64 = time.Now().Unix()
	var boxCnt uint32 = 1
	gomock.InOrder(
		channelBox.EXPECT().ExitBox(ctx, channelId, boxId, uid, boxType).Return(exitTime, boxCnt, nil),
		channelBox.EXPECT().DoCommonBox(ctx, channelId, bcBoxId, uid, boxType, false).Return(exitTime, nil),
	)
	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.ExitBoxReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.ExitBoxResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_ExitBox1",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.ExitBoxReq{
				Channelid: channelId,
				BoxType:   pb.BoxType_BoxTypeMelee,
				Boxid:     boxId,
				Uid:       uid,
			}},
			wantErr: false,
			wantOut: &pb.ExitBoxResp{
				BoxCnt: boxCnt,
				Opts:   uint64(exitTime),
			},
		},
		{name: "TestChannelBoxServer_ExitBox2",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.ExitBoxReq{
				Channelid:      channelId,
				BoxType:        pb.BoxType_BoxTypeMelee,
				Boxid:          0,
				Uid:            uid,
				BroadcastBoxid: 2,
			}},
			wantErr: false,
			wantOut: &pb.ExitBoxResp{
				Opts: uint64(exitTime),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.ExitBox(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExitBox() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("ExitBox() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_ExitChannel(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	var boxCnt uint32 = 1
	gomock.InOrder(
		channelBox.EXPECT().ExitBoxByExitChannel(ctx, channelId, uid).Return(boxId, boxCnt, boxType, nil),
	)

	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.ExitChannelReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.ExitChannelResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_ExitChannel",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.ExitChannelReq{
				Channelid: channelId,
				BoxType:   pb.BoxType_BoxTypeMelee,
				Uid:       uid,
			}},
			wantErr: false,
			wantOut: &pb.ExitChannelResp{
				BoxCnt:  boxCnt,
				BoxId:   boxId,
				BoxType: pb.BoxType_BoxTypeMelee,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.ExitChannel(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExitChannel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotOut.BoxId != tt.wantOut.BoxId {
				t.Errorf("ExitChannel() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_GetBoxInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	var boxCntMap = map[uint32]int64{3: 3}
	var userBoxId uint32 = 3
	var audioBoxId []uint32 = []uint32{2, 4}
	var mainCommonBoxid uint32 = 3
	var micCnt uint32 = 0
	var zBoxType uint32 = 0
	gomock.InOrder(
		redisDao.EXPECT().GetBoxsCntByChannelId(ctx, channelId).Return(boxCntMap, nil),
		mongoDao.EXPECT().GetBoxIdByUid(ctx, channelId, uid, zBoxType).Return(userBoxId, nil),
	)

	gomock.InOrder(
		channelBox.EXPECT().GetChannelAllBoxInfo(ctx, channelId, uid, boxType).Return(nil, micCnt, nil),
		channelBox.EXPECT().GetUserChannelBox(ctx, uid, channelId, boxType).Return(userBoxId, audioBoxId, nil),
		channelBox.EXPECT().GetChannelOpenMicBoxId(ctx, channelId).Return(mainCommonBoxid, nil),
	)

	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.GetBoxInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.GetBoxInfoResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_GetBoxInfo1",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.GetBoxInfoReq{
				Channelid: channelId,
				BoxType:   pb.BoxType_BoxTypeMelee,
				OpeUid:    uid,
			}},
			wantErr: false,
			wantOut: &pb.GetBoxInfoResp{
				UserBoxid:       userBoxId,
				BoxList:         nil,
				MicTotal:        0,
				UserAudioBoxids: audioBoxId,
				MainCommonBoxid: mainCommonBoxid,
			},
		},
		{name: "TestChannelBoxServer_GetBoxInfo2",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.GetBoxInfoReq{
				Channelid: channelId,
				BoxType:   pb.BoxType_BoxTypeRoleplay,
				OpeUid:    uid,
			}},
			wantErr: false,
			wantOut: &pb.GetBoxInfoResp{
				BoxInfo: []*pb.BoxCntInfo{
					{
						Boxid:      3,
						BoxUserCnt: 3,
					},
				},
				UserBoxid: userBoxId,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.GetBoxInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBoxInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("GetBoxInfo() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_GetBoxInfosByLimit(t *testing.T) {
	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	boxItems := &mongo.BoxItem{
		Id:        "",
		ChannelId: channelId,
		BoxId:     boxId,
		Uid:       uid,
	}
	var testCnt int64 = 0
	var lastTime int64 = 0
	mongoDao.EXPECT().GetBoxInfosLimit(ctx, channelId, boxId, boxType, lastTime, testCnt).Return([]*mongo.BoxItem{boxItems}, nil)
	type args struct {
		ctx context.Context
		in  *pb.GetBoxInfosByLimitReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.GetBoxInfosByLimitResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_GetBoxInfosByLimit",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.GetBoxInfosByLimitReq{
				Channelid: channelId,
				BoxType:   pb.BoxType_BoxTypeMelee,
				Boxid:     boxId,
			}},
			wantErr: false,
			wantOut: &pb.GetBoxInfosByLimitResp{
				BoxBaseInfos: []*pb.BoxBaseInfo{
					{
						Boxid: boxId,
						Uid:   uid,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.GetBoxInfosByLimit(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBoxInfosByLimit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("GetBoxInfosByLimit() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_GetBoxUserInMicInfos(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	var userIds = []uint32{uid}
	channelBox.EXPECT().GetBoxUserInMicInfos(ctx, channelId, boxId).Return(userIds, nil)

	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.GetBoxUserInMicInfosReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.GetBoxUserInMicInfosResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_GetBoxUserInMicInfos",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.GetBoxUserInMicInfosReq{
				Channelid: channelId,
				BoxType:   pb.BoxType_BoxTypeMelee,
				Boxid:     boxId,
			}},
			wantErr: false,
			wantOut: &pb.GetBoxUserInMicInfosResp{
				BoxUids: userIds,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.GetBoxUserInMicInfos(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBoxUserInMicInfos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("GetBoxUserInMicInfos() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_JoinBox(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	rspInfo := &common.EnterBoxRspInfo{
		IsSwitch:       true,
		OldBoxId:       0,
		OldBoxCnt:      0,
		NewBoxId:       0,
		NewBoxCnt:      0,
		BoxTimeStamp:   0,
		CommonBoxId:    0,
		BroadcastBoxId: 0,
	}
	gomock.InOrder(
		mongoDao.EXPECT().JoinBoxSuc(ctx, channelId, boxId, uid, boxType).Return(false, nil),
		channelBox.EXPECT().EnterBox(ctx, channelId, boxId, uid, uint32(pb.BoxType_BoxTypeRoleplay)).Return(rspInfo, nil),
	)

	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.JoinBoxReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.JoinBoxResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_JoinBox",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.JoinBoxReq{
				Channelid:   channelId,
				Uid:         uid,
				Boxid:       boxId,
				JoinBoxType: pb.JoinBoxType_AcceptType,
			}},
			wantErr: false,
			wantOut: &pb.JoinBoxResp{
				IsSwitch: true,
				EnterBoxInfos: []*pb.EnterBoxInfo{
					{
						BoxType: pb.EnterResultBoxType_SwitchType,
					},
					{
						Boxid: boxId,
					},
				},
				Opts:      0,
				Channelid: channelId,
				IsIgnore:  false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.JoinBox(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("JoinBox() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("JoinBox() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_SetChannelMicBoxId(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	var opts int64 = time.Now().Unix()
	gomock.InOrder(
		mongoDao.EXPECT().UpdateOpenMicId(ctx, channelId, boxId, uid).Return(opts, nil),
	)

	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.SetChannelMicBoxIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.SetChannelMicBoxIdResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_SetChannelMicBoxId",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.SetChannelMicBoxIdReq{
				ChannelId:    channelId,
				OpeUid:       uid,
				OpenMicBoxId: boxId,
			}},
			wantErr: false,
			wantOut: &pb.SetChannelMicBoxIdResp{
				Opts: opts,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.SetChannelMicBoxId(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetChannelMicBoxId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("SetChannelMicBoxId() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_CreateBoxInfoWithMic(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	gomock.InOrder(
		channelBox.EXPECT().CreateBoxInfoWithMic(ctx, gomock.Any()).Return(&pb.BoxInfo{}, nil),
	)
	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.UpsertBoxInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.UpsertBoxInfoResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_UpsertBoxInfo",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.UpsertBoxInfoReq{
				Box: nil,
			}},
			wantErr: false,
			wantOut: &pb.UpsertBoxInfoResp{
				Box: &pb.BoxInfo{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.UpsertBoxInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpsertBoxInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("UpsertBoxInfo() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestChannelBoxServer_UpsertBoxInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	redisDao := mocks.NewMockIRedisDao(ctl)
	micCli := mockChannelMic.NewMockIClient(ctl)
	mongoDao := mocks.NewMockIMongoDao(ctl)
	channelBox := mocks.NewMockIChannelBox(ctl)

	gomock.InOrder(
		channelBox.EXPECT().UpsertBoxInfoWithMic(ctx, gomock.Any()).Return(&pb.BoxInfo{}, nil),
	)
	type fields struct {
		sc             *conf.StartConfig
		mDao           mongo.IMongoDao
		channelBoxInst model.IChannelBox
		redisDao       redis.IRedisDao
		micCli         channelmic.IClient
		micGoCli       channel_mic_go.IClient
		chMsgExpress   channel_msg_express.IClient
		subMicChannel  *event.MicEventSubscriber
		subAudit       *event.AuditResultSubscriber
	}
	type args struct {
		ctx context.Context
		in  *pb.UpsertBoxInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.UpsertBoxInfoResp
		wantErr bool
	}{
		{name: "TestChannelBoxServer_UpsertBoxInfo",
			fields: fields{redisDao: redisDao, micCli: micCli, mDao: mongoDao, channelBoxInst: channelBox},
			args: args{ctx: ctx, in: &pb.UpsertBoxInfoReq{
				Box: &pb.BoxInfo{
					BoxId: 1,
				},
			}},
			wantErr: false,
			wantOut: &pb.UpsertBoxInfoResp{
				Box: &pb.BoxInfo{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ChannelBoxServer{
				sc:             tt.fields.sc,
				mDao:           tt.fields.mDao,
				channelBoxInst: tt.fields.channelBoxInst,
				redisDao:       tt.fields.redisDao,
				micCli:         tt.fields.micCli,
				micGoCli:       tt.fields.micGoCli,
				chMsgExpress:   tt.fields.chMsgExpress,
				subMicChannel:  tt.fields.subMicChannel,
				subAudit:       tt.fields.subAudit,
			}
			gotOut, err := s.UpsertBoxInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpsertBoxInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("UpsertBoxInfo() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

/*func TestChannelBoxServer_NewChannelBoxServer(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	type fields struct {
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "TestChannelBoxServer_NewChannelBoxServer",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.args.ctx = context.WithValue(context.Background(), "configfile", "../channelbox.json")
			var cfg config.Configer
			_, err := NewChannelBoxServer(tt.args.ctx, cfg)
			if (err != nil) == tt.wantErr {
				t.Errorf("NewChannelBoxServer error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})

	}
}*/
