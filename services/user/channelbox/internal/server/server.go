package server

import (
	"context"
	"time"

	channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/user/channelbox/internal/model"

	channelmicgo "golang.52tt.com/clients/channel-mic-go"
	pb "golang.52tt.com/protocol/services/channelbox"
	"golang.52tt.com/services/user/channelbox/internal/conf"
	"golang.52tt.com/services/user/channelbox/internal/db/mongo"
	"golang.52tt.com/services/user/channelbox/internal/db/redis"
	"golang.52tt.com/services/user/channelbox/internal/event"
)

type ChannelBoxServer struct {
	sc             *conf.StartConfig
	mDao           mongo.IMongoDao
	channelBoxInst model.IChannelBox
	redisDao       redis.IRedisDao
	micCli         channelmic.IClient
	micGoCli       channelmicgo.IClient
	chMsgExpress   channelmsgexpress.IClient
	subMicChannel  *event.MicEventSubscriber
	subAudit       *event.AuditResultSubscriber
}

func NewChannelBoxServer(ctx context.Context, cfg *conf.StartConfig) (*ChannelBoxServer, error) {
	log.Infof("StartConfig: %+v, %+v, %+v, %+v, %+v", cfg.GetSubKafkaConfig(), cfg.GetAuditKafkaConfig(), cfg.GetSubMicKafkaConfig(), cfg.GetRedisConfig(), cfg.GetMongoConfig())
	mDao, err := mongo.NewMongoDao(ctx, cfg.GetMongoConfig())
	if err != nil {
		log.Errorf("Failed to create mongodb %v", err)
		return nil, err
	}
	err = mDao.CreateIndexes()
	if err != nil {
		log.Errorf("mDao.CreateIndexes err: %v", err)
	}

	rDB, err := redis.NewRedisDao(cfg.GetRedisConfig())
	if err != nil {
		log.Errorf("Failed to create redis err: %v", err)
		return nil, err
	}

	micCli := channelmic.NewIClient()
	micGoCli := channelmicgo.NewIClient()
	chMsgCli := channelmsgexpress.NewIClient()

	channelBoxInst := model.NewChannelBox(mDao, rDB, chMsgCli, micCli)

	_, err = event.NewSwitchEventLinkSubscriber(cfg.GetSubKafkaConfig().ClientID,
		cfg.GetSubKafkaConfig().GroupID, cfg.GetSubKafkaConfig().TopicList(), cfg.GetSubKafkaConfig().BrokerList(), channelBoxInst)
	if nil != err {
		log.Errorf("event.NewSwitchEventLinkSubscriber err: %v, config:%+v", err, cfg.GetSubKafkaConfig())
		return nil, err
	}
	/*err = switchChannelSub.Start()
	if nil != err {
		log.Errorf("event.NewSwitchEventSubscriber Start err: %v, config:%+v", err, sc.GetSubKafkaConfig())
		return nil, err
	}*/

	_, err = event.NewMicEventSubscriber(cfg.GetSubMicKafkaConfig().ClientID,
		cfg.GetSubMicKafkaConfig().GroupID, cfg.GetSubMicKafkaConfig().TopicList(), cfg.GetSubMicKafkaConfig().BrokerList(), channelBoxInst)
	if nil != err {
		log.Errorf("event.NewMicEventSubscriber err: %v, config:%+v", err, cfg.GetSubMicKafkaConfig())
		return nil, err
	}

	auditSub, err := event.NewAuditResultSubscriber(cfg.GetAuditKafkaConfig().ClientID, cfg.GetAuditKafkaConfig().GroupID, cfg.GetAuditKafkaConfig().TopicList(), cfg.GetAuditKafkaConfig().BrokerList(), channelBoxInst)
	if err != nil {
		log.Errorf("event.NewAuditResultSubscriber config(%v) err: %v", nil, err)
		return nil, err
	}

	return &ChannelBoxServer{
		sc:             cfg,
		mDao:           mDao,
		redisDao:       rDB,
		channelBoxInst: channelBoxInst,
		micCli:         micCli,
		micGoCli:       micGoCli,
		chMsgExpress:   chMsgCli,
		subAudit:       auditSub,
	}, nil
}

func (s *ChannelBoxServer) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *ChannelBoxServer) EnterBox(ctx context.Context, in *pb.EnterBoxReq) (out *pb.EnterBoxResp, err error) {
	out = &pb.EnterBoxResp{}

	if in.GetBoxid() != 0 {
		enterInfo, err := s.channelBoxInst.EnterBox(ctx, in.GetChannelid(), in.GetBoxid(), in.GetUid(), uint32(in.GetBoxType()))
		if err != nil {
			log.ErrorWithCtx(ctx, "channelBoxInst.EnterBox err:%v, channelId:%d, boxId:%d, uid:%d", err, in.GetChannelid(), in.GetBoxid(), in.GetUid())
			return out, err
		}
		if enterInfo.IsSwitch {
			enterOldBoxInfo := &pb.EnterBoxInfo{
				BoxType: pb.EnterResultBoxType_SwitchType,
				Boxid:   enterInfo.OldBoxId,
				BoxCnt:  enterInfo.OldBoxCnt,
			}
			out.EnterBoxInfos = append(out.EnterBoxInfos, enterOldBoxInfo)
		}

		out.IsSwitch = enterInfo.IsSwitch
		out.Channelid = in.GetChannelid()
		out.Opts = uint64(enterInfo.BoxTimeStamp)

		enterNewBoxInfo := &pb.EnterBoxInfo{
			BoxType:     pb.EnterResultBoxType_EnterType,
			Boxid:       in.GetBoxid(),
			BoxCnt:      enterInfo.NewBoxCnt,
			AudioBoxIds: []uint32{enterInfo.CommonBoxId, enterInfo.BroadcastBoxId},
		}
		out.EnterBoxInfos = append(out.EnterBoxInfos, enterNewBoxInfo)
	} else {
		boxId := in.GetBroadcastBoxid()
		if boxId != 0 {
			enterTime, err := s.channelBoxInst.DoCommonBox(ctx, in.GetChannelid(), boxId, in.GetUid(), uint32(in.GetBoxType()), true)
			if err != nil {
				log.ErrorWithCtx(ctx, "EnterBox channelBoxInst.DoCommonBox err:%v, channelId:%d, boxId:%d, uid:%d", err, in.GetChannelid(), boxId, in.GetUid())
				return out, err
			}
			out.Channelid = in.GetChannelid()
			out.Opts = uint64(enterTime)

			enterNewBoxInfo := &pb.EnterBoxInfo{
				BoxType: pb.EnterResultBoxType_EnterType,
				Boxid:   boxId,
				BoxCnt:  0,
			}
			out.EnterBoxInfos = append(out.EnterBoxInfos, enterNewBoxInfo)
		}
	}
	log.InfoWithCtx(ctx, "EnterBox req:%+v, rsp:%+v", in, out)
	return out, err
}

func (s *ChannelBoxServer) ExitBox(ctx context.Context, in *pb.ExitBoxReq) (out *pb.ExitBoxResp, err error) {
	out = &pb.ExitBoxResp{}
	if in.GetBoxid() != 0 {
		opts, boxCnt, err := s.channelBoxInst.ExitBox(ctx, in.GetChannelid(), in.GetBoxid(), in.GetUid(), uint32(in.GetBoxType()))
		if err != nil {
			log.Errorf("channelBoxInst.ExitBox err:%v, req:%+v", err, in)
			return out, err
		}
		out.BoxCnt = boxCnt
		out.Opts = uint64(opts)
	} else {
		boxId := in.GetBroadcastBoxid()
		if boxId != 0 {
			enterTime, err := s.channelBoxInst.DoCommonBox(ctx, in.GetChannelid(), boxId, in.GetUid(), uint32(in.GetBoxType()), false)
			if err != nil {
				log.ErrorWithCtx(ctx, "ExitBox Broadcastid channelBoxInst.DoCommonBox err:%v, channelId:%d, boxId:%d, uid:%d", err, in.GetChannelid(), boxId, in.GetUid())
				return out, err
			}
			out.Opts = uint64(enterTime)
		}

		boxId = in.GetCommonBoxid()
		if boxId != 0 {
			enterTime, err := s.channelBoxInst.DoCommonBox(ctx, in.GetChannelid(), boxId, in.GetUid(), uint32(in.GetBoxType()), false)
			if err != nil {
				log.ErrorWithCtx(ctx, "ExitBox CommonBoxid channelBoxInst.DoCommonBox err:%v, channelId:%d, boxId:%d, uid:%d", err, in.GetChannelid(), boxId, in.GetUid())
				return out, err
			}
			out.Opts = uint64(enterTime)
		}
	}

	log.InfoWithCtx(ctx, "ExitBox req:%+v, rsp:%+v", in, out)
	return out, nil
}

func (s *ChannelBoxServer) GetBoxInfo(ctx context.Context, in *pb.GetBoxInfoReq) (out *pb.GetBoxInfoResp, err error) {
	out = &pb.GetBoxInfoResp{}
	/*boxMap, err := s.mDao.GetBoxsCntByChannelId(ctx, in.GetChannelid())
	if err != nil {
		log.Errorf("mDao.GetBoxsCntByChannelId err:%v, req:%+v", err, in)
		return out, err
	}
	for _, item := range boxMap {
		out.BoxInfo = append(out.BoxInfo, &pb.BoxCntInfo{
			Boxid:      item.BoxId,
			BoxUserCnt: item.Cnt,
		})
	}*/

	if in.GetBoxType() == pb.BoxType_BoxTypeRoleplay {
		cntMap, err := s.redisDao.GetBoxsCntByChannelId(ctx, in.GetChannelid())
		if err != nil {
			log.ErrorWithCtx(ctx, "redisDao.GetBoxsCntByChannelId err:%v, req:%+v", err, in)
			return out, err
		}
		for boxId, cnt := range cntMap {
			out.BoxInfo = append(out.BoxInfo, &pb.BoxCntInfo{
				Boxid:      boxId,
				BoxUserCnt: uint32(cnt),
			})
		}

		boxId, err := s.mDao.GetBoxIdByUid(ctx, in.GetChannelid(), in.GetOpeUid(), uint32(in.GetBoxType()))
		if err != nil {
			log.ErrorWithCtx(ctx, "mDao.GetBoxIdByUid err:%v, req:%+v", err, in)
			return out, err
		}

		out.UserBoxid = boxId
	} else {
		out.BoxList, out.MicTotal, err = s.channelBoxInst.GetChannelAllBoxInfo(ctx, in.GetChannelid(), in.GetOpeUid(), uint32(in.GetBoxType()))
		if err != nil {
			log.ErrorWithCtx(ctx, "channelBoxInst.GetChannelAllBoxInfo in(%v) err: %v", in, err)
			return
		}

		if len(out.GetBoxList()) > 0 {
			var boxIdList []uint32
			for _, box := range out.GetBoxList() {
				boxIdList = append(boxIdList, box.GetBoxId())
			}

			out.BoxInfo, err = s.channelBoxInst.GetChannelBoxUserNums(ctx, in.GetChannelid(), boxIdList)
			if err != nil {
				log.ErrorWithCtx(ctx, "channelBoxInst.GetBoxUserNums in(%v) err: %v", in, err)
				return
			}
		}

		out.UserBoxid, out.UserAudioBoxids, err = s.channelBoxInst.GetUserChannelBox(ctx, in.GetOpeUid(), in.GetChannelid(), uint32(in.GetBoxType()))
		if err != nil {
			log.ErrorWithCtx(ctx, "channelBoxInst.GetUserChannelBox in(%v) err: %v", in, err)
			return
		}

		out.MainCommonBoxid, err = s.channelBoxInst.GetChannelOpenMicBoxId(ctx, in.GetChannelid())
		if err != nil {
			log.ErrorWithCtx(ctx, "channelBoxInst.GetChannelOpenMic in(%v) err: %v", in, err)
			return
		}
	}

	log.InfoWithCtx(ctx, "GetBoxInfos req:%+v, rsp:%+v", in, out)
	return out, nil
}

func (s *ChannelBoxServer) BatchGetBoxInfos(ctx context.Context, in *pb.BatchGetBoxInfosReq) (out *pb.BatchGetBoxInfosResp, err error) {
	out = &pb.BatchGetBoxInfosResp{}
	boxMap, err := s.mDao.BatchGetBoxInfos(ctx, in.GetChannelid(), uint32(in.GetBoxType()), in.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "mDao.BatchGetBoxInfos err:%v, req:%+v", err, in)
		return out, err
	}
	for _, uid := range in.GetUidList() {
		if userInfo, ok := boxMap[uid]; ok && userInfo != nil {
			var audioBoxIds []uint32
			audioBoxIds = append(audioBoxIds, userInfo.CommonBoxId)
			audioBoxIds = append(audioBoxIds, userInfo.BroadcastBoxId)
			out.BoxBaseInfos = append(out.BoxBaseInfos, &pb.BoxBaseInfo{
				Boxid:       userInfo.BoxId,
				Uid:         userInfo.Uid,
				Opts:        uint64(userInfo.BoxTime),
				AudioBoxids: audioBoxIds,
			})
		} else {
			out.BoxBaseInfos = append(out.BoxBaseInfos, &pb.BoxBaseInfo{
				Boxid: 0,
				Uid:   uid,
				Opts:  0,
			})
		}

	}
	log.InfoWithCtx(ctx, "BatchGetBoxInfos req:%+v, rsp:%+v", in, out)
	return out, nil
}

func (s *ChannelBoxServer) JoinBox(ctx context.Context, in *pb.JoinBoxReq) (out *pb.JoinBoxResp, err error) {
	out = &pb.JoinBoxResp{}
	ignore, err := s.mDao.JoinBoxSuc(ctx, in.GetChannelid(), in.GetBoxid(), in.GetUid(), uint32(in.GetJoinBoxType()))
	if err != nil {
		log.Errorf("mDao.JoinBoxSuc err:%v, req:%+v", err, in)
		return out, err
	}
	if ignore {
		out.IsIgnore = true
		log.WarnWithCtx(ctx, "JoinBoxSuc ignore err, in:%+v, out:%+v", in, out)
		return out, nil
	}
	if in.GetJoinBoxType() == pb.JoinBoxType_AcceptType {
		enterInfo, err := s.channelBoxInst.EnterBox(ctx, in.GetChannelid(), in.GetBoxid(), in.GetUid(), uint32(pb.BoxType_BoxTypeRoleplay))
		if err != nil {
			log.ErrorWithCtx(ctx, "channelBoxInst.EnterBox err:%v, req:%+v", err, in)
			return out, err
		}
		if enterInfo.IsSwitch {
			enterOldBoxInfo := &pb.EnterBoxInfo{
				BoxType: pb.EnterResultBoxType_SwitchType,
				Boxid:   enterInfo.OldBoxId,
				BoxCnt:  enterInfo.OldBoxCnt,
			}
			out.EnterBoxInfos = append(out.EnterBoxInfos, enterOldBoxInfo)
		}

		out.IsSwitch = enterInfo.IsSwitch
		out.Channelid = in.GetChannelid()
		out.Opts = uint64(enterInfo.BoxTimeStamp)

		enterNewBoxInfo := &pb.EnterBoxInfo{
			BoxType: pb.EnterResultBoxType_EnterType,
			Boxid:   in.GetBoxid(),
			BoxCnt:  enterInfo.NewBoxCnt,
		}
		out.EnterBoxInfos = append(out.EnterBoxInfos, enterNewBoxInfo)
	}

	log.InfoWithCtx(ctx, "JoinBox req:%+v, rsp:%+v", in, out)
	return out, err
}

func (s *ChannelBoxServer) ClearJoinBox(ctx context.Context, in *pb.ClearJoinBoxReq) (out *pb.ClearJoinBoxResp, err error) {
	out = &pb.ClearJoinBoxResp{}
	err = s.channelBoxInst.ClearJoinInfo(ctx, in.GetChannelid(), in.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxInst.ClearJoinInfo err:%v, req:%+v", err, in)
		return out, err
	}
	log.InfoWithCtx(ctx, "CleanJoinBox req:%+v, rsp:%+v", in, out)
	return out, err
}

func (s *ChannelBoxServer) GetBoxInfosByLimit(ctx context.Context, in *pb.GetBoxInfosByLimitReq) (out *pb.GetBoxInfosByLimitResp, err error) {
	out = &pb.GetBoxInfosByLimitResp{}
	boxItems, err := s.mDao.GetBoxInfosLimit(ctx, in.GetChannelid(), in.GetBoxid(), uint32(in.GetBoxType()), in.GetLastTime(), in.GetGetCnt())
	if err != nil {
		log.ErrorWithCtx(ctx, "mDao.GetBoxInfosLimit err:%v, req:%+v", err, in)
		return out, err
	}
	for _, boxItem := range boxItems {
		out.BoxBaseInfos = append(out.BoxBaseInfos, &pb.BoxBaseInfo{
			Boxid: in.GetBoxid(),
			Uid:   boxItem.Uid,
			Opts:  uint64(boxItem.BoxTime),
		})
	}
	log.InfoWithCtx(ctx, "GetBoxInfosByLimit req:%+v, rsp:%+v", in, out)
	return out, nil
}

func (s *ChannelBoxServer) GetBoxUserInMicInfos(ctx context.Context, in *pb.GetBoxUserInMicInfosReq) (out *pb.GetBoxUserInMicInfosResp, err error) {
	out = &pb.GetBoxUserInMicInfosResp{}
	boxUids, err := s.channelBoxInst.GetBoxUserInMicInfos(ctx, in.GetChannelid(), in.GetBoxid())
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxInst.GetBoxUserInMicInfos err:%v, req:%+v", err, in)
		return out, err
	}
	out.BoxUids = boxUids
	log.InfoWithCtx(ctx, "GetBoxUserInMicInfos req:%+v, rsp:%+v", in, out)
	return out, nil
}

func (s *ChannelBoxServer) ExitChannel(ctx context.Context, in *pb.ExitChannelReq) (out *pb.ExitChannelResp, err error) {
	out = &pb.ExitChannelResp{}
	boxId, boxType, boxCnt, err := s.channelBoxInst.ExitBoxByExitChannel(ctx, in.GetChannelid(), in.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "ExitChannel err:%v, req:%+v", err, in)
		return out, nil
	}
	out.BoxId = boxId
	out.BoxType = pb.BoxType(boxType)
	out.BoxCnt = boxCnt
	nowTime := time.Now()
	opts := nowTime.UnixNano() / 1e6
	out.Opts = uint64(opts)
	log.InfoWithCtx(ctx, "ExitChannel req:%+v, rsp:%+v", in, out)
	return out, nil
}

func (s *ChannelBoxServer) UpsertBoxInfo(ctx context.Context, in *pb.UpsertBoxInfoReq) (out *pb.UpsertBoxInfoResp, err error) {
	out = new(pb.UpsertBoxInfoResp)

	if in.GetBox().GetBoxId() == 0 {
		out.Box, err = s.channelBoxInst.CreateBoxInfoWithMic(ctx, in.GetBox())
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelBoxServer.UpsertBoxInfo channelBoxInst.CreateBoxInfoWithMic err: %v", err)
			return
		}
	} else {
		out.Box, err = s.channelBoxInst.UpsertBoxInfoWithMic(ctx, in.GetBox())
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelBoxServer.UpsertBoxInfo channelBoxInst.UpsertBoxInfoWithMic err: %v", err)
			return
		}
	}
	log.InfoWithCtx(ctx, "ChannelBoxServer.UpsertBoxInfo in:%s, out:%s", in.String(), out.String())

	return
}

func (s *ChannelBoxServer) DelBoxInfo(ctx context.Context, in *pb.DelBoxInfoReq) (out *pb.DelBoxInfoResp, err error) {
	out = new(pb.DelBoxInfoResp)
	log.InfoWithCtx(ctx, "ChannelBoxServer.DelBoxInfo in: %v", in)

	out.BoxNum, err = s.channelBoxInst.DelBoxInfo(ctx, in.GetChannelId(), in.GetBoxId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBoxServer.DelBoxInfo channelBoxInst.DelBoxInfo err: %v", err)
		return
	}

	return
}

func (s *ChannelBoxServer) SetChannelMicBoxId(ctx context.Context, in *pb.SetChannelMicBoxIdReq) (out *pb.SetChannelMicBoxIdResp, err error) {
	out = new(pb.SetChannelMicBoxIdResp)

	opts, err := s.mDao.UpdateOpenMicId(ctx, in.GetChannelId(), in.GetOpenMicBoxId(), in.GetOpeUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelMicBoxId UpdateOpenMicId err: %v", err)
		return
	}
	out.Opts = opts
	log.InfoWithCtx(ctx, "SetChannelMicBoxId in:%+v, out:%+v", in, out)
	return
}

func (s *ChannelBoxServer) SetMicApplyRecords(ctx context.Context, in *pb.SetMicApplyRecordsReq) (out *pb.SetMicApplyRecordsResp, err error) {
	out = new(pb.SetMicApplyRecordsResp)
	log.InfoWithCtx(ctx, "SetMicApplyRecords in: %+v", in)

	if in.GetChannelId() == 0 || len(in.GetRecords()) == 0 {
		log.WarnWithCtx(ctx, "SetMicApplyRecords invalid in: %+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	var records []*redis.MicApplyRecord
	for _, record := range in.GetRecords() {
		records = append(records, &redis.MicApplyRecord{
			MicId:      int(record.GetMicId()),
			Uid:        record.GetUid(),
			BoxId:      record.GetBoxId(),
			MicType:    uint32(record.GetMicType()),
			Status:     uint32(record.GetMicStatus()),
			UpdateTime: record.GetUpdateAt(),
		})
	}
	err = s.redisDao.SetMicApplyRecords(ctx, in.GetChannelId(), records)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMicApplyRecords redis err: %v", err)
		return out, err
	}

	return out, nil
}

func (s *ChannelBoxServer) GetMicApplyRecords(ctx context.Context, in *pb.GetMicApplyRecordsReq) (out *pb.GetMicApplyRecordsResp, err error) {
	out = new(pb.GetMicApplyRecordsResp)
	log.InfoWithCtx(ctx, "GetMicApplyRecords in: %+v", in)

	if in.GetChannelId() == 0 {
		log.WarnWithCtx(ctx, "GetMicApplyRecords invalid in: %+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	records, err := s.redisDao.GetMicApplyRecords(ctx, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMicApplyRecords redis err: %v", err)
		return out, err
	}

	for _, record := range records {
		out.Records = append(out.Records, &pb.MicApplyRecord{
			MicId:     uint32(record.MicId),
			MicType:   pb.MicType(record.MicType),
			MicStatus: pb.MicStatus(record.Status),

			Uid:      record.Uid,
			BoxId:    record.BoxId,
			UpdateAt: record.UpdateTime,
		})
	}

	log.InfoWithCtx(ctx, "GetMicApplyRecords out: %+v", out)
	return out, nil
}

func (s *ChannelBoxServer) ShutDown() {
}
