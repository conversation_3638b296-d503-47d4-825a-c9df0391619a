package model

import (
	"context"
	"fmt"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/services/topic_channel/tab"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPb "golang.52tt.com/protocol/app/channel"
	lpb "golang.52tt.com/protocol/app/channel-roleplay-logic"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channelbox"
	channelmicPB "golang.52tt.com/protocol/services/channelmicsvr"
	"golang.52tt.com/services/user/channelbox/internal/common"
	"golang.52tt.com/services/user/channelbox/internal/db/mongo"
	"golang.52tt.com/services/user/channelbox/internal/db/redis"
)

const (
	originBoxId = 1000

	SubChannelCommonBoxId  uint32 = 2
	MainChannelCommonBoxId        = 3
	BroadCastBoxId         uint32 = 4

	MIN_MIC_IDX = 1
	MAX_MIC_IDX = 50

	APPLY_EXPIRE_SECOND = 3 //麦位那边申请过期是3s

	constValidMicFlag   = 1
	constUnValidMicFlag = 0
	constNumOne         = 1 //只是常量，用于数据边界，或者数目累加1
)

type ChannelBox struct {
	mDao     mongo.IMongoDao
	redisDao redis.IRedisDao

	chMsgCli channelmsgexpress.IClient
	micCli   channelmic.IClient
	tabCli   *tcTab.Client
}

func NewChannelBox(mDao mongo.IMongoDao, redisDao redis.IRedisDao, chMsgCli channelmsgexpress.IClient, micCli channelmic.IClient,
	tabCli *tcTab.Client) *ChannelBox {
	return &ChannelBox{
		mDao:     mDao,
		redisDao: redisDao,

		chMsgCli: chMsgCli,
		micCli:   micCli,
		tabCli:   tabCli,
	}
}

func (s *ChannelBox) EnterBox(ctx context.Context, channelid, boxid, uid, boxType uint32) (rspInfo *common.EnterBoxRspInfo, err error) {
	isSwitch, oldBoxId, commonBoxId, broadcastBoxId, boxTimeStamp, err := s.mDao.AddBoxInfo(ctx, channelid, boxid, uid, boxType)
	if err != nil {
		return nil, err
	}
	var boxCntOld uint32
	if isSwitch {
		err := s.redisDao.DelChannelBoxUser(ctx, channelid, oldBoxId, uid)
		if err != nil {
			return nil, err
		}
		boxCntOld, err = s.redisDao.GetChannelBoxUserCnt(ctx, channelid, oldBoxId)
		if err != nil {
			return nil, err
		}
	}

	err = s.redisDao.AddChannelBoxUser(ctx, channelid, boxid, uid)
	if err != nil {
		return nil, err
	}
	boxCntNew, err := s.redisDao.GetChannelBoxUserCnt(ctx, channelid, boxid)
	if err != nil {
		return nil, err
	}

	if boxType == uint32(pb.BoxType_BoxTypeMelee) {
		// 进团战包厢，要删除主房间记录
		err = s.redisDao.DelMainChannelUser(ctx, channelid, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelBox.EnterBox DelMainChannelUser err: %v, channelid: %d, uid: %d", err, channelid, uid)
			return nil, err
		}
	}

	rspInfo = &common.EnterBoxRspInfo{
		IsSwitch:       isSwitch,
		OldBoxId:       oldBoxId,
		OldBoxCnt:      boxCntOld,
		NewBoxId:       boxid,
		NewBoxCnt:      boxCntNew,
		BoxTimeStamp:   boxTimeStamp,
		CommonBoxId:    commonBoxId,
		BroadcastBoxId: broadcastBoxId,
	}
	return rspInfo, nil
}

func (s *ChannelBox) DoCommonBox(ctx context.Context, channelid, boxid, uid, boxType uint32, isEnter bool) (boxTimeStamp int64, err error) {
	if boxid == SubChannelCommonBoxId {
		if !isEnter {
			boxid = 0
		}
		boxTimeStamp, err = s.mDao.UpdateCommonBoxInfo(ctx, channelid, boxid, uid, boxType)
	} else if boxid == BroadCastBoxId {
		if !isEnter {
			boxid = 0
		}
		boxTimeStamp, err = s.mDao.UpdateBroadcastBoxInfo(ctx, channelid, boxid, uid, boxType)
	} else {
		log.ErrorWithCtx(ctx, "DoCommonBox err, boxid:%d", boxid)
	}

	log.InfoWithCtx(ctx, "DoCommonBox test, boxid:%d", boxid)

	if err != nil {
		return 0, err
	}
	return boxTimeStamp, nil
}

func (s *ChannelBox) ExitBoxByExitChannel(ctx context.Context, channelid, uid uint32) (uint32, uint32, uint32, error) {
	userBox, err := s.mDao.DelChannelUserBox(ctx, channelid, uid)
	if err != nil {
		return 0, 0, 0, err
	}

	if userBox == nil {
		return 0, 0, 0, nil
	}

	var boxCnt uint32
	if userBox.BoxId != 0 {
		err = s.redisDao.DelChannelBoxUser(ctx, channelid, userBox.BoxId, uid)
		if err != nil {
			return 0, 0, 0, err
		}

		boxCnt, err = s.redisDao.GetChannelBoxUserCnt(ctx, channelid, userBox.BoxId)
		if err != nil {
			return 0, 0, 0, err
		}
	} else {
		log.Infof("ExitBoxByExitChannel boxid 0, channelid:%d, uid:%d", channelid, uid)
	}

	return userBox.BoxId, userBox.BoxType, boxCnt, nil
}

func (s *ChannelBox) ExitBox(ctx context.Context, channelid, boxid, uid, boxType uint32) (int64, uint32, error) {
	boxTime, err := s.mDao.UpdateDelBoxInfo(ctx, channelid, 0, uid, boxType)
	if err != nil {
		return 0, 0, err
	}
	err = s.redisDao.DelChannelBoxUser(ctx, channelid, boxid, uid)
	if err != nil {
		return 0, 0, err
	}
	boxCnt, err := s.redisDao.GetChannelBoxUserCnt(ctx, channelid, boxid)
	if err != nil {
		return 0, 0, err
	}
	/*boxCnt, err := s.mDao.ModifyBoxCnt(ctx, channelid, boxid, -1)
	if err != nil {
		return 0, err
	}*/
	if boxType == uint32(pb.BoxType_BoxTypeMelee) {
		// 退出团战包厢，要重新加入主房间
		err = s.redisDao.AddMainChannelUser(ctx, channelid, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelBox.ExitBox AddMainChannelUser err: %v, channelid: %d, uid: %d", err, channelid, uid)
			return 0, 0, err
		}
	}
	return boxTime, boxCnt, nil
}

func (s *ChannelBox) ClearJoinInfo(ctx context.Context, channelid, uid uint32) error {
	err := s.mDao.DelJoinBoxInfo(ctx, channelid, uid)
	if err != nil {
		return err
	}
	return nil
}

func (s *ChannelBox) ClearChannel(ctx context.Context, channelid uint32) error {
	cnt, err := s.mDao.DelBoxInfos(ctx, channelid)
	if err != nil {
		return err
	}
	if cnt >= 1 {
		err = s.mDao.DelJoinBoxInfos(ctx, channelid) //多清了一次非剧本钉的房间数据
		if err != nil {
			log.ErrorWithCtx(ctx, "mDao.DelJoinBoxInfos err: %v", err)
		}
		err = s.redisDao.DelChannelBoxs(ctx, channelid)
		if err != nil {
			log.ErrorWithCtx(ctx, "redisDao.DelChannelBoxs err: %v", err)
		}

		boxList, err := s.mDao.GetChannelAllBox(ctx, channelid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelBox.ClearChannel channelId:%d mDao.GetChannelAllBox err: %v", channelid, err)
			return err
		}

		if len(boxList) > 0 {
			var boxIdList []uint32
			for _, box := range boxList {
				boxIdList = append(boxIdList, box.BoxId)
			}

			err = s.redisDao.DelChannelBox(ctx, channelid, boxIdList...)
			if err != nil {
				log.ErrorWithCtx(ctx, "ChannelBox.ClearChannel channelId(%d) redisDao.DelChannelBox err: %v", channelid, err)
				return err
			}
		}
	}
	return nil
}

func (s *ChannelBox) SetMicUids(ctx context.Context, channelid uint32, micUids []uint32, serverTimeMs uint64) error {
	err := s.redisDao.SetMic(ctx, channelid, micUids, serverTimeMs)
	return err
}

func (s *ChannelBox) getMicList(ctx context.Context, channelid uint32) (micList *redis.MicList, err error) {
	micList, err = s.redisDao.GetMic(ctx, channelid)
	return micList, err
}

func (s *ChannelBox) GetBoxUserInMicInfos(ctx context.Context, channelId, boxId uint32) (boxUids []uint32, err error) {
	micList, err := s.getMicList(ctx, channelId)
	if err != nil {
		return nil, err
	}
	log.DebugWithCtx(ctx, "GetBoxUserInMicInfos getMicList, channelId:%d, mic:%v", channelId, micList)
	if micList != nil && len(micList.MicUids) != 0 {
		boxItems, err := s.mDao.GetBoxUserInfos(ctx, channelId, boxId, uint32(pb.BoxType_BoxTypeRoleplay), micList.MicUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "mDao.GetBoxUserInfos err:%v, channelId:%d, boxId:%d, micList.MicUids:%v", err, channelId, boxId, micList.MicUids)
			return nil, err
		}
		for _, boxItem := range boxItems {
			boxUids = append(boxUids, boxItem.Uid)
		}
	}
	return boxUids, nil
}

func (s *ChannelBox) genBoxName(ctx context.Context, box *pb.BoxInfo, oldBoxInfo *mongo.BoxInfo) (string, uint32, error) {
	// 修改名称
	if box.GetBoxName() != oldBoxInfo.Name {
		// 审核中不能修改
		if oldBoxInfo.State == mongo.BoxNameCreate || oldBoxInfo.State == mongo.BoxNameAuditing {
			return "", 0, protocol.NewExactServerError(nil, status.ErrChannelboxBoxNameReview)
		}

		return box.GetBoxName(), mongo.BoxNameAuditing, nil
	}
	return "", 0, nil
}

func (s *ChannelBox) CreateBoxInfoWithMic(ctx context.Context, box *pb.BoxInfo) (*pb.BoxInfo, error) {
	bi := &mongo.BoxInfo{
		ChannelId:  box.GetChannelId(),
		BoxId:      box.GetBoxId(),
		BoxType:    uint32(box.GetBoxType()),
		CreatorUid: box.GetCreatorUid(),
	}

	// return before
	ob := &pb.BoxInfo{
		ChannelId:  box.GetChannelId(),
		BoxId:      box.GetBoxId(),
		CreatorUid: box.GetCreatorUid(),
		BoxType:    box.GetBoxType(),
	}
	var err error
	bi.PublicMicList, bi.NormalMicList, err = s.AllocMic(ctx, box)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.UpsertBoxInfo allocPubMic box(%v) err: %v", box, err)
		return nil, err
	}

	boxId, err := s.mDao.IncrKey(ctx, fmt.Sprintf("channelbox_%d", bi.ChannelId), 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.UpsertBoxInfo mDao.IncrKey box(%v) err: %v", box, err)
		return nil, err
	}

	bi.BoxId = originBoxId + boxId
	bi.ReviewName = box.GetBoxName()
	bi.MicCapacity = box.GetMicCap()

	err = s.mDao.CreateBox(ctx, bi)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.UpsertBoxInfo mDao.CreateBox box(%v) err: %v", box, err)
		return nil, err
	}

	ob.BoxId = bi.BoxId
	ob.PublicMicList = bi.PublicMicList
	ob.NormalMicList = bi.NormalMicList

	return ob, nil
}

func (s *ChannelBox) UpsertBoxInfoWithMic(ctx context.Context, box *pb.BoxInfo) (*pb.BoxInfo, error) {
	bi := &mongo.BoxInfo{
		ChannelId:  box.GetChannelId(),
		BoxId:      box.GetBoxId(),
		BoxType:    uint32(box.GetBoxType()),
		CreatorUid: box.GetCreatorUid(),
	}

	// return before
	ob := &pb.BoxInfo{
		ChannelId:  box.GetChannelId(),
		BoxId:      box.GetBoxId(),
		CreatorUid: box.GetCreatorUid(),
		BoxType:    box.GetBoxType(),
	}

	old, err := s.mDao.GetBox(ctx, bi.ChannelId, bi.BoxId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.UpsertBoxInfo mDao.GetBoxInfo box(%v) err: %v", box, err)
		return nil, err
	}

	// 修改名称
	bi.ReviewName, bi.State, err = s.genBoxName(ctx, box, old)
	if err != nil {
		return nil, err
	}

	// 更改麦位数
	if bi.MicCapacity != old.MicCapacity {
		bi.MicCapacity = box.GetMicCap()

		boxUserNum, err := s.redisDao.GetChannelBoxUserCnt(ctx, box.GetChannelId(), box.GetBoxId())
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelBox.UpsertBoxInfo redisDao.GetChannelBoxUser box(%v) err: %v", box, err)
			return nil, err
		}

		// 子频道内有人
		if boxUserNum > 0 {
			return nil, protocol.NewExactServerError(nil, status.ErrChannelboxBoxInUse)
		}

		bi.PublicMicList, bi.NormalMicList, err = s.AllocMic(ctx, box)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelBox.UpsertBoxInfo allocMic box(%v) err: %v", box, err)
			return nil, err
		}

		ob.PublicMicList = bi.PublicMicList
		ob.NormalMicList = bi.NormalMicList
	}

	_, err = s.mDao.UpdateBox(ctx, bi)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.UpsertBoxInfo mDao.UpdateBox box(%v) err: %v", box, err)
		return nil, err
	}

	ob.CreatorUid = old.CreatorUid
	ob.BoxName = old.Name
	ob.MicCap = old.MicCapacity

	return ob, nil
}

func (s *ChannelBox) AllocMic(ctx context.Context, box *pb.BoxInfo) (pubMicList []uint32, norMicList []uint32, err error) {
	const exp = 5 * time.Second

	var (
		key = fmt.Sprintf("alloc_mic_lock_%d", box.GetChannelId())
		val = fmt.Sprintf("%d", time.Now().UnixNano())
	)

	var ok bool
	for i := 0; i < 5; i++ {
		ok, err = s.redisDao.Lock(ctx, key, val, exp)
		if err != nil {
			return
		}

		if ok {
			break
		}

		time.Sleep(10 * time.Millisecond)
	}

	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrChananelboxMicAllocBusy)
		return
	}

	defer func() { _ = s.redisDao.Unlock(ctx, key, val) }()

	boxes, err := s.mDao.GetChannelBoxList(ctx, box.GetChannelId(), uint32(box.GetBoxType()))
	if err != nil {
		return
	}
	applyRecords, err := s.redisDao.GetMicApplyRecords(ctx, box.GetChannelId())
	if err != nil {
		return
	}
	micRsp, err := s.micCli.GetMicrList(ctx, box.GetChannelId(), box.CreatorUid)
	if err != nil {
		return
	}

	usedMics, allUsedNum, subAllocNum := s.calcUsedMics(micRsp, applyRecords, boxes, box)
	if allUsedNum+box.GetMicCap() > 50 {
		err = protocol.NewExactServerError(nil, status.ErrChannelboxMicInUse)
		return
	}
	if subAllocNum+box.GetMicCap() > 30 {
		err = protocol.NewExactServerError(nil, status.ErrChannelboxMicNotEnough)
		return
	}

	for i := MAX_MIC_IDX; i >= MIN_MIC_IDX; i-- {
		if usedMics[i] == 1 {
			continue
		}
		if len(pubMicList) < 3 {
			pubMicList = append(pubMicList, uint32(i))
		} else if len(norMicList) < int(box.GetMicCap())-3 {
			norMicList = append(norMicList, uint32(i))
		} else {
			break
		}
	}
	return
}

func (s *ChannelBox) calcUsedMics(micRsp *channelmicPB.GetMicrListResp, applyRecords []*redis.MicApplyRecord,
	boxes []*mongo.BoxInfo, box *pb.BoxInfo) ([]uint32, uint32, uint32) {
	usedMics := make([]uint32, MAX_MIC_IDX+constNumOne)
	for _, micRecord := range micRsp.AllMicList {
		if micRecord.MicId < MIN_MIC_IDX || micRecord.MicId > MAX_MIC_IDX {
			continue
		}
		if micRecord.MicUid != constUnValidMicFlag {
			usedMics[micRecord.MicId] = constValidMicFlag
		}
	}

	now := uint32(time.Now().Unix())
	for _, applyRecord := range applyRecords {
		if applyRecord.MicId < MIN_MIC_IDX || applyRecord.MicId > MAX_MIC_IDX {
			continue
		}
		if applyRecord.Status == uint32(pb.MicStatus_MicStatusApplying) && now-applyRecord.UpdateTime <= APPLY_EXPIRE_SECOND {
			usedMics[applyRecord.MicId] = 1
		}
	}

	subAllocNum := uint32(0)
	for _, b := range boxes {
		if b.BoxId == box.GetBoxId() {
			continue
		}
		for _, micId := range b.PublicMicList {
			usedMics[micId] = constValidMicFlag
		}
		for _, micId := range b.NormalMicList {
			usedMics[micId] = constValidMicFlag
		}
		subAllocNum += b.MicCapacity
	}
	allUsedNum := uint32(0)
	for i := MIN_MIC_IDX; i <= MAX_MIC_IDX; i++ {
		if usedMics[i] == constValidMicFlag {
			allUsedNum += constNumOne
		}
	}
	return usedMics, allUsedNum, subAllocNum
}

func (s *ChannelBox) DelBoxInfo(ctx context.Context, channelId, boxId uint32) (uint32, error) {
	boxUserNum, err := s.redisDao.GetChannelBoxUserCnt(ctx, channelId, boxId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.DelBoxInfo redisDao.GetChannelBoxUserCnt channelId(%d) boxId(%d) err: %v", channelId, boxId, err)
		return 0, err
	}

	if boxUserNum > 0 {
		log.ErrorWithCtx(ctx, "channel %d box %d user num %d > 0", channelId, boxId, boxUserNum)
		return 0, protocol.NewExactServerError(nil, status.ErrChannelboxBoxInUse)
	}

	box, err := s.mDao.DelBox(ctx, channelId, boxId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.DelBoxInfo mDao.DelBox channelId(%d) boxId(%d) err: %v", channelId, boxId, err)
		return 0, err
	}

	if box == nil {
		return 0, protocol.NewExactServerError(nil, status.ErrChannelboxBoxNotExists)
	}

	boxNum, err := s.mDao.CountChannelBox(ctx, channelId, box.BoxType)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.DelBoxInfo mDao.CountChannelBox channelId(%d) boxType(%d) err: %v", channelId, box.BoxType, err)
		return 0, err
	}

	return boxNum, nil
}

func (s *ChannelBox) GetChannelAllBoxInfo(ctx context.Context, channelId, uid, boxType uint32) ([]*pb.BoxInfo, uint32, error) {
	boxList, err := s.mDao.GetChannelBoxList(ctx, channelId, boxType)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.GetChannelAllBoxInfo mDao.GetChannelBoxList channelId(%d) boxType(%d) err: %v",
			channelId, boxType, err)
		return nil, 0, err
	}

	if len(boxList) == 0 {
		return nil, 0, nil
	}

	var (
		micTotal uint32
	)

	boxes := make([]*pb.BoxInfo, 0)

	for _, box := range boxList {
		micTotal += box.MicCapacity

		var boxName string
		if box.State == 2 || box.State == 3 {
			// 审核通过 || 审核不通过
			boxName = box.Name
		} else if box.State == 0 && uid == box.CreatorUid {
			// 刚创建审核中
			boxName = box.ReviewName
		} else if box.State == 1 {
			// 修改审核中
			if uid == box.CreatorUid {
				boxName = box.ReviewName
			} else {
				boxName = box.Name
			}
		} else {
			continue
		}

		b := &pb.BoxInfo{
			ChannelId:     box.ChannelId,
			BoxId:         box.BoxId,
			CreatorUid:    box.CreatorUid,
			BoxType:       pb.BoxType(box.BoxType),
			BoxName:       boxName,
			MicCap:        box.MicCapacity,
			PublicMicList: box.PublicMicList,
			NormalMicList: box.NormalMicList,
		}

		boxes = append(boxes, b)
	}

	return boxes, micTotal, nil
}

func (s *ChannelBox) GetChannelBoxUserNums(ctx context.Context, channelId uint32, boxIdList []uint32) ([]*pb.BoxCntInfo, error) {
	countMap, err := s.redisDao.GetChannelBoxUserNums(ctx, channelId, boxIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.GetChannelAllBoxInfo redisDao.GetChannelAllBoxUserCnt channelId(%d) boxIdList(%v) err: %v",
			channelId, err)
		return nil, err
	}

	counts := make([]*pb.BoxCntInfo, 0)
	for boxId, userCnt := range countMap {
		counts = append(counts, &pb.BoxCntInfo{
			Boxid:      boxId,
			BoxUserCnt: userCnt,
			Channelid:  channelId,
		})
	}

	return counts, nil
}

func (s *ChannelBox) GetUserChannelBox(ctx context.Context, uid, channelId, boxType uint32) (uint32, []uint32, error) {
	box, err := s.mDao.GetUserChannelBox(ctx, uid, channelId, boxType)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserChannelBox mDao.GetUserChannelBox uid(%d) channelId(%d) boxType(%d) err: %v",
			uid, channelId, boxType)
		return 0, nil, err
	}

	if box == nil {
		return 0, nil, nil
	}

	return box.BoxId, []uint32{box.CommonBoxId, box.BroadcastBoxId}, nil
}

func (s *ChannelBox) GetChannelOpenMicBoxId(ctx context.Context, channelId uint32) (uint32, error) {
	mic, err := s.mDao.GetOpenMicId(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.GetChannelOpenMic mDao.GetOpenMicId channelId(%d) err: %v", channelId, err)
		return 0, err
	}

	if mic == nil {
		return 0, nil
	}

	return mic.OpenMicBoxId, nil
}

func (s *ChannelBox) HandleBoxNamePassResult(ctx context.Context, channelId, boxId, uid uint32, boxName string) error {
	old, err := s.mDao.UpdateBox(ctx, &mongo.BoxInfo{
		ChannelId:  channelId,
		BoxId:      boxId,
		Name:       boxName,
		State:      2,
		ReviewName: "",
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.HandleBoxNamePassResult mDao.UpdateBox channelId(%d) boxId(%d) boxName(%s) err: %v",
			channelId, boxId, boxName)
		return err
	}

	notify := &lpb.BoxChangeNotify{
		Box: &lpb.BoxInfo{
			Channelid: channelId,
			Boxid:     boxId,
			BoxName:   boxName,
			BoxType:   lpb.BoxType(old.BoxType),
		},
		ChangedAt: uint64(time.Now().UnixMilli()),
		BoxUid:    uid,
		Result:    lpb.BoxChangeNotify_ResultPass,
		ExistsBox: true,
	}

	if old.State == 0 {
		notify.Op = lpb.BoxChangeNotify_OpCreate
	} else {
		notify.Op = lpb.BoxChangeNotify_OpUpdateName
	}

	err = s.broadcastBoxChange(ctx, channelId, notify)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.HandleBoxNamePassResult broadcastBoxChange channelId(%d) boxId(%d) boxName(%s) err: %v",
			channelId, boxId, boxName)
		return err
	}

	return nil
}

func (s *ChannelBox) HandleBoxNameRejectResult(ctx context.Context, channelId, boxId, uid uint32) error {
	old, err := s.mDao.UpdateBox(ctx, &mongo.BoxInfo{
		ChannelId:  channelId,
		BoxId:      boxId,
		Name:       "***",
		State:      3,
		ReviewName: "",
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.HandleBoxNamePassResult UpdateBox channelId(%d) boxId(%d) err: %v", channelId, boxId)
		return err
	}

	notify := &lpb.BoxChangeNotify{
		Box: &lpb.BoxInfo{
			Channelid: channelId,
			Boxid:     boxId,
			BoxName:   "***",
			BoxType:   lpb.BoxType(old.BoxType),
		},
		ChangedAt: uint64(time.Now().UnixMilli()),
		BoxUid:    uid,
		Result:    lpb.BoxChangeNotify_ResultReject,
		ExistsBox: true,
	}

	if old.State == 0 {
		notify.Op = lpb.BoxChangeNotify_OpCreate
	} else {
		notify.Op = lpb.BoxChangeNotify_OpUpdateName
	}

	err = s.broadcastBoxChange(ctx, channelId, notify)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.HandleBoxNamePassResult broadcastBoxChange channelId(%d) boxId(%d) boxName(%s) err: %v",
			channelId, boxId)
		return err
	}

	return nil
}

func (s *ChannelBox) broadcastBoxChange(ctx context.Context, channelId uint32, notify *lpb.BoxChangeNotify) error {
	optContent, err := proto.Marshal(notify)
	if err != nil {
		log.ErrorWithCtx(ctx, "broadcastBoxInfo proto.Marshal err: %v", err)
		return err
	}

	err = s.chMsgCli.SendChannelBroadcastMsg(ctx, &channelPb.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         uint32(channelPb.ChannelMsgType_CHANNEL_BOX_INFO_CHANGE_NOTIFY),
		PbOptContent: optContent,
		Content:      []byte(""),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "broadcastBoxChange SendChannelBroadcastMsg channelId(%d) err: %v", channelId, err)
		return err
	}

	log.InfoWithCtx(ctx, "broadcastBoxChange channelId(%d) notify(%v) successfully", channelId, notify)
	return nil
}

// 切换玩法时，需要更新主房间用户信息，需要将包厢里的用户加入到主房间用户列表中，如果超人数客户端会走退房流程，再删除记录（可能会有不一致的情况，暂时不处理了）
func (s *ChannelBox) UpdateMainChannelUserWhenSwitchTab(ctx context.Context, channelId uint32, oldTabId uint32) error {
	tabRsp, terr := s.tabCli.FiniteTabs(ctx, &tab.FiniteTabsReq{
		Tabs: []*tab.Tab{
			{
				Id: oldTabId,
			},
		},
	})
	if terr != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.UpdateMainChannelUserWhenSwitchTab tabCli.FiniteTabs oldTabId(%d) err: %v", oldTabId, terr)
		return terr
	}
	if len(tabRsp.Tabs) == 0 {
		log.WarnWithCtx(ctx, "ChannelBox.UpdateMainChannelUserWhenSwitchTab tabCli.FiniteTabs oldTabId(%d) no tab found", oldTabId)
		return nil
	}
	if tabRsp.GetTabs()[0].GetCategoryMapping() != uint32(topic_channel.CategoryType_MELEE_TYPE) {
		// 不是从团战房切到其他房间的，没有包厢信息，不需要更新
		return nil
	}
	// 先暴力一次性处理
	uids, err := s.mDao.GetAllUsersInBox(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.UpdateMainChannelUserWhenSwitchTab mDao.GetAllUsersInBox channelId(%d) err: %v", channelId, err)
		return err
	}
	if len(uids) == 0 {
		return nil
	}

	err = s.redisDao.AddMainChannelUser(ctx, channelId, uids...)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.UpdateMainChannelUserWhenSwitchTab redisDao.AddMainChannelUser channelId(%d) err: %v", channelId, err)
		return err
	}
	return nil
}
