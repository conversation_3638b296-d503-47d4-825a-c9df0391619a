// Code generated by ifacemaker; DO NOT EDIT.

package redis

import (
	"context"
	"time"
)

// IRedisDao ...
type IRedisDao interface {
	GetMicApplyRecords(ctx context.Context, channelId uint32) ([]*MicApplyRecord, error)
	SetMicApplyRecords(ctx context.Context, channelId uint32, records []*MicApplyRecord) error
	SetMic(ctx context.Context, channelId uint32, micUids []uint32, serverTimeMs uint64) (err error)
	GetMic(ctx context.Context, channelId uint32) (micList *MicList, err error)
	AddChannelBoxUser(ctx context.Context, channelId, boxId, uid uint32) (err error)
	DelChannelBoxUser(ctx context.Context, channelId, boxId, uid uint32) (err error)
	DelChannelBoxs(ctx context.Context, channelId uint32) (err error)
	GetChannelBoxUserCnt(ctx context.Context, channelId, boxId uint32) (boxCnt uint32, err error)
	GetBoxsCntByChannelId(ctx context.Context, channelId uint32) (cntMap map[uint32]int64, err error)
	GetChannelBoxUserNums(ctx context.Context, channelId uint32, boxIdList []uint32) (map[uint32]uint32, error)
	DelChannelBox(ctx context.Context, channelId uint32, boxIdList ...uint32) error
	Lock(ctx context.Context, key, val string, exp time.Duration) (bool, error)
	Unlock(ctx context.Context, key, val string) error
	AddMainChannelUser(ctx context.Context, channelId uint32, uids ...uint32) (err error)
	DelMainChannelUser(ctx context.Context, channelId, uid uint32) (err error)
	GetMainChannelUsers(ctx context.Context, channelId uint32) (uids []uint32, err error)
}
