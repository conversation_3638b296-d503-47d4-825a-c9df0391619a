package redis

import (
	context "context"
	"time"
)

type IRedisDao interface {
	Add<PERSON>han<PERSON>BoxUser(ctx context.Context, channelId, boxId, uid uint32) (err error)
	DelChannelBox(ctx context.Context, channelId uint32, boxIdList ...uint32) error
	DelChannelBoxUser(ctx context.Context, channelId, boxId, uid uint32) (err error)
	DelChannelBoxs(ctx context.Context, channelId uint32) (err error)
	GetBoxsCntByChannelId(ctx context.Context, channelId uint32) (cntMap map[uint32]int64, err error)
	GetChannelBoxUserCnt(ctx context.Context, channelId, boxId uint32) (boxCnt uint32, err error)
	GetChannelBoxUserNums(ctx context.Context, channelId uint32, boxIdList []uint32) (map[uint32]uint32, error)
	GetMic(ctx context.Context, channelId uint32) (micList *MicList, err error)
	GetMicApplyRecords(ctx context.Context, channelId uint32) ([]*MicApplyRecord, error)
	SetMic(ctx context.Context, channelId uint32, micUids []uint32, serverTimeMs uint64) (err error)
	SetMicApplyRecords(ctx context.Context, channelId uint32, records []*MicApplyRecord) error
	Lock(ctx context.Context, key, val string, exp time.Duration) (bool, error)
	Unlock(ctx context.Context, key, val string) error
}
