package redis

import (
	"context"
	"fmt"
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"strconv"
	"time"
)

//go:generate ifacemaker -f *.go -s RedisDao -p redis -o redis_api.go -i IRedisDao
//go:generate mockgen -destination=../../mocks/mock_redis.go -package=mocks golang.52tt.com/services/user/channelbox/internal/db/redis IRedisDao
const DefaultBoxCnt uint32 = 5

type RedisDao struct {
	cli *redis.Client
}

func NewRedisDao(cfg *config.RedisConfig) (*RedisDao, error) {
	cli := redis.NewClient(&redis.Options{
		Network:            cfg.Protocol,
		Addr:               cfg.Addr(),
		DB:                 cfg.DB,
		PoolSize:           cfg.PoolSize,
		IdleCheckFrequency: cfg.IdleCheckFrequency(),
	})

	_, err := cli.Ping().Result()
	if err != nil {
		log.Errorf("NewRedisCache ping err: %v", err)
		return nil, err
	}

	rd := &RedisDao{cli: cli}
	return rd, nil
}

func (d *RedisDao) getChannelBoxUserKey(chanenlId, boxId uint32) string {
	return fmt.Sprintf("channelbox_users_%d_%d", chanenlId, boxId)

}

func (d *RedisDao) AddChannelBoxUser(ctx context.Context, channelId, boxId, uid uint32) (err error) {
	err = d.cli.SAdd(d.getChannelBoxUserKey(channelId, boxId), uid).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelBoxUser SAdd err:%v, channelId:%d, boxId:%d, uid:%d, err", channelId, boxId, uid)
		return err
	}
	return nil
}

func (d *RedisDao) DelChannelBoxUser(ctx context.Context, channelId, boxId, uid uint32) (err error) {
	err = d.cli.SRem(d.getChannelBoxUserKey(channelId, boxId), uid).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelBoxUser SRem err:%v, channelId:%d, boxId:%d, uid:%d", err, channelId, boxId, uid)
		return err
	}
	return nil
}

func (d *RedisDao) DelChannelBoxs(ctx context.Context, channelId uint32) (err error) {
	pipeline := d.cli.Pipeline()
	var index uint32
	for index = 1; index <= DefaultBoxCnt; index++ {
		pipeline.Del(d.getChannelBoxUserKey(channelId, index))
	}
	_, err = pipeline.Exec()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelBoxs exec err:%v, channelId:%d", err, channelId)
		return err
	}
	return nil
}

func (d *RedisDao) GetChannelBoxUserCnt(ctx context.Context, channelId, boxId uint32) (boxCnt uint32, err error) {
	cnt, err := d.cli.SCard(d.getChannelBoxUserKey(channelId, boxId)).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelBoxUserCnt SCard err:%v, channelId:%d, boxId:%d", err, channelId, boxId)
		return 0, err
	}
	boxCnt = uint32(cnt)
	return boxCnt, nil
}

func (d *RedisDao) GetBoxsCntByChannelId(ctx context.Context, channelId uint32) (cntMap map[uint32]int64, err error) {
	pipeline := d.cli.Pipeline()
	result := make(map[uint32]*redis.IntCmd, DefaultBoxCnt)
	cntMap = make(map[uint32]int64, DefaultBoxCnt)
	var index uint32
	for index = 1; index <= DefaultBoxCnt; index++ {
		result[index] = pipeline.SCard(d.getChannelBoxUserKey(channelId, index))
	}
	_, err = pipeline.Exec()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBoxsCntByChannelId exec err:%v, channelId:%d", err, channelId)
		return nil, err
	}
	for index, r := range result {
		cnt, err := r.Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "[GetByChannelIDs] get result error:%v", err)
			continue
		}
		cntMap[index] = cnt
	}
	err = pipeline.Close()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBoxsCntByChannelId pipeline.Close err:%v, channelId:%d", err, channelId)
	}
	return cntMap, nil
}

func (d *RedisDao) GetChannelBoxUserNums(ctx context.Context, channelId uint32, boxIdList []uint32) (map[uint32]uint32, error) {
	pl := d.cli.Pipeline()

	resultMap := make(map[uint32]*redis.IntCmd)
	for _, boxId := range boxIdList {
		resultMap[boxId] = pl.SCard(d.getChannelBoxUserKey(channelId, boxId))
	}

	_, err := pl.Exec()
	if err != nil {
		log.ErrorWithCtx(ctx, "RedisDao.GetChannelAllBoxUserCnt pl.Exec channelId(%d) boxIdList(%v) err: %v",
			channelId, boxIdList, err)
		return nil, err
	}

	_ = pl.Close()

	cntMap := make(map[uint32]uint32)
	for boxId, result := range resultMap {
		val, err := result.Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "RedisDao.GetChannelAllBoxUserCnt result.Result channelId(%d) boxId(%d) err: %v",
				channelId, boxId, err)
			return nil, err
		}

		cntMap[boxId] = uint32(val)
	}

	return cntMap, nil
}

func (d *RedisDao) DelChannelBox(ctx context.Context, channelId uint32, boxIdList ...uint32) error {
	keys := make([]string, 0)
	for _, boxId := range boxIdList {
		keys = append(keys, d.getChannelBoxUserKey(channelId, boxId))
	}

	_, err := d.cli.Del(keys...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "RedisDao.DelChannelBox channelId(%d) boxIdList(%v) err: %v", channelId, boxIdList, err)
		return err
	}

	return nil
}

func (d *RedisDao) Lock(ctx context.Context, key, val string, exp time.Duration) (bool, error) {
	ok, err := d.cli.SetNX(key, val, exp).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "RedisDao.lock key(%s) err: %v", key, err)
		return false, err
	}

	return ok, nil
}

func (d *RedisDao) Unlock(ctx context.Context, key, val string) error {
	const script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end"

	v, err := d.cli.Eval(script, []string{key}, val).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "RedisDao.unlock Eval key(%s) err: %v", key, err)
		return err
	}

	if v == 0 {
		log.ErrorWithCtx(ctx, "RedisDao.unlock failed")
	}

	return nil
}

func (d *RedisDao) getMainChannelUserKey(channelId uint32) string {
	return fmt.Sprintf("channelbox_main_users_%d", channelId)
}

func (d *RedisDao) AddMainChannelUser(ctx context.Context, channelId uint32, uids ...uint32) (err error) {
	key := d.getMainChannelUserKey(channelId)

	// 将[]uint32转换为[]interface{}
	args := make([]interface{}, len(uids))
	for i, uid := range uids {
		args[i] = uid
	}

	// 使用转换后的参数调用SAdd
	err = d.cli.SAdd(key, args...).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddMainChannelUser SAdd err:%v, channelId:%d, uids:%v", err, channelId, uids)
		return err
	}
	return nil
}

func (d *RedisDao) DelMainChannelUser(ctx context.Context, channelId, uid uint32) (err error) {
	key := d.getMainChannelUserKey(channelId)
	err = d.cli.SRem(key, uid).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelMainChannelUser SRem err:%v, channelId:%d, uid:%d", err, channelId, uid)
		return err
	}
	return nil
}

func (d *RedisDao) GetMainChannelUsers(ctx context.Context, channelId uint32) (uids []uint32, err error) {
	key := d.getMainChannelUserKey(channelId)

	uidStrs, err := d.cli.SMembers(key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMainChannelUsers SMembers err:%v, channelId:%d", err, channelId)
		return nil, err
	}
	uids = make([]uint32, 0, len(uidStrs))
	for _, uidStr := range uidStrs {
		uid, err := strconv.Atoi(uidStr)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMainChannelUsers strconv.Atoi err:%v, uidStr:%s, channelId:%d", err, uidStr, channelId)
			continue
		}
		uids = append(uids, uint32(uid))
	}
	return uids, nil
}
