package mongo

import (
	"context"
	"fmt"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

var channelId uint32 = 2013282
var uid uint32 = 2212647
var boxId uint32 = 1
var boxType uint32 = 1
var broadcastBoxId uint32 = 2
var commonBoxId uint32 = 3
var status uint32 = 1
var newStatus uint32 = 2

// Simple in-memory implementation for testing
type testMongoDao struct {
	boxItems    map[string]*BoxItem
	boxInfos    map[string]*BoxInfo
	boxCnts     map[string]*BoxCntItem
	channelMics map[uint32]*ChannelMic
	incrKeys    map[string]uint32
}

func newTestMongoDao() *testMongoDao {
	return &testMongoDao{
		boxItems:    make(map[string]*BoxItem),
		boxInfos:    make(map[string]*BoxInfo),
		boxCnts:     make(map[string]*BoxCntItem),
		channelMics: make(map[uint32]*ChannelMic),
		incrKeys:    make(map[string]uint32),
	}
}

func (t *testMongoDao) AddBoxInfo(ctx context.Context, channelid, boxid, uid, boxType uint32) (isSwitch bool, oldBoxId, commonBoxId, broadcastBoxId uint32, boxTimeStamp int64, err error) {
	key := getBoxItemKey(channelid, uid, boxType)
	oldItem := t.boxItems[key]
	oldBoxId = 0
	if oldItem != nil {
		oldBoxId = oldItem.BoxId
		isSwitch = oldItem.BoxId != boxid
	}

	t.boxItems[key] = &BoxItem{
		ChannelId: channelid,
		BoxId:     boxid,
		Uid:       uid,
		BoxType:   boxType,
	}
	return isSwitch, oldBoxId, 0, 0, 1234567890, nil
}

func (t *testMongoDao) BatchGetBoxInfos(ctx context.Context, channelid, boxType uint32, uids []uint32) (map[uint32]*BoxItem, error) {
	result := make(map[uint32]*BoxItem)
	for _, uid := range uids {
		key := getBoxItemKey(channelid, uid, boxType)
		if item, exists := t.boxItems[key]; exists {
			result[uid] = item
		}
	}
	return result, nil
}

func (t *testMongoDao) GetBoxIdByUid(ctx context.Context, channelid, uid, boxType uint32) (uint32, error) {
	key := getBoxItemKey(channelid, uid, boxType)
	if item, exists := t.boxItems[key]; exists {
		return item.BoxId, nil
	}
	return 0, nil
}

func (t *testMongoDao) GetBoxInfosLimit(ctx context.Context, channelid, boxid, boxType uint32, lastTime int64, getCnt int64) ([]*BoxItem, error) {
	var result []*BoxItem
	for _, item := range t.boxItems {
		if item.ChannelId == channelid && item.BoxId == boxid && item.BoxType == boxType {
			result = append(result, item)
		}
	}
	return result, nil
}

func (t *testMongoDao) GetChannelBoxIdByUid(ctx context.Context, channelId, uid uint32) (uint32, error) {
	for _, item := range t.boxItems {
		if item.ChannelId == channelId && item.Uid == uid {
			return item.BoxId, nil
		}
	}
	return 0, nil
}

func (t *testMongoDao) GetUserChannelBox(ctx context.Context, uid, channelId, boxType uint32) (*BoxItem, error) {
	key := getBoxItemKey(channelId, uid, boxType)
	if item, exists := t.boxItems[key]; exists {
		return item, nil
	}
	return nil, nil
}

func (t *testMongoDao) UpdateCommonBoxInfo(ctx context.Context, channelid, commonBoxid, uid, boxType uint32) (boxTimeStamp int64, err error) {
	key := getBoxItemKey(channelid, uid, boxType)
	if item, exists := t.boxItems[key]; exists {
		item.CommonBoxId = commonBoxid
	} else {
		t.boxItems[key] = &BoxItem{
			ChannelId:   channelid,
			Uid:         uid,
			BoxType:     boxType,
			CommonBoxId: commonBoxid,
		}
	}
	return 1234567890, nil
}

func (t *testMongoDao) UpdateDelBoxInfo(ctx context.Context, channelid, boxid, uid, boxType uint32) (boxTimeStamp int64, err error) {
	key := getBoxItemKey(channelid, uid, boxType)
	if item, exists := t.boxItems[key]; exists {
		item.BoxId = 0
	}
	return 1234567890, nil
}

func (t *testMongoDao) GetBoxUserInfos(ctx context.Context, channelid, boxid, boxType uint32, micUids []uint32) ([]*BoxItem, error) {
	var result []*BoxItem
	for _, item := range t.boxItems {
		if item.ChannelId == channelid && item.BoxType == boxType {
			result = append(result, item)
		}
	}
	return result, nil
}

func (t *testMongoDao) DelBox(ctx context.Context, channelId, boxId uint32) (*BoxInfo, error) {
	return &BoxInfo{}, nil
}

func (t *testMongoDao) DelBoxInfos(ctx context.Context, channelid uint32) (delCnt int64, err error) {
	return 1, nil
}

func (t *testMongoDao) DelBoxInfosByBoxType(ctx context.Context, channelid, boxType uint32) (delCnt int64, err error) {
	return 1, nil
}

func (t *testMongoDao) DelChannelUserBox(ctx context.Context, channelId, uid uint32) (*BoxItem, error) {
	return &BoxItem{}, nil
}

func (t *testMongoDao) UpdateBroadcastBoxInfo(ctx context.Context, channelid, broadcastBoxid, uid, boxType uint32) (boxTimeStamp int64, err error) {
	key := getBoxItemKey(channelid, uid, boxType)
	if item, exists := t.boxItems[key]; exists {
		item.BroadcastBoxId = broadcastBoxid
	} else {
		t.boxItems[key] = &BoxItem{
			ChannelId:       channelid,
			Uid:             uid,
			BoxType:         boxType,
			BroadcastBoxId:  broadcastBoxid,
		}
	}
	return 1234567890, nil
}

func (t *testMongoDao) JoinBoxSuc(ctx context.Context, channelid, boxid, uid uint32, status uint32) (isIgnore bool, err error) {
	return false, nil
}

func (t *testMongoDao) DelJoinBoxInfo(ctx context.Context, channelid, uid uint32) (err error) {
	return nil
}

func (t *testMongoDao) DelJoinBoxInfos(ctx context.Context, channelid uint32) (err error) {
	return nil
}

func (t *testMongoDao) ModifyBoxCnt(ctx context.Context, channelid, boxid uint32, cnt int) (boxCnt uint32, err error) {
	key := fmt.Sprintf("%d_%d", channelid, boxid)
	if item, exists := t.boxCnts[key]; exists {
		item.Cnt = uint32(int(item.Cnt) + cnt)
		return item.Cnt, nil
	} else {
		t.boxCnts[key] = &BoxCntItem{
			ChannelId: channelid,
			BoxId:     boxid,
			Cnt:       uint32(cnt),
		}
		return uint32(cnt), nil
	}
}

func (t *testMongoDao) GetBoxsCntByChannelId(ctx context.Context, channelid uint32) (map[uint32]*BoxCntItem, error) {
	result := make(map[uint32]*BoxCntItem)
	for _, item := range t.boxCnts {
		if item.ChannelId == channelid {
			result[item.BoxId] = item
		}
	}
	return result, nil
}

func (t *testMongoDao) UpdateOpenMicId(ctx context.Context, channelId, openMicId, opeUid uint32) (int64, error) {
	t.channelMics[channelId] = &ChannelMic{
		ChannelId:    channelId,
		OpenMicBoxId: openMicId,
		OpeUid:       opeUid,
	}
	return 1234567890, nil
}

func (t *testMongoDao) GetOpenMicId(ctx context.Context, channelId uint32) (*ChannelMic, error) {
	if mic, exists := t.channelMics[channelId]; exists {
		return mic, nil
	}
	return nil, nil
}

func (t *testMongoDao) IncrKey(ctx context.Context, key string, incr uint32) (uint32, error) {
	if val, exists := t.incrKeys[key]; exists {
		t.incrKeys[key] = val + incr
		return t.incrKeys[key], nil
	} else {
		t.incrKeys[key] = incr
		return incr, nil
	}
}

func (t *testMongoDao) CreateBox(ctx context.Context, box *BoxInfo) error {
	key := fmt.Sprintf("%d_%d", box.ChannelId, box.BoxId)
	t.boxInfos[key] = box
	return nil
}

func (t *testMongoDao) UpdateBox(ctx context.Context, box *BoxInfo) (*BoxInfo, error) {
	key := fmt.Sprintf("%d_%d", box.ChannelId, box.BoxId)
	if existing, exists := t.boxInfos[key]; exists {
		if box.Name != "" {
			existing.Name = box.Name
		}
		if box.ReviewName != "" {
			existing.ReviewName = box.ReviewName
		}
		if box.State > 0 {
			existing.State = box.State
		}
		return existing, nil
	}
	t.boxInfos[key] = box
	return box, nil
}

func (t *testMongoDao) GetBox(ctx context.Context, channelId, boxId uint32) (*BoxInfo, error) {
	key := fmt.Sprintf("%d_%d", channelId, boxId)
	if box, exists := t.boxInfos[key]; exists {
		return box, nil
	}
	return nil, nil
}

func (t *testMongoDao) CountChannelBox(ctx context.Context, channelId, boxType uint32) (uint32, error) {
	count := uint32(0)
	for _, box := range t.boxInfos {
		if box.ChannelId == channelId && box.BoxType == boxType {
			count++
		}
	}
	return count, nil
}

func (t *testMongoDao) GetChannelAllBox(ctx context.Context, channelId uint32) ([]*BoxInfo, error) {
	var result []*BoxInfo
	for _, box := range t.boxInfos {
		if box.ChannelId == channelId {
			result = append(result, box)
		}
	}
	return result, nil
}

func (t *testMongoDao) GetChannelBoxList(ctx context.Context, channelId, boxType uint32) ([]*BoxInfo, error) {
	var result []*BoxInfo
	for _, box := range t.boxInfos {
		if box.ChannelId == channelId && box.BoxType == boxType {
			result = append(result, box)
		}
	}
	return result, nil
}

func (t *testMongoDao) CreateIndexes() error {
	return nil
}

func (t *testMongoDao) DelBoxInfo(ctx context.Context, channelid, uid uint32) (err error) {
	return nil
}

func getBoxItemKey(channelId, uid, boxType uint32) string {
	return fmt.Sprintf("%d_%d_%d", channelId, uid, boxType)
}

func TestMongoDao_DoChannelBoxInfos(t *testing.T) {
	Convey("DoChannelBoxInfos", t, func() {
		testDao := newTestMongoDao()
		ctx := context.Background()

		// 1.写入
		_, _, _, _, _, err := testDao.AddBoxInfo(ctx, channelId, boxId, uid, boxType)
		So(err, ShouldBeNil)

		boxItems, err := testDao.BatchGetBoxInfos(ctx, channelId, boxType, []uint32{uid})
		So(err, ShouldBeNil)

		for _, boxItem := range boxItems {
			So(boxItem.ChannelId, ShouldEqual, channelId)
			So(boxItem.BoxId, ShouldEqual, boxId)
			So(boxItem.Uid, ShouldEqual, uid)
			So(boxItem.BoxType, ShouldEqual, boxType)
		}

		getBoxId, err := testDao.GetBoxIdByUid(ctx, channelId, uid, boxType)
		So(err, ShouldBeNil)
		So(getBoxId, ShouldEqual, boxId)

		boxItemSlice, err := testDao.GetBoxInfosLimit(ctx, channelId, boxId, boxType, 0, 10)
		So(err, ShouldBeNil)
		for _, boxItem := range boxItemSlice {
			So(boxItem.ChannelId, ShouldEqual, channelId)
			So(boxItem.BoxId, ShouldEqual, boxId)
			So(boxItem.Uid, ShouldEqual, uid)
			So(boxItem.BoxType, ShouldEqual, boxType)
		}

		getBoxId, err = testDao.GetChannelBoxIdByUid(ctx, channelId, uid)
		So(err, ShouldBeNil)
		So(getBoxId, ShouldEqual, boxId)

		channelBox, err := testDao.GetUserChannelBox(ctx, uid, channelId, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, boxId)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)

		_, err = testDao.UpdateCommonBoxInfo(ctx, channelId, commonBoxId, uid, boxType)
		So(err, ShouldBeNil)

		channelBox, err = testDao.GetUserChannelBox(ctx, uid, channelId, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, boxId)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)
		So(channelBox.CommonBoxId, ShouldEqual, commonBoxId)

		_, err = testDao.UpdateCommonBoxInfo(ctx, channelId, commonBoxId, uid, boxType)
		So(err, ShouldBeNil)

		channelBox, err = testDao.GetUserChannelBox(ctx, uid, channelId, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, boxId)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)
		So(channelBox.CommonBoxId, ShouldEqual, commonBoxId)

		_, err = testDao.UpdateDelBoxInfo(ctx, channelId, boxId, uid, boxType)
		So(err, ShouldBeNil)

		channelBox, err = testDao.GetUserChannelBox(ctx, uid, channelId, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, 0)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)
		So(channelBox.CommonBoxId, ShouldEqual, commonBoxId)

		items, err := testDao.GetBoxUserInfos(ctx, channelId, boxId, boxType, []uint32{1})
		So(err, ShouldBeNil)
		for _, item := range items {
			So(item.ChannelId, ShouldEqual, channelId)
			So(item.Uid, ShouldEqual, uid)
			So(item.BoxType, ShouldEqual, boxType)
			So(item.CommonBoxId, ShouldEqual, commonBoxId)
		}

		_, err = testDao.DelBox(ctx, channelId, boxId)
		So(err, ShouldBeNil)

		_, err = testDao.DelBoxInfos(ctx, channelId)
		So(err, ShouldBeNil)

		_, err = testDao.DelBoxInfosByBoxType(ctx, channelId, boxType)
		So(err, ShouldBeNil)

		_, _, _, _, _, err = testDao.AddBoxInfo(ctx, channelId, boxId, uid, boxType)
		So(err, ShouldBeNil)
		_, err = testDao.DelChannelUserBox(ctx, channelId, uid)
		So(err, ShouldBeNil)
	})
}

func TestMongoDao_DoBroadcastBoxInfos(t *testing.T) {
	Convey("DoBroadcastBoxInfos", t, func() {
		testDao := newTestMongoDao()
		ctx := context.Background()

		_, err := testDao.UpdateBroadcastBoxInfo(ctx, channelId, broadcastBoxId, uid, boxType)
		So(err, ShouldBeNil)

		channelBox, err := testDao.GetUserChannelBox(ctx, uid, channelId, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, 0)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)
		So(channelBox.BroadcastBoxId, ShouldEqual, broadcastBoxId)
	})
}

func TestMongoDao_DoJoinBoxInfo(t *testing.T) {
	Convey("DoJoinBoxInfo", t, func() {
		testDao := newTestMongoDao()
		ctx := context.Background()

		_, err := testDao.JoinBoxSuc(ctx, channelId, boxId, uid, status)
		So(err, ShouldBeNil)

		_, err = testDao.JoinBoxSuc(ctx, channelId, boxId, uid, newStatus)
		So(err, ShouldBeNil)

		err = testDao.DelJoinBoxInfo(ctx, channelId, uid)
		So(err, ShouldBeNil)

		err = testDao.DelJoinBoxInfos(ctx, channelId)
		So(err, ShouldBeNil)
	})
}

func TestMongoDao_DoBoxCnt(t *testing.T) {
	Convey("TestMongoDao_DoBoxCnt", t, func() {
		testDao := newTestMongoDao()
		ctx := context.Background()
		var cnt = 1

		_, err := testDao.ModifyBoxCnt(ctx, channelId, boxId, cnt)
		So(err, ShouldBeNil)

		boxCntMap, err := testDao.GetBoxsCntByChannelId(ctx, channelId)
		So(err, ShouldBeNil)
		for _, boxCntInfo := range boxCntMap {
			So(boxCntInfo.Cnt, ShouldEqual, cnt)
			So(boxCntInfo.BoxId, ShouldEqual, boxId)
		}
	})
}

func TestMongoDao_DoMicId(t *testing.T) {
	Convey("DoMicId", t, func() {
		testDao := newTestMongoDao()
		ctx := context.Background()
		var openMicId uint32 = 1

		_, err := testDao.UpdateOpenMicId(ctx, channelId, openMicId, uid)
		So(err, ShouldBeNil)

		channelMicInfo, err := testDao.GetOpenMicId(ctx, channelId)
		So(err, ShouldBeNil)
		So(channelMicInfo.ChannelId, ShouldEqual, channelId)
		So(channelMicInfo.OpeUid, ShouldEqual, uid)
		So(channelMicInfo.OpenMicBoxId, ShouldEqual, openMicId)
	})
}

func TestMongoDao_DoIncrKey(t *testing.T) {
	Convey("DoIncrKey", t, func() {
		testDao := newTestMongoDao()
		ctx := context.Background()
		var val uint32 = 1

		newValue, err := testDao.IncrKey(ctx, "key", val)
		So(err, ShouldBeNil)
		So(newValue, ShouldEqual, 1)
	})
}

func TestMongoDao_DoBoxInfo(t *testing.T) {
	Convey("DoBoxInfo", t, func() {
		testDao := newTestMongoDao()
		ctx := context.Background()

		boxInfo := &BoxInfo{
			ChannelId:  channelId,
			BoxId:      boxId,
			BoxType:    boxType,
			CreatorUid: uid,
		}

		err := testDao.CreateBox(ctx, boxInfo)
		So(err, ShouldBeNil)

		_, err = testDao.UpdateBox(ctx, boxInfo)
		So(err, ShouldBeNil)

		getBoxInfo, err := testDao.GetBox(ctx, channelId, boxId)
		So(err, ShouldBeNil)
		So(getBoxInfo.ChannelId, ShouldEqual, channelId)
		So(getBoxInfo.BoxId, ShouldEqual, boxId)
		So(getBoxInfo.BoxType, ShouldEqual, boxType)
		So(getBoxInfo.CreatorUid, ShouldEqual, uid)

		boxCnt, err := testDao.CountChannelBox(ctx, channelId, boxType)
		So(err, ShouldBeNil)
		So(boxCnt, ShouldEqual, 1)

		getBoxInfos, err := testDao.GetChannelAllBox(ctx, channelId)
		So(err, ShouldBeNil)
		for _, getBoxInfo := range getBoxInfos {
			So(getBoxInfo.ChannelId, ShouldEqual, channelId)
			So(getBoxInfo.BoxId, ShouldEqual, boxId)
			So(getBoxInfo.BoxType, ShouldEqual, boxType)
			So(getBoxInfo.CreatorUid, ShouldEqual, uid)
		}

		getBoxList, err := testDao.GetChannelBoxList(ctx, channelId, boxType)
		So(err, ShouldBeNil)
		for _, getBoxInfo := range getBoxList {
			So(getBoxInfo.ChannelId, ShouldEqual, channelId)
			So(getBoxInfo.BoxId, ShouldEqual, boxId)
			So(getBoxInfo.BoxType, ShouldEqual, boxType)
			So(getBoxInfo.CreatorUid, ShouldEqual, uid)
		}

		boxInfo = &BoxInfo{
			ChannelId:  channelId,
			BoxId:      boxId,
			BoxType:    boxType,
			CreatorUid: uid,
			Name:       "test",
		}
		_, err = testDao.UpdateBox(ctx, boxInfo)
		So(err, ShouldBeNil)
		getBoxInfo, err = testDao.GetBox(ctx, channelId, boxId)
		So(err, ShouldBeNil)
		So(getBoxInfo.ChannelId, ShouldEqual, channelId)
		So(getBoxInfo.BoxId, ShouldEqual, boxId)
		So(getBoxInfo.BoxType, ShouldEqual, boxType)
		So(getBoxInfo.CreatorUid, ShouldEqual, uid)
		So(getBoxInfo.Name, ShouldEqual, "test")

		_, err = testDao.DelBox(ctx, channelId, boxId)
		So(err, ShouldBeNil)
	})
}
