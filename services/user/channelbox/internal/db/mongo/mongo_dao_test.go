package mongo

import (
	"context"
	"github.com/orlangure/gnomock"
	gnomock_mongo "github.com/orlangure/gnomock/preset/mongo"
	. "github.com/smartystreets/goconvey/convey"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"testing"
)

var mongoCli *MongoDao
var channelId uint32
var uid uint32
var boxId uint32
var boxType uint32
var broadcastBoxId uint32
var commonBoxId uint32
var status uint32
var newStatus uint32

func init() {
	p := gnomock_mongo.Preset(
	//mongo.WithData("./testdata/"),
	//mongo.WithUser("gnomock", "gnomick"),
	)
	c, err := gnomock.Start(p)
	if err != nil {
		log.Errorf("Failed to create mongodb err:%v", err)
		return
	}
	mongoConfig := &config.MongoConfig{
		Database:    "channelbox",
		MaxPoolSize: 500,
		Addrs:       c.DefaultAddress(),
	}
	mDao, err := NewMongoDao(context.Background(), mongoConfig)
	if err != nil {
		log.Errorf("Failed to create mongodb %v", err)
		return
	}
	mDao.CreateIndexes()
	mongoCli = mDao

	channelId = 2013282
	uid = 2212647
	boxId = 1
	boxType = 1
	status = 1
	newStatus = 2
}

func TestMongoDao_DoChannelBoxInfos(t *testing.T) {
	Convey("DoChannelBoxInfos", t, func() {
		ctx := context.Background()

		// 1.写入
		_, _, _, _, _, err := mongoCli.AddBoxInfo(ctx, channelId, boxId, uid, boxType)
		So(err, ShouldBeNil)

		boxItems, err := mongoCli.BatchGetBoxInfos(ctx, channelId, boxType, []uint32{uid})
		So(err, ShouldBeNil)

		for _, boxItem := range boxItems {
			So(boxItem.ChannelId, ShouldEqual, channelId)
			So(boxItem.BoxId, ShouldEqual, boxId)
			So(boxItem.Uid, ShouldEqual, uid)
			So(boxItem.BoxType, ShouldEqual, boxType)
		}

		getBoxId, err := mongoCli.GetBoxIdByUid(ctx, channelId, uid, boxType)
		So(err, ShouldBeNil)
		So(getBoxId, ShouldEqual, boxId)

		boxItemSlice, err := mongoCli.GetBoxInfosLimit(ctx, channelId, boxId, boxType, 0, 10)
		So(err, ShouldBeNil)
		for _, boxItem := range boxItemSlice {
			So(boxItem.ChannelId, ShouldEqual, channelId)
			So(boxItem.BoxId, ShouldEqual, boxId)
			So(boxItem.Uid, ShouldEqual, uid)
			So(boxItem.BoxType, ShouldEqual, boxType)
		}

		getBoxId, err = mongoCli.GetChannelBoxIdByUid(ctx, channelId, uid)
		So(err, ShouldBeNil)
		So(getBoxId, ShouldEqual, boxId)

		channelBox, err := mongoCli.GetUserChannelBox(ctx, uid, channelId, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, boxId)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)

		_, err = mongoCli.UpdateCommonBoxInfo(ctx, channelId, commonBoxId, uid, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, boxId)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)

		channelBox, err = mongoCli.GetUserChannelBox(ctx, uid, channelId, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, boxId)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)
		So(channelBox.CommonBoxId, ShouldEqual, commonBoxId)

		_, err = mongoCli.UpdateCommonBoxInfo(ctx, channelId, commonBoxId, uid, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, boxId)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)

		channelBox, err = mongoCli.GetUserChannelBox(ctx, uid, channelId, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, boxId)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)
		So(channelBox.CommonBoxId, ShouldEqual, commonBoxId)

		_, err = mongoCli.UpdateDelBoxInfo(ctx, channelId, boxId, uid, boxType)
		So(err, ShouldBeNil)

		channelBox, err = mongoCli.GetUserChannelBox(ctx, uid, channelId, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, 0)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)
		So(channelBox.CommonBoxId, ShouldEqual, commonBoxId)

		items, err := mongoCli.GetBoxUserInfos(ctx, channelId, boxId, boxType, []uint32{1})
		So(err, ShouldBeNil)
		for _, item := range items {
			So(item.ChannelId, ShouldEqual, channelId)
			So(item.BoxId, ShouldEqual, 0)
			So(item.Uid, ShouldEqual, uid)
			So(item.BoxType, ShouldEqual, boxType)
			So(item.CommonBoxId, ShouldEqual, commonBoxId)
		}

		_, err = mongoCli.DelBox(ctx, channelId, boxId)
		So(err, ShouldBeNil)

		_, err = mongoCli.DelBoxInfos(ctx, channelId)
		So(err, ShouldBeNil)

		_, err = mongoCli.DelBoxInfosByBoxType(ctx, channelId, boxType)
		So(err, ShouldBeNil)

		_, _, _, _, _, err = mongoCli.AddBoxInfo(ctx, channelId, boxId, uid, boxType)
		So(err, ShouldBeNil)
		_, err = mongoCli.DelChannelUserBox(ctx, channelId, uid)
		So(err, ShouldBeNil)
	})
}

func TestMongoDao_DoBroadcastBoxInfos(t *testing.T) {
	Convey("DoBroadcastBoxInfos", t, func() {
		ctx := context.Background()
		_, err := mongoCli.UpdateBroadcastBoxInfo(ctx, channelId, broadcastBoxId, uid, boxType)
		So(err, ShouldBeNil)

		channelBox, err := mongoCli.GetUserChannelBox(ctx, uid, channelId, boxType)
		So(err, ShouldBeNil)
		So(channelBox.ChannelId, ShouldEqual, channelId)
		So(channelBox.BoxId, ShouldEqual, 0)
		So(channelBox.Uid, ShouldEqual, uid)
		So(channelBox.BoxType, ShouldEqual, boxType)
		So(channelBox.BroadcastBoxId, ShouldEqual, broadcastBoxId)
	})
}

func TestMongoDao_DoJoinBoxInfo(t *testing.T) {
	Convey("DoJoinBoxInfo", t, func() {
		ctx := context.Background()
		_, err := mongoCli.JoinBoxSuc(ctx, channelId, boxId, uid, status)
		So(err, ShouldBeNil)

		_, err = mongoCli.JoinBoxSuc(ctx, channelId, boxId, uid, newStatus)
		So(err, ShouldBeNil)

		err = mongoCli.DelJoinBoxInfo(ctx, channelId, uid)
		So(err, ShouldBeNil)

		err = mongoCli.DelJoinBoxInfos(ctx, channelId)
		So(err, ShouldBeNil)
	})
}

func TestMongoDao_DoBoxCnt(t *testing.T) {
	Convey("TestMongoDao_DoBoxCnt", t, func() {
		ctx := context.Background()
		var cnt int = 1
		_, err := mongoCli.ModifyBoxCnt(ctx, channelId, boxId, cnt)
		So(err, ShouldBeNil)

		boxCntMap, err := mongoCli.GetBoxsCntByChannelId(ctx, channelId)
		So(err, ShouldBeNil)
		for _, boxCntInfo := range boxCntMap {
			So(boxCntInfo.Cnt, ShouldEqual, cnt)
			So(boxCntInfo.BoxId, ShouldEqual, boxId)
		}
	})
}

func TestMongoDao_DoMicId(t *testing.T) {
	Convey("DoMicId", t, func() {
		ctx := context.Background()
		var openMicId uint32 = 1
		_, err := mongoCli.UpdateOpenMicId(ctx, channelId, openMicId, uid)
		So(err, ShouldBeNil)

		channelMicInfo, err := mongoCli.GetOpenMicId(ctx, channelId)
		So(err, ShouldBeNil)
		So(channelMicInfo.ChannelId, ShouldEqual, channelId)
		So(channelMicInfo.OpeUid, ShouldEqual, uid)
		So(channelMicInfo.OpenMicBoxId, ShouldEqual, openMicId)
	})
}

func TestMongoDao_DoIncrKey(t *testing.T) {
	Convey("DoIncrKey", t, func() {
		ctx := context.Background()
		var val uint32 = 1
		newValue, err := mongoCli.IncrKey(ctx, "key", val)
		So(err, ShouldBeNil)
		So(newValue, ShouldEqual, 1)
	})
}

func TestMongoDao_DoBoxInfo(t *testing.T) {
	Convey("DoJoinBoxInfo", t, func() {
		ctx := context.Background()
		boxInfo := &BoxInfo{
			ChannelId:  channelId,
			BoxId:      boxId,
			BoxType:    boxType,
			CreatorUid: uid,
		}

		err := mongoCli.CreateBox(ctx, boxInfo)
		So(err, ShouldBeNil)

		_, err = mongoCli.UpdateBox(ctx, boxInfo)
		So(err, ShouldBeNil)

		getBoxInfo, err := mongoCli.GetBox(ctx, channelId, boxId)
		So(err, ShouldBeNil)
		So(getBoxInfo.ChannelId, ShouldEqual, channelId)
		So(getBoxInfo.BoxId, ShouldEqual, boxId)
		So(getBoxInfo.BoxType, ShouldEqual, boxType)
		So(getBoxInfo.CreatorUid, ShouldEqual, uid)

		boxCnt, err := mongoCli.CountChannelBox(ctx, channelId, boxType)
		So(boxCnt, ShouldEqual, 1)

		getBoxInfos, err := mongoCli.GetChannelAllBox(ctx, channelId)
		So(err, ShouldBeNil)
		for _, getBoxInfo := range getBoxInfos {
			So(getBoxInfo.ChannelId, ShouldEqual, channelId)
			So(getBoxInfo.BoxId, ShouldEqual, boxId)
			So(getBoxInfo.BoxType, ShouldEqual, boxType)
			So(getBoxInfo.CreatorUid, ShouldEqual, uid)
		}

		getBoxList, err := mongoCli.GetChannelBoxList(ctx, channelId, boxType)
		So(err, ShouldBeNil)
		for _, getBoxInfo := range getBoxList {
			So(getBoxInfo.ChannelId, ShouldEqual, channelId)
			So(getBoxInfo.BoxId, ShouldEqual, boxId)
			So(getBoxInfo.BoxType, ShouldEqual, boxType)
			So(getBoxInfo.CreatorUid, ShouldEqual, uid)
		}

		boxInfo = &BoxInfo{
			ChannelId:  channelId,
			BoxId:      boxId,
			BoxType:    boxType,
			CreatorUid: uid,
			Name:       "test",
		}
		_, err = mongoCli.UpdateBox(ctx, boxInfo)
		So(err, ShouldBeNil)
		getBoxInfo, err = mongoCli.GetBox(ctx, channelId, boxId)
		So(err, ShouldBeNil)
		So(getBoxInfo.ChannelId, ShouldEqual, channelId)
		So(getBoxInfo.BoxId, ShouldEqual, boxId)
		So(getBoxInfo.BoxType, ShouldEqual, boxType)
		So(getBoxInfo.CreatorUid, ShouldEqual, uid)
		So(getBoxInfo.Name, ShouldEqual, "test")

		_, err = mongoCli.DelBox(ctx, channelId, boxId)
		So(err, ShouldBeNil)

		err = mongoCli.Close()
		So(err, ShouldBeNil)

	})
}
