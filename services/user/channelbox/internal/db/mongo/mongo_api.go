// Code generated by ifacemaker; DO NOT EDIT.

package mongo

import (
	"context"

)

// IMongoDao ...
type IMongoDao interface {
	Close() error
	CreateIndexes() error
	BatchGetBoxInfos(ctx context.Context, channelid, boxType uint32, uids []uint32) (map[uint32]*BoxItem, error)
	GetBoxInfosLimit(ctx context.Context, channelid, boxid, boxType uint32, lastTime int64, getCnt int64) ([]*BoxItem, error)
	GetBoxUserInfos(ctx context.Context, channelid, boxid, boxType uint32, micUids []uint32) ([]*BoxItem, error)
	DelBoxInfo(ctx context.Context, channelid, uid uint32) (err error)
	DelChannelUserBox(ctx context.Context, channelId, uid uint32) (*BoxItem, error)
	DelBoxInfosByBoxType(ctx context.Context, channelid, boxType uint32) (delCnt int64, err error)
	DelBoxInfos(ctx context.Context, channelid uint32) (delCnt int64, err error)
	AddBoxInfo(ctx context.Context, channelid, boxid, uid, boxType uint32) (isSwitch bool, oldBoxId, commonBoxId, broadcastBoxId uint32, boxTimeStamp int64, err error)
	UpdateDelBoxInfo(ctx context.Context, channelid, boxid, uid, boxType uint32) (boxTimeStamp int64, err error)
	UpdateCommonBoxInfo(ctx context.Context, channelid, commonBoxid, uid, boxType uint32) (boxTimeStamp int64, err error)
	UpdateBroadcastBoxInfo(ctx context.Context, channelid, broadcastBoxid, uid, boxType uint32) (boxTimeStamp int64, err error)
	JoinBoxSuc(ctx context.Context, channelid, boxid, uid uint32, status uint32) (isIgnore bool, err error)
	DelJoinBoxInfo(ctx context.Context, channelid, uid uint32) (err error)
	DelJoinBoxInfos(ctx context.Context, channelid uint32) (err error)
	ModifyBoxCnt(ctx context.Context, channelid, boxid uint32, cnt int) (boxCnt uint32, err error)
	GetBoxsCntByChannelId(ctx context.Context, channelid uint32) (map[uint32]*BoxCntItem, error)
	GetBoxIdByUid(ctx context.Context, channelid, uid, boxType uint32) (uint32, error)
	GetChannelBoxIdByUid(ctx context.Context, channelId, uid uint32) (uint32, error)
	GetUserChannelBox(ctx context.Context, uid, channelId, boxType uint32) (*BoxItem, error)
	IncrKey(ctx context.Context, key string, incr uint32) (uint32, error)
	CreateBox(ctx context.Context, box *BoxInfo) error
	UpdateBox(ctx context.Context, box *BoxInfo) (*BoxInfo, error)
	DelBox(ctx context.Context, channelId, boxId uint32) (*BoxInfo, error)
	GetBox(ctx context.Context, channelId, boxId uint32) (*BoxInfo, error)
	CountChannelBox(ctx context.Context, channelId, boxType uint32) (uint32, error)
	GetChannelBoxList(ctx context.Context, channelId, boxType uint32) ([]*BoxInfo, error)
	GetChannelAllBox(ctx context.Context, channelId uint32) ([]*BoxInfo, error)
	UpdateOpenMicId(ctx context.Context, channelId, openMicId, opeUid uint32) (int64, error)
	GetOpenMicId(ctx context.Context, channelId uint32) (*ChannelMic, error)
	IsUserInBox(ctx context.Context, channelId, uid uint32) (bool, error)
	GetAllUsersInBox(ctx context.Context, channelId uint32) ([]uint32, error)
}
