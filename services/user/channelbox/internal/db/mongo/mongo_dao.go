package mongo

import (
	"context"
	"encoding/json"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"time"
)

const (
	BoxTypeRolePlay = 1
	BoxTypeMelee    = 2
)

//go:generate ifacemaker -f *.go -s MongoDao -p mongo -o mongo_api.go -i IMongoDao
//go:generate mockgen -destination=../../mocks/mock_mongo.go -package=mocks golang.52tt.com/services/user/channelbox/internal/db/mongo IMongoDao
type MongoDao struct {
	channelBoxPool *mongo.Collection
	joinBoxPool    *mongo.Collection
	boxCntPool     *mongo.Collection
	incrKeyPool    *mongo.Collection
	boxPool        *mongo.Collection
	channelMicPool *mongo.Collection
	mongoCli       *mongo.Client
}

func NewMongoDao(ctx context.Context, cfg *config.MongoConfig) (*MongoDao, error) {
	//client := &mongo.Client{}
	client, err := mongo.NewClient(cfg.OptionsForPrimaryPreferred())
	if err != nil {
		log.ErrorWithCtx(ctx, "NewMongoDao NewClient err: (%v)", err)
		return nil, err
	}

	if err = client.Connect(ctx); err != nil {
		log.ErrorWithCtx(ctx, "newServer Connect err: (%v)", err)
		return nil, err
	}

	if err = client.Ping(ctx, readpref.Primary()); err != nil {
		log.ErrorWithCtx(ctx, "newServer Ping err: (%v)", err)
		return nil, err
	}

	channelBoxPool := client.Database(cfg.Database).Collection("channelbox")
	channelBoxCntPool := client.Database(cfg.Database).Collection("boxcnt")
	joinBoxPool := client.Database(cfg.Database).Collection("joinbox")
	incrKeyPool := client.Database(cfg.Database).Collection("incr_key")
	boxPool := client.Database(cfg.Database).Collection("box_info")
	channelMicPool := client.Database(cfg.Database).Collection("channel_mic")

	return &MongoDao{
		channelBoxPool: channelBoxPool,
		joinBoxPool:    joinBoxPool,
		boxCntPool:     channelBoxCntPool,
		incrKeyPool:    incrKeyPool,
		boxPool:        boxPool,
		channelMicPool: channelMicPool,
		mongoCli:       client,
	}, nil
}

func (m *MongoDao) Close() error {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	err := m.mongoCli.Disconnect(ctx)
	cancel()
	if err != nil {
		log.Errorf("mongoCli.Disconnect err:%v", err)
		return err
	}
	return nil
}

func (m *MongoDao) CreateIndexes() error {
	var err error
	opts := options.CreateIndexes().SetMaxTime(10 * time.Second)
	_, err = m.channelBoxPool.Indexes().CreateMany(
		context.Background(),
		[]mongo.IndexModel{
			{
				Keys:    bson.D{primitive.E{Key: "channelid", Value: 1}, primitive.E{Key: "uid", Value: 1}},
				Options: options.Index().SetUnique(true),
			}, {
				Keys:    bson.D{primitive.E{Key: "channelid", Value: 1}, primitive.E{Key: "boxid", Value: 1}, primitive.E{Key: "uid", Value: 1}},
				Options: options.Index().SetUnique(true),
			}, {
				Keys:    bson.D{primitive.E{Key: "channelid", Value: 1}, primitive.E{Key: "boxid", Value: 1}, primitive.E{Key: "box_time", Value: -1}},
				Options: options.Index().SetUnique(false),
			},
		},
		opts,
	)
	if err != nil {
		log.Errorf("createIndexs channelBoxPool err:%v", err)
	}

	_, err = m.joinBoxPool.Indexes().CreateMany(
		context.Background(),
		[]mongo.IndexModel{
			{
				Keys:    bson.D{primitive.E{Key: "uid", Value: 1}, primitive.E{Key: "channelid", Value: 1}, primitive.E{Key: "boxid", Value: 1}},
				Options: options.Index().SetUnique(true),
			},
		},
		opts,
	)
	if err != nil {
		log.Errorf("createIndexs joinBoxPool err:%v", err)
	}

	_, err = m.boxPool.Indexes().CreateMany(
		context.Background(),
		[]mongo.IndexModel{
			{
				Keys:    bson.D{primitive.E{Key: "channel_id", Value: 1}, primitive.E{Key: "box_id", Value: 1}},
				Options: options.Index().SetUnique(true),
			},
		})
	if err != nil {
		log.Errorf("createIndexes boxInfoPool err: %v", err)
	}

	_, err = m.channelMicPool.Indexes().CreateMany(
		context.Background(),
		[]mongo.IndexModel{
			{
				Keys:    bson.D{primitive.E{Key: "channel_id", Value: 1}},
				Options: options.Index().SetUnique(true),
			},
		})
	if err != nil {
		log.Errorf("createIndexes boxInfoPool err: %v", err)
	}

	return nil
}

type BoxItem struct {
	Id             string    `bson:"_id"`
	ChannelId      uint32    `bson:"channelid,omitempty"`
	BoxId          uint32    `bson:"boxid,omitempty"`
	Uid            uint32    `bson:"uid,omitempty"`
	BoxTime        int64     `bson:"box_time,omitempty"`
	UpdateTime     time.Time `bson:"update_time"`
	BoxType        uint32    `bson:"box_type"` // 0， 包厢  1，团战子频道
	CommonBoxId    uint32    `bson:"common_boxid,omitempty"`
	BroadcastBoxId uint32    `bson:"broadcast_boxid,omitempty"`
}

func (m *MongoDao) BatchGetBoxInfos(ctx context.Context, channelid, boxType uint32, uids []uint32) (map[uint32]*BoxItem, error) {
	cursor, err := m.channelBoxPool.Find(ctx, bson.M{"channelid": channelid, "uid": bson.M{"$in": uids}})
	if err != nil {
		log.Errorf("BatchGetBoxInfos Find err:%v", err)
		return nil, err
	}
	boxMap := make(map[uint32]*BoxItem)
	for cursor.Next(ctx) {
		info := &BoxItem{}
		if err := cursor.Decode(info); err != nil {
			log.ErrorWithCtx(ctx, "BatchGetBoxInfos cursor.Decode err:%v", err)
			return boxMap, err
		}
		boxMap[info.Uid] = info
	}
	if err := cursor.Err(); err != nil {
		log.ErrorWithCtx(ctx, "BatchGetBoxInfos cursor err:%v", err)
		return boxMap, err
	}

	if err := cursor.Close(ctx); err != nil {
		log.ErrorWithCtx(ctx, "BatchGetBoxInfos cursor.Close err:%v", err)
	}
	return boxMap, nil
}

func (m *MongoDao) GetBoxInfosLimit(ctx context.Context, channelid, boxid, boxType uint32, lastTime int64, getCnt int64) ([]*BoxItem, error) {
	var filter = bson.M{"channelid": channelid, "boxid": boxid, "box_type": boxType}
	if lastTime != 0 {
		filter["box_time"] = bson.M{"$lt": lastTime}
	}
	cursor, err := m.channelBoxPool.Find(ctx, filter, options.Find().SetLimit(getCnt).SetSort(bson.M{"box_time": -1}))
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetBoxInfos Find err:%v", err)
		return nil, err
	}
	userItems := make([]*BoxItem, 0)
	for cursor.Next(ctx) {
		info := &BoxItem{}
		if err := cursor.Decode(info); err != nil {
			log.ErrorWithCtx(ctx, "BatchGetBoxInfos cursor.Decode err:%v", err)
			return userItems, err
		}
		userItems = append(userItems, info)
	}
	if err := cursor.Err(); err != nil {
		log.ErrorWithCtx(ctx, "BatchGetBoxInfos cursor err:%v", err)
		return userItems, err
	}

	if err := cursor.Close(ctx); err != nil {
		log.ErrorWithCtx(ctx, "BatchGetBoxInfos cursor.Close err:%v", err)
	}
	return userItems, nil
}

func (m *MongoDao) GetBoxUserInfos(ctx context.Context, channelid, boxid, boxType uint32, micUids []uint32) ([]*BoxItem, error) {
	cursor, err := m.channelBoxPool.Find(ctx, bson.M{"channelid": channelid, "boxid": boxid, "box_type": boxType, "uid": bson.M{"$in": micUids}})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBoxUserInfos Find err:%v", err)
		return nil, err
	}
	userItems := make([]*BoxItem, 0)
	for cursor.Next(ctx) {
		info := &BoxItem{}
		if err := cursor.Decode(info); err != nil {
			log.ErrorWithCtx(ctx, "GetBoxUserInfos cursor.Decode err:%v", err)
			return userItems, err
		}
		userItems = append(userItems, info)
	}
	if err := cursor.Err(); err != nil {
		log.ErrorWithCtx(ctx, "GetBoxUserInfos cursor err:%v", err)
		return userItems, err
	}

	if err := cursor.Close(ctx); err != nil {
		log.ErrorWithCtx(ctx, "GetBoxUserInfos cursor.Close err:%v", err)
	}
	return userItems, nil
}

func (m *MongoDao) DelBoxInfo(ctx context.Context, channelid, uid uint32) (err error) {
	_, err = m.channelBoxPool.DeleteMany(ctx, bson.M{"channelid": channelid, "uid": uid})
	if err != nil && err != mongo.ErrNoDocuments {
		log.ErrorWithCtx(ctx, "DelBoxInfo DeleteMany err:%v, uids:%, channelid:%d", err, uid, channelid)
		return err
	}
	return nil
}

func (m *MongoDao) DelChannelUserBox(ctx context.Context, channelId, uid uint32) (*BoxItem, error) {
	res := m.channelBoxPool.FindOneAndDelete(ctx, bson.M{"channelid": channelId, "uid": uid})
	if err := res.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}

		log.ErrorWithCtx(ctx, "DelChannelUserBox FindOneAndDelete channelId(%d) uid(%d) err: %v", channelId, uid, err)
		return nil, err
	}

	var item BoxItem
	err := res.Decode(&item)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelUserBox Decode channelId(%d) uid(%d) err: %v", channelId, uid, err)
		return nil, err
	}

	return &item, nil
}

func (m *MongoDao) DelBoxInfosByBoxType(ctx context.Context, channelid, boxType uint32) (delCnt int64, err error) {
	res, err := m.channelBoxPool.DeleteMany(ctx, bson.M{"channelid": channelid, "box_type": boxType})
	if err != nil && err != mongo.ErrNoDocuments {
		log.ErrorWithCtx(ctx, "DelBoxInfo DeleteMany err:%v, channelid:%d", err, channelid)
		return 0, err
	}
	return res.DeletedCount, nil
}

func (m *MongoDao) DelBoxInfos(ctx context.Context, channelid uint32) (delCnt int64, err error) {
	res, err := m.channelBoxPool.DeleteMany(ctx, bson.M{"channelid": channelid})
	if err != nil && err != mongo.ErrNoDocuments {
		log.ErrorWithCtx(ctx, "DelBoxInfo DeleteMany err:%v, channelid:%d", err, channelid)
		return 0, err
	}
	return res.DeletedCount, nil
}

func (m *MongoDao) AddBoxInfo(ctx context.Context, channelid, boxid, uid, boxType uint32) (isSwitch bool, oldBoxId, commonBoxId,
	broadcastBoxId uint32, boxTimeStamp int64, err error) {
	nowTime := time.Now()
	opts := nowTime.UnixMilli()
	result := m.channelBoxPool.FindOneAndUpdate(ctx, bson.M{"channelid": channelid, "uid": uid}, bson.M{"$set": bson.M{"boxid": boxid,
		"box_time": opts, "update_time": nowTime, "common_boxid": 0, "broadcast_boxid": 0, "box_type": boxType}}, options.FindOneAndUpdate().SetUpsert(true))
	if err := result.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return false, 0, 0, 0, opts, nil
		}
		return false, 0, 0, 0, 0, err
	}
	var oldBoxInfo BoxItem
	err = result.Decode(&oldBoxInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddBoxInfo Decode err: %v, channelid:%d, uid:%d, boxid:%d, boxType:%d", err, channelid, uid, boxid, boxType)
		return false, 0, 0, 0, 0, err
	}

	if oldBoxInfo.BoxType != boxType {
		log.ErrorWithCtx(ctx, "AddBoxInfo channelId(%d) boxId(%d) uid(%d) boxType(%d) != %d", channelid, uid, boxid, boxType, oldBoxInfo.BoxType)
	}

	if oldBoxInfo.BoxId != 0 && boxid != oldBoxInfo.BoxId {
		return true, oldBoxInfo.BoxId, 0, 0, opts, nil
	}
	return false, oldBoxInfo.BoxId, oldBoxInfo.CommonBoxId, oldBoxInfo.BroadcastBoxId, opts, nil
}

func (m *MongoDao) UpdateDelBoxInfo(ctx context.Context, channelid, boxid, uid, boxType uint32) (boxTimeStamp int64, err error) {
	nowTime := time.Now()
	opts := nowTime.UnixMilli()
	result := m.channelBoxPool.FindOneAndUpdate(ctx, bson.M{"channelid": channelid, "uid": uid}, bson.M{"$set": bson.M{"boxid": 0,
		"box_time": opts, "update_time": nowTime, "common_boxid": 0, "broadcast_boxid": 0}}, options.FindOneAndUpdate().SetUpsert(false))
	if err := result.Err(); err != nil {
		log.ErrorWithCtx(ctx, "UpdateDelBoxInfo channelBoxPool.FindOneAndUpdate err:%v, channelid:%d, boxid:%d, uid:%d", err, channelid, boxid, uid)
		if err == mongo.ErrNoDocuments {
			return opts, nil
		}
		return 0, err
	}
	return opts, nil
}

func (m *MongoDao) UpdateCommonBoxInfo(ctx context.Context, channelid, commonBoxid, uid, boxType uint32) (boxTimeStamp int64, err error) {
	nowTime := time.Now()
	opts := nowTime.UnixMilli()
	result := m.channelBoxPool.FindOneAndUpdate(ctx, bson.M{"channelid": channelid, "uid": uid, "box_type": boxType, "boxid": bson.M{"$gt": 0}},
		bson.M{"$set": bson.M{"box_time": opts, "update_time": nowTime, "common_boxid": commonBoxid, "box_type": boxType}},
		options.FindOneAndUpdate().SetUpsert(false))
	if err := result.Err(); err != nil {
		log.ErrorWithCtx(ctx, "UpdateCommonBoxInfo channelBoxPool.FindOneAndUpdate err:%v, channelid:%d, boxid:%d, uid:%d", err, channelid, commonBoxid, uid)
		if err == mongo.ErrNoDocuments {
			return opts, nil
		}
		return 0, err
	}
	return opts, nil
}

func (m *MongoDao) UpdateBroadcastBoxInfo(ctx context.Context, channelid, broadcastBoxid, uid, boxType uint32) (boxTimeStamp int64, err error) {
	nowTime := time.Now()
	opts := nowTime.UnixMilli()
	result := m.channelBoxPool.FindOneAndUpdate(ctx, bson.M{"channelid": channelid, "uid": uid, "box_type": boxType, "boxid": bson.M{"$eq": 0}}, bson.M{"$set": bson.M{"boxid": 0,
		"box_time": opts, "update_time": nowTime, "broadcast_boxid": broadcastBoxid, "box_type": boxType}}, options.FindOneAndUpdate().SetUpsert(true))
	if err := result.Err(); err != nil {
		log.ErrorWithCtx(ctx, "UpdateBroadcastBoxInfo channelBoxPool.FindOneAndUpdate err:%v, channelid:%d, boxid:%d, uid:%d", err, channelid, broadcastBoxid, uid)
		if err == mongo.ErrNoDocuments {
			return opts, nil
		}
		return 0, err
	}
	return opts, nil
}

type JoinBoxItem struct {
	Id         string    `bson:"_id"`
	ChannelId  uint32    `bson:"channelid,omitempty"`
	BoxId      uint32    `bson:"boxid,omitempty"`
	Uid        uint32    `bson:"uid,omitempty"`
	Status     int64     `bson:"status,omitempty"`
	JoinTime   int64     `bson:"join_time"`
	UpdateTime time.Time `bson:"update_time"`
}

func (m *MongoDao) JoinBoxSuc(ctx context.Context, channelid, boxid, uid uint32, status uint32) (isIgnore bool, err error) {
	nowTime := time.Now()
	result := m.joinBoxPool.FindOneAndUpdate(ctx, bson.M{"channelid": channelid, "boxid": boxid, "uid": uid}, bson.M{"$set": bson.M{
		"status": status, "join_time": nowTime.Unix(), "update_time": nowTime}}, options.FindOneAndUpdate().SetUpsert(true))
	if err := result.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return false, nil
		} else {
			log.ErrorWithCtx(ctx, "FindOneAndUpdate err:%v, channelid:%d, boxid:%d, uid:%d", err, channelid, boxid, uid)
			return false, err
		}

	}
	item := &JoinBoxItem{}
	err = result.Decode(item)
	if err != nil {
		log.ErrorWithCtx(ctx, "FindOneAndUpdate Decode err:%v, channelid:%d, boxid:%d, uid:%d", err, channelid, boxid, uid)
		return false, err
	}
	if dua := nowTime.Unix() - item.JoinTime; dua <= 5 { //用户在同个审批中小于5秒说明是推送下发慢了，忽视这个请求
		return true, nil
	}
	return false, nil
}

func (m *MongoDao) DelJoinBoxInfo(ctx context.Context, channelid, uid uint32) (err error) {
	_, err = m.joinBoxPool.DeleteMany(ctx, bson.M{"channelid": channelid, "uid": uid})
	if err != nil && err != mongo.ErrNoDocuments {
		log.ErrorWithCtx(ctx, "joinBoxPool DeleteMany err:%v, uids:%, channelid:%d", err, uid, channelid)
		return err
	}
	return nil
}

func (m *MongoDao) DelJoinBoxInfos(ctx context.Context, channelid uint32) (err error) {
	_, err = m.joinBoxPool.DeleteMany(ctx, bson.M{"channelid": channelid})
	if err != nil && err != mongo.ErrNoDocuments {
		log.ErrorWithCtx(ctx, "joinBoxPool DeleteMany err:%v, channelid:%d", err, channelid)
		return err
	}
	return nil
}

type BoxCntItem struct {
	Id        string `bson:"_id"`
	ChannelId uint32 `bson:"channelid,omitempty"`
	BoxId     uint32 `bson:"boxid,omitempty"`
	Cnt       uint32 `bson:"cnt,omitempty"`
}

func (m *MongoDao) ModifyBoxCnt(ctx context.Context, channelid, boxid uint32, cnt int) (boxCnt uint32, err error) {
	result := m.boxCntPool.FindOneAndUpdate(ctx, bson.M{"channelid": channelid, "boxid": boxid}, bson.M{"$inc": bson.M{"cnt": cnt}},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After))
	if err := result.Err(); err != nil && err != mongo.ErrNoDocuments {
		return 0, err
	}
	item := &BoxCntItem{}
	err = result.Decode(item)
	if err != nil {
		return 0, err
	}
	return item.Cnt, nil
}

func (m *MongoDao) GetBoxsCntByChannelId(ctx context.Context, channelid uint32) (map[uint32]*BoxCntItem, error) {
	cursor, err := m.boxCntPool.Find(ctx, bson.M{"channelid": channelid})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBoxsCnt Find err:%v", err)
		return nil, err
	}
	poolInfos := make(map[uint32]*BoxCntItem)
	for cursor.Next(ctx) {
		info := &BoxCntItem{}
		if err := cursor.Decode(info); err != nil {
			log.ErrorWithCtx(ctx, "GetBoxsCnt cursor.Decode err:%v", err)
			return poolInfos, err
		}
		poolInfos[info.BoxId] = info
	}
	if err := cursor.Err(); err != nil {
		log.ErrorWithCtx(ctx, "GetBoxsCnt cursor err:%v", err)
		return poolInfos, err
	}

	if err := cursor.Close(ctx); err != nil {
		log.ErrorWithCtx(ctx, "GetBoxsCnt cursor.Close err:%v", err)
	}
	return poolInfos, nil
}

/*func (m *MongoDao) GetBoxsCnt(ctx context.Context, channelid, uid uint32, boxids []uint32) (map[uint32]*BoxCntItem, error) {
	cursor, err := m.boxCntPool.Find(ctx, bson.M{"channelid": channelid, "uid": uid, "boxid": bson.M{"$in": boxids}})
	if err != nil {
		log.Errorf("GetBoxsCnt Find err:%v", err)
		return nil, err
	}
	poolInfos := make(map[uint32]*BoxCntItem)
	for cursor.Next(ctx) {
		info := &BoxCntItem{}
		if err := cursor.Decode(info); err != nil {
			log.Errorf("GetBoxsCnt cursor.Decode err:%v", err)
			return poolInfos, err
		}
		poolInfos[info.BoxId] = info
	}
	if err := cursor.Err(); err != nil {
		log.Errorf("GetBoxsCnt cursor err:%v", err)
		return poolInfos, err
	}

	if err := cursor.Close(ctx); err != nil {
		log.Errorf("GetBoxsCnt cursor.Close err:%v", err)
	}
	return poolInfos, nil
}

func (m *MongoDao) GetBoxCnt(ctx context.Context, channelid, boxid, uid uint32) (*BoxCntItem, error) {
	result := m.boxCntPool.FindOne(ctx, bson.M{"channelid": channelid, "uid": uid, "boxid": boxid})
	if result.Err() != nil {
		if result.Err() == mongo.ErrNoDocuments {
			//log.Warnf(" to getActiveRelation uid(%d) relationType(%v) but no documents in result ", uid, rt)
			return nil, nil
		}

		log.Errorf("GetBoxCnt err: %v", result.Err())
		return nil, result.Err()
	}

	item := &BoxCntItem{}
	err := result.Decode(item)
	if err != nil {
		return nil, err
	}
	return item, nil
}*/

func (m *MongoDao) GetBoxIdByUid(ctx context.Context, channelid, uid, boxType uint32) (uint32, error) {
	result := m.channelBoxPool.FindOne(ctx, bson.M{"channelid": channelid, "uid": uid, "box_type": boxType})
	if result.Err() != nil {
		if result.Err() == mongo.ErrNoDocuments {
			return 0, nil
		}

		log.Errorf("GetBoxCnt err: %v", result.Err())
		return 0, result.Err()
	}

	item := &BoxItem{}
	err := result.Decode(item)
	if err != nil {
		return 0, err
	}
	return item.BoxId, nil
}

func (m *MongoDao) GetChannelBoxIdByUid(ctx context.Context, channelId, uid uint32) (uint32, error) {
	result := m.channelBoxPool.FindOne(ctx, bson.M{"channelid": channelId, "uid": uid})
	if result.Err() != nil {
		if result.Err() == mongo.ErrNoDocuments {
			return 0, nil
		}

		log.Errorf("GetBoxCnt err: %v", result.Err())
		return 0, result.Err()
	}

	item := &BoxItem{}
	err := result.Decode(item)
	if err != nil {
		return 0, err
	}

	return item.BoxId, nil
}

func (m *MongoDao) GetUserChannelBox(ctx context.Context, uid, channelId, boxType uint32) (*BoxItem, error) {
	result := m.channelBoxPool.FindOne(ctx, bson.M{"channelid": channelId, "uid": uid, "box_type": boxType})
	if err := result.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}

		log.ErrorWithCtx(ctx, "GetUserChannelBox channelBoxPool.FindOne uid(%d) channelId(%d) boxType(%d) err: %v", uid, channelId, boxType, err)
		return nil, err
	}

	var item BoxItem
	err := result.Decode(&item)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserChannelBox result.Decode uid(%d) channelId(%d) boxType(%d) err: %v", uid, channelId, boxType, err)
		return nil, err
	}

	return &item, nil
}

type IncrKey struct {
	Key   string `bson:"_id"`
	Index uint32 `bson:"index"`
}

func (m *MongoDao) IncrKey(ctx context.Context, key string, incr uint32) (uint32, error) {
	res := m.incrKeyPool.FindOneAndUpdate(ctx, bson.M{"_id": key}, bson.M{"$inc": bson.M{"index": incr}},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After))
	if err := res.Err(); err != nil {
		log.ErrorWithCtx(ctx, "MongoDao.IncrKey incrKeyPool.FindOneAndUpdate key(%s) incr(%d) err: %v", key, incr, err)
		return 0, err
	}

	var ik IncrKey
	err := res.Decode(&ik)
	if err != nil {
		log.ErrorWithCtx(ctx, "MongoDao.IncrKey res.Decode key(%s) incr(%d) err: %v", key, incr, err)
		return 0, err
	}

	return ik.Index, nil
}

const (
	BoxNameCreate    = 0
	BoxNameAuditing  = 1
	BoxNameAuditSuc  = 2
	BoxNameAuditFail = 3
)

type BoxInfo struct {
	ChannelId uint32 `bson:"channel_id"`
	BoxId     uint32 `bson:"box_id"`

	BoxType    uint32 `bson:"box_type"` // 包厢类型 0:角色扮演 1:团战
	CreatorUid uint32 `bson:"creator_uid"`

	Name  string `bson:"name"`
	State uint32 `bson:"state"` // 0:刚创建 1:审核中 2:审核通过 3:审核不通过

	ReviewName  string `bson:"review_name"`  // 审核中的包厢名称
	MicCapacity uint32 `bson:"mic_capacity"` // 包厢麦位数限制

	PublicMicList []uint32 `bson:"public_mic_list"` // 公共麦位ID列表
	NormalMicList []uint32 `bson:"normal_mic_list"` // 普通麦位ID列表
}

func (boxInfo *BoxInfo) String() string {
	data, _ := json.Marshal(boxInfo)
	return string(data)
}

func (m *MongoDao) CreateBox(ctx context.Context, box *BoxInfo) error {
	res, err := m.boxPool.InsertOne(ctx, box)
	if err != nil {
		log.ErrorWithCtx(ctx, "MongoDao.CreateBoxInfo box(%v) err: %v", box, err)
		return err
	}

	log.DebugWithCtx(ctx, "MongoDao.CreateBoxInfo result: %v", res)
	return nil
}

func (m *MongoDao) UpdateBox(ctx context.Context, box *BoxInfo) (*BoxInfo, error) {
	updates := bson.M{"review_name": box.ReviewName}
	if box.Name != "" {
		updates["name"] = box.Name
	}
	if box.State > 0 {
		updates["state"] = box.State
	}
	if box.MicCapacity > 0 {
		updates["mic_capacity"] = box.MicCapacity
		updates["public_mic_list"] = box.PublicMicList
		updates["normal_mic_list"] = box.NormalMicList
	}

	res := m.boxPool.FindOneAndUpdate(ctx, bson.M{"channel_id": box.ChannelId, "box_id": box.BoxId}, bson.M{"$set": updates},
		options.FindOneAndUpdate().SetReturnDocument(options.Before))
	if err := res.Err(); err != nil {
		log.ErrorWithCtx(ctx, "Mongo.UpdateBox boxPool.FindOneAndUpdate box(%v) err: %v", box, err)
		return nil, err
	}

	var bi BoxInfo
	err := res.Decode(&bi)
	if err != nil {
		log.ErrorWithCtx(ctx, "Mongo.UpdateBox res.Decode box(%v) err: %v", box, err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "MongoDao.UpdateBoxInfo result: %v", res)
	return &bi, nil
}

func (m *MongoDao) DelBox(ctx context.Context, channelId, boxId uint32) (*BoxInfo, error) {
	res := m.boxPool.FindOneAndDelete(ctx, bson.M{"channel_id": channelId, "box_id": boxId})
	if err := res.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}

		log.ErrorWithCtx(ctx, "MongoDao.DelBox boxPool.FindOneAndDelete channelId(%d) boxId(%d) err: %v", channelId, boxId, err)
		return nil, err
	}

	var bi BoxInfo
	err := res.Decode(&bi)
	if err != nil {
		log.ErrorWithCtx(ctx, "MongoDao.DelBox res.Decode channelId(%d) boxId(%d) err: %v", channelId, boxId, err)
		return nil, err
	}

	return &bi, nil
}

func (m *MongoDao) GetBox(ctx context.Context, channelId, boxId uint32) (*BoxInfo, error) {
	res := m.boxPool.FindOne(ctx, bson.M{"channel_id": channelId, "box_id": boxId})
	if err := res.Err(); err != nil {
		log.ErrorWithCtx(ctx, "MongoDao.GetBoxInfo boxInfoPool.FindOne channelId(%d) boxId(%d) err: %v", channelId, boxId, err)
		return nil, err
	}

	var bi BoxInfo
	err := res.Decode(&bi)
	if err != nil {
		log.ErrorWithCtx(ctx, "MongoDao.GetBoxInfo res.Decode channelId(%d) boxId(%d) err: %v", channelId, boxId, err)
	}

	log.DebugWithCtx(ctx, "MongoDao.GetBoxInfo BoxInfo: %v", bi)
	return &bi, nil
}

func (m *MongoDao) CountChannelBox(ctx context.Context, channelId, boxType uint32) (uint32, error) {
	n, err := m.boxPool.CountDocuments(ctx, bson.M{"channel_id": channelId, "box_type": boxType})
	if err != nil {
		log.ErrorWithCtx(ctx, "Mongo.CountChannelBox CountDocuments channelId(%d) boxType(%d) err: %v", channelId, boxType)
		return 0, err
	}

	return uint32(n), nil
}

func (m *MongoDao) GetChannelBoxList(ctx context.Context, channelId, boxType uint32) ([]*BoxInfo, error) {
	cursor, err := m.boxPool.Find(ctx, bson.M{"channel_id": channelId, "box_type": boxType})
	if err != nil {
		log.ErrorWithCtx(ctx, "MongoDao.GetChannelBoxList boxInfoPool.Find channelId(%d) err: %v", channelId, err)
		return nil, err
	}

	var list []*BoxInfo
	err = cursor.All(ctx, &list)
	if err != nil {
		log.ErrorWithCtx(ctx, "MongoDao.GetChannelBoxList cursor.All channelId(%d) err: %v", channelId, err)
		return nil, err
	}

	return list, nil
}

func (m *MongoDao) GetChannelAllBox(ctx context.Context, channelId uint32) ([]*BoxInfo, error) {
	cursor, err := m.boxPool.Find(ctx, bson.M{"channel_id": channelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "MongoDao.GetChannelAllBox boxInfoPool.Find channelId(%d) err: %v", channelId, err)
		return nil, err
	}

	var list []*BoxInfo
	err = cursor.All(ctx, &list)
	if err != nil {
		log.ErrorWithCtx(ctx, "MongoDao.GetChannelAllBox cursor.All channelId(%d) err: %v", channelId, err)
		return nil, err
	}

	return list, nil
}

type ChannelMic struct {
	ChannelId    uint32    `bson:"channel_id"`
	OpenMicBoxId uint32    `bson:"open_mic_box_id"` //等同于某个子频道id
	OpeUid       uint32    `bson:"ope_uid"`
	UpdateTime   time.Time `bson:"update_time"`
}

func (m *MongoDao) UpdateOpenMicId(ctx context.Context, channelId, openMicId, opeUid uint32) (int64, error) {
	opts := time.Now().UnixMilli()
	updates := bson.M{}
	updates["open_mic_box_id"] = openMicId
	updates["ope_uid"] = opeUid
	updates["update_time"] = opts

	_, err := m.channelMicPool.UpdateOne(ctx, bson.M{"channel_id": channelId}, bson.M{"$set": updates}, options.Update().SetUpsert(true))
	if err != nil {
		log.ErrorWithCtx(ctx, "channelMicPool.UpdateOne err: %v", err)
		return 0, err
	}

	return opts, nil
}

func (m *MongoDao) GetOpenMicId(ctx context.Context, channelId uint32) (*ChannelMic, error) {
	res := m.channelMicPool.FindOne(ctx, bson.M{"channel_id": channelId})
	if err := res.Err(); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetOpenMicId channelMicPool.FindOne err: %v, channelId:%d", err, channelId)
		return nil, err
	}

	var cm ChannelMic
	err := res.Decode(&cm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOpenMicId decode err: %v, channelId:%d", err, channelId)
		return nil, err
	}

	return &cm, nil
}

func (m *MongoDao) IsUserInBox(ctx context.Context, channelId, uid uint32) (bool, error) {
	// 检查用户是否在包厢中
	res := m.channelBoxPool.FindOne(ctx, bson.M{"channelid": channelId, "uid": uid, "boxid": bson.M{"$gt": 0}})
	if err := res.Err(); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, nil // 用户不在包厢中
		}
		log.ErrorWithCtx(ctx, "IsUserInBox channelBoxPool.FindOne err: %v, channelId:%d, uid:%d", err, channelId, uid)
		return false, err // 查询出错
	}
	return true, nil // 用户在包厢中
}

func (m *MongoDao) GetAllUsersInBox(ctx context.Context, channelId uint32) ([]uint32, error) {
	cursor, err := m.channelBoxPool.Find(ctx, bson.M{"channelid": channelId, "boxid": bson.M{"$gt": 0}}, options.Find().SetProjection(bson.M{"uid": 1,"_id": 0}))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllUsersInBox channelBoxPool.Find err: %v, channelId:%d", err, channelId)
		return nil, err
	}
	type UserInBox struct {
		Uid uint32 `bson:"uid"`
	}
	var users []*UserInBox
	err = cursor.All(ctx, &users)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllUsersInBox cursor.All err: %v, channelId:%d", err, channelId)
		return nil, err
	}
	userIds := make([]uint32, 0, len(users))
	for _, user := range users {
		userIds = append(userIds, user.Uid)
	}
	return userIds, nil
}
