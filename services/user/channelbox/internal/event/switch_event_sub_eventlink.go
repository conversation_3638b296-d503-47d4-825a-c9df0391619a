package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/pkg/log"
	eventPB "golang.52tt.com/protocol/services/topic_channel/event"
	"golang.52tt.com/services/user/channelbox/internal/model"
	"time"
)

type SwitchEventLinkSubscriber struct {
	channelBoxInst *model.ChannelBox
	topic          string
}

func NewSwitchEventLinkSubscriber(clientId, groupId string, topics, brokers []string, channelBoxInst *model.ChannelBox) (*SwitchEventLinkSubscriber, error) {
	sub := &SwitchEventLinkSubscriber{
		channelBoxInst: channelBoxInst,
		topic:          topics[0],
	}

	conf := kafka.DefaultConfig()
	conf.ClientID = clientId
	conf.Consumer.Offsets.Initial = kafka.OffsetNewest
	conf.Consumer.Return.Errors = true

	kafkaSub, err := kafka.NewSubscriber(brokers, conf)
	if err != nil {
		log.Errorf("create kafka-subscriber err: %v", err)
		return nil, err
	}

	err = kafkaSub.SubscribeContext(groupId, topics, subscriber.ProcessorContextFunc(sub.handlerEvent))
	if err != nil {
		log.Errorf("Subscribe err: %v", err)
		return nil, err
	}

	return sub, nil
}

func (s *SwitchEventLinkSubscriber) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	switch msg.Topic {
	case s.topic:
		return s.handlerSwitchChannelEventEvent(ctx, msg)
	default:
	}
	return nil, false
}

func (s *SwitchEventLinkSubscriber) handlerSwitchChannelEventEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	switchTabEvent := &eventPB.SwitchChannelTabEvent{}

	err := proto.Unmarshal(msg.Value, switchTabEvent)
	if err != nil {
		log.Errorf("proto.Unmarshal err:%v", err)
		return err, false
	}

	log.Debugf("handlerSwitchChannelEventEvent event:%s, partition:%d, offset:%d", switchTabEvent.String(), msg.Partition, msg.Offset)

	err = s.channelBoxInst.ClearChannel(ctx, switchTabEvent.GetChannelId())
	if err != nil {
		return err, true
	}

	//s.eventChannel <- switchTabEvent

	return nil, false
}
