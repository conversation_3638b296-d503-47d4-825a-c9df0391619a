package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/user/channelbox/internal/conf"

	pb "golang.52tt.com/protocol/services/channelbox"

	interServer "golang.52tt.com/services/user/channelbox/internal/server"

	// use server startup
	startup "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server" // 兼容tyr公共库
)

func main() {
	var (
		server *interServer.ChannelBoxServer
		cfg    = &conf.StartConfig{}
		err    error
	)

	// config file support yaml & json, default channelbox.json/yaml
	if err := startup.New("channelbox", cfg).
		AddGrpcServer(grpc.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				kafka.InitEventLinkSubWithGrpcSvr(s)
				if server, err = interServer.NewChannelBoxServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterChannelBoxServer(s, server)

				// grpcurl -plaintext -d '{"value":"hello"}' 127.0.0.1:80 demo.echo.EchoService.Echo
				// grpcurl -plaintext 127.0.0.1:80 grpc.health.v1.Health.Check
				echo.RegisterEchoServiceServer(s, server)
				return nil
			}),
		).WithCloseFunc(func(ctx context.Context) {
		// do something when server terminating
		server.ShutDown()
	}).Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
