package event

import (
	"context"
	context_info "golang.52tt.com/pkg/context-info"
	"os"
	"os/signal"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"

	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/monitor/web"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkachannalevent"
	"golang.52tt.com/services/user/channel-roleplay-logic/internal/model"
)

const (
	topicTypeChannelEvent         = "simple_channel_ev"
	ChannelType_USER_CHANNEL_TYPE = 3
)

type RoomEventSubscriber struct {
	sub subscriber.Subscriber

	channelBoxInst *model.ChannelBox
	eventChannel   chan *kafkachannalevent.ChSimpleEvent
}

func NewRoomEventSubscriber(ctx context.Context, clientId, groupId string, topics, brokers []string, channelBoxInst *model.ChannelBox) (*RoomEventSubscriber, error) {
	conf := kafka.DefaultConfig()
	conf.ClientID = clientId
	conf.Consumer.Offsets.Initial = kafka.OffsetNewest
	conf.Consumer.Return.Errors = true

	goroutinesNum := 32
	sub := &RoomEventSubscriber{
		channelBoxInst: channelBoxInst,
		eventChannel:   make(chan *kafkachannalevent.ChSimpleEvent, goroutinesNum),
	}

	kafkaSub, err := kafka.NewSubscriber(brokers, conf)
	if err != nil {
		log.ErrorWithCtx(
			ctx,
			"InitImMsgSubscriber NewSubscriber brokers(%+v) cfg(%+v) err: %v",
			brokers,
			conf,
			err,
		)
		return nil, err
	}
	err = kafkaSub.SubscribeContext(groupId, topics, subscriber.ProcessorContextFunc(sub.handlerEvent))
	if err != nil {
		log.ErrorWithCtx(
			ctx,
			"InitImMsgSubscriber groupId(%s) topics(%+v) err: %v",
			groupId,
			topics,
			err,
		)
		return nil, err
	}

	sub.sub = kafkaSub
	for i := 0; i < goroutinesNum; i++ {
		go sub.procChannelEventInChan()
	}

	return sub, nil
}

func (s *RoomEventSubscriber) Close() {
	s.sub.Stop()
}

func (s *RoomEventSubscriber) CloseEventChannel() {
	close(s.eventChannel)
	log.Infof("CloseEventChannel")
}

func (s *RoomEventSubscriber) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	switch msg.Topic {
	case topicTypeChannelEvent:
		return s.handlerRoomEvent(ctx, msg)
	}
	return nil, false
}

func (s *RoomEventSubscriber) handlerRoomEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	channelEvent := &kafkachannalevent.ChSimpleEvent{}
	ctx  = context_info.GenReqId(ctx)
	err := proto.Unmarshal(msg.Value, channelEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerRoomEvent Failed to proto.Unmarshal err:%s", err.Error())
		return err, false
	}

	log.Debugf("handlerRoomEvent:%v, pid:%d, offset:%d", channelEvent.String(), msg.Partition, msg.Offset)

	if channelEvent.GetEventType() == uint32(kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_LEAVE) ||
		channelEvent.GetEventType() == uint32(kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_EXPIRE_QUIT) ||
		channelEvent.GetEventType() == uint32(kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_ENTER) {
		if channelEvent.GetChannelType() == ChannelType_USER_CHANNEL_TYPE {
			s.eventChannel <- channelEvent
		}
	}

	return nil, false
}

func (s *RoomEventSubscriber) handlerRoomEventMsg(channelEvent *kafkachannalevent.ChSimpleEvent) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	log.InfoWithCtx(ctx, "handlerRoomEventEvent:%v", channelEvent.String())
	if channelEvent.GetEventType() == uint32(kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_ENTER) {
		return s.channelBoxInst.EnterChannel(ctx, channelEvent.GetChId(), channelEvent.GetUid())
	}
	err := s.channelBoxInst.ExitChannel(ctx, channelEvent.GetChId(), channelEvent.GetUid())
	if err != nil {
		return err
	}
	web.IncrKafka(1)
	return nil
}

func (s *RoomEventSubscriber) procChannelEventInChan() {
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, os.Interrupt)

	for {
		select {
		case channelEvent, ok := <-s.eventChannel:
			if !ok {
				log.Infoln("procChannelEventInChan Close")
				return
			} else {
				err := s.handlerRoomEventMsg(channelEvent)
				if err != nil {
					time.Sleep(10 * time.Millisecond)
					continue
				}
			}

		case <-signals:
			log.Errorf("procChannelEventInChan signal Interrupt err")
			time.Sleep(100 * time.Millisecond)
			continue
		}
	}
}
