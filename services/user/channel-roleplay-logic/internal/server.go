package internal

import (
	"context"
	"golang.52tt.com/protocol/services/channelbox"
	"time"

	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/channel"
	channel_scheme "golang.52tt.com/clients/channel-scheme"
	"golang.52tt.com/clients/channelol"

	"golang.52tt.com/clients/account"
	channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
	channelrole "golang.52tt.com/clients/channel-roleplay"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/channel-roleplay-logic"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/user/channel-roleplay-logic/internal/conf"
	"golang.52tt.com/services/user/channel-roleplay-logic/internal/event"
	"golang.52tt.com/services/user/channel-roleplay-logic/internal/model"
)

type Server struct {
	sc              *conf.ServiceConfigT
	channelBoxInst  *model.ChannelBox
	channelRoleInst *model.ChannelRoleModel
	accountCli      *account.Client
	micCli          *channelmic.Client
	roomSub         *event.RoomEventSubscriber
	channelBoxCli   channelbox.ChannelBoxClient
}

func NewServer(ctx context.Context, sc *conf.ServiceConfigT) (*Server, error) {
	log.InfoWithCtx(ctx, "NewServer sc: %+v")

	accountCli, _ := account.NewClient()
	micCli := channelmic.NewClient()
	channelBoxCli, _ := channelbox.NewClient(ctx)
	channelMsgExpress, _ := channelmsgexpress.NewClient()
	channelRoleCli, _ := channelrole.NewClient()
	channelCli := channel.NewClient()
	auditCli := censoring_proxy.NewClient()
	channelolCli := channelol.NewClient()
	channelSchemeCli := channel_scheme.NewClient()

	channelBoxInst := model.NewChannelBox(channelBoxCli, accountCli, micCli, channelMsgExpress, channelRoleCli, channelCli, auditCli)
	channelRoleInst := model.NewRoleModel(channelRoleCli, channelolCli, channelSchemeCli)

	rpLogic := &Server{
		sc:              sc,
		channelBoxInst:  channelBoxInst,
		channelRoleInst: channelRoleInst,
		accountCli:      accountCli,
		micCli:          micCli,
		channelBoxCli:   channelBoxCli,
	}

	if !sc.GetIsGray() {
		kafkaSub, err := event.NewRoomEventSubscriber(ctx, sc.GetSubKafkaConfig().ClientID, sc.GetSubKafkaConfig().GroupID, sc.GetSubKafkaConfig().TopicList(), sc.GetSubKafkaConfig().BrokerList(), channelBoxInst)
		if err != nil {
			log.ErrorWithCtx(ctx, "NewRoomEventSubscriber err %s ", err.Error())
			return nil, err
		}
		rpLogic.roomSub = kafkaSub
	}
	return rpLogic, nil
}

func (s *Server) ShutDown() {
	if s.roomSub != nil {
		s.roomSub.Close()
		time.Sleep(2 * time.Second) //等2秒队列数据处理完

		s.roomSub.CloseEventChannel()
	}
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) GetChannelHoldMicUserRoleList(ctx context.Context, in *pb.GetChannelHoldMicUserRoleListReq) (out *pb.GetChannelHoldMicUserRoleListResp, err error) {
	out = new(pb.GetChannelHoldMicUserRoleListResp)
	if in.GetChannelId() == 0 {
		log.WarnWithCtx(ctx, "Server GetChannelHoldMicUserRoleList channelId 0")
		return
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "user not found")
		return
	}

	out.RoleList, err = s.channelRoleInst.GetChannelHoldMicUserRoleList(ctx, in.GetChannelId(), serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "Server GetChannelHoldMicUserRoleList err: %v", err)
		return
	}

	return
}

func (s *Server) SetMyChannelRole(ctx context.Context, in *pb.SetMyChannelRoleReq) (out *pb.SetMyChannelRoleResp, err error) {
	out = new(pb.SetMyChannelRoleResp)

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "user not found")
		return
	}
	log.InfoWithCtx(ctx, "Server SetMyChannelRole in: %v, serviceinfo:%s", in, serviceInfo.String())

	if in.GetChannelId() == 0 || in.GetRoleName() == "" {
		log.WarnWithCtx(ctx, "SetMyChannelRole invalid in: %v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	switch in.GetRoleType() {
	case pb.RoleType_RoleTypeRolePlay, pb.RoleType_RoleTypeVest:
	default:
		log.WarnWithCtx(ctx, "SetMyChannelRole invalid in: %v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	err = s.channelRoleInst.AuditChannelUserRole(ctx, in.GetRoleType(), in.GetChannelId(), serviceInfo.UserID, in.GetRoleName())
	if err != nil {
		log.ErrorWithCtx(ctx, "Server SetMyChannelRole err: %v", err)
		return
	}

	return
}

func getBoxName(boxIndex uint32) string {
	var boxName string
	if boxIndex == 1 {
		boxName = "1号"
	} else if boxIndex == 2 {
		boxName = "2号"
	} else if boxIndex == 3 {
		boxName = "3号"
	} else if boxIndex == 4 {
		boxName = "4号"
	} else if boxIndex == 5 {
		boxName = "5号"
	} else {
		log.Warnf("invalid boxIndex %d", boxIndex)
	}
	return boxName
}

func (s *Server) GetBoxInfo(ctx context.Context, in *pb.GetBoxInfoReq) (out *pb.GetBoxInfoResp, err error) {
	out = &pb.GetBoxInfoResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "get uid err, in:%+v", serviceInfo)
		return
	}

	if in.GetBoxType() == pb.BoxType_BoxTypeRoleplay {
		boxMap, userCurrentBoxId, err := s.channelBoxInst.GetBoxInfo(ctx, in.GetChannelid(), serviceInfo.UserID)
		if err != nil {
			log.ErrorWithCtx(ctx, "channelBoxInst.GetBoxInfo err:%v, in:%+v, uid:%d", err, in, serviceInfo.UserID)
			return out, err
		}
		var boxIndex uint32
		for boxIndex = 1; boxIndex <= 5; boxIndex++ {
			boxName := getBoxName(boxIndex)
			if boxInfo, ok := boxMap[boxIndex]; ok {
				out.BoxInfo = append(out.BoxInfo, &pb.BoxInfo{
					Channelid:  in.GetChannelid(),
					Boxid:      boxInfo.GetBoxid(),
					BoxUserCnt: boxInfo.GetBoxUserCnt(),
					BoxName:    boxName,
				})
			} else {
				out.BoxInfo = append(out.BoxInfo, &pb.BoxInfo{
					Channelid:  in.GetChannelid(),
					Boxid:      boxIndex,
					BoxName:    boxName,
					BoxUserCnt: 0,
				})
			}
		}
		out.UserBoxid = userCurrentBoxId
	} else {
		out.BoxInfo, out.UserBoxid, out.MainCommonBoxid, out.UserAudioBoxids, out.MicTotal, err = s.channelBoxInst.GetChannelAllBoxInfo(ctx, in.GetChannelid(), serviceInfo.UserID, uint32(in.GetBoxType()))
		if err != nil {
			log.ErrorWithCtx(ctx, "channelBoxInst.GetChannelAllBoxInfo uid(%d) in(%v) err: %v", serviceInfo.UserID, in, err)
			return
		}
	}

	log.InfoWithCtx(ctx, "GetBoxInfo in:%+v, out:%+v, uid:%d", in, out, serviceInfo.UserID)
	return
}

func (s *Server) EnterBox(ctx context.Context, in *pb.EnterBoxReq) (out *pb.EnterBoxResp, err error) {
	out = &pb.EnterBoxResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "get uid err, in:%+v", serviceInfo)
		return
	}

	if in.GetBoxType() == pb.BoxType_BoxTypeRoleplay {
		boxMicUids, err := s.channelBoxInst.CheckIsInMic(ctx, in.GetChannelid(), in.GetBoxid())
		if err != nil {
			log.ErrorWithCtx(ctx, "channelBoxInst.CheckIsInMic err:%v, in:%+v, uid:%d", err, in, serviceInfo.UserID)
			return out, err
		}

		if len(boxMicUids) != 0 {
			err = s.channelBoxInst.ApplyBox(ctx, in.GetChannelid(), in.GetBoxid(), serviceInfo.UserID)
			if err != nil {
				log.ErrorWithCtx(ctx, "channelBoxInst.ApplyBox err:%v, in:%+v, uid:%d", err, in, serviceInfo.UserID)
				return out, err
			}
			out.IsNeedApply = true
			log.InfoWithCtx(ctx, "ApplyBox in:%+v, out:%+v, uid:%d", in, out, serviceInfo.UserID)
		} else {
			enterOpts, err := s.channelBoxInst.EnterBox(ctx, in.GetChannelid(), in.GetBoxid(), serviceInfo.UserID, uint32(pb.BoxType_BoxTypeRoleplay))
			if err != nil {
				log.ErrorWithCtx(ctx, "channelBoxInst.EnterBox err:%v, in:%+v, uid:%d", err, in, serviceInfo.UserID)
				return out, err
			}
			out.OptTs = enterOpts
			log.InfoWithCtx(ctx, "EnterBox in:%+v, out:%+v, uid:%d", in, out, serviceInfo.UserID)
		}
	}

	if in.GetBoxType() == pb.BoxType_BoxTypeMelee {
		if in.GetBoxid() > 0 {
			out.OptTs, err = s.channelBoxInst.EnterBox(ctx, in.GetChannelid(), in.GetBoxid(), serviceInfo.UserID, uint32(pb.BoxType_BoxTypeMelee))
			if err != nil {
				log.ErrorWithCtx(ctx, "EnterBox channelBoxInst.EnterBox uid(%d) in(%v) err: %v", serviceInfo.UserID, in, err)
				return
			}
		}

		if in.GetAudioBoxid() == model.BroadCastBoxId {
			enterOpts, err := s.channelBoxInst.DoBroadCastBoxMic(ctx, in.GetChannelid(), in.GetBoxid(), serviceInfo.UserID, in.GetAudioBoxid(), uint32(in.GetBoxType()), true)
			if err != nil {
				log.ErrorWithCtx(ctx, "channelBoxInst.EnterBox err:%v, in:%+v, uid:%d", err, in, serviceInfo.UserID)
				return out, err
			}
			out.OptTs = enterOpts
			log.InfoWithCtx(ctx, "EnterBox in:%+v, out:%+v, uid:%d", in, out, serviceInfo.UserID)
		}
	}
	log.InfoWithCtx(ctx, "EnterBox in:%+v, out:%+v, uid:%d", in, out, serviceInfo.UserID)

	return out, nil
}

func (s *Server) ExitBox(ctx context.Context, in *pb.ExitBoxReq) (out *pb.ExitBoxResp, err error) {
	out = &pb.ExitBoxResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "get uid err, in:%+v", serviceInfo)
		return
	}
	if in.GetBoxid() != 0 {
		opts, err := s.channelBoxInst.ExitBox(ctx, in.GetChannelid(), in.GetBoxid(), serviceInfo.UserID, uint32(in.GetBoxType()), 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "ExitBox channelBoxInst.ExitBox err:%v, in:%+v, uid:%d", err, in, serviceInfo.UserID)
			return out, err
		}
		out.OptTs = opts
	} else {
		if in.GetAudioBoxid() == model.BroadCastBoxId {
			enterOpts, err := s.channelBoxInst.DoBroadCastBoxMic(ctx, in.GetChannelid(), in.GetBoxid(), serviceInfo.UserID, in.GetAudioBoxid(), uint32(in.GetBoxType()), false)
			if err != nil {
				log.ErrorWithCtx(ctx, "ExitBox channelBoxInst.DoBroadCastBoxMic err:%v, in:%+v, uid:%d", err, in, serviceInfo.UserID)
				return out, err
			}
			out.OptTs = enterOpts
			log.InfoWithCtx(ctx, "ExitBox in:%+v, out:%+v, uid:%d", in, out, serviceInfo.UserID)
		}

		if in.GetAudioBoxid() == model.SubChannelCommonBoxId {
			opts, err := s.channelBoxInst.ExitBox(ctx, in.GetChannelid(), in.GetBoxid(), serviceInfo.UserID, uint32(in.GetBoxType()), in.GetAudioBoxid())
			if err != nil {
				log.ErrorWithCtx(ctx, "ExitBox channelBoxInst.ExitBox err:%v, in:%+v, uid:%d", err, in, serviceInfo.UserID)
				return out, err
			}
			out.OptTs = opts
		}
	}

	log.InfoWithCtx(ctx, "ExitBox in:%+v, out:%+v, uid:%d", in, out, serviceInfo.UserID)
	return out, nil
}

func (s *Server) HandleApplyBox(ctx context.Context, in *pb.HandleApplyBoxReq) (out *pb.HandleApplyBoxResp, err error) {
	out = &pb.HandleApplyBoxResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "get uid err, in:%+v", serviceInfo)
		return
	}
	err = s.channelBoxInst.HandleApplyBox(ctx, in.GetChannelid(), in.GetBoxid(), in.GetUid(), in.GetApplyBoxType(), serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxInst.HandleApplyBox err:%v, in:%+v, uid:%d", err, in, serviceInfo.UserID)
		return out, err
	}
	log.InfoWithCtx(ctx, "HandleApplyBox in:%+v, out:%+v, uid:%d", in, out, serviceInfo.UserID)
	return out, nil
}

func (s *Server) GetBoxInfosByLimit(ctx context.Context, in *pb.GetBoxInfosByLimitReq) (out *pb.GetBoxInfosByLimitResp, err error) {
	out = &pb.GetBoxInfosByLimitResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "get uid err, in:%+v", serviceInfo)
		return
	}
	boxInfos, err := s.channelBoxInst.GetBoxInfosByLimit(ctx, in.GetChannelid(), in.GetBoxid(), uint32(in.GetBoxType()), in.GetLastTime(), in.GetGetCnt())
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxInst.GetBoxInfosByLimit err:%v, in:%+v, uid:%d", err, in, serviceInfo.UserID)
		return out, err
	}
	uids := make([]uint32, 0, len(boxInfos))
	for _, boxInfo := range boxInfos {
		uids = append(uids, boxInfo.GetUid())
	}

	userMap, err := s.accountCli.BatGetUserByUid(ctx, uids...)
	if err != nil {
		log.ErrorWithCtx(ctx, "accountCli.BatGetUserByUi err:%v, in:%+v, uid:%d", err, in, serviceInfo.UserID)
		return out, err
	}

	out.Channelid = in.GetChannelid()
	out.Boxid = in.GetBoxid()
	for _, boxInfo := range boxInfos {
		out.LastTime = int64(boxInfo.GetOpts())
		out.BoxUserInfos = append(out.BoxUserInfos, &pb.BoxUserInfo{
			Uid:      boxInfo.GetUid(),
			Accunt:   userMap[boxInfo.GetUid()].GetUsername(),
			NickName: userMap[boxInfo.GetUid()].GetNickname(),
			Gender:   userMap[boxInfo.GetUid()].GetSex(),
		})
	}
	log.InfoWithCtx(ctx, "GetBoxInfosByLimit in:%+v, out:%+v, uid:%d", in, out, serviceInfo.UserID)
	return out, nil
}

func (s *Server) GetChannelUserRoleList(ctx context.Context, in *pb.GetChannelUserRoleListReq) (out *pb.GetChannelUserRoleListResp, err error) {
	out = new(pb.GetChannelUserRoleListResp)
	if len(in.GetUidList()) == 0 {
		log.WarnWithCtx(ctx, "ChannelRoleplayLogic GetChannelUserRoleList uidList empty")
		return
	}

	log.DebugWithCtx(ctx, "ChannelRoleplayLogic GetChannelUserRoleList in: %+v", in)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "ChannelRoleplayLogic GetChannelUserRoleList uid not exists")
		return
	}

	out.RoleList, out.AuditRole, err = s.channelRoleInst.GetChannelUserRoleList(ctx, in.GetRoleType(), svcInfo.UserID, in.GetChannelId(), in.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelRoleplayLogic GetChannelUserRoleList err: %v", err)
		return
	}

	log.DebugWithCtx(ctx, "ChannelRoleplayLogic GetChannelUserRoleList out: %+v", out)
	return
}

func (s *Server) UpsertBoxInfo(ctx context.Context, in *pb.UpsertBoxInfoReq) (out *pb.UpsertBoxInfoResp, err error) {
	out = new(pb.UpsertBoxInfoResp)
	log.InfoWithCtx(ctx, "Server.UpsertBoxInfo in: %v", in)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "Server.UpsertBoxInfo uid not exists")
		return
	}

	out.Box, err = s.channelBoxInst.UpsertBoxInfo(ctx, svcInfo.UserID, in.GetBox())
	if err != nil {
		log.ErrorWithCtx(ctx, "Server.UpsertBoxInfo channelBoxInst.UpsertBoxInfo uid(%d) err: %v", svcInfo.UserID, err)
		return
	}

	return
}

func (s *Server) DelBoxInfo(ctx context.Context, in *pb.DelBoxInfoReq) (out *pb.DelBoxInfoResp, err error) {
	out = new(pb.DelBoxInfoResp)
	log.InfoWithCtx(ctx, "Server.DelBoxInfo in: %v", in)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "Server.DelBoxInfo uid not exists")
		return
	}

	err = s.channelBoxInst.DelBoxInfo(ctx, svcInfo.UserID, in.GetChannelId(), in.GetBoxId())
	if err != nil {
		log.ErrorWithCtx(ctx, "Server.DelBoxInfo channelBoxInst.DelBoxInfo uid(%d) channelId(%v) boxId(%d) err: %v",
			svcInfo.UserID, in.GetChannelId(), in.GetBoxId(), err)
		return
	}

	return
}

func (s *Server) OpenCommonMic(ctx context.Context, in *pb.OpenCommonMicReq) (out *pb.OpenCommonMicResp, err error) {
	out = new(pb.OpenCommonMicResp)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "Server.UpsertBoxInfo uid not exists")
		return
	}

	err = s.channelBoxInst.OpenCommonMic(ctx, svcInfo.UserID, in.GetChannelId(), in.GetCommonBoxid())
	if err != nil {
		log.ErrorWithCtx(ctx, "OpenCommonMic OpenCommonMic err: %v, in:%+v", err, in)
		return
	}
	log.InfoWithCtx(ctx, "OpenCommonMic in: %v, out:%+v", in, out)
	return
}

func (s *Server) GetMainChannelUserList(ctx context.Context, in *pb.GetMainChannelUserListReq) (*pb.GetMainChannelUserListResp, error) {
	out := &pb.GetMainChannelUserListResp{}
	if in.GetChannelId() == 0 {
		log.WarnWithCtx(ctx, "GetMainChannelUserList channelId 0")
		return out, nil
	}
	boxRsp, err := s.channelBoxCli.GetMainChannelUsers(ctx, &channelbox.GetMainChannelUsersReq{
		ChannelId: in.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMainChannelUserList channelBoxCli.GetMainChannelUsers err:%v, in:%+v", err, in)
		return out, err
	}
	uids := boxRsp.GetUserIds()
	if len(uids) == 0 {
		log.InfoWithCtx(ctx, "GetMainChannelUserList no users in channelId %d", in.GetChannelId())
		return out, nil
	}
	out.UserInfos = make([]*pb.BoxUserInfo, 0, len(uids))
	// 分批次查询account信息，每次查询100个用户,很少会超过100的，暂时不做并发
	for i := 0; i < len(uids); i += 100 {
		end := i + 100
		if end > len(uids) {
			end = len(uids)
		}
		batchUids := uids[i:end]
		userMap, err := s.accountCli.BatGetUserByUid(ctx, batchUids...)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMainChannelUserList accountCli.BatGetUserByUid err:%v, in:%+v", err, in)
			continue
		}
		for _, uid := range batchUids {
			if userInfo, ok := userMap[uid]; ok {
				out.UserInfos = append(out.UserInfos, &pb.BoxUserInfo{
					Uid:      userInfo.GetUid(),
					Accunt:   userInfo.GetUsername(),
					NickName: userInfo.GetNickname(),
					Gender:   userInfo.GetSex(),
				})
			}
		}
	}
	log.InfoWithCtx(ctx, "GetMainChannelUserList in:%s, len(out):%d", in, len(out.GetUserInfos()))
	return out, nil
}
