package model

import (
	"context"
	"fmt"
	"strconv"

	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	cybros_arbiter_v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	"google.golang.org/grpc/codes"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account"
	channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
	channelrole "golang.52tt.com/clients/channel-roleplay"
	"golang.52tt.com/clients/channelbox"
	"golang.52tt.com/clients/channelmic"

	//PushNotification "golang.52tt.com/clients/push-notification/v2"
	"time"

	"golang.52tt.com/pkg/log"
	appChannelPB "golang.52tt.com/protocol/app/channel"
	channelPb "golang.52tt.com/protocol/app/channel"
	channel_roleplay_logic_pb "golang.52tt.com/protocol/app/channel-roleplay-logic"
	channelRolePb "golang.52tt.com/protocol/services/channel-roleplay"
	pb "golang.52tt.com/protocol/services/channelbox"
)

const (
	SubChannelCommonBoxId = 2
	BroadCastBoxId        = 4
)

type ChannelBox struct {
	channelBoxCli        *channelbox.Client
	accountCli           *account.Client
	micCli               *channelmic.Client
	channelMsgExpressCli *channelmsgexpress.Client
	roleCli              *channelrole.Client
	channelCli           *channel.Client
	auditCli             *censoring_proxy.Client
}

func NewChannelBox(channelBoxCli *channelbox.Client, accountCli *account.Client, micCli *channelmic.Client, channelMsgExpressCli *channelmsgexpress.Client, roleCli *channelrole.Client, channelCli *channel.Client, auditCli *censoring_proxy.Client) *ChannelBox {
	return &ChannelBox{
		channelBoxCli:        channelBoxCli,
		accountCli:           accountCli,
		micCli:               micCli,
		channelMsgExpressCli: channelMsgExpressCli,
		roleCli:              roleCli,
		channelCli:           channelCli,
		auditCli:             auditCli,
	}
}

func (s *ChannelBox) GetBoxInfo(ctx context.Context, channelid, opeUid uint32) (boxMap map[uint32]*pb.BoxCntInfo, boxId uint32, err error) {
	boxMap = make(map[uint32]*pb.BoxCntInfo)
	req := pb.GetBoxInfoReq{
		OpeUid:    opeUid,
		Channelid: channelid,
	}
	rsp, err := s.channelBoxCli.GetBoxInfo(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxCli.GetBoxInfo err:%v, req:%+v", err, req)
		return nil, 0, err
	}
	boxInfos := rsp.GetBoxInfo()
	for _, boxInfo := range boxInfos {
		boxMap[boxInfo.GetBoxid()] = boxInfo
	}
	boxId = rsp.GetUserBoxid()
	return boxMap, boxId, nil
}

func (s *ChannelBox) broadcastUsers(ctx context.Context, uid, channelId, boxId uint32, userName, nickName string, notifyInfo *channel_roleplay_logic_pb.OpeBoxNotify) {
	pbContent, _ := proto.Marshal(notifyInfo)
	bMsg := &channelPb.ChannelBroadcastMsg{
		FromUid:      uid,
		FromAccount:  userName,
		FromNick:     nickName,
		ToChannelId:  channelId,
		Time:         uint64(time.Now().Unix()),
		Type:         uint32(appChannelPB.ChannelMsgType_USER_OPE_CHANNEL_BOX_NOTIFY),
		PbOptContent: pbContent,
		Content:      []byte(""),
		ChannelBoxId: boxId,
	}
	err := s.channelMsgExpressCli.SendChannelBroadcastMsg(ctx, bMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "broadcastUsers channelMsgExpressCli.SendChannelBroadcastMsg err: %v", err)
		return
	}
	log.InfoWithCtx(ctx, "broadcastUsers channelid:%d, uid:%d, notifyInfo:%+v", channelId, uid, notifyInfo)
}

func (s *ChannelBox) broadcastApplyInfo(ctx context.Context, uid, channelId, boxId uint32, userName, nickName string, notifyInfo *channel_roleplay_logic_pb.ApplyNotify) {
	pbContent, _ := proto.Marshal(notifyInfo)
	bMsg := &channelPb.ChannelBroadcastMsg{
		FromUid:      uid,
		FromAccount:  userName,
		FromNick:     nickName,
		ToChannelId:  channelId,
		Time:         uint64(time.Now().Unix()),
		Type:         uint32(appChannelPB.ChannelMsgType_USER_APPLY_CHANNEL_BOX_NOTIFY),
		PbOptContent: pbContent,
		Content:      []byte(""),
		ChannelBoxId: boxId,
	}
	err := s.channelMsgExpressCli.SendChannelBroadcastMsg(ctx, bMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "broadcastApplyInfo channelMsgExpressCli.SendChannelBroadcastMsg err: %v", err)
		return
	}
	log.InfoWithCtx(ctx, "broadcastUsers channelid:%d, uid:%d, notifyInfo:%+v", channelId, uid, notifyInfo)
}

func (s *ChannelBox) broadcastBoxInfoChange(ctx context.Context, channelId uint32, notify *channel_roleplay_logic_pb.BoxChangeNotify) error {
	optContent, err := proto.Marshal(notify)
	if err != nil {
		log.ErrorWithCtx(ctx, "broadcastBoxInfo proto.Marshal err: %v", err)
		return err
	}

	err = s.channelMsgExpressCli.SendChannelBroadcastMsg(ctx, &channelPb.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         uint32(appChannelPB.ChannelMsgType_CHANNEL_BOX_INFO_CHANGE_NOTIFY),
		PbOptContent: optContent,
		Content:      []byte(""),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "broadcastBoxInfo SendChannelBroadcastMsg channelId(%d) err: %v", channelId, err)
		return err
	}

	log.InfoWithCtx(ctx, "broadcastBoxInfo channelId(%d) notify(%v) successfully", channelId, notify)
	return nil
}

/*func (s *ChannelBox) pushUser(uid uint32, notifyInfo *channel_roleplay_logic_pb.OpeBoxNotify) error {
	var uidList = []uint32{uid}

	byteNotify, err := proto.Marshal(notifyInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushUser marshal err: %v, %+v", err, notifyInfo)
		return err
	}

	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_GUIDE_CARTEAM_ENTER_ROOM_PUSH),
		Content: byteNotify,
	}
	pushMessageBytes, _ := pushMessage.Marshal()

	err = s.pushClient.PushToUsers(context.Background(), uidList, &pushPb.CompositiveNotification{
		Sequence:           uid,
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPb.ProxyNotification{
			Type:    uint32(pushPb.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	})

	log.Debugf("pushUser, uid:%d, cmd:%d, PushToUsers %+v ", uid, pushMessage.Cmd, uidList)

	if err != nil {
		log.ErrorWithCtx(ctx, "PushToUsers err:%v, %+v", err, notifyInfo)
		return err
	}

	return nil
}*/

func (s *ChannelBox) genNotifyInfo(opeType channel_roleplay_logic_pb.BoxNotifyUserOpType, channelId, boxId, uid, boxCnt uint32, notifyType channel_roleplay_logic_pb.BoxNotifyType, roleName string, opts uint64, boxType uint32) *channel_roleplay_logic_pb.OpeBoxNotify {
	boxInfo := &channel_roleplay_logic_pb.OpeBoxInfo{
		OpeType:    opeType,
		Boxid:      boxId,
		Uid:        uid,
		BoxCnt:     boxCnt,
		NotifyType: notifyType,
		OptTs:      opts,
	}
	notifyInfo := &channel_roleplay_logic_pb.OpeBoxNotify{
		OpeBoxInfo: []*channel_roleplay_logic_pb.OpeBoxInfo{boxInfo},
		ChannelId:  channelId,
		RoleName:   roleName,
		BoxType:    channel_roleplay_logic_pb.BoxType(boxType),
	}
	if notifyType == channel_roleplay_logic_pb.BoxNotifyType_UserOpType {
		notifyInfo.Msg = "退出包厢"
	} else {
		notifyInfo.Msg = "包厢暂时无法进入，请选择其他包厢"
	}
	return notifyInfo
}

func (s *ChannelBox) genSwitchNotifyInfo(channelId, uid uint32, boxInfos []*pb.EnterBoxInfo, notifyType channel_roleplay_logic_pb.BoxNotifyType, roleName, opeRoleName string, opts uint64, boxType uint32) *channel_roleplay_logic_pb.OpeBoxNotify {
	notifyInfo := &channel_roleplay_logic_pb.OpeBoxNotify{
		ChannelId: channelId,
		BoxType:   channel_roleplay_logic_pb.BoxType(boxType),
	}
	for _, boxInfo := range boxInfos {
		var opeType channel_roleplay_logic_pb.BoxNotifyUserOpType
		if boxInfo.BoxType == pb.EnterResultBoxType_SwitchType {
			opeType = channel_roleplay_logic_pb.BoxNotifyUserOpType_ExitBox
		} else {
			opeType = channel_roleplay_logic_pb.BoxNotifyUserOpType_EnterBox
			if notifyType == channel_roleplay_logic_pb.BoxNotifyType_UserOpType {
				notifyInfo.Msg = "进入包厢"
			} else {
				//notifyInfo.Msg = opeRoleName + "_已同意_" + roleName + "_进入包厢"
				notifyInfo.OpeRoleName = opeRoleName
			}
			notifyInfo.RoleName = roleName
		}
		boxInfo := &channel_roleplay_logic_pb.OpeBoxInfo{
			OpeType:     opeType,
			Boxid:       boxInfo.GetBoxid(),
			Uid:         uid,
			BoxCnt:      boxInfo.GetBoxCnt(),
			NotifyType:  notifyType,
			OptTs:       opts,
			AudioBoxIds: boxInfo.GetAudioBoxIds(),
		}
		notifyInfo.OpeBoxInfo = append(notifyInfo.OpeBoxInfo, boxInfo)
	}

	return notifyInfo
}

func (s *ChannelBox) genApplyInfo(channelId, boxId, uid uint32, roleName string) *channel_roleplay_logic_pb.ApplyNotify {
	notifyInfo := &channel_roleplay_logic_pb.ApplyNotify{
		ChannelId: channelId,
		Boxid:     boxId,
		Uid:       uid,
		Msg:       "申请进入当前包厢",
		RoleName:  roleName,
	}

	return notifyInfo
}

func (s *ChannelBox) genBroadCastNotifyInfo(opeType channel_roleplay_logic_pb.BoxNotifyUserOpType, channelId, uid uint32, notifyType channel_roleplay_logic_pb.BoxNotifyType, roleName string, opts uint64, boxType channel_roleplay_logic_pb.BoxType) *channel_roleplay_logic_pb.OpeBoxNotify {
	boxInfo := &channel_roleplay_logic_pb.OpeBoxInfo{
		OpeType:    opeType,
		Boxid:      0,
		Uid:        uid,
		BoxCnt:     0,
		NotifyType: notifyType,
		OptTs:      opts,
	}
	notifyInfo := &channel_roleplay_logic_pb.OpeBoxNotify{
		OpeBoxInfo: []*channel_roleplay_logic_pb.OpeBoxInfo{boxInfo},
		ChannelId:  channelId,
		RoleName:   roleName,
		BoxType:    boxType,
	}
	return notifyInfo
}

func (s *ChannelBox) CheckIsInMic(ctx context.Context, channelid, boxid uint32) (boxMicUids []uint32, err error) {
	req := pb.GetBoxUserInMicInfosReq{
		Boxid:     boxid,
		Channelid: channelid,
	}
	rsp, err := s.channelBoxCli.GetBoxUserInMicInfos(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxCli.GetBoxUserInMicInfos err:%v, req:%+v", err, req)
		return nil, err
	}
	boxMicUids = rsp.GetBoxUids()
	/*micRsp, err := s.micCli.GetMicrList(ctx, channelid, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelUserRoleModel GetChannelHoldMicUserRoleList err: %v", err)
		return nil, err
	}
	if micRsp.GetMicrMode() == uint32(channelPb.EChannelMicMode_ROLE_PLAY_MIC_SPACE_MODE) {
		if len(micRsp.GetAllMicList()) == 0 {
			return boxMicUids, nil
		}
		var micUids []uint32
		for _, micInfo := range micRsp.GetAllMicList() {
			micUids = append(micUids, micInfo.GetMicUid())
		}
		req := pb.GetBoxUserInMicInfosReq{
			Boxid:     boxid,
			Channelid: channelid,
			MicUids:   micUids,
		}
		rsp, err := s.channelBoxCli.GetBoxUserInMicInfos(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "channelBoxCli.GetBoxUserInMicInfos err:%v, req:%+v", err, req)
			return nil, err
		}
		boxMicUids = append(boxMicUids, rsp.GetBoxUids()...)
	} else {
		log.ErrorWithCtx(ctx, "mic mode:%d err, channelid:%d, boxid:%d, boxMicUids:%v", micRsp.GetMicrMode(), channelid, boxid, boxMicUids)
	}*/
	return boxMicUids, nil
}

func (s *ChannelBox) EnterBox(ctx context.Context, channelid, boxid, uid, boxType uint32) (enterTimeStamp uint64, err error) {
	req := pb.EnterBoxReq{
		Uid:       uid,
		Boxid:     boxid,
		Channelid: channelid,
		BoxType:   pb.BoxType(boxType),
	}

	rsp, err := s.channelBoxCli.EnterBox(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxCli.EnterBox err:%v, req:%+v", err, req)
		return 0, err
	}

	userInfo, err := s.accountCli.GetUserByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxCli.EnterBox err:%v, uid:%d", err, uid)
		return 0, err
	}

	roleName := userInfo.GetNickname()
	if err = s.replaceChannelRoleName(ctx, channelid, uid, boxType, &roleName); err != nil {
		log.ErrorWithCtx(ctx, "getChannelRoleName channelId(%d) uid(%d) boxType(%d) err: %v", channelid, uid, boxType, err)
		return 0, err
	}

	enterNotifyInfo := s.genSwitchNotifyInfo(channelid, uid, rsp.GetEnterBoxInfos(), channel_roleplay_logic_pb.BoxNotifyType_UserOpType, roleName, "", rsp.GetOpts(), boxType)
	s.broadcastUsers(ctx, uid, channelid, boxid, userInfo.GetUsername(), userInfo.GetNickname(), enterNotifyInfo)
	return rsp.GetOpts(), nil
}

func (s *ChannelBox) DoBroadCastBoxMic(ctx context.Context, channelid, boxid, uid, broadcastBoxId, boxType uint32, isEnter bool) (enterTimeStamp uint64, err error) {

	var opts uint64
	if isEnter {
		isAdmin, err := s.isChannelAdmin(ctx, channelid, uid)
		if err != nil {
			return 0, err
		}

		if !isAdmin {
			log.ErrorWithCtx(ctx, "OpenCommonMic channel %d user %d not admin", channelid, uid)
			return 0, protocol.NewExactServerError(codes.PermissionDenied, status.ErrAccountPermissionDenied, "您已被取消管理员权限")
		}
		req := pb.EnterBoxReq{
			Uid:            uid,
			Boxid:          boxid,
			Channelid:      channelid,
			BroadcastBoxid: broadcastBoxId,
			BoxType:        pb.BoxType(boxType),
		}
		rsp, err := s.channelBoxCli.EnterBox(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "channelBoxCli.EnterBox err:%v, req:%+v", err, req)
			return 0, err
		}
		opts = rsp.GetOpts()
	} else {
		req := pb.ExitBoxReq{
			Uid:            uid,
			Boxid:          boxid,
			Channelid:      channelid,
			BroadcastBoxid: broadcastBoxId,
			BoxType:        pb.BoxType(boxType),
		}
		rsp, err := s.channelBoxCli.ExitBox(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "channelBoxCli.ExitBox err:%v, req:%+v", err, req)
			return 0, err
		}
		opts = rsp.GetOpts()
	}

	userInfo, err := s.accountCli.GetUserByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "accountCli.GetUserByUid err:%v, uid:d", err, uid)
		return 0, err
	}
	roleName := userInfo.GetNickname()
	var opeType channel_roleplay_logic_pb.BoxNotifyUserOpType
	if isEnter {
		opeType = channel_roleplay_logic_pb.BoxNotifyUserOpType_EnterBroadcastMicBox
	} else {
		opeType = channel_roleplay_logic_pb.BoxNotifyUserOpType_ExitBroadcastMicBox
	}
	enterNotifyInfo := s.genBroadCastNotifyInfo(opeType, channelid, uid, channel_roleplay_logic_pb.BoxNotifyType_UserOpType, roleName, opts, channel_roleplay_logic_pb.BoxType(boxType))

	s.broadcastUsers(ctx, uid, channelid, boxid, userInfo.GetUsername(), userInfo.GetNickname(), enterNotifyInfo)
	return opts, nil
}

func (s *ChannelBox) ApplyBox(ctx context.Context, channelid, boxid, uid uint32) error {
	userInfo, err := s.accountCli.GetUserByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "accountCli.GetUserByUid err:%v, uid:d", err, uid)
		return err
	}
	roleName := userInfo.GetNickname()
	roleInfos, roleErr := s.roleCli.GetChannelUserRoleList(ctx, channelRolePb.RoleType_RoleTypeRolePlay, channelid, []uint32{uid})
	if roleErr != nil {
		log.ErrorWithCtx(ctx, "roleCli.GetChannelUserRoleList err:%v, uid:%d", roleErr, uid)
		return roleErr
	}
	if len(roleInfos) >= 1 {
		roleName = roleInfos[0].GetRoleName()
	}
	applyNotifyInfo := s.genApplyInfo(channelid, boxid, uid, roleName)
	s.broadcastApplyInfo(ctx, uid, channelid, boxid, userInfo.GetUsername(), userInfo.GetNickname(), applyNotifyInfo)
	return nil
}

func (s *ChannelBox) ExitBox(ctx context.Context, channelid, boxid, uid, boxType, commonBoxId uint32) (uint64, error) {
	req := pb.ExitBoxReq{
		Uid:         uid,
		Boxid:       boxid,
		Channelid:   channelid,
		BoxType:     pb.BoxType(boxType),
		CommonBoxid: commonBoxId,
	}

	rsp, err := s.channelBoxCli.ExitBox(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxCli.ExitBox err:%v, req:%+v", err, req)
		return 0, err
	}

	if boxid != 0 {
		userInfo, err := s.accountCli.GetUserByUid(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "accountCli.GetUserByUid err:%v, uid:d", err, uid)
			return 0, err
		}

		roleName := userInfo.GetNickname()
		if err := s.replaceChannelRoleName(ctx, channelid, uid, boxType, &roleName); err != nil {
			log.ErrorWithCtx(ctx, "getChannelRoleName channelId(%d) uid(%d) boxType(%d) err: %v", channelid, uid, boxType, err)
			return 0, err
		}

		enterNotifyInfo := s.genNotifyInfo(channel_roleplay_logic_pb.BoxNotifyUserOpType_ExitBox, channelid, boxid, uid, rsp.GetBoxCnt(), channel_roleplay_logic_pb.BoxNotifyType_UserOpType, roleName, rsp.GetOpts(), boxType)
		s.broadcastUsers(ctx, uid, channelid, boxid, userInfo.GetUsername(), userInfo.GetNickname(), enterNotifyInfo)
	}

	return rsp.GetOpts(), nil
}

func (s *ChannelBox) HandleApplyBox(ctx context.Context, channelid, boxid, uid uint32, applyType channel_roleplay_logic_pb.ApplyBoxType, serviceUid uint32) error {
	var joinType pb.JoinBoxType
	if applyType == channel_roleplay_logic_pb.ApplyBoxType_ApplyAccept {
		joinType = pb.JoinBoxType_AcceptType
	} else {
		joinType = pb.JoinBoxType_RejectType
	}
	req := pb.JoinBoxReq{
		Uid:         uid,
		Boxid:       boxid,
		Channelid:   channelid,
		JoinBoxType: joinType,
	}
	rspInfo, err := s.channelBoxCli.JoinBox(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxCli.JoinBox err:%v, req:%+v", err, req)
		return err
	}
	if rspInfo.IsIgnore {
		log.InfoWithCtx(ctx, "HandleApplyBox ignore, req:%v", req)
		return nil
	}

	userMap, err := s.accountCli.BatGetUserByUid(ctx, serviceUid, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "accountCli.GetUserByUid err:%v, uid:d", err, uid)
		return err
	}

	roleMap := make(map[uint32]*channelRolePb.UserRole)
	roleInfos, roleErr := s.roleCli.GetChannelUserRoleList(ctx, channelRolePb.RoleType_RoleTypeRolePlay, channelid, []uint32{serviceUid, uid})
	if roleErr != nil {
		log.ErrorWithCtx(ctx, "roleCli.GetChannelUserRoleList err:%v, uid:%d", roleErr, uid)
		return roleErr
	}
	for _, roleInfo := range roleInfos {
		roleMap[roleInfo.Uid] = roleInfo
	}
	var opeRoleName, roleName, userName, userNickName string
	if roleInfo, ok := roleMap[serviceUid]; ok {
		opeRoleName = roleInfo.GetRoleName()
		if opeRoleName == "" {
			opeRoleName = userMap[serviceUid].GetNickname()
		}
	} else {
		opeRoleName = userMap[serviceUid].GetNickname()
	}
	if roleInfo, ok := roleMap[uid]; ok {
		roleName = roleInfo.GetRoleName()
		if roleName == "" {
			roleName = userMap[uid].GetNickname()
		}
	} else {
		roleName = userMap[uid].GetNickname()
	}
	userName = userMap[uid].GetUsername()
	userNickName = userMap[uid].GetNickname()

	var boxId uint32
	for _, boxInfo := range rspInfo.GetEnterBoxInfos() {
		if boxInfo.BoxType == pb.EnterResultBoxType_EnterType {
			boxId = boxInfo.GetBoxid()
		}
	}
	if applyType == channel_roleplay_logic_pb.ApplyBoxType_ApplyAccept {
		enterNotifyInfo := s.genSwitchNotifyInfo(channelid, uid, rspInfo.GetEnterBoxInfos(), channel_roleplay_logic_pb.BoxNotifyType_ApplyAcceptUserType, roleName, opeRoleName, rspInfo.GetOpts(), uint32(channel_roleplay_logic_pb.BoxType_BoxTypeRoleplay))
		s.broadcastUsers(ctx, uid, channelid, boxId, userName, userNickName, enterNotifyInfo)
	} else {
		rejectNotifyInfo := s.genNotifyInfo(channel_roleplay_logic_pb.BoxNotifyUserOpType_EnterNone, channelid, boxid, uid, 0, channel_roleplay_logic_pb.BoxNotifyType_ApplyRejectUserType, roleName, 0, uint32(channel_roleplay_logic_pb.BoxType_BoxTypeRoleplay))
		s.broadcastUsers(ctx, uid, channelid, boxId, userName, userNickName, rejectNotifyInfo)
	}

	return nil
}

func (s *ChannelBox) GetBoxInfosByLimit(ctx context.Context, channelid, boxid, boxType uint32, lastTime, getCnt int64) ([]*pb.BoxBaseInfo, error) {
	req := pb.GetBoxInfosByLimitReq{
		Boxid:     boxid,
		Channelid: channelid,
		LastTime:  lastTime,
		GetCnt:    getCnt,
		BoxType:   pb.BoxType(boxType),
	}
	rsp, err := s.channelBoxCli.GetBoxInfosByLimit(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxCli.GetBoxInfosByLimit err:%v, req:%+v", err, req)
		return nil, err
	}
	return rsp.GetBoxBaseInfos(), nil
}

func (s *ChannelBox) ExitChannel(ctx context.Context, channelid, uid uint32) error {
	req := pb.ExitChannelReq{
		Uid:       uid,
		Channelid: channelid,
	}
	rsp, err := s.channelBoxCli.ExitChannel(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "channelBoxCli.GetBoxInfosByLimit err:%v, req:%+v", err, req)
		return err
	}

	if rsp.GetBoxId() != 0 {
		userInfo, err := s.accountCli.GetUserByUid(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "accountCli.GetUserByUid err:%v, uid:d", err, uid)
			return err
		}
		roleName := userInfo.GetNickname()
		roleInfos, roleErr := s.roleCli.GetChannelUserRoleList(ctx, channelRolePb.RoleType_RoleTypeRolePlay, channelid, []uint32{uid})
		if roleErr != nil {
			log.ErrorWithCtx(ctx, "roleCli.GetChannelUserRoleList err:%v, uid:%d", roleErr, uid)
			return roleErr
		}
		if len(roleInfos) >= 1 {
			roleName = roleInfos[0].GetRoleName()
		}
		enterNotifyInfo := s.genNotifyInfo(channel_roleplay_logic_pb.BoxNotifyUserOpType_ExitBox, channelid, rsp.GetBoxId(), uid, rsp.GetBoxCnt(), channel_roleplay_logic_pb.BoxNotifyType_UserOpType, roleName, rsp.GetOpts(), uint32(rsp.GetBoxType()))
		s.broadcastUsers(ctx, uid, channelid, rsp.GetBoxId(), userInfo.GetUsername(), userInfo.GetNickname(), enterNotifyInfo)
	}

	return nil
}

func (s *ChannelBox) UpsertBoxInfo(ctx context.Context, uid uint32, box *channel_roleplay_logic_pb.BoxInfo) (*channel_roleplay_logic_pb.BoxInfo, error) {
	isAdmin, err := s.isChannelAdmin(ctx, box.GetChannelid(), uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.UpsertBoxInfo isChannelAdmin uid(%d) box(%v) err: %v", uid, box, err)
		return nil, err
	}

	if !isAdmin {
		log.ErrorWithCtx(ctx, "channel %d user %d not admin", box.GetChannelid(), uid)
		return nil, protocol.NewExactServerError(codes.PermissionDenied, status.ErrChannelboxAdminPermCanceled)
	}

	rsp, err := s.channelBoxCli.UpsertBoxInfo(ctx, pb.UpsertBoxInfoReq{
		Box: &pb.BoxInfo{
			ChannelId:     box.GetChannelid(),
			BoxId:         box.GetBoxid(),
			CreatorUid:    uid,
			BoxType:       pb.BoxType(box.GetBoxType()),
			BoxName:       box.GetBoxName(),
			MicCap:        box.GetMicCap(),
			PublicMicList: box.GetPublicMicList(),
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.UpsertBoxInfo channelBoxCli.UpsertBoxInfo uid(%d) box(%v) err: %v", uid, box, err)
		return nil, err
	}

	oldBox := rsp.GetBox()

	// 子频道名称送审
	if box.GetBoxName() != oldBox.GetBoxName() {
		err = s.auditBoxName(ctx, uid, oldBox.GetChannelId(), oldBox.GetBoxId(), box.GetBoxName())
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelBox.UpserBoxInfo auditBoxName channelId(%d) boxId(%d) boxName(%s) err: %v",
				oldBox.GetChannelId(), oldBox.GetBoxId(), box.GetBoxName())
			return nil, err
		}
	}

	// 麦位更改广播
	if box.GetBoxid() > 0 && box.GetMicCap() != oldBox.GetMicCap() {
		err = s.broadcastBoxInfoChange(ctx, box.GetChannelid(), &channel_roleplay_logic_pb.BoxChangeNotify{
			Op: channel_roleplay_logic_pb.BoxChangeNotify_OpUpdateMic,
			Box: &channel_roleplay_logic_pb.BoxInfo{
				Channelid:     box.GetChannelid(),
				Boxid:         box.GetBoxid(),
				BoxType:       box.GetBoxType(),
				MicCap:        box.GetMicCap(),
				PublicMicList: box.PublicMicList,
			},
			ChangedAt: uint64(time.Now().Unix() / 1e6),
			BoxUid:    uid,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelBox.UpsertBoxInfo broadcastBoxInfoChange box(%v) err: %v", box, err)
			return nil, err
		}
	}

	return &channel_roleplay_logic_pb.BoxInfo{
		Channelid:     oldBox.GetChannelId(),
		Boxid:         oldBox.GetBoxId(),
		BoxName:       box.GetBoxName(),
		BoxType:       box.GetBoxType(),
		MicCap:        box.GetMicCap(),
		PublicMicList: oldBox.GetPublicMicList(),
	}, nil
}

func (s *ChannelBox) DelBoxInfo(ctx context.Context, uid, channelId, boxId uint32) error {
	isAdmin, err := s.isChannelAdmin(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.DelBoxInfo isChannelAdmin uid(%d) channelId(%d) boxId(%d) err: %v", uid, channelId, boxId, err)
		return err
	}

	if !isAdmin {
		log.ErrorWithCtx(ctx, "channel %d user %d not admin", channelId, uid)
		return protocol.NewExactServerError(codes.PermissionDenied, status.ErrChannelboxAdminPermCanceled)
	}

	rsp, err := s.channelBoxCli.DelBoxInfo(ctx, pb.DelBoxInfoReq{
		ChannelId: channelId,
		BoxId:     boxId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.DelBoxInfo channelBoxCli.DelBoxInfo channelId(%d) boxId(%d) err: %v", channelId, boxId, err)
		return err
	}

	now := uint64(time.Now().Unix() / 1e6)
	err = s.broadcastBoxInfoChange(ctx, channelId, &channel_roleplay_logic_pb.BoxChangeNotify{
		Op:        channel_roleplay_logic_pb.BoxChangeNotify_OpDelete,
		ChangedAt: now,
		BoxUid:    uid,
		ExistsBox: rsp.GetBoxNum() > 0,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.DelBoxInfo broadcastBoxInfoChange channelId(%d) boxId(%d) err: %v", channelId, boxId, err)
		return err
	}

	return nil
}

func (s *ChannelBox) isChannelAdmin(ctx context.Context, channelId, uid uint32) (bool, error) {
	adminList, rpcErr := s.channelCli.GetChannelAdmin(ctx, uid, channelId)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.IsChannelAdmin channelCli.GetChannelAdmin channelId(%d) uid(%d) err: %v", channelId, uid, rpcErr)
		return false, rpcErr
	}

	var isAdmin bool
	for _, admin := range adminList {
		if uid == admin.GetUid() {
			isAdmin = true
			break
		}
	}

	return isAdmin, nil
}

func (s *ChannelBox) broadcastUsersCommonMicInfo(ctx context.Context, uid, channelId, boxId uint32, userName, nickName string, notifyInfo *channel_roleplay_logic_pb.OpenCommonMicBC) {
	pbContent, _ := proto.Marshal(notifyInfo)
	bMsg := &channelPb.ChannelBroadcastMsg{
		FromUid:      uid,
		FromAccount:  userName,
		FromNick:     nickName,
		ToChannelId:  channelId,
		Time:         uint64(time.Now().Unix()),
		Type:         uint32(appChannelPB.ChannelMsgType_CHANNEL_BOX_COMMON_MIC_NOTIFY),
		PbOptContent: pbContent,
		Content:      []byte(""),
		ChannelBoxId: boxId,
	}
	err := s.channelMsgExpressCli.SendChannelBroadcastMsg(ctx, bMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "broadcastUsersCommonMicInfo channelMsgExpressCli.SendChannelBroadcastMsg err: %v", err)
		return
	}
	log.InfoWithCtx(ctx, "broadcastUsersCommonMicInfo channelid:%d, uid:%d, notifyInfo:%+v", channelId, uid, notifyInfo)
}

func (s *ChannelBox) OpenCommonMic(ctx context.Context, uid, channelId, commmonBoxId uint32) error {
	isAdmin, err := s.isChannelAdmin(ctx, channelId, uid)
	if err != nil {
		return err
	}

	if !isAdmin {
		log.ErrorWithCtx(ctx, "OpenCommonMic channel %d user %d not admin", channelId, uid)
		return protocol.NewExactServerError(codes.PermissionDenied, status.ErrAccountPermissionDenied, "您已被取消管理员权限")
	}

	setRsp, err := s.channelBoxCli.SetChannelMicBoxId(ctx, pb.SetChannelMicBoxIdReq{
		ChannelId:    channelId,
		OpeUid:       uid,
		OpenMicBoxId: commmonBoxId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "OpenCommonMic channelBoxCli.SetChannelMicBoxId err: %v", err)
		return err
	}

	userInfo, err := s.accountCli.GetUserByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "accountCli.GetUserByUid err:%v, uid:d", err, uid)
		return err
	}
	notifyInfo := &channel_roleplay_logic_pb.OpenCommonMicBC{
		CommonBoxid: commmonBoxId,
		OpeUid:      uid,
		ChannelId:   channelId,
		UserName:    userInfo.GetUsername(),
		NickName:    userInfo.GetNickname(),
		Opts:        setRsp.GetOpts(),
	}
	s.broadcastUsersCommonMicInfo(ctx, uid, channelId, 0, userInfo.GetUsername(), userInfo.GetNickname(), notifyInfo)

	return nil
}

func (s *ChannelBox) GetChannelAllBoxInfo(ctx context.Context, channelId, uid, boxType uint32) ([]*channel_roleplay_logic_pb.BoxInfo, uint32, uint32, []uint32, uint32, error) {
	rsp, rpcErr := s.channelBoxCli.GetBoxInfo(ctx, pb.GetBoxInfoReq{
		OpeUid:    uid,
		Channelid: channelId,
		BoxType:   pb.BoxType(boxType),
	})
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.GetChannelAllBoxInfo channelBoxCli.GetBoxInfo channelId(%d) uid(%d) boxType(%d) err: %v",
			channelId, uid, boxType)
		return nil, 0, 0, nil, 0, rpcErr
	}

	countMap := make(map[uint32]*pb.BoxCntInfo)
	for _, count := range rsp.GetBoxInfo() {
		countMap[count.GetBoxid()] = count
	}

	list := make([]*channel_roleplay_logic_pb.BoxInfo, 0, len(rsp.GetBoxList()))
	for _, box := range rsp.GetBoxList() {
		info := &channel_roleplay_logic_pb.BoxInfo{
			Channelid:     box.GetChannelId(),
			Boxid:         box.GetBoxId(),
			BoxName:       box.GetBoxName(),
			BoxType:       channel_roleplay_logic_pb.BoxType(box.GetBoxType()),
			MicCap:        box.GetMicCap(),
			PublicMicList: box.GetPublicMicList(),
		}

		count := countMap[box.GetBoxId()]
		if count != nil {
			info.BoxUserCnt = count.GetBoxUserCnt()
		}

		list = append(list, info)
	}

	return list, rsp.GetUserBoxid(), rsp.GetMainCommonBoxid(), rsp.GetUserAudioBoxids(), rsp.GetMicTotal(), nil
}

func (s *ChannelBox) replaceChannelRoleName(ctx context.Context, channelId, uid, boxType uint32, roleName *string) error {
	var typ channelRolePb.RoleType
	switch pb.BoxType(boxType) {
	case pb.BoxType_BoxTypeRoleplay:
		typ = channelRolePb.RoleType_RoleTypeRolePlay
	case pb.BoxType_BoxTypeMelee:
		typ = channelRolePb.RoleType_RoleTypeVest
	default:
		return nil
	}

	roleInfos, err := s.roleCli.GetChannelUserRoleList(ctx, typ, channelId, []uint32{uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "roleCli.GetChannelUserRoleList err:%v, uid:%d", err, uid)
		return err
	}

	if len(roleInfos) >= 1 {
		*roleName = roleInfos[0].GetRoleName()
	}

	return nil
}

func (s *ChannelBox) auditBoxName(ctx context.Context, uid, channelId, boxId uint32, boxName string) error {
	ch, rpcErr := s.channelCli.GetChannelSimpleInfo(ctx, uid, channelId)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.auditBoxName channelCli.GetChannelSimpleInfo channelId(%d) err: %v", channelId, rpcErr)
		return rpcErr
	}

	user, rpcErr := s.accountCli.GetUserByUid(ctx, uid)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "AuditModel.auditText accountCli.GetUserByUid uid(%d) err: %v", uid, rpcErr)
		return rpcErr
	}

	auditRsp, err := s.auditCli.Text().AsyncScanText(ctx, &cybros_arbiter_v2.ScanTextReq{
		Context: &cybros_arbiter_v2.TaskContext{
			AppId: "quicksilver",
			UserInfo: &cybros_arbiter_v2.User{
				Id:       uint64(user.GetUid()),
				Alias:    user.GetAlias(),
				Phone:    user.GetPhone(),
				Nickname: user.GetNickname(),
			},
			Category:    "ROOM_SUB_CHANNEL_NAME",
			BelongObjId: ch.GetChannelViewId(),
		},
		TextData: &cybros_arbiter_v2.TextData{
			Metadata: &cybros_arbiter_v2.Metadata{DataId: fmt.Sprintf("channelbox_%d_%d", channelId, boxId)},
			Content:  boxName,
		},
		Callback: &cybros_arbiter_v2.Callback{Params: map[string]string{
			"channel_id": strconv.Itoa(int(channelId)),
			"box_id":     strconv.Itoa(int(boxId)),
			"uid":        strconv.Itoa(int(uid)),
			"box_name":   boxName,
		}},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelBox.auditBoxName auditCli.AsyncScanText err: %v", err)
		return err
	}

	log.InfoWithCtx(ctx, "ChannelBox.auditBoxName auditCli.AsyncScanText taskId: %d", auditRsp.GetTaskId())
	return nil
}
