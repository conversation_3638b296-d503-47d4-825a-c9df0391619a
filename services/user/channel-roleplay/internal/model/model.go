package model

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"time"

	"golang.52tt.com/services/user/channel-roleplay/internal/entity"

	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channelim"
	"golang.52tt.com/clients/channelmic"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPB "golang.52tt.com/protocol/app/channel"
	pushPB "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-roleplay"
	channelimPB "golang.52tt.com/protocol/services/channelim"
	cybros_arbiter_v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	notifyPB "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/user/channel-roleplay/internal/conf"
	"golang.52tt.com/services/user/channel-roleplay/internal/db"
	"golang.52tt.com/services/user/channel-roleplay/internal/utils"
)

type ChannelUserRoleModel struct {
	store db.IMongoDao
	cache db.IRedisDao

	config *conf.ServiceConfigT

	auditCli censoring_proxy.IClient
	micCli   channelmic.IClient
	pushCli  push.IClient
	chImCli  channelim.IClient
	accCli   account.IClient
	chCli    channel.IClient
}

func NewChannelUserRoleModel(
	store db.IMongoDao,
	cache db.IRedisDao,
	config *conf.ServiceConfigT,
	clients *entity.ChannelUserRoleModelClients,
) *ChannelUserRoleModel {
	return &ChannelUserRoleModel{
		store:    store,
		cache:    cache,
		auditCli: clients.AuditCli,
		micCli:   clients.MicCli,
		pushCli:  clients.PushCli,
		chImCli:  clients.ChImCli,
		accCli:   clients.AccCli,
		chCli:    clients.ChCli,
	}
}

func (m *ChannelUserRoleModel) GetChannelUserRole(ctx context.Context,
	typ, channelId, uid uint32) (*pb.UserRole, error) {
	roleList, err := m.getChannelUserRoleList(ctx, typ, channelId, []uint32{uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelUserRoleModel GetChannelUserRole err: %v", err)
		return nil, err
	}

	var role *pb.UserRole
	if len(roleList) > 0 {
		role = roleList[0]
	}

	return role, nil
}

func (m *ChannelUserRoleModel) GetChannelUserRoleList(ctx context.Context, uid, typ, channelId uint32,
	uidList []uint32) ([]*pb.UserRole, *pb.UserRole, error) {
	roleList, err := m.getChannelUserRoleList(ctx, typ, channelId, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelUserRoleModel GetChannelUserRoleList err: %v", err)
		return nil, nil, err
	}

	var role *pb.UserRole
	if uid > 0 {
		nameRecord, err := m.cache.GetRoleNameAuditRecord(ctx, typ, channelId, uid)
		if err != nil && err != redis.Nil {
			log.ErrorWithCtx(ctx, "ChannelUserRoleModel GetChannelUserRoleList err: %v", err)
			return nil, nil, err
		}

		if nameRecord != nil {
			role = &pb.UserRole{Uid: uid, RoleName: nameRecord.Content, UpdatedAt: uint32(time.Now().Unix())}
		}
	}

	return roleList, role, nil
}

func (m *ChannelUserRoleModel) GetChannelHoldMicUserRoleList(ctx context.Context,
	typ, channelId, uid uint32) ([]*pb.UserRole, error) {
	// 获取房间内的麦上用户
	micRsp, rpcErr := m.micCli.GetMicrList(ctx, channelId, 0)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "GetChannelHoldMicUserRoleList micCli.GetMicrList err: %v", rpcErr)
		return nil, rpcErr
	}

	uidList := []uint32{uid}
	for _, mic := range micRsp.GetAllMicList() {
		if mic.GetMicUid() > 0 && mic.GetMicUid() != uid {
			uidList = append(uidList, mic.GetMicUid())
		}
	}

	roleList, err := m.getChannelUserRoleList(ctx, typ, channelId, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelHoldMicUserRoleList getChannelUserRoleList err: %v", err)
		return nil, err
	}

	nameRecord, err := m.cache.GetRoleNameAuditRecord(ctx, typ, channelId, uid)
	if err != nil && err != redis.Nil {
		log.ErrorWithCtx(ctx, "GetChannelHoldMicUserRoleList cache.GetRoleNameAuditRecord err: %v", err)
		return nil, err
	}

	list := make([]*pb.UserRole, 0, len(roleList))
	var myRole *pb.UserRole

	for _, role := range roleList {
		r := &pb.UserRole{Uid: role.Uid, RoleName: role.RoleName, UpdatedAt: role.UpdatedAt}
		if uid == role.Uid {
			myRole = r
		}

		list = append(list, r)
	}

	if nameRecord != nil {
		now := uint32(time.Now().Unix())
		if myRole != nil {
			myRole.RoleName = nameRecord.Content
			myRole.UpdatedAt = now
		} else {
			list = append(list, &pb.UserRole{Uid: uid, RoleName: nameRecord.Content, UpdatedAt: now})
		}
	}

	log.DebugWithCtx(ctx, "ChannelUserRoleModel GetChannelHoldMicUserRoleList list: %v", list)
	return list, nil
}

func (m *ChannelUserRoleModel) AuditChannelUserRole(ctx context.Context, typ, channelId, uid uint32,
	roleName string) error {
	log.InfoWithCtx(ctx, "ChannelUserRoleModel AuditChannelUserRole typ %d channel %d user %d", typ, channelId, uid)

	setNum, err := m.cache.GetUserRoleSetNum(ctx, typ, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AuditChannelUserRole typ(%d) uid(%d) err: %v", typ, uid, err)
		return err
	}
	if setLimit := m.config.GetSetRoleLimit(); setNum >= setLimit {
		log.WarnWithCtx(ctx, "AuditChannelUserRole typ(%d) uid(%d) setNum(%d) >= limit(%d)", typ, uid, setNum, setLimit)
		return protocol.NewExactServerError(nil, status.ErrRoleplayRoleSetTooMany)
	}

	// 是否已有角色名称在审核中
	record, err := m.cache.GetRoleNameAuditRecord(ctx, typ, channelId, uid)
	if err != nil && err != redis.Nil {
		log.ErrorWithCtx(ctx, "AuditChannelUserRole cache.GetRoleNameAuditRecord err: %v", err)
		return err
	}

	if record != nil && time.Now().Before(time.Unix(record.Expire, 0)) {
		var errMsg string
		switch typ {
		case uint32(pb.RoleType_RoleTypeRolePlay):
			errMsg = "角色名称审核中"
		case uint32(pb.RoleType_RoleTypeVest):
			errMsg = "游戏名审核中"
		default:
			log.DebugWithCtx(ctx, "AuditChannelUserRole unsupported RoleType %d", typ)
		}

		log.InfoWithCtx(ctx, "AuditChannelUserRole in review")
		return protocol.NewExactServerError(nil, status.ErrRoleplayRoleNameAuditing, errMsg)
	}

	ch, err := m.chCli.GetChannelSimpleInfo(ctx, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AuditChannelUserRole chCli.GetChannelSimpleInfo err: %v", err)
		return err
	}

	user, err := m.accCli.GetUser(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AuditChannelUserRole accCli.GetUser err: %v", err)
		return err
	}

	// 记录要修改的角色名称
	err = m.cache.AddRoleNameAuditRecord(ctx, typ, channelId, uid, roleName, time.Now().Add(30*time.Minute).Unix())
	if err != nil {
		log.ErrorWithCtx(ctx, "AuditChannelUserRole cache.AddRoleNameAuditRecord err: %v", err)
		return err
	}

	// 角色名称送审
	auditRoleNameRsp, err := m.auditCli.Text().AsyncScanText(ctx, &cybros_arbiter_v2.ScanTextReq{
		Context: &cybros_arbiter_v2.TaskContext{
			AppId:       conf.AuditAppCode,
			Category:    utils.RoleType2AuditCategory(typ),
			BelongObjId: ch.GetChannelViewId(),
			UserInfo: &cybros_arbiter_v2.User{
				Id:       uint64(user.GetUid()),
				Alias:    user.GetAlias(),
				Nickname: user.GetNickname(),
			},
		},
		TextData: &cybros_arbiter_v2.TextData{
			Metadata: &cybros_arbiter_v2.Metadata{DataId: fmt.Sprintf("%d_%d_%d", channelId, uid, time.Now().Unix())},
			Content:  roleName,
		},
		Callback: &cybros_arbiter_v2.Callback{
			Params: map[string]string{
				"channel_id": strconv.Itoa(int(channelId)),
				"op_uid":     strconv.Itoa(int(uid)),
			},
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AuditChannelUserRole auditCli.AsyncScanText err: %v", err)
		return err
	}

	// 记录用户设置角色次数
	err = m.cache.IncUserRoleSetNum(ctx, typ, uid, m.config.GetSetRoleExpire())
	if err != nil {
		log.ErrorWithCtx(ctx, "AuditChannelUserRole IncUserRoleSetNum typ(%d) uid(%d) err: %v", typ, uid, err)
		return err
	}

	log.InfoWithCtx(ctx, "AuditChannelUserRole audit task id: %v", auditRoleNameRsp.GetTaskId())
	return nil
}

func (m *ChannelUserRoleModel) HandleRoleNameAuditResult(ctx context.Context, user *cybros_arbiter_v2.User,
	channelId, roleType uint32, suggestion cybros_arbiter_v2.Suggestion) error {

	switch suggestion {
	case cybros_arbiter_v2.Suggestion_PASS:
		// 机审/人审通过
		err := m.handleRoleNameAuditPass(ctx, user, channelId, roleType)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleRoleNameAuditResult handleRoleNameAuditPass err: %v", err)
			return err
		}
	case cybros_arbiter_v2.Suggestion_REJECT:
		// 机审/人审拒绝
		err := m.handleRoleNameAuditReject(ctx, user, channelId, roleType)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleRoleNameAuditResult handleRoleNameAuditReject err: %v", err)
			return err
		}
	case cybros_arbiter_v2.Suggestion_REVIEW:
		log.InfoWithCtx(ctx, "ChannelUserRoleModel HandleRoleNameAuditResult channel %d user %d role name need review",
			channelId, user.GetId())
	default:
		log.ErrorWithCtx(ctx, "HandleRoleNameAuditResult unsupported suggestion %d", suggestion)
	}

	return nil
}

func (m *ChannelUserRoleModel) handleRoleNameAuditPass(ctx context.Context, user *cybros_arbiter_v2.User,
	channelId, roleType uint32) error {

	uid := uint32(user.GetId())
	now := uint32(time.Now().Unix())

	record, err := m.cache.GetRoleNameAuditRecord(ctx, roleType, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleRoleNameAuditPass cache.GetRoleNameAuditRecord err: %v", err)
		return err
	}

	if record == nil {
		return nil
	}

	err = m.setChannelUserRole(ctx, roleType, channelId, uid, record.Content)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleRoleNameAuditPass setChannelUserRole err: %v", err)
		return err
	}

	err = m.cache.DelRoleNameAuditRecord(ctx, roleType, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleRoleNameAuditPass cache.DelRoleNameAuditRecord err: %v", err)
		return err
	}

	// push 变更的角色名称到房间
	err = m.pushRoleToChannel(ctx, channelId, nil, &pb.ChannelUserRoleNotify{
		Role:   &pb.UserRole{Uid: uid, RoleName: record.Content, UpdatedAt: now},
		Result: pb.ChannelUserRoleNotify_AuditResultPass,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleRoleNameAuditPass pushRoleToChannel err: %v", err)
		return err
	}

	var text string
	switch roleType {
	case uint32(pb.RoleType_RoleTypeRolePlay):
		text = fmt.Sprintf("%s角色设定为", user.GetNickname())
	case uint32(pb.RoleType_RoleTypeVest):
		text = fmt.Sprintf("%s游戏名设定为", user.GetNickname())
	default:
		log.ErrorWithCtx(ctx, "handleRoleNameAuditPass unsopported RoleType %d", roleType)
	}

	// 公屏 角色名称修改成功
	err = m.sendMsgToChannel(ctx, channelId, uint32(channelPB.ChannelMsgType_CHANNEL_ROLE_CHANGE_MSG), "",
		&pb.ChannelRoleChangeMsg{Text: text, RoleName: record.Content})
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleRoleNameAuditRecord sendMsgToChannel err: %v", err)
		return err
	}

	return nil
}

func (m *ChannelUserRoleModel) handleRoleNameAuditReject(ctx context.Context, user *cybros_arbiter_v2.User,
	channelId, roleType uint32) error {

	uid := uint32(user.GetId())
	now := uint32(time.Now().Unix())

	err := m.cache.DelRoleNameAuditRecord(ctx, roleType, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleRoleNameAuditResult handleRoleNameAuditReject err: %v", err)
		return err
	}

	role, err := m.GetChannelUserRole(ctx, roleType, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleRoleNameAuditResult GetChannelUserRole err: %v", err)
		return err
	}

	r := &pb.UserRole{Uid: uid, UpdatedAt: now}
	if role != nil {
		r.RoleName = role.RoleName
	}

	// push 变更角色名称失败, 恢复原角色名称
	err = m.pushRoleToUser(ctx, uid, &pb.ChannelUserRoleNotify{
		Role:   r,
		Result: pb.ChannelUserRoleNotify_AuditResultReject,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleRoleNameAuditResult pushRoleToUser err: %v", err)
		return err
	}

	return nil
}

func (m *ChannelUserRoleModel) HandleChannelUserHoldMic(ctx context.Context, typ, channelId, uid uint32) error {
	now := uint32(time.Now().Unix())

	role, err := m.GetChannelUserRole(ctx, typ, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleChannelUserHoldMic GetChannelUserRole err: %v", err)
		return err
	}

	// 给房间内除自己外的用户推送上麦用户角色
	if role != nil {
		r := &pb.ChannelUserRoleNotify{
			Role: &pb.UserRole{
				Uid:       role.Uid,
				RoleName:  role.RoleName,
				UpdatedAt: role.UpdatedAt,
			},
			Result: pb.ChannelUserRoleNotify_AuditResultPass,
		}

		log.DebugWithCtx(ctx, "HandleChannelUserHoldMic push channel role %v", r)

		err = m.pushRoleToChannel(ctx, channelId, []uint32{uid}, r)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelUserHoldMic pushRoleToChannel err: %v", err)
			return err
		}
	}

	nameRecord, err := m.cache.GetRoleNameAuditRecord(ctx, typ, channelId, uid)
	if err != nil && err != redis.Nil {
		log.ErrorWithCtx(ctx, "HandleChannelUserHoldMic GetRoleNameAuditRecord err: %v", err)
		return err
	}

	// 给自己推送审核中的角色
	if nameRecord != nil {
		result := pb.ChannelUserRoleNotify_AuditResultReview
		if typ == uint32(pb.RoleType_RoleTypeRolePlay) {
			result = pb.ChannelUserRoleNotify_AuditResultPass
		}

		r := &pb.ChannelUserRoleNotify{
			Role: &pb.UserRole{
				Uid:       uid,
				RoleName:  nameRecord.Content,
				UpdatedAt: now,
			},
			Result: result,
		}

		log.DebugWithCtx(ctx, "HandleChannelUserHoldMic push my role %v", r)

		err = m.pushRoleToUser(ctx, uid, r)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelUserHoldMic pushRoleToUser err: %v", err)
			return err
		}
	}

	return nil
}

func (m *ChannelUserRoleModel) pushRoleToChannel(ctx context.Context, channelId uint32,
	skipUidList []uint32, roleNotify *pb.ChannelUserRoleNotify) error {
	notify, err := m.genRoleNotify(ctx, roleNotify)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushRoleToChannel genRoleNotify err: %v", err)
		return err
	}

	err = m.pushCli.PushMulticast(ctx, uint64(channelId), fmt.Sprintf("%d@channel", channelId), skipUidList, notify)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushRoleToChannel pushCli.PushMulticast err: %v", err)
		return err
	}

	return nil
}

func (m *ChannelUserRoleModel) pushRoleToUser(ctx context.Context, uid uint32,
	roleNotify *pb.ChannelUserRoleNotify) error {
	notify, err := m.genRoleNotify(ctx, roleNotify)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelUserRoleModel pushRoleToUser")
		return err
	}

	err = m.pushCli.PushToUsers(ctx, []uint32{uid}, notify)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelUserRoleModel pushRoleToUser err: %v", err)
		return err
	}

	return nil
}

func (m *ChannelUserRoleModel) genRoleNotify(ctx context.Context,
	roleNotify *pb.ChannelUserRoleNotify) (*notifyPB.CompositiveNotification, error) {
	data, err := proto.Marshal(roleNotify)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelUserRoleModel pushRoleToChannel err: %v", err)
		return nil, err
	}

	pushMsg := &pushPB.PushMessage{
		Cmd:     uint32(pushPB.PushMessage_CHANNEL_ROLE_PUSH),
		Content: data,
	}

	payload, err := proto.Marshal(pushMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelUserRoleModel pushRoleToChannel err: %v", err)
		return nil, err
	}

	notify := &notifyPB.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: push.DefaultPolicy,
		ProxyNotification: &notifyPB.ProxyNotification{
			Type:       uint32(notifyPB.ProxyNotification_PUSH),
			Payload:    payload,
			Policy:     notifyPB.ProxyNotification_DEFAULT,
			ExpireTime: 60,
		},
	}

	return notify, nil
}

func (m *ChannelUserRoleModel) sendMsgToChannel(ctx context.Context, channelId, msgType uint32,
	content string, pbMsg proto.MessageV1) error {
	data, err := proto.Marshal(pbMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelUserRoleModel sendMsgToChannel err: %v", err)
		return err
	}

	commonMsg := &channelimPB.ChannelCommonMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         msgType,
		Content:      content,
		PbOptContent: data,
	}

	_, _, err = m.chImCli.SendCommonMessage(ctx, 0, channelId, commonMsg, true, msgType)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelUserRoleModel sendMsgToChannel err: %v", err)
		return err
	}

	return nil
}

func (m *ChannelUserRoleModel) setChannelUserRole(ctx context.Context, typ, channelId, uid uint32, name string) error {
	err := m.store.SetChannelUserRole(ctx, typ, channelId, uid, name)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelUserRoleModel setChannelUserRole err: %v", err)
		return err
	}

	ttl, err := m.cache.TTLChannelRole(ctx, typ, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelUserRoleModel setChannelUserRole err: %v", err)
		return err
	}

	if ttl > 5*time.Second {
		err = m.cache.SetChannelUserRole(ctx, typ, channelId, map[uint32]*pb.UserRole{uid: {Uid: uid, RoleName: name}})
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelUserRoleModel setChannelUserRoleName err: %v", err)
			return err
		}
	} else {
		err = m.cache.DelChannelRole(ctx, typ, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelUserRoleModel setChannelUserRoleName err: %v", err)
			return err
		}
	}

	return nil
}

func (m *ChannelUserRoleModel) getChannelUserRoleList(ctx context.Context, typ, channelId uint32,
	uidList []uint32) ([]*pb.UserRole, error) {

	const limit = 1000
	if len(uidList) == 0 {
		return nil, nil
	}
	roles, err := m.cache.GetChannelUserRole(ctx, typ, channelId, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "getChannelUserRoleList cache.GetChannelUserRole err: %v", err)
		return nil, err
	}

	if len(roles) == 0 {
		/*err = m.loadChannelRole(ctx, typ, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "getChannelUserRoleList loadChannelRole err: %v", err)
			return nil, err
		}

		roles, err = m.cache.GetChannelUserRole(ctx, typ, channelId, uidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "getChannelUserRoleList cache.GetChannelUserRole err: %v", err)
			return nil, err
		}*/

		storeRoleList, err := m.store.GetChannelUserRoleList(ctx, typ, channelId, uidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "loadChannelRole store.GetChannelRoleList err: %v", err)
			return nil, err
		}

		roles = map[uint32]*pb.UserRole{0: {Uid: 0}}
		if len(storeRoleList) > limit {
			roles[math.MaxUint32] = &pb.UserRole{Uid: math.MaxUint32}
		}

		for _, storeRole := range storeRoleList {
			roles[storeRole.Uid] = &pb.UserRole{Uid: storeRole.Uid, RoleName: storeRole.RoleName, UpdatedAt: storeRole.UpdatedAt}
		}

		err = m.cache.SetChannelUserRole(ctx, typ, channelId, roles)
		if err != nil {
			log.ErrorWithCtx(ctx, "loadChannelRole SetChannelUserRole err: %v", err)
			return nil, err
		}
	}

	list := make([]*pb.UserRole, 0, len(roles))
	for _, role := range roles {
		if role.GetUid() > 0 && role.GetUid() < math.MaxUint32 {
			list = append(list, role)
		}
	}

	return list, nil
}

func (m *ChannelUserRoleModel) SetChannelMicList(ctx context.Context, chId uint32, uidList []uint32, updateAt uint64) error {
	var err error
	if len(uidList) > 0 {
		err = m.cache.SetChannelMicList(ctx, chId, uidList, updateAt)
	} else {
		err = m.cache.DelChannelMicList(ctx, chId, updateAt)
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelMicList chId(%d) uidList(%v) err: %v", chId, uidList, err)
		return err
	}
	return nil
}

func (m *ChannelUserRoleModel) GetChannelMicList(ctx context.Context, chId uint32) ([]uint32, error) {
	uidList, err := m.cache.GetChannelMicList(ctx, chId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMicList cache.GetChannelMicList chId(%d) err: %v", chId, err)
		return nil, err
	}

	return uidList, nil
}
