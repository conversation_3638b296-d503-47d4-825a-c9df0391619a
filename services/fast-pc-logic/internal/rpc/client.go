package rpc

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/grpc/client/interceptor"
	"golang.52tt.com/protocol/web-server/mod"
	"google.golang.org/grpc"
)

type Clients struct {
	Mod mod.ModClient
}

func NewClients() (*Clients, error) {
	c := new(Clients)

	var (
		err error
		ctx = context.Background()
	)

	c.Mod, err = mod.NewClient(ctx, grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients mod client failed: %v", err)
		return nil, err
	}

	return c, nil
}
