package internal

import (
	"context"
	grpc "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"

	"gitlab.ttyuyin.com/tyr/x/log"
	fast_pc_logic "golang.52tt.com/protocol/app/fast-pc-logic"
	mod_pb "golang.52tt.com/protocol/web-server/mod"
	config "golang.52tt.com/services/fast-pc-logic/internal/config/ttconfig/fast_pc_logic"
)

// GetToolBoxEntrance 获取工具箱入口
func (s *Server) GetToolBoxEntrance(ctx context.Context, req *fast_pc_logic.GetToolBoxEntranceReq) (*fast_pc_logic.GetToolBoxEntranceResp, error) {
	out := &fast_pc_logic.GetToolBoxEntranceResp{}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetToolBoxEntrance get service info failed")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}

	out.Entrances = config.GetFastPcLogicConfig().GetToolBoxEntranceConfigByClientType(uint32(serviceInfo.ClientType))
	log.InfoWithCtx(ctx, "GetToolBoxEntrance serviceInfo: %+v, out: %+v", serviceInfo, out)

	return out, nil
}

// GetModTagList 获取模组分类列表
func (s *Server) GetModTagList(ctx context.Context, req *fast_pc_logic.GetModTagListReq) (*fast_pc_logic.GetModTagListResp, error) {
	out := &fast_pc_logic.GetModTagListResp{}
	modResp, err := s.clients.Mod.GetTagList(ctx, &mod_pb.None{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetModTagList Mod.GetTagList err: %v", err)
		return out, err
	}

	out.Tags = modResp.GetTags()
	out.QueryChatLink = config.GetFastPcLogicConfig().GetModQueryChatLink()
	log.InfoWithCtx(ctx, "GetModTagList out: %+v", out)

	return out, nil
}

// GetModList 获取模组列表
func (s *Server) GetModList(ctx context.Context, req *fast_pc_logic.GetModListReq) (*fast_pc_logic.GetModListResp, error) {
	out := &fast_pc_logic.GetModListResp{}

	modReq := &mod_pb.GetModListReq{
		Search: req.GetSearch(),
		Id:     req.GetId(),
		Tag:    req.GetTag(),
		Page:   req.GetPage(),
		Size:   req.GetSize(),
	}

	modResp, err := s.clients.Mod.GetModList(ctx, modReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetModList Mod.GetModList err: %v", err)
		return out, err
	}

	out.ModList = convertModList(modResp.GetList())
	out.Total = modResp.GetTotal()
	log.InfoWithCtx(ctx, "GetModList req: %+v, out: %+v", req, out)

	return out, nil
}

func convertModList(modList []*mod_pb.ModDetail) []*fast_pc_logic.ModDetail {
	if modList == nil {
		return nil
	}
	result := make([]*fast_pc_logic.ModDetail, 0, len(modList))
	for _, mod := range modList {
		result = append(result, convertModDetail(mod))
	}
	return result
}

func convertModDetail(modDetail *mod_pb.ModDetail) *fast_pc_logic.ModDetail {
	return &fast_pc_logic.ModDetail{
		Id:            modDetail.GetId(),
		Title:         modDetail.GetName(),
		ImgUrl:        modDetail.GetImgUrl(),
		Author:        modDetail.GetCreatedBy(),
		GameName:      modDetail.GetGame(),
		UpdateTime:    modDetail.GetUpdatedAt(),
		Desc:          modDetail.GetDesc(),
		FileUrl:       modDetail.GetFileUrl(),
		FileSize:      modDetail.GetFileSize(),
		GameShortName: modDetail.GetGameShortName(),
	}
}
