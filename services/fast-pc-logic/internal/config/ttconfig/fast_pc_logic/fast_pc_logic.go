package config

import (
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/config/ttconfig"
	pb "golang.52tt.com/protocol/app/fast-pc-logic"
	"sync/atomic"
)

type FastPcLogicConfig struct {
	ToolBoxEntranceConfig *ToolBoxEntranceConfig `json:"tool_box_entrance_config"`
}

type ToolBoxEntranceConfig struct {
	EntranceConfig   map[uint32][]*pb.ToolBoxEntrance `json:"entrance_config"`     // key: clientType(9:pc,10:pc_web) value: entrance list
	ModQueryChatLink string                           `json:"mod_query_chat_link"` // mod咨询群聊链接
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *FastPcLogicConfig) Format() error {
	return nil
}

var (
	atomicFastPcLogicConfig *atomic.Value
)

//func init() {
//	if err := InitFastPcLogicConfig(); err != nil {
//	    panic(err)
//	}
//}

// InitFastPcLogicConfig
// 可以选择外部初始化或者直接init函数初始化
func InitFastPcLogicConfig() error {
	cfg := &FastPcLogicConfig{}
	atomCfg, err := ttconfig.AtomLoad("/data/cicd-dy-conf/ser/fast-pc-logic.json", cfg)
	if nil != err {
		return err
	}
	atomicFastPcLogicConfig = atomCfg
	return nil
}

func SetFastPcLogicConfig(cfg *FastPcLogicConfig) {
	if atomicFastPcLogicConfig == nil {
		atomicFastPcLogicConfig = &atomic.Value{}
	}
	if cfg == nil {
		cfg = &FastPcLogicConfig{}
	}
	atomicFastPcLogicConfig.Store(cfg)
}

func GetFastPcLogicConfig() *FastPcLogicConfig {
	return atomicFastPcLogicConfig.Load().(*FastPcLogicConfig)
}

func (s *FastPcLogicConfig) GetToolBoxEntranceConfigByClientType(clientType uint32) []*pb.ToolBoxEntrance {
	if s == nil || s.ToolBoxEntranceConfig == nil {
		return nil
	}
	return s.ToolBoxEntranceConfig.EntranceConfig[clientType]
}

func (s *FastPcLogicConfig) GetModQueryChatLink() string {
	if s == nil || s.ToolBoxEntranceConfig == nil {
		return ""
	}
	return s.ToolBoxEntranceConfig.ModQueryChatLink
}
