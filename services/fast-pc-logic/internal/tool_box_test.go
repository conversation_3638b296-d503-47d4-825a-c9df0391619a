package internal

import (
	"context"
	"errors"
	config "golang.52tt.com/services/fast-pc-logic/internal/config/ttconfig/fast_pc_logic"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	grpc_info "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
	"golang.52tt.com/pkg/protocol"
	fast_pc_logic "golang.52tt.com/protocol/app/fast-pc-logic"
	"golang.52tt.com/protocol/web-server/mod"
	"golang.52tt.com/services/fast-pc-logic/internal/rpc"
)

func init() {
	config.SetFastPcLogicConfig(&config.FastPcLogicConfig{
		ToolBoxEntranceConfig: &config.ToolBoxEntranceConfig{
			EntranceConfig: map[uint32][]*fast_pc_logic.ToolBoxEntrance{
				protocol.ClientTypePcLFG: {{
					Name:         "mod",
					Icon:         "icon1",
					EntranceType: 1,
					TtLink:       "",
				}, {
					Name:         "攻略",
					Icon:         "icon2",
					EntranceType: 2,
					TtLink:       "guide_link",
				},
				},
				protocol.ClientTypePcLFGWeb: {{
					Name:         "攻略",
					Icon:         "icon2",
					EntranceType: 2,
					TtLink:       "guide_link",
				},
				},
			},
			ModQueryChatLink: "test-query-chat-link",
		},
	})

}

func genMockClient(ctrl *gomock.Controller) *rpc.Clients {
	mockModClient := mod.NewMockModClient(ctrl)

	return &rpc.Clients{
		Mod: mockModClient,
	}
}

func TestServer_GetToolBoxEntrance(t *testing.T) {
	type fields struct {
		clients *rpc.Clients
	}
	type args struct {
		ctx context.Context
		req *fast_pc_logic.GetToolBoxEntranceReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *fast_pc_logic.GetToolBoxEntranceResp
		wantErr bool
	}{
		{
			name: "success with service info - PC LFG",
			fields: fields{
				clients: &rpc.Clients{},
			},
			args: args{
				ctx: grpc_info.WithServiceInfo(context.Background(), &grpc_info.ServiceInfo{
					ClientType: protocol.ClientTypePcLFG,
				}),
				req: &fast_pc_logic.GetToolBoxEntranceReq{},
			},
			want: &fast_pc_logic.GetToolBoxEntranceResp{
				Entrances: []*fast_pc_logic.ToolBoxEntrance{
					{
						Name:         "mod",
						Icon:         "icon1",
						EntranceType: 1,
						TtLink:       "",
					},
					{
						Name:         "攻略",
						Icon:         "icon2",
						EntranceType: 2,
						TtLink:       "guide_link",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success with service info - PC LFG Web",
			fields: fields{
				clients: &rpc.Clients{},
			},
			args: args{
				ctx: grpc_info.WithServiceInfo(context.Background(), &grpc_info.ServiceInfo{
					ClientType: protocol.ClientTypePcLFGWeb,
				}),
				req: &fast_pc_logic.GetToolBoxEntranceReq{},
			},
			want: &fast_pc_logic.GetToolBoxEntranceResp{
				Entrances: []*fast_pc_logic.ToolBoxEntrance{
					{
						Name:         "攻略",
						Icon:         "icon2",
						EntranceType: 2,
						TtLink:       "guide_link",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "fail without service info",
			fields: fields{
				clients: &rpc.Clients{},
			},
			args: args{
				ctx: context.Background(),
				req: &fast_pc_logic.GetToolBoxEntranceReq{},
			},
			want:    &fast_pc_logic.GetToolBoxEntranceResp{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				clients: tt.fields.clients,
			}
			got, err := s.GetToolBoxEntrance(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Server.GetToolBoxEntrance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if got == nil {
					t.Errorf("Server.GetToolBoxEntrance() got = nil, want non-nil")
					return
				}
				if !reflect.DeepEqual(got.Entrances, tt.want.Entrances) {
					t.Errorf("Server.GetToolBoxEntrance() got.Entrances = %v, want %v", got.Entrances, tt.want.Entrances)
				}
			}
		})
	}
}

func TestServer_GetModTagList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := genMockClient(ctrl)
	mockModClient := clients.Mod.(*mod.MockModClient)
	type fields struct {
		clients *rpc.Clients
	}
	type args struct {
		ctx context.Context
		req *fast_pc_logic.GetModTagListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *fast_pc_logic.GetModTagListResp
		wantErr bool
		setup   func()
	}{
		{
			name: "success",
			fields: fields{
				clients: &rpc.Clients{
					Mod: mockModClient,
				},
			},
			args: args{
				ctx: context.Background(),
				req: &fast_pc_logic.GetModTagListReq{},
			},
			want: &fast_pc_logic.GetModTagListResp{
				Tags:          []string{"Action", "Adventure"},
				QueryChatLink: "test-query-chat-link",
			},
			wantErr: false,
			setup: func() {
				mockModClient.EXPECT().GetTagList(gomock.Any(), &mod.None{}).Return(&mod.GetTagListResp{
					Tags: []string{"Action", "Adventure"},
				}, nil)
			},
		},
		{
			name: "mod client error",
			fields: fields{
				clients: &rpc.Clients{
					Mod: mockModClient,
				},
			},
			args: args{
				ctx: context.Background(),
				req: &fast_pc_logic.GetModTagListReq{},
			},
			want:    &fast_pc_logic.GetModTagListResp{},
			wantErr: true,
			setup: func() {
				mockModClient.EXPECT().GetTagList(gomock.Any(), &mod.None{}).Return(nil, errors.New("mod service error"))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				clients: tt.fields.clients,
			}
			if tt.setup != nil {
				tt.setup()
			}
			got, err := s.GetModTagList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Server.GetModTagList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if got == nil {
					t.Errorf("Server.GetModTagList() got = nil, want non-nil")
					return
				}
				if !reflect.DeepEqual(got.Tags, tt.want.Tags) {
					t.Errorf("Server.GetModTagList() got.Tags = %v, want %v", got.Tags, tt.want.Tags)
				}
				if got.QueryChatLink != tt.want.QueryChatLink {
					t.Errorf("Server.GetModTagList() got.QueryChatLink = %v, want %v", got.QueryChatLink, tt.want.QueryChatLink)
				}
			}
		})
	}
}

func TestServer_GetModList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := genMockClient(ctrl)
	mockModClient := clients.Mod.(*mod.MockModClient)
	type fields struct {
		clients *rpc.Clients
	}
	type args struct {
		ctx context.Context
		req *fast_pc_logic.GetModListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *fast_pc_logic.GetModListResp
		wantErr bool
		setup   func()
	}{
		{
			name: "success",
			fields: fields{
				clients: &rpc.Clients{
					Mod: mockModClient,
				},
			},
			args: args{
				ctx: context.Background(),
				req: &fast_pc_logic.GetModListReq{
					Search: "test",
					Id:     "1",
					Tag:    "action",
					Page:   1,
					Size:   10,
				},
			},
			want: &fast_pc_logic.GetModListResp{
				ModList: []*fast_pc_logic.ModDetail{
					{
						Id:            "1",
						Title:         "Test Mod",
						ImgUrl:        "http://example.com/image.jpg",
						Author:        "Test Author",
						GameName:      "Test Game",
						UpdateTime:    1234567890,
						Desc:          "Test Description",
						FileUrl:       "http://example.com/file.zip",
						FileSize:      1536,
						GameShortName: "Test Game Short",
					},
				},
				Total: 1,
			},
			wantErr: false,
			setup: func() {
				mockModClient.EXPECT().GetModList(gomock.Any(), &mod.GetModListReq{
					Search: "test",
					Id:     "1",
					Tag:    "action",
					Page:   1,
					Size:   10,
				}).Return(&mod.GetModListResp{
					List: []*mod.ModDetail{
						{
							Id:            "1",
							Name:          "Test Mod",
							ImgUrl:        "http://example.com/image.jpg",
							CreatedBy:     "Test Author",
							Game:          "Test Game",
							UpdatedAt:     1234567890,
							Desc:          "Test Description",
							FileUrl:       "http://example.com/file.zip",
							FileSize:      1536, // 1.5MB in KB
							GameShortName: "Test Game Short",
						},
					},
					Total: 1,
				}, nil)
			},
		},
		{
			name: "empty mod list",
			fields: fields{
				clients: &rpc.Clients{
					Mod: mockModClient,
				},
			},
			args: args{
				ctx: context.Background(),
				req: &fast_pc_logic.GetModListReq{
					Search: "",
					Id:     "",
					Tag:    "",
					Page:   1,
					Size:   10,
				},
			},
			want: &fast_pc_logic.GetModListResp{
				ModList: []*fast_pc_logic.ModDetail{},
				Total:   0,
			},
			wantErr: false,
			setup: func() {
				mockModClient.EXPECT().GetModList(gomock.Any(), &mod.GetModListReq{
					Search: "",
					Id:     "",
					Tag:    "",
					Page:   1,
					Size:   10,
				}).Return(&mod.GetModListResp{
					List:  []*mod.ModDetail{},
					Total: 0,
				}, nil)
			},
		},
		{
			name: "mod client error",
			fields: fields{
				clients: &rpc.Clients{
					Mod: mockModClient,
				},
			},
			args: args{
				ctx: context.Background(),
				req: &fast_pc_logic.GetModListReq{},
			},
			want:    &fast_pc_logic.GetModListResp{},
			wantErr: true,
			setup: func() {
				mockModClient.EXPECT().GetModList(gomock.Any(), gomock.Any()).Return(nil, errors.New("mod service error"))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				clients: tt.fields.clients,
			}
			if tt.setup != nil {
				tt.setup()
			}
			got, err := s.GetModList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Server.GetModList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if got == nil {
					t.Errorf("Server.GetModList() got = nil, want non-nil")
					return
				}
				if got.Total != tt.want.Total {
					t.Errorf("Server.GetModList() got.Total = %v, want %v", got.Total, tt.want.Total)
				}
				if len(got.ModList) != len(tt.want.ModList) {
					t.Errorf("Server.GetModList() got.ModList length = %v, want %v", len(got.ModList), len(tt.want.ModList))
				}
				// 验证第一个元素的详细信息
				if len(got.ModList) > 0 && len(tt.want.ModList) > 0 {
					if got.ModList[0].FileSize != tt.want.ModList[0].FileSize {
						t.Errorf("Server.GetModList() got.ModList[0].FileSize = %v, want %v", got.ModList[0].FileSize, tt.want.ModList[0].FileSize)
					}
				}
			}
		})
	}
}

func Test_convertModList(t *testing.T) {
	type args struct {
		modList []*mod.ModDetail
	}
	tests := []struct {
		name string
		args args
		want []*fast_pc_logic.ModDetail
	}{
		{
			name: "nil input",
			args: args{
				modList: nil,
			},
			want: nil,
		},
		{
			name: "empty list",
			args: args{
				modList: []*mod.ModDetail{},
			},
			want: []*fast_pc_logic.ModDetail{},
		},
		{
			name: "single mod",
			args: args{
				modList: []*mod.ModDetail{
					{
						Id:        "1",
						Name:      "Test Mod",
						ImgUrl:    "http://example.com/image.jpg",
						CreatedBy: "Test Author",
						Game:      "Test Game",
						UpdatedAt: 1234567890,
						Desc:      "Test Description",
						FileUrl:   "http://example.com/file.zip",
						FileSize:  1536, // 1.5MB in KB
					},
				},
			},
			want: []*fast_pc_logic.ModDetail{
				{
					Id:         "1",
					Title:      "Test Mod",
					ImgUrl:     "http://example.com/image.jpg",
					Author:     "Test Author",
					GameName:   "Test Game",
					UpdateTime: 1234567890,
					Desc:       "Test Description",
					FileUrl:    "http://example.com/file.zip",
					FileSize:   1536,
				},
			},
		},
		{
			name: "multiple mods",
			args: args{
				modList: []*mod.ModDetail{
					{
						Id:        "1",
						Name:      "Test Mod 1",
						ImgUrl:    "http://example.com/image1.jpg",
						CreatedBy: "Test Author 1",
						Game:      "Test Game 1",
						UpdatedAt: 1234567890,
						Desc:      "Test Description 1",
						FileUrl:   "http://example.com/file1.zip",
						FileSize:  512, // 512KB
					},
					{
						Id:        "2",
						Name:      "Test Mod 2",
						ImgUrl:    "http://example.com/image2.jpg",
						CreatedBy: "Test Author 2",
						Game:      "Test Game 2",
						UpdatedAt: 1234567891,
						Desc:      "Test Description 2",
						FileUrl:   "http://example.com/file2.zip",
						FileSize:  2048, // 2MB
					},
				},
			},
			want: []*fast_pc_logic.ModDetail{
				{
					Id:         "1",
					Title:      "Test Mod 1",
					ImgUrl:     "http://example.com/image1.jpg",
					Author:     "Test Author 1",
					GameName:   "Test Game 1",
					UpdateTime: 1234567890,
					Desc:       "Test Description 1",
					FileUrl:    "http://example.com/file1.zip",
					FileSize:   512,
				},
				{
					Id:         "2",
					Title:      "Test Mod 2",
					ImgUrl:     "http://example.com/image2.jpg",
					Author:     "Test Author 2",
					GameName:   "Test Game 2",
					UpdateTime: 1234567891,
					Desc:       "Test Description 2",
					FileUrl:    "http://example.com/file2.zip",
					FileSize:   2048,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertModList(tt.args.modList)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertModList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertModDetail(t *testing.T) {
	type args struct {
		modDetail *mod.ModDetail
	}
	tests := []struct {
		name string
		args args
		want *fast_pc_logic.ModDetail
	}{
		{
			name: "file size in KB",
			args: args{
				modDetail: &mod.ModDetail{
					Id:        "1",
					Name:      "Test Mod",
					ImgUrl:    "http://example.com/image.jpg",
					CreatedBy: "Test Author",
					Game:      "Test Game",
					UpdatedAt: 1234567890,
					Desc:      "Test Description",
					FileUrl:   "http://example.com/file.zip",
					FileSize:  512, // 512KB
				},
			},
			want: &fast_pc_logic.ModDetail{
				Id:         "1",
				Title:      "Test Mod",
				ImgUrl:     "http://example.com/image.jpg",
				Author:     "Test Author",
				GameName:   "Test Game",
				UpdateTime: 1234567890,
				Desc:       "Test Description",
				FileUrl:    "http://example.com/file.zip",
				FileSize:   512,
			},
		},
		{
			name: "file size in MB",
			args: args{
				modDetail: &mod.ModDetail{
					Id:        "1",
					Name:      "Test Mod",
					ImgUrl:    "http://example.com/image.jpg",
					CreatedBy: "Test Author",
					Game:      "Test Game",
					UpdatedAt: 1234567890,
					Desc:      "Test Description",
					FileUrl:   "http://example.com/file.zip",
					FileSize:  1536, // 1.5MB in KB
				},
			},
			want: &fast_pc_logic.ModDetail{
				Id:         "1",
				Title:      "Test Mod",
				ImgUrl:     "http://example.com/image.jpg",
				Author:     "Test Author",
				GameName:   "Test Game",
				UpdateTime: 1234567890,
				Desc:       "Test Description",
				FileUrl:    "http://example.com/file.zip",
				FileSize:   1536,
			},
		},
		{
			name: "file size in GB",
			args: args{
				modDetail: &mod.ModDetail{
					Id:        "1",
					Name:      "Test Mod",
					ImgUrl:    "http://example.com/image.jpg",
					CreatedBy: "Test Author",
					Game:      "Test Game",
					UpdatedAt: 1234567890,
					Desc:      "Test Description",
					FileUrl:   "http://example.com/file.zip",
					FileSize:  2097152, // 2GB in KB
				},
			},
			want: &fast_pc_logic.ModDetail{
				Id:         "1",
				Title:      "Test Mod",
				ImgUrl:     "http://example.com/image.jpg",
				Author:     "Test Author",
				GameName:   "Test Game",
				UpdateTime: 1234567890,
				Desc:       "Test Description",
				FileUrl:    "http://example.com/file.zip",
				FileSize:   2097152,
			},
		},
		{
			name: "file size exactly 1MB",
			args: args{
				modDetail: &mod.ModDetail{
					Id:        "2",
					Name:      "Test Mod 2",
					ImgUrl:    "http://example.com/image2.jpg",
					CreatedBy: "Test Author 2",
					Game:      "Test Game 2",
					UpdatedAt: 1234567891,
					Desc:      "Test Description 2",
					FileUrl:   "http://example.com/file2.zip",
					FileSize:  1024, // exactly 1MB in KB
				},
			},
			want: &fast_pc_logic.ModDetail{
				Id:         "2",
				Title:      "Test Mod 2",
				ImgUrl:     "http://example.com/image2.jpg",
				Author:     "Test Author 2",
				GameName:   "Test Game 2",
				UpdateTime: 1234567891,
				Desc:       "Test Description 2",
				FileUrl:    "http://example.com/file2.zip",
				FileSize:   1024,
			},
		},
		{
			name: "file size exactly 1GB",
			args: args{
				modDetail: &mod.ModDetail{
					Id:        "3",
					Name:      "Test Mod 3",
					ImgUrl:    "http://example.com/image3.jpg",
					CreatedBy: "Test Author 3",
					Game:      "Test Game 3",
					UpdatedAt: 1234567892,
					Desc:      "Test Description 3",
					FileUrl:   "http://example.com/file3.zip",
					FileSize:  1048576, // exactly 1GB in KB
				},
			},
			want: &fast_pc_logic.ModDetail{
				Id:         "3",
				Title:      "Test Mod 3",
				ImgUrl:     "http://example.com/image3.jpg",
				Author:     "Test Author 3",
				GameName:   "Test Game 3",
				UpdateTime: 1234567892,
				Desc:       "Test Description 3",
				FileUrl:    "http://example.com/file3.zip",
				FileSize:   1048576,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertModDetail(tt.args.modDetail)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertModDetail() = %v, want %v", got, tt.want)
			}
		})
	}
}
