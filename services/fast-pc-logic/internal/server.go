package internal

import (
	"context"
	"gitlab.ttyuyin.com/tyr/x/log"
	config "golang.52tt.com/services/fast-pc-logic/internal/config/ttconfig/fast_pc_logic"
	"golang.52tt.com/services/fast-pc-logic/internal/rpc"
)

type StartConfig struct {
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	if err := config.InitFastPcLogicConfig(); err != nil {
		log.Errorf("NewServer InitFastPcLogicConfig failed: %v", err)
		return nil, err
	}

	clients, err := rpc.NewClients()
	if err != nil {
		log.Errorf("NewServer init rpc clients failed: %v", err)
		return nil, err
	}

	return &Server{
		clients: clients,
	}, nil
}

type Server struct {
	clients *rpc.Clients
}

func (s *Server) ShutDown() {

}
