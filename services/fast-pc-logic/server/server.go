package fastpclogicserver

import (
	"context"
	ttlogicfilter "gitlab.ttyuyin.com/bizFund/bizFund/pkg/foundation/grpc/logic/filter"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/startup/suit/smart"
	"golang.52tt.com/services/fast-pc-logic/internal"
	"google.golang.org/grpc"

	pb "golang.52tt.com/protocol/app/api/fast_pc"
)

const ServerName = "fast-pc-logic"

type Server struct {
	smartstartup.UnimplementedServer
	smartstartup.UnimplementedGrpcServer

	*internal.Server
	cfg internal.StartConfig
}

func (s *Server) Name() string {
	return ServerName
}

func (s *Server) RegisterGRPC(registrar *grpc.Server) error {
	pb.RegisterFastPcLogicServer(registrar, s)

	return nil
}

func (s *Server) GRPCServerInterceptor() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{
		smartstartup.LogicInterceptor(
			smartstartup.WithGrpcRequestInterceptors(ttlogicfilter.FilterInterceptor()),
		),
	}
}

func (s *Server) Init(ctx context.Context) error {
	server, err := internal.NewServer(ctx, &s.cfg)
	if err != nil {
		return err
	}
	s.Server = server
	return nil
}

func (s *Server) Close(ctx context.Context) {
	s.ShutDown()
}

func (s *Server) RegisterConfig() map[string]*smartstartup.Config {
	return map[string]*smartstartup.Config{
		ServerName: {
			Config: &s.cfg,
		},
	}
}
