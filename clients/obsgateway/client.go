package obsgateway

import (
	"context"
	//"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/common/status"
	"google.golang.org/grpc"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	pb "golang.52tt.com/protocol/services/obs-object-gateway"
)

const (
	serviceName = "obs-object-gateway"
)

type Client struct {
	client.BaseClient
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewObsObjectGatewayClient(cc)
			}, dopts...,
		),
	}, nil
}
func (c *Client) typedStub() pb.ObsObjectGatewayClient {
	return c.Stub().(pb.ObsObjectGatewayClient)
}
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

type UploadTokenReq struct {
	App        string
	Scope      string
	Expiration int32
	KeyPrefix  string
	//参见:"golang.52tt.com/services/obs/mp" mp.MediaProcess
	MediaProcess string
	CustomId     string
}
type DownloadTokenReq struct {
	App        string
	Scope      string
	Key        string
	Expiration int32
}
type DeleteTokenReq struct {
	App        string
	Scope      string
	Expiration int32
}

func (c *Client) ClaimDownloadToken(ctx context.Context, req *DownloadTokenReq) (string, protocol.ServerError) {
	return c.claimOneToken(ctx, req)
}
func (c *Client) ClaimUploadToken(ctx context.Context, req *UploadTokenReq) (string, protocol.ServerError) {
	return c.claimOneToken(ctx, req)
}
func (c *Client) claimOneToken(ctx context.Context, req interface{}) (string, protocol.ServerError) {
	tokens, err := c.ClaimToken(ctx, []interface{}{req})
	if err != nil {
		return "", err
	}
	if len(tokens) != 1 {
		return "", protocol.NewServerError(status.ErrSys)
	}
	return tokens[0], nil
}

func (c *Client) ClaimToken(ctx context.Context, reqs []interface{}) ([]string, protocol.ServerError) {
	claims := make([]*pb.TokenClaims, 0, len(reqs))
	for _, r := range reqs {
		switch xr := r.(type) {
		case *UploadTokenReq:
			if len(xr.App) <= 0 || len(xr.Scope) <= 0 || xr.Expiration <= 0 {
				return nil, protocol.NewServerError(status.ErrObsgwInvalidArgument)
			}
			claims = append(claims, &pb.TokenClaims{
				Claims: &pb.TokenClaims_UploadClaims{
					UploadClaims: &pb.UploadTokenClaims{
						App:          xr.App,
						Scope:        xr.Scope,
						Expiration:   xr.Expiration,
						KeyPrefix:    xr.KeyPrefix,
						MediaProcess: xr.MediaProcess,
						CustomId:     xr.CustomId,
					},
				},
			})
		case *DownloadTokenReq:
			if len(xr.App) <= 0 || len(xr.Scope) <= 0 || xr.Expiration <= 0 {
				return nil, protocol.NewServerError(status.ErrObsgwInvalidArgument)
			}
			claims = append(claims, &pb.TokenClaims{
				Claims: &pb.TokenClaims_DownloadClaims{
					DownloadClaims: &pb.DownloadTokenClaims{
						App:        xr.App,
						Scope:      xr.Scope,
						Key:        xr.Key,
						Expiration: xr.Expiration,
					},
				},
			})
		case *DeleteTokenReq:
			if len(xr.App) <= 0 || len(xr.Scope) <= 0 || xr.Expiration <= 0 {
				return nil, protocol.NewServerError(status.ErrObsgwInvalidArgument)
			}
			claims = append(claims, &pb.TokenClaims{
				Claims: &pb.TokenClaims_DeleteClaims{
					DeleteClaims: &pb.DeleteTokenClaims{
						App:        xr.App,
						Scope:      xr.Scope,
						Expiration: xr.Expiration,
					},
				},
			})
		default:
			return nil, protocol.NewServerError(status.ErrObsgwInvalidArgument)
		}
	}

	req := &pb.ClaimTokenReq{
		Claims: claims,
	}
	resp, err := c.typedStub().ClaimToken(ctx, req)
	if err == nil {
		tokens := make([]string, 0, len(resp.GetTokens()))
		for _, t := range resp.GetTokens() {
			tokens = append(tokens, t)
		}
		return tokens, nil
	} else {
		return nil, protocol.ToServerError(err)
	}
}

// opts: WithKey(key), WithContentType(type), WithMediaProcess(), WithCustomId()
func (c *Client) Upload(ctx context.Context, app, scope string, data []byte, opts ...Option) (retKey string, etag string, srverr protocol.ServerError) {
	if (len(app) <= 0) || (len(scope) <= 0) || (len(data) <= 0) {
		srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
		return
	}
	uopts := defaultOptions()
	for _, opt := range opts {
		err := opt(uopts)
		if err != nil {
			srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
			return
		}
	}
	req := &pb.UploadReq{
		Oid: &pb.ObjectId{
			App:   app,
			Scope: scope,
			Key:   uopts.uploadOption.key,
		},
		Data:         data,
		ContentType:  uopts.uploadOption.contentType,
		MediaProcess: uopts.uploadOption.mediaProcess,
		CustomId:     uopts.uploadOption.customId,
	}

	resp, err := c.typedStub().Upload(ctx, req)
	if err == nil {
		retKey = resp.GetKey()
		etag = resp.GetEtag()
	} else {
		srverr = protocol.ToServerError(err)
	}
	return
}

// opts: WithRangeStart(), WithRangeEnd()
func (c *Client) Download(ctx context.Context, app, scope, key string, opts ...Option) (data []byte, contentType string, exists bool, srverr protocol.ServerError) {
	if (len(app) <= 0) || (len(scope) <= 0) || (len(key) <= 0) {
		srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
		return
	}
	uopts := defaultOptions()
	for _, opt := range opts {
		if err := opt(uopts); err != nil {
			srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
			return
		}
	}
	rangeStart := uopts.downloadOption.rangeStart
	rangeEnd := uopts.downloadOption.rangeStart
	if rangeStart < 0 || rangeEnd < 0 || rangeStart > rangeEnd {
		srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
		return
	}

	req := &pb.DownloadReq{
		Oid: &pb.ObjectId{
			App:   app,
			Scope: scope,
			Key:   key,
		},
		RangeStart: rangeStart,
		RangeEnd:   rangeEnd,
	}
	resp, err := c.typedStub().Download(ctx, req, grpc.MaxCallRecvMsgSize(512*1024*1024))
	if err != nil {
		srverr = protocol.ToServerError(err)
		return
	}
	exists = resp.GetExists()
	if exists {
		data = []byte(resp.GetData())
		contentType = resp.GetContentType()
	}
	return
}
func (c *Client) Delete(ctx context.Context, app, scope, key string) (srverr protocol.ServerError) {
	if (len(app) <= 0) || (len(scope) <= 0) || (len(key) <= 0) {
		srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
		return
	}
	req := &pb.DeleteReq{
		Oid: &pb.ObjectId{
			App:   app,
			Scope: scope,
			Key:   key,
		},
	}
	_, err := c.typedStub().Delete(ctx, req)
	if err != nil {
		srverr = protocol.ToServerError(err)
		return
	}
	return
}

// opts: WithKey(key), WithContentType(type), WithReturnUrl(),
// WithReturnDownloadToken(int32) -- 私有桶要
func (c *Client) CopyUrl(ctx context.Context, srcUrl, destApp, destScope string, opts ...Option) (retKey string, etag string, retUrl string, downloadToken string, srverr protocol.ServerError) {
	if (len(srcUrl) <= 0) || (len(destApp) <= 0) || (len(destScope) <= 0) {
		srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
		return
	}
	uopts := defaultOptions()
	for _, opt := range opts {
		err := opt(uopts)
		if err != nil {
			srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
			return
		}
	}

	req := &pb.CopyReq{
		SrcUrl: srcUrl,
		DestOid: &pb.ObjectId{
			App:   destApp,
			Scope: destScope,
			Key:   uopts.uploadOption.key,
		},
		ContentType:     uopts.uploadOption.contentType,
		WithDestUrl:     uopts.returnOption.retUrl,
		TokenExpiration: uopts.tokenOption.expiration,
		NoReview:        uopts.uploadOption.noReview,
	}

	resp, err := c.typedStub().Copy(ctx, req)
	if err == nil {
		retKey = resp.GetKey()
		etag = resp.GetEtag()
		retUrl = resp.GetUrl()
		downloadToken = resp.GetToken()
	} else {
		srverr = protocol.ToServerError(err)
	}
	return
}

// options: WithTokenExpiration
func (c *Client) RedirectUrl(ctx context.Context, srcUrl string, opts ...Option) (retUrl string, srverr protocol.ServerError) {
	if len(srcUrl) <= 0 {
		srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
		return
	}
	uopts := defaultOptions()
	for _, opt := range opts {
		err := opt(uopts)
		if err != nil {
			srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
			return
		}
	}

	req := &pb.RedirectUrlReq{
		Url:        srcUrl,
		Expiration: uopts.tokenOption.expiration,
	}
	resp, err := c.typedStub().RedirectUrl(ctx, req)
	if err == nil {
		retUrl = resp.GetUrl()
	} else {
		srverr = protocol.ToServerError(err)
	}
	return
}

// opts: WithKey(key), WithContentType(type),  WithCustomId()
func (c *Client) InitMultipartUpload(ctx context.Context, app, scope string, opts ...Option) (retKey string, uploadId string, blkSize int, srverr protocol.ServerError) {
	if (len(app) <= 0) || (len(scope) <= 0) {
		srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
		return
	}
	uopts := defaultOptions()
	for _, opt := range opts {
		err := opt(uopts)
		if err != nil {
			srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
			return
		}
	}
	req := &pb.MultipartUploadInitReq{
		Oid: &pb.ObjectId{
			App:   app,
			Scope: scope,
			Key:   uopts.uploadOption.key,
		},
		ContentType: uopts.uploadOption.contentType,
		CustomId:    uopts.uploadOption.customId,
	}
	resp, err := c.typedStub().MultipartUploadInit(ctx, req)
	if err != nil {
		srverr = protocol.ToServerError(err)
		return
	}
	retKey = resp.GetKey()
	uploadId = resp.GetUploadId()
	blkSize = int(resp.GetBlksize())
	return
}
func (c *Client) UploadPart(ctx context.Context, app, scope, key string, uploadId string, partNum int32, data []byte) (etag string, srverr protocol.ServerError) {
	if (len(app) <= 0) || (len(scope) <= 0) || (len(key) <= 0) ||
		(len(uploadId) <= 0) ||
		(len(data) <= 0) {
		srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
		return
	}
	//从 1 开始, 连续，原因: ucloud 要求必须从0开始、连续，tx/qiniu要求范围是1~10000
	if partNum < 1 {
		srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
		return
	}
	req := &pb.MultipartUploadReq{
		Oid: &pb.ObjectId{
			App:   app,
			Scope: scope,
			Key:   key,
		},
		UploadId: uploadId,
		PartNum:  partNum,
		Data:     data,
	}
	resp, err := c.typedStub().MultipartUpload(ctx, req)
	if err != nil {
		srverr = protocol.ToServerError(err)
		return
	}
	etag = resp.GetEtag()
	return
}
func (c *Client) CompleteMultipartUpload(ctx context.Context, app, scope, key string, uploadId string, partNums []int32, etags []string, contentType string, mediaProcess string) (etag string, srverr protocol.ServerError) {
	if (len(app) <= 0) || (len(scope) <= 0) || (len(key) <= 0) ||
		(len(uploadId) <= 0) ||
		(len(partNums) <= 0) || (len(partNums) != len(etags)) {
		srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
		return
	}

	parts := make([]*pb.MultipartUploadCompleteReq_Part, 0)
	for i := 0; i < len(partNums); i++ {
		//从 1 开始, 连续，原因: ucloud 要求必须从0开始、连续，tx/qiniu要求范围是1~10000
		if partNums[i] < 1 {
			srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
			return
		}
		parts = append(parts, &pb.MultipartUploadCompleteReq_Part{
			PartNum: partNums[i],
			Etag:    etags[i],
		})
	}
	req := &pb.MultipartUploadCompleteReq{
		Oid: &pb.ObjectId{
			App:   app,
			Scope: scope,
			Key:   key,
		},
		UploadId:     uploadId,
		ContentType:  contentType,
		Parts:        parts,
		MediaProcess: mediaProcess,
	}
	//log.Infof("CompleteMultipartUpload, req:%+v", req)
	resp, err := c.typedStub().MultipartUploadComplete(ctx, req)
	if err != nil {
		srverr = protocol.ToServerError(err)
		return
	}
	etag = resp.GetEtag()
	return
}
func (c *Client) AbortMultipartUpload(ctx context.Context, app, scope, key string, uploadId string) (srverr protocol.ServerError) {
	if (len(app) <= 0) || (len(scope) <= 0) || (len(key) <= 0) {
		srverr = protocol.NewServerError(status.ErrObsgwInvalidArgument)
		return
	}
	req := &pb.MultipartUploadAbortReq{
		Oid: &pb.ObjectId{
			App:   app,
			Scope: scope,
			Key:   key,
		},
		UploadId: uploadId,
	}
	_, err := c.typedStub().MultipartUploadAbort(ctx, req)
	if err != nil {
		srverr = protocol.ToServerError(err)
		return
	}
	return
}
