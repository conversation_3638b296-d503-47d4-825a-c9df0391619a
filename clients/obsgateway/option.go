package obsgateway

import (
	"errors"
)

// //////////////////////////////////
// Upload
func WithKey(key string) Option {
	return func(opts *Options) error {
		opts.uploadOption.key = key
		return nil
	}
}
func WithContentType(contentType string) Option {
	return func(opts *Options) error {
		opts.uploadOption.contentType = contentType
		return nil
	}
}

func WithMediaProcess(mp string) Option {
	return func(opts *Options) error {
		opts.uploadOption.mediaProcess = mp
		return nil
	}
}

func WithCustomId(customId string) Option {
	return func(opts *Options) error {
		opts.uploadOption.customId = customId
		return nil
	}
}

// //////////////////////////////////
// Download
func WithRangeStart(v int64) Option {
	return func(opts *Options) error {
		opts.downloadOption.rangeStart = v
		return nil
	}
}
func WithRangeEnd(v int64) Option {
	return func(opts *Options) error {
		opts.downloadOption.rangeEnd = v
		return nil
	}
}

// //////////////////////////////////
// return
func WithReturnUrl() Option {
	return func(opts *Options) error {
		opts.returnOption.retUrl = true
		return nil
	}
}
func WithReturnDownloadToken(tokenExpiration int32) Option {
	return func(opts *Options) error {
		if tokenExpiration > 0 {
			opts.tokenOption.expiration = tokenExpiration
			return nil
		} else {
			return errors.New("wrong token expiration")
		}
	}
}
func WithTokenExpiration(tokenExpiration int32) Option {
	return func(opts *Options) error {
		if tokenExpiration > 0 {
			opts.tokenOption.expiration = tokenExpiration
			return nil
		} else {
			return errors.New("wrong token expiration")
		}
	}
}

func WithNoReview() Option {
	return func(opts *Options) error {
		opts.uploadOption.noReview = true
		return nil
	}
}

type Option func(*Options) error

type Options struct {
	uploadOption struct {
		key          string
		contentType  string
		mediaProcess string
		customId     string
		noReview     bool
	}
	downloadOption struct {
		rangeStart int64
		rangeEnd   int64
	}
	tokenOption struct {
		expiration int32
	}
	returnOption struct {
		retUrl bool
	}
}

func defaultOptions() *Options {
	return &Options{}
}
