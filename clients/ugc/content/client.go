/**
 * Author: Orange
 * Date: 18-11-6
 */

package content

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/log"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"google.golang.org/grpc"

	//"golang.52tt.com/pkg/foundation/grpc/load_balance"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/ugc/content"
)

const (
	serviceName = "ugc-content"

	Text  = pb.PostInfo_TEXT
	Image = pb.PostInfo_IMAGE
	Video = pb.PostInfo_VIDEO
	CMS   = pb.PostInfo_CMS

	// content status rename
	None                     = pb.ContentStatus_CONTENT_STATUS_NONE
	WaitForReview            = pb.ContentStatus_CONTENT_STATUS_UNDER_REVIEW
	PreparedButWaitForReview = pb.ContentStatus_CONTENT_STATUS_UNDER_REVIEW_AND_PREPARED
	Suspicious               = pb.ContentStatus_CONTENT_STATUS_SUSPICIOUS
	Illegal                  = pb.ContentStatus_CONTENT_STATUS_ILLEGAL
	Normal                   = pb.ContentStatus_CONTENT_STATUS_NORMAL
	Deleted                  = pb.ContentStatus_CONTENT_STATUS_DELETED
	Banned                   = pb.ContentStatus_CONTENT_STATUS_BANNED
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewUgcContentClient(cc)
		}, dopts...),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.UgcContentClient { return c.Stub().(pb.UgcContentClient) }

// -------- rpc --------

func (c *Client) BatchGetPostListById(ctx context.Context, in *pb.BatchGetPostListByIdReq,
	opts ...grpc.CallOption) ([]*pb.PostInfo, protocol.ServerError) {
	if resp, err := c.typedStub().BatchGetPostListById(ctx, in, opts...); err != nil {
		log.Errorf("BatchGetPostListById Failed: %v", err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp.GetPostList(), nil
	}

}

func (c *Client) AddPost(ctx context.Context, in *pb.AddPostReq,
	opts ...grpc.CallOption) (*pb.AddPostResp, protocol.ServerError) {
	var generalContents []*pb.GeneralContent
	for _, v := range in.GetGeneralContents() {
		if originName, ok := pb.ContentOrigin_name[int32(v.GetContentOrigin())]; ok {
			v.OriginName = originName
		}
		generalContents = append(generalContents, v)
	}
	if len(generalContents) != 0 {
		in.GeneralContents = generalContents
	}

	if resp, err := c.typedStub().AddPost(ctx, in, opts...); err != nil {
		log.Errorf("AddPost Failed: %v", err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) MarkAttachmentUploaded(ctx context.Context, in *pb.MarkAttachmentUploadedReq,
	opts ...grpc.CallOption) (*pb.MarkAttachmentUploadedResp, protocol.ServerError) {
	if resp, err := c.typedStub().MarkAttachmentUploaded(ctx, in, opts...); err != nil {
		log.Errorf("MarkAttachmentUploaded post_id: %s, comment_id: %s, key: %s, status:%d Failed: %v",
			in.GetPostId(), in.GetCommentId(), err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) UpdateAttachmentStatus(ctx context.Context, in *pb.UpdateAttachmentStatusReq,
	opts ...grpc.CallOption) (*pb.UpdateAttachmentStatusResp, protocol.ServerError) {
	if resp, err := c.typedStub().UpdateAttachmentStatus(ctx, in, opts...); err != nil {
		log.Errorf("UpdateAttachmentStatus %+v failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) UpdateAttachmentDownloadPrivacy(ctx context.Context, in *pb.UpdateAttachmentPrivacyReq,
	opts ...grpc.CallOption) (*pb.UpdateAttachmentPrivacyResp, protocol.ServerError) {
	if resp, err := c.typedStub().UpdateAttachmentPrivacy(ctx, in, opts...); err != nil {
		log.Errorf("UpdateAttachmentDownloadPrivacy %+v failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

// func (c *Client) GetVMVideoExist(ctx context.Context, in *pb.GetVMVideoExistReq,
// 	opts ...grpc.CallOption) (*pb.GetVMVideoExistResp, protocol.ServerError) {
// 	if resp, err := c.typedStub().GetVMVideoExist(ctx, in, opts...); err != nil {
// 		log.Errorf("GetVMVideoExist post_id: %s,key:%s  Failed: %v",
// 			in.GetPostId(), in.GetKey(), err)
// 		return nil, protocol.ToServerError(err)
// 	} else {
// 		return resp, nil
// 	}
// }

func (c *Client) GetPostById(ctx context.Context, postId string,
	opts ...grpc.CallOption) (*pb.PostInfo, protocol.ServerError) {
	return c.GetPostByIdWithUser(ctx, postId, 0, 0, 0, opts...)
}

func (c *Client) GetPostByIdWithUser(ctx context.Context, postId string, fromUserId uint32, marketId, clientType uint32,
	opts ...grpc.CallOption) (*pb.PostInfo, protocol.ServerError) {
	if resp, err := c.typedStub().GetPostById(ctx, &pb.GetPostByIdReq{
		PostId:        postId,
		ContentType:   pb.ContentType_ORIGIN_TEXT,
		QueryByUserId: fromUserId,
		MarketId:      marketId,
		ClientType:    clientType,
	}, opts...); err != nil {
		log.Errorf("GetPostByIdWithUser post_id: %s Failed: %v", postId, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp.GetPost(), nil
	}
}

func (c *Client) GetPostByIdFormatted(ctx context.Context, postId string,
	opts ...grpc.CallOption) (*pb.PostInfo, protocol.ServerError) {
	if resp, err := c.typedStub().GetPostById(ctx, &pb.GetPostByIdReq{PostId: postId, ContentType: pb.ContentType_FORMATTED}, opts...); err != nil {
		log.Errorf("GetPostById post_id: %s Failed: %v", postId, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp.GetPost(), nil
	}
}

func (c *Client) DelPost(ctx context.Context, postId string,
	opts ...grpc.CallOption) (*pb.DelPostResp, protocol.ServerError) {
	if resp, err := c.typedStub().DelPost(ctx, &pb.DelPostReq{PostId: postId}, opts...); err != nil {
		log.Errorf("DelPost post_id: %s Failed: %v", postId, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) BanPostById(ctx context.Context, in *pb.BanPostByIdReq,
	opts ...grpc.CallOption) (*pb.BanPostByIdResp, protocol.ServerError) {
	if resp, err := c.typedStub().BanPostById(ctx, in); err != nil {
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) ReportPostView(ctx context.Context, in *pb.ReportPostViewReq,
	opts ...grpc.CallOption) (*pb.ReportPostViewResp, protocol.ServerError) {
	if resp, err := c.typedStub().ReportPostView(ctx, in); err != nil {
		log.Errorf("ReportPostView %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}
func (c *Client) ReportPostViewV2(ctx context.Context, in *pb.ReportPostViewV2Req,
	opts ...grpc.CallOption) (*pb.ReportPostViewV2Resp, protocol.ServerError) {
	if resp, err := c.typedStub().ReportPostViewV2(ctx, in); err != nil {
		log.Errorf("ReportPostView %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) ReportPostShared(ctx context.Context, in *pb.ReportPostShareReq,
	opts ...grpc.CallOption) (*pb.ReportPostShareResp, protocol.ServerError) {
	if resp, err := c.typedStub().ReportPostShare(ctx, in); err != nil {
		log.Errorf("ReportPostShared %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

// 下掉了
// 话题的帖子查看次数上报
func (c *Client) ReportTopicViewCount(ctx context.Context, in *pb.ReportTopicViewCountReq,
	opts ...grpc.CallOption) (*pb.ReportTopicViewCountRsp, protocol.ServerError) {
	if resp, err := c.typedStub().ReportTopicViewCount(ctx, in); err != nil {
		log.Errorf("ReportTopicViewCount %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

// 话题帖子的查看次数查询
func (c *Client) GetTopicViewCount(ctx context.Context, in *pb.GetTopicViewCountReq,
	opts ...grpc.CallOption) (*pb.GetTopicViewCountRsp, protocol.ServerError) {
	if resp, err := c.typedStub().GetTopicViewCount(ctx, in); err != nil {
		log.Errorf("GetTopicViewCount %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) UpdatePostTags(ctx context.Context, in *pb.UpdatePostTagsReq,
	opts ...grpc.CallOption) (*pb.UpdatePostTagsResp, protocol.ServerError) {
	if resp, err := c.typedStub().UpdatePostTags(ctx, in); err != nil {
		log.Errorf("UpdatePostTags %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) UpdatePostPrivacyPolicy(ctx context.Context, uid uint32, postId string, policy pb.PostPrivacyPolicy,
	opts ...grpc.CallOption) protocol.ServerError {
	_, err := c.typedStub().UpdatePostPrivacyPolicy(ctx,
		&pb.UpdatePostPrivacyPolicyReq{UserId: uid, PostId: postId, Policy: policy}, opts...)

	return protocol.ToServerError(err)
}

func (c *Client) GetPostsByFilter(ctx context.Context, in *pb.GetPostsByFilterReq,
	opts ...grpc.CallOption) (*pb.GetPostsByFilterResp, protocol.ServerError) {
	if resp, err := c.typedStub().GetPostsByFilter(ctx, in); err != nil {
		log.Errorf("GetPostsByFilter %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) GetPostTags(ctx context.Context, in *pb.GetPostTagsReq,
	opts ...grpc.CallOption) (*pb.GetPostTagsResp, protocol.ServerError) {
	if resp, err := c.typedStub().GetPostTags(ctx, in); err != nil {
		log.Errorf("GetPostTags %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) BatchGetPostTags(ctx context.Context, postIdList []string,
	opts ...grpc.CallOption) (*pb.BatchGetPostTagsResp, protocol.ServerError) {
	if resp, err := c.typedStub().BatchGetPostTags(ctx, &pb.BatchGetPostTagsReq{
		PostIdList: postIdList,
	}); err != nil {
		log.Errorf("BatchGetPostTags %+v Failed: %v", postIdList, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

// -------- 评论 --------
func (c *Client) AddComment(ctx context.Context, in *pb.AddCommentReq,
	opts ...grpc.CallOption) (*pb.AddCommentResp, protocol.ServerError) {
	if resp, err := c.typedStub().AddComment(ctx, in); err != nil {
		log.Errorf("AddComment %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) BanCommentById(ctx context.Context, in *pb.BanCommentByIdReq,
	opts ...grpc.CallOption) (*pb.BanCommentByIdResp, protocol.ServerError) {
	if resp, err := c.typedStub().BanCommentById(ctx, in); err != nil {
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) GetCommentList(ctx context.Context, in *pb.GetCommentListReq,
	opts ...grpc.CallOption) (*pb.GetCommentListResp, protocol.ServerError) {
	if resp, err := c.typedStub().GetCommentList(ctx, in); err != nil {
		log.Errorf("GetCommentList %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) GetCommentById(ctx context.Context, commentId string,
	opts ...grpc.CallOption) (*pb.CommentInfo, protocol.ServerError) {
	if resp, err := c.typedStub().GetCommentById(ctx, &pb.GetCommentByIdReq{CommentId: commentId, ContentType: pb.ContentType_ORIGIN_TEXT}); err != nil {
		log.Errorf("GetCommentById %s failed: %v", commentId, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp.GetComment(), nil
	}
}

func (c *Client) BatchGetCommentByIds(ctx context.Context, commentIdList []string, marketId, clientType uint32,
	opts ...grpc.CallOption) (map[string]*pb.CommentInfo, protocol.ServerError) {
	if resp, err := c.typedStub().BatchGetCommentByIds(ctx, &pb.BatchGetCommentByIdsReq{
		CommentIdList: commentIdList, ContentType: pb.ContentType_ORIGIN_TEXT, MarketId: marketId, ClientType: clientType,
	}); err != nil {
		return nil, protocol.ToServerError(err)
	} else {
		return resp.GetCommentInfos(), nil
	}
}

func (c *Client) DelComment(ctx context.Context, in *pb.DelCommentReq,
	opts ...grpc.CallOption) (*pb.DelCommentResp, protocol.ServerError) {
	if resp, err := c.typedStub().DelComment(ctx, in); err != nil {
		log.Errorf("DelComment %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) GetCommentsByFilter(ctx context.Context, in *pb.GetCommentsByFilterReq,
	opts ...grpc.CallOption) (*pb.GetCommentsByFilterResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCommentsByFilter(ctx, in)
	return resp, protocol.ToServerError(err)
}

// -------- 点赞 --------
func (c *Client) AddAttitude(ctx context.Context, in *pb.AddAttitudeReq,
	opts ...grpc.CallOption) (*pb.AddAttitudeResp, protocol.ServerError) {
	if resp, err := c.typedStub().AddAttitude(ctx, in); err != nil {
		log.Errorf("AddAttitude %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) DelAttitude(ctx context.Context, in *pb.DelAttitudeReq,
	opts ...grpc.CallOption) (*pb.DelAttitudeResp, protocol.ServerError) {
	if resp, err := c.typedStub().DelAttitude(ctx, in); err != nil {
		log.Errorf("DelAttitude %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) GetAttitudeUserList(ctx context.Context, in *pb.GetAttitudeUserListReq,
	opts ...grpc.CallOption) (*pb.GetAttitudeUserListResp, protocol.ServerError) {
	if resp, err := c.typedStub().GetAttitudeUserList(ctx, in); err != nil {
		log.Errorf("GetAttitudeUserList %+v Failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) CheckUserAttitudeAvailable(ctx context.Context, uid uint32,
	opts ...grpc.CallOption) protocol.ServerError {
	if _, err := c.typedStub().CheckUserAttitudeAvailable(ctx, &pb.CheckUserAttitudeAvailableReq{Uid: uid}); err != nil {
		log.Errorf("CheckUserAttitudeAvailable %d Failed: %v", uid, err)
		return protocol.ToServerError(err)
	} else {
		return nil
	}
}

func (c *Client) UpdatePostSpecialLabel(ctx context.Context, in *pb.UpdatePostSpecialLabelReq,
	opts ...grpc.CallOption) (*pb.UpdatePostSpecialLabelResp, protocol.ServerError) {
	if resp, err := c.typedStub().UpdatePostSpecialLabel(ctx, in); err != nil {
		log.Errorf("UpdatePostSpecialLabel %+v failed: %v", in, err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) MarkFirstPost(ctx context.Context, in *pb.MarkFirstPostReq,
	opts ...grpc.CallOption) (*pb.MarkFirstPostResp, protocol.ServerError) {
	if resp, err := c.typedStub().MarkFirstPost(ctx, in, opts...); err != nil {
		log.Errorf("MarkFirstPost post_id: %s, user:%d Failed: %v",
			in.GetPostId(), in.GetUserId(), err)
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) SetUserNewestPost(ctx context.Context, uid uint32, postId string, expiration int64,
	opts ...grpc.CallOption) protocol.ServerError {
	in := &pb.SetUserNewestPostReq{
		Uid:        uid,
		PostId:     postId,
		Expiration: expiration,
	}
	_, err := c.typedStub().SetUserNewestPost(ctx, in, opts...)
	return protocol.ToServerError(err)
}

func (c *Client) BatchGetUserNewestPost(ctx context.Context, uids []uint32,
	opts ...grpc.CallOption) (map[uint32]string, protocol.ServerError) {
	in := &pb.BatchGetUserNewestPostReq{
		Uids: uids,
	}
	if resp, err := c.typedStub().BatchGetUserNewestPost(ctx, in, opts...); err != nil {
		return nil, protocol.ToServerError(err)
	} else {
		return resp.PostMap, nil
	}
}

func (c *Client) AppReport(ctx context.Context, in *pb.AppReportReq,
	opts ...grpc.CallOption) (*pb.AppReportResp, protocol.ServerError) {
	if resp, err := c.typedStub().AppReport(ctx, in, opts...); err != nil {
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) AddPostDirectly(ctx context.Context, in *pb.AddPostDirectlyReq,
	opts ...grpc.CallOption) (*pb.AddPostDirectlyResp, protocol.ServerError) {
	var generalContents []*pb.GeneralContent
	for _, v := range in.GetGeneralContents() {
		if originName, ok := pb.ContentOrigin_name[int32(v.GetContentOrigin())]; ok {
			v.OriginName = originName
		}
		generalContents = append(generalContents, v)
	}
	if len(generalContents) != 0 {
		in.GeneralContents = generalContents
	}
	if resp, err := c.typedStub().AddPostDirectly(ctx, in, opts...); err != nil {
		return nil, protocol.ToServerError(err)
	} else {
		return resp, nil
	}
}

func (c *Client) GetUserWonAttitudeCount(ctx context.Context, uid uint32) (uint32, protocol.ServerError) {
	r, err := c.typedStub().GetUserWonAttitudeCount(ctx, &pb.GetUserWonAttitudeCountReq{
		Uid: uid,
	})
	return r.GetTotal(), protocol.ToServerError(err)
}

func (c *Client) AddStickyContent(ctx context.Context, req *pb.AddStickyContentReq,
	opts ...grpc.CallOption) protocol.ServerError {
	_, err := c.typedStub().AddStickyContent(ctx, req, opts...)
	return protocol.ToServerError(err)
}

func (c *Client) RemoveStickyContent(ctx context.Context, req *pb.RemoveStickyContentReq,
	opts ...grpc.CallOption) protocol.ServerError {
	_, err := c.typedStub().RemoveStickyContent(ctx, req, opts...)
	return protocol.ToServerError(err)
}

func (c *Client) GetStickyContent(ctx context.Context, req *pb.GetStickyContentReq,
	opts ...grpc.CallOption) (*pb.GetStickyContentResp, protocol.ServerError) {
	r, err := c.typedStub().GetStickyContent(ctx, req, opts...)
	return r, protocol.ToServerError(err)
}

func (c *Client) BatchGetStickyPost(ctx context.Context, req *pb.BatchGetStickyPostReq,
	opts ...grpc.CallOption) (*pb.BatchGetStickyPostResp, protocol.ServerError) {
	r, err := c.typedStub().BatchGetStickyPost(ctx, req, opts...)
	return r, protocol.ToServerError(err)
}

func (c *Client) UpdateContentStickyStatus(ctx context.Context, uid uint32,
	postId, commentId string, status pb.StickyStatus,
	opts ...grpc.CallOption) protocol.ServerError {
	_, err := c.typedStub().UpdateContentStickyStatus(ctx, &pb.UpdateContentStickyStatusReq{
		UserId:    uid,
		PostId:    postId,
		CommentId: commentId,
		Status:    status,
	}, opts...)
	return protocol.ToServerError(err)
}

func (c *Client) GetStrictUserPostedCount(ctx context.Context, uid uint32, opts ...grpc.CallOption) (*pb.GetStrictUserPostedCountResp, protocol.ServerError) {
	resp, err := c.typedStub().GetStrictUserPostedCount(ctx, &pb.GetStrictUserPostedCountReq{
		UserId: uid,
	}, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateVideoUrl(ctx context.Context, postId, attachmentKey, newUrl, vmUrl string, opts ...grpc.CallOption) (*pb.UpdateVideoUrlResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateVideoUrl(ctx, &pb.UpdateVideoUrlReq{
		PostId:        postId,
		AttachmentKey: attachmentKey,
		NewUrl:        newUrl,
		VmUrl:         vmUrl,
	}, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateVideoUrlObs(ctx context.Context, postId, attachmentKey, newUrl, vmUrl, noVmUrl string, opts ...grpc.CallOption) (*pb.UpdateVideoUrlResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateVideoUrl(ctx, &pb.UpdateVideoUrlReq{
		PostId:        postId,
		AttachmentKey: attachmentKey,
		NewUrl:        newUrl,
		VmUrl:         vmUrl,
		NoVmUrl:       noVmUrl,
		IsObs:         true,
	}, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdatePostMachineAuditById(ctx context.Context, machinePostReq *pb.UpdatePostMachineAuditByIdReq) (*pb.UpdatePostMachineAuditByIdRsp, protocol.ServerError) {
	resp, err := c.typedStub().UpdatePostMachineAuditById(ctx, machinePostReq)
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetForcePostsInUserRcmdFeed(ctx context.Context, req *pb.GetForcePostsInUserRcmdFeedReq) (*pb.GetForcePostsInUserRcmdFeedResp, protocol.ServerError) {
	resp, err := c.typedStub().GetForcePostsInUserRcmdFeed(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AssociatePostWithTopic(ctx context.Context, req *pb.AssociatePostWithTopicReq) (*pb.AssociatePostWithTopicResp, protocol.ServerError) {
	resp, err := c.typedStub().AssociatePostWithTopic(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetPostRiskCheckInfo(ctx context.Context, req *pb.SetPostRiskCheckInfoReq, opts ...grpc.CallOption) (*pb.SetPostRiskCheckInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().SetPostRiskCheckInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}
