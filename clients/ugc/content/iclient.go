package content

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ugc/content"
	"google.golang.org/grpc"
)

//go:generate mockgen -destination=../../mocks/ugc/content/iclient.go -package=content golang.52tt.com/clients/ugc/content/iclient.go IClient
type IClient interface {
	client.BaseClient
	BatchGetPostListById(ctx context.Context, in *pb.BatchGetPostListByIdReq, opts ...grpc.CallOption) ([]*pb.PostInfo, protocol.ServerError)
	AddPost(ctx context.Context, in *pb.AddPostReq, opts ...grpc.CallOption) (*pb.AddPostResp, protocol.ServerError)
	MarkAttachmentUploaded(ctx context.Context, in *pb.MarkAttachmentUploadedReq, opts ...grpc.CallOption) (*pb.MarkAttachmentUploadedResp, protocol.ServerError)
	UpdateAttachmentStatus(ctx context.Context, in *pb.UpdateAttachmentStatusReq, opts ...grpc.CallOption) (*pb.UpdateAttachmentStatusResp, protocol.ServerError)
	UpdateAttachmentDownloadPrivacy(ctx context.Context, in *pb.UpdateAttachmentPrivacyReq, opts ...grpc.CallOption) (*pb.UpdateAttachmentPrivacyResp, protocol.ServerError)
	GetPostById(ctx context.Context, postId string, opts ...grpc.CallOption) (*pb.PostInfo, protocol.ServerError)
	GetPostByIdWithUser(ctx context.Context, postId string, fromUserId uint32, marketId, clientType uint32, opts ...grpc.CallOption) (*pb.PostInfo, protocol.ServerError)
	GetPostByIdFormatted(ctx context.Context, postId string, opts ...grpc.CallOption) (*pb.PostInfo, protocol.ServerError)
	DelPost(ctx context.Context, postId string, opts ...grpc.CallOption) (*pb.DelPostResp, protocol.ServerError)
	BanPostById(ctx context.Context, in *pb.BanPostByIdReq, opts ...grpc.CallOption) (*pb.BanPostByIdResp, protocol.ServerError)
	ReportPostView(ctx context.Context, in *pb.ReportPostViewReq, opts ...grpc.CallOption) (*pb.ReportPostViewResp, protocol.ServerError)
	ReportPostViewV2(ctx context.Context, in *pb.ReportPostViewV2Req, opts ...grpc.CallOption) (*pb.ReportPostViewV2Resp, protocol.ServerError)
	ReportPostShared(ctx context.Context, in *pb.ReportPostShareReq, opts ...grpc.CallOption) (*pb.ReportPostShareResp, protocol.ServerError)
	ReportTopicViewCount(ctx context.Context, in *pb.ReportTopicViewCountReq, opts ...grpc.CallOption) (*pb.ReportTopicViewCountRsp, protocol.ServerError)
	GetTopicViewCount(ctx context.Context, in *pb.GetTopicViewCountReq, opts ...grpc.CallOption) (*pb.GetTopicViewCountRsp, protocol.ServerError)
	UpdatePostTags(ctx context.Context, in *pb.UpdatePostTagsReq, opts ...grpc.CallOption) (*pb.UpdatePostTagsResp, protocol.ServerError)
	UpdatePostPrivacyPolicy(ctx context.Context, uid uint32, postId string, policy pb.PostPrivacyPolicy, opts ...grpc.CallOption) protocol.ServerError
	GetPostsByFilter(ctx context.Context, in *pb.GetPostsByFilterReq, opts ...grpc.CallOption) (*pb.GetPostsByFilterResp, protocol.ServerError)
	GetPostTags(ctx context.Context, in *pb.GetPostTagsReq, opts ...grpc.CallOption) (*pb.GetPostTagsResp, protocol.ServerError)
	BatchGetPostTags(ctx context.Context, postIdList []string, opts ...grpc.CallOption) (*pb.BatchGetPostTagsResp, protocol.ServerError)
	AddComment(ctx context.Context, in *pb.AddCommentReq, opts ...grpc.CallOption) (*pb.AddCommentResp, protocol.ServerError)
	BanCommentById(ctx context.Context, in *pb.BanCommentByIdReq, opts ...grpc.CallOption) (*pb.BanCommentByIdResp, protocol.ServerError)
	GetCommentList(ctx context.Context, in *pb.GetCommentListReq, opts ...grpc.CallOption) (*pb.GetCommentListResp, protocol.ServerError)
	GetCommentById(ctx context.Context, commentId string, opts ...grpc.CallOption) (*pb.CommentInfo, protocol.ServerError)
	BatchGetCommentByIds(ctx context.Context, commentIdList []string, marketId, clientType uint32, opts ...grpc.CallOption) (map[string]*pb.CommentInfo, protocol.ServerError)
	DelComment(ctx context.Context, in *pb.DelCommentReq, opts ...grpc.CallOption) (*pb.DelCommentResp, protocol.ServerError)
	GetCommentsByFilter(ctx context.Context, in *pb.GetCommentsByFilterReq, opts ...grpc.CallOption) (*pb.GetCommentsByFilterResp, protocol.ServerError)
	AddAttitude(ctx context.Context, in *pb.AddAttitudeReq, opts ...grpc.CallOption) (*pb.AddAttitudeResp, protocol.ServerError)
	DelAttitude(ctx context.Context, in *pb.DelAttitudeReq, opts ...grpc.CallOption) (*pb.DelAttitudeResp, protocol.ServerError)
	GetAttitudeUserList(ctx context.Context, in *pb.GetAttitudeUserListReq, opts ...grpc.CallOption) (*pb.GetAttitudeUserListResp, protocol.ServerError)
	CheckUserAttitudeAvailable(ctx context.Context, uid uint32, opts ...grpc.CallOption) protocol.ServerError
	UpdatePostSpecialLabel(ctx context.Context, in *pb.UpdatePostSpecialLabelReq, opts ...grpc.CallOption) (*pb.UpdatePostSpecialLabelResp, protocol.ServerError)
	MarkFirstPost(ctx context.Context, in *pb.MarkFirstPostReq, opts ...grpc.CallOption) (*pb.MarkFirstPostResp, protocol.ServerError)
	SetUserNewestPost(ctx context.Context, uid uint32, postId string, expiration int64, opts ...grpc.CallOption) protocol.ServerError
	BatchGetUserNewestPost(ctx context.Context, uids []uint32, opts ...grpc.CallOption) (map[uint32]string, protocol.ServerError)
	AppReport(ctx context.Context, in *pb.AppReportReq, opts ...grpc.CallOption) (*pb.AppReportResp, protocol.ServerError)
	AddPostDirectly(ctx context.Context, in *pb.AddPostDirectlyReq, opts ...grpc.CallOption) (*pb.AddPostDirectlyResp, protocol.ServerError)
	GetUserWonAttitudeCount(ctx context.Context, uid uint32) (uint32, protocol.ServerError)
	AddStickyContent(ctx context.Context, req *pb.AddStickyContentReq, opts ...grpc.CallOption) protocol.ServerError
	RemoveStickyContent(ctx context.Context, req *pb.RemoveStickyContentReq, opts ...grpc.CallOption) protocol.ServerError
	GetStickyContent(ctx context.Context, req *pb.GetStickyContentReq, opts ...grpc.CallOption) (*pb.GetStickyContentResp, protocol.ServerError)
	BatchGetStickyPost(ctx context.Context, req *pb.BatchGetStickyPostReq, opts ...grpc.CallOption) (*pb.BatchGetStickyPostResp, protocol.ServerError)
	UpdateContentStickyStatus(ctx context.Context, uid uint32, postId, commentId string, status pb.StickyStatus, opts ...grpc.CallOption) protocol.ServerError
	GetStrictUserPostedCount(ctx context.Context, uid uint32, opts ...grpc.CallOption) (*pb.GetStrictUserPostedCountResp, protocol.ServerError)
	UpdateVideoUrl(ctx context.Context, postId, attachmentKey, newUrl, vmUrl string, opts ...grpc.CallOption) (*pb.UpdateVideoUrlResp, protocol.ServerError)
	UpdateVideoUrlObs(ctx context.Context, postId, attachmentKey, newUrl, vmUrl, noVmUrl string, opts ...grpc.CallOption) (*pb.UpdateVideoUrlResp, protocol.ServerError)
	UpdatePostMachineAuditById(ctx context.Context, machinePostReq *pb.UpdatePostMachineAuditByIdReq) (*pb.UpdatePostMachineAuditByIdRsp, protocol.ServerError)
	GetForcePostsInUserRcmdFeed(ctx context.Context, req *pb.GetForcePostsInUserRcmdFeedReq) (*pb.GetForcePostsInUserRcmdFeedResp, protocol.ServerError)
	AssociatePostWithTopic(ctx context.Context, req *pb.AssociatePostWithTopicReq) (*pb.AssociatePostWithTopicResp, protocol.ServerError)
	SetPostRiskCheckInfo(ctx context.Context, in *pb.SetPostRiskCheckInfoReq, opts ...grpc.CallOption) (*pb.SetPostRiskCheckInfoResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
