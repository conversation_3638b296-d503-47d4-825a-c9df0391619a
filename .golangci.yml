version: "2"

run:
  timeout: 5m
  relative-path-mode: gomod
  issues-exit-code: 0 # 不因 lint 错误退出
  tests: false # 不检查测试文件
  modules-download-mode: readonly # 只读模式下载模块
  allow-parallel-runners: true # 允许并行运行 linter
#  allow-serial-runners: true
#  go: '1.21'
#  concurrency: 4

linters:
  exclusions: # 屏蔽特定 linter 的特定规则
    generated: lax
    rules:
      - linters:
          - staticcheck
        text: "SA1019" # Deprecated API usage
      - linters:
          - staticcheck
        text: "S1" # Code simplifications
      - linters:
          - staticcheck
        text: "ST1" # Stylistic issues
      - linters:
          - staticcheck
        text: "QF1" # Quickfixes
      - text: "Error return value of .((os\\.)?std(out|err)\\..*|.*Close|.*Flush|os\\.Remove(All)?|.*printf?|os\\.(Un)?Setenv). is not checked"
        linters:
          - errcheck
      - text: "G115: integer overflow" # 屏蔽类型转换溢出检查，因为在 tt 中使用了大量的类型转换
        linters:
          - gosec
      - text: "(G401|G402|G403|G404|G405|G406|G501|G502|G503|G504|G505|G506|G507):" # 屏蔽检测使用不安全的哈希/加密方法
        linters:
          - gosec
      # 屏蔽常见误报
      - text: "G103: Use of unsafe calls should be audited"
        linters:
          - gosec
      - text: "G204: Subprocess launch(ed with (variable|function call)|ing should be audited)"
        linters:
          - gosec
      - text: "(G301|G302): (Expect directory permissions to be 0750 or less|Expect file permissions to be 0600 or less)"
        linters:
          - gosec
      - text: "G304: Potential file inclusion via variable"
        linters:
          - gosec
      - text: "(G201|G202): SQL string (formatting|concatenation)"
        linters:
          - gosec
      - text: "(possible misuse of unsafe.Pointer|should have signature)"
        linters:
          - govet
      - text: "SA4011"
        linters:
          - staticcheck
      - text: "Error return value of .((os\\.)?std(out|err)\\..*|.*Close|.*Flush|os\\.Remove(All)?|.*printf?|os\\.(Un)?Setenv). is not checked"
        linters:
          - errcheck
  default: none # 不使用默认 linter
  disable: # 屏蔽保留 linter
    - ineffassign
    - unused
  enable:
    # bug 检查类
    - asasalint
    - asciicheck
    - bidichk
    - bodyclose
#    - contextcheck # 检查 ctx 传递中断 -- 这类报错较多
#    - durationcheck # 检查是否有两个时间间隔相乘的情况 -- 可忽略
    - errcheck
#    - errchkjson # 强制忽略 json marshal 报错 -- 可忽略
    - errorlint
#    - exhaustive # 检查 switch 语句是否包含所有枚举值
    - gocheckcompilerdirectives
#    - gochecksumtype # 仅对 sumtype 标记的类型生效 -- 未使用 sumtype
    - gosec
#    - gosmopolitan 多语言支持 -- 未使用，且不支持中文
    - govet # Go 官方 vet
    - gocritic # 代码检查工具
    #    - loggercheck # 检查日志库使用 -- 不支持自定义日志库
    - makezero
    - musttag
    - nilerr # 检查 nil 错误 -- nilnesserr 已包含
    - noctx
#    - protogetter
    - reassign
    - rowserrcheck
#    - spancheck # 检查 span 错误 -- 未使用 & too slow
#    - sqlclosecheck # 检查 sql 连接关闭 -- 检测不准确
    - staticcheck
#    - testifylint # 检查 testify 使用 -- 可忽略
#    - zerologlint # 检查 zerolog 使用 -- 未使用
    # 额外检查
    - depguard # 检查依赖包使用
    - nilnesserr # 检查 nil 错误
  settings:
    asasalint:
      exclude:
        - sqlx.In
        - log.
    gocritic:
      disable-all: true
      enabled-checks:
        - appendAssign
        - argOrder
        - badCall
        - badCond
        - badLock
        - badRegexp
        - badSorting
        - badSyncOnceFunc
        - builtinShadowDecl
        - caseOrder
        - codegenComment
#        - commentedOutCode # 检查注释掉的代码 -- 可忽略
        - deferInLoop
        - deprecatedComment
        - dupArg
        - dupBranchBody
        - dupCase
        - dupSubExpr
        - dynamicFmtString
#        - emptyDecl # 检查空声明 -- 可忽略
        - evalOrder
        - exitAfterDefer
        - externalErrorReassign
        - filepathJoin
        - flagDeref
        - flagName
        - mapKey
        - nilValReturn
        - offBy1
        - rangeAppendAll
        - regexpPattern
        - returnAfterHttpError
        - sloppyLen
        - sloppyReassign
        - sloppyTypeAssert
        - sortSlice
        - sprintfQuotedString
        - sqlQuery
        - syncMapLoadAndDelete
        - truncateCmp
        - uncheckedInlineErr
        - unnecessaryDefer
        - weakCond

    errorlint:
      errorf: false # 屏蔽 fmt.Errorf 必须使用 %w verb -- 可忽略
    depguard:
      rules:
        tt:
          deny:
            - pkg: "github.com/golang/protobuf/proto" # 此包的 Marshal 和 Unmarshal 方法不支持 gogo 产物
              desc: use deprecated proto package, use "gitlab.ttyuyin.com/tyr/x/compatible/proto" instead
issues:
  max-issues-per-linter: 0
  max-same-issues: 0
  new-from-merge-base: origin/release/tt_rel_prod # 仅检查与 release/tt_rel_prod 分支的差异
#  fix: true # 自动修复