package supervision

import (
	"context"
	"golang.52tt.com/kaihei-pkg/common"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	channelPlayTabPb "golang.52tt.com/protocol/services/channel-play-tab"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"strconv"
	"strings"
	"time"
)

// key:tabId, value:[]*channel_play_tab_pb.FastPCCategoryConfig
func (s *Supervisory) IsFastPcCategoryFilter(serviceInfo *grpc.ServiceInfo, tabId uint32, fastPCCategoryConfig map[uint32][]*channelPlayTabPb.FastPCCategoryConfig) bool {
	if protocol.IsFastPcClientType(uint32(serviceInfo.ClientType)) {
		v, ok := fastPCCategoryConfig[tabId]
		if ok && len(v) > 0 {
			return false
		}
		return true
	}
	return false
}

// GetFilterMap 配置监管
// 针对某些小游戏的玩法或类目，按照等级和注册时间，进行实名校验过滤
func (s *Supervisory) GetFilterMap(ctx context.Context, serviceInfo *grpc.ServiceInfo, channelPkg string, supConfInst *SupervisoryConf) (tabFilter map[uint32]bool, categoryFilter map[uint32]bool) {
	tabFilter = make(map[uint32]bool)
	categoryFilter = make(map[uint32]bool)
	newCtx, cancel := context.WithTimeout(ctx, 200*time.Millisecond)
	defer cancel()
	if serviceInfo.ClientType == protocol.ClientTypeANDROID && channelPkg == "" { // 安卓新版本有默认值不为空，旧版本上传为空，服务器查询用户的渠道号信息
		var err protocol.ServerError
		channelPkg, _, err = s.accountCppClient.GetUserActivateInfo(newCtx, serviceInfo.UserID)
		if err != nil {
			if err.Code() != status.ErrAccountNotExist {
				log.ErrorWithCtx(newCtx, "GetUserActivateInfo err:%v, uid:%d", err, serviceInfo.UserID)
			}
		}
	}

	resp, err := s.ChannelPlayTabClient.GetUserTabsRealNameConfig(newCtx, &channelPlayTabPb.GetUserTabsRealNameConfigReq{
		MarketId:   serviceInfo.MarketID,
		ChannelPkg: channelPkg,
		CliVer:     serviceInfo.ClientVersion,
	})
	if err != nil {
		log.ErrorWithCtx(newCtx, "GetUserTabsRealNameConfig fail, err:%s, serviceInfo:%+v", err.Error(), serviceInfo)

	} else if resp.GetConfigs().GetSwitchStatus() { // 开关状态开启为关闭展示配置的玩法
		// 敏感分类监管，开关为开需要监管处理
		if s.GetUserRegulatoryLevelByUid(newCtx, serviceInfo.UserID, supConfInst) == REGULATORY_LEVEL_SIMPLE_MINOR {
			for _, tabId := range resp.GetConfigs().GetTabIds() {
				tabFilter[tabId] = true
			}
			for _, categoryId := range resp.GetConfigs().GetCategoryIds() {
				categoryFilter[categoryId] = true
			}
		}
	}
	//log.DebugWithCtx(newCtx, "GetUserTabsRealNameConfig, serviceInfo:%+v, channelPkg:%s, config:%+v",
	//	serviceInfo, channelPkg, resp.GetConfigs())
	return
}

// 小游戏灰度策略逻辑处理函数
func (s *Supervisory) MiniGameStageStrategy(tab *tabPB.Tab, baseInfo *grpc.ServiceInfo, tabWhiteListMap map[uint32]map[uint32]bool) bool {
	if s.env == common.Staging {
		return false
	}

	if tab == nil {
		return true
	}

	needCheckOldStrategy, needFilter := s.DoNewShieldRuleFilter(tab.GetShieldRule(), baseInfo)
	if !needCheckOldStrategy {
		return needFilter || s.TabWhiteUidListControl(tab, baseInfo, tabWhiteListMap)
	}

	return RiskControlByVestBag(tab, baseInfo) || s.TabVersionControl(tab, baseInfo) || s.TabWhiteUidListControl(tab, baseInfo, tabWhiteListMap)
}

// DoNewShieldRuleFilter 新版玩法马甲包版本号白名单过滤逻辑，如果新版没有配置，则需要走一次旧版配置
func (s *Supervisory) DoNewShieldRuleFilter(shieldRules *tabPB.ShieldRuleForTab, baseInfo *grpc.ServiceInfo) (
	needCheckOldStrategy, isFiltered bool) {
	if shieldRules == nil || len(shieldRules.GetShieldRuleMap()) == 0 {
		return true, false
	}
	// 新版过滤规则有配置，不执行旧版过滤规则
	needCheckOldStrategy = false

	rule := getShieldRule(shieldRules, baseInfo)
	if rule == nil {
		isFiltered = false
		return needCheckOldStrategy, isFiltered
	}

	isFiltered = doShieldFilter(baseInfo.ClientVersion, rule)
	log.Debugf("DoNewShieldRuleFilter, baseInfo:%s, matchRule:%s, needCheckOldStrategy:%v, isFiltered:%v, tabid:%d",
		baseInfo.String(), rule.String(), needCheckOldStrategy, isFiltered, shieldRules.GetTabId())
	return needCheckOldStrategy, isFiltered
}

func doShieldFilter(clientVersion uint32, rule *tabPB.Rules) (isFiltered bool) {
	if rule.GetIsShieldAllVersion() {
		return true
	}
	var higher, lower, equal, notEqual bool

	if len(rule.GetHigherThanVersion()) > 0 {
		higherVersion := protocol.TransClientVersion(rule.GetHigherThanVersion())
		if clientVersion > higherVersion {
			higher = true
		}
	}
	if len(rule.GetLowerThanVersion()) > 0 {
		lowerVersion := protocol.TransClientVersion(rule.GetLowerThanVersion())
		if clientVersion < lowerVersion {
			lower = true
		}
	}
	if len(rule.GetEqualVersions()) > 0 {
		equal = isContainVersion(clientVersion, rule.GetEqualVersions())
	}
	if len(rule.GetNotEqualVersions()) > 0 {
		notEqual = !isContainVersion(clientVersion, rule.GetNotEqualVersions())
	}
	return higher || lower || equal || notEqual
}

func isContainVersion(clientVersion uint32, configVersions []string) bool {
	if len(configVersions) == 0 {
		return false
	}
	for _, v := range configVersions {
		if clientVersion == protocol.TransClientVersion(v) {
			return true
		}
	}
	return false
}

func getShieldRule(shieldRules *tabPB.ShieldRuleForTab, baseInfo *grpc.ServiceInfo) *tabPB.Rules {
	if shieldRules == nil {
		return nil
	}
	marketId := baseInfo.MarketID
	clientType := baseInfo.ClientType
	//platformType := protocol.NewTerminalType(baseInfo.TerminalType).Platform()

	var target uint32
	var clientTypeEnum tabPB.ShieldClientType
	//通过clientType来区分旧pc和极速版pc
	if clientType == protocol.ClientTypePcTT {
		target = uint32(tabPB.ShieldTargetEnum_SHIELD_TARGET_PC)
		clientTypeEnum = tabPB.ShieldClientType_SHIELD_CLIENT_TYPE_PC

	} else if protocol.IsFastPcClientType(uint32(clientType)) {
		target = uint32(tabPB.ShieldTargetEnum_SHIELD_TARGET_FAST_PC)
		if clientType == protocol.ClientTypePcLFG {
			clientTypeEnum = tabPB.ShieldClientType_SHIELD_CLIENT_TYPE_FAST_PC
		} else if clientType == protocol.ClientTypePcLFGWeb {
			clientTypeEnum = tabPB.ShieldClientType_SHIELD_CLIENT_TYPE_FAST_PC_WEB
		} else {
			log.Warnf("getShieldRule unSupport clientType svInfo:%s", baseInfo.String())
			return nil
		}
	} else {
		switch protocol.MarketID(marketId) {
		case protocol.TT_MarketID:
			target = uint32(tabPB.ShieldTargetEnum_SHIELD_TARGET_TT)
		case protocol.HUANYOU_MarketID:
			target = uint32(tabPB.ShieldTargetEnum_SHIELD_TARGET_HUANYOU)
		case protocol.MIC_MarketID:
			target = uint32(tabPB.ShieldTargetEnum_SHIELD_TARGET_MAIKE)
		case protocol.MIJING_MarketID:
			target = uint32(tabPB.ShieldTargetEnum_SHIELD_TARGET_MIJING)
		default:
			log.Warnf("getShieldRule unSupport marketId svInfo:%s", baseInfo.String())
			return nil
		}
		if clientType == protocol.ClientTypeIOS {
			clientTypeEnum = tabPB.ShieldClientType_SHIELD_CLIENT_TYPE_IOS
		} else if clientType == protocol.ClientTypeANDROID {
			clientTypeEnum = tabPB.ShieldClientType_SHIELD_CLIENT_TYPE_ANDROID
		} else {
			log.Warnf("getShieldRule unSupport clientType svInfo:%s", baseInfo.String())
			return nil
		}
	}
	rule := shieldRules.GetShieldRuleMap()[target]
	if rule == nil || len(rule.GetRules()) == 0 {
		return nil
	}
	for _, r := range rule.GetRules() {
		if r.GetClientType() == clientTypeEnum {
			return r
		}
	}

	return nil
}

// 版本控制过滤
func (s *Supervisory) TabVersionControl(tab *tabPB.Tab, baseInfo *grpc.ServiceInfo) (needFilter bool) {
	verDetail := make([]string, 0)
	verUpperLimitDetail := make([]string, 0)

	cliVer := genClientVersionMap(tab.GetClientVer())
	cliUpperLimitVer := genClientVersionMap(tab.GetClientVerUpperLimit())
	if len(cliVer) == 0 && len(cliUpperLimitVer) == 0 {
		//没有配置策略
		return false
	}

	if r, ok := cliVer[baseInfo.ClientType]; ok {
		verDetail = strings.Split(r, ".")
	}
	if r, ok := cliUpperLimitVer[baseInfo.ClientType]; ok {
		verUpperLimitDetail = strings.Split(r, ".")
	}
	var major, minor, patch, upperMajor, upperMinor, upperPatch int
	var upperLimitVersion, lowerLimitVersion uint32

	// 版本号优先条件
	if len(verDetail) == 3 {
		major, _ = strconv.Atoi(verDetail[0])
		minor, _ = strconv.Atoi(verDetail[1])
		patch, _ = strconv.Atoi(verDetail[2])
		lowerLimitVersion = protocol.FormatClientVersion(uint8(major), uint8(minor), uint16(patch))
	}

	if len(verUpperLimitDetail) == 3 {
		upperMajor, _ = strconv.Atoi(verUpperLimitDetail[0])
		upperMinor, _ = strconv.Atoi(verUpperLimitDetail[1])
		upperPatch, _ = strconv.Atoi(verUpperLimitDetail[2])
		upperLimitVersion = protocol.FormatClientVersion(uint8(upperMajor), uint8(upperMinor), uint16(upperPatch))
	}

	return !accordClientVersion(baseInfo.ClientVersion, upperLimitVersion, lowerLimitVersion)
}

func GetWhiteListByTabId(tabId uint32, tabWhiteListMap map[uint32]map[uint32]bool) map[uint32]bool {
	res := make(map[uint32]bool)
	if tabWhiteListMap == nil {
		return res
	}
	if uidMap, ok := tabWhiteListMap[tabId]; ok {
		res = uidMap
	}
	return res
}

// 白名单过滤
func (s *Supervisory) TabWhiteUidListControl(tab *tabPB.Tab, baseInfo *grpc.ServiceInfo, tabWhiteListMap map[uint32]map[uint32]bool) (needFilter bool) {
	uidTailNum := make(map[string]bool, 0)
	uidWhiteList := make(map[string]bool, 0)
	tabBatchWhiteUidMap := GetWhiteListByTabId(tab.GetId(), tabWhiteListMap)

	if len(tab.GetUidTailNum()) > 0 {
		tailNum := strings.Split(tab.GetUidTailNum(), ",")
		for _, n := range tailNum {
			uidTailNum[n] = true
		}
	}
	if len(tab.GetUidWhiteList()) > 0 {
		whiteList := strings.Split(tab.GetUidWhiteList(), ",")
		for _, l := range whiteList {
			uidWhiteList[l] = true
		}
	}
	if len(uidTailNum) == 0 && len(uidWhiteList) == 0 && len(tabBatchWhiteUidMap) == 0 {
		//没有配置白名单
		return false
	}

	// 求用户id尾号
	uidStr := strconv.Itoa(int(baseInfo.UserID))
	tailNum := uidStr[len(uidStr)-1:]

	if _, ok := uidTailNum[tailNum]; ok {
		return false
	}
	if _, ok := uidWhiteList[uidStr]; ok {
		return false
	}
	if tabBatchWhiteUidMap[baseInfo.UserID] {
		return false
	}

	return true
}

func accordClientVersion(clientVersion, upperLimitVersion, lowerLimitVersion uint32) bool {
	lowerRes := false
	upperRes := false
	if lowerLimitVersion == 0 || clientVersion >= lowerLimitVersion {
		lowerRes = true
	}
	if upperLimitVersion == 0 || clientVersion <= upperLimitVersion {
		upperRes = true
	}
	return lowerRes && upperRes
}

func genClientVersionMap(clientVersion string) (cliVerMap map[uint16]string) {
	cliVerMap = make(map[uint16]string, 0)
	if len(clientVersion) > 0 {
		cliVerSpl := strings.Split(clientVersion, ",")
		for _, v := range cliVerSpl {
			t := strings.Split(v, "-")
			switch t[0] {
			case "ios":
				cliVerMap[protocol.ClientTypeIOS] = t[1]
			case "android":
				cliVerMap[protocol.ClientTypeANDROID] = t[1]
			case "pc":
				cliVerMap[protocol.ClientTypePcTT] = t[1]
			default:
				continue
			}

		}
	}
	return cliVerMap
}

// RiskControlByVestBag 按马甲包风控
func RiskControlByVestBag(tab *tabPB.Tab, baseInfo *grpc.ServiceInfo) bool {
	if len(tab.GetShieldMarketIds()) == 0 {
		return false
	}
	for _, u := range tab.GetShieldMarketIds() {
		if baseInfo.MarketID == u {
			return true
		}
	}
	return false
}

// 旧首页对tab的过滤策略集合,true则过滤
// 旧首页音乐玩法通过手动配置roomLabel字段，使音乐的玩法也展示在首页筛选器中
func (s *Supervisory) GamePageFilterStrategy(tabInfo *tabPB.Tab, serviceInfo *grpc.ServiceInfo, tabWhiteListMap map[uint32]map[uint32]bool) bool {
	return s.MiniGameStageStrategy(tabInfo, serviceInfo, tabWhiteListMap) ||
		(tabInfo.HomePageType != tabPB.HomePageType_HomePageTypeGAME && tabInfo.RoomLabel == "")
}

// 旧首页首页二级筛选器对tab的过滤策略集合,true则过滤
func (s *Supervisory) GamePageSecondaryFilterStrategy(tabInfo *tabPB.Tab, serviceInfo *grpc.ServiceInfo, tabWhiteListMap map[uint32]map[uint32]bool) bool {
	return s.MiniGameStageStrategy(tabInfo, serviceInfo, tabWhiteListMap)
}

// 根据动态配置文件判断tab是否需要实名才能玩
/*func (s *Supervisory) CheckTabRealNameState(tabId uint32) bool {
	return conf.ChannelPlayLogicConfig.IsOnlyRealNamePlayTab(tabId)
}*/

// 新首页一级筛选器对tab的过滤策略集合,true则过滤
// 新首页不展示音乐tab，只配置音乐筛选项具体过滤逻辑由音乐控制
func (s *Supervisory) NewHomePageFilterStrategy(tabInfo *tabPB.Tab, serviceInfo *grpc.ServiceInfo, tabWhiteListMap map[uint32]map[uint32]bool) bool {
	return s.MiniGameStageStrategy(tabInfo, serviceInfo, tabWhiteListMap) || tabInfo.HomePageType != tabPB.HomePageType_HomePageTypeGAME
}
func PlatformTypeNotMatch(platformType tabPB.PlatformType, userPlatform protocol.Platform) bool {
	return !(platformType == tabPB.PlatformType_ALL ||
		(platformType == tabPB.PlatformType_ANDROID_IOS && userPlatform == protocol.MOBILE) ||
		(platformType == tabPB.PlatformType_PC && userPlatform == protocol.PC))
}
