// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/kaihei-pkg/aigc/msg (interfaces: Sender)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
)

// MockSender is a mock of Sender interface.
type MockSender struct {
	ctrl     *gomock.Controller
	recorder *MockSenderMockRecorder
}

// MockSenderMockRecorder is the mock recorder for MockSender.
type MockSenderMockRecorder struct {
	mock *MockSender
}

// NewMockSender creates a new mock instance.
func NewMockSender(ctrl *gomock.Controller) *MockSender {
	mock := &MockSender{ctrl: ctrl}
	mock.recorder = &MockSenderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSender) EXPECT() *MockSenderMockRecorder {
	return m.recorder
}

// SendSingleMsg mocks base method.
func (m *MockSender) SendSingleMsg(arg0 context.Context, arg1, arg2 uint32, arg3 *chat_bot.SendingMsg, arg4 *chat_bot.SendOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendSingleMsg", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendSingleMsg indicates an expected call of SendSingleMsg.
func (mr *MockSenderMockRecorder) SendSingleMsg(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSingleMsg", reflect.TypeOf((*MockSender)(nil).SendSingleMsg), arg0, arg1, arg2, arg3, arg4)
}
