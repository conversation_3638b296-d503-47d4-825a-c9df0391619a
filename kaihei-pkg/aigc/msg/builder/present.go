package builder

import (
	"encoding/json"
	"fmt"
	"time"

	"golang.52tt.com/kaihei-pkg/aigc/msg/id"
	web_im_logic "golang.52tt.com/protocol/app/web-im-logic"
	aigc_ext_content "golang.52tt.com/protocol/services/aigc/aigc-ext-content"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
)

// BuildUserPresentMsg 构造用户送礼消息
func BuildUserPresentMsg(presentConfig *userpresent_go.PresentBaseConfig) *chat_bot.SendingMsg {
	var priceTypeText string
	switch userpresent_go.PresentPriceType(presentConfig.GetPriceType()) {
	case userpresent_go.PresentPriceType_PRESENT_PRICE_RED_DIAMOND:
		priceTypeText = "红钻"
	case userpresent_go.PresentPriceType_PRESENT_PRICE_TBEAN:
		priceTypeText = "T豆"
	}

	extMsg := &aigc_ext_content.UserPresentMsg{
		Source: "user",

		Icon:  presentConfig.GetIconUrl(),
		Title: fmt.Sprintf("送出%s", presentConfig.GetName()),
		Desc:  fmt.Sprintf("价值%d%s", presentConfig.GetPrice(), priceTypeText),
	}

	ext, _ := json.Marshal(extMsg)

	msg := &chat_bot.ImMsg{
		MsgId:  id.SingleMsgID(),
		SentAt: time.Now().UnixMilli(),

		ImCmdType:  uint32(web_im_logic.ImCmdType_IM_CMD_TYPE_USER_PRESENT),
		ImBusiType: uint32(web_im_logic.ImBusiType_IM_BUSI_TYPE_MULTI_ROLE),

		Ext:     ext,
		Content: fmt.Sprintf("送出礼物[%s]", presentConfig.GetName()),
	}

	return &chat_bot.SendingMsg{
		Msg: msg,

		SegIdx: 0,
		SegCnt: 1,
	}
}

// BuildSentenceGiftMsg 构造句数礼物消息
func BuildSentenceGiftMsg(giverName string, num uint32) *chat_bot.SendingMsg {
	extMsg := &aigc_ext_content.AIGiftMsg{
		Type: uint32(aigc_ext_content.AIGiftMsg_AI_GIFT_TYPE_SENTENCE_NUMBER),
		Name: "句数",
		Num:  num,
		Desc: "快去和TA畅聊心事吧",
	}

	ext, _ := json.Marshal(extMsg)

	msg := &chat_bot.ImMsg{
		MsgId: id.SingleMsgID(),

		SentAt: time.Now().UnixMilli(),

		ImCmdType:  uint32(web_im_logic.ImCmdType_IM_CMD_TYPE_AI_PRESENT),
		ImBusiType: uint32(web_im_logic.ImBusiType_IM_BUSI_TYPE_MULTI_ROLE),

		Ext:     ext,
		Content: fmt.Sprintf("[礼物]收到来自%s的礼物", giverName),
	}

	return &chat_bot.SendingMsg{
		Msg: msg,

		SegIdx: 0,
		SegCnt: 1,
	}
}

// BuildChatBackgroundGiftMsg 构造聊天背景礼物消息
func BuildChatBackgroundGiftMsg(giverName string, bgName, bgUrl string) *chat_bot.SendingMsg {
	extMsg := &aigc_ext_content.AIGiftMsg{
		Type: uint32(aigc_ext_content.AIGiftMsg_AI_GIFT_TYPE_CHAT_BACKGROUND),
		Icon: bgUrl,
		Name: bgName,
		Desc: "快去使用看看效果吧",
	}

	ext, _ := json.Marshal(extMsg)

	msg := &chat_bot.ImMsg{
		MsgId:  id.SingleMsgID(),
		SentAt: time.Now().UnixMilli(),

		ImCmdType:  uint32(web_im_logic.ImCmdType_IM_CMD_TYPE_AI_PRESENT),
		ImBusiType: uint32(web_im_logic.ImBusiType_IM_BUSI_TYPE_MULTI_ROLE),

		Ext:     ext,
		Content: fmt.Sprintf("[礼物]收到来自%s的礼物", giverName),
	}

	return &chat_bot.SendingMsg{
		Msg: msg,

		SegIdx: 0,
		SegCnt: 1,
	}
}

// BuildUserPresentGuideMsg 构造用户送礼引导消息
func BuildUserPresentGuideMsg(content string) *chat_bot.SendingMsg {
	extMsg := &aigc_ext_content.UserPresentGuideMsg{
		Content: content,
	}
	ext, _ := json.Marshal(extMsg)
	msg := &chat_bot.ImMsg{
		MsgId:      id.SingleMsgID(),
		SentAt:     time.Now().UnixMilli(),
		ImCmdType:  uint32(web_im_logic.ImCmdType_IM_CMD_TYPE_USER_PRESENT_GUIDE),
		ImBusiType: uint32(web_im_logic.ImBusiType_IM_BUSI_TYPE_MULTI_ROLE),
		Ext:        ext,
		Content:    content,
	}
	return &chat_bot.SendingMsg{
		Msg:    msg,
		SegIdx: 0,
		SegCnt: 1,
	}
}
