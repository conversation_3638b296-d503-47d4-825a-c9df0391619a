package sender

import (
	"context"

	"gitlab.ttyuyin.com/tt-infra/middleware/event"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/kaihei-pkg/aigc/internal/config"
	"golang.52tt.com/kaihei-pkg/aigc/msg"
)

type sender struct {
	singleMsgPublisher publisher.Publisher
}

func New(ctx context.Context) (msg.Sender, error) {
	const (
		singleMsgPublisherName = "send_single_msg"
	)

	option := config.GetAigcSdkConfig().SingleMsgPublisherOption
	factory, err := event.NewEventFactory(&event.Options{
		Publisher: map[string]*publisher.Options{
			singleMsgPublisherName: &option,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "New NewEventFactory option(%+v) err: %v", option, err)
		return nil, err
	}

	singleMsgPublisher, err := factory.NewSyncPublisher(singleMsgPublisherName)
	if err != nil {
		log.ErrorWithCtx(ctx, "New NewSyncPublisher name(%s) option(%+v) err: %v", singleMsgPublisherName, option, err)
		return nil, err
	}

	return &sender{
		singleMsgPublisher: singleMsgPublisher,
	}, nil
}
