package sender

import (
	"context"
	"strconv"

	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/kaihei-pkg/aigc/internal/config"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
)

// SendSingleMsg partner给用户发送消息
func (s *sender) SendSingleMsg(ctx context.Context, uid, partnerId uint32, msg *chat_bot.SendingMsg, opt *chat_bot.SendOption) error {
	ev := &chat_bot.SingleMsgSendEvent{
		Uid:       uid,
		PartnerId: partnerId,

		SendingMsg: msg,

		Opt: opt,
	}
	bin, err := proto.Marshal(ev)
	if err != nil {
		return err
	}

	key := publisher.StringEncoder(strconv.FormatUint(uint64(partnerId), 10))
	topic := config.GetAigcSdkConfig().SingleMsgPublisherOption.Topics[0]

	pubMsg := &publisher.ProducerMessage{
		Topic: topic,

		Key:   key,
		Value: publisher.ByteEncoder(bin),
	}
	if result := s.singleMsgPublisher.Publish(ctx, pubMsg); result.Err != nil {
		return result.Err
	}

	log.InfoWithCtx(ctx, "SendSingleMsg Publish ev(%+v) finished", ev)
	return nil
}
