package receiver

import (
	"context"

	"gitlab.ttyuyin.com/tt-infra/middleware/event"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	"gitlab.ttyuyin.com/tyr/x/log"

	"golang.52tt.com/kaihei-pkg/aigc/internal/config"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
)

type SingleMsgReceiver func(ctx context.Context, event *chat_bot.AIMsgEvent) (bool, error)

func RecvSingleMsg(ctx context.Context, consumerGroup string, receiver SingleMsgReceiver) error {
	const (
		singleMsgSubscriberName = "recv_single_msg"
	)

	option := config.GetAigcSdkConfig().SingleMsgSubscriberOption
	option.GroupID = consumerGroup

	factory, err := event.NewEventFactory(&event.Options{
		Subscriber: map[string]*subscriber.Options{
			singleMsgSubscriberName: &option,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "RecvSingleMsg NewEventFactory option(%+v) err: %v", option, err)
		return err
	}

	if _, err := factory.NewSubscriber(singleMsgSubscriberName, nil, subscriber.ProcessorContextFunc(func(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
		var ev chat_bot.AIMsgEvent
		if err := proto.Unmarshal(msg.Value, &ev); err != nil {
			return err, false
		}

		retry, err := receiver(ctx, &ev)
		return err, retry
	})); err != nil {
		log.ErrorWithCtx(ctx, "RecvSingleMsg NewSubscriber name(%s) err: %v", singleMsgSubscriberName, err)
		return err
	}

	return nil
}
