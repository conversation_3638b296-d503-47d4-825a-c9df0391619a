package config

import (
	"sync/atomic"

	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/config/ttconfig"
	"gitlab.ttyuyin.com/tyr/x/log"
)

type AigcSdkConfig struct {
	SingleMsgPublisherOption  publisher.Options  `json:"single_msg_publisher_option"`
	SingleMsgSubscriberOption subscriber.Options `json:"single_msg_subscriber_option"`
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *AigcSdkConfig) Format() error {
	log.Infof("AigcSdkConfig Format: %+v", s)
	return nil
}

var (
	atomicAigcSdkConfig *atomic.Value
)

func init() {
	if err := InitAigcSdkConfig(); err != nil {
		panic(err)
	}
}

// InitAigcSdkConfig
// 可以选择外部初始化或者直接init函数初始化
func InitAigcSdkConfig() error {
	var (
		err error
		cfg AigcSdkConfig
	)
	atomicAigcSdkConfig, err = ttconfig.AtomLoad("/data/cicd-dy-conf/prj/aigc-sdk.json", &cfg)
	return err
}

func GetAigcSdkConfig() *AigcSdkConfig {
	return atomicAigcSdkConfig.Load().(*AigcSdkConfig)
}
