#!/bin/bash

run() {
    echo "+ $*"
    "$@"
}

export GOROOT=/usr/local/go
export GOPATH=/home/<USER>/go
#export GOPRIVATE="*.ttyuyin.com"
#export GONOPROXY="*.ttyuyin.com"
export GOPROXY="https://goproxy.ttyuyin.com,https://goproxy.cn,direct"
export GONOSUMDB="*.ttyuyin.com"
export GO111MODULE=on
export PATH=$PATH:$GOROOT/bin:$GOPATH/bin:/usr/local/bin:/usr/bin

go env GOROOT
go env GOPATH
go env GOPROXY
echo ""

golangci-lint version
golangci-lint config path
echo ""

echo "git fetch origin $CI_COMMIT_BRANCH"
git fetch origin $CI_COMMIT_BRANCH > /dev/null
echo "git fetch origin release/tt_rel_prod"
git fetch origin release/tt_rel_prod > /dev/null

# 获取当前分支和 release/tt_rel_prod 分支的共同祖先提交
CHANGED_FILES=$(git diff --diff-filter=d --name-only $(git merge-base origin/$CI_COMMIT_BRANCH origin/release/tt_rel_prod) $CI_COMMIT_SHA | grep '\.go$')
#CHANGED_FILES=$(git diff --diff-filter=d --name-only $CI_COMMIT_BEFORE_SHA $CI_COMMIT_SHA | grep '\.go$')

[ -z "$CHANGED_FILES" ] && { echo ">> ✅ 无代码改动"; exit 0; }

CHANGED_DIRS=$(echo "$CHANGED_FILES" | xargs -n1 dirname | sort -u)

run go mod tidy

cfg=".golangci.yml"
if [ -f ~/.golangci.yml ]; then
    echo ">> 使用 runner 的 golangci-lint 配置文件"
    cfg="/home/<USER>/.golangci.yml"
fi

prog="golangci-lint"
if [ -f /home/<USER>/golangci-lint ]; then
    echo ">> 使用 runner 的 cgolangci-lint 程序"
    prog="/home/<USER>/golangci-lint"
fi
if [ -f /home/<USER>/custom-gcl ]; then
    echo ">> 使用 runner 的 custom-gcl 程序"
    prog="/home/<USER>/custom-gcl"
fi

FAILED=false
LINT_MESSAGES=""

for dir in $CHANGED_DIRS; do
    if find "$dir" -maxdepth 1 -name '*.go' | grep -q .; then
        OUTPUT=$(run ${prog} -c ${cfg} run "${dir}" 2>&1)
        echo "$OUTPUT"

        MESSAGE=$(echo "$OUTPUT" | grep -E '^[^ ]+\.go:[0-9]+')
        if [ "$MESSAGE" ];
        then
          FAILED=true
          if [ -z "$LINT_MESSAGES" ]; then
              LINT_MESSAGES=${MESSAGE}
          else
              LINT_MESSAGES=${LINT_MESSAGES}"\n"${MESSAGE}
          fi
        fi
    fi
done

if [ "$FAILED" == "true" ]
then
    echo ">> ❌ golangci-lint 检测不通过"

    LINT_JSON=$(jq -Rn --arg msg "$LINT_MESSAGES" '$msg' | sed 's/^"//;s/"$//')

    curl -X POST -H "Content-Type: application/json" \
      -d "{
        \"msg_type\": \"interactive\",
        \"card\": {
          \"type\": \"template\",
          \"data\": {
            \"template_id\": \"AAqzpkYaQpyDk\",
            \"template_version_name\": \"1.0.0\",
            \"template_variable\": {
              \"GITLAB_USER_NAME\": \"${GITLAB_USER_NAME}\",
              \"GITLAB_USER_EMAIL\": \"${GITLAB_USER_EMAIL}\",
              \"CI_PROJECT_NAME\": \"${CI_PROJECT_NAME}\",
              \"CI_COMMIT_BRANCH\": \"${CI_COMMIT_BRANCH}\",
              \"CI_JOB_URL\": {\"url\": \"${CI_JOB_URL}\"},
              \"LINT_MESSAGE\": \"${LINT_JSON}\"
            }
          }
        }
      }" \
      https://open.feishu.cn/open-apis/bot/v2/hook/f82a96cd-e691-494d-96e1-e3cb7f6ee7f5 > /dev/null
fi