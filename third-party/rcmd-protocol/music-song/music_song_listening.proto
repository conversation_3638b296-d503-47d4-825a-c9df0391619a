syntax="proto3";

package rcmd.music_song;

option go_package = "golang.52tt.com/protocol/services/rcmd/music_song";

service MusicSong {
  //挂房听歌歌单推荐
  rpc GetListenSongOrder(ListenSongOrderReq) returns(ListenSongOrderRsp);
}

message ListenSongOrderReq {
  uint32 channel_id = 1;
  uint32 limit = 2;
  uint32 uid = 3; //房主UID
}

message SongOrderItem {
  string name = 1;        // 歌曲名
  string author = 2;      // 作者
  uint32 owner_uid = 3;         // 上传者id
  string owner_account = 4;     // 上传者account
  string owner_nickname = 5;   // 上传者名字
  uint32 music_type = 6; //音乐类型 1原唱、2伴奏
  string file = 7;   // 歌曲文件
  string file_size = 8;   // 歌曲文件大小
  string file_hash = 9;   // 歌曲文件hash
  string song_id = 10; //歌曲ID
}

message ListenSongOrderRsp {
  repeated SongOrderItem song_list = 1;
}