syntax="proto3";

package rcmd.music_channel;

option go_package = "golang.52tt.com/protocol/services/rcmd/music_channel";

service MusicSongKtv {
  //KTV猜你喜欢
  rpc GetKtvLikeSong(MusicSongKtvReq) returns(MusicSongKtvRsp);

  //KTV推荐列表
  rpc GetKtvList(MusicSongKtvReq) returns(MusicSongKtvRsp);

  //KTV豆腐块内容推荐
  rpc GetKtvBlockSong(MusicSongKtvBlockReq) returns(MusicSongKtvRsp);
}

message MusicSongKtvReq {
  uint32 uid  = 1;
  uint32 limit = 2;
}

message KTVCopyright {
  int32 song_lyric = 1; // 是否有词曲伴奏版权，可以通过 [requestAccompaniment] 拿到伴奏资源
  int32 recordingval = 2; // 是否有歌曲录音版权，可以通过 [requestSong] 拿到歌曲资源。
  int32 channel = 3; // 歌曲渠道。0：音速达有权歌曲。其他值：其他渠道歌曲。
}

//歌曲信息
message SongInfo {
  string song_id = 1; // 歌曲id
  string song_name = 2; // 歌曲名称
  string singer_name = 3; // 歌手名称
  string album_name = 4; // 专辑名称
  string album_img = 5; // 专辑图片
  int32 duration = 6; // 时长
  KTVCopyright copyright = 7;// 版权信息
  string meta_id = 8; //透传给客户端,客户端需要上报到数仓
  int32 vendor_id = 9; // 歌曲的版权方
  int32 pitch_ability = 10; // 是否具有音高线。1：有 2：没有
}

message MusicSongKtvRsp {
  repeated SongInfo song_info_list = 1;
}

message MusicSongKtvBlockReq {
  uint32 uid  = 1;
  uint32 limit = 2;
  uint32 block_id = 3; //豆腐块ID
}