syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/esport-statistics";

package esport_statistics;

service EsportStatistics {
    // 批量获取用户首次接单时间
    rpc BatchGetUserFirstTakeOrdersTime (BatchGetUserFirstTakeOrdersTimeRequest) returns (BatchGetUserFirstTakeOrdersTimeResponse);
    // 获取电竞指导接单统计数据（单个game_id）
    rpc GetCoachStatistics (GetCoachStatisticsRequest) returns (GetCoachStatisticsResponse);
    // 获取用户最多下单的游戏
    rpc GetUserMostPayGame (GetUserMostPayGameRequest) returns (GetUserMostPayGameResponse);
    // 获取电竞指导全部游戏统计数据
    rpc GetCoachAllStatistics (GetCoachAllStatisticsRequest) returns (GetCoachAllStatisticsResponse);
    // 处理电竞指导身份回收
    rpc HandleRecallCoach (HandleRecallCoachRequest) returns (HandleRecallCoachResponse);
    // 批量获取电竞指导周期内接单数量
    rpc BatchGetCoachDurationOrderNum (BatchGetCoachDurationOrderNumRequest) returns (BatchGetCoachDurationOrderNumResponse);
    // 批量获取电竞指导接单数量
    rpc BatchGetOrderCntByGameId (BatchGetOrderCntByGameIdRequest) returns (BatchGetOrderCntByGameIdResponse);
    // 获取用户月度总消费金额
    rpc GetUserTotalExpenditureAmountMonthly (GetUserTotalExpenditureAmountMonthlyRequest) returns (GetUserTotalExpenditureAmountMonthlyResponse);

    // esport-visit 相关（大神谁看过我 + 从游戏卡片进入IM页）
    // 上报用户访问大神IM页
    rpc ReportUserVisitIMPage (ReportUserVisitIMPageRequest) returns (ReportUserVisitIMPageResponse);
    // 上报用户访问大神游戏详情卡片
    rpc ReportUserVisitGameCard (ReportUserVisitGameCardRequest) returns (ReportUserVisitGameCardResponse);
    // 获取大神 谁看过我summary数据
    rpc GetBeVisitorRecordCount (GetBeVisitorRecordCountRequest) returns (GetBeVisitorRecordCountResponse);
    // 获取大神 谁看过我记录
    rpc GetBeVisitorRecordList (GetBeVisitorRecordListRequest) returns (GetBeVisitorRecordListResponse);
    // 获取指定用户给指定大神最新一次的下单记录
    rpc GetUserCoachLeastOrder (GetUserCoachLeastOrderRequest) returns (GetUserCoachLeastOrderResponse);
    // 获取用户在指定时间段内下过单的大神
    rpc GetUserOrderCoachList (GetUserOrderCoachListRequest) returns (GetUserOrderCoachListResponse);

    // 批量获取电竞指导转化率
    rpc BatGetCoachConversionRate (BatGetCoachConversionRateRequest) returns (BatGetCoachConversionRateResponse);
    // 批量获取电竞指导订单量统计
    rpc BatGetCoachOrderSum (BatGetCoachOrderSumRequest) returns (BatGetCoachOrderSumResponse);
    // 重建用户下过单的记录
    rpc RebuildUserOrderCoachList (RebuildUserOrderCoachListRequest) returns (RebuildUserOrderCoachListResponse);
    // 获取电竞指导流水值
    rpc GetCoachOrderRunningValue (GetCoachOrderRunningValueRequest) returns (GetCoachOrderRunningValueResponse);
    // 获取电竞指导老板数
    rpc GetCoachCustomerNum (GetCoachCustomerNumRequest) returns (GetCoachCustomerNumResponse);
    // 获取大神今日访问量
    rpc GetCoachTodayUv (GetCoachTodayUvRequest) returns (GetCoachTodayUvResponse);
}

// 批量获取电竞指导首次接单时间
message BatchGetUserFirstTakeOrdersTimeRequest {
    repeated uint32 uid_list = 1;
}

message BatchGetUserFirstTakeOrdersTimeResponse {
    // 首次接单（下单）时间
    map<uint32, int64> time_map = 1;
}

// 获取用户特定game的统计数据
message GetCoachStatisticsRequest {
    uint32 uid = 1;
    uint32 game_id = 2; // 按game_id统计
}

message StatisticsData {
    uint32 order_num = 1; // 订单总量
    uint32 customer_num = 2; // 服务用户总数
}

message GetCoachStatisticsResponse {
    StatisticsData data = 1; //
}

// 获取大神全部技能统计
message GetCoachAllStatisticsRequest {
    uint32 uid = 1;
}

message GetCoachAllStatisticsResponse {
    map<uint32, StatisticsData> game_data_map = 1; // <game_id, data>
}

// 获取用户最多下单的游戏
message GetUserMostPayGameRequest {
    uint32 uid = 1;
}

message GetUserMostPayGameResponse {
    uint32 game_id = 1;
}

// 处理电竞指导身份回收
message HandleRecallCoachRequest {
    uint32 coach_uid = 1; // 电竞指导uid
}

message HandleRecallCoachResponse {

}

// 批量获取指导周期内接单量
message BatchGetCoachDurationOrderNumRequest {
    repeated uint32 uid_list = 1; // 指导uid列表
    uint32 game_id = 2; // 按game_id 分类
    uint32 day_cnt = 3; // n天内的接单量
}

message BatchGetCoachDurationOrderNumResponse {
    map<uint32, uint32> order_num_map = 1; // 接单量
}

// 批量获取电竞指导接单数量
message BatchGetOrderCntByGameIdRequest {
    repeated uint32 uid_list = 1; // 指导uid列表
    uint32 game_id = 2; // game_id
}

message BatchGetOrderCntByGameIdResponse {
    map<uint32, uint32> order_num_map = 1; // <uid, order_num>
}

message GetUserTotalExpenditureAmountMonthlyRequest {
    uint32 uid = 1; // 用户 id
    int64 month = 2;// 月份时间戳，例如 2013-01-01
}

message GetUserTotalExpenditureAmountMonthlyResponse {
    int64 total_amount = 1; // 月度总消费金额
}

message GetUserCoachLatestOrderTimeRequest {
    uint32 uid = 1; // 用户 id
    uint32 coach_uid = 2; // 电竞指导 id
}

message GetUserCoachLatestOrderTimeResponse {
    int64 latest_order_time = 1; // 用户最新下单时间
}

// *********** 大神侧 谁看过我相关 ***********

message ReportUserVisitIMPageRequest {
    uint32 uid = 1;
    uint32 coach_uid = 2;
    bool get_only = 3;     // 只获取访问次数，不更新
}

message ReportUserVisitIMPageResponse {
    uint32 visit_cnt = 3; // 访问次数
}

message ReportUserVisitGameCardRequest {
    uint32 uid = 1;
    uint32 coach_uid = 2;
    uint32 game_id = 3;
}

message ReportUserVisitGameCardResponse {
}

message GetBeVisitorRecordCountRequest {
    uint32 coach_uid = 1;
}

message GetBeVisitorRecordCountResponse {
    uint32 count = 1;
    uint32 growth = 2;
}

message VisitRecord {
    uint32 uid = 1;
    uint32 count = 2;
    int64 update_time = 3; // 最新访问时间戳（秒级）
    uint32 game_id = 4;
    int64 last_order_ts = 5; // 最后一次下单时间戳（秒级）
}

message GetBeVisitorRecordListRequest {
    uint32 coach_uid = 1;
    uint32 offset = 2;
    uint32 limit = 3;
}

message GetBeVisitorRecordListResponse {
    repeated VisitRecord record_list = 1;
    uint32 next_offset = 2;
    // 是否到达底部
    bool is_end = 3;
}

message GetUserCoachLeastOrderRequest {
    uint32 uid = 1;// 用户uid
    uint32 coach_uid = 2;// 大神uid
}

message GetUserCoachLeastOrderResponse {
    int64 order_time = 1;// 最新一次下单时间
}

message GetUserOrderCoachListRequest {
    uint32 uid = 1;// 用户uid
    int64 start_time = 2;// 开始时间
    int64 end_time = 3;// 结束时间
}

message GetUserOrderCoachListResponse {
    map<uint32, int64> coach_order_time_map = 1;// 大神uid -> 最新一次下单时间
}

message CoachConversionRate {
    uint32 coach_uid = 1;
    double conversion_rate = 2;
}

message BatGetCoachConversionRateRequest {
    uint32 page_num = 1;
    uint32 page_size = 2;
}

message BatGetCoachConversionRateResponse {
    repeated CoachConversionRate coach_conversion_rate_list = 1;
}

message CoachOrderSum {
    uint32 coach_uid = 1;
    uint32 days14_game_order_sum = 2;
    uint32 days14_all_order_sum = 3;
    uint32 history_game_order_sum = 4;
    uint32 history_all_order_sum = 5;
}

message BatGetCoachOrderSumRequest {
    repeated uint32 coach_uid_list = 1;
    uint32 game_id = 2;
}

message BatGetCoachOrderSumResponse {
    repeated CoachOrderSum coach_order_sum_list = 1;
}

message RebuildUserOrderCoachListRequest {

}

message RebuildUserOrderCoachListResponse {

}

message GetCoachOrderRunningValueRequest {
    uint32 coach_uid = 1;
    int64  start_time = 2;
    int64  end_time = 3;
}

message GetCoachOrderRunningValueResponse {
    uint32 running_value = 1;
}

message GetCoachCustomerNumRequest {
    uint32 coach_uid = 1;
    int64  start_time = 2;
    int64  end_time = 3;
}

message GetCoachCustomerNumResponse {
    uint32 customer_num = 1;
}

// 获取大神今日访问量
message GetCoachTodayUvRequest {
    uint32 coach_uid = 1;
}

// 获取大神今日访问量
message GetCoachTodayUvResponse {
    uint32 uv = 1;
}

