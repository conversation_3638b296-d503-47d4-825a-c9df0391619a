syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/pgc-channel-ticket";
package pgc_channel_ticket;


service PgcChannelTicketSvr {
    // 设置券配置
    rpc SetTicketConf(SetTicketConfReq) returns (SetTicketConfResp) {}

    // 删除券配置
    rpc DelTicketConf(DelTicketConfReq) returns (DelTicketConfResp) {}

    // 获取券列表
    rpc GetTicketConfList(GetTicketConfListReq) returns (GetTicketConfListResp) {}

    // 检查用户是否有券
    rpc CheckUserTicket(CheckUserTicketReq) returns (CheckUserTicketResp) {}

    // 获取用户体验券列表
    rpc GetUserTicketList(GetUserTicketListReq) returns (GetUserTicketListResp) {}

    // 检测体验券的使用权限
    rpc CheckTicketUsePer(CheckTicketUsePerReq) returns (CheckTicketUsePerResp) {}
}

message TicketConf {
   uint32 ticket_id = 1; // 券id
   repeated uint32 cid_list = 2;  // 可用房间id
   string pkg_top_msg = 3;   // 背包顶部说明文案
   string popup_msg = 4;  // 首页弹窗说明文案 
   uint32 update_ts = 5; // 操作时间
   string im_push_msg = 6;  // im推送msg
}

// 设置券配置
message SetTicketConfReq {
   TicketConf ticket_conf = 1; 
}
message SetTicketConfResp {
}

//删除券配置
message DelTicketConfReq {
   uint32 ticket_id = 1; // 券id
}
message DelTicketConfResp {
}

// 获取券配置列表
message GetTicketConfListReq {
}
message GetTicketConfListResp {
   repeated TicketConf conf_list = 1;
}

//检查用户在房间是否有可用券
message CheckUserTicketReq{
   uint32 uid = 1;
   uint32 cid = 2; 
}
message CheckUserTicketResp{
   bool is_has_ticket = 1;
}

enum GetTicketSourceType {
   GET_TICKET_SOURCE_TYPE_INVALID = 0; // 无效
   GET_TICKET_SOURCE_TYPE_POPUP = 1;  // app首页弹窗
   GET_TICKET_SOURCE_TYPE_CHANNEL_ENTER = 2;  // 进房
   GET_TICKET_SOURCE_TYPE_CHANNEL_QUIT = 3;  // 退房
}

// 体验券信息
message TicketInfo {
   uint32 id = 1;  // 券id
   string name = 2;  // 券名称
   string popup_msg = 3;  // 弹窗说明文案
   uint32 expire_ts = 4; // 有效期 单位秒
   string icon_url = 5; // 券图标
   string ch_jump_rul = 6;  // 房间跳转短链接 
}

// 获取用户体验券列表
message GetUserTicketListReq{
   uint32 source_type = 1; // 获取来源
   uint32 uid = 2;  
   uint32 cid = 3;  // 房间id
}
message GetUserTicketListResp{
   repeated TicketInfo ticket_list = 1;    
   repeated TicketInfo ch_ticket_list = 2;  // 用户本房间可用券列表
}

// 检测体验券的使用权限
message CheckTicketUsePerReq{
   uint32 uid = 1; 
   uint32 cid = 2; 
   uint32 ticket_id = 3;  // 券id
}
message CheckTicketUsePerResp{
   bool has_per_channel = 1;  //房间是否有使用权限
   bool has_per_user = 2;   // 用户是有收礼权限
   string ch_jump_rul = 3;  // 房间跳转短链接 
   string pkg_top_msg = 4;  // 背包顶部说明文案
   string user_no_per_toast = 5; //用户没权限toast
}   

