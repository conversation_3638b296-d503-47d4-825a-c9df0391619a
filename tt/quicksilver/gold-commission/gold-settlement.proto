syntax = "proto3";

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";
import "tt/quicksilver/settlement-bill/settlement-bill.proto";

option go_package = "golang.52tt.com/protocol/services/gold-commission";
package gold_commission;

service GoldSettlement {

  // 多人互动会长佣金结算对账
  rpc GetAmuseGoldSettleOrderCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetAmuseGoldSettleOrderList (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  // 语音直播会长佣金结算对账
  rpc GetYuyinGoldSettleOrderCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetYuyinGoldSettleOrderList (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  // 多人互动会长额外奖励结算对账
  rpc GetAmuseExtraSettleOrderCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetAmuseExtraSettleOrderList (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  // 语音直播会长额外奖励结算对账
  rpc GetYuyinExtraSettleOrderCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetYuyinExtraSettleOrderList (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  // 手动触发结算流程（佣金月结）
  rpc NewFlow(NewFlowReq) returns (NewFlowResp) {}
  // 手动触发特殊结算流程
  rpc NewSpecialFlow(NewSpecialFlowReq) returns (NewSpecialFlowResp) {}
}

message NewFlowReq {
  bool certain = 1; // 确认操作，避免误调接口
  settlement_bill.SettlementBillType bill_type = 2; // 结算单类型
  uint32 settlement_date = 3; // 结算月份 YYYY+MM 202304
  repeated uint32 guild_id_list=4;
}
message NewFlowResp {}

message NewSpecialFlowReq {
  bool certain = 1; // 确认操作，避免误调接口
}
message NewSpecialFlowResp {}