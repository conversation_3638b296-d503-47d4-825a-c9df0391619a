syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-performance";

package channel_performance;

service ChannelPerformance {
    // 添加活动,相同房间存在活动则覆盖
    rpc AddPerformance (AddPerformanceReq) returns (AddPerformanceResp) {
    }
    // 删除活动
    rpc DelPerformance (DelPerformanceReq) returns (DelPerformanceResp) {
    }
    // 获取活动
    rpc GetPerformance (GetPerformanceReq) returns (GetPerformanceResp) {
    }
    // 列出活动(还没过期的活动)
    rpc ListPerformance (ListPerformanceReq) returns (ListPerformanceResp) {
    }
    //包括过期的，按操作时间倒排
    rpc ListPerformanceHistory(ListPerformanceHistoryReq) returns (ListPerformanceHistoryResp) {
    }
    // 设置当前阶段
    rpc SetCurrentPerformanceStage (SetCurrentPerformanceStageReq) returns (SetCurrentPerformanceStageResp) {
    }
    //获取房间多媒体信息
    rpc GetChannelMediaInfo (GetChannelMediaInfoReq) returns (GetChannelMediaInfoResp){
    }
    //设置房间多媒体信息
    rpc SetChannelMediaInfo (SetChannelMediaInfoReq) returns (SetChannelMediaInfoResp){
    }
}

message Guest {
    uint32 uid = 1;
    string introduction = 2;
}

message Stage {
    uint32 id = 1; // 环节序号, 必须大于0

    string title = 2; // 标题
    string icon = 3; // 图标

    string begin_time = 4; // 开始时间

    string introduction_img = 5; // 环节介绍图, 存在则忽略嘉宾列表

    repeated Guest guest_list = 6; // 嘉宾列表
}

message Performance {
    uint32 channel_id = 1; // 房间标识

    string title = 2;
    string icon = 3; // 入口图标

    uint32 current_stage_id = 4; // 当前节目

    repeated Stage stage_list = 5; // 节目单

    int64 visible_begin_time = 6; // 活动可见开始时间
    int64 visible_end_time = 7; // 活动可见截至时间

    // 跟随属性
    string channel_tag = 8; // 房间标签

    string playmate_follow_text = 9; // 跟随文案
    string playmate_follow_img = 10; // 跟随图片

    int64 stage_update_time = 11; // 当前节目更新时间戳
    int64 performance_update_time = 12; // 节目单更新时间戳

    uint32 maintainer = 13; // 维护者
    string opt = 14 ; //操作人
}

message AddPerformanceReq {
    Performance performance = 1;
}

message AddPerformanceResp {
}

message DelPerformanceReq {
    uint32 channel_id = 1; // 房间标识
}

message DelPerformanceResp {
}

message GetPerformanceReq {
    uint32 channel_id = 1; // 房间标识
}

message GetPerformanceResp {
    Performance performance = 1;
}

message ListPerformanceReq {
}

message ListPerformanceResp {
    repeated Performance performance_list = 1;
}

message ListPerformanceHistoryReq {
    repeated uint32  cid_list = 1;  //如果cid_list为空，则返回前200条记录,否则查询cid_list的记录
    uint32 opt_begin_ts = 2 ;
    uint32 opt_end_ts = 3;
}
message ListPerformanceHistoryResp {
    repeated Performance performance_list = 1;
}

message SetCurrentPerformanceStageReq {
    uint32 channel_id = 1; // 房间标识
    uint32 current_stage_id = 2; // 当前节目
}

message SetCurrentPerformanceStageResp {
}

message GetChannelMediaInfoReq {
    uint32 channel_id = 1; // 房间标识
}
message GetChannelMediaInfoResp {
    string video_link = 1; //直播视频流链接
    int64 update_time = 2; // 更新时间
}

message SetChannelMediaInfoReq {
    uint32 channel_id = 1; // 房间标识
    string video_link = 2; //直播视频流链接
    int64 update_time = 3; // 更新时间
}
message SetChannelMediaInfoResp {
}