syntax = "proto3";

package muse_night_plan;
option go_package = "golang.52tt.com/protocol/services/muse-night-plan";


// buf:lint:ignore SERVICE_PASCAL_CASE
service muse_night_plan {
  rpc BatSetText(BatSetTextReq)returns(BatSetTextResp){}
  rpc BatSetSmsTemplate(BatSetSmsTemplateReq)returns(BatSetSmsTemplateResp){}
  rpc GetNightPlanWelcomePop(GetNightPlanWelcomePopReq)returns(GetNightPlanWelcomePopResp){}
  rpc UserClickPop(UserClickPopReq)returns(UserClickPopResp){}
  rpc GetAppointChannelIds(GetAppointChannelIdsReq)returns(GetAppointChannelIdsResp){}
}
message BatSetTextReq{
     repeated string text=1;
}

message BatSetTextResp{

}
message BatSetSmsTemplateReq{
    repeated  string text=1;
}
message BatSetSmsTemplateResp{

}

message GetNightPlanWelcomePopReq{
          uint32 channel_id=1;
          uint32 uid=2;
}

message  GetNightPlanWelcomePopResp{
    WelcomePopInfo info=1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message WelcomePopInfo{
  bool     StartFlag=1;
  string   Topic=2;
  repeated OptionInfo Option=3;
  string   PictureUrl=4;
}
message OptionInfo{
  uint32 option_id=1;
  string option_text=2;
}
message UserClickPopReq{
    uint32 channel_id=1;
    uint32 index=2;
    OptionInfo option=3;
    uint32 uid=4;
}

message UserClickPopResp{

}
message GetAppointChannelIdsReq{

}
message GetAppointChannelIdsResp{
  repeated uint32 channel_ids=1;
}
