syntax = "proto3";

//c++ vipprivilegesvr服务重构为go服务vipprivilegesvr-go

option go_package = "golang.52tt.com/protocol/services/vipprivilegesvr-go";
// namespace
package vipprivilegesvr_go;


//财富值变化
message OnRichChangeReq
{
  uint64 old_rich = 1;
  uint64 new_rich = 2;
  uint32 uid = 3;
}

message OnRichChangeResp
{

}

message VipLevelConfig
{
  uint32 index = 1;
  uint32 min_score = 2;
  uint32 max_score = 3;
}

message GetVipLevelConfigsReq
{
}

message GetVipLevelConfigsResp
{
  repeated VipLevelConfig vip_level_configs = 1;
}

//vip服务体系的特权
message Privilege
{
  uint32 id = 1;
  string name = 2;
  string status = 3;
  string desc = 4;
}

message GetVipPrivilegeListReq
{
}

message GetVipPrivilegeListResp
{
  repeated Privilege privilege_list = 1;
}

enum VipKefuLevel
{
  VIP0 = 0;
  VIP1 = 1;
  VIP2 = 2;
  VIP3 = 3;
}

enum VipOnlineStatus
{
  OFF = 0; //离线
  ON = 1; //在线
}

message AddVipKefuReq
{
  uint32 uid = 1;
  uint32 display_id = 2;
  string username = 3;
  string name = 4;
  string id = 5;
  uint32 level = 6; //enum VipKefuLevel
}

message AddVipKefuResp
{
}

message SetKefuOnlineStatusReq
{
  uint32 uid = 1;
  uint32 status = 2; //enum VipOnlineStatus
}

message SetKefuOnlineStatusResp
{

}

message GetKefuByUidReq
{
  uint32 uid = 1;
  uint32 level = 2;
}

message GetKefuByUidResp
{
  uint32 kefu_uid = 1;
}

message SetKefu2UidReq
{
  uint32 uid = 1;
  uint32 kefu_uid = 2;
  uint32 level = 3;
}

message SetKefu2UidResp
{
}

message GetVipKefusReq
{
  uint32 level = 1;
  bool online = 2;
}

message GetVipKefusResp
{
  repeated uint32 kefu_list = 1;
}

message KefuInfo
{
  string name = 1;
  string id = 2;
  string service_name = 3;
  string ttaccount = 4;
  uint32 status = 5;
  uint32 vip = 6;
  uint32 uid = 7;
}

message GetVipKefuInfoListReq
{

}

message GetVipKefuInfoListResp
{
  repeated KefuInfo kefu_list = 1;
}

message SetKefuVipReq
{
  uint32 uid = 1;
  uint32 level = 2;
}

message SetKefuVipResp
{

}

message DeleteVipKefuReq
{
  uint32 uid = 1;
}

message DeleteVipKefuResp
{

}

//添加根据财富值获取VIP等级以及客服名称的接口。替换原来c++ vipprivilegehelper.cpp中的逻辑
message GetVipInfoByRichReq
{
  uint32 uid = 1;
  uint64 rich = 2;         //如果rich值为0，则获取当前用户的财富值
  bool   is_need_kefu = 3;  //是否需要返回客服名称
}
message GetVipInfoByRichResp
{
  uint32 vip_level = 1;
  string kefu_name = 2;
}

// VIP礼包类型
enum GiftPackageType
{
  GIFT_PACKAGE_TYPE_UNSPECIFIED = 0;
  GIFT_PACKAGE_TYPE_VIP5 = 1; // VIP5 至尊帝皇升级礼包
}

// VIP礼包发放类型
enum DistributionType
{
  DISTRIBUTION_TYPE_UNSPECIFIED = 0;
  DISTRIBUTION_TYPE_USER = 1; // 用户主动领取
  DISTRIBUTION_TYPE_OP = 2; // 运营手动发放
}

// 用户名单类型
enum UserListType
{
  USER_LIST_TYPE_UNSPECIFIED = 0;
  USER_LIST_TYPE_BLACK = 1; // 黑名单
  USER_LIST_TYPE_WHITE = 2; // 白名单
}


// VIP礼包包裹
message VipGiftPackage
{
  uint32 package_id = 1; // 包裹ID
  string package_name = 2; // 包裹名称
  GiftPackageType package_type = 3; // 礼包类型
  float ranking = 4; // 排名
  uint32 total_price = 5; // 包裹总价值
  repeated VIPPackageItemCfg item_list = 6; // 包裹物品列表
}

// 包裹物品配置 （组合了apicenter-go/backpack-api.proto 和 backpack-base.proto）
message VIPPackageItemCfg
{
  uint32 bg_item_id = 1;        // 物品ID
  uint32 bg_id = 2;             // 包裹ID
  uint32 source_id = 4;         // 礼物id
  string item_name = 3;         // 礼物名称
  uint32 item_count = 5;        // 数量
  string img_url = 6;           // 礼物icon
  uint64 tbean_value = 7;       // T豆价值
  uint32 fin_time = 8;          // 截止时间
  uint32 dynamic_fin_time = 9;  // 发放后的有效时间(秒)
  uint32 months = 10;           // 发放后第几个月的1号过期，半年=6，一年=12
}

message VipGiftPackageItem
{
  uint32 gift_id = 1; // 礼物id
  string gift_name = 2; // 礼物名称
  uint32 gift_count = 3; // 礼物数量
  uint64 gift_price = 4; // 礼物价格
  string gift_icon = 5; // 礼物icon
  float ranking = 6; // 排名
  uint32 pack_id = 7; // 所属包裹ID
}

message CreateVipGiftPackageReq
{
  message CreateVipGiftPackageInfo {
    uint32 package_id = 1; // 包裹ID
    float ranking = 4; // 排名
  }
  repeated CreateVipGiftPackageInfo package_list = 1;
  bool validate = 2; // 仅校验
}
message CreateVipGiftPackageResp
{
}

message GetVipGiftPackageListReq
{
  uint32 package_id = 1; // 包裹ID
  string package_name = 2; // 包裹名称
  uint32 offset = 3;
  uint32 limit = 4;
}
message GetVipGiftPackageListResp
{
  repeated VipGiftPackage package_list = 1;
  uint32 total = 2;
}

message DeleteVipGiftPackageReq
{
  uint32 package_id = 1; // 包裹ID
}
message DeleteVipGiftPackageResp
{
}

message GiftPackDistributionRecord
{
  uint32 uid = 1;
  string tid = 2;
  string nickname = 3;
  uint32 receive_time = 4;
  repeated VipGiftPackageItem receive_gift_list = 5; // 礼物列表
  DistributionType distribution_type = 6; // 发放类型
}
message GetVipGiftPackDistributionRecordListReq
{
  uint32 start_time = 1; // 开始时间
  uint32 end_time = 2; // 结束时间
  uint32 uid = 3; // uid
  uint32 offset = 4;
  uint32 limit = 5;
}
message GetVipGiftPackDistributionRecordListResp
{
  repeated GiftPackDistributionRecord record_list = 1;
  uint32 total = 2;
}

message AddVipGiftPackUserListReq
{
  repeated uint32 uid_list = 2; // 用户ID列表
  UserListType list_type = 3; // 名单类型
}
message AddVipGiftPackUserListResp
{
}

message GetVipGiftPackUserListReq
{
  UserListType list_type = 1; // 名单类型
  uint32 uid = 2;
  uint32 offset = 3;
  uint32 limit = 4;
}
message GetVipGiftPackUserListResp
{
  message UserListInfo {
    uint32 uid = 1;
    uint32 create_time = 2;
  }
  repeated UserListInfo user_list = 1;
  uint32 total = 2;
}

message DeleteVipGiftPackUserListReq
{
  repeated uint32 uid_list = 1; // 用户ID列表
  UserListType list_type = 2; // 名单类型
}

message DeleteVipGiftPackUserListResp
{
}

message GetUserVipGiftPackageEntranceReq
{
  uint32 uid = 1;
}
message GetUserVipGiftPackageEntranceResp
{
  bool has_vip_gift_package = 2; // 是否有待领取的VIP礼包
  bool show_red_dot = 3; // 是否显示红点
  string entrance_icon_url = 4; // 入口icon
  string entrance_icon_md5 = 5; // 入口icon md5
  string entrance_jump = 6; // 入口跳转链接
  bool is_condition_met = 7; // 是否满足领取条件
  bool is_blocked = 8; // 是否被ban
}

message GetUserVipGiftPackageInfoReq
{
  uint32 uid = 1;
}
message GetUserVipGiftPackageInfoResp {
  uint32 uid = 1;
  uint64 rich_val = 2; // 财富值
  uint32 cur_vip_level = 3; // vip等级
  uint64 vip5_rich_val = 4; // vip5等级财富值
  bool is_condition_met = 6; // 是否满足领取条件
  bool is_first_met = 7; // 是否达到条件并进入领取页面（用于展示弹框）
  bool is_received = 8; // 是否已领取
  bool is_received_from_op = 9; // 是否已通过运营手动发放

  // 待领取状态
  repeated VipGiftPackageItem vip_gift_list = 10; // vip礼包礼物列表
  uint64 available_gift_total_price = 11; // 可领取礼物总价值

  // 已领取状态
  repeated VipGiftPackageItem received_gift_list = 12; // 已领取礼物列表
  uint64 received_gift_total_price = 13; // 已领取礼物总价值
  uint64 received_time = 14; // 领取时间
  string confirm_text = 15; // 确认文案
}

message SubmitReceiveVipGiftPackageReq
{
  uint32 uid = 1;
  repeated VipGiftPackageItem gift_list = 2; // 礼物ID+数量
}
message SubmitReceiveVipGiftPackageResp
{
}

message ModifyVipGiftPackageRankingReq
{
  uint32 package_id = 1; // 包裹ID
  float ranking = 4; // 排名
}
message ModifyVipGiftPackageRankingResp
{
}

message TestResetUserVipGiftPackageReq
{
  uint32 uid = 1;
}
message TestResetUserVipGiftPackageResp
{
}

// buf:lint:ignore SERVICE_PASCAL_CASE
service vipPrivilegeSvr {

  rpc OnRichChange (OnRichChangeReq) returns (OnRichChangeResp) {}

  rpc GetVipLevelConfigs (GetVipLevelConfigsReq) returns (GetVipLevelConfigsResp) {}

  rpc GetVipPrivilegeList (GetVipPrivilegeListReq) returns (GetVipPrivilegeListResp){}

  rpc AddVipKefu (AddVipKefuReq) returns (AddVipKefuResp){}
  rpc SetKefuOnlineStatus (SetKefuOnlineStatusReq) returns (SetKefuOnlineStatusResp){}

  rpc SetKefu2Uid (SetKefu2UidReq) returns (SetKefu2UidResp){}

  rpc GetKefuByUid (GetKefuByUidReq) returns (GetKefuByUidResp){}
  rpc GetVipKefus (GetVipKefusReq) returns (GetVipKefusResp){}
  rpc GetVipKefuInfoList (GetVipKefuInfoListReq) returns (GetVipKefuInfoListResp){}

  rpc SetKefuVip (SetKefuVipReq) returns (SetKefuVipResp){}
  rpc DeleteVipKefu (DeleteVipKefuReq) returns (DeleteVipKefuResp){}

  rpc GetVipInfoByRich (GetVipInfoByRichReq) returns (GetVipInfoByRichResp) {}

  // VIP礼包
  rpc CreateVipGiftPackage (CreateVipGiftPackageReq) returns (CreateVipGiftPackageResp) {}
  rpc GetVipGiftPackageList (GetVipGiftPackageListReq) returns (GetVipGiftPackageListResp) {}
  rpc DeleteVipGiftPackage (DeleteVipGiftPackageReq) returns (DeleteVipGiftPackageResp) {}
  rpc ModifyVipGiftPackageRanking (ModifyVipGiftPackageRankingReq) returns (ModifyVipGiftPackageRankingResp) {}
  // 礼包发放记录
  rpc GetVipGiftPackDistributionRecordList (GetVipGiftPackDistributionRecordListReq) returns (GetVipGiftPackDistributionRecordListResp) {}
  // 黑白名单
  rpc AddVipGiftPackUserList (AddVipGiftPackUserListReq) returns (AddVipGiftPackUserListResp) {}
  rpc GetVipGiftPackUserList (GetVipGiftPackUserListReq) returns (GetVipGiftPackUserListResp) {}
  rpc DeleteVipGiftPackUserList (DeleteVipGiftPackUserListReq) returns (DeleteVipGiftPackUserListResp) {}
  // 用户VIP礼包入口检查
  rpc GetUserVipGiftPackageEntrance (GetUserVipGiftPackageEntranceReq) returns (GetUserVipGiftPackageEntranceResp) {}
  // 获取用户VIP礼包信息
  rpc GetUserVipGiftPackageInfo (GetUserVipGiftPackageInfoReq) returns (GetUserVipGiftPackageInfoResp) {}
  // 用户领取VIP礼包
  rpc SubmitReceiveVipGiftPackage (SubmitReceiveVipGiftPackageReq) returns (SubmitReceiveVipGiftPackageResp) {}

  // 测试接口 重置用户VIP礼包信息（领取状态、入口、红点）
  rpc TestResetUserVipGiftPackage (TestResetUserVipGiftPackageReq) returns (TestResetUserVipGiftPackageResp) {}
}

