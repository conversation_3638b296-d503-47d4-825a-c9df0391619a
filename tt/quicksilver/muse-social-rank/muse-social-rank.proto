syntax = "proto3";

package muse_social_rank;

option go_package = "golang.52tt.com/protocol/services/muse-social-rank";

import "tt/quicksilver/extension/options/options.proto";

service MuseSocialRank {
  option (service.options.service_ext) = {
    service_name: "muse-social-rank"
  };

  /* 榜单顶部品类tab列表 */
  rpc GetSocialCommunityRankTopTabList(GetSocialCommunityRankTopTabListRequest) returns (GetSocialCommunityRankTopTabListResponse) {}
  /* 榜单 */
  rpc GetSocialCommunityRank(GetSocialCommunityRankRequest) returns (GetSocialCommunityRankResponse) {}
  /* 榜单社群状态 */
  rpc GetSocialCommunityRankBrandChannelStatus(GetSocialCommunityRankBrandChannelStatusRequest) returns (GetSocialCommunityRankBrandChannelStatusResponse) {}
  /* 上榜社群的公演房增加榜单入口 */
  rpc OnRankCheck(OnRankCheckRequest) returns (OnRankCheckResponse) {}
  rpc SendWeeklyData(SendWeeklyDataRequest) returns (SendWeeklyDataResponse) {}
  /* 根据房间（或者社群ID）查榜单（周榜）的荣誉标识 */
  rpc GetRankHonorSignByChannelIds(GetRankHonorSignByChannelIdsRequest) returns (GetRankHonorSignByChannelIdsResponse) {}

  /* 上榜社群的公演房增加榜单入口 */
  rpc BatGetRankInChannel(BatGetRankInChannelRequest) returns (BatGetRankInChannelResponse) {}


  /* 根据社群ID 查榜单（周榜）的荣誉标识 */
  rpc GetRankHonorSignBySocialCommunityId(GetRankHonorSignBySocialCommunityIdRequest) returns (GetRankHonorSignBySocialCommunityIdResponse) {}

  rpc GetStayHeartbeatSize(GetStayHeartbeatSizeRequest) returns (GetStayHeartbeatSizeResponse) {}

  rpc RefreshBrandCache(RefreshBrandCacheRequest) returns (RefreshBrandCacheResponse) {}

  rpc GetBrandHotKeyStatistics(GetBrandHotKeyStatisticsRequest) returns (GetBrandHotKeyStatisticsResponse) {}

  rpc GetValidBrandStatistics(GetValidBrandStatisticsRequest) returns (GetValidBrandStatisticsResponse) {}

  rpc CallHeartbeat(CallHeartbeatRequest) returns (CallHeartbeatResponse) {}

  rpc ModifyDataCache(ModifyDataCacheRequest) returns (ModifyDataCacheResponse) {}

  rpc BrandRankDetail(BrandRankDetailRequest) returns (BrandRankDetailResponse) {}

  rpc BalanceRank(BalanceRankRequest) returns (BalanceRankResponse) {}

  rpc GetRankDetail(GetRankDetailRequest) returns (GetRankDetailResponse) {}

  rpc GetWeekRankRealtimeDetail(GetWeekRankRealtimeDetailRequest) returns (GetWeekRankRealtimeDetailResponse) {}

  rpc RebuildWeekRank(RebuildWeekRankRequest) returns (RebuildWeekRankResponse) {}
}

/* 榜单顶部品类tab列表 */
message GetSocialCommunityRankTopTabListRequest{
  uint32 uid = 1;
}

message TopTabInfo{
  string tab_name = 1;
  string tab_category_id = 2; /* 品类ID */
}

message GetSocialCommunityRankTopTabListResponse{
  repeated TopTabInfo tab_list = 1;
}

enum SocialCommunityRankType{
  SOCIAL_COMMUNITY_RANK_TYPE_UNSPECIFIED = 0;
  SOCIAL_COMMUNITY_RANK_TYPE_WEEK = 1;
  SOCIAL_COMMUNITY_RANK_TYPE_HOUR = 2;
}

/* 榜单 */
message GetSocialCommunityRankRequest{
  uint32 uid = 1;
  string tab_category_id = 2;
  uint32 rank_type = 3; // SocialCommunityRankType
  bool is_next_week_rank_data = 4; // 是否获取的是下周的预排榜单 true：是
}

message SocialCommunityRankBrandInfo{
  string brand_id = 1; /* 社群id */
  string brand_logo = 2; /* 社群logo */
  string brand_name = 3; /* 社群名 */
}

message SocialCommunityRankElementInfo{
  SocialCommunityRankBrandInfo brand_rank_info = 1;
  uint32 impact = 2; /* 影响力值 */
}

message SocialCommunityRankMeSortInfo{
  SocialCommunityRankElementInfo brand_rank_info = 1;
  uint32 this_week_rank_index = 2; /* 本周排名 */
  uint32 next_week_rank_index = 3; /* 下周预估排名 */
}

message GetSocialCommunityRankResponse{
  repeated SocialCommunityRankElementInfo rank_element_list = 1; /* 榜单信息 */
  SocialCommunityRankMeSortInfo me_rank_info = 2; /* 我的社群排名信息 */
  bool in_this_category = 3;
}

/* 榜单社群状态 */
message GetSocialCommunityRankBrandChannelStatusRequest{
  string tab_category_id = 1; /* 品类ID */
  uint32 rank_type = 2; // SocialCommunityRankType
  repeated string brand_id_list = 3; /* 社群id */
}

/* 在房状态 */
enum SocialCommunityRankBrandChannelStatus{
  SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_UNSPECIFIED = 0;
  SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_CHAT = 1; /* 聊天中 暂时不用 */
  SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_SHOW = 2; /* 演出中 */
  SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_IN_CHANNEL = 3; // 房间中，主理人个人房处于发布中且未上锁
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SocialCommunityRankBrandChannelStatusInfo{
  uint32 channel_id = 1;
  uint32 channel_status = 2; // SocialCommunityRankBrandChannelStatus
  uint32 user_count = 3; /* 在房人数 */
  uint32 BrandMemberCount = 4;
  string BrandDesc = 5;
  string BrandContent = 6;
  string BrandContentPostId = 7;
}

message GetSocialCommunityRankBrandChannelStatusResponse{
  repeated SocialCommunityRankBrandChannelStatusInfo channel_status_list = 1;
}

message OnRankCheckRequest{
  uint32 uid = 1;
}

message OnRankCheckResponse{
  bool is_on_rank = 1; /* 是否上榜 */
  SocialCommunityRankBrandInfo brand_rank_info = 2;
  string category_type_simple_desc = 3; /* 名次文案 社群对应的品类类型短文案 */
  string category_name = 4; // 社群对应品类名称
  string rank_no = 5; // No.x TOP 10%
  string category_id = 6; // 品类ID
}

/* 根据房间（或者社群ID）查榜单（周榜）的荣誉标识 */
message GetRankHonorSignByChannelIdsRequest{
  repeated uint32 channel_id_list = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SocialRankHonorSignInfo{
  string icon = 1; /* icon */
  repeated string style_color_list = 2; /* 样式 底色 */
  string text = 3; /* 文案 */
  string category_id = 4; // 品类ID
  uint32 rank_type = 5; // SocialCommunityRankType
  string social_community_id = 6; // 社群ID
  string RankInChannelText = 7; // 上榜社群的公演房增加榜单入口 临时用
  string jump_rank_url = 8; // 榜单入口链接
}

message GetRankHonorSignByChannelIdsResponse{
  map<uint32, SocialRankHonorSignInfo> channel_honor_sign_map = 1;
}

/* 根据社群ID 查榜单（周榜）的荣誉标识 */
message GetRankHonorSignBySocialCommunityIdRequest{
  string social_community_id = 1;
}

message GetRankHonorSignBySocialCommunityIdResponse{
 SocialRankHonorSignInfo rank_honor_sign = 1;
}

/* 根据社群ID查榜单（周榜）的荣誉标识 */
message GetRankHonorSignBySocialCommunityIdsRequest{
  repeated string social_community_id_list = 1;
}

message GetRankInfoBySocialCommunityIdResponse{
  map<string, SocialRankHonorSignInfo> channel_honor_sign_map = 1;
}

/* 上榜社群的公演房增加榜单入口 */
message BatGetRankInChannelRequest{
  repeated uint32 channel_id_list = 1;
}

message RankInChannelInfo{
  string category_id = 1; // 品类ID
  uint32 rank_type = 2; // SocialCommunityRankType
  string text = 3; // 文案
  string social_community_id = 4; // 社群ID
  string jump_rank_url = 5; // 榜单入口链接
}

message BatGetRankInChannelResponse{
  map<uint32, RankInChannelInfo> rank_in_channel_info_map = 1;
}

message SendWeeklyDataRequest{
  string brand_id = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SendWeeklyDataResponse{
  repeated string msgList = 1;
}

message RefreshBrandCacheRequest {
  repeated string brand_id_list = 1;
}

message RefreshBrandCacheResponse {

}

message GetBrandHotKeyStatisticsRequest {
  bool include_key_list = 1;
}

message GetBrandHotKeyStatisticsResponse {
  string hour_key = 1;
  int64 hour_key_count = 2;
  repeated string hour_key_list = 3;
  string week_key = 4;
  int64 week_key_count = 5;
  repeated string week_key_list = 6;
}

message GetStayHeartbeatSizeRequest {

}

message GetStayHeartbeatSizeResponse {
  repeated int64 size_list = 1;
}

message GetValidBrandStatisticsRequest {
  bool include_detail_list = 1;
}

message GetValidBrandStatisticsResponse {
  int64 total_count = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  message Statistics {
    string CategoryId = 1;
    int64 count = 2;
    repeated string BrandIdList = 3;
  }
  repeated Statistics detail_list = 2;
}

message CallHeartbeatRequest {
}

message CallHeartbeatResponse {

}

message ModifyDataCacheRequest {
  message MoveRank {
    string category_id = 1;
  }
  MoveRank move_week_rank = 1;
}

message ModifyDataCacheResponse {
}

message BrandRankDetailRequest {
  string brand_id = 1;
}

message BrandRankDetailResponse {

  message ChannelDetail {
    uint32 channel_id = 1;
    uint32 enter_uid_count = 2;
    repeated uint32 enter_uid_list = 3;
    string enter_uid_count_key = 4;
    int64 stay_time = 5;
    string stay_time_key = 6;
  }

  repeated ChannelDetail hour_channel_list = 1;
  repeated ChannelDetail week_channel_list = 2;

  message BrandDetail {
    int64 respect_min = 1;
    int64 respect_max = 2;
    int64 respect = 3;
    string respect_key = 4;
    int64 member_count_min = 5;
    int64 member_count_max = 6;
    int64 member_count = 7;
    string member_count_key = 8;
  }

  BrandDetail hour_brand = 3;
  BrandDetail week_brand = 4;

  message ContentDetail {
    string id = 1;
    string content = 2;
    int64 respect = 3;
    string date = 4;
    string key = 5;
  }

  int64 hour_impact = 5;
  int64 week_impact = 6;

  ContentDetail content_detail = 7;

  string brand_introduce = 8;
  int64 brand_member_count = 9;
}

message BalanceRankRequest {
}

message BalanceRankResponse {
}

message GetRankDetailRequest {
  string tab_category_id = 1;
  enum RankType {
    Hour = 0;
    Week = 1;
  }
  RankType rank_type = 2;

  enum RankPeriod {
    Current = 0;
    Last = 1;
  }
  RankPeriod rank_period = 3;
  uint32 uid = 4;
}

message GetRankDetailResponse {
  message RankDetail {
    string brand_id = 1;
    string brand_name = 2;
    string brand_logo = 3;
    int64 impact = 4;
    SocialCommunityRankBrandChannelStatusInfo detail = 5;
  }
  repeated RankDetail list = 1;
  SocialCommunityRankMeSortInfo me_rank_info = 2; /* 我的社群排名信息 */
}

message GetWeekRankRealtimeDetailRequest {
  string tab_category_id = 1;
  enum RankPeriod {
    Current = 0;
    Last = 1;
  }
  RankPeriod rank_period = 2;
}

message GetWeekRankRealtimeDetailResponse {
  repeated SocialCommunityRankElementInfo real_list = 1;
  repeated SocialCommunityRankElementInfo all_list = 2;
}

message RebuildWeekRankRequest {
  string tab_category_id = 1;
  enum RankPeriod {
    Current = 0;
    Last = 1;
  }
  RankPeriod rank_period = 2;
}

message RebuildWeekRankResponse {

}