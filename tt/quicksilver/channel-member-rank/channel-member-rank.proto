syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-member-rank";

package channel_member_rank;

service ChannelMemberRank {
  // 获取房间爱意榜
  rpc GetChannelConsumeRankList (GetChannelConsumeRankListReq) returns (GetChannelConsumeRankListResp) {
  }
  // 获取指定用户的消费信息信息
  rpc GetUserConsumeInfo (GetUserConsumeInfoReq) returns (GetUserConsumeInfoResp) {
  }
  // 批量获取指定用户的消费信息信息
  rpc BatGetUserConsumeInfo (BatGetUserConsumeInfoReq) returns (BatGetUserConsumeInfoResp) {
  }
  // 隐藏/取消隐藏用户的消费信息
  rpc HideUserChannelConsume (HideUserChannelConsumeReq) returns (HideUserChannelConsumeResp) {
  }
  // 结算神秘人消费
  rpc SettleUkwConsume (SettleUkwConsumeReq) returns (SettleUkwConsumeResp) {
  }
}

enum SourceType {
  SOURCE_TYPE_UNSPECIFIED = 0;
  SOURCE_TYPE_PRESENT = 1; // 礼物
  SOURCE_TYPE_KNIGHT = 2; // 骑士团
  SOURCE_TYPE_TBEAN = 3; // T豆
  SOURCE_TYPE_ACTIVE = 4; // 活跃值
}

enum OrderStatus {
  ORDER_STATUS_DELAY = 0; // 延迟
  ORDER_STATUS_ABANDON = 1; // 废弃
  ORDER_STATUS_COMMIT = 2; // 提交
}

// 成员消费信息
message MemberConsumeInfo
{
  uint32 uid = 1;
  uint64 consume_value = 2; // 爱意值
  ChannelMemberVip vip_level_info = 3;
  bool is_auto_hidden_consume = 4; // 用户是否被自动隐藏消费
  uint32 rank = 5; // 排名
}

// 成员等级信息
message ChannelMemberVip {
  uint32 curr_level_id = 1;   // 成员当前的VIP ID
  string curr_level_name = 2;   // 成员当前VIP ID 对应的Vip名称
  uint64 curr_level_value = 3;  // 成员当前的VIP等级数值
  uint64 curr_level_min_value = 7; // 当前VIP等级的最小值
  uint32 next_level_id = 4;         // 达到下一级的VIP ID
  string next_level_name = 5;       // 达到下一级的VIP等级名称
  uint64 next_level_min_value = 6;  // 达到下一集需要的最小VIP数值
}

// 获取房间爱意榜
message GetChannelConsumeRankListReq {
  uint32 channel_id = 1;
  uint32 offset = 2;
  uint32 limit = 3;

  // 需要获取自己的排名信息才填以下字段
  uint32 uid = 4;
  bool is_show_hidden = 6; // 是否自动隐藏不活跃消费用户
  uint32 channel_type = 7;
}
message GetChannelConsumeRankListResp {
  repeated MemberConsumeInfo rank_info_list = 1;
  MemberConsumeInfo my_rank_info = 2;
  uint64 d_value = 3;    // 距离上一名的差值
  uint32 rank = 4;       // 我的排名
  bool is_can_hide_consume = 5;
  bool is_hidden_consume = 6; // 用户是否已手动隐藏消费
  bool is_auto_hidden_consume = 7; // 用户是否被自动隐藏消费
}

// 获取指定用户的消费信息信息
message GetUserConsumeInfoReq {
  uint32 channel_id = 1;
  uint32 uid = 2;
}
message GetUserConsumeInfoResp {
  MemberConsumeInfo consume_info = 1;
}

// 批量获取指定用户的消费信息信息
message BatGetUserConsumeInfoReq {
  uint32 channel_id = 1;
  repeated uint32 uid_list = 2;
}
message BatGetUserConsumeInfoResp {
  repeated MemberConsumeInfo consume_info_list = 1;
}

// 隐藏/取消隐藏用户的消费信息
message HideUserChannelConsumeReq
{
  uint32 uid = 1;
  uint32 channel_id = 2;
  bool is_hide = 3; // true 代表隐藏，false 代表取消隐藏
}
message HideUserChannelConsumeResp
{
}

// 结算神秘人消费
message SettleUkwConsumeReq {
  uint32 channel_id = 1;
  uint32 uid = 2;
  bool certain = 3; // 确认
}
message SettleUkwConsumeResp {
}