syntax = "proto3";

option go_package = "fellow-house-http";

package fellow_house_http;

// 前缀：tt-revenue-http-logic/fellow-house

message FellowHouseRes {
  enum ResType {
    ENUM_RES_TYPE_UNKNOWN = 0;
    ENUM_RES_TYPE_VAP = 1;
    ENUM_RES_TYPE_LOTTIE = 2;
  }
  uint32 res_type = 1;         // 资源类型
  string house_res = 2;        // 小屋资源
  string house_res_md5 = 3;    // 小屋资源md5
}

enum FellowHouseType{
  ENUM_FELLOW_HOUSE_TYPE_UNKNOWN = 0;  // 未知
  ENUM_FELLOW_HOUSE_TYPE_CP = 1;   // CP
  ENUM_FELLOW_HOUSE_TYPE_BRO = 2;  // 基友
  ENUM_FELLOW_HOUSE_TYPE_LADYBRO = 3;  // 闺蜜
  ENUM_FELLOW_HOUSE_TYPE_INTIMATE = 4;  // 知己
  ENUM_FELLOW_HOUSE_TYPE_PARTNER = 5;  // 搭子
}

message FellowHouseCfg {
  uint32 house_id = 1;       // 小屋配置ID
  string name = 2;           // 小屋名称
  string house_desc = 3;     // 小屋副标题
  string icon = 4;           // 小屋图标
  FellowHouseRes house_res = 5;  // 小屋资源
  uint32 bonus_percentile = 6; // 加成比例，百分之 n
  uint32 origin_price = 7;   // 原价，T豆
  uint32 discount_price = 8; // 优惠价，T豆
}

message FellowHouseInfo {
  FellowHouseCfg house_cfg = 1;    // 小屋配置信息
  int64 expire_ts = 2;    // 小屋过期时间戳，秒级
  bool in_use = 3;        // 是否正在使用
  uint32 use_day_cnt = 4; // 使用天数（入住天数）
}

// 获取用户拥有的小屋列表
message GetUserFellowHouseListReq {
  uint32 uid = 1;
  uint32 cp_uid = 2;
}

message GetUserFellowHouseListResp {
  repeated FellowHouseInfo list = 1;
}

// 获取用户曾经有过的小屋列表，挚友空间用，已过期的小屋也会返回
message GetUserHistoryFellowHouseListReq {
  uint32 uid = 1;
  uint32 cp_uid = 2;
}

message GetUserHistoryFellowHouseListResp {
  repeated FellowHouseInfo list = 1;
}

// 使用/取消使用小屋
message SetUserFellowHouseInuseReq {
  uint32 uid = 1;
  uint32 cp_uid = 2;
  uint32 house_id = 3;    // 小屋配置id，为 0 则为取消使用
}

message SetUserFellowHouseInuseResp {}