syntax = "proto3";

// namespace
// buf:lint:ignore PACKAGE_LOWER_SNAKE_CASE 已迁移 psr，此处协议废弃
package HotWordSearch;
option go_package = "golang.52tt.com/protocol/services/hotwordsearch-go";

// basic - 基本数据元     frontend - 后台配置相关 
// client - 客户端相关    test - 测试相关

// ======================================= basic ==============================================

// 展示样式
enum HotWordType {
    ENUM_HotWordType_INVALID = 0;
    ENUM_DEFAULT_TYPE = 1;                     // 普通
    ENUM_HIGHLIGHT_TYPE = 2;                   // 强调
}

// 数据来源
enum SourceType {
    ENUM_SourceType_INVALID = 0;
    ENUM_SEARCH_RECORD = 1;                    // 搜索关键词 
    ENUM_PRESS_RECORD = 2;                     // 发布房间名
}

// 测试指令
enum TestType {
    ENUM_TestType_INVALID = 0;
    ENUM_LIST_ALLKEY = 1;                      // 列出所有RedisKey
    ENUM_DELETE_ALLKEY = 2;                    // 删除所有RedisKey
    ENUM_DELETE_ONEKEY = 3;                    // 删除指定RedisKey
}

// 热词数据元
message HotWord {
     string word = 1;                  // 内容
     HotWordType type = 2;             // 样式
}

// 热词组
message HotWordGroup {
     uint32 begin_point_second = 1;    // 起始展示时间 UTC时间 自1970/1/1起的秒数
     uint32 end_point_second = 2;      // 终止展示时间 UTC时间 自1970/1/1起的秒数
     uint32 group_idx = 3;             // 热词组Idx，允许非连续的组序号
    repeated HotWord hot_word_list = 4;        // 热词组列表
     uint32 version = 5;               // version 1为猜你想搜
  uint32 force_push_cnt = 6; // 运营后台强推个数
}

// 敏感词操作记录
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BlackListsOp {
     string opt_name = 1;              // 登陆管理员用户名
     bool addOp = 2;                   // true：增加 false：删除
    repeated string word_list = 3;             // 内容列表
     uint32 time_stamp = 4;            // 登陆时间
}

// 排名数据元
message RankAndScore {
     string word = 1;                  // 关键词
     int64 count = 2;                  // 计数
}

// ======================================= frontend ==============================================

message HotWordSearchGetGroupsReq {
     uint32 start_idx = 1;             // 分页起始idx，从0开始
     uint32 required_size = 2;         // 分页大小
     uint32 version = 3;//版本大小 1
}
message HotWordSearchGetGroupsResp {
    repeated HotWordGroup group_list = 1;      // 热词组列表
     uint32 total = 2;                 // 后台存储的热词组总数
    uint32 force_push_cnt = 3; // 运营后台强推个数
}

message HotWordSearchSetGroupsReq {
     HotWordGroup group = 1;           // 热词组
     uint32 mode = 2;//0  覆盖 1 新增
     uint32 group_idx = 3;
     uint32 version = 4;//版本大小 1

}
message HotWordSearchSetGroupsResp {
    // nothing
}

message HotWordSearchDelGroupsReq {
     uint32 group_idx = 1;             // 热词组idx
     uint32 version = 2;//版本大小 1
}
message HotWordSearchDelGroupsResp {
    // nothing
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message HotWordSearchAddBlackListsOpReq {
     string opt_name = 1;              // 登陆管理员用户名
     bool addOp = 2;                   // true：增加 false：删除
    repeated string word_list = 3;             // 内容列表   
     uint32 time_stamp = 4;            // 登陆时间
}
message HotWordSearchAddBlackListsOpResp {
    // nothing           
}

message HotWordSearchGetBlackListsOpReq {
     uint32 start_idx = 1;             // 分页起始idx，从0开始
     uint32 required_size = 2;         // 分页大小
}
message HotWordSearchGetBlackListsOpResp {
    repeated BlackListsOp record_list = 1;     // 操作记录
     uint32 total = 2;                 // 操作记录总数
}

message HotWordSearchGetSearchRankReq {
     uint32 begin_point_second = 1;    // 起始时间 UTC时间 自1970/1/1起的秒数
     uint32 end_point_second = 2;      // 终止时间 UTC时间 自1970/1/1起的秒数
     uint32 start_idx = 3;             // 分页起始idx，从0开始
     uint32 required_size = 4;         // 分页大小
}
message HotWordSearchGetSearchRankResp {
    repeated RankAndScore word_list = 1;       // 搜索关键词排名列表，从大到小排序
     uint32 total = 2;                 // Rank记录总数
}

message HotWordSearchGetPressRankReq {
     uint32 begin_point_second = 1;    // 起始时间 UTC时间 自1970/1/1起的秒数
     uint32 end_point_second = 2;      // 终止时间 UTC时间 自1970/1/1起的秒数
     uint32 start_idx = 3;             // 分页起始idx，从0开始
     uint32 required_size = 4;         // 分页大小
}
message HotWordSearchGetPressRankResp {
    repeated RankAndScore word_list = 1;       // 发布房间名排名列表，从大到小排序
     uint32 total = 2;                 // Rank记录总数
}

// ======================================= client ==============================================

message HotWordSearchFetchReq {
     uint32 start_grp_idx = 1;         // 分页起始idx，从0开始
     uint32 required_size = 2;         // 分页大小
     uint32 version = 3;//版本大小 1
}
message HotWordSearchFetchResp {
     uint32 next_grp_idx = 1;          // 下次请求拉取的热词组的提示idx
    repeated HotWordGroup group_list = 2;      // 本次请求的热词组列表
}

// ======================================= logic ==============================================

message HotWordSearchAddRecordReq {
    repeated string word_list = 1;             // 搜索内容
     uint32 time_stamp = 2;            // 搜索时间戳
     SourceType type = 3;              // 数据来源类型
     uint32 version = 4;//版本大小 1
}
message HotWordSearchAddRecordResp {
     string debug = 1;                 // 该记录写入的key
}

// ======================================= test ==============================================

message HotWordSearchHandleKeyReq {
     TestType type = 1;                // 测试操作类型 1->打印 2->删除 3->删除指定
     string key = 2;                   // op3 删除的key
}
message HotWordSearchHandleKeyResp {
    repeated string key_list = 1;              // 所有存在的key
}

message GetFirstHotWordGroupLenReq {
  uint32 version = 1;
}

message GetFirstHotWordGroupLenResp {
  uint32 first_group_len = 1;
}

// ===============================================================================================


service HotWordSearch {

  // ======================================= frontend ==============================================

  rpc GetHotWordGroups(HotWordSearchGetGroupsReq)returns(HotWordSearchGetGroupsResp) {}

  rpc SetHotWordGroup(HotWordSearchSetGroupsReq)returns(HotWordSearchSetGroupsResp) {}

  rpc DelHotWordGroup(HotWordSearchDelGroupsReq)returns(HotWordSearchDelGroupsResp) {}

  rpc GetFirstHotWordGroupLen(GetFirstHotWordGroupLenReq) returns(GetFirstHotWordGroupLenResp){}

  rpc GetBlackListsOp(HotWordSearchGetBlackListsOpReq)returns(HotWordSearchGetBlackListsOpResp) {}

  rpc AddBlackListsOp(HotWordSearchAddBlackListsOpReq)returns(HotWordSearchAddBlackListsOpResp) {}

  rpc GetSearchRecordRank(HotWordSearchGetSearchRankReq)returns(HotWordSearchGetSearchRankResp) {}

  rpc GetPressRecordRank(HotWordSearchGetPressRankReq)returns(HotWordSearchGetPressRankResp) {}

  // ======================================= client ==============================================

  rpc FetchHotWordGroups(HotWordSearchFetchReq)returns(HotWordSearchFetchResp) {}

  // ======================================= logic ==============================================

  rpc AddRecord(HotWordSearchAddRecordReq)returns(HotWordSearchAddRecordResp) {}

  // ======================================= test ==============================================

  rpc HandleKey(HotWordSearchHandleKeyReq)returns(HotWordSearchHandleKeyResp) {}

}