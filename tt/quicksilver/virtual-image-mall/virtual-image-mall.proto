syntax = "proto3";

import "tt/quicksilver/virtual-image-resource/virtual-image-resource.proto";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

package virtual_image_mall;
option go_package = "golang.52tt.com/protocol/services/virtual-image-mall";

service VirtualImageMall {
    // 获取商品数据列表
    rpc GetCommodityDataList(GetCommodityDataListRequest) returns (GetCommodityDataListResponse) {}
    rpc GetCommodityRecommendList(GetCommodityRecommendListRequest) returns (GetCommodityRecommendListResponse) {}
    // 根据价格获取商品数据列表
    rpc GetCommodityDataListByPrice(GetCommodityDataListByPriceRequest) returns (GetCommodityDataListByPriceResponse) {}


    // 批量添加商品
    rpc BatAddCommodity(BatAddCommodityReq) returns (BatAddCommodityResp) {}
    //更新商品信息
    rpc UpdateCommodity(UpdateCommodityReq) returns (UpdateCommodityResp) {}
    //批量增加推荐商品
    rpc BatAddCommodityRecommend(BatAddCommodityRecommendReq) returns (BatAddCommodityRecommendResp) {}
    //编辑推荐商品
    rpc UpdateCommodityRecommend(UpdateCommodityRecommendReq) returns (UpdateCommodityRecommendResp) {}
    //删除推荐商品信息
    rpc DelCommodityRecommend(DelCommodityRecommendReq) returns (DelCommodityRecommendResp) {}

    // 记录购买订单
    rpc BuyCommodityData(BuyCommodityDataRequest) returns (BuyCommodityDataResponse) {}
    // 更新购买订单状态
    rpc UpdateCommodityDataOrdersStatus(UpdateCommodityDataOrdersStatusRequest) returns (UpdateCommodityDataOrdersStatusResponse) {}
    // 获取购买中订单
    rpc GetCommodityDataOrdersPaying(GetCommodityDataOrdersPayingRequest) returns (GetCommodityDataOrdersPayingResponse) {}

    // 批量获取购买订单
    rpc BatchGetCommodityDataOrders(BatchGetCommodityDataOrdersRequest) returns (BatchGetCommodityDataOrdersResponse) {}

    // 添加购物车
    rpc AddShoppingCar(AddShoppingCarRequest) returns (AddShoppingCarResponse) {}
    // 删除购物车
    rpc DelShoppingCar(DelShoppingCarRequest) returns (DelShoppingCarResponse) {}

    // 批量获取购物车
    rpc BathGetShoppingCar(BathGetShoppingCarRequest) returns (BathGetShoppingCarResponse) {}

    //更新商品的物品信息
    rpc UpdateCommodityResource(UpdateCommodityResourceReq) returns (UpdateCommodityResourceResp) {}

    // 更新商品套餐红点
    rpc UpdateCommodityPackageRedDot(UpdateCommodityPackageRedDotReq) returns (UpdateCommodityPackageRedDotResp) {}

    // 获取未刷新红点的商品套餐数据
    rpc GetUnRefreshedRedDotPackageData(GetUnRefreshedRedDotPackageDataReq) returns (GetUnRefreshedRedDotPackageDataResp) {}
    // 品类用户已读
    rpc CommodityUserReadRedDot(CommodityUserReadRedDotReq) returns (CommodityUserReadRedDotResp) {}

    // 获取用户未读品类
    rpc GetUserUnReadCommodityRedDot(GetUserUnReadCommodityRedDotReq) returns (GetUserUnReadCommodityRedDotResp) {}

  //  rpc GetBuyCommodityDataSuccess(GetBuyCommodityDataSuccessRequest) returns (GetBuyCommodityDataSuccessResponse) {}

    rpc BatchGetCommodityRedDot(BatchGetCommodityRedDotReq) returns (BatchGetCommodityRedDotResp) {}
    rpc DelCommodityData(DelCommodityDataReq) returns (DelCommodityDataResp) {}

    // 根据时间范围返回订单统计数据
    rpc GetOrderDataByTimeRange(GetOrderDataByTimeRangeRequest) returns (GetOrderDataByTimeRangeResponse) {}

    // 查询各品类下生效的商品数量
    rpc GetCategoryCommodityCountList(GetCategoryCommodityCountListRequest) returns (GetCategoryCommodityCountListResponse) {}

    /*********对账接口**********/
    // T豆消费数据对账
    rpc GetConsumeTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetConsumeOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

}

enum CommodityGainPath {
    COMMODITY_GAIN_PATH_UNKNOWN = 0;
    COMMODITY_GAIN_PATH_PURCHASE = 1;  // 购买
    COMMODITY_GAIN_PATH_GIFT = 2;      // 礼物
    COMMODITY_GAIN_PATH_ACTIVITY = 3;  // 活动
    COMMODITY_GAIN_PATH_REWARD = 4;    // 奖励
    COMMODITY_GAIN_PATH_EXCHANGE = 5;  // 兑换
    COMMODITY_GAIN_PATH_OTHER = 6;     // 其他
    COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD = 7; // 无限换装卡权益
}

// 上架状态
enum ShelfStatus {
    SHELF_STATUS_UNKNOWN = 0;
    SHELF_STATUS_FUTURE = 1;   //待上架
    SHELF_STATUS_NOW = 2;      //上架中
    SHELF_STATUS_EXPIRE = 3;   //已下架
}

// 价格套餐
message CommodityDataPackage {
    uint32 package_id = 1;              // 套装ID
    uint32 price = 2;                   // 价格
    uint32 discount_price = 3;          // 折扣价格
    uint32 discount_rate = 4;           // 折扣率
    uint32 effective_day = 5;           // 有效天数
    uint32 shelf_time = 6;              // 上架时间
    uint32 expire_time = 7;             // 下架时间
    uint32 commodity_id = 8;            // 商品ID
    uint32 shelf_status = 9;            // 上架状态 see ShelfStatus
    bool expire_time_show = 10;         // 是否展示下架时间
    bool is_refreshed_red_dot = 11;     // 是否刷新红点
}

enum CommodityType {
    COMMODITY_TYPE_UNSPECIFIED = 0;
    COMMODITY_TYPE_SINGLE = 1;  // 单类
    COMMODITY_TYPE_SUIT = 2;    // 套装

}

message CustomizeLogotype {
    string logotype = 1;                // 自定义标识
    uint32 shelf_time = 2;              // 上架时间
    uint32 expire_time = 3;             // 下架时间
}

// 活动链接
message ActUrl{
   string url = 1;
   uint32 market_id = 2;   // 0:TT语音 2:欢游 3:在呀 4:T次元 5:麦可  6:谜境
   uint32 client_type = 3;  // 0:全部 1:安卓 2:ios
}

message ActivityInfo {
    string desc = 1;       // 活动描述
    string desc_color = 2;  //描述色值
    repeated ActUrl url_list = 3;  // 活动链接
}

enum RightsType {
    RIGHTS_TYPE_UNKNOWN = 0;
    RIGHTS_TYPE_UNLIMITED_CARD = 1;  // 无限换装卡
}

message CommodityData {
    uint32 commodity_id = 1;                            // 商品ID
    string level_icon = 2;                              // 等级图标
    uint32 level = 3;                                   // 商品等级
    CustomizeLogotype customize_icon = 4;               // 运营自定义标识
    string commodity_icon = 5;                          // 商品图标
    string commodity_animation = 6;                     // 商品动画
    string commodity_name = 7;                          // 商品名称
    uint32 gain_path = 8;                               // 获得方式 see CommodityGainPath
    repeated uint32 resource_id_list = 9;              // 资源id列表
    uint32 category = 10;                               // 资源品类 see virtual_image_resource VirtualImageResourceCategory
    uint32 sub_category = 11;                           // 资源子分类
    uint32 commodity_type = 12;                         // 商品类型 see CommodityType
    uint32 rank = 13;                                   // 排序
    uint32 create_time = 14;                            // 创建时间
    uint32 update_time = 15;                            // 更新时间
    uint32 resource_sex = 16;                           // 资源性别     1: 男  2: 女 3: 通用 和 virtual_image_resource sex定义一致
    repeated CommodityDataPackage price_package_list = 17;        // 价格套餐列表
    ActivityInfo act_info = 18;                         // 活动信息
    string spine_animation = 19;                        // 体态动作
    uint32 shelf_time = 20;                             // 上架时间
    uint32 expire_time = 21;                            // 下架时间
    uint32 red_dot_version = 22;                        // 红点版本
    uint32 promotional_video_id = 23;                   // 宣传片id
    uint32 support_rights = 24;                         // 【废弃】支持权益 see RightsType
}

enum SortType {
    SORT_TYPE_UNKNOWN = 0;
    SORT_TYPE_ASC = 1;  // 升序
    SORT_TYPE_DESC = 2;  // 降序
}

message SortData {
    string field = 1;  // 排序字段
    uint32 sort_type = 2;  // 排序类型 see SortType
}

// 获取商品数据列表
message GetCommodityDataListRequest{
    uint32 category = 1;                                // 资源品类 see virtual_image_resource; VirtualImageResourceCategory
    uint32 sub_category = 2;                            // 资源子分类
    repeated uint32 commodity_id_list = 3;              // 商品id 列表
    repeated uint32 resource_id_list = 4;               // 资源id
    uint32 commodity_type = 5;                          // 商品类型 see CommodityType
    string commodity_name = 6;                          // 商品名称
    uint32 resource_sex = 7;                            // 资源性别
    uint32 level = 8;                                   // 商品等级
    uint32 gain_path = 9;                               // 获得方式 see CommodityGainPath
    uint32 shelf_status = 10;                           // 上架状态 see ShelfStatus
    uint32 page = 11;                                   // 页码 从1开始
    uint32 page_size = 12;                              // 页数 为0时全部获取
    bool   is_sex_select = 13;                          // 是否性别筛选
    repeated uint32 resource_sex_list = 14;             // 性别多选
    uint32 rank_sort = 15;                              // 排名字段排序 see SortType
    uint32 min_price = 16;                              // 最小原价
    uint32 max_price = 17;                              // 最大原价
    uint32 min_discount_price = 18;                     // 最小折扣价格
    uint32 max_discount_price = 19;                     // 最大折扣价格
    SortData sort_data = 20;                            // 排序信息
} 

message GetCommodityDataListResponse{
    repeated CommodityData commodity_data_list = 1; // 商品数据列表
    uint32 total_cnt = 2;  // 总数
}

// 根据价格获取商品数据列表
message GetCommodityDataListByPriceRequest{
    uint32 price = 1;                                   // 价格
    repeated uint32 resource_sex_list = 2;              // 资源性别
}

message GetCommodityDataListByPriceResponse{
    repeated CommodityData commodity_data_list = 1; // 商品数据列表
}

// 批量添加商品
message BatAddCommodityReq {
   repeated CommodityData data_list = 1;
}
message BatAddCommodityResp {
}

//更新商品信息
message UpdateCommodityReq {
   CommodityData data = 1;
}
message UpdateCommodityResp {
}

//更新商品的物品信息
message UpdateCommodityResourceReq{
   uint32 id = 1;                        // 资源id
   string name = 2;                      // 名称
   string icon = 3;                      // 图标
   uint32 sex = 4;                       // 性别 1: 男  2: 女 3: 通用 和 virtual_image_resource sex定义一致
}
message UpdateCommodityResourceResp{
}

message CommodityRecommendInfo {
   CommodityData data = 1;  // 商品信息
   uint32 rec_rank = 2;          // 推荐排名
}

// 获取推荐商品列表
message GetCommodityRecommendListRequest{
    uint32 category = 1;                                // 资源品类 see virtual_image_resource; VirtualImageResourceCategory
    uint32 sub_category = 2;                            // 资源子分类
    repeated uint32 commodity_id_list = 3;              // 商品id列表
    repeated uint32 resource_id_list = 4;               // 资源id
    uint32 commodity_type = 5;                          // 商品类型 see CommodityType
    string commodity_name = 6;                          // 商品名称
    uint32 resource_sex = 7;                            // 资源性别
    uint32 level = 8;                                   // 商品等级
    uint32 gain_path = 9;                               // 获得方式 see CommodityGainPath
    uint32 shelf_status = 10;                           // 上架状态 see ShelfStatus
    uint32 page = 11;                                   // 页码 从1开始
    uint32 page_size = 12;                              // 页数 为0时全部获取
    bool   is_sex_select = 13;                          // 是否性别筛选
    repeated uint32 resource_sex_list = 14;             // 性别多选
    SortData sort_data = 15;                            // 排序信息
}

message GetCommodityRecommendListResponse{
    repeated CommodityRecommendInfo rec_list = 1;  // 推荐商品数据
    uint32 total_cnt = 2;  // 总数
}

//批量增加推荐商品
message BatAddCommodityRecommendReq {
   repeated CommodityRecommendInfo info_list = 1;
}
message BatAddCommodityRecommendResp {
}


//编辑推荐商品
message UpdateCommodityRecommendReq{
   CommodityRecommendInfo info = 1;
}
message UpdateCommodityRecommendResp{
}

//删除推荐商品信息
message DelCommodityRecommendReq{
   uint32 commodity_id = 1;  // 商品id
   repeated uint32 commodity_id_list = 2;
}
message DelCommodityRecommendResp{
}

enum CommodityPayStatus {
    COMMODITY_PAY_STATUS_UNKNOWN = 0;
    COMMODITY_PAY_STATUS_PAYING = 1;  // 支付中
    COMMODITY_PAY_STATUS_FREEZE = 2;    // 冻结
    COMMODITY_PAY_STATUS_FREEZE_FAIL = 3;    // 冻结
    COMMODITY_PAY_STATUS_SHIPPED = 4; // 已发货
    COMMODITY_PAY_STATUS_REFUND = 5;  // 退款
}

// 商品订单信息
message CommodityDataOrders {
    string data_order_id = 1;              // 商品订单ID
    uint32 commodity_id = 2;               // 商品ID
    uint32 package_id = 3;                 // 套装ID
    uint32 count = 4;                      // 购买数量
    uint32 total_price = 5;                // 总价格
    uint32 avg_price = 6;                  // 均价
    uint32 create_time = 7;                // 创建时间
    uint32 pay_status = 8;                 // 支付状态
    uint32 uid = 9;                        // 用户ID
    uint32 update_time = 10;               // 更新时间
    repeated uint32 resource_id_list = 11;          // 资源ID列表
    string commodity_name = 12;            // 商品名称
    uint32 effective_day = 13;             // 有效天数
    uint32 commodity_type = 14;            // 商品类型 see CommodityType
    uint32 category = 15;                  // 资源品类 see virtual_image_resource VirtualImageResourceCategory
    uint32 sub_category = 16;              // 资源子分类
    string big_trade_no = 17;              // 大订单号
    string commodity_icon = 18;                          // 商品图标
    string level_icon = 19;                              // 等级图标
    uint32 promotional_video_id = 21;                   // 宣传片id
}

// 购物车商品基础数据
message ShoppingItemBasic {
    uint32 shopping_item_id = 1;        // 购物车商品ID
    uint32 commodity_id = 2;            // 商品ID
    uint32 package_id = 3;              // 套装ID
    uint32 count = 4;                   // 购买数量
    uint32 total_price = 5;              // 总价格
    uint32 avg_price = 6;                // 均价
}

message BuyCommodityDataRequest {
    repeated CommodityDataOrders orders = 1;        // 商品订单信息
}

message BuyCommodityDataResponse {

}

message UpdateCommodityDataOrdersStatusRequest {
    string  data_order_id = 1;        // 商品订单ID
    uint32 pay_status = 2;            // 支付状态
    string big_trade_no = 3;          // 外部订单号
    bool is_commit = 4;                // 是否提交
    uint32 uid = 5;                    // 用户ID
    uint32 create_time = 6;            // 创建时间
    uint32 total_price = 7;            // 总价格
}

message UpdateCommodityDataOrdersStatusResponse {

}

message BatchGetCommodityDataOrdersRequest {
    repeated string data_order_id = 1;        // 商品订单ID
    bool is_big_trade_no = 2;                 // 是否大订单号
}

message BatchGetCommodityDataOrdersResponse {
    repeated CommodityDataOrders orders = 1;        // 商品订单信息
}

// 获取已冻结订单
message GetCommodityDataOrdersPayingRequest {
}

message GetCommodityDataOrdersPayingResponse {
    repeated CommodityDataOrders orders = 1;        // 商品订单信息
}


message AddShoppingCarRequest {
    ShoppingItemBasic commodity_item = 2;       // 购物车商品
}

message AddShoppingCarResponse {

}

message DelShoppingCarRequest {
    repeated uint32 shopping_item_id = 1;        // 购物车商品ID
}

message DelShoppingCarResponse {

}

message BathGetShoppingCarRequest {

}

message BathGetShoppingCarResponse {
    repeated ShoppingItemBasic shopping_item_list = 1;        // 购物车商品列表
}

message UpdateCommodityPackageRedDotReq {
    uint32 package_id = 1;              // 套装ID
    bool is_refreshed_red_dot = 2;      // 是否刷新红点
    uint32 category = 3;                  // 资源品类 see virtual_image_resource VirtualImageResourceCategory
    uint32 sub_category = 4;              // 资源子分类
    uint32 commodity_id = 5;              // 商品ID
    uint32 shelf_time = 6;                // 上架时间
    uint32 expire_time = 7;               // 下架时间
    bool is_continue = 8;                   // 是否继续
}

message UpdateCommodityPackageRedDotResp {

}

message GetUnRefreshedRedDotPackageDataReq {

}

message GetUnRefreshedRedDotPackageDataResp {
    repeated CommodityDataPackage package_list = 1;        // 价格套餐列表
}

message CommodityUserReadRedDotReq {
    uint32 category = 1;                  // 资源品类 see virtual_image_resource VirtualImageResourceCategory
    uint32 sub_category = 2;              // 资源子分类
    uint32 uid = 3;                       // 用户ID
    uint32 red_dot_aler_type = 4;         // 红点提醒类型
}

message CommodityUserReadRedDotResp {

}

message GetUserUnReadCommodityRedDotReq {
    uint32 uid = 1;                       // 用户ID
    uint32 red_dot_aler_type = 2;         // 红点提醒类型
    repeated uint32 category_list = 3;    // 一级品类列表
}

message GetUserUnReadCommodityRedDotResp {
    uint32 red_dot_aler_type = 1;         // 红点提醒类型
    bool has_red_dot = 2;         // 是否有红点
    repeated virtual_image_resource.VirtualImageResourceCategoryInfo category_list = 3;        // 未读品类列表
}

message BatchGetCommodityRedDotReq {
    bool is_get_detailed_data = 1;      // 是否获取详细数据
}

message BatchGetCommodityRedDotResp {
    uint32 global_version = 1; // 全局版本号
    map<uint32, uint32 > commodity_id_version = 2; // 商品版本号
}

message DelCommodityDataReq {
    uint32 commodity_id = 1;  // 商品id
}

message DelCommodityDataResp {
}

// 根据时间范围返回订单统计数据
message GetOrderDataByTimeRangeRequest {
    int64 start_time = 1;  // 开始时间
    int64 end_time = 2;    // 结束时间
}

message GetOrderDataByTimeRangeResponse {
    repeated uint32 uid_list = 1;  // 购买用户uid列表
    int64 total_price = 2;         // 总价格(T豆)
    int64 total_count = 3;         // 总订单数
}

// 各品类下商品数量
message CategoryCommodityCount {
    uint32 category = 1;                  // 资源品类 see virtual_image_resource VirtualImageResourceCategory
    uint32 sub_category = 2;              // 资源子分类
    uint32 cnt = 3;
}

// 查询各品类下生效的商品数量
message GetCategoryCommodityCountListRequest{
    repeated uint32 resource_sex_list = 1;  // 性别
}

message GetCategoryCommodityCountListResponse{
    repeated CategoryCommodityCount list = 1;
}