syntax = "proto3";

package reconcile_v2_svr;
// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/reconcile-v2-svr/reconcile-backpack";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

import "tt/quicksilver/extension/options/options.proto";

service ReconcileBackpack {
  option (service.options.old_package_name) = "ReconcileBackpack.ReconcileBackpack";

  //获取时间范围内的背包订单数量和物品数量
  rpc GetBackpackItemUseCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内的背包订单列表
  rpc GetBackpackItemUseOrder(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  //获取时间范围内获得的背包订单数量和物品数量
  rpc GetBackpackItemGainCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内的获得的背包订单列表
  rpc GetBackpackItemGainOrder(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
}