syntax = "proto3";

package reconcile_v2_svr;
// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/reconcile-v2-svr/reconcile-present";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

import "tt/quicksilver/extension/options/options.proto";

service ReconcilePresent {
  option (service.options.old_package_name) = "ReconcilePresent.ReconcilePresent";

  //获取时间范围内的积分数量和金额
  rpc GetAllUserPresentCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内的订单列表
  rpc GetUserPresentOrderList(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  //获取时间范围内的积分数量和金额
  rpc GetUserPresentCountBySource(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内的订单列表
  rpc GetUserPresentOrderListBySource(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  //获取时间范围内的积分数量和金额
  rpc GetUserPresentCountByChannelType(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内的订单列表
  rpc GetUserPresentOrderListByChannelType(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  //获取时间范围内指定房间的积分数量和金额
  rpc GetUserPresentCountByChannelId(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内指定房间的订单列表
  rpc GetUserPresentOrderListByChannelId(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  //获取时间范围内指定游戏类型的积分数量和金额
  rpc GetUserPresentCountByGameId(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内指定房间的订单列表
  rpc GetUserPresentOrderListByGameId(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  //获取时间范围内礼物券的积分数量和金额
  rpc GetTicketCountByChannelType(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内礼物券的订单列表
  rpc GetTicketOrderListByChannelType(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  //补单
  rpc ReplaceBackpackOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

  //查订单
  rpc GetOrderInfoById(GetOrderInfoByIdReq) returns (GetOrderInfoByIdResp){}

  //查礼物券订单
  rpc GetTicketOrderInfoById(GetOrderInfoByIdReq) returns (GetOrderInfoByIdResp){}

  rpc GetUserPresentSumByGiftId(GetUserPresentSumByGiftIdReq) returns (GetUserPresentSumByGiftIdResp){}

  //查时间范围内订单信息列表
  rpc GetOrderInfoListByTimeRange(GetOrderInfoListByTimeRangeReq) returns (GetOrderInfoListByTimeRangeResp){}

  //获取时间范围内指定礼物ID的积分数量和金额
  rpc GetPresentCountByGiftIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内指定礼物ID的订单列表
  rpc GetPresentOrderListByGiftIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  //获取指定uid在时间段内送出的背包礼物金额，以及时间段内所有背包送礼的总金额和总人数
  rpc GetBackpackGiftAmount(GetBackpackGiftAmountReq) returns (GetBackpackGiftAmountResp) {}
}

message GetOrderInfoByIdReq{
  string order_id = 1;
}

message GetOrderInfoByIdResp{
  OrderInfo order_info = 1;
}

message OrderInfo{
  uint32 from_uid = 1;
  uint32 to_uid = 2;
  uint32 item_id = 3;
  string order_id = 4;
  uint32 item_count = 5;
  uint64 create_time = 6;
  uint32 present_source = 7;
  uint64 total_price = 8;
  uint32 chanel_id = 9;
  uint64 total_score = 10;
  uint32 guild_id = 11;
  uint32 channel_type = 12;
  uint32 channel_game_id = 13;
  string deal_token = 14;
  uint32 add_rich = 15;
  uint32 add_charm = 16;
}

message GetUserPresentSumByGiftIdReq{
  uint32 begin_time = 1;
  uint32 end_time = 2;
  repeated uint32 gift_id_list = 3;
}

message GetUserPresentSumByGiftIdResp{
  repeated Statistics stats = 1;
}


// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message Statistics  {
  uint32 ItemId = 1;
  uint32 OrderCount = 2;
  uint32 ItemCount = 3;
  uint64 TotalScore = 4;
  uint64 TotalPrice = 5;
  uint32 PeopleCount = 6;
  repeated uint32 uid = 7;
}

message GetOrderInfoListByTimeRangeReq {
  int64 begin_time = 1;
  int64 end_time = 2;
}

message GetOrderInfoListByTimeRangeResp {
  repeated OrderInfo order_info_list = 1;
}

message GetBackpackGiftAmountReq {
  repeated uint32 uid_list = 1; // 送礼用户ID
  uint32 begin_time = 2; // 开始时间戳
  uint32 end_time = 3; // 结束时间戳
}

message GetBackpackGiftAmountResp {
  uint64 total_amount = 1; // 总金额
  uint32 total_people = 2; // 总人数
  uint32 uid_total_amount = 3; // 传入的uid送出礼物的总值
}