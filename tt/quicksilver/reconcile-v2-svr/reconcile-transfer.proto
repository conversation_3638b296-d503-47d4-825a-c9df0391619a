syntax = "proto3";

package reconcile_v2_svr;
// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/reconcile-v2-svr/reconcile-transfer";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

import "tt/quicksilver/extension/options/options.proto";

service ReconcileTransfer {
  option (service.options.old_package_name) = "ReconcileTransfer.ReconcileTransfer";

  //获取时间范围内的结算订单数量和金额
  rpc GetTransferOrderCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

}