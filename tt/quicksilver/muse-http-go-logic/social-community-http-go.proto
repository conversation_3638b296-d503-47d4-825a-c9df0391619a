syntax = "proto3";
package muse_http_go_logic;

//  生成 pb文件
//  protoc --go_out=plugins=grpc:./gen-go social-community-http-go.proto
//    protoc  --go_out=./gen-go --go_opt=paths=source_relative --go-grpc_out=./gen-go --go-grpc_opt=paths=source_relative  social-community-http-go.proto
option go_package = "golang.52tt.com/protocol/services/muse-http-go-logic";
enum BrandMemberRole{
  Brand_Role_None = 0;
  Brand_Role_Captain = 1;//主理人
  Brand_Role_Kernel = 2;//核心成员
  Brand_Role_Vice_Captain = 3;//副主理人
  Brand_Producer = 4;//制作人
  Brand_Fans = 5;//粉丝
}

message GetSocialCommunityBaseInfoReq {
  string social_community_id = 1;
  uint32 uid = 2; // 用户ID
}

message GetSocialCommunityBaseInfoResp {
  string social_community_name = 1;       //社群名称
  BrandMemberRole role = 2; // 用户在社群中的角色
}

// 获取排行榜
message GetSocialCommunityExpRankReq {
  string social_community_id = 1; // 社群ID
  uint32 uid = 2; // 用户ID
  uint32 offset = 3;
  uint32 is_last = 4; // 0-当前榜单，1-上期榜单
}

message GetSocialCommunityExpRankResp {
  repeated RankInfo rank_list = 1; // 排行榜列表
  RankInfo my_rank_info = 2; // 我的排名信息，如果当前用户不是社团成员，该字段为空
  bool has_next_page = 5; // 下一页是否有数据
}

message RankInfo {
  uint32 uid = 1; // 用户ID
  string account = 2; // 用户账号
  string nick_name = 3; // 用户昵称
  int32 exp = 4; // 用户经验值
  int32 rank = 5; // 排名
}

message GetSocialCommunityProfilePagesReq{
  uint32 uid = 1;
  string social_community_id = 2;
}

message SimpleUserInfo{
  uint32 uid = 1;
  string account = 2;    // 用户账号
  string nick_name = 3;  // 用户昵称
  uint32 sex = 4;          //用户性别
  string avatar = 5;      // 用户头像
}

message SocialCommunityMemberInfo{
  SimpleUserInfo user_info = 1;
  string intro = 2; // 成员介绍
  BrandMemberRole  role = 3; /* 角色 BrandMemberRole */
  int64 community_contribution=4;  /*社群累计贡献值*/
}

message SocialCommunityPhotoAlbumKeyURL{
  string key = 1;
  string url = 2;
}

message UserInfoInTheCommunity{
  SimpleUserInfo user_info = 1;
  uint32 joining_duration = 2; //加入时长
  BrandMemberRole  role = 3; /* 角色 BrandMemberRole */
}

message MuseSocialCommunityNavSecondaryBar{
  oneof content{
    MuseSocialCommunityChannel channel = 1;
    MuseSocialCommunityGroup group = 2;
    MuseSocialCommunityAnnounce announce = 3;
  }
}

message MuseSocialCommunityAnnounce  {
  string name = 1;
  string desc = 2;
  string logo = 3;
  string lottie = 4;
  string lottie_md5 = 5;
  string bg_color = 6;//"#****"
}

enum BrandChannelType{
  None = 0;
  Chat = 1;
  Show = 2;
}

message MuseSocialCommunityChannel {
  uint32 channel_id = 1;
  string name = 2;//不是房间名，是导航栏显示的名称
  string desc = 3;//描述文案
  string logo = 4;
  string lottie = 5;
  string lottie_md5 = 6;
  BrandChannelType channel_type = 7;
  string bg_color = 8;//"#****"
}

message MuseSocialCommunityGroup {
  uint32 group_id = 1;
  string name = 2;//不是群聊名，是导航栏显示的名称
  string desc = 3;//描述文案
  string logo = 4;
  string lottie = 5;
  string lottie_md5 = 6;
  string bg_color = 7;//"#****"
}

message SocialCommunityDetail{
  string social_community_id = 1;       //社群ID
  string social_community_logo = 2;      //社群logo
  string social_community_name = 3;      //社群名称
  string category_name = 4;              //品类名称
  SimpleUserInfo captain = 5;               //主理人名称
  string social_community_intro = 6;     //社群介绍
  uint32 member_count = 7;                //社群所有成员数量
  int32 integral_count = 8;            //社群积分数
  int32 sign_count = 9;                //认证数
  string category_type_simple_desc = 10;
  uint32 brand_professionalism = 11; //1-专业，2-非专业
  string vision = 12;  //愿景
  uint32 level = 13;//社团等级
  string level_logo = 14;//社团等级logo
  string category_id = 15;
  Professionalism professionalism_info = 16;
  SocialCommunityBackground social_community_background=17;     //社群背景
  uint32 limit_member_count = 18; //社群限制成员数
}


message SocialCommunityBackground{
  string social_community_background_url=1;     //社群背景
  uint32 social_community_background_type=2;  //类型
  string social_community_first_frame=3; //首帧图片
}
enum SocialCommunityBackgroundType{
  SOCIAL_COMMUNITY_BACKGROUND_TYPE_NONE=0;
  SOCIAL_COMMUNITY_BACKGROUND_TYPE_IMAGE=1;  //图片背景
  SOCIAL_COMMUNITY_BACKGROUND_TYPE_VIDEO=2;   //视频背景
}

message Professionalism{
  uint32 brand_professionalism = 1;
  string picture_url = 2;
  string text=3;
}

message GetSocialCommunityProfilePagesResp{
  repeated MuseSocialCommunityNavSecondaryBar secondary_bars = 2;//只有当前的bar有值
  SocialCommunityDetail  detail_info = 3;
  repeated SocialCommunityMemberInfo  member_list = 4;
  repeated SocialCommunityPhotoAlbumKeyURL photo_list = 5;
  string social_community_background_logo = 6; /*社群logo背景*/ // 如果为空，则取资源包中的 bg_pic_url
  repeated string robot_url = 7;//机器人头像
  string topic_id = 8;//当前厂牌话题的id
  uint32 view_count = 9;//话题热度
  UserInfoInTheCommunity user_info = 10;
  SocialRankHonorSignInfo honor_info = 12; // 榜单（周榜）的荣誉标识
  NewProfileResource resource = 13; // 资源包
  int64 recent_view_count=14;       //最近访问量
  int64 increase_view_count=15;      //新增访问量
  MainSubCommunityInfo main_sub_community_info = 16; // 主分社
}

// 主分社
message MainSubCommunityInfo{
  repeated SocialCommunitySimpleInfo sub_community_list = 1; // 子社群列表
  repeated SocialCommunitySimpleInfo main_community_list = 2; // 主社群列表
}

// 档案页资源包
message NewProfileResource{
  string bg_pic_url = 1; // 社团档案页背景图
  uint32 bg_pic_type = 2;//社团档案页背景图类型 0：image； 1:video
  string bg_pic_first_frame_pic_url = 3;//社团档案页背景图首帧
  string intro_head_pic_url = 4; // 社团介绍头部图
  string photo_title_pic_url = 5; // 社团相册标题图
  string photo_bg_pic_url = 6; // 社团相册底图
  string member_info_bg_pic_url = 7; // 社团身份底图
  string common_color = 8; // 通用颜色
  string text_color = 9; // 字体颜色
  string kernel_member_head_pic_url = 10; // 核心成员墙头部图
  string kernel_member_bg_pic_url = 11; // 核心成员底图
  string user_card_pic_url =12; // 个人卡片底
  string user_card_im_pic_url = 13; // IM个人卡片底
  string text_shadow_color = 14; // 文字投影颜色
}

message SocialRankHonorSignInfo{
  string icon = 1; /* icon */
  repeated string style_color_list = 2; /* 样式 底色 */
  string text = 3; /* 文案 */
  string category_id = 4; // 品类ID
  uint32 rank_type = 5; // SocialCommunityRankType
}

message JoinSocialCommunityAndJumpRequest{
  string social_community_id = 1;
  uint32 uid = 2;
}

message JoinSocialCommunityAndJumpResponse{
}

message JoinSocialCommunityKernelRequest{
  string social_community_id = 1;
  uint32 uid = 2;
  string invite_code = 3; // 邀请码
}

message JoinSocialCommunityKernelResponse{

}

message JoinSocialCommunityFansRequest{
  string social_community_id = 1;
  uint32 uid = 2;
}

message JoinSocialCommunityFansResponse{

}

message SubmitApplicationToJoinCommunityRequest{
  string social_community_id = 1;
  uint32 uid = 2;
  string reason=3;
}

message SubmitApplicationToJoinCommunityResponse{
}

message GetSocialCommunityMemberCardRequest{
  string social_community_id = 1;
  string uid = 2;
}

message GetSocialCommunityMemberCardResponse{
  SocialCommunityMemberCard member_card = 1;
}

// 社团成员名片
message SocialCommunityMemberCard{
  SimpleUserInfo user_info = 1;
  string member_text = 2;/*角色名*/
  string intro = 3; // 成员介绍
  SocialCommunitySimpleInfo social_community_simple_info = 4; // 社群信息
  CardResource resource = 5; // 资源包
  PersonalCertForSocialCommunityUser personal_cert = 6; // 自定义认证标
}

message PersonalCertForSocialCommunityUser{
  string icon = 1;
  string text = 2;
  repeated string color = 3;
  string text_shadow_color = 4;
}

// 名片资源包
message CardResource{
  string common_color = 1; // 通用颜色
  string text_color = 2; // 字体颜色
  string user_card_pic_url =3; // 个人卡片底
  string text_shadow_color = 4; // 文字投影颜色
}

message SocialCommunitySimpleInfo{
  string social_community_id = 1;       //社群ID
  string social_community_logo = 2;      //社群logo
  string social_community_name = 3;      //社群名称
  string social_community_intro = 4;     //社群介绍
  string category_type_simple_desc = 5;// 社团品类类型短文案
}

//获取我的社群列表
message GetMySocialCommunityListReq{
  uint32 uid=1;
}


message GetMySocialCommunityListResp{
  repeated SocialCommunitySimpleInfo info=1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  bool   isClose=2;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  bool   isValid=3;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  int32  UserType=4;

}

message InviteNumberInfo{
  int32 invited_numbers=1;
  int32  required_invite_number_total=2;
}

// 社群卡片资源包
message SocialCommunityCardResource{
  string common_color = 1; // 通用颜色
  string text_color = 2; // 字体颜色
  string social_community_card_pic_url=3;//社群卡片背景
  string social_community_card_logo_color=4;// 社群卡片描边颜色
}

message SocialCommunityActivityInfo{
  SocialCommunityCardResource resource=1;
  string member_text=2;  //成员角色
  string my_invite_code=3;  //我的邀请码
  bool achieve_base_invite_task=4;   //true--已完成基本邀请任务， false ---还未完成最低邀请任务
  InviteNumberInfo invite_number_info=5; //未完成邀请任务邀请配置信息

}


//获取我的社群活动信息
message GetMySocialCommunityActivityInfoReq{
  uint32 uid=1;
  string social_community_id=2;
}

message GetMySocialCommunityActivityInfoResp{
  SocialCommunityActivityInfo  info=1;
}


//戳ta一下
message SendActivityTaskRemainMsgReq{
  uint32 uid =1;
  uint32 invited_uid=2;
  string social_community_id=3;
}

message SendActivityTaskRemainMsgResp{

}

message GetUserByUidReq{
  uint32 uid=1;
}

message GetUserByUidResp{
  SimpleUserInfo   user=1;
}

message GetMyTotalRewardsReq{
  uint32  uid=1;
}

message GetMyTotalRewardsResp{
  int32 my_rewards=1;  //我的总激励金
  int32 my_invite_count=2;  //我的总邀请数
  int32 encourage_balance=3;  //我的激励金余额
  int32 member_invite_count=4;//成员邀请总数
}

message GetMySocialCommunityRewardsReq{
  uint32  uid=1;
  string social_community_id=2;
}

message GetMySocialCommunityRewardsResp{
  int32 my_rewards=1;  //我的激励金
  int32 my_invite_count=2;  //我的邀请数
  int32 member_invite_count=3;//成员邀请总数

}


message GetEncourageDetailsListReq{
  uint32 uid=1;
  string social_community_id=2;
  int32 offset_id=3;// 主键ID
  int32 limit=4;
  int32  recruit_schedule=5;
}
enum RecruitSchedule{
  RecruitSchedule_NotStart = 0;
  RecruitSchedule_InProgress = 1;
  RecruitSchedule_Finished = 2;
}


message GetEncourageDetailsListResp{
  repeated UserEncourageRecord record=1;
  int64 count=2;
}

message UserEncourageRecord{
  uint64 id=1;
  InviteUserInfo user_info=2;
  uint64 create_time=3;
  int32  rewards=4;  //奖励
  string title=5;    //大标题
  string social_community_content=6;//社群名字+短文案
  repeated TaskInfo  extra_task=7;     //
  string source=8;  //用户来源
  string social_community_id=9;
}

message OverMultipleDaysTask{
  string front_content=1;
  string progress_content=2;
  string back_content=3;
}

message TaskInfo{
  // buf:lint:ignore ONEOF_LOWER_SNAKE_CASE
  oneof Task{
    OverMultipleDaysTask  over_multiple_days_task=1;
  }
}


message InviteUserInfo{
  uint32 uid=1;
  string account = 2;    // 用户账号
  string nick_name = 3;  // 用户昵称
  string avatar = 4;      // 用户头像
  int32  user_type=5; //用户类型 //1--老用户  2--新用户
}

enum UserType {
  NewReg = 0;
  Recall = 1;
}