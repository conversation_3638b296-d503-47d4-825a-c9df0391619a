syntax = "proto3";
package muse_http_go_logic;

//  生成 pb文件
//  protoc --go-grpc_out=plugins=grpc:./gen-go social-community-encourage-http-go.proto
//  protoc-gen-go-grpc 1.4.0 : protoc  --go_out=./gen-go --go_opt=paths=source_relative --go-grpc_out=./gen-go --go-grpc_opt=paths=source_relative  social-community-encourage-http-go.proto
option go_package = "golang.52tt.com/protocol/services/muse-http-go-logic";

message GetLikesActivityUserCompletedTaskProgressRequest{
  uint32 uid=1;
}

message CheckInTaskReward{
  int32 check_in_days=1;
  int32 unlocked_highest_level_reward=2; //已解锁的最高等级奖励
}

message AccumulateLikesTaskReward{
  int32 like_count=1;
  int32 unlocked_highest_level_reward=2;
}

message GetLikesActivityUserCompletedTaskProgressResponse{
  CheckInTaskReward check_in=1;
  AccumulateLikesTaskReward like=2;
  bool activity_switch=3;
}