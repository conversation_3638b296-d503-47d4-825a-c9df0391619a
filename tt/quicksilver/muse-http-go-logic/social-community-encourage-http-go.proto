syntax = "proto3";
package muse_http_go_logic;

//  生成 pb文件
//  protoc --go-grpc_out=plugins=grpc:./gen-go social-community-encourage-http-go.proto
//  protoc-gen-go-grpc 1.4.0 : protoc  --go_out=./gen-go --go_opt=paths=source_relative --go-grpc_out=./gen-go --go-grpc_opt=paths=source_relative  social-community-encourage-http-go.proto
option go_package = "golang.52tt.com/protocol/services/muse-http-go-logic";


enum FundPoolSegmentLockStatus{
  FundPoolUnLock = 0;
  FundPoolLock = 1;
  FundPoolInProgress = 2; // 进行中
}

// 社群奖励基金池分段配置
message FundPoolSegment{
  uint32 max_member_count = 1; // 人数上限
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 Amount = 2; // 金额 单位 分
  uint32 unlock_status = 3; // 是否解锁 FundPoolSegmentLockStatus
}

message FundPoolInfoByMonth{
  uint32 unlock_status = 1; // 是否解锁 FundPoolSegmentLockStatus
  uint32 has_received_fee = 2; // 已领取金额 单位 分[向下取]
  repeated FundPoolSegment segments = 3; // 分段配置
  uint32 month = 4; // 月份
  uint32 remain_days = 5; // 剩余天数
  uint32 member_count_to_unlock = 6; // 还差xx人解锁
  bool can_receive = 7; // 是否可以领取
}

// 获取基金池信息
message GetFundPoolReq{
  uint32 uid = 1;
}

message GetFundPoolResp{
  repeated FundPoolInfoByMonth fund_pool_info_list = 1;
}

// 领取基金池奖励
message ReceiveFundPoolAwardReq{
  uint32 uid = 1;
}

message ReceiveFundPoolAwardResp{
  uint32 amount = 1; // 领取金额 单位分
}

// 提现额度
message GetWithdrawQuotaReq{
  uint32 uid = 1;
}

message FirstWithdrawQuota{
  int32 amount = 1; // 首次提现金额 单位分
  bool  is_present = 2;
}

message WithdrawQuota{
  int32 amount = 1; // 提现金额 单位分
  bool unlock_status = 2;   //true解锁，false 未解锁
  bool is_present = 3;
}

message GetWithdrawQuotaResp{
  FirstWithdrawQuota first_withdraw = 1;// 首次提现
  repeated WithdrawQuota withdraw = 2;
}

// 绑定微信
message BindWechatReq{
  uint32 uid = 1;
  string code = 2;
}

message BindWechatResp{
}

message GetBindWechatReq{
  uint32 uid = 1;
}

message GetBindWechatResp{
  bool is_bind = 1;
}

message WithdrawReq{
  uint32 uid = 1;
  int32 amount = 2; // 提现金额 单位分
}

message WithdrawResp{
}

// 获取提现金额信息
message GetWithdrawAmountReq{
  uint32 uid = 1;
}

message GetWithdrawAmountResp{
  uint32 total_amount = 1; // 累计激励金 单位分
  uint32 balance = 2; // 激励金余额 单位分
}

// 提现记录
message ListWithdrawRecordReq{
  uint32 uid = 1;
  uint32 offset_id = 2;
  uint32 page_size = 3;
}

enum WithdrawRecordStatus{
  WithdrawRecordStatusInProgress = 0; // 提现中
  WithdrawRecordStatusSuccess = 1;
  WithdrawRecordStatusFail = 2;
}

message WithdrawRecord{
  uint32 id = 1;
  int32 amount = 2; // 提现金额 单位分
  int32 status = 3; // WithdrawRecordStatus
  uint32 create_time = 4;
}

message ListWithdrawRecordResp{
  repeated WithdrawRecord records = 1;
}

// 每周提现次数
message GetWeeklyWithdrawCountReq{
  uint32 uid = 1;
}

message GetWeeklyWithdrawCountResp{
  uint32 total_count = 1;
  uint32 used_count = 2;
}