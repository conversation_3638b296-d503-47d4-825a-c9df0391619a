syntax = "proto3";

package muse_http_go_logic;

option go_package = "golang.52tt.com/protocol/services/muse-http-go-logic";

/* 榜单顶部品类tab列表 */
message GetSocialCommunityRankTopTabListRequest{
  uint32 uid = 1;
}

message TopTabInfo{
  string tab_name = 1;
  string tab_category_id = 2; /* 品类ID */
}

message GetSocialCommunityRankTopTabListResponse{
  repeated TopTabInfo tab_list = 1;
}

enum SocialCommunityRankType{
  SOCIAL_COMMUNITY_RANK_TYPE_UNSPECIFIED = 0;
  SOCIAL_COMMUNITY_RANK_TYPE_WEEK = 1;
  SOCIAL_COMMUNITY_RANK_TYPE_HOUR = 2;
}

/* 榜单 */
message GetSocialCommunityRankRequest{
  uint32 uid = 1;
  string tab_category_id = 2;
  uint32 rank_type = 3; // SocialCommunityRankType
  bool is_next_week_rank_data = 4; // 是否获取的是下周的预排榜单 true：是
}

message SocialCommunityRankBrandInfo{
  string brand_id = 1; /* 社群id */
  string brand_logo = 2; /* 社群logo */
  string brand_name = 3; /* 社群名 */
}

message SocialCommunityRankElementInfo{
  SocialCommunityRankBrandInfo brand_rank_info = 1;
  uint32 impact = 2; /* 影响力值 */
}

message SocialCommunityRankMeSortInfo{
  SocialCommunityRankElementInfo brand_rank_info = 1;
  uint32 this_week_rank_index = 2; /* 本周排名 */
  uint32 next_week_rank_index = 3; /* 下周预估排名 */
}

message GetSocialCommunityRankResponse{
  repeated SocialCommunityRankElementInfo rank_element_list = 1; /* 榜单信息 */
  SocialCommunityRankMeSortInfo me_rank_info = 2; /* 我的社群排名信息 */
  bool in_this_category = 3;
}

/* 榜单社群状态 */
message GetSocialCommunityRankBrandChannelStatusRequest{
  string tab_category_id = 1; /* 品类ID */
  uint32 rank_type = 2; // SocialCommunityRankType
  repeated string brand_id_list = 3; /* 社群id */
}

/* 在房状态 */
enum SocialCommunityRankBrandChannelStatus{
  SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_UNSPECIFIED = 0;
  SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_CHAT = 1; /* 聊天中 暂时不用 */
  SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_SHOW = 2; /* 演出中 */
  SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_IN_CHANNEL = 3; // 房间中，主理人个人房处于发布中且未上锁
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SocialCommunityRankBrandChannelStatusInfo{
  uint32 channel_id = 1;
  uint32 channel_status = 2; // SocialCommunityRankBrandChannelStatus
  uint32 user_count = 3; /* 在房人数 */
  uint32 BrandMemberCount = 4;
  string BrandDesc = 5;
  string BrandContent = 6;
  string BrandContentPostId = 7;
}

message GetSocialCommunityRankBrandChannelStatusResponse{
  repeated SocialCommunityRankBrandChannelStatusInfo channel_status_list = 1;
}

message OnRankCheckRequest{
  uint32 uid = 1;
}

message OnRankCheckResponse{
  bool is_on_rank = 1; /* 是否上榜 */
  SocialCommunityRankBrandInfo brand_rank_info = 2;
  string category_type_simple_desc = 3; /* 名次文案 社群对应的品类类型短文案 */
  string category_name = 4; // 社群对应品类名称
  string rank_no = 5; // No.x TOP 10%
  string category_id = 6; // 品类ID
}

/* 根据房间（或者社群ID）查榜单（周榜）的荣誉标识 */
message GetRankHonorSignByChannelIdsRequest{
  repeated uint32 channel_id_list = 1;
}

message RankHonorSignInfo{
  string social_community_logo = 1; /* 社群logo */
  string style_url = 2; /* 样式 */
  string text = 3; /* 文案 */
}

message GetRankInfoByChannelIdsResponse{
  map<uint32, RankHonorSignInfo> channel_honor_sign_map = 1;
}

/* 根据社群ID查榜单（周榜）的荣誉标识 */
message GetRankHonorSignBySocialCommunityIdsRequest{
  repeated string social_community_id_list = 1;
}

message GetRankInfoBySocialCommunityIdResponse{
  map<string, RankHonorSignInfo> channel_honor_sign_map = 1;
}

/* 上榜社群的公演房增加榜单入口 */
message BatGetRankInChannelRequest{
  repeated uint32 channel_id_list = 1;
}

message RankInChannelInfo{
  string category_id = 1; // 品类ID
  uint32 rank_type = 2; // SocialCommunityRankType
  string text = 3; // 文案
}

message BatGetRankInChannelResponse{
  map<uint32, RankInChannelInfo> rank_in_channel_info_map = 1;
}

message SendWeeklyDataRequest{
  string brand_id = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SendWeeklyDataResponse{
  repeated string msgList = 1;
}