syntax = "proto3";

// buf:lint:ignore PACKAGE_DIRECTORY_MATCH
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package com.quwan.dspiopother.proto;

option java_package = "com.quwan.dspiopother.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/iop-home-recommend";

service HomePageRecommendService {
  // 获取推荐的pgc房间列表
  rpc GetRecommendPgcList(GetRecommendPgcListReq) returns (GetRecommendPgcListResp) {}
}

message GetRecommendPgcListReq{
  string app_name = 1; // app业务名 如ttyuyin
  string app_type = 2;  // 马甲包标识, 和埋点平台的app_type一致, tt语音(ttvoice), 麦可(maike), 谜境(mijing), 欢游(huanyou)
  string site_id = 3; // 资源位id,
  string biz_site_id=4; // 资源位业务标识
  string uid = 5;  // 用户uid
  map<string, string> req_ext = 6;  // 请求扩展参数
}

message GetRecommendPgcListResp{
  string is_display = 1; // 是否展示，1-展示，0-不展示
  repeated RecRoomInfo room_info = 2;  // 推荐房间信息,每个坑位对应一个roomInfo
  map<string, string> resp_ext = 3;  // 扩展参数
}

message RecRoomInfo {
  string room_id = 1; // 房间id，如果无法为用户生成对应坑位的推荐房间，则房间id为空
}