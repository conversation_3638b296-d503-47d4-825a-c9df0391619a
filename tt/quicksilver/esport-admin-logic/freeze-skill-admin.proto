syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/esport_admin_logic";

package esport_admin_logic;

service FreezeSkillAdmin {
  // 获取大神技能信息
  rpc GetCoachSkillInfo (GetCoachSkillInfoRequest) returns (GetCoachSkillInfoResponse);
  // 批量冻结大神技能
  rpc BatFreezeCoachSkill (BatFreezeCoachSkillRequest) returns (BatFreezeCoachSkillResponse);
  // 解冻大神技能
  rpc UnfreezeCoachSkill (UnfreezeCoachSkillRequest) returns (UnfreezeCoachSkillResponse);
  // 获取大神技能信息列表
  rpc GetCoachSkillInfoList (GetCoachSkillInfoListRequest) returns (GetCoachSkillInfoListResponse);
  // 获取技能冻结操作列表
  rpc GetSkillFreezeOperationList (GetSkillFreezeOperationListRequest) returns (GetSkillFreezeOperationListResponse);
}

enum FreezeType {
  FREEZE_TYPE_UNFREEZE = 0; // 未冻结
  FREEZE_TYPE_FOREVER = 1; // 永久冻结
  FREEZE_TYPE_TO_TIME = 2; // 冻结至指定时间
}

message CoachDetail {
  uint32 uid = 1;
  string ttid = 2;
  string nickname = 3;
  uint32 esport_er_type = 4; // see ESportErType
  uint32 guild_id = 5;
  string guild_name = 6;
  uint32 obtain_role_ts = 7; // 获得身份时间
}

message SkillDetail {
  uint32 game_id = 1;
  string game_name = 2;
  FreezeType freeze_type = 3; // 冻结状态
  int64 freeze_stop_ts = 4; // 冻结结束时间
  bool is_deleted = 5; // 是否已删除
  float rank = 6; // 排序等级
}

message GetCoachSkillInfoRequest {
  uint32 uid = 1;
  bool is_freeze = 2;
}

message GetCoachSkillInfoResponse {
  repeated SkillDetail skill_list = 1;
}

message FreezeCoachSkill {
  uint32 uid = 1;
  repeated uint32 game_id_list = 2;
}

message BatFreezeCoachSkillRequest {
  repeated FreezeCoachSkill freeze_list = 1;
  FreezeType freeze_type = 2;
  int64 freeze_stop_ts = 3;
  string freeze_reason = 4;
  string op_user = 5;
}

message BatFreezeCoachSkillResponse {
  // freeze_list[0] 请求成功，则 err_list[0] = ''
  // freeze_list[1] 请求失败，则 err_list[1] = '报错信息'
  repeated string err_list = 1;
}

message UnfreezeCoachSkillRequest {
  uint32 uid = 1;
  repeated uint32 game_id_list = 2;
  string op_user = 3;
}

message UnfreezeCoachSkillResponse {
}

enum QueryType {
  QUERY_TYPE_BY_UID = 0;      // 通过uid查询
  QUERY_TYPE_BY_GUILD_ID = 1; // 通过guildid查询
  QUERY_TYPE_BY_ALL = 2;      // 获取所有
}

message GetCoachSkillInfoListRequest {
  int32 page = 1;
  int32 page_size = 2;
  repeated uint32 uid_list = 3;
  uint32 guild_id = 4;
  uint32 query_type = 5; // see QueryType
}

message GetCoachSkillInfoListResponse {
  repeated CoachSkillInfo coach_skill_info_list = 1;
  int32 total = 2;
}

message CoachSkillInfo {
  CoachDetail coach_detail = 1;
  repeated SkillDetail skill_list = 2;
}

message GetSkillFreezeOperationListRequest {
  int32 page = 1;
  int32 page_size = 2;
  repeated uint32 uid_list = 3;
  uint32 guild_id = 4;
  uint32 game_id = 5;
  uint32 query_type = 6; // see QueryType
}

message GetSkillFreezeOperationListResponse {
  repeated SkillFreezeOperation operation_list = 1;
  int32 total = 2;
}

message SkillFreezeOperation {
  CoachDetail coach_detail = 1;
  repeated SkillDetail skill_list = 2;
  FreezeType freeze_type = 3;
  int64 freeze_stop_ts = 4;
  string freeze_reason = 5;
  string op_user = 6;
  int64 op_ts = 7;
}
