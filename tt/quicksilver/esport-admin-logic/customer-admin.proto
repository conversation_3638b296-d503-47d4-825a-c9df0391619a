syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/esport_admin_logic";

package esport_admin_logic;

service CustomerServiceAdmin {
    // 获取已创建的客服号信息
    rpc GetCustomerAccounts (GetCustomerAccountsRequest) returns (GetCustomerAccountsResponse);

    // 创建客服账号
    rpc CreateCustomerAccount (CreateCustomerAccountRequest) returns (CreateCustomerAccountResponse);

    // 获取托管大神配置
    rpc GetManagedGods (GetManagedGodsRequest) returns (GetManagedGodsResponse);

    // 添加托管大神
    rpc AddManagedGod (AddManagedGodRequest) returns (AddManagedGodResponse);

    // 移除托管大神
    rpc RemoveManagedGod (RemoveManagedGodRequest) returns (RemoveManagedGodResponse);
}

message GetCustomerAccountsRequest {
    int32 page = 1; // 分页页码
    int32 size = 2; // 每页大小
    uint32 guild_id = 3; // 公会ID
    repeated uint32 customer_uid = 4; // 客服号UID
}

enum CustomerAccountStatus {
    CUSTOMER_ACCOUNT_STATUS_UNSPECIFIED = 0;
    CUSTOMER_ACCOUNT_STATUS_NORMAL = 1; // 正常
    CUSTOMER_ACCOUNT_STATUS_BANNED = 2; // 封禁
    CUSTOMER_ACCOUNT_STATUS_UNREG = 3; // 注销
}

message CustomerAccount {
    string customer_ttid = 1;
    uint32 customer_uid = 2;
    string customer_nickname = 3;
    uint32 guild_id = 4;
    uint32 short_guild_id = 5;
    uint32 long_guild_id = 6;
    string guild_name = 7;
    string guild_admin_ttid = 8;
    string creator = 9;
    int64 create_time = 10; // 时间戳
    string password = 11; // 初始密码
    CustomerAccountStatus status = 12; // 状态
}

message GetCustomerAccountsResponse {
    repeated CustomerAccount customer_accounts = 1;
    int32 total = 2; // 总记录数
}

message CreateCustomerAccountRequest {
    uint32 guild_id = 1; // 公会ID
    string password = 2; // 公会密码
    string creator = 3; // 创建者
}

message CreateCustomerAccountResponse {
    CustomerAccount customer_account = 1;
}

message GetManagedGodsRequest {
    int32 page = 1; // 分页页码
    int32 size = 2; // 每页大小
    uint32 guild_id = 3; // 公会ID
    uint32 coach_uid = 4; // 电竞大神的uid
}

enum ManagedGodStatus {
    MANAGED_GOD_STATUS_UNSPECIFIED = 0;
    MANAGED_GOD_STATUS_MANAGED = 1; // 托管中
    MANAGED_GOD_STATUS_UNMANAGED = 2; // 未托管
}

message ManagedGod {
    string ttid = 1;
    uint32 uid = 2;
    string nickname = 3;
    ManagedGodStatus status = 4;
    uint32 customer_uid = 5; // 客服号UID，当状态是 MANAGED_GOD_STATUS_MANAGED 时有效
    string customer_nickname = 6; // 客服昵称，当状态是 MANAGED_GOD_STATUS_MANAGED 时有效
    string customer_ttid = 7;// 客服ttid，当状态是 MANAGED_GOD_STATUS_MANAGED 时有效
}

message GetManagedGodsResponse {
    repeated ManagedGod managed_gods = 1;
    int32 total = 2; // 总记录数
}

message AddManagedGodRequest {
    uint32 customer_uid = 1; // 客服的uid
    repeated uint32 coach_uid = 2; // 大神uid
}

message AddManagedGodResponse {
}

message RemoveManagedGodRequest {
    uint32 customer_uid = 1; // 客服的uid
    repeated uint32 coach_uid = 2; // 大神uid
}

message RemoveManagedGodResponse {
}
