syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/esport_admin_logic";

package esport_admin_logic;

service EsportGodLevelAdmin {
    // GetGodLevelConfig 获取大神等级配置
    rpc GetGodLevelConfig (GetGodLevelConfigRequest) returns (GetGodLevelConfigResponse) {}
}


message GodLevelBaseConf {
    uint32 level = 1;   // 等级
    string name = 2;   // 等级名称
}

message GetGodLevelConfigRequest {
}

message GetGodLevelConfigResponse {
    repeated GodLevelBaseConf configs = 1;
}