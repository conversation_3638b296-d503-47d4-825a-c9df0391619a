syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/esport_admin_logic";

package esport_admin_logic;

// 策略状态
enum StrategyStatus {
    STRATEGY_STATUS_UNSPECIFIED = 0;
    STRATEGY_STATUS_RUNNING = 1;// 运行中
    STRATEGY_STATUS_PAUSED = 2;// 暂停
    STRATEGY_STATUS_DRAFT = 3;// 待运行
}

enum DelimitType {
    DELIMIT_TYPE_UNSPECIFIED = 0; // 默认全部
    DELIMIT_TYPE_AB_TEST = 1;// ab实验
    DELIMIT_TYPE_USER_GROUP = 2;// 人群包
}

// 策略信息
message Strategy {
    // 召回源信息
    message RecallSource {
        int64 id = 1;
        int32 total_quantity = 2; // 召回的数量
        int32 minimum_quantity = 3; //最低召回的数量
    }
    message AbTestSetting {
        string experiment_tag = 1; // 实验tag
        repeated string experiment_args = 2; // 实验参数
    }
    message UserGroupSetting {
        repeated int64 user_group_ids = 1; // 人群包ID列表
    }
    uint32 id = 1;
    uint32 scene_type = 2; // 场景
    string name = 3; // 名称
    uint32 priority = 4; // 优先级
    StrategyStatus status = 5; // 状态
    string last_operator = 6; // 最后操作人
    int64 last_operation_time = 7; // 最后操作时间
    DelimitType delimit_type = 8; // 圈定类型
    AbTestSetting ab_test_setting = 9; // ab实验设置
    UserGroupSetting user_group_setting = 10; // 人群包ID列表
    repeated int64 test_list_ids = 20; // 测试名单ID列表
    repeated RecallSource recall_sources = 21; // 召回源信息列表
    int32 minimum_recall_quantity = 22; // 策略最低量
}

// 过滤规则信息（游离类型）
message FilterRule {
    uint32 id = 1;
    string name = 2; // 规则名称
}

// 场景信息（游离类型）
message Scene {
    uint32 type = 1;//类型
    string name = 2;// 名称
    repeated Scene children = 3;// 子场景
}

message RecallSource {
    enum SortParameterType {
        SORT_PARAMETER_TYPE_UNKNOWN = 0; // 默认全局
        SORT_PARAMETER_TYPE_CUSTOMER = 1; // 自定义
        SORT_PARAMETER_TYPE_RANDOM =2 ; // 随机
    }

    // 过滤规则信息
    message FilterRuleValue {
        uint32 id = 1;
        int32 quantity = 2;// 数量
    }
    uint32 id = 1;
    string name = 2;
    string last_operator = 3; // 最后操作人
    int64 last_operation_time = 4; // 最后操作时间
    RecallSourceRuleSetting rule_setting = 5;  // 使用树形结构表示召回规则的逻辑关系
    uint32 sort_parameter_type = 6; // 排查参数的类型， see SortParameterType
    SortParameterTable customer = 7; // 自定义排序参数
    repeated uint32 queue_rule_type_list = 10;
    repeated FilterRuleValue filter_rule_values = 11; // 过滤规则信息列表
}

message RecallSourceRuleSetting {
    message Condition {
        uint32 rule_type = 1; // 类型
        string value = 2;
        uint32 operator_type = 3; // 操作符类型
        repeated uint32 selected_path = 4;// 选中路径，root->leaf 的 type 值
    }
    message Set {
        repeated Condition conditions = 1;
    }
    repeated Set rule_set = 1;
}

// 召回源规则（游离类型）
message RecallSourceRule {
    message Operator {
        uint32 type = 1;// 类型
        string name = 2;// 名称
        repeated string values = 3;// 选项
    }
    uint32 type = 1;// 类型
    string name = 2;// 名称
    repeated Operator operator = 3;// 操作符
    repeated RecallSourceRule children = 4;// 子规则
}

// 排序参数表（游离类型）
message SortParameterTable {
    message Item {
        string value = 1;
        int32 score = 2;
    }
    message SortParameter {
        uint32 type = 1; // 类型
        string name = 2; // 名称
        repeated Item items = 3; // 选项
        repeated SortParameter children = 4; // 子排序参数
    }
    repeated SortParameter parameters = 1; // 排序参数
}

// 多级排序规则（游离类型）
message RecallSourceQueueRule {
    uint32 type = 1;
    string name = 2;
}


// 定义策略管理服务
service EsportRcmdAdminService {
    //列出策略列表
    rpc ListStrategies (ListStrategiesRequest) returns (ListStrategiesResponse);
    // 获取特定策略的详细信息
    rpc GetStrategy (GetStrategyRequest) returns (GetStrategyResponse);
    // 创建新策略
    rpc CreateStrategy (CreateStrategyRequest) returns (CreateStrategyResponse);
    // 更新现有策略
    rpc UpdateStrategy (UpdateStrategyRequest) returns (UpdateStrategyResponse);
    // 暂停策略
    rpc PauseStrategy (PauseStrategyRequest) returns (PauseStrategyResponse);
    // 启动策略
    rpc StartStrategy (StartStrategyRequest) returns (StartStrategyResponse);
    // 删除策略
    rpc DeleteStrategy (DeleteStrategyRequest) returns (DeleteStrategyResponse);
    // 获取过滤规则
    rpc GetFilterRules (GetFilterRulesRequest) returns (GetFilterRulesResponse);
    // 获取场景列表
    rpc GetScenes (GetScenesRequest) returns (GetScenesResponse);
    // 列出所有召回源
    rpc ListRecallSources (ListRecallSourcesRequest) returns (ListRecallSourcesResponse);
    // 获取某个召回源的详细信息
    rpc GetRecallSourceDetails (GetRecallSourceDetailsRequest) returns (GetRecallSourceDetailsResponse);
    // 新建召回源
    rpc CreateRecallSource (CreateRecallSourceRequest) returns (CreateRecallSourceResponse);
    // 编辑召回源
    rpc UpdateRecallSource (UpdateRecallSourceRequest) returns (UpdateRecallSourceResponse);
    // 删除召回源
    rpc DeleteRecallSource (DeleteRecallSourceRequest) returns (DeleteRecallSourceResponse);
    // 获取召回源规则列表
    rpc GetRecallSourceRuleList (GetRecallSourceRuleListRequest) returns (GetRecallSourceRuleListResponse);
    // 获取默认的排序参数表
    rpc GetDefaultSortParameterTable (GetDefaultSortParameterTableRequest) returns (GetDefaultSortParameterTableResponse);
    // 更新默认的排序参数表
    rpc UpdateDefaultSortParameterTable (UpdateDefaultSortParameterTableRequest) returns (UpdateDefaultSortParameterTableResponse);
    // 获取多级排序的规则列表
    rpc GetRecallSourceQueueRule (GetRecallSourceQueueRuleRequest) returns (GetRecallSourceQueueRuleResponse);
}

message GetRecallSourceQueueRuleRequest {

}

message GetRecallSourceQueueRuleResponse {
    repeated RecallSourceQueueRule list = 1;
}


// 请求策略列表
message ListStrategiesRequest {
    uint32 scene_type = 1;// 场景类型，必填
    string name = 2; // 规则名称名称
    uint32 page_number = 3; // 页码
    uint32 page_size = 4; // 每页大小
}

// 响应策略列表
message ListStrategiesResponse {
    repeated Strategy strategies = 1;
    uint32 total = 2;
}

// 获取策略详情请求
message GetStrategyRequest {
    uint32 id = 1;
}

// 获取策略详情响应
message GetStrategyResponse {
    Strategy strategy = 1;
}

// 创建策略请求
message CreateStrategyRequest {
    Strategy strategy = 1;
}

// 创建策略响应
message CreateStrategyResponse {
    uint32 id = 1;
}

// 更新策略请求
message UpdateStrategyRequest {
    Strategy strategy = 1;
}

// 更新策略响应
message UpdateStrategyResponse {
}

// 暂停策略请求
message PauseStrategyRequest {
    uint32 id = 1;
}

// 暂停策略响应
message PauseStrategyResponse {
}

// 启动策略请求
message StartStrategyRequest {
    uint32 id = 1;
}

// 启动策略响应
message StartStrategyResponse {
}

// 删除策略请求
message DeleteStrategyRequest {
    uint32 id = 1;
}

// 删除策略响应
message DeleteStrategyResponse {
}

// 获取过滤规则请求
message GetFilterRulesRequest {}

// 获取过滤规则响应
message GetFilterRulesResponse {
    repeated FilterRule filter_rules = 1;
}

// 获取场景列表请求
message GetScenesRequest {}

// 获取场景列表响应
message GetScenesResponse {
    repeated Scene list = 1;
}

message ListRecallSourcesRequest {
    uint32 page_number = 1;
    uint32 page_size = 2;
    string name = 3; // 召回源名称
    uint32 id = 4; // 召回源ID
}

message ListRecallSourcesResponse {
    repeated RecallSource recall_sources = 1;
    uint32 total = 2;
}

message GetRecallSourceDetailsRequest {
    uint32 recall_source_id = 1;
}

message GetRecallSourceDetailsResponse {
    RecallSource recall_source = 1;
}

message CreateRecallSourceRequest {
    RecallSource recall_source = 1;
}

message CreateRecallSourceResponse {
    uint32 recall_source_id = 1;
}

message UpdateRecallSourceRequest {
    RecallSource recall_source = 1;
}

message UpdateRecallSourceResponse {}

message DeleteRecallSourceRequest {
    uint32 recall_source_id = 1;
}

message DeleteRecallSourceResponse {}

message GetRecallSourceRuleListRequest {
}

message GetRecallSourceRuleListResponse {
    repeated RecallSourceRule rules = 1;
}

message GetDefaultSortParameterTableRequest {

}

message GetDefaultSortParameterTableResponse {
    SortParameterTable sort_parameter_table = 1;
}

message UpdateDefaultSortParameterTableRequest {
    SortParameterTable sort_parameter_table = 1;
}

message UpdateDefaultSortParameterTableResponse {
}
