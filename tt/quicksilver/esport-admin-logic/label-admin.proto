syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/esport_admin_logic";

package esport_admin_logic;


service EsportLabelAdmin {
    // CreateLabel 创建大神或技能标识
    rpc CreateLabel (CreateLabelRequest) returns (CreateLabelResponse) {}
    // EditLabel 编辑标识配置
    rpc EditLabel (EditLabelRequest) returns (EditLabelResponse) {}
    // DeleteLabel 删除标识配置
    rpc DeleteLabel (DeleteLabelRequest) returns (DeleteLabelResponse) {}
    // ListLabels 查询标识列表
    rpc ListLabels (ListLabelsRequest) returns (ListLabelsResponse) {}
    // IssueLabel 发放标识
    rpc IssueLabel (IssueLabelRequest) returns (IssueLabelResponse) {}
    // RevokeLabel 用于回收已发放标识。
    rpc RevokeLabel (RevokeLabelRequest) returns (RevokeLabelResponse);
    // QueryIssuanceRecords 用于查询发放记录列表。
    rpc QueryIssuanceRecords (QueryIssuanceRecordsRequest) returns (QueryIssuanceRecordsResponse);
    // AddRenownedPlayer 发放知名选手标识
    rpc AddRenownedPlayer (AddRenownedPlayerRequest) returns (AddRenownedPlayerResponse) {}
    // ListRenownedPlayers 查询知名选手列表
    rpc ListRenownedPlayers (ListRenownedPlayersRequest) returns (ListRenownedPlayersResponse) {}
    // 检查教练身份和检查是否拥有游戏技能
    rpc CheckCoachIdentityAndSkill (CheckCoachIdentityAndSkillRequest) returns (CheckCoachIdentityAndSkillResponse);
    // BatchRemoveRenownedPlayers 批量移除知名选手
    rpc BatchRemoveRenownedPlayers (BatchRemoveRenownedPlayersRequest) returns (BatchRemoveRenownedPlayersResponse) {}
    // UpdateRenownedPlayerRequest 更新知名选手信息
    rpc UpdateRenownedPlayer (UpdateRenownedPlayerRequest) returns (UpdateRenownedPlayerResponse) {}
    // IssueLabelPreCheck 发放标识前检查
    rpc IssueLabelPreCheck (IssueLabelRequest) returns (IssueLabelResponse) {}
}

// 价格单位
// buf:lint:ignore ENUM_PASCAL_CASE
enum GAME_PRICING_UNIT_TYPE {
    GAME_PRICING_UNIT_TYPE_INVALID = 0;
    GAME_PRICING_UNIT_TYPE_PER_30_MIN = 1; // 30分钟价格
    GAME_PRICING_UNIT_TYPE_PER_GAME = 2; // 每场游戏价格
}

// LabelType 标识类型
enum LabelType {
    LABEL_TYPE_UNSPECIFIED = 0; // 非法
    LABEL_TYPE_COACH = 1; // 大神标识
    LABEL_TYPE_SKILL = 2; // 技能标识
    LABEL_TYPE_SPECIAL = 3; // 特色标识
    LABEL_TYPE_VOICE = 4; // 语音标识,运营后台无需关注
}

// LabelInfo 标识信息
message LabelInfo {
    uint32 id = 1; // 标识ID
    LabelType label_type = 2; // 标识类型
    string label_image = 3; // 标识图片
    bool has_pricing = 4; // 是否加成价格
    uint32 pricing_amount = 5; // 加成价格，单位为T豆
    uint32 applicable_level = 6; // 标签可申请等级，如“X级及以上”
    uint32 display_order = 7; // 展示顺序，数字越小越靠前
    string label_description = 8; // 标识简介
    string label_requirements = 9; // 标识要求
    string label_name = 10; // 标识名称，仅对技能标识类型有效
    uint32 game_id = 11; // 技能 id，仅对技能标识类型有效
    string apply_entry = 12; // 申请入口(问卷链接)
    string game_name = 13;// 技能名称，仅对技能标识类型有效
}

// 创建大神或技能标识
message CreateLabelRequest {
    LabelInfo label_info = 1; // 标识信息
}

// 创建标识响应
message CreateLabelResponse {
}

// 编辑标识配置
message EditLabelRequest {
    LabelInfo label_info = 1; // 标识信息
}

// 编辑标识响应
message EditLabelResponse {
}

// 删除标识配置
message DeleteLabelRequest {
    uint32 label_id = 1; // 标识ID
}

// 删除标识响应
message DeleteLabelResponse {
}

// 查询标识列表
message ListLabelsRequest {
    uint32 page_number = 1; // 页码
    uint32 page_size = 2;
    uint32 label_id = 3; // 标识ID（精确搜索）
    LabelType label_type = 4; // 标识类型（按类型）
    uint32 game_id = 5; // 游戏ID(搜索该游戏下所有标签)
}

// 查询标识列表响应
message ListLabelsResponse {
    repeated LabelInfo label_info_list = 1; // 标识信息列表
    uint32 total_cnt = 2; // 记录总数
}

// IssueLabelRequest 发放标识请求
message IssueLabelRequest {
    uint32 label_id = 1; // 标识ID
    repeated uint32 uid = 2; // 用户 id
    int64 effective_start_time = 3; // 生效开始时间
    int64 effective_end_time = 4; // 生效结束时间
}

// IssueLabelResponse 发放标识响应
message IssueLabelResponse {
    repeated uint32 fail_coach_id = 1;// 校验失败的大神id
}

// RevokeLabelRequest 用于回收已发放标识
message RevokeLabelRequest {
    uint32 label_id = 1; // 标识ID
}

// RevokeLabelResponse 用于回收已发放标识
message RevokeLabelResponse {
}


enum IssuanceStatus {
    ISSUANCE_STATUS_UNSPECIFIED = 0; // 非法
    ISSUANCE_STATUS_EFFECTIVE = 1; // 生效中
    ISSUANCE_STATUS_EXPIRED = 2; // 已过期
    ISSUANCE_STATUS_PENDING = 3; // 待生效
}

// QueryIssuanceRecordsRequest 用于查询发放记录列表
message QueryIssuanceRecordsRequest {
    int32 page_number = 1;
    int32 page_size = 2;
    uint32 coach_ids = 3; // 教练 id 列表
    uint32 label_id = 4; // 标识ID
    IssuanceStatus status = 5; // 状态
    LabelType label_type = 6; // 标识类型
}

// 发放记录的数据模型。
message IssuanceRecord {
    uint32 id = 1;
    uint32 uid = 2; // 用户的 id
    int64 start_time = 3;
    int64 end_time = 4;
    LabelInfo label_info = 5;
}

// QueryIssuanceRecordsResponse 用于查询发放记录列表
message QueryIssuanceRecordsResponse {
    repeated IssuanceRecord issuance_record_list = 1; // 发放记录列表
    uint32 total_cnt = 2; // 记录总数
}

// AddRenownedPlayerRequest 发放知名选手标识
message AddRenownedPlayerRequest {
    message RenownedPlayerInfo {
        uint32 uid = 1; // 大神的用户 id
        string skill_name = 2; // 技能名称
        int64 price = 3; // 知名选手的游戏价格
    }
    repeated RenownedPlayerInfo info_list = 1; // 知名选手列表
}

// AddRenownedPlayerResponse 发放知名选手标识
message AddRenownedPlayerResponse {
    string err_msg = 1; // 错误信息
}

// ListRenownedPlayersRequest 查询知名选手列表
message ListRenownedPlayersRequest {
    int32 page_number = 1;
    int32 page_size = 2;
    uint32 uid = 3;// 大神的用户 id
    string game_name = 4; // 技能名称
}

// RenownedPlayerInfo 知名选手信息
message RenownedPlayerInfo {
    uint32 id = 1; // 记录 id
    uint32 uid = 2; // 大神uid
    string username = 3; // 大神ttid
    string nickname = 4; // 大神昵称
    uint32 game_id = 5; // 技能id
    int64 order_price = 6; // 接单价格
    GAME_PRICING_UNIT_TYPE game_pricing_unit_type = 7; // 价格单位
    string game_name = 8; // 技能名称
    int64 update_time = 9; // 更新时间
    int64 create_time = 10; // 创建时间
}

// ListRenownedPlayersResponse 查询知名选手列表响应
message ListRenownedPlayersResponse {
    repeated RenownedPlayerInfo player_infos = 1; // 知名选手信息列表
    uint32 total_cnt = 2; // 总数
}

// BatchRemoveRenownedPlayersRequest 批量移除知名选手
message BatchRemoveRenownedPlayersRequest {
    repeated uint32 id_list = 1; // 记录 id 列表
}

// BatchRemoveRenownedPlayersResponse 批量移除知名选手响应
message BatchRemoveRenownedPlayersResponse {
    string err_msg = 1; // 错误信息
}

// UpdateRenownedPlayerRequest 更新知名选手信息
message UpdateRenownedPlayerRequest {
    uint32 id = 1; // 记录 id
    int64 order_price = 2; // 接单价格
}

// UpdateRenownedPlayerResponse 更新知名选手信息响应
message UpdateRenownedPlayerResponse {
    string err_msg = 1; // 错误信息
}

message CheckCoachIdentityAndSkillRequest {
    message CheckItem {
        uint32 coach_id = 1;// 教练 id
        string game_name = 2;// 技能名称
    }
    repeated CheckItem check_list = 1; // 检查列表
}

// 检查不同过则err_msg非空
message CheckCoachIdentityAndSkillResponse {
    string err_msg = 1; // 错误信息
}
