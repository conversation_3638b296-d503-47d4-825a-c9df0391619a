syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/presentprivilege";
package presentprivilege;


service PresentPrivilegeServer {
  rpc AddPrivilege (AddPrivilegeReq) returns (AddPrivilegeResp) {
  }
  rpc GetPrivilegeList (GetPrivilegeListReq) returns (GetPrivilegeListResp) {
  }

  rpc DelPrivilegeList (DelPrivilegeListReq) returns (DelPrivilegeListResp) {
  }

  rpc GetPresentPrivilege (GetPresentPrivilegeReq) returns (GetPresentPrivilegeResp) {
  }

  rpc CheckPrivilege (CheckPrivilegeReq) returns (CheckPrivilegeResp) {
  }

  rpc AddTreasurePrivilege (AddTreasurePrivilegeReq) returns (AddTreasurePrivilegeResp) {
  }

  rpc DelTreasurePrivilegeList (DelTreasurePrivilegeListReq) returns (DelTreasurePrivilegeListResp) {
  }

  rpc GetTreasurePrivilegeList (GetTreasurePrivilegeListReq) returns (GetTreasurePrivilegeListResp) {
  }

  rpc GetTreasurePrivilege (GetTreasurePrivilegeReq) returns (GetTreasurePrivilegeResp) {
  }

  rpc CheckTreasurePrivilege (CheckTreasurePrivilegeReq) returns (CheckTreasurePrivilegeResp) {
  }

  rpc GetConditionVal (GetConditionValReq) returns (GetConditionValResp) {
  }

  rpc GetTreasurePrivilegeHistory (GetTreasurePrivilegeHistoryReq) returns (GetTreasurePrivilegeHistoryResp) {
  }

  rpc AddConditionValForTest (AddConditionValForTestReq) returns (AddConditionValForTestResp) {
  }

  rpc GetNowPrivilegePeople (GetNowPrivilegePeopleReq) returns (GetNowPrivilegePeopleResp) {
  }
}

//条件类型
enum ConditionType {
  NORMAL = 0; //普通
  RECHARGE = 1;//充值
  SEND_GIFT = 2;//送礼
  NOBILITY_LEVEL = 3;//贵族等级
  RICH_LEVEL = 4;//财富等级
  CONSUMER = 5;//消费T豆
}

enum GiftType{
  GIFT_T_NORMAL = 0;
  GIFT_SPECIAL = 1;
}

//条件值
message ConditionValue {
  uint64 value = 1; //根据ConditionType不同，对应不同的值,到100,
  uint32 level = 2; //等级1的礼物
  uint32 begin_time = 3;//充值、送礼 这两种类型开始结束时间
  uint32 end_time = 4;
  uint32 seconds = 5;//时间（秒）//添加多少权限时间，后台统一为秒传过来
}

message Condition{
  ConditionType type = 1; //RICH_LEVEL = 4;//财富等级
  ConditionValue value = 2;
}

//后台礼物权限发放
message Privilege {
  uint32 id = 1; //配置ID
  uint32 gift_id = 2;//礼物ID
  repeated Condition condition = 3; //条件,可以多个
  uint32 update_time = 4;
}

message AddPrivilegeReq{
  string order_id = 1; //唯一订单，避免重复发
  repeated uint32 uid_list = 2;
  Privilege privilege = 3;
}

message AddPrivilegeResp{
}

message CheckPrivilegeReq{
  Privilege privilege = 1;
}

message CheckPrivilegeResp{
}

//取权限列表接口
message GetPrivilegeListReq{
  uint32 off = 1;
  uint32 limit = 2;
}

message GetPrivilegeListResp{
  repeated Privilege privilege_list = 1;
  uint32 total = 2;
}
//删除权限接口
message DelPrivilegeListReq{
  uint32 id = 1; //需要删除的配置ID
}

message DelPrivilegeListResp{
}

//礼物权限
message PresentPrivilege{
  uint32 gift_id = 1;
  uint32 level = 2; //权限等级

  uint32 begin_time = 3; //权限开始
  uint32 expire_time = 4;//权限结束

  uint64 value = 5; //目前数值
  uint32 days = 6; //增加天数
}

//去用户礼物权限列表
message GetPresentPrivilegeReq{
  uint32 uid = 1;
}

message GetPresentPrivilegeResp{
  repeated PresentPrivilege privilege_list = 1;
  map<string, uint64> map_condition_value = 2;
}

message UserChangeItem {
  uint32 uid = 1;
  uint32 gift_id = 2;
  uint32 level = 3;
}

message PresentPrivilegeChangeEvent {
  repeated UserChangeItem change_list = 1;
}


// 礼物权限 - 新
// 与旧的PresentPrivilege的区别主要在于有发放时间是明确的而非跟随礼物本身，有相对时间/截止日期两种发放方式。

message TreasurePrivilege{
  uint32 gift_id = 1;
  uint32 begin_time = 2; //权限开始
  uint32 end_time = 3;//权限结束

  repeated TreasurePrivilegeCondition condition = 4; // 权限条件
}

// 权限条件
message TreasurePrivilegeCondition{
  uint32 gift_id = 1; // 礼物id
  ConditionType condition_type = 2; // 条件类型
  uint32 condition_value = 3; // 条件当前值
  uint32 condition_target_value = 4; // 满足条件所需值
}

message GetTreasurePrivilegeReq{
  uint32 uid = 1;
}

message GetTreasurePrivilegeResp{
  repeated TreasurePrivilege treasure_privilege = 1;
}

//条件值
message TreasureConditionValue {
  uint64 value = 1; //根据ConditionType不同，对应不同的值,到100,
  uint32 begin_time = 2;//充值、送礼 这两种类型开始结束时间
  uint32 end_time = 3;
  uint32 privilege_seconds = 4; //添加多少权限时间，后台统一为秒传过来
  uint32 privilege_end = 5    ; //添加多少权限时间，按截止时间发放，与privilege_seconds只能存在一个，privilege_seconds优先生效。
  uint32 expire_time = 6   ; // 统计的截止时间，在这个时间之后不会再发放权限。
}

message TreasureCondition{
  ConditionType type = 1; //RICH_LEVEL = 4;//财富等级
  TreasureConditionValue value = 2;
}

//后台礼物权限发放
message TreasurePrivilegeConfig {
  uint32 id = 1; //配置ID
  uint32 gift_id = 2;//礼物ID
  repeated TreasureCondition condition = 3; //条件,可以多个
  uint32 update_time = 4;
}

message AddTreasurePrivilegeReq{
  string order_id = 1; //唯一订单，避免重复发
  repeated uint32 uid_list = 2;
  TreasurePrivilegeConfig privilege = 3;
  uint32 end_time = 4;  // 发放的截止日期
  uint32 end_time_rel = 5;  // 发放的相对时间
}

message AddTreasurePrivilegeResp{
  repeated TreasureEnd privilege = 1;
}

message TreasureEnd {
  uint32 uid = 1;
  uint32 gift_id = 2;
  uint32 end_time = 3;
}

//取权限列表接口
message GetTreasurePrivilegeListReq{
  uint32 off = 1;
  uint32 limit = 2;
}

message GetTreasurePrivilegeListResp{
  repeated TreasurePrivilegeConfig privilege_list = 1;
  uint32 total = 2;
}

//删除权限发放配置接口
message DelTreasurePrivilegeListReq{
  repeated uint32 id = 1; //需要删除的配置ID
}

message DelTreasurePrivilegeListResp{
}

message CheckTreasurePrivilegeReq{
  TreasurePrivilegeConfig privilege = 1;
}

message CheckTreasurePrivilegeResp{
}


message GetConditionValReq{
  uint32 uid = 1;
  repeated TimeRange time_range = 2;
}

message TimeRange {
  uint32 begin_time = 1;
  uint32 end_time = 2;
}

message GetConditionValResp{
  repeated TimeValue time_value = 1;
}

message TimeValue {
  uint32 begin_time = 1;
  uint32 end_time = 2;
  repeated conditionVal condition_val = 3;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message conditionVal {
  ConditionType typ = 1;
  uint64 val = 2;
}

message GetTreasurePrivilegeHistoryReq{
  uint32 uid = 1;  // 要获取记录的用户uid
}

message GetTreasurePrivilegeHistoryResp{
  repeated TreasurePrivilegeHistory history_list = 1;
}

message TreasurePrivilegeHistory {
  string gift_icon = 1; // 礼物缩略图
  string gift_name = 2; // 礼物名
  uint32 gift_price = 3; // 礼物价格
  uint32 gain_time = 4; // 权限获取时间戳
  uint32 fin_time = 5 ; // 权限到期时间 (截止时间，秒级时间戳)
  uint32 fin_seconds = 6 ; // 权限到期时间（相对时间，秒级）
}

message AddConditionValForTestReq{
  uint32 uid = 1;
  ConditionType typ = 2;
  uint32 val = 3;
  uint32 time = 4;  // 时间戳
}

message AddConditionValForTestResp{

}

message GetNowPrivilegePeopleReq{
}

message GetNowPrivilegePeopleResp{
  map<uint32,uint32> user_map = 1;
}