syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/account-apicenter";

package account_apicenter;

service AccountApicenter {
  // 封禁用户
  rpc BanUser(BanUserReq) returns (BanUserResp) {}
  // 解封用户
  rpc RecoverUser(RecoverUserReq) returns (RecoverUserResp) {}
  // 批量解封用户
  rpc BatchRecoverUser(BatchRecoverUserReq) returns (BatchRecoverUserResp) {}
  // 批量封禁用户
  rpc BatchBanUserWithDevice(BatchBanUserWithDeviceReq) returns (BatchBanUserWithDeviceResp) {}
  // 更新封禁审核记录
  rpc UpdateBannedCheckRecord (UpdateBannedCheckRecordReq) returns (UpdateBannedCheckRecordResp) {}

  // 创建内部账号
  rpc CreateInternalAccount(CreateInternalAccountRequest) returns (CreateInternalAccountResponse) {}

  // 更新用户相册
  rpc UpdatePhotoAlbum(UpdatePhotoAlbumRequest) returns (UpdatePhotoAlbumResponse) {}
}

message UpdatePhotoAlbumRequest {
  // 用户ID
  uint32 uid = 1;
  // 客户端信息，主要用于找到对应客户端上传的 obs 域名 和 送审相关，serviceInfo 里面有值的可以不用填。
  ClientInfo client_info = 2;
  // 旧版obs
  repeated string img_key_list = 3;
  // 新版obs，全量key
  repeated string new_img_key_list = 4;
}

message ClientInfo {
  // 马甲包
  uint32 market_id = 1;
  // 客户端类型
  uint32 client_type = 2;
  // 客户端 ip
  uint32 client_ip = 3;
}

message UpdatePhotoAlbumResponse {

}

enum BanType {
  // 未指定
  OP_UNKNOWN = 0;
  // 封禁
  OP_BAN = 1;
  // 解封
  OP_UNBAN = 2;
}

enum OpSourceType {
  // 未指定
  OP_SOURCE_TYPE_UNSPECIFIED = 0;
  // 黑色运营后台
  OP_SOURCE_TYPE_BACKSTAGE = 1;
}

message BanUserReq {
  // 账号
  string account = 1;
  // 封禁原因
  string reason = 2;
  // 操作人
  string operator_name = 3;
  // 自动解封时间
  int64 auto_recovery_at = 4;
  // 是否封禁设备
  bool with_device = 5;
  // 证明图片
  string proof_pic = 6;
  // 原因详情
  string reason_detail = 7;
  // 封禁时长，单位天。 永久封禁填 **********
  uint64 banned_days = 8;
  // 业务错误返回在 resp 里
  bool ret_resp_err = 9;
  // ttid 为空时，使用 uid
  uint32 uid = 10;
  // 操作来源
  OpSourceType op_source = 11;
}

message BanUserResp {
  // 错误码
  int32 err_code = 1;
  // 错误描述
  string err_msg = 2;
}

message RecoverUserReq {
  // 账号
  string account = 1;
  // 解封原因
  string reason = 2;
  // 操作人
  string operator_name = 3;
  // 是否解封设备
  bool is_recover_device = 4;
  // 操作来源
  OpSourceType op_source = 5;
}

message RecoverUserResp {
}

message BatchRecoverUserReq {
  // 解封列表
  repeated RecoverUserInfo list = 1;
  // 操作人
  string operator_name = 2;
  // 是否为检查操作
  bool is_check = 3;
  // 操作来源
  OpSourceType op_source = 4;
}

message RecoverUserInfo {
  // uid
  uint32 uid = 1;
  // 解封原因
  string reason = 2;
  // 是否解封设备
  bool is_recover_device = 4;
}

message BatchRecoverUserResp {
  // 校验可成功解封账号的数量
  uint32 valid_num = 1;
}


message BatchBanUserWithDeviceReq {
  // 设备ID
  string device_id = 1;
  // 需要封禁的uid列表
  repeated uint32 uid_list = 2;
  // 操作类型
  BanType op_type = 3;
  // 操作原因
  string reason = 4;
  // 自动解封时间
  int64  auto_recovery_at = 5;
  // 相关图片
  string proof_pic = 6;
  // 原因详情
  string reason_detail = 7;
  // 封禁时长，单位天。 永久封禁填 **********
  uint64 banned_days = 8;
  // 操作人名称
  string operator_name = 9;
  // 操作来源
  OpSourceType op_source = 10;
}

message BatchBanUserWithDeviceResp {
}

message UpdateBannedCheckRecordReq {
  enum UpdateType {
    // 未指定
    UPDATE_TYPE_UNSPECIFIED = 0;
    // 解封
    UPDATE_TYPE_RECOVER = 1;
    // 保持封禁
    UPDATE_TYPE_KEEP_BANNED = 2;
    // 驳回解封
    UPDATE_TYPE_REJECT_RECOVER = 3;
    // 解封审批中
    UPDATE_TYPE_CHECKING = 4;
    // 更新为待核实
    UPDATE_TYPE_TO_CHECK = 5;
  }
  // 更新类型
  UpdateType update_type = 1;
  // 批量解封工单id
  repeated uint32 task_id_list = 2;
  // 操作人
  string operator_name = 3;
  // 解封原因
  string reason = 4;
  // 是否解封设备
  bool is_recover_device = 5;
  // 操作来源
  OpSourceType op_source = 6;
}

message UpdateBannedCheckRecordResp {
}

message CreateInternalAccountRequest {
  // 调用方场景，必填.
  string caller_scene = 1;
  // 内部账号类型，必填. 详见 InternalAccountType
  uint32 internal_account_type = 2;
  // 员工工号，当 internal_account_type 为 INTERNAL_ACCOUNT_TYPE_REGULAR_EMPLOYEE 时必填.
  uint32 employee_id = 3;
  // 手机号，可选. 不填系统自动生成 91 开头的 11 位数字。
  string phone = 4;
  // 性别，可选. 不填默认女性。 see account.proto USER_SEX
  int32 sex = 5;
  // 密码，可选. 不填系统自动生成。正常 8 位及以上含数字和大小写字母，md5 加密。
  string password = 6;
  // 昵称，可选. 不填系统自动生成。
  string nickname = 7;
}

message CreateInternalAccountResponse {
  // 创建的内部账号 uid.
  uint32 uid = 1;
  // tt id; 对应 account.proto 的 UserResp.alias 字段.
  string tt_id = 2;
}

enum InternalAccountType {
  // 未定义内部账号类型
  INTERNAL_ACCOUNT_TYPE_UNSPECIFIED = 0;
  // 正式员工内部账号， 以 20 + 员工工号开头的 10 位数字
  INTERNAL_ACCOUNT_TYPE_REGULAR_EMPLOYEE = 1;
  // 特殊内部账号，以 210 开头 的 10 位数字
  INTERNAL_ACCOUNT_TYPE_SPECIAL = 2;
  // 推荐工具内部账号，以 211 开头的 10 位数字， 最多创建 10 万个
  INTERNAL_ACCOUNT_TYPE_RECOMMENDATION_TOOL = 3;
}

