syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/probgamecenter";
package probgamecenter;


service ProbGameCenter {
    rpc CheckFuse (CheckFuseReq) returns (CheckFuseResp) {}

    //运维接口
    rpc UnFuse (UnFuseReq) returns (UnFuseResp) {}
}

message CheckFuseItem
{
    int64 consume_value = 1;
    int64 prize_value = 2;
    string order_id = 3;
    uint32 uid = 4;
}

//同一个业务多个CheckFuseItem,其中一个失败会全部失败
message CheckFuseReq {
    string business_id = 1;
    string deal_token = 2;
    repeated CheckFuseItem check_item_list = 3;
}

message CheckFuseResult {
    string order_id = 1;
    bool is_fuse = 2;   // 熔断检查结果，true熔断
    string reason = 3;
    string deal_token = 4;
}

message CheckFuseResp {
    repeated CheckFuseResult check_result_list = 1;
}

message UnFuseReq{
    string business_id = 1;
}

message UnFuseResp{}

