syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/settlement-bill";
package settlement_bill;

service SettlementBill {
  // 创建结算单
  rpc CreateSettlementBill(CreateSettlementBillReq) returns (CreateSettlementBillResp) {}
  // 税点录入
  rpc RecordTaxRate(RecordTaxRateReq) returns (RecordTaxRateResp) {}
  // 获取当前时间点会长税点
  rpc GetGuildTaxRate(GetGuildTaxRateReq) returns (GetGuildTaxRateResp) {}
  // 获取税点列表
  rpc GetTaxRateList(GetTaxRateListReq) returns (GetTaxRateListResp) {}
  // 批量获取税点
  rpc BatchGetTaxRate(BatchGetTaxRateReq) returns (BatchGetTaxRateResp) {}
  // 修改税点
  rpc SetTaxRate(SetTaxRateReq) returns (SetTaxRateResp) {}
  // 额外收益录入
  rpc RecordExtraIncome(RecordExtraIncomeReq) returns (RecordExtraIncomeResp) {}
  // 关联发票到结算单
  rpc AssociateReceipt(AssociateReceiptReq) returns (AssociateReceiptResp) {}
  // 获取额外收益录入列表
  rpc GetExtraIncomeRecordList(GetExtraIncomeRecordListReq) returns (GetExtraIncomeRecordListResp) {}
  // 获取发票有效总金额
  rpc GetReceiptTotalAmount(GetReceiptTotalAmountReq) returns (GetReceiptTotalAmountResp) {}

  // 获取额外收益详情列表 - 深度
  rpc GetExtraIncomeDetailDeepCoop(GetExtraIncomeDetailReq) returns (GetExtraIncomeDetailDeepCoopResp) {}
  // 获取额外收益详情列表 - 主播补贴
  rpc GetExtraIncomeDetailChannelSubsidy(GetExtraIncomeDetailReq) returns (GetExtraIncomeDetailChannelSubsidyResp) {}
  // 获取额外收益详情列表 - 新公会补贴
  rpc GetExtraIncomeDetailNewGuildSubsidy(GetExtraIncomeDetailReq) returns (GetExtraIncomeDetailNewGuildSubsidyResp) {}
  // 获取额外收益详情列表 - 扣款
  rpc GetExtraIncomeDetailDeduct(GetExtraIncomeDetailReq) returns (GetExtraIncomeDetailDeductResp) {}
  // 获取多人互动额外收益预付款金额（用于结算）
  rpc GetAmuseExtraIncomePrepaid(GetAmuseExtraIncomePrepaidReq) returns (GetAmuseExtraIncomePrepaidResp) {}

  // 获取结算单详情
  rpc GetSettlementBill(GetSettlementBillReq) returns (GetSettlementBillResp) {}
  // 获取待上传发票的结算单
  rpc GetSettlementBillWaitReceipt(GetSettlementBillWaitReceiptReq) returns (GetSettlementBillWaitReceiptResp) {}
  // 获取待提现的结算单列表
  rpc GetSettlementBillWaitWithdraw(GetSettlementBillWaitWithdrawReq) returns (GetSettlementBillWaitWithdrawResp) {}
  // 获取会长预付金额
  rpc GetPrepaidMoney(GetPrepaidMoneyReq) returns (GetPrepaidMoneyResp) {}
  // 获取会长扣款金额
  rpc GetDeductMoney(GetDeductMoneyReq) returns (GetDeductMoneyResp) {}
  // 获取发票单列表（分页）
  rpc GetReceiptBillList(GetReceiptBillListReq) returns (GetReceiptBillListResp) {}
  // 获取发票单关联的所有发票与运单
  rpc GetReceiptList(GetReceiptListReq) returns (GetReceiptListResp) {}
  // 获取发票单关联的所有结算单数据
  rpc GetAssociatedBillItems(GetAssociatedBillItemsReq) returns (GetAssociatedBillItemsResp) {}
  // 记录发票文件
  rpc RecordReceiptFile(RecordReceiptFileReq) returns (RecordReceiptFileResp) {}
  // 生成结算单PDF
  rpc GenSettleBillPdf(GenSettleBillPdfReq) returns (GenSettleBillPdfResp) {}
  // 多人互动深度合作待提现数据
  rpc GetWaitWithdrawDeepCoop(GetWaitWithdrawDeepCoopReq) returns (GetWaitWithdrawDeepCoopResp) {}
  // 语音主播新公会补贴待提现数据
  rpc GetWaitWithdrawYuyinSubsidy(GetWaitWithdrawYuyinSubsidyReq) returns (GetWaitWithdrawYuyinSubsidyResp) {}
  // 获取待提现的所有周期（月份）
  rpc GetWaitWithdrawMonths(GetWaitWithdrawMonthsReq) returns (GetWaitWithdrawMonthsResp) {}
  // 获取单个结算单所有周期（月份）
  rpc GetMonthsBySettleBillId(GetMonthsBySettleBillIdReq) returns (GetMonthsBySettleBillIdResp) {}
  // 结算单确认提现
  rpc ConfirmWithdraw(ConfirmWithdrawReq) returns (ConfirmWithdrawResp) {}
  // 获取扣款详情
  rpc GetDeductMoneyDetailByBillId(GetDeductMoneyListByBillIdReq) returns (GetDeductMoneyListByBillIdResp) {}
  // 获取主播积分提现记录
  rpc GetAnchorScoreWithdrawRecords(GetAnchorScoreWithdrawRecordsReq) returns (GetAnchorScoreWithdrawRecordsResp) {}

  // 手动推送确认扣款报表
  rpc ReportConfirmDeductMoney(ReportConfirmReq) returns (ReportConfirmResp) {}
  // 手动推送确认深度合作收益报表
  rpc ReportConfirmDeepCoop(ReportConfirmReq) returns (ReportConfirmResp) {}
  // 手动推送确认语音补贴报表
  rpc ReportConfirmYuyinSubsidy(ReportConfirmReq) returns (ReportConfirmResp) {}
  // 结算报表
  rpc ReportConfirmWith(ReportConfirmWithReq) returns (ReportConfirmWithResp) {}

  // 查询是否允许提现
  rpc IsAllowWithdraw(IsAllowWithdrawReq) returns (IsAllowWithdrawResp) {}
  // 非对公提现
  rpc PrivateWithdraw(PrivateWithdrawReq) returns (PrivateWithdrawResp) {}

  // 通过灵犀审批单ID获取结算单详情
  rpc GetSettlementBillDetailByInstId(GetSettlementBillDetailByInstIdReq) returns (GetSettlementBillDetailByInstIdResp) {}

  // 运营后台 录入活动奖励
  rpc RecordActivityReward(RecordActivityRewardReq) returns (RecordActivityRewardResp) {}
  // 运营后台 获取公会活动奖励汇总列表
  rpc GetGuildActivityRewardSummaryList(GetGuildActivityRewardSummaryListReq) returns (GetGuildActivityRewardSummaryListResp) {}
  // 运营后台 获取公会成员活动奖励汇总列表
  rpc GetGuildActivityRewardMemberSummaryList(GetGuildActivityRewardMemberSummaryListReq) returns (GetGuildActivityRewardMemberSummaryListResp) {}
  // 运营后台 获取公会成员活动奖励明细列表
  rpc GetGuildActivityRewardMemberDetailList(GetGuildActivityRewardMemberDetailListReq) returns (GetGuildActivityRewardMemberDetailListResp) {}

  // 会长后台 获取结算单关联的活动奖励汇总
  rpc GetBillActivityRewardSummary(GetBillActivityRewardSummaryReq) returns (GetBillActivityRewardSummaryResp) {}
  // 会长后台 获取结算单关联的活动奖励成员明细
  rpc GetBillActivityRewardMemberDetail(GetBillActivityRewardMemberDetailReq) returns (GetBillActivityRewardMemberDetailResp) {}
}

// 结算单类型
enum SettlementBillType
{
  UnKnownBillType = 0;
  GiftScore = 1;          // 对公礼物积分
  AwardScore = 2;         // 对公奖励积分
  MaskPKScore = 3;        // 对公蒙面PK积分
  AmuseCommission = 4;    // 多人互动会长佣金 (娱乐房佣金)
  YuyinBaseCommission = 5;// 语音直播会长佣金 (语音基础佣金)
  MonthMiddle = 6;        // 语音直播会长额外奖励
  DeepCoop = 7;           // 多人互动会长额外奖励 (旧深度合作)
  YuyinSubsidy = 8;       // 语音直播新公会补贴&主播补贴
  KnightScore = 9;        // 对公骑士积分
  AmuseExtra = 10;        // 多人互动会长额外奖励 (新自动结算 2022.11)
  InteractGameCommission = 11; // 互动游戏会长佣金
  InteractGameExtraCommission = 12; // 互动游戏会长额外奖励
  ESportScore = 13; // 对公电竞积分
  ESportCommission = 14; // 电竞会长佣金
  ActivityReward = 15; // 活动奖励
}

// 结算周期
enum SettlementCycle
{
  UnKnownSettlementCycle = 0;
  SettlementCycleWeekly = 1; // 周结
  SettlementCycleMonthly = 2; // 月结
}

// 结算单状态
enum SettlementBillStatus
{
  Creating = 0; // 结算单创建中
  WaitWithdraw = 1; // 待确认提现（创建完成）
  WaitReceipt = 3; // 待上传发票（确认提现完成）
  WaitAudit = 5; // 待审核（包含OA流程所有审核状态）
  WaitAdjustReceipt = 6; // 待再次上传发票（驳回后）
  Finished = 7; // 完成（当OA流程完成时）
}

// 灵犀审核状态
enum OAAuditBillStatus
{
  OAAuditCreating = 0; // 创建中
  OAFirstViewing = 1; // 初审中
  OAReviewing = 2; // 复审中
  OARefused = 3; // 驳回
  OAFinished = 4; // 完成
}

enum PrivateWithdrawStatus
{
  PWSInvalid = 0;
  PWSCreating = 1; // 创建中
  PWSSuccess = 2; // 失败
  PWSFail = 3; // 失败
  PWSLimitReqFail = 4; // 提现失败，请求限额失败（重试）
  PWSLimitOverFail = 5; // 提现失败，条件不符，超出限额（不重试）
  PWSComReqFail = 6; // 提现失败，请求佣金平台失败（重试）
  PWSComFail = 7; // 提现失败，条件不符（不重试）
  PWSTBeanRiskFail = 8; // 提现失败，豆商风控（不重试）
}

// 发票提交状态，用于检查重复发票
enum ReceiptStatus
{
  ReceiptWaitSubmit = 0; // 发票待提交
  ReceiptSubmitted = 1; // 发票已提交
}

// 风控状态
enum RiskStatus
{
  RiskNo = 0; // 未风控
  RiskTBean = 1; // T豆风控
}

message GiftScoreBill {
  uint64 gift_score = 1; // 对公礼物积分
}

message AwardScoreBill {
  uint64 award_score = 1; // 对公奖励积分
}

message AmuseCommissionBill {
  uint64 amuse_commission = 1; // 娱乐房佣金
  uint64 deduct_money = 2; // 扣款
}

message YuyinBaseCommissionBill {
  uint64 yuyin_base_commission = 1; // 语音直播基础佣金
  uint64 deduct_money = 2; // 扣款
}

// 对公蒙面pk积分结算单
message MaskPKScoreBill {
  uint64 mask_pk_score = 1; // 对公蒙面PK积分
}

// 月中会长佣金收益结算单
message MonthMiddleBill {
  uint64 yuyin_award_commission = 1; // 语音直播奖励佣金
}

// 多人互动深度合作结算单
message DeepCoopBill {
  uint64 deep_coop_money = 1; // 深度合作结算金额
  uint64 prepaid_money = 2; // 预付金额
}

// 语音直播补贴结算单
message YuyinSubsidyBill {
  uint64 yuyin_anchor_subsidy = 1; // 语音直播主播补贴
  uint64 yuyin_new_guild_subsidy = 2; // 语音直播新公会补贴
}

message GeneralBill {
  uint64 money = 1; // 结算金额
  uint64 deduct_money = 2; // 扣款金额
  uint64 prepaid_money = 3; // 预付金额
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CreateSettlementBillReq {
  // v2 创建时不记录金额
  // GeneralBill gift_score_bill = 1;
  // GeneralBill award_score_bill = 2;
  // GeneralBill mask_pk_score_bill = 3;
  // GeneralBill amuse_commission_bill = 4;
  // GeneralBill yuyin_base_commission_bill = 5;
  // GeneralBill month_middle_bill = 6;

  // 运营录入特殊处理
  GeneralBill deep_coop_bill = 7;
  YuyinSubsidyBill yuyin_subsidy = 8;

  SettlementBillType Bill_type = 20; // 结算单类型
  uint32 uid = 22; // 对公会长UID
  uint32 guild_id = 23; // 工会ID
  uint32 settle_start = 24; // 结算开始
  uint32 settle_end = 25; // 结算结束
  uint64 last_balance = 26; // 上期余额
  uint64 prepaid_money = 27; // 预付金额
  uint64 settle_money = 88; // 结算金额（分）实际结算金额以佣金平台为准
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CreateSettlementBillResp {
  string Bill_id = 1; // 结算单号
  bool is_merged = 2; // 是否合并
}

// 结算单详情结构
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SettlementBillDetail {
  string bill_id = 1; // 结算单ID
  SettlementBillType type = 2; // 结算单类型
  uint32 uid = 3; // 会长ID
  SettlementBillStatus status = 4; // 状态
  uint32 tax_rate = 5; // 税点
  uint64 income_sum = 6; // 总收益金额
  uint64 actual_income_pay = 7; // 实际付款金额
  uint32 create_time = 8; // 创建时间
  uint32 finished_time = 9; // 完成时间
  string fileId = 11; // 文件ID
  string fileUrl = 12; // 文件URL
  uint32 settle_start = 14; // 结算周期开始时间
  uint32 settle_end = 15; // 结算周期结束时间
  uint32 settle_date = 16; // 对账日期
  bool allow_withdraw = 17; // 允许提现

  GeneralBill general_bill_data = 101;
  GeneralBill deep_coop_bill = 107;
  YuyinSubsidyBill yuyin_subsidy = 108;
}

message GetGuildTaxRateReq {
  uint32 uid = 1; // 会长UID
}

message GetGuildTaxRateResp {
  uint32 tax_rate = 1; // 税点
  uint32 start_time = 2; // 生效日期
}

message TaxRate {
  uint32 tax_rate = 1; // 税点
  uint32 uid = 2;
  uint32 start_time = 3; // 生效日期

  // 仅列表展示
  uint32 create_time = 4; // 创建时间
  uint32 update_time = 5; // 更新时间
  string operator = 6; // 操作人
}

message RecordTaxRateReq {
  string operator = 1; // 操作人
  repeated TaxRate tax_rate_list = 4; // 录入列表
}

message RecordTaxRateResp {}

message SetTaxRateReq {
  uint32 uid = 1; // 会长UID
  uint32 tax_rate = 2; // 税点
  uint32 start_time = 3; // 生效日期
  string operator = 4; // 操作人
}

message SetTaxRateResp {}

message AssociateReceiptReq {
  uint32 uid = 1; // 会长ID
  repeated string receipt_ids = 2; // 发票IDs
  repeated string settlement_bill_ids = 3; // 结算单号
  repeated string express_order_ids = 4; // 运单号
  string receipt_bill_id = 5; // 发票单ID
}

message AssociateReceiptResp {
  string receipt_bill_id = 1; // 发票单ID TTFPXXX，对应一组发票与一组结算单，对应一个OA审核流程
}

// 额外收益类型
enum ExtraIncomeType {
  UnknownExtraType = 0;
  DeepCooperation = 1; // 深度合作
  YuyinChannelSubsidy = 2; // 语音直播主播补贴+新公会补贴
  Deduction = 3; // 扣款
  AmuseExtraPrepaid = 4; // 预付款
}

// 额外收益类型详情查询类型
enum ExtraIncomeDetailType {
  UnknownExtraDetailType = 0;
  DeepCooperationDetail = 1; // 深度合作
  YuyinChannelSubsidyDetail = 2; // 语音直播主播补贴
  YuyinNewGuildSubsidyDetail = 3; // 新公会补贴
  AmuseExtraPrepaidDetail = 4; // 预付款
}

// 深度合作单个房间收益信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DeepCooperationChannelIncome {
  uint64 flow_this_month = 1; // 本月房间流水
  uint64 flow_last_month = 2; // 上月房间流水
  int32 grow_rate = 3; // 房间流水增长率
  string channel_name = 4; // 房间名
  string guild_name = 5; // 所属公会靓号
  uint32 guild_display_id = 6; // 所属公会靓号
  string channel_type = 7; // 房间类型
  uint32 settlement_rate = 8; // 结算比例
  uint64 settlement_money = 9; // 房间结算金额
  uint32 ID = 10;
  uint32 channel_id = 11; // 房间id
}

// 深度合作收益信息
message DeepCooperationIncome {
  uint32 uid = 1; // 公会id
  uint64 total_flow_this_month = 2; // 本月总流水
  uint64 total_flow_last_month = 3; // 上月总流水
  int32 grow_rate = 4; // 总流水增长率
  uint64 settlement_money = 5; // 总结算金额
  uint64 prepaid_money = 6; // 预付金额
  uint64 actual_settlement_money = 7; // 实际结算金额
  string remark = 8; // 备注
  repeated DeepCooperationChannelIncome channels = 9; // 房间列表
}

// 单个主播补贴
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AnchorSubsidyIncome {
  uint32 ttid = 1; // TTid
  string nickname = 2; // TT昵称
  string tag = 3; // 品类
  string anchor_flag = 4; // 主播类型
  string guild_name = 5; // 所属公会
  string plan_date = 6; // 计划时间
  uint64 gift_flow = 7; // 主播个人收礼流水
  uint32 valid_live_days = 8; // 主播开播有效天
  uint64 subsidy_money = 9; // 补贴金额
  uint32 ID = 10;
}

// 语音直播主播补贴
message YuyinAnchorSubsidyIncome {
  uint32 uid = 1; // 会长ID
  uint64 subsidy_sum = 2; // 总补贴金额
  repeated AnchorSubsidyIncome anchors = 3; // 主播列表
}

// 新公会补贴收益信息
message YuyinNewGuildSubsidyIncome {
  uint32 uid = 1; // 会长ID
  string guild_name = 2; // 公会名称
  string join_time = 3; // 入驻时间
  uint64 flow_this_month = 4; // 当月流水
  string subsidy_date = 5; // 补贴时间
  uint64 subsidy_money = 6; // 补贴金额
}
// 扣款信息
message DeductMoney {
  uint32 uid = 1; // 会长ID
  uint64 amuse_deduct_money = 2; // 扣款金额（娱乐房佣金）
  string amuse_remark = 3; // 备注（娱乐房佣金）
  uint64 yuyin_deduct_money = 4; // 扣款金额（语音基础佣金）
  string yuyin_remark = 5; // 备注（语音基础佣金）
}

// 多人互动额外奖励预付款
message AmuseExtraPrepaidIncome {
  uint32 uid = 1; // 公会id
  uint64 prepaid_money = 2; // 预付金额
  string remark = 3; // 备注
}

message RecordExtraIncomeReq {
  uint32 settlement_date = 2; // 结算日期
  ExtraIncomeType income_type = 3; // 收益类型
  string operator = 4; // 操作人
  string file_name = 5; // 文件名
  string file_md5 = 6; // 文件特征码
  string file_url = 7; // 文件特征码

  repeated DeepCooperationIncome deep_cooperation_list = 10; // 深度合作列表
  repeated YuyinAnchorSubsidyIncome yuyin_anchor_subsidy_list = 11; // 语音直播补贴列表
  repeated YuyinNewGuildSubsidyIncome yuyin_new_guild_subsidy_list = 12; // 新公会补贴列表
  repeated DeductMoney deduct_list = 13; // 扣款列表
  repeated AmuseExtraPrepaidIncome amuse_extra_prepaid = 14; // 多人互动额外奖励预付款
}

message RecordExtraIncomeResp {

}

message GetTaxRateListReq {
  uint32 uid = 1; // 会长ID
  uint32 offset = 2;
  uint32 limit = 3;
}

message GetTaxRateListResp {
  repeated TaxRate list = 1; // 列表
  uint32 total = 2; // 总数
}

message BatchGetTaxRateReq {
  repeated uint32 uid_list = 1; // 会长ID列表
}

message BatchGetTaxRateResp {
  map<uint32, TaxRate> tax_rate_map = 1; // 税点列表
}

message GetExtraIncomeRecordListReq {
  uint32 uid = 1; // 会长ID
  uint32 offset = 2;
  uint32 limit = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ExtraIncomeRecordItem {
  uint32 ID = 1; // ID
  string operator = 2; // 操作人
  uint32 create_time = 3; // 创建时间
  ExtraIncomeType income_type = 4; // 收益类型
  uint32 settlement_date = 5; // 结算日期
  string file_name = 6; // 文件名
  string file_url = 7; // 文件url
}

message GetExtraIncomeRecordListResp {
  repeated ExtraIncomeRecordItem list = 1; // 列表
  uint32 total = 2; // 总数
}

message GetExtraIncomeDetailReq {
  uint32 uid = 1; // 会长ID
  uint32 offset = 2;
  uint32 limit = 3;
  uint32 settlement_date = 4; // 结算周期
}

message GetExtraIncomeDetailDeepCoopResp {
  uint32 total = 1; // 总数
  DeepCooperationIncome deep_cooperation = 6; // 深度合作列表
}

message GetExtraIncomeDetailChannelSubsidyResp {
  uint32 total = 1; // 总数
  YuyinAnchorSubsidyIncome yuyin_anchor_subsidy = 7; // 语音直播补贴列表
}

message GetExtraIncomeDetailNewGuildSubsidyResp {
  uint32 total = 1; // 总数
  YuyinNewGuildSubsidyIncome yuyin_new_guild_subsidy = 8; // 新公会补贴列表
}

message GetSettlementBillReq {
  uint32 uid = 1; // 会长ID
  string bill_id = 2; // 结算单ID
}

message GetSettlementBillResp {
  SettlementBillDetail bill = 1; // 结算单
}

message GetSettlementBillWaitReceiptReq {
  uint32 uid = 1; // 会长ID
  string receipt_bill_id = 2; // 发票工单ID，重新提交时
}

message GetSettlementBillWaitReceiptResp {
  repeated SettlementBillDetail bills = 1; // 结算单列表
  bool disable_verify = 2; // 禁用发票校验的会长
}

message GetPrepaidMoneyReq {
  uint32 uid = 1;
}

message GetPrepaidMoneyResp {
  uint64 prepaid_money = 1; // 预付金额
}

message GetDeductMoneyReq {
  uint32 uid = 1;
}

message GetDeductMoneyResp {
  uint64 amuse_deduct_money = 1; // 扣款金额（娱乐房佣金）
  uint64 yuyin_deduct_money = 2; // 扣款金额（语音基础佣金）
}

message GetReceiptBillListReq {
  uint32 uid = 1; // 会长uid
  uint32 offset = 2;
  uint32 limit = 3;
  repeated OAAuditBillStatus status = 4;
}

message GetReceiptBillListResp {
  // 单个TTFPXXX
  // buf:lint:ignore MESSAGE_PASCAL_CASE
  message receiptBill {
    string receipt_bill_id = 1; // 发票单ID
    uint32 status = 2; // 灵犀状态
    repeated string settlement_bill_id = 3; // 关联结算单
    string remark = 4; // 信息
  }
  repeated receiptBill receipt_bill_list = 1; // 发票单列表
  uint32 total = 2;
}

message GetReceiptListReq {
  string receipt_bill_id = 1; // 发票单ID
}
message GetReceiptListResp {
  // 一张发票
  message ReceiptItem {
    string receipt_id = 1; // 灵犀发票ID
    string file_name = 2; // 文件名
    string file_url = 3; // 文件URL
  }
  repeated ReceiptItem receipts = 1; // 发票列表
  repeated string express_order_ids = 2; // 运单号IDs
}

message GetAssociatedBillItemsReq {
  uint32 uid = 1; // 会长uid
  string receipt_bill_id = 2; // 发票单ID
}

message GetAssociatedBillItemsResp {
  repeated SettlementBillDetail list = 1;
}

message RecordReceiptFileReq {
  string receipt_id = 1;
  string file_name = 2;
  uint64 size = 3;
  string show_size = 4;
  string file_class = 5;
  uint32 uid = 6;
  string receipt_date = 7; // 开票日期
  string receipt_code = 8; // 发票代码
  string receipt_no = 9; // 发票号码
  string tax_rate = 10; // 发票税率
  uint64 tax_amount = 11; // 发票税额
  uint64 amount = 12; // 发票含税金额
  uint64 ex_tax_amount = 13; // 发票不含税金额
  string v_code = 14; // 校验码
  string content = 15; // 项目名称
  string seller_name = 16; // 销售方名称
  string seller_tax_no = 17; // 销售方纳税人识别号
  string purchaser_name = 18; // 购买方名称
  string purchaser_tax_no = 19; // 购买方纳税人识别号
  string verify_result = 20; // 校验结果
}

message RecordReceiptFileResp {
  string receipt_sign = 1;
  repeated ReceiptErrorInfo error_list = 2;
  bool result = 3;
}

message GenSettleBillPdfReq {
  string bill_id = 1; // 结算单ID
}

message GenSettleBillPdfResp {
  string file_id = 1; // 文件ID
}

message GetWaitWithdrawDeepCoopReq {
  uint32 uid = 1;
}

message GetWaitWithdrawDeepCoopResp {
  uint32 tax_rate = 1; // 税点
  uint64 income_sum = 2; // 总收益金额
  uint64 actual_income_pay = 3; // 实际付款金额
  GeneralBill deep_coop_bill = 15;
}

message GetWaitWithdrawYuyinSubsidyReq {
  uint32 uid = 1;
}

message GetWaitWithdrawYuyinSubsidyResp {
  uint32 tax_rate = 1; // 税点
  uint64 income_sum = 2; // 总收益金额
  uint64 actual_income_pay = 3; // 实际付款金额
  YuyinSubsidyBill yuyin_subsidy = 16;
}

message GetWaitWithdrawMonthsReq {
  uint32 uid = 1;
  SettlementBillType bill_type = 2; // 结算单类型
}

message GetWaitWithdrawMonthsResp {
  repeated uint32 months = 1; // 202201...
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetMonthsBySettleBillIdReq {
  uint32 uid = 1;
  string billId = 2;
}

message GetMonthsBySettleBillIdResp {
  repeated uint32 months = 1; // 202201...
}

message GetExtraIncomeDetailDeductResp {
  message DeductItem {
    uint64 amuse_deduct_money = 1; // 扣款金额（娱乐房佣金）
    string amuse_remark = 2; // 备注（娱乐房佣金）
    uint64 yuyin_deduct_money = 3; // 扣款金额（语音基础佣金）
    string yuyin_remark = 4; // 备注（语音基础佣金）
  }
  repeated DeductItem list = 1;
}

message ConfirmWithdrawReq {
  string bill_id = 1; // 结算单ID
  bool always_allow = 2; // 绕过提现周期检查
}

message ConfirmWithdrawResp {
  string bill_id = 1; // 结算单ID
  uint64 withdraw_money = 2; // 提现金额
}

message GetSettlementBillWaitWithdrawReq {
  uint32 uid = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetSettlementBillWaitWithdrawResp {
  repeated SettlementBillDetail list = 1;
  uint32 taxCompensation_rate = 2;
  string taxCompensation_rate_text = 3;
  uint32 compensation_point = 4;
  string compensation_point_text = 5;
  uint32 tax_rate = 6;
}

message GetDeductMoneyListByBillIdReq {
  string bill_id = 1; // 结算单ID
}

message DeductMoneyDetail {
  uint64 deduct_money = 1; // 扣款金额
  string remark = 2; // 备注
  uint32 settlement_date = 3; // 结算日期
}

message GetDeductMoneyListByBillIdResp {
  repeated DeductMoneyDetail list = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ReportConfirmReq {
  uint64 start_time = 1; // 范围开始时间
  uint64 end_time = 2; // 范围结束时间
  bool confirmSendEmail = 3; // 确认发送邮件，避免误推
}

message ReportConfirmResp {
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ReportConfirmWithReq {
  repeated SettlementBillType Bill_type = 1; // 结算单类型
}
message ReportConfirmWithResp {
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message IsAllowWithdrawReq {
  SettlementBillType Bill_type = 1; // 结算单类型
  uint32 uid = 2;
  uint32 time = 3; // 为0时为当前时间
}
message IsAllowWithdrawResp {
  bool allow_withdraw = 1; // 是否允许提现
  uint64 limit_balance = 2; // 限额余额
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message PrivateWithdrawReq {
  SettlementBillType Bill_type = 1; // 结算单类型
  uint32 uid = 2;
  uint32 time = 3; // 为0时为当前时间
  uint64 money = 4; // 提现金额
  string remark = 5; // 备注
}
message PrivateWithdrawResp {
  uint64 real_money = 1; // 实际提现金额
}

message GetAmuseExtraIncomePrepaidReq {
  uint32 uid = 1;
  uint32 settlement_date = 2;
}
message GetAmuseExtraIncomePrepaidResp {
  uint64 prepaid = 1;
  string remark = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorScoreWithdrawRecordsReq {
  SettlementBillType Bill_type = 1; // 结算单类型
  uint32 anchor_uid = 2; // 主播UID
  uint32 offset = 3;
  uint32 limit = 4;
}
message AnchorScoreWithdrawRecord {
  uint32 anchor_uid = 1; // 主播UID
  uint32 guild_owner = 2; // 对公会长UID
  string bill_id = 3; // 结算单ID
  uint64 guild_owner_wd_money = 4; // 会长提现金额（分）
  string guild_owner_wd_money_cny = 5; // 会长提现金额（元）
  uint64 anchor_wd_money = 6; // 主播提现金额（分）
  string anchor_wd_money_cny = 7; // 主播提现金额（元）
  uint64 settle_start = 8; // 结算起始时间
  uint64 settle_end = 9; // 结算结束时间
  uint64 withdraw_time = 10; // 发起提现时间
}
message GetAnchorScoreWithdrawRecordsResp {
  repeated AnchorScoreWithdrawRecord anchor_withdraw_list = 1;
  uint32 total = 2;
}

// 获取发票有效总金额
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetReceiptTotalAmountReq {
  repeated string receiptIds = 1; // 发票ID
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetReceiptTotalAmountResp {
  map<string, uint64> list = 1; // 发票ID => 金额
  uint64 totalAmount = 2; // 总金额
  uint64 validTotalAmount = 3; // 有效总金额（不包含失败）
}

message ReceiptErrorInfo {
  string title = 1;
  string result = 2;
  string reason = 3;
}

// 结算单详情
message SettlementBillDetailV2 {
  string bill_id = 1; // 结算单ID
  SettlementBillType type = 2; // 结算单类型
  string type_desc = 3; // 结算单类描述（项目名称）
  uint32 uid = 4; // 对公会长UID
  SettlementBillStatus status = 5; // 状态
  uint32 settle_date = 6; // 对账日期
  uint32 settle_start = 7; // 结算周期开始时间
  uint32 settle_end = 8; // 结算周期结束时间
  string company_name = 9; // 对公公司名称（供应商名称）

  uint64 income_sum = 10; // 总收益金额（分）
  uint64 actual_income_pay = 11; // 实际付款金额（分）
  uint64 prepaid_money = 12; // 预付金额（分）
  uint64 deduct_money = 13; // 扣款金额（分）

  uint32 tax_rate = 14; // 税点
  float tax_compensation_rate = 15; // 税点补偿率
  float compensation_point = 16; // 补点比例

  uint32 create_time = 17; // 创建时间
  uint32 withdraw_time = 18; // 提现时间
  uint32 finished_time = 19; // 完成时间
  string file_id = 20; // 结算单PDF 灵犀文件ID
}
// 审批单详情
message ReceiptBillDetailV2 {
  string receipt_bill_id = 1; // 发票单ID
  string inst_id = 2; // 灵犀审批单ID // 如 1836666656884174849
  string seq_no = 3; // seq_no // 如 HZJSD202409191459458393
  OAAuditBillStatus status = 4; // 灵犀状态
  repeated SettlementBillDetailV2 settlement_bill_list = 5; // 关联结算单
}
message GetSettlementBillDetailByInstIdReq {
  repeated string inst_id = 1; // 灵犀审批单ID
}
message GetSettlementBillDetailByInstIdResp {
  repeated ReceiptBillDetailV2 receipt_bill_list = 1; // 灵犀审批单列表
}

// 活动奖励录入类型
enum ActivityRewardType {
  ACTIVITY_REWARD_TYPE_UNEXPECTED = 0;
  ACTIVITY_REWARD_TYPE_GUILD = 1; // 公会
  ACTIVITY_REWARD_TYPE_MEMBER = 2; // 成员
}

// 活动奖励结算状态
enum ActivityRewardStatus {
  ACTIVITY_REWARD_STATUS_UNEXPECTED = 0; // 未知状态
  ACTIVITY_REWARD_STATUS_SETTLED = 1; // 已结算
  ACTIVITY_REWARD_STATUS_WITHDRAWN = 2; // 已提现
}

// 公会奖励汇总记录（汇总维度：会长UID+结算单状态）
message GuildActivityRewardSummary {
  string summary_id = 1; // 记录ID
  uint32 guild_owner = 2; // 会长UID
  uint64 settle_start = 3; // 结算周期开始时间
  uint64 settle_end = 4; // 结算周期结束时间
  uint64 settle_money = 5; // 结算金额
  uint64 create_time = 6; // 创建时间
  uint64 update_time = 7; // 更新时间
  string operator = 8; // 操作人
  string bill_id = 9; // 结算单ID
  ActivityRewardStatus status = 10; // 奖励状态
  SettlementBillStatus bill_status = 11; // 结算单状态
  repeated GuildActivityRewardDetail detail_list = 12; // 明细列表
}

// 公会奖励明细（即每条录入记录）
message GuildActivityRewardDetail {
  string summary_id = 1; // 汇总记录ID
  uint32 guild_owner = 2; // 会长UID
  uint64 settle_start = 3; // 结算周期开始时间
  uint64 settle_end = 4; // 结算周期结束时间
  string activity_name = 5; // 活动名称
  uint64 reward_money = 6; // 奖励金额
  uint64 create_time = 7; // 创建时间
  uint64 update_time = 8; // 更新时间
  string operator = 9; // 操作人
  string remark = 10; // 备注
}

// 成员奖励汇总记录（汇总维度：会长UID+结算单状态）
message MemberActivityRewardSummary {
  string summary_id = 1; // 记录ID
  uint32 guild_owner = 2; // 会长UID
  uint64 settle_start = 3; // 结算周期开始时间
  uint64 settle_end = 4; // 结算周期结束时间
  uint64 settle_money = 5; // 结算金额
  uint64 create_time = 6; // 创建时间
  uint64 update_time = 7; // 更新时间
  string operator = 8; // 操作人
  string bill_id = 9; // 结算单ID
  ActivityRewardStatus status = 10; // 奖励状态
  SettlementBillStatus bill_status = 11; // 结算单状态
  repeated MemberActivityRewardDetail member_reward_list = 12; // 成员奖励列表
}

// 成员奖励明细（即每条录入记录）
message MemberActivityRewardDetail {
  string summary_id = 1; // 汇总记录ID
  uint32 guild_owner = 2; // 会长UID
  string activity_name = 3; // 活动名称
  uint64 settle_start = 4; // 结算周期开始时间
  uint64 settle_end = 5; // 结算周期结束时间
  uint32 member_uid = 6; // 成员UID
  string member_ttid = 7; // 成员TTID
  uint64 reward_money = 8; // 奖励金额
  uint64 create_time = 9; // 创建时间
  uint64 update_time = 10; // 更新时间
  string operator = 11; // 操作人
  string remark = 12; // 备注
}

// 录入活动奖励
message RecordActivityRewardReq {
  string activity_name = 1; // 活动名称
  bool is_compensation_point = 2; // 是否补点
  uint32 settle_start = 3; // 结算周期开始时间
  uint32 settle_end = 4; // 结算周期结束时间
  string operator = 5; // 操作人
  string remark = 6; // 备注
  ActivityRewardType reward_type = 7; // 奖励类型
  repeated GuildActivityRewardDetail guild_reward_list = 8; // 公会奖励列表
  repeated MemberActivityRewardDetail member_reward_list = 9; // 成员奖励列表
}
message RecordActivityRewardResp {
}

// 获取公会活动奖励汇总列表
message GetGuildActivityRewardSummaryListReq {
  uint32 guild_owner = 1; // 会长UID（废弃）
  uint32 offset = 2;
  uint32 limit = 3;
  repeated uint32 guild_owner_list = 4; // 会长UID
}
message GetGuildActivityRewardSummaryListResp {
  repeated GuildActivityRewardSummary guild_reward_summary_list = 1; // 公会奖励汇总列表
  uint32 total = 2; // 总条数
}

// 获取公会成员活动奖励汇总列表
message GetGuildActivityRewardMemberSummaryListReq {
  repeated uint32 guild_owner = 1; // 会长UID
  repeated uint32 member_uid = 2; // 成员UID
  uint32 offset = 3;
  uint32 limit = 4;
  string activity_name = 5; // 活动名称
}
message GetGuildActivityRewardMemberSummaryListResp {
  repeated MemberActivityRewardSummary member_reward_summary_list = 1; // 成员奖励汇总列表
  uint32 total = 2; // 总条数
}

// 获取公会成员活动奖励明细列表
message GetGuildActivityRewardMemberDetailListReq {
  string summary_id = 1; // 成员奖励汇总ID
  uint32 offset = 2;
  uint32 limit = 3;
}
message GetGuildActivityRewardMemberDetailListResp {
  repeated MemberActivityRewardDetail member_reward_detail_list = 1; // 成员奖励明细列表
  uint32 total = 2; // 总条数
}

// 获取结算单关联的活动奖励汇总
message GetBillActivityRewardSummaryReq {
  string bill_id = 1; // 结算单ID
  uint32 uid = 2; // 对公会长UID
}
message GetBillActivityRewardSummaryResp {
  message Item {
    string summary_id = 1; // 汇总记录ID
    string activity_name = 2; // 活动名称
    uint64 settle_money = 3; // 结算金额
    ActivityRewardType reward_type = 4; // 奖励类型
    repeated BillGuildRewardDetail guild_reward_detail_list = 5; // 公会奖励明细列表（公会类型数量不多直接返回）
  }
  repeated Item list = 1; // 活动奖励汇总列表
}
// 结算单关联的公会活动奖励明细
message BillGuildRewardDetail {
  uint32 guild_owner = 1; // 会长UID
  string activity_name = 2; // 活动名称
  uint64 settle_start = 3; // 结算周期开始时间
  uint64 settle_end = 4; // 结算周期结束时间
  uint64 settle_money = 5; // 结算金额
  string remark = 6; // 备注
}
// 结算单关联的成员活动奖励明细
message BillMemberRewardDetail {
  uint32 member_uid = 1; // 成员UID
  string activity_name = 2; // 活动名称
  uint64 settle_start = 3; // 结算周期开始时间
  uint64 settle_end = 4; // 结算周期结束时间
  uint64 settle_money = 5; // 结算金额
  string remark = 6; // 备注
}

// 获取结算单关联的活动奖励成员明细
message GetBillActivityRewardMemberDetailReq {
  string bill_id = 1; // 结算单ID
  uint32 uid = 2; // 对公会长UID
  string summary_id = 3; // 成员奖励汇总ID
  uint32 offset = 4;
  uint32 limit = 5;
  string activity_name = 6; // 活动名称
}
message GetBillActivityRewardMemberDetailResp {
  repeated BillMemberRewardDetail list = 1; // 活动奖励成员明细列表
  uint32 total = 2; // 总条数
}