syntax = "proto3";

package channel_music_go;
option go_package = "golang.52tt.com/protocol/services/channel_music_go";

service ChannelMusicGo {
    rpc GetChannelMusicStatus(GetChannelMusicStatusRequest) returns (GetChannelMusicStatusResponse);
    rpc SetChannelMusicStatus(SetChannelMusicStatusRequest) returns (SetChannelMusicStatusResponse);
    rpc ChannelMusicCtrl(ChannelMusicCtrlRequest) returns (ChannelMusicCtrlResponse);
    rpc SetChannelMusicCurrent(SetChannelMusicCurrentRequest) returns (SetChannelMusicCurrentResponse);
    rpc AddChannelMusic(AddChannelMusicRequest) returns (AddChannelMusicResponse);
    rpc GetChannelMusicList(GetChannelMusicListRequest) returns (GetChannelMusicListResponse);
    rpc SetChannelMusicListType(SetChannelMusicListTypeRequest) returns (SetChannelMusicListTypeResponse);
    rpc GetChannelMusicListType(GetChannelMusicListTypeRequest) returns (GetChannelMusicListTypeResponse);
    rpc ClearChannelMusicList(ClearChannelMusicListRequest) returns (ClearChannelMusicListResponse);
}

message MusicInfo {
    string client_key = 1;
    string name = 2;
    string author = 3;
    uint32 uid = 4;         // 上传者id
    uint32 volume = 5;      // 服务器的音量
    int64 key = 6;          // zset中的score
    uint32 is_local = 8;    // 是否是客户端本地歌曲 1-是 0-否
    uint32 status = 9;      // 播放状态
    uint32 music_type = 10; //音乐类型 1原唱、0伴奏
    uint32 start_time = 11; //开始播放时间(百灵上报)
}

// 播放器状态
message GetChannelMusicStatusRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
    repeated uint32 status_type_list = 3; // ChannelMusicStatusType, 根据需要获取的状态列表返回
}
message GetChannelMusicStatusResponse {
    uint32 play_mode = 1;
    uint32 volume = 2;
    bool can_share = 3;
    bool is_playing = 4;
    bool is_free_mode = 5;
    bytes current_playing = 6;  // 这里面是MusicInfo结构
    uint32 percent = 7;
    string next_music_key = 8;
    uint32 timeout_count = 9;   // 心跳超时次数, 过渡阶段, 需要先在原来的战歌播放管理结构查出来, 后续迁到心跳管理模块存储后废弃
}

enum ChannelMusicStatusType {
    EMUSIC_STATUS_UNKNOWN = 0;
    EMUSIC_STATUS_PLAY_MODE = 1;
    EMUSIC_STATUS_VOLUME = 2;
    EMUSIC_STATUS_CAN_SHARE = 3;
    EMUSIC_STATUS_FREE_MODE = 4;
    EMUSIC_STATUS_NEXT_MUSIC = 5;
    EMUSIC_STATUS_PERCENT = 6;
    EMUSIC_STATUS_PLAY_STATUS = 7;
    EMUSIC_STATUS_CURRENT_PLAYING = 8;
    EMUSIC_STATUS_TIMEOUT_COUNT = 9;
}
message SetChannelMusicStatusRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
    repeated uint32 modify_type_list = 3; // ChannelMusicStatusType
    uint32 timeout_count = 4;
    uint32 percent = 5;
}

message SetChannelMusicStatusResponse {
}

enum MusicChangedType {
    EMUSIC_CHANGED_UNKNOWN               = 0;
    EMUSIC_CHANGED_AUTO_NORMAL           = 1; // 客户端上报歌曲播放完毕, 正常切换
    EMUSIC_CHANGED_AUTO_NOT_PLAY_NETWORK = 2; // 客户端上报网络问题, 根据心跳超时次数切歌
    EMUSIC_CAHNGED_AUTO_NOT_PLAY_FLIE    = 3;
    EMUSIC_CHANGED_MANUAL                = 4; // 主动切歌, 正常切换
    EMUSIC_STOPED_DELETED                = 5;
    EMUSIC_STOPED                        = 6;
    EMUSIC_STOPED_LIST_EMPTY             = 7;
    EMUSIC_CHANGED_AUTO_NOT_HEARTBEAT    = 8; // 心跳超时切歌
}

// 控制
message ChannelMusicCtrlRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 channel_ctrl = 3;       // see channel_.proto -> ChannelMusicCtrl
    uint32 music_changed_type = 4; // MusicChangedType 默认EMUSIC_CHANGED_MANUAL, 正常切换不需要关注
}
message ChannelMusicCtrlResponse {
    MusicInfo next_music = 1;
}

message SetChannelMusicCurrentRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 music_id = 3;
    uint32 music_changed_type = 4;  // MusicChangedType 默认EMUSIC_CHANGED_MANUAL, 正常切换不需要关注
}
message SetChannelMusicCurrentResponse {
}

message AddChannelMusicRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
    repeated MusicInfo music_info_list = 3;
}

message AddChannelMusicResponse {
    repeated MusicInfo music_added_list = 1;
    uint32 max_music_count = 2;
    uint32 current_music_count = 3;
}

message GetChannelMusicListRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message GetChannelMusicListResponse {
    repeated MusicInfo music_list = 1;
}

// 战歌列表类型
enum MusicListType {
    MusicListType_Default = 0;  // TT
    MusicListType_NC = 1;       // 网易云音乐
}

message SetChannelMusicListTypeRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 music_list_type = 3;  // see MusicListType
}
message SetChannelMusicListTypeResponse {
}

message GetChannelMusicListTypeRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
}
message GetChannelMusicListTypeResponse {
    uint32 music_list_type = 1;  // see MusicListMode
}

message ClearChannelMusicListRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
}
message ClearChannelMusicListResponse {
}
