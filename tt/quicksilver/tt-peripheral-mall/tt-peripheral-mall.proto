syntax = "proto3";

package tt_peripheral_mall;
import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/unified-pay-rmb/unified-pay-rmb.proto";

option go_package = "golang.52tt.com/protocol/services/tt-peripheral-mall";

service TTPeripheralMall {
  option (service.options.service_ext) = {
    service_name: "tt-peripheral-mall"
  };

  // 获取商品简略信息列表
  rpc GetProductSimpleInfoList(GetProductSimpleInfoListReq) returns(GetProductSimpleInfoListResp);
  // 获取商品详细信息
  rpc GetProductDetailInfo(GetProductDetailInfoReq) returns(GetProductDetailInfoResp);
  // 下单
  rpc OrderProduct(OrderProductReq) returns(OrderProductResp);
  // 获取用户历史订单列表
  rpc GetUserOrderList(GetUserOrderListReq) returns(GetUserOrderListResp);
  // 支付结果回调
  rpc PayRmbResultNotify(PayRmbResultNotifyReq) returns (PayRmbResultNotifyResp) {}

  // 获取用户订单地址
  rpc GetUserOrderAddr(GetUserOrderAddrReq) returns(GetUserOrderAddrResp);
  // 设置用户订单地址
  rpc SetUserOrderAddr(SetUserOrderAddrReq) returns(SetUserOrderAddrResp);

  // 批量新增商品
  rpc BatAddProduct(BatAddProductReq) returns(BatAddProductResp);
  // 更新商品信息
  rpc UpdateProduct(UpdateProductReq) returns(UpdateProductResp);
  // 删除商品
  rpc DeleteProduct(DeleteProductReq) returns(DeleteProductResp);
}

message ProductSimple {
    uint32 product_id = 1;
    string title = 2;        // 商品标题
    string homepage_url = 3; // 主页图
    uint32 price = 4;        // 商品单价
    bool is_stock_out = 5;   // 缺货中

    uint32 origin_price = 6; // 原价(展示用)
}

message GetProductSimpleInfoListReq {
  uint32 uid = 1;
  uint32 offset = 2;
  uint32 limit = 3;
}

message GetProductSimpleInfoListResp {
  repeated ProductSimple product_list = 1;
}

message ProductDetail {
    uint32 product_id = 1;
    string title = 2;          // 商品标题
    string homepage_url = 3;   // 商品主页图url
    string detail_desc = 4;    // 商品详细介绍
    uint32 price = 5;          // 商品单价
    uint32 stock = 6;          // 库存
    uint32 limit_per_order =7; // 每单限购数量
    repeated string pic_urls = 8; // 商品图片url列表

    uint32 priority = 9; // 优先级

    uint32 origin_price = 10; // 原价(展示用)
    uint32 user_ordered_cnt = 11; // 用户已下单数量
}

// 下单地址
message OrderAddrInfo {
  string name = 1;
  string phone = 2;
  string address = 3;
}

message GetProductDetailInfoReq {
  uint32 uid = 1;
  uint32 product_id = 2;
}

message GetProductDetailInfoResp {
  ProductDetail product_detail = 1;
  //OrderAddrInfo last_addr = 2;
}

message SingleProductOrder {
  uint32 product_id = 1;
  uint32 amount = 2;
}

message PeripheralPayReq {
  string pay_channel = 1;   //下单的渠道  必填
  string bundle_id = 2;     //IOS的produceID(appstore支付必传)
  string pay_product_id = 3;    //IOS的product_id (appstore支付必传)
  uint32 market_id = 4;
  string os_type = 5;
  string version = 6;
  string user_ip = 7;
  string device_id = 8;
}

message PeripheralPayResp{
  string token = 1;
  string pay_order_id = 2; // 支付订单号
  string cli_order_title = 3; // 订单标题
  string tsk = 4; // 加密字符串
  string channel_map = 5; // 唤起支付渠道的参数, 用于安卓、IOS、前端使用
  uint32 order_time  = 6; // 下单时间
  string t_pay_order_no = 7; // 第三方订单号
}

message OrderProductReq {
  uint32 uid = 1;
  repeated SingleProductOrder list = 2;
  OrderAddrInfo addr = 3;
  PeripheralPayReq pay_info = 4; // 支付信息
}

message OrderProductResp {
  PeripheralPayResp pay_info = 1;
}

message OrderDetail {
  string order_id = 1; // 订单号
  repeated ProductDetail product_list = 2;    // 购买商品列表
  OrderAddrInfo addr = 3;                     // 下单地址信息
  uint32 total_price = 4;                     // 总价
  int64 timestamp = 5;                        // 下单时间
}

message GetUserOrderListReq {
  uint32 uid = 1;
  uint32 offset = 2;
  uint32 limit = 3;
}

message GetUserOrderListResp {
  repeated OrderDetail order_list = 1;  // 订单列表
}

// 支付结果回调
message PayRmbResultNotifyReq {
  unified_pay_rmb.RmbPayOrderInfo order_result = 1;
}
message PayRmbResultNotifyResp {}

message BatAddProductReq {
  uint32 op_user = 1;
  repeated ProductDetail product_list = 2;
}

message BatAddProductResp {}

message UpdateProductReq {
  uint32 op_user = 1;
  ProductDetail info = 3;
}

message UpdateProductResp {}

message DeleteProductReq {
  uint32 op_user = 1;
  uint32 product_id = 2;
}

message DeleteProductResp {}

// 获取用户下单地址
message GetUserOrderAddrReq {
  uint32 uid = 1;
}

message GetUserOrderAddrResp {
  OrderAddrInfo addr = 1;
}

// 保存用户下单地址
message SetUserOrderAddrReq {
  uint32 uid = 1;
  OrderAddrInfo addr = 2;
}

message SetUserOrderAddrResp {
}
