syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package muse_channel_hot_game;
import "hobby_channel/hobby-channel_.proto";
option go_package = "golang.52tt.com/protocol/services/muse-channel-hot-game";

// buf:lint:ignore SERVICE_PASCAL_CASE
service muse_channel_hot {
  //获取玩法的热聊挑战
  rpc GetMuseChannelHotGame (GetMuseChannelHotGameRequest) returns (GetMuseChannelHotGameResponse);

  //获取热聊挑战浮窗
  rpc GetMuseChannelHotGameFloat (GetMuseChannelHotGameFloatRequest) returns (GetMuseChannelHotGameFloatResponse) ;

  /* 参加或取消参加热聊 */
  rpc SetMuseChannelHotGameJoinStatus(SetMuseChannelHotGameJoinStatusRequest) returns (SetMuseChannelHotGameJoinStatusResponse) ;

  /* 获取挑战详情 */
  rpc GetMuseChannelHotGameDetail(GetMuseChannelHotGameDetailRequest) returns (GetMuseChannelHotGameDetailResponse) ;
  rpc GetMuseChannelHotGameDetailV2(GetMuseChannelHotGameDetailV2Request) returns (GetMuseChannelHotGameDetailV2Response) ;

  //获取用户所在房
  rpc GetMuseUserInChannelId(GetMuseUserInChannelIdRequest) returns (GetMuseUserInChannelIdResponse) ;

  rpc UpsertMuseChannelHotGame(UpsertMuseChannelHotGameReq) returns (UpsertMuseChannelHotGameResp);

  rpc SearchMuseChannelHotGame(SearchMuseChannelHotGameReq) returns (SearchMuseChannelHotGameResp);

  //给房间加点赞值
  rpc AddMuseChannelHotGameValue (AddMuseChannelHotGameValueRequest) returns (AddMuseChannelHotGameValueResponse) ;
  // 增加新热聊值公式中参数的用户
  rpc AddHotGameComputeUser (AddHotGameComputeUserRequest) returns (AddHotGameComputeUserResponse) ;

  rpc BatchChannelViewDesc(BatchChannelViewTopicRequest)returns (BatchChannelViewTopicResponse);


  rpc GetAllHotGameForRank(GetAllHotGameForRankReq) returns (GetAllHotGameForRankResp);
  rpc GetChannelListByHotGameId(GetChannelListByHotGameIdReq) returns (GetChannelListByHotGameIdResp);

  // 根据房间ID获取挑战点赞值
  rpc GetChannelValueByChannelId(GetChannelValueByChannelIdReq) returns (GetChannelValueByChannelIdResp);

  rpc PushMuseCommonXMLMsgNotify(PushMuseCommonXMLMsgNotifyReq) returns (PushMuseCommonXMLMsgNotifyResp);
}

message BatchChannelViewTopicRequest{
  repeated uint32 channel_ids = 1;
}

message BatchChannelViewTopicResponse{
  map<uint32, string> channel_topic_map = 1;
}

message AddMuseChannelHotGameValueRequest{
  uint32 channel_id = 1;
  int64 value = 2;
}

message AddMuseChannelHotGameValueResponse{

}

// 增加新热聊值公式中参数的用户
message AddHotGameComputeUserRequest{
  uint32 channel_id = 1;
  uint32 value_type = 2; // HotGameValueType
  string uid = 3; // 如果是关注的话，是uid1-uid2
}

enum HotGameValueType{
  HOT_GAME_VALUE_TYPE_NONE = 0;
  HOT_GAME_VALUE_TYPE_STAY = 1; // 停留时长用户
  HOT_GAME_VALUE_TYPE_MIC = 2; // 开麦用户
  HOT_GAME_VALUE_TYPE_FOLLOW = 3;  // 关注
}

message AddHotGameComputeUserResponse{
  bool need_stop = 1; // 是否需要停止
}


message GetMuseChannelHotGameRequest{
  uint32 tab_id = 1;
}

message GetMuseChannelHotGameResponse{
  string hot_game_id = 1;//为空 代表没有
  string title = 2;
  string desc = 3;
}

message GetMuseChannelHotGameFloatRequest{
  uint32 channel_id = 1;
}

message GetMuseChannelHotGameFloatResponse{
  string title = 1;
  string icon = 2;
  uint32 number = 3;
  string desc = 4;
  string jump_url = 5;
  uint32 channel_id = 6;
  string hot_game_id = 7;
}

message SetMuseChannelHotGameJoinStatusRequest{
  uint32 channel_id = 1;
  string hot_game_id = 2;
  MuseChannelHotGameJoinStatus join_status = 3;//0-取消挑战，1-参加挑战
}

enum MuseChannelHotGameJoinStatus{
  MUSE_CHANNEL_HOT_GAME_JOIN_STATUS_CANCEL_UNSPECIFIED = 0;
  MUSE_CHANNEL_HOT_GAME_JOIN_STATUS_JOIN = 1;
}

message SetMuseChannelHotGameJoinStatusResponse{
}

message GetMuseChannelHotGameDetailRequest{
  string hot_game_id = 1;
  uint32 uid = 2;
}

message GetMuseChannelHotGameDetailResponse{
  HotGameDetail hot_game_detail = 1;
  uint32 user_channel_id = 2;
  repeated HotGameUser users = 3;
  HotGameUser user = 4;
}

message GetMuseChannelHotGameDetailV2Request{
  string hot_game_id = 1;
  uint32 uid = 2;
  int32 offset=3;
  int32 limit=4;
}

message GetMuseChannelHotGameDetailV2Response{
  HotGameDetail hot_game_detail = 1;
  uint32 user_channel_id = 2;
  repeated HotGameUser day_rank = 3;
  repeated HotGameUser total_rank = 4;
  HotGameUser day_user = 5;
  HotGameUser total_user = 6;
}

message HotGameDetail {
  string id = 1;
  string title = 2;
  string desc = 3;
  string image = 4;
  uint32 member_count = 5;
  string tag = 6;
  repeated uint32 tab_ids = 7;
  NewHotGameTabResource new_hot_game_tab_resource = 8;
}

message HotGameUser {
  uint32 uid = 1;
  uint32 channel_id = 2;
  string account = 3;//头像
  string nick_name = 4;//用户昵称
  uint32 value = 5;//热度值
  HotGameUserStatus status = 6;//0-无状态，1-在房，2-挑战中
  uint32 sex = 7;
  MusicChannelPersonalCert personal_cert = 8;//个人认证标签
  // 用户自己查看自己信息时独有
  int64 rank = 9; // 排名
  uint32 value_diff = 10; // 差距热度值
}

enum HotGameUserStatus{
  HOT_GAME_USER_STATUS_NONE = 0;
  HOT_GAME_USER_STATUS_IN_CHANNEL = 1;
  HOT_GAME_USER_STATUS_IN_GAME = 2;
}

message MusicChannelPersonalCert{
  string icon = 1;
  string text = 2;
  repeated string color = 3;
  string text_shadow_color = 4;
  string introduce = 5; /* 介绍 */
  string jump_url = 6; /* 跳转链接 */
  string cert_type_name = 7; /* 类型名称 */
  string social_community_id=8;/*社群id*/
}

message GetMuseUserInChannelIdRequest{
  uint32 uid = 1;
}

message GetMuseUserInChannelIdResponse{
  uint32 channel_id = 1;
}


message UpsertMuseChannelHotGameReq{
  MuseChannelHotGame item = 1;
}

message MuseChannelHotGame{
  string id = 1;
  string title = 2;
  string publish_desc = 3;
  string intro = 4;
  string intro_image = 5;
  repeated uint32 tab_ids = 6;
  MuseChannelHotGameStatus status = 7;
  int64 create_at = 8;//秒，时间戳，增删入参不需要传
  int64 update_at = 9;//秒，时间戳，增删入参不需要传
  string offset_id = 10;
  string note = 11;
  NewHotGameTabResource new_hot_game_tab_resource = 12;
}

message NewHotGameTabVideoUrl {
  string cover_image_url=2;
  string video_url=1;

}

message NewHotGameTabImageUrl {
  string cover_image_url=1;
  string long_image_url=2;

}

message NewHotGameTabResource{
  oneof resource{
    NewHotGameTabImageUrl new_hot_game_tab_image_url=1;
    NewHotGameTabVideoUrl new_hot_game_tab_video_url=2;
  }
}


enum MuseChannelHotGameStatus {
  undefined = 0;
  in_use = 1;
  in_pause = 2;
}


message UpsertMuseChannelHotGameResp{

}

message SearchMuseChannelHotGameReq{
  string id = 1;
  string offset_id = 2;//下一页要把上一页最后的id带上
  uint32 tab_id = 3;
  uint32 count = 5;//数量，不带默认20
  MuseChannelHotGameStatus status = 6;
  string regx_name = 7;
}

message SearchMuseChannelHotGameResp{
  repeated MuseChannelHotGame muse_channel_hot_games = 1;
}

// 获取所有的挑战并排行
message GetAllHotGameForRankReq {
}

message GetAllHotGameForRankResp {
  repeated HotGameDetail hot_game_detail_list = 1;
}

// 房间tab
message GetChannelListByHotGameIdReq {
  string hot_game_id = 1;
}

message GetChannelListByHotGameIdResp {
  map<uint32, ga.hobby_channel.ListHobbyChannelResp.HobbyChannelItem>  channel_view_map = 1;
}

// 根据房间ID获取挑战点赞值
message GetChannelValueByChannelIdReq {
  uint32 channel_id = 1;
  uint32 tab_id = 2;
}

message GetChannelValueByChannelIdResp {
  uint32 value = 1;
  HotGameNewValueInfo value_info = 2;
}

message PushMuseCommonXMLMsgNotifyReq {
  uint32 channel_id = 1;
  uint32 uid = 2;
  uint32 msg_type = 3; // 消息类型 MuseCommonXMLMsgType
  string xml_msg = 4; // xml消息
  uint32 msg_content_type = 5; // 消息内容类型 MuseCommonXMLMsgContentType 用来区分，上报
}

message PushMuseCommonXMLMsgNotifyResp {
}

// 新热聊挑战热聊值
message HotGameNewValueInfo{
  uint32 day_rank_value = 1; // 日榜值
  uint32 total_rank_value = 2; // 总榜值
  uint32 day_rank_position = 3; // 位次
  uint32 day_rank_percent_position = 4; // 日榜分位
  uint32 total_rank_position = 5;
}

