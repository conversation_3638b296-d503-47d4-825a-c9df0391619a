
syntax = "proto3";

package fellow_level_award;
option go_package = "golang.52tt.com/protocol/services/fellow-level-award";



service FellowLevelAward {
  // -------------------- 运营后台协议 begin --------------------------------
  //获取挚友等级奖励列表
  rpc GetLevelAwardConfig(GetLevelAwardConfigReq) returns(GetLevelAwardConfigResp) {}
  //批量添加挚友等级奖励
  rpc BatchAddLevelAwardConfig(BatchAddLevelAwardConfigReq) returns(BatchAddLevelAwardConfigResp) {}
  //编辑挚友等级奖励
  rpc EditLevelAwardConfig(EditLevelAwardConfigReq) returns(EditLevelAwardConfigResp) {}
  //删除挚友等级奖励
  rpc DelLevelAwardConfig(DelLevelAwardConfigReq) returns(DelLevelAwardConfigResp) {}
  // -------------------- 运营后台协议 end ----------------------------------


  // -------------------- web端协议 begin -------------------------------------
  //获取用户奖励领取状态列表
  rpc GetUserLevelAwardList(GetUserLevelAwardListReq) returns(GetUserLevelAwardListResp) {}
  //领取用户等级奖励
  rpc ReceiveLevelAward(ReceiveLevelAwardReq) returns(ReceiveLevelAwardResp) {}

  // -------------------- web端协议 end ----------------------------------------

  // --------------------server协议 begin -------------------------------------
  //获取挚友IM浮层信息
  rpc GetFellowIMAwardMsg(GetFellowIMAwardMsgReq) returns(GetFellowIMAwardMsgResp) {}
  //获取升级奖励信息
  rpc GetUpgradeLevelAward(GetUpgradeLevelAwardReq) returns(GetUpgradeLevelAwardResp) {}

  rpc TestClearUserLevelAward(TestClearUserLevelAwardReq) returns(TestClearUserLevelAwardResp) {}

  rpc SendAvailableAwardMsg(SendAvailableAwardMsgReq) returns(SendAvailableAwardMsgResp) {}

  //获取用户当前奖励资源信息
  rpc GetUserCurrentAwardInfo(GetUserCurrentAwardInfoReq) returns(GetUserCurrentAwardInfoResp) {}

  rpc BatchGetUserCurrentAwardInfo(BatchGetUserCurrentAwardInfoReq) returns(BatchGetUserCurrentAwardInfoResp) {}

  //添加用户等级奖励
  rpc AddLevelAward(AddLevelAwardReq) returns(AddLevelAwardResp) {}


}

enum LevelAwardItemType {
  LEVEL_AWARD_ITEM_UNSPECIFIED = 0;         // 默认值，正常情况不应该传该值
  LEVEL_AWARD_ITEM_Package = 1;             // 包裹奖励
  LEVEL_AWARD_ITEM_OfficialCert = 2;        // 大V认证
  LEVEL_AWARD_ITEM_Nameplate = 3;           // 个人铭牌
  LEVEL_AWARD_ITEM_HeadWear = 4;            // 麦位框
  LEVEL_AWARD_ITEM_Horse = 5;               // 坐骑
  LEVEL_AWARD_ITEM_Float = 6;               // 主页飘
  LEVEL_AWARD_ITEM_ChannelInfoCard = 7;     // 房间资料卡
  LEVEL_AWARD_ITEM_CP_HeadWear = 102;       // cp麦位框
  LEVEL_AWARD_ITEM_BREAKING_NEWS = 103;     // 全服公告
  LEVEL_AWARD_ITEM_LIGATURE = 104;          // 挚友连线
  LEVEL_AWARD_ITEM_SPECIAL_EFFECT = 105;    // 双人进房特效
  LEVEL_AWARD_ITEM_MIC_SOFFA = 106;         // 双人麦位沙发
  LEVEL_AWARD_ITEM_OTHERS = 10001;          // 其他奖励

}


message LevelAwardItem{
  uint32 item_type = 1;    //物品类型 see LevelAwardItemType
  string item_name = 2;    //物品名称
  string item_id = 3;      //物品id
  uint32 item_count = 4;   //物品数量or天数
  string item_icon = 5;    //物品图标
  string item_count_info = 6; //物品数量描述
  string item_type_name = 7;  //物品类型名称
  uint32 rank = 8;          //排序
  string resource = 9;      //资源url
  string md5 = 10;          // 资源md5
}

//获取挚友等级奖励列表请求
message GetLevelAwardConfigReq{
  uint32 level = 1;     //挚友等级>0 根据等级搜索
}

message GetLevelAwardConfigResp{
  repeated LevelAwardConf conf_list = 1;
  repeated uint32 addible_level = 2; //可添加的等级列表
}

//挚友等级奖励配置
message LevelAwardConf{
  uint32 level = 1;                       //挚友等级
  repeated LevelAwardItem award_list = 2; //奖励列表
}

//批量添加挚友等级奖励请求
message BatchAddLevelAwardConfigReq{
  repeated LevelAwardConf conf_list = 1; //挚友等级奖励配置
}

message BatchAddLevelAwardConfigResp{
}

//编辑挚友等级奖励请求
message EditLevelAwardConfigReq{
    LevelAwardConf conf = 1; //挚友等级奖励配置
}

message EditLevelAwardConfigResp{
}

//删除挚友等级奖励请求
message DelLevelAwardConfigReq{
    uint32 level = 1; //挚友等级
}

message DelLevelAwardConfigResp{
}

//--------------
enum AwardStatus {
  AWARD_STATUS_UNSPECIFIED = 0; // 无效值
  AWARD_STATUS_NOT_AWARD = 1;   // 待解锁
  AWARD_STATUS_AVAILABLE_AWARD = 2;  // 可领取
  AWARD_STATUS_AWARDED = 3;      // 已领取
}
message GetUserLevelAwardListReq{
  uint32 my_uid = 1; //用户uid
  uint32 fellow_uid = 2; //挚友uid
  uint32 fellow_level = 3; //挚友等级
  uint32 fellow_type = 4; //挚友类型
}

message UserLevelAwardInfo{
   LevelAwardConf award = 1;
   uint32 award_status = 2;     // see AwardStatus
   string top_award_desc = 3;  //最高等级描述
}

message GetUserLevelAwardListResp{
  uint32 my_uid = 1; //用户uid
  uint32 fellow_uid = 2; //挚友uid
  UserLevelAwardInfo next_award = 3; //下一个等级奖励
  repeated UserLevelAwardInfo award_list = 4; //奖励列表
}

//领取用户等级奖励请求
message ReceiveLevelAwardReq{
  uint32 my_uid = 1; //用户uid
  uint32 fellow_uid = 2; //挚友uid
  uint32 level = 3; //等级
  uint32 fellow_type = 4; //挚友类型
}

message ReceiveLevelAwardResp{
}

//挚友下一个等级奖励信息
message FellowNextLevelAwardMsg{
  string next_award_level = 1; //下一奖励等级描述
  repeated LevelAwardItem award_list = 2; //奖励列表
}

message GetFellowNextLevelAwardMsgReq{
  uint32 my_uid = 1; //用户uid
  uint32 fellow_uid = 2; //挚友uid
  uint32 level = 3; //等级
}

message GetFellowNextLevelAwardMsgResp{
  FellowNextLevelAwardMsg msg = 1;
}

//挚友IM浮层信息
message GetFellowIMAwardMsgReq{
  uint32 my_uid = 1; //用户uid
  uint32 fellow_uid = 2; //挚友uid
  uint32 level = 3; //等级
}

message GetFellowIMAwardMsgResp{
  string next_award_level = 1; //下一奖励等级描述
  string available_award = 2; //可领取的奖励描述
}

//当前等级奖励信息
message GetUpgradeLevelAwardReq{
  uint32 my_uid = 1; //用户uid
  uint32 fellow_uid = 2; //挚友uid
  uint32 level = 3; //等级
  uint32 fellow_type = 4; //挚友类型
}

message GetUpgradeLevelAwardResp{
  LevelAwardConf award = 1;
  uint32 award_status = 2;     // see AwardStatus
  string next_award_msg = 3;  //下一奖励等级描述
  LevelAwardConf my_award = 4;      //我的奖励
  LevelAwardConf fellow_award = 5;  //挚友的奖励
}

message TestClearUserLevelAwardReq{
  uint32 uid = 1; //用户uid
  uint32 fellow_uid = 2; //挚友uid
  uint32 level = 3;
}

message TestClearUserLevelAwardResp{

}

message SendAvailableAwardMsgReq{
}

message SendAvailableAwardMsgResp{
}

message GetUserCurrentAwardInfoReq{
  uint32 my_uid = 1; //用户uid
  uint32 fellow_uid = 2; //挚友uid
  uint32 award_type = 3; //奖励类型 see LevelAwardItemType
  uint32 fellow_type = 4; //挚友类型
}

message GetUserCurrentAwardInfoResp{
  string resource = 1;      //资源url
  string md5 = 2;          // 资源md5
}

message FellowAwardInfo {
  uint32 fellow_uid = 1; //挚友uid
  uint32 fellow_type = 2; //挚友类型
}

message BatchGetUserCurrentAwardInfoReq{
  uint32 my_uid = 1; //用户uid
  uint32 award_type = 2; //奖励类型 see LevelAwardItemType
  repeated FellowAwardInfo fellow_list = 3; //挚友列表
}

message BatchGetUserCurrentAwardInfoResp{
  map<uint32, GetUserCurrentAwardInfoResp> award_map = 1;
}


//添加用户等级奖励请求
message AddLevelAwardReq{
  uint32 my_uid = 1; //用户uid
  uint32 fellow_uid = 2; //挚友uid
  uint32 level = 3; //等级
  uint32 award_type = 4; // see LevelAwardItemType
  uint32 fellow_type = 5; //挚友类型
  uint32 day_count = 6; //天数
}

message AddLevelAwardResp{
}