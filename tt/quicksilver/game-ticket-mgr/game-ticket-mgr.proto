syntax = "proto3";

package game_ticket_mgr;
option go_package = "golang.52tt.com/protocol/services/game-ticket-mgr";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service GameTicketMgr {
  // 新增游戏券配置
  rpc AddGameTicketCfg(GameTicketCfg) returns (AddGameTicketCfgResp);
  // 删除游戏券配置
  rpc DelGameTicketCfg(DelGameTicketCfgReq) returns (DelGameTicketCfgResp);
  // 获取所有游戏券配置
  rpc GetAllGameTicketCfg(GetAllGameTicketCfgReq) returns (GameTicketCfgList);
  // 获取游戏券配置
  rpc GetGameTicketCfg(GetGameTicketCfgReq) returns (GameTicketCfg);

  // 获取用户游戏券余额明细
  rpc GetUserRemainDetail(GetUserRemainDetailReq) returns (UserRemainDetail);
  // 获取用户游戏券余额汇总
  rpc GetUserRemainSummary(GetUserRemainSummaryReq) returns (UserRemainSummary);

  // 发放游戏券
  rpc SendUserGameTicket(SendUserGameTicketReq) returns (SendUserGameTicketResp);
  // 批量发放游戏券
  rpc BatchSendUserGameTicket(BatchSendUserGameTicketReq) returns (Empty);
  // 冻结游戏券
  rpc FreezeUserGameTicket(FreezeUserGameTicketReq) returns (FreezeUserGameTicketResp);
  // 确认冻结游戏券
  rpc ConfirmFreezeOrder(ConfirmFreezeOrderReq) returns (ConfirmFreezeOrderResp);

  /*********对账接口**********/
  // 发放数据对账
  rpc GetGainTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetGainOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  // 使用数据对账
  rpc GetCostTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetCostOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  //优惠券接口/////////////////////

  // 新增优惠券配置
  rpc AddCouponConfig (AddCouponConfigReq) returns (AddCouponConfigResp) {}
  // 修改优惠券配置
  rpc ModifyCouponConfig (ModifyCouponConfigReq) returns (ModifyCouponConfigResp) {}
  // 获取优惠券配置列表
  rpc ListCouponConfig (ListCouponConfigReq) returns (ListCouponConfigResp) {}
  // 批量发放优惠券，多用于管理后台发放
  rpc BatGrantCoupon (BatGrantCouponReq) returns (BatGrantCouponResp) {}
  // 发放优惠券，多用于程序发放
  rpc GrantCoupon (GrantCouponReq) returns (GrantCouponResp) {}
  // 查询用户优惠券数量
  rpc GetUserCouponCount (GetUserCouponCountReq) returns (GetUserCouponCountResp) {}
  // 查询用户优惠券列表
  rpc GetUserCouponList (GetUserCouponListReq) returns (GetUserCouponListResp) {}
  // 查询用户生效中的优惠券，成都密境专用
  rpc GetUserValidCoupon (GetUserValidCouponReq) returns (GetUserValidCouponResp) {}

  // 获取密室券订单查询选项
  rpc GetGameTicketOrderQueryOptions(GetGameTicketOrderQueryOptionsRequest) returns (GetGameTicketOrderQueryOptionsResponse){}
  // 获取密室券订单列表
  rpc GetGameTicketOrderList(GetGameTicketOrderListRequest) returns (GetGameTicketOrderListResponse){}
}

enum GainSourceType {
  UnknownGainSourceType = 0;
  GameTicketRecharge = 1; // 游戏券充值
  InviteNewUser = 2;      // 邀请新用户
  LoginTask = 3;          // 登录任务
  GameAward = 4;          // 游戏发放
  OperatorSend = 5;       //运营发放-成都密境运营活动券
  OperatorSendTT = 6;     //运营发放-tt运营活动券
  ActivitySendTT = 7;     //TT活动发放
  GameTicketRmbRecharge = 8; // 游戏券充值
}

enum CostDestType {
  UnknownCostDestType = 0;
}

enum ExpireType {
  Forever = 0;            // 永久
  AbsoluteTimeSec = 1;    // 绝对时间(秒)
  RelativeTimeSec = 2;    // 相对时间(秒)
  RelativeTimeMonth = 3;  // 相对时间(月)，n个月后的1号过期
}

enum CostPriorityType {
  Free = 0;
  Paid = 1;
}

message GameTicketCfg {
  uint32 ticket_id = 1;       // 券的id
  string name = 2;            // 券的名字
  string desc = 3;            // 券的描述
  ExpireType expire_type = 4; // 过期类型
  int64 expire_duration = 5;
  uint32 price = 6;           // 价值(T豆)
}

message AddGameTicketCfgResp {}

message DelGameTicketCfgReq {
  uint32 ticket_id = 1;       // 券的id
}

message DelGameTicketCfgResp {}

message GetAllGameTicketCfgReq {}

message GameTicketCfgList {
  repeated GameTicketCfg cfg_list = 1;
}

message GetGameTicketCfgReq {
  uint32 ticket_id = 1;       // 券的id
}

message TicketRemainDetail {
  uint32 ticket_id = 1;       // 券的id
  string name = 2;            // 券的名字
  int64 expire_time = 3;      // 到期时间
  GainSourceType source = 4;  // 获得来源
  uint32 num = 5;             // 数量
  uint32 price = 6;           // 价值(T豆)
}

// 详细信息，即按不同券、来源、过期时间分栏
message GetUserRemainDetailReq {
  uint32 uid = 1;
  uint32 ticket_id = 2; // 券的id, 填0则获取所有
}

message UserRemainDetail {
  uint32 uid = 1;
  repeated TicketRemainDetail list = 2;
}

message RemainSummary {
  uint32 ticket_id = 1;       // 券的id
  string name = 2;            // 券的名字
  uint32 num = 3;             // 数量
  uint32 price = 4;           // 价值(T豆)
}

// 按券汇总数据，相同券id为一栏
message GetUserRemainSummaryReq {
  uint32 uid = 1;
  uint32 ticket_id = 2; // 券的id, 填0则获取所有
}

message UserRemainSummary {
  uint32 uid = 1;
  repeated RemainSummary list = 2;
}

// 发放游戏券
message SendUserGameTicketReq {
  uint32 uid = 1;
  string order_id = 2;
  uint32 ticket_id = 3;
  uint32 num = 4;
  GainSourceType source = 5; // 获取来源
  int64 outside_time = 6;
  string activity_name = 7; // 活动名称
}

message SendUserGameTicketResp {
  uint32 final_num = 1; // 发放成功后的余额, 指detail余额
}

message BatchSendUserGameTicketReq {
  repeated SendUserGameTicketReq list = 1;
}

message Empty {
}

/********* 扣除： 先冻结后确认 ***********/
// 冻结游戏券
message FreezeUserGameTicketReq {
  uint32 uid = 1;
  string order_id = 2;
  uint32 ticket_id = 3;
  uint32 num = 4;
  CostDestType cost_type = 5;
  CostPriorityType cost_priority = 6;

  string coupon_id = 7;         //优惠券id
  uint32 coupon_reduce_num = 8; //优惠券所减免的密室券数量，用于成本核算
  uint32 usage_type = 9; //使用场景
  UsageContext usage_context = 10; //使用时的上下文，用于校验优惠券条件，也方便查用户反馈
}

message UsageContext{
  uint32 game_id = 1; //当前在哪个游戏下
  repeated uint32 chapters = 2; //涉及哪些章节
  uint32 playmate_uid = 3; //玩伴id（仅使用优惠券时传）
  repeated uint32 player_uids = 4; // 玩剧本的用户uid列表
}

message FreezeUserGameTicketResp {
  int64 freeze_time = 1;  // 冻结时间
  repeated TicketRemainDetail freeze_list = 2;  //冻结明细
}

enum FreezeStatus {
  Freeze = 0;   // 冻结中
  Commit = 1;   // 确认扣除
  Rollback = 2; // 回滚
}

// 确认冻结的订单
message ConfirmFreezeOrderReq {
  uint32 uid = 1;
  string order_id = 2;
  FreezeStatus confirm_status = 3;
  int64 freeze_time = 4;
}

message ConfirmFreezeOrderResp {}

//优惠券协议////////////////////////////////////////

enum CouponType{
  COUPON_TYPE_UNSPECIFIED = 0;
  COUPON_TYPE_DISCOUNT = 1;     //折扣解锁券
  COUPON_TYPE_REDUCTION = 2;    //减免解锁券
  COUPON_TYPE_SINGLE_TRIAL = 3; //单次限免券
  COUPON_TYPE_FREE = 4;         //免费券
}

enum CouponGrantSource{
  COUPON_GRANT_SOURCE_NONE = 0;
  COUPON_GRANT_SOURCE_TT_ACTIVITY = 1; //TT web活动发放
  COUPON_GRANT_SOURCE_TT_ADMIN = 2;    //TT手动发放
  COUPON_GRANT_SOURCE_MJ_ACTIVITY = 3; //成都密境 web活动发放
  COUPON_GRANT_SOURCE_MJ_ADMIN = 4;    //成都密境手动发放
}

message CouponContentLimit{
  uint32 tab_id = 1; // 只能在该玩法id下使用
  repeated uint32 chapters = 2; //玩法章节id列表，空则不限制章节
}

message CouponConfig{
  uint32 coupon_conf_id = 1; //配置id,新增不填
  string coupon_name = 2;         // 券名称，方便运营人员识别
  CouponGrantSource coupon_grant_source = 3; // 发放来源
  CouponType coupon_type = 4;     // 优惠券类型
  uint32 coupon_reduction = 5;    // 能抵扣的密室券数量，仅减免解锁券有值
  uint32 coupon_discount = 6;     // 折扣，范围0～99，如88代表8.8折，仅折扣解锁券有值
  repeated CouponContentLimit content_limits = 7;  // 内容限制，无限制则为空
  string operator = 8;            // 操作人员
  int64 create_time = 9;          // 创建时间，仅作展示用，外部不用写入
  int64 modify_time = 10;         // 修改时间，仅作展示用，外部不用写入
}

message AddCouponConfigReq{
  CouponConfig coupon_config = 1;
}

message AddCouponConfigResp{

}

message ModifyCouponConfigReq{
  CouponConfig coupon_config = 1;
}
message ModifyCouponConfigResp{

}

message ListCouponConfigReq{

}
message ListCouponConfigResp{
  repeated CouponConfig coupon_configs = 1;
}

message CouponGrantUser{
  uint32 uid = 1;
  repeated uint32 playmate_uid_limits = 2; //玩伴限制，无限制则为空
}

// 批量发放优惠券，多用于管理后台发放
message BatGrantCouponReq{
  repeated GrantCouponRecord records = 1; // 被发放列表
  //批量接口下列参数将会覆盖records内同名参数
  string source_detail = 3;           // 活动名称，透传，用于统计分析
  string grant_note = 4;              // 活动备注，方便运营人员查看
  string operator = 5;                // 操作人员

  //下列参数不为0时会覆盖records内同名参数
  int64 start_ts = 6;                 // 开始时间戳
  int64 stop_ts = 7;                  // 结束时间戳
}

message BatGrantCouponResp{
}

message GrantCouponRecord{
  CouponGrantUser users = 1;          // 被发放用户
  uint32 coupon_conf_id = 2;          // 券的配置id
  //以下参数批量发放在外部传入
  string source_detail = 3;           // 活动名称，透传，用于统计分析
  string grant_note = 4;              // 活动备注，方便运营人员查看
  string operator = 5;                // 用于溯源，填写调用发放接口的程序名
  int64 start_ts = 6;                 // 开始时间戳
  int64 stop_ts = 7;                  // 结束时间戳
  string out_order_id = 8;            //外部订单号防止重复发放
}

// 发放优惠券，多用于程序发放
message GrantCouponReq{
  GrantCouponRecord record = 1;
}
message GrantCouponResp{
}
enum CouponStatus{
  COUPON_STATUS_UNDEFINED = 0;
  COUPON_STATUS_UNUSED = 1; // 未使用的
  COUPON_STATUS_USED = 2; // 已使用的
}

enum CouponQueryType{
  COUPON_QUERY_TYPE_UNSPECIFIED = 0;
  COUPON_QUERY_TYPE_VALID = 1;   // 有效的，包括未生效和正在生效中的
  COUPON_QUERY_TYPE_INVALID = 2; // 无效的，包括已使用和已过期的
}

message GetUserCouponCountReq{
  uint32 uid = 1;
  CouponQueryType query_type = 2;
}

message GetUserCouponCountResp{
  uint32 count = 1;
}

message GetUserCouponListReq{
  uint32 uid = 1;
  CouponQueryType query_type = 2;
}

message GetUserCouponListResp{
  repeated UserCouponDetail coupons = 1;
}

message UserCouponDetail{
  string coupon_id = 1;
  CouponStatus coupon_status = 2;
  int64 coupon_start_ts = 3;
  int64 coupon_stop_ts = 4;
  repeated uint32 playmate_uid_limits = 5;

  CouponConfig coupon_config = 10;
}

message GetUserValidCouponReq{
  uint32 uid = 1;
}

message GetUserValidCouponResp{
  repeated UserValidCoupon coupons = 1;
}

message UserValidCoupon{
  string coupon_id = 1;           // 券的唯一id
  CouponType coupon_type = 2;     // 券类型
  uint32 coupon_reduction = 3;    // 能抵扣的密室券数量，仅减免解锁券有值
  uint32 coupon_discount = 4;     // 折扣，范围0～99，如88代表8.8折，仅折扣解锁券有值
  int64 coupon_start_ts = 5;      // 开始时间戳
  int64 coupon_stop_ts = 6;       // 结束时间戳
  repeated GameContentLimit content_limits = 7; // 内容限制，无限制则为空
  repeated uint32 playmate_uid_limits = 8;      // 玩伴限制，无限制则为空
}

message GameContentLimit{
  uint32 game_id = 1; // 只能在该game下使用
  repeated uint32 chapters = 2; //game下章节id列表，空则不限制章节
}

message GetGameTicketOrderQueryOptionsRequest{}
message GetGameTicketOrderQueryOptionsResponse{
  repeated GameTicketOrderQueryOption opts = 1; // 查询选项
}

message GameTicketOrderQueryOption {
  GameTicketOrderQueryType query_type = 1; // 查询类型
  repeated string sub_type = 2; // 查询子类型
  repeated GameTicketOrderStatusWrapper status = 3; // 状态
}

enum GameTicketOrderQueryType{
  GAME_TICKET_ORDER_QUERY_TYPE_UNSPECIFIED = 0;
  GAME_TICKET_ORDER_QUERY_TYPE_ALL = 1; // 全部
  GAME_TICKET_ORDER_QUERY_TYPE_RECHARGE = 2; // 充值
  GAME_TICKET_ORDER_QUERY_TYPE_CONSUME = 3; // 消费
}

message GetGameTicketOrderListRequest{
  uint32 uid = 1; // 用户id
  uint32 query_month = 2; // 查询月份
  GameTicketOrderQueryOption opt = 3; // 选项
  uint32 count = 4; // 数量
  GameTicketOrderListLoadMore load_more = 5; // 首次拉取不传, 加载更多时原封不动地填入上一次GetGameTicketOrderListResp中的load_more字段
}

message GetGameTicketOrderListResponse{
  repeated GameTicketOrder order_list = 1; // 订单列表
  GameTicketOrderListLoadMore load_more = 2; // 下一次加载更多时, 将load_more原封不动地填入请求Req的load_more中; 如果不包含此字段, 表示已经拉完了
}

message GameTicketOrderListLoadMore{
  uint32 last_page = 1;
  uint32 last_count = 2;
}

enum GameTicketOrderStatus{
  GAME_TICKET_ORDER_STATUS_UNSPECIFIED = 0;
  GAME_TICKET_ORDER_STATUS_GAINED = 1; // 已到账
  GAME_TICKET_ORDER_STATUS_CONSUMED = 2; // 已消费
  GAME_TICKET_ORDER_STATUS_FREEZED = 3; // 已冻结
  GAME_TICKET_ORDER_STATUS_ROLLBACK = 4; // 已退回
}

message GameTicketOrderStatusWrapper{
  GameTicketOrderStatus status = 1;
  string text = 2;
}


message GameTicketOrder{
  string order_desc = 1; // 订单描述
  GameTicketOrderStatusWrapper status = 2; // 订单状态
  string order_id = 3; // 订单id
  uint32 updated_at = 4; // 更新时间
  uint32 balance = 5; // 密室券余额
}

