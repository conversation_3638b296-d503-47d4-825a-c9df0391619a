syntax = "proto3";

package game_group_chat;

option go_package = "golang.52tt.com/protocol/services/game-group-chat";

service GameGroupChat {
  // 获取用户发群聊配置
  rpc GetUserExtraInfoConfig(GetUserExtraInfoConfigReq) returns(GetUserExtraInfoConfigResp) {}
  // 获取用户群除基本数据外的业务数据
  rpc GetUserExtraInfo(GetUserExtraInfoReq) returns(GetUserExtraInfoResp) {}
  // 更新用户群除基本数据外的业务数据
  rpc SetUserExtraInfo(SetUserExtraInfoReq) returns(SetUserExtraInfoResp) {}

  // 获取用户群除基本数据外的业务数据
  rpc BatchGetUsersSetExtraInfo(BatchGetUsersSetExtraInfoReq) returns(BatchGetUsersSetExtraInfoResp) {}

  // 获取群活跃度状态
  rpc GetGameGroupActiveStatus(GetGameGroupActiveStatusReq) returns(GetGameGroupActiveStatusResp) {}

}

message GetUserExtraInfoConfigReq {
  uint32 uid = 1;
}

message GroupChatLabel {
  repeated string labels = 1;
}

message GetUserExtraInfoConfigResp {
  // 群名库
  repeated string names = 1;
  // 群简介库
  repeated string intros = 2;
  // 用户群标签
  repeated string labels = 3;
}


message GetUserExtraInfoReq {
  uint32 group_id = 1;
  uint32 uid = 2;
}

message GetUserExtraInfoResp {
  UserExtraInfoItem item = 1;
}

message UserExtraInfoItem {
  uint32 uid = 1;
  uint32 tab_id = 2;
  uint32 group_id = 3;
  repeated string select_labels = 4;
  repeated string config_labels = 5;
}

message SetUserExtraInfoReq {
  UserExtraInfoItem item = 1;
  // 操作类型
  Action action = 2;
}

message SetUserExtraInfoResp {
}


message BatchGetUsersSetExtraInfoReq {
  repeated uint32 group_ids = 1;
}

message BatchGetUsersSetExtraInfoResp {
  map<uint32, UserExtraInfoItem> items = 1;
}


message GetGameGroupActiveStatusReq {
  repeated uint32 group_ids = 1;
}

message GetGameGroupActiveStatusResp {
  // k:group_id, v:status(1-热聊状态)
  map<uint32, uint32> active_map = 1;
}


enum GroupLabelType {
  INVALID = 0; // 默认
  AGE_LABEL = 1; // 年龄标签
  CITY_LABEL = 2; // 城市标签
  MALE_LABEL = 3; // 男用户标签
  FEMALE_LABEL = 4; // 女用户标签
  OTHER_LABEL = 5; // 其它标签
}

enum Action{
  ACTION_INVALID = 0; // 默认
  ACTION_CREATE = 1; // 创建标签
  ACTION_EDIT = 2; // 更新标签
}

message GroupLabel {
  // 标签类型
  GroupLabelType type = 1;
  // 标签名称
  string name = 2;
}

// 创建群聊kafka上报协议
message GameGroupChatEvent {
  // 群组id
  uint32 group_id = 1;
  // 群主uid
  uint32 uid = 2;
  // 群组标签
  repeated GroupLabel group_label = 3;
  // 玩法id
  uint32 tabid = 4;
  // 操作类型
  Action action = 5;
}
