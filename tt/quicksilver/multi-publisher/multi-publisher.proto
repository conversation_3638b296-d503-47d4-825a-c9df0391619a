syntax = "proto3";

package multi_publisher;

option go_package = "golang.52tt.com/protocol/services/multi-publisher";

import "tt/quicksilver/extension/options/options.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";

// 多频道消息发布
service MultiPublisher {

  option (service.options.service_ext) = {
    service_name: "multi-publisher"
  };

  // 获取订阅令牌
  rpc GrantSubscribeToken(GrantSubscribeTokenReq) returns (GrantSubscribeTokenResp) {}
  // 校验订阅令牌
  rpc ValidateSubscribeToken(ValidateSubscribeTokenReq) returns (ValidateSubscribeTokenResp) {}

  // 广播
  rpc Broadcast(BroadcastReq) returns (BroadcastResp) {}
  // 拉取消息
  rpc GetMessages(GetMessagesReq) returns (GetMessagesResp) {}
  // 批量根据seqId获取消息
  rpc GetMessagesBySeqId(GetMessagesBySeqIdReq) returns (GetMessagesBySeqIdResp) {}
}

message ClientInfo {
  // 必填，设备id
  bytes device_id = 1;
  // 必填，客户端类型
  uint32 client_type = 2;
  // 必填，终端类型
  uint32 terminal_type = 3;
  // 必填，客户端ip
  string client_ip = 4;
}

message GrantSubscribeTokenReq {
  // 必选
  uint32 uid = 1;
  // 必选，授权的频道路径列表
  repeated string channel_path_list = 2;
  // 可选，有效期时长，单位秒
  int64 expires_in = 3;
  // 必填，客户端信息，与 ServiceInfo 里的数据一样
  ClientInfo client_info = 4;
  // 可选，自定义参数
  map<string, string> extra = 5;
}

message Token {
  // 令牌内容
  string token = 1;
  // 可选，有效期，单位秒
  int64 expires_in = 2;
}

message GrantSubscribeTokenResp {
  // 订阅令牌
  Token token = 1;
}

message ValidateSubscribeTokenReq {
  // 订阅令牌
  string token = 1;
}

enum ValidateStatusCode {
  // 未指定
  VALIDATE_STATUS_CODE_UNSPECIFIED = 0;
  // 有效
  VALIDATE_STATUS_CODE_VALID = 1;
  // 无效
  VALIDATE_STATUS_CODE_INVALID = 2;
}

message ValidateSubscribeTokenResp {
  // 校验状态码
  ValidateStatusCode code = 1;
}

message Message {
  // 序列id，没填则自动生成
  uint64 seq_id = 1;
  // 必填，频道路径
  string channel_path = 2;
  // 必填，发送者
  Sender sender = 3;
  // 必填，自定义消息数据
  google.protobuf.Any data = 4;
  // 创建时间，不填则自动设置为当前时间
  google.protobuf.Timestamp create_time = 5;
}

message Sender {
  // uid
  uint32 uid = 1;
}

message SendOption {
  // 不做推送通知
  bool no_push = 1;
  // 推送时必填，推送消息类型，see push_.proto PushMessage.CMD_TYPE
  uint32 push_cmd_type = 2;
  // 推送上报标签，可按业务场景监控推送
  string push_label = 3;
  // 不做存储
  bool no_store = 4;
}

message BroadcastReq {
  // 消息
  Message message = 1;
  // 发送选项
  SendOption opt = 2;
}

enum BroadcastStatusCode {
  // 未指定
  BROADCAST_STATUS_CODE_UNSPECIFIED = 0;
  // 成功
  BROADCAST_STATUS_CODE_SUCCESS = 1;
  // 非法请求
  BROADCAST_STATUS_CODE_INVALID_REQUEST = 2;
  // 写消息失败
  BROADCAST_STATUS_CODE_WRITE_MESSAGE_FAILED = 3;
  // 推送消息失败
  BROADCAST_STATUS_CODE_PUSH_MESSAGE_FAILED = 4;
}

message BroadcastStatus {
  // 序列id
  uint64 seq_id = 1;
  // 广播状态码
  BroadcastStatusCode code = 2;
  // 推送请求id，用于跟踪推送链路
  string request_id = 3;
}

message BroadcastResp {
  // 消息状态
  BroadcastStatus status = 1;
}

message GetMessagesReq {
  enum IterType {
    // 未指定，默认正序
    ITER_TYPE_UNSPECIFIED = 0;
    // 正序遍历
    ITER_TYPE_FORWARD = 1;
    // 倒序遍历
    ITER_TYPE_REVERSE = 2;
    // 上下文
    ITER_TYPE_CONTEXT = 3;
  }
  // 必填，频道路径
  string channel_path = 1;
  // 可选，起始 seq_id
  uint64 start_seq_id = 2;
  // 可选，限制查询数量
  uint32 limit = 3;
  // 遍历类型
  IterType iter_type = 4;
}

message GetMessagesResp {
  // 消息列表
  repeated Message messages = 1;
}

message GetMessagesBySeqIdReq {
  // 必填，频道路径
  string channel_path = 1;
  // 必填，序列id
  repeated uint64 seq_id_list = 2;
}

message GetMessagesBySeqIdResp {
  // 消息列表
  map<uint64, Message> message_map = 1;
}
