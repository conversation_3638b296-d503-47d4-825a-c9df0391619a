syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channellistening";
package channellistening;
/* 歌单类型*/
enum SongType{
  OldSongSheetType = 0; /* 挂房听歌 */
  NewSongSheetType = 1; /*就是说唱*/
}
service ChannelListening {
  // 喜欢某首歌
  rpc LikeSong (LikeSongReq) returns (LikeSongResp) {}
  // 获取被喜欢歌曲列表
  rpc GetBeLikedSongList (GetBeLikedSongListReq) returns (GetBeLikedSongListResp) {}
  // 获取喜欢的歌曲列表
  rpc GetLikedSongList (GetLikedSongListReq) returns (GetLikedSongListResp) {}
  rpc GetChannelListeningSimpleInfo (GetChannelListeningSimpleInfoReq) returns (GetChannelListeningSimpleInfoResp) {}
  // 获取花花列表
  rpc GetUserFlowerList (GetUserFlowerListReq) returns (GetUserFlowerListResp) {}
  // 获取花花详情
  rpc GetFlowerDetail (GetFlowerDetailReq) returns (GetFlowerDetailResp) {}
  // 添加歌单
  rpc AddSongMenu (AddSongMenuReq) returns (AddSongMenuResp) {}
  // 获取歌单
  rpc GetAllSongMenu (GetAllSongMenuReq) returns (GetAllSongMenuResp) {}
  // 获取歌单
  rpc GetAllSongMenuForApp (GetAllSongMenuReq) returns (GetAllSongMenuResp) {}
  // 更新歌单
  rpc UpdateSongMenu (UpdateSongMenuReq) returns (UpdateSongMenuResp) {}
  // 重排序歌单
  rpc ReorderSongMenu (ReorderSongMenuReq) returns (ReorderSongMenuResp) {}
  // 往歌单添加歌曲
  rpc AddSong (AddSongReq) returns (AddSongResp) {}
  // 更新审核状态
  rpc UpdateSongStatus(UpdateSongStatusReq) returns (UpdateSongStatusResp) {}
  // 批量往歌单添加歌曲
  rpc BatchAddSong (BatchAddSongReq) returns (BatchAddSongResp) {}
  // 搜索歌曲
  rpc SearchSong (SearchSongReq) returns (SearchSongResp) {}
  // 获取歌曲
  rpc GetSongByMenuId (GetSongByMenuIdReq) returns (GetSongByMenuIdResp) {}
  // 删除歌曲
  rpc DeleteSong (DeleteSongReq) returns (DeleteSongResp) {}
  // 删除歌曲
  rpc DeleteSongV2 (DeleteSongV2Req) returns (DeleteSongV2Resp) {}
  // 获取所有歌曲
  rpc GetSongs (GetSongsReq) returns (GetSongsResp) {}
  // 歌曲排序
  rpc ReorderSong (ReorderSongReq) returns (ReorderSongResp) {}
  // 设置打卡话题
  rpc SetListeningCheckInTopic (SetListeningCheckInTopicReq) returns (SetListeningCheckInTopicResp) {}

  // 获取状态配置
  rpc GetAllMoodCfg (GetAllMoodCfgReq) returns (GetAllMoodCfgResp) {}
  // 设置状态
  rpc SetListeningUserMood (SetListeningUserMoodReq) returns (SetListeningUserMoodResp) {}
  // 获取麦上用户状态
  rpc GetAllListeningOnMicUserMood (GetAllListeningOnMicUserMoodReq) returns (GetAllListeningOnMicUserMoodResp) {}

  /***************打卡、掉落卡片***************************/
  // 打卡
  rpc ListeningCheckIn (ListeningCheckInReq) returns (ListeningCheckInResp) {}
  rpc ListeningCheckInV2 (ListeningCheckInReq) returns (ListeningCheckInResp) {}
  rpc ListeningCheckInV3 (ListeningCheckInV3Req) returns (ListeningCheckInV3Resp) {}

  //获取掉落卡片
  rpc  ListeningDropCardV3 (ListeningDropCardV3Req) returns(ListeningDropCardV3Resp){}

  //分享
  rpc ListeningCheckInShared(ListeningCheckInSharedReq) returns(ListeningCheckInSharedResp){}
  rpc ListeningCheckInSharedV3(ListeningCheckInSharedReq) returns(ListeningCheckInSharedResp){}

  //获取分享内容
  rpc GetChannelListeningCheckInShareCard(GetChannelListeningCheckInShareCardReq)returns(GetChannelListeningCheckInShareCardResp){}
  rpc GetListeningCheckInShareCardV3(GetListeningCheckInShareCardV3Req)returns(GetListeningCheckInShareCardV3Resp){}

  //获取星座
  rpc Constellation(ConstellationReq) returns(ConstellationResp){}

  // 获取打卡榜
  rpc GetListeningUserCheckInInfoList (GetListeningUserCheckInInfoListReq) returns (GetListeningUserCheckInInfoListResp) {}
  // 获取当日在房详情
  rpc GetSharedInfo (GetSharedInfoReq) returns (GetSharedInfoResp) {}
  // 批量获取签到信息
  rpc BatchGetCheckInInfo (BatchGetCheckInInfoReq) returns (BatchGetCheckInInfoResp) {}
  // 获取花花详情
  rpc GetUserFlower (GetUserFlowerReq) returns (GetUserFlowerResp) {}
  rpc BatchGetUserFlower (BatchGetUserFlowerReq) returns (BatchGetUserFlowerResp) {}
}

message ListeningDropCardV3Req{
  uint32 uid = 1;
  uint32 channel_id = 2;
  Constellation constellation = 3;
}

message ListeningDropCardV3Resp{
  ChannelListeningShareCardV3 share_card = 1;
}

// 喜欢某首歌
message LikeSongReq{
  uint32 uid = 1;
  uint32 channel_id = 2;
  ChannelListeningSong song = 3;
}
message LikeSongResp{}
message ChannelListeningSong{
  string id = 1;
  string name = 2;
}

// 被喜欢歌曲列表
message GetBeLikedSongListReq{
  uint32 channel_id = 1;
  uint32 count = 2;
  ChannelListeningLoadMore load_more = 3; // 首次拉取不传, 加载更多时原封不动地填入上一次GetBeLikedSongListResp中的load_more字段
}
message GetBeLikedSongListResp{
  repeated BeLikedSong songs = 1;
  ChannelListeningLoadMore load_more = 2;   // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
}
message BeLikedSong{
  string account = 1;
  string nickname = 2;
  uint32 sex = 3;
  string song_name = 4;
}

// 喜欢的歌曲列表
message GetLikedSongListReq{
  uint32 uid = 1;
  uint32 channel_id = 2;
}
message GetLikedSongListResp{
  repeated string song_list = 1;
}

message ChannelListeningLoadMore {
  uint32 last_page = 1;
  uint32 last_count = 2;
}

// 挂房听歌简要信息
message GetChannelListeningSimpleInfoReq{
  uint32 uid = 1;
  uint32 channel_id = 2;
}
message GetChannelListeningSimpleInfoResp{
  repeated string like_user_accounts = 1; // 喜欢某首歌的用户
  uint32 like_user_count = 2; // 喜欢歌的用户总数
  uint32 today_flower_count = 3; // 当天的花花数
  uint32 entered_at = 4; // 进房时间（当这个值为0时，客户端用本地时间当成进房时间）
  ChannelListenTheme theme = 5;
  string rule_url = 6; // 花花规则
  string check_in_topic = 7; // 打卡话题
  ChannelListeningCheckInInfo check_in_info = 8;
  bool has_user_set_mood = 9; // 用户是否设置心情
}

message ChannelListeningCheckInInfo{
  bool have_check_in = 1; // 当天是否已打卡
  uint32 day = 2; // 打卡天数
  string title = 3; // 打卡称号
  uint32 expire_at = 4; // 过期时间
}

// 获取花花列表
message GetUserFlowerListReq{
  uint32 uid = 1;
  uint32 channel_id = 2;
  uint32 count = 3;
  ChannelListeningLoadMore load_more = 4; // 首次拉取不传, 加载更多时原封不动地填入上一次GetUserFlowerListResp中的load_more字段
}
message GetUserFlowerListResp{
  repeated UserFlower flowers = 1;
  UserFlower flower = 2; // 个人花花记录（仅首次请求时返回）
  uint32 today_flower_count = 3; // 个人花花记录（仅首次请求时返回）
  ChannelListeningLoadMore load_more = 4;   // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
}

message UserFlower{
  string account = 1;
  string nickname = 2;
  uint32 sex = 3;
  uint32 rank = 4;
  uint32 num = 5;
  string check_in_title = 6;
}

message GetFlowerDetailReq{
  uint32 uid = 1;
  uint32 channel_id = 2;
  uint32 count = 3;
  ChannelListeningLoadMore load_more = 4;
}
message GetFlowerDetailResp{
  repeated FlowerDetail details = 1;
  ChannelListeningLoadMore load_more = 2;
}
message FlowerDetail{
  string date = 1;
  string desc = 2; // "在房时长1min"
  uint32 flower_count = 3;
}

message ChannelListenTheme{
  string bg = 1; // 房间背景
  string player_bg = 2; // 音乐播放器背景
  string clock_bg = 3; // 闹钟背景
  string half_flower_url = 4; // 半颗花
  string entire_flower_url = 5; // 完整的花
  string unit = 6; // 单位
  string name = 7;
  repeated string hold_mic_bgs = 8; // 麦上背景
  string leave_mic_bg = 9; // 麦下背景
  string mic_seat = 10; // 麦上麦位
  string normal_seat = 11; // 麦下麦位
  string closed_seat = 12; // 闭麦麦位
  string share_entrance_icon = 13; // 分享入口图标

  repeated string new_hold_mic_bgs = 14; // 麦上背景
  string new_leave_mic_bg = 15; // 麦下背景

  string resource_url = 16;//主题资源url
  string resource_md5 = 17;//主题资源md5

}


// 添加歌单
message AddSongMenuReq{
  SongMenu menu = 1;
  uint32  song_sheet_type = 2;
}
message AddSongMenuResp{
  SongMenu menu = 1;

}

message SongMenu{
  string id = 1;
  string name = 2;
  string bg = 3;
  bool is_open = 4;
}

// 获取歌单
message GetAllSongMenuReq{
  uint32  song_sheet_type = 1;
}
message GetAllSongMenuResp{
  repeated SongMenu menus = 1;
}

// 更新歌单
message UpdateSongMenuReq{
  SongMenu menu = 1;
}
message UpdateSongMenuResp{
  SongMenu menu = 1;
}

// 重排序歌单
message ReorderSongMenuReq{
  repeated SongMenu menus = 1;
}
message ReorderSongMenuResp{}

// 往歌单添加歌曲
message AddSongReq{
  Song song = 1;
}
message AddSongResp{}

message UpdateSongStatusReq{
  string song_id = 1;
  uint32 status = 2;
}
message UpdateSongStatusResp{}

message BatchAddSongReq{
  repeated Song songs = 1;
}
message BatchAddSongResp{}

// 搜索歌曲
message SearchSongReq{
  string menu_id = 1;
  string song_name = 2;
  string singer = 3;
  uint32 page = 4;
  uint32 count = 5;
}
message SearchSongResp{
  repeated Song songs = 1;
  uint32 total_count = 2;
}

// 获取歌曲
message GetSongByMenuIdReq{
  string menu_id = 1;
}
message GetSongByMenuIdResp{
  repeated Song songs = 1;
}

// 删除歌曲
message DeleteSongReq{
  string menu_id = 1;
  repeated string song_ids = 2;
}
message DeleteSongResp{}

// 删除歌曲
message DeleteSongV2Req{
  string song_id = 1;
}
message DeleteSongV2Resp{}

message Song{
  string id = 1;
  string name = 2;
  string singer = 3;
  uint32 music_type = 4;
  string file = 5;
  string file_size = 6;
  string file_type = 7;
  string file_hash = 8;
  uint32 uid = 9;
  string nickname = 10;
  uint64 created_at = 11;
  uint32 status = 12;
  string account = 13;
  uint32 order = 14; // 序号
  SongMenu menu = 15;
}

// 获取所有歌曲
message GetSongsReq{
  uint32 page = 1;
  uint32 count = 2;
}
message GetSongsResp{
  repeated Song songs = 1;
  uint32 total_count = 2;
}

// 歌曲排序
message ReorderSongReq{
  Song song = 1;
  uint32 target_order = 2;
}
message ReorderSongResp{}

message SetListeningCheckInTopicReq{
  uint32 channel_id = 1;
  string topic = 2;
}
message SetListeningCheckInTopicResp{}

message ListeningCheckInReq{
  uint32 uid = 1;
  uint32 channel_id = 2;
}

message ListeningCheckInResp{
  ChannelListeningCheckInType type = 1;
  ChannelListeningCheckInInfo check_in_info = 2;
  ChannelListeningCheckInShareCard share_card = 3;
}

enum Constellation{
  undefined = 0;
  aries = 1;//白羊座 3月21-4月19
  taurus = 2;//金牛座 4月20-5月20
  gemini = 3;//双子座 5月21-6月21
  cancer = 4;//巨蟹座 6月22-7月22
  leo = 5;//狮子座 7月23-8月22
  virgo = 6;//处女座 8月23-9月22
  libra = 7;//天秤座 9月23-10月23
  scorpio = 8;//天蝎座 10月24-11月22
  sagittarius = 9;//射手座 11月23-12月21
  capricorn = 10;//摩羯座 12月22-1月19
  aquarius = 11;//水瓶座 1月20-2月18
  pisces = 12;//双鱼座 2月19-3月20
}

message ConstellationReq{
  uint32 uid = 1;
}

message ConstellationResp{
  Constellation constellation_choose = 1;
}

message ListeningCheckInV3Req{
  uint32 uid = 1;
  uint32 channel_id = 2;
  Constellation constellation_choose = 3;//不传走用户生日星座
}

message ListeningCheckInV3Resp{
  ChannelListeningCheckInType type = 1;
  ChannelListeningCheckInInfo check_in_info = 2;
  ChannelListeningShareCardV3 share_card = 3;
}

message ListeningCheckInSharedReq{
  string share_id = 1;
}

message ListeningCheckInSharedResp{

}

message ChannelListeningShareCardV3{
  string account = 1;//客户端用来拼接头像
  string nick_name = 2;//昵称
  int64 show_time = 3;//展示时刻的时间戳

  uint32 display_id = 4;//用来显示房间号
  uint32 channel_id = 5;//进房
  string channel_name = 6;//房名

  string card_img = 7;//卡片图片
  string bg_img = 8;//背景图片
  string bt_desc = 9;//按钮文案
  string web_title = 10;//web整个页面标题

  string fortune = 11;//运势
  Constellation match_constellation = 12;//星座
  Constellation choose_constellation = 13;//自身星座

  ChannelListeningCheckInShareInfo share_info = 14;//分享信息，web不需要
  string channel_view_id = 15;
}



message ChannelListeningCheckInShareCard{
  string check_in_title_desc = 1;//用户称号文案-逆袭卷王
  int64 show_time = 2;//展示时刻的时间戳
  string account = 3;//客户端用来拼接头像
  string nick_name = 4;//昵称
  string little_title = 5;//小标题-今日摸鱼运势
  string title = 6;//大标题-上班摸鱼，下班回家
  uint32 channel_id = 7;//房间id
  string card_img = 8;//卡片图片
  string web_bg = 9;//web背景图片
  string bt_desc = 10;//按钮文案
  string web_title = 11;//web整个页面标题
  ChannelListeningCheckInShareInfo share_info = 12;//分享信息，web不需要
  uint32 uid = 13;
  uint32 display_id = 14;
  string channel_view_id = 15;
}

message GetChannelListeningCheckInShareCardReq{
  string share_id = 1;
}

message GetChannelListeningCheckInShareCardResp{
  ChannelListeningCheckInShareCard share_card = 1;
}

message ChannelListeningCheckInShareInfo{
  string id = 1;//分享id
  string title = 2;//大标题
  string sub_title = 3;//小标题
  string img = 4;//分享图片
  string share_url = 5;//分享url
  string new_sub_title = 6;//小标题
}

message GetListeningCheckInShareCardV3Req{
  string share_id = 1;
}

message GetListeningCheckInShareCardV3Resp{
  ChannelListeningShareCardV3 share_card = 1;
}

enum ChannelListeningCheckInType{
  ChannelListeningCheckInType_UNDEFINED = 0;
  ChannelListeningCheckInType_SUCCESS = 1; // 打卡成功
  ChannelListeningCheckInType_GET_TITLE = 2; // 获得称号
  ChannelListeningCheckInType_RENEW_TITLE = 3; // 续期称号
}

// 获取打卡榜
message GetListeningUserCheckInInfoListReq{
  uint32 channel_id = 1;
  uint32 uid = 2;
  uint32 count = 3;
  ChannelListeningLoadMore load_more = 4; // 首次拉取不传, 加载更多时原封不动地填入上一次GetUserCheckInInfoListResp中的load_more字段
}
message GetListeningUserCheckInInfoListResp{
  repeated UserCheckInInfo list = 1;
  UserCheckInInfo info = 2; // 个人打卡记录（仅首次请求时返回）
  ChannelListeningLoadMore load_more = 3;   // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
}

message UserCheckInInfo{
  string account = 1;
  string nickname = 2;
  uint32 sex = 3;
  uint32 rank = 4;
  uint32 day = 5; // 连续打卡天数
  string check_in_title = 6; // 称号
}

// 获取当天在房详情
message GetSharedInfoReq{
  uint32 channel_id = 1;
  uint32 uid = 2;
}
message GetSharedInfoResp{
  string title = 1; // 标题
  string bg_url = 2; // 背景图片
  string text = 3; // 文案
  string card_url = 4; // 卡片
  string theme = 5; // 挂房听歌主题
  SharedInfoMood mood = 6;
  string new_bg_url = 7; // 背景url
}

message SharedInfoMood{
  string hand_lottie = 1; // 手部lottie
  string foot_lottie = 2; // 脚部lottie
  string bg = 3; // 桌子切图
  string mood_name = 4; // 状态名称
}

message BatchGetCheckInInfoReq{
  uint32 channel_id = 1;
  repeated uint32 uids = 2;
}
message BatchGetCheckInInfoResp{
  map<uint32, ChannelListeningCheckInInfo> info = 1;
}

message GetUserFlowerReq{
  uint32 channel_id = 1;
  uint32 uid = 2;
}
message GetUserFlowerResp{
  UserFlower flower = 1;
  string flower_name = 2;
  string unit = 3;
}

message BatchGetUserFlowerReq{
  uint32 channel_id = 1;
  repeated uint32 uid_list = 2;
}
message BatchGetUserFlowerResp{
  map<uint32, UserFlower> user_flower_map = 1;
  string flower_name = 2;
  string unit = 3;
}

message MusicBook{
  map<uint32, uint32> rhythm_point = 1; // key为时间戳（单位：毫秒），value为音频文件md5
}

message ListeningMood{
  string id = 1;
  string text = 2; // 状态文案
  string img = 3; // 图片
  string lottie1_url = 4;
  string lottie1_md5 = 5;
  string lottie2_url = 6;
  string lottie2_md5 = 7;
  string lottie_complete_url = 8;
  string lottie_complete_md5 = 9;
}

message GetAllMoodCfgReq{}
message GetAllMoodCfgResp{
  repeated ListeningMood moods = 1;
}

message SetListeningUserMoodReq{
  uint32 channel_id = 1;
  uint32 uid = 2;
  string mood_id = 3;
}
message SetListeningUserMoodResp{}

message GetAllListeningOnMicUserMoodReq{
  uint32 channel_id = 1;
  uint32 uid = 2;
}
message GetAllListeningOnMicUserMoodResp{
  map<uint32, ListeningMood> user_mood = 1;
}