syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/superplayerdress";
package superplayerdress;

service SuperPlayerDress {
    rpc GetDressConfigMaxVersion (GetDressConfigMaxVersionReq) returns (GetDressConfigMaxVersionResp) {
    }

    rpc GetDressConfigList (GetDressConfigListReq) returns (GetDressConfigListResp) {
    }

    rpc GetDressInUse ( GetDressInUseReq ) returns ( GetDressInUseResp ) {
    }

    rpc SetDressInUse ( SetDressInUseReq ) returns ( SetDressInUseResp ) {
    }

    rpc GetWebDressConfig (GetWebDressConfigReq) returns (GetWebDressConfigResp) {
    }

    rpc GetUserCurrChatBgDressIdList (GetUserCurrChatBgDressIdListReq) returns (GetUserCurrChatBgDressIdListResp) {
    }

    rpc SetChatBgSpecialDressInUse (SetChatBgSpecialDressInUseReq) returns (SetChatBgSpecialDressInUseResp) {
    }

    rpc SetDefaultDressInUse (SetDefaultDressInUseReq) returns (SetDefaultDressInUseResp) {
    }

    rpc GetUserCurrChatBgSpecialDressId (GetUserCurrChatBgSpecialDressIdReq) returns (GetUserCurrChatBgSpecialDressIdResp) {
    }

    rpc GetUserDressHistory (GetUserDressHistoryReq) returns (GetUserDressHistoryResp) {
    }

    // 运营后台装扮配置相关接口
    rpc AddDressInfo (AddDressInfoReq) returns (AddDressInfoResp) {
    }
    rpc UpdateDressInfo (UpdateDressInfoReq) returns (UpdateDressInfoResp) {
    }
    rpc UpdateDressInfoList ( UpdateDressInfoListReq ) returns ( UpdateDressInfoListResp ) {
    }
    rpc DelDressInfo (DelDressInfoReq) returns (DelDressInfoResp) {
    }
    rpc GetDressInfoList (GetDressInfoListReq) returns (GetDressInfoListResp) {
    }

    // 体验装扮相关接口
    // 发放体验装扮
    rpc SendDressExperience (SendDressExperienceReq) returns (SendDressExperienceResp) {}
    // 获取用户可体验装扮列表
    rpc GetDressUserExperienceInfo (GetDressUserExperienceInfoReq) returns (GetDressUserExperienceInfoResp) {}
    // 体验装扮配置接口
    // 获取体验装扮包裹配置
    rpc GetDressExperiencePackage (GetDressExperiencePackageReq) returns (GetDressExperiencePackageResp) {}
    // 设置体验装扮包裹配置
    rpc SetDressExperiencePackage (SetDressExperiencePackageReq) returns (SetDressExperiencePackageResp) {}
}

//装扮类型
enum DressType {
  DRESS_TYPE_UNSPECIFIC = 0;
  DRESS_TYPE_ROOM_SUIT = 1;  //房间套装
  DRESS_TYPE_SPECIAL_CONCERN = 2;  //特别关心
  DRESS_TYPE_CHAT_BACKGROUND = 3;  //聊天背景
  DRESS_TYPE_CHAT_BUBBLE = 4;  //聊天气泡
}

// 装扮状态
enum DressStatusType {
  DRESS_STATUS_SHELF = 0;  // 上架中
  DRESS_STATUS_UNSHELF = 1; // 下架中
}

//会员装扮
message Dress {
    uint32 id = 1;     //装扮ID
    uint32  level = 2;   //最小多少级才有这个装扮
    string resource_url = 3; //客户端资源URL
    string resource_md5 = 4; //客户端资源md5
    string min_client_version = 5; //客户端最低版本限制
    string name = 6; //装扮名称
    string desc = 7; //装扮描述
    int64 version = 8; //装扮版本号
    string web_list = 9; //web 列表图
    string web_preview = 10; //web 预览图，客户端静态兜底
    string web_audio = 11; //web 音频
    string web_video = 12; //web 视频
    string web_config = 13; // 用来保存设计上传的zip资源链接
    string web_top = 14; //web  top图
    string web_static = 15; //web 静态配件图
    uint32 removed = 16; //是否已经下架 0否1是  see DressStatusType
    string com_desc = 17;  // 组件文案
    uint64 begin_ts = 18; 
    uint64 end_ts = 19; 
    uint32 is_default = 20;  // 是否是默认装扮
    string operator = 21; // 操作者
    int64  index = 22; //排序，小到大
    string preview_mp4 = 23; // 预览图 mp4压缩版
}

// 装扮信息
message DressInfo {
   uint32 dress_type = 1; //装扮类型 see DressType
   Dress dress_info = 2; // 装扮信息
}

// 添加装扮
message AddDressInfoReq {
   DressInfo info = 1;
}
message AddDressInfoResp {
  uint32 max_version = 1; //配置最新版本
}

// 更新装扮
message UpdateDressInfoReq {
   DressInfo info = 1;
   uint32 max_version = 2;
}
message UpdateDressInfoResp {
  uint32 max_version = 1; //配置最新版本
}

//更新多个装扮
message UpdateDressInfoListReq {
   repeated DressInfo info_list = 1;
   uint32 max_version = 2; //配置最新版本
}
message UpdateDressInfoListResp {
  uint32 max_version = 1; //配置最新版本
}

// 删除装扮
message DelDressInfoReq {
   uint32 dress_type = 1; //装扮类型 see DressType
   uint32 dress_id = 2; // 装扮id
}
message DelDressInfoResp {
  uint32 max_version = 1; //配置最新版本
}

// 获取装扮列表 
message GetDressInfoListReq {
   uint32 dress_type = 1; //装扮类型 see DressType 填0 代表获取全部
}
message GetDressInfoListResp {
   repeated DressInfo info_list = 1;
   uint32 max_version = 2; //配置最新版本
}

message GetDressConfigMaxVersionReq{
      int64 max_version = 1; //本地最大版本号
      DressType dress_type = 2; //装扮类型 see DressType
}

message GetDressConfigMaxVersionResp {
    int64 max_version = 1; //服务器最大版本号
    DressType dress_type = 2; //装扮类型 see DressType
}

//会员装扮配置列表
message GetDressConfigListReq{
      DressType dress_type = 1; //装扮类型 see DressType
      uint32 offset = 2; //开始偏移  开始为0
      uint32 limit = 3; //分页大小  建议每次50
}

message GetDressConfigListResp {
    DressType dress_type = 1; //装扮类型 see DressType
    repeated Dress dress_list = 2; //等级对应的装扮列表
    uint32 offset = 3; //开始偏移 开始为0
    uint32 limit = 4; //分页大小 建议每次50
    bool is_end = 5; //是否已经全部结束
}

//获取正在使用的装扮（通用 只返回一个ID的）
message GetDressInUseReq{
      int64 id = 1;             // uid or channel id
      DressType dress_type = 2; //装扮类型 see DressType
      uint32  level = 3;        //当前会员等级
}

 //获取正在使用的装扮
 message GetDressInUseResp{
       int64 id = 1;             // uid or channel id
       DressType dress_type = 2; //装扮类型 see DressType
       uint32  level = 3;        //当前会员等级
       uint32 dress_id = 4;      //装扮ID
 }

 //设置正在使用的装扮
 message SetDressInUseReq{
       int64 id = 1;            // uid or channel id
       DressType dress_type = 2; //装扮类型 see DressType
       uint32  level = 3;        //当前会员等级
       uint32 dress_id = 4;       //装扮ID 取消使用为0
 }

 //
 message SetDressInUseResp{
       int64 id = 1;            // uid or channel id
       DressType dress_type = 2; //装扮类型 see DressType
       uint32  level = 3;        //当前会员等级
       uint32 dress_id = 4;       //装扮ID
       uint32 need_level = 5;     // 装扮所需ID，无论如何都返回
 }

//前端会员装扮配置列表
message GetWebDressConfigReq{
      DressType dress_type = 1; //装扮类型 see DressType
}

message GetWebDressConfigResp {
    DressType dress_type = 1; //装扮类型 see DressType
    repeated Dress dress_list = 2; //
}


// 获取用户当前聊天背景
message GetUserCurrChatBgDressIdListReq {
    uint32 uid = 1;
    uint32 level = 2;        //当前会员等级
}

//特殊装扮
message ChatBgSpecial {
    string to_account = 1;     //聊天对象
    uint32 dress_id = 2;     //装扮ID
}


message GetUserCurrChatBgDressIdListResp {
    uint32 common_dress_id = 1;             //普通聊天背景装扮
    repeated ChatBgSpecial dress_list = 2;  //特殊装扮
}


 //设置正在使用的装扮
 message SetChatBgSpecialDressInUseReq{
      int64 id = 1;            // my uid  
      uint32  level = 3;        //当前会员等级
      uint32 dress_id = 4;       //装扮ID 取消使用为0
      string to_account = 5;
      string replace_account = 6;
 }

 //
 message SetChatBgSpecialDressInUseResp{
 }


 //设置默认使用的装扮
 message SetDefaultDressInUseReq{
      int64 uid = 1;            //uid  
 }

message SetDefaultDressInUseResp{
}

// 获取用户当前聊天背景
message GetUserCurrChatBgSpecialDressIdReq {
    uint32 uid = 1;
    uint32 level = 2;        //当前会员等级
    string account = 3;
}

message GetUserCurrChatBgSpecialDressIdResp {
    uint32 dress_id = 1;      //1V1聊天背景装扮
}

// 获取用户会员装扮历史记录
message GetUserDressHistoryReq {
   uint32 uid = 1;
   DressType type = 2;
   uint32 offset = 3;  //分页偏移 开始是0
   uint32 limit = 4;   //单页大小 建议单次20
}

// 装扮历史记录
message UserDressHistory {
    uint32 dress_id = 1;
    uint32 create_time = 2;
    uint32 removed = 3;  // 0 正常 1已经下架
    string name = 4;
    string web_list = 5;
    uint32 level = 6;
}

// 获取用户会员装扮历史记录
message GetUserDressHistoryResp {
   uint32 uid = 1;
   DressType type = 2;
   uint32 offset = 3;
   uint32 limit = 4;
   repeated UserDressHistory history_list = 5;  //装扮历史记录   history_list < limit 结束
}

// 发放会员装扮体验包
message SendDressExperienceReq {
  uint32 super_player_uid = 1; // 会员ID
  uint64 incr_conf_package = 2; // 需要增加的对应配置包，若传0则为默认配置
  string order_id = 3; // 订单ID，由调用这个接口方维护
  string reason = 4;// 理由
  int64  server_time = 5; // 任务服务时间戳，同一个单不能变
}

message SendDressExperienceResp {
}

message ExperienceInfo {
  Dress dress_info = 1; // 装扮信息
  int64 experience_end_time = 2; // 体验装扮截止时间戳
}

// 获取用户当前可体验装扮列表
message GetDressUserExperienceInfoReq {
  uint32 uid = 1; // 会员ID
  DressType dress_type = 2; //装扮类型 see DressType
}
message GetDressUserExperienceInfoResp {
  repeated ExperienceInfo experience_list = 1; // 可体验装扮列表
}

// 体验装扮配置类型
enum ExperienceConfigType {
  EXPERIENCE_CONFIG_TYPE_UNSPECIFIC = 0;
  EXPERIENCE_CONFIG_TYPE_LEVEL = 1;  // 根据等级进行体验
  EXPERIENCE_CONFIG_TYPE_ITEM = 2;  // 根据道具进行体验
}

// 体验装扮等级类型
enum LevelType {
  LEVEL_TYPE_UNSPECIFIC = 0;
  LEVEL_TYPE_LESS_THAN = 1;  // 小于
  LEVEL_TYPE_MORE_THAN = 2;  // 大于
  LEVEL_TYPE_EQUAL = 3;  // 等于
  LEVEL_TYPE_LESS_THAN_OR_EQUAL = 4;  // 小于等于
  LEVEL_TYPE_MORE_THAN_OR_EQUAL = 5;  // 大于等于
}

// 体验装扮配置
message DressExperienceConfig {
  ExperienceConfigType type = 1; // see ExperienceConfigType
  uint32 level = 2; // 等级
  LevelType level_type = 3; // see LevelType
  int64 start_time = 4; // 开始时间 当根据等级进行体验时，使用该字段
  int64 end_time = 5; // 结束时间 当根据等级进行体验时，使用该字段
  uint32 item_id = 6; // 道具ID
  DressType item_type = 7; // 道具类型
  int64 duration = 8; // 体验时长 当根据道具进行体验时，使用该字段
}

message DressExperiencePackage {
  uint64 id = 1; // 包裹ID
  repeated DressExperienceConfig config_list = 2; // 体验装扮配置列表
  bool only_can_get_once = 3; // 是否只能领取一次
}

// 获取体验装扮包裹
message GetDressExperiencePackageReq {
  uint64 id = 1; // 包裹ID，为0返回默认值
}
message GetDressExperiencePackageResp {
  DressExperiencePackage package = 1; // 包裹配置
}

// 设置体验装扮包裹
message SetDressExperiencePackageReq {
  DressExperiencePackage package = 1; // 包裹配置
  string operator = 2; // 操作人
}
message SetDressExperiencePackageResp {
}

//会员穿戴装扮 新增
message SuperPlayerUseDressEvent {
  uint32 super_player_uid = 1; // 会员ID
  uint32 create_time = 2; // 事件产生时间
  DressType type = 3; // 装扮类型
  uint32 dress_id = 4; // 穿戴装扮ID
}