syntax = "proto3";

package present_count_go;

option go_package = "golang.52tt.com/protocol/services/present-count-go";
import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

message PresentCountReq
{
  uint32 channel_id = 1;//房间id
  bool is_off = 2;// true 打开 false 关闭
  repeated uint32 micr_users = 3;
  uint32 uid = 4; //uid
  uint32 channel_type = 5; // 房间类型
}

message GetPresentCountByIdReq
{
  uint32 channel_id = 1;
  uint32 uid = 2;
}

message GetPresentCountByIdResp
{
  uint32 price = 1;
}

message PresentCountResp
{
}

//获得房间送礼统计开关状态
message GetPresentCountStateReq
{
  uint32 channel_id = 1;
}

message MicsPresentCountInfo
{
  uint32 uid = 1;
  uint32 price = 2;
  string bg_suffix = 3;
  string ic_suffic = 4;
}

message GetPresentCountStateResp
{
  bool state = 1;
  repeated MicsPresentCountInfo mics_present_count = 2;
  string style_prefix = 3;
  string uncompiled_prefix = 4;
  string bg_suffix = 5;
  string ic_suffic = 6;
}

message ClearUserPresentCountReq {
  uint32 uid = 1;
  uint32 channel_id = 2;
}

message ClearUserPresentCountResp {}

message PresentCountTargetUser{
    uint32 uid = 1;
    uint32 receive = 2;
    uint32 next_level_value = 3;
    string next_level_url = 4;
}

//排行榜，首杀那些需要神秘人的当时状态信息，所以定义一个神秘人信息结构
message UkwInfo {
    uint32 uid = 1;  // 真uid
    string account = 2; 
    string nickname = 3;
    uint32 level = 4; 
    string medal = 5; // 神秘人勋章
    string head_frame = 6; // 神秘人头像框
    uint32 fake_uid = 7;  // 假uid
 }

message PresentCountRankUser{
    uint32 uid = 1;
    uint32 send_value = 2;
    uint32 rank = 3;
    uint32 d_value = 4;
    UkwInfo ukw_info = 5; 
}

message GetPresentCountRankReq{
    uint32 uid = 1;
    uint32 target_uid = 2;
    uint32 channel_id = 3;
    uint32 offset = 4;
    uint32 limit = 5;
}

message GetPresentCountRankResp{
    uint32 channel_id = 1;
    PresentCountTargetUser target_user = 2;
    PresentCountRankUser my_info = 3;
    repeated PresentCountRankUser rank_list = 4;
    uint32 next_offset = 5;
    uint32 view_cnt = 6;
}

service PresentCountGo
{
    // 3、添加旧的服务名声明，格式：包名.服务名
  option (service.options.old_package_name) = "PresentCountGo";

  rpc SwitchPresentCount(PresentCountReq) returns(PresentCountResp){
  }

  rpc GetPresentCountById(GetPresentCountByIdReq) returns (GetPresentCountByIdResp){
  }

  rpc GetPresentCountState(GetPresentCountStateReq) returns (GetPresentCountStateResp){
  }

  rpc ClearUserPresentCount(ClearUserPresentCountReq) returns (ClearUserPresentCountResp){
  }

  rpc GetPresentCountRank(GetPresentCountRankReq) returns (GetPresentCountRankResp){
    }
  // ===================== 礼物kafka对账 =========================
  //礼物->图鉴对账
  rpc TimeRangeCount (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.CountResp ) {}
  rpc TimeRangeOrderIds (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.OrderIdsResp ) {}
  //补单图鉴
  rpc FixPresentCountOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

}
