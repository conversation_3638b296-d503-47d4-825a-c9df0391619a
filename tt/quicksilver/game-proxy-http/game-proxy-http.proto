syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/game-proxy-http";
package game_proxy_http;

// 成都游戏Http接口

message Plot {
  uint32 plot_chapter = 1;
  string plot_id = 2;
  string plot_name = 3;
  bool is_unlock = 4;
}

//接口地址:/api/tt/TTInviteUnlock
//请求方式:POST
//请求数据类型:application/json
//响应数据类型:*/*
message InviteUnlockReq {
  uint32 be_invited_uid = 1; // 被邀请的用户UID
  uint32 chapter_num = 2; // 解锁章节数
  uint32 gameid = 3; // 游戏ID
  uint32 uid = 4; // 目标解锁用户
  string order_id = 5; // 订单号 避免重复发奖
}
message InviteUnlockResp {
  repeated Plot data = 1;
}

//接口地址:/api/tt/TTGetUserPlot
//请求方式:POST
//请求数据类型:application/json
//响应数据类型:*/*
message GetUserPlotReq {
  uint32 gameid = 1; // 游戏ID
  uint32 uid = 2; // 目标用户
}
message GetUserPlotResp {
  repeated Plot data = 1;
}

//接口地址:/api/tt/TTUnlockScript
//请求方式:POST
//请求数据类型:application/json
//响应数据类型:*/*
message UnlockPlotReq {
  repeated string chapter_list = 1; //需要解锁的章节ID列表  和chapter_num 2选1 优先级高于chapter_num
  uint32 chapter_num = 2; //需要解锁的章节数量  和chapter_list 2选1 优先级低于chapter_list
  uint32 gameid = 3; // 游戏ID
  uint32 uid = 4; // 目标解锁用户
  string order_id = 5; //订单ID 当订单ID重复的时候会返回成功，且返回已解锁列表，但不会实际解锁
  string make = 6;//解锁说明
  string type = 7;//活动的type (成都提供，纸嫁衣累计登录：nextLogin）
}
message UnlockPlotResp {
  repeated Plot data = 1;
}