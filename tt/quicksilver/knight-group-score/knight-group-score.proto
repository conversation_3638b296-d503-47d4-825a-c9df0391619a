syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/knightgroupscore";
package knightgroupscore;


service KnightGroupScore {
    rpc AddKnightScore (AddKnightScoreReq) returns (AddKnightScoreResp) {
    }
    rpc GetKnightScore (GetKnightScoreReq) returns (GetKnightScoreResp) {
    }
    rpc GetKnightScoreDetail (GetKnightScoreDetailReq) returns (GetKnightScoreDetailResp) {
    }
    rpc TimeRangeScoreOrder ( TimeRangeReq ) returns ( CountResp ) {
    }
    rpc TimeRangeOrderIds ( TimeRangeReq ) returns ( OrderIdsResp ) {
    }
    rpc GetKnightScoreRecordList(GetKnightScoreRecordListReq) returns (GetKnightScoreRecordListResp) {
    }
}

enum ReasonType
{
	REASON_UNKNOWN = 0;
	REASON_RECEIVE_PRESENT = 1;		// 收到礼物获得积分
	REASON_EXCHANGE_RED_DIAMOND = 2;// 积分兑换红钻（不存在这种情况）
	REASON_SETTLEMENT = 3; 			// 积分提现
	REASON_EXCHANGE_TBEANS = 4;		// 积分兑换T豆
	REASON_SETTLEMENT_REVERT = 5;	// 提现余额返积分,2017-09一次性操作
	REASON_TRIVIAGAME_REWARD = 6;	// 答题活动奖励积分
	REASON_WITHDRAW_FAILED_ROLL= 7;	        // 提现打款失败，回滚积分
	REASON_EXCHANGE_TCOIN_FAILED_ROLL= 8;	// 兑换T豆失败，回滚积分
	REASON_OFFICIAL_RECYCLE= 9;	            // 官方运营回收积分
	REASON_OFFICIAL_REWARD= 10;	            // 官方运营发放积分
	REASON_GUILD_CHANGE_PRIVATE = 11;	    // 主动将个人对公转为对私扣除积分
	REASON_GUILD_QUIT = 12;			//解约导致个人对公转为对私扣除积分
	REASON_GUILD_OFFICIAL_RECYCLE = 13;	//官方回收个人对公权扣除积分
	REASON_GUILD_EXCHANGE = 14;	//会长提现个人对公积分
}


enum KnightOwnerType
{
   ENUM_UNKOWN_OWNER = 0;  // 未开通
   ENUM_USER_OWNER = 1;  // 用户
   ENUM_GUILD_OWNER = 2;  // 公会
}

//增加、扣减积分
message AddKnightScoreReq {
    string order_id = 1; //订单号
    string reason_detail = 2; //理由
    ReasonType reason_type = 3;
    uint32 owner_id = 4;
    int32 score = 5;
    uint32 server_time = 6;
}

message AddKnightScoreResp{
    int64 final_score = 1;
}

//查询当前骑士积分
message GetKnightScoreReq {
    uint32 owner_id = 1;
}

message GetKnightScoreResp{
    int64 score = 1;
}

message KnightScoreDetail {
    int32 change_score = 1;
    ReasonType reason_type = 2;
    string reason_detail = 3;
    string order_id = 4;
    uint32 create_time = 5;
}

message GetKnightScoreDetailReq {
    uint32 owner_id = 1;
    int64 begin_time = 2;
    int64 end_time = 3;
    uint32 off = 4;
    uint32 limit = 5;
    repeated ReasonType reason_type_list = 6; //不传就全部
}

message GetKnightScoreDetailResp{
    repeated KnightScoreDetail detail_list = 1;
}

//对账接口
//获得订单数据
message TimeRangeReq {
  int64 begin_time = 1;
  int64 end_time = 2;
  string params = 3;
}

//响应order_id个数
message CountResp {
  uint32 count = 1;
  uint32 value = 2; //总价值，如果没有填0
}

//响应orderId详情
message OrderIdsResp {
  repeated string order_ids = 1;
  repeated uint32 values = 2;
  repeated uint32 uids = 3;
}

// 骑士积分记录
message KnightScoreRecord {
   uint32 uid = 1;
   uint32 before_ts_remain_score = 2; // 查询开始时间之前剩余积分
   uint32 new_score = 3;  // 新产生的积分
   uint32 exchange_money_score = 4;  // 提现积分
   uint32 exchange_tbean_score = 5;  // 兑换T豆积分
   uint32 exchange_money_fail_return_score = 6;   //提现失败返还骑士积分
   uint32 official_reclaim_score = 7;   // 官方运营回收骑士积分
   uint32 official_grant_score = 8;   // 官方运营发放骑士积分
   uint32 remain_score = 9;  // 剩余骑士积分
}

// 获取骑士积分记录列表
message GetKnightScoreRecordListReq {
   uint32 begin_ts = 1;
   uint32 end_ts = 2;
   repeated uint32 uid_list = 3;
   uint32 page = 4; // 从1开始
   uint32 page_size = 5;
   uint32 min_remain_score = 6;  // 最小剩余积分
}
message GetKnightScoreRecordListResp {
   repeated KnightScoreRecord record_list = 1;
   uint32 total_cnt = 2;
   uint32 next_page = 3;   // 0表示结束
}