syntax = "proto3";

package channelol_stat_go;
option go_package = "golang.52tt.com/protocol/services/channelol-stat-go";

service ChannelOlStatGo {

    // 获取当前全网在线房间列表
    rpc GetChannelOLListByType(GetChannelOLListByTypeReq)returns( GetChannelOLListByTypeResp){
    }

    // Get 房间在线人数统计
    rpc GetChannelOLStatInfo(GetChannelOLStatInfoReq)returns( GetChannelOLStatInfoRsp){
    }

    //批量获取房间在线人数
    rpc BatGetChannelMemberSize(BatGetChannelMemberSizeReq)returns( BatGetChannelMemberSizeResp){

    }
}

message GetChannelOLListByTypeReq {
  uint32 count = 1;
  uint32 offset = 2;
  uint32 min_member_limit = 3;
  uint32 channel_type = 4;
}

message GetChannelOLListByTypeResp {
  repeated ChannelOLStatInfo channel_list = 1;
  uint32 all_channel_cnt = 2;   // 满足channel_type和min_member_limit限制的全部房间数
  uint32 all_member_cnt = 3;    // 满足channel_type和min_member_limit限制的全部在线人数
}

message ChannelOLStatInfo {
  uint32 channel_id = 1;
  uint32 member_cnt = 2;
}

message GetChannelOLStatInfoReq
{
  uint32 channel_id = 1;
}

message GetChannelOLStatInfoRsp
{
  ChannelOLStatInfo stat_info = 1;
}

message BatGetChannelMemberSizeReq{
  repeated uint32 channel_id_list = 1;
}

message ChannelMemberSizeInfo {
  uint32 channel_id = 1;
  uint32 member_cnt = 2;
}
message BatGetChannelMemberSizeResp{
  repeated ChannelMemberSizeInfo channel_member_size_list = 1;
}




