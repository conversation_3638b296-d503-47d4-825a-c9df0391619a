syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-msg-express";

package channel_msg_express;

import "channel/channel_.proto";
import "tt/quicksilver/push_notification/v2/push/pushnotification_v2.proto";

service ChannelMsgExpress {
    // 发送带序列号的消息
    rpc SendChannelMsg (SendChannelMsgReq) returns (SendChannelMsgResp) {
    }
    // 发送无序列号的消息
    rpc SendChannelBroadcastMsg (SendChannelBroadcastMsgReq) returns (SendChannelBroadcastMsgResp) {
    }
    // 发送给特定用户
    rpc PushToUsers(PushToUsersReq) returns (PushToUsersRsp) {
    }
    // 发送给特定用户（需要可靠推送给多用户时使用该接口）
    rpc PushToUserMap(PushToUserMapReq) returns (PushToUserMapRsp) {
    }
    // 消息转发
    rpc SetDuplicateRule (SetDuplicateRuleReq) returns (SetDuplicateRuleResp) {
    }
    // 消息转发
    rpc GetDuplicateRule (GetDuplicateRuleReq) returns (GetDuplicateRuleResp) {
    }
}

message PushOption {
    uint32 sequence = 1;                    // 废弃，该字段移到各个请求体的外层
    repeated uint32 terminal_type_list = 2;
    PushNotification.TerminalTypePolicy terminal_type_policy = 3;
    uint32 proxy_notification_type = 4;
    PushNotification.ProxyNotification.Policy proxy_notification_policy = 5;
    uint32 proxy_notification_expire_time = 6;
    bool sync = 7;
}

message SendChannelMsgReq {
    ga.channel.ChannelMsg msg = 1;
    string server_name = 2;                  // 调用方名称
    PushOption push_opts = 3;                // 推送的高级选项
    repeated uint32 skip_uid_list = 4;       // 跳过的uid列表
}
message SendChannelMsgResp {
    uint32 sequence = 1;
    string request_id = 2;                   // 推送系统返回的标识
}

message SendChannelBroadcastMsgReq {
    ga.channel.ChannelBroadcastMsg msg = 1;
    string server_name = 2;                  // 调用方名称
    PushOption push_opts = 3;                // 推送的高级选项
    repeated uint32 skip_uid_list = 4;       // 跳过的uid列表
}
message SendChannelBroadcastMsgResp {
    uint32 sequence = 1;
    string request_id = 2;                   // 推送系统返回的标识
}

message PushToUsersReq{
    ga.channel.ChannelBroadcastMsg msg = 1;
    string server_name = 2;                  // 调用方名称
    PushOption push_opts = 3;                // 推送的高级选项
    repeated uint32 uid_list = 4;            // 推送的uid列表
    uint32 sequence = 5;                     // reliable推送时填写的序列号
}
message PushToUsersRsp{
    uint32 sequence = 1;
    string request_id = 2;                   // 推送系统返回的标识
}

message PushToUserMapReq {
    ga.channel.ChannelBroadcastMsg msg = 1;
    string server_name = 2;                  // 调用方名称
    PushOption push_opts = 3;                // 推送的高级选项
    map<uint32, uint32> seq_map = 4;         // reliable推送时填写的序列号映射(uid->seqId)，若seqId为零则服务会自动生成
}
message PushToUserMapRsp {
    string request_id = 1;                   // 推送系统返回的标识
}

enum ERuleType{
    RULE_UNKNOW = 0;
    RULE_DUPLEX = 1;
    RULE_SIMPLEX = 2;
}

message DuplicateRule {
    CmdPolicy policy = 1;

    uint32 from_channel_id = 2; // 消息来源房间
    repeated uint32 to_channel_id_list = 3; // 消息副本发送房间, 数组为空则清除

    int64 expire_at = 4; //规则超时时间

    string rule_id = 5;
    uint32 rule_type = 6;  //参考ERuleType
}

message SetDuplicateRuleReq {
   repeated DuplicateRule rules = 1;
}
message SetDuplicateRuleResp {
}
message GetDuplicateRuleReq{
    repeated string ids = 1;
}
message GetDuplicateRuleResp{
    repeated DuplicateRule rules = 1;
}

service CmdPolicyService {
    rpc SetCmdPolicy (CmdPolicyInfo) returns (SetCmdPolicyInfoResp) {
    }
    rpc GetCmdPolicy (CmdPolicy) returns (GetCmdPolicyResp) {
    }
}

message CmdPolicy {
    string policy_group = 1;
    string policy_name = 2;
}

message CmdPolicyInfo {
    CmdPolicy policy = 1;
    repeated uint32 cmd_list = 2;
}

message SetCmdPolicyInfoResp {
}

message GetCmdPolicyResp {
    CmdPolicyInfo info = 1;
}