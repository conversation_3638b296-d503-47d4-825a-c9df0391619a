syntax = "proto3";

package lark_config_server;

option go_package = "golang.52tt.com/protocol/services/lark-config-server";

service LarkConfigServer {
    // 提交多维表格表更
    rpc CommitBitable(CommitBitableRequest) returns(CommitBitableResponse);

    // 获取多维表格
    rpc GetBitable(GetBitableRequest) returns(GetBitableResponse);
}

message Strings {
    repeated string strings = 1;
}

// 多维表格数据结构
message Bitable {
    message Field {
        enum Type {
            TYPE_UNSPECIFIED = 0;
            // 文本
            TYPE_TEXT = 1;
            // 数字
            TYPE_NUMBER = 2;
            // 单选
            TYPE_SINGLE_SELECT = 3;
            // 多选
            TYPE_MULTI_SELECT = 4;
            // 日期
            TYPE_DATE_TIME = 5;
            // 关联
            TYPE_LINK = 6;
            // 自动编号
            TYPE_AUTO_NUMBER = 8;
        }

        // 字段ID
        string id = 1;
        // 字段名
        string name = 2;
        // 字段类型
        Type type = 3;
    }

    message Row {
        message Link {
            repeated string row_ids = 1;
        }

        message MultiSelect {
            repeated string selected = 1;
        }

        message Column {
            oneof value {
                // 文本
                string text = 1;
                // 数字
                double number = 2;
                // 单选
                string single_select = 3;
                // 多选
                MultiSelect multi_select = 4;
                // 日期
                int64 date_time = 5;
                // 关联
                Link link = 6;
                // 自动编号
                string auto_number = 8;
            }
        }

        // 行记录ID
        string id = 1;
        // 列 key:字段名
        map<string, Column> columns = 2;
    }

    //                                   |-------- doc_id ---------|       |----- id -----|
    // https://q9jvw0u5f5.feishu.cn/base/MF8LbcY88aqC6kspvyNcmFb3nch?table=tblqo1ancVshh4lg

    // 表ID
    string id = 1;
    // 表所属文档ID
    string doc_id = 2;

    // 表字段
    repeated Field fields = 11;
    // 行记录
    repeated Row rows = 12;
}

message CommitBitableRequest {
    string namespace = 1;
    string key = 2;

    string doc_id = 6;
    string table_id = 7;
}

message CommitBitableResponse {
}

message GetBitableRequest {
    string namespace = 1;
    string key = 2;
}

message GetBitableResponse {
    Bitable bitable = 1;
}
