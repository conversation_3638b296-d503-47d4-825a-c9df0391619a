syntax = "proto3";

package golddiamond_logic;

message OperateInfoT {
  string month_income = 1;
  repeated string income_imgs = 2;
  string consume = 3;
  repeated string consume_imgs = 4;
  uint32 teamsize = 5;
  repeated string accompanys = 6;
  uint32 estimate_teams = 7;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ApplicationCooperateReq {
  uint32 guildid = 1;
  uint32 application_type = 2;             //1代表通过财富值的申请  2，代表外站考核的申请
  string platform_name = 3;                //站外平台名称
  string platform_content = 4;             //主要经营内容
  uint32 platform_id = 5;                  //门外房间id或主播id 弃用
  repeated string platform_certification = 6;       //经营截图证明
  string platform_id_str = 7; //门外房间id或主播id string
  uint32 apply_type = 8;
  uint32 ttguild = 9;
  uint32 platform_guild_type = 10;
  string platform_guildid = 11;
  string platform_guild_ownerId = 12;
  repeated string platform_guild_authimgs = 13;
  string establish_time = 14;
  OperateInfoT operate = 15;
  uint32 estimates = 16;
  string contact = 17;
}

message ApplicationCooperateRsp {
  bool is_app = 1; //是否申请过,true为申请过
  bool is_limit = 2; //是否受限
  int64 limit_end_time = 3; //到期时间
}

message CooperateReq {
  uint32 guildid = 1;
  uint32 applied = 2;
}

message CooperateRsp {
  uint32 applied = 1;
}

message ChannelListReq {
  uint32 guildid = 1;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message channelinfot {
  uint32 channelid = 1;
  uint32 displayid = 2;
  string channel_name = 3;
  string channel_view_id = 4;
}

message ChannelListRsp {
  repeated channelinfot channel_list = 1;
}

message PendingReq {
  uint32 guildid = 1;
}

message PendingRsp {
  int64 income_balance = 1;
  int64 members = 2;
  int64 consume = 3;
  int64 start_time = 4;
  int64 end_time = 5;
}

message PendingDetailReq {
  uint32 guildid = 1;
  uint32 query_type = 2;
  uint32 page = 3;
  uint32 page_num = 4;
}

message PendingDetail {
  uint32 uid = 1;
  uint32 channelid = 2;
  string name = 3;
  string channel_name = 4;
  int64 consume = 5;
  int64 income = 6;
  string channel_view_id = 7;
}

message PendingDetailRsp {
  repeated PendingDetail pending_list = 1;
  bool next_page = 2;
}

message YuyinPendingDetail {
  uint32 paid_uid = 1;
  uint32 anchor_id = 2;
  string paid_name = 3;
  string anchor_name = 4;
  int64 consume = 5;
  int64 income = 6;
  string paid_ttid = 7;
  string anchor_ttid = 8;
}

message YuyinPendingDetailRsp {
  repeated YuyinPendingDetail pending_list = 1;
  bool next_page = 2;
}

message TodayIncomeReq {
  uint32 guildid = 1;
}

message TodayIncomeInfo {
  uint32 channelid = 1;
  string channel_name = 2;
  int64 members = 3;
  int64 consume = 4;
  int64 income = 5;
  string channel_view_id = 6;
}

message TodayIncomeRsp {
  repeated TodayIncomeInfo channel_list = 1;
}

message YuyinTodayIncomeReq {
  uint32 guildid = 1;
}

message YuyinTodayIncomeInfo {
  string anchor_name = 1;
  int64 members = 3;
  int64 fee = 4;
  int64 present_fee = 5; // 送礼流水
  int64 knight_fee = 6; // 骑士团流水
  int64 income = 7;
  int64 consume = 8; // 同fee，兼容就接口
  int64 virtual_live_fee = 9; // 虚拟直播流水
}

message YuyinTodayIncomeRsp {
  repeated YuyinTodayIncomeInfo list = 1;
}

message MonthIncomeReq {
  uint32 guildid = 1;
  int64 month_time = 2;
}

message MonthIncomeInfo {
  int64 date_time = 1;
  int64 consume = 2;
  int64 members = 3;
  int64 income = 4;
  int64 fee = 5;
  int64 present_fee = 6; // 送礼流水
  int64 knight_fee = 7; // 骑士团流水
  int64 werewolf_fee = 8; // 狼人杀流水
  int64 interact_game_fee = 9; // 互动游戏流水
  int64 virtual_live_fee = 10; // 虚拟直播流水
  int64 esport_fee = 11; // 电竞流水
}

message MonthIncomeRsp {
  repeated MonthIncomeInfo month_income_list = 1;
}

message ConsumeSearchReq {
  uint32 guildid = 1;
  string account = 2;
  uint32 channelid = 3;
  int64 start_time = 4;
  int64 end_time = 5;
  uint32 page = 6;
  uint32 page_num = 7;
}

message ConsumeDetail {
  string account = 1;
  string name = 2;
  int64 consume = 3;
  int64 consume_time = 4;
  string channel_name = 5;
}

message ConsumeSearchRsp {
  bool next_page = 1;
  repeated ConsumeDetail income_list = 2;
}

message YuyinConsumeSearchReq {
  uint32 guildid = 1;
  string account = 2;
  uint32 anchor_uid = 3;
  int64 start_time = 4;
  int64 end_time = 5;
  uint32 page = 6;
  uint32 page_num = 7;
}

message YuyinConsumeDetail {
  string paid_account = 1;
  string paid_name = 2;
  int64 consume = 3;
  int64 consume_time = 4;
  string anchor_account = 5;
  string anchor_name = 6;
}

message YuyinConsumeSearchRsp {
  bool next_page = 1;
  repeated YuyinConsumeDetail income_list = 2;
}

message ChannelIncomeReq {
  uint32 guildid = 1;
  uint32 channelid = 2;
  uint32 range_type = 3;
  int64 start_time = 4;
  int64 end_time = 5;
  uint32 page = 6;
  uint32 page_num = 7;
}

message ChannelIncomeInfo {
  int64 consume_time = 1;
  int64 members = 2;
  int64 consume = 3;
  int64 income = 4;
}

message ChannelIncomeRsp {
  int64 consume = 1;
  int64 income = 2;
  bool next_page = 3;
  repeated ChannelIncomeInfo channel_income_list = 4;
}

message DayTrendInfo {
  uint32 day = 1;
  int64 income = 2;
}

message GuildChannelInfo {
  uint32 channelid = 1;
  uint32 displayid = 2;
  string channel_name = 3;
  int64 consume = 4;
  float month_tonow_qoq = 5; // 本月/上月环比
  string channel_view_id = 6;
}

message GuildInitInfoReq {
  uint32 guildid = 1;
}

message GuildInitInfoRsp {
  string guild_name = 1;
  uint32 guildid = 2;
  string account = 3;
  uint32 guild_type = 4;
  uint32 applied = 5;
  int64 server_time = 6;
  int64 today_income = 7;
  int64 yesterday_income = 8;
  int64 this_month_income = 9;
  int64 last_month_income = 10;
  repeated DayTrendInfo day_list = 11;
  repeated GuildChannelInfo channel_list = 12;
  double channel_cash = 13;
  double game_cash = 14;
  double live_cash = 15;
  float day_qoq = 16;
  float month_qoq = 17;
  uint32 wealth_value = 18;
  bool is_application = 19;
  float lastday_qoq = 20; // 今日/上月今日环比
  float month_tonow_qoq = 21; // 本月/上月环比
  int64 last_month_same_period = 22; // 上月同期值
  repeated DayTrendInfo last_day_list = 23; // 上月同期日期收益趋势
  double audio_basic_cash = 24;//直播基础佣金
  double audio_extra_cash = 25;//直播额外佣金
  int64 month_six_income = 26; //6个月收益
}

message GuildCheckReq {
  uint32 guildid = 1;
}

// 检查公开房
message GuildCheckRsp {
  bool has_amusement = 1;//是否有公開房
  uint32 multi_guild_type = 2;
  uint32 audio_guild_type = 3;
  uint32 multi_applied = 4;
  uint32 audio_applied = 5;
}


// 公会日流水环比(废弃)
message GuildConsumeDayQoqReq {
  uint32 guildid = 1;
  int64 day = 2;
}

message GuildConsumeDayQoqRsp {
  int64 stat_time = 1;
  int64 consume = 2;
  int64 compare_stat_time = 3;
  int64 compare_consume = 4;
  float qoq = 5;
}

// 公会日流水环比 新
message GuildDayComparisonReq {
  uint32 guildid = 1;
  int64 day = 2;
}
message DayComparisonInfo {
  uint64 stat_time = 1; // 查询日
  uint64 day_income = 2; // 当日收益
  uint64 last_month_same_period = 3; // 上月同日收益
  float qoq = 5;
}
message GuildDayComparisonResp {
  // 根据合作库类型返回
  DayComparisonInfo amuse_day_comparison = 1; // 娱乐房对比
  DayComparisonInfo yuyin_day_comparison = 2; // 直播放对比
  DayComparisonInfo esport_day_comparison = 3; // 电竞对比
}

message ConsumeRankItem {
  string alias = 1;
  string nick_name = 2;
  string account = 3;
  int64 consume = 4;
}

enum RangeType {
  DAY_RANGE_TYPE = 0;
  MONTH_RANGE_TYPE = 1;
}

// 公会推荐房消费Top10用户列表
message ConsumeRankReq {
  RangeType consume_type = 1;
  uint32 guildid = 2;
}

message ConsumeRankRsp {
  repeated ConsumeRankItem consume_rank_list = 1;
}

// 公会推荐房日流水消费数据
message RoomConsumeQoqReq {
  uint32 guildid = 1;
  uint32 channelid = 2;
  int64 day = 3;
}

message RoomConsumeQoqRsp {
  int64 stat_time = 1;
  int64 consume = 2;
  int64 compare_stat_time = 3;
  int64 compare_consume = 4;
  float qoq = 5;
  uint32 channelid = 6;
}

message GetGuildAnchorListReq{
  uint32 guildid = 1;
  uint32 page = 2;
  uint32 page_num = 3;
}

message GuildAnchorSimpleInfo{
  uint32 uid = 1;
  string name = 2;
  string ttid = 3;
}
message GetGuildAnchorListRsp{
  repeated GuildAnchorSimpleInfo list = 1;
  uint32 total = 2;
}

enum ErrorCode {
  ERROR_CODE_DEFAULT = 0;
  ERROR_CODE_PARR = 3001;
  ERROR_CODE_UNMARSHAL = 3002;
  ERROR_CODE_INVALID = 3003;
  ERROR_CODE_NETWORK = 3004;
}

message GetGuildIdReq {
}

message GetGuildIdRsp {
  uint32 guildid = 1;
  string account = 2;
  uint32 guild_short_id = 3;
}



//new ************************************ [exam]
/*是否限制*/
/*assessStatus多人合作库申请推荐房考核状态*/
message ExamUidGuildIdInfo{
  string uid = 1;
  uint32 guildid = 2;
}
message RecommendExamLimitCheckResp{
  uint32 is_limit = 1;
}

/*openChannel 公会厅*/
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message OpenChannelReq{
  string uid = 1;
  uint32 guildid = 2;
  uint32 page = 3;
  uint32 pageSize = 4;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message TagInfo{
  string tagbg = 1;
  string tagText = 2;
  uint32 tagId = 3;
  uint32 channelId = 4;
  string channelName = 5;
}

message OpenChannelResp{
  repeated TagInfo list = 1;
  uint32 total = 2;
}

/*multiLaymans 申请多人互动所有人列表*/
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message RecommendUsersReq{
  string uid = 1;
  uint32 guildid = 2;
  uint32 channelId = 3;
  uint32 page = 4;
  uint32 pageSize = 5;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UserInfo{
  uint32 uid = 1;
  string account = 2;
  string nickName = 3;
  string alias = 4; /*alias 用户靓号 若有显示靓号,否则返回ttid*/
}

/*searchMultiLayman 搜索多人互动公开厅考核所有人*/
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SearchMultiLayman{
  string uid = 1;
  uint32 guildid = 2;
  uint32 channelId = 3;
  string keyword = 4;
}
message RecommendUsersResp{
  repeated UserInfo list = 1;
  uint32 total = 2;
}

/*推荐房考核申请信息*/
/*multiAssess 申请多人互动推荐房考核*/
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message RecommendExamBaseInfo{
  string uid = 1;
  uint32 guildid = 2;
  uint32 purpose = 3;  /*ExamPurpose*/
  uint32 channelId = 4;
  uint32 currentTag = 5;
  uint32 examTag = 6;
  repeated uint32 examUids = 7;
  string password = 8;
}

/*获取考核标签*/
enum ExamTagType{
  TAG_DEFAULT = 0;
  TAG_RECOMMEND = 1;
  TAG_YUYIN = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ExamTagInfo{
  uint32 examId = 1;
  string tag = 2;
  uint32 limit_number = 3;
}

/*mutliRoomExam 多人互动合作库推荐房考核项目*/
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetExamTagListResp{
  repeated ExamTagInfo examList = 1;
}

/*语音直播*/
enum YuyinExamStatus{
  YE_DEFAULT = 0;
  YE_COMMITTED = 1;
  YE_REJECTED = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message YuyinExamApplicationInfo{
  string uid = 1;
  string account = 2;
  string nickName = 3;
  string alias = 4;
  uint32 examTag = 5;
  string link = 6;
  uint32 beginTime = 7;
  uint32 endTime = 8;
  string tagName = 9;
  uint32 assessStatus = 10;
}

/*会长处理主播考核申请*/
/*handleAssess*/
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message YuyinExamGuildOwnerOperationReq{
  string uid = 1;
  repeated string anchorUids = 2;
  uint32 guildid = 3;
  YuyinExamStatus yuyinStatus = 4;
}
message YuyinExamGuildOwnerOperationResp{
}

/*主播提交考核*/
message YuyinExamReq{
  YuyinExamApplicationInfo info = 1;
}
message YuyinExamResp{
}

/*主播查看提交的考核信息*/
message GetYuyinExamReq{
  string uid = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetYuyinExamResp{
  bool isLimit = 1;
  YuyinExamApplicationInfo info = 2;
}

/*会长查看主播提交的考核信息*/
/*assessList*/
message GetYuyinExamForGuildResp{
  repeated YuyinExamApplicationInfo list = 1;
  uint32 total = 2;
}

/*appraisalStatus 考核状态*/
message GetYuyinExamStatusReq{
  string uid = 1;
}
message GetYuyinExamStatusResp{
  int32 status = 1;
}

message TagsInfo{
  uint32 id = 1;
  string name = 2;
}
message SubmitInfo{
  uint32 tag = 1;
  string link = 2;
  string contact = 3;
}
/*appraisalInit 信息提交初始化*/
message YuyinExamInfo{
  repeated TagsInfo tags = 1;
  SubmitInfo submit = 2;
}

/*submitAppraisal 提交考核内容*/
message YuyinSubmit{
  string uid = 1;
  uint32 tag = 2;
  string link = 3;
  string contact = 4;
}

/*会长查看申请记录*/
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ApplicationRecordForGuildOwnerReq{
  string uid = 1;
  uint32 guildid = 2;
  string keyword = 3;
  uint32 page = 4;
  uint32 pageSize = 5;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ApplicationRecordInfo{
  uint32 submitTime = 1;
  repeated YuyinExamApplicationInfo items = 2;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ApplicationRecordForGuildOwnerResp{
  repeated ApplicationRecordInfo list = 1;
  uint32 nextPage = 2;
}

message CheckUidMainReq {
  string uid = 1;
}

message CheckUidMainResp {
  uint32 is_main = 1;
}

message AmuseExtraIncomeDetailReq {
  string uid = 1;
  uint32 guild_id = 2;
  uint32 year_month = 3; // 202201
}

message AmuseExtraIncomeDetailResp {
  uint32 year_month = 1; // 202201
  uint64 this_month_fee = 2; // 本月流水
  uint64 last_month_fee = 3; // 上月流水
  uint64 this_month_income = 4; // 本月收益（金钻）
  string this_month_income_cny = 5; // 本月收益（元）
  uint64 prepaid_money = 6; // 预付金额（分）
  string prepaid_money_cny = 7; // 预付金额（元）
  string remark = 8; // 备注
  string grow_rate = 9; // 增长率
  repeated ChannelItem channel_list = 10;

  message ChannelItem {
    uint32 channel_id = 1;
    uint32 channel_display_id = 2; // 房间display_id
    string channel_name = 3; // 房间名
    string channel_tag = 4; // 房间标签
    uint64 this_month_fee = 5; // 本月流水
    string settlement_rate = 6; // 结算比例
    uint64 this_month_income = 7; // 本月收益（金钻）
    uint32 guild_id = 8; // 公会id
    uint32 guild_display_id = 9; // 公会display_id
    uint64 last_month_fee = 10; // 上月流水
    string grow_rate = 11; // 增长率
    string channel_view_id = 12; // 房间view_id
  }
}

message InteractGameIncomeInfo {
  string anchor_name = 1; // 主播昵称
  int64 members = 3; // 消费人数
  int64 fee = 4; // 流水
  int64 income = 7; // 金钻收益
  int64 game_duration = 8; // 游戏时长
}

message InteractGameIncomeRsp {
  repeated InteractGameIncomeInfo list = 1;
}


message InteractGameExtraIncomeReq {
  uint32 guild_id = 1;
  uint32 month_time = 2;
}
message InteractGameExtraIncomeResp {
  uint64 game_month_total_fee = 1; // 本月互动游戏总流水 T豆
  uint64 game_month_extra_income = 2; // 本月互动游戏额外奖励 金钻
  repeated InteractGameExtraIncomeDetail list = 3;
}
message InteractGameExtraIncomeDetail {
  uint32 anchor_uid = 2;
  string anchor_ttid = 3;
  string anchor_account = 4;
  string anchor_nickname = 5;
  uint32 anchor_sex = 6; // 0-女，1-男

  uint64 game_month_fee = 7; // 本月互动游戏流水 T豆
  float game_income_rate = 8; // 收益比率
  uint64 game_income = 9; // 收益 金钻
}

message EsportIncomeInfo {
  string anchor_name = 1; // 主播昵称
  int64 fee = 4; // 流水
  int64 income = 7; // 金钻收益
}

message EsportIncomeRsp {
  repeated EsportIncomeInfo list = 1;
}

message EsportPendingRsp {
  int64 this_month_fee = 1; // 本月电竞订单流水（T豆）
  int64 this_month_income = 2; // 本月电竞收益（金钻）
  int64 start_time = 3;
  int64 end_time = 4;
}

message EsportCoachesMonthIncomeReq {
  uint32 guild_id = 1;
  uint32 page = 2;
  uint32 page_num = 3;
}
message EsportCoachesMonthIncomeInfo {
  uint32 uid = 1;
  string ttid = 2;
  string nickname = 3;
  uint64 fee = 4; // 本月电竞订单流水（T豆）
  uint64 income = 5; // 本月收益（金钻）
}
message EsportCoachesMonthIncomeRsp {
  repeated EsportCoachesMonthIncomeInfo list = 1;
  uint32 total = 2;
}

message EsportConsumeSearchReq {
  uint32 guildid = 1;
  uint32 coach_uid = 3; // 电竞指导uid
  int64 start_time = 4; // 查询开始时间
  int64 end_time = 5; // 查询结束时间
  uint32 page = 6;
  uint32 page_num = 7;
}
message EsportConsumeDetail {
  int64 date = 1; // 日期
  uint32 coach_uid = 2; // 电竞指导uid
  string coach_ttid = 3; // 电竞指导ttid
  string coach_nickname = 4; // 电竞指导昵称
  uint64 fee = 5; // 本月电竞订单流水（T豆）
  uint64 income = 6;// 本月收益（金钻）
}
message EsportConsumeSearchRsp {
  bool next_page = 1;
  repeated EsportConsumeDetail income_list = 2;
  uint64 total_fee = 3; // 总流水
  uint64 total_income = 4; // 总收益
  int64 start_time = 5; // 查询开始时间
  int64 end_time = 6; // 查询结束时间
}

/*esport skill*/


//获取新增技能审核信息请求
message BatchGetAuditSkillRequest {
  uint32 guild_id = 1;
  string ttid = 2;
  repeated uint32 audit_type = 3;     // 审核结果 see esport-role.proto EsportAuditType
  uint32 off_set = 4;
  uint32 limit = 5;

  // 查单个详情
  uint32 query_uid = 6;
  uint32 query_audit_source = 7;
  string query_audit_token = 8;

}
//获取新增技能审核信息请求
message BatchGetAuditSkillResponse {
  uint32 guild_id = 1;
  uint32 uid = 2;
  repeated uint32 audit_type = 3;     // 审核结果 see esport-role.proto EsportAuditType
  uint32 off_set = 4;
  uint32 limit = 5;
  repeated AuditSkillRecord list = 6; //审核记录
  uint32 total_count = 7;
}

message AuditSkillRecord {
  uint32 uid = 1;
  string account = 2;
  string nick_name = 3;
  string ttid = 4;
  string audit_token = 5;            //审核唯一记录
  uint32 audit_type = 6;             //审核结果 see esport-role.proto EsportAuditType
  uint32 apply_time = 7;             //申请时间
  repeated UserSkillInfo skill = 8;  //技能信息
  uint32 audit_source = 9;          //技能审核来源 see AuditSource
  string reason = 10;
  uint32 guild_id = 11;

  uint32 sex = 12;
  uint32 sign_time = 13; // 签约时间
  uint32 sign_expire_time = 14; // 签约到期时间
}
//用户游戏资料信息
message UserSkillInfo {
  uint32 game_id = 1;
  string game_name = 2;                      // 游戏名称
  string skill_evidence = 3;                 // 技能图
  string audio = 4;                          // 语音介绍
  uint32 audio_duration = 5;                 // 语音介绍时长(秒数)
  repeated SectionInfo section_list = 6;    // 技能详细信息
  string text_desc = 7;                      // 文字介绍

  string game_icon = 8;
}
message SectionInfo {
  string section_name = 1;        // 资料名称（段位信息、擅长位置、擅长英雄...）
  repeated string item_list = 2;  // 填写项
}

//设置用户技能审核结果
message SetUserSkillAuditTypeRequest {
  uint32 guild_id = 1;
  uint32 anchor_uid = 2;
  string audit_token = 3;
  uint32 audit_type = 4;        // 审核结果 see esport-role.proto EsportAuditType
  string reason = 5;            //原因
}

message SetUserSkillAuditTypeResponse {
}

/*esport skill*/

