syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channelbackground";

package channelbackground;


service ChannelBackground {

    // ============================================移动端调用======================================
    rpc GetCurrentChannelBackground(GetCurrentChannelBackgroundReq) returns (GetCurrentChannelBackgroundResp) {
    }
    rpc SetCurrentChannelBackground(SetCurrentChannelBackgroundReq) returns (SetCurrentChannelBackgroundResp) {
    }
    rpc CheckChannelBackgroundUpdate(CheckChannelBackgroundUpdateReq) returns (CheckChannelBackgroundUpdateResp) {
    }
    rpc GetChannelBackgroundList(GetChannelBackgroundListReq) returns (GetChannelBackgroundListResp) {
    }
    // 获取通用背景信息(非直播房背景)
    rpc GetChannelBackgroundConfById(GetChannelBackgroundConfByIdReq) returns (GetChannelBackgroundConfByIdResp) {
    }
    // ============================================移动端调用======================================

    // ============================================管理端调用======================================
    // 添加背景配置
    rpc AddChannelBackgroundConf(AddChannelBackgroundConfReq) returns (AddChannelBackgroundConfResp) {
    }
    // 删除背景配置
    rpc DelChannelBackgroundConf(DelChannelBackgroundConfReq) returns (DelChannelBackgroundConfResp) {
    }
    // 获取背景配置
    rpc GetChannelBackgroundConf(GetChannelBackgroundConfReq) returns (GetChannelBackgroundConfResp) {
    }
    // 添加背景配置V2
    rpc AddChannelBackgroundConfV2(AddChannelBackgroundConfReq) returns (AddChannelBackgroundConfResp) {
    }
    // 添加背景配置V2
    rpc UpdateChannelBackgroundConfV2(UpdateChannelBackgroundConfReq) returns (UpdateChannelBackgroundConfResp) {
    }
    // 删除背景配置V2
    rpc DelChannelBackgroundConfV2(DelChannelBackgroundConfReq) returns (DelChannelBackgroundConfResp) {
    }
    // 获取背景配置V2
    rpc GetChannelBackgroundConfV2(GetChannelBackgroundConfV2Req) returns (GetChannelBackgroundConfV2Resp) {
    }
    // 批量下发背景
    rpc BatchGiveChannelBg(BatchGiveChannelBgReq) returns (BatchGiveChannelBgResp) {
    }
    // 更新下发的背景信息
    rpc UpdateGivenChannelBg(UpdateGivenChannelBgReq) returns (UpdateGivenChannelBgResp) {
    }
    // 删除下发的背景信息
    rpc DeleteGivenChannelBg(DeleteGivenChannelBgReq) returns (DeleteGivenChannelBgResp) {
    }
    // 查询下发的背景信息
    rpc ListGivenChannelBg(ListGivenChannelBgReq) returns (ListGivenChannelBgResp) {
    }

    // 发放DIYLimit类型的背景，可叠加背景有效时间，不在运营后台产生发放记录
    rpc GiveDIYChannelBg(GiveDIYChannelBgReq) returns (GiveDIYChannelBgResp) {
    }

    // 更新开黑背景排序
    rpc UpdateDiyKHBackgroundSort(UpdateDiyKHBackgroundSortReq) returns (UpdateDiyKHBackgroundSortResp) {
    }

    // 获取热门推荐背景配置
    rpc GetRecommendBackground(GetRecommendBackgroundReq) returns (GetRecommendBackgroundResp) {
    }

    // 更新热门推荐背景配置
    rpc UpdateRecommendBackground(UpdateRecommendBackgroundReq) returns (UpdateRecommendBackgroundResp) {
    }

    // 删除热门推荐背景配置
    rpc DeleteRecommendBackground(DeleteRecommendBackgroundReq) returns (DeleteRecommendBackgroundResp) {
    }
    // ============================================管理端调用======================================

    // 语音直播房间背景相关

    // ============================================移动端调用======================================
    rpc GetChannelLiveBgList(GetChannelLiveBgListReq) returns (GetChannelLiveBgListResp) {
    }
    rpc GetChannelCurrLiveBg(GetChannelCurrLiveBgReq) returns (GetChannelCurrLiveBgResp) {
    }
    rpc SetChannelCurrLiveBg(SetChannelCurrLiveBgReq) returns (SetChannelCurrLiveBgResp) {
    }
    // ============================================移动端调用======================================

    // ============================================管理端调用======================================
    rpc GiveChannelLiveBg(GiveChannelLiveBgReq) returns (GiveChannelLiveBgResp) {
    }
    rpc AddLiveChannelBgConf(AddLiveChannelBgConfReq) returns (AddLiveChannelBgConfResp) {
    }
    rpc GetLiveChannelBgConf(GetLiveChannelBgConfReq) returns (GetLiveChannelBgConfResp) {
    }
    rpc DelLiveChannelBgConf(DelLiveChannelBgConfReq) returns (DelLiveChannelBgConfResp) {
    }
    // ============================================管理端调用======================================

}

enum BackgroundType {
    Forever = 0;
    TimeLimit = 1;
    Default = 2;
    LevelLimit = 3;
    DIYLimit = 4;       // 用户限时
}

enum BackgroundResourceType {
    UNKNOWN = 0; // 未知
    PHOTO = 1; // 图片
    VIDEO = 2; // 视频mp4
}

// 房间背景操作来源
enum BackgroundRequestSource {
    BackgroundRequestSource_Default = 0;
    BackgroundRequestSource_AppWeb = 1;    // 活动web
    BackgroundRequestSource_Backstage = 2; // 运营后台
}

//房间背景从运营后台发放给哪些房间类型
enum BackgroundBackstageLiveType {
    BackgroundBackstageLiveType_DEFAULT = 0; //不区分类型
    BackgroundBackstageLiveType_ONLY_UGC= 1; //仅发放给UGC
    BackgroundBackstageLiveType_REVENUE_UGC = 2; //营收UGC
    BackgroundBackstageLiveType_REVENUE_PGC = 3; //营收PGC
}

enum BackgroundSubType {
    BackgroundSubType_DIY_Default = 0; // 通用类型
    BackgroundSubType_DIY_KH = 1;      // 开黑, 仅开黑房使用
}

message ChannelBackgroundInfo {
    uint64 background_id = 1;
    string background_name = 2;
    BackgroundType background_type = 3;
    string background_url = 4;
    string md5_sum = 5;    // 图片MD5值
    int64 create_time = 6; // 背景图后台配置时间
    int64 start_time = 7;  // 限时背景开始时间
    int64 end_time = 8;    // 限时背景结束时间
    bool is_force = 9;     // 是否强制应用到所有房间（用于限时背景）

    repeated uint32 access_level_list = 10; // 可访问的房间等级列表逗号分隔，仅 background_type = LevelLimit 时有效
    BackgroundResourceType resource_type = 11; // 背景资源类型
    string resource_url = 12;          // 资源url
    uint32 background_level = 13;      // 背景等级，用于运营侧的透传记录，无业务意义，枚举见 channelbackground-api.proto -> BackgroundLevel
    BackgroundBackstageLiveType backstage_live_type = 14 ; //从后台可以发放给哪些房间

    BackgroundSubType sub_type = 15; // 背景分类，目前仅DIY背景有分类，对背景进一步分类: 通用背景(默认)，开黑
    repeated uint64 sub_type_relate_id = 16;  // 子类型关联id，如开黑背景关联的玩法scheme_id，sub_type = BackgroundSubType_DIY_KH 时，为空表示关联所有开黑玩法
    string description = 17;         // 背景介绍
    int64 desc_show_time_begin = 18; // 背景介绍展示开始时间
    int64 desc_show_time_end = 19;   // 背景介绍展示结束时间
    bool is_hide = 20;               // 是否隐藏，隐藏后只在用户持有背景列表展示，目前只有DIY开黑分类背景使用
}

message GetCurrentChannelBackgroundReq {
    uint32 channel_id = 1;
}

message GetCurrentChannelBackgroundResp {
    uint32 channel_id = 1;
    ChannelBackgroundInfo force_use_channel_background_info = 2; // 后台配置的需要强制应用到所有房间的背景
    ChannelBackgroundInfo user_use_channel_background_info = 3;  // 用户自己更换的房间背景(永久背景和限时背景，不包括默认背景)
}

message SetCurrentChannelBackgroundReq {
    uint32 channel_id = 1;
    uint64 background_id = 2;
    uint32 op_source = 3;  // 来源，BackgroundRequestSource
}

message SetCurrentChannelBackgroundResp {
    ChannelBackgroundInfo channel_background_info = 1;
}

message CheckChannelBackgroundUpdateReq {
    uint32 uid = 1;
}

message CheckChannelBackgroundUpdateResp {
    int64 version = 1;  // 版本号，给客户端用来判断后台永久背景配置是否有变化
}

message GetChannelBackgroundListReq {
    uint32 channel_id = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetChannelBackgroundListResp {
    repeated ChannelBackgroundInfo forever_channel_background_list = 1;
    repeated ChannelBackgroundInfo time_limit_channel_background_list = 2;

    int64 version = 3; // 版本号，给客户端用来判断后台背景配置是否有变化

    repeated ChannelBackgroundInfo level_limit_channel_background_list = 4;

    repeated ChannelBackgroundInfo DIY_limit_channel_background_list = 5;
}

message AddChannelBackgroundConfReq {
    ChannelBackgroundInfo background_info = 1;
}

message AddChannelBackgroundConfResp {
}

message UpdateChannelBackgroundConfReq {
    ChannelBackgroundInfo background_info = 1;
}

message UpdateChannelBackgroundConfResp {
}

message DelChannelBackgroundConfReq {
    uint64 background_id = 1;
}

message DelChannelBackgroundConfResp {
}


message GetChannelBackgroundConfReq {
}

message GetChannelBackgroundConfResp {
    repeated ChannelBackgroundInfo background_list = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetChannelBackgroundConfV2Req {
    uint32 page = 1; // 页号
    uint32 size = 2; // 页大小
    string name = 3; // 背景名称，模糊查询
    repeated BackgroundBackstageLiveType backstage_live_type_list = 4;
    // 是否仅查询开黑背景
    bool is_select_KH = 5;
}

message GetChannelBackgroundConfV2Resp {
    repeated ChannelBackgroundInfo background_list = 1;
    uint32 total = 2; // 总的数据量
    uint64 total_page = 3; // 总页数
}

message UpdateDiyKHBackgroundSortReq {
    // 背景id列表, 按照列表顺序排序
    repeated uint64 background_id_list = 1;
}

message UpdateDiyKHBackgroundSortResp {
}

// 推荐背景可见类型
enum RecommendBgVisibleType {
    // 默认，全部可见
    RecommendBgVisibleType_Default = 0;
    // 人群包可见
    RecommendBgVisibleType_SpecPacket = 1;
}

// 热门房间背景推荐配置
message RecommendBackgroundInfo {
    // 背景id
    ChannelBackgroundInfo background_info = 1;
    // 排序key, 越小越靠前
    uint32 sort_key = 2;
    // 推荐背景可见类型
    RecommendBgVisibleType visible_type = 3;
    // 推荐背景可见类型描述，可见类型为人群包是人群包ID
    string visible_value = 4;
    // 修改时间
    int64 modify_time = 5;
}

message GetRecommendBackgroundReq {
}

message GetRecommendBackgroundResp {
    // 热门房间背景推荐列表
    repeated RecommendBackgroundInfo recommend_background_list = 1;
}

message UpdateRecommendBackgroundReq {
    // 热门房间背景推荐列表
    repeated RecommendBackgroundInfo recommend_background_list = 1;
}

message UpdateRecommendBackgroundResp {
}

message DeleteRecommendBackgroundReq {
    // 背景id
    repeated uint64 background_id_list = 1;
}

message DeleteRecommendBackgroundResp {
}

message GetChannelBackgroundConfByIdReq {
    // 背景id
    uint64 background_id = 1;
}

message GetChannelBackgroundConfByIdResp {
    // 背景详情
    ChannelBackgroundInfo background_info = 1;
}

// 语音直播房房间背景
message LiveChannelBackgroundInfo {
    enum BackgroundType {
        Default = 0;
        TimeLimit = 1;
        Forever = 2;
    }

    uint64 background_id = 1;
    string background_name = 2;
    uint32 background_type = 3;
    string static_picture = 4;          // 背景静态图
    string mic_picture_url = 5;         // 麦位背景图url
    string mic_dynamic_url = 6;         // 麦位动效资源url
    string mic_dynamic_md5sum = 7;      // 麦位动效资源MD5值
    string background_video_url = 8;    // 背景视频url
    string background_video_md5sum = 9; // 背景视频MD5值
    int64 obtain_time = 10;             // 背景获得时间
    int64 end_time = 11;                // 背景到期时间
    string background_thumbnail_url = 12;       // 背景缩略图
    string voice_mic_dynamic_url = 13;          // 音频麦位动效资源url
    string voice_mic_dynamic_md5sum = 14;       // 音频麦位动效资源MD5值
    string low_version_bg_video_url = 15;       // 低版本手机背景视频url
    string low_version_bg_video_md5sum = 16;    // 低版本手机背景视频MD5值
}

message GetChannelLiveBgListReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message GetChannelLiveBgListResp {
    repeated LiveChannelBackgroundInfo background_list = 1;
}

message GiveChannelLiveBgReq {
    uint32 channel_id = 1;
    uint64 background_id = 2;
    int64 incr_expire_sec = 3;        // 增加的过期时间（秒）,可小于0
    uint32 target_uid = 4;            // 目标uid
    bool without_default_tt_push = 5; // 不需要默认的tt助手推送
    uint32 op_source = 6;             // 来源，BackgroundRequestSource
}

message GiveChannelLiveBgResp {

}

message GiveDIYChannelBgReq {
    uint32 channel_id = 1;
    uint64 background_id = 2;
    int64 incr_expire_sec = 3;        // 增加的过期时间（秒）,可小于0
    uint32 target_uid = 4;            // 目标uid
    bool without_default_tt_push = 5; // 不需要默认的tt助手推送
    uint32 op_source = 6;             // 来源，BackgroundRequestSource（透传记录）
    string op_source_desc = 7;        // 来源描述，如运营后台操作填操作人（透传记录）
}

message GiveDIYChannelBgResp {
    bool is_success = 1;
}

enum ChannelType {
    CHANNEL_TYPE_UNKNOWN = 0;
    MULTIPLAYER = 1;
    DATING = 2;
}

message ChannelBgInfo {
    uint64 background_id = 1; // 背景id
    repeated uint32 channel_id = 2; // 房间 id
    int64 expire_sec = 3;  // 过期时长（秒），无论是否已有有效时长，均覆盖
    ChannelType channel_type = 4; // 房间类型
    int64 incr_expire_sec = 5; // 在已有有效时长基础上,增加的过期时间（秒）,可小于0
}

message BatchGiveChannelBgReq {
    ChannelBgInfo infos = 1;
    uint32 op_source = 2;      // 来源，BackgroundRequestSource（透传记录）
    string op_source_desc = 3; // 来源描述，如运营后台操作填操作人（透传记录）
}

message BatchGiveChannelBgResp {
    repeated uint32 fail_list = 1; // 发放失败的房间display id
}

message UpdateGivenChannelBgReq {
    int64 id = 1;              // 下发记录id
    uint32 channel_id = 2;     // 房间id
    uint64 background_id = 3;  // 背景id
    int64 expire_sec = 4;      // 过期时长（秒）
    uint32 op_source = 5;      // 来源，BackgroundRequestSource（透传记录）
    string op_source_desc = 6; // 来源描述，如运营后台操作填操作人（透传记录）
}

message UpdateGivenChannelBgResp {

}

message DeleteGivenChannelBgReq {
    repeated int64 id = 1;     // 下发记录id
    uint32 op_source = 5;      // 来源，BackgroundRequestSource（透传记录）
    string op_source_desc = 6; // 来源描述，如运营后台操作填操作人（透传记录）
}

message DeleteGivenChannelBgResp {

}

message GivenChannelGgInfo {
    int64 id = 1; // 下发记录id
    uint32 channel_id = 2; // 房间id
    uint64 background_id = 4; // 背景id
    string static_picture = 5; // 背景静态图
    string background_name = 6; // 背景名称
    uint64 create_timestamp = 7; // 创建时间，时间戳，秒
    uint64 expire_time = 8 ; // 剩余有效时间，秒
    uint64 update_time = 9; // 更新时间，时间戳，秒
    ChannelType channel_type = 10; // 房间类型
    BackgroundResourceType background_resource_type = 11;
    uint32 op_source = 12;      // 来源，BackgroundRequestSource（透传记录）
    string op_source_desc = 13; // 来源描述，如运营后台操作填操作人（透传记录）
    uint32 background_level = 14; // 背景等级，see channelbackground-api.proto -> BackgroundLevel
}

message ListGivenChannelBgReq {
    uint32 page = 1; // 页号
    uint32 size = 2; // 页大小
    uint32 channel_id = 3; // 房间id
    
    message Int64Wrapper {
        int64 val_value = 1;
    }
    Int64Wrapper last_id = 4;   //上一次查询返回id，对应GivenChannelGgInfo.id
    uint32 ga_channel_type = 5; //see channel_.proto -> ChannelType
}

message ListGivenChannelBgResp {
    repeated GivenChannelGgInfo data = 1; // 下发记录
    uint32 total = 2; // 总的数据量
    uint64 total_page = 3; // 总页数
    
    int64 next_id = 4;  // 下一页查询id
}

message GetChannelCurrLiveBgReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message GetChannelCurrLiveBgResp {
    uint32 channel_id = 1;
    LiveChannelBackgroundInfo curr_background_info = 2;
    LiveChannelBackgroundInfo default_background_info = 3;
}

message SetChannelCurrLiveBgReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint64 background_id = 3;
    uint32 op_source = 4; // 来源，BackgroundRequestSource
}

message SetChannelCurrLiveBgResp {
    LiveChannelBackgroundInfo curr_background_info = 1;
}

message AddLiveChannelBgConfReq {
    LiveChannelBackgroundInfo background_info = 1;
}

message AddLiveChannelBgConfResp {
}

message DelLiveChannelBgConfReq {
    uint64 background_id = 1;
}

message DelLiveChannelBgConfResp {
}


message GetLiveChannelBgConfReq {
}

message GetLiveChannelBgConfResp {
    repeated LiveChannelBackgroundInfo background_list = 1;
}

