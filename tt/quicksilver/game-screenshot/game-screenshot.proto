syntax = "proto3";

package game_screenshot;
option go_package = "golang.52tt.com/protocol/services/game-screenshot";

service GameScreenshot {
    rpc GetGameScreenshotSummary (GetGameScreenshotSummaryReq) returns (GetGameScreenshotSummaryResp) {}

    rpc GetGameScreenshotSetting (GetGameScreenshotSettingReq) returns (GetGameScreenshotSettingResp) {}

    rpc UpdateGameScreenshotSetting (UpdateGameScreenshotSettingReq) returns (UpdateGameScreenshotSettingResp) {}

    rpc GetDiyGameScreenshot (GetDiyGameScreenshotReq) returns (GetDiyGameScreenshotResp) {}

    rpc SetDiyGameScreenshotFinish (SetDiyGameScreenshotFinishReq) returns (SetDiyGameScreenshotFinishResp) {}

    ////////////////////////////////////////////

    rpc GetOfficialGameScreenshotList (GetOfficialGameScreenshotListReq) returns (GetOfficialGameScreenshotListResp) {}

    rpc SetOfficialGameScreenshot (SetOfficialGameScreenshotReq) returns (SetOfficialGameScreenshotResp) {}

    rpc DelOfficialGameScreenshot (DelOfficialGameScreenshotReq) returns (DelOfficialGameScreenshotResp) {}

    rpc GetGameScreenshotGuideList (GetGameScreenshotGuideListReq) returns (GetGameScreenshotGuideListResp) {}

    rpc SetGameScreenshotGuide (SetGameScreenshotGuideReq) returns (SetGameScreenshotGuideResp) {}

    rpc DelGameScreenshotGuide (DelGameScreenshotGuideReq) returns (DelGameScreenshotGuideResp) {}

    rpc UpdateDIYGameScreenshotStatus (UpdateDIYGameScreenshotStatusReq) returns (UpdateDIYGameScreenshotStatusResp) {}
}

////////////////////////////////////////////

//开关状态
enum GameScreenshotSwitchStatus {
    GMAE_UNSET = 0; //该游戏未设置
    UNSET = 1; //该房间未设置
    OPEN = 2; //开启
    CLOSE = 3; //关闭
}
//操作类型
enum GameScreenshotAction {
    MOD = 0; //添加或修改
    DEL = 1; //删除
}
//截图类型
enum GameScreenshotType {
    OFFICIAL = 0;
    DIY = 1;
}
//审核状态
enum GameScreenshotAuditStatus {
    INIT = 0; //初始化，待上传
    REVIEWING = 3; //审核中
    CENSOR_FAIL = 4; //发起审核失败
    NORMAL = 5; //正常
    BANED = 6; //被禁
    DELETED = 7; //已删除
}

enum AdminOperation {
    ADD = 0; //添加
    UPDATE = 1; //更新
}

//官方截图
message OfficialGameScreenshot {
    string path = 1; //图片的完整url
    string name = 2;
}
//自定义截图
message DiyGameScreenshot {
    string path = 1; //图片的完整url
    string name = 2;
    uint32 pos = 3; //位置，填充0～9
    GameScreenshotAuditStatus status = 4;
}
//开黑截图设置
message GameScreenshotSetting {
    GameScreenshotType type = 1;
    string path = 2; //图片的完整url
    string name = 3;
}

////////////////////////////////////////////

//房间首页获取开黑截图摘要
message GetGameScreenshotSummaryReq {
    uint32 channel_id = 1;
    uint32 tab_id = 2;
}
message GetGameScreenshotSummaryResp {
    GameScreenshotSwitchStatus switch_status = 1; //开关状态
    bool has_settings = 2; //是否有截图设置
}

//获取开黑截图设置
message GetGameScreenshotSettingReq {
    uint32 channel_id = 1;
    uint32 tab_id = 2;
}
message GetGameScreenshotSettingResp {
    GameScreenshotSwitchStatus switch_status = 1; //开关状态
    repeated GameScreenshotSetting settings = 2; //截图设置
}

//更新开黑截图设置
message UpdateGameScreenshotSettingReq {
    uint32 channel_id = 1;
    uint32 tab_id = 2;
    GameScreenshotSwitchStatus switch_status = 3; //开关状态
    repeated GameScreenshotSetting settings = 4; //截图设置
}
message UpdateGameScreenshotSettingResp {
}

//获取自定义截图
message GetDiyGameScreenshotReq {
    uint32 uid = 1;
    uint32 tab_id = 2;
}
message GetDiyGameScreenshotResp {
    repeated DiyGameScreenshot screenshots = 1;
}

//设置自定义截图完成
message SetFinishReqInfo {
    GameScreenshotAction action = 1; //是修改还是删除
    uint32 pos = 2; //位置，填充0～9
    string path = 3; //图片的完整url
    string name = 4;
}
message SetDiyGameScreenshotFinishReq {
    uint32 uid = 1;
    uint32 tab_id = 2;
    repeated SetFinishReqInfo set_list = 3;
}
message SetDiyGameScreenshotFinishResp {
}

////////////////////////////////////////////

message OfficialGameScreenshotInfo {
    uint32 tab_id = 1;
    bool is_open = 2; //是否开启
    repeated OfficialGameScreenshot screenshots = 3;
    string update_user = 4;
    uint64 update_time = 5; //时间戳，单位秒
}
//获取官方开黑截图列表
message GetOfficialGameScreenshotListReq {
}
message GetOfficialGameScreenshotListResp {
    repeated OfficialGameScreenshotInfo game_list = 1;
}

//设置官方开黑截图
message SetOfficialGameScreenshotReq {
    uint32 tab_id = 1;
    bool is_open = 2; //是否开启
    repeated OfficialGameScreenshot screenshots = 3;
    string update_user = 4;
    AdminOperation operation = 5;
}
message SetOfficialGameScreenshotResp {
}

//删除官方开黑截图
message DelOfficialGameScreenshotReq {
    uint32 tab_id = 1;
    string update_user = 2;
}
message DelOfficialGameScreenshotResp {
}

message GameScreenshotGuideInfo {
    uint32 tab_id = 1;
    string guide_url = 2;
    string update_user = 3;
    uint64 update_time = 4; //时间戳，单位秒
    uint32 u_game_id = 5; //游戏id, 废弃
    uint32 game_card_id = 6; //游戏卡id（对应游戏卡配置的）
}
//获取开黑引导列表
message GetGameScreenshotGuideListReq {
    uint32 tab_id = 1;
}
message GetGameScreenshotGuideListResp {
    repeated GameScreenshotGuideInfo game_list = 1;
}

//设置开黑引导
message SetGameScreenshotGuideReq {
    uint32 tab_id = 1;
    string guide_url = 2;
    string update_user = 3;
    AdminOperation operation = 4;
    uint32 u_game_id = 5; //游戏id 废弃字段
    uint32 game_card_id = 6; //游戏卡id（对应游戏卡配置的）
}
message SetGameScreenshotGuideResp {
}

//删除开黑引导
message DelGameScreenshotGuideReq {
    uint32 tab_id = 1;
    string update_user = 2;
}
message DelGameScreenshotGuideResp {
}

message UpdateDIYGameScreenshotStatusReq {
    uint32 uid = 1;
    uint32 tab_id = 2;
    string url = 3;
    GameScreenshotAuditStatus status = 4;
    uint32 audit_type = 5; // 0 机审， 1人审
    string tip_reason = 6; //记录操作来源，查问题
}

message UpdateDIYGameScreenshotStatusResp {
}