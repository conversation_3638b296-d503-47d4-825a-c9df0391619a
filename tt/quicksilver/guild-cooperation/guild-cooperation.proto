syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/guild-cooperation";
package guild_cooperation;

// 公会入驻（合作库）
service GuildCooperation {
  // 获取合作信息
  rpc GetCooperationInfo(GetCooperationInfoReq) returns (GetCooperationInfoResp);
  // 通过手机号获取合作信息
  rpc GetCooperationInfoByPhone(GetCooperationInfoByPhoneReq) returns (GetCooperationInfoByPhoneResp);
  // 提交申请资料
  rpc SubmitApplication(SubmitApplicationReq) returns (SubmitApplicationResp);
  // 创建公会
  rpc CreateGuild(CreateGuildReq) returns (CreateGuildResp);
  // 申请合作
  rpc ApplyCooperation(ApplyCooperationReq) returns (ApplyCooperationResp);

  // 获取入驻申请列表
  rpc GetApplications(GetApplicationsReq) returns (GetApplicationsResp);
  // 查看申请资料
  rpc GetApplicationInfo(GetApplicationInfoReq) returns (GetApplicationInfoResp);
  // 通过申请
  rpc ApproveApplication(ApproveApplicationReq) returns (ApproveApplicationResp);
  // 拒绝申请
  rpc RejectApplication(RejectApplicationReq) returns (RejectApplicationResp);
  // 获取公会信息
  rpc GetGuildInfo(GetGuildInfoReq) returns (GetGuildInfoResp);
  // 获取操作历史
  rpc GetOperationHistory(GetOperationHistoryReq) returns (GetOperationHistoryResp);
  // 获取会长的合作库申请资料
  rpc GetApplicationInfoByUid(GetApplicationInfoByUidReq) returns (GetApplicationInfoByUidResp);

  // 创建/编辑文案
  rpc EditNotification(EditNotificationReq) returns (EditNotificationResp);
  // 获取通知文案列表
  rpc GetNotificationList(GetNotificationListReq) returns (GetNotificationListResp);
  // 删除文案
  rpc DeleteNotification(DeleteNotificationReq) returns (DeleteNotificationResp);

  // 创建申请限制
  rpc CreateApplyLimit(CreateApplyLimitReq) returns (CreateApplyLimitResp);
  // 获取申请限制列表
  rpc GetApplyLimitList(GetApplyLimitListReq) returns (GetApplyLimitListResp);
  // 解除申请限制
  rpc DeleteApplyLimit(DeleteApplyLimitReq) returns (DeleteApplyLimitResp);

  // 获取合作库公会列表
  rpc GetCooperationGuildList(GetCooperationGuildListReq) returns (GetCooperationGuildListResp);
  // 手动添加合作库公会（管理后台接口）
  rpc AddCooperationGuild(AddCooperationGuildReq) returns (AddCooperationGuildResp);
  // 批量手动添加合作库公会（管理后台接口）
  rpc BatchAddCooperationGuild(BatchAddCooperationGuildReq) returns (BatchAddCooperationGuildResp);
  // 移除合作库公会
  rpc DelCooperationGuild(DelCooperationGuildReq) returns (DelCooperationGuildResp);
  // 获取合作库操作记录
  rpc GetCooperationOptList(GetCooperationOptListReq) returns (GetCooperationOptListResp);

  // --------------------------------- 以下接口提供服务端使用 ---------------------------------------
  // 批量获取所有合作库公会ID
  rpc GetCooperationGuildIds(GetCooperationGuildIdsReq) returns (GetCooperationGuildIdsResp);
  // 获取公会合作信息
  rpc GetGuildCooperationInfos(GetGuildCooperationInfosReq) returns (GetGuildCooperationInfosResp);
  // 获取公会合作历史信息
  rpc GetGuildCooperationHistory(GetGuildCooperationHistoryReq) returns (GetGuildCooperationHistoryResp);
  // 获取公会指定时间的合作状态
  rpc GetGuildCooperationWithTime(GetGuildCooperationWithTimeReq) returns (GetGuildCooperationWithTimeResp);
  // 获取公会历史的合作时间段
  rpc GetGuildCooperationHistoryPeriod(GetGuildCooperationHistoryPeriodReq) returns (GetGuildCooperationHistoryPeriodResp);
  // 获取所有无佣金公会信息
  rpc GetAllNoBonusGuild(GetAllNoBonusGuildReq) returns (GetAllNoBonusGuildResp);
  // 设置无佣金公会
  rpc SetNoBonusGuild(SetNoBonusGuildReq) returns (SetNoBonusGuildResp);
}

enum CooperationType {
  CTypeInvalid = 0;
  CTypeAmuse = 4; // 娱乐房
  CTypeYuyin = 7; // 语音房
  CTypeESports = 8; // 电竞
}

// 申请步骤
enum ApplicationStep {
  StepWaitSubmit = 0; // 待提交资料
  StepSubmit = 3; // 提交资料
  StepCommunicate = 5; // 微信沟通
  StepCooperation = 7; // 申请合作
}

// 申请操作
enum ApplicationOperation {
  OperationInvalid = 0;
  OperationApproval = 1; // 通过
  OperationReject = 3; // 拒绝
}

// 请求查询状态
enum ReqApplyStatus {
  Invalid = 0;
  Submitted = 1; // 已提交
  Rejected = 3; // 已拒绝
  Communicating = 5; // 沟通中
  WaitApply = 6; // 待申请合作
  Applied = 7; // 已申请合作
  Cooperating = 10; // 已加入合作库
}

// 合作库操作类型
enum CoopOptType {
  InvalidType = 0;
  Add = 1; // 添加
  Del = 2; // 删除
}

// 申请来源
enum SourcePlatform {
  PLATFORM_INVALID = 0;
  PLATFORM_APP = 1; // 端内
  PLATFORM_WEB = 2; // 官网
  PLATFORM_OPERATOR = 3; // 运营手动（仅陪玩）
}

enum MsgType {
  MSG_TYPE_INVALID = 0;
  MSG_TYPE_PASS = 1; // 通过文案
  MSG_TYPE_REJECT = 2; // 拒绝文案
}

message CooperationInfo {
  CooperationType cooperation_type = 1; // 合作类型
  bool is_cooperation = 2; // 是否已加入合作库
  ApplicationStep application_step = 3; // 当前步骤
  ApplicationOperation application_operation = 4; // 当前操作
  string application_id = 5; // 申请ID
  uint32 reapply_time = 6; // 重新申请时间
}
message GetCooperationInfoReq {
  uint32 uid = 1;
}
message GetCooperationInfoResp {
  repeated CooperationInfo cooperation_info = 1;
  bool esport_entrance_switch = 2; // 电竞入口开关
}

message GetCooperationInfoByPhoneReq {
  string phone = 1;
}
message GetCooperationInfoByPhoneResp {
  repeated CooperationInfo cooperation_info = 1;
  bool esport_entrance_switch = 2; // 电竞入口开关
}


// 提交申请资料
message SubmitApplicationReq {
  uint32 uid = 1;
  string external_platform = 2; // 站外平台
  string wechat = 3; // 微信号
  string corporation = 4; // 公司名称
  string legal_person = 5; // 法人
  string corporation_code = 6; // 公司地址
  string bank_account = 7; // 银行账号
  repeated string business_license_photos = 8; // 营业执照url
  repeated string bill_history_photos = 9; // 收款记录url
  CooperationType cooperation_type = 10; // 合作类型（二期废弃）
  repeated CooperationType cooperation_types = 11; // 合作类型s
  string phone = 12; // 手机号
  SourcePlatform source_platform = 13; // 来源
  string create_guild_name = 14; // 公会名称（仅运营手动创建陪玩时生效）
  string operator = 15; // 操作人
}
message SubmitApplicationResp {
  string application_id = 1; // 废弃
  repeated string application_ids = 2; // 支持多选类型
}

// 创建公会
message CreateGuildReq {
  uint32 uid = 1;
  string guild_name = 2; // 公会名称
}
message CreateGuildResp {
  uint32 guild_id = 1;
}

message ApplyCooperationReq {
  uint32 uid = 1;
  uint32 guild_id = 2; // 公会ID
  string application_id = 3; // 申请ID
}
message ApplyCooperationResp {

}

message CooperationApplication {
  string application_id = 1; // 申请编号
  uint32 uid = 2;
  string ttid = 3;
  CooperationType cooperation_type = 4; // 合作类型
  string wechat_id = 5; // 微信号
  uint32 create_time = 6; // 申请时间
  string operator = 7; // 操作人
  uint32 update_time = 8;
  string legal_person = 9; // 法人
  string corporation = 10; // 公司名称
  string corporation_code = 11; // 公司信息码
  string bank_account = 12; // 对公帐户
  repeated string business_license_photos = 13; // 营业执照url
  repeated string bill_history_photos = 14; // 收款记录url
  string reason = 16; // 拒绝原因
  string external_platform = 17; // 站外平台
  ReqApplyStatus current_status = 15; // 当前状态
  ApplicationStep step = 18; // 申请当前步骤
  ApplicationOperation operation = 19; // 申请当前操作
  string current_status_desc = 20; // 当前状态描述
  string approve_desc = 21; // 审批描述
  uint32 reapply_time = 22; // 重新申请时间
  string phone = 23; // 手机号
  SourcePlatform source_platform = 24; // 来源
}

message GetApplicationsReq {
  ReqApplyStatus req_apply_status = 1; // 申请状态
  uint32 offset = 3;
  uint32 limit = 4;
  string ttid = 5; // 查询TTid
  string wechat = 6; // 查询Wechat
  CooperationType cooperation_type = 7; // 合作类型
}
message GetApplicationsResp {
  repeated CooperationApplication applications = 1;
  uint32 total = 2;
}

message GetApplicationInfoReq {
  string application_id = 1;
  uint32 uid = 2;
}
message GetApplicationInfoResp {
  CooperationApplication application = 1;
}

message ApproveApplicationReq {
  string application_id = 1;
  string operator = 2;
  ApplicationStep step = 3; // 申请步骤（避免状态变化）
  uint32 notification_id = 4; // 通知文案ID
}
message ApproveApplicationResp {

}

message RejectApplicationReq {
  string application_id = 1;
  string operator = 2;
  ApplicationStep step = 3;
  uint32 notification_id = 4; // 通知文案ID
}
message RejectApplicationResp {

}

message EditNotificationReq {
  uint32 notification_id = 2;
  string title = 3;
  string content = 4;
  string operator = 5;
  string usage = 6;
  MsgType msg_type = 7;
}
message EditNotificationResp {

}

message Notification {
  uint32 notification_id = 1;
  string title = 2;
  string content = 3;
  uint32 create_time = 4;
  uint32 update_time = 5;
  string operator = 6;
  string usage = 7;
  MsgType msg_type = 8;
}
message GetNotificationListReq {
  MsgType msg_type = 1; // 非必填，不填展示全部
}
message GetNotificationListResp {
  repeated Notification notifications = 1;
}

message DeleteNotificationReq {
  uint32 notification_id = 2;
}
message DeleteNotificationResp {

}

message CreateApplyLimitReq {
  string ttid = 1;
  uint32 start_time = 2;
  uint32 end_time = 3;
  string operator = 4;
  uint32 id = 5;
}
message CreateApplyLimitResp {

}

message ApplyLimit {
  uint32 uid = 1;
  string ttid = 2;
  uint32 start_time = 3;
  uint32 end_time = 4;
  string operator = 5;
  uint32 create_time = 6;
  uint32 update_time = 7;
  uint32 id = 8;
}
message GetApplyLimitListReq {
  uint32 offset = 1;
  uint32 limit = 2;
  string ttid = 3;
}
message GetApplyLimitListResp {
  repeated ApplyLimit apply_limits = 1;
  uint32 total = 2;
}

message DeleteApplyLimitReq {
  uint32 uid = 1;
}
message DeleteApplyLimitResp {

}

// 获取公会信息
message GetGuildInfoReq {
  uint32 uid = 1;
}
message GetGuildInfoResp {
  uint32 guild_id = 1;
  uint32 guild_owner = 2;
  string guild_name = 3;
  bool is_real_name = 4; // 完成实名认证
}

message OperateHistory {
  string application_id = 1; // 申请编号
  string operator = 2; // 操作人
  uint32 create_time = 3; // 操作时间
  CooperationType cooperation_type = 4; // 合作类型
  uint32 uid = 5;
  string ttid = 6;
  ApplicationStep step = 8; // 申请步骤
  ApplicationOperation operation = 9; // 申请操作
}
message GetOperationHistoryReq {
  uint32 offset = 1;
  uint32 limit = 2;
  string ttid = 3; // 查询TTid
  string operator = 4; // 查询操作人
}
message GetOperationHistoryResp {
  repeated OperateHistory operate_histories = 1;
  uint32 total = 2;
}

message GetApplicationInfoByUidReq {
  uint32 uid = 1;
}
message GetApplicationInfoByUidResp {
  CooperationApplication application = 1;
}

message CooperationGuildInfo {
  uint32 guild_id = 1; // 公会ID
  uint32 guild_display_id = 2; // 公会靓号
  string guild_name = 3; // 公会名称
  uint32 uid = 4; // 会长ID
  string ttid = 5; // 会长TTID
  string nick_name = 6; // 会长昵称
  string operator = 7; // 操作人
  int64 join_time = 8; // 加入时间
  CooperationType cooperation_type = 9; // 合作类型
}

message GetCooperationGuildListReq {
  uint32 offset = 1;
  uint32 limit = 2;
  uint32 guild_id = 3; // 根据公会ID进行查询
  uint32 uid = 4; // 根据会长Uid查询
  string operator = 5; // 根据操作人查询
  CooperationType coop_type = 6; // 根据合作库类型进行查询
}
message GetCooperationGuildListResp {
  repeated CooperationGuildInfo info_list = 1;
  uint32 total = 2;
}

message AddCooperationGuildReq {
  uint32 guild_id = 1; // 需要添加的公会ID
  CooperationType coop_type = 2; // 要添加的合作库类型
  string operator = 3; // 操作人
}
message AddCooperationGuildResp {}

message BatchAddCooperationGuildReq {
  repeated uint32 guild_list = 1; // 需要批量添加的公会ID
  CooperationType coop_type = 2; // 要添加的合作库类型
  string operator = 3; // 操作人
}
message BatchAddCooperationGuildResp {
  repeated uint32 succeed_list = 1;
}

message DelCooperationGuildReq {
  uint32 guild_id = 1; // 需要移除的公会ID
  CooperationType coop_type = 2; // 要删除的合作库类型
  string operator = 3; // 操作人
}
message DelCooperationGuildResp {}

message CooperationOptInfo {
  uint32 guild_id = 1; // 公会ID
  uint32 guild_display_id = 2; // 公会靓号
  string guild_name = 3; // 公会名称
  uint32 uid = 4; // 会长ID
  string ttid = 5; // 会长TTID
  CoopOptType opt_type = 6; // 操作类型
  int64 opt_time = 7; // 操作时间
  string operator = 8; // 操作人
  CooperationType cooperation_type = 9; // 合作类型
}

message GetCooperationOptListReq {
  uint32 offset = 1;
  uint32 limit = 2;
  uint32 guild_id = 3; // 根据公会ID或者靓号ID进行查询
  uint32 uid = 4; // 根据会长Uid查询
  CooperationType coop_type = 5; // 根据合作库类型进行查询
}
message GetCooperationOptListResp {
  repeated CooperationOptInfo list = 1;
  uint32 total = 2;
}

message GetCooperationGuildIdsReq {
  CooperationType coop_type = 1; // 合作类型
  uint32 offset = 2;
  uint32 limit = 3; // 兼容旧接口，如果不填，则不进行分页
  repeated uint32 guild_list = 4; // 查询公会ID列表
}
message GetCooperationGuildIdsResp {
  repeated uint32 guild_list = 1; // 公会ID列表
  uint32 total = 2;
}

message GetGuildCooperationInfosReq {
  uint32 guild_id = 1; // 需要获取的公会ID
}
message GetGuildCooperationInfosResp {
  bool is_amuse_coop_guild = 1; // 是否是娱乐合作库公会
  bool is_yuyin_coop_guild = 2; // 是否是语音合作库公会
  bool is_esport_coop_guild = 5; // 是否是电竞合作库公会
  uint32 amuse_application = 3; // 兼容旧接口，当为合作库公会时为1，否则为0
  uint32 yuyin_application = 4; // 兼容旧接口，当为合作库公会时为1，否则为0
}

message CooperationHistory {
  uint32 guild_id = 1; // 公会ID
  CoopOptType opt_type = 2; // 操作类型
  int64 opt_time = 3; // 操作时间
  string operator = 4; // 操作人
  CooperationType cooperation_type = 5; // 合作类型
}

message GetGuildCooperationHistoryReq {
  uint32 guild_id = 1; // 公会ID
  CooperationType coop_type = 2; // 合作类型
}
message GetGuildCooperationHistoryResp {
  repeated CooperationHistory infos = 1;
}

message GetGuildCooperationWithTimeReq {
  uint32 guild_id = 1; // 公会ID
  int64 snapshot_time = 2; // 当前时间
}
message GetGuildCooperationWithTimeResp {
  bool is_exist = 1; // 是否存在合作关系
  bool is_amuse_coop_guild = 2; // 是否是娱乐合作库公会
  bool is_yuyin_coop_guild = 3; // 是否是语音合作库公会
  bool is_esport_coop_guild = 6; // 是否是语音合作库公会
  uint32 guild_type = 4; // 公会类型，当为合作库公会时为1，否则为0
  uint32 library_type = 5; // 合作类型，兼容旧接口，当前时间点如果两种合作都存在，哪个记录先就是哪个类型，实际按照是否为某种公会类型进行判断
}

// 公会历史合作时间段
message CoopHistoryPeriod {
  CooperationType coop_type = 1; // 合作类型
  int64 begin_time = 2; // 合作时间段开始时间
  int64 end_time = 3; // 合作时间段结束时间
}

message GetGuildCooperationHistoryPeriodReq {
  uint32 guild_id = 1; // 公会ID
}
message GetGuildCooperationHistoryPeriodResp {
  repeated CoopHistoryPeriod period = 1; // 历史合作时间段
  int64 snapshot_time = 2; // 当前时间
}

message GuildNoBonus {
  uint32 guild_id = 1; // 公会ID
  string remark = 2; // 备注
}

message GetAllNoBonusGuildReq {}
message GetAllNoBonusGuildResp {
  repeated GuildNoBonus guild_list = 1;
}

message SetNoBonusGuildReq {
  uint32 guild_id = 1;
  string remark = 2;
}
message SetNoBonusGuildResp {}

// 合作变更事件
message CooperationChangeEvent {
  uint32 guild_id = 1; // 公会ID
  CooperationType coop_type = 2; // 合作类型
  CoopOptType opt_type = 3; // 操作类型
  int64 change_time = 4; // 变更时间
}