syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channelonlinerank";
package channelonlinerank;
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service ChannelOnlineRank {
    rpc GetMemberRankList (GetMemberRankListReq) returns (GetMemberRankListResp) {
    }
    rpc GetMemberWeekRankList( GetMemberWeekRankListReq ) returns ( GetMemberWeekRankListResp ){
    }
    rpc FixOnlineRank ( FixOnlineRankReq ) returns ( FixOnlineRankResp ) {
    }

    // v2
    rpc GetMemberRankListV2 (GetMemberRankListReq) returns (GetMemberRankListResp) {}
    rpc GetMemberWeekRankListV2( GetMemberWeekRankListReq ) returns ( GetMemberWeekRankListResp ){}
   
    rpc GetMemberRankVal (GetMemberRankValReq) returns (GetMemberRankValResp) {}


    rpc SettleYkwWeekRank ( SettleYkwWeekRankReq ) returns ( SettleYkwWeekRankResp ){}

    rpc FixOnlineRankV2 ( FixOnlineRankV2Req ) returns ( FixOnlineRankV2Resp ){}

    rpc GetOrderCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetOrderList(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
    rpc ReplaceOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

    rpc TestAddPresentEvent ( TestAddPresentEventReq ) returns ( Empty ){}
 }

enum ChannelOnlineRankValSourceType {
    UNSPECIFIED =0; // 无效值
    SEND_GIFT=1;//送礼
    KNIGHT=2;//开通骑士团
    ACTIVITY=3;//活跃值
    WEREWOLF_TBEAN=4;//狼人杀T豆消费
}


message GetMemberRankListReq {
    uint32 oper_uid = 1;
    uint32 channel_id = 2;
    uint32 channel_type = 3;
    bool invisible_vaild = 4;
    bool is_live_channel = 5;
    int64 start = 6;
    int64 stop = 7;
}

message GetMemberRankListResp {
	uint32 channel_id = 1;
	repeated ChannelMemberRank member_list = 2;
	uint32 all_member_size = 3;

    ChannelMemberRank my_rank_info = 4;
    uint32 d_value = 5;    // 距离上一名的差值
    uint32 rank = 6;       // 我的排名
}


message UserRankMetadata {
    uint32 uid = 1;
    int32 nobility_level = 2;  //贵族等级
    bool  nob_insisible  = 3; //是否开启贵族隐身

    int32 total_consume  = 4; //房间历史总消费
    int32 consume       = 5; //本场消费（公开房当天消费，直播房本场消费）
    int32 ykw_level      = 6; //神秘人等级
    int32 timestamp     = 7; //2050-01-01 00:00:00 2524579200 - nowts
}

message ChannelMemberVip {
	uint32 curr_level_id   = 1;   // 成员当前的VIP ID
	string curr_level_name = 2;   // 成员当前VIP ID 对应的Vip名称
	uint32 curr_level_value = 3;  // 成员当前的VIP等级数值

	uint32 next_level_id = 4;         // 达到下一级的VIP ID
	string next_level_name = 5;       // 达到下一级的VIP等级名称
	uint32 next_level_min_value = 6;  // 达到下一集需要的最小VIP数值
    uint32 nobility_level = 7;        // 贵族等级
    bool   invisible = 8; //是否隐身
}

// 房间成员排名信息
message ChannelMemberRank {
    uint32 uid  = 1;         // 成员uid
    uint32 ts   = 2;         // 时间
	uint32 rank_value  = 3;  // 分数
	ChannelMemberVip vip_level_info = 4; // vip信息
    uint32 rank = 5;         // 排名
    uint32 total_consum = 6; // 查旧服务的 历史总消费 ,在线榜用

    uint32 ukw_level=7;
    uint32 rank_total_consum=8; // 缓存的总值
}

message RefreshTop3Task {
    uint32 cid = 1;
    uint32 channel_type = 2;
    repeated uint32 uid_list = 3;
    uint32 ts = 4;
}

message ReloadConsumeTask {
    uint32 cid = 1;
    uint32 channel_type = 2;
    uint32 uid= 3;
}

enum OnlineRankValSourceType
{
   ENUM_SEND_PRESENT = 0;  // 送礼
   ENUM_ACTIVITY = 1;  // 活跃值
   ENUM_TBEAN_CONSUME = 2;  // 消费T豆
   ENUM_YKW_EXPOSURE  = 3;  //神秘人现身
}

enum ConsumeStatusType
{
    ENUM_DELAY = 0;  //延迟第二天凌晨3点
    ENUM_ABANDON = 1;  //丢弃
    ENUM_COMMIT = 2;  //提交
    ENUM_ALL = 10; //所有订单
}

// 获取房间消费土豪榜
message MemberConsumeInfo
{
	uint32 uid = 1;
	uint32 consume_cnt = 2;

	ChannelMemberVip vip_level_info = 3;
    bool is_auto_hidden_consume = 4; //用户是否被自动隐藏消费
}

message GetMemberWeekRankListReq {
    uint32 cid = 1;
    uint32 begin = 2;
    uint32 limit = 3;

    // 需要获取自己的排名信息才填以下字段
    uint32 uid = 4;
    uint32 view_cnt = 5; // 半屏页榜单上展示个数
}

message GetMemberWeekRankListResp {
    repeated MemberConsumeInfo rank_info_list = 1;

    MemberConsumeInfo my_rank_info = 2;
    uint32 d_value = 3;    // 距离上一名的差值
    uint32 rank = 4;       // 我的排名
}

message FixOnlineRankReq{
    uint32 channel_id = 1; //需要更新的房间ID,0全网刷新
}

message FixOnlineRankResp{}

 
message CheckChannelOnlineMemberReq{
    uint32 channel_id = 1; //需要检查的房间ID,0全网刷新
}

message CheckChannelOnlineMemberResp{}


message FixOnlineRankV2Req {
    bool certain = 1;
    uint32 channel_id = 2;
    uint32 uid = 3;
}
message FixOnlineRankV2Resp {
}

message SettleYkwWeekRankReq {
    bool certain = 1;
}
message SettleYkwWeekRankResp {
    
}

message TestAddPresentEventReq{
    string order_id=1;
    uint32 send_uid=2;
    uint32 channel_id=3;
    uint32 price=4;
    uint32 channel_type=5;
    uint32 send_time=6;
}
message Empty {
}

message GetMemberRankValReq {
    uint32 uid=1;
    uint32 channel_id=2;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetMemberRankValResp {
    uint32 rank_cache_val=1;
    uint32 rank_cache_record_val=2;
    uint32 rank_db_val=3;

    repeated OrderInfo orderList=4;
}
message OrderInfo {
    string order_id=1;
    uint32 uid=2;
    uint32 cid=3;
    uint32 total_price=4;
    uint32 create_time=5;
    ConsumeStatusType status=6; 
}