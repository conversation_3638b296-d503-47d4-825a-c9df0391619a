syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channellivefans";
package channellivefans;


service ChannelLiveFans {

    rpc CreateFansGroup (CreateFansGroupReq) returns (CreateFansGroupResp) {
    }
    rpc AddFansLoveValue(AddFansLoveValueReq) returns (AddFansLoveValueResp) {
    }
    rpc GetFansRankList(GetFansRankListReq) returns (GetFansRankListResp) {
    }
    rpc GetFansInfo ( GetFansInfoReq) returns ( GetFansInfoResp) {
    }
    rpc BatchGetFansInfo (BatchGetFansInfoReq) returns (BatchGetFansInfoResp) {
    }
    rpc GetAnchorFansInfo (GetAnchorFansInfoReq) returns (GetAnchorFansInfoResp) {
    }
    rpc GetAnchorFansData (GetAnchorFansDataReq) returns (GetAnchorFansDataResp) {
    }
    rpc AddAnchorPopularity (AddAnchorPopularityReq) returns (AddAnchorPopularityResp) {
    }
    rpc GetFansAllAnchorsInfo (GetFansAllAnchorsInfoReq) returns (GetFansAllAnchorsInfoResp) {
    }
    rpc GetFansAddedGroupList (GetFansAddedGroupListReq) returns (GetFansAddedGroupListResp) {
    }
    rpc SetFansGroupName (SetFansGroupNameReq) returns (SetFansGroupNameResp) {
    }
    rpc GetNeedVerifyGroupNameList (GetNeedVerifyGroupNameListReq) returns (GetNeedVerifyGroupNameListResp) {
    }
    rpc GroupNameVerifiedInfo (GroupNameVerifiedInfoReq) returns (GroupNameVerifiedInfoResp) {
    }
    rpc CheckSetGroupNamePermit (CheckSetGroupNamePermitReq) returns (CheckSetGroupNamePermitResp) {
    }
    rpc AddPlateConfig (AddPlateConfigReq) returns (AddPlateConfigResp) {
    }
    rpc UpdatePlateConfig (UpdatePlateConfigReq) returns (UpdatePlateConfigResp) {
    }
    rpc DelPlateConfig (DelPlateConfigReq) returns (DelPlateConfigResp) {
    }
    rpc GetPlateConfigList (GetPlateConfigListReq) returns (GetPlateConfigListResp) {
    }
    rpc GrantAnchorPlate (GrantAnchorPlateReq) returns (GrantAnchorPlateResp) {
    }
    rpc UpdateGrantedPlateInfo (UpdateGrantedPlateInfoReq) returns (UpdateGrantedPlateInfoResp) {
    }
    rpc DelGrantedPlateInfo (DelGrantedPlateInfoReq) returns (DelGrantedPlateInfoResp) {
    }
    rpc GetGrantedPlateList (GetGrantedPlateListReq) returns (GetGrantedPlateListResp) {
    }
    rpc GetPlateConfigById (GetPlateConfigByIdReq) returns (GetPlateConfigByIdResp) {
    }
    rpc SetFansMissionFinishStatus (SetFansMissionFinishStatusReq) returns (SetFansMissionFinishStatusResp) {
    }
    rpc CheckGroupNameExist (CheckGroupNameExistReq ) returns (CheckGroupNameExistResp) {
    }
    rpc CheckUserIsFans(CheckUserIsFansReq) returns (CheckUserIsFansResp) {
    }
    rpc GetFansCntByTs(GetFansCntByTsReq) returns (GetFansCntByTsResp) {
    }
    rpc WearAnchorPlate(WearAnchorPlateReq) returns (WearAnchorPlateResp) {
    }
    rpc GetAnchorValidGrantPlates(GetAnchorValidGrantPlatesReq) returns (GetAnchorValidGrantPlatesResp) {
    }
    rpc GetGroupNameVerifyRecordList(GetGroupNameVerifyRecordListReq) returns(GetGroupNameVerifyRecordListResp) {
    }
    rpc BatGroupNameVerify(BatGroupNameVerifyReq) returns (BatGroupNameVerifyResp) {
    }
    
    //加入粉丝团
    rpc JoinFansGroup(JoinFansGroupReq) returns (JoinFansGroupResp){}

    //退出粉丝团
    rpc LeaveFansGroup(LeaveFansGroupReq) returns (LeaveFansGroupResp) {}

    // 重置主播粉丝团的一些信息
    rpc ResetFansGroup(ResetFansGroupReq) returns (ResetFansGroupResp) {}

    // 用户赠送粉丝团专属礼物检查
    rpc SendFansGiftCheck(SendFansGiftCheckReq) returns (SendFansGiftCheckResp) {}

    // 发放用户粉丝团专属礼物特权
    rpc GrantFansGiftPrivilege(GrantFansGiftPrivilegeReq) returns (GrantFansGiftPrivilegeResp) {}

    // 获取用户粉丝团专属礼物特权
    rpc GetUserFansGiftPri(GetUserFansGiftPriReq) returns (GetUserFansGiftPriResp) {}
}

// 创建粉丝团
message CreateFansGroupReq {
    uint32 anchor_uid = 1;
}
message CreateFansGroupResp {
    uint32 group_id = 1;
}

// 增加粉丝亲密值
message AddFansLoveValueReq {
    uint32 fans_uid = 1;
    uint32 anchor_uid = 2;
    uint32 love_value = 3;
    uint32 channel_id = 4;
    uint32 mission_id = 5; // 任务id

    string order_id=6;
}
message AddFansLoveValueResp {
    uint32 new_love_value = 1;
    uint32 fans_level = 2;
    uint32 group_id = 3;     // 粉丝团id
}

// 动效铭牌配置 
message DynamicPlateConfig {
    string img_url = 1;
    string level_dynamic_url = 2;  // 等级动效
    string name_dynamic_url = 3;   // 团名动效
    string name_font_color = 4;    //团名字体颜色
    string name_shadow_color = 5;  // 团名阴影
    string level_font_color = 6;   //等级字体颜色
    string level_shadow_color = 7;  // 等级字体阴影
}

// 带团名粉丝铭牌
message GroupNamePlateInfo {
    string img_url = 1;       // 底板url
    string name_font_color = 2;    //团名字体颜色
    uint32 width = 3;
    uint32 height = 4;
    PlateDecorationConfig plate_decoration = 5;  // 铭牌装饰
    string name_shadow_color = 6;
    string level_font_color = 7;   //等级字体颜色
    string level_shadow_color = 8;
    string plate_name = 9; // 铭牌名
    uint32 grant_id = 10;  // 发放id
    uint32 remain_days = 11;  // 剩余有效天数
    bool is_wear = 12;  // 是否佩戴
    DynamicPlateConfig dynamic_plate = 13;  // 动效
    bool is_default = 14; 
}

// 普通铭牌样式
message CommonPlateInfo {
    string common_img_url = 1;
    string level_font_color = 2;   //等级字体颜色
    string level_shadow_color = 3;
}

// 铭牌装饰配置
message PlateDecorationConfig {
    string img_url = 1;
    uint32 width = 2;
    uint32 height = 3;
}

// 粉丝铭牌
message FansPlateInfo {
    enum EnumFansPlateType {
        E_PLATE_COMMON = 0;       // 普通的
        E_PLATE_GROUP_NAME = 1;   // 带团名的
    }
    uint32 type = 1;            // see EnumFansPlateType
    CommonPlateInfo common_plate_info = 2;  // 普通铭牌样式
    GroupNamePlateInfo name_plate_info = 3;   // 带团名粉丝铭牌样式
}

message FansInfo {
   uint32 uid = 1;
   bool   is_fans = 2;
   uint32 fans_level = 3;
   bool   is_valid = 4;      //铭牌是否有效
   uint32 love_value = 5;    // 亲密值
   uint32 gap_next_value = 6;  //距离下一等级的亲密值
   uint32 next_level = 7;      // 下一个粉丝等级
   float  value_ratio = 8;     // 百分比
   FansPlateInfo plate_info = 9;
   string group_name = 10;
   int64  add_group_ts = 11;  // 加团时间
}

enum EnumRankType {
    E_ACTIVE = 0;
    E_NEW = 1;
    E_INACTIVE =2;
}

// 获取粉丝排行榜
message GetFansRankListReq {
    uint32 anchor_uid = 1;
    uint32 rank_type = 2;   // see EnumRankType
    uint32 offset = 3;
    uint32 limit = 4;
    uint32 cid = 5; // 房间id
}
message GetFansRankListResp {
    repeated uint32 fans_list = 1;
    map<uint32, FansInfo> fans_info_map = 2;
    uint32 next_offset = 3;
    uint32 active_fans_cnt = 4;
    uint32 inactivie_fans_cnt = 5;
    uint32 new_fans_cnt = 6;
}

enum EnumKnightType {
    E_NO_KNIGHT = 0;  // 非骑士
    E_COMMON_KNIGHT = 1; // 普通骑士
    E_CHIEF_KNIGHT =2;  // 首席骑士
}

// 获取粉丝信息
message GetFansInfoReq {
    uint32 uid = 1;
    uint32 anchor_uid =2;
    uint32 knight_type = 3;
}
message GetFansInfoResp {
    FansInfo fans_info = 1;
}

// 批量获取粉丝信息
message BatchGetFansInfoReq {
    uint32 anchor_uid = 1;
    repeated uint32 uid_list = 2;
    map<uint32,uint32> knight_type_map = 3;
}
message BatchGetFansInfoResp {
    map<uint32, FansInfo>  fans_info_map = 1;
}

// 获取主播的粉丝团信息
message GetAnchorFansInfoReq {
   uint32 anchor_uid = 1;
}
message GetAnchorFansInfoResp {
   uint32 fans_cnt = 1;    //粉丝数量
   uint32 item_id = 2;     // 加团礼物id
   string group_name = 3;   // 团名
   FansPlateInfo name_plate_info = 4;   // 带团名粉丝铭牌样式
}

message FansData {
   string date = 1; //日期
   uint32 fans_cnt = 2; //粉丝人数
}

// 根据时间获取主播的粉丝信息
message GetAnchorFansDataReq {
   uint32 anchor_uid = 1;   //主播uid
   uint32 begin_time = 2;
   uint32 end_time = 3;
}
message GetAnchorFansDataResp {
   repeated FansData fans_data_list = 1;
}

// 增加主播粉丝亲密度
message AddAnchorPopularityReq {
   uint32 fans_uid = 1;
   uint32 anchor_uid = 2;
   uint32 channel_id = 3;
}
message AddAnchorPopularityResp {
}

// 获取用户加入的粉丝团列表
message GetFansAllAnchorsInfoReq {
    uint32 fans_uid = 1;
}
message GetFansAllAnchorsInfoResp {
    repeated uint32 anchor_uids = 1;
}

message FansAddedGroupInfo {
    string group_name = 1;      // 团名
    uint32 fans_level = 2;      // 粉丝等级
    FansPlateInfo plate_info = 3;  //粉丝铭牌信息
    uint32 love_value = 4;   // 亲密值
}

// 获取用户加入的粉丝团详细信息
message GetFansAddedGroupListReq{
    uint32 fans_uid = 1;
    uint32 offset = 2;
    uint32 limit = 3;
}
message GetFansAddedGroupListResp{
    repeated uint32 anchor_list = 1;      // 加入的粉丝团列表
    map<uint32, FansAddedGroupInfo> added_group_map = 2;    // 铭牌信息
    uint32  added_group_cnt = 3;   // 加入的粉丝团数量
}

// 检测团名是否被占用
message CheckGroupNameExistReq {
    uint32 anchor_uid = 1;
    string group_name = 2;
}
message CheckGroupNameExistResp {
    bool is_exist = 1;
}

// 主播设置粉丝团名称
message SetFansGroupNameReq {
    uint32 anchor_uid = 1;
    string group_name = 2;
    string ip_addr = 3;
    string device_id = 4;
    string smdevice_id = 5;
}
message SetFansGroupNameResp {
}

// 检测主播是否有设置粉丝团名称权限
message CheckSetGroupNamePermitReq {
    uint32 anchor_uid = 1;
}
message CheckSetGroupNamePermitResp {
    string desc = 1;  // 设置团名说明文案
}

message VerifyGroupNameInfo {
    uint32 anchor_uid = 1;
    string group_name = 2;
    int64  summit_ts  = 3;
    uint32 verify_result = 4;  // see EnumVerifyResult
    string not_pass_reason = 5; // 审核不通过原因
    string operator = 6; // 操作人账号
    int64  operate_ts = 7; // 操作时间
    string anchor_ttid = 8; // 主播ttid
    string anchor_nick = 9;  // 主播昵称
}

// 获取需要审核的团名列表
message GetNeedVerifyGroupNameListReq {
    uint32 uid = 1;   // 查询指定主播
    uint32 offset = 2; // 从0开始
    uint32 limit = 3;
}
message GetNeedVerifyGroupNameListResp {
    repeated VerifyGroupNameInfo info_list = 1;
    uint32 next_offset = 2; // 为0表示结束
    uint32 total_cnt = 3;
}

// 团名审核结果
message GroupNameVerifiedInfoReq {
    enum EnumVerifyResult {
        E_RESULT_NOT_PASS = 0;
        E_RESUTL_PASS = 1;
    }
    uint32 anchor_uid = 1;
    string group_name = 2;
    uint32 verify_result = 3;  // see EnumVerifyResult
    string not_pass_reason = 4; // 审核不通过原因
    uint32 summit_ts = 5; // 提交时间
    string operator = 6; // 操作人账号
    int64  operate_ts = 7; // 操作时间
    string anchor_ttid = 8; // 主播ttid
    string anchor_nick = 9;  // 主播昵称
}
message GroupNameVerifiedInfoResp {
}

// 批量审核团名
message BatGroupNameVerifyReq {
   repeated VerifyGroupNameInfo info_list = 1;
}
message BatGroupNameVerifyResp {
   repeated string fail_ttids = 1;
}


// 获取审核记录列表
message GetGroupNameVerifyRecordListReq {
    uint32 offset = 1; // 从0开始
    uint32 limit = 2;
    uint32 uid = 3;  // 查询指定uid
}
message GetGroupNameVerifyRecordListResp {
    repeated VerifyGroupNameInfo record_list = 1;
    uint32 next_offset = 2; // 为0表示结束
    uint32 total_cnt = 3;
}


//**** 运营后台铭牌配置相关 ******//

message PlateImgConfig {
    string img_url = 1;
    uint32 name_font_cnt_min = 2;
    uint32 name_font_cnt_max = 3;
    uint32 width = 4;  // 图片宽度
    uint32 height = 5;  // 图片高度
    uint32 level_width = 6; // 等级宽度
}

// 铭牌装饰
message PlateDecoration {
    string img_url = 1;
    uint32 name_font_cnt_min = 2;
    uint32 name_font_cnt_max = 3;
    uint32 width = 4;  // 图片宽度
    uint32 height = 5;  // 图片高度
}

message PlateConfig {
    repeated PlateImgConfig img_list = 1;
    string level_font_color = 2;   //等级字体颜色
    uint32 min_level = 3;
    uint32 max_level = 4;
}

message PlateConfigInfos {
    uint32 plate_id = 1;
    string plate_name = 2;
    string name_font_color = 3;   //团名字体颜色
    repeated PlateConfig plate_config_list = 4;
    repeated PlateDecoration plate_decoration_list = 5;
}

// 增加粉丝铭牌配置
message AddPlateConfigReq {
   PlateConfigInfos config_info = 1;
}
message AddPlateConfigResp {
}

// 更新粉丝铭牌配置
message UpdatePlateConfigReq {
   PlateConfigInfos config_info = 1;
}
message UpdatePlateConfigResp {
}

// 删除粉丝铭牌配置
message DelPlateConfigReq {
   uint32 plate_id = 1;
}
message DelPlateConfigResp {
}

// 分页获取粉丝铭牌配置
message GetPlateConfigListReq {
    uint32 offset = 1;
    uint32 limit = 2;
    uint32 plate_id = 3; 
    string plate_name = 4; 
}
message GetPlateConfigListResp{
    repeated PlateConfigInfos info_list = 1;
    uint32 next_offset = 2;
    uint32 total_cnt = 3;
}

message GetPlateConfigByIdReq {
   uint32 plate_id = 1;
}
message GetPlateConfigByIdResp {
   PlateConfigInfos plate_info = 1;
}

message GrantedPlateInfo {
    uint32 granted_id = 1;    // 发放id
    uint32 anchor_uid = 2;
    uint32 plate_id = 3;
    int64  begin_ts = 4;
    int64  end_ts = 5;
    int64  update_ts = 6;
    uint32 day_cnt = 7; // 发放天数 可以叠加。新的发放不需要填 begin_ts和end_ts
    bool   is_wear = 8;  // 是否佩戴
    PlateConfigInfos plate_info = 9;  // 铭牌配置
}

// 发放铭牌
message GrantAnchorPlateReq {
    GrantedPlateInfo grant_info = 1;
}
message GrantAnchorPlateResp {
}

// 修改发放铭牌
message UpdateGrantedPlateInfoReq {
    GrantedPlateInfo grant_info = 1;
}
message UpdateGrantedPlateInfoResp {
}

// 删除发放铭牌
message DelGrantedPlateInfoReq {
    uint32 granted_id = 1;
}
message DelGrantedPlateInfoResp {
}

// 分页获取发放铭牌列表
message GetGrantedPlateListReq {
    uint32 offset = 1;
    uint32 limit = 2;
    uint32 anchor_uid = 3;  // 指定uid查询
    uint32 plate_id = 4; 
    string plate_name = 5; 
}
message GetGrantedPlateListResp{
    repeated GrantedPlateInfo info_list = 1;
    uint32 next_offset = 2;
    uint32 total_cnt = 3;
}

//**** 运营后台铭牌配置相关 end ***** //

// 设置粉丝送礼任务已完成
message SetFansMissionFinishStatusReq {
    uint32 anchor_uid = 1;
    uint32 fans_uid = 2;
}
message SetFansMissionFinishStatusResp {
}

// 检测用户是否是主播粉丝
message CheckUserIsFansReq {
   uint32 uid = 1;
   uint32 anchor_uid = 2;  // 主播uid
}
message CheckUserIsFansResp {
   bool is_fans = 1;
   uint32 item_id = 2;     // 加团礼物id
   uint32 fans_level = 3; // 粉丝等级
}

//获取某个时间点之前的粉丝团人数
message GetFansCntByTsReq {
   uint32 anchor_uid = 1;
   uint32 ts = 2;
}
message GetFansCntByTsResp {
   uint32 fans_cnt = 1;
}

// 佩戴铭牌
message WearAnchorPlateReq {
    uint32 grant_id = 1; // 发放id
    uint32 anchor_uid = 2;  //主播uid
}  
message WearAnchorPlateResp {
}

// 获取主播有效的铭牌配置列表
message GetAnchorValidGrantPlatesReq {
    uint32 anchor_uid = 1; 
}
message GetAnchorValidGrantPlatesResp {
    repeated GroupNamePlateInfo info_list = 1;
}


//加入粉丝团API
message JoinFansGroupReq {
	uint32  uid = 1;
	uint32  anchor_uid = 2;
	uint32  channel_id = 3;
	uint32  channel_type = 4;
	uint32  create_time = 5;
	string  order_id = 6;
	string  from_desc = 7;
}
message JoinFansGroupResp {
}


// 退出粉丝团
message LeaveFansGroupReq {
   uint32 fans_uid = 1;  // 粉丝uid
   uint32 anchor_uid = 2;  // 主播uid
}
message LeaveFansGroupResp {
}


// 重置主播粉丝团的一些信息
message ResetFansGroupReq {
   uint32 anchor_uid = 1;
}
message ResetFansGroupResp {
}


// 用户赠送粉丝团专属礼物检查
message SendFansGiftCheckReq {
   uint32 uid = 1; // 用户uid
   uint32 anchor_uid = 2;  // 主播uid
   uint32 gift_id = 3;  // 礼物id
   uint32 gift_fans_lv = 4;  // 礼物粉丝等级
}
message SendFansGiftCheckResp {
}


// 粉丝团专属礼物特权
message FansGiftPrivilege {
   uint32 anchor_uid = 1; // 主播uid
   uint32 fans_uid = 2; // 粉丝uid
   repeated uint32 gift_id_list = 3; // 礼物id
   uint32 begin_ts = 4;  // 开始时间
   uint32 end_ts = 5;  // 结束时间
}

// 发放用户粉丝团专属礼物特权
message GrantFansGiftPrivilegeReq {
   repeated FansGiftPrivilege pri_list = 1;
}
message GrantFansGiftPrivilegeResp {
}


// 获取用户粉丝团专属礼物特权
message GetUserFansGiftPriReq {
   uint32 uid = 1; 
   uint32 anchor_uid = 2; // 主播uid
}
message GetUserFansGiftPriResp {
   map<uint32,uint32> map_giftid_ts = 1;   
}

