syntax = "proto3";

package game_http_logic;

import "google/api/annotations.proto";

option go_package = "golang.52tt.com/protocol/services/game-http-logic";

service GameHttpLogic {
    // 获取开黑形象自评题目
    rpc GetSelfRateQuestions(GetSelfRateQuestionsReq) returns (GetSelfRateQuestionsResp) {
        option (google.api.http) = {
            post: "/game-http-logic/user-rate/getSelfRateQuestions"
        };
    }

    // 提交开黑形象自评结果
    rpc SubmitSelfRateResult(SubmitSelfRateResultReq) returns (SubmitGameUserRateResp) {
        option (google.api.http) = {
              post: "/game-http-logic/user-rate/submitSelfRateResult"
        };
    }

    // 获取用户测评形象结果
    rpc GetUserImageRateResult(GetUserImageRateResultReq) returns (GameUserPersonalImageRespItem) {
        option (google.api.http) = {
            post: "/game-http-logic/user-rate/getUserImageRateResult"
        };
    }

    // 获取用户信息
    rpc GetUserInfo(GetUserInfoReq) returns (GetUserInfoResp) {
        option (google.api.http) = {
            post: "/game-http-logic/game-account/getUserInfo"
        };
    }

    // 获取用户被评列表
    rpc GetGameUserBeRateList(GetGameUserBeRateListReq) returns (GetGameUserBeRateListResp) {
        option (google.api.http) = {
            post: "/game-http-logic/user-rate/getGameUserBeRateList"
        };
    }

    // 获取用户评价列表
    rpc GetGameUserRateList(GetGameUserRateListReq) returns (GetGameUserRateListResp) {
        option (google.api.http) = {
            post: "/game-http-logic/user-rate/getGameUserRateList"
        };
    }

    // 提交用户评价
    rpc SubmitGameUserRate(SubmitGameUserRateReq) returns (SubmitGameUserRateResp) {
        option (google.api.http) = {
            post: "/game-http-logic/user-rate/submitGameUserRate"
        };
    }

    // 获取红点信息
    rpc GetRedDotInfo(GetRedDotInfoReq) returns (GetRedDotInfoResp) {
        option (google.api.http) = {
            post: "/game-http-logic/red-dot/getRedDotInfo"
        };
    }

    // 标记红点已读
    rpc MarkRedDotRead(MarkRedDotReadReq) returns (MarkRedDotReadResp) {
        option (google.api.http) = {
            post: "/game-http-logic/red-dot/markRedDotRead"
        };
    }

    // 获取聚合红点信息
    rpc GetAggregateRedDotInfo(GetAggregateRedDotInfoReq) returns (GetAggregateRedDotInfoResp) {
        option (google.api.http) = {
            post: "/game-http-logic/red-dot/getAggregateRedDotInfo"
        };
    }

    // 标记聚合红点已读
    rpc MarkAggregateRedDotRead(MarkAggregateRedDotReadReq) returns (MarkAggregateRedDotReadResp) {
        option (google.api.http) = {
            post: "/game-http-logic/red-dot/markAggregateRedDotRead"
        };
    }

    // 获取用户信息
    rpc GetUserInfos(GetUserInfosReq) returns (GetUserInfosResp) {
        option (google.api.http) = {
            post: "/game-http-logic/game-account/getUserInfos"
        };
    }

    // 根据用户自定义输入内容重排标签
    rpc ReorderRateTags(ReorderRateTagsReq) returns (ReorderRateTagsResp) {
        option (google.api.http) = {
            post: "/game-http-logic/user-rate/reorderRateTags"
        };
    }
}


// 自评问题配置
message H5Question {
  message Option {
    uint32 opt_id = 1;  // 选项id
    string content = 2; // 选项内容
  }
  // 题目id
  string question_id = 1;
  // 题目描述
  string description = 2;
  // 选项
  repeated Option options = 3;
  // 题目所属维度id
  string dimension_id = 4;
}

// 获取开黑形象自评题目
message GetSelfRateQuestionsReq {
  uint32 tab_id = 1;
}

message GetSelfRateQuestionsResp {
  repeated H5Question questions = 1;
}

message SelfRateResult {
  // 题目id
  string question_id = 1;
  // 选项id
  uint32 opt_id = 2;
  // 题目所属维度id
  string dimension_id = 3;
}

// 提交开黑形象自评结果
message SubmitSelfRateResultReq {
  uint32 tab_id = 1;
  repeated SelfRateResult results = 2;  // 答题结果
}

// 获取用户测评形象结果
message GetUserImageRateResultReq {
  uint32 uid = 1;
  uint32 tab_id = 2;
}

message GameUserPersonalImageRespItem {
  // 维度分数
  repeated DimensionItem item = 1;
  // 评价总分
  float total_score = 2;
  // 开黑关键字，aigc返回
  string game_key_words = 3;
  // 关键字描述，aigc返回
  string game_key_words_desc = 4;
  // 自我介绍，aigc返回
  string self_intro = 5;
  // 相似用户占比，数据库写入时取10%-40%之间的随机数，H5测评结果用
  string similar_user_percent = 6;
  // 用户账号
  string account = 7;
  // 用户昵称
  string nickname = 8;
  // 玩法名称
  string tab_name = 9;
}

message DimensionItem {
  // 维度id
  string id = 1;
  // 维度名称
  string name = 2;
  // 维度分数
  float score = 3;
}

message UserInfo {
  uint32 uid = 1;
  string username = 2;
  string nickname = 3;
  string alias = 4;
  string phone = 5;
  int32 sex = 6;
}

// 获取用户信息
message GetUserInfoReq {
}
message GetUserInfoResp {
  UserInfo user_info = 1;
}

message GetGameUserBeRateListReq {
  // 用户id
  uint32 uid = 1;
  // last record time，分页用
  uint64 last_record_time = 2;
}

message GetGameUserBeRateListResp {
  // 被评详情数据
  repeated GameUserBeRateItem items = 1;
  // 是否已经到底部，true为底部
  bool load_finish = 2;
}

message GameUserBeRateItem {
  // 好评item
  message LikeItem {
    // 评价id
    string id = 1;
    // 用户id
    uint32 uid = 2;
    string account = 3;
    string nickname = 4;
    int32 sex = 5;
    // 关注状态
    bool is_follow = 6;
    // 评价时间文案
    string rate_time_text = 7;
    // 下发时间，单位:ms
    int64 create_time = 8;
    // 筛选的评价标签
    repeated string tags = 9;
    // 用户自定义评价
    string user_rate_text = 10;

  }
  // 扣分记录item
  message DeductItem {
    // 扣分记录id
    string id = 1;
    // 标题
    string title = 2;
    // 副标题
    string subtitle = 3;
    // 下发时间，单位:ms
    int64 create_time = 4;
  }

  // item类型，1-好评，2-扣分
  uint32 item_type = 1;
  oneof content {
    LikeItem like_item = 2; // item_type=1时取
    DeductItem deduct_item = 3; // item_type=2时取
  }
}

message GetGameUserRateListReq {
  // 评价来源，互动提示信息可用于不同文案
  uint32 source = 1;
  // 用户id
  uint32 rate_uid = 2;
  // last id，分页用
  string last_id = 3;
}

message GetGameUserRateListResp {
  // 喜欢的评价文案
  string like_text = 1;
  // 不喜欢的评价文案
  string dislike_text = 2;
  // 评价详情数据
  repeated GameUserRateItem data = 3;
  // 是否已经到底部，true为底部
  bool load_finish = 4;
  // 6.60版本-赞标签最大选择个数，0表示无限制
  uint32 max_positive_select_count = 5;
  // 6.60版本-踩标签最大选择个数，0表示无限制
  uint32 max_negative_select_count = 6;
}

message GameUserRateItem {
  // id
  string id = 1;
  // 用户id
  uint32 uid = 2;
  string account = 3;
  string nickname = 4;
  uint32 sex = 5;
  // 是否关注
  bool is_follow = 6;
  // 交互提示信息，如：5小时前跟Ta一起玩过王者荣耀
  string intro_tips = 7;
  // 评论状态， 1-未评价，2-已评价，3-已过期
  uint32 status = 8;
  // 评价意向，赞或踩，0-未选，1-赞，2-踩
  uint32 attitude = 9;
  // 评价标签
  repeated GameUserRateTag tags = 10;
  // 创建时间
  uint64 create_time = 11;
  // 用户自定义评价
  string user_rate_text = 12;
}

// 标签详情
message GameUserRateTag {
  // 维度标签id，普通标签无
  string id = 1;
  // 标签名称
  string name = 2;
  // 1-正向标签，点赞展示；2-负向标签，踩展示
  uint32 tag_type = 3;
}

message BaseRequest {
  string device_id = 1;
  uint32 market_id = 2;
  uint32 client_type = 3;
  uint32 client_version = 4;
  string client_ip = 5;
}

message SubmitGameUserRateReq {
  // 评价id
  string id = 1;
  // 评价意向，赞或踩，0-未选，1-赞，2-踩
  uint32 attitude = 2;
  // 选择的评价标签
  repeated GameUserRateTag select_tags = 3;
  // 用户自定义评价
  string user_rate_text = 4;
  // 基本参数
  BaseRequest base_request = 5;
}
message SubmitGameUserRateResp {
  bool is_shield_pass = 1;
  string not_pass_text = 2;
}

// 获取红点信息
message GetRedDotInfoReq {
  // 业务类型, see enum game_red_dot_logic.proto RedDotBizType
  uint32 biz_type = 1;
  // 业务场景key
  string biz_suffix_key = 2;
}

message GetRedDotInfoResp {
  // 未读红点数
  uint32 count = 1;
}

// 标记红点已读
message MarkRedDotReadReq {
  // 业务类型, see enum game_red_dot_logic.proto RedDotBizType
  uint32 biz_type = 1;
  // 业务场景key, 如ai群聊类型: 传群id
  string biz_suffix_key = 2;
}

message MarkRedDotReadResp {
}

// 获取聚合红点信息，目前只有ai社区用到
message GetAggregateRedDotInfoReq {
  // 业务类型, see enum game_red_dot_logic.proto RedDotBizType
  uint32 biz_type = 1;
}

message GetAggregateRedDotInfoResp {
  // 未读红点数
  uint32 count = 1;
}

// 标记聚合红点已读，目前只有ai社区用到
message MarkAggregateRedDotReadReq {
  // 业务类型, see enum game_red_dot_logic.proto RedDotBizType
  uint32 biz_type = 1;
}

message MarkAggregateRedDotReadResp {
}

message GetUserInfosReq {
  repeated uint32 uids = 1;
}

message GetUserInfosResp {
  repeated UserInfo user_infos = 1;
}

// 获取推荐评价标签
message ReorderRateTagsReq {
  // 用户自定义评价
  string user_rate_text = 1;
  // 评价id
  string rate_item_id = 2;
}

message ReorderRateTagsResp {
  // 返回重排后的标签列表
  repeated GameUserRateTag rate_tags = 1;
}
