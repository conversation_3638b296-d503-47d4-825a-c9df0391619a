syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/muse-channel-heat";
package muse_channel_heat;

 enum MainSwitchStatus{
    UnknownStatus = 0;
    TurnOn = 1; /* 开启 */
    TurnOff = 2; /* 关闭 */
    }
// buf:lint:ignore SERVICE_PASCAL_CASE
service channelHeat {
      rpc SetMainSwitchStatus(SetMainSwitchStatusReq)returns(SetMainSwitchStatusResp){}
    rpc StartHeatChannel(StartHeatChannelReq)returns(StartHeatChannelResp){}
  rpc RefusalToHeat(RefusalToHeatReq)returns(RefusalToHeatResp){}
}
message SetMainSwitchStatusReq{
  uint32 main_switch_status=1;

}
message SetMainSwitchStatusResp{

}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message StartHeatChannelReq{
   uint32 channelId=1;
   uint32 tab_id=2;
}

message StartHeatChannelResp{}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message RefusalToHeatReq{
  uint32 channelId=1;
  uint32 tab_id=2;

}
message RefusalToHeatResp{

}