syntax = "proto3";

package levelup_present;

import "tt/quicksilver/extension/options/options.proto";

option go_package = "golang.52tt.com/protocol/services/levelup-present";

service LevelupPresent {
  option (service.options.old_package_name) = "LevelupPresent.LevelupPresent";

  //新增父级升级礼物配置
  rpc AddLevelupParentPresent(LevelupParentPresentData) returns (EmptyMsg) {}

  //修改父级升级礼物配置
  rpc UpdateLevelupParentPresent(LevelupParentPresentData) returns (EmptyMsg) {}

  //删除父级升级礼物配置
  rpc DeleteLevelupParentPresent(ItemReq) returns (EmptyMsg) {}

  //新增子级升级礼物配置
  rpc AddLevelupChildPresent(LevelupChildPresentData) returns (EmptyMsg) {}

  //修改子级升级礼物配置
  rpc UpdateLevelupChildPresent(LevelupChildPresentData) returns (EmptyMsg) {}

  //删除子级升级礼物配置
  rpc DeleteLevelupChildPresent(ItemReq) returns (EmptyMsg) {}

  //获取全部的父级升级礼物全基础配置
  rpc GetAllLevelupParentPresent(EmptyMsg) returns (LevelupPresentParentMap) {}

  //获取礼物的父级礼物ID
  rpc GetLevelupParentPresentById(ItemReq) returns (ItemRes) {}

  //新增升级礼物批量配置
  rpc AddLevelupBatch(LevelupPresentBatchData) returns (EmptyMsg) {}

  //修改升级礼物批量配置
  rpc UpdateLevelupBatch(LevelupPresentBatchData) returns (EmptyMsg) {}

  //删除升级礼物批量配置
  rpc DeleteLevelupBatch(ItemBatchReq) returns (EmptyMsg) {}

  //获取某个升级礼物的批量配置
  rpc GetLevelupBatchById(ItemReq) returns (LevelupPresentBatchList) {}

  //获取所有升级礼物的批量配置
  rpc GetAllLevelupBatch(EmptyMsg) returns (LevelupPresentBatchDataMap) {}

  //获取升级礼物的所有历史版本，后台管理使用
  rpc GetLevelupPresentVersionList(ItemReq) returns (VersionList) {}

  //新增活动升级礼物的版本，后台管理使用
  rpc AddLevelupPresentVersion(ItemVersionReq) returns (EmptyMsg) {}

  //获取用户全部升级礼物当前版本的状态，APP端礼物栏等级状态
  rpc GetUserAllLevelupPresentStatus(UidReq) returns (UserLevelupPresentStatusMap) {}

  //增加用户某个升级礼物的经验，version要上游传，防止请求瞬间，版本切换，导致用户看到的礼物是之前的等级，而写入的是新的等级
  rpc AddUserLevelupPresentExp(UidItemVersionReq) returns (UserLevelExp) {}

  //分页查询父级升级礼物
  rpc GetLevelupParentPresent(OffsetTypeReq) returns (LevelupParentPresentList) {}

  //查看父级下面的子升级礼物
  rpc GetLevelupChildrenPresent(ItemReq) returns (LevelupChildPresentList) {}

  //获取父级礼物的全部配置信息
  rpc GetLevelupParentPresentData(ItemReq) returns (LevelupParentPresentAllData) {}

  //根据ID获取子级礼物配置，无缓存，后台管理用
  rpc GetLevelupChildPresentData(ItemReq) returns (LevelupChildPresentData) {}

  //增加礼物等级达到人数，并返回是否推送
  rpc AddLevelupPerson(ItemLevelReq) returns (PushRes) {}

  //获取升级rank
  rpc GetLevelupUserRank(LevelupUserRankReq) returns (LevelupUserRankRes) {}
}

message EmptyMsg {
}

message ItemReq {
  uint32 item_id = 1;
}

message ItemLevelReq {
  uint32 item_id = 1;
  uint32 level = 2;
  uint32 version = 3;
  uint32 uid = 4;
}

message ItemRes {
  uint32 item_id = 1;
}

message PushRes {
  bool push = 1;
  uint32 rank = 2;
}

message ItemVersionReq {
  uint32 item_id = 1;
  uint32 version = 2;
}

message OffsetTypeReq {
  uint32 offset = 1;
  uint32 limit = 2;
  int32 present_type = 3;
}

message UidReq {
  uint32 uid = 1;
}

message UidItemReq {
  uint32 uid = 1;
  uint32 parent_item_id = 2;
}

message UidItemVersionReq {
  uint32 uid = 1;
  uint32 item_id = 2;
  uint32 version = 3;
  uint32 item_count = 4;
  string order_id = 5;
}

message UserLevelExp {
  uint32 level = 1;
  uint32 exp = 2;
}

message ItemBatchReq {
  uint32 item_id = 1;
  uint32 batch_count = 2;
}

message LevelupParentPresentData {
  uint32 parent_item_id = 1;
  uint32 present_type = 2;
  uint32 current_version = 3;
  string zip_url = 4;
  uint32 create_time = 5;
  uint32 update_time = 6;
  string color_1 = 7;
  string color_2 = 8;
  string levelup_bg = 9;
  string levelup_number = 10;
  string levelup_level = 11;
  string cms_suffix = 12;
}

message LevelupParentPresentAllData {
  uint32 parent_item_id = 1;
  uint32 present_type = 2;
  uint32 current_version = 3;
  string zip_url = 4;
  uint32 create_time = 5;
  uint32 update_time = 6;
  repeated LevelupChildPresentData level_list = 7;
  string color_1 = 8;
  string color_2 = 9;
  string levelup_bg = 10;
  string levelup_number = 11;
  string levelup_level = 12;
  string cms_suffix = 13;
}

message LevelupChildPresentData {
  uint32 item_id = 1;
  uint32 parent_item_id = 2;
  uint32 level = 3;
  uint32 exp = 4;
  uint32 create_time = 5;
  uint32 update_time = 6;
}

message LevelupPresentBatchData {
  uint32 item_id = 1;
  uint32 batch_count = 2;
  string effect_url = 3;
  string effect_md5 = 4;
  string effect_desc = 5;
  uint32 create_time = 6;
  uint32 update_time = 7;
}

message LevelupPresentBatchList {
  repeated LevelupPresentBatchData list = 1;
}

message LevelupPresentBatchDataMap {
  map<uint32, LevelupPresentBatchList> batch_map = 1;
}

message VersionData {
  uint32 version = 1;
  uint32 create_time = 2;
}

message VersionList {
  repeated VersionData list = 1;
}

message UserLevelupPresentStatusMap {
  map<uint32, UserLevelExp> map = 1;
}

message LevelupPresentParentMap {
  map<uint32, LevelupParentPresentAllData> map = 1;
}

message LevelupParentPresentList {
  uint32 total = 1;
  repeated LevelupParentPresentData list = 2;
}

message LevelupChildPresentList {
  repeated LevelupChildPresentData list = 1;
}

message LevelupUserRankReq {
  uint32 item_id = 1;
  uint32 level = 2;
  uint32 version = 3;
  uint32 offset = 4;
  uint32 limit = 5;
}

message UserRank {
  uint32 uid = 1;
  uint32 rank = 2;
  uint32 create_time = 3;
}

message LevelupUserRankRes {
  repeated UserRank rank_list = 1;
}

