syntax = "proto3";


option go_package = "golang.52tt.com/protocol/services/glory-celebrity";
package glory_celebrity;

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";


//名流殿堂内部协议

service GloryCelebrityService {

    //获取名流周榜信息
    rpc GetCelebrityWeekRank (GetCelebrityWeekRankReq) returns (GetCelebrityWeekRankRsp);

    //获取名流殿堂-顶部信息(最强，最新)
    rpc GetCelebrityPalaceTopInfo (GetCelebrityPalaceTopInfoReq) returns (GetCelebrityPalaceTopInfoRsp);
    //获取名流殿堂-分页列表
    rpc GetCelebrityPalaceInfoList (GetCelebrityPalaceInfoListReq) returns (GetCelebrityPalaceInfoListRsp);

    //名流殿堂-回顾
    rpc ReplayCelebrityPalace (ReplayCelebrityPalaceReq) returns (ReplayCelebrityPalaceRsp);

    rpc ReportFeishu (ReportFeishuReq) returns (ReportFeishuRsp);

    //礼物->名流殿堂对账
    rpc TimeRangeCount (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.CountResp ) {}
    rpc TimeRangeOrderIds (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.OrderIdsResp ) {}
    //补单名流殿堂
    rpc FixCelebrityPalaceOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
}


//名流周榜用户信息
message CelebrityRankUser {
    uint32 uid       = 1;
    uint32 score     = 2;  //荣耀星钻数量
}

//获取名流周榜信息
message GetCelebrityWeekRankReq {
    uint32 uid     = 1;    // 用户uid
    uint32 time    = 2;    // 获取周期时间
    bool   only_rank = 3;  // 仅返回榜单
}
message GetCelebrityWeekRankRsp {
    repeated  CelebrityRankUser rank_list = 1;
    uint32  my_score = 2;                 //我的星钻
    bool      is_filter = 3;               //自己是否被过滤
}

//名流堂用户
message CelebrityPlaceUser {
    uint32 uid                 = 1;     //用户uid
    string ukw_account         = 2;     //神秘人帐号(为空，不是神秘人)
    string ukw_nickname        = 3;     //神秘人呢称
}

enum CelebrityPlaceType {
    Nornal      = 0;  //普通
    New         = 1;  //最新
    Best        = 2;  //最强
}
// 名流殿堂-信息
message CelebrityPalaceInfo {
    CelebrityPlaceUser user  = 1;     //用户信息
    uint32 celebrity_id      = 2;     //名流id
    uint32 uid               = 3;     //用户uid
    uint32 rank              = 4;     //第几位达成
    uint32 ts                = 5;     //达成时间戳
    uint32 sum               = 6;     //累计送礼个数
    uint32 type              = 7;     //类型:CelebrityPlaceType
    uint32 last_ts           = 8;     //最新送礼时间
    uint32 last_num          = 9;     //最新送礼数量
}

//获取名流殿堂-顶部信息(最强，最新)
message GetCelebrityPalaceTopInfoReq {
    bool   is_cur_periods    = 1;     // 是否是当期
    uint32 gift_id           = 2;     // 名流典藏礼物id(1期只有一种礼物，传0会使用默认配置的礼物)
    uint32   uid             = 3;     //操作者uid
}
message GetCelebrityPalaceTopInfoRsp {
    repeated CelebrityPalaceInfo  pos_info = 1;           //最新、最强
    uint32   curr_gift_id                  = 2;           //当前礼物id
    repeated uint32 gift_id_list           = 3;           //可选礼物ID列表
    CelebrityPalaceInfo  self_info         = 4;           //自己的信息，如果celebrity_id=0. 未达成
}

//获取名流殿堂-分页列表
message GetCelebrityPalaceInfoListReq {
    bool   is_cur_periods    = 1;     // 是否是当期
    uint32 gift_id           = 2;     // 名流典藏礼物id(1期只有一种礼物，传0会使用默认配置的礼物)
    uint32 last_celebrity_id = 3;     //上一页最后一位的id
    uint32 limit             = 4;
}
message GetCelebrityPalaceInfoListRsp {
    repeated CelebrityPalaceInfo info_list = 2;           //其它列表
}


//名流殿堂-回顾
message ReplayCelebrityPalaceReq {
    uint32   uid           = 1;     //操作者uid
    uint32   celebrity_id  = 2;     //名流id
    bool   is_cur_periods  = 3;   // 是否是当期
}
message ReplayCelebrityPalaceRsp {
    CelebrityPlaceUser   send_user    = 1;    //送礼者
    CelebrityPlaceUser   to_user      = 2;    //收礼者 
    uint32   gift_id                  = 3;    //礼物 id
    uint32   ts                       = 4;    //送礼时间
    uint32   gift_num                 = 5;    //送礼数量
    string   show_cid                 = 6;    //送礼所在房间展示id
}

message ReportFeishuReq {
	uint32   ts                       = 1;    //时间
}

message ReportFeishuRsp {
	uint32   ts                       = 1;    //时间
}
