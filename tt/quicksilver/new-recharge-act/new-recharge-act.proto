syntax = "proto3";

package new_recharge_act;
import "tt/quicksilver/extension/options/options.proto";
option go_package = "golang.52tt.com/protocol/services/new-recharge-act";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service NewRechargeAct {
    option (service.options.service_ext) = {
        service_name: "new-recharge-act"
      };

    // 获取用户首充活动状态
    rpc GetUserFirstRechargeActStatusInfo(GetUserFirstRechargeActStatusReq) returns (GetUserFirstRechargeActStatusResp) {}
    // 获取首充活动弹窗信息
    rpc GetNewRechargeActPopupInfo(GetNewRechargeActPopupInfoReq) returns (GetNewRechargeActPopupInfoResp) {}
    // 模拟首充事件（测试）
    rpc TestFirstRechargeEvent(TestFirstRechargeEventReq) returns (TestFirstRechargeEventResp) {}
    // 清除用户首充任务状态（测试）
    rpc ClearUserFirstRechargeStatus(ClearUserFirstRechargeStatusReq) returns (ClearUserFirstRechargeStatusResp) {}

    // ======================== 对账接口 ===============================
    // 发放包裹数据对账
    rpc GetAwardTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetAwardOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
}

// 获取用户首充活动状态
message GetUserFirstRechargeActStatusReq {
    uint32 uid = 1;
    bool with_act_banner = 2;
}

message FirstRechargeBannerInfo {
    string banner_url = 1;
    string jump_url = 2;
}

message GetUserFirstRechargeActStatusResp {
    bool is_act_open = 1;
    bool is_first_recharge_fin = 2;// 是否完成首充
    int64 finish_ts = 3; // 首充活动结束时间
    FirstRechargeBannerInfo banner_info = 4;
}

// 首充奖励信息
message RechargeActAwardInfo {
    enum ItemType {
        ITEM_TYPE_UNSPECIFIED = 0;
        ITEM_TYPE_TBEAN = 1;        // T豆
        ITEM_TYPE_RED_DIAMONDD = 2; // 红钻
        ITEM_TYPE_GOLD_DIAMOND_WITH_BOUNS = 3; // 红钻加成（魅力礼物）
        ITEM_TYPE_HEADWEAR = 4;     // 麦位框
        ITEM_TYPE_NAMEPLATE= 5;     // 个人铭牌
        ITEM_TYPE_HORSE= 6;         // 虚拟装扮（天）
        ITEM_TYPE_OFFICIAL_CERT= 7; // 大v认证
        ITEM_TYPE_USER_DECORATION= 8; // 主页飘
        ITEM_TYPE_MEDAL= 9;         // 勋章
    }
    uint32 item_type = 1;
    string award_id = 2;
    uint32 amount = 3;
    string name = 4;
    string static_img = 5;
    string gif_url = 6;
    string gift_type_desc = 7;
}

// 首充档位信息
message RechargeActLevelInfo {
    uint32 level_id = 1;
    float price = 2;        // 对应单位金额（RMB)
    string set_desc_url = 3; // 套装描述图片
    uint32 bean_saved = 4;   // 节省T豆
    string product_id = 5;   // 对应的商品id
    repeated RechargeActAwardInfo award_list = 6;
}

// 获取首充活动弹窗信息
message GetNewRechargeActPopupInfoReq{
    uint32 uid = 1;
}

message GetNewRechargeActPopupInfoResp{
    repeated RechargeActLevelInfo info_list = 1;
    string rule_url = 2; // 规则页链接
}

message TestFirstRechargeEventReq {
    uint32 uid = 1;
    uint32 recharge_price = 2;
    bool is_reissue = 3;
    string reissue_secret = 4;
}

message TestFirstRechargeEventResp {
}

message ClearUserFirstRechargeStatusReq {
    uint32 uid = 1;
}

message ClearUserFirstRechargeStatusResp {
}