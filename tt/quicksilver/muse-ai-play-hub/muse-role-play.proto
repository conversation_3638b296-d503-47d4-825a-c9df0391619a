syntax = "proto3";

package muse_ai_play_hub;

option go_package = "golang.52tt.com/protocol/services/muse-ai-play-hub";

/*运营后台*/
message UpsertRolePlayCpRequest {
    RolePlayCpRelation role_play_cp_relation = 1;
}

message RolePlayCpRelation {
  string cp_id = 1; // cp_id
  string cp_name = 2; // cp名称
  uint32 cp_status = 3; // cp状态  ---CPRelationStatus
  uint32 sort=4; //排序
  bool is_display =5; //是否展示
  repeated RolePlayRoleInfo role_info_list = 6; // 角色信息列表
  string plot_background_url=7;  //剧情背景图
}
enum CPRelationStatus {
  CPRelationStatusUnspecified = 0;
  //正常
  CPRelationStatusNormal = 1;
  //删除
  CPRelationStatusDeleted = 2;
}

message RolePlayRoleInfo {
  string role_id = 1; // 角色id
  string role_name = 2; // 角色名称
  uint32 role_type = 3; // 角色类型   ---RoleType
  string role_pic_url = 4; // 角色图片
}
enum RoleType {
  RoleTypeUnspecified = 0;
  //男
  RoleTypeMan = 1;
  //女
  RoleTypeWoman = 2;

}

message UpsertRolePlayCpResponse {
}

// 获取cp关系列表
message GetRolePlayCpListRequest{
}

message GetRolePlayCpListResponse{
  repeated RolePlayCpRelation role_play_cp_relation_list = 1; // cp列表
}

message MoveRolePlayCpRequest{
  repeated  string cp_id_list = 1; // cp_id列表
}

message MoveRolePlayCpResponse{

}


/*运营后台*/


// CreateMyRoleCardRequest 创建我的角色卡请求
message CreateMyRoleCardRequest {
  string card_id = 1; // 卡片id
  string cp_id = 2; // cp_id
  string role_id = 3; // 角色id
  string introduce = 4; // 介绍
  repeated string pic_url_list = 5; // 图片url列表
}

// CreateMyRoleCardResponse 创建我的角色卡响应
message CreateMyRoleCardResponse {
  string card_id = 1; // 卡片id
}



//在线消息

/* event */

message MuseRolePlayRcmdEvent{
  uint32 role_play_event_type = 1; // 角色扮演事件类型 RolePlayEventType
  bytes role_play_event = 2; // 角色扮演事件 RolePlayEventType
}

enum RolePlayEventType{
  RolePlayEventTypeUnspecified = 0; // 未指定
  RolePlayEventTypeCreateUserCard = 1; // 角色扮演创建用户卡片事件 RolePlayCreateUserCardEvent
  RolePlayEventTypeUpdateUserCard = 2; // 角色扮演更新用户卡片事件 RolePlayUpdateUserCardEvent
  RolePlayEventTypeDeleteUserCard = 3; // 角色扮演删除用户卡片事件 RolePlayDeleteUserCardEvent
  RolePlayEventTypeUserLike = 4; // 角色扮演用户喜欢/不喜欢对方角色卡事件 RolePlayCplikeUserEvent
  RolePlayEventTypeChangeRelationship = 5; // 角色扮演CP关系变更事件 RolePlayCpChangeUserRelationshipEvent
  UserOnboardingWhitelist = 6; // 用户承接白名单事件 UserOnboardingWhitelistEvent
}

// 角色扮演创建用户卡片事件
message RolePlayCreateUserCardEvent {
  uint32 uid = 1; // 用户id
  string cp_id = 2; // cp_id
  string role_id = 3; // 角色id
  string role_card_id = 4; // 角色卡id
  string introduce = 5; // 介绍
  repeated string pic_url_list = 6; // 图片url列表
  uint32 create_time = 7; // 创建时间
  bool is_temp = 8; // 是否临时卡片
}

// 角色扮演更新用户卡片事件
message RolePlayUpdateUserCardEvent{
  uint32 uid = 1; // 用户id
  string role_card_id = 2; // 角色卡id
  string introduce = 3; // 介绍
  repeated string pic_url_list = 4; // 图片url列表
  uint32 update_time = 5; // 更新时间
  bool is_temp = 6; // 是否临时卡片
}

// 角色扮演删除用户卡片事件
message RolePlayDeleteUserCardEvent{
  uint32 uid = 1; // 用户id
  string role_card_id = 2; // 角色卡id
}


message MuseRolePlayRcmdGeneralEvent{
  uint32 role_play_event_type = 1; // 角色扮演事件类型 RolePlayEventType
  bytes role_play_event = 2; // 角色扮演事件 RolePlayEventType
}




//角色扮演用户喜欢/不喜欢对方角色卡事件
message RolePlayCplikeUserEvent{
  int32 status=1;         //RolePlayUserLikeStatus
  uint32 from_uid=2;
  uint32 to_uid=3;
  string role_card_id=4;  //角色卡id
  int32 create_time=5;

}

//点击了喜欢/不喜欢
enum RolePlayUserLikeStatus {
  RolePlayUserLikeStatusUnspecified = 0; // 未指定
  RolePlayUserLikeStatusLike = 1; // 喜欢
  RolePlayUserLikeStatusDislike = 2; // 不喜欢
}

//角色扮演CP关系变更事件
message RolePlayCpChangeUserRelationshipEvent{
  int32 status=1;         //UserRelationshipStatus
  uint32 from_uid=2;
  uint32 to_uid=3;
  string cp_id=4;  //结成/解除的cp类型id
  int32 create_time=5;
}

message UserOnboardingWhitelistEvent {
  uint32 business_type = 1; // 业务类型，27-即时闪聊， 28-角色扮演
  uint32 uid = 2; // 用户id
}


enum UserRelationshipStatus{
  UserRelationshipStatusUnspecified = 0; // 未指定
  //结成cp
  UserRelationshipStatusCp = 1;
  //断开cp
  UserRelationshipStatusCpBreak = 2;
}


/* event */


// GenRoleCardIntroduceRequest 生成角色卡片介绍请求
message GenRoleCardIntroduceRequest {
  string card_id = 1; // 卡片id
  string cp_id = 2; // cp_id
  string role_name = 3; // 角色名
  string introduce = 4; // 介绍
  string role_id = 5; // 角色id
  bool is_create = 6; // 是否创建
}

message GenRoleCardIntroduceResponse {
  string card_id = 1; // 卡片id
}

// GenRoleCardPicRequest 生成角色卡片图片请求
message GenRoleCardPicRequest {
  string card_id = 1; // 卡片id
  string cp_id = 2; // cp_id
  string role_name = 3; // 角色名
  string style = 4; // 风格
  string introduce = 5; // 介绍
  string reference_pic = 6; // 参考图片
  string role_id = 7; // 角色id
  bool is_create = 8; // 是否创建
}

message GenRoleCardPicResponse {
  string card_id = 1; // 卡片id
}

// GetMyRoleCardGenResultRequest 获取我的简介/形象生成结果
message GetMyRoleCardGenResultRequest {
  string card_id = 1; // 卡片id
  bool is_last_request = 2; // 是否最后一次请求
  string style = 3; // 图片风格
}

message GetMyRoleCardGenResultResponse {
  string introduce = 1; // 介绍
  string pic_url = 2; // 图片url
}

// GetCreateRoleCardInfoRequest 获取创建角色卡信息请求
message GetCreateRoleCardInfoRequest {
}

message GetCreateRoleCardInfoResponse {
  uint32 gen_pic_remain_count = 1; // 剩余生成图片次数
  uint32 create_card_remain_count = 2; // 剩余创建卡片次数
  uint32 user_gender = 3; // 用户性别
}

// GetMyRoleCardRequest 获取我的角色卡
message GetMyRoleCardRequest {
}

message GetMyRoleCardResponse {
  repeated RoleCard role_card_list = 1; // 角色卡列表
}

// DeleteMyRoleCardRequest 删除我的角色卡
message DeleteMyRoleCardRequest {
  string card_id = 1; // 卡片id
}

message DeleteMyRoleCardResponse {}

// UpdateMyRoleCardRequest 更新我的角色卡
message UpdateMyRoleCardRequest {
  string card_id = 1; // 卡片id
  string introduce = 2; // 介绍
  repeated string pic_url_list = 3; // 图片url列表
  bool is_change = 4; // 是否发送图片更换
}

message UpdateMyRoleCardResponse {}


//获取可用的cp关系列表

message GetAvailableRolePlayCpListRequest{

}

message GetAvailableRolePlayCpListResponse{
  repeated RolePlayCpRelation role_play_cp_relation_list = 1; // cp列表
}

// 角色卡
message RoleCard {
  string cp_id = 1; // cp id
  string cp_name=2; //cp名称
  string role_id = 3; // 角色id
  string card_id = 4; // 卡片id
  uint32 uid = 5; // 角色卡用户uid
  string my_role_name = 6; // 角色名
  string target_role_name = 7; // 目标角色名
  string introduce = 8; // 介绍
  repeated string pic_url_list = 9; // 图片url列表
  uint32 status = 10; // 状态
  bool is_display=11; //是否可展示
  bool is_like_me=12;//
  uint32 is_temporary = 13; // 是否临时卡片
}

enum RoleCardStatus {
  RoleCardStatusUnspecified = 0; // 未指定
  RoleCardStatusNormal = 1; // 正常
  RoleCardStatusDeleted = 2; // 删除
  RoleCardStatusDisplay = 3; // 展示
  RoleCardStatusNotDisplay = 4; // 不展示
}

//批量获取角色卡信息
message BatchGetRoleCardRequest{
      repeated string card_id_list=1;
}

message BatchGetRoleCardResponse{
  repeated RoleCard role_card_list = 1;
}

// 用户卡片下一个/组CP请求
message RolePlayUserLikeRequest{
  string role_card_id = 1; // 角色卡id
  uint32 like_type = 2; // RolePlayUserLikeType
  uint32 uid = 3;
}

message RolePlayUserLikeResponse{
  bool is_create_new_role_card=1;
  string cp_id=2;
  string role_id=3;
  string role_name=4;
}

enum RolePlayUserLikeType {
  ROLE_PLAY_USER_LIKE_TYPE_UNSPECIFIED=0;// 未指定
  ROLE_PLAY_USER_LIKE_TYPE_NEXT=1;// 下一个
  ROLE_PLAY_USER_LIKE_TYPE_CP=2; // 组CP
  ROLE_PLAY_USER_LIKE_TYPE_UNLIKE=3;  //不喜欢
  ROLE_PLAY_USER_LIKE_TYPE_SAY_HELLO=4;//打招呼
}

message GetRolePlayReceiveCpListRequest{
  string role_card_id =1;
  string offset=2;
  int32 limit=3;
  bool  is_load_clicked_data=4;
}

message GetRolePlayReceiveCpListResponse{
  repeated  RolePlayReceiveCpInfo receive_cp_info_list = 1;  //请求匹配列表
  string offset=2;
  bool  is_load_clicked_data=3;
}

message RolePlayReceiveCpInfo{
  uint32 uid=1;
  string role_card_id = 2; // 角色卡id
  string cp_name = 3; // cp名称
  string role_name = 4; // 角色名称
  string like_label = 5; // 点赞标签
  string introduce = 6; // 介绍
  repeated string pic_url_list = 7; // 图片url列表
  string distance_text = 8; // 距离:单位米
  bool is_new_version=9; //是否是新版本
  bool is_clicked=10;
  string id=11;
}

message GetRolePlayNewReceiveCPMessageRequest{
    uint32 uid=1;
}

message GetRolePlayNewReceiveCPMessageResponse{
  RolePlayUserInfo remind_info=1;

}

message RolePlayUserInfo{
  uint32 uid=1;
  string role_name=2;
  string role_intro=3;
  string first_pic=4;
  string role_plot_content=5;
  string role_plot_target=6;
}

message GetRolePlayUserCpRelationRequest{
    uint32 uid_a=1;
    uint32  uid_b=2;
}

message GetRolePlayUserCpRelationResponse{
  bool is_cp_relation=1;
  string cp_name=2;
  repeated  RolePlayUserInfo user_info=3;

}

message GetRolePlayBindCpRelationBackgroundPicRequest{
  uint32 target_uid=1;
}

message GetRolePlayBindCpRelationBackgroundPicResponse{
  string background_pic_url=1;
}



/*运营后台二期*/
message GetRolePlayPlotLibraryRequest{
  string relation_id=1;   //绑定关系id
  string role_id=2;       //角色id
  string plot_content=3;  //剧情内容
  uint32 display_status=4;   //是否展示
  int64  offset_id=5;
  int64  limit=6;
  repeated string plot_id_list=7;
}

message GetRolePlayPlotLibraryResponse{
  repeated RolePlayPlotLibrary plot_libraries=1;
  int64 total=2; //>10000加log(服务端)
}

enum RolePlayPlotDisplayStatus{
  ROLE_PLAY_PLOT_DISPLAY_STATUS_UNSPECIFIED=0;
  ROLE_PLAY_PLOT_DISPLAY_STATUS_DISPLAY=1;
  ROLE_PLAY_PLOT_DISPLAY_STATUS_HIDE=2;
}


message DelRolePlayPlotLibraryRequest{
  repeated string id_list=1;

}


message DelRolePlayPlotLibraryResponse{

}

message UpsertRolePlayPlotLibraryRequest{
  repeated RolePlayPlotLibrary plot_libraries=1;
}

message UpsertRolePlayPlotLibraryResponse{

}

message RolePlayPlotLibrary{
  string id=1;
  string relation_id=2;          //关系id
  string relation_name=3;        //关系名
  repeated RolePlot role_plot_list=4; //角色信息
  uint32 display_status=5;     //是否展示

}

message RolePlot{
  string role_id=1;    //角色id
  string role_name=2;  //角色名
  string plot_content=3;  //剧情内容
  string target=4;      //目标
}



/*运营后台二期*/



message GetRolePlaySendCpListRequest{
  string role_card_id =1;
  string offset=2;
  int32 limit=3;

}


message GetRolePlaySendCpListResponse{
  repeated  RolePlayReceiveCpInfo send_cp_info_list = 1;  //请求匹配列表
  string offset=2;
}

message GetUserSayHelloRemainTimesRequest{
  uint32 uid=1;

}

message GetUserSayHelloRemainTimesResponse{
  int32 remain_times=1;
}

message RespDataRolePlaySayHello {
  RolePlayUserInfo role_play_from_user_info = 1; // 角色信息RolePlayUserInfo
  RolePlayUserInfo role_play_to_user_info = 2; // 角色信息RolePlayUserInfo
    string cp_name = 3;
}


message HiddenRolePlaySayHelloRecordRequest{
  string id=2;
  uint32 source_type=3;  //RolePlaySayHelloRecordType
}


message HiddenRolePlaySayHelloRecordResponse{


}

enum RolePlaySayHelloRecordType{
  ROLE_PLAY_SAY_HELLO_RECORD_TYPE_UNSPECIFIED = 0;
  ROLE_PLAY_SAY_HELLO_RECORD_TYPE_RECEIVE= 1;
  ROLE_PLAY_SAY_HELLO_RECORD_TYPE_SEND = 2;
}


message ReportRolePlayUserClickedCardRequest{
  string role_card_id=1; //废弃
  string id =2;
}

message ReportRolePlayUserClickedCardResponse{
}


message RebuildRolePlayUserUnClickedStatusRequest{}

message RebuildRolePlayUserUnClickedStatusResponse{}
