syntax = "proto3";

package muse_ai_play_hub;

option go_package = "golang.52tt.com/protocol/services/muse-ai-play-hub";


message RemFirstChatListRequest{
  repeated string chat_id=1;
}

message RemFirstChatListResponse{}

message GetInspirationChatSourceRequest{
  uint32 from_uid=1;
  uint32 to_uid=2;
}
message GetInspirationChatSourceResponse{
      int32 chat_source=1;
 }

message GetChatHistoryMessageCountRequest{
    uint32 user_a=1;
    uint32 user_b=2;
}

message GetChatHistoryMessageCountResponse{
  int32 count=1;
}

message BatchGuideFollowChatListRequest{

}

message BatchGuideFollowChatListResponse{
  repeated string guide_follow_chat_list=1;
}

message BatchFirstChatListRequest{

}

message BatchFirstChatListResponse{
  repeated string first_chat_list=1;
}

message UpdateAIPlayHubStatusRequest{
  uint32 from_uid=1;
  uint32 to_uid=2;
  int32 switch_status=3;
}

message UpdateAIPlayHubStatusResponse{
}


message IsHitChattingSourceRequest{
  repeated uint32 uids = 1;
}

message IsHitChattingSourceResponse{
    bool hit_chatting_source=1;
}

message GetLastedHistoryMessageRequest{
  repeated  uint32 uids=1;
  int32 count=2;
}

message ChatHistoryMessage{
  string message=1;
  uint32 from_uid=2;
  string from_nickname=3;
  int64 time=4;
  uint32 source=5;
}
message GetLastedHistoryMessageResponse{
    repeated ChatHistoryMessage chat_history_message=1;

}

message IncAndGetInspirationUsedTimesRequest{
  uint32 from_uid=1;
  uint32 to_uid=2;
  int32 type=3;
}

message IncAndGetInspirationUsedTimesResponse{
  int32 used_times=1;
}

message GetAiCupidSwitchStatusRequest{
  repeated  uint32 uids=1;
}

message GetAiCupidSwitchStatusResponse{
  int32 switch_status=1;
}

message GetAiInspirationRequestIntervalRequest{
  uint32   from_uid=1;
  uint32   to_uid=2;
  int32 type=3;
}

message GetAiInspirationRequestIntervalResponse{
  int32 interval=1;
}

message SetAiInspirationRequestIntervalRequest{
  uint32   from_uid=1;
  uint32   to_uid=2;
  int32 type=3;
}

message SetAiInspirationRequestIntervalResponse{

}

message GetInspirationUsedTimesRequest{
  uint32 from_uid=1;
  uint32 to_uid=2;
  int32 type=3;
}

message GetInspirationUsedTimesResponse{
  int32 used_times=1;
}

// 测试接口
message AIPlayHubTestRequest{
  string cmd = 1; /* Add GuideTopic */
  uint32 uid = 2;
  uint32 target_uid = 3;
}

message AIPlayHubTestResponse{
}