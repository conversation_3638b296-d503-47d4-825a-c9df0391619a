syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/contract-http-logic";
package contract_http_logic;




message GetHallTaskResp {
  repeated HallTaskDetial day_task_list = 1; // 日任务
  repeated HallTaskDetial week_task_list = 2;// 周任务
  string reward_msg = 3; // 任务奖励说明
  bool is_show = 4; // 为false则不展示
}
message HallTaskDetial {
  string task_name = 1; // 任务名称
  string task_progress = 2; // 任务进度  例如 1h20min/2h
  float rate = 3; // 进度比率
  uint32 task_val = 4; // 已完成第几个梯度
  repeated string val_list = 5; //梯度数值
  string date = 6; // 当日日期
}


message GetHallTaskHistoryResp {
  repeated HallTaskHistoryDetial list = 1;
}
message HallTaskHistoryDetial {
  string date = 1; // 自然周
  repeated HallTaskDetial day_task_list = 2;
  repeated HallTaskDetial week_task_list = 3;
}

message GetESportCoachInfoReq {
  uint32 guild_id = 1; // 公会id
  uint32 coach_uid = 2; // 电竞指导uid
}
message UserSkillItem {
  uint32 game_id = 1; // 游戏id
  string game_name = 2; // 游戏名称
  string game_icon = 3; // 游戏图标
  uint32 game_type = 4; // 游戏类型
  uint32 type_sort = 5; // 游戏类型排序
}
message ESportMonthData {
  uint64 order_amount = 11; // 总收益
  uint64 total_orders = 12; // 总订单数
  uint32 take_order_days = 13; // 接单天数
  uint32 active_days = 14; // 活跃天数
  uint32 people_served = 15; // 服务人数
  uint32 new_customers = 16; // 新客户数
  uint32 repurchases = 17; // 复购数
  uint32 violations_a = 18; // A类违规次数
  uint32 violations_b = 19; // B类违规次数
  uint32 violations_c = 20; // C类违规次数
}
message GetESportCoachInfoResp {
  uint32 guild_id = 1; // 公会id
  uint32 guild_short_id = 2; // 公会短id
  string guild_name = 3; // 公会名称
  uint32 coach_uid = 4; // 电竞指导uid
  string coach_ttid = 5; // 电竞指导ttid
  string coach_nick = 6; // 电竞指导昵称
  int64 contract_begin_time = 7; // 合约开始时间
  int64 contract_end_time = 8; // 合约结束时间
  // 技能
  repeated UserSkillItem skill_list = 9; // 技能列表
  // 数据
  ESportMonthData this_month_data = 21; // 本月数据
}

//段位等信息
message SectionInfo {
  string section_name = 1;        // 资料名称（段位信息、擅长位置、擅长英雄...）
  repeated string item_list = 2;  // 填写项
  uint32 section_id = 3;          // section_id
}
//用户游戏资料信息
message UserSkillInfo {
  uint32 game_id = 1;
  string game_name = 2;                              // 游戏名称
  string skill_evidence = 3;                         // 技能图
  string skill_desc = 4;                              // 技能图说明
  string audio = 5;                                   // 语音介绍
  uint32 audio_duration = 6;                          // 语音介绍时长(秒数)
  repeated SectionInfo section_list = 7;             // 技能详细信息
  string text_desc = 8;
  uint32 game_rank = 9;                                //游戏排名
}

message GetESportUserSkillInfoReq {
  uint32 coach_uid = 1; // 电竞指导uid
  uint32 game_id = 2; // 游戏id
}
message GetESportUserSkillInfoResp {
  uint32 coach_uid = 1; // 电竞指导uid
  uint32 game_id = 2; // 游戏id
  UserSkillInfo skill_info = 3; // 技能信息
}

//申请签约身份前置检查
message SignIdentityPreCheckReq {
  uint32 guild_id = 1;  // 公会id
  uint32 identity_type = 2; // 签约身份 see anchorcontract-go.proto SIGN_ANCHOR_IDENTITY
}
message SignIdentityPreCheckResp {
}

//申请签约-触发风控事件检查
message ApplySignRiskCheckReq {
  uint32 identity_type = 1; // 签约身份 see anchorcontract-go.proto SIGN_ANCHOR_IDENTITY
  string device_id = 2;
  uint32 client_type = 3;
  uint32 app_version = 4;
  string face_auth_result_token = 5; // 人脸
  uint32 guild_id = 6; // 公会id(多人，个播签约时需要)
}
message ApplySignRiskCheckResp {
  // 人脸context
  string face_auth_context_json = 1;
}

message ApplySignReq {
  uint32 identity_type = 1; // 签约身份 see anchorcontract-go.proto SIGN_ANCHOR_IDENTITY
  repeated string contract_names = 2; //签属协议名称
}
message ApplySignResp {
}


// 公会业务类型
message GuildBusiness {
  string name = 1; // 公会名称
  uint32 type = 2; // 公会类型
  bool available = 3; // 是否可用
}

// 获取公会信息
message TopGuildInfo {
  uint32 guild_id = 1; // 公会id
  uint32 short_id = 2;  // 公会短id
  string name = 3; // 公会名称
  string guild_tag = 4; // 公会简介
  string owner_name = 5; // 公会所有者名称
  string owner_account = 6; // 公会所有者账号
  string owner_alias = 7; // 公会所有者别名
  uint32 owner_uid = 8; // 公会所有者uid
  repeated GuildBusiness business_list = 9; // 公会业务列表

  //
  uint32 rank = 10; // 排名
  string recommend_tag = 11;    // 推荐标签
  string ability_tag = 12 [deprecated = true];      // 能力标签
  string honor_title = 13;      // 荣誉称号
  int64 from_time = 14;         // 开始时间
  int64 to_time = 15;           // 结束时间
  repeated string ability_tag_list = 16;      // 能力标签列表
}

// 获取推荐置顶公会列表
message GetRecommendTopGuildListReq {

}

message GetRecommendTopGuildListResp {
  repeated TopGuildInfo guild_list = 1; // 公会列表
}



 //获取解约方式
message GetCancelContractTypesReq {
  uint32 guild_id = 1;  // 公会id
  uint32 scene    = 2;  // 场景  0:签约 1:解约
  uint32 worker_type = 3; // 签约时，需要传类型
}
message CanCancelContract {
  uint32 cancel_type = 1; // 解约方式 see anchorcontract-go.proto CancelContractType
  bool   enabled = 2;     // 是否可用
  string name    = 3;     // 名称
  string desc    = 4;     // 描述
  bool show_but_disable = 5;   // 显示但不可选
  string disable_msg = 6; // 不可选的报错提示
  bool is_new = 7; // 是否是新增的解约方式，普通成员转合约成员时使用
}
message GetCancelContractTypesResp {
  repeated CanCancelContract cancel_types = 1; // 解约方式
  int64   pay_cost = 2;     //付费解约金额(单位分)
  }

//申请解约
message ApplyCancelContractReq {
   uint32 guild_id=1;    //公会id
   uint32 cancel_type=2; //解约方式
   repeated string proof_urls =3; //证据url
   string pay_desc = 4; //付费解约描述
   repeated ProofContent proof_list = 5; //证据列表，图片/视频
   string cancel_reason = 6; //解约原因
   repeated string negotiate_reason_type = 7; // 协商解约类型
}

message ProofContent {
  enum ProofType{
    ProofType_Invalid = 0;
    ProofType_Video = 1; //视频
    ProofType_Image = 2; //图片
  }
  string key = 1; // 证据key
  string censor_key = 2; // 审查通过的key，用于在提交视频时确认该视频已经通过审核
  ProofType type = 3; // 证据类型
}

message ProofShowContent {
  enum ProofType{
    ProofType_Invalid = 0;
    ProofType_Video = 1; //视频
    ProofType_Image = 2; //图片
  }
  string url = 1;
  ProofType type = 2; // 证据类型
}

message ApplyCancelContractResp {
}

message LockPayCancelAmountReq {
  uint32 guild_id = 1; // 公会id
}
message LockPayCancelAmountResp {
  int64 pay_cost = 1; //付费解约金额(单位分)
  int64 expire_ts = 2; //过期时间戳
  int64 start_ts = 3;  //开始时间戳
}
// 检查是否可以解约
message CheckCanCancelContractReq {
  uint32 guild_id = 1; // 公会id
}
message CheckCanCancelContractResp {
  int64 lock_pay_cost = 1; //>0有锁定付费解约金额(单位分)
  int64 lock_expire_ts = 2; //过期时间戳
  int64 lock_start_ts = 3;  //开始时间戳
  enum CancelStage {
    CancelContractTypeDefault = 0;
    CancelContractTypeWaitingForMaster = 1; // 等待会长处理
    CancelContractTypeWaitingForOfficial = 2; // 等待官方处理
    CancelContractTypeWaitingForMasterQuiet = 3; // 低收入解约 - 等待会长处理
    CancelContractTypeWaitingForMasterNoReason = 4; // 无理由解约 - 等待会长处理
    CancelContractTypeWaitingForMasterLive = 5; // 直播解约 - 等待会长处理
    CancelContractTypeWaitingForOfficialPay = 6; // 付费解约 - 等待官方处理
    CancelContractTypeWaitingForMasterPayAccept = 7; // 付费解约 - 等待会长同意
    CancelContractTypeWaitingForMasterNegotiateAccept = 8; // 协商解约 - 等待会长同意
  }
  CancelStage cancel_stage = 4; // 解约阶段
  int64 stage_expire_ts = 5; //阶段过期时间戳
  string reject_reason = 6; // 拒绝原因
  repeated ProofShowContent proof_list = 7; //证据列表，图片/视频
}

// 获取解约申请列表
message GetApplyCancelContractListReq {
  uint32 guild_id = 1; // 公会id
  uint32 page = 2;
  uint32 page_num = 3;
}
message ApplyCancelContractItem {
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 cancel_type = 3; // 解约方式
  string account = 4;
  string nickname = 5;
  string alias = 6;
  uint32 apply_time = 7; // 申请时间戳
  uint32 end_time = 8; // 结束时间戳
  repeated uint32 anchor_identity_list = 9; //申请的签约身份
  string reason = 10; // 解约原因
  bool   show_accept_btn = 11; //是否展示 同意
  bool   show_reject_btn = 12; //是否展示 拒绝
  repeated ProofShowContent proof_list = 13; //证据列表，图片/视频
  string cancel_reason = 14; //解约原因
  repeated string negotiate_reason_type = 15; // 协商解约类型
}

message GetApplyCancelContractListResp {
  repeated ApplyCancelContractItem list = 1;
  uint32 next_page = 2;
}

message GetNegotiateReasonTypeReq {
}

message GetNegotiateReasonTypeResp {
  repeated string negotiate_reason_type = 1; // 可选的协商解约类型
}

// 同意解约
message AcceptApplyCancelContractReq {
  uint32 target_uid = 1;
  uint32 guild_id = 2;
}
message AcceptApplyCancelContractResp {
}

// 拒绝解约
message RejectApplyCancelContractReq {
  uint32 target_uid = 1;
  uint32 guild_id = 2;
  repeated string proof_urls = 3; // 证据url
  string  reject_reason = 4; // 拒绝原因
  repeated ProofContent proof_list = 5; //证据列表，图片/视频
}

message RejectApplyCancelContractResp {
}

message ContractWorker {
  uint32 worker_type = 1; // 从业者类型 see anchorcontract-go.proto ContractWorkerType
  string worker_name = 2; // 从业者名称
  string worker_desc = 3; // 从业者字段描述
}
message ContractPrivilege {
  string name = 1; // 权益名称
  string icon = 2; // 权益图标
  bool   is_select = 3; //是否选中
  string note = 4; // 备注
  bool is_new = 5; // 是否是新增的权益
}
message ContractPrivilegeGroup {
  string name = 1; // 权益组名称
  repeated ContractPrivilege privilege_list = 2; // 权益列表
}
message GetContractPrivilegeListReq {
  uint32 guild_id = 1; // 公会id
  uint32 worker_type = 2; // 签约时，需要传类型
}
message GetContractPrivilegeListResp {
  repeated ContractPrivilegeGroup group_list = 1; // 权益组列表
  repeated ContractWorker worker_list = 2; // 从业者列表
}

message SignLiveSubmit {
  uint32 tag = 1;
  string link = 2;
  string contact = 3;
}
// 申请签约
message ApplySignContractReq {
  uint32 guild_id = 1; // 公会id
  uint32 identity_type = 2; // 签约身份 see anchorcontract-go.proto SIGN_ANCHOR_IDENTITY
  uint32 duration = 3; // 签约时长
  uint32 worker_type = 4; // 从业者类型 see anchorcontract-go.proto ContractWorkerType
  SignLiveSubmit submit = 5; // 语音直播签约参数
}
message ApplySignContractResp {
}

//晋升信息
message PromoteInfo {
   uint32 id = 1;  //邀请id
   uint32 sign_months = 2;  // 重新签约月数
}

//签约信息
message UserContractInfo {
   uint32 worker_type = 1; // 从业者类型 see anchorcontract-go.proto ContractWorkerType
}


// 签约管理， 获取用户的签约相关信息 /contract-http-logic/contract/GetUserContractInfo
message GetUserContractInfoReq {
}
message GetUserContractInfoResp {
  PromoteInfo promote_info = 1;  //晋升邀请信息
  UserContractInfo contract_info = 2;  //签约相关信息
}

// 处理晋升邀请
message ProcPromoteInviteReq {
   //处理结果
   enum ProcResType {
      ProcResType_Invalid = 0;  //无效
      ProcResType_Agree = 1;  // 同意
      ProcResType_NO_Agree = 2;  // 拒绝
   }
   uint32 proc_res = 1;  // 处理结果 see ProcResType 
   uint32 id = 2; // 邀请id
}
message ProcPromoteInviteResp {
}


//获取公会的管理员列表
message GetGuildMgrListReq {
   uint32 guild_id = 1; // 公会id
}
message GetGuildMgrListResp {
   repeated uint32 uid_list = 1;
}

// 审核上传的视频
message CensorVideoReq {
  uint32 guild_id = 1;
  uint32 uid = 2;
  repeated string video_key = 3; // 视频key
}

message CensorVideoResp {
  repeated CensorResult censor_result = 1;
}

message CensorResult {
  string video_key = 1;  // 视频key
  string censor_key = 2; // 审查通过的key，用于在提交申请时确认该视频已经通过审核
  string transcode_url = 3; // 转码后的url，预览用
}

// 获取是否需要进行成员身份确认
message GetNeedConfirmWorkerTypeReq {
  uint32 uid = 1; // 用户id
  uint32 guild_id = 2; // 公会id
}

message GetNeedConfirmWorkerTypeResp {
  bool need_confirm = 1; // 是否需要身份确认
}

// 进行成员身份修改
message ModifyWorkerTypeReq {
  uint32 worker_type = 1; // 从业者类型 see anchorcontract-go.proto ContractWorkerType
}

message ModifyWorkerTypeResp {
}

// 新主播任务
message AnchorLevelNewTask {
  uint32 remain_time = 1;
  uint32 need_week_active_days = 2;
  uint32 remain_week_active_days = 3;
  uint32 need_week_platform_days = 4;
  uint32 remain_week_platform_days = 5;
  uint32 week_gift_value = 6;
  string anchor_check_level = 7;// 新主播考核等级
  uint32 week_num = 8;  // 目前是第几周 ， 如果是0，则显示下周一开始
  uint32 task_type = 9; // 任务类型，1:纯新达人 2:回归达人 3:新达人
}

// 查看合约权益与解约方式变更
message GetContractChangeInfoReq {
  uint32 id = 1; // 唯一id，用于查看对应的权益变更
}

message GetContractChangeInfoResp {
  repeated ContractPrivilegeGroupChange group_list = 1; // 权益组列表
  repeated CanCancelContractChange cancel_types = 2; // 解约方式
  bool is_accept = 3;  // 是否已经接受
}

message ContractPrivilegeGroupChange {
  string name = 1; // 权益组名称
  repeated ContractPrivilege privilege_list = 2; // 权益列表
  repeated ContractPrivilege removed_privilege_list = 3; // 减少的权益列表
}

message CanCancelContractChange{
  repeated CanCancelContract cancel_types = 1; // 现有的解约方式
  repeated CanCancelContract removed_cancel_types = 2; // 减少的解约方式
}
message HandleContractChangeReq {
  uint32 id = 1; // 唯一id
  bool is_agree = 2; // 是否同意
}
message HandleContractChangeResp {
}
// 查看拒绝理由
message GetRejectReasonReq {
  uint32 id = 1; // 唯一id
}
message GetRejectReasonResp {
  string reject_reason = 1; // 拒绝理由
  repeated ProofShowContent proof_list = 2; //证据列表，图片/视频
}


/////////新手主播引导相关 start/////////

// 获取新手主播信息
message GetNewbieAnchorInfoReq {
}

message IdentityObtainDay {
  uint32 identity_type = 1; // 新手主播身份 see SIGN_ANCHOR_IDENTITY
  uint32 obtain_day = 2; // 获得该身份的天数
  bool is_newbie_anchor = 3; // 是否是新手主播
}
message GetNewbieAnchorInfoResp {
  repeated IdentityObtainDay identity_list = 2;  // 新手主播身份列表
  uint32 sign_day = 3; //签约天数
 }

enum KnowledgeTaskStatus {
  KnowledgeTaskStatus_Invalid = 0; // 无效状态
  KnowledgeTaskStatus_NotFinish = 1; // 未完成
  KnowledgeTaskStatus_Finished = 2; // 已完成
}
// 知识点任务
message KnowledgeTask {
  string task_id = 1; // 任务id
  string task_name = 2; // 任务名称
  string jump_url = 3; // 跳转链接
  uint32 status = 4; // 任务状态 see KnowledgeTaskStatus
}
message NewbieGuideContent {
  string title = 1; // 标题
  string content = 2; // 内容
  repeated string urls = 3; // 配图链接
}

// 子主题信息
message NewbieGuideSubTheme {
  string theme_name = 1; // 子主题名称
  repeated NewbieGuideContent content_list = 2; // 内容列表
  repeated KnowledgeTask task_list = 3; // 知识点任务列表
}

// 主题信息
message NewbieGuideTheme {
  string theme_name_url = 1; // 主题名称URL
  repeated NewbieGuideSubTheme sub_theme_list = 2; // 子主题列表(数量为1时，没有子主题TAB)
}
// 获取新手指南信息
message GetNewbieAnchorGuideInfoReq {
  uint32 identity_type = 1; // 身份 see SIGN_ANCHOR_IDENTITY
  uint32 is_advance = 2;    //是否是进阶TAB
}
message GetNewbieAnchorGuideInfoResp {
  repeated NewbieGuideTheme theme_list = 1; // 主题列表
}

// 新手任务类型
enum NewbieAnchorTaskType {
  NEWBIE_ANCHOR_TASK_TYPE_UNSPECIFIED = 0;
  NEWBIE_ANCHOR_TASK_TYPE_BEGINNER = 1; // 入门任务
  NEWBIE_ANCHOR_TASK_TYPE_ADVANCED = 2; // 进阶任务
}

// 新手任务奖励状态
enum NewbieRewardStatus {
  NEWBIE_REWARD_STATUS_UNSPECIFIED = 0; // 未知状态
  NEWBIE_REWARD_STATUS_NOT_CLAIMED = 1; // 未领取
  NEWBIE_REWARD_STATUS_CLAIMED = 2; // 已领取
  NEWBIE_REWARD_STATUS_EXPIRED = 3; // 已过期
}

// 获取新手任务
message GetNewbieAnchorTaskReq {
  uint32 identity_type = 1; // 身份类型
}

// 新手任务项
message NewbieAnchorTaskItem {
  string task_string = 1;      // 任务名称
  string task_icon = 2;        // 任务icon
  bool   is_completed = 3;     // 是否完成
  uint32 progress = 4;         // 进度N
  uint32 total = 5;            // 完成值M (N/M)
}

// 新手任务日期
message NewbieAnchorTaskDay {
  enum Status {
    STATUS_UNSPECIFIED = 0;
    STATUS_NOT_COMPLETED = 1; // 未完成
    STATUS_COMPLETED = 2; // 已完成
    STATUS_EXPIRED = 3; // 已过期
  }
  uint64 day = 1;                               // 日期天
  Status complete_status = 2;                   // 完成状态
  repeated NewbieAnchorTaskItem task_list = 3;  // 任务列表
}

// 获取新手入门任务
message NewbieAnchorTaskBeginner {
  repeated NewbieAnchorTaskDay days = 1;           // 任务日期列表
  uint32 completed_days = 2;                       // 实际累计完成天数
  message Reward {
    uint32 condition_days = 1;                     // 累计完成天数可领取
    string chest_icon = 3;                         // 奖励宝箱图标URL
    NewbieRewardStatus claim_status = 4;           // 领取状态
    string main_text = 7; // 主文案
    string sub_text = 8; // 副文案
  }
  repeated Reward rewards = 6;                     // 奖励列表
}

// 获取新手进阶任务
message NewbieAnchorTaskAdvanced {
  uint32 period = 1;                               // 周期 1 2
  uint64 period_start_time = 2;                    // 周期开始时间
  uint64 period_end_time = 3;                      // 周期结束时间
  repeated NewbieAnchorTaskItem task_list = 4;     // 任务列表
  NewbieRewardStatus claim_status = 5;             // 领取状态
  message RewardItem {
    string reward_name = 1;      // 奖励项名称
    string reward_icon = 2;      // 奖励项图标URL
  }
  repeated RewardItem reward_items = 6; // 奖励项列表
}

message GetNewbieAnchorTaskResp {
  NewbieAnchorTaskBeginner beginner_task = 1; // 新手入门任务
  repeated NewbieAnchorTaskAdvanced advanced_task_list = 2; // 新手进阶任务列表
}

// 领取新手入门任务奖励
message ClaimNewbieAnchorTaskRewardReq {
  uint32 identity_type = 1; // 身份类型
}

// 领取新手进阶任务奖励
message ClaimNewbieAnchorTaskAdvancedRewardReq {
  uint32 identity_type = 1; // 身份类型
  uint32 period = 2; // 周期 1 2
}

// 领取新手任务奖励
message ClaimNewbieAnchorTaskRewardResp {
  repeated TaskRewardItem rewards = 2;   // 该天数下的奖励列表
}

// 单个奖励项
message TaskRewardItem {
  string reward_name = 1;      // 奖励名称，如"优质新人头像框3天"
  string reward_icon = 2;      // 奖励图标URL
  uint32 count = 3;            // 数量（目前都是 天）
}

// 奖励条件
message TaskRewardCondition {
  string condition_desc = 1;             // 条件说明，如"累计完成3天任务"
  repeated TaskRewardItem rewards = 2;   // 该天数下的奖励列表
  bool is_claimed = 3;                   // 是否已领取
  string chest_icon = 4;                 // 奖励宝箱图标URL
}

// 获取新手任务奖励列表
message GetNewbieAnchorTaskRewardListReq {
  uint32 identity_type = 1; // 身份类型
  NewbieAnchorTaskType task_type = 2; // 任务类型
}
message GetNewbieAnchorTaskRewardListResp {
  repeated TaskRewardCondition reward_condition_list = 1; // 奖励条件列表
}


enum WidgetType {
  WIDGET_TYPE_INVALID_UNSPECIFIED = 0; // 无效
  WIDGET_TYPE_NEWBIE_ANCHOR_LIVE_READY = 1; // 听听开播准备页
  WIDGET_TYPE_NEWBIE_ANCHOR_LIVE_ROOM  = 2; // 房间
}
// 新手主播引导挂件信息
message GetNewbieAnchorWidgetReq {
  uint32 widget_type = 1; // see WidgetType
  uint32 cid = 2;
}

enum WidgetStatus {
  WIDGET_STATUS_INVALID_UNSPECIFIED = 0; // 无效
  WIDGET_STATUS_NORMAL = 1; // 普通转态，无任务无奖励
  WIDGET_STATUS_HAS_TASK = 2; // 有任务，无奖励
  WIDGET_STATUS_HAS_REWARD = 3; //  有奖励
}
message GetNewbieAnchorWidgetResp {
  bool is_newbie = 1; // 是否新手， 否则不展示挂件
  uint32 identity_type = 2; // 新手身份类型 see SIGN_ANCHOR_IDENTITY
  string title = 3; // 标题
  string task_progress = 4; // 任务进度
  uint32 status = 5; // 任务状态 see WidgetStatus
  string pop_txt = 6; // 引导泡泡文本
  uint32 pop_type = 7; // 引导泡泡类型
 }

/////////新手主播引导相关 end/////////
