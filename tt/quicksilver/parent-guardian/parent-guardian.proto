syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/parent-guardian";

package parent_guardian;

service ParentGuardian {
    rpc SwitchParentGuardian(ParentGuardianSwitchReq) returns (ParentGuardianSwitchResp) {}
    rpc GetParentGuardianState(ParentGuardianStateReq) returns (ParentGuardianStateResp) {}
    rpc CheckParentGuardianPassword(ParentGuardianCheckPasswordReq) returns (ParentGuardianCheckPasswordResp) {}
    rpc ParentGuardianUpdatePassword(ParentGuardianUpdatePwdReq) returns (ParentGuardianUpdatePwdResp) {}
    rpc BatchGetParentGuardianInfo(BatchGetParentGuardianInfoReq) returns (BatchGetParentGuardianInfoResp) {}
    rpc CheckAppealCntIsOverLimit(CheckAppealCntIsOverLimitReq) returns (CheckAppealCntIsOverLimitResp) {}
}

message ParentGuardianSwitchReq {
    uint32 uid = 1;
    bool on_off = 2; // true:on;false:off
    string password = 3;
    bool is_force_off = 4; // 无需密码，强制关闭，用于忘记密码申诉成功后关闭该模式
}

message ParentGuardianSwitchResp {
}

message ParentGuardianStateReq {
    uint32 uid = 1;
}

message ParentGuardianStateResp {
    bool on_off = 2;
}

message ParentGuardianCheckPasswordReq {
    uint32 uid = 1;
    string password = 2;
}

message ParentGuardianCheckPasswordResp {
    bool is_pass = 1;
}

message ParentGuardianUpdatePwdReq {
    uint32 uid = 1;
    string password = 2;
}

message ParentGuardianUpdatePwdResp {
}

message ParentGuardianInfo {
    uint32 uid = 1;
    bool is_on = 2;   //是否打开家长模式
}

message BatchGetParentGuardianInfoReq {
    repeated uint32 uid_list = 1;
}

message BatchGetParentGuardianInfoResp {
    repeated ParentGuardianInfo info_list = 1;
}

//检查是否超过今日申诉限制次数
message CheckAppealCntIsOverLimitReq {
    uint32 uid = 1;
}

message CheckAppealCntIsOverLimitResp {
    bool is_over_limit = 1;
}