syntax="proto3";

//import "tt/quicksilver/common/tlvpickle/skbuiltintype.proto";

// namespace
package asynctexthandler;


message UserInfoContext
{
    string category = 1;
}

//AntiSpamDataType::GUILD_GROUP_NAME (see AntiSpamDataType)
message GuildModifyGroupNameContext
{
    uint32 guild_id = 1;
    uint32 group_id = 2;
}

//AntiSpamDataType::TEMP_GROUP_NAME (see AntiSpamDataType)
message TempGroupModifyNameContext
{
    uint32 group_id = 1;
}

//AntiSpamDataType::TGROUP_DESC (see AntiSpamDataType)
message TGroupModifyDescContext
{
    uint32 group_id = 1;
    uint32 new_game_id = 2;
    string city_code = 3;
    string city_name = 4;
}

//AntiSpamDataType::TGROUP_NAME (see AntiSpamDataType)
message TGroupModifyNameContext
{
    uint32 group_id = 1;
}

//AntiSpamDataType::GUILD_OR_GROUP_POST_NOTICE (see AntiSpamDataType)
message GroupPublishBulletinHandlerContext
{
    uint32 guild_id = 1;
    uint32 group_id = 2;
    uint32 group_type = 3;
	uint32 split_index = 4; //标题与内容的中间索引， text包含的内容为 标题 + “;” + 内容
}

//AntiSpamDataType::CHANNEL_NAME (see AntiSpamDataType)
message ChannelModifyNameContext
{
  uint32 channel_id = 1;
  uint32 oper_uid = 2;
  uint32 timestamp = 3;
  uint32 channel_type = 4;
}

//AntispamLogic::CHANNEL_TOPIC (see AntiSpamDataType)
message ChannelModifyDescContext
{
  uint32 channel_id = 1;
  uint32 oper_uid = 2;
  uint32 timestamp = 3;
  uint32 channel_type = 4;
}

//AntispamLogic::CHANNEL_TOPIC_DETAIL (see AntiSpamDataType)
message ChannelModifyTopicDetailContext
{
  uint32 channel_id = 1;
  uint32 oper_uid = 2;
  uint32 timestamp = 3;
  uint32 channel_type = 4;
}

// AntispamLogic::CHNNNEL_WELCOME (see AntiSpamDataType)
message ChannelModifyWelcomeMsgContext           
{
  uint32 channel_id = 1;
  uint32 oper_uid = 2;
  uint32 timestamp = 3;
  uint32 channel_type = 4;
}

message TextCheckContext
{
    string text = 1;    
    uint32 uid = 2;
    uint32 data_type = 3;    //应用场景
    uint32 result = 4;      //结果 see TextCheckResp::RESULT (antispamlogic.proto)
    bytes  cxt = 5;         //上下文
}

message GuildInfoModifyContext
{
    uint32 guild_id = 1;
    uint32 timestamp = 2;
    string before_text = 3;
}

message GuildCircleTopicContext
{
    uint32 guild_id      = 1;
    uint32 timestamp     = 2;
    string topic_title   = 3;  
    string topic_content = 4;
    repeated string topic_img_keylist = 5;
    bool is_highlight = 6;
}

message GuildCircleCommentContext
{
    uint32 guild_id       = 1;
    uint32 timestamp      = 2;
    uint32 topic_id       = 3; 
    uint32 ref_comment_id = 4;
    string content        = 5;
    repeated string img_keylist  = 6;
}
