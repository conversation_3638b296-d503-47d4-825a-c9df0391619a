syntax = "proto3";
package comm_push_schedule;

import "tt/quicksilver/push_notification/v3/push-server.proto";

option go_package = "golang.52tt.com/protocol/services/comm-push-schedule";

service CommPushSchedule {
    //定时队列的离线推送
    rpc OfflineTimedQueuePush(OfflineTimedQueuePushReq) returns (OfflineTimedQueuePushResp) {}

    //获取指定推送时间点的推送队列长度
    rpc GetPushQueueLen(GetPushQueueLenReq) returns (GetPushQueueLenResp) {}

    //批量获取指定推送时间点和pushType的推送队列长度
    rpc BatchGetPushQLen(BatchGetPushQLenReq) returns (BatchGetPushQLenResp) {}

    //直接离线推送
    rpc DirectOfflinePush(DirectOfflinePushReq) returns (DirectOfflinePushResp) {}

    //批量直接离线推送
    rpc BatchDirectOfflinePush(BatchDirectOfflinePushReq) returns (BatchDirectOfflinePushResp) {}
}

enum EPushCarry {
    E_DEFAULT_CARRY = 0;
    E_PUSH = 100;
    E_SMS  = 101;
}

enum EPushRecItemType {
    E_DEFAULT_ITEM_TYPE = 0;
    E_UID = 1;
}
enum EPushTask {
    E_DEFAULT_TASK = 0;
    E_TIMED_TASK = 1;
    E_DIRECT_TASK = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message OfflineTimedQueuePushReq {
    EPushCarry pushCarry = 1;                                 //推送的载体
    repeated push_notification.v3.SendPushTaskReq pushEvents = 2; //推送的事件
    uint32 pushMoment = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message OfflineTimedQueuePushResp {
    uint32 recvTs = 1; //接收到事件的时间点
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetPushQueueLenReq {
    uint32 moment = 1;
    uint32 pushCarry = 2; //100-离线push, 101-短信
    uint32 pushType = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetPushQueueLenResp {
    uint32 queueLen = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchGetPushQLenReq {
    uint32 moment = 1;
    repeated uint32 pushTypes = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchGetPushQLenResp {
    map<uint32, uint32> pushType2QLen = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message OfflinePushItem {
    push_notification.v3.SendPushTaskReq pushTaskObj = 1;
    EPushRecItemType itemType = 2;
    string itemId = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DirectOfflinePushReq {
    OfflinePushItem pushItem = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DirectOfflinePushResp {
    uint32 recvTs = 1; //接收到事件的时间点
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchDirectOfflinePushReq {
    repeated OfflinePushItem pushItems = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchDirectOfflinePushResp {
    uint32 recvTs = 1; //接收到事件的时间点
}

//用于异步检查sdk活跃用户的数据结构
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CheckSdkActivePushItem {
    uint32 pushMoment = 1;
    EPushCarry pushCarry = 2; //推送的载体
    EPushTask  pushTask = 3;  //任务分类，定时推送任务or直接推送任务
    repeated OfflinePushItem pushEvents = 4; //推送的事件
    uint32 checkedCnt = 5; //已经进行过活跃度检测的次数
    uint32 maxCheckCnt = 6; //最大进行过活跃度检测的次数
}