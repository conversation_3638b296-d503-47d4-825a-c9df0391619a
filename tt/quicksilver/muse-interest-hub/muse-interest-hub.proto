syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package muse_interest_hub.muse_interest_hub;
option go_package = "golang.52tt.com/protocol/services/muse-interest-hub/muse-interest-hub";

import "tt/quicksilver/extension/options/options.proto";

service MuseInterestHub {
  option (service.options.service_ext) = {
    service_name: "muse-interest-hub"
  };

  /* 兴趣内容相关 开关合集 */
  rpc GetMuseSwitchHub(GetMuseSwitchHubRequest) returns (GetMuseSwitchHubResponse) {}
  rpc SetMuseSwitchHub(SetMuseSwitchHubRequest) returns (SetMuseSwitchHubResponse) {}
  rpc BatGetUserMuseSwitch(BatGetUserMuseSwitchRequest) returns (BatGetUserMuseSwitchResponse) {}

  // 获取MT实验策略内容
  rpc GetMTExperimentStrategy(GetMTExperimentStrategyRequest) returns (GetMTExperimentStrategyResponse) {}
  // 获取MT实验策略内容 V2 根据策略枚举获取结果
  rpc GetMTExperimentStrategyV2(GetMTExperimentStrategyV2Request) returns (GetMTExperimentStrategyV2Response) {}
  rpc GetMTExperimentStrategyV3(GetMTExperimentStrategyV2Request) returns (GetMTExperimentStrategyV2Response) {}
  // 获取MT推荐画像内容
  rpc GetRcmdABTestStrategy(GetRcmdABTestStrategyRequest) returns (GetRcmdABTestStrategyResponse) {}

  rpc IsABTestA2NonEnterChannelUser(IsABTestA2NonEnterChannelUserRequest) returns (IsABTestA2NonEnterChannelUserResponse) {}
  rpc IsABTestEnterChannelUser(IsABTestEnterChannelUserRequest) returns (IsABTestEnterChannelUserResponse) {}

  //记录房间邀请的记录
  rpc InsertChannelInviteRecord(InsertChannelInviteRecordRequest) returns (InsertChannelInviteRecordResponse) {}
  rpc SendChannelInviteReward(SendChannelInviteRewardRequest) returns (SendChannelInviteRewardResponse) {}
  rpc ClearInvitedRewardList(ClearInvitedRewardListRequest) returns (ClearInvitedRewardListResponse) {}

  rpc ModifyOneClickInviteUsageCount(ModifyOneClickInviteUsageCountRequest) returns (ModifyOneClickInviteUsageCountResponse) {}

  rpc GetUserCurrentlyFollowUser(GetUserCurrentlyFollowUserRequest)returns(GetUserCurrentlyFollowUserResponse){}

  //破冰信息
  rpc GetCustomIcebreakerPopupSwitch(GetCustomIcebreakerPopupSwitchRequest)returns(GetCustomIcebreakerPopupSwitchResponse){}
  rpc SetCustomIcebreakerPopupSwitch(SetCustomIcebreakerPopupSwitchRequest)returns(SetCustomIcebreakerPopupSwitchResponse){}
  rpc GetChannelCustomIcebreakerPopup(GetChannelCustomIcebreakerPopupRequest)returns(GetChannelCustomIcebreakerPopupResponse){}
  rpc UpdateChannelCustomIcebreakerPopup(UpdateChannelCustomIcebreakerPopupRequest)returns(UpdateChannelCustomIcebreakerPopupResponse){}

  // 承接白名单
  rpc AddWhitelist(AddWhitelistRequest) returns (AddWhitelistResponse) {}
  rpc GetUserWhitelist(GetUserWhitelistRequest) returns (GetUserWhitelistResponse) {}
}

// 兴趣内容相关 开关合集
message GetMuseSwitchHubRequest{
  uint32 uid=1;
}

message GetMuseSwitchHubResponse{
  map<uint32, bool> is_open_map = 1; // SwitchHubType true：开 false：关
}


message SetMuseSwitchHubRequest{
  uint32 switch_type = 1; /* SwitchHubType */
  bool is_open = 2; // true 开
}

message SetMuseSwitchHubResponse{
}

message BatGetUserMuseSwitchRequest{
  repeated uint32 uid_list = 1;
  uint32 switch_type = 2; /* SwitchHubType */
}

message BatGetUserMuseSwitchResponse{
  map<uint32, bool> is_open_map = 1; // SwitchHubType true：开 false：关
}

// 获取MT实验策略内容
message GetMTExperimentStrategyRequest{
  repeated string layer_tag_list = 1;          // 层标签列表
  repeated uint32 uid_list = 2;                // 用户uid列表
}

message MTExperimentStrategyUserParams{
  uint32 uid = 1;                   // 用户uid
  map<string, string> all_argv = 2; // 所有参数
}

message MTExperimentStrategyUserParamsList{
  repeated MTExperimentStrategyUserParams user_params = 1; // 用户参数列表
}

message GetMTExperimentStrategyResponse{
  map<string, MTExperimentStrategyUserParamsList> layer_tag_strategy_map = 1; // 层标签策略映射
  repeated MTExperimentStrategyUserParams layer_tag_strategy=2;
}

// 获取MT实验策略内容 V2 根据策略枚举获取结果
message GetMTExperimentStrategyV2Request{
  repeated uint32 uid_list = 1;                   // 用户uid 列表
  repeated uint32 strategy_list = 2; // 策略列表 muse_interest_hub_logic.proto STRATEGY_TYPE
}

message GetMTExperimentStrategyV2Response{
  map<uint32, StrategyValueMap> user_strategy_value_map = 1; // muse_interest_hub_logic.proto STRATEGY_TYPE_VALUE
}

message StrategyValueMap{
  map<uint32, uint32> strategy_value_map = 1; // muse_interest_hub_logic.proto STRATEGY_TYPE_VALUE
}

// 获取MT推荐画像内容
message GetRcmdABTestStrategyRequest{
  repeated uint32 uid_list = 1;                // 用户uid列表
  int32 scene = 3;                             // 推荐的场景 rcmd_music_channel.proto enum STRATEGY
  repeated uint32 personal_fields = 2;          // 用户画像字段 rcmd_music_channel.proto enum PERSONA
}

message PersonalData{
  map<int32,string> data_map = 1; // 用户画像数据 PERSONA -> value
}

message GetRcmdABTestStrategyResponse{
  map<uint32, PersonalData> user_map = 1; // 用户策略映射
}

message InsertChannelInviteRecordRequest {
  uint32 inviter = 1;
  uint32 channel_id = 2;
  uint32 invited_uid = 3;
}

message InsertChannelInviteRecordResponse {
}

message SendChannelInviteRewardRequest {
  uint32 uid = 1;
}

message SendChannelInviteRewardResponse {
}

message ClearInvitedRewardListRequest {
  uint32 uid = 1;
}

message ClearInvitedRewardListResponse {
}

message IsABTestA2NonEnterChannelUserRequest {
  repeated uint32 uids = 1;
}

message IsABTestA2NonEnterChannelUserResponse {
  map<uint32, bool> result = 1;
  map<uint32, bool> in_abtest = 2;
}

message ModifyOneClickInviteUsageCountRequest {
  uint32 channel_id = 1;
  int32 count = 2;
}

message ModifyOneClickInviteUsageCountResponse {
  int32 count = 1;
}

message IsABTestEnterChannelUserRequest {
  repeated uint32 uids = 1;
}

message IsABTestEnterChannelUserResponse {
  map<uint32, bool> result = 1;
}

message GetUserCurrentlyFollowUserRequest{
    uint32 uid=1;
}

message GetUserCurrentlyFollowUserResponse{
    repeated uint32 followed_uids=1;

}

message GetCustomIcebreakerPopupSwitchRequest{
    uint32 channel_id=1;
}

message GetCustomIcebreakerPopupSwitchResponse{

  bool is_open=1;
}

message SetCustomIcebreakerPopupSwitchRequest{
  uint32 channel_id=1;
  bool is_open=2;
}

message SetCustomIcebreakerPopupSwitchResponse{

}

message QnAPair{
  string question=1;
  repeated string answers=2;
  repeated AnswerInfo answer_info=3;
}

message AnswerInfo{
  int32 id=1;
  string name=2;
}

message GetChannelCustomIcebreakerPopupRequest{
    uint32 channel_id=1;


}

message GetChannelCustomIcebreakerPopupResponse{
      QnAPair qna_pair=1;
}

message UpdateChannelCustomIcebreakerPopupRequest{
    uint32 channel_id=1;
    QnAPair qna_pair=2;
}

message UpdateChannelCustomIcebreakerPopupResponse{

}

message AddWhitelistRequest {
  uint32 uid = 1;
  string strategy_id = 2; // 策略ID
}

message AddWhitelistResponse {
}

message GetUserWhitelistRequest {
  uint32 uid = 1;
  repeated uint32 business_type = 2; // 业务类型，不传返回用户所有业务策略的白名单
}

message GetUserWhitelistResponse {
  repeated uint32 business_type = 1; // 用户白名单所属的业务策略列表
}