syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package muse_interest_hub.event;
option go_package = "golang.52tt.com/protocol/services/muse-interest-hub/event";

// 用户定位授权上报 客户端触发首页上报
message UserLocationAuthReportEvent{
  uint32 uid = 1;
  string client_ip_str = 2;
  bool is_open = 3; // true：开 false：关
  double longitude = 4; // 经度
  double latitude = 5; // 纬度
  string city = 6;
  string province = 7;
  bool is_open_app_auth = 8;
  uint32 source=9;  //UserLocationReportEventSource
}

enum UserLocationReportEventSource {
  UserLocationReportEventSource_UNKNOWN = 0;
  UserLocationReportEventSource_User_Auth = 1;
  UserLocationReportEventSource_Flash_Chat = 2;
}