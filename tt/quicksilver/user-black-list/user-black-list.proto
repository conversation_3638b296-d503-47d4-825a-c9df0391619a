syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/user-black-list";

// buf:lint:ignore PACKAGE_LOWER_SNAKE_CASE 已迁移 psr，此处协议废弃
package userBlackListService;


service UserBlackList {

    rpc GetUserBlackList (GetUserBlackListReq) returns (GetUserBlackListResp) {
    }

    rpc AddUserBlackList (AddUserBlackListReq) returns (AddUserBlackListResp) {
    }

    rpc DelUserBlackList (DelUserBlackListReq) returns (DelUserBlackListResp) {
    }

    rpc CheckIsInBlackList (CheckIsInBlackListReq) returns (CheckIsInBlackListResp) {
    }

}

//上报数据给中台用
message BlackEvent {
    uint32 active_uid = 1;
    uint32 black_uid = 2;
    enum OperationType {
        Unknow  = 0;
        DoBlack = 1;
        UnDoBlack = 2;
    }
    
    OperationType op = 4;
    uint64 time_at = 5;
}

message GetUserBlackListReq {
    uint32 active_uid = 1;
    uint32 page = 2;
    uint32 count = 3;
}

message GetUserBlackListResp {
    uint32 active_uid = 1;
    uint32 total = 2;
    repeated uint32 black_list = 3;
}

message AddUserBlackListReq {
    uint32 active_uid = 1;  //主动拉黑方uid
    uint32 passive_uid = 2; //被拉黑方uid
}

message AddUserBlackListResp {
}

message DelUserBlackListReq {
    uint32 active_uid = 1;  //主动拉黑方uid
    uint32 passive_uid = 2; //被拉黑方uid
}

message DelUserBlackListResp {
}

message CheckIsInBlackListReq {
    uint32 active_uid = 1;  //主动拉黑方uid
    uint32 passive_uid = 2; //被拉黑方uid
}

message CheckIsInBlackListResp {
    bool b_in = 1;
}

