syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/masked-pk-score";
package masked_pk_score;


service MaskedPKScore {
  rpc GetUserMaskPkScore(GetUserMaskPkScoreReq) returns (GetUserMaskPkScoreResp) {}
  rpc AddUserMaskPkScore(AddUserMaskPkScoreReq) returns (AddUserMaskPkScoreResp) {}
  rpc GetUserMaskPkChangeLog(GetUserMaskPkChangeLogReq) returns (GetUserMaskPkChangeLogResp) {}
}

enum SourceType {
  Unknown = 0;
  EntertainmentMaskedPkAward = 1;       // 娱乐厅蒙面PK奖励
  LiveMaskedPkAward = 2;                // 语音直播厅蒙面PK奖励
  ScoreExchange = 3;                    // 积分提现扣除积分
  ScoreExchangeReturn = 4;              // 积分提现失败返还积分
  OfficeReclaimScore = 5;               // 运营回收积分
  OfficeGiveScore = 6;                  // 运营发放积分
  GuildChangePrivate = 7;				//主动将个人对公转为对私扣除积分
  GuildQuit = 8;						//解约导致个人对公转为对私扣除积分
  GuildOfficalRecycle = 9;				//官方回收个人对公权扣除积分
  GuildExchange = 10;					//会长提现个人对公积分
}

message GetUserMaskPkScoreReq {
  uint32 uid = 1;
}

message GetUserMaskPkScoreResp {
  uint32 score = 1;
}

message AddUserMaskPkScoreReq {
  string order_id = 1;
  uint32 uid = 2;
  int32 change_score = 3;
  uint32 source_type = 4;
  uint32 outside_time = 5;
  string desc = 6;
}

message AddUserMaskPkScoreResp{
  uint32 final_score = 1;
}

message ScoreChangeLog {
  uint32 id = 1;
  uint32 uid = 2;
  string order_id = 3;
  int32 change_score = 4;
  uint32 source_type = 5;
  uint32 outside_time = 6;
  uint32 create_time = 7;
  string desc = 8;
}

// 获取近两个月的流水
message GetUserMaskPkChangeLogReq {
  uint32 uid = 1;
  repeated uint32  source_type_list = 2;
  uint32 begin = 3;
  uint32 limit = 4;
}

message GetUserMaskPkChangeLogResp {
  repeated ScoreChangeLog log_list = 1;
}