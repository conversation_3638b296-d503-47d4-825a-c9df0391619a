syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/third-party-auth-proxy";

package third_party_auth_proxy;

message GetQQUserInfoReq {
  string appid = 1;
  string openid = 2;
  string access_token = 3;
}

message GetQQUserInfoResp {
  string appid = 1;
  string openid = 2;
  string nickname = 3;
  string gender = 4;
  string figureurl_qq_1 = 5; //大小为40×40像素的QQ头像URL。
  string figureurl_qq_2 = 6; //大小为100×100像素的QQ头像URL。需要注意，不是所有的用户都拥有QQ的100x100的头像，但40x40像素则是一定会有。
}

message GetWechatUserInfoReq {
  string access_token = 1;
  string open_id = 2;
}

message GetWechatUserInfoResp {
  string openid = 1;
  string unionid = 2;
  string nickname = 3;
  string gender = 4;
  string headimage = 5;
}

message VerifyAppleReq {
  string auth_code = 1;
  string id_token = 2;
  string user_id = 3;
}

message VerifyAppleResp {
  string user_id = 1;
}

message VerifyQQReq {
  string access_token = 1;
}

message VerifyQQResp {
  string appid = 1;
  string openid = 2;
  string unionid = 3;
}

message GetCMCCPhoneReq  {
  string appid = 1;
  string token = 2;
}

message GetCMCCPhoneResp  {
  string phone = 1;
}

message GetCUCCPhoneReq {
  string access_code = 1;
  string apikey = 2;
  string md5 = 3; //应用 md5
}

message GetCUCCPhoneResp {
  string phone = 1;
}

service ThirdPartyAuthGo {

  rpc VerifyQQ(VerifyQQReq) returns (VerifyQQResp) {}
  rpc VerifyApple(VerifyAppleReq) returns (VerifyAppleResp) {}

  rpc GetWechatUserInfo(GetWechatUserInfoReq) returns (GetWechatUserInfoResp) {}
  rpc GetQQUserInfo(GetQQUserInfoReq) returns (GetQQUserInfoResp) {}

  rpc GetCMCCPhone (GetCMCCPhoneReq) returns (GetCMCCPhoneResp) {}
  rpc GetCUCCPhone (GetCUCCPhoneReq) returns (GetCUCCPhoneResp) {}

}

