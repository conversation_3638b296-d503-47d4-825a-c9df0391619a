syntax = "proto3";

package muse_likes_activity;
option go_package = "golang.52tt.com/protocol/services/muse-likes-activity";


service MuseLikesActivity {
  rpc GetLikesActivityUserCompletedTaskProgress(GetLikesActivityUserCompletedTaskProgressRequest) returns (GetLikesActivityUserCompletedTaskProgressResponse) {}
  rpc SimulateLikes(SimulateLikesRequest)returns(SimulateLikesResponse){}
}

message SimulateLikesRequest{
    uint32 channel_id=1;
    uint32 uid=2;

}

message SimulateLikesResponse{

}

 message GetLikesActivityUserCompletedTaskProgressRequest{
      uint32 uid=1;
}


message CheckInTaskReward{
  int32 check_in_days=1;
  int32 unlocked_highest_level_reward=2; //已解锁的最高等级奖励
}

message AccumulateLikesTaskReward{
  int32 like_count=1;
  int32 unlocked_highest_level_reward=2;
}

message GetLikesActivityUserCompletedTaskProgressResponse{
  CheckInTaskReward check_in=1;
  AccumulateLikesTaskReward like=2;
  bool activity_switch=3;
}