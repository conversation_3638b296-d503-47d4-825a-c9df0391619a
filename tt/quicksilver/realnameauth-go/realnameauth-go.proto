syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/realnameauth-go";
package realnameauth_go;


service RealNameAuthGoSvr {
   // （旧）跟人群包相关
   rpc FaceCheckResultReport(FaceCheckResultReportReq) returns (FaceCheckResultReportResp) {};

   // （新）人脸结果上报
   rpc ReportFaceAuthResult(ReportFaceAuthResultReq) returns (ReportFaceAuthResultResp) {};

   // 设置设备禁止人脸实名的状态
   rpc SetDeviceForbidRealNameStatus(SetDeviceForbidRealNameStatusReq) returns (SetDeviceForbidRealNameStatusResp) {};

   rpc ClearDeviceForbidRealNameStatus(ClearDeviceForbidRealNameStatusReq) returns (ClearDeviceForbidRealNameStatusResp) {};

   rpc InDeviceForbidRealNameStatus(InDeviceForbidRealNameStatusReq) returns (InDeviceForbidRealNameStatusResp) {};

   rpc CreateFaceAuthResultToken(CreateResultTokenReq) returns (CreateResultTokenResp) {};
   rpc VerifyFaceAuthResultToken(VerifyResultTokenReq) returns (VerifyResultTokenResp) {};

   // 记录用户最近人脸验证信息
   rpc SetUserLastFaceAuthInfo(SetUserLastFaceAuthInfoReq) returns (SetUserLastFaceAuthInfoResp) {};
   // 查询用户最近人脸验证信息
   rpc GetUserLastFaceAuthInfo(GetUserLastFaceAuthInfoReq) returns (GetUserLastFaceAuthInfoResp) {};
}

// 人脸验证上下文
message FaceAuthContext {
   string biz_scene = 1;              // 货币组人脸场景
   uint32 channel_id = 2;             // 调起人脸时的房间
   string face_auth_require_text = 3; // 人脸认证要求文案
   string face_auth_success_text = 4; // 人脸认证成功文案
   uint64 uid = 5;
   string processing_text = 6; // 人脸中时展示的文案
   string failure_text = 7; // 人脸失败时展示的文案
   bool is_mark = 8; // 是否标记
   int64 face_auth_start_at = 9; // 人脸认证开始时间
   string face_auth_result_desc = 10; // 人脸认证结果描述
   string source_type = 11; // 人脸验证事件来源 风控事件id
   string face_report_scene = 12; // 人脸验证上报场景
   map<string, string> params = 13; // 实时风控或埋点上报相关参数
   uint32 market_id = 14; // 马甲包
   uint32 terminal_type = 15; // 终端类型
   uint32 client_type = 16; // 客户端类型
   string req_id = 17; // 请求id
   bool face_auth_result_token_disposable = 18; // 创建一次性人脸token
   // 人脸验证弹窗展示时间
   uint32 face_auth_popup_ttl = 19;
   // 人脸验证弹窗超时提示文案
   string face_auth_popup_timeout_text = 20;
   // 人脸验证流程时长限制
   uint32 face_auth_process_time_limit = 21;
   // 人脸验证流程超时弹窗提示标题
   string face_auth_process_timeout_title = 22;
   // 人脸验证流程超时弹窗提示文案
   string face_auth_process_timeout_text = 23;
}

message ReportFaceAuthResultReq {
   uint32 uid = 1;
   bool is_pass = 2;
   bytes device_id = 3;
   uint32 market_id = 4;
   uint32 terminal_type = 5;
   string result_request_id = 6; // 获取人脸结果请求id
   FaceAuthContext face_auth_context = 7;
   uint32 req_source = 8; // see auth.proto FaceResultReqSource
}

message ReportFaceAuthResultResp {
}

//上报人脸验证结果
message FaceCheckResultReportReq {
   uint32 uid = 1;
   uint32 trigger_scene = 2;  // see ga_base.proto AuthScene
   bytes  request_id = 3;    // 请求跟踪id
   bool   is_pass = 4;  // 验证是否通过
   bytes  device_id = 5; // 设备id
}
message FaceCheckResultReportResp {
}


message SetDeviceForbidRealNameStatusReq {
   bytes device_id = 1;
   int64 ttl = 2; // 秒
   uint64 uid = 3;
}

message SetDeviceForbidRealNameStatusResp {

}

message InDeviceForbidRealNameStatusReq {
   bytes device_id = 1;
   uint64 uid = 2;
}

message InDeviceForbidRealNameStatusResp {
   bool in_forbid = 1;
   int64 expired_at = 2;
}

message ClearDeviceForbidRealNameStatusReq {
   bytes device_id = 1;
   uint64 uid = 2;
}

message ClearDeviceForbidRealNameStatusResp {

}

message FaceAuthResultToken {
   string provider_code = 1;
   bool result = 2;
   uint64 uid = 3;
   string scene = 4;
   bytes device_id = 5;
   int64 expired_at = 6;
   bool is_mark = 7;
   // 成功校验后即失效
   bool disposable = 8;
}

message VerifyResultTokenReq {
   string result_token = 1;
   uint64 uid = 2;
   string scene = 3;
   bytes device_id = 4;
}

message VerifyResultTokenResp {
   bool is_pass = 1;
   uint64 uid = 2;
}

message CreateResultTokenReq {
   FaceAuthResultToken data = 1;
}

message CreateResultTokenResp {
   string token = 1;
}

enum FaceAuthScene {
  // 未指定
  FACE_AUTH_SCENE_UNSPECIFIED = 0;
  // 消费
  FACE_AUTH_SCENE_CONSUME = 1;
}

enum FaceAuthStatus {
  // 未指定
  FACE_AUTH_STATUS_UNSPECIFIED = 0;
  // 人脸验证通过并确认下一步
  FACE_AUTH_STATUS_PASS_AND_CONFIRM = 1;
}

message UserLastFaceAuthInfo {
  // 用户ID
  uint32 uid = 1;
  // 人脸验证场景
  FaceAuthScene scene = 2;
  // 人脸验证状态
  FaceAuthStatus status = 3;
  // 更新时间，单位秒
  int64 update_ts = 4;
}

message SetUserLastFaceAuthInfoReq {
  // 人脸验证状态信息列表
  repeated UserLastFaceAuthInfo info_list = 1;
  // 缓存时长，单位秒
  int64 cache_ttl = 2;
}

message SetUserLastFaceAuthInfoResp {
}

message GetUserLastFaceAuthInfoReq {
  // 人脸验证场景
  FaceAuthScene scene = 1;
  // 用户ID列表
  repeated uint32 uid_list = 2;
}

message GetUserLastFaceAuthInfoResp {
  // 人脸验证状态信息列表
  repeated UserLastFaceAuthInfo info_list = 1;
}