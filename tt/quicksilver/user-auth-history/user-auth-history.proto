syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/user-auth-history";

package user_auth_history;

service UserAuthHistory {

  rpc GetUserLoginHistory(GetUserLoginHistoryReq) returns(GetUserLoginHistoryResp) {}
  rpc RecordUserLogin(RecordUserLoginReq) returns(RecordUserLoginResp) {}

  rpc GetUserLoginWithDevice(GetUserLoginWithDeviceReq) returns (GetUserLoginWithDeviceResp) {}

  rpc GetUserLoginDevice(GetUserLoginDeviceReq) returns (GetUserLoginDeviceResp) {}
  rpc BatchGetUserLoginDevice (BatchGetUserLoginDeviceReq) returns (BatchGetUserLoginDeviceResp) {}

  rpc GetUserLastLoginInfo(GetUserLastLoginInfoReq) returns(UserLoginInfo) {}
  rpc BatchGetUserLastLoginInfo (BatchGetUserLastLoginInfoReq) returns (BatchGetUserLastLoginInfoResp) {}

  rpc VerifyCAPTCHASuccess(VerifyCAPTCHASuccessReq) returns(VerifyCAPTCHASuccessResp) {}
  rpc GetLastVerifySuccessInfo(GetLastVerifySuccessInfoReq) returns(GetLastVerifySuccessInfoResp) {}

  rpc GetUserRegInfo(GetUserRegInfoReq) returns (GetUserRegInfoResp) {}

  rpc GetDeviceIdInfo(GetDeviceIdInfoReq) returns (GetDeviceIdInfoResp) {}

  rpc TrackUserLogin(TrackUserLoginReq) returns (TrackUserLoginResp) {}

  rpc IsUserInvalid(IsUserInvalidReq) returns (IsUserInvalidResp) {}

  rpc ClearUserLoginHistory(ClearUserLoginHistoryReq) returns (ClearUserLoginHistoryResp) {}

}

// buf:lint:ignore ENUM_PASCAL_CASE
enum LOGIN_OP_TYPE {
  LOGIN_OP_Nil = 0;
  LOGIN_OP_Reg = 1;		     // 注册
  LOGIN_OP_Manual = 2;		     // 手工登录
  LOGIN_OP_AutoLogin = 3;	     // 自动登录
  LOGIN_OP_Sdk_Activate = 4;    // SDK激活
  LOGIN_OP_IOS_Activate = 5;   // tt ios 激活
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UserLoginInfo {
  uint64 uid = 1;
  uint32 op_type = 2;    // LOGIN_OP_TYPE
  int32 result = 3;					// 操作结果
  string phone = 4;					// 手机号
  uint32 third_party_type = 5;		// 第三方账号类型
  string openid = 6;					// openid
  
  string imei = 10;
  string os_ver = 11;				// 操作系统版本
  string os_type = 12;				// 操作系统类型
  string device_model = 13;			// 机器型号
  string signature = 14;				// 签名
  string device_info = 15;			// 设备信息
  uint32 is_emulator = 16;			// 是否模拟器
  string device_id = 17;				// 设备号
  uint32 client_ver = 18;			// 客户端版本类型
  string clientIp = 19;				// 客户端ip
  string client_channel_id = 20;     // 客户端渠道号
  
  uint32 client_type = 21;           // 客户端类型(仅限TT -- 0: Android, 1: iOS)
  uint32 terminal_type = 22;         // 终端类型(包含平台/操作系统/APPID)
  
  bool is_quick_login_from_sdk = 23; // 是否从SDK快速登录
  string login_time = 24; //没用?
  int32 client_port = 25;
  string idfa = 26;  //IOS IDFA
  string login_account = 27; //登录用账号:phone/ttid
  string username = 28;
  string alias = 29;
  string nickname = 30;
  string user_signature = 31; //个性签名
  string proxy_ip = 32;
  uint32 proxy_port = 33;
  uint32 client_id = 34;
  uint32 market_id = 35;
}

message GetUserLoginHistoryReq {
  uint64 uid = 1;
  int64 begin_time = 2;
  int64 end_time = 3;
}

message GetUserLoginHistoryResp {
  repeated UserLoginInfo login_history = 1;
}

message RecordUserLoginReq {
  UserLoginInfo info = 1;
  uint32 invalid = 2;				// 是否同个机器注册的。如果是则invalid = 1
}

message RecordUserLoginResp {
  bool is_new_usual_device = 1;	// 新的常用设备
}

message UserLoginHit {
  uint64 uid = 1;
  int64 login_at = 2;
}

message GetUserLoginWithDeviceReq {
  string device_id = 1;
  int64 begin_login_at = 2; // default now-7d, min now-90d
  int64 end_login_at = 3;   // default now
}

message GetUserLoginWithDeviceResp {
  string device_id = 1;
  repeated UserLoginHit login_users = 2;
}

message TrackUserLoginReq {
  uint64 uid = 1;
  int64 login_at = 2;
  string device_id = 3;
  string imei = 4;
  string ip	= 5;
}

message TrackUserLoginResp {
}

message IsUserInvalidReq {
  uint64 uid = 1;
}

message IsUserInvalidResp {
  bool invalid = 1;
}

message GetUserLoginDeviceReq {
  uint64 uid	= 1;
}

message UserLoginDevice {
  string device_id = 1;
  int64 login_at = 2;
}

message GetUserLoginDeviceResp {
  uint64 uid	= 1;
  repeated UserLoginDevice login_devices = 2;
}

message GetUserLastLoginInfoReq {
  uint64 uid = 1;
}

message BatchGetUserLastLoginInfoReq {
  repeated uint64 uid_list = 1;
}

message BatchGetUserLastLoginInfoResp {
  repeated UserLoginInfo info_list = 1;
}

message VerifyCAPTCHASuccessReq {
  uint64 uid	= 1;
  uint32 verify_reason = 2;	//验证原因：1登录
  string device_id = 3;	//设备号
}

message VerifyCAPTCHASuccessResp {
}

message GetLastVerifySuccessInfoReq {
  uint64 uid	= 1;
  uint32 verify_reason = 2;	//验证原因：1登录
}

message GetLastVerifySuccessInfoResp {
  string device_id = 1;	//设备号
  int64 timestamp = 2;
}

message GetUserRegInfoReq {
  uint64 uid = 1;
}

message GetUserRegInfoResp {
  UserLoginInfo info = 1;
}

message BatchGetUserLoginDeviceReq {
  repeated uint64 uids = 1;
}

message BatchUserLoginDevice {
  uint64 uid	= 1;
  string device_id = 2;
  int64 login_at = 3;
}

message BatchGetUserLoginDeviceResp {
  repeated BatchUserLoginDevice login_devices = 1;
}

message RegUserInfo {
  uint64 uid = 1;
  string phone = 2;
  string reg_at = 3;
}

message GetDeviceIdInfoReq {
  string device_id = 1;				// hex
}

message GetDeviceIdInfoResp {
  int64 reg_user_count = 1;			// 注册用户的数量
  repeated RegUserInfo reg_info_list = 2;		// 注册用户列表
}

message ClearUserLoginHistoryReq {
  uint32 year = 1;
  uint32 month = 2;
}

message ClearUserLoginHistoryResp {
}