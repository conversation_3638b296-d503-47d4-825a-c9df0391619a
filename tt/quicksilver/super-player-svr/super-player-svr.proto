syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/superplayersvr";
package superplayersvr;


service SuperPlayer {
  // 会员权益相关
  // ------------------------------
  // 获取会员信息
  rpc GetSuperPlayerInfo (GetSuperPlayerInfoReq) returns (GetSuperPlayerInfoResp) {}
  // 批量获取会员信息
  rpc BatchGetSuperPlayerInfo (BatchGetSuperPlayerInfoReq) returns (BatchGetSuperPlayerInfoResp){}
  // 增加会员成长值
  rpc AddSuperPlayerValue (AddSuperPlayerValueReq) returns (AddSuperPlayerValueResp) {}
  // 获取会员配置（成长值等级等）
  rpc GetSuperPlayerConf (GetSuperPlayerConfReq) returns (GetSuperPlayerConfResp) {}
  // 获取会员成长值变更记录
  rpc GetSuperPlayerValueRecord (GetSuperPlayerValueRecordReq) returns (GetSuperPlayerValueRecordResp) {}
  // 会员弹窗消息
  rpc SuperPlayerIdentityPopup (SuperPlayerIdentityPopupReq) returns (SuperPlayerIdentityPopupResp) {}
  // 获取会员过期续费提醒信息
  rpc GetRenewalRemindInfo (GetRenewalRemindInfoReq) returns (GetRenewalRemindInfoResp) {}
  // ------------------------------

  // 订单与签约相关
  // ------------------------------
  // 订单相关
  // 开通超级会员
  rpc OpenSuperPlayer (OpenSuperPlayerReq) returns (OpenSuperPlayerResp) {} // 该接口暂时取消
  // 下单
  rpc PlaceOrder (PlaceOrderReq) returns (PlaceOrderResp) {}
  // 取消订单
  rpc CancelOrder (CancelOrderReq) returns (CancelOrderResp) {} // 该接口目前没有使用
  // 订单状态
  rpc GetOrderStatus (GetOrderStatusReq) returns (GetOrderStatusResp) {}
  // 支付中心通知订单状态
  rpc NoticeOrderStatus (NoticeOrderStatusReq) returns (NoticeOrderStatusResp) {}
  // 补单接口
  rpc ReplacementOrder (ReplacementOrderReq) returns (ReplacementOrderResp) {}
  // 自动下单补单接口
  rpc  PlaceAutoPayOrder (PlaceAutoPayOrderReq) returns (PlaceAutoPayOrderResp) {}
  // 设备是否购买过
  rpc GetDeviceBuyRecord (GetDeviceBuyRecordReq) returns (GetDeviceBuyRecordResp){}
  // 活动接口 发放超级会员体验装扮
  rpc SendSuperPlayerDressExperience (SendSuperPlayerDressExperienceReq) returns (SendSuperPlayerDressExperienceResp) {}
  // 获取用户购买记录
  rpc GetSuperPlayerPayRecordList (GetSuperPlayerPayRecordListReq) returns (GetSuperPlayerPayRecordListResp) {}
  // 获取用户最新的购买记录
  rpc GetLatestPayRecord (GetLatestPayRecordReq) returns (GetLatestPayRecordResp) {}
  // 可升级的天数
  rpc GetCanUpgradeDays (GetCanUpgradeDaysReq) returns (GetCanUpgradeDaysResp) {}
  // 当前剩余订单时长
  rpc GetUserOrderLeftDays (GetUserOrderLeftDaysReq) returns (GetUserOrderLeftDaysResp) {}
  // 签约相关
  // 获取签约信息
  rpc GetSuperPlayerContract (GetSuperPlayerContractReq) returns (GetSuperPlayerContractResp) {}
  // 取消签约
  rpc CancelContract (CancelContractReq) returns (CancelContractResp) {}
  // 更新签约关系
  rpc NotifyContract (NotifyContractReq) returns (NotifyContractResp) {}
  // 撤销订单权限
  rpc RevokeOrder (RevokeOrderReq) returns (RevokeOrderResp) {}
  // 在活动指定时间前是否为年费SVIP
  rpc IsAnnualSvipBeforeActivity (IsAnnualSvipBeforeActivityReq) returns (IsAnnualSvipBeforeActivityResp) {}
  // ------------------------------

  // 广告相关
  // ------------------------------
  // 会员广告语配置
  // 添加会员主入口广告语配置
  rpc AddSuperPlayerEntryAdvConf (AddSuperPlayerEntryAdvConfReq) returns (AddSuperPlayerEntryAdvConfResp) {}
  // 更新会员主入口广告语配置
  rpc UpdateSuperPlayerEntryAdvConf (UpdateSuperPlayerEntryAdvConfReq) returns (UpdateSuperPlayerEntryAdvConfResp){}
  // 删除会员主入口广告语配置
  rpc DelSuperPlayerEntryAdvConf (DelSuperPlayerEntryAdvConfReq) returns (DelSuperPlayerEntryAdvConfResp) {}
  // 获取会员主入口所有广告语配置
  rpc GetAllSuperPlayerEntryAdvConf (GetAllSuperPlayerEntryAdvConfReq) returns (GetAllSuperPlayerEntryAdvConfResp) {}
  // 获取有效会员主入口广告语配置
  rpc GetSuperPlayerEntryAdvConf (GetSuperPlayerEntryAdvConfReq) returns (GetSuperPlayerEntryAdvConfResp) {}
  // banner广告位
  // 添加banner广告位
  rpc AddBannerAdvConf (AddBannerAdvConfReq) returns (AddBannerAdvConfResp) {}
  // 更新banner广告位
  rpc UpdateBannerAdvConf (UpdateBannerAdvConfReq) returns (UpdateBannerAdvConfResp) {}
  // 删除banner广告位
  rpc DelBannerAdvConf (DelBannerAdvConfReq) returns (DelBannerAdvConfResp) {}
  // 根据状态获取banner广告位列表
  rpc GetBannerAdvConfListByStatus (GetBannerAdvConfListByStatusReq) returns (GetBannerAdvConfListByStatusResp) {}
  // banner广告位排序
  rpc BannerAdvConfSort (BannerAdvConfSortReq) returns (BannerAdvConfSortResp) {}
  // ------------------------------

  // 售卖套餐相关
  // ------------------------------
  // 会员套餐配置
  // 添加套餐配置
  rpc AddPackage (AddPackageReq) returns (AddPackageResp) {}
  // 修改套餐配置
  rpc UpdatePackage (UpdatePackageReq) returns (UpdatePackageResp) {}
  // 根据状态获取套餐列表
  rpc GetPackageListByStatus (GetPackageListByStatusReq) returns (GetPackageListByStatusResp) {}
  // 新增在售架套餐配置
  rpc AddSalePackage (AddSalePackageReq) returns (AddSalePackageResp) {}
  // 修改在售架套餐配置
  rpc UpdateSalePackage (UpdateSalePackageReq) returns (UpdateSalePackageResp) {}
  // 获取在售架套餐列表
  rpc GetSalePackageListByStatus (GetSalePackageListByStatusReq) returns (GetSalePackageListByStatusResp) {}
  // 套餐排序
  rpc SalePackageSort (SalePackageSortReq) returns (SalePackageSortResp) {}
  // 校验上架套餐信息
  rpc CheckSalePackageInfo (CheckSalePackageInfoReq) returns (CheckSalePackageInfoResp) {}
  // 获取套餐列表（提供给http接口，暂未用到）
  rpc GetPackageList (GetPackageListReq) returns (GetPackageListResp) {}
  // 获取svip套餐列表（提供给http接口，暂未用到）
  rpc GetSvipPackageList (GetSvipPackageListReq) returns (GetSvipPackageListResp) {}
  // 活动使用接口 获取活动售卖包信息
  rpc GetSalePackageListForActivity (GetSalePackageListForActivityReq) returns (GetSalePackageListForActivityResp) {}
  // 增加套餐限购配置
  rpc AddPackageLimit (AddPackageLimitReq) returns (AddPackageLimitResp) {}
  // 修改套餐限购配置
  rpc UpdatePackageLimit (UpdatePackageLimitReq) returns (UpdatePackageLimitResp) {}
  // 删除套餐限购配置
  rpc DelPackageLimit (DelPackageLimitReq) returns (DelPackageLimitResp) {}
  // 批量获取套餐限购配置
  rpc BatchGetPackageLimit (BatchGetPackageLimitReq) returns (BatchGetPackageLimitResp) {}
  // 根据ID获取套餐限购配置
  rpc GetPackageLimitById (GetPackageLimitByIdReq) returns (GetPackageLimitByIdResp) {}
  // 获取用户被限购的套餐ID列表
  rpc GetUserLimitPackageIdList (GetUserLimitPackageIdListReq) returns (GetUserLimitPackageIdListResp) {}
  // 根据套餐ID获取在售架套餐
  rpc GetSalePackageForLimit (GetSalePackageForLimitReq) returns (GetSalePackageForLimitResp) {}
  // 获取具有优惠卷的在售套餐
  rpc GetSalePackageWithCoupon (GetSalePackageWithCouponReq) returns (GetSalePackageWithCouponResp) {}
  // 新活动使用接口 根据指定套餐ID获取活动售卖包信息
  rpc GetSalePackageForActivityNew (GetSalePackageForActivityNewReq) returns (GetSalePackageForActivityNewResp) {}
  // 新活动使用接口 根据限购ID列表获取活动售卖包信息
  rpc GetPackageForActivityByLimitIds (GetPackageForActivityByLimitIdsReq) returns (GetPackageForActivityByLimitIdsResp) {}
  // 根据套餐ID获取套餐信息
  rpc GetPackageById (GetPackageByIdReq) returns (GetPackageByIdResp) {}
  // 校验套餐是否配置限购
  rpc ValidPackagesHasLimit (ValidPackagesHasLimitReq) returns (ValidPackagesHasLimitResp) {}

  // 分组套餐相关
  // 新增分组套餐配置
  rpc AddGroupPackage (AddGroupPackageReq) returns (AddGroupPackageResp) {}
  // 批量新增分组套餐配置
  rpc BatchAddGroupPackage (BatchAddGroupPackageReq) returns (BatchAddGroupPackageResp) {}
  // 提前检查批量添加套餐
  rpc CheckBatchAddGroupPackage (CheckBatchAddGroupPackageReq) returns (CheckBatchAddGroupPackageResp) {}
  // 新增/编辑分组套餐配置
  rpc EditGroupPackage (EditGroupPackageReq) returns (EditGroupPackageResp) {}
  // 修改分组
  rpc ModifyGroupPackage (ModifyGroupPackageReq) returns (ModifyGroupPackageResp) {}
  // 设置分组状态
  rpc SetGroupPackageStatus (SetGroupPackageStatusReq) returns (SetGroupPackageStatusResp) {}
  // 查询分组信息
  rpc GetGroupPackage (GetGroupPackageReq) returns (GetGroupPackageResp) {}
  // 获取分组信息
  rpc BatchGetGroupPackage (BatchGetGroupPackageReq) returns (BatchGetGroupPackageResp) {}

  // 优惠券相关
  // 获取优惠券弹窗限制
  rpc GetCouponPopUpLimit (GetCouponPopUpLimitReq) returns (GetCouponPopUpLimitResp) {}
    // 优惠券弹窗记录
  rpc CouponPopUpRecord (CouponPopUpRecordReq) returns (CouponPopUpRecordResp) {}
  // 优惠券弹窗次数
  rpc GetCouponPopUpCount (GetCouponPopUpCountReq) returns (GetCouponPopUpCountResp) {}
  // 优惠券弹窗且未支付统计
  rpc CouponPopUpAndPay (CouponPopUpAndPayReq) returns (CouponPopUpAndPayResp) {}
  // 获取优惠券弹窗且未支付次数
  rpc GetCouponPopUpAndUnpay (GetCouponPopUpAndUnpayReq) returns (GetCouponPopUpAndUnpayResp) {}
  // ------------------------------


  // 测试接口
  // ------------------------------
  // 修改会员过期时间（废弃）
  rpc ModifySuperPlayerExpireTime (ModifySuperPlayerExpireTimeReq) returns (ModifySuperPlayerExpireTimeResp) {}
  // 过期指定类型的会员时间
  rpc ExpireSuperPlayerDays (ExpireSuperPlayerDaysReq) returns (ExpireSuperPlayerDaysResp) {}
  // 清除会员开通记录
  rpc ClearSuperPlayerSign (ClearSuperPlayerSignReq) returns (ClearSuperPlayerSignResp) {}
  // 拉起支付宝下次签约支付
  rpc PushAlipayNextContract (PushAlipayNextContractReq) returns (PushAlipayNextContractResp) {}
  // ------------------------------
}

// SuperPlayerStatus 会员状态
// 可表示普通会员和SVIP会员的状态，通过不同的枚举值体现
enum SuperPlayerStatus
{
  ENUM_STATUS_NO_OPEN = 0;  // 未开通
  ENUM_STATUS_OPENING = 1;  // 生效中（有效期内且剩余时间 >72h）
  ENUM_STATUS_SOON_EXPIRE = 2;  // 即将过期（有效期内且剩余时间 ≤72h）
  ENUM_STATUS_EXPIRED = 3;  // 已过期（用户开通过但已不在有效期内）
  ENUM_STATUS_SIGN = 4; // 用户开通连续订阅未到期且保持签约状态
  ENUM_STATUS_PENDING = 5; // 待生效（用户存在剩余的普通超级玩家有效天数，但由于当前有SVIP生效中套餐，因此普通超级玩家天数待生效的状态，仅普通会员存在该状态）
}

//补单
message ReplacementOrderReq{
  string old_order_id = 1; //原旧订单
  int32  create_time = 2; //原旧单下单时间
}

message ReplacementOrderResp {
  string order_id = 1;
}

message OpenSuperPlayerOrder {
  string order_id = 1; //订单号，有对对应系统保证不重复
  uint32 super_player_uid = 2; //需要加成长值的uid
  int64  inc_value = 3; //需要加的成长值，可能是负值
  int64  months = 4; //加几个月，废弃
  string reason = 5; //加成长值的理由
  int64  server_time = 6; //发起增加值的服务的时间，同一个order_id不能有变化
  string package_id = 7; //套餐ID
  int64 days = 8; //加几天
  int64 mins = 9; //加几分钟。 days,mins 二选一
}

//支付后，调这个接口增加会员时间
message OpenSuperPlayerReq{
  uint32 uid = 1;//购买的用户UID
//  int64 months = 2; // 购买多少个月（30天）废弃
//  int64 value = 3; // 增加会员值 废弃
  int64 server_time = 4; // 同一个单，不能变
  string order_id = 5; // 订单号，使用这个单进行幂等
//  string package_id = 6; // 套餐ID 暂时废弃
//  int64 mins = 7; // package_id 和 mins 二选一，有package_id优选使用套餐配置增加的会员时间 废弃
  uint32 source_id = 8; // 来源哪个活动发放的
  uint32 days = 9; // 发放多少天
  PackageType package_type = 10; // 套餐类型
}

message OpenSuperPlayerResp {
  SuperPlayerInfo super_player_info = 1; //返回增加时间后的会员信息
}


message ModifySuperPlayerExpireTimeReq {
  uint32 uid = 1;
  uint32 expire_at = 2;
}

message ModifySuperPlayerExpireTimeResp {}

//会员等级信息等
message SuperPlayerInfo {
  uint32 super_player_uid = 1; //uid
  int64 super_player_value = 2; //会员值
  int64 super_player_level = 3; //会员等级
  int64 begin_timestamp = 4; //当前会员周期开始时间戳，
  int64 expire_timestamp = 5; //当前会员周期结束时间戳，也是一个版本号，push和get拿到不同结果时，取版本号大的。
  int64 year_member_expire_timestamp = 6; //年费会员结束时间戳
  bool has_expired = 7; // 当前帐号是否已过期。注意！该字段仅做外显，其余部分根据expire_timestamp进行判断用户是否有超级会员权限
  int64 svip_begin_timestamp = 8; //svip会员周期开始时间戳，没开通过是0
  int64 svip_expire_timestamp = 9; //svip会员周期结束时间戳，没开通过是0；也是一个版本号，push和get拿到不同结果时，取版本号大的。
  int64 svip_year_member_expire_timestamp = 10; //svip年费会员结束时间戳
  SuperPlayerVipType super_player_type = 11; //会员类型
  bool is_super_player = 18; //是否是超级玩家
  bool is_svip = 12; //是否是svip
  bool is_signed_super_player = 13; //是否开通过超级玩家
  bool is_signed_svip = 14; //是否开通过SVIP
  EntryAdvStatus entry_adv_status = 15; // 入口文案状态
  SuperPlayerStatus status = 16; //会员状态
  SuperPlayerStatus svip_status = 17; // svip状态
}

// 超级玩家类型
enum SuperPlayerVipType {
  SUPER_PLAYER_VIP_TYPE_UNSPECIFIED = 0; // 未开通
  SUPER_PLAYER_VIP_TYPE_SUPER_PLAYER = 1; // 超级玩家
  SUPER_PLAYER_VIP_TYPE_SUPER_PLAYER_ANNUAL = 2; // 年费超级玩家
  SUPER_PLAYER_VIP_TYPE_SVIP = 3; // SVIP
  SUPER_PLAYER_VIP_TYPE_SVIP_ANNUAL = 4; // 年费SVIP
}

//取会员等级信息等
message GetSuperPlayerInfoReq {
  uint32 super_player_uid = 1; //
  bool ignore_cache = 2;// 跳过缓存
  bool for_order = 3; // 是否是下单时调用，不做时间数据兼容
}

message GetSuperPlayerInfoResp {
  SuperPlayerInfo super_player_info = 1;
}

message GetPopUpMsgReq{
  uint32 super_player_uid = 1;
}

message GetPopUpMsgResp{
  string pop_up_msg = 1;
}

message PayRecord {
  int64 time_stamp = 1; //充值时间
  string desc = 2; //充值描述
  float price = 3; //套餐价格
  int64 begin_time = 4;  //订单开始时间
  int64 expire_time = 5; //订单结束时间时间
  string package_id = 6; //套餐ID
}

message GetSuperPlayerPayRecordListReq{
  uint32 super_player_uid = 1;
  int64 off = 2;
  int64 count = 3;
  uint32 market_id = 4;
}

message GetSuperPlayerPayRecordListResp {
  repeated PayRecord pay_record_list = 1;
}

message GetLatestPayRecordReq{
  uint32 super_player_uid = 1;
}

message GetLatestPayRecordResp {
  PayRecord pay_record = 1;
  uint32 timestamp = 2; // 最新的支付时间
}

//套餐签约信息
message SuperPlayerContractInfo {
  string pay_channel = 1; //签约渠道
  string status = 2;//签约状态
  string package_id = 3; //签约的套餐名
  string contract_id = 4;
  uint32 uid = 5;
  string extra_tag = 6;//
  uint32 next_pay_timestamp = 7; //下次扣款时间
  uint32 market_id = 8; //
  uint32 sign_time = 9 ;//签约时间
  float  price = 10 ;//下次扣款金额
  PackageType package_type = 11; // 套餐类型
}

//取会员签约信息
message GetSuperPlayerContractReq {
  uint32 super_player_uid = 1; //
  uint32 market_id = 2;
  string source = 3; //请求来源
}

message GetSuperPlayerContractResp {
  repeated SuperPlayerContractInfo super_player_contract_list = 1; //正在签约中的签约信息
  string last_ios_contract_id = 2; //最后一次签约的ID
  bool is_show_manager_contract_btn = 3; //是否显示签约管理入口
}

message CancelOrderReq {
  uint32 uid = 1;
  string order_id = 2;
}

message CancelOrderResp {}


message CancelContractReq {
  uint32 uid = 1;
  string contract_id = 2;
}

message CancelContractResp {}

message GetOrderStatusReq{
  string order_id = 1;
}

message GetOrderStatusResp{
  EnumOrderStatus status = 1;
}

//批量取接口
message BatchGetSuperPlayerInfoReq {
  repeated uint32 super_player_uid_list = 1; //UID列表,最多50个
}

message BatchGetSuperPlayerInfoResp {
  repeated SuperPlayerInfo super_player_info_list = 1; //会员信息列表
}

//会员特权
message Privilege {
  int64 id = 1;
  string name = 2; //权利名
  string desc = 3; //描述
  int64  level = 4; //最小多少级才有这个权利
}

message LevelPrivilege {
  int64 level = 1; //等级
  repeated Privilege privilege_list = 2; //等级对应的权益列表
}

//会员等级权益列表
message GetSuperPlayerConfReq{
}

message GetSuperPlayerConfResp {
  repeated LevelPrivilege level_privilege_list = 1; //权限列表
  int64 expire_notify_hours = 2; //差多少小时过期算即将过期
  repeated SuperPlayerEntryAdvConf adv_conf_list = 3;
}

message AddSuperPlayerValOrder {
  string order_id = 1; //订单号，有对对应系统保证不重复
  uint32 super_player_uid = 2; //需要加成长值的uid
  int64  inc_value = 3; //需要加的成长值，可能是负值
  string reason = 4; //加成长值的理由
  int64  server_time = 5; //发起增加值的服务的时间，同一个order_id不能有变化
  string parent_order = 6;// 对应的充值套餐
}

//增加会员值
message AddSuperPlayerValueReq {
  uint32 super_player_uid = 1;
  int64 incr_super_player_value = 2; //需要增加数量
  string order_id = 3; //订单ID，由调用这个接口方维护
  string reason = 4;//理由
  int64  server_time = 5; //任务服务时间戳，同一个单不能变
}

message AddSuperPlayerValueResp {
  int64 super_player_value = 1; //增加后当前值
}

message SuperPlayerValueRecord {
  uint32 super_player_uid = 1;
  int64 time_stamp = 2;
  string reason = 3;
  int64  incr_value = 4;
}

//查询会员成长值明细
message GetSuperPlayerValueRecordReq {
  uint32 super_player_uid = 1;
  int64 off = 2;
  int64 count = 3; //最多限制100条
}

message GetSuperPlayerValueRecordResp {
  repeated SuperPlayerValueRecord record_list = 1; //成长值明细
  int64 today_incr_value = 2; //今天增加总值
  int64 value_change_timestamp = 3; //最新变化时间戳
}

enum EnumTimerEventType {
  UNKOWN = 0;
  MEMBER_OPEN_EVENT = 1; //开通事件
  MEMBER_TIMEOUT_NOTIFY_EVENT = 2;//过期提醒事件
  EMEBER_TIMEOUT_EVENT = 3; //过期事件
  MEMBER_ADD_USER_SCORE = 4; //添加积分定时器
}

//定时事件
message TimerEvent {
  EnumTimerEventType event_type = 1;
  uint32 super_player_uid = 2;
  int64 trigger_time_stamp = 3;
  int64 months = 4; //开通天数，如果是开通事件需填这个参数，已经废弃
  int64 days = 5; //开通天数，如果是开通事件需填这个参数
  string package_id = 6; //
  string identity = 7;
  int64 add_value = 8;//
  string extra_para = 9; //
  int64 mins = 10; //开通体验时长
  string package_name = 11; // 开通套餐名称
  PackageType package_type = 12; // 套餐类型
  bool is_upgrade = 13; // 是否升级套餐
}

// 展示条件
message DisplayCondition
{
  enum CondType {
    ENUM_USER_ALL = 0;    // 全部用户
    ENUM_USER_SPECIFIED = 1;  // 指定用户
  }
  uint32 cond_type = 1;  // see CondType
  repeated uint32 super_player_status = 2; // 会员状态 see SuperPlayerStatus
  uint32 user_lv = 3;  // 账号等级
  uint32 rich_value = 4; // 财富值
  uint32 nobility_lv = 5; // 贵族等级
  repeated uint32 white_list = 6; // 白名单用户
  repeated uint32 svip_player_status = 7; // svip状态 see SuperPlayerStatus
  repeated string people_group_list = 8; // 人群包
}

// 套餐状态
enum PackageStatus
{
  ENUM_PACKAGE_STATUS_USING = 0; // 使用中
  ENUM_PACKAGE_STATUS_STOPING = 1; // 停用中
}

// 在售架套餐状态
enum SalePackageStatus
{
  ENUM_SALE_PACKAGE_STATUS_SHELF = 0;  // 上架中
  ENUM_SALE_PACKAGE_STATUS_UNSHELF = 1;  // 下架中
  ENUM_SALE_PACKAGE_STATUS_WAITSHELF = 2;  // 待上架
}

// 套餐类型
enum PackageType
{
  ENUM_PACKAGE_TYPE_NORMAL = 0; // 普通会员
  ENUM_PACKAGE_TYPE_SVIP = 1; // SVIP
}

//套餐
message Package {
  string id = 1; //套餐ID
  string product_id = 2; //苹果商店商品ID
  string name = 3; //套餐名称
  string desc = 4; //套餐说明
  string label = 5; //套餐角标文案  // 废弃
  float  original_price = 6; //套餐原价
  float  price = 7;//套餐售价
  int32  months = 8; //几个月    // 废弃
  int32  value = 9; //非自动续费增加的成长值；如果是包X的可能第一个周期和后续的周期值不一样，需要从配置中获取
  bool   auto = 10; //是否自动续费
  repeated string pay_channel_list = 11; // 支持的支付渠道列表
  int32  days = 12;   // 套餐会员天数
  uint32 package_status = 13;  // see PackageStatus
  int32  auto_value = 14;  // 自动续费增加的成长值
  string operator = 15;   // 操作人
  uint64 update_ts = 16;   // 更新时间
  float  discount_price = 17;//套餐售价
  uint32 market_id = 18; //marketID
  PackageType package_type = 19; // 套餐类型
  bool is_show_coupon = 20; // 是否展示优惠券
  uint32 group_id = 21; //分组ID, 0表示独立套餐
}

// 在售架的套餐信息
message SalePackage {
  uint32 sale_id = 1;
  Package package_info = 2;
  uint64 begin_ts = 3;
  uint64 end_ts = 4;
  DisplayCondition condition = 5;  // 展示条件
  uint32 weight = 6;   // 权重字段，用于排序
  uint32 sale_status = 7; // see SalePackageStatus
  string label = 8; //套餐角标文案
  string operator = 9;  // 操作人
  uint64 update_ts = 10;  // 更新时间
  string remarks = 11; // 运营备注
  uint32 market_id = 12; //marketID
  PackageType package_type = 13; // 套餐类型
  UpgradePackage upgrade_package_info = 14; // 升级套餐配置
  bool is_upgrade = 15; // 是否升级套餐
  bool is_show_coupon = 16; // 是否展示优惠券
  uint32 group_id = 17; //分组ID，当存在分组ID时，不需要填充package_info（仅新增或修改时需要填充）
}

// 增加套餐配置
message AddPackageReq
{
  Package package_info = 1;
  UpgradePackage upgrade_package_info = 2;
  bool is_upgrade = 3;
}
message AddPackageResp
{
}

// 修改套餐配置
message UpdatePackageReq
{
  enum UpdateType {
    ENUM_UPDATE_BASE = 0; // 更新基本信息
    ENUM_UPDATE_ENABLE = 1;  // 启用
    ENUM_UPDATE_UNABLE = 2;  // 停用
  }
  uint32 update_type = 1; // see UpdateType
  Package package_info = 2;
  UpgradePackage upgrade_package_info = 3;
  bool is_upgrade = 4;
}
message UpdatePackageResp
{
}

// 获取套餐列表
message GetPackageListByStatusReq
{
  uint32 package_status = 1;  // see PackageStatus
  PackageType package_type = 2; // 套餐类型
  bool is_show_coupon = 3; // 是否展示优惠券套餐
}
message GetPackageListByStatusResp
{
  repeated Package package_list = 1;
  repeated UpgradePackage upgrade_package_list = 2;
}

// 套餐子类型
enum PackageSubType {
    PackageSubType_Unspecified = 0; // 未指定
    PackageSubType_Normal = 1; // 单次购买
    PackageSubType_AutoRenew = 2; // 自动续费
    PackageSubType_UpgradeSVIP = 3; // 升级SVIP
    PackageSubType_UpgradeAll = 4; // 升级全部
}

message MarketInfo {
    uint32 market_id = 1;
    bool   is_ios = 2;
    string product_id = 3;   //苹果商店商品ID
    uint32 package_id = 4;   //套餐ID，新增填0
}
// 分组套餐配置
message GroupPackage {
  uint32 group_id = 1; //分组ID
  PackageType package_type = 2; // 套餐大类型
  PackageSubType sub_type  = 3; // 套餐类型
  string name = 4; //套餐名称
  string desc = 5; //套餐说明
  float  original_price = 6; //套餐原价
  float  price = 7;//套餐售价
  int32  value = 8; //非自动续费增加的成长值；如果是包X的可能第一个周期和后续的周期值不一样，需要从配置中获取
  int32  days = 9;   // 套餐会员天数
  int32  auto_value = 10;  // 自动续费增加的成长值
  repeated MarketInfo market_list = 11; //市场列表
  bool is_show_coupon = 12; // 是否展示优惠券
  uint32 package_status = 14;  // see PackageStatus
  string operator = 15;   // 操作人
}
// 添加分组套餐配置
message AddGroupPackageReq {
  GroupPackage group_package = 1;
}
message AddGroupPackageResp {
}

// 批量添加分组套餐配置
message BatchAddGroupPackageReq {
  repeated GroupPackage group_list = 1;
}
message BatchAddGroupPackageResp {
}

// 提前检查批量添加套餐
message CheckBatchAddGroupPackageReq {
  repeated GroupPackage group_list = 1;
}
message CheckBatchAddGroupPackageResp {
}


// 新增/编辑分组
message EditGroupPackageReq {
  uint32 group_id = 1; //分组ID, 0表示新增分组
  repeated MarketInfo market_list = 2; //市场列表
  PackageType package_type = 3; // 套餐大类型: 会员&SVIP,新增必填
  PackageSubType sub_type  = 4; // 套餐类型,新增必填
  string operator = 5;   // 操作人
}
message EditGroupPackageResp {
}

// 修改分组套餐
message ModifyGroupPackageReq {
  GroupPackage group_package = 1;
}
message ModifyGroupPackageResp {
}

// 设置分组状态
message SetGroupPackageStatusReq {
  uint32 group_id = 1; //分组ID
  uint32 package_status = 2;  // see PackageStatus
  string operator = 3;   // 操作人
}
message SetGroupPackageStatusResp {
}

// 查询分组
message GetGroupPackageReq {
  uint32 group_id = 1; //分组ID
}
message GetGroupPackageResp {
  GroupPackage group_package = 1;
}

message BatchGetGroupPackageReq {
  string key_word = 1; //关键字，为空时搜索全部（支持ID和名称的模糊搜索）
  PackageType package_type = 2; // 套餐大类型: 会员&SVIP,必填
}
message BatchGetGroupPackageResp {
  repeated GroupPackage group_list = 1;
}

// 增加在售架套餐配置
message AddSalePackageReq
{
  SalePackage info = 1;
  bool is_check = 2; // 是否是校验，若为校验，不会操作数据，仅做校验
}
message AddSalePackageResp
{
}

// 修改在售架套餐配置
message UpdateSalePackageReq
{
  SalePackage info = 1;
}
message UpdateSalePackageResp
{
}

// 获取在售架套餐列表
message GetSalePackageListByStatusReq
{
  uint32 status = 1;  // see SalePackageStatus
  bool with_cache = 2;   // 是否读内存缓存
  PackageType package_type = 3; // 套餐类型
  bool is_show_coupon = 4; // 是否展示优惠券套餐
}
message GetSalePackageListByStatusResp
{
  repeated SalePackage package_list = 1;
}

// 套餐排序
message SalePackageSortReq
{
  repeated uint32 sale_id_list = 1;
}
message SalePackageSortResp
{
}

// 校验上架套餐信息
message CheckSalePackageInfoReq
{
  string package_id = 1;   // 套餐id
  uint64 begin_ts = 2;
  uint64 end_ts = 3;
  uint32 sale_id = 4;   // 上架id
  PackageType package_type = 5; // 套餐类型
  uint32 group_id = 6; //分组ID，当存在分组ID时，不需要填充package_id
  bool is_upgrade = 7; // 是否升级套餐
  UpgradePackageType upgrade_package_type = 8; // 升级套餐类型
  uint32 market_id = 9; //marketID（若使用分组ID则可以不传）
}
message CheckSalePackageInfoResp
{
  repeated string already_on_shelf_list = 1; // 已经上架的套餐ID列表
}

message PackageExtraInfo {
  string package_id = 1;
  string pay_channel = 2;
  bool discount = 3;
}

//获取套餐配置
message GetPackageListReq {
}

message GetPackageListResp {
  repeated Package package_list = 1;//套餐配置列表
  repeated PackageExtraInfo package_extra_info_list = 2; //套餐额外信息
}

//获取套餐配置
message GetSvipPackageListReq {
}

message GetSvipPackageListResp {
  repeated UpgradePackage upgrade_package_list = 1; // 升级套餐配置列表
  repeated Package package_list = 2; // 套餐配置列表
  repeated PackageExtraInfo package_extra_info_list = 3; // 套餐额外信息
}

message PackageOrder {
  uint32 uid = 1;
  string package_id = 2;//套餐ID
  string pay_channel = 3; //支付渠道

  string order_type = 4; //订单类型类型 默认:BUY
  string os_type = 5; //系统类型 (i IOS系统 a android系统)
  string version = 6;
  string remark = 7; //备注
  string bundle_id = 8; //IOS的包名(appstore支付必传)
  string code = 9; //端外支付code
  uint32 market_id = 10; //marketID
  string device_id = 11; //
  PackageType package_type = 12; // 套餐类型
  repeated string original_transaction_ids = 13; // 苹果帐号使用，新版本用于AB帐号问题消除
}

//下单
message PlaceOrderReq{
  PackageOrder order = 1;
}

message PlaceOrderResp{
  string order_id = 1;
  string token = 2;
  string cli_order_no = 3; //第三方订单号
  string cli_order_title = 4;
  string tsk = 5; //加密字符串
  string channel_map = 6; //唤起支付渠道的参数, 用于安卓、IOS、前端使用
  bool is_first_open = 7; //是否是第一次开通
}

//订单状态
enum EnumOrderStatus {
  ORDER_INIT = 0; //下单
  ORDER_PAY_SUCCESS = 1; //支付成功
  ORDER_PAY_FAILED = 2; //支付失败
  ORDER_REVOKE = 3; //撤销
}

enum EnumOrderType {
  ORDER_TYPE_INITIATIVE_PAY = 0; // initiative
  ORDER_TYPE_AUTO_PAY = 1; //自动扣款
  ORDER_TYPE_UPGRADE = 2; //升级
  ORDER_TYPE_ROLLBACK = 3; // 升级撤销回滚订单
}

//支付中心告知订单支付成功
message NoticeOrderStatusReq{
  string order_id = 1;
  bool result = 2; //支付结果
  string reason = 3; //如果是失败，失败的原因
  uint32 super_player_uid = 4; //被开通人UID
  float total_fee = 5; //真正支付价钱
  string begin_time = 6; //这笔支付会员的开始时间 2016-01-02 15:04:05
  string end_time = 7; //这笔支付会员的结束时间
  string notice_time = 8; //统一对账时间
  string pay_channel = 9; //支付渠道
}

message NoticeOrderStatusResp {
  string result = 1;//ok
}

//通知解除连续签约
message NotifyContractReq {
  uint32 super_player_uid = 1;
  string pay_channel = 2; //支付渠道
  string order_id = 3;
  string contract_id = 4; //签约ID
  string status = 5; //签约状态
  string product_id = 6; //苹果商品ID
  string next_pay_time = 7; //下次扣款时间
  uint32 real_buyer_id = 8; //操作支付的人
}

message NotifyContractResp {
}

// 会员主入口广告语配置
message SuperPlayerEntryAdvConf
{
  enum TimeRangeType {
    TIME_RANGE_TYPE_UNKNOWN = 0;  // 无效时间范围
    TIME_RANGE_TYPE_LONG_TERM = 1;  // 长期有效
    TIME_RANGE_TYPE_SPECIFIED = 2;  // 指定时间范围
  }
  uint32 adv_id = 1; // 广告语ID
  repeated string adv_msg = 4;    // 入口文案
  uint32 red_dot_frequency = 5;  // 红点展示频率, 每 red_dot_frequency 天展示一次，若为9999则表示不展示红点，若为3650则表示只展示一次
  uint64 begin_ts = 6;  // 开始时间，当为长期有效时，默认为0值
  uint64 end_ts = 7;  // 结束时间，当为长期有效时，默认为0值
  string remarks = 8; // 运营备注
  uint64 update_ts = 9;  // 更新时间
  TimeRangeType time_range_type = 11;  // 生效时间范围类型
  string operator = 12; // 操作人，若为修改和新增需要传入新的操作人
}

// EntryAdvStatus 入口广告展示状态
// 包括了普通会员和SVIP会员
enum EntryAdvStatus
{
  ENUM_ENTRY_ADV_STATUS_NO_OPEN = 0;  // 从未开通普通超级玩家且从未开通SVIP的用户
  ENUM_ENTRY_ADV_STATUS_OPENING = 1;  // 普通超级玩家生效中或SVIP生效中用户，或超级玩家处于待生效且SVIP生效中，或超级玩家处于生效中且SVIP即将过期
  ENUM_ENTRY_ADV_STATUS_SOON_EXPIRE = 2;  // 普通超级玩家即将过期且SVIP即将过期用户，或普通超级玩家即将过期用户（仅剩普通超级玩家）、或SVIP即将过期用户（仅剩svip)
  ENUM_ENTRY_ADV_STATUS_EXPIRED = 3;  // 普通超级玩家已过期且SVIP已过期用户
}

// 获取有效会员主入口广告语配置
message GetSuperPlayerEntryAdvConfReq
{
}
message GetSuperPlayerEntryAdvConfResp
{
  repeated SuperPlayerEntryAdvConf conf_list = 1;
}

// 新增会员入口广告语配置
message AddSuperPlayerEntryAdvConfReq
{
  SuperPlayerEntryAdvConf adv_conf = 1;
}
message AddSuperPlayerEntryAdvConfResp
{
}

// 更新会员主入口广告语配置
message UpdateSuperPlayerEntryAdvConfReq
{
  SuperPlayerEntryAdvConf adv_conf = 1;
}
message UpdateSuperPlayerEntryAdvConfResp
{
}

// 删除会员主入口广告语配置
message DelSuperPlayerEntryAdvConfReq
{
  uint32 adv_id = 1;
}
message DelSuperPlayerEntryAdvConfResp
{
}

enum EntryAdvType
{
  ENUM_ENTRY_ADV_TYPE_All = 0;  // 查询全部
  ENUM_ENTRY_ADV_TYPE_ACTIVE = 1;  // 查询生效中的
  ENUM_ENTRY_ADV_TYPE_EXPIRE = 2;  // 查询过期的
  ENUM_ENTRY_ADV_TYPE_SOON_ACTIVE = 3;  // 查询即将生效的
}

// 获取所有的会员主入口广告语配置
message GetAllSuperPlayerEntryAdvConfReq
{
  EntryAdvType adv_type = 1;  // 查询类型（注：查询类型根据当前时间进行区分）
  uint32 page = 2;  // 页码
  uint32 page_size = 3;  // 每页数量
}
message GetAllSuperPlayerEntryAdvConfResp
{
  repeated SuperPlayerEntryAdvConf conf_list = 1;
  uint32 total = 2;  // 总数
}


enum PlatFormType
{
  ENUM_PLATFORM_ALL = 0;  // 所有平台
  ENUM_PLATFORM_ANDROID = 1;   // android
  ENUM_PLATFORM_IOS = 2;  // ios
}

enum BannerAdvStatus
{
  ENUM_BANNER_ADD_USING = 0; //使用中
  ENUM_BANNER_ADD_END = 1;  // 结束
}


// 会员页banner广告位
message BannerAdvConf
{
  uint32 adv_id = 1;
  uint32 platform_type = 2; // 展示系统
  string jump_url = 3;   // 跳转链接
  uint64 begin_ts = 4;
  uint64 end_ts = 5;
  string banner_img = 6;   // 会员页广告位
  DisplayCondition condition = 7;  // 展示条件
  string remarks = 8;  // 备注
  uint32 weight = 9;  // 权重，排序
  uint32 adv_status = 10; // 广告状态  see BannerAdvStatus
  string operator = 11;   //操作人
}

// 增加banner广告位
message AddBannerAdvConfReq
{
  BannerAdvConf conf = 1;
}
message AddBannerAdvConfResp
{
}

// 更新banner广告位
message UpdateBannerAdvConfReq
{
  BannerAdvConf conf = 1;
}
message UpdateBannerAdvConfResp
{
}

// 删除banner广告位
message DelBannerAdvConfReq
{
  uint32 adv_id = 1;
}
message DelBannerAdvConfResp
{
}

// 根据状态获取banner广告位列表
message GetBannerAdvConfListByStatusReq
{
  uint32 adv_status = 1; //  see BannerAdvStatus
  bool  with_cache = 2;  // 是否读缓存
}
message GetBannerAdvConfListByStatusResp
{
  repeated BannerAdvConf conf_list = 1;
}

// 广告排序
message BannerAdvConfSortReq
{
  repeated uint32 adv_id_list = 1;
}
message BannerAdvConfSortResp
{
}


// 支付相关接口
message ApiResponseHeader {
  string result = 1;
  string message = 2;
}

//连续签约额外参数
message ApiPeriodParam {
  string contract_id = 1; //签约ID
  string plan_id = 2; //签约模板
  string period_type = 3; //周期类型 DAY / MONTH
  int64 period = 4; //周期, 与周期类型并用, 如 30DAY, 3MONTH
  string contract_notify_url = 5; //业务方的签约回调地址
  string execute_time = 6; //执行首次扣费时间 (yyyy-MM-dd HH:mm:ss)
  string product_code = 7; //商品code（支付宝专用参数，用来区分VIP和SVIP套餐，SVIP版本补充参数）
  string single_amount = 8; //单次扣款金额(支付宝订阅必传，单位元，用来标识下次扣款金额)
}

message ApiPlaceOrderReq{
  string order_type = 1; //订单类型类型 默认:BUY
  string os_type = 2; //系统类型 (i IOS系统 a android系统)
  string pay_channel = 3; //支付渠道,货币组指定
  string business_id = 4; //子业务id，货币组指定
  string fm = 5; //TT语音、欢游等对应在货币系统的标识,用于APP支付参数解密, 若payChannel不为空,必传
  string version = 6;
  string cli_order_no = 7;
  string cli_buyer_id = 8;
  string cli_price = 9;
  string cli_order_title = 10;
  string cli_order_desc = 11;
  string cli_notify_url = 12;
  string create_time = 13; //外部订单时间(“yyyy-MM-dd HH:mm:ss”)
  string remark = 14; //备注
  string bundle_id = 15; //IOS的包名(appstore支付必传)
  string product_id = 16; //商品ID(appstore必传)

  ApiPeriodParam period_param = 17; //签约信息
  string code = 18; //端外支付code
  string time_out = 19; //订单支持超时时间（分钟）
  repeated string original_transaction_ids = 20; // 苹果帐号使用，新版本用于AB帐号问题消除
  uint64 uid = 21; //用户ID，补充参数，货币grpc接口使用
  string business_scene_code = 22; // 业务场景码，用于区分业务属性
}

message ApiPlaceOrderResp {
  string order_no = 1;
  string token = 2;
  string cli_order_no = 3;
  string cli_order_title = 4;
  string order_price = 5;
  string tsk = 6; //加密字符串
  string channel_map = 7 ;//唤起支付渠道的参数, 用于安卓、IOS、前端使用
}

message ApiGetContractReq{
  string contract_id = 1;
  string business_id = 2;
  string buyer_id = 3;
  string product_code = 4; //商品code（支付宝专用参数，用来区分VIP和SVIP套餐，SVIP版本补充参数）
  uint64 uid = 5; //用户ID，补充参数，货币grpc接口使用
  string fm = 6; //TT语音、欢游等对应在货币系统的标识,用于APP支付参数解密, 若payChannel不为空,必传
}

message ApiGetContractResp{
  string code = 1; //判断查询成功与否(成功为 “10000”)
  string contract_id = 2;
  string business_id = 3;
  string client_id = 4;
  string pay_channel = 5;
  string order_no = 6;
  string id = 7;
}

message ApiCancelContractReq {
  string contract_id = 1;
  string business_id = 2;
  string buyer_id = 3;
  string contract_notify_url = 4;
  string product_code = 5; //商品code（支付宝专用参数，用来区分VIP和SVIP套餐，SVIP版本补充参数）
  uint64 uid = 6; //用户ID，补充参数，货币grpc接口使用
  string fm = 7; //TT语音、欢游等对应在货币系统的标识,用于APP支付参数解密, 若payChannel不为空,必传
}

message ApiCancelContractResp{
  string code = 1;
  string msg = 2;
  string pay_channel = 3;
  string order_no = 4;
}

message DeductParam {
  string contract_id = 1;
  string plan_id = 2; //签约模板
  string product_code = 3; //商品code（支付宝专用参数，用来区分VIP和SVIP套餐，SVIP版本补充参数）
  string single_amount = 4; //单次扣款金额(支付宝订阅必传，单位元，用来标识下次扣款金额)
}

message ApiAutoPayReq{
  string order_type = 1;
  string os_type = 2;
  string pay_channel = 3;
  string business_id = 4;
  string fm = 5;
  string version = 6;
  string cli_order_no = 7;
  string cli_buyer_id = 8;
  string cli_price = 9;
  string cli_order_title = 10;
  string cli_order_desc = 11;
  string cli_notify_url = 12;
  string create_time = 13;
  string remark = 14;
  string bundle_id = 15;
  string product_id = 16;
  string user_ip = 17;
  DeductParam deduct_param = 18;
  uint64 uid = 19; //用户ID，补充参数，货币grpc接口使用
  string business_scene_code = 22; // 业务场景码，用于区分业务属性
}

message ApiAutoPayResp {
  string order_no = 1;
  string token = 2;
  string cli_order_no = 3;
  string cli_order_title = 4;
  string order_price = 5;
  string tsk = 6;
  string channel_map = 7;
}

message ApiDelayPayReq {
  string contract_id = 1;
  string business_id = 2;
  string buyer_id = 3;
  string deduct_time = 4;
  string memo = 5;
  string pay_channel = 6;
  uint64 uid = 7; //用户ID，补充参数，货币grpc接口使用
  string fm = 8; //TT语音、欢游等对应在货币系统的标识,用于APP支付参数解密, 若payChannel不为空,必传
}

message ApiDelayPayResp {
  string head = 1;
}


// 会员身份提示弹窗
message SuperPlayerIdentityPopupReq {
  enum ReqType {
    REQ_QUERY = 0;   // 查询弹窗信息
    REQ_FINISHED = 1;  // 设置已完成弹窗提示
    REQ_VALUE_CHANGE_TIME = 2; //设置成长值变化更新时间
    REQ_WEB_CANCEL_CONTRACT = 3; //会员页取消订阅提示
  }
  uint32 req_type = 1;    // see  ReqType
  uint32 uid = 2;
  uint32 val_change_timestamp = 3;
  uint32 market_id = 4;
}
message SuperPlayerIdentityPopupResp {
  bool is_need_push = 1;    // 是否需要弹窗
  uint32 before_lv = 2;    // 升级前等级
  uint32 after_lv = 3;     // 升级后等级
  string pop_up_msg = 4;   //弹框提示文案
}

//会员充值
message SuperPlayerRechargeEvent {
  uint32 super_player_uid = 1; //会员ID
  uint32 create_time = 2; //事件产生时间（订单支付时间）
  uint32 begin_time = 3; //会员开始时间
  uint32 expire_time = 4;//会员结束时间
  uint32 super_player_value = 5;//会员成长值
  string order_id = 6; //充值订单
  string package_id = 7; //购买的套餐ID
  string pay_channel = 8; //购买渠道
  uint32 add_super_player_value = 9; //增加的成长值
  string package_name = 10; //购买的套餐名称
  uint32 package_type = 11; // 套餐类型
  bool is_vip_before = 12; // 订单开通前是否是VIP
  bool is_svip_before = 13; // 订单开通前是否是SVIP
  uint32 limit_id = 14; // 限购ID（若无限购则ID为0）
  uint32 pay_create_time = 15; // 支付创建时间(订单下单的时间)
}

message PlaceAutoPayOrderReq {
  string produce_id = 1; //APPSTORE 商品ID
  string contract_id = 2; //签约ID
  bool has_discount = 3; //是否有折扣
}

message PlaceAutoPayOrderResp{
  string order_id = 1;
}

message GetDeviceBuyRecordReq{
  string device_id = 1;
}
message GetDeviceBuyRecordResp{
  string last_buy_order_id = 1;
}

// 获取在售活动套餐列表
message GetSalePackageListForActivityReq
{
  bool with_cache = 1;   // 是否读内存缓存
}
message GetSalePackageListForActivityResp
{
  repeated SalePackage package_list = 1;
}

// 发放会员装扮体验包
message SendSuperPlayerDressExperienceReq {
  uint32 super_player_uid = 1; // 会员ID
  uint64 incr_conf_package = 2; // 需要增加的对应配置包，若传0则为默认配置
  string order_id = 3; // 订单ID，由调用这个接口方维护
  string reason = 4;// 理由
  int64  server_time = 5; // 任务服务时间戳，同一个单不能变
}

message SendSuperPlayerDressExperienceResp {
}

//升级套餐
message UpgradePackage {
  string id = 1; //升级套餐ID
  string product_id = 2; //苹果商店商品ID
  string name = 3; //套餐名称
  string desc = 4; //套餐说明
  UpgradePackageType upgrade_package_type = 5; //套餐类型
  float  original_price = 6; //套餐原价
  float  price = 7;//套餐售价
  int32  value = 8; //非自动续费增加的成长值
  repeated string pay_channel_list = 9; // 支持的支付渠道列表
  int32  upgrade_days = 10;   // 升级
  uint32 package_status = 11;  // see PackageStatus
  string operator = 12;   // 操作人
  uint64 update_ts = 13;   // 更新时间
  float  discount_price = 14;//套餐售价
  uint32 market_id = 15; //marketID
  UpgradeAllConfig upgrade_all_config = 16; // 升级全部（ENUM_UPGRADE_PACKAGE_TYPE_UPGRADE_ALL）的情况下专用的字段
  bool is_show_coupon = 17; // 是否展示优惠券
  uint32 group_id = 18; //分组ID, 0表示独立套餐
}

// 套餐类型
enum UpgradePackageType
{
  ENUM_UPGRADE_PACKAGE_TYPE_NORMAL = 0; // 普通升级SVIP
  ENUM_UPGRADE_PACKAGE_TYPE_UPGRADE_ALL = 1; // 升级全部
}

message UpgradeAllConfig{
  float upgrade_average_price = 1; // 升级均价
  float upgrade_average_value = 2; // 平均成长值
  float upgrade_average_original_price = 3; // 平均原价
  repeated UpgradePrice upgrade_price_list = 4; // 升级均价 - 分段， 如果这部分存在，就会忽略upgrade_average_price和upgrade_average_value
}

message UpgradePrice{
  uint32 start_days = 1;
  uint32 end_days = 2;
  bool is_infinite = 3; // （结束时间）是否无限
  float price = 4; // 该时间区间内的均价
  float value = 5; // 该时间区间内的平均成长值
}

// GetCanUpgradeDaysReq 获取可以升级的时间
message  GetCanUpgradeDaysReq {
  uint32 super_player_uid = 1;
}
message GetCanUpgradeDaysResp {
  uint32 super_player_uid = 1;
  uint32 can_upgrade_days = 2;
}

// GetUserOrderLeftDaysReq
message GetUserOrderLeftDaysReq {
  uint32 super_player_uid = 1;
}
message GetUserOrderLeftDaysResp {
  uint32 super_player_uid = 1;
  uint32 vip_left_days = 2;
  uint32 svip_left_days = 3;
}

message ExpireSuperPlayerDaysReq {
  uint32 uid = 1;
  uint32 days = 2;
  PackageType package_type = 3;
}
message ExpireSuperPlayerDaysResp {
}

message ClearSuperPlayerSignReq {
  uint32 uid = 1;
}
message ClearSuperPlayerSignResp {
}

message PushAlipayNextContractReq {
  uint32 uid = 1;
  PackageType package_type = 2;
}
message PushAlipayNextContractResp {
  string err_msg = 1;
}

// PackageLimit 限购信息
message PackageLimit {
  uint32 id = 1; // 限购ID
  string limit_name = 2; // 限购名称
  PackageType package_type = 3; // 套餐类型
  EnumOrderType order_type = 4; // 订单类型
  repeated LimitPackageIdInfo package_id_info = 5; // 限购套餐ID信息列表
  int64 start_time = 6; // 限购开始时间
  int64 end_time = 7; // 限购结束时间
  uint32 limit_count = 8; // 限购数量
  string remark = 9; // 备注
  string operator = 10; // 操作人
  int64 update_ts = 11; // 更新时间
}

// LimitPackageIdInfo 限购套餐ID信息
message LimitPackageIdInfo {
  uint32 market_id = 1; // 马甲包
  uint32 ios_package_id = 2; // ios套餐ID
  LimitSalePackage ios_sale_package = 3; // 限购套餐信息列表
  uint32 android_package_id = 4; // android套餐ID
  LimitSalePackage android_sale_package = 5; // 限购套餐信息列表
}

// AddPackageLimitReq 添加限购信息
message AddPackageLimitReq {
  PackageLimit limit_info = 1;
  string operator = 2; // 操作人
  bool is_check = 3; // 是否是校验，若为校验，不会操作数据，仅做校验
}
message AddPackageLimitResp {
}

// UpdatePackageLimitReq 更新限购信息
message UpdatePackageLimitReq {
  PackageLimit limit_info = 1;
  string operator = 2; // 操作人
  bool is_check = 3; // 是否是校验，若为校验，不会操作数据，仅做校验
}
message UpdatePackageLimitResp {
}

// DelPackageLimitReq 删除限购信息
message DelPackageLimitReq {
  uint32 id = 1; // 限购ID
  string operator = 2; // 操作人
}
message DelPackageLimitResp {
}

// BatchGetPackageLimitReq 批量获取限购信息
message BatchGetPackageLimitReq {
  int64 page = 1; // 页码
  int64 limit = 2; // 每页数量
  int64 start_time = 3; // 开始时间(选填)
  int64 end_time = 4; // 结束时间(选填)
  repeated uint32 limit_id_list = 5; // 限购ID列表(选填)
  PackageType package_type = 6; // 套餐类型(如果使用限购ID列表，则必须要传)
}
message BatchGetPackageLimitResp {
  repeated PackageLimit limit_list = 1; // 限购信息列表
  int64 total = 2; // 总数
}

// GetPackageLimitById 获取限购信息
message GetPackageLimitByIdReq {
  uint32 id = 1; // 限购ID
}
message GetPackageLimitByIdResp {
  PackageLimit limit_info = 1; // 限购信息
}

// GetUserLimitPackageIdListReq 获取用户限购套餐ID列表
message GetUserLimitPackageIdListReq {
  uint32 uid = 1;
}
message GetUserLimitPackageIdListResp {
  repeated UserLimitPackage limit_pkg_list = 1;
}

// UserLimitPackage 用户限购套餐ID
message UserLimitPackage {
  string package_id = 1;
  PackageType package_type = 2;
}

message GetSalePackageForLimitReq {
  PackageType package_type = 1; // 套餐类型
  EnumOrderType order_type = 2; // 订单类型
  uint32 package_id = 3; // 套餐ID
}
message GetSalePackageForLimitResp {
  LimitSalePackage limit_info = 1;
}

// LimitSalePackage 限购在售架套餐信息
message LimitSalePackage {
  PackageType package_type = 1; // 套餐类型
  EnumOrderType order_type = 2; // 订单类型
  string pkg_id = 3; //套餐ID
  repeated string pay_channel_list = 4; // 支持的支付渠道列表
  int32  days = 5; // 套餐会员天数
  string price = 6; //套餐售价
  int32  value = 7; //非自动续费增加的成长值;
  string os_type = 8; //系统类型 (i IOS系统 a android系统)
  string original_price = 9; //套餐原价
  uint32 market_id = 10; // marketID
  string product_id = 11; //苹果商店商品ID
  uint32 limit_id = 12; // 限购ID
  string name = 13; // 套餐名称
  string desc = 14; // 套餐说明
}

message GetSalePackageForActivityNewReq {
  repeated ActivitySaleInfo activity_sale_info_list = 1; // 活动售卖信息列表（可以包含下架的或上架中的套餐）
}
message GetSalePackageForActivityNewResp {
  repeated ActivitySalePackage pkg_info_list = 1;
}

message GetPackageForActivityByLimitIdsReq {
  repeated uint32 limit_id_list = 1; // 限购ID列表
  PackageType package_type = 2; // 套餐类型
}

message GetPackageForActivityByLimitIdsResp {
  repeated LimitSalePackage limit_info_list = 1;
}

// ActivitySaleInfo 活动售卖信息
message ActivitySaleInfo {
  PackageType package_type = 1; // 套餐类型
  EnumOrderType order_type = 2; // 订单类型
  uint32 pkg_id = 3; // 套餐ID
}

// ActivitySalePackage 活动套餐信息
message ActivitySalePackage {
  PackageType package_type = 1; // 套餐类型
  EnumOrderType order_type = 2; // 订单类型
  string pkg_id = 3; //套餐ID
  repeated string pay_channel_list = 4; // 支持的支付渠道列表
  int32  days = 5; // 套餐会员天数
  string original_price = 6; //套餐原价
  string price = 7; // 套餐售价
  int32  value = 8; // 非自动续费增加的成长值;
  string os_type = 9; // 系统类型 (i IOS系统 a android系统)
  uint32 market_id = 10; // marketID
  string product_id = 11; //苹果商店商品ID
  string name = 12; // 套餐名称
  string desc = 13; // 套餐说明
}

// RevokeOrderReq 撤销订单请求
message RevokeOrderReq {
  string order_id = 1; // 订单ID
  string notify_time = 2; // 通知时间
  uint32 uid = 3; // 用户UID
}
message RevokeOrderResp {
}

message IsAnnualSvipBeforeActivityReq {
  uint32 uid = 1;
}
message IsAnnualSvipBeforeActivityResp {
  uint32 uid = 1;
  bool is_annual_svip = 2;
}

message GetRenewalRemindInfoReq {
  uint32 uid = 1;
}
message GetRenewalRemindInfoResp {
  int64 vip_expire_time = 1; // VIP到期时间
  int64 svip_expire_time = 2; // SVIP到期时间
  uint32 lost_value = 3; // 会员到期失去的成长值
  bool vip_signed = 4; // VIP是否签约
  bool svip_signed = 5; // SVIP是否签约
}

// 获取在售架优惠套餐列表
message GetSalePackageWithCouponReq
{
}
message GetSalePackageWithCouponResp
{
  repeated SalePackage package_list = 1;
}

message CouponPopUpLimit {
  uint32 scene = 1; // 场景
  uint32 day_limit = 2;    // 每日弹窗次数限制
  uint32 week_limit = 3;   // 每周弹窗次数限制
  uint32 cooldown_time = 4; // 弹窗冷却时间
  bool pop_up_switch = 5; // 弹窗开关
  uint32 pay_cooldown_time = 6; // 支付冷却时间

}

// 获取优惠券弹窗
message GetCouponPopUpLimitReq {
}

message GetCouponPopUpLimitResp {
  repeated CouponPopUpLimit limit_list = 1; // 弹窗限制列表
  string resource_md5 = 2; // 资源MD5
  string resource_url = 3; // 资源URL
  uint32 boundary_time = 4; // 边界时间
}

// 优惠券弹窗记录
message CouponPopUpRecordReq {
  uint32 uid = 1; // 用户ID
  uint32 scene = 2; // 场景
}

message CouponPopUpRecordResp {
}

// 获取优惠券弹窗次数
message GetCouponPopUpCountReq {
  uint32 uid = 1; // 用户ID
  uint32 scene = 2; // 场景
}

message GetCouponPopUpCountResp {
  uint32 uid = 1; // 用户ID
  uint32 scene = 2; // 场景
  uint32 day_count = 3; // 当日次数
}

// 优惠券且支付统计
message CouponPopUpAndPayReq {
  uint32 uid = 1; // 用户ID
  uint32 scene = 2; // 场景
  bool is_pay = 3; // 是否支付
}

message CouponPopUpAndPayResp {

}

message GetCouponPopUpAndUnpayReq {
  uint32 uid = 1; // 用户ID
  uint32 scene = 2; // 场景
}

message GetCouponPopUpAndUnpayResp {
  uint32 uid = 1; // 用户ID
  uint32 scene = 2; // 场景
  uint32 count = 3; // 弹窗且未支付次数
  uint32 timestamp = 4; // 最近一次弹窗且未支付时间
}

message GetPackageByIdReq {
  string package_id = 1; // 套餐ID
  PackageType package_type = 2; // 套餐类型
  bool is_upgrade = 3; // 是否升级套餐
}

message GetPackageByIdResp {
  Package package = 1;
}

// 校验套餐是否包含限购
message ValidPackagesHasLimitReq {
  uint32 group_id = 1; // 分组ID（与套餐ID二选一）
  uint32 package_id = 2; // 套餐ID（与分组ID二选一）
  PackageType package_type = 3; // 套餐类型
}
message ValidPackagesHasLimitResp {
  bool has_limit = 1;
  repeated uint32 limit_id_list = 2;
  uint64 first_limit_start_time = 3; // 第一个限购开始时间
  uint64 first_limit_end_time = 4; // 第一个限购结束时间
}