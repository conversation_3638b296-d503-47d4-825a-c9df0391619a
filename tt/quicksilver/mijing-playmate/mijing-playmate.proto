syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/mijing-playmate";
package mijing_playmate;

service MijingPlaymate {
  //邀请拼场列表
  rpc PiecingGroupList(PiecingGroupListReq)returns(PiecingGroupListResp);
  //邀请拼场
  rpc InvitedPiecingGroup(InvitedPiecingGroupReq)returns(InvitedPiecingGroupResp);
  // 设置拼场通知开关
  rpc SetPiecingGroupNotify(SetPiecingGroupNotifyReq) returns (SetPiecingGroupNotifyResp);
  // 获取拼场通知配置
  rpc GetPiecingGroupNotify(GetPiecingGroupNotifyReq) returns (GetPiecingGroupNotifyResp);
  // 批量获取邀请状态
  //  rpc BatchCheckAlreadyInvite(BatchCheckAlreadyInviteReq) returns (BatchCheckAlreadyInviteResp);
  // 是否是熟人
  rpc IsAcquaintance(IsAcquaintanceReq) returns (IsAcquaintanceResp);
  // 根据剧本/章节/玩本模式做匹配 顺便直接上报了用户数据
  rpc ScenarioMatch(ScenarioMatchRequest) returns (ScenarioMatchResponse);
  // 根据目标用户做匹配 顺便直接上报了用户数据
  rpc ScenarioPair(ScenarioPairRequest) returns (ScenarioPairResponse);
  // 根据ScenarioMatch匹配到的结果做绑定
  rpc ScenarioBind(ScenarioBindRequest) returns (ScenarioBindResponse);
  // 根据用户申请匹配的信息获取推荐用户列表
  rpc ScenarioRecommendUserList(ScenarioRecommendUserListRequest) returns (ScenarioRecommendUserListResponse);
  // 标记用户已读数据
  rpc ScenarioRecommendUserMarkRead(ScenarioRecommendUserMarkReadRequest) returns (ScenarioRecommendUserMarkReadResponse);
  // 获取用户已读历史
  rpc ScenarioRecommendUserGetHistory(ScenarioRecommendUserGetHistoryRequest) returns (ScenarioRecommendUserGetHistoryResponse);
  // 上报邀人组队的邀请状态
  rpc ReportChannelInviteTeam(ReportChannelInviteTeamRequest) returns (ReportChannelInviteTeamResponse);
  // 获取邀人组队的邀请状态
  rpc GetReportChannelInviteTeam(GetReportChannelInviteTeamRequest) returns (GetReportChannelInviteTeamResponse);
  // 验证用户是否可以进入房间
  rpc CheckChannelMatchLimitation(CheckChannelMatchLimitationRequest) returns (CheckChannelMatchLimitationResponse);
  // 获取首页我的小分队娱乐页入口开关
  rpc GetHomeTeamEntertainmentEntrance(GetHomeTeamEntertainmentEntranceRequest) returns (GetHomeTeamEntertainmentEntranceResponse);
  // 上报首页我的小分队娱乐页入口开关
  rpc ReportHomeTeamEntertainmentEntrance(ReportHomeTeamEntertainmentEntranceRequest) returns (ReportHomeTeamEntertainmentEntranceResponse);
  // 获取首页我的小分队用户互动数据
  rpc GetHomeTeamFriendData(GetHomeTeamFriendDataRequest) returns (GetHomeTeamFriendDataResponse);
  // 上报首页我的小分队用户互动数据
  rpc ReportHomeTeamFriendData(ReportHomeTeamFriendDataRequest) returns (ReportHomeTeamFriendDataResponse);
}

// 获取拼场通知配置
message GetPiecingGroupNotifyReq{
  uint32 uid = 1;
}

message GetPiecingGroupNotifyResp{
  bool is_enable = 1; // 是否可用
}

message IsAcquaintanceReq{
  uint32 uid1 = 1;
  uint32 uid2 = 2;
}
message IsAcquaintanceResp{
  bool is_acquaintance = 1;
}

enum PiecingGroupNotifyStatus{
  PiecingGroupNotifyStatus_UNDEFINED = 0;
  PiecingGroupNotifyStatus_TEM_DISABLE = 1; // 临时关闭
  PiecingGroupNotifyStatus_FOREVER_DISABLE = 2; // 永久关闭
  PiecingGroupNotifyStatus_ENABLE = 3; // 打开
}

// 设置拼场通知开关
message SetPiecingGroupNotifyReq{
  uint32 uid = 1;
  PiecingGroupNotifyStatus status = 2; // 拼场配置状态
}

message SetPiecingGroupNotifyResp{}

//邀请拼场列表
message PiecingGroupListReq{
  uint32 uid = 1;
  int64 count = 2;
}

message PiecingGroupListResp{
  repeated uint32 uid = 1;
}

//邀请拼场
message InvitedPiecingGroupReq{
  uint32 inviter_uid = 1;
  repeated PiecingGroupParameter invitee_list = 2;
  enum InviteSource{
    INVITE_SOURCE_CHANNEL_GROUP_LIST = 0;
  }

  InviteSource invite_source = 3;

  uint32 channel_id = 4;
  string channel_name = 5;
  string play_mode_name = 6;
  string inviter_account = 7;
  uint32 tab_id = 8;
}

message InvitedPiecingGroupResp{

}

//拼场邀请参数
message PiecingGroupParameter{
  uint32 uid = 1;
  string footprint = 2;// 推荐链路上报用
}

/*
message BatchCheckAlreadyInviteReq{
  uint32 inviter_uid = 1;
  repeated uint32 invitee_uid_list = 2;
}

message BatchCheckAlreadyInviteResp{
  map<uint32, bool> invited_status_map = 1;
}*/

// = = = = = 「Start」 v1.36.0 剧本匹配 = = = = =

// 这里的用户结构啰
message ScenarioMatchUser {
  uint32 uid = 1; // 用户ID
  uint32 channel_id = 2; // 都是点击后传上来的，保留下，后面要直接跳房间怎么处理呢
  uint32 scenario_id = 3; // 剧本ID
  repeated ScenarioPlot plots = 4; // 我的章节开通结果？是否回传呢？我觉得不用也行
  ScenarioPlayMode play_mode = 5; // 玩本方式
  ScenarioPlotMode plot_mode = 6; // 是否限制章节
  bool is_matching = 7; // 是否匹配中
  string plot_name = 8; // 匹配中用户的匹配章节
  string debug = 11; // 调试展示
}

enum ScenarioMatchRequestType {
  SCENARIO_MATCH_REQUEST_TYPE_UNSPECIFIED = 0;
  SCENARIO_MATCH_REQUEST_TYPE_MATCH       = 1;
  SCENARIO_MATCH_REQUEST_TYPE_CANCEL      = 2;
}

// 玩本方式
enum ScenarioPlayMode {
  SCENARIO_PLAY_MODE_UNSPECIFIED  = 0;
  SCENARIO_PLAY_MODE_UNLIMITED    = 1; // 不限
  SCENARIO_PLAY_MODE_TEXT         = 2; // 文字
  SCENARIO_PLAY_MODE_VOICE        = 3; // 语音
}

// 是否当前本限制
enum ScenarioPlotMode {
  SCENARIO_PLOT_MODE_UNSPECIFIED = 0;
  SCENARIO_PLOT_MODE_UNLIMITED   = 1; // 不限
  SCENARIO_PLOT_MODE_CURRENT     = 2; // 当前章节
}

enum ScenarioMatchResultType {
  SCENARIO_MATCH_RESULT_TYPE_UNSPECIFIED  = 0;
  SCENARIO_MATCH_RESULT_TYPE_PADDING      = 1; // 默认等待中
  SCENARIO_MATCH_RESULT_TYPE_MATCHED      = 2; // 匹配成功直接返回
  SCENARIO_MATCH_RESULT_TYPE_CANCEL       = 3; // 用户操作房间类型等 被动取消匹配推送 不会在同步接口返回
  SCENARIO_MATCH_RESULT_TYPE_UNAUTHORIZED = 4; // 匹配操作 目标剧本ID 相关章节无权限
  SCENARIO_MATCH_RESULT_TYPE_MISSING      = 5; // 匹配操作 目标不存在
  SCENARIO_MATCH_RESULT_TYPE_NOT_MATCHING = 6; // 匹配操作 目标已经不在匹配中
}

// 匹配结果
message ScenarioMatchResult {
  uint64 match_id = 1; // 配对唯一Key，在Accept 回传
  ScenarioMatchUser user = 2; // 我的用户信息
  ScenarioMatchUser match_user = 3; // 匹配到的用户
  uint32 matched_channel_id = 4; // 目标房间ID
  uint32 matched_scenario_id = 5; // 目标剧本
  string matched_plot_id = 6; // 目标章节
  ScenarioMatchResultType type = 7; // 匹配结果类型 等待/成功
  uint32 matched_timeout = 8; // 匹配成功后的等待超时
  string matched_scenario_bg = 9; // 匹配背景信息
}

// 剧本数据，还要根据客户端传上来看看，为啥不用chapter？对其客户端拿到的数据名称
message ScenarioPlot {
  string plot_id = 1; // 章节ID
  string plot_name = 2; // 章节
  bool is_unlock = 3; // 是否已授权
  bool is_selected = 4; // 是否当前章节
}

// 查询条件
message ScenarioQueryOption {
  ScenarioPlayMode play_mode = 1; // 玩本方式
  ScenarioPlotMode plot_mode = 2; // 是否限制章节
  uint32 scenario_id = 3; // 剧本ID
  repeated ScenarioPlot plots = 4; // 剧本章节+是否已解锁+是否当前章节
}

// rpc 点击快速匹配接口 调完接口就等呗，取消也是这个
message ScenarioMatchRequest {
  uint32 uid = 1; // 用户ID
  ScenarioQueryOption query = 2;
  uint32 channel_id = 3; // 房间ID
  ScenarioMatchRequestType request_type = 4;  // 取消也用这个接口，取消只需要传个类型上来就行了
  repeated uint32 except_uid_list = 5; // 过滤的uid
}
message ScenarioMatchResponse {
  ScenarioMatchResult result = 1; // 如果立刻匹配上了 就直接返回结果了，取消匹配不返回
}

// rpc 点击匹配TA接口 调完接口就等呗，没取消
message ScenarioPairRequest {
  uint32 matching_uid = 1; // 谁点击匹配TA
  uint32 matched_uid = 2; // 点了谁
}
message ScenarioPairResponse {
  ScenarioMatchResult result = 1; // 如果立刻匹配上了 就直接返回结果了
}


// ================================================

// rpc 等待N秒后未收到MatchPush 主动获取用户列表
message ScenarioRecommendUserListRequest {
  uint32 uid = 1;
  repeated uint32 except_uid_list = 2; // 过滤的uid
}
message ScenarioRecommendUserListResponse {
  repeated ScenarioMatchUser list = 1;
}

// ================================================

enum ScenarioBindResultType {
  SCENARIO_BIND_RESULT_TYPE_UNSPECIFIED = 0;
  SCENARIO_BIND_RESULT_TYPE_ACCEPTED    = 1;
  SCENARIO_BIND_RESULT_TYPE_REJECTED    = 2;
}

enum ScenarioBindStatus {
  SCENARIO_BIND_STATUS_UNSPECIFIED  = 0;
  SCENARIO_BIND_STATUS_HALF         = 1;
  SCENARIO_BIND_STATUS_PAIR         = 2;
  SCENARIO_BIND_STATUS_CANCEL       = 3;
}

// 接受结果
message ScenarioBindResult {
  uint64 match_id = 1; // 绑定的ID
  uint32 bind_counter = 2; // 已绑定人数
  ScenarioBindStatus bind_status = 3; // 绑定状态
  // 冗余一遍 接收到BIND_STATUS_PAIR直接进房
  uint32 matched_channel_id = 4; // 目标房间ID
  uint32 matched_scenario_id = 5; // 目标剧本
  string matched_plot_id = 6; // 目标章节
  uint32 bind_timeout = 7; // 绑定成功后的等待超时
}
// rpc 接受/拒绝邀请
message ScenarioBindRequest {
  uint32 uid = 1;
  uint64 match_id = 2;  // 配对唯一Key
  ScenarioBindResultType bind_type = 3; // 绑定或者取消
}
message ScenarioBindResponse {
  ScenarioBindResult result = 2;
}

message ScenarioRecommendUserMarkReadRequest {
  uint32 uid = 1; // 我的UID
  repeated uint32 uid_list = 2; // 标记已读UID
}

message ScenarioRecommendUserMarkReadResponse {
}

message ScenarioRecommendUserGetHistoryRequest {
  uint32 uid = 1; // 我的UID
}

message ScenarioRecommendUserGetHistoryResponse {
  repeated uint32 uid_list = 1; // 已读UID
}

// rpc 验证用户是否可以进入房间
message CheckChannelMatchLimitationRequest {
  uint32 uid = 1; // 用户ID
  uint32 channel_id = 2; // 用户要进哪个房
}
message CheckChannelMatchLimitationResponse {
  bool is_limited = 1; // 是否被限制
}
// = = = = = 「End」 v1.36.0 剧本匹配 = = = = =

// = = = = = 「Begin」王者匹配 - 邀人组队 = = = = =
// 上报邀人组队的邀请状态
message ReportChannelInviteTeamRequest {
  uint32 inviter_uid = 1;  // 邀请人 UID
  uint32 invitee_uid = 2;  // 被邀请人 UID
}

message ReportChannelInviteTeamResponse {

}

// 获取邀人组队的邀请状态
message GetReportChannelInviteTeamRequest {
  uint32 inviter_uid = 1;  // 邀请人 UID
  repeated uint32 invitee_uid_list = 2;  // 被邀请人 UID 列表
}

message GetReportChannelInviteTeamResponse {
  map<uint32, bool> invitee_status_map = 1;  // 被邀请人 UID 列表
}
// = = = = = 「End」王者匹配 - 邀人组队 = = = = =

// = = = = = 「Begin」v1.39.0 首页我的小分队 & 房间邀人组队 - 优化 = = = = =
enum HomeTeamEntertainmentEntrance {
  Home_Team_ENTERTAINMENT_ENTRANCE_UNSPECIFIED = 0;
  Home_Team_ENTERTAINMENT_ENTRANCE_ON = 1;  // 开启
  Home_Team_ENTERTAINMENT_ENTRANCE_OFF = 2;  // 关闭
}

// 获取首页我的小分队娱乐页入口开关
message GetHomeTeamEntertainmentEntranceRequest {
  repeated uint32 uid_list = 1;
}

message GetHomeTeamEntertainmentEntranceResponse {
  map<uint32, HomeTeamEntertainmentEntrance> uid_entrance_map = 1;  // Key：用户ID，Value：娱乐页入口
}

// 上报首页我的小分队娱乐页入口开关
message ReportHomeTeamEntertainmentEntranceRequest {
  map<uint32, HomeTeamEntertainmentEntrance> uid_entrance_map = 1;  // Key：用户ID，Value：娱乐页入口
}

message ReportHomeTeamEntertainmentEntranceResponse {

}

// 首页小分队 - 用户互动数据
message HomeTeamFriendData {
  uint32 follow_count = 1;  // 跟随次数「消息页及首页的跟随点击」
  uint32 interaction_count = 2;  // 互动次数「IM 聊天、共同在房」
}

// 获取首页我的小分队用户互动数据，如果没有数据「home_team_friend_data_map」长度为 0
message GetHomeTeamFriendDataRequest {
  uint32 uid = 1;  // 用户 ID
  repeated uint32 friend_uid_list = 2;  // 好友 UID 列表
}

message GetHomeTeamFriendDataResponse {
  map<uint32, HomeTeamFriendData> home_team_friend_data_map = 1;  // Key：好友 UID，Value：用户互动数据
}

// 上报首页我的小分队用户互动数据
message ReportHomeTeamFriendDataRequest {
  uint32 uid = 1;  // 用户 ID
  map<uint32, HomeTeamFriendData> home_team_friend_data_map = 2;  // Key：好友 UID，Value：用户互动数据
}

message ReportHomeTeamFriendDataResponse {

}
// = = = = = 「End」v1.39.0 首页我的小分队 & 房间邀人组队 - 优化 = = = = =
