syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package muse_shining_point.shining_point;
option go_package = "golang.52tt.com/protocol/services/muse-shining-point/muse-shining-point";

import "tt/quicksilver/extension/options/options.proto";

service MuseShiningPoint {
  option (service.options.service_ext) = {
    service_name: "muse-shining-point"
  };

  /*---------闪光点运营后台---------*/
  rpc SortShiningPointClassify(SortShiningPointClassifyRequest) returns (SortShiningPointClassifyResponse) {}
  rpc UpsertShiningPoint(UpsertShiningPointRequest) returns (UpsertShiningPointResponse) {}
  rpc AppendShiningPoint(AppendShiningPointRequest) returns (AppendShiningPointResponse) {}
  rpc GetAllShiningPoint(GetAllShiningPointRequest) returns (GetAllShiningPointResponse) {}
  /*---------闪光点运营后台---------*/

  /*---------闪光点用户---------*/
  rpc GetUserShiningPoint(GetUserShiningPointRequest) returns (GetUserShiningPointResponse) {}
  rpc BatGetUserShiningPoint(BatGetUserShiningPointRequest) returns (BatGetUserShiningPointResponse) {}
  rpc UpdateUserShiningPoint(UpdateUserShiningPointRequest) returns (UpdateUserShiningPointResponse) {}
  rpc ShiningPointFriendCert(ShiningPointFriendCertRequest) returns (ShiningPointFriendCertResponse) {}
   rpc GetUserPopulateGuidanceBubble(GetUserPopulateGuidanceBubbleRequest) returns (GetUserPopulateGuidanceBubbleResponse) {}
  rpc ShiningPointFriendCertWithoutCheckOnlyTest(ShiningPointFriendCertRequest) returns (ShiningPointFriendCertResponse) {}
  // 批量认证
  rpc BatchShiningPointFriendCert(BatchShiningPointFriendCertRequest) returns (BatchShiningPointFriendCertResponse) {}
  // 引导用户添加闪光点页面
  rpc GetUserAddShiningPointGuidance(GetUserAddShiningPointGuidanceRequest) returns (GetUserAddShiningPointGuidanceResponse) {}
  // 获取闪光点介绍页
  rpc GetShiningPointIntroPage(GetShiningPointIntroPageRequest) returns (GetShiningPointIntroPageResponse) {}
  // 闪光点排行榜
  rpc ListShiningPointRank(ListShiningPointRankRequest) returns (ListShiningPointRankResponse) {}

  // 修改榜单参数
  rpc UpdateShiningPointRankParams(UpdateShiningPointRankParamsRequest) returns (UpdateShiningPointRankParamsResponse) {}

  //批量获取闪光点信息
  rpc BatGetShiningPointInfo(BatGetShiningPointInfoRequest) returns (BatGetShiningPointInfoResponse) {}

}


// 闪光点分类排序
message SortShiningPointClassifyRequest{
  repeated ShiningPointClassifySort classify_sort_list = 1; // 闪光点分类排序列表
}

message ShiningPointClassifySort{
  string classify_id = 1; // 闪光点分类id
  uint32 order_num = 2; // 顺序 0-999
}

message SortShiningPointClassifyResponse{
}

// 闪光点
message UpsertShiningPointRequest{
  ShiningPointClassify point_classify = 1; // 闪光点分类列表
}

// 闪光点分类
message ShiningPointClassify{
  string classify_id = 1; // 闪光点分类id
  string classify_name = 2; // 闪光点分类名称
  repeated ShiningPointInfo point_list = 3; // 闪光点列表
  bool is_delete = 4; // 是否删除
}

// 闪光点
message ShiningPointInfo{
  string point_id = 1; // 闪光点id
  string point_name = 2; // 闪光点名称
  uint32 order_num = 3; // 顺序 0-999
  bool is_delete = 4; // 是否删除
  uint32 point_dic_id = 5; // 闪光点字典id
}

message UpsertShiningPointResponse{
}

// 批量添加 不会更新已有的数据
message AppendShiningPointRequest{
  repeated AppendShiningPointData data_list = 1; // 闪光点分类列表
}

message AppendShiningPointData{
  string classify_name = 1; // 闪光点分类名称
  repeated string point_name_list = 2; // 闪光点列表
}

message AppendShiningPointResponse{
  map<string,string> same_point_map = 1; // 重复的闪光点
  map<string,string> exist_point_map = 2; // 已存在的闪光点
}

message GetAllShiningPointRequest{
}

message GetAllShiningPointResponse{
  repeated ShiningPointClassify point_classify_list = 1; // 闪光点分类列表
}

// 获取用户闪光点
message GetUserShiningPointRequest{
  uint32 uid = 1; // 用户id
  bool need_check_ab_test = 2; // 是否需要检查ab测试
  uint32 page_source = 3; // ShiningPointPageSource 客态详情页需要返回认证剩余次数
}

message GetUserShiningPointResponse{
  repeated ShiningPointInfo point_list = 1; // 闪光点列表
  repeated ShiningPointExtraInfo cert_list = 2; // 闪光点额外信息
  UserShiningPointCertCountRemain user_cert_remain = 3; // 闪光点认证剩余次数
}

message UserShiningPointCertCountRemain{
  uint32 total_remain = 1; // 今日总认证剩余次数
  uint32 this_user_remain = 2; // 可帮该用户认证剩余次数
}

// 更新用户闪光点
message UpdateUserShiningPointRequest{
  repeated string point_list = 1; // 闪光点列表
}

message UpdateUserShiningPointResponse{
}

// 获取用户闪光点
message BatGetUserShiningPointRequest{
  repeated uint32 uids = 1; // 用户id
}

message BatGetUserShiningPointResponse{
  map<uint32,GetUserShiningPointResponse> user_shining_point_map = 1; // 用户闪光点
}

message ShiningPointExtraInfo{
  string point_id = 1; // 闪光点id
  uint32 cert_count = 2; // 认证次数
  uint32 cert_type = 3; // ShiningPointCertType
  bool is_cert_by_me = 4; // 是否被我认证
  uint32 cert_times_by_me = 5; // 我认证的次数
  bool is_cert_by_me_today = 6; // 今日是否被我认证
}

/* 闪光点 认证 */
message ShiningPointFriendCertRequest{
  uint32 uid = 1; // 用户id
  string point_id = 2; // 闪光点id
}

message ShiningPointFriendCertResponse{
}

// 批量认证
message BatchShiningPointFriendCertRequest{
  uint32 uid = 1; // 用户id
  repeated string point_id_list = 2; // 闪光点id列表
}

// 批量认证返回
message BatchShiningPointFriendCertResponse{
}

// 引导用户添加闪光点页面
message GetUserAddShiningPointGuidanceRequest{
  uint32 page_source = 1; // ShiningPointPageSource SHINING_POINT_PAGE_SOURCE_HOME_PAGE_MIX_CHANNEL
}

message GetUserAddShiningPointGuidanceResponse{
  repeated ShiningPointInfo point_list = 1; // 闪光点列表(随机一部分)
}

// GetShiningPointIntroPageRequest 获取闪光点介绍页请求
message GetShiningPointIntroPageRequest{
  uint32 uid = 1; // 用户id
  repeated string point_id_list = 2; // 闪光点id列表
  bool need_check_ab_test = 3; // 是否需要检查ab测试
}

// ShiningPointIntroPage 闪光点介绍页
message ShiningPointIntroPage{
  string point_id = 1; // 闪光点id
  string point_name = 2; // 闪光点名称
  repeated string cert_friend_accounts = 3; // 认证好友账号列表
  uint32 cert_count = 4; // 认证次数 xx位好友认证
  uint32 rank = 5; // 排名
  float defeat_rate = 6; // 打败xx.x%用户 保留一位小数
}

// GetShiningPointIntroPageResponse 获取闪光点介绍页响应
message GetShiningPointIntroPageResponse{
  repeated ShiningPointIntroPage intro_page_list = 1; // 闪光点介绍页列表
  bool hide_cert_button = 2; // 是否隐藏【我也要认证此标签】按钮
}

message GetUserPopulateGuidanceBubbleRequest{
  uint32 uid = 1;

}

message GetUserPopulateGuidanceBubbleResponse{
   ShiningPointGuidanceBubble guidance_bubble = 1; // 气泡信息
}

message ShiningPointGuidanceBubble{
  uint32 bubble_type = 1; // ShiningPointGuidanceBubbleTypes
  string bubble_text = 2; // 引导气泡 文案
}

// 闪光点排行榜
message ListShiningPointRankRequest{
  uint32 uid = 1; // 用户id
  string point_id = 2; // 闪光点id
  uint32 offset = 3; // 偏移量
  uint32 limit = 4; // 限制数量
}

message ShiningPointRankItem{
  uint32 rank = 1; // 排名
  uint32 uid = 2; // 用户信息
  uint32 cert_count = 3; // 认证次数
}

message SelfShiningPointRankItem{
    ShiningPointRankItem rank = 1; // 自己的排名
    uint32 cert_count_to_up = 2; // 距离上榜认证次数
}

message ListShiningPointRankResponse{
  ShiningPointInfo point_info = 1; // 闪光点信息
  repeated ShiningPointRankItem rank_list = 2; // 闪光点排行榜
  SelfShiningPointRankItem self_rank = 3; // 自己的排名
  uint32 min_cert_count = 4; // 最小认证次数
}

// 修改榜单参数
message UpdateShiningPointRankParamsRequest{
  uint32 star_threshold = 1; // 点亮阈值
  uint32 rank_limit = 2; // 排行榜数量
}

message UpdateShiningPointRankParamsResponse{
  uint32 star_threshold = 1; // 点亮阈值
  uint32 rank_limit = 2; // 排行榜数量
}


message BatGetShiningPointInfoRequest{
    repeated  string ids=1;
}

message BatGetShiningPointInfoResponse{
    map<string,ShiningPointInfo> id_point_map=1;
}