syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/pgc-digital-bomb";
package pgc_digital_bomb;



service PgcDigitalBomb {
  // 数字炸弹设置游戏阶段
  rpc SetDigitalBombPhase(SetDigitalBombPhaseReq) returns (SetDigitalBombPhaseResp) {}

  // 获取游戏信息
  rpc GetDigitalBombInfo(GetDigitalBombInfoReq) returns (GetDigitalBombInfoResp) {}

  // 数字炸弹报名
  rpc DigitalBombEnroll(DigitalBombEnrollReq) returns (DigitalBombEnrollResp) {}

  // 数字炸弹选择参与用户
  rpc DigitalBombSelectGameUser(DigitalBombSelectGameUserReq) returns (DigitalBombSelectGameUserResp) {}

  // 数字炸弹选择数字
  rpc DigitalBombSelectNumber(DigitalBombSelectNumberReq) returns (DigitalBombSelectNumberResp) {}

  // 批量获取数字炸弹阶段
  rpc BatchGetDigitalBombPhase(BatchGetDigitalBombPhaseReq) returns (BatchGetDigitalBombPhaseResp) {}
}

enum DigitalBombPhase {
  // 预留1-10作为通用phase, 见SetGamePhaseReq.GamePhaseType
  DIGITAL_BOMB_PHASE_UNKNOWN = 0;
  DIGITAL_BOMB_PHASE_ENROLL = 1; // 报名, 对应通用phased的开启玩法SetGamePhaseReq.GamePhaseType.GAME_PHASE_START
  DIGITAL_BOMB_PHASE_DIRECT_END = 2; // 直接结束, 抽屉点击结束游戏, 对应通用phased的开启玩法SetGamePhaseReq.GamePhaseType.GAME_PHASE_FIN
  DIGITAL_BOMB_PHASE_SELECTING = 11; // 选择
  DIGITAL_BOMB_PHASE_SELECTED = 12; // 选择完成, 未爆炸
  DIGITAL_BOMB_PHASE_EXPLODE = 13; //  爆炸(结束)
  DIGITAL_BOMB_PHASE_MANUAL_END = 14; //  提前主动结束
  DIGITAL_BOMB_PHASE_SYS_END = 15; // 人数不足自动结束
  DIGITAL_BOMB_PHASE_TIMEOUT_END = 16; // 报名阶段主持麦10分钟无人自动结束
}

// 设置阶段
message SetDigitalBombPhaseReq {
  uint32 channel_id = 1;
  uint32 phase = 2;
  uint32 participate_type = 3;
}

message SetDigitalBombPhaseResp {}

message GetDigitalBombInfoReq {
  uint32 channel_id = 1;
}

// 获取游戏信息
message GetDigitalBombInfoResp {
  DigitalBombGameInfo info = 1;
}

// 报名/取消报名
message DigitalBombEnrollReq {
  uint32 channel_id = 1;
  uint32 uid = 2;
  uint32 op = 3; // 0.取消报名 1.报名
}

message DigitalBombEnrollResp {
}

// 选择参与用户
message DigitalBombSelectGameUserReq {
  uint32 channel_id = 1;
  uint32 uid = 2;
  uint32 op = 3; // 0.取消选人, 1.选人
}

message DigitalBombSelectGameUserResp {
}

message UserProfile {
  uint32 uid = 1;
  string account = 2; //账号
  string nickname = 3; // 昵称
  string account_alias = 4; // 靓号，预留字段，暂时未赋值
  uint32 sex = 5; //用户性别
  UserPrivilege privilege = 6;
}

enum EUserPrivilegeType
{
  ENUM_USER_PRIVILEGE_UNKNOWN = 0; // 无效值
  ENUM_USER_PRIVILEGE_UKW = 1;  // 神秘人
}

message UserPrivilege {
  string account = 1; // 权益指定的账号，如神秘人服务提供的：神秘人头像对应的account
  string nickname = 2; // 权益指定的昵称，如神秘人服务提供的：神秘人+编号
  uint32 type = 3; // 权益类型, 0: 无效，1： 神秘人
  bytes options = 4; // 权益特殊属性，如 神秘人 对应 UserUKWInfo
}


message DigitalBombUserInfo {
  UserProfile user_info = 1;
  bool is_participate = 2;
}
message DigitalBombGameInfo {
  uint32 play_id = 1;           // 场次id
  UserProfile user_profile = 2; // 当前操作用户
  uint32 phase = 3; // 游戏阶段
  repeated DigitalBombUserInfo user_info = 4; // 用户列表
  uint32 participate_limit = 5; // 参与人数限制
  uint32 selected = 6; // 选择数字
  uint32 select_range_start = 7; // 可选区域起始值
  uint32 select_range_end = 8; // 可选区域结束值
  uint32 total_range_start = 9; // 整体区域起始值
  uint32 total_range_end = 10; // 整体域结束值
  uint64 select_end_time = 11; // 选择结束时间
  uint64 server_time = 12; // 服务器时间 ms
  uint32 total_select_time = 13; // 总选择时间
  uint32 enroll_limit = 14; // 报名限制人数
  uint32 enroll_type = 15; // 报名类型 见GameUserType
  DigitalBombGameInfoText text = 16; // 相关文案
}

// 数字炸弹文案
message DigitalBombGameInfoText {
  string host_enroll_text = 1; // 主持侧报名文案
  string user_enroll_text = 2; // 用户侧报名文案
  string enroll_user_limit = 3; // 报名限制人群文案
  string time_out_end = 4; // 自动结束toast
}

// 选择数字
message DigitalBombSelectNumberReq {
  uint32 channel_id = 2;
  uint32 uid = 3;
  uint32 select_num = 4;
}

message DigitalBombSelectNumberResp {
}

message UKWHandleReq {
  uint32 uid = 1;
  uint32 fake_uid = 2;
}

message BatchGetDigitalBombPhaseReq {
  repeated uint32 channel_id = 1;
}

message BatchGetDigitalBombPhaseResp {
  map<uint32, uint32> phase_map = 1; // key: channel_id, value: see DigitalBombPhase
}