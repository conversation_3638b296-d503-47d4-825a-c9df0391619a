syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/perfect-match-game";
import "tt/quicksilver/rcmd-local/perfect-match/rcmd-perfect-match.proto";
import "tt/quicksilver/switch-scheme-checker/switch_scheme_checker.proto";

package perfect_match_game;

service PerfectMatchGame {
    //初始化玩家信息
    rpc InitPrefectMatchGamePlayers(InitPrefectMatchGamePlayersReq) returns(InitPrefectMatchGamePlayersResp){}
    //获取玩法信息
    rpc GetPrefectMatchGameInfo(GetPrefectMatchGameInfoReq) returns(GetPrefectMatchGameInfoResp){}
    //切换玩法阶段
    rpc SetPrefectMatchGamePhase(SetPrefectMatchGamePhaseReq) returns(SetPrefectMatchGamePhaseResp){}
    //选择爆灯对象
    rpc BlowLight(BlowLightReq)returns(BLowLightResp){}
    //选择心动对象
    rpc ChooseTheOne(ChooseTheOneReq)returns(ChooseTheOneResp){}
    // 查询已有线索
    rpc GetCoupleClues(GetCoupleCluesReq) returns (GetCoupleCluesResp) {}
    // 查询我的问卷
    rpc GetUserQuestionnaire(GetUserQuestionnaireReq) returns (GetUserQuestionnaireResp) {}
    // 查询我的道具
    rpc GetUserCluesProp(GetUserCluesPropReq) returns (GetUserCluesPropResp) {}
    // 使用道具获得线索
    rpc UseCluesProp(UseCluesPropReq) returns (UseCluesPropResp) {}
    // 主持人发放线索
    rpc PublishClues(PublishCluesReq) returns (PublishCluesResp) {}
    // 模拟游戏结果，测试结算
    rpc GameResultSimulation(GameResultSimulationReq) returns (GameResultSimulationResp) {}
    // 判断是否能上麦
    rpc CanHoldMic(CanHoldMicReq) returns (CanHoldMicResp) {}

    // 检查玩法切换前置条件
    rpc Check(switch_scheme_checker.CheckReq) returns(switch_scheme_checker.CheckResp) {}

}

// 结果类型
enum PerfectCpResultType {
    PERFECT_CP_RESULT_TYPE_UNSPECIFIED = 0;
    PERFECT_CP_RESULT_TYPE_TRIPLE_MATCH = 1;                //双向爆灯且互选成功又是系统天配
    PERFECT_CP_RESULT_TYPE_DOUBLE_MATCH_ONE_LIGHT = 2;      //单向爆灯且互选成功又是系统天配
    PERFECT_CP_RESULT_TYPE_DOUBLE_MATCH_NO_LIGHT = 3;       //互选成功是系统天配
    PERFECT_CP_RESULT_TYPE_DOUBLE_MATCH_NOT_MATCH = 4;      //双向爆灯互选成功不是系统天配
    PERFECT_CP_RESULT_TYPE_CHOOSE_ONE_LIGHT_NOT_MATCH = 5;  //单向爆灯互选成功不是系统天配
    PERFECT_CP_RESULT_TYPE_DOUBLE_LIGHT = 6;                //双向爆灯没有互选成功不是系统天配
}

message PlayerInitInfo {
    uint32 uid = 1;
    uint32 mic_id = 2;
    uint32 cp_uid = 3;
    repeated rcmd.perfect_match.Question question_list = 4;  // uid用户的问卷及线索
    string couple_match_id = 5; // 每对天配匹配Id
}

message InitPrefectMatchGamePlayersReq {
    uint32 channel_id = 1;
    repeated PlayerInitInfo list = 2;
    string match_id = 3; // 该局匹配Id
}

message InitPrefectMatchGamePlayersResp {}

message PlayerInfo {
    uint32 uid = 1;
    uint32 mic_id = 2;
    uint32 choose_uid = 3; //心动对象uid
    bool choose_flag = 4; //用户是否已选择心动对象
}

// 玩家送礼值、爆灯信息等动态信息
message PlayerDynamicInfo {
    uint32 uid = 1;
    uint32 send_value = 3;   // 送礼值
    uint32 target_value = 4; // 可爆灯目标送礼值
    uint32 light_status = 5; // see perfect_couple_match_logic.proto PlayerLightStatus
    uint32 target_uid = 6;   // 爆灯对象
    uint64 server_time = 7;  // 服务器时间
}

// 发放线索按钮
message CluesPublishButton {
    bool click_enable = 1;                   // 按钮是否可点击
    string clues_publish_button_text = 2;    // 发放线索按钮文案，为空则不展示
}
  
// 主持人关注信息
message PerfectMatchHostFocus {
    bool switch_phase_enable = 1; // true-可切换至下一阶段
    string switch_phase_disable_toast = 2;  // 不可切换下一阶段的提示，仅当switch_phase_enable为false有效
    CluesPublishButton clues_publish_button = 3;    // 发放线索按钮
}

// 玩家侧道具按钮状态
enum CluesPropStatus {
    CLUES_PROP_STATUS_UNSPECIFIED = 0;  // 不展示
    CLUES_PROP_STATUS_ENABLE = 1;       // 展示
}

message PerfectMatchGameInfo {
    uint32 channel_id = 1;
    uint32 game_id = 2;
    uint32 phase = 3;  // 阶段 see PerfectMatchGamePhaseType
    uint64 phase_end_ts = 4;
    uint64 server_time_sec = 5;
    repeated PlayerInfo player_list = 6;
    PerfectMatchHostFocus host_focus = 7;
    CluesPropStatus clues_prop_status = 8;  // 玩家的道具按钮状态
    uint64 game_begin_time = 9; //轮次开始时间
    uint32 pub_clues_cnt = 10;   // 当前已发放线索的条数
}

message GetPrefectMatchGameInfoReq{
    uint32 channel_id = 1;
    uint32 uid = 2;
}

message GetPrefectMatchGameInfoResp{
    PerfectMatchGameInfo game_info = 1;
    repeated PlayerDynamicInfo info_list = 3;
}

message SetPrefectMatchGamePhaseReq{
    uint32 channel_id = 1;
    uint32 game_id = 2;
    uint32 target_phase = 3; // 目标阶段
    uint32 op_uid = 4; // 操作人uid
}

message SetPrefectMatchGamePhaseResp{
}

message BlowLightReq{
    uint32 channel_id = 1;
    uint32 game_id = 2;
    uint32 op_uid = 3;
    uint32 target_uid = 4;
}
message BLowLightResp{
}

message ChooseTheOneReq{
    uint32 channel_id = 1;
    uint32 game_id = 2;
    uint32 op_uid = 3;
    uint32 target_uid = 4;
}
message ChooseTheOneResp{
}

// 对象的线索
message CoupleClues {
    string clues_text = 1;    // 线索文案
}

// 查询已有线索
message GetCoupleCluesReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
    bool get_all_player = 4;   // 获取所有玩家的线索
}

message PlayerCluesInfo{
    uint32 uid = 1;
    repeated CoupleClues info_list = 2;
}

message GetCoupleCluesResp {
    PlayerCluesInfo cp_clues = 1;
    repeated PlayerCluesInfo cp_clues_list = 2; // 主持人可以看所有玩家拿到的线索
    uint32 curr_publish_cnt = 3;                // 当前公布的线索条数
}

message Questionnaire{
    string question = 1;    // 问题
    string answer = 2;      // 回答
}

// 查询我的问卷
message GetUserQuestionnaireReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
}

message GetUserQuestionnaireResp {
    repeated Questionnaire questionnaire_list = 1;
}

enum CluesPropType {
    CLUES_PROP_TYPE_UNSPECIFIED = 0;      // 无效值
    CLUES_PROP_TYPE_ADD_NEW_CLUES = 1;    // 增加一条新线索
    CLUES_PROP_TYPE_EXCLUDE_ERR_OBJ = 2;  // 排除一个错误对象
}

// 额外的线索
message ExtraClues {
    string clues_text = 1;
    uint32 obj_uid = 2; // 对象玩家uid
}

// 线索道具信息
message CluesProp {
    enum PropStatus {
        PROP_STATUS_UNSPECIFIED = 0; // 无效值
        PROP_STATUS_NOT_USED = 1;    // 未使用
        PROP_STATUS_USED = 2;        // 已使用
    }

    uint32 prop_id = 1;             // 道具id
    CluesPropType prop_type = 2;    // 道具类型
    string prop_desc = 3;           // 道具描述
    uint32 gift_id = 4;             // 礼物id
    PropStatus status = 5;          // 状态
    ExtraClues extra_clues = 6; // 通过道具获得的线索
}

// 查询我的道具
message GetUserCluesPropReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
}

message GetUserCluesPropResp {
    repeated CluesProp prop_list = 1;
}

// 使用道具获得线索
message UseCluesPropReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
    uint32 prop_id = 4;    // 道具id
    uint32 target_uid = 5; // 想要获取的线索的目标uid
}

message UseCluesPropResp {}

// 主持人发放线索
message PublishCluesReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
}

message PublishCluesResp {}


// 模拟游戏结果，测试结算
message GameResultSimulationReq {
    uint32 channel_id = 1;
    repeated PlayerInitInfo player_list = 2;  // 天配玩家信息列表
    map<uint32, uint32> light_map = 3;        // 爆灯配对映射
    map<uint32, uint32> choose_map = 4;       // 选择对象映射
}

message GameResultSimulationResp {}

message CanHoldMicReq {
    uint32 channel_id = 1;
    uint32 op_uid = 2;
    uint32 target_uid = 3;
    uint32 mic_id = 4;
}

message CanHoldMicResp {
}