syntax = "proto3";

package smash_egg_notify;
// import "tt/quicksilver/extension/options/options.proto";
option go_package = "golang.52tt.com/protocol/services/smash-egg-notify";

//1：奖池下架提醒、2：奖池及兑换更新提醒、3：兑换更新提醒
enum NotifyType{
  All_Type = 0;
  Prize_Pool_Off = 1;
  Pool_Conversion_Update = 2;
  Conversion_Update = 3;
  Gift_Update = 4;
}

//1:房间浮层提示，2：公屏提示，3：红点提示，4：转转浮层提示 5:礼物提示
enum NotifyPlace{
  All_Place = 0;
  Floating_Place = 1;
  Public_Place = 2;
  Zhuanzhuan_Place = 3;
  Gift_Floating_Place = 4;
}

message SmashEggNotify{
  NotifyType notify_type = 1;
  uint32 notify_begin = 2;              //提醒开始时间
  uint32 notify_end = 3;                //提醒结束时间
  uint32 has_floating = 4;              //是否浮层提示
  string floating_text = 5;             //浮层文案文字
  uint32 floating_duration = 6;         //浮层文案显示时长
  uint32 has_zhuanzhuan = 7;            //是否转转浮层提示
  string zhuanzhuan_text = 8;           //转转浮层文案文字
  uint32 zhuanzhuan_duration = 9;       //转转浮层文案显示时长
  uint32 has_public = 10;               //是否公屏提示
  string public_text = 11;              //公屏文案文字
  uint32 has_red = 13;                  //是否有红点提示
  uint32 modify_time = 14;              //修改时间，时间戳
  string text_color = 15;               //公屏文字颜色

  uint32 has_exchange = 17;            //是否转转兑换浮层提示
  string exchange_text = 18;           //转转兑换浮层文案文字
  uint32 exchange_duration = 19;       //转转兑换浮层文案显示时长

  ActivityType activity_type = 20;     // 活动类型
  string public_entry_url = 21;               // 公屏活动入口url
}

enum ActivityType{
    ACTIVITY_TYPE_UNSPECIFIED = 0;
    ACTIVITY_TYPE_A = 1;              // 活动A
    ACTIVITY_TYPE_B = 2;              // 活动B
}

message GiftNotify{
  uint32 notify_id = 1;
  uint32 notify_begin = 2;              //提醒开始时间
  uint32 notify_end = 3;                //提醒结束时间
  string gift_text = 4;           //转转礼物文案文字
  uint32 gift_duration = 5;       //转转礼物文案显示时长
  uint32 modify_time = 6;              //修改时间，时间戳
  string operator_name = 7;
}

message GetNotifyReq{
  NotifyType notify_type = 1;
  //  NotifyPlace notify_place = 2;

  ActivityType activity_type = 2;
}

message GetNotifyResp{
  repeated SmashEggNotify smash_egg_notify = 1;
  repeated GiftNotify gift_notify = 2;
}

message SetNotifyReq{
  SmashEggNotify smash_egg_notify = 1;
  ActivityType activity_type = 2;
  string operator_name = 3;
}

message SetNotifyResp{
  uint32 is_success = 1;
}


message SetGiftNotifyReq{
  GiftNotify gift_notify = 1;
}

message SetGiftNotifyResp{
  uint32 is_success = 1;
}

message DeleteGiftNotifyReq{
  uint32 notify_id = 1;
  string operator_name = 2;
}

message DeleteGiftNotifyResp{
  uint32 is_success = 1;
}

message GetCurrentNotifyReq{
    uint32 uid = 1;
    uint32 activity_type = 2;
    uint32 vision_id = 3;
}

message GetCurrentNotifyResp{
  repeated SmashEggNotify smash_egg_notify = 1;
  GiftNotify gift_notify = 2;
}

message LightEffectConfig{
    uint32 pack_id = 1;      //包裹id
    uint32 effect_id = 2;    //光效id
    string pack_name = 3;    //包裹名称
    string prize_string = 4; //中奖文案
    uint32 mtime = 5;        //编辑(添加)时间,传参时，前端不需要传
    string prize_name = 6;   //奖励名称，取代xxx占位符，没有xxx时，安卓端展示在最前面
}

message AddSmashLightEffectsReq{
    repeated LightEffectConfig conf_list =1 ;
}

message AddSmashLightEffectsResp{
    repeated uint32 pack_id_list = 1; // 重复、或添加失败的pack_id_list
}

message UpdateSmashLightEffectReq{
    LightEffectConfig conf =1 ;
}

message UpdateSmashLightEffectResp{
}

message DelLightEffectByPackIdReq{
    uint32 pack_id = 1;      //包裹id
}

message DelLightEffectByPackIdResp{
}

message GetAllSmashLightEffectsReq{
}

message GetAllSmashLightEffectsResp{
    repeated LightEffectConfig conf_list =1 ;
}

// 非运营后台使用
// message GetAllLightEffectsForCacheReq{
//     uint32 mtime_version =1;
// }

// message GetAllLightEffectsForCacheResp{
//     bool need_to_update = 1;
//     uint32 mtime_version = 2;
//     repeated LightEffectConfig conf_list =3;
// }

message GetLightEffectInfoByPackIdReq{
    uint32 pack_id = 1;
}

message GetLightEffectInfoByPackIdResp{
    LightEffectConfig conf =1;
}

service Notify{
    // option (service.options.service_ext) = {
    //     service_name: "notify"
    //   };
  //获取提醒信息
  rpc GetNotify(GetNotifyReq) returns (GetNotifyResp){

  }

  //设置提醒
  rpc SetNotify(SetNotifyReq) returns (SetNotifyResp){

  }


  //设置礼物提醒
  rpc SetGiftNotify(SetGiftNotifyReq) returns (SetGiftNotifyResp){

  }

  //设置礼物提醒
  rpc DeleteGiftNotify(DeleteGiftNotifyReq) returns (DeleteGiftNotifyResp){

  }

  //获取当前应该展示的提醒信息
  rpc GetCurrentNotify(GetCurrentNotifyReq) returns (GetCurrentNotifyResp){

  }

  // 转转中奖光效
  rpc AddSmashLightEffects(AddSmashLightEffectsReq) returns (AddSmashLightEffectsResp){}
  rpc UpdateSmashLightEffect(UpdateSmashLightEffectReq) returns (UpdateSmashLightEffectResp){}
  rpc DelLightEffectByPackId(DelLightEffectByPackIdReq) returns (DelLightEffectByPackIdResp){}
  rpc GetAllSmashLightEffects(GetAllSmashLightEffectsReq) returns (GetAllSmashLightEffectsResp){}

//   rpc GetAllLightEffectsForCache(GetAllLightEffectsForCacheReq) returns (GetAllLightEffectsForCacheReq){}
  rpc GetLightEffectInfoByPackId(GetLightEffectInfoByPackIdReq) returns (GetLightEffectInfoByPackIdResp){}

  // ================= 换皮通用配置后台 ===========================

  // 娱乐玩法视觉配置接口
  rpc SetSmashActivityVisionConfig (SetSmashActivityVisionConfigReq) returns (SetSmashActivityVisionConfigResp){}
  rpc UpdateSmashActivityVisionConfig (UpdateSmashActivityVisionConfigReq) returns (UpdateSmashActivityVisionConfigResp){}
  rpc GetSmashActivityVisionConfig (GetSmashActivityVisionConfigReq) returns (GetSmashActivityVisionConfigResp){}
  rpc DelSmashActivityVisionById (DelSmashActivityVisionByIdReq) returns (DelSmashActivityVisionByIdResp){}

  // 玩法道具配置
  rpc AddSmashPropConfig (AddSmashPropConfigReq) returns (AddSmashPropConfigResp){}
  rpc GetAllSmashPropConfig (GetAllSmashPropConfigReq) returns (GetAllSmashPropConfigResp){}
  rpc GetSmashPropConfigById(GetSmashPropConfigByIdReq) returns (GetSmashPropConfigByIdResp){}

// ====================== 换皮业务接口 ===========================
  rpc GetSmashActivityVisionConfigById (GetSmashActivityVisionConfigByIdReq) returns (GetSmashActivityVisionConfigByIdResp){}
}



// ================== 换皮通用配置后台 ===========================

message SmashActivityVisionConfig{
    uint32 vision_id = 1;
    string vision_name = 2;
    string text_config = 3;         // 文案及色值资源json字符串

    string entry_res = 4;           // 入口资源
    string activity_resource = 5;   // 活动视觉资源包
    string activity_resource_md5 = 6;   // 活动视觉资源包md5

    string public_entry_url = 7;    // 公屏入口资源url

    int64 update_time = 8;          // 更新时间
    bool is_del = 9;                // 是否已删除
}

// 设置玩法视觉配置
message SetSmashActivityVisionConfigReq{
    SmashActivityVisionConfig conf =1 ;
}

message SetSmashActivityVisionConfigResp{
}

// 更新玩法视觉配置，覆盖更新
message UpdateSmashActivityVisionConfigReq{
    SmashActivityVisionConfig conf =1 ;
}

message UpdateSmashActivityVisionConfigResp{
}

// 获取玩法视觉配置(全量)
message GetSmashActivityVisionConfigReq{
}

message GetSmashActivityVisionConfigResp{
    repeated SmashActivityVisionConfig vision_list =1 ;
}

// 删除玩法视觉配置
message DelSmashActivityVisionByIdReq{
    uint32 vision_id = 1;
}

message DelSmashActivityVisionByIdResp{
}

message SmashPropConfig{
    uint32 prop_id = 1;  // 道具id
    string prop_name = 2; // 道具名
    string prop_icon = 3; // 道具icon
    int64 update_time = 5; // 更新时间
    uint32 price = 6;      // 价格
}

// 获取全量道具配置列表
message GetAllSmashPropConfigReq{
}

message GetAllSmashPropConfigResp{
    repeated SmashPropConfig conf_list = 1;
}


// 新增道具配置
message AddSmashPropConfigReq{
    SmashPropConfig conf = 1;
    string op_user = 2;   // 操作人
}

message AddSmashPropConfigResp{
}

// 通过道具id获取道具配置
message GetSmashPropConfigByIdReq{
    uint32 prop_id = 1;
}

message GetSmashPropConfigByIdResp{
    SmashPropConfig conf = 1;
}

message GetSmashActivityVisionConfigByIdReq{
    uint32 vision_id = 1;
}

message GetSmashActivityVisionConfigByIdResp{
    SmashActivityVisionConfig conf = 1;
}