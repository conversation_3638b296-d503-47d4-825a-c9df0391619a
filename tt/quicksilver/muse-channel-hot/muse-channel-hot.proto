syntax = "proto3";

package muse_channel_hot;
option go_package = "golang.52tt.com/protocol/services/muse-channel-hot";

// buf:lint:ignore SERVICE_PASCAL_CASE
service muse_channel_hot {
  rpc BatchChannelHot(BatchChannelHotReq)returns(BatchChannelHotResp);
}

message BatchChannelHotReq{
  repeated uint32 channel_ids = 1;
}

message BatchChannelHotResp{
  map<uint32, ChannelHot> channel_hot = 1;
}

message ChannelHot{
  uint32 channel_id = 1;
  float hot = 2;//整体热度
  float mic_hot = 3;//麦上热度
  float enter_hot = 4;//进房热度
  int64 update_time = 5;//更新时间
}

