syntax = "proto3";

package highcontentproxy;
option go_package = "golang.52tt.com/protocol/services/highcontentproxy";

service HighContentProxy {
    rpc UpdatePostTags (UpdatePostTagsReq) returns (UpdatePostTagsRsp) {}

    rpc GetRecommendRandByUIDs (GetRecommendCardRandByUIDsReq) returns (GetRecommendCardRandResp) {}
    rpc GetRecommendCard (GetRecommendCardReq) returns (GetRecommendCardResp) {}
    rpc GetRecommendCardRand (GetRecommendCardRandReq) returns (GetRecommendCardRandResp) {}
    rpc GetRecommendAggregation (GetRecommendAggregationReq) returns (GetRecommendAggregationResp) {}
    rpc GetRecommendExpert (GetRecommendExpertReq) returns (GetRecommendExpertResp) {}

    rpc GetForceInsertPostInfo (GetForceInsertPostInfoReq) returns (GetForceInsertPostInfoResp) {}
    rpc GetValidForceInsertPostInfo (GetValidForceInsertPostInfoReq) returns (GetValidForceInsertPostInfoResp) {}

    // useless
    rpc UpdateStreamRecommend (UpdateStreamRecommendReq) returns (UpdateStreamRecommendResp) {}
    rpc GetStreams (GetStreamsReq) returns (GetStreamsResp) {}

    rpc UpdatePostRecommend (UpdatePostRecommendReq) returns (UpdatePostRecommendResp) {} // 只更新加权推荐

    ////tgl 精选流TGL机器人管理 
    rpc GetTglRobotList(GetTglRobotListReq) returns (GetTglRobotListRsp){}
}

enum PostType {
    Kol = 0;
    Tgl = 1;
}

message UpdatePostTagsReq {
    string post_id = 1;
    repeated uint32 tags = 2;
    uint32 user_id = 3; // 操作者uid
    PostType post_type = 4;
}

message UpdatePostTagsRsp {
    
}

// card
message RecommendCardInfo{
    uint32 uid = 1;
    string account = 2;
    string alias = 3; // 数字账号
    string nickname = 4; // 昵称
    uint32 gender = 5; // 性别
    string certify_title = 6; //官方认证
    string certify_intro = 7; //认证介绍
    string certify_style = 8; //认证样式, official官方认证，ent娱乐房认证，ugc优质内容生产者
    bool attention = 9;
    string certify_special_effect_icon = 10;  //大v图标动效
}

message GetRecommendCardReq {
    uint32 offset = 1;
    uint32 limit = 2;
}

message GetRecommendCardResp {
    repeated RecommendCardInfo info = 1;
}

message GetRecommendCardRandReq {
    uint32 cnt = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetRecommendCardRandByUIDsReq {
    repeated uint32 uidList = 1;
}


message GetRecommendCardRandResp {
    repeated RecommendCardInfo info = 1;
    uint32 total = 2;
}

// card end

// aggregation
message GetRecommendAggregationReq{
    uint32 category = 1;
    uint32 offset = 2;
    uint32 limit = 3;
}

message GetRecommendAggregationResp{
    repeated RecommendCardInfo info_list = 1;
}

// aggregation end

message ExpertTagInfo{
    uint32 category = 1;
    string tag_name = 2;
}

// expert
message GetRecommendExpertReq{
}

message GetRecommendExpertResp{
    repeated ExpertTagInfo category_list = 1;
    repeated RecommendCardInfo info_list = 2;
}
// expert end

enum ContentStatus {
    CONTENT_STATUS_NONE = 0;
    CONTENT_STATUS_UNDER_REVIEW = 1; // 文字审核过了, 等待图片审核
    CONTENT_STATUS_UNDER_REVIEW_AND_PREPARED = 2; // 客户端已经上报图片上传完毕, 等待审核
    CONTENT_STATUS_SUSPICIOUS = 3;
    CONTENT_STATUS_ILLEGAL = 4;
    CONTENT_STATUS_NORMAL = 5;
    CONTENT_STATUS_DELETED = 6; // 这个值要改大一点
    CONTENT_STATUS_BANNED = 7; // 由官方操作的封禁
}

message AttachmentInfo {
    enum AttachmentType {
        NONE = 0;
        IMAGE = 1;
        GIF = 2;
        VIDEO = 3;
        CMS = 4; // cms帖子
        AUDIO = 5;
    }
    string key = 1;
    AttachmentType type = 2; // 附件类型
    string content = 3; // 附件内容,一般来说是url
    string extra = 4; // 客户端自己玩
    ContentStatus status = 5;

    // 给type == video用的, 转码参数
    string param = 10;
}
message PostInfo{
    enum PostType {
        NONE = 0;
        TEXT = 1;
        IMAGE = 2;
        VIDEO = 3;
        CMS = 4; // cms
        AUDIO = 5;
    } // must be same as ugc_.proto
    string post_id = 1;
    string topic_id = 2;
    PostType post_type = 3;
    string content = 4;
    repeated AttachmentInfo attachments = 5;
    uint64 create_at = 6; // 发帖时间, unix second
    uint32 comment_count = 7;
    uint32 attitude_count = 8;
    uint32 view_count = 9; // 浏览量
    uint32 user_id = 10;
    ContentStatus status = 11;
    uint32 top_level_comment_count = 12; //一级评论的数量
    uint32 share_count = 13; // 分享数
    string label = 14; // 运营配置的特殊label
    uint32 magnified_view_count = 15;   // 扩大后的view_count

    string sub_topic_id = 16;//话题id
    repeated uint32 tags = 17; // 推荐的tag
    int32 content_type = 18;    //内容的类型，区分content里是否包含自定义富文本的内容
}

// force insert
message ForceInsertPostInfo{
    uint32 index = 1;
    uint32 begin_ts = 2;
    uint32 end_ts = 3;
    PostInfo post_info = 4;
}

message GetForceInsertPostInfoReq{
    uint32 offset = 1;
    uint32 limit = 2;
}

message GetForceInsertPostInfoResp{
    repeated  ForceInsertPostInfo force_info_list = 1;
    uint32 cnt = 2;
}

message ValidForceInsertPostInfo{
    uint32 index = 1;
    PostInfo post_info = 2;
}

message GetValidForceInsertPostInfoReq{
    uint32 offset = 1;
    uint32 limit = 2;
}

message GetValidForceInsertPostInfoResp{
    repeated  ValidForceInsertPostInfo force_info_list = 1;
    uint32 cnt = 2;
}
// force insert end

// content mgr

// recommend
enum RecommendType {
    DEFAULT_RECOMMEND = 0;
    NOT_RECOMMEND = 1;
    WEIGHT_RECOMMEND = 2;
}

enum KOLLevel{
    KOL_LEVEL_NONE = 0;
    KOL_LEVEL_S1 = 1;
    KOL_LEVEL_S2 = 2;
    KOL_LEVEL_P1 = 3;
    KOL_LEVEL_P2 = 4;
    KOL_LEVEL_P3 = 5;
}

message StreamItem {
    string post_id = 1; //帖子id
    uint32 time = 2;    //发帖时间
    uint32 recommend = 3;
    uint32 uid = 4;
    repeated uint32 tags = 5;
    PostInfo post_info = 6;
    uint32 level = 7;
    int64 valid_begin_time = 8;
    int64 valid_end_time = 9;
}

message UpdateStreamRecommendReq {
    PostType post_type = 1;
    repeated string postids = 2;
    uint32 recommend = 3;
    repeated uint32 tags = 4;
    int64 begin_time = 5;
    int64 end_time = 6;
}

message UpdateStreamRecommendResp {
}

message GetStreamsReq{
    PostType post_type = 1;
    uint32 offset = 2;
    uint32 limit = 3;
    int64 begin_time = 4;
    int64 end_time = 5;
    uint32 state = 6;
    uint32 uid = 7;
    uint32 kol_level = 8;
    repeated uint32 tags = 9;
}

message GetStreamsResp{
    repeated StreamItem info = 1;
    uint32 cnt = 2;
}

// content mgr end

message UpdatePostRecommendReq {
    repeated string post_id_list = 1;
    uint32 recommend = 2;
    uint32 user_id = 3; // uid
    PostType post_type = 4;
    int64 valid_begin_time = 5;
    int64 valid_end_time = 6;
}

message UpdatePostRecommendResp {
}


//////~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~获取以及导出
enum TglRobotStatusType {
    All = 0;//所有名单
    Normal = 1;//正常状态
    Blacklist = 2;//黑名单
}

enum Gender {
    Gender_UNKNOWN = 0;
    Gender_MALE = 1;
    Gender_FEMALE = 2;
}

message GetTglRobotListReq {
    //status 1为正常 2为黑名单
    TglRobotStatusType status = 1;
    string ttid = 2;
    int64 begin_time = 3;
    int64 end_time = 4;
    uint32 page_no = 5;
    uint32 page_size = 6;
}


message TglRobotItem {
    TglRobotStatusType status = 1;
    uint32 uid = 3;
    string ttid = 4;
    //昵称
    string nick_name = 5;
    string icon_url = 6;
    //创建时间
    int64 create_time = 7;
    //性别
    Gender gender = 8;
}

message GetTglRobotListRsp {
    repeated TglRobotItem list = 1;
    uint32 total = 2;
    uint32 page_no = 3;
    uint32 page_size = 4;
}
message ExportTglRobotRsp {
    string file_name = 1;
}