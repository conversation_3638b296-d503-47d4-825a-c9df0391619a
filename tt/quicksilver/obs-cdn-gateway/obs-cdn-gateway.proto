syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/obscdngateway";
package obscdngateway;


service ObsCdnGateway {
    // 只会 刷新该 url 对应的 cnd, 而不刷新对象对应的所有 cdn 
    rpc RefreshUrl (RefreshUrlReq) returns (RefreshUrlResp) {} 
    // 刷新对象的所有 cdn 
    rpc RefreshObject2 (RefreshObject2Req ) returns (RefreshObject2Resp) {} 

    //the following be deprecated, 2022.06.10 
    //rpc RefreshUrls (RefreshUrlsReq) returns (RefreshUrlsResp) {}
    //rpc RefreshObject (RefreshObjectReq) returns (RefreshObjectResp) {}
    //rpc GetScope2DomainsMap (GetScope2DomainsMapReq) returns (GetScope2DomainsMapResp) {}
    //rpc GetInfoByUrl(GetInfoByUrlReq) returns (GetInfoByUrlResp) {}
    //deprecated
    //rpc GetRealUrl(GetRealUrlReq) returns (GetRealUrlResp) {} 
}

message RefreshUrlReq {
    string url = 1;
}
message RefreshUrlResp {
    EC err_code = 1;           //EC 
    string err_msg = 2;
    RefreshUrlTask task = 3; //提交成功才有 task
} 

message RefreshObject2Req   {
    ObjectId oid = 1;
}
message RefreshObject2Resp {
    EC   err_code = 1;  //EC 
    string err_msg = 2;
    repeated RefreshUrlTask tasks = 3; //提交成功才有 task
} 

message RefreshUrlTask {
    string url = 1;
    string task_id = 2;
}

message ObjectId {
    // app+scope or provider+bucket
    string app = 1;
    string scope = 2;
    string provider = 3;
    string bucket = 4;
    string key = 5; //
}
enum EC {
    Ok = 0;
    PartialOk = 1;
    InternalError = 2;
    Timeout = 3;
    InvalidParameter = 4;
    NoCdn = 5;
    RemoteError = 6; //简单处理:供应商返回错误 
}


////////////

//message RefreshUrlsReq {
//    string url = 1;
//}
  
//message RefreshUrlsResp {
//    bool result = 1;
//}

//message RefreshObjectReq {
//    string appid = 1;
//    string scope = 2;
//    string objectId = 3;
//}
  
//message RefreshObjectResp {
//    bool result = 1;
//}

//message GetScope2DomainsMapReq {
//}

//message GetScope2DomainsMapResp {
//    repeated Scope2Domain Scope2DomainList = 1;
//    repeated DomainHttpType DomainHttpTypeList =2;
//}

//message DomainHttpType {
//    string Domain = 1;
//    bool IsHttpType = 2;
//}

//message Scope2Domain {
//    string Scope = 1;
//    string Appid = 2;
//    repeated string DomainList = 3;
//}

//message GetInfoByUrlReq {
//    string url = 1;
//}

//message GetInfoByUrlResp {
//    string Appid = 1;
//    string Scope = 2;
//    string ObjectId = 3;
//    string bucket = 4;
//}

//message GetRealUrlReq {
//    string Url = 1;
//}

//message GetRealUrlResp {
//    string Url = 1;
//}
