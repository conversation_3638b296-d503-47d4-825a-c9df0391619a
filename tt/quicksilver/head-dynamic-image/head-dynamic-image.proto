syntax = "proto3";

package head_dynamic_image;

service HeadDynamicImage {
    rpc GetHeadDynamicImageMd5(GetHeadDynamicImageMd5Req) returns(GetHeadDynamicImageMd5Resp){}
    rpc TestAsyncScanImage(UidReq) returns(Empty){}
    rpc NotifyHeadDynamicImageUpdated(UidReq) returns(Empty){}
}

message UidReq {
    uint32 uid=1;
}
message Empty{}

message GetHeadDynamicImageMd5Req {
    repeated uint32 uid_list = 1;
    
}
message GetHeadDynamicImageMd5Resp {
    map<uint32, string> uid_headimage_md5_map = 1; // key:uid 有动态头像才会返回
}
