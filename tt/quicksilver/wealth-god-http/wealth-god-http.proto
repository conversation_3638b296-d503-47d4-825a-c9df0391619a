syntax = "proto3";

option go_package = "wealth-god-http";

package wealth_god_http;


message GetWealthMissionInfoRequest {
    string god_id = 1; // 财神ID
}

enum WealthMissionType {
    WEALTH_MISSION_TYPE_UNSPECIFIED = 0; // 未知类型
    WEALTH_MISSION_TYPE_STAY_IN_GAME = 1; // 在游戏中停留
    WEALTH_MISSION_TYPE_SEND_TBEAN_GIFT = 2; // 发送T豆礼物
}

message WealthMissionInfo {
    uint32 mission_type = 1; // 任务类型，see WealthMissionType
    string mission_icon = 2; // 任务图标
    string mission_name = 3; // 任务名称
    string mission_desc = 4; // 任务描述
    string mission_cnt_uint = 5; // 任务计数单位
    uint32 need_mission_cnt = 6; // 需要完成计数
    uint32 current_cnt = 7; // 当前完成计数
    bool is_reach_limit = 8; // 是否达到上限（在房任务每日完成次数有限制）
}


// 获取财神任务信息
message GetWealthMissionInfoResponse {
    repeated WealthMissionInfo wealth_mission_info_list = 1; // 财神降临任务信息列表
}


// uri: /tt-revenue-http-logic/wealth-god/get_wealth_god_box_reward_list
// 获取财神宝箱奖励列表
message GetWealthGodBoxRewardListRequest {
    uint32 page = 1;
    uint32 page_size = 2;
}

message GetWealthGodBoxRewardListResponse {
    repeated WealthGodBoxRewardItem reward_list = 1;
    bool has_more = 2;
}

message WealthGodBoxRewardItem {
    int64 id = 1; // id，用于列表去重
    string icon_url = 2; // 图标
    string title = 3; // 标题
    string sub_title = 4; // 副标题
    bool is_rare = 5; // 是否稀有
    string time_str = 6; // 时间
}
