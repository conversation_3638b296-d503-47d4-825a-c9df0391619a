syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/ttcmdstatistics";
package ttcmdstatistics;


//各个appID，由服务提供方决定
enum AppID{
    // 各app ID
    ENUM_APPID_UNKNOW = 0;
    ENUM_APPID_TT = 1;
    ENUM_APPID_YINPAI = 2;
    ENUM_APPID_MINIGAME = 3;
    ENUM_APPID_TTWEB = 4;
}

enum AppConfID{
    // 各app 上报配置 ID
    ENUM_APP_CONF_UNKNOW = 0;
    
    // 1~100给TT 
    ENUM_APP_CONF_TT_Andriod = 1;
    ENUM_APP_CONF_TT_IOS = 2;
    ENUM_APP_CONF_TT_WINDOW = 3;
    ENUM_APP_CONF_TT_MAC = 4;
    ENUM_APP_CONF_TT_MINIPROGRAM = 5;

    // 101~200给音派
    ENUM_APP_CONF_YINPAI_Andriod = 101;
    ENUM_APP_CONF_YINPAI_IOS = 102;
    ENUM_APP_CONF_YINPAI_WINDOW = 103;
    ENUM_APP_CONF_YINPAI_MAC = 104;

    // 201～300给小游戏
    ENUM_APP_CONF_MINIGAME_Andriod = 201;
    ENUM_APP_CONF_MINIGAME_IOS = 202;
    ENUM_APP_CONF_MINIGAME_WINDOW = 203;
    ENUM_APP_CONF_MINIGAME_MAC = 204;
    ENUM_APP_CONF_MINIGAME_OTHER = 209;

    //301~400给TTWEB
    ENUM_APP_CONF_TTWEB_Andriod = 301;
    ENUM_APP_CONF_TTWEB_IOS = 302;

    //TODO：301以后给其他
}


enum AppType{
    //此枚举类用于特殊场景，例如：在安卓手机上通过小程序打开app
    //使用app时客户端平台类型
    ENUM_APP_TYPE_ORIGINAL = 0;
    ENUM_APP_TYPE_QQ_MINIPROGRAM = 1;
    ENUM_APP_TYPE_WECHAT_MINIPROGRAM = 2;
}

enum RetCodeStatus{
    ENUM_RETCODE_NO_USE = 0;

    ENUM_RETCODE_SUCCESS = 200;
    
    ENUM_RETCODE_INNER_ERR = 500;       //内部错误(即系统错误)
    ENUM_RETCODE_LOGIC_ERR = 501;       //逻辑错误
    ENUM_RETCODE_TRAFFIC_ERR = 502;     //过载错误
}

message ClientInfo {
    bytes app_version = 1;     // app版本号
    uint32 os_type = 2;         // 1:other 2:andriod 3:ios 4:window 5:mac
    bytes os_version = 3;      // 操作系统版本(os_type的详细版本，例如win10、ios12)
    bytes rom_version = 4;     //(移动端)版本号 约有40个字节
    bytes phone_brand = 5;     // 手机品牌
    uint32 network = 6;         // wifi:0 未知:1 2g:2 3g:3 4g:4 5g:5 6g:6 ...
    string client_ip = 7;       //服务器解码后自己填
    int64  timestamp = 8;       //服务器解码后自己填
    bytes model= 9;            //手机型号
    AppID app_id = 10;          //应用编号：如TT、音派
    bytes device_id = 11;      //设备号
    AppType app_type = 12;      //qq小程序，微信小程序、原生app等
    uint32 subapp_id = 13;      //子应用id，小程序用可用、区分小程序游戏
}//待定122字节

message BizInfo {
    RetCodeStatus ret_code1 = 1;    //统一命令状态标准码，参考http状态码，提供给普罗米修斯；                            
    int32 ret_code2 = 3;            //业务没经转换的错误码，由客户端决定，希望能帮助业务定位大概问题
    bytes ret_msg = 2;
    uint32 cost = 4;                //单位为ms
    string cmd = 5;                 //cmd(这里可以是命令号也可以是uri)
    uint32 uid = 6;                 //uid 是否填写、以及怎么取值由客户端决定
    string uidstr = 7;              //用于其他的用户唯一标别,例如openid
} //80字节

enum PushInfoSource{
    ENUM_SOURCE_UNKNOWN = 0;
    ENUM_SOURCE_OLD = 1;       //旧推送通道
    ENUM_SOURCE_NEW = 2;       //新推送通道
}

message PushInfo{
    uint32 source = 1;
    uint64 size = 2;
    string ext = 3;
}

message BizStastics {
    ClientInfo client_info = 1;
    repeated BizInfo biz_infos = 2;  //聚合的包在客户端最大长度不超过配置中的max_bizinfo_length，否则直接丢弃
    repeated PushInfo push_infos = 3;      //推送采集消息,一个是old，一个是new
}

message StasticsConfig {
    AppConfID app_conf_id = 1;      //appID 区分客户端
    bool is_report = 4;             //是否上报 
    repeated uint32 cmds = 2;       //命令号列表,数字
    repeated string api_name = 3;   //方法名列表，适用于http和grpc类请求，只包含path
    uint32 interval = 5;            //上报时间间隔,单位秒 取值区间：[1,3600]
    uint32 retry_times = 6;         //客户端发包失败重试次数 取值区间:[0,5]
    uint32 retry_interval = 7;      //发包失败重试间隔,单位秒 取值区间:[1,10]
    uint32 max_bizinfo_length = 8;  //length(biz_infos) 区间：[10,2000] 
    repeated uint32 push_msg_types = 9;       //推送消息类型,数字,客户端是推送的cmd
}
