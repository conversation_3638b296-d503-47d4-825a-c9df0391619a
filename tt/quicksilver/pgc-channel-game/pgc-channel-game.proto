syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/pgc-channel-game";

package pgc_channel_game;

import "tt/quicksilver/pgc-adventure/pgc-adventure.proto";
import "tt/quicksilver/pgc-digital-bomb/pgc-digital-bomb.proto";

service PgcChannelGame {
    // 获取小游戏列表
    rpc GetGameList (GetGameListReq) returns (GetGameListResp) {}

    // 设置小游戏开始或者结束
    rpc SetGamePhase (SetGamePhaseReq) returns (SetGamePhaseResp) {}

    // 进房获取游戏信息
    rpc GetChannelGameInfo (GetChannelGameInfoReq) returns (GetChannelGameInfoResp) {}

    // 甩雷
    rpc SetNextBombUser (SetNextBombUserReq) returns (SetNextBombUserResp) {}

    // 清理雷信息
    rpc ClearUserBombInfo (ClearUserBombInfoReq) returns (ClearUserBombInfoResp) {}

    // 设置房间当前的游戏
    rpc SetChannelCurrentGameInfo (SetChannelCurrentGameInfoReq) returns (SetChannelCurrentGameInfoResp) {}
    // 删除房间当前的游戏
    rpc DelChannelCurrentGameInfo (DelChannelCurrentGameInfoReq) returns (DelChannelCurrentGameInfoResp) {}
    // 进房获取游戏信息
    rpc GetChannelCurrentGameInfo (GetChannelCurrentGameInfoReq) returns (GetChannelCurrentGameInfoResp) {}
    // 批量获取房间游戏状态
    rpc BatchGetGameBombPhase(BatchGetGameBombPhaseReq) returns (BatchGetGameBombPhaseResp) {}

    // ===========================v2===========================
}


enum PgcChannelGameId
{
    GAME_INVALID = 0;          // 无效值
    GAME_THROW_BOME = 1;        //甩雷
    GAME_DIGITAL_BOMB = 2;      //数字炸弹
    GAME_ADVENTURE = 3;         // 大冒险
}

//某个具体的操作按钮
message GameOperateButton {
    uint32 button_id = 1;         //button_id
    uint32 button_type = 2;           //按钮类型  see ButtonType
    string button_text = 3;           //操作按钮文案
}


//操作区域信息
message GameOperateInfo {
    uint32 operate_id = 1;
    string title = 2;                    //标题
    repeated GameOperateButton button_list = 3;    //标题下方的可操作列表
}

message ChoseOperate {
    uint32 operate_id = 1;        //see GameOperateInfo operate_id
    uint32 button_id = 2;      //see GameOperateTab button_id
}


//玩法页信息
message GameSummary {
    uint32 game_id = 1;               // gameid
    string game_name = 2;           // 游戏名
    string game_icon = 3;                    // 游戏icon
    string cms_url = 4;               // cms链接
    repeated GameOperateInfo info = 5;       // 操作区域
}


//获取小游戏列表
message GetGameListReq {
    uint32 channel_id = 1;
}

message GetGameListResp {
    repeated GameSummary game_list = 2;
}



//设置小游戏开始或者结束
message SetGamePhaseReq {
    uint32 channel_id = 1;
    uint32 game_id = 2;           //see GameSummary game_id
    uint32 target_phase = 3;    //see pgc-channel-game-logic.proto GamePhaseType
    repeated ChoseOperate chose = 4;
}

message SetGamePhaseResp {
    uint32 game_id = 1;
    uint32 target_phase = 2;
}


//甩雷
message SetNextBombUserReq {
    uint32 channel_id = 1;
    uint32 throw_uid = 2;  //甩雷的对象
}

message SetNextBombUserResp {
}


//进房获取游戏信息
message GetChannelGameInfoReq {
    uint32 channel_id = 1;
}

message GetChannelGameInfoResp {
    uint32 channel_id = 1;
    uint32 current_game_id = 2;
    uint32 phase = 3;           //see pgc-channel-game-logic.proto GamePhaseType
    GameThrowBombInfo info = 4;  //甩雷信息
    pgc_digital_bomb.DigitalBombGameInfo digital_bomb_info = 5; // 数字炸弹信息
    pgc_adventure.AdventureGameInfo adventure_info = 6;     // 大冒险信息
}

message GameThrowBombInfo {
    GameBombUserInfo bomb = 1;                   //雷所在用户信息
    repeated GameBombPunishInfo punish = 2;      //甩雷惩罚信息
    repeated GameThrowInfo throw_list = 3;       //麦上用户甩雷信息列表
    uint32 game_bomb_type = 4;                   // see GameBombType
}

//麦上用户甩雷信息
message GameThrowInfo
{
    uint32 uid = 1;
    uint32 gift_val = 2;            // 收礼值
    bool throw_status = 3;          // 可以甩状态 0不可以
}

//雷所在用户信息  
message GameBombUserInfo {
    uint32 uid = 1;        //雷所在uid
    uint32 end_time = 2;    //甩雷结束时间
    bool need_rescue = 3;           //需要展示救的按钮
    uint32 auto_throw_time = 4;     //不操作自动甩雷时间 时间戳
    uint32 server_time = 5;         //服务器当前时间戳
}



message UserProfile {
    uint32 uid = 1;
    string account = 2; //账号
    string nickname = 3; // 昵称
    string account_alias = 4; // 靓号，预留字段，暂时未赋值
    uint32 sex = 5; //用户性别
    UserPrivilege privilege = 6;
}

enum EUserPrivilegeType
{
    ENUM_USER_PRIVILEGE_UNKNOWN = 0; // 无效值
    ENUM_USER_PRIVILEGE_UKW = 1;  // 神秘人
}

message UserPrivilege {
    string account = 1; // 权益指定的账号，如神秘人服务提供的：神秘人头像对应的account
    string nickname = 2; // 权益指定的昵称，如神秘人服务提供的：神秘人+编号
    uint32 type = 3; // 权益类型, 0: 无效，1： 神秘人
    bytes options = 4; // 权益特殊属性，如 神秘人 对应 UserUKWInfo
}


//甩雷-惩罚信息
message GameBombPunishInfo {
    UserProfile user_profile = 1;
    uint32 end_time = 2;    //惩罚倒计时结束时间，为0清除惩罚
    uint32 rescue_gift_id = 3;          //解救礼物ID
    string rescue_gift_name = 4;          //解救礼物ID
    string icon_url = 5;                //惩罚特效zip资源包
    string icon_md5 = 6;                //资源包md5
    uint32 server_time = 7;             //服务器当前时间戳
}


//清理雷信息
message ClearUserBombInfoReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
}

message ClearUserBombInfoResp {
}


message GetChannelCurrentGameInfoReq {
    uint32 channel_id = 1;
}

message GetChannelCurrentGameInfoResp {
    uint32 channel_id = 1;
    uint32 current_game_id = 2;
}

message SetChannelCurrentGameInfoReq {
    uint32 channel_id = 1;
    uint32 game_id = 2;
}

message SetChannelCurrentGameInfoResp {

}

message DelChannelCurrentGameInfoReq {
    uint32 channel_id = 1;
    uint32 game_id = 2;
    bool force = 3; //是否强制删除
}

message DelChannelCurrentGameInfoResp {

}

message BatchGetGameBombPhaseReq {
    repeated uint32 channel_id = 1;
}

message BatchGetGameBombPhaseResp {
    map<uint32, uint32> phase_map = 1; // key: channel_id, value: see pgc-channel-game-logic_.proto GamePhaseType
}