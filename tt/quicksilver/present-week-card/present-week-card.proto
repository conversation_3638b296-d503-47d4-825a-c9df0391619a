syntax = "proto3";

package present_week_card;

option go_package = "golang.52tt.com/protocol/services/present-week-card";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service PresentWeekCard {

    // 获取周卡详情
    rpc GetPresentWeekCardInfo(GetPresentWeekCardInfoReq) returns (GetPresentWeekCardInfoResp) {}
    // 获取周卡入口信息
    rpc GetPresentWeekCardAccess(GetPresentWeekCardEntryReq) returns (GetPresentWeekCardEntryResp) {}
    // 购买周卡
    rpc BuyPresentWeekCard(BuyPresentWeekCardReq) returns (BuyPresentWeekCardResp) {}
    // 领取周卡奖励
    rpc ReceivePresentWeekCardReward(ReceivePresentWeekCardRewardReq) returns (ReceivePresentWeekCardRewardResp) {}
    // 获取全量礼物奖励信息
    rpc GetAllAwardInfo(GetAllAwardInfoReq) returns (GetAllAwardInfoResp) {}

    // 手动上下线
    rpc ManualOnlineEvent(ManualOnlineEventRequest) returns (ManualOnlineEventResponse) {}

    // ======================== 后台接口 ============================
    // 获取周卡配置
    rpc GetPresentWeekCardConfig(GetPresentWeekCardConfigRequest) returns (GetPresentWeekCardConfigResponse) {}
    // 设置周卡配置
    rpc SetPresentWeekCardConfig(SetPresentWeekCardInfoRequest) returns (SetPresentWeekCardInfoResponse) {}
    // 更新周卡配置
    rpc UpdatePresentWeekCardConfig(UpdatePresentWeekCardConfigRequest) returns (UpdatePresentWeekCardConfigResponse) {}
    // 删除周卡配置
    rpc DeletePresentWeekCardConfig(DeletePresentWeekCardConfigRequest) returns (DeletePresentWeekCardConfigResponse) {}
    // 更新周卡奖励信息
    rpc UpdatePresentWeekCardAwardInfo(UpdatePresentWeekCardAwardInfoRequest) returns (UpdatePresentWeekCardAwardInfoResponse) {}
    // 获取指定周卡奖励信息
    rpc GetPresentWeekCardAwardInfo(GetPresentWeekCardAwardInfoRequest) returns (GetPresentWeekCardAwardInfoResponse) {}
    // 获取本地周卡信息
    rpc GetWeekCardInfoLocal(GetWeekCardInfoLocalRequest) returns (GetWeekCardInfoLocalResponse) {}

    // ======================== 对账接口 ===============================
    // 发放包裹数据对账
    rpc GetAwardTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetAwardOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
    // T豆消费数据对账
    rpc GetConsumeTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetConsumeOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
}

// 奖励领取状态
enum AwardStatusType{
    AWARD_STATUS_TYPE_UNSPECIFIED = 0;
    AWARD_STATUS_TYPE_LOCKING = 1;          // 尚不可领取
    AWARD_STATUS_TYPE_WAIT_TO_RECV = 2;     // 待领取
    AWARD_STATUS_TYPE_ALREADY_RECEIVED = 3; // 已领取
    AWARD_STATUS_TYPE_EXPIRED = 4;          // 已过期
}

// 奖励类型
enum AwardItemType{
    AWARD_ITEM_TYPE_UNSPECIFIED = 0; // 无效值
    AWARD_ITEM_TYPE_PACKAGE = 1;     // 包裹礼物
    AWARD_ITEM_TYPE_NOBLE_CARD = 2;  // 贵族体验卡
    AWARD_ITEM_TYPE_MIC_STYLE = 3;   // 麦位框
    AWARD_ITEM_TYPE_HORSE = 4;       // 坐骑
    AWARD_SUPER_PLAYER = 5;          // 超级玩家体验卡
    AWARD_ITEM_TYPE_DECORATION = 6;  // 用户主页飘
    AWARD_ITEM_TYPE_NAMEPLATE = 7;   // 个人铭牌
}

message AwardItemInfo{
    string award_id = 1;     // 奖励ID
    uint32 amount = 2;       // 数量/状态-天数
    uint32 award_type = 3;   // 奖励类型  see AwardItemType
    uint32 award_value = 4;  // 奖励价值，T豆
}

message DailyAwardInfo{
    uint32 status = 1;                        // 奖励状态，see AwardStatusType
    repeated AwardItemInfo award_list = 2;    // 奖品列表

    uint32 award_order = 3;                   // 奖励顺序
}

// 周卡详情
message PresentWeekCardInfo{
    uint32 card_id = 1;       // 周卡id
    string tier_name = 2;     // 档位名称
    uint32 price = 3;         // 档位价格
    uint32 origin_price = 4;  // 原价，仅作展示作用, 不为0时展示原价文案
    
    enum BuyStatus{
        BUY_STATUS_UNSPECIFIED = 0;
        BUY_STATUS_NOT_BUY = 1;      // 未购买
        BUY_STATUS_ALREADY_BUY = 2;  // 已购买
        BUY_STATUS_LOCKING = 3;      // 当前不可购买
    }
    uint32 buy_status = 5;                  // 周卡购买状态
    repeated DailyAwardInfo award_list = 6; // 奖励列表

    string selling_point_text = 7;          // 周卡卖点文案

    uint32 expire_time = 8;                // 过期时间
    uint32 buy_time = 9;                   // 购买时间
    uint32 cycle_cnt = 10;               // 测试模式奖励领取周期
}

  
// 获取周卡入口信息
message GetPresentWeekCardEntryReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message GetPresentWeekCardEntryResp{
    bool have_access = 1;           // 是否有入口
    enum ReceiveStatus{
        RECEIVE_STATUS_UNSPECIFIED = 0;
        RECEIVE_STATUS_WAIT_TO_RECV = 1;       // 奖励待领取
        RECEIVE_STATUS_NO_AWARD_AVAILABLE = 2; // 不可领取
    }
    uint32 receive_status = 2;          // 可领取状态
    
    // pgc 自动拉起半屏提醒配置
    bool need_to_auto_show = 3; // 是否需要自动拉起
    uint32 stay_seconds = 4;    // 当日进入pgc房间首次停留 x 秒时，自动展示半屏页
    uint32 every_n_day = 5;     // 每n天自动展示一次；为0时，当天展示一次
}

// 获取周卡详情
message GetPresentWeekCardInfoReq{
    uint32 uid = 1;
}

message GetPresentWeekCardInfoResp{
    repeated PresentWeekCardInfo card_list = 1; // 周卡列表
}

// 购买周卡
message BuyPresentWeekCardReq{
    uint32 uid = 1;
    uint32 card_id = 2;
    int64 outside_time = 3;
}

message BuyPresentWeekCardResp{
    uint32 balance = 1;         // T豆余额
}

// 领取周卡奖励
message ReceivePresentWeekCardRewardReq{
    uint32 uid = 1;
    uint32 card_id = 2;
    int64 outside_time = 3;
    uint32 recv_idx = 4;  // 用户期待领取的奖励序号
}

message ReceivePresentWeekCardRewardResp{

    repeated DailyAwardInfo award_list = 1;    // 奖品列表
    bool with_extra_award = 2;    // 是否包括额外奖励
}

// 获取全量奖励信息
message GetAllAwardInfoReq{
}

message GetAllAwardInfoResp{
    repeated AwardItemInfo award_list = 1; // 周卡列表
}

// =============== 后台接口 ===============

message PresentWeekCardConf {
    uint32 card_id = 1;       // 周卡id
    string tier_name = 2;     // 档位名称
    uint32 price = 3;         // 档位价格
    uint32 origin_price = 4;  // 原价，仅作展示作用, 不为0时展示原价文案
    string selling_point_text = 5; // 周卡卖点文案

    int64 min_last_month_consume = 6; // 最小上月消费,满足才可见该周卡
    int64 min_history_consume = 7;    // 最小历史消费,满足才可见该周卡

    bool is_del = 8; // 是否删除
}

message PresentWeekCardAwardInfo{
    uint32 card_id = 1;                     // 周卡id
    repeated DailyAwardInfo award_list = 2; // 奖励列表
    DailyAwardInfo extra_award = 3;        // 额外奖励
}

// 设置周卡信息
message SetPresentWeekCardInfoRequest{
    PresentWeekCardConf conf =1;
}

message SetPresentWeekCardInfoResponse{
}

// 获取所有生效中周卡配置信息
message GetPresentWeekCardConfigRequest{}

message GetPresentWeekCardConfigResponse{
    repeated PresentWeekCardConf card_list = 1; // 周卡列表
}

// 获取指定周卡的奖励信息
message GetPresentWeekCardAwardInfoRequest{
    uint32 card_id = 1;
}

message GetPresentWeekCardAwardInfoResponse{
    PresentWeekCardAwardInfo award_info = 1;
}

// 更新周卡配置
message UpdatePresentWeekCardConfigRequest{
    uint32 card_id = 1; 
    enum UpdateField{
        UPDATE_FIELD_UNSPECIFIED = 0;
        UPDATE_FIELD_PRICE = 1; // 修改价格
        UPDATE_FIELD_ORIGIN_PRICE = 2; // 修改原价
        UPDATE_FIELD_TIER_NAME = 3; // 修改档位名称
        UPDATE_FIELD_MIN_LAST_MONTH_CONSUME = 4; // 修改最小上月消费
        UPDATE_FIELD_MIN_HISTORY_CONSUME = 5; // 修改最小历史消费
        UPDARE_FIELD_SELLIN_POINT_TEXT = 6; // 修改卖点文案
    }

    uint32 field_type = 2;    // see UpdateField
    uint32 value = 3;         // uint32类型值
    string str_value = 4;     // string类型值
}

message UpdatePresentWeekCardConfigResponse{
}

// 更新周卡奖励信息
message UpdatePresentWeekCardAwardInfoRequest{
    uint32 card_id = 1;
    PresentWeekCardAwardInfo award_info = 2;
}

message UpdatePresentWeekCardAwardInfoResponse{
}

// 删除周卡配置
message DeletePresentWeekCardConfigRequest{
    uint32 card_id = 1;
}

message DeletePresentWeekCardConfigResponse{
}

// 获取本地周卡信息
message GetWeekCardInfoLocalRequest{
    uint32 card_id = 1;
}

message GetWeekCardInfoLocalResponse{
    PresentWeekCardConf conf = 1;
    PresentWeekCardAwardInfo award_info = 2;
}



message ManualOnlineEventRequest{
    uint32 uid = 1;
    uint32 event_type = 2; // 0-login 1-logout
}
message ManualOnlineEventResponse{
    
}