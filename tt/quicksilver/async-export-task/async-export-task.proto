syntax = "proto3";

package async_export_task;

import "tt/quicksilver/extension/options/options.proto";

option go_package = "golang.52tt.com/protocol/services/async-export-task";

service AsyncExportTask {
  option (service.options.old_package_name) = "AsyncExportTask.AsyncExportTask";

  //通过类型翻页获取异步下载任务列表
  rpc GetExportAllTaskList(GetExportAllTaskListReq) returns (GetExportAllTaskListResp) {}
  //获取临时下载地址
  rpc GetTempUrl(IdReq) returns (TempUrlResp) {}
  //多人互动额外奖励结算报表异步导出
  rpc CreateAmuseExtraIncomeSettleListExportTask(CreateAmuseExtraIncomeSettleListExportTaskReq) returns (Empty) {}

  // 主播查询
  rpc QueryAnchorInfoTask(QueryAnchorInfoTaskReq) returns (QueryAnchorInfoTaskResp) {}

  // 查询pgc推荐库的历史备份信息
  rpc QueryPgcPrepareBackupInfo(QueryPgcPrepareBackupInfoReq) returns (QueryPgcPrepareBackupInfoResp) {}

  // 查询奖励积分信息
  rpc QueryAnchorScoreList(QueryAnchorScoreListReq) returns (QueryAnchorScoreListResp) {}
}

message IdReq {
  uint32 id = 1;
}

message TempUrlResp {
  string temp_url = 1;
}

message GetExportAllTaskListReq {
  uint32 task_type = 1;
  uint32 offset = 2;
  uint32 limit = 3;
}

message GetExportAllTaskListResp {
  repeated ExportTaskData list = 1;
  uint32 total = 2;
}

enum TaskType{
  Unknown = 0;
  CreateAmuseExtraIncomeSettleList = 1;
  QueryAnchorInfo = 2; // 主播信息查询
  QueryPrepareList = 3;  // 评级库记录列表
  QueryAnchorScore = 4;  // 奖励积分导出
}

message ExportTaskData {
  uint32 id = 1;
  string task_desc = 2;
  uint32 task_type = 3;
  uint32 percent = 4; //运行百分比
  uint32 create_time = 5;
  string err_data = 6;  //错误信息
}

message CreateAmuseExtraIncomeSettleListExportTaskReq {
  uint32 guild_id = 1;  //0则是全部导出
  uint32 start_time = 2;
  uint32 end_time = 3;
  uint32 compare_start_time = 4;
  uint32 compare_end_time = 5;
  uint32 master_uid = 6; // 对公会长UID
}

message Empty {

}


enum BanStatusType{
   BanStatusNoValid = 0; // 无效
   BanStatusNo = 1 ;  // 正常，没封禁
   BanStatusYes = 2;   // 封禁
}

// 查询主播信息
message QueryAnchorInfoTaskReq {
  repeated uint32 uid_list = 1;  //指定uid查询
  repeated string ttid_list = 2;  // 指定tid查询
  uint32 ban_status = 3;  //  see BanStatusType
}
message QueryAnchorInfoTaskResp {
}


// 查询pgc推荐库的历史备份信息
message QueryPgcPrepareBackupInfoReq {
  int64 version = 1;  //  备份版本
}
message QueryPgcPrepareBackupInfoResp {
}


// 查询奖励积分信息
message QueryAnchorScoreListReq {
   uint32 begin_ts = 1;
   uint32 end_ts = 2;
   repeated uint32 uid_list = 3;  // 指定uid查询
   repeated string ttid_list = 4;  // 指定tid查询
   bool is_has_exchage = 5;   // 提现积分或已汇总对公积分大于0
}
message QueryAnchorScoreListResp {
}