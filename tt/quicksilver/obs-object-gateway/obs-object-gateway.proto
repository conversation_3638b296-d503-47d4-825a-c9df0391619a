syntax = "proto3";

package obs_object_gateway;

option go_package = "golang.52tt.com/protocol/services/obs-object-gateway";

service ObsObjectGateway {
    rpc ClaimToken(ClaimTokenReq) returns (ClaimTokenResp) {}
    rpc ValidateToken(ValidateTokenReq) returns (ValidateTokenResp) {}
    rpc Upload(UploadReq)returns(UploadResp){}
    rpc Download(DownloadReq)returns(DownloadResp){}
    rpc Delete(DeleteReq)returns(DeleteResp){}
    rpc MultipartUploadInit(MultipartUploadInitReq)returns(MultipartUploadInitResp){}
    rpc MultipartUpload(MultipartUploadReq)returns(MultipartUploadResp){}
    rpc MultipartUploadComplete(MultipartUploadCompleteReq)returns(MultipartUploadCompleteResp ){}
    rpc MultipartUploadAbort(MultipartUploadAbortReq)returns(MultipartUploadAbortResp){}
    rpc Copy(CopyReq)returns(CopyResp){}

    //TODO:deprecated
    rpc Freeze(FreezeReq)returns(FreezeResp){}
    //TODO:deprecated
    rpc Unfreeze(UnfreezeReq)returns(UnfreezeResp){}

    
    //media processing
    rpc SubmitMediaProcessTask(SubmitMediaProcessTaskReq) returns (SubmitMediaProcessTaskResp) {} 
    //rpc QueryMediaProcessTask(QueryMediaProcessTaskReq) returns (QueryMediaProcessTaskResp) {} 
    rpc TriggerQueryMediaProcessTask(TriggerQueryMediaProcessTaskReq) returns (TriggerQueryMediaProcessTaskResp) {} 
    rpc CancelMediaProcessTask(CancelMediaProcessTaskReq) returns (CancelMediaProcessTaskResp) {} 

    //redirect, 2023.02.16
    rpc Redirect(RedirectReq) returns (RedirectResp) {} 
    rpc RedirectUrl(RedirectUrlReq) returns (RedirectUrlResp) {} 

    //转发对象事件, 2023.05.18
    rpc SendObjectEvent(SendObjectEventReq)returns(SendObjectEventResp){}

}

message ClaimTokenReq {
    repeated TokenClaims claims = 1;
}
message ClaimTokenResp { 
    repeated string tokens = 1;
}
message TokenClaims { 
    oneof claims {
        UploadTokenClaims upload_claims  = 1;
        DownloadTokenClaims download_claims  = 2;
        DeleteTokenClaims  delete_claims = 3; 
    }
}
message UploadTokenClaims {
    string app = 1;
    string scope = 2;
    int32 expiration = 3; //second
    string key_prefix = 4; // optional, key前缀匹配，兼容广场使用的七牛
    string media_process = 5; // optional, 媒体处理，同 SubmitMediaProcessTaskReq.process 
    string custom_id = 6; //optional, 用于关联业务自定义id，比如 帖子postid, 2022.07,08
}
message DownloadTokenClaims {
    string app = 1;
    string scope = 2;
    int32  expiration  = 3; //second
    string key = 4; //optional
}
message DeleteTokenClaims {
    string app = 1;
    string scope = 2;
    int32  expiration  = 3;
} 
message ValidateTokenReq {
    string token = 1;
    //string key = 2; // ??? 可选，检查 key_prefix
} 
message ValidateTokenResp {
    TokenClaims claims = 1;
}

message UploadReq {
    ObjectId oid = 1;
    bytes data = 2;
    string content_type = 3;
    bool no_review = 4;
    string media_process = 5; // optional, 媒体处理，同 SubmitMediaProcessTaskReq.process 
    string custom_id = 6;
    int32 download_token_expiration = 7; //公有桶忽略; 私有桶，需>0 
}
message UploadResp {
    string key = 1;
    string etag = 2;
    repeated string mp_task_id_list = 3; 
    string download_token = 4; 
}
message DownloadReq {
    ObjectId oid = 1;
    int64 range_start = 2;
    int64 range_end = 3;
}
message DownloadResp { 
    bytes data = 1;
    string content_type = 2;
    bool exists = 3;
}
message DeleteReq {
    ObjectId oid = 1;
    bool freezed = 2;
}
message DeleteResp {
}
message MultipartUploadInitReq {
    ObjectId oid = 1;
    string content_type = 2;
    string custom_id = 3;
}
message MultipartUploadInitResp {
    string key = 1;
    string upload_id = 2;
    int32 blksize = 3; //ucloud 有返回, 但不强制；tx/qiniu 无
}
message MultipartUploadReq {
    ObjectId oid = 1;
    string upload_id  = 2;
    int32 part_num = 3; //从 1 开始, 连续，原因: ucloud 要求必须从0开始、连续，tx/qiniu要求范围是1~10000
    bytes data = 4;
} 
message MultipartUploadResp {
    string etag = 1;
}
message MultipartUploadCompleteReq {
    message Part {
        int32 part_num = 1;
        string etag = 2;
    }

    ObjectId oid = 1;
    string content_type = 2;
    string upload_id = 3;
    repeated Part parts = 4;
    bool no_review = 5;
    string media_process = 6; // optional, 媒体处理，同 SubmitMediaProcessTaskReq.process 
}
message MultipartUploadCompleteResp { 
    string etag = 1;
    repeated string mp_task_id_list = 3; 
}
message MultipartUploadAbortReq {
    ObjectId oid = 1;
    string upload_id = 2;
} 
message MultipartUploadAbortResp {
}
message FreezeReq {
    ObjectId oid = 1;
}
message FreezeResp {
}
message UnfreezeReq {
    ObjectId oid = 1;
}
message UnfreezeResp {
}
message CopyReq {
    string src_url = 1;
    ObjectId dest_oid = 2;  //oid.key is optional
    string content_type = 3; //optional
    bool with_dest_url = 4; //是否返回目的 url
    int32 token_expiration = 5; //公有桶忽略; 私有桶，需>0 
    bool no_review = 6;
    string custom_id = 7;
}
message CopyResp {
    string key = 1;
    string etag = 2;
    string url  = 3; // optional
    string token = 4;
}
message SubmitMediaProcessTaskReq {
    ObjectId oid = 1;
    string process = 2;  //见 obs/mp/MediaProcess
}
message SubmitMediaProcessTaskResp { 
    repeated string task_id_list = 1;
}
//task 状态
//message MediaProcessTaskState {
//    enum STValue{
//        Nil = 0;
//        Complete = 1;
//        Running = 2; 
//        Cancelled = 3; 
//        NotExists = 4;
//    }
//    string task_id = 1;
//    STValue state = 2;
//}
message TriggerQueryMediaProcessTaskReq {
    ObjectId oid = 1;
    repeated string task_id_list = 2;
}
message TriggerQueryMediaProcessTaskResp {
    //bool exists = 1;
    //repeated MediaProcessTaskState states = 2;
    //string result = 3; //见 obs/mp.MediaProcessResult 
}
message CancelMediaProcessTaskReq {
    ObjectId oid = 1;
    repeated string task_id_list = 2;
    bool is_timeout = 3;
}
message CancelMediaProcessTaskResp {
    //bool exists = 1;
} 

message RedirectReq {
    string app = 1;    
    string scope = 2; 
    string key = 3;
    string ref_host = 4; //optional, 52tt.com, etc
    int32 expiration = 5; //second
}
message RedirectResp {
    string provider = 1;
    string bucket = 2;
    string domain = 3;
    string key = 4;
    string token_query = 5;
}
message RedirectUrlReq {
    string url = 1;
    int32 expiration = 5; //second
}
message RedirectUrlResp {
    string url = 1;
}
message SendObjectEventReq {
    ObjectId oid = 1;
    bytes data = 2;
}
message SendObjectEventResp {
}

message ObjectId {
    // app+scope or provider+bucket
    string app = 1;
    string scope = 2;
    string provider = 3;
    string bucket = 4;
    string key = 5; //
}
