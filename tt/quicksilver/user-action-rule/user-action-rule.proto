syntax = "proto3";

package user_action_rule;

option go_package = "golang.52tt.com/protocol/services/user-action-rule";

service UserActionRule {
  // 批量查询用户行为规则
  rpc BatchGetUserActionRule(BatchGetUserActionRuleReq) returns (BatchGetUserActionRuleResp) {}
  // 设置用户行为规则，例如禁止上麦等
  rpc SetUserActionRule(SetUserActionRuleReq) returns (SetUserActionRuleResp) {}
}

// see risk_mng_api.proto
enum UserActionRuleType {
  USER_ACTION_RULE_TYPE_UNSPECIFIED = 0;
  USER_ACTION_RULE_TYPE_PUBLISH_ROOM = 1; // 禁止发布房间
  USER_ACTION_RULE_TYPE_ROOM_PUBLISH_MSG = 2; // 用户在房间内禁止发公屏消息
  USER_ACTION_RULE_TYPE_ROOM_CLICK_MIC = 3; // 用户在房间内禁止上麦
  USER_ACTION_RULE_TYPE_ENTER_ENTERTAINMENT_ROOM = 4; // 禁止进入娱乐房(包括 公会公开房、语音直播房)
  USER_ACTION_RULE_TYPE_ENTER_NOT_ENTERTAINMENT_ROOM = 5; // 禁止进入约玩房(包括 除了 公会公开房、语音直播房 之外的其他房间)
  USER_ACTION_RULE_TYPE_ROOM_CLICK_MIC_V2 = 6; // 用户在房间内禁止上麦
  USER_ACTION_RULE_TYPE_IM_NOT_SEEN  = 7; // IM场景下违规内容自见
  USER_ACTION_RULE_TYPE_CHANNEL_IM_NOT_SEEN  = 8; // 房间公屏场景下违规内容自见
}

enum UserActionRuleOpType {
  USER_ACTION_RULE_OP_TYPE_UNSPECIFIED = 0;
  USER_ACTION_RULE_OP_TYPE_ENABLE = 1; // 启用规则
  USER_ACTION_RULE_OP_TYPE_DISABLE = 2; // 关闭规则
}

message UserActionRuleInfo {
  uint32 id = 1;
  uint32 uid = 2;
  UserActionRuleType rule_type = 3;
  UserActionRuleOpType op_type = 4;
  int64 begin_at = 5; // 开始时间戳，秒
  int64 end_at = 6; // 结束时间戳，秒
  int64 update_at = 7; // 更新时间戳，秒
  string reason = 8; // 操作原因
  string operator_name = 9; // 操作人
  int32 market_id = 10; // 生效的马甲包id
  int32 client_type = 11; // 生效的终端类型
}

message UserActionRuleList {
  repeated UserActionRuleInfo action_rule_list = 1;
}

message BatchGetUserActionRuleReq{
  repeated uint32 uid_list = 1;
}

message BatchGetUserActionRuleResp{
  map<uint32, UserActionRuleList> user_action_rule_map = 1;
}

message SetUserActionRuleReq {
  uint32 uid = 1;
  UserActionRuleType rule_type = 2;
  UserActionRuleOpType op_type = 3;
  int64 begin_at = 4; // 开始时间戳，秒
  int64 end_at = 5; // 结束时间戳，秒
  string reason = 6; // 操作原因
  string operator_name = 7; // 操作人
  repeated uint32 market_id_list = 8; // 生效的马甲包id列表，为空时表示全部生效
  repeated uint32 client_type_list = 9; // 生效的终端类型列表，为空时表示全部生效
}

message SetUserActionRuleResp {
}

