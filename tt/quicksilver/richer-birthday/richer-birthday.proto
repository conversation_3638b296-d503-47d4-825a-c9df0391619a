syntax = "proto3";

package richer_birthday;

option go_package = "golang.52tt.com/protocol/services/richer-birthday";

service RicherBirthdayService {
  rpc GetRicherBirthdayRegisterStatus (GetRicherBirthdayRegisterStatusRequest) returns (GetRicherBirthdayRegisterStatusResp) {}
  rpc RegisterRicherBirthday (RegisterRicherBirthdayReq) returns (RegisterRicherBirthdayResp) {}
  rpc GetRicherBirthdayRewardList (GetRicherBirthdayRewardListReq) returns (GetRicherBirthdayRewardListResp) {}
  rpc GetRicherBirthdayInfo (GetRicherBirthdayInfoRequest) returns (GetRicherBirthdayInfoResponse) {}
  rpc HideRicherBirthdaySwitch (HideRicherBirthdaySwitchRequest) returns (HideRicherBirthdaySwitchResponse) {}
  rpc GetHideRicherBirthdaySwitch (GetHideRicherBirthdaySwitchRequest) returns (GetHideRicherBirthdaySwitchResponse) {}
  rpc GetRicherBirthdayGiftCfgInRoom (GetRicherBirthdayGiftCfgInRoomRequest) returns (GetRicherBirthdayGiftCfgInRoomResponse) {}
  rpc ReceiveRicherBirthdayGift (ReceiveRicherBirthdayGiftRequest) returns (ReceiveRicherBirthdayGiftResponse) {}
  rpc CheckIfReceivedRicherBirthdayGift (CheckIfReceivedRicherBirthdayGiftRequest) returns (CheckIfReceivedRicherBirthdayGiftResponse) {}
  rpc GetRicherBirthdayRankList (GetRicherBirthdayRankListRequest) returns (GetRicherBirthdayRankListResponse) {}
  rpc GetUserRicherBirthdayRank (GetUserRicherBirthdayRankRequest) returns (GetUserRicherBirthdayRankResponse) {}
  rpc GetRicherBirthdayBlessList (GetRicherBirthdayBlessListRequest) returns (GetRicherBirthdayBlessListResponse) {}
  rpc ReportRicherBirthdayBless (ReportRicherBirthdayBlessRequest) returns (ReportRicherBirthdayBlessResponse) {}
  rpc CheckIfCouldSendRicherBirthdayGift (CheckIfCouldSendRicherBirthdayGiftRequest) returns (CheckIfCouldSendRicherBirthdayGiftResponse) {}
  rpc BathGetRicherBirthdayInfo(BathGetRicherBirthdayInfoRequest) returns (BathGetRicherBirthdayInfoResponse) {}
  rpc SendRicherBirthdaySystemMessage(SendRicherBirthdaySystemMessageRequest) returns (SendRicherBirthdaySystemMessageResponse) {}
  rpc TestSetUserBirthday(TestSetUserBirthdayRequest) returns (TestSetUserBirthdayResponse) {}
}

// 获取生日信息
message GetRicherBirthdayRegisterStatusRequest {
  uint32 uid = 1;
}

// 查看生日登记状态
message GetRicherBirthdayRegisterStatusResp {
  bool is_richer = 1; // 是否为大R
  int64 birthday = 2; // 生日时间戳， 秒
  bool could_modify = 3; // 是否可以修改，true:可以修改
}


// 生日登记（设置或者修改）
message RegisterRicherBirthdayReq {
  uint32 uid = 1;
  int64 birthday = 2; // 生日时间戳， 秒
  bool is_update = 3; // 是否更新生日
}

message RegisterRicherBirthdayResp {
}


// 获取生日奖励列表
message GetRicherBirthdayRewardListReq {
}

message RicherBirthdayReward {
  string reward_id = 1; // 奖励ID
  string reward_name = 2; // 奖励名称
  uint32 reward_type = 3; // 奖励类型
  uint32 reward_num = 4; // 奖励数量
  string reward_icon = 5; // 奖励图标
}

message GetRicherBirthdayRewardListResp {
  repeated RicherBirthdayReward richer_birthday_reward_list = 1;
}


message BirthdayBlessingCfg {
  repeated uint32 gift_id_list = 1;  // 祝福礼物id列表
  repeated string blessing_text = 2; // 祝福语列表
}


message GetRicherBirthdayInfoRequest {
  uint32 uid = 1;
}

message GetRicherBirthdayInfoResponse {
  map<uint32, int64> birthday_timestamp_map = 1; // 近三天生日用户的生日timestamp,秒
  BirthdayBlessingCfg blessing_cfg  = 2; // 祝福配置
  bool is_richer = 3; // 是否是大R，大R才有隐藏生日开关
}

message HideRicherBirthdaySwitchRequest {
  uint32 uid = 1;
  bool is_hide = 2;
}

message HideRicherBirthdaySwitchResponse {
}

message GetHideRicherBirthdaySwitchRequest {
  uint32 uid = 1;
}

message GetHideRicherBirthdaySwitchResponse {
  bool is_hide = 1;
}

message GetRicherBirthdayGiftCfgInRoomRequest {
  uint32 uid = 1;
  uint32 channel_id = 2; // 房间id
}

message GetRicherBirthdayGiftCfgInRoomResponse {
  repeated uint32 gift_id_list = 1; // 礼物架礼物id列表
}

// 领取生日礼包
message ReceiveRicherBirthdayGiftRequest {
  uint32 uid = 1;
}

message ReceiveRicherBirthdayGiftResponse {
}

// 检查是否拉取了生日礼包
message CheckIfReceivedRicherBirthdayGiftRequest {
  uint32 uid = 1;
  int64 birthday = 2; // 生日时间戳， 秒
}

message CheckIfReceivedRicherBirthdayGiftResponse {
  bool received = 1;
}

message RankInfo {
  uint32 uid = 1;
  uint32 val = 2; // 榜单值
  uint32 rank = 3;// 排名
}

// 获取榜单列表
message GetRicherBirthdayRankListRequest {
  uint32 uid = 1;
  uint32 limit = 2;
}

message GetRicherBirthdayRankListResponse {
  repeated RankInfo rank_list = 1;
  RankInfo my_rank = 2;
}

// 获取用户榜单信息
message GetUserRicherBirthdayRankRequest {
  uint32 uid = 1;
}

message GetUserRicherBirthdayRankResponse {
  RankInfo rank = 1;
}

message BlessInfo {
    uint32 from_uid = 1; // 用户信息
    string order_id = 2; // 订单号
    uint32 gift_id = 3; // 礼物信息
    string bless_content = 4; // 祝福内容
    int64 bless_time = 5; // 祝福时间
    uint32 num = 6; // 礼物数量
}

// 获取用户的祝福列表
message GetRicherBirthdayBlessListRequest {
  uint32 uid = 1;
  uint32 offset = 2;
  uint32 limit = 3;
}

message GetRicherBirthdayBlessListResponse {
  repeated BlessInfo bless_list = 1;
}


// 上报订单祝福语
message ReportRicherBirthdayBlessRequest {
  uint32 from_uid = 1;
  uint32 to_uid = 2;
  string bless_content = 3;
  int64 bless_time = 4;
  string order_id = 5;
  uint32 gift_id = 6;
  uint32 num = 7;
}

message ReportRicherBirthdayBlessResponse {
}

// 检查是否能发送生日礼物
message CheckIfCouldSendRicherBirthdayGiftRequest {
  uint32 uid = 1; // 发送者uid
  uint32 target_uid = 2;  // 目标用户id
  uint32 gift_id = 3; // 礼物id
  uint32 channel_id = 4; // 房间id
  uint32 send_source = 5; // 送礼来源
}

enum RicherBirthdayGiftType {
  RICHER_BIRTHDAY_GIFT_TYPE_UNSPECIFIED = 0; // 未指定
  RICHER_BIRTHDAY_GIFT_TYPE_BLESS = 1; // 礼物架祝福礼物
  RICHER_BIRTHDAY_GIFT_TYPE_RICHER = 2; // 大R返场礼物
  RICHER_BIRTHDAY_GIFT_TYPE_IM = 3; // IM祝福礼物
  RICHER_BIRTHDAY_GIFT_TYPE_NORMAL = 4; // 普通礼物
}

// 检查是否能发送生日礼物返回
message CheckIfCouldSendRicherBirthdayGiftResponse {
  RicherBirthdayGiftType gift_type = 1; // 礼物类型
  bool could_send = 2; // 是否能发送
}

// 批量获取用户生日信息
message BathGetRicherBirthdayInfoRequest {
  repeated uint32 uid_list = 1;
}

message BathGetRicherBirthdayInfoResponse {
  map<uint32, int64> birthday_timestamp_map = 1; // 用户生日timestamp,秒
}


message SendRicherBirthdaySystemMessageRequest {
    uint32 uid = 1;
    uint32 target_uid = 2;
}
message  SendRicherBirthdaySystemMessageResponse {

}

message TestSetUserBirthdayRequest {
    uint32 uid = 1;
    int64 birthday = 2;
}

message TestSetUserBirthdayResponse {
}