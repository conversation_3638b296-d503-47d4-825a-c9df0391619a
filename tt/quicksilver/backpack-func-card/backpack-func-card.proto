syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/backpack-func-card";
package backpack_func_card;
import "tt/quicksilver/backpack-base/backpack-base.proto";

service BackpackFuncCard {
  // 功能卡配置
  rpc AddFuncCardCfg (AddFuncCardCfgReq) returns (AddFuncCardCfgResp);
  // 删除功能卡
  rpc DelFuncCardCfg (DelFuncCardCfgReq) returns (DelFuncCardCfgResp);
  // 获取功能卡配置
  rpc GetFuncCardCfg (GetFuncCardCfgReq) returns (GetFuncCardCfgResp);
  // 使用功能卡
  rpc UseFuncCard (UseFuncCardReq) returns (UseFuncCardResp);
  // 检查财富魅力加速卡状态
  rpc GetAccelerateCardUsage (GetAccelerateCardUsageReq) returns (GetAccelerateCardUsageResp);
  // 批量检查财富魅力加速卡状态
  rpc BatchGetAccelerateCardUsage (BatchGetAccelerateCardUsageReq) returns (BatchGetAccelerateCardUsageResp);
}

// 功能卡片配置
message FuncCardCfg
{
  uint32 card_id = 1;
  backpack_base.PackageItemType card_type = 2; // 功能卡类型
  string card_name = 3; // 功能卡名称
  string card_desc = 4; // 功能卡描述
  string card_url = 5; // 功能卡图标
  uint32 card_times = 6; // 加速卡倍率（加速卡）
  uint32 valid_time = 7; // 有效时间
  bool is_del = 8; // 是否删除
  uint32 uid = 9; // 创建者
  uint64 card_value = 10; // 数值（财富卡）
}

message AddFuncCardCfgReq {
  FuncCardCfg card_cfg = 1;
}
message AddFuncCardCfgResp {
}

message DelFuncCardCfgReq {
  uint32 card_id = 1;
}
message DelFuncCardCfgResp {
}

message GetFuncCardCfgReq {
  repeated uint32 card_id_list = 1;
  bool     is_origin_desc = 2;       //是否使用原描述
}
message GetFuncCardCfgResp {
  repeated FuncCardCfg card_cfg_list = 1;
}

message UseFuncCardReq {
  uint32 uid = 1;
  uint32 card_id = 2;
  backpack_base.PackageItemType card_type = 3; // 功能卡类型
  uint32 use_count = 5;
  uint32 target_uid = 6;
  uint32 channel_id = 7;
}
message UseFuncCardResp {
  uint32 remain = 1;
  string msg = 2;
}

message GetAccelerateCardUsageReq {
  uint32 uid = 1;
}
message GetAccelerateCardUsageResp {
  repeated FuncCardCfg user_item_list = 1;
}

message BatchGetAccelerateCardUsageReq {
  repeated uint32 uid_list = 1;
}
message BatchGetAccelerateCardUsageResp {
  repeated FuncCardCfg user_item_list = 1;
}
