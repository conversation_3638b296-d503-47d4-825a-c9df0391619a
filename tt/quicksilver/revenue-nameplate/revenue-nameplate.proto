syntax = "proto3";

package revenuenameplate;
option go_package = "golang.52tt.com/protocol/services/revenuenameplate";

import "tt/quicksilver/extension/options/options.proto";

service RevenueNameplate {
  option (service.options.service_ext) = {
    service_name: "revenue-nameplate"
  };
  // 提供给管理后台的接口
  // 配置铭牌
  rpc SetNameplate (SetNameplateReq) returns (SetNameplateResp) {}
  // 修改铭牌
  rpc UpdateNameplate (UpdateNameplateReq) returns (UpdateNameplateResp) {}
  // 分页获取所有铭牌配置（支持条件查询）
  rpc GetAllNameplates (GetAllNameplatesReq) returns (GetAllNameplatesResp) {}
  // 获取指定铭牌配置
  rpc GetNamePlate (GetNamePlateReq) returns (GetNamePlateResp) {}
  // 发放名牌
  rpc AssignNamePlate (AssignNamePlateReq) returns (AssignNamePlateResp) {}
  // 获取发放记录
  rpc GetAssignRecord (GetAssignRecordReq) returns (GetAssignRecordResp) {}
  // 活动发放接口
  rpc ActivityAssignNameplate (ActivityAssignNameplateReq) returns (ActivityAssignNameplateResp) {}

  // 提供给客户端的接口
  // 获取用户铭牌配置
  rpc GetUserNameplateInfo (GetUserNameplateInfoReq) returns (GetUserNameplateInfoResp) {}
  // 批量获取用户铭牌配置
  rpc BatchGetUserNameplates (BatchGetUserNameplatesReq) returns (BatchGetUserNameplatesResp) {}
  // 设置用户铭牌配置
  rpc SetUserNameplateInfo (SetUserNameplateInfoReq) returns (SetUserNameplateInfoResp) {}
  // 获取用户所有可配置铭牌
  rpc GetUserAllNameplateList (GetUserAllNameplateListReq) returns (GetUserAllNameplateListResp) {}
  // 获取铭牌配置
  rpc GetNamePlateFromCache (GetNamePlateReq) returns (GetNamePlateResp) {}
}

// NameplateType 用户铭牌资源类型
enum NameplateType {
  TYPE_ERROR = 0; // 错误类型
  TYPE_LOTTER = 1;  // 资源类型lotter
}

// ScenesType 场景类型
enum ScenesType {
  SCENES_NORMAL_TYPE = 0; // 通用场景
  SCENES_ROOM_FOLLOW_TYPE = 1; // 房间跟随场景
}

// RevenueNameplateInfo 营收管理后台铭牌结构
message RevenueNameplateInfo {
  uint32 id = 1; // 铭牌id（设置时为0）
  string name = 2; // 铭牌名称
  string base_url = 3; // 铭牌基础资源url
  string dynamic_url = 4; // 铭牌动态资源的url
  NameplateType type = 5; // 资源类型
  bool is_follow_need = 6; // 是否出现在跟随位头像上方
  uint32 reward_time = 7; // 发放时长，单位s
  string remark = 8; // 备注
  uint32 opt_time = 9; // 操作时间
}

// NameplateDetailInfo 铭牌信息结构
message NameplateDetailInfo {
  uint32 id = 1; // 铭牌id
  string name = 2; // 铭牌名称
  string base_url = 3; // 铭牌基础资源url
  string dynamic_url = 4; // 铭牌动态资源的url
  NameplateType type = 5; // 资源类型
  uint32 start_time = 6; // 开始时间
  uint32 finish_time = 7; // 到期时间
  bool is_use = 8; // 是否穿戴
}

message SetNameplateReq {
  RevenueNameplateInfo nameplate = 1; // 需要设置的铭牌信息
}
message SetNameplateResp {
  uint32 id = 1; // 创建的铭牌ID
}

message UpdateNameplateReq {
  RevenueNameplateInfo nameplate = 1; // 需要设置的铭牌信息
}
message UpdateNameplateResp {
  uint32 id = 1; // 更新的铭牌ID
}

message GetAllNameplatesReq {
  uint32 limit = 1; // 分页大小
  uint32 page = 2; // 页数
  uint32 id = 3; // 根据id模糊匹配
  string name = 4; // 根据名称进行模糊匹配
}
message GetAllNameplatesResp {
  repeated RevenueNameplateInfo nameplates = 1; // 返回的铭牌列表
  uint32 limit = 2; // 分页大小
  uint32 page = 3; // 页数
  uint32 count = 4; // 返回列表数量
}

message GetNamePlateReq {
  repeated uint32 id_list = 1; // 需要查询的uid
}
message GetNamePlateResp {
  repeated RevenueNameplateInfo nameplates = 1; // 铭牌信息，从缓存获取时发放时长、备注、操作时间为空值
}

message AssignRecord {
  uint32 id = 1; // 发放记录ID，客户端分配，提交发放时不需要考虑
  uint32 nameplate_id = 2; // 需要发放的铭牌id
  uint32 uid = 3; // 需要发放的uid（这个和TTID只需要发一个即可）
  string tt_id = 4; // 需要发放的TTID（这个和uid只需要发一个即可）
  uint64 reward_time = 5; // 发放时长，单位s
  uint32 is_assign = 6; // 是否已经完成发放，获取信息时返回
}

message AssignNamePlateReq {
  repeated AssignRecord record = 1; // 需要发放的信息
}
message AssignNamePlateResp {
}

message GetAssignRecordReq {
  uint32 limit = 1; // 分页大小
  uint32 page = 2; // 页数
}
message GetAssignRecordResp {
  repeated AssignRecord record = 1; // 需要发放的信息
}

message ActivityAssignNameplateReq {
  string order_id = 1; // 订单ID（具有幂等的能力）
  uint32 nameplate_id = 2; // 需要发放的铭牌id
  uint32 uid = 3; // 需要发放的uid
  int64 reward_time = 5; // 发放时长，单位s，支持负值，若为负值则表示回收时间
  string push_msg_text_prefix = 6;   // TT助手推送文本前缀
  int64 create_time = 7; // 订单创建时间
}
message ActivityAssignNameplateResp {}

message GetUserNameplateInfoReq {
  uint32 uid = 1; // 需要查询的uid(如果有就按uid查，如果没有就按链接的uid查)
  ScenesType scenes = 2; // 使用场景
}
message GetUserNameplateInfoResp {
  repeated NameplateDetailInfo nameplates = 1; // 铭牌信息，仅返回已穿戴的
}

// UserNameplate 用户穿戴铭牌信息
message UserNameplate {
  uint32 uid = 1; // 用户uid
  repeated NameplateDetailInfo nameplates = 2; // 铭牌信息，仅返回已穿戴的
}

message BatchGetUserNameplatesReq {
  repeated uint32 uid_list = 1; // 需要查询的uid列表
  ScenesType scenes = 2; // 使用场景
}
message BatchGetUserNameplatesResp {
  repeated UserNameplate user_nameplates = 1; // 返回的用户信息列表
}

message SetUserNameplateInfoReq {
  uint32 uid = 1; // 需要查询的uid
  repeated uint32 id_list = 2; // 需要进行穿戴的id列表
}
message SetUserNameplateInfoResp {
}

message GetUserAllNameplateListReq {
  uint32 uid = 1; // 需要查询的uid
}
message GetUserAllNameplateListResp {
  repeated NameplateDetailInfo nameplates = 1; // 用户所有铭牌信息
}