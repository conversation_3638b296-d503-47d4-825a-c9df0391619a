syntax = "proto3";

package roomranking;

service RoomRank {

    rpc CheckMaskedPkRankEntry(CheckMaskedPkRankEntryReq) returns (CheckMaskedPkRankEntryRes) {}
    // 获取蒙面pk榜
    rpc GetMaskPkRank(GetMaskPkRankReq) returns (GetMaskPkRankRes) {}
}

// 蒙面pk榜单入口
message CheckMaskedPkRankEntryReq {
}

message CheckMaskedPkRankEntryRes {
  bool entry_enable = 1;      // 是否展示蒙面pk榜单入口
}

message MaskPkRankItem {
    uint32 rank     = 1;
    uint32 uid      = 2;
    uint32 value    = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message MaskPkRankItemMe {
    uint32 rank = 1;
    uint32 uid  = 2;
    uint32 value = 3;
    string rankDescribe = 4;    // 排名描述
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetMaskPkRankReq {
    uint32 uid = 1;
    uint32 channelId = 2;
}

message GetMaskPkRankRes {
    repeated MaskPkRankItem list = 1; // 榜单列表
	MaskPkRankItemMe me = 2;        // 榜单底部个人信息
	bool showrank = 3;      // 是否显示蒙面pk榜
}