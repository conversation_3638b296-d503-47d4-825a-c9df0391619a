syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/api-internal-http";

package api_internal_http;

enum QueryUidsType {
  UNKNOWN = 0;
  BY_ACCOUNT = 1;				// 根据帐号
  BY_ACCOUNT_NUMERIC = 2;		// 根据数字帐号
  BY_PHONE = 3; 				// 根据手机号
}

message QueryUidsResp {
  map<string, uint64> key_to_uid = 1;
}

message UserUid {
  uint64 uid = 1;
}

message UserInfo {
  uint64 id = 1; // 用户id
  string username = 2; // 用户名
  string nickname = 3; // 昵称
  int32 gender = 4; // 性别
  uint32 status = 5; // 0：正常，1：被封号
  string username_numeric = 6; // 数字帐号
  uint64 last_quit_guild_id = 7; // 最后一次退会的公会ID
  int64 last_quit_guild_time = 8; // 最后一次退会的时间
  int64 register_at = 9; // 注册时间
  int64 last_login_time = 10; //上次登录时间
  uint64 current_guild_id = 11; //当前公会ID
  string phone = 12;
}

message GetUsersByUidsResp {
  map<uint64, UserInfo> uid_to_user = 1;
}

message LoginWithAccountReq {
  string account = 1;
  string password = 2;
  int64 sdk_game_id = 3;
  string client_ip = 4;
}

message LoginResp {
  UserInfo user = 1;
  string magic = 2;
}

message LoginWithPhoneReq {
  string phone = 1;
  string password = 2;
  int64 sdk_game_id = 3;
  string client_ip = 4;
}

// 发送选项
message ImSendOption {
    // 马甲包，默认全部都收到
    // market_id -> app_name 转换关系:
    // 0->tt, 2->huanyou, 5->maike, 6->mijing
    repeated uint32 market_id_list = 1;
    // 马甲包，默认全部都收到
    // 0->tt, 2->huanyou, 5->maike, 6->mijing
    repeated string app_name_list = 2;

    // 平台类型，默认全平台都收到
    // client_type -> app_platform 转换关系:
    // 0->android, 1->ios, 5->pc
    repeated uint32 client_type_list = 3;
    // 平台类型，默认全平台都收到
    // android, ios, pc
    repeated string app_platform_list = 4;

    // 是否属于附件发送
    bool has_attachment = 5;
    // 附件有效时间 秒
    uint32 attachment_ttl = 6;

    // 私聊：不推消息给发送方
    bool ignore_from = 7;
    // 私聊：不推消息给接收方
    bool ignore_to = 8;

    // 推送给所有订阅用户
    bool send_all_followers = 9;

    // 是否1v1私聊离线推送
    bool with_offline_push = 10;
}

// 文本消息
message ImText {
    // 文本内容
    string content = 1;
    // 高亮文字
    string highlight = 2;
    // 高亮文字对应的链接
    string url = 3;
}

message SendTTAssistantTextReq {
    uint64 to_uid = 1;
    ImText text = 2;
    ImSendOption opt = 3;
}

message GetUserOnlineInfoReq {
    string uid = 1;
}

message UserOnlineInfo {
    uint64 uid = 1;
    int64 online_at = 2;
    uint32 market_id = 3;
    string client_ip = 4;
    uint32 client_type = 5;
    string device_model = 6;
    string os_ver = 7;
    string os_type = 8;
    string device_brand = 9;
    uint32 screen_height = 10;
    uint32 screen_width = 11;
    string app_version = 12;
}

message GetUserOnlineInfoResp {
    map<uint32, UserOnlineInfo> online_info_map = 1; // client_type -> UserOnlineInfo
}

