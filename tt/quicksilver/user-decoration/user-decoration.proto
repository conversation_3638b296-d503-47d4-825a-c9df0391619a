syntax = "proto3";

package user_decoration;

option go_package = "golang.52tt.com/protocol/services/user-decoration";

service UserDecoration {

  /* -------运营后台-------- */

  // Upsert 用于为用户添加或更新一个装饰品。
  rpc Upsert (UpsertReq) returns (UpsertResp);

  // Decorations 返回某类装饰品。
  rpc Decorations (DecorationsReq) returns (DecorationsResp);

  // GetDecorationById 返回某个装饰品。
  rpc GetDecorationById (GetDecorationByIdReq) returns (GetDecorationByIdResp);

  // InsertDecoration 插入装饰品。
  rpc InsertDecoration (InsertDecorationReq) returns (InsertDecorationResp);

  // DelDecoration 删除装饰品。
  rpc DelDecoration (DelDecorationReq) returns (DelDecorationResp);

  // BatchUpsertDecoration 批量为用户添加或更新一个装饰品。
  rpc BatchUpsertDecoration(BatchUpsertDecorationReq) returns (BatchUpsertDecorationResp);

  /* ---------APP--------- */

  // UserDecorations 用于返回用户所拥有某类装饰品的全部信息。
  rpc UserDecorations (UserDecorationsReq) returns (UserDecorationsResp);

  // Current 用于获取用户当前佩戴的装饰品。
  rpc Current (CurrentReq) returns (CurrentResp);

  // Adorn 用于用户佩戴装饰品
  rpc Adorn (AdornReq) returns (AdornResp);

  // Remove 用于用户卸下装饰品
  rpc Remove (RemoveReq) returns (RemoveResp);

  // PushAwardNotifyForTest
  rpc PushAwardNotifyForTest (PushAwardNotifyForTestReq) returns (PushAwardNotifyForTestResp);


};

// 用户装饰品的类型
enum Type {
  INVALID = 0; // 无效类型
  FLOAT = 1; // 主页飘
  WALL = 2; // 礼物墙
  CHANNEL_INFO_CARD = 3; //房间资料卡//
}

// 装饰品主要信息
message Decoration {
  Type typ = 1; // typ 代表装饰品的类型
  string id = 2; // id 代表（特定类型下的）装饰品ID
  string version = 3; // version 代表装饰品的版本
  string fusion_ttid = 4; // 融合特效对方的ttid
  uint32 identity_id = 5; //区别旧ID，数据库中唯一主键
  LiveAwardInfoCardExtend live_award_info_card_extend = 6; // 直播奖励信息卡扩展(跟用户不跟配置，所以放这里了)
}

// 装饰品细节信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DecDetail {
  string name = 1; // name 代表装饰品的名字
  string url = 2; // url 代表装饰品（z包）对应的链接(半屏)
  string md5 = 3; // md5 代表装饰品（z包）的MD5
  string url_pic = 4; // url_pic 代表装饰品（图片）对应的链接
  WallExtend wall_extend = 5; // 礼物墙光效额外信息，序列化存在mysql的wall_extend中
  bool is_show = 6; // 运营后台配置，是否展示在光效架 ，必须放在外面以便过滤
  uint32 rank = 7; // 运营后台配置，展示顺序【数字越小越优先】
  int64 begin = 8; // 运营后台配置，在光效架展示的开始时间
  int64 end = 9; // 运营后台配置，在光效架展示的结束时间
  StatusType StatusType = 10; //【不用配】用于向logic返回特效的一个特定状态
  string update_user = 11; // 运营后台配置，最后一次修改的操作人
  int64 update_time = 12; //【不用配】用于向运营后台返回最后的修改时间
  string resource_url = 13; // 运营后台配置，整个资源包的地址（全屏）
  DecorationCustomType type = 15; // 主页飘的特效种类
  uint32 default_award_time = 16; // 默认发放时间，单位秒
  DecorationSourceType source_type = 17; // 装扮资源类型
  string fusion_color = 18; // 融合的昵称颜色
  string notes = 19; // 备注
  string resource_md5 = 20; // resource_url 字段对应资源md5//
  ChannelInfoCardType channel_info_card_type = 21; // 房间资料卡类型
  LiveAwardInfoCardConfig live_award_info_card_config = 22; // 直播奖励信息卡配置
}

enum ChannelInfoCardType {
  CHANNEL_INFO_CARD_TYPE_UNKNOWN = 0;
  CHANNEL_INFO_CARD_TYPE_NORMAL = 1; // 普通
  CHANNEL_INFO_CARD_TYPE_LIVE_AWARD = 2; // 直播奖励
}

message LiveAwardInfoCardConfig{
  string frame_img_url = 1;  // 边框
  string head_wear_img_url = 2;  // 麦位边框
  string top_animation_url = 3;  // 顶部动画
  string top_animation_md5 = 4;
  string live_channel_preview_img = 5;  // 主播资料卡预览图
  string normal_channel_preview_img = 6;  // 普通资料卡预览图
  string pc_live_channel_preview_img = 7; // pc主播资料卡预览图
  string pc_normal_channel_preview_img = 8; // pc普通资料卡预览图
  string pc_frame_img_url = 9; // pc边框
  string notify_preview_img = 10; // 通知弹窗预览图
  enum AwardType{
    AWARD_TYPE_UNKNOWN = 0;
    AWARD_TYPE_RICH = 1;    //土豪，只展示个人资料卡预览图
    AWARD_TYPE_ANCHOR = 2;    //主播，展示主播资料卡预览图
  }
  AwardType award_type = 11; // 奖励类型
}

message LiveAwardInfoCardExtend{
  string follow_icon_color = 1; // 关注图标颜色
  string award_text = 2;  // 奖励文案，用于头像框和边框填充
  string frame_award_text_background = 3;  // 边框处奖励文案的底图
  string award_text_color = 4;  // 奖励文案的颜色
  string top_animation_fusion_img = 5; // 顶部融合动画融合图片
  string frame_award_text_background_god = 6; // 如果是神王，边框处奖励文案的底图
}

enum DecorationSourceType {
  DecUnknown = 0;
  DecLottie = 1;
  DecVap = 2;
}

enum DecorationCustomType {
  DecNone = 0;
  DecFusion = 1; // 融合
}

enum StatusType {
  NORMAL = 0; // 一般状态
  MAX = 1; // 最高等级
  NONE = 2; // 未拥有
}

message WallExtend{
  string backend_url = 1; //礼物墙光效背景图url
  string frame_url = 2; //礼物墙光效中边框动效对应的zip包url
  string frame_md5 = 3; // frame_url对应的md5
  Color color = 4; // 【不用配】光效配套颜色相关配置
  string panel_url = 5; // 光效上拉面板背景图url
  string button_url = 6; // 装饰品在个人装扮页面的活动跳转链接
  string cms_url = 7; // 装饰品在个人装扮页面右上角跳转链接
  SourceType source_type = 8; // 礼物墙光效边框的类型，视频/图片
  uint32 appear_time = 9; // 礼物架的出现时间
  string preview_pic = 10; // 礼物墙光效动画在个人装扮页面的预览静态图
  string preview_frame = 11; // 礼物墙光效边框在个人装扮页面的预览静态图
  uint32 disappear_time = 12; //礼物墙光效的消失时间.
  uint32 cycle_time = 13; // 礼物墙光效动画开始循环的时间，单位毫秒
  string icon_mark = 14; // 个人装扮栏 礼物墙光效右下角标注图的url
}

enum SourceType {
  PIC = 0; // 图片
  VAP = 1; // vap视频,zip包
  LOTTIE = 2; // Lottie动画,zip
}

message Color{
  string panel_array = 1; //上拉面板箭头颜色
  string panel_text = 2; //上拉面板文字颜色
  string bar = 3; // bar渐变层颜色
  string backend = 4; // 背景颜色
  repeated string name_v1 = 5; // 礼物名-第一阶段颜色
  repeated string name_v2 = 6; // 礼物名-第二阶段颜色
  repeated string name_v3 = 7; // 礼物名-第三阶段颜色
  string count_v1 = 8; // 礼物数量-第一阶段颜色
  string count_v2 = 9; // 礼物数量-第二阶段颜色
}



// 装饰品时间信息
message Tim {
  int64 begin = 1; // 装饰品开始发行时间
  int64 end = 2; // 装饰品结束发行时间
}

// 装饰品信息集合
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DecInfo {
  Decoration decoration = 1;
  DecDetail decDetail = 2;
  Tim tim = 3;
}

message UserDecorationsReq {
  uint32 uid = 1;
  Type typ = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UserDecorationsResp {
  repeated DecInfo decInfos = 1;
}

message CurrentReq {
  uint32 uid = 1;
  Type typ = 2;
}

message CurrentResp {
  Decoration dec = 1;
  Tim time = 2;
}

message AdornReq {
  uint32 uid = 1;
  Decoration dec = 2;
}

message AdornResp {}

message RemoveReq {
  uint32 uid = 1;
  Type typ = 2;
}

message RemoveResp {}

// 时间叠加类型
enum TimeOverlayType {
  TIMEINVALID = 0; // 无效类型
  TIMEALTER = 1; // 更改
  TIMEAPPEND = 2; // 增加
}

message UpsertReq {
  uint32 uid = 1;
  Decoration dec = 2;
  TimeOverlayType typ = 3;
  int64 dur = 4;
  string order_id = 5;
  string fusion_ttid = 6; // 融合特效对方的ttid
  LiveAwardInfoCardExtend card_extend = 7; // 用于主播资料卡的额外信息
}

message UpsertResp {}

/* ------------------ */

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DecorationInfo {
  Decoration decoration = 1;
  DecDetail decDetail = 2;
}

message DecorationsReq {
  Type typ = 1;
  string id = 2; //暂时只支持房间资料卡
  bool all = 3; //是否取全部（包括已经删除的）
  bool need_live_award_info_card = 4; // 是否需要直播奖励资料卡
}

message DecorationsResp {
  repeated DecorationInfo decoration_infos = 1;
}

message InsertDecorationReq {
  DecorationInfo decoration_info = 1;
}

message InsertDecorationResp {}

message DelDecorationReq {
  Decoration decoration = 1;
}

message DelDecorationResp {}

message BatchUpsertDecorationReq {
  repeated DecorationUserInfo user = 1;
  Decoration dec = 2;
  TimeOverlayType typ = 3;
  int64 dur = 4;
}

message DecorationUserInfo{
  uint32 uid = 1;  // 要发放的uid
  string fusion_ttid = 2; // 融合特效对方的ttid
}

message BatchUpsertDecorationResp {
}

message GetDecorationByIdReq{
  Type typ = 1; // typ 代表装饰品的类型
  string id = 2; // id 代表（特定类型下的）装饰品ID
  string version = 3; // 版本
}

message GetDecorationByIdResp{
  DecorationInfo dec_info = 1;
}

message PushAwardNotifyForTestReq {
  uint32 uid = 1;
  string name = 2; // 资料卡名称
  uint32 day =3; // 天数
  string preview_img = 4; // 预览图链接
  string id = 5; // 要佩戴的装扮id
}

message PushAwardNotifyForTestResp {}