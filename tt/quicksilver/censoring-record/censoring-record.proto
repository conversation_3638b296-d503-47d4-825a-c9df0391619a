syntax = "proto3";

package censoring_record;

option go_package = "golang.52tt.com/protocol/services/censoring-record";

service CensoringRecord {
    rpc LockAudit(LockAuditReq) returns (LockAuditResp) {}
    rpc UnlockAudit(UnlockAuditReq) returns (UnlockAuditResp) {}

    // 获取最新审核生效信息
    rpc GetAuditEffectInfo(GetAuditEffectInfoReq) returns (GetAuditEffectInfoResp) {}
    rpc SetAuditEffectInfo(SetAuditEffectInfoReq) returns (SetAuditEffectInfoResp) {}
}

message LockAuditReq {
    // 送审场景
    string scene_code = 1;
    // 锁标识
    string lock_key = 2;
    // 过期时间，默认3天
    int64 lock_ttl = 3;
}

message LockAuditResp {
    bool is_acquired = 1; 
}

message UnlockAuditReq {
    // 送审场景
    string scene_code = 1;
    // 锁标识
    string lock_key = 2;
}

message UnlockAuditResp {
    bool is_released = 1;
}

message AuditEffectInfo {
    // 审核场景码
    string scene_code = 1;
    // 主体id
    string record_key = 2;
    // 结果生效时间
    int64 effect_ts = 3;

    // 送审时间
    int64 submit_ts = 4;
    // 工单id
    string biz_record_id = 5;
    // 简易送审内容
    string brief_content = 6;
}

message GetAuditEffectInfoReq {
    string scene_code = 1; 
    string record_key = 2;
}

message GetAuditEffectInfoResp {
    AuditEffectInfo effect_info = 1;
}

message SetAuditEffectInfoReq {
    AuditEffectInfo effect_info = 1;
}

message SetAuditEffectInfoResp {
}

