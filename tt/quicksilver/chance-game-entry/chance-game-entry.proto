syntax = "proto3";

package chance_game_entry;
option go_package = "golang.52tt.com/protocol/services/chance-game-entry";

// 以后可能有新的娱乐玩法或者其他的需要用户与房间黑白名单的配置需求，不同的玩法，具体的黑白名单可能不同，所以这里先加一个配置备用
// 玩法类型
enum ChanceGameType {
    OnePiece = 0;               // 航海寻宝
    ChanceGameTypeAll = 1;
    StarTrek = 2;               // 星际巡航
}
enum ListType{
    UserBlack = 0;
    UserWhite = 1;
    ChannelBlack = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AddChannelBWListReq{
    repeated uint32 display_id_list = 1;
    uint32 black_or_white = 2;  // 0-黑名单,1-白名单
    uint32 Game_type = 3;       // 玩法类型，seeChanceGameType
}

message AddChannelBWListResp{
}


// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AddUserBWListReq{
    repeated uint32 uid_list = 1;
    uint32 black_or_white = 2;  // 0-黑名单,1-白名单
    uint32 Game_type = 3;       // 玩法类型，seeChanceGameType
}

message AddUserBWListResp{
}

// 删除黑/白名单
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DelFromBWListReq{
    repeated uint32 uid_list = 1;
    repeated uint32 display_id_list = 2;
    uint32 black_or_white = 3;  // 0-黑名单,1-白名单
    uint32 channel_or_user = 4; // 0-房间,1-用户
    uint32 Game_type = 5;       // 玩法类型，seeChanceGameType
}

message DelFromBWListResp{
}

message ChannelBWListInfo{
    uint32 display_id = 1;
    uint32 channel_id = 2;
    string channel_name = 3;
    string owner_ttid = 4;
    string nickname = 5;
    uint32 ctime = 6;       // 时间戳
}

message UserBWListInfo{
    uint32 uid = 1;
    string ttid = 2;
    string nickname = 3;
    uint32 ctime = 4;       // 时间戳
}

// 获取房间黑白名单list
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetChannelBWListInfoReq{
    uint32 black_or_white = 1;  // 0-黑名单,1-白名单
    uint32 Game_type = 2;       // 玩法类型，seeChanceGameType
    uint32 page = 3;            // 页码，从1开始
    uint32 count = 4;

    uint32 display_id = 5;      // 字段值不为零时，做搜索功能
}

message GetChannelBWListInfoResp{
    repeated ChannelBWListInfo cbw_info_list = 1;
    uint32 total_page = 2;      // 名单总页数
}

// 获取用户黑白名单list
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetUserBWListInfoReq{
    uint32 black_or_white = 1;  // 0-黑名单,1-白名单
    uint32 Game_type = 2;       // 玩法类型，seeChanceGameType
    uint32 page = 3;            // 页码，从1开始
    uint32 count = 4;

    uint32 uid = 5;             // 字段值不为零时，做搜索功能
}

message GetUserBWListInfoResp{
    repeated UserBWListInfo ubw_info_list = 1;
    uint32 total_page = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CheckIfAccessibleReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 gameType =3;     // see ChanceGameType
    uint32 client_ip = 4;
    uint32 market_id = 5;
}

message CheckIfAccessibleResp{
    bool can_see = 1;           // 是否可见 航海寻宝入口
    bool star_trek_entry = 2;   // 是否可见 星际巡航入口
}

service ChanceGameEntry {
    // --- 6.20.0 需求 ---
    // 玩法开关
    rpc SetChanceGameSwitch(SetChanceGameSwitchReq) returns(SetChanceGameSwitchResp){}
    rpc GetAllChanceGameSwitchState(GetAllChanceGameSwitchStateReq) returns(GetAllChanceGameSwitchStateResp){}
    // 玩法黑白名单
    rpc AddChannelBWListV2(AddChannelBWListV2Req) returns (AddChannelBWListV2Resp){}
    rpc AddUserBWListV2 (AddUserBWListV2Req) returns (AddUserBWListV2Resp){}
    rpc BatDelUserBWListV2(BatDelUserBWListV2Req) returns(BatDelUserBWListV2Resp){}
    rpc BatDelChannelBWListV2(BatDelChannelBWListV2Req) returns(BatDelChannelBWListV2Resp){}
    rpc GetChannelBWListInfoV2(GetChannelBWListInfoV2Req) returns(GetChannelBWListInfoV2Resp){}
    rpc GetUserBWListInfoV2(GetUserBWListInfoV2Req) returns(GetUserBWListInfoV2Resp){}

    // 可见人群配置
    rpc SetChanceGameAccessCond(SetChanceGameAccessCondReq) returns(SetChanceGameAccessCondResp){}
    rpc GetChanceGameAccessCond(GetChanceGameAccessCondReq) returns(GetChanceGameAccessCondResp){}
    rpc GetMagicSpiritAccessCond(GetMagicSpiritAccessCondReq) returns(GetMagicSpiritAccessCondResp){}
    rpc DelMagicSpiritAccessCond(DelMagicSpiritAccessCondReq) returns(DelMagicSpiritAccessCondResp){}
    rpc GetActivityAccessCond(GetActivityAccessCondReq) returns(GetActivityAccessCondResp){}
    rpc DelActivityAccessCond(DelActivityAccessCondReq) returns(DelActivityAccessCondResp){}

    rpc CheckGameEntryAccess(CheckGameEntryAccessReq)returns(CheckGameEntryAccessResp){}
    rpc CheckMagicSpiritAccess(CheckMagicSpiritAccessReq)returns(CheckMagicSpiritAccessResp){}
    // 从内存缓存中获取配置
    rpc GetLocalCacheConf(GetLocalCacheConfReq) returns(GetLocalCacheConfResp){}
    // 获取用户各条件数值
    rpc GetUserValue(GetUserValueReq) returns (GetUserValueResp){}

    // 提醒浮层通用接口
    rpc SetGameNotifyInfo(SetGameNotifyInfoReq) returns(SetGameNotifyInfoResp){}
    rpc GetGameNotifyInfo(GetGameNotifyInfoReq) returns(GetGameNotifyInfoResp){}

    rpc BatGetNotifyInfo(BatGetNotifyInfoReq) returns(BatGetNotifyInfoResp){}

    // 玩法接口定时开放
    rpc SetChanceGameOpenTime(SetGameSwitchOpenTimeReq) returns(SetGameSwitchOpenTimeResp){}
    rpc GetChanceGameOpenTime(GetGameSwitchOpenTimeReq) returns(GetGameSwitchOpenTimeResp){}
    rpc CheckChanceGameIsOpen(CheckChanceGameIsOpenReq) returns(CheckChanceGameIsOpenResp){}
    rpc DelGameSwitchOpenTime(DelGameSwitchOpenTimeReq) returns(DelGameSwitchOpenTimeResp){}


    // 冒险岛活动
    rpc CheckAdventureActAccess(CheckAdventureActAccessReq) returns(CheckAdventureActAccessResp){}

    // 活动权限检查接口
    rpc CheckActivityAccess(CheckActivityAccessReq) returns(CheckActivityAccessResp){}
}

// ------------------------- 6.20.0 娱乐玩法安全需求 -----------------------------------

enum NewChanceGameType {
    NewChanceGameType_Invalid = 0;
    NewChanceGameType_OnePiece = 1;               // 航海寻宝
    NewChanceGameType_StarTrek = 2;               // 星际巡航
    NewChanceGameType_MagicSpirit = 3;            // 守护精灵（幸运礼物）
    NewChanceGameType_SmashEgg = 4;               // 转转
    NewChanceGameType_GoldSmash = 5;              // 金色转转
    NewChanceGameType_CatCanteen = 6;             // 猫猫餐厅
    NewChanceGameType_GloryMagic = 7;             
    NewChanceGameType_StarTrain = 8;              // 摘星列车
    NewChanceGameType_AdventureAct = 9;           // 冒险岛活动
    NewChanceGameType_CommonActivity = 10;        // 通用活动入口
}

// 安全开关
message SetChanceGameSwitchReq {
    uint32 game_type = 1;  // 玩法类型，see NewChanceGameType
    bool on_or_off = 2;    // true-打开，false-关闭
}

message SetChanceGameSwitchResp {
}

message ChanceGameSwitchState{
    uint32 game_type = 1;  // 玩法类型，see NewChanceGameType
    bool on_or_off = 2;    // true-打开，false-关闭
}

//一次拉取所有玩法入口状态
message GetAllChanceGameSwitchStateReq {
}

message GetAllChanceGameSwitchStateResp {
    repeated ChanceGameSwitchState switch_list = 1;  // 开关状态列表
}

// 玩法黑白名单类型
enum ChanceGameListType{
    ChanceGameListType_Invalid = 0;
    ChanceGameListType_Black = 1;  // 黑名单
    ChanceGameListType_White = 2;  // 白名单
}

message ListItemConf{
    uint32 list_id = 1;            // 处理用户名单时为uid，处理房间名单时为channel_id
    repeated uint32 game_type = 2; // see NewChanceGameType
    uint32 ctime = 3;              // 新增操作时不需要填

    repeated uint32 activity_id = 4; // 活动id列表
}

message AddChannelBWListV2Req{
    repeated ListItemConf channel_list = 1;
    uint32 list_type = 2;       // 名单类型，see ChanceGameListType
}

message AddChannelBWListV2Resp{
}

message AddUserBWListV2Req{
    repeated ListItemConf user_list = 1;
    uint32 list_type = 2;          // 名单类型，see ChanceGameListType
}

message AddUserBWListV2Resp{
}

// 删除用户黑/白名单
message BatDelUserBWListV2Req{
    repeated uint32 uid_list = 1;
    uint32 list_type = 2;    //see ChanceGameListType
}

message BatDelUserBWListV2Resp{
}

// 删除房间黑名单
message BatDelChannelBWListV2Req{
    repeated uint32 channel_id_list = 1;
}

message BatDelChannelBWListV2Resp{
}

// 获取房间黑白名单list
message GetChannelBWListInfoV2Req{
    uint32 list_type = 1;  // 名单类型，see ChanceGameListType
    uint32 page = 3;            // 页码，从1开始
    uint32 count = 4;

    uint32 channel_id = 5;      // 字段值不为零时，做搜索功能
}

message GetChannelBWListInfoV2Resp{
    repeated ListItemConf channel_list = 1;
    uint32 total_cnt = 2;      // 记录总条数
}

// 获取用户黑白名单list
message GetUserBWListInfoV2Req{
    uint32 list_type = 1;       //see ChanceGameListType
    uint32 page = 3;            // 页码，从1开始
    uint32 count = 4;

    uint32 uid = 5;             // 字段值不为零时，做搜索功能
}

message GetUserBWListInfoV2Resp{
    repeated ListItemConf ubw_info_list = 1;
    uint32 total_cnt = 2;
}

// ------------------ 可见人群、豁免条件 配置 ----------
// 可见人群配置
enum ConditionType{
    ConditionType_Invalid = 0;
    ConditionType_Nobility = 1;               //贵族值
    ConditionType_Wealth = 2;                 //财富值
    ConditionType_Charm = 3;                  //魅力值
    ConditionType_PlatformLv = 4;             //平台等级
    ConditionType_RechargeThirty = 5;         //近30天累充金额
    ConditionType_RechargeEighty = 6;         //近90天累充金额
    ConditionType_RechargeHundrednEighty = 7; //近180天累充金额
}

// 豁免条件
enum ExemptCondType{
    ExemptCondType_Invalid = 0;
    ExemptCondType_SmashThirty = 1;       // 近30天内投入过魔力球
    ExemptCondType_SmashChanceRemain = 2; // 转转魔力球数量
    ExemptCondType_SmashLuckyValue = 3;   // 转转幸运值
    ExemptCondType_OnePieceThirty = 4;    // 近30天内投入过汽油
    ExemptCondType_OnePieceMileage = 5;   // 航海里程值
    ExemptCondType_StarTrekThirty = 6;    // 近30天内在星际巡航投入礼物
    ExemptCondType_MagicSpiritThirty =7;  // 近30天送出过幸运礼物
    ExemptCondType_CatCanteenThirty = 8;  // 猫猫餐厅近30消费过小鱼干
}

// 关联关系
enum RelateType{
    RelateType_Invalid = 0;
    RelateType_And = 1;     //且
    RelateType_Or = 2;      //或
}

//可见人群配置/豁免条件配置
enum CondType{
    CondType_AccessCond = 0; 
    CondType_ExemptCond = 1; // 豁免条件
}

// 二级条件配置
message SubAccessCondition{
    uint32 condition_type = 1; // 可见人群条件时，参考ConditionType;配置豁免条件时，参考ExemptCondType
    uint32 threshold = 2;      // 门槛 (>=)
}

// 一级条件配置
message AccessCondition{
    repeated SubAccessCondition sub_list = 1; //二级条件列表
    uint32 relate_type = 2;                   //二级条件关联关系
}

// 设置可见人群配置
message SetChanceGameAccessCondReq{
    uint32 game_type = 1;                        //see NewChanceGameType
    uint32 cond_type = 2;                        //see CondType
    repeated AccessCondition condition_list = 3; //一级条件列表
    uint32 relate_type = 4;                      //一级条件关联关系
    
    // 6.36.0 幸运礼物可见配置区分礼物id
    uint32 magic_id = 5;

    // 新增通用活动入口id
    uint32 activity_id = 6; // 活动id，当game_type=10时必填
}

message SetChanceGameAccessCondResp{
}

// 获取可见人群配置
message GetChanceGameAccessCondReq{
    uint32 game_type = 1;  //see NewChanceGameType
    uint32 cond_type = 2;  //see CondType

    // 活动id
    uint32 activity_id = 3; // 活动id，当game_type=10时必填
}


message GetChanceGameAccessCondResp{
    repeated AccessCondition condition_list = 1; //一级条件列表
    uint32 relate_type = 2;                      //一级条件关联关系
}

// 获取幸运礼物可见人群配置
message GetMagicSpiritAccessCondReq{
    uint32 game_type = 1;  //see NewChanceGameType
    uint32 cond_type = 2;  //see CondType
}

message MagicSpiritAccessCond{
    uint32 magic_id = 1;
    repeated AccessCondition condition_list = 2; //一级条件列表
    uint32 relate_type = 3;                      //一级条件关联关系
}

message GetMagicSpiritAccessCondResp{
    repeated MagicSpiritAccessCond conf_list = 1;
}

message DelMagicSpiritAccessCondReq{
    uint32 magic_id = 1;
    uint32 cond_type = 2; //see CondType
}

message DelMagicSpiritAccessCondResp{
}

message ActivityAccessCond{
    uint32 activity_id = 1;
    repeated AccessCondition condition_list = 2; //一级条件列表
    uint32 relate_type = 3;                      //一级条件关联关系
}

message GetActivityAccessCondReq{
    uint32 cond_type = 1;   // see CondType
}

message GetActivityAccessCondResp{
    repeated ActivityAccessCond condition_list = 1; // 一级条件列表
}

message DelActivityAccessCondReq{
    uint32 activity_id = 1; // 活动id，当game_type=10时必填
    uint32 cond_type = 2;   // see CondType
}

message DelActivityAccessCondResp{}


// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CheckGameEntryAccessReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    repeated uint32 gameType =3;     // see NewChanceGameType
    uint32 client_ip = 4;
    uint32 market_id = 5;
}

message GameEntryAccess{
    bool access = 1;           // 是否可见
    bool switch = 2;
    repeated AccessCondition condition_list = 3; // 豁免条件列表
    uint32 relate_type = 4;                      // 关联关系
    uint32 game_type = 5;
}

message CheckGameEntryAccessResp{
    repeated GameEntryAccess conf_list = 1;
}

message LocalCacheAccessConf{
    uint32 cond_type = 1;  //see CondType
    repeated AccessCondition condition_list = 2; //一级条件列表
    uint32 relate_type = 3;                      //一级条件关联关系
}

message GetLocalCacheConfReq{
    uint32 game_type = 1;  //see NewChanceGameType
    uint32 magic_spirit_id = 2; // 幸运礼物id
}

message GetLocalCacheConfResp{
    bool state = 1;
    repeated uint32 user_white_list = 2;
    repeated uint32 user_black_list = 3;
    repeated uint32 channel_list = 4;
    repeated LocalCacheAccessConf conf_list = 5;

    uint32 begin_time = 6;
    uint32 end_time = 7;
}

message GetUserValueReq{
    uint32 uid = 1;
}

message GetUserValueResp{
    uint32 uid = 1;
    uint64 nobility =2;
    uint32 wealth =3;
    uint32 charm =4;
    uint32 platform_lv =5;
    uint32 recharge_thirty =6;
    uint32 recharge_ninety =7;
    uint32 recharge_oez =8;  // 近180天累充
    bool auth_result =9;    // 实名认证结果
    uint64 wealth_v2 =10;
    uint64 charm_v2 =11;

    repeated uint32 white_game_list = 12; // 玩法白名单列表
    repeated uint32 black_game_list = 13; // 玩法黑名单列表
}

// ====================== 玩法浮层提醒运营后台 begin ====================================

enum NotifyGameType {
    NotifyGameType_UNSPECIFIED = 0;
    NotifyGameType_CAT_CANTEEN = 1; // 猫猫餐厅
}

// 提醒浮层通用接口
enum NotifyPlaceType {
    NotifyPlaceType_UNSPECIFIED = 0;
    NotifyPlaceType_CHANNEL = 1; //房间 浮层提醒
    NotifyPlaceType_GAME = 2;    //玩法首页浮层提醒
    NotifyPlaceType_PUBLIC= 3;   //公屏提醒
}

enum NotifyType{
    NotifyType_UNEXPECTED = 0;
    NotifyType_COMMON = 1;  //玩法通用提醒
  }
  
message NotifyItem {
    bool is_need = 1;    // 提醒开关
    string text = 2;     // 文案
    string color = 3;    // 文案字体颜色，有值时有效
    uint32 duration = 4; // 显示时长，有值时有效
    NotifyPlaceType place_type = 5; // 提醒位置
}

message NotifyInfo {
    NotifyType notify_type = 1;
    uint32 begin_time = 2;            // 提醒开始时间
    uint32 end_time = 3;              // 提醒结束时间
    repeated NotifyItem notify_list = 4; // 提醒信息列表
    bool has_red = 5;                 // 是否有红点提示
}

message SetGameNotifyInfoReq{
    NotifyGameType game_type = 1;
    NotifyInfo info = 2;
}

message SetGameNotifyInfoResp{
}

message GetGameNotifyInfoReq{
    NotifyGameType game_type = 1;
    NotifyType notify_type = 2;
}

message GetGameNotifyInfoResp{
    NotifyInfo info = 1;
}

// ============== 玩法浮层提醒运营后台 end ==============

message GameNotifyInfo{
    NotifyGameType game_type = 1;
    repeated NotifyInfo notify_list = 2;
    uint64 version_time = 3;
}

message BatGetNotifyInfoReq{
    repeated uint32 game_type_list = 1;
}

message BatGetNotifyInfoResp{
    repeated GameNotifyInfo notify_list = 1;
}

message CheckMagicSpiritAccessReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    bool check_all = 3;     // 检查所有可见配置
    uint32 magic_spirit_id = 4; // 检查指定礼物的可见配置
}

message MagicSpiritAccess{
    uint32 magic_spirit_id = 1;
    bool access = 2;           // 是否可见
    repeated AccessCondition condition_list = 3; // 豁免条件列表
    uint32 relate_type = 4;                      // 关联关系
}

message CheckMagicSpiritAccessResp{
    bool switch = 1;
    repeated MagicSpiritAccess conf_list = 2;
}

// -- 6.38.0 -- 玩法开关定时开放
message SetGameSwitchOpenTimeReq{
    uint32 game_type = 1;
    uint32 begin_time = 2;  // 开关开启时间，秒级时间戳
    uint32 end_time = 3;    // 开关关闭时间
    string op_user = 4;     // 操作者

    uint32 activity_id = 5; // 活动id
}

message SetGameSwitchOpenTimeResp{
}

message GetGameSwitchOpenTimeReq{
    uint32 game_type = 1;  // 传入10时，获取所有活动玩法开关时间
}

message ActivityGameOpenTime{
    uint32 activity_id = 2; // 活动id
    bool switch_status = 3; // 开关状态
    uint32 begin_time = 4;  // 开始时间
    uint32 end_time = 5;    // 结束时间
}

message GetGameSwitchOpenTimeResp{
    uint32 game_type = 1;     
    bool switch_status = 2;  // 玩法开启状态
    uint32 begin_time = 3;
    uint32 end_time = 4;

    repeated ActivityGameOpenTime activity_list = 5; // 活动玩法开关时间列表
}

message DelGameSwitchOpenTimeReq{
    uint32 game_type = 1;  // 玩法类型
    uint32 activity_id = 2; // 活动id
}

message DelGameSwitchOpenTimeResp{}


message CheckChanceGameIsOpenReq{
    uint32 game_type = 1;
    uint32 activity_id = 2; // 活动id
}

message CheckChanceGameIsOpenResp{
    uint32 game_type = 1;
    bool switch_status = 2;  // 玩法开启状态
}

message CheckAdventureActAccessReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 client_ip = 3;
    uint32 market_id = 4;
}

message CheckAdventureActAccessResp{
    GameEntryAccess access = 1; // 是否可见
}


message CheckActivityAccessReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 chennel_type = 3; // 房间类型
    uint32 scheme_id = 4; // 房间布局id
    repeated uint32 activity_ids = 5; // 活动id
    uint32 client_type = 6;
    uint32 client_version = 7;
    uint32 market_id = 8;

    bool check_all_activity = 9; // 是否检查所有活动的可见性
    bool get_market_info_by_ctx = 10; // 是否通过context获取马甲包等信息
}

message CheckActivityAccessResp {
    repeated uint32 have_access_ids = 1; // 可见活动id列表
}