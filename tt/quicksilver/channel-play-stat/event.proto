syntax="proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package channel_play_stat.event;

option go_package = "golang.52tt.com/protocol/services/channel-play-stat/event";

message ReasonInfo {
    uint32 cnt = 1;
    string desc = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ChannelFilterItem {
   uint32 UserId = 1;
   uint32 channel_id = 2;
   map<uint32,ReasonInfo> filter_cnt = 3;
   uint32 suc_cnt = 4;
   int64 publish_time = 5;
   uint32 tab_id = 6;
   uint32 terminal_type = 7;
   uint32 market_id = 8;
   string req_tab_info = 9;
}

message ChannelPushInfo {
    repeated ChannelFilterItem channel_filter_items = 1;
    uint32 enter_sources = 3;
    string buss_name = 4;
}

// 新用户访问首页事件
message NewUserVisitHomePageEvent {
    uint32 uid = 1;
    // 马甲包
    uint32 market_id = 2;
    // 访问首页时间(秒)
    int64 visited_at = 3;
    // 客户端版本
    uint32 client_version = 4;
    // 客户端类型
    uint32 client_type = 5;
    // 客户端设备号
    string device_id = 6;
}
