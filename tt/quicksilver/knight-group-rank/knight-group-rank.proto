syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/knightgrouprank";
package knightgrouprank;


service KnightGroupRank {
    rpc GetLoveRank (GetLoveRankReq) returns (GetLoveRankResp) {
    }
    rpc GetWeekRank (GetWeekRankReq) returns (GetWeekRankResp) {
    }
    rpc GetCampInfo( GetCampInfoReq ) returns ( GetCampInfoResp ){
    }
    rpc SetTempNblShowUp( SetTempNblShowUpReq ) returns ( SetTempNblShowUpResp ){
    }
    rpc GenChief( GenChiefReq ) returns ( GenChiefResp ){
    }
}

message MemberInfo {
    uint32 channel_id = 1;
    uint32 anchor_uid = 2;
    uint32 knight_uid = 3;
    uint32 begin_time = 4;
    uint32 expire_time = 5;
    uint32 value = 6;
    uint32 total_day = 7;    //总开通天数
}

message RankValueInfo {
    uint32 uid = 1;
    uint32 val = 2;
    uint32 days = 3;
    uint32 left_days = 4;
    bool is_chief = 5; //是否首席
    uint32 begin_ts = 6; //
    bool is_in_channel = 7; //是否在房
}

message GetLoveRankReq {
    uint32 channel_id = 1;
    uint32 anchor_uid = 2;
    uint32 knight_uid = 3;
}

message GetLoveRankResp{
    repeated RankValueInfo rank_list = 1;
    RankValueInfo user_rank_val = 2;
}

message GetWeekRankReq {
    uint32 channel_id = 1;
}

message GetWeekRankResp {
    repeated RankValueInfo rank_list = 1;
}

//骑士营地入口信息，客户端定时拉取
message GetCampInfoReq {
    uint32 channel_id = 1;
    uint32 anchor_uid = 2;
}

message GetCampInfoResp{
    uint32 in_channel_cnt = 2;
    RankValueInfo top_user = 3;
}

message SetTempNblShowUpReq{
    uint32 uid = 1;
    uint32 creater_uid = 2;
    uint32 channel_id = 3;
}

message SetTempNblShowUpResp{
}

//for test
message GenChiefReq {
    uint32 anchor_uid = 1;
    uint32 channel_id = 2;
    uint32 chief_uid  = 3;
}

message GenChiefResp{
}