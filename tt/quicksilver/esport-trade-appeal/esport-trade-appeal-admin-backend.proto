syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/esport-trade-appeal";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package esport_trade_appeal_admin_backend;

import "tt/quicksilver/esport-trade-appeal/esport-trade-appeal.proto";

// 定义处理状态的枚举类型
enum ProcessingStatus {
    PROCESSING_STATUS_UNSPECIFIED = 0;
    PROCESSING_STATUS_WAITING = 1; // 等待处理
    PROCESSING_STATUS_PENDING = 2; // 待处理
    PROCESSING_STATUS_PROCESSED = 3; // 已处理
}

// 定义审查状态的枚举类型
enum AuditStatus {
    AUDIT_STATUS_UNSPECIFIED = 0;
    AUDIT_STATUS_PENDING = 1; // 待审查
    AUDIT_STATUS_APPROVED = 2; // 已通过
    AUDIT_STATUS_REJECTED = 3; // 已拒绝
}

service AppealAdminBackendService {
    // 获取申诉列表
    rpc GetAppealList (GetAppealListRequest) returns (GetAppealListResponse) {}
    // 获取申诉详情
    rpc GetAppealDetail (GetAppealDetailRequest) returns (GetAppealDetailResponse) {}
    // 审查申诉
    rpc AuditAppeal (AuditAppealRequest) returns (AuditAppealResponse) {}
}

message GetAppealListRequest {
    int64 start_time = 1; // 开始时间
    int64 end_time = 2; // 结束时间
    uint32 user_id = 3; // 用户ID
    ProcessingStatus status = 4; // 处理状态
    int32 page_number = 5; // 页数
    int32 page_size = 6; // 每页数量
    AuditStatus audit_status = 7; // 审查状态
    string order_number = 8;  // 订单编号
    uint32 coach_id = 9; // 教练ID
}


message AppealInfo {
    uint32 user_id = 1; // 用户ID
    uint32 esport_coach_id = 2; // 电竞教练ID
    int64 appeal_time = 3; // 申诉时间
    string appeal_description = 4; // 申诉说明
    ProcessingStatus processing_status = 5; // 处理状态
    AuditStatus audit_status = 6; // 审查状态
    string order_number = 7;  // 订单编号
    string audit_notes = 8; // 审核备注
    int64 audit_time = 9; // 审核时间
    string appeal_id = 10; // 申诉ID
}

message GetAppealListResponse {
    repeated AppealInfo appeal_list = 1; // 申诉列表
    int32 total_items = 2; // 总条目数
}

message GetAppealDetailRequest {
    string appeal_id = 1; // 申诉ID
}


message AppealDetail {
    string appeal_reason = 1; // 申诉原因
    uint32 user_id = 2; // 用户ID
    string appeal_description = 3; // 申诉说明
    repeated string appeal_images = 4; // 申诉凭证图片列表
    uint32 esport_coach_id = 5; // 电竞教练ID
    string esport_coach_description = 6; // 电竞教练说明
    repeated string esport_coach_images = 7; // 电竞教练凭证图片列表
    string order_number = 8;  // 订单编号
    double refund_amount = 9; // 用户退款金额
    string refund_reason = 10; // 用户退款原因
    string refund_detail = 11; // 用户退款详情说明
    int32 refund_quantity = 12; // 用户退款的数量
    esport_trade_appeal.RefundType refund_type = 13; // 用户退款的类型
    string rejection_reason = 14; // 电竞教练拒绝退款原因
    string rejection_detail = 15; // 电竞教练拒绝退款详情说明
    ProcessingStatus processing_status = 16; // 处理状态
    AuditStatus audit_status = 17; // 审查状态
    string appeal_id = 18; // 申诉ID
    int64 order_create_time = 19; // 下单时间
    string audit_notes = 20; // 审核备注
    int64 audit_time = 21; // 审核时间
}

message GetAppealDetailResponse {
    AppealDetail appeal_detail = 1; // 申诉详情
}

message AuditAppealRequest {
    string appeal_id = 1; // 申诉ID
    bool approve = 2; // 如果为true，申诉被批准。如果为false，申诉被拒绝。
    string notes = 3; // 审核备注
}

message AuditAppealResponse {
    bool success = 1; // 如果为true，审查操作成功。
}