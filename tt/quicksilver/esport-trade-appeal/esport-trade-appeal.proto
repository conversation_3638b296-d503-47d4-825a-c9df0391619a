syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/esport-trade-appeal";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package esport_trade_appeal;

import "tt/quicksilver/extension/options/options.proto";

//退款服务
service RefundService {
    option (service.options.service_ext) = {
        service_name: "esport-trade-appeal"
    };

    //创建退款请求
    rpc CreateRefund (CreateRefundRequest) returns (CreateRefundResponse) {}

    //接受退款请求
    rpc AcceptRefund (AcceptRefundRequest) returns (AcceptRefundResponse) {}

    //拒绝退款请求
    rpc RejectRefund (RejectRefundRequest) returns (RejectRefundResponse) {}

    //发起申诉
    rpc InitiateAppeal (InitiateAppealRequest) returns (InitiateAppealResponse) {}

    //电竞指导者提交申诉信息
    rpc SubmitGuideAppealInfo (SubmitGuideAppealInfoRequest) returns (SubmitGuideAppealInfoResponse) {}

    // 获取订单的退款信息视图
    rpc GetOrderRefundView (GetOrderRefundViewRequest) returns (GetOrderRefundViewResponse) {}

    // 批量获取订单退款状态
    rpc BatchGetOrderRefundStatus (BatchGetOrderRefundStatusRequest) returns (BatchGetOrderRefundStatusResponse) {}

    //创建急速退款请求
    rpc CreateFastRefund (CreateRefundRequest) returns (CreateRefundResponse) {}

    // 检查是否有急速退款权限
    rpc CheckFastRefundPermission (CheckFastRefundPermissionRequest) returns (CheckFastRefundPermissionResponse) {}

}

//创建退款请求
message CreateRefundRequest {
    string order_id = 1; //订单ID
    uint32 quantity = 2; //退款数量
    string reason = 3; //退款原因
    string instructions = 4; //退款说明
    RefundType type = 5; //退款类型
}

//创建退款请求响应
message CreateRefundResponse {
}

//接受退款请求
message AcceptRefundRequest {
    string refund_request_id = 1; //退款请求ID
}

//接受退款请求响应
message AcceptRefundResponse {
}

//拒绝退款请求
message RejectRefundRequest {
    string refund_request_id = 1; //退款请求ID
    string reason = 2; //拒绝原因
    string instructions = 3; //拒绝说明
}

//拒绝退款请求响应
message RejectRefundResponse {
}

//发起申诉请求
message InitiateAppealRequest {
    string refund_id = 1; //订单ID
    string reason = 2; //申诉原因
    repeated string proof_images = 3; //申诉证明图片
}

//发起申诉请求响应
message InitiateAppealResponse {
}

//电竞指导者提交申诉信息
message SubmitGuideAppealInfoRequest {
    string appeal_id = 1; //申诉ID
    string coach_appeal_description = 2; //电竞指导者申诉描述
    repeated string coach_appeal_images = 3; //电竞指导者申诉图片
}

//电竞指导者提交申诉信息响应
message SubmitGuideAppealInfoResponse {
}

//退款类型
enum RefundType {
    REFUND_TYPE_FULL = 0; //全额退款
    REFUND_TYPE_PARTIAL = 1; //部分退款
}

// 退款途径
enum RefundWay{
    REFUND_WAY_NORMAL_UNSPECIFIED = 0;// 正常
    REFUND_WAY_FAST = 1;//急速
}

// 订单售后状态
enum RefundStatus {
    REFUND_STATUS_UNSPECIFIED = 0;
    REFUND_STATUS_REFUNDING = 1;         // 退款中
    REFUND_STATUS_REFUND_ACCEPT = 2;     // 电竞指导接受退款
    REFUND_STATUS_REFUND_REJECT = 3;     // 电竞指导拒绝退款
    REFUND_STATUS_APPEALING = 4;         // 申诉中
    REFUND_STATUS_APPEALING_ACCEPT = 5;  // 申诉成功
    REFUND_STATUS_APPEALING_REJECT = 6;  // 申诉失败
}

// 退款信息
message OrderRefundView {
    string refund_id = 1;      // 退款id
    uint32 status = 2;         // 状态 包含退款中申诉中等等 RefundStatus
    RefundType refund_type = 3;    // 退款类型
    string refund_reason = 4;         // 退款原因
    uint32 refund_price = 5;   // 退款金额
    uint32 refund_count = 6;   // 退款数量
    string refund_desc = 7;           // 退款说明
    int64 end_time = 8;        // 该状态的截止时间
    string refund_reject_reason = 10;// 退款拒绝原因
    string refund_reject_desc = 11;// 退款拒绝说明
    string appeal_desc = 9;// 申诉说明
    string appeal_reason = 12;// 申诉原因
    bool can_coach_upload_appeal = 13;// 能否上传申诉凭证
    string appeal_id = 14;// 申诉id
    int64 coach_upload_appeal_deadline = 15;// 电竞指导上传申诉凭证截止时间
    bool can_appeal = 16;// 能否发起申诉
    RefundWay refund_way = 17; // 退款途径
}

message GetOrderRefundViewRequest {
    string order_id = 1; //订单ID
}

message GetOrderRefundViewResponse {
    OrderRefundView order_refund_view = 1; //订单退款信息视图
}

message BatchGetOrderRefundStatusRequest {
    repeated string order_ids = 1; //订单ID
}

message BatchGetOrderRefundStatusResponse {
    message OrderRefundStatus {
        string order_id = 1; //订单ID
        uint32 status = 2; //退款状态
        int64 end_time = 3; //该状态的截止时间
        bool can_coach_upload_appeal = 4; //能否上传申诉凭证
        string refund_id = 5; //退款ID
        string appeal_id = 6; //申诉ID
        RefundType refund_type = 7; //退款类型
        int64 coach_upload_appeal_deadline = 8;// 电竞指导上传申诉凭证截止时间
        bool can_appeal = 9;// 能否发起申诉
        int64 status_update_time = 10;// 状态更新时间
    }
    repeated OrderRefundStatus order_refund_status = 1; //订单退款状态
}

message CheckFastRefundPermissionRequest {
    uint32  uid = 1; //用户ID
    string  order_id = 2; //订单ID
}

message CheckFastRefundPermissionResponse {
    bool has = 1;// 是否有急速退款权限
}
