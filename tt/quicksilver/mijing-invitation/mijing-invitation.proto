syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/mijing-invitation";

package mijing_invitation;

service MijingInvitation {

  // 获取或者生成邀请码
  rpc GetGenInvitation (GetGenInvitationReq) returns (GetGenInvitationResp){};
  // 绑定归因
  rpc AcceptAscribeInvitation (AcceptAscribeInvitationReq) returns (AcceptAscribeInvitationResp){};
  // 拒绝归因
  rpc RejectAscribeInvitation (RejectAscribeInvitationReq) returns (RejectAscribeInvitationResp){};
  // 绑定邀请
  rpc AcceptInvitation (AcceptInvitationReq) returns (AcceptInvitationResp){};
  // 获取邀请码相关的用户数据
  rpc GetInviterByCode (GetInviterByCodeReq) returns (GetInviterByCodeResp){};
  // Debug 邀请绑定
  rpc DebugInviteSub (DebugInviteSubReq) returns (DebugInviteSubResp){};
  // 参加累计登陆解锁章节活动
  rpc JoinInUnLockChapterActivity (JoinInUnLockChapterActivityReq) returns (JoinInUnlockChapterActivityResp){};
  // 获取用户活动信息
  rpc GetUserActivityInfo (GetUserActivityInfoReq) returns (GetUserActivityInfoResp){};
  // 解锁章节
  rpc UnLockChapter (UnLockChapterReq) returns (UnLockChapterResp){};
  // debug参加活动
  rpc DebugJoinInUnlockChapter (DebugJoinInUnlockChapterReq) returns (DebugJoinInUnlockChapterResp){};
  // debug增加任务券
  rpc DebugAddMissionCoupon (DebugAddMissionCouponReq) returns (DebugAddMissionCouponResp){};
  // debug修改用户信息
  rpc DebugEditUserActivityInfo (DebugEditUserActivityInfoReq) returns (DebugEditUserActivityInfoResp){}
}

message GetGenInvitationReq {
  string activity_id = 1;
  uint32 uid = 2;
}

message GetGenInvitationResp {
  InvitationConfig config = 1;
  InvitationDetail detail = 2; // 详情
  Award award = 3; // 奖励信息
}


message AcceptAscribeInvitationReq {
  string activity_id = 1;
  uint32 uid = 2; // 这个用户要确认归因产生的绑定
  string device_id = 3; // 绑定时的设备ID
}

message AcceptAscribeInvitationResp {
  uint32 inviter_uid = 1;
  uint32 invitee_uid = 2;
  bool bind_result = 3;
  string reason = 4;
}

message RejectAscribeInvitationReq {
  string activity_id = 1;
  uint32 uid = 2; // 这个用户要拒绝归因产生的绑定
}

message RejectAscribeInvitationResp {
}

message AcceptInvitationReq {
  string activity_id = 1;
  string invitation_code = 2;
  uint32 bind_uid = 3; // 要绑定这个邀请码的uid
  string device_id = 4; // 绑定时的设备ID
}

message AcceptInvitationResp {
  uint32 inviter_uid = 1;
  uint32 invitee_uid = 2;
  bool bind_result = 3;
  string reason = 4;
}


message GetInviterByCodeReq {
  string activity_id = 1;
  string invitation_code = 2;
}

message GetInviterByCodeResp {
  Inviter inviter = 1;
}


// 相关通用配置
message InvitationConfig {
  string activity_id = 1;
  uint64 invite_time_limit = 2;
  string invite_time_limit_desc = 3;
}

// 明细数据
message InvitationDetail {
  Inviter inviter = 1;
  repeated Invitee invitees = 2;
}

// 邀请者
message Inviter {
  uint32 uid = 1;
  uint32 my_inviter_uid = 2; // 谁邀请的我
  int64 accept_at = 3; // 接受邀请的时间
  string invitation_code = 4;
  uint32 counter = 5;
  bool can_be_invitee = 6; // 是否允许成为被邀请者
  uint32 ascribe_inviter_uid = 7; // 归因的uid，有my inviter 不反回
}

// 被邀请者
message Invitee {
  uint32 uid = 1;
  int64 accept_at = 2; // 接受邀请的时间
}

// 奖励
message Award {
  repeated AwardItem list = 1; // 详细列表
  uint32 sum = 2; // 总数
  uint32 hold = 3; // 持有
}

message AwardItem {
  string image_uri = 1; // 图
  string title  = 2; // 标题
  AwardStatus status = 3; // 状态
  string status_msg = 4; // 状态的信息 未获得 已获得 免费
}

//筛选表头
enum AwardStatus {
  AWARD_STATUS_UNDEFINED = 0; // 未获得
  AWARD_STATUS_FREE = 1; // 免费
  AWARD_STATUS_OBTAINED = 2; // 已获得
}

message DebugInviteSubReq {
  string activity_id = 1;
  uint32 uid = 2;
  uint32 inviter_uid = 3;
  string device_id = 4;
  string app_id = 5; // mijing
  string device_type  = 6; // pull_reg_new_device recall_new_device_old_account recall_old_device_new_account recall_old_device_old_account active
  string time = 7;
}

message DebugInviteSubResp {}

// =================累计登陆解锁活动================================
// 参加活动
message JoinInUnLockChapterActivityReq {
  uint32 uid = 1;
  string activity_id = 2;
  uint32 scenario_id = 3;
  uint32 playmate_uid = 4; // 对局玩伴uid
  string inning_id = 5; // 对局ID
}

message JoinInUnlockChapterActivityResp {
  repeated UserUnLockActivityInfo user_info = 1;
  bool is_pop = 2; // 是否需要弹窗（当传入用户在人群包里）
}

// 用户活动信息
message UserUnLockActivityInfo {
  uint32 uid = 1;
  int64 start_time = 2; // 活动开始时间
  int64 end_time = 3; // 活动结束时间
  int64 next_award_time = 4; // 倒计时解锁时间
}

// 活动页信息
message GetUserActivityInfoReq {
  uint32 uid = 1;
  string activity_id = 2;
  uint32 scenario_id = 3;
}

message GetUserActivityInfoResp {
  string activity_id = 1; // 活动ID
  uint32 scenario_id = 2; // 剧本ID
  int64 start_time = 3;// 用户参与活动时间
  int64 end_time = 4;// 活动结束时间
  int64 now_time = 5;// 当前时间
  repeated Plot plot_data = 6;// 章节信息
  bool pop_up_mission_bubble = 7;// 每日通关解锁气泡是否弹出（没有可解锁且未使用通关结果机会弹出）
}

// 章节解锁信息
message Plot {
  uint32 plot_chapter = 1;
  string plot_id = 2;
  string plot_name = 3;
  int64 award_time = 4;// 解锁奖励领取时间
  ActivityStatus lock_status = 5;
}

enum ActivityStatus {
  ACTIVITY_STATUS_UNDEFINED = 0; // NONE
  ACTIVITY_STATUS_UNLOCKED = 1; // 已解锁
  ACTIVITY_STATUS_UNLOCK = 2; // 可解锁
  ACTIVITY_STATUS_LOCK = 3; // 不可解锁
}

// 解锁章节
message UnLockChapterReq {
  string plot_id = 1; // (plot_id)
  uint32 uid = 2;
  string activity_id = 3;
  uint32 scenario_id = 4;
}

message UnLockChapterResp {}

// debug接口
message DebugJoinInUnlockChapterReq {
  uint32 uid = 1;
  string activity_id = 2;
  uint32 scenario_id = 3;
}

message DebugJoinInUnlockChapterResp {
  UserUnLockActivityInfo user_info = 1;
}

message DebugAddMissionCouponReq {
  uint32 uid = 1;
  string activity_id = 2;
  uint32 scenario_id = 3;
  int64 start_time = 4; // 有效开始时间
  int64 end_time = 5; // 有效结束时间
}

message DebugAddMissionCouponResp {}

message DebugEditUserActivityInfoReq {
  uint32 uid = 1;
  string activity_id = 2;
  uint32 scenario_id = 3;
  int64 next_award_time = 4;
  int64 end_time = 5; // 有效结束时间
}

message DebugEditUserActivityInfoResp{}