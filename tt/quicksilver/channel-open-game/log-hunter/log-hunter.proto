syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-open-game/log-hunter";

package log_hunter;

service LogHunter {

    //基础接口
    rpc Collect (CollectReq) returns (CollectResp) {

    }

    //扩展接口
    rpc SetConfig(SetConfigReq) returns (SetConfigResp) {

    }

    rpc GetConfig(GetConfigReq) returns (GetConfigResp) {

    }
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message Log {
    int32 Level = 1; // 级别
	uint64 Timestamp = 2; // 时间戳
	string Caller = 3; // 调用代码
	string Msg = 4; //内容
}

message CollectReq {
    string service = 1; // 服务名
    repeated Log logs = 2; // 日志
}

message CollectResp {
}

message SetConfigReq {
    string service = 1; // 服务名
	bool enable = 2;    // 启用 / 禁用
    int32 level = 3;    // 日志级别， -1：debug, 0: info, 1: warning, 2: error
	string pattern = 4; // 日志内容匹配
}
message SetConfigResp {
}

message GetConfigReq {
    string service = 1; // 服务名
}
message GetConfigResp {
	bool enable = 1;    // 启用 / 禁用
    int32 level = 2;    // 日志级别， -1：debug, 0: info, 1: warning, 2: error
	string pattern = 3; // 日志内容匹配
}