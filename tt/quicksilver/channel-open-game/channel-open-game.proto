syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-open-game";

package channel_open_game;

service ChannelOpenGame {

    //基础接口
    rpc GetSupportGameList (GetSupportGameListReq) returns (GetSupportGameListResp) {

    }

    rpc GetGameMaintain (GetGameMaintainReq) returns (GetGameMaintainResp) {

    }

    rpc BatchGetGameInfo (BatchGetGameInfoReq) returns (BatchGetGameInfoResp) {

    }

    rpc SetChannelGame (SetChannelGameReq) returns (SetChannelGameResp) {

    }

    rpc UpdateChannelGame (UpdateChannelGameReq) returns (UpdateChannelGameResp) {

    }

    rpc SetChannelGameSync (SetChannelGameSyncReq) returns (SetChannelGameSyncResp) {

    }

    rpc SetChannelGameAsync (SetChannelGameAsyncReq) returns (SetChannelGameAsyncResp) {

    }

    rpc SetChannelGameMaster (SetChannelGameMasterReq) returns (SetChannelGameMasterResp) {

    }

    rpc SetChannelGameInfo (SetChannelGameInfoReq) returns (SetChannelGameInfoResp) {

    }

    rpc GetChannelGameInfo (GetChannelGameInfoReq) returns (GetChannelGameInfoResp) {

    }

    rpc GetGameModeInfo (GetGameModeInfoReq) returns (GetGameModeInfoResp) {

    }

    //扩展接口
    rpc SetChannelGameStatus (SetChannelGameStatusReq) returns (SetChannelGameStatusResp) {

    }

    rpc SetChannelGameMember (SetChannelGameMemberReq) returns (SetChannelGameMemberResp) {

    }

    rpc SetChannelGamePlayerOpenId (SetChannelGamePlayerOpenIdReq) returns (SetChannelGamePlayerOpenIdResp) {

    }

    rpc GetChannelGameBaseInfo (GetChannelGameBaseInfoReq) returns (GetChannelGameBaseInfoResp) {

    }
}

message ChannelOpenGamePlayer {
    uint32 uid = 1;
    uint32 status = 2; //0：等待确认，1：已确认

    string username = 3;
    string nickname = 4;
    uint32 sex = 5;

    string openid = 6;
    uint32 seq = 7; //玩家编号
}

message GameBaseInfo {
    uint32 cp_id = 1;
    uint32 game_id = 2;
    string game_version = 3;

    string game_name = 4;
    string game_pic = 5;
    string game_package = 6;
    string game_url = 7;

    uint32 engine_ver = 8;

    uint32 game_member_cnt_limit = 9; // 进入游戏的人数限制

    uint32 engine_type = 10;

    repeated uint32 game_app_limit = 11;
    repeated uint32 game_platform_limit = 12;

    uint32 create_time = 13;

    string game_res_url = 14;

    uint32 start_time = 15;

    string game_extra_properties = 16;

    string game_digest = 17;
    string game_res_digest = 18;

    string game_mode = 19;

    string js_url = 20;
    string js_digest = 21;

    string main_package_url = 22;  //分包时的主包url
}

message ChannelGameMaintain {
    int64 maintain_begin = 1;
    int64 maintain_end = 2;
    string maintain_text = 3;
    int64 maintain_key = 4; //维护标识
}

message ChannelGameMode {
    string mode_key = 1; //模式key
    string game_param = 2; //游戏参数
    uint32 player_limit = 3; //限制人数
    repeated uint32 player_limit_list = 4; //限制人数列表
}

message ChannelOpenGameInfo {
    GameBaseInfo base = 1;

    uint32 game_status = 2; // 游戏状态，0：等待，1：进行中
    uint32 game_master = 3;
    uint32 game_member_cnt = 4; // 进入游戏的人数

    repeated ChannelOpenGamePlayer game_players = 5; // 游戏玩家列表

    uint32 game_initiator = 6; // 发起者

    string game_current_round = 7; // 游戏回合

    bool is_new_player = 8; // 是否为新玩家

    ChannelGameMode game_mode = 9;
    ChannelGameMaintain game_maintain = 10;

    int64 load_seq = 11; //房间变更序号，用于同步房间状态和游戏操作
}

message GetSupportGameListReq {
    uint32 cp_id = 1;
    uint32 app_id = 2; //1：tt, 2: huanyou, 3: zaiya
    uint32 platform = 3; //1：ios, 2: android
}

message GetSupportGameListResp {
    repeated GameBaseInfo base = 1;
}

message GetGameMaintainReq {
    uint32 game_id = 1;
    string game_version = 2; //为空则取最新版本数据
}

message GetGameMaintainResp {
    ChannelGameMaintain game_maintain = 1;
}

message BatchGetGameInfoReq {
    repeated uint32 game_id = 1;
}

message BatchGetGameInfoResp {
    repeated GameBaseInfo base = 1;
}

message SetChannelGameReq {
    uint32 channel_id = 1;

    uint32 game_id = 3;
    string game_version = 4;

    uint32 game_initiator = 5; // 发起者

    string game_mode = 6; // 游戏模式
}

message SetChannelGameResp {

}

message SetChannelGameSyncReq {
    uint32 channel_id = 1;

    uint32 game_id = 3;
    string game_version = 4;

    uint32 game_initiator = 5; // 发起者

    string game_mode = 6; // 游戏模式
}

message SetChannelGameSyncResp {
    string game_version = 1;
    ChannelGameMode info = 2;
    int64 load_seq = 3; // 加载序号
}

message SetChannelGameAsyncReq {
    uint32 channel_id = 1;

    uint32 game_id = 3;
    string game_version = 4;

    uint32 game_initiator = 5; // 发起者

    string game_mode = 6; // 游戏模式
}

message SetChannelGameAsyncResp {

}

message UpdateChannelGameReq {
    uint32 channel_id = 1;
}

message UpdateChannelGameResp {
    int64 load_seq = 1; // 加载序号
}

message SetChannelGameMasterReq {
    uint32 channel_id = 1;

    uint32 game_id = 2;
    uint32 game_master = 3;
}
message SetChannelGameMasterResp {

}

message SetChannelGameInfoReq {
    uint32 channel_id = 1;

    ChannelOpenGameInfo info = 2;
}
message SetChannelGameInfoResp {

}

message GetChannelGameInfoReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
}
message GetChannelGameInfoResp {
    ChannelOpenGameInfo info = 1;
}

message GetGameModeInfoReq {
    uint32 game_id = 1;
    string game_version = 2;
    string game_mode = 3;
}
message GetGameModeInfoResp {
    ChannelGameMode info = 1;
}

message SetChannelGameStatusReq {
    uint32 channel_id = 1;
    uint32 game_id = 2;
    uint32 game_status = 3;
}
message SetChannelGameStatusResp {
}

message SetChannelGameMemberReq {
    uint32 channel_id = 1;
    uint32 game_id = 2;
    uint32 game_member_cnt = 3;
}

message SetChannelGameMemberResp {
}

message SetChannelGamePlayerOpenIdReq {
    uint32 channel_id = 1;
    uint32 game_id = 2;

    repeated uint32 uids = 3;
    repeated string openids = 4;
}

message SetChannelGamePlayerOpenIdResp {

}

message GetChannelGameBaseInfoReq {
    uint32 channel_id = 1;
}
message GetChannelGameBaseInfoResp {
    uint32 game_id = 1;
    string game_version = 2;
    string game_name = 3;   // datacenter需要获取返回
    uint32 game_member_cnt_limit = 4;
    uint32 cp_id = 5;
    string game_mode_info = 6; // 游戏模式信息
    string mode = 7; // 游戏模式
    int64 load_seq = 8; // 房间变更序号，用于同步房间状态和游戏操作
}
