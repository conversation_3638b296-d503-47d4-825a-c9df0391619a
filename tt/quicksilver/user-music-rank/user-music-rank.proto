syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/user-music-rank";

package user_music_rank;

service UserMusicRank {
  // 批量获取用户段位称号
  rpc GetMusicRankUserInfo(GetMusicRankUserInfoReq) returns (GetMusicRankUserInfoResp);

  //批量获取用户实力信息
  rpc BatchUserPowerInfo(BatchUserPowerInfoReq) returns (BatchUserPowerInfoResp);

  //批量获取用户当前称号
  rpc BatchUserGlory(BatchUserGloryReq) returns (BatchUserGloryResp);

  //获取用户当前称号列表
  rpc ListUserGlories(ListUserGloriesReq) returns (ListUserGloriesResp);

  //设置用户当前外显称号
  rpc SetUserGlory(SetUserGloryReq) returns (SetUserGloryResp);

  //获取用户历史称号列表
  rpc ListUserHistoryGlories(ListUserHistoryGloriesReq) returns (ListUserHistoryGloriesResp);

  //设置用户当前位置
  rpc SetUserLocation(SetUserLocationReq) returns (SetUserLocationResp);

  //获取用户歌手唱力榜
  rpc ListUserSingerScore(ListUserSingerScoreReq) returns (ListUserSingerScoreResp);

  //获取用户歌手唱力详情
  rpc GetUserSingerScoreDetail(GetUserSingerScoreDetailReq) returns (GetUserSingerScoreDetailResp);

  //获取用户歌曲记录
  rpc ListUserMusicRecords(ListUserMusicRecordsReq) returns (ListUserMusicRecordsResp);

  //获取段位排行榜
  rpc GetStarRank(GetStarRankReq) returns (GetStarRankResp);

  //获取歌手唱力排行榜
  rpc GetSingerScoreRank(GetSingerScoreRankReq) returns (GetSingerScoreRankResp);

  //歌曲是否在在排行榜歌曲中
  rpc IsSongsInRank(IsSongsInRankReq) returns (IsSongsInRankResp);

  // 获取用户位置
  rpc GetUserLocation(GetUserLocationReq) returns (GetUserLocationResp);

  // 获取用户位置权限设置
  rpc GetUserLocationAuth(GetUserLocationAuthReq) returns (GetUserLocationAuthResp);

  // 获取用户位置权限设置
  rpc SetUserLocationAuth(SetUserLocationAuthReq) returns (SetUserLocationAuthResp);

  // 根据歌曲获取用户对应的称号
  rpc GetUserMatchGlory(GetUserMatchGloryReq) returns (GetUserMatchGloryResp);

  rpc GetUserMusicRankDialog(GetUserMusicRankDialogReq) returns (GetUserMusicRankDialogResp);

  rpc UpdateUserPowerScore(UpdateUserPowerScoreReq) returns (UpdateUserPowerScoreResp);

  rpc UpdateUserStarLevel(UpdateUserStarLevelReq) returns (UpdateUserStarLevelResp);

  rpc UserMusicRankDialogConfirm(UserMusicRankDialogConfirmReq) returns (UserMusicRankDialogConfirmResp);

  rpc UserMusicRankTrigger(UserMusicRankTriggerReq) returns (UserMusicRankTriggerResp);

  rpc UserMusicRankGloryTrigger(UserMusicRankGloryTriggerReq) returns (UserMusicRankGloryTriggerResp);

  rpc GetRatingForScore(GetRatingForScoreReq) returns (GetRatingForScoreResp);

  rpc GetSeasonWebInfo(GetSeasonWebInfoReq) returns (GetSeasonWebInfoResp);

  rpc GetUserSchoolInfo(GetUserSchoolInfoReq) returns(GetUserSchoolInfoResp);

  rpc ListSchools(ListSchoolsReq) returns(ListSchoolsResp);

  rpc BindSchool(BindSchoolReq) returns(BindSchoolResp);

  rpc GetUserRankInSchool(GetUserRankInSchoolReq) returns(GetUserRankInSchoolResp);

  rpc GetUserRankInCity(GetUserRankInCityReq) returns(GetUserRankInCityResp);

  rpc GetSchoolRankInCity(GetSchoolRankInCityReq) returns(GetSchoolRankInCityResp);

  rpc GetSchoolRank(GetSchoolRanReq) returns(GetSchoolRanResp);

  rpc GetMirrorId(GetMirrorIdReq)returns(GetMirrorIdResp);

  rpc GetMirrorData(GetMirrorDataReq)returns(GetMirrorDataResp);

  rpc RebuildSchoolBind(RebuildSchoolBindReq)returns(RebuildSchoolBindResp);

  rpc RebuildSchoolRank(RebuildSchoolRankReq)returns(RebuildSchoolRankResp);

  rpc UpdateSchoolScore(UpdateSchoolScoreReq)returns(UpdateSchoolScoreResp);

  rpc UpsertSchools(UpsertSchoolsReq)returns(UpsertSchoolsResp);

  rpc UpsertCities(UpsertCitiesReq)returns(UpsertCitiesResp);

  rpc DelSongs(DelSongsReq)returns(DelSongsResp);

  rpc AddBlackUids(AddBlackUidsReq) returns(AddBlackUidsResp);

  rpc RemoveBlackUids(RemoveBlackUidsReq) returns(RemoveBlackUidsResp);

  rpc ListBlackUids(ListBlackUidsReq) returns(ListBlackUidsResp);

}

message AddBlackUidsReq{
  repeated uint32 uids = 1;
}
message AddBlackUidsResp{

}

message RemoveBlackUidsReq{
  repeated uint32 uids = 1;
}
message RemoveBlackUidsResp{

}

message ListBlackUidsReq{

}
message ListBlackUidsResp{
  message UidRecord {
    uint32 uid = 1;
    int64 create_at = 2;
  }

  repeated UidRecord records = 1;
}

message DelSongsReq{
  repeated string song_ids = 1;
}

message DelSongsResp{

}

message UpsertCitiesReq{
  repeated City cities = 1;
  message City {
    string id = 1;
    string name = 2;
  }
}

message UpsertCitiesResp{

}

message UpsertSchoolsReq{
  repeated SchoolInfo schools = 1;
  message SchoolInfo {
    string id = 1;
    string name = 2;
    string img = 3;
    string internal_id = 4;
    string city_id = 5;
  }
}

message UpsertSchoolsResp{

}

message UpdateSchoolScoreReq{
  uint32 uid = 1;
  int64 score = 2;
}

message UpdateSchoolScoreResp{
}

message GetMirrorDataReq{
  string mirror_id = 1;
}

message GetMirrorDataResp{
  User user = 1;
  repeated SchoolInfo schools = 2;
  repeated UserRankInfo users = 3;
  repeated UserRankInfo city_users = 4;
  repeated SchoolInfo city_schools = 5;
  message User{
    string user_name = 1;
    string user_avatar = 2;
    int64  hot_score = 3;
    string school_id = 4;
    string school_name = 5;
    int32 user_rank = 6;
    int32 school_rank = 7;
    string city_id = 8;
    string city_name = 9;
    int32 city_rank = 10;
  }


  message UserRankInfo {
    string user_name = 1;
    string user_avatar = 2;
    int64  hot_score = 3;
    string school_id = 4;
    string school_name = 5;
  }

  message SchoolInfo {
    int64  hot_score = 1;
    string school_id = 2;
    string school_name = 3;
    string school_img = 4;
  }

}

message GetMirrorIdReq{
  uint32 uid = 1;
}

message GetMirrorIdResp{
  string mirror_id = 1;
}

message RebuildSchoolBindReq{
  string school_id = 1;
}

message RebuildSchoolBindResp{

}

message RebuildSchoolRankReq{
}

message RebuildSchoolRankResp{

}

message GetSchoolRanReq{
  uint32 uid = 1;
}

message GetSchoolRanResp{
  repeated School schools = 1;
  message School {
    string id = 1;
    string name = 2;
    string img = 3;
    int64 hot_score = 4;
  }
}

message GetUserRankInSchoolReq{
  uint32 uid = 1;
}

message GetUserRankInSchoolResp{
  message User{
    uint32 uid = 1;
    string name = 2;
    School school = 3;
    int64 hot_score = 4;
  }

  message School {
    string id = 1;
    string name = 2;
    string img = 3;
  }

  repeated User users = 1;
}

message GetSchoolRankInCityReq{
  uint32 uid = 1;
}

message GetSchoolRankInCityResp{
  repeated School schools = 1;
  string city_id = 2;
  string city_name = 3;
  message School {
    string id = 1;
    string name = 2;
    string img = 3;
    int64 hot_score = 4;
  }
}

message GetUserRankInCityReq{
  uint32 uid = 1;
}

message GetUserRankInCityResp{
  message User{
    uint32 uid = 1;
    string name = 2;
    School school = 3;
    int64 hot_score = 4;
  }

  message School {
    string id = 1;
    string name = 2;
    string img = 3;
  }

  repeated User users = 1;
  string city_id = 2;
  string city_name = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BindSchoolReq{
  uint32 uid = 1;
  string school_id = 2;
  string real_name = 3;
  string phone = 4;
  uint32 app_ID = 5;
  uint32 os = 6;
}
message BindSchoolResp{
}


message ListSchoolsReq{
}



message ListSchoolsResp{
  repeated School schools = 1;

  message School {
    string id = 1;
    string name = 2;
    string img = 3;
  }
}


message GetUserSchoolInfoReq{
  uint32 uid = 1;
}

message GetUserSchoolInfoResp{
  uint32 uid = 1;
  int64 hot_score = 2;
  School school = 3;
  string city_name = 4;
  string city_code = 5;
  int32 city_rank = 6;

  message School {
    string id = 1;
    string name = 2;
    string img = 3;
    int32 user_rank = 4;
    int32 school_rank = 5;

  }
}

message GetSeasonWebInfoReq{
}

message GetSeasonWebInfoResp{
  string title_img = 1;
  repeated string desc_imgs = 2;
  repeated string rule_imgs = 3;
}

message UserMusicRankGloryTriggerReq {

}
message UserMusicRankGloryTriggerResp {
}

message UserMusicRankTriggerReq {

}
message UserMusicRankTriggerResp {
}

/***********************************GetMusicRankUserInfo**********************************************/

message GetMusicRankUserInfoReq {
  bool need_mic = 1;
  repeated uint32 uids = 2;
}
message GetMusicRankUserInfoResp {
  map<uint32, UserGloryInfo> user_glory_map = 1;
}
message UserGloryInfo {
  uint32 uid = 1;
  GloryResource glory_resource = 2;

  string dan_img = 3;
  string dan_bg_img = 4;
  string dan_name = 5;
  string mic_img = 6;
  string dan_mic_img = 7;
}

/***********************************BatchUserPowerInfo**********************************************/

message BatchUserPowerInfoReq {
  repeated uint32 uids = 1;
}
message BatchUserPowerInfoResp {
  map<uint32, UserPowerInfo> user_power_map = 1;
}

/***********************************BatchUserGlory**********************************************/

message BatchUserGloryReq {
  repeated uint32 uids = 1;
}
message BatchUserGloryResp {
  map<uint32, GloryResource> user_glory_map = 1;
}


/***************************************ListUserGlories******************************************/

message ListUserGloriesReq {
  uint32 uid = 1;
}
message ListUserGloriesResp {
  repeated GloryResource glories = 1;
}

/*************************************SetUserGlory********************************************/

message SetUserGloryReq {
  uint32 uid = 1;
  string glory_id = 2;//称号id
}
message SetUserGloryResp {
}

/***************************************ListUserHistoryGlories******************************************/

message ListUserHistoryGloriesReq {
  uint32 uid = 1;
  uint32 limit = 2;
  string offset = 3;//上一页最后glory_id

}
message ListUserHistoryGloriesResp {
  bool is_end = 1;
  repeated GloryResource glories = 2;
}

/***************************************SetUserLocation******************************************/

message SetUserLocationReq {
  uint32 uid = 1;
  string ip = 2;
}
message SetUserLocationResp {
  string location = 1; //拼接的地理位置信息
}

/***************************************GetUserLocation******************************************/

message GetUserLocationReq {
  uint32 uid = 1;
  string ip = 2;
  bool is_cache = 3;
}

message GetUserLocationResp {
  string location = 1; //拼接的地理位置信息
  bool is_sensitive = 2; // false:不是敏感地区 true:敏感地区
}

// 获取用户权限
message GetUserLocationAuthReq {
  uint32 uid = 1;
}

message GetUserLocationAuthResp {
  bool is_allowed = 1; // true:用户允许定位 false：用户不允许定位
}

// 设置用户权限
message SetUserLocationAuthReq {
  uint32 uid = 1;
  bool is_allowed = 2; // true:用户允许定位 false：用户不允许定位
}

message SetUserLocationAuthResp {
}

/*************************************GetUserScoreRank********************************************/

message ListUserSingerScoreReq {
  uint32 uid = 1;
}
message ListUserSingerScoreResp {
  repeated SingerScoreInfo infos = 1;

  message SingerScoreInfo{
    string singer_id = 1;//歌手ID
    string singer_name = 2;//歌手名称
    string singer_avatar = 3;//歌手头像
    GloryResource glory = 4;//称号
    uint32 score = 5;//唱力
    string desc = 6;//该用户当前唱力描述文案
  }
}

/**************************************GetUserSingerScoreDetail*******************************************/

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetUserSingerScoreDetailReq {
  uint32 uid = 1;
  string singer_id = 2;//歌手id
  ReqType reqType = 3;
  enum ReqType{
    Full = 0;//包含歌曲信息
    Simple = 1;//只有歌手信息
  }
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetUserSingerScoreDetailResp {
  SingerScoreInfo singer_score_info = 1;//歌手唱力信息
  Season season = 2;//赛季
  repeated SongScoreInfo song_score_info = 3;//歌手歌曲唱力信息
  Location loc = 4;
  uint32 rankMinScore = 5;//最低上榜分

  message SingerScoreInfo{
    string singer_id = 1;//歌手ID
    string singer_name = 2;//歌手名称
    string singer_avatar = 3;//歌手头像
    GloryResource glory = 4;//称号
    uint32 score = 5;//当前唱力
    uint32 max_score = 6;//历史最高唱力
    uint32 sang_num = 7;//当前赛季已唱歌曲
  }

  message SongScoreInfo{
    string song_id = 1;//歌曲id
    string image = 2;//歌曲封面
    string name = 3;//歌曲名称
    uint32 score = 4;//歌曲最高得分
    uint32 total_score = 5;//歌曲总分
    string singer_names_desc = 6;//歌手名称拼接
    uint32 origin_score = 7;//原始分数
    uint32 burst_light_num = 8;//爆灯数量
    string burst_light_copy = 9;//加成文案
  }
}

/**************************************GetStarRank*******************************************/

message GetStarRankReq {
  uint32 uid = 1;
}
message GetStarRankResp {
  repeated UserStarInfo user_star_info = 1;

  message UserStarInfo{
    uint32 uid = 1;//用户id
    string name = 2;//用户名称
    string avatar = 3;//用户头像
    StarLevel star_level = 4;//用户段位等级，单位半颗星
  }
}

/************************************GetSingerScoreRank*********************************************/

message GetSingerScoreRankReq {
  string singer_id = 1;
  GloryLevel level = 2;
  uint32 uid = 3;
}
message GetSingerScoreRankResp {
  repeated UserScoreInfo user_score_info = 1;

  message UserScoreInfo{
    uint32 id = 1;//用户id
    string name = 2;//用户名称
    string avatar = 3;//用户头像
    uint32 score = 4;//唱力
  }
}

/************************************ListUserMusicRecords*********************************************/

message ListUserMusicRecordsReq {
  uint32 uid = 1;//用户id
  uint32 limit = 2;//条数
  string offset_id = 3;//上一次的记录的最后一条，第一次不传
}
message ListUserMusicRecordsResp {
  bool is_end = 1;
  repeated Record records = 2;
  enum SingType {
    Single = 0; //单唱
    Tutti = 1;  // 合唱
    RobSing = 2; // 抢唱
  }
  message Record{
    uint64 create_at = 1;//创建时间
    string desc = 2;//描述文案， 也即描述标题
    int32 star_level = 3;//获取星个数，单位半颗
    SingType sing_type = 4; // 演唱类型
    uint32 score = 5;  // 演唱得分
  }
}

/************************************IsSongsInRan*********************************************/

message IsSongsInRankReq {
  repeated string song_ids = 1;//歌曲id集合
}
message IsSongsInRankResp {
  map<string, bool> song_id_map = 1;
}

// 获取用户段位信息
message GetUserGloryReq {
  uint32 uid = 1;
}
message GetUserGloryResp {
  GloryResource user_glory = 1;
  string head_image = 2;
  string nick_name = 3;
}

/************************************GetUserMatchGlory*********************************************/

message GetUserMatchGloryReq {
  uint32 uid = 1;
  string song_id = 2;
  string singer_name = 3;
}
message GetUserMatchGloryResp {
  GloryResource resource = 1;
}

/************************************GetUserMusicRankDialog*********************************************/
message GetUserMusicRankDialogReq {
  uint32 uid = 1;
}
message GetUserMusicRankDialogResp {
  GloryDialog glory_dialog = 1;
  SeasonDialog season_dialog = 2;
}

message GloryDialog {
  uint32 country_count = 1;
  uint32 province_count = 2;
  uint32 city_count = 3;

  repeated SingerGlory glories = 4;

  message SingerGlory{
    string singer_name = 1;
    string singer_image = 2;
    GloryResource glory = 3;
  }
}

message SeasonDialog {
  string first_title_img = 1;
  string second_title_img = 2;
  StarLevel pre_star_level = 3;
  StarLevel cur_star_level = 4;

  message StarLevel{
    string title = 1;
    string img = 2;
  }
}
/************************************UpdateUserPowerScore*********************************************/
message UpdateUserPowerScoreReq{
  uint32 uid = 1;
  string singer_id = 2;
  string song_id = 3;
  uint32 score = 4;
  uint32 burst_light_num = 5;
}

message UpdateUserPowerScoreResp{

}
/************************************UpdateUserStarLevel*********************************************/
message UpdateUserStarLevelReq{
  uint32 uid = 1;
  int32 star_level = 2;
}

message UpdateUserStarLevelResp{

}

/************************************UserMusicRankDialogConfirm*********************************************/
message UserMusicRankDialogConfirmReq{
  uint32 uid = 1;
  ReqType req_type = 2;
  enum ReqType{
    SessionDialog = 0;
    GloryDialog = 1;
  }
}

message UserMusicRankDialogConfirmResp{

}

/************************************GetRatingForScore*********************************************/

message GetRatingForScoreReq{
  uint32 score = 1;
  uint32 max_score = 2;
}

message GetRatingForScoreResp{
  uint32 id = 1;
  string name = 2;
  uint32 score_percentage = 3;
}

/************************************公共参数*************************************************/
message UserPowerInfo{
  uint32 uid = 1;
  Location loc = 2;
  GloryResource glory = 3;
  StarLevel star_level = 4;//段位排行
}

message Location{
  string country = 1;
  string country_code = 2;
  string province = 3;
  string province_code = 4;
  string city = 5;
  string city_code = 6;
}

//message Glory{
//  string id = 1;
//  GloryLevel level = 2;
//  uint32 rank = 3;
//  string singer_id = 4;//歌手id
//  string singer_name = 5;//歌手名称
//  string singer_avatar = 6;//歌手头像
//  string location_code = 7;//地理位置code
//  string location_desc = 8;//地理位置描述
//}

enum GloryLevel{
  City = 0;//市级称号
  Province = 1;//省级称号
  Country = 2;//国服称号
}

message StarLevel{
  uint32 level = 1;//单位半颗星
  string level_desc = 2;//段位描述
  uint32 star = 3;//段位几星，单位半颗星
  string image = 4;
  string bg_image = 5;
  bool is_top_level = 6; //true:代表已经到了最高段位
  uint32 star_style = 7; // 前端星星个数样式
}

message Season{
  uint32 id = 1;//赛季id
  uint64 start_at = 2;//当前赛季开始时间
  uint64 end_at = 3;//当前赛季结束时间
  string first_title_img = 4;
  string second_title_img = 5;

  string title_img = 6;
  repeated string desc_imgs = 7;
  repeated string rule_imgs = 8;
}

message GloryResource {
  string glory_name = 1; // 称号名称
  string glory_img = 2; // 头标
  string glory_color = 3; // 背景颜色
  uint32 glory_rank = 4; // 排行分
  string play_color = 5;
  string banner_img = 6;
  string glory_bg_img = 7; // 背景图
  string glory_id = 8;
  GloryLevel level = 9;

  string loc_code = 10;//位置code
  string singer_id = 11;//歌手id
}