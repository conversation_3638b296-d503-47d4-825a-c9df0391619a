syntax = "proto3";

package push.core;

import "google/protobuf/any.proto";

option go_package = "golang.52tt.com/protocol/services/mijing_push_trans;MijingPushTrans";

// 消息在推送系统中路由的结构体
message PushNotification {
  TypedMessage message              = 1;  //必要
  repeated Destination destinations = 2;  //必要
  uint64 seq_id                     = 3;
  bytes message_id                  = 4;
  uint64 server_time                = 5;
  //以下暂未支持

  uint32 expire   = 6;  //消息过期时间 单位 s
  bool sync       = 7;
  uint32 priority = 8;
  bool force      = 9;  //强制推送 会推送到所有proxy 非特别特别重要的消息 不要使用

  string request_id = 10;
  string dye_id = 11;
  uint32 push_type = 12;
  string push_label = 13;
}

message TypedMessage {
  oneof type {
    bytes payload                   = 1;
    google.protobuf.Any payload_any = 5;
    Command command                 = 6;
  }
}

message Destination {
  oneof destination_type {
    PushToUsers push_to_users    = 1;
    PushMulticast push_multicast = 2;
    PushToTopic push_to_topic    = 3;
    //no support
    //        BroadcastChannel broadcast_channel = 3;
    //TODO 全服
  }
}

//enum TopicType {
//    GROUP = 0;
//}

message Topic {
  string topic    = 1;
  string provider = 2;
}

enum TopicEventOp {
  ADD    = 0;  //增加 即订阅
  REMOVE = 1;  //取消 即取消订阅
}

message UserTopicRelationEvent {
  UserTopicRelation relation = 1;
  TopicEventOp op            = 2;
}

message UserTopicRelation {
  uint64 uid            = 1;
  repeated Topic topics = 2;
}

////因为TT和TTChat都在用的原因，所以有些pb都订两份好了
//enum BroadcastChannelType {
//    DEFAULT = 0;
//}
//
//message BroadcastChannel {
//    uint64 broadcast_channel_id                 = 1;
//    BroadcastChannelType broadcast_channel_type = 2;
//}

message PushToUsers {
  repeated NotifyUser notify_users = 1;
}
message NotifyUser {
  uint64 uid            = 1;
  string skip_device_id = 2;
}

message PushMulticast {
  uint64 channel_id          = 1;
  int32 channel_type         = 2;  //对应 ga下的channelType 房间类型  默认为房间  TT请忽略这个字段
  MulticastScopeOption scope = 3;
}
message UidList {
  repeated uint64 uids = 1;
}
message MulticastScopeOption {
  oneof option {
    UidList only = 1;
    UidList skip = 2;
  }
}
message PushToTopic {
  Topic topic = 1;
}
message Command {
  oneof type {
    Evict evict                     = 1;
    UserTopicRelationEvent relation = 2;
  }
}
// no supported
message Evict {
  oneof policy {
    EvictUser user     = 1;
    EvictDevice device = 2;
    EvictUsersInChannel users_in_channel = 3;
  }
}

message EvictUsersInChannel {
  uint64 channel_id = 1;
  repeated uint64 uid_list = 2;
}

message EvictUser {
  uint64 uid = 1;
}
message EvictDevice {
  uint64 uid       = 1;
  string device_id = 2;
  bool reverse     = 3;  //false: 驱逐uid+device_id的流   true:驱逐uid中除了device_id以外的流
}
