syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package enigma.global.api.push;

import "google/protobuf/any.proto";

option go_package = "golang.52tt.com/protocol/services/mijing_push_message;MijingPushMessage";


// 推送的消息，在这个下面做扩展
message PushMessage {
  // 类型
  oneof type {
    // 外部的消息,支持TT直接用我们在线推送协议，同 notify_push 差不多的东西
    ExternalMessage external_msg = 9;
  }
}

enum ExternalPushType {
  // 未定义
  EXTERNAL_PUSH_TYPE_UNSPECIFIED = 0;
  // 普通消息
  EXTERNAL_PUSH_TYPE_NORMAL = 1;
}

message ExternalMessage {
  // 标题
  string title = 1;
  // 内容
  string content = 2;
  // 跳转路径
  string navigate_url = 3;
  // 自定图标，未使用
  string icon = 4;
  // 推送类型
  ExternalPushType push_type = 5;
  // 请求ID
  string request_id = 6;
  // tt 任务ID
  string task_id = 7;
  // 扩展字段 json
  string extra_data = 8;
  // 扩展字段 可选的业务自定义的载荷, 该字段应该定义在自己的业务的 API 里面, 值得注意的是该字段必须短小且精悍, 不能将一个很大的对象放在这里
  google.protobuf.Any custom = 9;
}
