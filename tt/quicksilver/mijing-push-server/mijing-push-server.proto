syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package push.server;

import "tt/quicksilver/mijing-push-server/core/trans.proto";

option go_package = "golang.52tt.com/protocol/services/mijing_push_server;MijingPushServer";

service PushServer{
  rpc Push(PushServerReq) returns(PushServerResp){}
  rpc StreamPush(stream PushServerReq) returns (stream PushServerResp){}
}

message PushServerReq{
  core.PushNotification push_notification=1;
  //todo 结构优化一下 或者这里预留一些只跟push-server有关的非关键字段
  /*    Destination destination=2;
      TerminalTypePolicy terminal_type_policy = 3; //是不是可以去掉*/
}
message Destination{
  oneof destination_type {
    core.PushToUsers push_to_users = 1;
    core.PushMulticast    push_multicast = 2;
    //TODO 全服
  }
}

message PushServerResp{
  uint64 sequence = 1;
  uint32 total = 2; //请求总计产生的推送任务次数
  uint32 failed = 3; //失败次数 0即全部成功
  repeated PushResult detail = 4; //所有推送详细情况
}

message PushResult{
  string key =1; //推送key(即proxy名称/或ip)
  bool success = 2; //是否成功
  repeated uint64 ids = 3; //本次推送涉及的 uid或者channel_id (注意 一个uid只会在一个推送任务上 但是一个channel_id 可能在不同的推送任务上)
}