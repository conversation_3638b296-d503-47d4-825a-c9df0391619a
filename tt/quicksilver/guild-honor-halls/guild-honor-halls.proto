syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/guildhonorhalls";
package guildhonorhalls;

service GuildHonorHalls {

    rpc SetHonorGuildList( SetHonorGuildListReq ) returns( SetHonorGuildListResp ){
    }

    rpc GetHonorGuildList( GetHonorGuildListReq ) returns( GetHonorGuildListResp ){
    }

    rpc GetHonorGuild( GetHonorGuildReq ) returns( GetHonorGuildResp ){
    }

    rpc SetGuildHotChannelList( SetGuildHotChannelListReq ) returns( SetGuildHotChannelListResp ){
    }

    rpc GetGuildHotChannelList ( GetGuildHotChannelListReq ) returns ( GetGuildHotChannelListResp ) {
    }

    rpc SetGuildCharmMemberList ( SetGuildCharmMemberListReq ) returns ( SetGuildCharmMemberListResp ) {
    }

    rpc GetGuildCharmMemberList( GetGuildCharmMemberListReq ) returns( GetGuildCharmMemberListResp ){
    }

    rpc SearchGuildMember( SearchGuildMemberReq ) returns( SearchGuildMemberResp ){
    }

    rpc SetTagItemList( SetTagItemListReq ) returns( SetTagItemListResp ){
    }

    rpc SetHonorTitle( SetHonorTitleReq ) returns( SetHonorTitleResp ){
    }

    rpc GetTagItemList( GetTagItemListReq ) returns( GetTagItemListResp ){
    }

    rpc GetHonorTitle( GetHonorTitleReq ) returns( GetHonorTitleResp ){
    }

    rpc DelTagItemList( DelTagItemListReq ) returns( DelTagItemListResp ){
    }

    rpc DelHonorTitleList( DelHonorTitleListReq ) returns( DelHonorTitleListResp ){
    }
}

enum HonorType {
    HONOR_GUILD = 0; //荣誉公会
    HIGH_QUALITY_GUILD= 1; //高质量公会
}

enum GuildType {
    FUN_GUILD = 0; //娱乐公会
    LIVE_GUILD = 1;//直播公会

    ALL_GUILD = 1024; //所以
}

//展示中，结束，删除
enum ShowState {
    SHOWING = 0;
    TIMEOUT= 1;
    DELETE = 2;
    WAIT_SHOW = 3;//待展示
    ALL_STATE = 1024;
}

message TagItem {
    string text = 1;
    string para = 2;
}

message HonorTitle {
    string text = 1;
    string icon_url = 2;
}

message HonorGuild {
    int64 id = 1;
    uint32 guild_id = 2;
    uint32 short_id = 3;
    int64 begin_time = 4; //开始展示时间戳
    int64 end_time = 5; //结束展示时间戳
    int64 index_weight = 6; //排名权重
    repeated HonorTitle honour_title = 7;//荣誉称号
    int64 member_cnt = 8;
    string guild_name = 9;
    string guild_desc = 10;
    bool is_cooperation = 11; //是否合作库
    repeated TagItem tag_list = 12;
    ShowState guild_state = 13; //
    HonorType honor_type = 14;
}

message SetTagItemListReq{
    repeated TagItem tag_item = 1;
    bool is_add = 2; //新增
}

message SetTagItemListResp{
}

message GetTagItemListReq{}
message GetTagItemListResp{
    repeated TagItem tag_list = 1;
}

message DelTagItemListReq{
    repeated TagItem tag_list = 1;
}

message DelTagItemListResp{
}

message SetHonorTitleReq {
    repeated HonorTitle honor_title_list = 1;
    bool is_add = 2; //新增
}

message SetHonorTitleResp {
}

message DelHonorTitleListReq{
    repeated HonorTitle honor_title_list = 1;
}

message DelHonorTitleListResp{
}

message GetHonorTitleReq{
}

message GetHonorTitleResp{
    repeated HonorTitle title_list = 1;
}

//添加公会列表
message SetHonorGuildListReq{
    repeated HonorGuild guild_id_list = 1;
    HonorType honor_type = 2;
    GuildType guild_type = 3;
}

message SetHonorGuildListResp{
}

//取荣誉公会列表
message GetHonorGuildListReq{
    HonorType honor_type = 1;
    GuildType guild_type = 2;
    ShowState guild_state = 3;
    int64 off = 4;
    int64 count = 5;
}

message GetHonorGuildListResp{
    repeated HonorGuild guild_info_list = 1;
    int64 total_count = 2;
}

//单取
message GetHonorGuildReq {
    uint32 guild_id = 1;
}

message GetHonorGuildResp {
    HonorGuild guild_info = 1;
}

message GuildChannel {
    uint32 channel_id = 1;
    string channel_name = 2;
    string channel_desc = 3;
    int64 index_weight = 4; //排名权重
    uint32 channel_type = 5; //channel_.proto enum ChannelType
    ShowState show_state = 6;
}

//设置公会热门房间
message SetGuildHotChannelListReq {
    uint32 guild_id = 1;
    repeated GuildChannel channel_info_list = 3; //公会房间列表
}

message SetGuildHotChannelListResp {
}

message GetGuildHotChannelListReq{
    uint32 guild_id = 1;
    uint32 channel_type = 2; //channel_.proto enum ChannelType
}

message GetGuildHotChannelListResp{
    repeated GuildChannel channel_list = 1;
}

//设置公会魅力成员
message GuildCharmMember {
    uint32 uid = 1;
    string nickname = 2;
    string account = 3;
    string alias = 4;

    ShowState show_state = 5; //待展示0，置顶1
}

message SetGuildCharmMemberListReq{
    uint32 guild_id = 1;
    repeated GuildCharmMember member_list = 2;
}

message SetGuildCharmMemberListResp {
}

message GetGuildCharmMemberListReq{
    uint32 guild_id = 1;
}

message GetGuildCharmMemberListResp{
     repeated GuildCharmMember member_list = 1;
}

//公会成员模糊搜索
message SearchGuildMemberReq{
    uint32 guild_id = 1;
    string nickname = 2;
}

message SearchGuildMemberResp{
    repeated GuildCharmMember member_list = 1;
}

