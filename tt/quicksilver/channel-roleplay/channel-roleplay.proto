syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-roleplay";

package channel_roleplay;

service ChannelRoleplay {
	// 获取房间内麦上用户角色信息
	rpc GetChannelHoldMicUserRoleList (GetChannelHoldMicUserRoleListReq) returns (GetChannelHoldMicUserRoleListResp) {}
	// 审核用户房间角色
	rpc AuditChannelUserRole (AuditChannelUserRoleReq) returns (AuditChannelUserRoleResp) {}
	// 获取房间用户角色信息
	rpc GetChannelUserRoleList(GetChannelUserRoleListReq) returns (GetChannelUserRoleListResp) {}
}

// 角色类型
enum RoleType {
	RoleTypeNil = 0;
	RoleTypeRolePlay = 1; // 角色扮演
	RoleTypeVest = 2; // 马甲
}

message GetChannelHoldMicUserRoleListReq {
	uint32 channel_id = 1;
	uint32 uid = 2;
	RoleType role_type = 3;
}

message UserRole {
	uint32 uid = 1;
	string role_name = 2;
	uint32 updated_at = 3;
}

message GetChannelHoldMicUserRoleListResp {
	repeated UserRole role_list = 1;
}

message AuditChannelUserRoleReq {
	uint32 channel_id = 1;
	uint32 uid = 2;
	string role_name = 3;
	RoleType role_type = 4;
}

message AuditChannelUserRoleResp {
}

message ChannelUserRoleNotify {
	enum AuditResult {
		AuditResultDefault = 0;
		AuditResultPass = 1;
		AuditResultReject = 2;
		AuditResultReview = 3;
	}

	UserRole role = 1;
	AuditResult result = 2;
}

message ChannelRoleChangeMsg {
	string text = 1;
	string role_name = 2;
}

message GetChannelUserRoleListReq {
	uint32 channel_id = 1;
	repeated uint32 uid_list = 2;

	RoleType role_type = 3;

	uint32 uid = 4;
}

message GetChannelUserRoleListResp {
	repeated UserRole role_list = 1;
	UserRole audit_role = 2;
}