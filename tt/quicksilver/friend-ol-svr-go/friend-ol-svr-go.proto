syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/friend-ol-svr-go";
package friend_ol_svr_go;

service FriendOlSvrGo {
  rpc UpdateOnlineStatus(UpdateOnlineStatusReq) returns (UpdateOnlineStatusResp) {}

  rpc GetOnlineFriends(GetOnlineFriendsReq) returns (GetOnlineFriendsResp) {}

  rpc GetOfflineFriends(GetOfflineFriendsReq) returns (GetOfflineFriendsResp) {}

  rpc UpdateUserPlayingGame(UpdateUserPlayingGameReq) returns (UpdateUserPlayingGameResp) {}

  rpc UpdateUserRoomId(UpdateUserRoomIdReq) returns (UpdateUserRoomIdResp) {}

  rpc GetFriendCacheCount(GetFriendCacheCountReq) returns (GetFriendCacheCountResp) {}

  rpc HandleSvipOnlineEvent(HandleSvipOnlineEventReq) returns (HandleSvipOnlineEventResp) {}

  rpc GetStealthInfo(GetStealthInfoReq) returns (GetStealthInfoResp) {}
}

enum OnlineStatus {
  OFFLINE = 0;
  ONLINE = 1;
  STEALTH = 2; // 隐身
}

message UpdateOnlineStatusReq {
  uint32 uid = 1;
  OnlineStatus ol_status = 2;
}

message UpdateOnlineStatusResp {

}

message FriendsDetail {
  uint32 uid = 1;
  OnlineStatus ol_status = 2;
  uint32 last_ol_time = 3;
  uint32 room_id = 4;
  string game_name = 5;
  uint32 channel_type = 6;
  bool channel_is_pwd = 7;
  bool invisible = 8;
}

message GetOnlineFriendsReq {
  uint32 uid = 1;

  repeated uint32 ext_check_online_uidlist = 2;
}

message GetOnlineFriendsResp {
  repeated FriendsDetail detail_list = 1;

  repeated FriendsDetail ext_check_offline_list = 2;
}

message GetOfflineFriendsReq {
  uint32 uid = 1;
}

message GetOfflineFriendsResp {
  repeated FriendsDetail detail_list = 1;
}

message UpdateUserPlayingGameReq {
  uint32 uid = 1;
  bool is_playing = 2;
  string game_name = 3;
}

message UpdateUserPlayingGameResp {

}

message UpdateUserRoomIdReq {
  uint32 uid = 1;
  uint32 channel_id = 2;
  bool is_enter = 3;
  uint32 channel_type = 4;
  bool is_pwd = 5;
}

message UpdateUserRoomIdResp {

}

message GetFriendCacheCountReq {
  uint32 uid = 1;
}

message GetFriendCacheCountResp
{
  uint32 count = 1;
}

// 在线隐身状态
enum OnlineSwitch
{
  ENUM_ONLINE_SWITCH_UNSPECIFIED = 0;
  ENUM_ONLINE_SWITCH_ONLINE = 1; // 在线
  ENUM_ONLINE_SWITCH_STEALTH = 2; // 隐身
}

message SvipOnlineEvent
{
  enum ChangeType
  {
    ENUM_CHANGE_TYPE_UNSPECIFIED = 0;
    ENUM_CHANGE_TYPE_MANUAL = 1; // 手动操作
    ENUM_CHANGE_TYPE_MORROW = 2; // 次日自动结束隐身
    ENUM_CHANGE_TYPE_SVIP_EXPIRED = 3; // SVIP到期结束隐身
  }
  uint32 uid = 1;
  OnlineSwitch online_switch = 2; // 在线状态
  ChangeType change_type = 3; // 变更类型
  int64 change_time = 4; // 变更时间
}

message HandleSvipOnlineEventReq {
    SvipOnlineEvent event = 1;
}

message HandleSvipOnlineEventResp {
}

enum StealthType {
    STEALTH_TYPE_UNSPECIFIED = 0;
    STEALTH_TYPE_ON  = 1;
    STEALTH_TYPE_OFF = 2;
}

message StealthInfo {
    // 隐身类型
    StealthType stealth_type = 1;
    // 隐身更新时间
    int64 stealth_update_ts = 2;
    // 上次在线时间
    int64 last_online_ts = 3;
}

message GetStealthInfoReq {
    repeated uint32 uid_list = 1;
}

message GetStealthInfoResp {
    map<uint32, StealthInfo> stealth_info_map = 2;
}

