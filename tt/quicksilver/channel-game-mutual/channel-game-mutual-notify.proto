syntax = "proto3";

package channel_game_mutual;

option go_package = "golang.52tt.com/protocol/services/channel-game-mutual";

// 通知回调接口
service Notify {
    rpc CallBack (CallBackRequest) returns (CallBackResponse) {}
}

// 通知类型。保证扩展性
enum CallBackType {
    CALL_BACK_TYPE_UNSPECIFIED = 0;
    CALL_BACK_TYPE_GAME_OUT_OF_TIME = 1; // 游戏超时。长时间没有结束的游戏，需要通知对应的服务
}

message CallBackRequest {
    uint32 channel_id = 1;
    uint64 game_id = 2;
    CallBackType notify_type = 3;
}

message CallBackResponse {
}

