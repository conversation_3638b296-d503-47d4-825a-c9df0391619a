syntax = "proto3";

package channel_game_mutual;

option go_package = "golang.52tt.com/protocol/services/channel-game-mutual";

// 接入游戏类型枚举，apollo配置中心的游戏配置id应以该枚举类型为准
enum GameType{
    GAME_TYPE_UNEXPECTED = 0; // 无效值
    GAME_TYPE_OFFER_ROOM = 1; // 拍卖房
    GAME_TYPE_PGC_DIGITAL_BOMB = 2; //数字炸弹
    GAME_TYPE_PGC_CHANNEL_PK = 3; // 跨房pk
    GAME_TYPE_PGC_THROW_BOMB = 4; // 甩雷
    GAME_TYPE_PGC_ADVENTRUE = 5; // 大冒险
    GAME_TYPE_REVENUE_EXT_GAME = 6; // 弹幕互动游戏
    GAME_TYPE_MASKED_PK_LIVE = 7; // 直播蒙面pk
    GAME_TYPE_SAMPLE_PK = 8; // 二人pk
    GAME_TYPE_MUL_PK = 9; // 四人pk
}