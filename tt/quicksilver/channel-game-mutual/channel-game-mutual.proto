syntax = "proto3";

package channel_game_mutual;

option go_package = "golang.52tt.com/protocol/services/channel-game-mutual";

service ChannelGameMutualCenter {
    // 注册。
    rpc Register (RegisterRequest) returns (RegisterRespone);
    // 取消注册。幂等
    rpc UnRegister (UnregisterRequest) returns (EmptyResponse);
    // 获取房间当前正在进行的游戏id列表
    rpc GetCurGameId (GetCurGameIdRequest) returns (GetCurGameIdResponse);
    // 根据玩法id获取玩法互融互斥关系列表
    rpc GetRelationByGameID (GetRelationByGameIDRequest) returns (GetRelationByGameIDResponse);
    // 判断目标房间是否能开启指定的游戏
    rpc CanOpenGame (CanOpenGameRequest) returns (CanOpenGameResponse);
}

message EmptyResponse {

}

message RegisterRequest {
    uint32 channel_id = 1; // 房间id
    uint64 game_id = 2; // 游戏id
}

message RegisterRespone {
    bool result = 1;   // 注册结果
    repeated uint64 game_id_list = 2;  // 进行中的互斥的游戏id列表(注册失败时才有值)
}

message UnregisterRequest {
    uint32 channel_id = 1; // 房间id
    uint64 game_id = 2; // 游戏id
}

message GetCurGameIdRequest {
    uint32 channel_id = 1; // 房间id
}

message ChannelGameIdList {
    repeated uint64 game_id = 1; // 游戏id
}

message GetCurGameIdResponse {
    ChannelGameIdList list = 1; // 游戏id
}

message GetRelationByGameIDRequest {
    uint64 game_id = 1; // 游戏id
}

message GetRelationByGameIDResponse {
    repeated uint64 mutual_game_id = 1; // 互斥的游戏id
    repeated uint64 include_game_id = 2; // 可以兼容的游戏id
}

message CanOpenGameRequest {
    uint32 channel_id = 1; // 房间id
    uint64 game_id = 2; // 游戏id
}

message CanOpenGameResponse {
    bool result = 1;   // 判断结果
    repeated uint64 game_id_list = 2;  // 进行中的互斥的游戏id列表(判断失败时才有值)
}