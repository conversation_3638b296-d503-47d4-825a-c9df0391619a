syntax = "proto3";

package masterapprentice;
option go_package = "golang.52tt.com/protocol/services/masterapprentice";


service MasterApprentice {
    /* 师徒列表模块 */
    // 根据注册时间返回在线徒弟
    rpc BatchGetOnlineReserveApprentice (BatchGetOnlineReserveApprenticeReq) returns (BatchGetOnlineReserveApprenticeResp) {}
    // 根据注册时间返回在线徒弟 年龄分层 召回
    rpc BatchGetOnlineReserveApprenticeV2 (BatchGetOnlineReserveApprenticeReq) returns (BatchGetOnlineReserveApprenticeResp) {}
    // 批量删除徒弟
    rpc BatchDelReserveApprentice (BatchDelReserveApprenticeReq) returns (BatchDelReserveApprenticeResp) {}
    // 批量添加师傅
    rpc BatchAddMaster (BatchAddMasterReq) returns (BatchAddMasterResp) {}
    // 批量删师傅
    rpc BatchDelMaster (BatchDelMasterReq) returns (BatchDelMasterResp) {}
    // 是否在徒弟大厅中
    rpc IsApprentice (IsApprenticeReq) returns (IsApprenticeResp) {}
    // 是否有收徒资格
    rpc IsMaster (IsMasterReq) returns (IsMasterResp) {}
    // 是师父&在收徒时间内
    rpc IsMasterInValidTime (IsMasterInValidTimeReq) returns (IsMasterInValidTimeResp) {}
    /* 师徒列表模块 end */

    /* 师徒关系模块 */
    // 获取师父的徒弟列表 进行中的
    rpc GetApprenticesList (GetApprenticesListReq) returns (GetApprenticesListResp) {
    }
    // 获取徒弟状态
    rpc GetApprenticeStatus (GetApprenticeStatusReq) returns (GetApprenticeStatusResp) {
    }
    // 获取师父信息
    rpc GetUserMasterInfo (GetUserMasterInfoReq) returns (GetUserMasterInfoResp) {
    }
    // 批量获取师父信息
    rpc BatchGetUserMasterInfo (BatchGetUserMasterInfoReq) returns (BatchGetUserMasterInfoResp) {
    }
    // 发出邀请
    rpc MasterInvite (MasterInviteReq) returns (MasterInviteResp) {
    }
    // 建立师徒关系 -- 徒弟点击接受邀请
    rpc EstablishShip (EstablishShipReq) returns (EstablishShipResp) {
    }
    // 获取师父的绑定的徒弟数(进行中)
    rpc GetApprenticeNumByMaster (GetApprenticeNumByMasterReq) returns (GetApprenticeNumByMasterResp) {
    }

    // 今日已完成
    rpc GetTodayCompleted (GetTodayCompletedReq) returns (GetTodayCompletedResp) {
    }

    // 当天的新鲜师父:现在是领取成师礼包当天
    rpc IsNewMaster (IsNewMasterReq) returns (IsNewMasterResp) {
    }

    // 针对同一个用户两次邀请发送时间<5min
    rpc SameApprentice (SameApprenticeReq) returns (SameApprenticeResp) {
    }



    /* 师徒关系模块 end */


    /*佣金模块 begin*/
    //用户提现
    rpc DrawBalance(DrawBalanceReq)returns (DrawBalanceResp){}
    //获取余额
    rpc GetUserBalanceByUserID(GetUserBalanceByUserIDReq)returns (GetUserBalanceByUserIDResp){}
    //获取其订单列表
    rpc GetOrderListByUserID(GetOrderListByUserIDReq)returns (GetOrderListByUserIDResp){}
    //获取订单信息
    rpc GetOrderInfoByUserIDOrderID(GetOrderInfoByUserIDOrderIDReq)returns (GetOrderInfoByUserIDOrderIDResp){}
    //获取订单信息 供佣金平台反查
    rpc GetOrderInfoByOrderID(GetOrderInfoByOrderIDReq)returns (GetOrderInfoByOrderIDResp){}
    //佣金平台更新佣金信息 将订单状态标记
    rpc UpdateOrderInfoByOrderID(UpdateOrderInfoByOrderIDReq)returns (UpdateOrderInfoByOrderIDResp){}
    //绑定微信信息
    rpc BindWXUserPayInfo(BindWXUserPayInfoReq)returns (BindWXUserPayInfoResp){}
    //查询微信绑定信息
    rpc GetBindingInfo(GetBindingInfoReq)returns(GetBindingInfoResp){}

    /*佣金模块 end*/

    rpc GetCurrentTask(CurrentTaskReq)returns (CurrentTaskRsp){}
    rpc GetHistoryTask(HistoryTaskReq)returns (HistoryTaskRsp){}

    // 获取师父是否第一次进师徒关系首页
    rpc MasterFirstHome(MasterFirstHomeReq)returns (MasterFirstHomeResp){}
    // 设置师父第一次进师徒关系首页
    rpc InsertMasterFirstHome(InsertMasterFirstHomeReq)returns (InsertMasterFirstHomeResp){}
    rpc GetActConfig (GetActConfigReq) returns (GetActConfigResp) {}

    // web 首页数据
    rpc MasterInitForWeb(MasterInitForWebReq)returns (MasterInitForWebResp){}

    // 获取进行中的师傅的所有徒弟信息
    rpc GetAllApprenticeInfosInProcess(GetAllApprenticeInfosInProcessReq)returns (GetAllApprenticeInfosInProcessResp){}

    // 成师礼包
    rpc ReceiveGift(ReceiveGiftReq)returns (ReceiveGiftResp){}
    // 监控 师父的收益和提现
    rpc MasterMonitorInToday(MasterMonitorInTodayReq)returns (MasterMonitorInTodayResp){}
    // 活动数据统计（活动成本和师父收益）
    rpc GetActivityStatistics(GetActivityStatisticsReq) returns (GetActivityStatisticsResp){}

    // 完成首日师徒任务
    rpc FinishFirstDayTask(FinishFirstDayTaskReq) returns (FinishFirstDayTaskResp){}
}



/* 师徒列表模块 */
// 返回注册时间大于等于begin且生日小于等于最小生日的徒弟
message BatchGetOnlineReserveApprenticeReq{
    uint32 begin = 1;
    uint32 limit = 2;
    uint32 min_birthday = 3; // 最小的生日日期
    uint32 uid = 4;//师傅uid
}
message BatchGetOnlineReserveApprenticeResp{
    repeated uint32 uids = 1;
}

message BatchDelReserveApprenticeReq{
    repeated uint32 uids = 1;
}
message BatchDelReserveApprenticeResp{}

message BatchAddMasterReq{
    repeated uint32 uids = 1;
}
message BatchAddMasterResp{}

message BatchDelMasterReq{
    repeated uint32 uids = 1;
}
message BatchDelMasterResp{}

message IsApprenticeReq{
    uint32 uid = 1;
}
message IsApprenticeResp{
    bool is_apprentice = 1;
}

message IsMasterReq{
    uint32 uid = 1;
}
message IsMasterResp{
    bool is_master = 1;
}

// 是师父&在收徒时间内
message IsMasterInValidTimeReq{
    uint32 uid = 1;
}
message IsMasterInValidTimeResp{
    bool is_master = 1;
}

/* 师徒列表模块 end */

/* 师徒关系模块 */
// 师徒角色
//enum UserMasterApprenticeRole{
//    DEFAULT = 0;
//    MASTER = 1; // 师父
//    APPRENTICE = 2; // 徒弟
//}
//message UserMasterApprenticeInfo{
//    UserMasterApprenticeRole role = 1;
//    uint32 uid = 2;
//    uint32 master_uid = 3;
//    repeated uint32 apprentices_uid = 4;
//}

// 获取师父的徒弟列表 进行中的
message GetApprenticesListReq{
    uint32 uid = 1;
}
message GetApprenticesListResp{
    repeated uint32 apprentices_uid_list = 4; // 进行中的徒弟列表
}

// 收徒状态
enum ApprenticeStatus{
    INVITING = 0; // 邀请中
    IN_PROCESS = 1; // 进行中
    COMPLETED = 2; // 已完成
    TERMINATION = 3; // 终止
}

// 师徒任务当天进度
//enum TodayMissionProcess{
//    MISSION_IN_PROCESS = 0;
//    MISSION_COMPLETED = 1;
//}

// 获取徒弟状态
message GetApprenticeStatusReq{
    uint32 uid = 1;
    uint32 apprentice_uid = 2;
}

message GetApprenticeStatusResp{
    uint32 uid = 1;
    uint32 apprentice_uid = 2;
    ApprenticeStatus status = 3;
    //    TodayMissionProcess mission = 4;
}

// 获取非邀请中的师父信息
message GetUserMasterInfoReq{
    uint32 apprentice_uid = 1;
}

message GetUserMasterInfoResp{
    uint32 master_uid = 2;
}

// 批量获取非邀请中的师父信息
message BatchGetUserMasterInfoReq{
    repeated uint32 apprentice_uids = 1;
}

message BatchGetUserMasterInfoResp{
    map<uint32,uint32> apprentice_to_master = 2;
}

// 发出邀请
message MasterInviteReq{
    uint32 master_uid = 1;
    uint32 apprentice_uid = 2;
    string apprentice_nickname = 3;
}
message MasterInviteResp{
    uint32 invite_cnt = 1; // 该徒弟被师父邀请过几次，用于首次弹文字链
}

// 建立师徒关系 -- 徒弟点击接受邀请
message EstablishShipReq{
    uint32 master_uid = 1;
    uint32 apprentice_uid = 2;
}
message EstablishShipResp{
    bool result = 1;
}

// 获取师父的绑定的徒弟数(进行中)
message GetApprenticeNumByMasterReq{
    uint32 master_uid = 1;
}
message GetApprenticeNumByMasterResp{
    uint32 cnt = 2;
}

// 今日已完成
message GetTodayCompletedReq{
    uint32 master_uid = 1;
}
message GetTodayCompletedResp{
    repeated uint32 apprentice_uid_list = 1;
}


// 当天的新鲜师父:现在是领取成师礼包当天
message IsNewMasterReq{
    uint32 master_uid = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message IsNewMasterResp{
   bool isNew = 1;
}


/* 师徒关系模块 end */


/* 佣金模块 begin */

message DrawBalanceReq {
    uint32 user_id = 1;
    int64 draw_balance = 2;
}

message DrawBalanceResp {
    string message = 1;
    string order_id = 2;
}


message GetUserBalanceByUserIDReq {
    uint32 user_id = 1;
}



message GetUserBalanceByUserIDResp {
    int64 total_balance = 1;
    int64 available_balance = 2;//
    int64 unavailable_balance = 3;//目前不可用的
    int64 status  = 4;//status 1 为正常状态 2为提现中 3为冻结
}


message OrderInfo {
    uint32 user_id = 1;
    string order_id = 2;
    int64 status  = 3;//提现订单 1初始化 2提现中 3提现成功 4提现失败
    int64 now_balance = 4; //创建订单前的余额
    int64 amount = 5;
    int64 type = 6;
    string describe = 7;
    string reason = 8;
    int64 create_time = 9;
    int64 update_time = 10;
}



message GetOrderListByUserIDReq {
    uint32 user_id = 1;
    int64 limit = 2;
    int64 last_id = 3;
}

message LoadMore {
    bool has_next = 1;
    int64 last_id = 2;
    int64 limit = 3;
}

message GetOrderListByUserIDResp {
    uint32 user_id = 1;
    repeated OrderInfo order_list  = 2;
    LoadMore load_more = 3;
}


message GetOrderInfoByUserIDOrderIDReq {
    uint32 user_id = 1;
    string order_id = 2;
}

message GetOrderInfoByUserIDOrderIDResp {
    OrderInfo order_info  = 1;
}

message GetOrderInfoByOrderIDReq {
    string order_id = 1;
}

message GetOrderInfoByOrderIDResp {
    OrderInfo order_info  = 1;
}

message UpdateOrderInfoByOrderIDReq {
    string order_id = 1;
    int64 status = 2;
    string reason = 3;
}

message UpdateOrderInfoByOrderIDResp {
    string message = 1;
}


message BindWXUserPayInfoReq {
    uint32 user_id = 1;
    string code = 2;
    string state = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BindWXUserPayInfoResp {
    string status = 1;//绑定状态, 若为 UNBANDIND 则为未绑定微信OPENID, 若为 BANDING 则已绑定微信OPENID
    string openId = 2;
    string nick_name = 3;
    string message = 4;
}

message GetBindingInfoReq {
    uint32 user_id = 1;
    string code = 2;
    string state = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetBindingInfoResp {
    string status = 1;//绑定状态, 若为 UNBANDIND 则为未绑定微信OPENID, 若为 BANDING 则已绑定微信OPENID
    string openId = 2;
    string nick_name = 3;
    string message = 4;
}


/* 佣金模块 end*/

enum HistoryTaskType{
    MissionFinish = 0;
    MissionUnvalid = 1;
}

message HistoryTaskReq {
    HistoryTaskType task_type = 1;
    uint32 offset = 2;
    uint32 limit = 3;
    uint32 master_uid = 4;
}

message HistoryTaskInfo {
    uint32 uid = 1;
    string nickname = 2;
    string account = 3;
    int64 income = 4;
    uint32 gender = 5;
    uint32 day = 6;
}

message HistoryTaskRsp {
    repeated HistoryTaskInfo task_info = 1;
    uint32 complete_cnt = 2;
    uint32 unvalid_cnt = 3;
}


message CurrentTaskReq {
    uint32 offset = 1;
    uint32 limit = 2;
    uint32 master_uid = 3;
}

message CurrentTaskInfo {
    uint32 uid = 1;
    string nickname = 2;
    string account = 3;
    int64 income = 4;
    uint32 day = 5;
    uint32 status = 6;
    uint32 gender = 7;
}

message CurrentTaskRsp {
    repeated CurrentTaskInfo task_info = 1;
}


// 获取师父是否第一次进师徒关系首页
message MasterFirstHomeReq{
    uint32 master_uid = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message MasterFirstHomeResp{
    bool isFirst = 1;
}

// 设置师父第一次进师徒关系首页
message InsertMasterFirstHomeReq{
    uint32 master_uid = 1;
}
message InsertMasterFirstHomeResp{
}

message GetActConfigReq{}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetActConfigResp{
    string begin_time = 1; // 活动开始时间
    string end_time = 2; // 活动结束时间
    string EndGetApprenticeTime = 3; // 收徒弟结束时间
    string EndTaskTime = 4; // 带徒弟结束时间
}

// web 首页数据
message MasterInitForWebReq{
    uint32 master_uid = 1;
}
message MasterInitForWebResp{
    uint32 first_entry = 1;
    uint32 recruit_time = 2;
    uint32 withdrawable_income = 3;
    uint32 total_income = 4;
    uint32 available_num = 5;
    uint32 additional_income = 6;
    uint32 today_income = 7; // 获取师父今日已赚
}

// 获取进行中的师傅的所有徒弟信息
message GetAllApprenticeInfosInProcessReq{
    uint32 master_uid = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ApprenticeInfo{
    uint32 uid = 1;
    ApprenticeStatus total_status = 2;
    uint32 Day = 3;
}
message GetAllApprenticeInfosInProcessResp{
    repeated ApprenticeInfo info_list = 1;
}


// 成师礼包
message ReceiveGiftReq{
    uint32 uid = 1;
}
message ReceiveGiftResp{
}

// 监控 师父的收益和提现
message MasterMonitorInTodayReq{
    uint32 limit_income = 1;
    uint32 limit_withdraw = 2;
    int64 check_ts = 3;
}
message MasterLimitInfo{
    uint32 master_uid = 1;
    uint32 amount = 2;
}
message MasterMonitorInTodayResp{
    repeated MasterLimitInfo exceed_income_masters = 1;
    repeated MasterLimitInfo exceed_withdraw_masters = 2;
}

// 活动数据统计（活动成本和师父收益）
message GetActivityStatisticsReq{
    string date = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ActivityStatistics{
    int64 TotalBalance = 1;     // 累计发放金额
    int64 TotalWithdraw = 2;    // 累计成功提现金额
    int64 AvailableBalance = 3; // 累计余额
    int64 AwardInDate = 4;      // 今日发放金额
    int64 WithdrawInDate = 5;   // 今日提现金额
    int64 AvgAward = 6;         // 师父累计平均收益
    int64 AvgAwardInDate = 7;   // 师父今日平均收益
}
message GetActivityStatisticsResp{
    ActivityStatistics statistics = 1;
}

// 针对同一个用户两次邀请发送时间<5min
message SameApprenticeReq{
    uint32 master_uid = 1;
    uint32 apprentice_uid = 2;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SameApprenticeResp{
    bool isSame = 1;
}

// 完成首日师徒任务
message FinishFirstDayTaskReq{
    uint32 master_id = 1;
    uint32 apprentice_id = 2;
    int64 datetime = 3;
}
message FinishFirstDayTaskResp{}