syntax = "proto3";
package tt_image_converter;

option go_package = "golang.52tt.com/protocol/services/tt_image_converter";



service TtImageConverter {

  // 图片转码
  rpc ImageConvert(ImageConvertReq) returns (ImageConvertResp){
  }
}


message ImageDpi
{
	uint32 wide = 3;  // 宽度
	uint32 len  = 4;  // 长度
	uint32 max_wide_or_len = 5;       // 最大长宽
}




// 图片转码
message ImageConvertReq
{
	bytes original_image = 1;     // 图片源数据
	
	string   target_image_type = 2; // 目标图片类型 webp, gif, jpeg, png
	ImageDpi target_dpi = 3;        // 长宽信息 如果全部为0表示 不做尺寸变化
	
	uint32 quality = 4;
}


message ImageConvertResp
{
	string   original_image_type = 1; // 转换之前的图片类型 webp, gif, jpeg, png
	ImageDpi original_dpi = 2;      // 转换之前的长宽信息
	
	bytes  target_image = 3;        // 转换之后的图片数据
}


