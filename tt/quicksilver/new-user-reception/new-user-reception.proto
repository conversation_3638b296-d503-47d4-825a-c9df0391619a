syntax = "proto3";

package new_user_reception;
option go_package = "golang.52tt.com/protocol/services/new-user-reception";

service NewUserReception {
  // 获取接新团用户白名单信息
  rpc GetUserReceptionGroupInfoByUid(GetUserReceptionGroupInfoByUidReq) returns (GetUserReceptionGroupInfoByUidResp);
  // 判断用户是否符合接新团新用户条件
  rpc IsUserInUserReceptionCond(IsUserInUserReceptionCondReq) returns (IsUserInUserReceptionCondResp);

  rpc TestPush(TestPushReq) returns (TestPushResp);

  // 运营后台
  rpc AddUserReceptionGroupWhitelist(AddUserReceptionGroupWhitelistReq) returns (AddUserReceptionGroupWhitelistResp);
  rpc GetUserReceptionGroupWhitelist(GetUserReceptionGroupWhitelistReq) returns (GetUserReceptionGroupWhitelistResp);
}

message GetUserReceptionGroupInfoByUidReq{
  uint32 uid = 1;
}

message GetUserReceptionGroupInfoByUidResp{
  UserReceptionGroupWhitelistItem info = 1;    // info为空表明该用户不在白名单中
}

message IsUserInUserReceptionCondReq{
  uint32 uid = 1;
  uint32 market_id = 2;
  string device_id = 3;
}

message IsUserInUserReceptionCondResp{
  bool is_in_cond = 1;
}


message AddUserReceptionGroupWhitelistReq{
  repeated uint32 uids = 1;
}

message AddUserReceptionGroupWhitelistResp{

}

message GetUserReceptionGroupWhitelistReq{
  uint32 page = 1;
  uint32 page_size = 2;

}

message UserReceptionGroupWhitelistItem {
  uint32 uid = 1;
  int64 update_ts = 2; // ms
}

message GetUserReceptionGroupWhitelistResp{
  repeated UserReceptionGroupWhitelistItem items = 1;
  uint32 total_num = 2; // 名单总数
}

message TestPushReq{
  uint32 uid = 1;
  uint32 cid = 2;
}

message TestPushResp{

}

