syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/esport_internal";

package esport_internal;

import "tt/quicksilver/esport-trade/esport-trade.proto";

service EsportInternal {
    // 批量分类优惠券
    rpc BatClassifyCoupon (BatClassifyCouponRequest) returns (BatClassifyCouponResponse);
    // 按uid列表 获取券后价格
    rpc GetCouponPriceByUid (GetCouponPriceByUidRequest) returns (GetCouponPriceByUidResponse);
    // 按技能列表 获取券后价格
    rpc GetCouponPriceBySkill (GetCouponPriceBySkillRequest) returns (GetCouponPriceBySkillResponse);
    // 获取同一个技能的一批大神的的价格信息
    rpc BatchGetCoachPricingInfo (BatchGetCoachPricingInfoRequest) returns (BatchGetCoachPricingInfoResponse);
    // 获取同一个大神的一批技能的的价格信息
    rpc BatchGetSkillPricingInfo (BatchGetSkillPricingInfoRequest) returns (BatchGetSkillPricingInfoResponse);

    // 查询指定用户是否在指定人群包中
    rpc CheckUserCrowdPackages (CheckUserCrowdPackagesRequest) returns (CheckUserCrowdPackagesResponse);
}


enum DiscountType {
    DISCOUNT_TYPE_UNSPECIFIED = 0; // 没有优惠
    DISCOUNT_TYPE_FIRST_ORDER = 1; // 首单优惠
    DISCOUNT_TYPE_COUPON = 2; // 优惠券
    DISCOUNT_TYPE_NEW_CUSTOMER = 3; // 新用户优惠
}

message BatchGetCoachPricingInfoRequest {
    uint32 uid = 1;
    repeated uint32 coach_uid = 2;
    uint32 game_id = 3;
    uint32 buy_amount = 4; // 购买数量
    CouponQueryOption query_option = 5; // 查询选项
}

message BatchGetCoachPricingInfoResponse {
    map<uint32, PriceInfo> price_map = 1; // key: coach_uid，value: 价格信息
}

message BatchGetSkillPricingInfoRequest {
    uint32 uid = 1;
    uint32 coach_uid = 2;
    repeated uint32 game_id = 3;
    uint32 buy_amount = 4; // 购买数量
    CouponQueryOption query_option = 5; // 查询选项
}

message BatchGetSkillPricingInfoResponse {
    map<uint32, PriceInfo> price_map = 1; // key: game_id，value: 价格信息
}

message DiscountInfo {
    DiscountType type = 1;
    uint32 price = 2; // 优惠的价格，例如 4 表示 -4 优惠4元
    string desc = 3; // 有关优惠的描述
}

message PriceInfo {
    uint32 coach_id = 1;
    uint32 game_id = 2;
    uint32 price = 3;
    string unit = 4;
    uint32 measure_cnt = 5;
    string measure_unit = 6;
    repeated DiscountInfo discount_list = 7;
}

message CouponQueryOption {
    bool do_not_check_risk = 1; // 不检查风控
    bool do_not_check_version = 2; // 不检查版本
    bool do_not_check_player_everyday_count = 3; // 不检查玩家每日使用次数
    bool do_not_check_coach_everyday_count = 4; // 不检查教练每日使用次数
}

message BatClassifyCouponRequest {
    uint32 player_uid = 1;
    uint32 coach_uid = 2;
    uint32 game_id = 3;
    uint32 original_price = 4; // 原价
    uint32 min_buy_amount = 5; // 最小购买数量
    uint32 max_buy_amount = 6; // 最大购买数量
    CouponQueryOption query_option = 7; // 查询选项
}

message BatClassifyCouponResponse {
    map<uint32, ClassifyCouponResponse> result_map = 1; // key: buy_amount
    bool can_use_coupon_switch = 2; // 是否可以使用优惠券开关
}

message ClassifyCouponResponse {
    uint32 buy_amount = 1; // 购买数量
    repeated ClassifyCouponResult available_list = 2; // 可用列表
    repeated ClassifyCouponResult unavailable_list = 3; // 不可用列表
}

message ClassifyCouponResult {
    esport_trade.Coupon coupon = 1;
    string reason = 2; // 原因
}

message GetCouponPriceByUidRequest {
    uint32 player_uid = 1;
    uint32 game_id = 2;
    uint32 buy_amount = 3; // 购买数量
    CouponQueryOption query_option = 4; // 查询选项
    repeated CouponPriceCoachInfo coach_list = 5;
}
message CouponPriceCoachInfo {
    uint32 coach_uid = 1;
    uint32 original_price = 2; // 原价
}

message GetCouponPriceByUidResponse {
    map<uint32, CouponPriceResult> result_map = 1; // key: coach_uid
}

message GetCouponPriceBySkillRequest {
    uint32 player_uid = 1;
    uint32 coach_uid = 2;
    uint32 buy_amount = 3; // 购买数量
    CouponQueryOption query_option = 4; // 查询选项
    repeated CouponPriceGameInfo game_list = 5;
}
message CouponPriceGameInfo {
    uint32 game_id = 1;
    uint32 original_price = 2; // 原价
}

message GetCouponPriceBySkillResponse {
    map<uint32, CouponPriceResult> result_map = 1; // key: game_id
}

message CouponPriceResult {
    uint32 coach_uid = 1;
    uint32 game_id = 2;
    bool has_price_with_coupon = 3; // 是否有券后价格
    uint32 price_with_coupon = 4; // 券后价格
    esport_trade.Coupon coupon = 5; // 使用的优惠券
}


message CheckUserCrowdPackagesRequest {
    uint32 uid = 1;// 用户uid
    repeated string crowd_package_ids = 2;// 人群包id
}

message CheckUserCrowdPackagesResponse {
    repeated string crowd_package_ids = 1;// 人群包id
}
