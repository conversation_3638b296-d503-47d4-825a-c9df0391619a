syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/muse-listening-rank";
package muse_listening_rank;

service MuseListeningRank {
  rpc GetUserInfo(GetUserInfoReq) returns(GetUserInfoResp);
  rpc GetUserRankInSchool(GetUserRankInSchoolReq) returns(GetUserRankInSchoolResp);
  rpc GetSchoolRank(GetSchoolRankReq) returns(GetSchoolRankResp);
  rpc GetMirrorId(GetMirrorIdReq)returns(GetMirrorIdResp);
  rpc GetMirrorData(GetMirrorDataReq)returns(GetMirrorDataResp);
  rpc AddFlower(AddFlowerReq)returns(AddFlowerResp);
  rpc ListChannelWithSchool(ListChannelWithSchoolReq)returns(ListChannelWithSchoolResp);
  rpc UpdateChannelSchoolBind(UpdateChannelSchoolBindReq)returns(UpdateChannelSchoolBindResp);
  rpc DelChannelSchoolBind(DelChannelSchoolBindReq)returns(DelChannelSchoolBindResp);
  rpc PreChannelSchoolBind(PreChannelSchoolBindReq)returns(PreChannelSchoolBindResp);
  rpc ListSchool(ListSchoolReq)returns(ListSchoolResp);
  rpc UpdateSchools(UpdateSchoolsReq)returns(UpdateSchoolsResp);
  rpc Geo(GeoReq)returns(GeoResp);
}

message GeoReq{
}

message GeoResp{
  repeated Province province = 1;
}

message Province{
  string code = 1;
  string name = 2;
  repeated City city = 3;
}

message City{
  string code = 1;
  string name = 2;
}

message ListSchoolReq{
  string school_name = 1;//模糊匹配
}

message ListSchoolResp{
  repeated ChannelSchool schools = 1;
}

message PreChannelSchoolBindReq{
  repeated string ttids = 1;
}

message PreChannelSchoolBindResp{
  repeated ListChannelSchoolItem item = 1;
}

message DelChannelSchoolBindReq{
  string id = 1;//需要修改的item的id必填
}

message DelChannelSchoolBindResp{
}

message UpdateChannelSchoolBindReq{
  repeated ChannelSchoolBind channel_schools = 1;
}

message UpdateChannelSchoolBindResp{

}

message ChannelSchoolBind{
  string id = 1;//需要修改的item的id必填

  string ttid = 2;
  string school_id = 3;
}
message UpdateSchoolsReq{
  repeated ChannelSchool schools = 1;
}

message UpdateSchoolsResp{
  repeated string failed_school_name = 1;
}

message ListChannelWithSchoolReq{
  string ttid = 1;//完全匹配
  uint32 channel_id = 2;//完全匹配
  string channel_name = 3;//模糊匹配
  string school_name = 4;//模糊匹配
  string offset_id = 5;//上一页最后item
  uint32 limit = 6;
}

message ListChannelWithSchoolResp{
  repeated ListChannelSchoolItem item = 1;
}

message ListChannelSchoolItem {
  string id = 1;
  string ttid = 2;
  uint32 channel_id = 3;
  string channel_name = 4;
  ChannelSchool school = 5;
}

message ChannelSchool{
  string id = 1;
  string school_id = 2;
  string school_name = 3;
  string city_id = 4;
  string city_name = 5;
  string province_id = 6;
  string province_name = 7;
  string school_code = 8;
  bool is_binded = 9;
}

message AddFlowerReq{
  uint32 uid = 1;
  int64 flower = 2;
}

message AddFlowerResp{
  uint32 uid = 1;
  int64 flower = 2;
}

message GetUserInfoReq{
  uint32 uid = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetUserInfoResp{
  uint32 uid = 1;
  string name = 2;
  string account = 3;
  int64 flower = 4;
  School school = 5;
  int32 user_rank_in_school = 6;
  int32 school_rank_in_city = 7;
  int32 school_rank_in_country = 8;
  uint32 channelId = 9;
}

message School {
  string id = 1;
  string name = 2;
  string img = 3;
  string city_id = 4;
  string city_name = 5;
  string real_name = 6;
  string phone = 7;
  repeated string source_list = 8;
  string obj_id = 9;
  string school_code = 10;
}

message GetUserRankInSchoolReq{
  uint32 uid = 1;
}

message UserWithFlower{
  uint32 uid = 1;
  string name = 2;
  string account = 3;
  School school = 4;
  int64 flower = 5;
}

message GetUserRankInSchoolResp{
  repeated UserWithFlower users = 1;
}

message GetSchoolRankReq{
  uint32 uid = 1;
  Level level = 2;
  enum Level{
    level_city = 0;
    level_country = 1;
  }
}

message GetSchoolRankResp{
  repeated SchoolWithFlower schools = 1;
}

message SchoolWithFlower {
  string id = 1;
  string name = 2;
  string img = 3;
  string city_id = 4;
  string city_name = 5;
  int64 flower = 6;
}

message GetMirrorIdReq{
  uint32 uid = 1;
}

message GetMirrorIdResp{
  string mirror_id = 1;
}

message GetMirrorDataReq{
  string mirror_id = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetMirrorDataResp{
  uint32 uid = 1;
  string name = 2;
  string account = 3;
  int64 flower = 4;
  School school = 5;
  int32 user_rank_in_school = 6;
  int32 school_rank_in_city = 7;
  int32 school_rank_in_country = 8;
  uint32 channelId = 9;

  repeated UserWithFlower users = 10;
  repeated SchoolWithFlower schools_in_city = 11;
  repeated SchoolWithFlower schools = 12;
}
