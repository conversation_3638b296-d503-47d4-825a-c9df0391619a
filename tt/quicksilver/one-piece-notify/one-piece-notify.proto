syntax = "proto3";

package one_piece_notify;
import "tt/quicksilver/extension/options/options.proto";
option go_package = "golang.52tt.com/protocol/services/one-piece-notify";

// //1：奖池下架提醒、2：奖池上新提醒
// enum NotifyType{
//     All_Type = 0;
//     Prize_Pool_Off = 1;
//     Prize_Pool_Update = 2;
// }

message NotifyInfo{
    uint32 notify_type = 1;         // see NotifyType, 废弃
    uint32 begin_time = 2;          // 提醒开始时间
    uint32 end_time = 3;            // 提醒结束时间

    uint32 has_floating = 4;        // 是否开启房间浮层提示
    string floating_text = 5;       // 房间浮层文案
    uint32 floating_duration = 6;   // 房间浮层显示时长

    uint32 has_public = 7;          // 是否开启公屏提示
    string public_text = 8;         // 公屏文案
    string public_color = 9;        // 公屏文字颜色

    uint32 has_red = 10;            // 是否有红点提示

    uint32 has_homepage = 11;       // 是否开启航海页浮层提示
    string home_text = 12;          // 提示文案
    uint32 htext_duration = 13;     // 航海页浮层显示时长
    
    // uint32 has_mileage = 14;        // 是否开启航海里程浮层提示
    // string mileage_text = 15;       // 提示文案
    // uint32 mtext_duration = 16;     // 航海里程浮层显示时长
}

message SetNotifyInfoReq{
    uint32 notify_type = 1;     //see NotifyType，废弃，默认值即可
    NotifyInfo notify_info = 2;
}

message SetNotifyInfoResp{
}

message GetNotifyInfoReq{
    uint32 notify_type = 1;     //see NotifyType，废弃，默认值即可
}

message GetNotifyInfoResp{
    NotifyInfo notify_info = 1;
}

message GetCurrentNotifyReq{
    uint32 notify_type = 1;
}

message GetCurrentNotifyResp{
    NotifyInfo notify_info = 1;
    uint32 ctime = 2;
}

service OnePieceNotify {
    option (service.options.service_ext) = {
        service_name: "one-piece-notify"
      };
    // =============================== 航海寻宝相关浮层配置 ========================
    rpc SetNotifyInfo(SetNotifyInfoReq) returns (SetNotifyInfoResp) {}

    rpc GetNotifyInfo(GetNotifyInfoReq) returns (GetNotifyInfoResp) {}

    rpc GetCurrentNotify(GetCurrentNotifyReq) returns (GetCurrentNotifyResp) {}
}