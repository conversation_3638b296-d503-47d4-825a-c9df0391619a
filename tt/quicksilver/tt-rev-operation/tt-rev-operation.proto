syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/tt-rev-operation";
package tt_rev_operation;

// 营收互动组公共运营后台服务
service TTRevOperation {
  // 获取房间玩法权限（perm->permission）
  rpc BatchGetChannelGameplayPerm(BatchGetChannelGameplayPermReq) returns (BatchGetChannelGameplayPermResp) {}
  // 批量修改房间玩法权限
  rpc BatchOperateChannelGameplayPerm(BatchOperateChannelGameplayPermReq) returns (BatchOperateChannelGameplayPermResp) {}

  // 获取房间当前拥有的玩法权限
  rpc GetChannelAllGameplay(GetChannelAllGameplayReq) returns (GetChannelAllGameplayResp) {}

  // 批量获取房间PK权限
  rpc BatGetChannelPKPerm(BatGetChannelPKPermReq) returns (BatGetChannelPKPermResp) {}
  // 批量修改房间PK权限
  rpc BatOperateChannelPKPerm(BatOperateChannelPKPermReq) returns (BatOperateChannelPKPermResp) {}
  // 判断是否有房间PK权限
  rpc HasChannelPKPerm(HasChannelPKPermReq) returns (HasChannelPKPermResp) {}

  // 批量获取房间天配玩法权限
  rpc BatchGetDoomedMatchChannelPerm(BatchGetDoomedMatchChannelPermReq) returns (BatchGetDoomedMatchChannelPermResp) {}
  // 批量修改房间天配玩法权限
  rpc BatchOperateDoomedMatchChannelPerm(BatchOperateDoomedMatchChannelPermReq) returns (BatchOperateDoomedMatchChannelPermResp) {}
  // 判断是否有天配玩法权限
  rpc HasDoomedMatchPerm(HasDoomedMatchPermReq) returns (HasDoomedMatchPermResp) {}

  // 移动端获取推荐列表
  rpc GetRecommendedChannel(GetRecommendedChannelReq) returns (GetRecommendedChannelResp) {}
  // 运营后台新增房间（批量or单个）
  rpc AddRecommendedChannel(AddRecommendedChannelReq) returns (AddRecommendedChannelResp) {}
  // 运营后台删除房间 (批量or单个)
  rpc RemoveRecommendedChannel(RemoveRecommendedChannelReq) returns(RemoveRecommendedChannelResp) {}
  // 运营后台查询推荐 记录 废弃
  rpc GetRecommendedChannelRecord(GetRecommendedChannelRecordReq) returns(GetRecommendedChannelRecordResp) {}
  //运营后台 分页 查询推荐 记录
  rpc GetRecommendedChannelRecordByPage(GetRecommendedChannelRecordByPageReq) returns(GetRecommendedChannelRecordByPageResp) {}

  // 娱乐玩法道具有效期查询
  rpc GetChanceGamePropOptions(GetChanceGamePropOptionsReq) returns(GetChanceGamePropOptionsResp) {}
  rpc GetUserProbGamePropDetail(GetUserProbGamePropDetailReq) returns (GetUserProbGamePropDetailResp) {}
}

// ================ 房间游戏玩法权限管理 ==================
enum ChannelGameplay {
  CHANNEL_GAME_PLAY_UNKNOW = 0;
  CHANNEL_GAME_PLAY_RID_LANDMINE = 1; // 甩雷
}

message ChannelGameplayPerm {
  uint32 channel_id = 1;
  uint32 tag_id = 2;
  ChannelGameplay gameplay = 3;
}

message BatchGetChannelGameplayPermReq {
  repeated ChannelGameplayPerm search_options = 1;
}

message BatchGetChannelGameplayPermResp {
  repeated uint32 has_entry_channel = 1; // 返回有权限的channelId
}

enum OperationType {
  UNKNOWN = 0;
  GRANT_PERM = 1;
  TAKE_BACK_PERM = 2;
}

message BatchOperateChannelGameplayPermReq {
  OperationType operation_type = 1;
  repeated ChannelGameplayPerm operations = 2;
}

message BatchOperateChannelGameplayPermResp {
}
// ================ 房间游戏玩法权限管理 ==================


// 获取房间当前拥有的玩法权限
message GetChannelAllGameplayReq {
  uint32 channel_id = 1;
  uint32 tag_id = 2;
}

message GetChannelAllGameplayResp {
  repeated uint32 gameplay_id_list = 1;
}


// =====================跨房pk权限===================
message ChannelPKPermInfo {
  string view_id = 1;
  string guild_name = 2;
  uint32 guild_id = 3;
  uint32 guild_short_id = 4;
  uint32 status = 5; // 1.权限生效, 2.权限回收
  uint32 effect_time_start = 6; // 生效时间始末
  uint32 effect_time_end = 7;
}

message BatGetChannelPKPermReq {
  repeated string view_id_list = 1;
}

message BatGetChannelPKPermResp {
  repeated ChannelPKPermInfo perm_list = 1;
}

enum ChannelPKPermOp {
    ChannelPKPermOpUnknown = 0;
    ChannelPKPermOpGrant = 1; // 发放权限, 恢复权限
    ChannelPKPermOpRecycle = 2; // 回收权限
}

message BatOperateChannelPKPermReq {
  repeated string view_id_list = 1;
  uint32 op = 2; // 见 ChannelPKPermOp
  uint32 effect_time_start = 3; // 生效时间始末
  uint32 effect_time_end = 4;
}

message BatOperateChannelPKPermResp {
}

message HasChannelPKPermReq {
  repeated uint32 channel_id = 1;
}

message HasChannelPKPermResp {
  map<uint32, bool> perm_map = 2;
}

// ==================天配房权限=================
message DoomedMatchChannelPerm {
  string view_id = 1;
  string guild_name = 2;
  uint32 guild_id = 3;
  uint32 guild_short_id = 4;
  bool has_perm = 5;
}

message BatchGetDoomedMatchChannelPermReq {
  repeated string view_id_list = 1;
}

message BatchGetDoomedMatchChannelPermResp {
  repeated DoomedMatchChannelPerm perm_list = 1;
}

enum DoomedMatchChannelPermOp {
    DoomedMatchChannelPermUnknown = 0;
    DoomedMatchChannelPermGrant = 1;
    DoomedMatchChannelPermRecycle = 2;
}

message BatchOperateDoomedMatchChannelPermReq {
  repeated string view_id_list = 1;
  uint32 op = 2; // 权限操作类型, 见 DoomedMatchChannelPermOp
}

message BatchOperateDoomedMatchChannelPermResp {
}

message HasDoomedMatchPermReq {
  uint32 channel_id = 1;
}

message HasDoomedMatchPermResp {
  bool has_perm = 1;
}

// ================================= 音乐推荐房间 ============================

// 移动端获取推荐列表
message GetRecommendedChannelReq {
  uint32 uid = 1;
  uint32 channel_id = 2;
  uint32 tab_id = 3;
  uint32 page_size = 4; // 默认20
  string page_token = 5;  // 分页token，首页为空
}

message GetRecommendedChannelResp {
  repeated uint32 channel_ids = 1; // 房间id列表
  string next_page_token = 2; // 下一页token，非空则还有下页数据，下次请求带上
}

// 添加推荐房间
message AddRecommendedChannelReq {
  repeated uint32 channel_ids = 1;  // 不大于50条
}

message AddRecommendedChannelResp {
}

// 删除推荐房间
message RemoveRecommendedChannelReq {
  repeated uint32 channel_ids = 1;
}

message RemoveRecommendedChannelResp {

}

// 查询房间推荐记录
message GetRecommendedChannelRecordReq {
  string channel_view_id = 1;  // 为空则返回全量
}

message RecommendedChannelRecord {
  string channel_view_id = 1; // 房间viewId
  string channel_name = 2; // 房间名称
  int64 create_ts = 3; // 创建时间戳
  uint32 channel_id = 4; // 房间id
}

message GetRecommendedChannelRecordResp {
  repeated RecommendedChannelRecord record_list = 1; // 记录列表
}

message GetRecommendedChannelRecordByPageReq {
  repeated string channel_view_ids = 1;  // 通过id列表查询（可选）
  uint32 page_size = 2; // 分页大小
  uint32 page_num = 3; // 当前页数，从0开始
}

message GetRecommendedChannelRecordByPageResp {
  repeated RecommendedChannelRecord record_list = 1; // 记录列表
  uint32 count = 2; // 记录总数
}

// 娱乐玩法道具查询相关
message PropConf {
    uint32 prop_id = 1;
    string prop_name = 2;
}

message ProbItemConfig {
    uint32 game_id = 1; // 玩法id
    string game_name = 2; // 玩法名称
    repeated PropConf prop_conf_list = 3; // 可选道具配置列表
}

// 获取娱乐玩法道具配置
message GetChanceGamePropOptionsReq {
}

message GetChanceGamePropOptionsResp {
  repeated ProbItemConfig config_list = 1; // 配置列表
}

// 道具明细
message UserProbGamePropDetail{
    uint32 prop_id = 1;
    string prop_name = 2;
    uint32 num = 3;         // 过期道具数量
    int64 expire_ts = 4;    // 道具过期时间
    string game_name = 5;   // 玩法名称
}

// 获取用户道具有效期明细
message GetUserProbGamePropDetailReq {
  uint32 uid = 1;           // 必填
  uint32 game_id = 2;       // 必填
  uint32 prop_id = 3;       // 必填

  int64 begin_ts = 4;  // 开始时间，秒级时间戳
  int64 end_ts = 5;    // 结束时间，秒级时间戳

  // 分页参数
  uint32 page_size = 6;
  uint32 page_idx = 7;  // 从第一页开始
}

message GetUserProbGamePropDetailResp {
  repeated UserProbGamePropDetail detail_list = 1; // 道具明细
  uint32 total_cnt = 2;  // 总条数
}