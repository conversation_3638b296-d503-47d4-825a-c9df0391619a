syntax = "proto3";

package game_ugc_middle;

option go_package = "golang.52tt.com/protocol/services/game-ugc-middle";

service GameUgcMiddle {
    // 发布帖子
    rpc PostPost(PostPostRequest) returns(PostPostResponse) {}
    // 标记帖子附件上传完毕
    rpc MarkPostAttachmentUploaded(MarkPostAttachmentUploadedRequest) returns(MarkPostAttachmentUploadedResponse) {}
}

// BaseRequest 有些header信息，客户端和WEB格式不一样
message BaseRequest {
    // 设备ID
    string device_id = 1;
}

enum PostType {
    POST_TYPE_UNSPECIFIED = 0;
    POST_TYPE_TEXT = 1;
    POST_TYPE_IMAGE = 2;
    POST_TYPE_VIDEO = 3;
    POST_TYPE_CMS = 4;
    POST_TYPE_AUDIO = 5;
}

enum PostOrigin {
    POST_ORIGIN_UNSPECIFIED = 0;
    // 破冰
    POST_ORIGIN_ICE_BREAK = 1;
    // 活动页
    POST_ORIGIN_ACT = 2;
    // 进驻动态
    POST_ORIGIN_REGISTER = 3;
    POST_ORIGIN_KOL = 4;
    POST_ORIGIN_TGL = 5;
    POST_ORIGIN_TGL_COVER = 6;
    POST_ORIGIN_AI_RAPER = 7;
    POST_ORIGIN_AI_FOREIGN_LANGUAGE = 8;
    POST_ORIGIN_MIJING_COMMENT = 9;
    // 开黑活动发帖
    POST_ORIGIN_GAME_ACTIVITY = 10;

    POST_ORIGIN_ANCIENT = 1000;
}

enum PostVisibleScope {
    VISIBLE_SCOPE_UNSPECIFIED = 0;
    // 仅开黑专区可见
    VISIBLE_SCOPE_GAME_ZONE = 1;
}

enum GamePostOrigin {
    GAME_POST_ORIGIN_UNSPECIFIED = 0;
    GAME_POST_ORIGIN_GAME_TAB = 1;
}

enum PostPrivacy {
    POST_PRIVACY_UNSPECIFIED = 0;
    POST_PRIVACY_PRIVATE = 1;
    POST_PRIVACY_PUBLIC = 2;
}

message PostAttachment {
    enum Type {
        TYPE_UNSPECIFIED = 0;
        TYPE_IMAGE = 1;
        TYPE_GIF = 2;
        TYPE_VIDEO = 3;
        TYPE_CMS = 4;
        TYPE_AUDIO = 5;
    }

    // 附件类型
    Type type = 1;
    // 附件内容
    string content = 2;
    //给客户端自定义玩，虽然以后加也可以
    string extra_info = 3;
    // 创建时间
    uint64 create_at = 4;
    string key = 5;
    // 带水印的
    string vm_content= 6;
    // 只有type = VIDEO 才有的
    string param = 10;
    //原视频封面
    string origin_video_cover = 11;
}

message PostExtra {
    uint32 width = 1;
    uint32 heigth = 2;
}

// 投票信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message VoteInfo {
    // 投票过期策略
    enum ExpiryT {
        ExpiryNever = 0; // 从不
        ExpiryCustom = 1; // 自定义
    }

    // 投票选项内容类型
    enum OptionT {
        OptionTNone = 0;
        OptionTText = 1; // 文本
    }

    // 投票整体内容类型
    enum VoteT {
        VoteTNone = 0;
        VoteTText = 1; // 文本
    }
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    message Option {
        // 投票选项id
        string id = 1;
        // 投票选项内容类型
        OptionT optionT = 2;
        // 投票选项内容
        string content = 3;
        // 选项投票人数
        uint32 votes = 4;
    }

    // 帖子所属用户uid
    uint32 uid = 1;
    // 投票标题
    string title = 2;

    // 投票过期策略
    ExpiryT expiryT = 3;
    // 投票过期时间戳(秒)
    uint32 expired_at = 4;

    // 用户投票选择的选项id, 为空表示没有投过
    string option_id = 5;
    // 投票选项列表
    repeated Option options = 6;

    // 总投票人数
    uint32 votes = 7;

    // 投票整体内容类型
    VoteT vote_t = 8;
}

message PostPostRequest {
    BaseRequest base_req = 1;
    // 帖子类型
    PostType type = 2;
    // 帖子标题
    string title = 3;
    // 帖子内容
    string content = 4;

    // 帖子来源
    PostOrigin origin = 5;
    // 开黑帖子来源
    GamePostOrigin game_origin = 6;

    // 图片附件数量
    uint32 attachment_image_count = 7;
    // 视频附件数量
    uint32 attachment_video_count = 8;
    // 音频附件数量
    uint32 attachment_audio_count = 9;
    // 附件列表
    repeated PostAttachment predefined_attachments = 10;

    PostExtra extra = 11;
    PostPrivacy privacy = 12;
    PostVisibleScope visible_scope = 13;

    VoteInfo vote = 14;

    uint32 tab_id = 15;
    string config_tab_id = 16;
    string topic_id = 17;
}

message PostPostResponse {
    // 帖子ID
    string post_id = 2;

    // 上传图片的token
    string image_token = 3;
    // 上传视频的token
    string video_token = 4;
    // 上传音频的token
    string audio_token = 5;

    // 图片key
    repeated string image_keys = 6;
    // 视频key
    repeated string video_keys = 7;
    // 音频key
    repeated string audio_keys = 8;
}

// 附件
enum AttachmentType {
    ATTACHMENT_TYPE_NONE = 0;
    ATTACHMENT_TYPE_IMAGE = 1;
    ATTACHMENT_TYPE_GIF = 2;
    ATTACHMENT_TYPE_VIDEO = 3;
    ATTACHMENT_TYPE_CMS = 4;
    ATTACHMENT_TYPE_AUDIO = 5;
}

message UploadAttachmentInfo {
    AttachmentType attachment_type = 1;
    string attachment_key = 2;
    string extra_info = 3; //给客户端自定义玩，虽然以后加也可以
}

message MarkPostAttachmentUploadedRequest {
    uint32 uid = 1;
    string post_id = 2;
    repeated UploadAttachmentInfo attachment_list = 3; // 上传完的附件key，注意顺序
    // V3.4.2 - 支持图片评论，如果是上传评论的附件，则带上评论的id
    string comment_id = 4;
    bool is_obs = 5;
}

message MarkPostAttachmentUploadedResponse {
}
