syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/tmp-channel-alloc";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package tmp_channel_alloc;

service TmpChannelAlloc {
    rpc Alloc(AllocReq) returns (AllocResp) {}
    rpc Release(ReleaseReq) returns (ReleaseResp) {}

    // 新临时房分配接口
    rpc AllocV2(AllocReq) returns (AllocResp) {}
    rpc ReleaseV2(ReleaseReq) returns (ReleaseResp) {}
}

message AllocReq {
    string operator = 1;                    //操作者，业务标识
    uint32 channel_type = 2;                //房间类型
    uint32 scheme_id = 3;                   //房间玩法
    string channel_name = 4;                //房间名称
    string desc = 5;                        //房间描述
    string topic_detail = 6;                //房间话题详情
    uint64 background_id = 7;               //背景id
    ReleaseStrategy strategy = 8;           //房间释放策略
    uint32 mic_mode = 9;                    //麦位模式
    int64  background_expire_sec = 10;      //背景过期时长
}

enum ReleaseType {
    Unknown = 0;                //未知
    ChannelMember = 1;          //人数为零时自动释放
}

message ReleaseStrategy {
    uint32 expire_sec = 1;      //房间过期时间（秒）
    ReleaseType type = 2;       //房间释放策略类型
}

message AllocResp {
    uint32 channel_id = 1;      //房间标识
}

message ReleaseReq {
    string operator = 1;        //操作者，业务标识
    uint32 channel_id = 2;      //房间标识
    uint32 channel_type = 3;    //房间类型
}

message ReleaseResp {

}