syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package tmp_channel_alloc.event;

option go_package = "golang.52tt.com/protocol/services/tmp-channel-alloc/event";

// 房间释放模式
enum ReleaseMode {
    Unknown = 0;                // 未知
    ExpiredAuto = 1;            // 超时自动释放
    ReleasedByUser = 2;         // 用户主动释放
}

message ChannelReleaseEvent {
    string operator = 1;        // 操作者，业务标识
    uint32 channel_id = 2;      // 房间标识
    uint32 channel_type = 3;    // 房间类型
    int64 timestamp = 4;        // 释放时间
    ReleaseMode mode = 5;       // 房间释放模式
}