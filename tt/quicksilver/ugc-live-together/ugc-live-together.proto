syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/ugc-live-together";

package ugc_live_together;

service UgcLiveTogether {
  // 创建/修改直播配置
  rpc UpsertLiveConfig(UpsertLiveConfigReq) returns (UpsertLiveConfigResp) {}
  // 删除直播配置
  rpc DelLiveConfig(DelLiveConfigReq) returns (DelLiveConfigResp) {}
  // 获取直播配置列表
  rpc GetLiveConfigList(GetLiveConfigListReq) returns (GetLiveConfigListResp) {}
  // 停止直播
  rpc StopLive(StopLiveReq) returns (StopLiveResp) {}

  // 批量设置插播广告
  rpc BatchSetAdsConfig(BatchSetAdsConfigReq) returns (BatchSetAdsConfigResp) {}
  // 获取直播的广告列表
  rpc GetLiveAdsConfigList(GetLiveAdsConfigListReq) returns (GetLiveAdsConfigListResp) {}
  // 立即/取消推送插播广告
  rpc PushAds(PushAdsReq) returns (PushAdsResp) {}

  // 创建/修改气泡配置
  rpc UpsertBubbleConfig(UpsertBubbleConfigReq) returns (UpsertBubbleConfigResp) {}
  // 删除气泡配置
  rpc DelBubbleConfig(DelBubbleConfigReq) returns (DelBubbleConfigResp) {}
  // 获取气泡配置列表
  rpc GetBubbleConfigList(GetBubbleConfigListReq) returns (GetBubbleConfigListResp) {}

  //获取房间直播配置
  rpc GetChannelLiveInfo(GetChannelLiveInfoReq) returns (GetChannelLiveInfoResp){}
  //设置房间直播状态
  rpc SetChannelLiveStatus(SetChannelLiveStatusReq) returns (SetChannelLiveStatusResp){}
}

// 产品
enum App {
  AppNone = 0;
  AppTT = 1;
  AppHuanYou = 2;
  AppMaiKe = 3;
  AppMiJing = 6;      // 谜境，沉浸式剧本场景的马甲包。
}

// 平台
enum Platform {
  PlatformAll = 0;
  PlatformAndroid = 1;
  PlatformIOS = 2;
}

// 展示对象
enum UserType {
  UserTypeAll = 0; // 符合条件的所有用户
  UserTypeSpecified = 1; // 指定房主
  UserTypeCrowdPkg = 2; // 指定房主人群包
}

// 配置状态
enum ConfStatus {
  ConfStatusNone = 0;
  ConfStatusInactive = 1; // 未生效
  ConfStatusActive = 2; // 生效中
  ConfStatusExpired = 3; // 已过期
}

// 房主信息
message ChannelOwner {
  // 房主uid
  uint32 uid = 1;
  // 房主房间channel id
  uint32 cid = 2;
  // 房主tt id
  string tid = 3;
  // 房主昵称
  string nickname = 4;
}

// 插播广告配置
message AdsConfig {
  // 广告推送状态
  enum PushState {
    PushStateNone = 0;
    PushStateInit = 1; // 可推送(立即推送)
    PushStateProcessing = 2; // 推送中(取消推送)
  }

  uint32 id = 1;
  // 广告链接
  string link = 2;
  // 广告推送状态
  PushState state = 3;
  // 最近更新时间
  uint32 updated_at = 4;
  // 关联的直播配置id
  uint32 live_id = 5;
}

// 直播配置
message LiveConfig {
  enum PlayFormat {
    PlayFormatOther = 0; // 其他
    PlayFormatRtmp = 1; // rtmp
  }

  uint32 id = 1;
  uint32 updated_at = 2;

  // 产品
  repeated App app_list = 3;
  // 平台
  Platform platform = 4;

  // 生效开始时间
  uint32 begin_at = 5;
  // 生效结束时间
  uint32 end_at = 6;

  // 房间主题id
  uint32 tab_id = 7;

  // 房间主题名称
  string tab_name = 19;

  // 展示对象类型
  UserType user_type = 8;
  // 房主信息
  repeated ChannelOwner owner_list = 9;

  // 直播链接
  string live_link = 10;
  // 直播入口文案
  string entrance_text = 11;
  // 直播入口配图
  string entrance_pic = 12;
  // 直播悬浮球配图
  string float_pic = 13;

  // 视频标题
  string video_title = 14;
  // 视频比例：长度
  uint32 video_length = 15;
  // 视频比例：宽度
  uint32 video_width = 16;

  // 插播广告列表
  repeated AdsConfig ads_list = 17;

  // 配置状态
  ConfStatus status = 18;

  //人群包id
  string crowed_pkg_id = 20;

  // 视频播放格式
  PlayFormat play_format = 21;

  //是否有强引导
  bool has_strong_guide = 22;
  string guide_gif = 23; //强引导图片
  string guide_title = 24; //强引导标题
  uint32 limit_close_second = 25; //N秒后关闭按钮
}

enum UGCLiveTogetherStatus{
  NONE = 0;  //没变更
  CLOSE = 1; //关闭
  OPEN = 2;  //开启
}

//直播状态
message LiveStatus{
  UGCLiveTogetherStatus live_status = 3; //直播开启状态
  uint32 status_change_at = 4;//状态变更时间
}

// 气泡配置
message BubbleConfig {
  uint32 id = 1;
  uint32 updated_at = 2;

  // 产品
  repeated App app_list = 3;
  // 平台
  Platform platform = 4;

  // 房间主题id
  uint32 tab_id = 5;

  // 房间主题名称
  string tab_name = 11;

  // 生效开始时间
  uint32 begin_at = 6;
  // 生效结束时间
  uint32 end_at = 7;

  // 展示对象
  UserType user_type = 8;
  // 房主信息
  repeated ChannelOwner owner_list = 9;

  // 气泡文案
  string text = 10;

  // 配置状态
  ConfStatus status = 18;

  //人群包id
  string crowed_pkg_id = 19;

}

message UpsertLiveConfigReq {
  LiveConfig config = 1;
}

message UpsertLiveConfigResp {}

message DelLiveConfigReq {
  uint32 id = 1;
}

message DelLiveConfigResp {}

message GetLiveConfigListReq {
  // 生效开始时间
  uint32 begin_at = 1;
  // 生效结束时间
  uint32 end_at = 2;

  // 生效状态
  ConfStatus status = 3;
  // 推送产品
  repeated App app_list = 4;

  // 房间主题id
  repeated uint32 tab_ids = 5;
  // 直播链接
  string live_link = 6;

  uint32 offset = 7;
  uint32 limit = 8;
}

message GetLiveConfigListResp {
  repeated LiveConfig list = 1;
  uint32 total = 2;
}

message StopLiveReq {
  // 直播配置id
  uint32 id = 1;
}

message StopLiveResp {}

message BatchSetAdsConfigReq {
  // 关联的直播配置id
  uint32 live_id = 1;
  repeated AdsConfig ads_list = 2;
}

message BatchSetAdsConfigResp {}

message GetLiveAdsConfigListReq {
  uint32 live_id = 1;
}

message GetLiveAdsConfigListResp {
  repeated AdsConfig list = 1;
}

message PushAdsReq {
  enum Op {
    OpNone = 0;
    OpPush = 1; // 立即推送
    OpCancel = 2; // 取消推送
  }

  Op op = 1;

  // 插播广告配置id
  uint32 id = 2;
}

message PushAdsResp {}

message UpsertBubbleConfigReq {
  BubbleConfig config = 1;
}

message UpsertBubbleConfigResp {}

message DelBubbleConfigReq {
  uint32 id = 1;
}

message DelBubbleConfigResp {}

message GetBubbleConfigListReq {
  // 生效开始时间
  uint32 begin_at = 1;
  // 生效结束时间
  uint32 end_at = 2;

  // 配置状态
  ConfStatus status = 3;
  // 推送产品
  repeated App app_list = 4;

  // 房间主题id
  repeated uint32 tab_ids = 5;

  uint32 offset = 6;
  uint32 limit = 7;
}

message GetBubbleConfigListResp {
  repeated BubbleConfig list = 1;
  uint32 total = 2;
}
message GetChannelLiveInfoReq{
  uint32 channel_id = 1;
  uint32 tab_id = 2;
  uint32 role = 3;// 0房客，1房主
  App app = 4;
  Platform platform = 5;
  uint32 owner_uid = 6;
}

message GetChannelLiveInfoResp{
  LiveConfig live_config = 1;
  AdsConfig ads_config = 2;
  BubbleConfig bubble_config = 3;
  LiveStatus live_status = 4;
}

message SetChannelLiveStatusReq{
  uint32 channel_id = 1;
  uint32 live_status = 2; //开关状态：1，开 2，关
}
message SetChannelLiveStatusResp{

}