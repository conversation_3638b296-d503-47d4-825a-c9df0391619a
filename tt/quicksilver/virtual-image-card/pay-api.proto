syntax = "proto3";

package virtual_image_card;
option go_package = "golang.52tt.com/protocol/services/virtual-image-card";

message ApiResponseHeader {
  string result = 1;
  string message = 2;
}

//连续签约额外参数
message ApiPeriodParam {
  string contract_id = 1; //签约ID
  string plan_id = 2; //签约模板
  string period_type = 3; //周期类型 DAY / MONTH
  int64 period = 4; //周期, 与周期类型并用, 如 30DAY, 3MONTH
  string contract_notify_url = 5; //业务方的签约回调地址
  string execute_time = 6; //执行首次扣费时间 (yyyy-MM-dd HH:mm:ss)
  string product_code = 7; //商品code（支付宝专用参数，用来区分VIP和SVIP套餐，SVIP版本补充参数）
  string single_amount = 8; //单次扣款金额(支付宝订阅必传，单位元，用来标识下次扣款金额)
}

message ApiPlaceOrderReq{
  string order_type = 1; //订单类型类型 默认:BUY
  string os_type = 2; //系统类型 (i IOS系统 a android系统)
  string pay_channel = 3; //支付渠道,货币组指定
  string business_id = 4; //子业务id，货币组指定
  string fm = 5; //TT语音、欢游等对应在货币系统的标识,用于APP支付参数解密, 若payChannel不为空,必传
  string version = 6;
  string cli_order_no = 7;
  string cli_buyer_id = 8;
  string cli_price = 9;
  string cli_order_title = 10;
  string cli_order_desc = 11;
  string cli_notify_url = 12;
  string create_time = 13; //外部订单时间(“yyyy-MM-dd HH:mm:ss”)
  string remark = 14; //备注
  string bundle_id = 15; //IOS的包名(appstore支付必传)
  string product_id = 16; //商品ID(appstore必传)

  ApiPeriodParam period_param = 17; //签约信息
  string code = 18; //端外支付code
  string time_out = 19; //订单支持超时时间（分钟）
  repeated string original_transaction_ids = 20; // 苹果帐号使用，新版本用于AB帐号问题消除
  string business_scence_code = 21; // 业务场景码，用来区分是会员还是无限换装卡
}

message ApiPlaceOrderResp {
  string order_no = 1;
  string token = 2;
  string cli_order_no = 3;
  string cli_order_title = 4;
  string order_price = 5;
  string tsk = 6; //加密字符串
  string channel_map = 7 ;//唤起支付渠道的参数, 用于安卓、IOS、前端使用
}

message ApiGetContractReq{
  string contract_id = 1;
  string business_id = 2;
  string buyer_id = 3;
  string product_code = 4; //商品code（支付宝专用参数，用来区分VIP和SVIP套餐，SVIP版本补充参数）
  string business_scence_code = 5; // 业务场景码，用来区分是会员还是无限换装卡
}

message ApiGetContractResp{
  string code = 1; //判断查询成功与否(成功为 “10000”)
  string contract_id = 2;
  string business_id = 3;
  string client_id = 4;
  string pay_channel = 5;
  string order_no = 6;
  string id = 7;
}

message ApiCancelContractReq {
  string contract_id = 1;
  string business_id = 2;
  string buyer_id = 3;
  string contract_notify_url = 4;
  string product_code = 5; //商品code（支付宝专用参数，用来区分VIP和SVIP套餐，SVIP版本补充参数）
  string business_scence_code = 6; // 业务场景码，用来区分是会员还是无限换装卡
}

message ApiCancelContractResp{
  string code = 1;
  string msg = 2;
  string pay_channel = 3;
  string order_no = 4;
}

message DeductParam {
  string contract_id = 1;
  string plan_id = 2; //签约模板
  string product_code = 3; //商品code（支付宝专用参数，用来区分VIP和SVIP套餐，SVIP版本补充参数）
  string single_amount = 4; //单次扣款金额(支付宝订阅必传，单位元，用来标识下次扣款金额)
}

message ApiAutoPayReq{
  string order_type = 1;
  string os_type = 2;
  string pay_channel = 3;
  string business_id = 4;
  string fm = 5;
  string version = 6;
  string cli_order_no = 7;
  string cli_buyer_id = 8;
  string cli_price = 9;
  string cli_order_title = 10;
  string cli_order_desc = 11;
  string cli_notify_url = 12;
  string create_time = 13;
  string remark = 14;
  string bundle_id = 15;
  string product_id = 16;
  string user_ip = 17;
  DeductParam deduct_param = 18;
  string business_scence_code = 20; // 业务场景码，用来区分是会员还是无限换装卡
}

message ApiAutoPayResp {
  string order_no = 1;
  string token = 2;
  string cli_order_no = 3;
  string cli_order_title = 4;
  string order_price = 5;
  string tsk = 6;
  string channel_map = 7;
}

message ApiDelayPayReq {
  string contract_id = 1;
  string business_id = 2;
  string buyer_id = 3;
  string deduct_time = 4;
  string memo = 5;
  string pay_channel = 6;
  string business_scence_code = 7; // 业务场景码，用来区分是会员还是无限换装卡
}

message ApiDelayPayResp {
  string head = 1;
}
