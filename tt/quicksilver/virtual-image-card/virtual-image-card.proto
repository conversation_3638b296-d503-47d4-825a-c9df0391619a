syntax = "proto3";

package virtual_image_card;
import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";
option go_package = "golang.52tt.com/protocol/services/virtual-image-card";

service VirtualImageCard {
  option (service.options.service_ext) = {
    service_name: "virtual-image-card"
  };

  // 下单
  rpc PlaceOrder (PlaceOrderReq) returns (PlaceOrderResp);
  // 取消订单
  rpc CancelOrder (CancelOrderReq) returns (CancelOrderResp);
  // 支付回调
  rpc PayCallback (PayCallbackReq) returns (PayCallbackResp);
  // 签约回调
  rpc NotifyContract (NotifyContractReq) returns (NotifyContractResp);
  // 苹果的自动下单回调
  rpc PlaceAutoPayOrder (PlaceAutoPayOrderReq) returns (PlaceAutoPayOrderResp);
  // 订单退款
  rpc RevokeOrder (RevokeOrderReq) returns (RevokeOrderResp);
  // 使用体验卡
  rpc UseVirtualImageTrialCard (UseVirtualImageTrialCardReq) returns (UseVirtualImageTrialCardResp);
  // 生成统计报表
  rpc GenerateStat (GenerateStatReq) returns (GenerateStatResp);

  // 体验卡对账与补单
  rpc GetTrialCardTotalCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp);
  rpc GetTrialCardOrderIds (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp);
  rpc ReplaceTrialCardOrder (ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp);

  // 获取用户卡信息
  rpc GetUserCardInfo (GetUserCardInfoReq) returns (GetUserCardInfoResp);
  // 获取用户核销信息
  rpc GetUserRedemptionInfo (GetUserRedemptionInfoReq) returns (GetUserRedemptionInfoResp);
  // 获取用户可用套餐列表
  rpc GetUserPackageList (GetUserPackageListReq) returns (GetUserPackageListResp);
  // 获取购买历史
  rpc GetPurchaseHistory (GetPurchaseHistoryReq) returns (GetPurchaseHistoryResp);

  // ========================== 套餐运营后台 ==================================
  // 新增套餐
  rpc AddPackage(AddPackageReq) returns (AddPackageResp);
  // 批量新增分组套餐
  rpc BatchAddGroupPackage(BatchAddGroupPackageReq) returns (BatchAddGroupPackageResp);
  // 修改套餐内容
  rpc ModifyPackage(ModifyPackageReq) returns (ModifyPackageResp);
  // 更新套餐/分组状态（停用/启用）
  rpc UpdatePackageStatus(UpdatePackageStatusReq) returns (UpdatePackageStatusResp);
  // 获取套餐/分组列表(按更新时间排序)
  rpc GetPackageListByStatus(GetPackageListByStatusReq) returns (GetPackageListByStatusResp);
  // 新增/编辑分组套餐
  rpc EditGroupPackage(EditGroupPackageReq) returns (EditGroupPackageResp);
  // 修改分组套餐内容
  rpc ModifyGroupPackage(ModifyGroupPackageReq) returns (ModifyGroupPackageResp);
  // 增加在售架套餐
  rpc AddSalePackage(AddSalePackageReq) returns (AddSalePackageResp);
  // 修改在售架套餐
  rpc UpdateSalePackage(UpdateSalePackageReq) returns (UpdateSalePackageResp);
  // 获取在售架套餐列表
  rpc GetSalePackageListByStatus(GetSalePackageListByStatusReq) returns (GetSalePackageListByStatusResp);
  // 修改在售架套餐排序
  rpc SalePackageSort(SalePackageSortReq) returns (SalePackageSortResp);
  // 获取无限卡配置
  rpc GetVirtualImageCardCommonCfg(GetVirtualImageCardCommonCfgRequest) returns (GetVirtualImageCardCommonCfgResponse);
  // 获取无限卡入口状态
  rpc GetVirtualImageCardEntryStatus(GetVirtualImageCardEntryStatusRequest) returns (GetVirtualImageCardEntryStatusResponse);

}

enum PayChannel {
  PAY_CHANNEL_UNSPECIFIED = 0;
  PAY_CHANNEL_ALIPAY = 1;
  PAY_CHANNEL_WECHAT = 2;
  PAY_CHANNEL_APPSTORE = 3;
}

message PlaceOrderReq {
  uint32 uid = 1;
  uint32 package_id = 2; // 套餐ID
  PayChannel pay_channel = 3; // 支付渠道
  repeated string original_transaction_ids = 4; // 苹果帐号使用，用于消除AB账号问题
  uint32 pay_price_cent = 5; // 订单价格(分)
}

message PlaceOrderResp {
  string order_no = 1; // 货币订单号
  string token = 2;
  string cli_order_no = 3; // 卡订单号
  string cli_order_title = 4; //订单标题
  string order_price = 5; //订单价格
  string tsk = 6; //加密字符串
  string channel_map = 7; //唤起支付渠道的参数, 用于安卓、IOS、前端使用
}

message CancelOrderReq {
  uint32 uid = 1;
  string order_no = 2; // 货币订单号
}

message CancelOrderResp {
}

message PayCallbackReq {
  string order_no = 1; //支付平台订单
  string cli_order_no = 2; //卡服务订单
  string other_order_no = 3; //支付渠道订单
  string coin_pay_channel = 4; //支付渠道，可能和下单时不一致
  uint32 pay_price_cent = 5; //支付金额(分)，可能和订单金额不一致
  int64 pay_ts = 6; //支付时间戳
  uint32 active_uid = 7;//真正生效的uid
}

message PayCallbackResp {
}

message NotifyContractReq {
  string contract_id = 1;
  bool is_sign = 2; // 是否签约
  uint32 active_uid = 3;
  uint32 real_buyer_uid = 4;
  string product_id = 5 ; //APPStore 商品ID，可以在端外更换
  int64 next_pay_ts = 6; //下次扣款时间，只允许APPSTROE使用
}

message NotifyContractResp {
}

message PlaceAutoPayOrderReq {
  string contract_id = 1; // 签约ID
  string product_id = 2; // APPSTORE 商品ID
}

message PlaceAutoPayOrderResp {
}

// 订单退款
message RevokeOrderReq {
  string order_id = 1; // 订单ID
  string notify_time = 2; // 通知时间
  uint32 uid = 3; // 用户UID
}

message RevokeOrderResp {
}

message UseVirtualImageTrialCardReq {
  uint32 uid = 1;
  uint32 func_card_id = 2; // 功能卡id
  uint32 add_days = 3; // 可增加天数
}

message UseVirtualImageTrialCardResp {
}

message GenerateStatReq {
  // 没有严格1号开始/结束，留点tricky测试想象
  string start_time = 1; // 开始时间，格式：yyyy-MM-dd HH:mm:ss
  string end_time = 2; // 结束时间，格式：yyyy-MM-dd HH:mm:ss
}

message GenerateStatResp {
}

message UserCardInfo {
  uint32 uid = 1;
  int64 effect_ts = 2; // 生效时间戳。未开通过则为0
  int64 expire_ts = 3; // 过期时间戳。未开通过则为0
  repeated UserContractInfo contracts = 4; // 签约列表
  string discount_order_id = 5; // 首购优惠订单ID，空则说明没有使用过首购优惠
  string first_buy_order_id = 6; // 首次购买的订单，空就说明还没买过
  int64 buy_effect_ts = 7; // 购买生效时间戳，未开通过则为0
  int64 buy_expire_ts = 8; // 购买过期时间戳，未开通过则为0
  int64 trial_effect_ts = 9; // 试用生效时间戳，未开通过则为0
  int64 trial_expire_ts = 10; // 试用过期时间戳，未开通过则为0
}

message UserContractInfo {
  uint32 uid = 1;
  string contract_id = 2; // 签约ID
  PayChannel pay_channel = 3; // 支付渠道
  int64 next_pay_ts = 4; // 下次扣款时间戳
  int64 create_ts = 5; // 创建时间戳
  Package package = 6; // 套餐信息
}

message GetUserCardInfoReq {
  uint32 uid = 1;
}

message GetUserCardInfoResp {
  UserCardInfo card = 1;
  uint32 expire_alert_status_keep_hour = 2; // 即将到期状态持续小时数
}

message GetUserRedemptionInfoReq {
  uint32 uid = 1;
}

message GetUserRedemptionInfoResp {
  bool has_single_buy_order_waiting_redemption = 1; // 是否有单次购买的订单等待核销
}

message GetUserPackageListReq {
  uint32 uid = 1;
  uint32 market_id = 2;
  uint32 client_type = 3;
}

message GetUserPackageListResp {
  repeated UserPackage package_list = 1; // 套餐列表
}

message UserPackage {
  uint32 package_id = 1; // 套餐ID
  string name = 2; // 套餐名
  string desc = 3; // 描述角标
  uint32 current_price_cent = 4; // 现价RMB
  uint32 original_price_cent = 5; // 原价RMB，没有就不显示
  string discount_label = 6; // 优惠标签，如：首月/首日，没有则不显示
  uint32 daily_price_cent = 7; // 每日单价，该单价按每月30天计算，一位小数，向上取整
  uint32 pay_price_cent = 8; // 支付按钮上的价格
  string explanation = 9; // 套餐说明
  bool is_auto = 10; // 是否自动续费套餐
  repeated PayChannel pay_channel_list = 11; // 支持的支付渠道列表
  string product_id = 12; // 苹果商店商品ID, 仅ios使用，非空则为ios
  DisplayCondition show_condition = 13; // 套餐展示条件
}

message GetPurchaseHistoryReq {
  uint32 uid = 1;
  uint32 page = 2;
  uint32 page_size = 3;
}

message GetPurchaseHistoryResp {
  repeated PurchaseRecord purchase_list = 1;
  bool has_more = 2;
}

message PurchaseRecord {
  string order_id = 1; //订单号，用于可能的去重
  string time = 2;
  string desc = 3;
  string price = 4;
  bool has_refunded = 5; // 是否已退款
}

//套餐
message Package {
  uint32 id = 1; // 套餐ID
  string product_id = 2; // 苹果商店商品ID, 仅ios使用，非空则为ios
  string name = 3; // 套餐名称
  string desc = 4; // 套餐说明
  uint32  days = 5;   // 套餐会员天数
  uint32  original_price_cent = 6; //套餐原价
  uint32  price_cent = 7;// 套餐售价
  bool is_enabled = 8; // 是否启用
  string operator = 9;   // 操作人
  uint32 market_id = 10; // 马甲包id 0 TT、2欢游、5麦可
  uint32 group_id = 11; //分组ID, 0表示独立套餐
  uint64 update_ts = 12;   // 更新时间
  PackageType package_type = 13; // 套餐类型
  uint32 discount_price_cent = 14;// 优惠价格， 运营后台无需关注
  string discount_label = 15; // 首购提示语
  string discount_desc = 16;  // 首购角标
}

// 套餐类型
enum PackageType {
  PACKAGE_TYPE_UNSPECIFIED = 0; // 未指定
  PACKAGE_TYPE_NORMAL = 1; // 单次购买
  PACKAGE_TYPE_AUTO_RENEW = 2; // 自动续费
}

// 增加套餐配置
message AddPackageReq
{
  Package package_info = 1;
}
message AddPackageResp
{
}

// 更新套餐状态
message UpdatePackageStatusReq {
  bool is_enabled = 1; // 是否启用
  uint32 id = 2;
  bool is_group = 3; // 是否分组套餐
  string operator = 4; // 操作人
}

message UpdatePackageStatusResp {
}

// 获取套餐列表
message GetPackageListByStatusReq
{
  bool is_enabled = 1; // 是否启用
  PackageType package_type = 2; // 套餐类型
}


message MixPackage{
  bool is_group = 1; // 是否分组套餐，为true时从分组套餐中获取
  Package package_info = 2;
  GroupPackage group_package = 3;
}

message GetPackageListByStatusResp
{
  repeated MixPackage package_list = 1;
}


// 在售架套餐状态
enum SalePackageStatus
{
  ENUM_SALE_PACKAGE_STATUS_UNSPECIFIED = 0; // 未指定
  ENUM_SALE_PACKAGE_STATUS_SHELF = 1;  // 上架中
  ENUM_SALE_PACKAGE_STATUS_NOT_ON_SHELF = 2;  // 已下架
  ENUM_SALE_PACKAGE_STATUS_WAIT_SHELF = 3; // 待上架
}

enum CardStatus
{
  ENUM_STATUS_NO_OPEN = 0;  // 未开通
  ENUM_STATUS_OPENING = 1;  // 生效中（有效期内且剩余时间 >72h）
  ENUM_STATUS_SOON_EXPIRE = 2;  // 即将过期（有效期内且剩余时间 ≤72h）
  ENUM_STATUS_EXPIRED = 3;  // 已过期（用户开通过但已不在有效期内）
  ENUM_STATUS_SIGN = 4; // 用户开通连续订阅未到期且保持签约状态
  ENUM_STATUS_TRIAL = 5; // 体验中
}

// 展示条件
message DisplayCondition
{
  enum CondType {
    ENUM_USER_ALL = 0;    // 全部用户
    ENUM_USER_SPECIFIED = 1;  // 指定用户
  }
  uint32 cond_type = 1;  // see CondType
  repeated CardStatus card_status = 2; // 无限卡状态
  uint32 user_lv = 3;  // 账号等级
  uint32 rich_lv = 4; // 财富等级
  uint32 nobility_lv = 5; // 贵族等级
  repeated string people_group_list = 6; // 人群包
}

// 在售架的套餐信息
message SalePackage {
  uint32 sale_id = 1;
  Package package_info = 2; // 添加时，只填入id即可
  uint64 begin_ts = 3;
  uint64 end_ts = 4;
  DisplayCondition condition = 5;  // 展示条件
  uint32 weight = 6;   // 权重字段，用于排序
  SalePackageStatus sale_status = 7; // 在售状态
  string label = 8; //套餐角标文案
  string operator = 9;  // 操作人
  uint64 update_ts = 10;  // 更新时间
  string remarks = 11; // 运营备注
  uint32 group_id = 12; //分组ID，当存在分组ID时，不需要填充package_info（仅新增或修改时需要填充）
  uint32 market_id = 13; // 马甲包id 0 TT、2欢游、5麦可
}

// 马甲包、系统信息
message MarketInfo {
  uint32 market_id = 1; // 马甲包id 0 TT、2欢游、5麦可
  string product_id = 2;   //苹果商店商品ID, 仅ios使用，非空则为ios
  uint32 package_id = 3;   //套餐ID，新增填0
  uint64 update_ts = 4;   // 更新时间
}

// 分组套餐配置
message GroupPackage {
  uint32 group_id = 1; //分组ID
  PackageType package_type  = 2; // 套餐类型
  string name = 3; //套餐名称
  string desc = 4; //套餐说明
  uint32  original_price_cent = 5; //套餐原价
  uint32  price_cent = 6;//套餐售价
  int32  days = 7;   // 套餐会员天数
  repeated MarketInfo market_list = 8; //市场列表
  bool  is_enabled = 9;  // 是否启用
  string operator = 10;   // 操作人
}

// 批量添加分组套餐配置
message BatchAddGroupPackageReq {
  repeated GroupPackage group_list = 1; // 相同标记的放在同一个组内，只有一个的成为独立套餐
  bool is_check = 2; // 是否是校验，若为校验，不会操作数据，仅做校验
}
// 批量添加分组套餐配置
message BatchAddGroupPackageResp {
}

// 新增/编辑分组
message EditGroupPackageReq {
  uint32 group_id = 1; //分组ID, 0表示新增分组
  repeated MarketInfo market_list = 2; //市场列表
  string operator = 3;   // 操作人
}
// 新增/编辑分组
message EditGroupPackageResp {
}


// 修改套餐内容
message ModifyPackageReq {
  Package package_info = 1; // 套餐信息
}
message ModifyPackageResp {
}

// 修改分组套餐内容
message ModifyGroupPackageReq {
  GroupPackage group_package = 1;
}
// 修改分组套餐内容
message ModifyGroupPackageResp {
}


// 增加在售架套餐配置
message AddSalePackageReq
{
  SalePackage info = 1;
  bool is_check = 2; // 是否是校验，若为校验，不会操作数据，仅做校验
}

// 增加在售架套餐配置
message AddSalePackageResp
{
  repeated uint32 already_on_shelf_list = 1; // 已经上架的套餐ID列表
}

// 修改在售架套餐配置
message UpdateSalePackageReq {
  SalePackage info = 1;
}
// 修改在售架套餐配置
message UpdateSalePackageResp {
}


// 获取在售架套餐列表
message GetSalePackageListByStatusReq {
  SalePackageStatus status = 1;  // see SalePackageStatus
}
// 获取在售架套餐列表
message GetSalePackageListByStatusResp
{
  // 不做分页
  repeated SalePackage package_list = 1;
}


// 套餐排序
message SalePackageSortReq {
  repeated uint32 sale_id_list = 1;
}
// 套餐排序
message SalePackageSortResp {
}


message AboutToExpireCfg {
  string icon = 1; // 即将过期图标
  uint32 expire_alert_time = 15; // 过期提醒时间，单位小时（24/48/72小时）
}

// 获取虚拟形象卡片通用配置 cfg_version 变大时调用
message GetVirtualImageCardCommonCfgRequest {
}

message GetVirtualImageCardCommonCfgResponse {
  string wait_to_buy_icon = 1; // 从未购买/已过期购买动画 【1、房间挂件】
  string already_buy_icon = 2; // 已购买图标 【1、房间挂件】
  repeated AboutToExpireCfg about_to_expire_cfg_list = 3; // 即将过期动画 【1、房间挂件】, 列表顺序从近到远(一天、两天、三天)
  string first_enter_card_store_url = 4; // 首次进入无限卡商城弹窗vap 【2、商城弹窗】
  string ad_text = 5; // 广告文案 如：海量商品免费使用   【4、商城商品列表】
  uint32 n_day_show_once = 6; // 【4、商城商品列表】 n天只展示一次 用户处于从未购买或已过期状态时，选中无限换装卡权益内的商品后会有浮层提示购买卡，n天内最多出现1次
  uint32 cfg_version = 7; // 配置版本号
  string wait_to_buy_bg = 8; // 从未购买/已过期购买底图 【4、商城商品列表】
  string already_buy_bg = 9; // 已购买底图 【4、商城商品列表】
  string about_to_expire_bg = 10; // 即将过期底图 【4、商城商品列表】
  string store_resident_entry_icon = 11; // 商城常驻入口图标 【3、商城预览区】
  string store_tab_icon_selected = 12; // 商城【无限换装tab】选中图标
  string store_tab_icon_unselected = 13; // 商城【无限换装tab】未选中图标
  uint32 expire_alert_time = 14; // 过期提醒时间，单位小时（72小时）
  string first_enter_card_store_md5 = 15; // 首次进入无限卡商城弹窗md5 【2、商城弹窗】
  string pc_wait_to_buy_bg = 16; // pc 端从未购买/已过期购买背景图
  string pc_already_buy_bg = 17; // pc 端已购买背景图
  string pc_about_to_expire_bg = 18; // pc 端即将过期背景图
}

// 获取虚拟形象卡片入口状态
message GetVirtualImageCardEntryStatusRequest {
  uint32 uid = 1;
}

// 虚拟形象卡片入口状态
message GetVirtualImageCardEntryStatusResponse {
  uint32 expire_time = 1; // 无限卡过期时间，未购买则为0，结束时间戳
  string low_price_text = 2; // 【4、商城商品列表】 低价文案 低至10豆/天
  uint32 ad_idx = 3;  // 【1、房间挂件】房间资源位顺序，配置0时无限卡最后,非0时配置第几个就是在第几个,不管有没有打龙;如果超过了最大的个数就是最后
  uint32 cfg_version = 4; // 配置版本号, 版本号变大，则请求GetVirtualImageCardCommonCfg
  bool switch = 5; // 功能是否开启
  int64 trial_effect_ts = 6;
  int64 trial_expire_ts = 7;
  int64 buy_effect_ts = 8;
  int64 buy_expire_ts = 9;
}