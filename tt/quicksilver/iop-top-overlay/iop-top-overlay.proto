syntax = "proto3";

// buf:lint:ignore PACKAGE_DIRECTORY_MATCH
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package com.quwan.dspiopother.proto;
// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/iop-top-overlay";

option java_package = "com.quwan.dspiopother.proto";

service TopOverLaySiteService {
    rpc TopOverSiteStrategy(GetGlobalOverLayReq) returns (GetGlobalOverLayResp) {}
}

enum ETopOverLayType {
  E_TOP_OVER_LAY_TYPE_UNSPECIFIED = 0;   //无效
  E_TOP_OVER_LAY_TYPE_CHANNEL = 1;   // 房间样式浮窗
  E_TOP_OVER_LAY_TYPE_USER = 2;  // 用户样式浮窗
  E_TOP_OVER_LAY_TYPE_ESPORT_GOD = 3;  // 电竞大神样式浮窗
}

message GetGlobalOverLayReq {
  string app_name = 1;
  string app_type = 2;
  string site_id  = 3;
  uint32 uid = 4;
  map<string, string> req_ext = 5;  // 请求扩展参数，选填
}
message GetGlobalOverLayResp{
  uint32 type = 1;   //浮窗类型 ETopOverLayType
  bytes  data = 2;     //根据浮窗类型：PB序列化值，PB参见下面样式PB数据 type==0时为空
  map<string, string> report_data = 3; // 上报数据
  map<string, string> resp_ext = 4;  // 扩展参数
}

message TopOverChannelData {
  uint32 cid = 1;
  string title = 2;  //顶部名称
  string rec_txt = 3; //推荐文本
  string in_txt  = 4; //房间内文本
  uint32 follow_uid = 5; //被跟随的uid(0麦非神秘人uid)
  uint32 tag_id = 6;  //房间标签
}

message TopOverChannelUserData {
  uint32 cid = 1;
  uint32 uid = 2;
  string title = 3;  //顶部名称
  string rec_txt = 4; //推荐文本
  string in_txt  = 5; //房间内文本
  uint32 tag_id = 6;  //房间标签
}

message GlobalRcmdCoachData {
  uint32 coach_uid = 1;
  uint32 game_id = 2;
  string game_name = 3;
  string game_icon = 4;
  string card_title = 5;
  string button_text = 6;
}