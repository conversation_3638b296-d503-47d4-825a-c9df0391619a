syntax = "proto3";

package knight_privilege;

option go_package = "golang.52tt.com/protocol/services/knight-privilege";

service KnightPrivilege {
  rpc GetKnightPrivilegeInfo (GetKnightPrivilegeInfoReq) returns (GetKnightPrivilegeInfoResp) {}

  rpc GetKnightGroupDetialInfo (GetKnightGroupDetialInfoReq) returns (GetKnightGroupDetialInfoResp) {}

  rpc GetKnightGroupMemberWithNameplate (GetKnightGroupMemberWithNameplateReq) returns (GetKnightGroupMemberWithNameplateResp) {}
}


// 开通状态is_chief
enum KnightStatusType {
    KNIGHT_NO      = 0;   // 非骑士
    KNIGHT_COMMON  = 1;   // 普通骑士
    KNIGHT_CHIEF   = 2;   // 首席骑士
}

message GetKnightPrivilegeInfoReq {
    uint32 knight_uid = 1;
    uint32 channel_id = 2;
    uint32 request_type = 3;
}

enum RequestType {
    ALL                             = 0;
    SCEEN_RESOURCE_URL              = 1;
    KNIGHT_NAMEPLATE_RESOURCE_URL   = 2;
    KNIGHT_CARD_RESOURCE_URL        = 3;
    ANCHOR_UIDS                     = 4;
}


message GetKnightPrivilegeInfoResp {
    uint32 status                           = 1; // KnightStatusType
    string sceen_resource_url               = 2; // 骑士进房全屏特效url
    string sceen_resource_url_md5           = 3; // 骑士进房全屏特效url_md5
    string knight_nameplate_resource_url    = 4; // 骑士发言铭牌url
    string knight_card_resource_url         = 5; // 骑士个人资料卡铭牌url
    repeated uint32 anchor_uids             = 6; // 骑士守护的主播，在麦上的
    string knight_channel_msg_bg_url        = 7; // 骑士公屏发言背景url 
}

message KnightGroupDetialInfo {
    string pic_url     = 1;
    string title       = 2;
    string content     = 3;
}

message GetKnightGroupDetialInfoReq {
}
message GetKnightGroupDetialInfoResp {
    repeated KnightGroupDetialInfo info_list = 1; // 骑士详情页
}

message GetKnightGroupMemberWithNameplateReq {
    uint32 anchor_uid = 1;
    uint32 channel_id = 2;
}
message GetKnightGroupMemberWithNameplateResp {
    repeated uint32 uid_list = 1; // all
    uint32 chief_uid = 2;
    string knight_common_nameplate_resource_url = 3;
    string knight_chief_nameplate_resource_url = 4;
}