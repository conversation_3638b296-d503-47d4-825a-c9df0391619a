syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/account-isolator";

package account_isolator;

message GetIsolationSequenceReq {
  uint32 market_id = 1;
  string channel_pkg = 2;
  uint32 terminal_type = 3;
  uint32 client_version = 4;
}

message GetIsolationSequenceResp {
  repeated string scenes = 1;
}

message LockUidIsolationReq {
  uint64 uid = 1;
  string scene = 2;
}

message LockUidIsolationResp {

}

message TryLockUidIsolationReq {
  uint64 uid = 1;
  uint32 market_id = 2;
  string channel_pkg = 3;
  uint32 terminal_type = 4;
  uint32 client_version = 5;
}

message TryLockUidIsolationResp {
  bool lock = 1;
  string scene = 2;
}

message GetUidIsolationInfoReq {
  uint64 uid = 1;
}

message GetUidIsolationInfoResp {
  string scene = 1;
}

message CheckUidIsolationReq {
  uint64 uid = 1;
  uint32 market_id = 2;
  string channel_pkg = 3;
  uint32 terminal_type = 4;
  uint32 client_version = 5;
}

message CheckUidIsolationResp {
  bool pass = 1;
}

service AccountIsolator {

  rpc GetIsolationSequence(GetIsolationSequenceReq) returns (GetIsolationSequenceResp) {}

  rpc LockUidIsolation(LockUidIsolationReq) returns (LockUidIsolationResp) {}

  rpc TryLockUidIsolation(TryLockUidIsolationReq) returns (TryLockUidIsolationResp) {}

  rpc GetUidIsolationInfo(GetUidIsolationInfoReq) returns (GetUidIsolationInfoResp) {}

  rpc CheckUidIsolation(CheckUidIsolationReq) returns (CheckUidIsolationResp) {}

}