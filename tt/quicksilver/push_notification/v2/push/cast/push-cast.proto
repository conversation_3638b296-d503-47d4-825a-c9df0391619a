syntax = "proto3";

import "tt/quicksilver/push_notification/v2/push/pushnotification_v2.proto";

// buf:lint:ignore PACKAGE_LOWER_SNAKE_CASE 此协议废弃
package PushNotification.cast;

option go_package = "golang.52tt.com/protocol/services/push-notification/v2;push_notification";

service PushCast {
  // channelId-based broadcast
  // 基于房间ID消息分发，支持用户黑白名单和设备类型白名单（gaproxy处理）
  // 非重要消息基于channelId维度限流
  rpc ChannelCast(ChannelCastReq) returns (ChannelCastResp) {}
  // uid-based multicast
  // 基于用户在线信息UID+CID消息分发，上有提供下发UID列表，支持设备类型白名单（push-cast处理）
  // 非重要消息基于接口维度限流 即QPS上限
  rpc UniversalCast(UniversalCastReq) returns (UniversalCastResp) {}
  // 全服广播
  // 非重要消息基于接口维度限流 即QPS上限
  rpc BroadCast(BroadCastReq) returns (BroadCastResp) {}
}

message Target {
  repeated uint32 list = 1;
}

message Strategy {
  oneof user_id {
    bool all_user = 1;
    Target white_user = 2;  // uid_list
    Target black_user = 3;  // uid_list
  }
  oneof terminal_type {
    bool all_terminal = 4;
    Target black_terminal = 5;  // 同 presence-v2-user 的 terminal_type
  }
}

message ChannelCastReq {
  string push_label = 1;
  bool important = 2;     // false 会限流
  Strategy strategy = 3;  // 只能支持用户黑名单规则
  int64 client_ms = 4;    // 消息发送时间戳
  uint32 expire_at = 5;   // 消息有效截止事件戳 sec  NOT TTL!
  uint32 sequence = 6;
  uint32 channel_id = 7;
  ProxyNotification notify = 8;  // 不关心 type payload expire_time
  uint32 push_type = 9;          // 留给业务使用的类型
  uint32 app_id = 10;            // I DO NOT KNOW
}

message ChannelCastResp {
  // nothing
}

message UniversalCastReq {
  message PresId {
    uint32 user_id = 1;
    uint32 client_id = 2;
  }
  message PresIdList {
    repeated PresId list = 1;
  }
  string push_label = 1;
  bool important = 2;     // false 会限流
  Strategy strategy = 3;  // eg: pres_info为空时使用 user_id.white_user (<2500
  int64 client_ms = 4;    // 消息发送时间戳 ms
  uint32 expire_at = 5;   // 消息有效截止事件戳 sec  NOT TTL!
  uint32 sequence = 6;
  uint32 universal_id = 7;                 // eg: 1234567
  ProxyNotification notify = 8;            // 只关心 type payload expire_time
  uint32 push_type = 9;                    // 留给业务使用的类型
  map<uint64, PresIdList> pres_info = 10;  // [ip|port]-[]{u|c} 替代 strategy
  uint32 app_id = 11;                      // I DO NOT KNOW
}

message UniversalCastResp {
  // nothing
}

message BroadCastReq {
  string push_label = 1;
  bool important = 2;     // false 会限流
  Strategy strategy = 3;  // 只能支持用户黑名单规则
  int64 client_ms = 4;    // 消息发送时间戳
  uint32 expire_at = 5;   // 消息有效截止事件戳 sec  NOT TTL!
  uint32 sequence = 6;
  uint32 broadcast_id = 7;
  ProxyNotification notify = 8;  // 只关心 type payload expire_time
  uint32 push_type = 9;          // 留给业务使用的类型
  uint32 app_id = 10;            // I DO NOT KNOW
}

message BroadCastResp {
  // nothing
}


message ChannelCastMsg {
  string push_label = 1;  // push_label 漏斗模型任务ID
  bool important = 2;  // false：限流 批量消费 true：不限流 单条消费
  Strategy strategy = 3;    // 控制用户黑白名单，设备类型白名单
  uint32 channel_id = 4;    // 房间ID
  string req_id = 5;        // request_id
  uint64 dye_id = 6;        // dye_id
  uint32 sequence = 7;      // 消息序号
  int64 client_ms = 8;      // 调用方发送时间点 ms
  uint32 expire_at = 9;     // 消息过期时间点 sec
  uint32 cmd_type = 10;     // push cmd type
  uint32 push_type = 11;    // 业务使用的推送类型标识
  string addr = 12;         // 消费者agent关联的proxyAddr
  repeated uint32 index_list = 13;  // 消费者agent关联的proxyIndexList
  int64 svr_ns = 14;                // server recv at
  bytes biz_data = 15;
}

message UniversalCastMsg {
  message PresId {
    uint32 user_id = 1;
    uint32 client_id = 2;
  }
  string push_label = 1;  // push_label 漏斗模型任务ID
  bool important = 2;  // false：限流 批量消费 true：不限流 单条消费
  repeated PresId pres_id_list = 3;  // 推送目标列表
  uint32 universal_id = 4;           // universal_id
  string req_id = 5;                 // request_id
  uint64 dye_id = 6;                 // dye_id
  uint32 sequence = 7;               // 消息序号
  int64 client_ms = 8;               // 调用方发送时间点 ms
  uint32 expire_at = 9;              // 消息过期时间点 sec
  uint32 cmd_type = 10;              // push cmd type
  uint32 push_type = 11;             // 业务使用的推送类型标识
  string addr = 12;                  // 消费者agent关联的proxyAddr
  int64 svr_ns = 13;                 // server recv at
  bytes biz_data = 14;
}

message BroadcastMsg {
  string push_label = 1;  // push_label 漏斗模型任务ID
  bool important = 2;  // false：限流 批量消费 true：不限流 单条消费
  Strategy strategy = 3;    // 控制用户黑白名单，设备类型白名单
  uint32 broadcast_id = 4;  // broadcast_id
  string req_id = 5;          // request_id
  uint64 dye_id = 6;          // dye_id
  uint32 sequence = 7;        // 消息序号
  int64 client_ms = 8;        // 调用方发送时间点 ms
  uint32 expire_at = 9;       // 消息过期时间点 sec
  uint32 cmd_type = 10;       // push cmd type
  uint32 push_type = 11;      // 业务使用的推送类型标识
  string addr = 12;           // 消费者agent关联的proxyAddr
  int64 svr_ns = 13;          // server recv at
  bytes biz_data = 14;
}
