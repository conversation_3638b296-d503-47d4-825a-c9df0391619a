syntax = "proto3";

import "tt/quicksilver/push_notification/v2/apns/apnsdef.proto";

// buf:lint:ignore PACKAGE_LOWER_SNAKE_CASE
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package PushNotification;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/push-notification/v2;push_notification";

service PushNotification {
  rpc PushToUsers(stream PushToUsersReq) returns (stream PushToUsersResp) {}
  rpc PushMulticast(stream PushMulticastReq) returns (stream PushMulticastResp) {}

  rpc UnaryPushToUsers(PushToUsersReq) returns (PushToUsersResp) {}
  rpc UnaryPushToUserMap(PushToUserMapReq) returns (PushToUserMapResp) {}
  rpc UnaryPushMulticast(PushMulticastReq) returns (PushMulticastResp) {}
  rpc UnaryPushToPresenceList(PushToPresenceListReq) returns (PushToUsersResp) {}
  rpc UnaryPushChannelPathMulticast(PushChannelPathMulticastReq) returns (PushChannelPathMulticastResp) {}

  rpc PushRelationRegisterEvent(PushRelationRegisterEventReq) returns (PushRelationRegisterEventResp) {}
  rpc BatchPushRelationRegisterEvents(BatchPushRelationRegisterEventReq) returns (BatchPushRelationRegisterEventResp) {}

  rpc GetReliableProxyNotifications(GetReliableProxyNotificationsReq) returns (GetReliableProxyNotificationsResp) {}
  rpc MessageReceivedAck(MessageReceivedAckReq) returns (MessageReceivedAckResp) {}
  rpc KickoutPushdChannelUsers(KickoutPushdChannelUsersReq) returns (KickoutPushdChannelUsersResp) {}


  // 测试用，代码写死发到pushd的proxy-0进行处理
  rpc TestPushdToUserMap(PushToUserMapReq) returns (PushToUserMapResp) {}
}

service TerminalTypePolicyService {
  rpc SetTerminalTypePolicy(TerminalTypePolicyInfo) returns (SetTerminalTypePolicyResp) {}
  rpc GetTerminalTypePolicy(TerminalTypePolicy) returns (GetTerminalTypePolicyResp) {}
}

message ProxyNotification {
  enum Type {
    INVALID = 0;
    NOTIFY = 1;            // 使用CMD_Notify进行推送, 仅限TT使用(appId将被忽略)
    PUSH = 2;              // 使用CMD_Push进行推送, 仅限TT使用(appId将被忽略), 消息体为ga.PushMessage
    TRANSMISSION_PUSH = 3; // 使用CMD_TransmissionPush透传, 并用TransmissionPacket对payload进行包装
    KICKOUT = 4;           // 使用CMD_KICKOUT进行推送, 仅限TT使用(appId将被忽略)
  }
  enum Policy {
    DEFAULT = 0;   // 默认策略, 只推送一次, 不保证到达
    RELIABLE = 1;  // 过期前保证送达
  }

  uint32 type = 1;
  bytes payload = 2;
  Policy policy = 3;
  uint32 expire_time = 4;  // unix timestamp, not ttl, default is server time + 3600s
  bool sync = 5;
  uint32 priority = 6;  // 推送优先级 仅限限流广播消息
  uint32 push_type = 9; // 废弃
  string push_label = 10;  // 生产漏斗数据唯一标识，使用 在push-funnel.proto的push_label
}

message CompositiveNotification {
  uint32 sequence = 1;                      // sequence id for this push operation
  repeated uint32 terminal_type_list = 2;   // 只有在指定策略为PolicyCustom时，terminal_type_list才会生效
  uint32 app_id = 3;                        // app id
  ProxyNotification proxy_notification = 4; // tt proxy notification
  push_notification.v2.apns.Notification apns_notification = 5;  // APNs notification, not supported anymore
  uint32 apns_frequence = 6; // APNs notification frequence, not supported anymore
  uint32 client_timestamp = 7;
  TerminalTypePolicy terminal_type_policy = 8; // 不指定时，默认发全部终端类型
}

message TerminalTypePolicy {
  string policy_group = 1;
  string policy_name = 2;
}

message GetTerminalTypePolicyResp {
  repeated TerminalTypePolicyInfo terminal_type_policy_info_list = 1;
}

message SetTerminalTypePolicyResp {
  bool success = 1;
}

message TerminalTypePolicyInfo {
  TerminalTypePolicy terminal_type_policy = 1;
  repeated uint32 terminal_type_list = 2;
}

message MulticastAccount {
  uint64 id = 1;
  string account = 2;
}

message ProxyPushResult {
  int32 result = 1;
  map<uint32, uint32> user_notify_counts_map = 2;
}

message PushToUserMapReq {
  map<uint32, CompositiveNotification> uid_map = 1;
  bool sync = 2;
}

message PushToUserMapResp {
  map<uint32, uint32> sequence = 1;
  ProxyPushResult proxy_push_result = 2;
  string request_id = 3;
}

message PushToUsersReq {
  repeated uint32 uid_list = 1;
  CompositiveNotification notification = 2;
  bool sync = 3;
}

message PushMulticastReq {
  MulticastAccount multicast_account = 1;
  CompositiveNotification notification = 2;
  repeated uint32 skip_uids = 3;
  repeated MulticastAccount multicast_accounts = 4;  // if `multicast_accounts` isn't empty, `multicast_account` will be ignored.
  bool sync = 5;
  repeated uint32 only_uids = 6;  // 广播接口应急加上支持指定uid推送的功能
}

message PushChannelPathMulticastReq {
  CompositiveNotification notification = 1;
  repeated uint32 skip_uids = 2;
  repeated string channel_path_list = 3;
  // 实验功能，用于过滤指定要推送的channel_path下的某些子路径的连接，只支持跳过用户订阅的完整channel_path，
  // 例如用户1只订阅了/hello/world，用户2只订阅了/hello/world/go，如果指定了channel_path_list为/hello，
  // skip_channel_path_list指定为/hello/world，那么用户1不会收到推送，用户2会收到推送
  repeated string skip_channel_path_list = 4;
}

message PushChannelPathMulticastResp {
  uint32 sequence = 1;
  string request_id = 2;
}

message PushToUsersResp {
  uint32 sequence = 1;
  ProxyPushResult proxy_push_result = 2;
  string request_id = 3;
}

message PushMulticastResp {
  uint32 sequence = 1;
  string request_id = 2;
}

message Presence {
  uint32 proxy_ip = 1;
  uint32 proxy_port = 2;
  uint32 uid = 3;            // uid
  uint32 client_id = 4;      // clientId
  uint32 terminal_type = 5;  // Terminal type
  string proxy_name = 6;     // pushd proxy name
  uint64 conn_id = 7;        // pushd conn id
  uint32 market_id = 8;      // 马甲包id
  uint32 client_version = 9; // 客户端版本
}

message Presences {
  repeated Presence ps = 1;
}

message PushToPresenceListReq {
  repeated Presence presence_list = 1;
  CompositiveNotification notification = 2;
}

message PushRelationEventResult {
  message Succeed {
    repeated string deliver_addrs = 1;
  }
  oneof type {
    Succeed succeed = 1;
    string error = 2;
  }
}
enum RelationRegisterEventType {
  ENUM_CHANNEL_RELATION_REGENENT_REGIST = 0;
  ENUM_CHANNEL_RELATION_REGENENT_UNREGIST = 1;
  ENUM_GROUP_RELATION_REGENENT_REGIST = 2;
  ENUM_GROUP_RELATION_REGENENT_UNREGIST = 3;
}

message RelationRegisterEvent {
  RelationRegisterEventType reg_event_type = 1;
  uint32 uid = 2;
  repeated uint32 channels = 3;
  uint32 terminal_type = 4;  // terminal_type如果可为0

  // 以下为可选参数
  uint32 client_id = 5;  //
  uint64 event_seq = 6;  // 毫秒时间戳，不填会自动生成
  uint32 delay_ms = 7;   // 默认不延迟
}

// push the regist event to proxynotify
message PushRelationRegisterEventReq {
  RelationRegisterEvent event = 1;
}
// return the address of proxy_notify or error message;
message PushRelationRegisterEventResp {
  PushRelationEventResult result = 1;
}

message BatchPushRelationRegisterEventReq {
  repeated RelationRegisterEvent events = 1;
}

message BatchPushRelationRegisterEventResp {
  // <uid,result>
  map<uint32, PushRelationEventResult> results = 2;
}

message GetReliableProxyNotificationsReq {
  uint32 uid = 1;
  uint32 sequence_begin = 2;
  uint32 sequence_end = 3;
  uint32 count = 4;  // the MAX count of notifications to get, 0 means no limit
}

message ReliableProxyNotification {
  uint32 sequence = 1;
  repeated uint32 terminal_type_list = 2; // you have to specify at least one terminal
  uint32 app_id = 3;  // app id
  ProxyNotification proxy_notification = 4;  // tt proxy notification
}

// Clients are responsible for filtering the notifications with terminal types or app ids
message GetReliableProxyNotificationsResp {
  repeated ReliableProxyNotification notifications = 1;
}

message MessageReceivedAckReq {
  uint32 uid = 1;
  repeated uint32 sequence_list = 2;
}

message MessageReceivedAckResp {}

message ExpireKey {
  uint32 uid = 1;
  uint32 seq = 2;
}

message KickoutPushdChannelUsersReq {
  uint32 channel_id = 1;
  repeated uint32 uid_list = 2;
}

message KickoutPushdChannelUsersResp {
  // nothing
}
