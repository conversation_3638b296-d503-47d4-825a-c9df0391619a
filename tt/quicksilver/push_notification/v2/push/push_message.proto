syntax = "proto3";

import "tt/quicksilver/push_notification/v2/notify/proxy_notify.proto";
import "tt/quicksilver/push_notification/v2/user_group_relation/usergroup_relation_register.proto";

// buf:lint:ignore PACKAGE_LOWER_SNAKE_CASE
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package PushNotification;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/push-notification/v2;push_notification";

message PushMessage {
    oneof request_type {
        notify.PushWithUserList push_with_uid_list                  = 1;
        notify.PushWithMulticastAccount push_with_multicast_account = 2;
        RelationEvent relation_event                                = 3;
    }

    string request_id = 32;
    string dye_id = 33;
    uint32 push_type = 34; // 生产漏斗数据使用 在push-funnel.proto的PUSH_TYPE枚举类型中定义
    string push_label = 35;   // 为指定push_label生成漏斗数据使用
}

message RelationEvent {
    repeated UserGroupRelationReg.UserGroupRelationRegistEvent events = 1;
    uint64 server_received_at                                         = 2;
}

message PushLimitingMessage {
    repeated string groups = 1;
    bytes data             = 2;
    uint32 priority        = 3;  //推送优先级 仅限限流广播消息
    uint32 payload_length  = 4;
    string request_id      = 5;
    string dye_id          = 6;
    uint32 push_type       = 7;
    string push_label      = 8;
}
