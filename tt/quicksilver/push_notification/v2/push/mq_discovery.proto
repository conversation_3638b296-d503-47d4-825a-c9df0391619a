syntax = "proto3";

// buf:lint:ignore PACKAGE_LOWER_SNAKE_CASE 此协议废弃
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package PushNotification;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/push-notification/v2;push_notification";

service PushMqDiscover{
    rpc DiscoverMq(DiscoverReq) returns(DiscoverResp){}
    rpc StreamDiscover(stream DiscoverReq) returns (stream DiscoverResp){}
}

service PushMqManager{
    rpc MqStatus(StatusReq) returns (StatusResp){}
    //TODO
}


// buf:lint:ignore ENUM_PASCAL_CASE
enum DISCOVER_TYPE{
    MQ_PRODUCER = 0;
    MQ_CONSUMER = 1;
}

message DiscoverReq{
    DISCOVER_TYPE discover_type = 1;
    string queue = 2;
}

message DiscoverResp {
    string mq_ip = 1;
    uint32 mq_port = 2;
}

message StatusReq{

}

message StatusResp {
    repeated MqNode nodes = 1;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum MQ_STATUS{
    READY = 0;
    NOTREADY = 1;
}

message MqNode {
    string name = 1;
    string ip = 2;
    uint32 port = 3;
    MQ_STATUS status = 4;
    uint32 connection_num = 5;
    uint32 producer_num = 6;
    uint32 queue_num = 7;
}