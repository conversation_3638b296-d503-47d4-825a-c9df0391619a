syntax = "proto3";

import "tt/quicksilver/extension/options/options.proto";

package push_notification.v2;

option go_package = "golang.52tt.com/protocol/services/push-notification/v2;push_notification";

service PushFunnel {
  option (service.options.old_package_name) = "PushNotification.funnel.PushFunnel";

  rpc QueryFunnelData(QueryFunnelDataReq) returns (QueryFunnelDataResp){}
}

message QueryFunnelDataReq {
  repeated uint32 push_type_list = 1; // [UNKNOWN, TYPE_MAX)
  string scene_type = 2; // channel-push/user-push/app-broadcast/channel-broadcast/group-push
  string push_channel = 3; // agent/proxy/cast
  int64 unix_time = 4; // unix second
  repeated string push_label_list = 5; // [UNKNOWN, TYPE_MAX)
}

message FunnelStep {
  string stage = 1;
  int64 count = 2;
}

message QueryFunnelDataResp {
  repeated FunnelStep agent_channel = 1;
  repeated FunnelStep proxy_channel = 2;
  repeated FunnelStep cast_channel = 3;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum METRICS_SOURCE {
  PUSH_V2 = 0;
  PUSH_CAST = 1;
  PUSH_AGENT = 2;
  PUSH_PROXY = 3;
  GA_PROXY_NOTIFY = 4;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum V2_AGENT_STAGE {
  V2AgentReceive = 0;
  V2AgentSkipped = 1;
  V2AgentBroken = 2;
  V2AgentRpcFailure = 3;
  V2AgentSaveFailure = 4;
  V2AgentSaveSuccess = 5;
  V2AgentFailure = 6;
  V2AgentSuccess = 7;
  V2AgentStageMax = 8;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum V2_PROXY_STAGE {
  V2ProxyReceive = 0;
  V2ProxyFailure = 1;
  V2ProxySuccess = 2;
  V2ProxyStageMax = 3;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum CAST_AGENT_STAGE {
  CastAgentReceive = 0;
  CastAgentSkipped = 1;
  CastAgentDropped = 2;
  CastAgentBroken = 3;
  CastAgentRpcFailure = 4;
  CastAgentFailure = 5;
  CastAgentSuccess = 6;
  CastAgentStageMax = 7;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum AGENT_RMQ_STAGE {
  AgentRMQReceive = 0;
  AgentRMQLost = 1;
  AgentRMQTransmit = 2;
  AgentRMQStageMax = 3;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum AGENT_RDS_STAGE {
  AgentRDSReceive = 0;
  AgentRDSLost = 1;
  AgentRDSTransmit = 2;
  AgentRDSStageMax = 3;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum PROXY_RMQ_STAGE {
  ProxyRMQReceive = 0;
  ProxyRMQLost = 1;
  ProxyRMQTransmit = 2;
  ProxyRMQStageMax = 3;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum GAPROXY_RMQ_STAGE {
  GaProxyRMQReceive = 0;
  GaProxyRMQLost = 1;
  GaProxyRMQTransmit = 2;
  GaProxyRMQStageMax = 3;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum GAPROXY_RDS_STAGE {
  GaProxyRDSReceive = 0;
  GaProxyRDSLost = 1;
  GaProxyRDSTransmit = 2;
  GaProxyRDSStageMax = 3;
}

message PushTypeMap {
  map<uint32, StageMap> stats = 1;
  map<string, StageMap> stats_v2 = 2; //push_label stats
}

message StageMap {
  map<uint32, int64> count = 1;
}

message PushV2Metrics {
  PushTypeMap ch_agent = 1;  // 基于pushAgent通道的房间推送
  PushTypeMap un_agent = 2;  // 基于pushAgent通道的用户推送
  PushTypeMap bc_agent = 3;  // 基于pushAgent通道的全服推送
  PushTypeMap dc_agent = 4;  // 基于pushAgent通道的全频推送
  PushTypeMap gp_agent = 5;  // 基于pushAgent通道的群组推送
  PushTypeMap ch_proxy = 6;  // 基于pushProxy通道的房间推送
}

message PushCastMetrics {
  PushTypeMap ch_cast = 1;  // 基于pushCast通道的房间推送
  PushTypeMap un_cast = 2;  // 基于pushCast通道的用户推送
  PushTypeMap bc_cast = 3;  // 基于pushCast通道的全服推送
  PushTypeMap dc_cast = 4;  // 基于pushCast通道的全频推送
}

message PushAgentMetrics {
  PushTypeMap ch_rmq = 1;  // 基于PushAgent(RabbitMq)通道的房间推送
  PushTypeMap un_rmq = 2;  // 基于PushAgent(RabbitMq)通道的用户推送
  PushTypeMap bc_rmq = 3;  // 基于PushAgent(RabbitMq)通道的全服推送
  PushTypeMap dc_rmq = 4;  // 基于PushAgent(RabbitMq)通道的全频推送
  PushTypeMap gp_rmq = 5;  // 基于PushAgent(RabbitMq)通道的群组推送
  PushTypeMap ch_rds = 6;  // 基于PushAgent(Redis)通道的房间推送
  PushTypeMap un_rds = 7;  // 基于PushAgent(Redis)通道的用户推送
  PushTypeMap bc_rds = 8;  // 基于PushAgent(Redis)通道的全服推送
  PushTypeMap dc_rds = 9;  // 基于PushAgent(Redis)通道的全频推送
}

message PushProxyMetrics {
  PushTypeMap ch_rmq = 1;  // 基于pushProxy(RabbitMq)通道的房间推送
}

message GaProxyNotifyMetrics {
  PushTypeMap ch_rmq = 1;  // 基于PushAgent(RabbitMq)通道的房间推送
  PushTypeMap un_rmq = 2;  // 基于PushAgent(RabbitMq)通道的用户推送
  PushTypeMap bc_rmq = 3;  // 基于PushAgent(RabbitMq)通道的全服推送
  PushTypeMap dc_rmq = 4;  // 基于PushAgent(RabbitMq)通道的全频推送
  PushTypeMap gp_rmq = 5;  // 基于PushAgent(RabbitMq)通道的群组推送
  PushTypeMap ch_rds = 6;  // 基于PushAgent(RabbitMq)通道的房间推送
  PushTypeMap un_rds = 7;  // 基于PushAgent(RabbitMq)通道的用户推送
  PushTypeMap bc_rds = 8;  // 基于PushAgent(RabbitMq)通道的全服推送
  PushTypeMap dc_rds = 9;  // 基于PushAgent(RabbitMq)通道的全频推送
}

message GroupPushDetailData {
  string category = 1;
  bool important = 2;
  uint32 sequence = 3;
  uint32 group_id = 4;
  uint32 group_type = 5;
  string request_id = 6;
  uint32 push_type = 7;
  string service_name = 8;
  string push_channel = 9;
  uint32 handle_stage = 10;
  int64 time_stamp = 11;
  string describe = 12;
  uint32 dest_user_id = 13;
  string push_label = 14;
}

message ChanPushDetailData {
  string category = 1;
  bool important = 2;
  uint32 sequence = 3;
  uint32 channel_id = 4;
  string request_id = 5;
  uint32 push_type = 6;
  string service_name = 7;
  string push_channel = 8;
  uint32 handle_stage = 9;
  int64 time_stamp = 10;
  string describe = 11;
  uint32 dest_user_id = 12;
  string push_label = 13;
}

message UserPushDetailData {
  string category = 1;
  bool important = 2;
  uint32 sequence = 3;
  repeated uint64 user_id_list = 4;
  string request_id = 5;
  uint32 push_type = 6;
  string service_name = 7;
  string push_channel = 8;
  uint32 handle_stage = 9;
  int64 time_stamp = 10;
  string describe = 11;
  uint32 dest_user_id = 12;
  string push_label = 13;
}

message BroadcastDetailData {
  string category = 1;
  bool important = 2;
  uint32 sequence = 3;
  uint32 broadcast_id = 4;
  string request_id = 5;
  uint32 push_type = 6;
  string service_name = 7;
  string push_channel = 8;
  uint32 handle_stage = 9;
  int64 time_stamp = 10;
  string describe = 11;
  uint32 dest_user_id = 12;
  string push_label = 13;
}

message MetricsData {
  METRICS_SOURCE source = 1;
  bytes payload = 2;
}

// 注册漏斗模型在线推送业务类型 <PUSH_TYPE_MAX
// buf:lint:ignore ENUM_PASCAL_CASE
enum PUSH_TYPE {
  UNKNOWN = 0;
  TYPE_MAX = 65535;
}
