syntax="proto3";

import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/push_notification/v2/apns/apnsdef.proto";

// namespace
package push_notification.v2.apns;

option go_package = "golang.52tt.com/protocol/services/push-notification/v2/APNs;apns";

// 单推
message SinglecastNotification {
    uint32 uid = 1;
    // string from_account = 2;
    repeated uint32 target_app_list = 3;	// you have to specify at least one app (id)
    Notification body = 4;
    uint32 frequence = 5;
    repeated uint32 uid_list = 6;
}

message MulticastNotification {
    string multicast_account = 1;
    repeated uint32 exclude_uid_list = 2;
    repeated uint32 target_app_list = 3;	// you have to specify at least one app (id)
    Notification body = 4;
    uint32 frequence = 5;
}

// 指向性推送, 一般用于特殊用途...(比如踢下线)
message DirectiveNotification {
    UserDeviceToken user_device_token = 1;
    Notification body = 4;
}

//////////////////////////////////////////////////////////////////
// Mulicast Relationship
//////////////////////////////////////////////////////////////////

message UpdateMulticastRelationReq {
    enum CMD {
        CMD_Base = 0;
        CMD_Register = 1;
        CMD_Unregister = 2;
        CMD_Clean = 3;
    }
    uint32 cmd = 1;            // defined above
    repeated uint32 uid_list = 2;
    repeated string account_list = 3;   // list of multicast accounts
}

message UpdateMulticastRelationResp {
}

message QueryUserSubscribingAccountsReq {
    uint32 uid = 1;
}

message QueryUserSubscribingAccountsResp {
    repeated string account_list = 1;
}

message QueryAccountSubscribersReq {
    string account = 1;
}

message QueryAccountSubscribersResp {
    repeated uint32 uid_list = 2;
}

message Empty {}

service ApnsPush {
    option (service.options.old_package_name) = "PushNotification.APNs.ApnsPush";

    rpc PushNotification (SinglecastNotification) returns (Empty) {}
    rpc PushMulticastNotification (MulticastNotification) returns (Empty) {}
    rpc PushDirectiveNotification (DirectiveNotification) returns (Empty) {}

    rpc UpdateMulticastRelation (UpdateMulticastRelationReq) returns (UpdateMulticastRelationResp) {} 
}
