syntax="proto3";

import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/push_notification/v2/apns/apnsdef.proto";

option go_package = "golang.52tt.com/protocol/services/push-notification/v2/APNs;apns";

// namespace
package push_notification.v2.apns;

message RegisterDeviceTokenResp {

}

message UnregisterDeviceTokenResp {
    uint32 deleted = 1;
}

message UnregisterDeviceTokenReq {
     string token       = 1;
     uint32 timestamp   = 2;
     uint32 uid         = 3;
}

message QueryDeviceTokensReq {
    repeated uint32 app_id_list = 1;
    repeated uint32 uid_list    = 2;
}

message QueryDeviceTokensResp {
    repeated UserDeviceToken user_device_token_list = 1;
}

service ApnsToken {
    option (service.options.old_package_name) = "PushNotification.APNs.ApnsToken";

    rpc RegisterDeviceToken( UserDeviceToken ) returns( RegisterDeviceTokenResp ) {}
    rpc UnregisterDeviceToken( UnregisterDeviceTokenReq ) returns( UnregisterDeviceTokenResp ) {}
    rpc QueryDeviceTokens( QueryDeviceTokensReq ) returns (QueryDeviceTokensResp ) {}
}