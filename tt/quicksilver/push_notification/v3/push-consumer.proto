syntax = "proto3";

import "tt/quicksilver/extension/options/options.proto";

package push_notification.v3;

option go_package = "golang.52tt.com/protocol/services/push-notification/v3;push_server";


service PushConsumer {
  option (service.options.old_package_name) = "PushNotification.consumer.PushConsumer";

  // 初始化注册信息到redis中
  rpc InitPushCount(RegisterInfo) returns (Empty) {}
  // 累加推送次数
  rpc AddPushCount(RegisterInfo) returns (Empty) {}
  // 获取已推送次数
  rpc GetPushCount(RegisterInfo) returns (GetPushCountResp) {}
  // 忽略token更新事件的kafka消息数，用于临时清理kafka队列中的数据，慎用！！！
  rpc SetIgnoreTokenEventSize(SetIgnoreTokenEventSizeReq) returns (Empty) {}
}

message RegisterInfo {
    uint32 uid = 1;
    string pkg_channel = 2;
    uint32 version = 3;
    uint32 market_id = 4;
    uint32 limit_count = 5;
    uint32 limit_time = 6;
}

message Empty {
}

message GetPushCountResp {
    int64 count = 1;
}

message SetIgnoreTokenEventSizeReq {
  int64 count = 1;
}
