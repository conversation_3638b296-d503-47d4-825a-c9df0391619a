/*
 * push-v3-user 对接第三方推送平台的用户管理服务
 * 请勿直接RPC接口，代码路径中有cache包，可以缓存和自动刷新Token
 */

syntax = "proto3";

package push_notification.v3;

option go_package = "golang.52tt.com/protocol/services/push-notification/v3;push_server";

import "tt/quicksilver/extension/options/options.proto";

service PushUser {
  option (service.options.old_package_name) = "PushNotification.user.PushUser";

  // 个推离线推送平台
  rpc BindAlias(BindAliasReq) returns (BindAliasResp) {}
  rpc QueryAliasByCid(QueryAliasReq) returns (QueryAliasResp) {}
  rpc QueryCidByAlias(QueryCidReq) returns (QueryCidResp) {}
  rpc UnbindAlias(UnbindAliasReq) returns (UnbindAliasResp) {}
  rpc RevokeAlias(RevokeAliasReq) returns (RevokeAliasResp) {}
  rpc BindUserWithTag(BindUserWithTagReq) returns (BindUserWithTagResp) {}
  rpc BindTagWithUser(BindTagWithUserReq) returns (BindTagWithUserResp) {}
  rpc UnbindTagFromUser(UnbindTagFromUserReq) returns (UnbindTagFromUserResp) {}
  rpc QueryUserTag(QueryUserTagReq) returns (QueryUserTagResp) {}
  rpc AddBlackList(AddBlackListReq) returns (AddBlackListResp) {}
  rpc DelBlackList(DelBlackListReq) returns (DelBlackListResp) {}
  rpc QueryUserStatus(QueryUserStatusReq) returns (QueryUserStatusResp) {}
  rpc QueryDeviceStatus(QueryDeviceStatusReq) returns (QueryDeviceStatusResp) {}
  rpc QueryUserInfo(QueryUserInfoReq) returns (QueryUserInfoResp) {}
  rpc SetPushBadge(SetPushBadgeReq) returns (SetPushBadgeResp) {}
  rpc QueryUserCount(QueryUserCountReq) returns (QueryUserCountResp) {}
  rpc QueryReportData(QueryReportDataReq) returns (QueryReportDataResp) {}

  // 个推ido运营平台
  rpc QueryIdoTag(QueryIdoTagReq) returns (QueryIdoTagResp) {}
}

message BindAliasReq {
  message Data {
    string cid = 1;
    string alias = 2;
  }

  string push_agent = 1;
  string bundle_id = 2;
  repeated Data data_list = 3;
}

message BindAliasResp {
  // nothing
}

message QueryAliasReq {
  string push_agent = 1;
  string bundle_id = 2;
  string c_id = 3;
}

message QueryAliasResp {
  string alias = 1;
}

message QueryCidReq {
  string push_agent = 1;
  string bundle_id = 2;
  string alias = 3;
}

message QueryCidResp {
  repeated string c_id_list = 1;
}

message UnbindAliasReq {
  message Data {
    string cid = 1;
    string alias = 2;
  }

  string push_agent = 1;
  string bundle_id = 2;
  repeated Data data_list = 3;
}

message UnbindAliasResp {
  // nothing
}

message RevokeAliasReq {
  string push_agent = 1;
  string bundle_id = 2;
  string alias = 3;
}

message RevokeAliasResp {
  // nothing
}

message BindUserWithTagReq {
  string push_agent = 1;
  string bundle_id = 2;
  string c_id = 3;
  repeated string tag_list = 4;
}

message BindUserWithTagResp {
  // nothing
}

message BindTagWithUserReq {
  string push_agent = 1;
  string bundle_id = 2;
  string tag = 3;
  repeated string c_id_list = 4;
}

message BindTagWithUserResp {
  message Result {
    string c_id = 1;
    bool success = 2;
  }

  repeated Result result_list = 1;
}

message UnbindTagFromUserReq {
  string push_agent = 1;
  string bundle_id = 2;
  string tag = 3;
  repeated string c_id_list = 4;
}

message UnbindTagFromUserResp {
  message Result {
    string c_id = 1;
    bool success = 2;
  }

  repeated Result result_list = 1;
}

message QueryUserTagReq {
  string push_agent = 1;
  string bundle_id = 2;
  string c_id = 3;
}

message QueryUserTagResp {
  repeated string tag_list = 1;
}

message AddBlackListReq {
  string push_agent = 1;
  string bundle_id = 2;
  repeated string c_id_list = 3;
}

message AddBlackListResp {
  // nothing
}

message DelBlackListReq {
  string push_agent = 1;
  string bundle_id = 2;
  repeated string c_id_list = 3;
}

message DelBlackListResp {
  // nothing
}

message QueryUserStatusReq {
  string push_agent = 1;
  string bundle_id = 2;
  repeated string c_id_list = 3;
}

message QueryUserStatusResp {
  message UserStatus {
    string c_id = 1;
    string last_login_time = 2;
    string status = 3;
  }

  repeated UserStatus status_list = 1;
}

message QueryDeviceStatusReq {
  string push_agent = 1;
  string bundle_id = 2;
  repeated string c_id_list = 3;
}

message QueryDeviceStatusResp {
  message DeviceStatus {
    string c_id = 1;
    bool available = 2;
    string c_id_status = 3;
    string device_status = 4;
  }

  repeated DeviceStatus status_list = 1;
}

message QueryUserInfoReq {
  string push_agent = 1;
  string bundle_id = 2;
  repeated string c_id_list = 3;
}

message QueryUserInfoResp {
  message UserInfo {
    string c_id = 1;
    string client_app_id = 2;
    string package_name = 3;
    string device_token = 4;
    int32 phone_type = 5;
    string phone_model = 6;
    bool notification_switch = 7;
    string create_time = 8;
    int32 login_freq = 9;
  }

  repeated string invalid_list = 1;
  repeated UserInfo user_info_list = 2;
}

message SetPushBadgeReq {
  string push_agent = 1;
  string bundle_id = 2;
  repeated string c_id_list = 3;
  string operation = 4;
}

message SetPushBadgeResp {
  // nothing
}

message QueryUserCountReq {
  message Tag {
    string key = 1;
    repeated string values = 2;
    string opt_type = 3;
  }

  string push_agent = 1;
  string bundle_id = 2;
  repeated Tag tag_list = 3;
}

message QueryUserCountResp {
  int32 count = 1;
}

message QueryReportDataReq {
  uint32 uid = 1;
  string task = 2;
  string env = 3;
  string user = 4;
  string pwd = 5;
  uint32 last_hour = 6;
}

message QueryReportDataResp {
  repeated string result_list = 1;
}

message IdoTagReqItem {
  string push_agent = 1;
  string bundle_id = 2;
  string user_id = 3;     // gtcid
}

message IdoTags {
  int32 code = 1;         // 0:成功，其它值表示该user_id不存在有效标签
  string msg = 2;         // code为非0时存放具体描述
  string data = 3;        // 有效标签内容，json串, 格式为'{"custom":["自定义标签code列表"]，"gt":["个推标签code列表"], "external":["客户自有标签code列表，自有标签可通过页面、openapi根据用户id数据打标创建"]}'
}

message QueryIdoTagReq {
  repeated IdoTagReqItem item = 1;
}

message QueryIdoTagResp {
  map<string, IdoTags> user_tags = 2;  // key 为userId(gtcid)
}
