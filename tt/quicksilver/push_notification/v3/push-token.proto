syntax = "proto3";

package push_notification.v3;

option go_package = "golang.52tt.com/protocol/services/push-notification/v3;push_server";

import "tt/quicksilver/extension/options/options.proto";

service PushToken {
  option (service.options.old_package_name) = "PushNotification.token.PushToken";

  rpc RegisterDeviceToken(RegisterDeviceTokenReq) returns (RegisterDeviceTokenResp) {}
  rpc UnregisterDeviceToken(UnregisterDeviceTokenReq) returns (UnregisterDeviceTokenResp) {}
  rpc QueryDeviceToken(QueryDeviceTokenReq) returns (QueryDeviceTokenResp) {}
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum TOKEN_EVENT {
    REGISTER = 0;
    UN_REGISTER = 1;
}

message UserDeviceToken {
  uint32 uid = 1;        // uid
  uint32 app_id = 2;     // default 0
  uint32 token_type = 3; // APNs 0 UPush 1 OPPO 2 VIVO 3 GETUI 4 APPINTERNAL 5
  DeviceToken device_token = 4;
  uint32 market_id = 5; // tt 0 huanyou 2 zaiya 3
  TOKEN_EVENT token_event = 6; // 事件类型
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DeviceToken {
  string token = 1;      // android: cid of getui; ios: deviceToken
  string device_id = 2;  // I don't know
  int64 timestamp = 3;   // ts, server use time.Now() if not set
  uint32 os_type = 4;    // do not care
  string os_ver = 5;     // do not care
  uint32 app_id = 6;     // default 0
  uint32 app_ver = 7;    // do not care
  string sound = 8;      // do not care
  string voip_sound = 9; // do not care
  string bundle_id = 10; // com.yiyou.ga com.yiyou.tt com.yiyou.ga.debug com.yiyou.enterprise.tt
  uint32 market_id = 11; // tt 0 huanyou 2 zaiya 3
  string device_model = 12;     // 手机厂商
  uint32 client_type = 13;      // 安卓 0，ios 1, web 2, pcAssistant 3, car 4, pcTT 5, TxMini 6, Robot 253
  uint32 terminalType = 14;     // 终端类型
  uint32 client_version = 15;   // 客户端版本
  string pkg_channel = 16;      // 安装包渠道
  string ip = 17;          // 归因系统数据采集
  string user_agent = 18;  // 归因系统数据采集
  string manufacture_token = 19; // 手机厂商原生token
  string gt_user_cid = 20;       // 用于个推用户画像标签查询
}

message RegisterDeviceTokenReq {
  UserDeviceToken token = 1;
}

message RegisterDeviceTokenResp {
  // nothing
}

message UnregisterDeviceTokenReq {
  string token = 1;      // android: cid of getui; ios: deviceToken
  int64 timestamp = 2;   // ts, server use time.Now() if not set
  uint32 uid = 3;        // uid (can be 0 when token is not empty)
  uint32 app_id = 4;     // default 0
  uint32 token_type = 5; // device token type
  uint32 market_id = 6;  // tt 0 huanyou 2 zaiya 3
}

message UnregisterDeviceTokenResp {
  // nothing
}

message QueryDeviceTokenReq {
  repeated uint32 uid_list = 1;
  repeated string bundle_id = 2;
  uint32 limit = 998;        // of no use
  uint64 load_more = 999;    // of no use
  uint64 filter_mask = 1000; // of no use
}

message QueryDeviceTokenResp {
  repeated UserDeviceToken tokens = 1;
}
