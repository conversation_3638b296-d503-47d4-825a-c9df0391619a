syntax = "proto3";

package push_notification.v3;

option go_package = "golang.52tt.com/protocol/services/push-notification/v3;push_server";

import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/push_notification/v3/push-server.proto";

service PushProxy {
  option (service.options.old_package_name) = "PushNotification.proxy.PushProxy";

  // 按uid查询设备终端在线状态，目前只支持安卓个推用户
  rpc QueryDeviceStatusByUids(QueryDeviceStatusByUidsReq) returns (QueryDeviceStatusByUidsResp) {}
  // 运营类Push批量推送相同内容，活动类/广告类/拉活/召回/大批量集中推送等运营类Push使用
  rpc SendOpPush(SendOpPushReq) returns (SendOpPushResp) {}
  // 运营类Push批量推送个性化内容，活动类/广告类/拉活/召回/大批量集中推送等运营类Push使用
  rpc SendCustomOpPush(SendCustomOpPushReq) returns (SendCustomOpPushResp) {}
  // 核心业务批量推送，为避免核心业务消息堵塞，大批量集中推送以及非核心业务的场景禁止使用该接口
  rpc SendCorePush(SendCorePushReq) returns (SendCorePushResp) {}
  // 上传icon/picture至第三方厂商
  rpc XMUploadFile(XMUploadFileReq) returns (XMUploadFileResp) {}
  // 特定业务场景大批量推送，使用前需跟通道确认，需提前配置相应的业务类别及相关配置
  rpc SendCustomBusinessPush(SendCustomBusinessPushReq) returns (SendCustomBusinessPushResp) {}
}

// 区分公司不同app
// TODO: 补充其他appid
enum QwAppId {
  UNKNOWN = 0;
  TT = 1;         // TT语音
  HY = 2;         // 欢游
  MK = 8;         // 麦可，旧接口用一个整型参数不同位表示不同app，马甲包ID须跟push-server.proto NewTaskReq里面的app_platform保持兼容
  MJ = 16;        // 谜境
}

enum DeviceTokenType {
  APNS = 0;
  UPUSH = 1;
  OPPO = 2;
  VIVO = 3;
  GETUI = 4;
  APPINTERNAL = 5;
}

message QueryDeviceStatusByUidsReq {
  QwAppId appid = 1;
  repeated uint32 uid_list = 2;     // 不超过1000个uid，超过则返回错误
}

message QueryDeviceStatusByUidsResp {
  message DeviceStatus {// 设备状态信息
    uint32 uid = 1;
    bool available = 2;                 // 个推查询结果是否正常, false时表示该uid在个推查询时返回异常或者非个推用户
    string uid_status = 3;              // app在线状态，值为online或offline，online表示app在线，建议以我们后台用户在线管理进程状态为准
    string device_status = 4;           // 用户终端设备在线状态，值为online或offline，online表示用户设备有个推app处于活跃状态当中
    DeviceTokenType token_type = 5;     // 设备推送类型
  }

  map <uint32, DeviceStatus> device_status_map = 1;
}

// 运营类Push推送，批量uid推送相同内容时使用，非核心业务或大批量推送建议使用该接口
message SendOpPushReq {
  QwAppId appid = 1;
  string task_id = 2;                 // 任务id，必须填写
  Notification notification = 3;      // 推送内容
  repeated uint32 uid_list = 4;       // 推送目标，须<=1000个，否则返回错误
  int64 expired_time = 5;             // 失效时间戳。默认为当前时间延后24小时，受个推限制，最大不超过3天后的时间，调个推时设置的失效时间如果notification.extra中有key为ttl时会以ttl(失效秒数)为准
  repeated QwAppId appid_list = 6;    // appid列表，不填时默认按push-v3-proxy配置文件中的appid列表发送
}

message SendOpPushResp {
}

// 个性化运营类Push批量推送，每个uid都需要推送个性化内容时使用，非核心业务或大批量推送建议使用该接口
message SendCustomOpPushReq {
  QwAppId appid = 1;
  string task_id = 2;                 // 任务id，必须填写
  map<uint32, Notification> notification_map = 3;    // 个性化推送内容，请保证<=1000个，否则会返回错误
  int64 expired_time = 4;             // 失效时间戳。默认为当前时间延后24小时，受个推限制，最大不超过3天后的时间，调个推时设置的失效时间如果notification.extra中有key为ttl时会以ttl(失效秒数)为准
  repeated QwAppId appid_list = 5;    // appid列表，不填时默认按push-v3-proxy配置文件中的appid列表发送
}

message SendCustomOpPushResp {
}

// 核心业务批量推送，非核心业务禁止使用该接口，否则容易造成核心业务push消息堵塞
message SendCorePushReq {
  QwAppId appid = 1;
  string task_id = 2;                 // 任务id，必须填写
  Notification notification = 3;      // 推送内容
  repeated uint32 uid_list = 4;       // 推送目标，须<=1000个，否则返回错误
  int64 expired_time = 5;             // 失效时间戳。默认为当前时间延后24小时，受个推限制，最大不超过3天后的时间，调个推时设置的失效时间如果notification.extra中有key为ttl时会以ttl(失效秒数)为准
  repeated QwAppId appid_list = 6;    // appid列表，不填时默认按push-v3-proxy配置文件中的appid列表发送
}

message SendCorePushResp {
}

enum FileType {
  FT_UNKNOWN = 0;
  LARGE_ICON = 1;
  BIG_PICTURE = 2;
}

message XMUploadFileReq {
  FileType file_type = 1;
  string file_url = 2;
  QwAppId app_id = 3;
}

message XMUploadFileResp {
  string uri = 1;
  int64 ttl = 2;
}

message SendCustomBusinessPushReq {
  repeated QwAppId appid_list = 1;    // appid列表，不填时默认按push-v3-proxy配置文件中的appid列表发送
  string task_id = 2;                 // 任务id，必须填写
  map<uint32, Notification> notification_map = 3;    // 个性化推送内容，请保证<=1000个，否则会返回错误
  int64 expired_time = 4;             // 失效时间戳。默认为当前时间延后24小时，受个推限制，最大不超过3天后的时间，调个推时设置的失效时间如果notification.extra中有key为ttl时会以ttl(失效秒数)为准
  PushType push_type = 5;             // 特定业务场景，与Notification的PushType一致，务必保证该字段和notification_map里的pushType都要填
}

message SendCustomBusinessPushResp {
}