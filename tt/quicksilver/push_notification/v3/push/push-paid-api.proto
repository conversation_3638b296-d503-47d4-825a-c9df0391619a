syntax = "proto3";

// buf:lint:ignore PACKAGE_LOWER_SNAKE_CASE
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package PushNotification;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/push-notification/v3;push_server";

message OppoButton {
  string name = 1;
  int32 click_action_type = 2;
  string click_action_activity = 3;
  string click_action_url = 4;
  map<string, string> action_parameters = 5;
}

message OppoOption {
  message Filter {
    int32 unsupport_top_show = 1;
    int32 unsupport_unfold = 2;
    int32 unsupport_label = 3;
    int32 unsupport_skin = 4;
  }

  string notification_group = 1;
  string m_url = 2;
  bool senior_push = 3;
  bool unfold = 4;
  bool top_notification_bar_show = 5;
  string material_id = 6;    // 个推暂不支持
  int32 label_id = 7;        // 个推暂不支持
  Filter filter = 8;         // 个推暂不支持
  string advertiser_id = 9;  // 个推暂不支持
}

message OppoPaidApi {
  string small_picture_url = 1;  // 300字数内 144*144px <50k PNG/JPG/JPEG
  string medium_picture_url = 2; // 300字数内 <201*201px <256KB PNG/JPG/JPEG style必须为4
  string big_picture_url = 3;    // 300字数内 <876*324px <1M PNG/JPG/JPEG style必须为3
  repeated OppoButton button_list = 4;
  OppoOption option = 5;
}