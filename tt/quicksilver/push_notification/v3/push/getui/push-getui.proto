/**
 * robot_sync 自动生成后，在 push-getui.pb.go 目录执行 protoc-go-inject-tag -input="*.pb.go"
 * see more https://github.com/favadi/protoc-go-inject-tag
 * 原因： 个推API不统一，有一些奇葩的字段名需要单独指定，所有使用 @gotags 工具修改了 pb.go 文件
 * 涉及的字段有 IosChannel 中的 apns_collapse_id、content_available、thread_id 等
 *              AndroidChannel 中的 Options 及其内嵌字段
 */

syntax = "proto3";

// buf:lint:ignore PACKAGE_LOWER_SNAKE_CASE
package PushNotification.getui;

option go_package = "golang.52tt.com/protocol/services/push-notification/v3;push_server";

message Audience {
  message Tag {
    string key = 1;
    repeated string values = 2;
    string opt_type = 3;
  }

  repeated string cid = 1;
  repeated string alias = 2;
  repeated Tag tag = 3;
  string fast_custom_tag = 4;
}

message Settings {
  message Strategy {
    int32 default = 1;
    int32 ios = 2;
    int32 hw = 3;
    int32 ho = 4;
    int32 xm = 5;
    int32 xmg = 6;
    int32 vv = 7;
    int32 op = 8;
    int32 opg = 9;
    int32 mz = 10;
    int32 st = 11;
    int32 wx = 12;
  }

  int64 ttl = 1;
  Strategy strategy = 2;
  int32 speed = 3;
  int64 schedule_time = 4;
  string custom_callback = 5;
  bool filter_notify_off = 6;
  int32 active_days = 7;
}

message PushMessage {
  message Notification {
    string title = 1;
    string body = 2;
    string big_text = 3;
    string big_image = 4;
    string logo = 5;
    string logo_url = 6;
    string channel_id = 7;
    string channel_name = 8;
    string channel_level = 9;
    string click_type = 10;
    string intent = 11;
    string url = 12;
    string payload = 13;
    int64 notify_id = 14;
    string ring_name = 15;
    int32 badge_add_num = 16;
    string thread_id = 17;
  }

  message Revoke {
    string old_task_id = 1;
    bool force = 2;
  }

  string duration = 1;
  Notification notification = 2;
  string transmission = 3;
  Revoke revoke = 4;
}

message IosChannel {
  message Aps {
    message Alert {
      string title = 1;
      string body = 2;
      // @gotags: json:"action-loc-key,omitempty"
      string action_loc_key = 3 [json_name = "action-loc-key"];
      // @gotags: json:"loc-key,omitempty"
      string loc_key = 4 [json_name = "loc-key"];
      // @gotags: json:"loc-args,omitempty"
      repeated string loc_args = 5 [json_name = "loc-args"];
      // @gotags: json:"launch-image,omitempty"
      string launch_image = 6 [json_name = "launch-image"];
      // @gotags: json:"title-loc-key,omitempty"
      string title_loc_key = 7 [json_name = "title-loc-key"];
      // @gotags: json:"title-loc-args,omitempty"
      repeated string title_loc_args = 8 [json_name = "title-loc-args"];
      string subtitle = 9;
      // @gotags: json:"subtitle-loc-key,omitempty"
      string sub_title_loc_key = 10 [json_name = "subtitle-loc-key"];
      // @gotags: json:"subtitle-loc-args,omitempty"
      repeated string sub_title_loc_args = 11 [json_name = "subtitle-loc-args"];
    }

    Alert alert = 1;
    // @gotags: json:"content-available,omitempty"
    int32 content_available = 2 [json_name = "content-available"];
    string sound = 3;
    string category = 4;
    // @gotags: json:"thread-id,omitempty"
    string thread_id = 5 [json_name = "thread-id"];
    string timestamp = 6;
    string event = 7;
    // @gotags: json:"content-state,omitempty"
    map<string, string> content_state = 8 [json_name = "content-state"];
  }
  message Multimedia {
    string url = 1;
    int32 type = 2;
    bool only_wifi = 3;
  }

  string type = 1;
  Aps aps = 2;
  string auto_badge = 3;
  string payload = 4;
  repeated Multimedia multimedia = 5;
  // @gotags: json:"apns-collapse-id,omitempty"
  string apns_collapse_id = 6 [json_name = "apns-collapse-id,omitempty"];
}

message AndroidChannel {
  message Ups {
    message Notification {
      string title = 1;
      string body = 2;
      string click_type = 3;
      string intent = 4;
      string url = 5;
      int64 notify_id = 6;
    }
    message Revoke {
      string old_task_id = 1;
    }
    message Options {
      message HW {
        // @gotags: json:"/message/android/notification/badge/class,omitempty"
        string badge_class = 1 [json_name = "/message/android/notification/badge/class,omitempty"];
        // @gotags: json:"/message/android/notification/badge/add_num,omitempty"
        int32  badge_add_num = 2 [json_name = "/message/android/notification/badge/add_num,omitempty"];
        // @gotags: json:"/message/android/notification/badge/set_num,omitempty"
        int32  badge_set_num = 3 [json_name = "/message/android/notification/badge/set_num,omitempty"];
        // @gotags: json:"/message/android/notification/image,omitempty"
        string image = 4 [json_name = "/message/android/notification/image,omitempty"];
        // @gotags: json:"/message/android/notification/style,omitempty"
        int32 style = 5 [json_name = "/message/android/notification/style,omitempty"];
        // @gotags: json:"/message/android/notification/big_title,omitempty"
        string big_title = 6 [json_name = "/message/android/notification/big_title,omitempty"];
        // @gotags: json:"/message/android/notification/big_body,omitempty"
        string big_body = 7 [json_name = "/message/android/notification/big_body,omitempty"];
        // @gotags: json:"/message/android/notification/importance,omitempty"
        string importance = 8 [json_name = "/message/android/notification/importance,omitempty"];
        // @gotags: json:"/message/android/notification/default_sound,omitempty"
        bool default_sound = 9 [json_name = "/message/android/notification/default_sound,omitempty"];
        // @gotags: json:"/message/android/notification/channel_id,omitempty"
        string channel_id = 10 [json_name = "/message/android/notification/channel_id,omitempty"];
        // @gotags: json:"/message/android/notification/sound,omitempty"
        string sound = 11 [json_name = "/message/android/notification/sound,omitempty"];
        // @gotags: json:"/message/android/category,omitempty"
        string category = 12 [json_name = "/message/android/category,omitempty"];
        // @gotags: json:"/message/android/target_user_type,omitempty"
        int32 target_user_type = 13 [json_name = "/message/android/target_user_type,omitempty"];
      }
      message HO {
        // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
        message Button {
          string name = 1;
          int32 actionType = 2;
          int32 intentType = 3;
          string intent = 4;
          string data = 5;
        }

        // @gotags: json:"/android/notification/badge/badgeClass,omitempty"
        string badge_class = 1 [json_name = "/android/notification/badge/badgeClass,omitempty"];
        // @gotags: json:"/android/notification/badge/addNum,omitempty"
        int32 badge_add_num = 2 [json_name = "/android/notification/badge/addNum,omitempty"];
        // @gotags: json:"/android/notification/badge/setNum,omitempty"
        int32 badge_set_num = 3 [json_name = "/android/notification/badge/setNum,omitempty"];
        // @gotags: json:"/android/notification/icon,omitempty"
        string icon = 4 [json_name = "/android/notification/icon,omitempty"];
        // @gotags: json:"/android/notification/image,omitempty"
        string image = 5 [json_name = "/android/notification/image,omitempty"];
        // @gotags: json:"/android/notification/style,omitempty"
        int32 style = 6 [json_name = "/android/notification/style,omitempty"];
        // @gotags: json:"/android/notification/bigTitle,omitempty"
        string big_title = 7 [json_name = "/android/notification/bigTitle,omitempty"];
        // @gotags: json:"/android/notification/bigBody,omitempty"
        string big_body = 8 [json_name = "/android/notification/bigBody,omitempty"];
        // @gotags: json:"/android/notification/notifySummary,omitempty"
        string notifiy_summary = 9 [json_name = "/android/notification/notifySummary,omitempty"];
        // @gotags: json:"/android/notification/importance,omitempty"
        string importance = 10 [json_name = "/android/notification/importance,omitempty"];
        // @gotags: json:"/android/notification/buttons,omitempty"
        repeated Button buttons = 11 [json_name = "/android/notification/buttons,omitempty"];
        // @gotags: json:"/android/notification/when,omitempty"
        string when = 12 [json_name = "/android/notification/when,omitempty"];
      }
      // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
      message XM {
        // @gotags: json:"/extra.channel_id,omitempty"
        string channel_id = 1 [json_name = "/extra.channel_id,omitempty"];
        // @gotags: json:"/extra.notification_style_type,omitempty"
        string notification_style_type = 2 [json_name = "/extra.notification_style_type,omitempty"];
        // @gotags: json "/extra.notification_bigPic_uri,omitempty"
        string notification_bigPic_uri = 3 [json_name = "/extra.notification_bigPic_uri,omitempty"];
        // @gotags: json:"/extra.notification_large_icon_uri,omitempty"
        string notification_large_icon_uri = 4 [json_name = "/extra.notification_large_icon_uri,omitempty"];
        // @gotags: json:"/extra.sound_uri,omitempty"
        string sound_uri = 6 [json_name = "/extra.sound_uri,omitempty"];
        // @gotags: json:"/extra.locale,omitempty"
        string locale = 7 [json_name = "/extra.locale,omitempty"];
        // @gotags: json:"/extra.locale_not_in,omitempty"
        string locale_not_in = 8 [json_name = "/extra.locale_not_in,omitempty"];
        // @gotags: json:"/extra.model,omitempty"
        string model = 9 [json_name = "/extra.model,omitempty"];
        // @gotags: json:"/extra.model_not_in,omitempty"
        string model_not_in = 10 [json_name = "/extra.model_not_in,omitempty"];
        // @gotags: json:"/extra.app_version,omitempty"
        string app_version = 11 [json_name = "/extra.app_version,omitempty"];
        // @gotags: json:"/extra.app_version_not_in,omitempty"
        string app_version_not_in = 12 [json_name = "/extra.app_version_not_in,omitempty"];
        // @gotags: json:"/extra.notification_style_button_left_notify_effect,omitempty"
        string style_button_left_notify_effect = 13 [json_name = "/extra.notification_style_button_left_notify_effect,omitempty"];
        // @gotags: json:"/extra.notification_style_button_left_name,omitempty"
        string style_button_left_name = 14 [json_name = "/extra.notification_style_button_left_name,omitempty"];
        // @gotags: json:"/extra.notification_style_button_left_intent_uri,omitempty"
        string style_button_left_intent_uri = 15 [json_name = "/extra.notification_style_button_left_intent_uri,omitempty"];
        // @gotags: json:"/extra.notification_style_button_left_web_uri,omitempty"
        string style_button_left_web_uri = 16 [json_name = "/extra.notification_style_button_left_web_uri,omitempty"];
        // @gotags: json:"/extra.notification_style_button_left_intent_class,omitempty"
        string style_button_left_intent_class = 17 [json_name = "/extra.notification_style_button_left_intent_class,omitempty"];
        // @gotags: json:"/extra.notification_style_button_right_notify_effect,omitempty"
        string style_button_right_notify_effect = 18 [json_name = "/extra.notification_style_button_right_notify_effect,omitempty"];
        // @gotags: json:"/extra.notification_style_button_right_name,omitempty"
        string style_button_right_name = 19 [json_name = "/extra.notification_style_button_right_name,omitempty"];
        // @gotags: json:"/extra.notification_style_button_right_intent_uri,omitempty"
        string style_button_right_intent_uri = 20 [json_name = "/extra.notification_style_button_right_intent_uri,omitempty"];
        // @gotags: json:"/extra.notification_style_button_right_web_uri,omitempty"
        string style_button_right_web_uri = 21 [json_name = "/extra.notification_style_button_right_web_uri,omitempty"];
        // @gotags: json:"/extra.notification_style_button_right_intent_class,omitempty"
        string style_button_right_intent_class = 22 [json_name = "/extra.notification_style_button_right_intent_class,omitempty"];
        // @gotags: json:"/extra.notification_style_button_mid_notify_effect,omitempty"
        string style_button_mid_notify_effect = 23 [json_name = "/extra.notification_style_button_mid_notify_effect,omitempty"];
        // @gotags: json:"/extra.notification_style_button_mid_name,omitempty"
        string style_button_mid_name = 24 [json_name = "/extra.notification_style_button_mid_name,omitempty"];
        // @gotags: json:"/extra.notification_style_button_mid_intent_uri,omitempty"
        string style_button_mid_intent_uri = 25 [json_name = "/extra.notification_style_button_mid_intent_uri,omitempty"];
        // @gotags: json:"/extra.notification_style_button_mid_web_uri,omitempty"
        string style_button_mid_web_uri = 26 [json_name = "/extra.notification_style_button_mid_web_uri,omitempty"];
        // @gotags: json:"/extra.notification_style_button_mid_intent_class,omitempty"
        string style_button_mid_intent_class = 27 [json_name = "/extra.notification_style_button_mid_intent_class,omitempty"];
        // @gotags: json:"/time_to_live,omitempty"
        int64 time_to_live = 28 [json_name = "/time_to_live,omitempty"];
        // @gotags: json:"/extra.only_send_once,omitempty"
        string only_send_once = 29 [json_name = "/extra.only_send_once,omitempty"];
        // @gotags: json:"/time_to_send,omitempty"
        int64 time_to_send = 30 [json_name = "/time_to_send,omitempty"];
      }
      message OP {
        // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
        message BUTTON {
          // @gotags: json:"name,omitempty"
          string name = 1 [json_name = "name,omitempty"];
          // @gotags: json:"clickActionType,omitempty"
          int32 clickActionType = 2 [json_name = "clickActionType,omitempty"];
          // @gotags: json:"clickActionUrl,omitempty"
          string clickActionUrl = 3 [json_name = "clickActionUrl,omitempty"];
          // @gotags: json:"clickActionActivity,omitempty"
          string clickActionActivity = 4 [json_name = "clickActionActivity,omitempty"];
          // @gotags: json:"actionParameters,omitempty"
          map<string, string> actionParameters = 5 [json_name = "actionParameters,omitempty"];
        }
        message OPTION {
          message FILTER {
            // @gotags: json:"unsupport_top_show,omitempty"
            int32 unsupport_top_show = 1 [json_name = "unsupport_top_show,omitempty"];
            // @gotags: json:"unsupport_unfold,omitempty"
            int32 unsupport_unfold = 2 [json_name = "unsupport_unfold,omitempty"];
            // @gotags: json:"unsupport_label,omitempty"
            int32 unsupport_label = 3 [json_name = "unsupport_label,omitempty"];
            // @gotags: json:"unsupport_skin,omitempty"
            int32 unsupport_skin = 4 [json_name = "unsupport_skin,omitempty"];
          }

          // @gotags: json:"notification_group,omitempty"
          string notification_group = 1 [json_name = "notification_group,omitempty"];
          // @gotags: json:"m_url,omitempty"
          string m_url = 2 [json_name = "m_url,omitempty"];
          // @gotags: json:"senior_push,omitempty"
          bool senior_push = 3 [json_name = "senior_push,omitempty"];
          // @gotags: json:"unfold,omitempty"
          bool unfold = 4 [json_name = "unfold,omitempty"];
          // @gotags: json:"top_notification_bar_show,omitempty"
          bool top_notification_bar_show = 5 [json_name = "top_notification_bar_show,omitempty"];
          // @gotags: json:"material_id,omitempty"
          string material_id = 6 [json_name = "material_id,omitempty"];
          // @gotags: json:"label_id,omitempty"
          int32 label_id = 7 [json_name = "label_id,omitempty"];
          // @gotags: json:"filter,omitempty"
          FILTER filter = 8 [json_name = "filter,omitempty"];
          // @gotags: json:"advertiser_idp,omitempty"
          string advertiser_id = 9 [json_name = "advertiser_id,omitempty"];
        }

        // @gotags: json:"/channel_id,omitempty"
        string channel_id = 1 [json_name = "/channel_id,omitempty"];
        // @gotags: json:"/style,omitempty"
        int32 style = 2 [json_name = "/style,omitempty"];
        // @gotags: json:"/small_picture_id,omitempty"
        string small_picture_id = 3 [json_name = "/small_picture_id,omitempty"];
        // @gotags: json:"/big_picture_id,omitempty"
        string big_picture_id = 4 [json_name = "/big_picture_id,omitempty"];
        // @gotags: json:"/app_message_id,omitempty"
        string app_message_id = 5 [json_name = "/app_message_id,omitempty"];
        // @gotags: json:"/show_time_type,omitempty"
        int32 show_time_type = 6 [json_name = "/show_time_type,omitempty"];
        // @gotags: json:"/show_start_time,omitempty"
        int64 show_start_time = 7 [json_name = "/show_start_time,omitempty"];
        // @gotags: json:"/show_end_time,omitempty"
        int64 show_end_time = 8 [json_name = "/show_end_time,omitempty"];
        // @gotags: json:"/off_line,omitempty"
        bool off_line = 9 [json_name = "/off_line,omitempty"];
        // @gotags: json:"/off_line_ttl,omitempty"
        int32 off_line_ttl = 10 [json_name = "/off_line_ttl,omitempty"];
        // @gotags: json:"/show_ttl,omitempty"
        int32 show_ttl = 11 [json_name = "/show_ttl,omitempty"];
        // @gotags: json:"/small_picture_url,omitempty"
        string small_picture_url = 12 [json_name = "/small_picture_url,omitempty"];
        // @gotags: json:"/medium_picture_url,omitempty"
        string medium_picture_url = 13 [json_name = "/medium_picture_url,omitempty"];
        // @gotags: json:"/big_picture_url,omitempty"
        string big_picture_url = 14 [json_name = "/big_picture_url,omitempty"];
        // @gotags: json:"/buttons,omitempty"
        repeated BUTTON buttons = 15 [json_name = "/buttons,omitempty"];
        // @gotags: json:"/option,omitempty"
        OPTION option = 16 [json_name = "/option,omitempty"];
        // @gotags: json:"/category,omitempty"
        string category = 17 [json_name = "/category,omitempty"];
        // @gotags: json:"/notify_level,omitempty"
        int32 notify_level = 18 [json_name = "/notify_level,omitempty"];
      }
      message VV {
        // @gotags: json:"/classification,omitempty"
        int32 classification = 1 [json_name = "/classification,omitempty"];
        // @gotags: json:"/notifyType,omitempty"
        int32 notify_type = 2 [json_name = "/notifyType,omitempty"];
        // @gotags: json:"/networkType,omitempty"
        int32 network_type = 3 [json_name = "/networkType,omitempty"];
        // @gotags: json:"/timeToLive,omitempty"
        int32 time_to_live = 4 [json_name = "/timeToLive,omitempty"];
        // @gotags: json:"/category,omitempty"
        string category = 5 [json_name = "/category,omitempty"];
        // @gotags: json:"/pushMode,omitempty"
        int32 push_mode = 6 [json_name = "/pushMode,omitempty"];
      }
      message UPS {
        // @gotags: json:"bigText,omitempty"
        string big_text = 1 [json_name = "bigText,omitempty"];
        // @gotags: json:"bigImageUrl,omitempty"
        string big_image_url = 2 [json_name = "bigImageUrl,omitempty"];
      }

      // @gotags: json:"HW,omitempty"
      HW hw = 1 [json_name = "HW,omitempty"];
      // @gotags: json:"XM,omitempty"
      XM xm = 2 [json_name = "XM,omitempty"];
      // @gotags: json:"OP,omitempty"
      OP op = 3 [json_name = "OP,omitempty"];
      // @gotags: json:"VV,omitempty"
      VV vv = 4 [json_name = "VV,omitempty"];
      // @gotags: json:"HO,omitempty"
      HO ho = 5 [json_name = "HO,omitempty"];
      // @gotags: json:"XMG,omitempty"
      XM xmg = 6 [json_name = "XMG,omitempty"];
      // @gotags: json:"OPG,omitempty"
      OP opg = 7 [json_name = "OPG,omitempty"];
      // @gotags: json:"UPS,omitempty"
      UPS ups = 20 [json_name = "UPS,omitempty"];
    }

    Notification notification = 1;
    string transmission = 2;
    Revoke revoke = 3;
    Options options = 4;
  }

  Ups ups = 1;
}

message MiniProgram {
  message WX {
    string templated_id = 1;
    string page = 2;
    string miniprogram_state = 3;
    string lang = 4;
    map<string, string> data = 5;
  }

  WX wx = 1;
}

message PushChannel {
  IosChannel ios = 1;
  AndroidChannel android = 2;
  MiniProgram mp = 3;
}
