syntax = "proto3";

package push_notification.v3;

option go_package = "golang.52tt.com/protocol/services/push-notification/v3;push_server";

import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/push_notification/v3/push-server.proto";

service PushOps {
  option (service.options.old_package_name) = "PushNotification.ops.PushOps";

  // 批量推送任务
  // -Step 1, 创建任务
  rpc SendPush(SendPushReq) returns (SendPushResp) {}
  // -Step 2, 指定推送目标
  rpc BatchTargetUidList(BatchTargetUidListReq) returns (BatchTargetUidListResp) {}
}

enum PushSource {
  SOURCE_UNDEFINED = 0;            // 非法值
  SOURCE_SPECIFIED_CONDITION = 1;  // 不支持 !
  SOURCE_SPECIFIED_TT_ACCOUNT = 2; // 不支持 !
  SOURCE_SPECIFIED_TT_UID = 3;     // 支持
}

message SendPushReq {
  message BaseInfo {
    uint32 platform = 1;     // bitmap 苹果 1<<0 安卓 1<<4
    string title = 2;        // 标题
    string content = 3;      // 内容
    string jump_url = 4;     // 跳转链接
    uint32 notify_type = 5;  // 不支持 !
    uint32 app_platform = 6; // 仅支持 TT语音 1<<0

    repeated NotificationText notification_title = 21;   // 自定义标题
    repeated NotificationText notification_content = 22; // 自定义内容

    repeated string labels = 23; // 推送标签
    string icon_url = 24;        // 推送图片url
    string voice_type = 25;      // 声音选择
    string icon_type = 26;       // 推送图片url类型 eg: small big
    PushType push_type = 27;     // 定义在 push-server.proto

    map<string, string> extra = 28;  // 扩展字段
  }

  message Condition {
    string login_time_start = 1;  // 不支持 !
    string login_time_end = 2;    // 不支持 !
    bool login_include_today = 3; // 不支持 !
    uint32 gender = 4;            // 不支持 !
    string province = 5;          // 不支持 !
    string city = 6;              // 不支持 !
    uint32 android_version = 7;   // 不支持 !
    uint32 ios_version = 8;       // 不支持 !
  }

  BaseInfo base_info = 1;             // 必填
  PushSource push_source = 2;         // 必填 仅支持 3(SOURCE_SPECIFIED_TT_UID)
  Condition condition = 3;            // 不支持 !
  repeated string target_id_list = 4; // 内容需和 push_source 对应
  bool test = 5;                      // 仅检查参数是否有效
  uint32 invalid_time = 6;            // 推送失效时间
  bool batches_upload_target = 7;     // 分批传递 target_ttid_list
  uint64 seqid = 8;                   // 请求id，供服务端去重使用
  uint32 priority = 9;                // 优先级，数值越大优先级越高
}

message SendPushResp {
  string task_id = 1; // 测试模式下 TEST 前缀，正常模式下 QW 前缀
  uint32 result = 2;  // 0为正常，1为重复请求，其余为异常
}

message BatchTargetUidListReq {
  string task_id = 1;
  repeated string target_id_list = 2;
  bool latest_commit = 3; // true: 标志分批上传结束
  PushSource push_source = 4;
  uint64 seqid = 5;       // 请求id，供服务端去重使用
  uint32 priority = 6;    // 优先级，数值越大优先级越高
  PushType push_type = 7;
}

message BatchTargetUidListResp {
  uint32 result = 1; // 0为正常，1为重复请求，其余为异常
}
