syntax = "proto3";

package push_notification.v3;

option go_package = "golang.52tt.com/protocol/services/push-notification/v3;push_server";

import "tt/quicksilver/extension/options/options.proto";

service Push {
  option (service.options.old_package_name) = "PushNotification.Push";

  // 批量推送任务
  // -Step 1, 创建推送任务，推送目标>2000使用Step 2保存推送目标
  rpc NewTask (NewTaskReq) returns (NewTaskResp) {}
  // -Step 2，对Step 1创建的任务设置推送条件，推送目标量少跳过
  rpc InitPartOfTask (InitPartOfTaskReq) returns (InitPartOfTaskResp) {}
  // -Step 3，保存Step 2任务，等待分发，推送目标量少跳过
  rpc SaveTask (SaveTaskReq) returns (SaveTaskResp) {}
  // 直推任务
  rpc SendPushTask (SendPushTaskReq) returns (SendPushTaskResp) {}
}

// 直推
message SendPushTaskReq {
  Notification notification = 1;      // 推送内容
  repeated uint32 uid_list = 2;       // 推送目标
  uint32 app_platform = 3;            // 可多选 bitmap tt:1<<0 huanyou:1<<1 zaiya:1<<2
  PushTaskReportType report_type = 4; // 数据组特定情况下需要上报数据过去，做个标记
  string task_id = 10;                // 如果需要自己定义task id就传
}
message SendPushTaskResp {
  string task_id = 1;
}

// 运营批量推送 step1
message NewTaskReq {
  Notification notification = 1;      // 推送内容
  repeated uint32 token_type = 2;     // 指定APNs, UPush, OPPO, VIVO推送，默认为0全部...
  uint32 push_mode = 4;               // -- Deprecated 可多选 bitmap default 0 with nickname 1<<0 ,with lbs 1<<1
  repeated uint32 uid_list = 5;       // 指定uid推送，推送量大用户id使用InitPartOfTask传，不在这里传递
  uint64 push_at = 6;                 // 期望何时推送，10位时间戳
  uint32 app_platform = 7;            // 可多选 bitmap  tt:1<<0 huanyou:1<<1 zaiya:1<<2 maike:1<<3
  string task_id = 8;                 // 如果需要自己定义task id就传
  PushTaskReportType report_type = 9; // 数据组特定情况下需要上报数据过去，做个标记

  bool without_ttl = 20;                               // 传true任务信息不会删除，默认发送后15天自动删除任务
  repeated NotificationText notification_title = 21;   // 自定义通知标题，可添加昵称、lbs项，不同用户收到信息内容不同
  repeated NotificationText notification_content = 22; // 自定义通知内容，可添加昵称、lbs项，不同用户收到信息内容不同
  uint32 priority = 23;               // 优先级，数值越大优先级越高
}

message NewTaskResp {
  string task_id = 1;
}

// 运营批量推送 step2
// 调用示例
// {task_id: t1, sequence: 0, uid_list: u1,u2...u20000}
// {task_id: t1, sequence: 1, uid_list: u20001,u20002...u40000}
// {task_id: t1, sequence: 2, uid_list: u40001,u40002...u60000}
// {task_id: t1, sequence: 3, uid_list: u60001,u60002...u60009}
// 传完用户id之后，调用一下SaveTask接口
message InitPartOfTaskReq {
  string task_id = 1;
  repeated uint32 uid_list = 2; // 建议小于20000个为一组来调
  uint32 sequence = 3;          // 从0开始, 客户端每次保存uid列表之后+1
  PushType push_type = 4;        // 业务类型
}

message InitPartOfTaskResp {
  // nothing
}

// 运营批量推送 step3
message SaveTaskReq {
  string task_id = 1;
  uint64 push_at = 5; // 期望何时推送，10位时间戳，到秒
  uint32 priority = 6; // 优先级 (0-999)
}

message SaveTaskResp {
  // nothing
}

message UserFilter {
  repeated uint32 user_id = 1; // 指定uid推送
  repeated uint32 except_user_id = 2; //指定uid不推送
}

message Filter {
  // bool broadcast = 1; // 广播推送
  repeated uint32 token_type = 2; // 需要的运营商类型(token type)
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message Notification {
  string title = 1;
  string content = 2;
  string jump_url = 3;
  // 需要平台透传上报请使用下面的report_json字段，如若一定要用extra上报内容，请保证key名为全小写字母和下划线（数据平台要求）
  // ios会直接透传该字段给客户端，安卓则只获取部分字段塞进个推intent，功能与其他字段有重复使用的情况，历史原因，请不要使用重复key名
  // 目前已使用的key名: push_type/opt_user/contentId/content_id/transmission/labels/itemId/phone/u/act_id/task_id/sub_task_id/icon_url/voice_type/icon_type/expired_time
  // 付费推送接口：op_paid_api/vv_paid_api/xm_paid_api/hw_paid_api/ho_paid_api 以此类推 填充 push-paid-api.proto 中对应 message 的序列化内容
  map<string, string> extra = 4; // content_id: 策略ID xm_style_type(1多字/2大图) xm_pic_url xm_icon_url: 小米富文本推送
  string remark = 5;          // 推送通知的注释，直接透传上报给数据中心统计
  repeated string labels = 6; // 推送通知的注释，直接透传上报给数据中心统计
  string icon_url = 7;        // 推送图片url
  string voice_type = 8;      // 推送指定声音类型
  string icon_type = 10;      // 推送图片url类型 eg: small , big
  PushType pushType = 11;
  OptUser optUser = 12;
  bool with_monitoring = 13;
  PushPriority push_priority = 14; // 第三方厂商所需参数
  string report_json = 15;         // json字符串格式
}

enum ElementType {
  TEXT = 0;     // 项为纯文本
  NICKNAME = 1; // 项为显示昵称
  LBS = 2;      // 项为显示LBS信息
}

message NotificationElement {
  ElementType type = 1; // 哪个类型
  string text = 2;      // type为TEXT时的文案
}

message NotificationText {
  repeated NotificationElement text = 1;
}

message PushTask {
  string id = 1;
  uint64 create_at = 2; // 直推：= load_at                 | 运营推送：创建时间，step1调用时创建时间戳
  uint64 load_at = 3;   // 直推：分发时间戳，下一步写kafka | 运营推送：分发时间，pick之后创建subtask的时间戳，下一步写kafka
  uint64 done_at = 4;
  uint64 start_at = 5;

  enum Status {
    INIT = 0;     // 待检索分发
    LOADING = 1;  // 正在分发
    FINISHED = 2; // 已分发完成
    TIMEOUT = 3; // 处理超时
  }

  Status status = 6;
  string origin = 7;
  Notification notification = 8;
  Filter filter = 9;
  UserFilter user_filter = 10;
  repeated uint32 token_type = 11; // 指定APNs, UPush, OPPO, VIVO类型推送
  uint32 push_mode = 13;           // -- Deprecated

  message StatInfo {
    uint32 receiver = 1;
    uint32 success = 2;
    uint32 fail = 3;
    uint32 click = 4;
    uint32 arrival = 5;
  }
  map<string, StatInfo> stat_info = 20; // type -> info

  uint32 app_platform = 14; // 可多选 bitmap tt:1<<0 huanyou:1<<1 zaiya:1<<2;
  bool without_ttl = 15;    // 传true任务信息不会删除，默认发送后15天自动删除任务
  PushTaskReportType report_type = 16;

  repeated NotificationText notification_title = 21;   // 自定义通知标题，可添加昵称、lbs项，不同用户收到信息内容不同
  repeated NotificationText notification_content = 22; // 自定义通知内容，可添加昵称、lbs项，不同用户收到信息内容不同
}

message PushTaskEvent {
  string sub_task_id = 1;
  string task_id = 2;
  int32 device_token_type = 3;
  repeated TokenInfo tokens = 4;
  bool is_same_notification = 5;
  string bundle_id = 6;
  uint64 start_at = 7;        // 任务设置推送的时间(到这个时间就可以分发)
  uint64 load_at = 8;         // 开始分发的时间
}

message TokenInfo {
  string token = 1;
  string bundle_id = 2;
  uint32 uid = 3;
  uint32 app_id = 4;
  string device_id = 5;
  bytes presence_data = 7; // 在线信息
  Notification notification = 8;
  uint32 market_id = 9;
  uint32 os_type = 10;
  string device_model = 11; // 手机厂商信息
  string ip = 12;           // 归因系统数据采集
  string user_agent = 13;   // 归因系统数据采集
}

// 此枚举填写在notification extra 字段  key:push_type val:
enum PushType {
  Normal = 0;   // 默认运营后台触发的
  Attitude = 1; // 点赞
  Follow = 2;   // 关注
  Comment = 3;  // 评论产生
  UgcAt = 4;    // @
  Im = 5;       //

  LiveStart = 7;       // 语音直播
  FollowNewPost = 8;   // 玩伴发新动态
  UserInvite = 9;      // 用户被邀请
  ProvidePlayer = 10;  // 发放玩伴
  NewPlayer = 11;      // 新玩伴推送
  BeVisitorCount = 12; // 谁看过我推送
  FloatingWindow = 13;

  // MiniGamePlatform = 14;     // 小游戏平台调用

  FollowEnterRoom = 15;                // 关注好友进房通知
  MiniGamePlatform = 19;               // 小游戏平台调用
  SubNewUserRecall_UGC = 20;           // 次新用户UGC场景召回
  SubNewUserRecall_PGC = 21;           // 次新用户PGC场景召回
  SubNewUserRecall_ImChat = 22;        // 次新用户IM聊天场景召回
  SubNewUserRecall_UserBeVisited = 23; // 次新用户谁看过我场景
  SubNewUserRecall_RobotMsg = 24;      // 次新用户im假消息召回
  SubNewUserRecall_SMS = 25;           // 次新用户短信召回
  SubNewUserRecall_UserFeed = 26;      // 次新用户动态广场场景召回

  ChannelRelationRecall_ImLink = 27;   // 房间关系召回im链接推送场景召回
  ChannelRelationRecall_ChannelLink = 28;   // 房间关系召回房间链接推送场景召回

  RecallNewUser_PulishTopicChannel = 29;      // 新游承接 发布的房间推送

  UserRelationRecall_ImNotRead = 30;  //用户关系未读消息场景召回
  UserRelationRecall_ImNotDeal = 31;  //用户关系未处理im消息场景召回
  RecallDeclineUser = 32;      // 衰退期用户上报
  UserRelationRecall_RuleRcmdChannel = 33; //关系链召回发放房间规则版推荐推送策略
  RecallMinigameUser = 34;      // 小游戏用户召回
  MusicNestStartRecall = 35;    //乐窝开播召回

  SquareTopic = 36;      // 广场话题推送
  CommPopUpWindow = 37;  // 端内统一弹窗推送
  OperationRelation = 38; // 运营推送使用关系链

  MaikeChannel = 39;//麦克房间推送
  EnterRoom = 40; // 房客进房通知
  MysteryPlaceRecallOneDay = 41; // 秘境新注册用户召回（当天）
  MysteryPlaceRecallTwoDay = 42; // 秘境新注册用户召回（第二天）
  MysteryPlaceRecallThreeDay = 43; // 秘境新注册用户召回（第三天）
  NightPlan = 44;   //晚安计划，用户召回推送
  MysteryPlace_FriendOnline = 45; // 谜境用户上线场景，离线好友召唤推送
  SocialCommunityGroup = 46;      // 社区群聊
  ManufacturePaid = 47;           // 厂商付费推送
  MijingPartnerCards = 48; // 谜境找搭子推送
  MiddleArrivePaid = 49; // 中台触达付费场景
  MiddleArrivePaidManufacture = 50; // 中台触达付费场景付费推送
  MiddleDecline = 51; // 中台触达衰退期推送
  MiJingChatBot = 52; // 谜境聊天机器人
  ScenarioSaveNotification = 53; // 剧本存档召回
  MiJingSquare = 54;             // 谜境广场
  MiJingSquareSubNew = 55;       // 谜境广场次新
  MiJingScenarioReviews = 56;    // 谜境剧本评价
  MiJingScenarioReviewsSubNew = 57;    // 谜境剧本评价次新
  SignInRobbedExchangeActivityRemind = 58;   // 签到抢兑活动提醒
  ImAppoint  = 59; // im预约
  MijingScenarioRecommend = 60; // 谜境剧本推荐
  MijingScenarioContinuePlay = 61; // 谜境剧本续玩
  MijingScenarioCollectRemind = 62; // 谜境收藏剧本提醒
  GameHall = 63; // 组队大厅
  EnterRoomNotify = 64; // 进房提醒
  FriendReturnNotify = 65; // 好友回流提醒，林俊杨
  UserOnlineNotify = 66; // 用户上线提醒，冯灿威
  AICupidRecallNotify= 67;  //AI红娘次日召回消息， 于洋
}

message StartFreshmanRecallReq{
  PushType push_type = 1;
  repeated uint32 white_list = 2; // 当存在白名单时，此次推送仅对白名单用户生效
}
message StartFreshmanRecallResp{

}

// 此枚举填写在notification extra 字段  key:opt_user val:
enum OptUser {
  Human = 0; //
  Robot = 1; //
}

enum PushTaskReportType {
  None = 0;           // 默认不用上报到数据中心
  Operation = 1;      // 运营后台推送出去的
  SignInActivity = 2; // 签到领皮肤活动的推送任务
}

// 指定push channel消息类型限制等 
// https://dev.mi.com/console/doc/detail?pId=2086
enum PushPriority {
  PUSH_PRIORITY_UNDEFINED = 0;
  PUSH_PRIORITY_NORMAL = 1;    // 普通消息
  PUSH_PRIORITY_IMPORTANT = 2; // 重要消息
}

message AckNotification {
  enum Action {
    ARRIVAL = 0;
    CLICK = 1;
    DISPLAY = 2;
  }

  enum PushChannel {
    UNKNOW = 0;
    IOS = 1;
    UPUSH = 2;
    OPPO = 3;
    VIVO = 4;
    GETUI_OFFLINE = 5;
    GETUI_ONLINE = 6;
    TT = 7;
  }

  uint32 uid = 1;
  uint32 app_id = 2;
  Action action = 3;
  string device_id = 5;
  string push_type = 6;
  string opt_user = 7;

  PushChannel push_channel = 8;
  uint32 market_id = 9;
  string task_id = 10;
  string device_model = 11; // 手机厂商信息
}
