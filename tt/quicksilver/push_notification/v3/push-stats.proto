/*
 * push-v3-stats 对接第三方推送平台的数据统计服务
 * 请勿直接RPC接口，代码路径中有cache包，可以缓存和自动刷新Token
 * Query*接口中的taskId/groupId为个推的taskId，含RAS*或者RAS*前缀
 * Save*接口中的taskId为push系统生成的taskId
 */

syntax = "proto3";

package push_notification.v3;

option go_package = "golang.52tt.com/protocol/services/push-notification/v3;push_server";

import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/push_notification/v3/push-server.proto";

service PushStats {
  option (service.options.old_package_name) = "PushNotification.stats.PushStats";

  rpc QueryTasks(QueryTasksReq) returns (QueryTasksResp) {}
  rpc QueryTaskGroup(QueryTaskGroupReq) returns (QueryTaskGroupResp) {}
  rpc QueryPushCount(QueryPushCountReq) returns (QueryPushCountResp) {}
  rpc QueryPushDataByDay(QueryPushDataByDayReq) returns (QueryPushDataByDayResp) {}
  rpc QueryUserDataByDay(QueryUserDataByDayReq) returns (QueryUserDataByDayResp) {}
  rpc QueryOnlineUserBy24H(QueryOnlineUserBy24HReq) returns (QueryOnlineUserBy24HResp) {}
  rpc QueryTaskSchedule(QueryTaskScheduleReq) returns (QueryTaskScheduleResp) {}
  rpc QueryTaskDetail(QueryTaskDetailReq) returns (QueryTaskDetailResp) {}
  rpc SaveTaskId(SaveTaskIdReq) returns (SaveTaskIdResp) {}
  // 弃用，改用UpdatePushTotalStatV2
  rpc UpdatePushTotalStat(UpdatePushTotalStatReq) returns (UpdatePushTotalStatResp) {}
  // 弃用，改用UpdateTaskStatV2
  rpc UpdateTaskStat(UpdateTaskStatReq) returns (UpdateTaskStatResp) {}
  rpc UpdatePushTotalStatV2(UpdatePushTotalStatReqV2) returns (UpdatePushTotalStatResp) {}
  rpc UpdateTaskStatV2(UpdateTaskStatReqV2) returns (UpdateTaskStatResp) {}
  rpc UpdateFunnelTaskTime(UpdateFunnelTaskTimeReq) returns (UpdateFunnelTaskTimeResp) {}
  rpc CheckTotalStat(CheckTotalStatReq) returns (CheckTotalStatResp) {}
}

message BaseStatics {
  message Total {
    int32 target = 1;
    int32 receive = 2;
    int32 display = 3;
    int32 click = 4;
    int32 msg = 5;
  }
  message Detail {
    int32 target = 1;
    int32 receive = 2;
    int32 display = 3;
    int32 click = 4;
    string manufacturer = 5;
  }
  message Action {
    string key = 1;
    int32 value = 2;
  }

  Total total = 1;
  repeated Detail detail_list = 2;
  repeated Action action_list = 3;
}

message BasePushCount {
  string manufacturer = 1;
  string describe_tag = 2;
  int32 total_num = 3;
  int32 push_num = 4;
  int32 remain_num = 5;
  bool has_limit = 6;
}

message QueryTasksReq {
  string push_agent = 1;
  string bundle_id = 2;
  repeated string task_list = 3;
}

message QueryTasksResp {
  repeated string task_list = 1;
  repeated BaseStatics statics_list = 2;
}

message QueryTaskGroupReq {
  string push_agent = 1;
  string bundle_id = 2;
  string group_id = 3;
}

message QueryTaskGroupResp {
  BaseStatics statics = 1;
}

message QueryPushCountReq {
  string push_agent = 1;
  string bundle_id = 2;
}

message QueryPushCountResp {
  repeated BasePushCount count_list = 1;
}

message QueryPushDataByDayReq {
  string push_agent = 1;
  string bundle_id = 2;
  int64 unix_second = 3;
}

message QueryPushDataByDayResp {
  string date = 1;
  BaseStatics statics = 2;
}

message QueryUserDataByDayReq {
  string push_agent = 1;
  string bundle_id = 2;
  int64 unix_second = 3;
}

message QueryUserDataByDayResp {
  string date = 1;
  int32 accumulative = 2;
  int32 register = 3;
  int32 active = 4;
  int32 online = 5;
}

message QueryOnlineUserBy24HReq {
  string push_agent = 1;
  string bundle_id = 2;
}

message QueryOnlineUserBy24HResp {
  message OnlineInfo {
    int64 unix_millisecond = 1;
    int32 online = 2;
  }

  string date = 1;
  repeated OnlineInfo online_list = 2;
}

message QueryTaskScheduleReq {
  string push_agent = 1;
  string bundle_id = 2;
  string task_id = 3;
}

message QueryTaskScheduleResp {
  message ScheduleInfo {
    string item = 1;
    string desc = 2;
  }

  repeated ScheduleInfo schedule_list = 1;
}

message QueryTaskDetailReq {
  string push_agent = 1;
  string bundle_id = 2;
  string task_id = 3;
  string c_id = 4;
}

message QueryTaskDetailResp {
  message EventInfo {
    string time = 1;
    string event = 2;
  }

  repeated EventInfo event_list = 1;
}

// --------------------------------------------------------------------------

message PushMsgInfo {
  string task_id = 1;     // taskId from push-ops
  string t_id = 2;        // taskId from getui
  string c_id = 3;        // device_token, alias cid
  uint32 u_id = 4;        // uid from push-v3-token
  PushType push_type = 5; // define in push-server.proto Message Notification
  string app_id = 6; // 归因系统数据采集
  string user_ip = 7; // 归因系统数据采集
  string user_agent = 8; // 归因系统数据采集
}

message SaveTaskIdReq {
  string task_id = 1; // taskId from push-ops
  repeated PushMsgInfo info_list = 2;
}

message SaveTaskIdResp {
  // nothing
}

message PushTaskStat {
  string task_id = 1;
  string token_type = 2;
  int64 create_time = 3;
  int64 select = 4;
  int64 stored = 5;
  int64 queued = 6;
  int64 target = 7;
  int64 submit = 8;
  int64 arrive = 9;
  int64 click = 10;
  int64 last_update = 11;
  int64 pickup_time = 12;
}

message SaveTaskStatReq {
  repeated PushTaskStat stat_list = 1;
}

message SaveTaskStatResp {
  // nothing
}

message PushStatValue {
  string day = 1;
  int32 push_type = 2;
  int64 create_time = 3;
  int64 received = 4;
  int64 queued = 5;
  int64 target = 6;
  int64 submit = 7;
  int64 arrive = 8;
  int64 click = 9;
  int64 online = 10;
  int64 hw = 11;
  int64 xm = 12;
  int64 op = 13;
  int64 vv = 14;
  int64 apns = 15;
  int64 other = 16;
  int64 online_ar = 17;
  int64 gt_ar = 18;  // 弃用
  int64 apns_ar = 19;
  int64 hw_ar = 20;
  int64 xm_ar = 21;
  int64 op_ar = 22;
  int64 vv_ar = 23;
  int64 other_ar = 24;
}

message UpdatePushTotalStatReq {
  map<string, PushStatValue> total_stat = 1;
}

message PushStatValueV2 {
  string day = 1;
  int32 push_type = 2;
  int64 create_time = 3;
  // 各环节发送数量，key值: pushCore.SectionType
  map<int32, int64> section_count = 7;
  // 各手机厂家或在线通道下发数量，key值: pushCore.FunnelChannelType
  map<int32, int64> channel_send_count = 8;
  // 各手机厂家或在线通道到达数量，依赖客户端ack上报，key值: pushCore.FunnelChannelType
  map<int32, int64> channel_arrived_count = 9;
}

message UpdatePushTotalStatReqV2 {
  map<string, PushStatValueV2> total_stat = 1;
}

message UpdatePushTotalStatResp {
  // nothing
}

message TaskStatValue {
  string task_id = 1;
  int64 create_time = 2;
  int64 start_time = 3;
  int64 update_time = 4;
  int64 selected = 5;
  int64 received = 6;
  int64 queued = 7;
  int64 target = 8;
  int64 submit = 9;
  int64 arrive = 10;
  int64 click = 11;
  int64 online = 12;
  int64 hw = 13;
  int64 xm = 14;
  int64 op = 15;
  int64 vv = 16;
  int64 apns = 17;
  int64 other = 18;
  int64 end_time = 19;
}

message UpdateTaskStatReq {
  map<string, TaskStatValue> task_stat = 1;
}
message TaskStatValueV2 {
  string task_id = 1;
  int64 create_time = 2;
  int64 update_time = 3;
  int64 start_time = 4;
  int64 end_time = 5;
  // 各环节发送数量，key值: pushCore.SectionType
  map<int32, int64> section_count = 6;
  // 各手机厂家或在线通道下发数量，key值: pushCore.FunnelChannelType
  map<int32, int64> channel_send_count = 7;
}

message UpdateTaskStatReqV2 {
  map<string, TaskStatValueV2> task_stat = 1;
}

message UpdateTaskStatResp {
  // nothing
}

message UpdateTaskTimeReq {
  string task_id = 1;
  int64 start_time = 2;
}

message UpdateTaskTimeResp {
  // nothing
}

message TaskTime {
  int64 start_time = 1;
  int64 end_time = 2;
}

message UpdateFunnelTaskTimeReq {
  map<string, TaskTime> task_time = 1;
}

message UpdateFunnelTaskTimeResp {
  // nothing
}

message CheckTotalStatReq {
  int32 days = 1;
}

message CheckTotalStatResp {
  // nothing
}

