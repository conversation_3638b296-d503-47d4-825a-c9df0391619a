/*
 * push-v3-auth 对接第三方推送平台的凭据管理服务
 * 请勿直接RPC接口，代码路径中有cache包，可以缓存和自动刷新Token
 */

syntax = "proto3";

import "tt/quicksilver/extension/options/options.proto";

package push_notification.v3;

option go_package = "golang.52tt.com/protocol/services/push-notification/v3;push_server";

service PushAuth {
  option (service.options.old_package_name) = "PushNotification.auth.PushAuth";

  // 获取平台Token
  rpc GetAuthToken(GetAuthTokenReq) returns (GetAuthTokenResp) {}
  // 注销平台Token
  rpc DelAuthToken(DelAuthTokenReq) returns (DelAuthTokenResp) {}
}

message GetAuthTokenReq {
  string push_agent = 1;
  string bundle_id = 2;
}

message GetAuthTokenResp {
  string token = 1;
  int64 expire_at = 2; // unix_second Token过期时间
}

message DelAuthTokenReq {
  string push_agent = 1;
  string bundle_id = 2;
  string token = 3;
}

message DelAuthTokenResp {
  // nothing
}