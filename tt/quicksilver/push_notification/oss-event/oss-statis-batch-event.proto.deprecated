syntax = "proto2";

package ttdata;
option go_package = "golang.52tt.com/protocol/services/push-notification/oss-event;ossEvent";


message OssColumn {
  required uint32 idx = 1;
  required string val = 2;
  optional string key = 3;
}

message OssStatisRow {
  repeated OssColumn values = 1;
}

message OssStatisBatchEvent {
  required string src_ip = 1;
  required uint32 server_timestamp = 2;
  required string biz_type = 3;
  repeated OssStatisRow row_list = 4;
}
