syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/comm-search-index";
package comm_search_index;

service CommSearchIndex {

  // music search index
  rpc AddChannelMusicSearchIndex(AddChannelMusicSearchIndexReq) returns (AddChannelMusicSearchIndexResp) {}
  rpc DelChannelMusicSearchIndex(DelChannelMusicSearchIndexReq) returns (DelChannelMusicSearchIndexResp) {}
  rpc UpdateChannelMusicSearchIndex(UpdateChannelMusicSearchIndexReq) returns (UpdateChannelMusicSearchIndexResp) {}

  // community search index
  // 社团搜索索引写入
  rpc AddCommunityIndex(AddCommunityIndexReq) returns (AddCommunityIndexResp) {}

  // 社团搜索索引删除
  rpc DelCommunityIndex(DelCommunityIndexReq) returns (DelCommunityIndexResp) {}
}

message MusicInfo {
  uint32 id = 1;  // music id
  string oid = 2; // mongodb object id
  string name = 3;  // music name
  string artist = 4;  // music artist
}

message MusicIdInfo {
  uint32 id = 1;   // music id
  string oid = 2;  // mongodb object id
}

message AddChannelMusicSearchIndexReq {
  MusicInfo music_info = 1;
}

message AddChannelMusicSearchIndexResp {

}

message DelChannelMusicSearchIndexReq {
  repeated MusicIdInfo music_id_list = 1;
}

message DelChannelMusicSearchIndexResp {

}

message UpdateChannelMusicSearchIndexReq {
  MusicInfo music_info = 1;
}

message UpdateChannelMusicSearchIndexResp {

}

message AddCommunityIndexReq {
  string community_id = 1;   // 社团 id
  string community_name = 2; // 社团名称
}

message AddCommunityIndexResp {

}

message DelCommunityIndexReq {
  string community_id = 1;   // 社团 id
}

message DelCommunityIndexResp {
}