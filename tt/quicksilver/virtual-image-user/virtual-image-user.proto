syntax = "proto3";

package virtual_image_user;
import "tt/quicksilver/extension/options/options.proto";
option go_package = "golang.52tt.com/protocol/services/virtual-image-user";

service VirtualImageUser {
    option (service.options.service_ext) = {
        service_name: "virtual-image-user"
      };

    // 获取用户虚拟形象列表
    rpc GetUserVirtualImageList(GetUserVirtualImageListReq) returns (GetUserVirtualImageListResp) {}

    // 批量获取用户虚拟形象列表
    rpc BatchGetUserInuseItemInfo(BatchGetUserInuseItemInfoReq) returns (BatchGetUserInuseItemInfoResp) {}

    // 发放虚拟形象
    rpc GiveVirtualImageToUser(GiveVirtualImageToUserReq) returns (GiveVirtualImageToUserResp) {}

    // 保存套装搭配
    rpc SaveUserSuit(SaveUserSuitReq) returns (SaveUserSuitResp) {}

    // 获取用户保存套装搭配
    rpc GetUserSaveSuitList(GetUserSaveSuitListReq) returns (GetUserSaveSuitListResp) {}

    // 使用虚拟形象
    rpc SetUserVirtualImageInUse(SetUserVirtualImageInUseReq) returns (SetUserVirtualImageInUseResp) {}

    // 设置用户虚拟形象朝向
    rpc SetUserVirtualImageOrientation(SetUserVirtualImageOrientationReq) returns (SetUserVirtualImageOrientationResp) {}

    // 获取用户外显数据
    rpc GetUserDisplayData(GetUserDisplayDataReq) returns (GetUserDisplayDataResp) {}

    // 获取用户关系绑定列表
    rpc GetUserRelationList(GetUserRelationListRequest) returns (GetUserRelationListResponse) {}

    // 发起关系绑定邀请
    rpc SetBindInvite(SetBindInviteRequest) returns (SetBindInviteResponse) {}

    // 取消关系绑定邀请
    rpc CancelBindInvite(CancelBindInviteRequest) returns (CancelBindInviteResponse) {}

    // 接受绑定邀请
    rpc AcceptBindInvite(AcceptBindInviteRequest) returns (AcceptBindInviteResponse) {}

    // 拒绝绑定邀请
    rpc RejectBindInvite(RejectBindInviteRequest) returns (RejectBindInviteResponse) {}

    // 获取关系绑定请求状态
    rpc GetBindInviteStatus(GetBindInviteStatusRequest) returns (GetBindInviteStatusResponse) {}

    // 解除绑定
    rpc UnbindVirtualImage(UnbindVirtualImageRequest) returns (UnbindVirtualImageResponse) {}

    // 指定使用双人关系
    rpc SetVirtualBindInUse(SetVirtualBindInUseRequest) returns (SetVirtualBindInUseResponse) {}

    // 获取用户使用中的双人关系
    rpc GetUserRelationInUse(GetUserRelationInUseRequest) returns (GetUserRelationInUseResponse) {}

    // 获取用户外显开关列表
    rpc GetUserDisplaySwitch(GetUserDisplaySwitchRequest) returns (GetUserDisplaySwitchResponse) {}

    // 批量获取用户外显开关列表
    rpc BatchGetUserDisplaySwitch(BatchGetUserDisplaySwitchRequest) returns (BatchGetUserDisplaySwitchResponse) {}

    // 设置用户外显开关
    rpc SetUserDisplaySwitch(SetUserDisplaySwitchRequest) returns (SetUserDisplaySwitchResponse) {}

    // 获取用户待处理的关系绑定邀请列表
    rpc GetBindBeInvitedList(GetBindBeInvitedListRequest) returns (GetBindBeInvitedListResponse) {}

    // 获取用户物品获取记录
    rpc GetUserItemObtainRecord(GetUserItemObtainRecordReq) returns (GetUserItemObtainRecordResp) {}

    // 获取跟随进房时的虚拟形象信息
    rpc GetFollowEnterChannelInfo(GetFollowEnterChannelInfoRequest) returns (GetFollowEnterChannelInfoResponse) {}

    // 设置虚拟形象姿势类型
    rpc SetVirtualImagePoseType(SetVirtualImagePoseTypeRequest) returns (SetVirtualImagePoseTypeResponse) {}

    // 获取新手引导
    rpc GetVirtualImageBeginnerGuide(GetVirtualImageBeginnerGuideRequest) returns (GetVirtualImageBeginnerGuideResponse) {}

    // 标记新手引导完成
    rpc MarkVirtualImageBeginnerGuideDone(MarkVirtualImageBeginnerGuideDoneRequest) returns (MarkVirtualImageBeginnerGuideDoneResponse) {}

}

message UserItemInfo {
  uint32 user_item_id = 1;  // 用户物品id, 不存在则为0
  uint32 cfg_id = 2;        // 形象组件配置id
  int64 expire_time = 3;    // 过期时间
  bool in_use = 4;          // 是否正在使用
  uint32 sub_category = 5;  // 组件品类
  int64 update_time = 6;    // 更新时间
}

message UserSuitInfo {
  uint32 suit_id = 1;  // 套装id
  string name = 2;     // 套装名称
  repeated UserItemInfo items = 3;  // 套装包含的物品
  int64 expire_time = 4;  // 过期时间
  int64 update_time = 5;    // 更新时间
  string suit_icon = 6;
  string level_icon = 7;
  uint32 promotion_resource_id = 8; // 宣传片资源id
  uint32 special_pose_id = 9; // 特殊姿势id
}

// 获取用户虚拟形象列表
message GetUserVirtualImageListReq {
  uint32 uid = 1;  // 用户id
}

message GetUserVirtualImageListResp {
  repeated UserItemInfo items = 1;  // 用户虚拟形象列表
  repeated UserSuitInfo suits = 2;  // 用户虚拟形象套装列表
  uint32 orientation = 3; // 朝向， see VirtualImageOrientation
  repeated InuseItemInfo inuse_items = 4;  // 用户使用中的虚拟形象列表，包含未拥有的物品
}

enum VirtualImageOrientation {
  VIRTUAL_IMAGE_ORIENTATION_UNSPECIFIED = 0;
  VIRTUAL_IMAGE_ORIENTATION_RIGHT = 1;  // 朝右
  VIRTUAL_IMAGE_ORIENTATION_LEFT = 2;   // 朝左
}

// 设置用户虚拟形象朝向
message SetUserVirtualImageOrientationReq {
  uint32 uid = 1;
  uint32 orientation = 2; // 朝向， see VirtualImageOrientation
}

message SetUserVirtualImageOrientationResp {
}

message InuseItemInfo {
  uint32 cfg_id = 1;        // 形象组件配置id
  uint32 sub_category = 2;  // 组件品类
  int64 expire_time = 3;    // 过期时间
  uint32 use_rights_type = 4; // 权益类型 see virtual_image_logic.proto VirtualImageRightType
}

message UserInuseItemInfo {
  uint32 uid = 1;  // 用户id
  repeated InuseItemInfo items = 2;  // 用户虚拟形象列表
  uint32 orientation = 3;  // 朝向， see VirtualImageOrientation
}

// 批量获取用户虚拟形象列表
message BatchGetUserInuseItemInfoReq {
  repeated uint32 uid_list = 1;  // 用户id列表
}

message BatchGetUserInuseItemInfoResp {
  repeated UserInuseItemInfo user_inuse_item_info = 1;  // 用户虚拟形象列表
}

// 获取用户外显数据
message GetUserDisplayDataReq {
  uint32 uid = 1; // 用户uid
}

message GetUserDisplayDataResp {
  UserInuseItemInfo user_inuse_item_info = 1; // 用户虚拟形象列表
  repeated DisplaySwitch user_display_switch = 2;
  repeated PoseInfo pose_info = 4[deprecated=true]; // 姿势信息, 废弃

  UserInuseItemInfo relation_user_inuse_item_info = 3; // 关系绑定对象虚拟形象列表
}

message ItemInfo {
  uint32 cfg_id = 1;        // 形象组件配置id
  uint32 sub_category = 2;  // 组件品类
  uint32 use_rights_type = 3; // 在使用时有效，权益类型 see virtual_image_logic.proto VirtualImageRightType
}

message ItemGiveInfo {
  string order_id = 1;      // 订单号，用于唯一标识物品发放
  int32 duration_sec = 2;   // 发放时长(小于0时为回收)，秒级
  ItemInfo item = 3;        // 物品信息
  uint32 item_price = 4;    // 物品价格,单位T豆
}

message SuitGiveInfo {
  string order_id = 1;
  string suit_name = 2;  // 套装名称
  int32 duration_sec = 3;   // 发放时长(小于0时为回收)，秒级
  repeated ItemInfo items = 4;  // 套装包含的物品
  string suit_icon = 5; // 套装icon
  string level_icon = 6;// 套装等级icon
  uint32 suit_price = 7; // 套装价格,单位T豆
  uint32 promotion_resource_id = 8; // 宣传片资源id
}

enum VirtualImageGainSource {
  VIRTUAL_IMAGE_GAIN_UNSPECIFIED = 0;
  VIRTUAL_IMAGE_GAIN_MALL = 1;  // 商城
  VIRTUAL_IMAGE_GAIN_ACTIVITY = 2;  // 虚拟形象二期活动
  VIRTUAL_IMAGE_GAIN_MALL_FREE = 3;  // 商城免费商品
  VIRTUAL_IMAGE_GAIN_WEDDING = 4;   // 婚礼
  VIRTUAL_IMAGE_GAIN_PLATFORM = 5;  // 运营平台
}

// 发放虚拟形象
// 可通过套装进行发放，也可通过组件物品列表进行发放
message GiveVirtualImageToUserReq {
  uint32 uid = 1;
  uint32 source = 2;  // 发放来源, see VirtualImageGainSource
  repeated ItemGiveInfo items = 3;  // 发放的物品列表
  repeated SuitGiveInfo suits = 4;  // 发放的套装列表
  int64 outside_time = 5; // 外部发放时间，秒级时间戳

  string source_desc = 6; // 发放来源描述,（用于用户的获取记录的展示，非商城购买的来源需要填写，比如"超级航海活动获得"，"xxxx兑换"）
}

message GiveVirtualImageToUserResp {
}

// 保存套装搭配
message SaveUserSuitReq {
  uint32 uid = 1;
  uint32 suit_id = 2; // 套装id，0表示新增
  repeated uint32 cfg_id_list = 3;
}

message SaveUserSuitResp {
}

// 获取用户保存套装搭配
message GetUserSaveSuitListReq {
  uint32 uid = 1;
}

message GetUserSaveSuitListResp {
  repeated UserSuitInfo suits = 1;
}

// 使用虚拟形象
message SetUserVirtualImageInUseReq {
  uint32 uid = 1;
  repeated ItemInfo items = 2;
  bool is_full_update = 3;  // true: 全量覆盖用户已使用的物品；false: 仅更新相应类型物品
}

message SetUserVirtualImageInUseResp {}

message ItemObtainRecord{
    string record_id = 1; // 记录id
    uint32 item_type = 2; // 物品类型
    ItemInfo item_info = 3; // 单品信息
    SuitGiveInfo suit_info = 4; // 套装信息
    uint32 total_price = 5; // 总价

    uint32 obtain_source = 6; // 获取来源
    string obtain_desc = 7; // 获取来源描述
    int64 obtain_time = 8; // 获取时间
    int64 expire_time = 9; // 过期时间
    int64 valid_sec = 10;  // 发放时长
}

// 获取用户物品获取记录
message GetUserItemObtainRecordReq {
  uint32 uid = 1;
  uint32 limit = 2;
  string offset = 3;
}

message GetUserItemObtainRecordResp {
  repeated ItemObtainRecord record_list = 1;
  string next_offset = 2;
}

// ========================== 关系绑定 ==========================

message GetUserRelationListRequest {
    uint32 uid = 1; // 用户uid
}

message GetUserRelationListResponse {
    repeated uint32 bind_list= 1;    // 已绑定的用户uid列表
    repeated uint32 invite_list = 2; // 已发出的邀请列表
    uint32 max_bind_count = 3;       // 最大绑定数量
}

// 发起/取消 关系绑定邀请
message SetBindInviteRequest {
    uint32 uid = 1;      // 用户uid
    uint32 target_uid = 2; // 目标用户uid
}

message SetBindInviteResponse {
}

message CancelBindInviteRequest {
    uint32 uid = 1;      // 用户uid
    uint32 target_uid = 2; // 目标用户uid
    string invite_id = 3; // 邀请id
}

message CancelBindInviteResponse {

}

message AcceptBindInviteRequest {
    uint32 uid = 1;       // 用户uid
    string invite_id = 2; // 邀请id
}

message AcceptBindInviteResponse {
}

message RejectBindInviteRequest {
    uint32 uid = 1;       // 用户uid
    string invite_id = 2; // 邀请id
}

message RejectBindInviteResponse {
}

enum BindInviteStatus {
    BIND_INVITE_STATUS_UNSPECIFIED = 0; // 无效值，不展示弹窗
    BIND_INVITE_STATUS_UNHANDLED = 1; // 未处理
    BIND_INVITE_STATUS_ACCEPTED = 2;  // 已同意
    BIND_INVITE_STATUS_REJECTED = 3;  // 已拒绝
    BIND_INVITE_STATUS_EXPIRED = 4;   // 已失效
    BIND_INVITE_STATUS_CANCELED = 5;  // 已取消
}

// 获取关系绑定请求状态
message GetBindInviteStatusRequest {
    uint32 uid = 1;  // 用户uid
    string invite_id = 2;  // 邀请id
}

message GetBindInviteStatusResponse {
    BindInviteInfo info = 1; 
}

// 解除绑定
message UnbindVirtualImageRequest {
    uint32 uid = 1; // 用户uid
    uint32 target_uid = 2; // 目标用户uid
}

message UnbindVirtualImageResponse {
}

// 指定使用双人关系
message SetVirtualBindInUseRequest {
    uint32 uid = 1; // 用户uid
    uint32 target_uid = 2; // 目标用户target_uid,为0时表示使用单人模式
}

message SetVirtualBindInUseResponse {
}

// 获取用户使用中的双人关系
message GetUserRelationInUseRequest {
    uint32 uid = 1; // 用户uid
}

message GetUserRelationInUseResponse {
    uint32 target_uid = 1; // 目标用户uid,为0时表示使用单人模式
}

message DisplaySwitch {
  uint32 type = 1; // 开关类型(场景) see virtual_image_logic.proto VirtualImageDisplaySwitch
  bool switch_on = 2; // 开关状态
  uint32 pose_type = 3; // 姿势类型 see virtual_image_logic.proto VirtualImagePoseType
}

message PoseInfo {
  uint32 scene = 1; // 场景类型 see virtual_image_logic.proto VirtualImageDisplaySwitch
  uint32 pose_type = 2; // 姿势类型 see virtual_image_logic.proto VirtualImagePoseType
}

message GetUserDisplaySwitchRequest {
    uint32 uid = 1; // 用户uid
}

message GetUserDisplaySwitchResponse {
    repeated DisplaySwitch display_switch = 1; // 用户开关列表
}

message UserDisplaySwitch {
  uint32 uid = 1; // 用户uid
  repeated DisplaySwitch display_switch = 2; // 用户开关列表
}

message BatchGetUserDisplaySwitchRequest {
  repeated uint32 uid_list = 1; // 用户uid列表
}

message BatchGetUserDisplaySwitchResponse {
  repeated UserDisplaySwitch user_display_switch_list = 1; // 用户开关列表
}

message SetUserDisplaySwitchRequest {
    uint32 uid = 1; // 用户uid
    DisplaySwitch display_switch = 2; // 用户开关列表
}

message SetUserDisplaySwitchResponse {
}

message BindInviteInfo {
    uint32 uid = 1; // 邀请者uid
    uint32 target_uid = 2; // 被邀请者uid
    string invite_id = 3; // 邀请id
    uint32 status = 4; // see BindInviteStatus
}

// 获取用户待处理的关系绑定邀请列表
message GetBindBeInvitedListRequest {
    uint32 uid = 1; // 用户uid
}

message GetBindBeInvitedListResponse {
    repeated BindInviteInfo info_list = 1; // 待处理的邀请列表
}

message GetFollowEnterChannelInfoRequest {
  uint32 uid = 1; // 用户uid
  uint32 followed_uid = 2; // 被跟随用户
}

message GetFollowEnterChannelInfoResponse {
  UserInuseItemInfo user_item_info = 1;
  UserInuseItemInfo followed_user_item_info = 2;
}

message SetVirtualImagePoseTypeRequest {
  uint32 uid = 1; // 用户uid
  uint32 pose_type = 2; // see VirtualImagePoseType
  uint32 scene = 3; // see VirtualImageDisplaySwitch
}

message SetVirtualImagePoseTypeResponse {}

message GetVirtualImageBeginnerGuideRequest {
  uint32 uid = 1; // 用户uid
}

message GetVirtualImageBeginnerGuideResponse {
  bool has_guide = 1; // 是否有新手引导
}

message MarkVirtualImageBeginnerGuideDoneRequest {
  uint32 uid = 1; // 用户uid
}

message MarkVirtualImageBeginnerGuideDoneResponse {}
