syntax = "proto3";

package activity_taillight;

option go_package = "golang.52tt.com/protocol/services/activity-taillight";

// 活动尾灯服务
service ActivityTaillight {
    // 给用户发放尾灯
    rpc RewardTaillight (RewardTaillightReq) returns (RewardTaillightResp) {}
    // 获取用户尾灯
    rpc GetUserTaillight (GetUserTaillightReq) returns (GetUserTaillightResp) {}
    // 批量获取用户尾灯
    rpc BatchGetUserTaillight (BatchGetUserTaillightReq) returns (BatchGetUserTaillightResp) {}
    // 给用户发放铭牌
    rpc RewardNameplate (RewardNameplateReq) returns (RewardNameplateResp) {}
    // 获取用户铭牌
    rpc GetUserNameplate (GetUserNameplateReq) returns (GetUserNameplateResp) {}
    // 获取用户特征
    rpc GetUserFeature (GetUserFeatureReq) returns (GetUserFeatureResp) {}
    // 重建铭牌缓存
    rpc RebuildNameplateCache (RebuildNameplateCacheReq) returns (RebuildNameplateCacheResp);
}

// 尾灯业务id  
enum TaillightBizId {
    TaillightBizIdDefault = 0;

    // 蒙面PK专属金色尾灯 全平台可见
    TaillightBizIdMaskedPKPlatform = 1;
    // 蒙面PK专属尾灯 房间内可见
    TaillightBizIdMaskedPKChannel = 2;
}

// 尾灯的可见范围
enum TaillightScope {
    TaillightScopeDefault = 0;

    // 全平台可见
    TaillightScopePlatform = 1;
    // 房间内可见
    TaillightScopeChannel = 2;
}

// 铭牌类型
enum NameplateType {
    UNKNOWN = 0; // 未知
    NAMEPLATE_TYPE_A = 1; // 铭牌A类型，被邀请人可获取的铭牌
    NAMEPLATE_TYPE_B = 2; // 铭牌B类型，邀请人/被邀请人可获取的铭牌
    NAMEPLATE_TYPE_C = 3; // 铭牌C类型
}

// 铭牌业务参数
message NameplateBiz {
    int32 level = 1; // 铭牌等级
}

// 铭牌
message Nameplate {
    uint32 id = 1; // 铭牌id
    NameplateType type = 2; // 铭牌类型
    bytes biz_options = 3; // 业务额外参数，存放业务相关的信息，自定义xxxBiz
    string url = 4; // 铭牌资源url
}

// 铭牌列表
message NameplateList {
    repeated Nameplate nameplates = 1;
}

message UserTaillight {
    // 尾灯业务id
    TaillightBizId biz_id = 1;
    // 尾灯数量
    uint32 num = 2;
}

message UserTaillightList {
    repeated UserTaillight taillight_list = 1;
}

message UserTaillightRecord {
    uint32 biz_id = 1;
    uint32 scope = 2;
    uint32 channel_id = 3;
    uint32 expired_at = 4;
}

message UserTaillightRecordList {
    repeated UserTaillightRecord record_list = 1;
}

message RewardTaillightReq {
    // 发放什么类型的尾灯
    TaillightBizId biz_id = 1;
    // 发放尾灯的可见范围
    TaillightScope scope = 2;

    // 发放尾灯给哪个用户
    uint32 uid = 3;
    // 在哪个房间获得的尾灯
    uint32 channel_id = 4;

    // 活动id 哪个活动发放的尾灯
    string activity_id = 5;
    // 活动结束时间
    uint32 end_at = 6;
}

message RewardTaillightResp {
    // 活动结束后间隔多久尾灯失效(秒)
    uint32 interval = 1;
}

message GetUserTaillightReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message GetUserTaillightResp {
    repeated UserTaillight list = 1;
}

message BatchGetUserTaillightReq {
    repeated uint32 uid_list = 1;
    uint32 channel_id = 2;
}

message BatchGetUserTaillightResp {
    map<uint32, UserTaillightList> user_taillight_map = 1;
}

message RewardNameplateReq {
    Nameplate nameplate = 1; // 铭牌
    uint32 uid = 2; // 用户id
    int64 end_time = 3; // 铭牌结束时间，时间戳，精确到秒
}

message RewardNameplateResp {

}

message GetUserNameplateReq {
    uint32 uid = 1; // 用户id
}

message GetUserNameplateResp {
    repeated Nameplate nameplate_list = 1; // 铭牌列表
}

message GetUserFeatureReq {
    uint32 uid = 1; // 用户id
    uint32 channel_id = 2; // 房间id
    bool loose_mode = 3; // 是否开启宽松模式，宽松模式开启时，只返回能正常获取的特性（因出错导致获取失败特性不会返回，并且会忽略错误）
}

message GetUserFeatureResp {
    repeated Nameplate nameplate_list = 1; // 铭牌列表
    repeated UserTaillight taillight_list = 2; // 尾灯
}

message RebuildNameplateCacheReq {

}

message RebuildNameplateCacheResp {

}