syntax = "proto3";

package activity_taillight;


option go_package = "golang.52tt.com/protocol/services/activity-taillight";


// 铭牌

enum NameplateDef {
    NAMEPLATE_DEF_UNSPECIFIED = 0; // 未指定
    NAMEPLATE_DEF_HALL_OF_FAME = 1; // 名仕馆新星，A类
    NAMEPLATE_DEF_PRINCE = 2; // 亲王好友，A类
    NAMEPLATE_DEF_KING = 3; // 国王好友，A类
    NAMEPLATE_DEF_GOD_KING = 4; // 神王好友，A类
    NAMEPLATE_DEF_FLEDGLING = 5; // 初出茅庐，B类
    NAMEPLATE_DEF_HIDDEN_MASTER = 6; // 隐世高手，B类
    NAMEPLATE_DEF_JUNIOR_RANGER = 7; // 初级游侠，B类
    NAMEPLATE_DEF_GENERATION_HEROES = 8; // 一代豪杰，B类
    NAMEPLATE_DEF_PEERLESS_MASTER = 9; // 绝世宗师，B类
    NAMEPLATE_DEF_MASTER_GROUP = 10; // 名仕宗师团，C类

}

