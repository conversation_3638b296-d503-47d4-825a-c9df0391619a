syntax = "proto3";

package adventure_activity;
import "tt/quicksilver/extension/options/options.proto";
option go_package = "golang.52tt.com/protocol/services/adventure-activity";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service AdventureActivity {
  option (service.options.service_ext) = {
    service_name: "adventure-activity"
  };
    // 解除熔断
    rpc ReleaseFusing(ReleaseFusingReq) returns (ReleaseFusingResp) {}

    // 获取活动信息
    rpc GetCurrGameInfo(GetCurrGameInfoRequest) returns (GetCurrGameInfoResponse) {}
    // 购买抽奖机会
    rpc BuyChance(BuyChanceRequest) returns (BuyChanceResponse) {}
    // 进行抽奖
    rpc LotteryDraw(LotteryDrawRequest) returns (LotteryDrawResponse) {}
    // 获取平台中奖记录
    rpc GetPlatformWinningRecord(GetPlatformWinningRecordRequest) returns (GetPlatformWinningRecordResponse) {}
    // 获取用户冒险记录
    rpc GetUserAdventureRecord(GetUserAdventureRecordRequest) returns (GetUserAdventureRecordResponse) {}

    // 获取用户每日剩余可购买数量
    rpc GetDailyBuyChanceLimit(GetDailyBuyChanceLimitReq) returns (GetDailyBuyChanceLimitResp) {}

    // 发起道具退还
    rpc RefundProp(RefundPropReq) returns (RefundPropResp) {}

    // 重置加码奖库存
    rpc ResetTopNAwardStock(ResetTopNAwardStockReq) returns (ResetTopNAwardStockResp) {}

    // 清除平台冒险记录
    rpc ClearPlatformAdventureRecord(ClearPlatformAdventureRecordReq) returns (ClearPlatformAdventureRecordResp) {}
  
    // ==========配置相关接口==========
    // 设置关卡配置
    rpc SetLevelConf(SetLevelConfRequest) returns (SetLevelConfResponse) {}
    // 设置关卡奖池
    rpc SetLevelPrizePool(SetLevelPrizePoolRequest) returns (SetLevelPrizePoolResponse) {}
    // 获取活动所有关卡奖池信息
    rpc GetLevelConfWithPool(GetLevelConfWithPoolRequest) returns (GetLevelConfWithPoolResponse) {}

    // 防违规配置
    rpc SetAdventureIslandLimitConf(SetAdventureIslandLimitConfReq) returns(SetAdventureIslandLimitConfResp){}
    rpc GetAdventureIslandLimitConf(GetAdventureIslandLimitConfReq) returns(GetAdventureIslandLimitConfResp){}

    // 手动推送配置至飞书群
    rpc PushConfigToFeishu(PushConfigToFeishuReq) returns (PushConfigToFeishuResp) {}

    rpc BatAddUserRemain(BatAddUserRemainReq) returns (BatAddUserRemainResp) {}

  // ==========对账接口==========
  // T豆对账
    rpc GetTBeanTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetTBeanOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

    // 奖励数据对账
    rpc GetAwardTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetAwardOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
}


enum AwardType {
  AWARD_TYPE_UNSPECIFIED = 0; // 未知
  AWARD_TYPE_PACKAGE = 1; // 包裹奖励
  AWARD_TYPE_DRESS = 2; // 装扮奖励
}

// 奖励信息
message AwardInfo {
  string award_id = 1; // 奖励ID
  uint32 award_type = 2; // 奖励类型, see AwardType
  uint32 dress_sub_type = 3; // 装扮子类型，see award-center.proto EGiftType
  uint32 amount = 4; // 奖励数量
  
  uint32 award_worth = 5; // 奖励价值
  string award_name = 6; // 奖励名称
  string award_desc = 7; // 奖励描述
  string award_icon = 8; // 奖励图标
  
  int64 exp_time = 9; // 奖励过期时间, 秒级时间戳, 0表示不过期
  uint32 weight = 10; // 奖励权重, 用于随机抽奖时的权重

  uint32 card_id = 11; // 卡牌ID顺序
  uint32 price_type = 12; // 奖励单价类型 1-红钻 2-豆
}

// 关卡信息
message LevelCfg {
  uint32 level_id = 1; // 关卡ID
  string level_name = 2; // 关卡名称
  uint32 max_n_fixed = 3;  // 固定保底值
  AwardInfo level_award = 5; // 通关奖励信息
}

message LevelConfWithPool {
  LevelCfg level_cfg = 1; // 关卡配置信息
  repeated AwardInfo award_list = 2; // 奖池信息列表
}

// 奖池信息
message PrizePool {
  uint32 level_id = 1; // 关卡ID
  repeated AwardInfo award_list = 2; // 奖池信息列表
}

message SetLevelConfRequest {
  repeated LevelCfg level_conf_list = 1; // 关卡配置信息列表
}

message SetLevelConfResponse {
  bool success = 1; // 设置是否成功
}

// 设置关卡奖池(支持一个或多个关卡同时设置)
message SetLevelPrizePoolRequest {
    repeated PrizePool prize_pool_list = 1; // 奖池信息列表
}

message SetLevelPrizePoolResponse {
    bool success = 1; // 设置是否成功
}

// 获取活动所有关卡奖池信息
message GetLevelConfWithPoolRequest {
}

message GetLevelConfWithPoolResponse {
    repeated LevelConfWithPool level_list = 1; // 关卡信息列表
}

// 设置冒险岛防违规配置
message SetAdventureIslandLimitConfReq {
  uint32 daily_buy_chance_limit = 1; // 每日购买道具金额限制(元)
  uint32 daily_use_chance_limit = 2; // 每日消耗道具金额限制(元)
  uint32 single_buy_chance_limit = 3;// 单次购买道具金额限制(元)
}

message SetAdventureIslandLimitConfResp {
}

// 获取冒险岛防违规配置
message GetAdventureIslandLimitConfReq {}

message GetAdventureIslandLimitConfResp {
  uint32 daily_buy_chance_limit = 1; // 每日购买道具金额限制(元)
  uint32 daily_use_chance_limit = 2; // 每日消耗道具金额限制(元)
  uint32 single_buy_chance_limit = 3;// 单次购买道具金额限制(元)
}

// =========================== 活动玩法 =====================

// TOPN 加码奖励信息
message TopNAwardCfg {
  uint32 top_n_limit = 1; // 前N名登顶加码名额
  repeated AwardInfo award_list = 2; // 前n名加码奖励列表
  
  string popup_top_text = 3; //加码弹窗上方文案，<>内为高亮文案
  string popup_down_text = 4; // 加码弹窗下方文案。
}

message UserPlayFile {
  uint32 level_id = 2; // 当前关卡ID
  uint32 current_chance = 5; // 当前抽奖机会数量
  uint32 user_n = 6; // 用户当前的冒险值
  map<string, uint32> card_collection = 7; // 用户当前拥有的卡牌信息，key为卡牌ID，value为数量
}


// 获取活动信息 
message GetCurrGameInfoRequest { 
  uint32 uid = 1; // 用户ID
  uint32 channel_id = 2; // 如果当前在房间内，则传入房间ID
}

// 购买礼包配置信息
message BuyPropCfg{
    AwardInfo gift_info = 1; // 礼物信息
    repeated uint32 buy_amount_options = 3; // 购买数量选项列表, 单位为个
    uint32 unit_price = 4; // 购买单价, 单位为T豆
    uint32 daily_buy_limit = 5; // 每日购买数量限制
    uint32 single_buy_limit = 6;  // 单次购买数量限制
}

message GetCurrGameInfoResponse {
  uint32 activity_id = 1; // 活动ID
  string activity_name = 2; // 活动名称
  int64 begin_time = 3; // 活动开启时间
  int64 end_time = 4; // 活动结束时间
  BuyPropCfg buy_prop_cfg = 5; // 购买礼包礼物配置
  repeated LevelConfWithPool level_list = 6; // 关卡配置列表
  UserPlayFile user_play_file = 7; // 用户的关卡存档
  TopNAwardCfg top_n_award_info = 8; // TOPN加码奖励信息
  
    // 无权限的用户展示文案
  string no_permission_text = 9; // 无权限的用户展示文案
  bool has_access = 10; // 用户是否有权限访问该活动
}

// 购买抽奖机会
message BuyChanceRequest {
  uint32 uid = 1;
  uint32 channel_id = 2; // 房间
  uint32 chance_amount = 3;      // 数量
  uint32 fee = 4; // 购买费用, 单位为T豆
}

message BuyChanceResponse {
  uint32 final_chance_amount  = 1;    // 最终数量
  uint64 balance = 2;         // T豆余额
  uint32 daily_buy_cnt_remain = 3; //每日剩余的可购买数量
}

enum DrawResultType {
  DRAW_RESULT_UNSPECIFIED = 0;     // 无效值
  DRAW_RESULT_CARD_LIGHTED = 1;    // 点亮卡牌
  DRAW_RESULT_LEVEL_COMPLETED = 2; // 通关成功
  DRAW_RESULT_PEAK_REACHED = 3;    // 登顶奖励
  DRAW_RESULT_TOP_N_BOUNS = 4;     // 前n名加码奖励
}

// 抽奖结果
message DrawResult {
  uint32 level_id = 1; // 关卡ID
  repeated AwardInfo award_list = 2; // 奖励列表
  uint32 result_type = 3; // 抽奖结果类型, see LotteryResultType
  int64 ctime = 4; // 奖励时间

  uint32 top_ranking = 5; // 前n名排名, 0表示不是前n名
}

// 抽奖 url: /tt-revenue-http-logic/adventure-island/draw_card
message LotteryDrawRequest {
  uint32 uid = 1; // 用户ID
  uint32 channel_id = 2; // 如果当前在房间内，则传入房间ID
  uint32 level_id = 3; // 抽奖关卡ID
  uint32 amount = 4; // 抽奖数量, 单位为次

  enum Mode{
      MODE_MANUL = 0; // 手动
      MODE_AUTO = 1;  // 自动
  }
  uint32 mode = 5;   // 抽奖模式
}

message LotteryDrawResponse {
  repeated DrawResult result_list = 1; // 抽奖结果列表，依次展示
  UserPlayFile user_play_file = 2; // 更新后的用户关卡存档
}

// 平台中奖记录 轮播用
message PlatformWinningRecord{
    uint32 uid = 1; // 用户ID
    string nickname = 2; // 获奖用户名
    uint32 level_id = 3; // 关卡ID
    AwardInfo award_info = 4; // 奖品信息
    uint32 result_type = 5; // 抽奖结果类型, see DrawResultType
}

// 获取平台中奖记录
message GetPlatformWinningRecordRequest {
  uint32 channel_id = 1; // 如果当前在房间内，则传入房间ID
  string version = 2; // 版本号,首次请求时传入空字符串，后续请求传入上次返回的版本号
}

message GetPlatformWinningRecordResponse {
  string new_version = 1; // 新版本号
  repeated PlatformWinningRecord record_list = 2; // 平台中奖记录列表，用于轮播展示

  uint32 top_n_remain = 3; // 剩余的加码奖名额
  uint32 top_n_limit = 4;  // topn加码奖数量
  uint32 req_duration_sec = 5;    // 下次请求的时间间隔
}

// 获取用户冒险记录 
message GetUserAdventureRecordRequest {
    enum RecordType {
        RECORD_TYPE_UNSPECIFIED = 0; // 无效值
        RECORD_TYPE_CARD_LIGHTED = 2; // 点亮记录
        RECORD_TYPE_COMPLETED = 3; // 其他打卡/登顶记录
    }

    uint32 record_type = 1; // 记录类型, see RecordType
    string offset = 2; // 分页偏移量, 用于分页查询, 首次请求时传入空字符串，后续请求传入上次返回的offset
    uint32 limit = 3; // 分页限制, 每页记录数

    uint32 uid= 4; // 用户ID, 用于查询用户的冒险记录
}

message GetUserAdventureRecordResponse {
    string next_offset = 1; // 新的分页偏移量, 用于下一次分页查询,当返回的next_offset为空字符串时表示没有更多记录
    repeated DrawResult record_list = 2; // 用户冒险记录
}

message GetDailyBuyChanceLimitReq {
  uint32 uid = 1; // 用户ID
}

message GetDailyBuyChanceLimitResp {
  uint32 remain_daily_buy_cnt = 1; // 每日剩余可购买数量
}

message PushConfigToFeishuReq {
    bool is_push_hour_data = 1; // 是否推送小时数据
    bool is_push_config = 2; // 是否推送配置
    bool is_simulate_draw = 3; // 是否模拟抽奖
    uint32 simulate_draw_n = 4; // 模拟抽奖次数
    repeated int32 level_weight_list = 5; // 模拟抽奖时的关卡权重列表, 用于模拟抽奖时的关卡权重
}

message PushConfigToFeishuResp {
}


// 解除熔断
message ReleaseFusingReq {
  uint32 inject_rmb = 1;  // 注入奖池资金（元）
}

message ReleaseFusingResp {}


message BatAddUserRemainReq {
  repeated uint32 uid_list = 1; // 用户ID列表
  uint32 remain = 2; // 要增加的剩余次数
}
message BatAddUserRemainResp {
}

message RefundPropReq {
    enum RefundType {
        REFUND_TYPE_UNSPECIFIED = 0; // 未知
        REFUND_TYPE_SETTLE = 1; // 道具退还结算
        REFUND_TYPE_REFUND = 2; // 道具退还发放
    }
    uint32 op_type = 1; // 操作类型, see RefundType
}

message RefundPropResp {
}

message ResetTopNAwardStockReq {
  uint32 top_n_limit = 1; // 前N名登顶加码名额
}

message ResetTopNAwardStockResp {
}

message ClearPlatformAdventureRecordReq {
}

message ClearPlatformAdventureRecordResp {
}