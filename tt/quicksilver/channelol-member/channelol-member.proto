syntax = "proto3";

package channelol_member;
option go_package = "golang.52tt.com/protocol/services/channelol_member";

service ChannelolMember {
    rpc GetChannelOnlineMember(GetChannelOnlineMemberRequest) returns (GetChannelOnlineMemberResponse);
}

message ChannelMemberOnlineInfo {
    uint32 uid = 1;
}

message GetChannelOnlineMemberRequest {
    uint32 channel_id = 1;
    uint32 offset = 2;
    uint32 count = 3;
}

message GetChannelOnlineMemberResponse {
    repeated ChannelMemberOnlineInfo channel_member_list = 1;
}
