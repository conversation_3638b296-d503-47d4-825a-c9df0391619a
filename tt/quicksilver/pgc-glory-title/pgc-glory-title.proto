syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/pgc-glory-title";
package pgc_glory_title;
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service PgcGloryTitleSvr {
  //获取冠名状态
  rpc GetPgcGloryTitleData(GetPgcGloryTitleDataReq) returns (GetPgcGloryTitleDataResp) {}

  // 获取用户的被冠名信息
  rpc GetUserTitledInfo(GetUserTitledInfoReq) returns (GetUserTitledInfoResp) {}

  //冠名配置
  rpc GetPgcGloryTitleConfig(Empty) returns (GetPgcGloryTitleConfigResp) {}
  rpc SetPgcGloryTitleConfig(GetPgcGloryTitleConfigData) returns (Empty) {}

  //冠名榜单
  rpc GetPgcGloryTitleAnchorRank(GetPgcGloryTitleAnchorRankReq) returns (GetPgcGloryTitleAnchorRankResp) {}
  rpc GetPgcGloryTitleInvestorRank(GetPgcGloryTitleInvestorRankReq) returns (GetPgcGloryTitleInvestorRankResp) {}
  rpc MannalSettlementRank (MannalSettlementRankReq) returns (MannalSettlementRankResp) {}
  rpc CheckTitleRank (CheckTitleRankReq) returns (CheckTitleRankResp){}

  //用户冠名权益开关
  rpc UserTitlePrivilegeSwitch(UserTitlePrivilegeSwitchReq) returns (UserTitlePrivilegeSwitchResp) {}

  // 获取房间冠名信息
  rpc GetChannelTitleInfo(GetChannelTitleInfoReq) returns(GetChannelTitleInfoResp) {}

  //获取用户的房间冠名信息
  rpc BatGetUserChannelTitleInfo(BatGetUserChannelTitleInfoReq) returns(BatGetUserChannelTitleInfoResp) {}

  //获取冠名的基本信息
  rpc GetPgcGloryTitleSimpleData(GetPgcGloryTitleDataReq) returns (GetPgcGloryTitleSimpleDataResp) {}

  //模拟增加送礼事件
  rpc TestPushEvent(TestPushEventReq) returns (Empty) {}

  //清空冠名数据
  rpc CleanData(GetPgcGloryTitleDataReq) returns (Empty) {}

  //清空冠名榜单数据
  rpc CleanRankData(CleanRankDataReq) returns (Empty) {}

  //查看主播冠名列表
  rpc GetTitledList(GetTitledListReq) returns (GetTitledListResp) {}

  //检查是否可以赠送冠名礼物
  rpc CheckTitleGiftIsSend(CheckTitleGiftIsSendReq) returns (CheckTitleGiftIsSendResp) {}

  //对账
  rpc GetUserPresentCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  //获取时间范围内的订单列表
  rpc GetUserPresentOrderList(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  //补单
  rpc ReplaceOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
}

message GetTitledListReq {
  uint32 channel_id = 1;
  uint32 from_uid = 2;
}

message GetTitledListResp {
  repeated uint32 list = 1;
}

message TestPushEventReq {
  uint32 from_uid = 1;
  uint32 to_uid = 2;
  uint32 channel_id = 3;
  uint32 guild_id = 4;
  uint32 price = 5;
}

message GetPgcGloryTitleSimpleDataResp {
  uint32 title_type = 1;
  int64 last_end_time = 2;
  int64 create_time = 3;
  int64 expire_time = 4;  //过期时间戳
  uint64 process = 5;
}

// 冠名类型
enum ETitleType {
    E_TITLE_TYPE_UNSPECIFIED = 0;   //无效
    E_TITLE_TYPE_WEEK = 1;   // 周冠名
    E_TITLE_TYPE_MONTH = 2;  // 月冠名
    E_TITLE_TYPE_DAY = 3;    // 日冠名
}

//权益类型 移位定义
enum PrivilegeType {
   PRIVILEGE_TYPE_UNSPECIFIED = 0;  //无效
   PRIVILEGE_TYPE_ENTER_EFFECT = 1;  // 进房特效
   PRIVILEGE_TYPE_HEADWEAR = 2;  // 冠名麦位框
   PRIVILEGE_TYPE_GIFT_SEND = 4;  // 礼物赠送权
   PRIVILEGE_TYPE_POP_UP = 8;  // 冠名弹窗
   PRIVILEGE_TYPE_PLATE = 16;  // 铭牌
}


message TitlePrivilege {
    //图片样式
    enum ImgStyle {
       IMG_STYLE_UNSPECIFIED = 0 ;  //无效
       IMG_STYLE_SHORT = 1;  // 短样式
       IMG_STYLE_LONG = 2; // 长样式
    }
    // 图片资源类型
    enum ImgType {
       IMG_TYPE_UNSPECIFIED = 0 ;  //无效
       IMG_TYPE_STATIC = 1;  // 静态图
       IMG_TYPE_DYNAMIC = 2; // 动态图
    }
    string small_img = 1;  // 缩略图
    string big_img = 2;  //大图
    string name = 3;
    string desc_msg = 4; 
    uint32 img_style = 5;  // 图片样式
    uint32 img_type = 6;  // 大图资源类型 see ImgType
    uint32 privilege_type = 7;   // 权益类型 see PrivilegeType
}

message TitlePrivilegeList {
    repeated TitlePrivilege list = 1;
}


message GetPgcGloryTitleDataReq {
  uint32 from_uid = 1;
  uint32 to_uid = 2;
  uint32 channel_id = 3;
  bool  is_day_title_ver = 4;
}

message GetPgcGloryTitleDataResp {
  uint32 current_price = 1; //当前送礼累计
  int64 begin_time = 2; //开始时间
  int64 end_time = 3; //结束时间
  uint32 week_price = 4;  //周冠价格
  uint32 month_price = 5; //月冠价格
  int64 expire_time = 6;  //过期时间戳
  uint32 titled_cnt = 7;  //被冠名人数
  string process_msg = 8; //进度文案
  bool privilege_switch = 9;  // 权益开关
  uint32 title_type = 10; //当前的冠名类型 see ETitleType
  map<uint32, TitlePrivilegeList> map_type_privilege = 11; // 特权列表 type see ETitleType
  string desc_msg = 12;  //描述文案
  uint32 day_price = 13;  //日冠价格
  map<uint32, bool> map_type_switch = 14;  // 权益的全局开关， type see PrivilegeType
}

message Empty {

}

message GetPgcGloryTitleConfigData {
  uint32 tag_id = 1;  //品类类型
  int64 begin_time = 2; //开始时间
  int64 end_time = 3; //结束时间
  uint32 week_price = 4;  //周冠价格
  uint32 month_price = 5; //月冠价格
  uint32 day_price = 6;  //日冠价格
}

message GetPgcGloryTitleConfigResp {
  repeated GetPgcGloryTitleConfigData list = 1;
}

//获取用户的被冠名信息
message GetUserTitledInfoReq {
   uint32 target_uid= 1;
   uint32 channel_id = 2;
}
message GetUserTitledInfoResp {
   bool has_entry = 1; // 是否有被冠名入口
   string channel_titled_rank = 2;  // 房间被冠名榜文案
   uint32 titled_cnt = 3;  //冠名人数
   repeated uint32 top_titled_list = 4; //冠名榜单
}

//用户冠名权益开关
message UserTitlePrivilegeSwitchReq {
   uint32 uid = 1; 
   bool is_on = 2; // true:打开 false:关闭
   uint32 target_uid = 3;
   uint32 channel_id = 4;
   uint32 privilge_type = 5;   // 某一权益的全局开关， 为0时表示对某一冠名用户的所有权益开关
}
message UserTitlePrivilegeSwitchResp {
}

// 获取房间冠名信息
message GetChannelTitleInfoReq {
   uint32 channel_id = 1; 
}
message GetChannelTitleInfoResp {
   bool has_entry = 1;  //是否有冠名权限
}

message UserChannelTitleInfo {
   uint32 title_type = 1; //当前的冠名类型 see ETitleType
   map<uint32,uint32> map_uid_type = 2;  // 冠名用户类型
   map<uint32, bool> map_type_switch = 3;  // 权益的全局开关， type see PrivilegeType
}


//获取用户的房间冠名信息
message BatGetUserChannelTitleInfoReq {
   repeated uint32 uid_list = 1;
   uint32 cid = 2; 
}
message BatGetUserChannelTitleInfoResp {
   map<uint32, UserChannelTitleInfo> map_uid_info  = 1;
   map<uint32,string> map_type_cert = 2;  // 冠名标识
   uint32 headwear_id = 3;  // 冠名麦位框id
   string enter_effect_url = 4; // 进房特效
   string enter_effect_md5 = 5; //进房特效md5
   string follow_enter_effect_url = 6; // 跟随进房特效
   string follow_enter_effect_md5 = 7; //跟随进房特效md5

}

//  获取冠名主播排行榜
message GetPgcGloryTitleAnchorRankReq {
  uint32 cid = 1; // 房间id
  uint32 uid = 2; // 请求用户id
}
message AnchorWeekRank {
  uint32 uid = 1; // 主播uid
  int64  love_val = 2; // 爱意值
  repeated uint32 top_uids = 3; // 前三
  uint32 total_titled_num = 4;  // 总冠名人数
  bool   is_titled_my = 5; // 我是否冠名过
}
message AnchorMicInfo {
  uint32 mic_id = 1; // 麦位id
  uint32 uid = 2; // 主播uid
}
message GetPgcGloryTitleAnchorRankResp {
  repeated AnchorWeekRank rank_list = 1;
  repeated uint32 last_top_uids = 2; // 上周前三
  int64 end_ts = 3; // 结束时间戳
  repeated AnchorMicInfo mic_anchor_uids = 4; // 麦上可冠名主播(rank_list == 0 时有值)
}

message InvestorMonthRank {
  uint32 rank = 1;                // 排名
  uint32 uid = 2;                 // 用户id
  int64  love_val = 3;            // 爱意值
  int64  expire_ts = 4;           // 到期时间戳
  uint32 title_type = 5;          // 冠名类型 ETitleType
}
message GetPgcGloryTitleInvestorRankReq{
  uint32 uid = 1; // 用户id
  uint32 cid = 2; // 房间id
  uint32 target_uid = 3;  // 目标用户id-
}
message GetPgcGloryTitleInvestorRankResp {
  repeated InvestorMonthRank rank_list = 1;
  repeated uint32 last_top_uids = 2; // 上月前三
  int64  end_ts = 3;     // 结束时间戳
  InvestorMonthRank my_rank = 4; // 我的排名
 }

message MannalSettlementRankReq {
  bool  is_test = 1; // 是否测试
  bool  is_week = 2;   //结算周榜
  bool  is_month = 3;  //结算月榜
}
message MannalSettlementRankResp {
}

message CheckTitleRankReq {
  uint32 uid = 1;
  uint32 cid = 2;
  uint32 anchor_uid = 3;
  bool   fix = 4;
}
message CheckTitleRankResp {
  uint64 month_store_val = 1;
  uint64 month_rank_val = 2;
  uint64 week_store_val = 3;
  uint64 week_rank_val = 4;
}

message CleanRankDataReq {
  uint32 cid = 1;
  uint32 uid = 2;
}


//检查是否可以赠送冠名礼物
message CheckTitleGiftIsSendReq {
   uint32 uid = 1;
   uint32 gift_id = 2;
   uint32 cid = 3; 
}
message CheckTitleGiftIsSendResp {
   bool is_can = 1; 
   string toast_msg = 2; 
}
