syntax = "proto3";

package exchange_middleware;

import "tt/quicksilver/extension/options/options.proto";

option go_package = "golang.52tt.com/protocol/services/exchange-middleware";

service ExchangeMiddleWare {
  option (service.options.old_package_name) = "ExchangeMiddleWare.ExchangeMiddleWare";

  //新增或修改app
  rpc SetApp(SetAppReq) returns (EmptyResp) {}
  //删除app
  rpc DelApp(DelAppReq) returns (EmptyResp) {}

  //获取余额，如果有一个服务失败，则会导致全部失败，如果想拆耦合，需要list长度1
  rpc GetBalance(GetBalanceReq) returns (GetBalanceResp) {}

  //兑换
  rpc Exchange(ExchangeReq) returns (ExchangeResp) {}

  //获取兑换记录
  rpc GetExchangeHistory(GetExchangeHistoryReq) returns (GetExchangeHistoryResp) {}

  //新增或修改新增client
  rpc SetUserClient(SetUserClientReq) returns (EmptyResp) {}
  //删除新增client
  rpc DelUserClient(DelUserClientReq) returns (EmptyResp) {}
}

message SetUserClientReq {
  string svr_name = 1;
  string get_data_method = 2;
  string add_data_method = 3;
  uint32 add_type = 4;
}

message DelUserClientReq {
  string svr_name = 1;
}

enum ExpireType {
  RelativeTime = 0;   //相对时间
  AbsoluteTime = 1;   //绝对时间
  MonthTime = 2;      //多少月后的1日0点
}


message GetDataReq {
  uint64 uid = 1;
  string svr_name = 2;
  string item_id = 3;
  string extra_data = 4;
}

message GetBalanceReq {
  repeated GetDataReq list = 1;
}

message GetDataResp {
  uint64 uid = 1;
  string svr_name = 2;
  string item_id = 3;
  int64 item_balance = 4; //兼容负的情况，万一一些需求需要负数
  uint64 expire_at = 5;
  string extra_data = 6;
}

message GetBalanceResp {
  repeated GetDataResp list = 1;
}

message GetExchangeHistoryReq {
  string appid = 1;
  uint64 uid = 2;
  uint32 offset = 3;
  uint32 limit = 4;
  string svr_name = 5;  //空则不过滤
  uint32 count_symbol = 6;  //0全部，1只显示增加的，2只显示扣减的
  int64 after_time = 7; //只显示何时以后的记录，默认为0
}

message ExchangeHistoryData {
  uint64 id = 1;
  uint64 uid = 2;
  string order_id = 3;
  string transaction_id = 4;
  string svr_name = 5;
  string item_id = 6;
  string item_name = 7;
  string item_icon = 8;
  uint64 item_count = 9;
  uint64 item_value = 10;
  uint64 item_balance = 11;
  uint64 expire_time = 12;
  uint64 create_time = 13;
}

message GetExchangeHistoryResp {
  repeated ExchangeHistoryData list = 1;
}

message DelAppReq {
  string appid = 1;
}

message SetAppReq {
  string appid = 1;
  string sign_key = 2;
  bool multi_uid = 3;
  uint32 item_max = 4;
  uint64 online_time = 5;
  bool has_conf = 6;
  int64 deleted = 7;
}

message ExchangeReqData {
  uint64 uid = 1;
  string svr_name = 2;
  string item_id = 3;
  int64 item_count = 4;
  uint64 expire_time = 5;
  uint32 source_type = 6;
  string extra_data = 7;
  uint32 config_id = 8;
  string order_id = 9;
}

message ExchangeReq {
  string appid = 1;
  string sign = 2;  //hex(md5({appid}{time}{sign_key}))
  int64 time = 3;
  repeated ExchangeReqData list = 4;
  string transaction_id = 5;   //不填则自动生成，当list里的数据唯一时，transaction_id==order_id
}

message ExchangeRespData {
  uint64 uid = 1;
  string svr_name = 2;
  string item_id = 3;
  int64 item_balance = 4;
  uint64 expire_at = 5;
  string extra_data = 6;
  string order_id = 7;
}

message ExchangeResp {
  repeated ExchangeRespData list = 1;
}

message EmptyResp {

}