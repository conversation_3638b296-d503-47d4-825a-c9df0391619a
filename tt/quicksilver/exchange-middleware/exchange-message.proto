syntax = "proto3";

package exchange_middleware;

option go_package = "golang.52tt.com/protocol/services/exchange-middleware";

//获取物品余额
message ExchangeGetBalanceReq {
  uint64 uid = 1;
  string item_id = 2;
  string extra_data = 3;
}

message ExchangeBatchGetBalanceReq {
  repeated ExchangeGetBalanceReq list = 1;
}

message ExchangeGetBalanceResp {
  uint64 uid = 1;
  string item_id = 2;
  int64 item_balance = 3;
  uint64 expire_at = 4;
  string extra_data = 5;
}

message ExchangeBatchGetBalanceResp {
  repeated ExchangeGetBalanceResp list = 1;
}

//请求增加物品
message ExchangeAddDataReq {
  string order_id = 1;
  uint64 uid = 2;
  string item_id = 3;
  int64 item_count = 4;
  uint64 expire_time = 5;
  string extra_data = 6;
}

message ExchangeBatchAddDataReq {
  repeated ExchangeAddDataReq list = 1;
}

message ExchangeAddDataResp {
  string order_id = 1;
  uint64 balance = 2;
  uint64 expire_at = 3;
  string extra_data = 4;
}

message ExchangeBatchAddDataResp {
  repeated ExchangeAddDataResp list = 1;
}
