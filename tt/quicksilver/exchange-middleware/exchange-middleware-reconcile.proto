syntax = "proto3";

package exchange_middleware;
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

import "tt/quicksilver/extension/options/options.proto";

option go_package = "golang.52tt.com/protocol/services/exchange-middleware";

service ExchangeMiddleWareReconcile {
  option (service.options.old_package_name) = "ExchangeMiddleWare.ExchangeMiddleWareReconcile";

  //对账
  rpc GetExchangeCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

}
