syntax = "proto3";

import "tt/quicksilver/channel-live-fans/channel-live-fans.proto";
option go_package = "golang.52tt.com/protocol/services/sign-anchor-stats";
package sign_anchor_stats;
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service SignAnchorStats {

  // 公会后台接口

  // 获取公会（当月）房间统计
  rpc GetMultiThisMonthChannelStat(GetMultiThisMonthChannelStatReq) returns (GetMultiThisMonthChannelStatResp) {}
  // 获取公会签约成员统计（按月）
  rpc GetMultiAnchorMonthStat(GetMultiAnchorMonthStatReq) returns (GetMultiAnchorMonthStatResp) {}
  // 获取成员房间接档统计（按日、月）
  rpc GetMultiAnchorChannelStat(GetMultiAnchorChannelStatReq) returns (GetMultiAnchorChannelStatResp) {}

  // 获取用户在房间类型4，7 的t豆消费
  rpc GetUserTbeanConsume(GetUserTbeanConsumeReq) returns(GetUserTbeanConsumeResp) {}

  // 获取公会成员日记录统计
  rpc GetMultiAnchorDailyStatsListByGuildId(GetMultiAnchorDailyStatsListByGuildIdReq) returns(GetMultiAnchorDailyStatsListByGuildIdResp) {}

  // 获取公会公开房月信息列表
  rpc GetPgcMonthlyInfoList(GetPgcMonthlyInfoListReq) returns(GetPgcMonthlyInfoListResp) {}

  // 获取公会公开房日信息列表
  rpc GetPgcDailyInfoList(GetPgcDailyInfoListReq) returns (GetPgcDailyInfoListResp) {}

  // 获取公会月信息列表
  rpc GetGuildMonthlyStatsInfoList(GetGuildMonthlyStatsInfoListReq) returns (GetGuildMonthlyStatsInfoListResp) {}

  // 获取多人互动主播日维度信息
  rpc GetMultiAnchorDailyStatsList(GetMultiAnchorDailyStatsListReq) returns (GetMultiAnchorDailyStatsListResp) {}



  /**/
  rpc GetMultiPlayerHomepage(GetMultiPlayerHomepageReq) returns (GetMultiPlayerHomepageResp) {}
  rpc GetMultiPlayerBaseInfo(GetMultiPlayerBaseInfoReq) returns (GetMultiPlayerBaseInfoResp) {}
  rpc GetMultiPlayerMonthConsumeTop10(GetMultiPlayerMonthConsumeTop10Req) returns (GetMultiPlayerMonthConsumeTop10Resp) {}
  rpc GetMultiPlayerMonthCommunityInfo(GetMultiPlayerMonthCommunityInfoReq) returns (GetMultiPlayerMonthCommunityInfoResp) {}
  rpc GetMultiPlayerDailyBaseInfo(GetMultiPlayerDailyBaseInfoReq) returns (GetMultiPlayerDailyBaseInfoResp) {}

  /*end*/

  //*** 互动消息 ***//
  rpc CheckUserInteractEntry(CheckUserInteractEntryReq) returns (CheckUserInteractEntryResp) {}
  rpc GetUserInteractInfo(GetUserInteractInfoReq) returns (GetUserInteractInfoResp) {}
  rpc GetUserInteractViewPer(GetUserInteractViewPerReq) returns (GetUserInteractViewPerResp) {}
  rpc SetUserInteractViewPer(SetUserInteractViewPerReq) returns (SetUserInteractViewPerResp) {}

  //*** 互动消息 ***//


  //*** 多人互动大厅任务 ***//
  // 运营后台
  // 查询公会房列表
  rpc GetGuildChannelList( GetGuildChannelListReq ) returns (GetGuildChannelListResp) {}
  // 配置承接大厅
  rpc AddMultiPlayerHall(AddMultiPlayerHallReq) returns (AddMultiPlayerHallResp) {}
  // 查询承接大厅
  rpc ListMultiPlayerHall(ListMultiPlayerHallReq) returns (ListMultiPlayerHallResp) {}
  // 回收承接大厅
  rpc DelMultiPlayerHall(DelMultiPlayerHallReq) returns (DelMultiPlayerHallResp) {}
  rpc GetGuildMultiPlayerHall(GetGuildMultiPlayerHallReq) returns (GetGuildMultiPlayerHallResp) {}
  


 
  // 公会查询任务配置信息
  rpc GetHallTaskConfList( GetHallTaskConfListReq ) returns (GetHallTaskConfListResp) {}
  rpc GetHallTaskConfById( GetHallTaskConfByIdReq ) returns (GetHallTaskConfByIdResp) {}
  
  // 公会配置任务
  rpc AddHallTaskConf( AddHallTaskConfReq ) returns (AddHallTaskConfResp) {}
  // 公会删除任务配置
  rpc DelHallTaskConf( DelHallTaskConfReq ) returns (DelHallTaskConfResp) {}
  // 公会分配任务
  rpc DistributeHallTask( DistributeHallTaskReq ) returns (DistributeHallTaskResp) {}
  // 删除成员任务
  rpc DelHallTask( DelHallTaskReq ) returns (DelHallTaskResp) {}
  // 公会查询分配任务
  rpc GetGuildHallTask( GetGuildHallTaskReq ) returns (GetGuildHallTaskResp) {}
  // 公会查询厅数据
  rpc GetGuildHallTaskStats( GetGuildHallTaskStatsReq ) returns (GetGuildHallTaskStatsResp) {}
  // 公会查询当天接档成员详情
  rpc GetGuildHallTaskStatsDetial( GetGuildHallTaskStatsDetialReq ) returns (GetGuildHallTaskStatsDetialResp) {}

  // 查签约成员被分配任务历史记录
  rpc GetHallTaskDistributeHistory( GetHallTaskDistributeHistoryReq ) returns (GetHallTaskDistributeHistoryResp) {}


  // 查询承接大厅任务缓存信息
  rpc GetHallTaskCacheInfo( GetHallTaskReq ) returns (GetHallTaskResp) {}
  // 查询签约成员当前周期正在进行的大厅任务
  rpc GetHallTask( GetHallTaskReq ) returns (GetHallTaskResp) {}
  // 查询签约成员大厅任务历史
  rpc GetHallTaskHistory( GetHallTaskHistoryReq ) returns (GetHallTaskHistoryResp) {}

  // 对账接口
  //获取时间范围内礼物券的数量和金额
  rpc GetTicketOrderCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  //获取时间范围内礼物券的订单列表
  rpc GetTicketOrderList(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  //获取时间范围内T豆礼物的数量和金额
  rpc GetPresentOrderCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  //获取时间范围内T豆礼物的订单列表
  rpc GetPresentOrderList(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  rpc ReplaceOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
  //*** 多人互动大厅任务 end ***//


  // 查询时间范围内在指定房间有效接档的签约成员uid
  rpc GetValidHoldDayUid( GetValidHoldDayUidReq ) returns (GetValidHoldDayUidResp) {}

  // 检查公会的子母关系
  rpc GetBindGuildInfo(GetBindGuildInfoReq) returns (GetBindGuildInfoResp) {}
  // 批量检查公会的子母关系
  rpc BatchGetBindGuildInfo(BatchGetBindGuildInfoReq) returns (BatchGetBindGuildInfoResp) {}
  //获取子母公会配置列表
  rpc GetBindGuildInfoList(GetBindGuildInfoListReq) returns (GetBindGuildInfoListResp) {}
}

enum TimeFilterUnit {
  BY_DAY = 0;
  BY_WEEK = 1;
  BY_MONTH = 2;
}

// 能力维度
enum AbilityType {
    Ability_Invalid = 0;  // 无
    Ability_Quality = 1;  // 优质
    Ability_Potential = 2; // 潜力
}

message GetMultiThisMonthChannelStatReq {
  uint32 guild_id = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  repeated uint32 channel_ids = 4;
}
message MultiThisMonthChannelStatItem {
  uint32 channel_id = 1;
  uint64 this_month_fee = 2;
  uint64 last_month_fee = 3;
  uint64 last_month_same_period_fee = 4;
  float chain_ratio = 5;
}
message GetMultiThisMonthChannelStatResp {
  repeated MultiThisMonthChannelStatItem list = 1;
  uint32 total = 5;
}

message GetMultiAnchorMonthStatReq {
  uint32 guild_id = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  repeated uint32 uid = 4;
  uint32 start_time = 5;
  uint32 end_time = 6;
  uint32 anchor_type = 7;
  // type todo
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message MultiAnchorMonthStatItem {
  string date = 1;
  uint32 uid = 2;
  uint32 register_time = 3;
  uint64 contractStartTime = 4;
  uint64 contractEndTime = 5;
  uint32 monthValidDay = 6; // 月有效接档天数
  uint32 monthValidHour = 7; // 月接档时长（小时）
  uint64 fee = 8; // 流水
  uint32 ability_type = 9;  // 能力维度类型 see AbilityType
  uint32 valid_sec = 10;  // 接档时长（秒）
}
message GetMultiAnchorMonthStatResp {
  repeated MultiAnchorMonthStatItem list = 1;
  uint32 total = 5;
}

message GetMultiAnchorChannelStatReq {
  uint32 guild_id = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  repeated uint32 uid = 4;
  uint32 start_time = 5;
  uint32 end_time = 6;
  TimeFilterUnit unit = 7;
  repeated uint32 cid_list = 8; // 支持房间列表查询
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message MultiAnchorChannelStatItem {
  string date = 1;
  uint32 uid = 2;
  uint32 channel_id = 3;
  uint64 date_timestamp = 4; // 日期时间戳
  uint32 monthValidDay = 6; // 月有效接档天数
  uint32 monthValidHour = 7; // 月接档时长（小时）
  uint64 income = 8; // 总收益
  uint64 present_income = 9; // 礼物收益
  uint64 werewolf_income = 10; // 狼人杀收益
}
message GetMultiAnchorChannelStatResp {
  repeated MultiAnchorChannelStatItem list = 1;
  uint32 total = 5;
}

message GetUserTbeanConsumeReq {
  uint32 uid = 1;
  uint32 begin_ts = 2;
  uint32 end_ts = 3;
}
message GetUserTbeanConsumeResp { 
  uint32 total = 1;
}

message GetMultiAnchorDailyStatsListByGuildIdReq {
  uint32 guild_id = 1;
  repeated uint32 anchor_uids = 2;
  uint32 begin_ts = 3;
  uint32 end_ts = 4;
}
message GetMultiAnchorDailyStatsListByGuildIdResp {
  repeated MultiAnchorDailyStats list = 1;
}

message MultiAnchorDailyStats {
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 date = 3;
  uint32 anchor_income = 4;
  bool is_valid_day = 5;
  uint32 valid_sec = 6;
}

// 获取多人互动主播日维度信息
message GetMultiAnchorDailyStatsListReq {
   uint32 guild_id = 1;
   repeated uint32 uid_list = 2;
   uint32 begin_ts = 3;
   uint32 end_ts = 4; 
   uint32 offset = 5;
   uint32 limit = 6; 
}
message GetMultiAnchorDailyStatsListResp {
   repeated MultiAnchorDailyStats list = 1;
   uint32 total_cnt = 2;
}


// pgc房间月信息
message PgcMonthlyInfo {
   uint32 ts = 1;
   uint32 channel_id = 2; 
   uint64 channel_fee = 3;
   uint32 valid_anchor_cnt = 4;  // 有效接档成员数
   uint32 active_anchor_cnt = 5;  // 活跃从业者
   uint32 quality_anchor_cnt = 6; // 优质从业者
   uint64 active_anchor_fee = 7;  // 活跃从业者贡献流水
   uint64 quality_anchor_fee = 8; // 优质从业者贡献流水
   float  quality_anchor_cnt_ratio = 9;  // 优质从业者数量占比
   float  quality_anchor_fee_ratio = 10;  // 优质从业者贡献流水占比
   uint64 total_channel_fee = 11;  // 结算房间总流水
   float  quality_anchor_trans_ratio = 12;  // 房间优质从业者转化率  
   float  channel_pkg_fee_ratio = 13;  // 房间背包流水占比
   uint32 profession_prac_cnt = 14;  // 专业从业者数
   uint32 valid_sign_member_cnt = 15;  // 有效签约成员数
}

// 获取公会公开房月信息列表
message GetPgcMonthlyInfoListReq {
    uint32 month_ts = 1;
    repeated uint32 cid_list = 2;  // 不为空时指定id查询
    uint32 page = 3;  // 从1开始
    uint32 page_size = 4;
    uint32 guild_id = 5;  // 不为空时指定id查询
}
message GetPgcMonthlyInfoListResp {
    repeated PgcMonthlyInfo info_list = 1;
    uint32 total_cnt = 2;  // 总数
    uint32 next_page = 3;  // 为0时表示结束
}


// 公会公开房日信息
message PgcDailyInfo {
    uint32 ts = 1;
    uint32 channel_id = 2;
    uint64 channel_fee = 3;
    uint32 anchor_fee = 4;  // 从业者收礼流水
    uint32 pkg_gift_fee = 5; // 包裹礼物流水
    uint32 tbean_gift_fee = 6; // T豆礼物流水
    uint32 send_gift_cnt = 7;  // 送礼人数
    uint32 one_hour_valid_cnt = 8;  // 接档1小时从业者数
    uint32 two_hour_valid_cnt = 9;  // 接档2小时从业者数
    uint32 four_hour_valid_cnt = 10;  // 接档4小时从业者数
}

// 获取公会公开房日信息列表
message GetPgcDailyInfoListReq {
   uint32 begin_ts = 1; // 开始时间
   uint32 end_ts = 2;  // 结束时间
   repeated uint32 cid_list = 3;  // 不为空时指定id查询
   uint32 page = 4;  // 从1开始
   uint32 page_size = 5;
   uint32 guild_id = 6; // 不为空时指定id查询
}
message GetPgcDailyInfoListResp {
   repeated PgcDailyInfo info_list = 1;
   uint32 total_cnt = 2; // 总数
   uint32 next_page = 3; // 为0时表示结束
}


// 公会月信息
message GuildMonthlyStatsInfo {
    uint32 ts = 1;
    uint64 guild_fee = 2;  // 公会月流水
    uint32 sign_anchor_cnt = 3; // 签约成员数量
    uint32 active_anchor_cnt = 4;  // 活跃从业者数量
    uint32 quality_anchor_cnt = 5;  // 优质从业者数量
    uint64 active_anchor_fee = 6;  // 活跃从业者贡献流水
    uint64 quality_anchor_fee = 7; // 优质从业者贡献流水
    float quality_anchor_cnt_ratio = 8;  // 优质从业者数量占比
    float quality_anchor_fee_ratio = 9;  // 优质从业者贡献流水占比
    float guild_pkg_fee_ratio = 10;  // 公会背包流水占比
    uint32 profession_prac_cnt = 11;  // 专业从业者数
    uint32 new_profession_prac_cnt = 12;  // 新增专业从业者
    uint32 valid_sign_member_cnt = 13;  // 有效签约成员
    uint32 new_valid_sign_mem_cnt = 14;  // 新增有效签约成员
}

// 获取公会月信息列表
message GetGuildMonthlyStatsInfoListReq {
    uint32 begin_month = 1;
    uint32 end_month = 2;  
    uint32 guild_id = 3;  
    uint32 page = 4;  // 从1开始
    uint32 page_size = 5;
}
message GetGuildMonthlyStatsInfoListResp {
    repeated uint32 guild_id_list = 1;  // 字母公会列表，母公会是第一个
    repeated GuildMonthlyStatsInfo info_list = 2;
    uint32 total_cnt = 3;  // 总数
    uint32 next_page = 4;  // 为0时表示结束
}

// /httpgo/multiplayer/getHomePageInfo 获取首页数据 
message GetMultiPlayerHomepageReq {
  uint32 uid = 1;
  uint32 client_type = 2;
  uint32 market_id = 3;
}
message GetMultiPlayerHomepageResp {
  uint32 uid = 1;
  string account = 2;
  string nickname = 3;
  
  uint32 guild_id = 4;
  uint32 guild_short_id = 5; // 公会靓号
  string guild_name = 6; // 公会名称

  uint32 sign_days = 7; // 签约时长
  uint32 fans_cnt = 8; // 粉丝人数
  uint32 friends_cnt = 10; // 玩伴人数

  // 今日数据
  MultiPlayerDailyInfo today_info = 11;
  // 本周
  MultiPlayerWeekInfo week_info = 12;
  // 本月
  MultiPlayerMonthInfo month_info = 13;
  
  // 公众号消息标题
  string public_msg = 14;
  
  // 当月消费榜前三头像
  repeated string consume_top3_account_list = 15;

  // 当月扩圈数据
  MultiPlayerMonthCommunityInfo this_month_community_info = 16;
}

message MultiPlayerMonthCommunityInfo {
  string date = 1; // eg 2022/11
  uint32 month_new_relation = 2; // 新增关系链
  uint32 month_new_valid_relation = 3; // 新增有效关系链
  uint32 month_focus_total = 4; //累计关注
  uint32 month_new_focus_cnt = 5; //新增关注
  uint32 month_loss_friend_cnt = 6; //流失玩伴
  uint32 month_loss_focus_cnt = 7; //流失关注

  // 月环比，有正负
  float month_new_relation_growrate = 8; // 新增关系链
  float month_new_valid_relation_growrate = 9; // 新增有效关系链
  float month_focus_total_growrate = 10; //累计关注
  float month_new_focus_cnt_growrate = 11; //新增关注
  float month_loss_friend_cnt_growrate = 12; //流失玩伴
  float month_loss_focus_cnt_growrate = 13; //流失关注
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum TYPE_MULTIPLAYER_INFO {
  TYPE_MULTIPLAYER_INFO_INVAILD = 0; // 无效值
  TYPE_MULTIPLAYER_INFO_TODAY = 1; // 今日
  TYPE_MULTIPLAYER_INFO_WEEK = 2; // 本周
  TYPE_MULTIPLAYER_INFO_MONTH = 3; // 本月
}

message MultiPlayerDailyInfo {
  string date = 1; // eg 2022/11/09
  string fee = 2; // 成员收礼
  uint32 sendpresent_uids_cnt = 3; // 送礼人数
  uint32 first_sendpresent_uids_cnt = 4; // 首送礼人数
  string hold_hour = 5; // 接档时长  xx小时xx分钟
  string active_hour = 6; // 活跃时长
  uint32 new_relation = 7; // 新增关系链
}

message MultiPlayerWeekInfo {
  string date = 1; 
  string fee = 2; // 成员收礼
  uint32 sendpresent_uids_cnt = 3; // 送礼人数
  uint32 first_sendpresent_uids_cnt = 4; // 首送礼人数
  uint32 hold_days_cnt = 5; // 有效接档天数
  uint32 active_days_cnt = 6; // 有效活跃天数
  uint32 new_relation = 7; // 新增关系链
  uint32 new_valid_relation = 8; // 新增有效关系链
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message MultiPlayerMonthInfo {
  string date = 1; 
  string fee = 2; // 成员收礼
  uint32 sendpresent_uids_cnt = 3; // 送礼人数
  uint32 first_sendpresent_uids_cnt = 4; // 首送礼人数
  uint32 hold_days_cnt = 5; // 有效接档天数
  uint32 active_days_cnt = 6; // 有效活跃天数
  uint32 new_relation = 7; // 新增关系链
  uint32 new_valid_relation = 8; // 新增有效关系链
  uint32 violation_total_cnt = 9; // 违规总数
  uint32 violation_A_cnt = 10;
  uint32 violation_B_cnt = 11;
  uint32 violation_C_cnt = 12;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message MultiPlayerMonthDetialInfo {
  string date = 1; // eg 2022/11
  string fee = 2; // 成员收礼
  uint32 sendpresent_uids_cnt = 3; // 送礼人数
  uint32 first_sendpresent_uids_cnt = 4; // 首送礼人数
  uint32 hold_days_cnt = 5; // 有效接档天数
  uint32 active_days_cnt = 6; // 有效活跃天数
  uint32 violation_total_cnt = 7; // 违规总数
  uint32 violation_A_cnt = 8;
  uint32 violation_B_cnt = 9;
  uint32 violation_C_cnt = 10;

  // 月环比，有正负
  float month_fee_growrate = 11; 
  float month_sendpresent_uids_cnt_growrate = 12; 
  float month_first_sendpresent_uids_cnt_growrate = 13; 
  float month_hold_days_cnt_growrate = 14; 
  float month_active_days_cnt_growrate = 15; 
  float month_violation_total_cnt_growrate = 16; 
}

// /httpgo/multiplayer/getBaseInfo 获取日，周，月数据详情
message GetMultiPlayerBaseInfoReq {
  uint32 type = 1; // see TYPE_MULTIPLAYER_INFO
  uint32 uid = 2;
}
message GetMultiPlayerBaseInfoResp {
  repeated MultiPlayerDailyInfo daily_info_list = 1; // 日维度最多返回20条，周维度最多返回7条
  repeated MultiPlayerDailyInfo week_info_list = 2; 
  repeated MultiPlayerMonthDetialInfo month_info_list = 3; // 月维度，【本月，上月】
}

message GetMultiPlayerDailyBaseInfoReq {
  uint32 uid = 1;
  uint32 day_ts = 2;
}
message GetMultiPlayerDailyBaseInfoResp {
  uint64 fee = 1; // 成员收礼
  uint32 valid_sec = 2; // 接档时长（秒）
  bool is_valid_day = 3; // 是否有效接档天
}

// /httpgo/multiplayer/getMonthConsumeTop10 获取月消费用户榜
message GetMultiPlayerMonthConsumeTop10Req {
  uint32 uid = 1;
}
message GetMultiPlayerMonthConsumeTop10Resp {
  // buf:lint:ignore MESSAGE_PASCAL_CASE
  message comsumer {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    uint64 month_consume_fee = 4;
  }
  repeated comsumer this_month_consume_top10 = 1; // 本月消费top10
  repeated comsumer last_month_consume_top10 = 2; // 上月消费top10
}

// /httpgo/multiplayer/getMonthCommunityInfo 获取月扩圈数据详情
message GetMultiPlayerMonthCommunityInfoReq {
  uint32 uid = 1;
}
message GetMultiPlayerMonthCommunityInfoResp {
  MultiPlayerMonthCommunityInfo this_month_community_info = 1; // 本月
  MultiPlayerMonthCommunityInfo last_month_community_info = 2; // 上月
}


message DailyInfo {
  uint32 income = 1; // 成员收礼
  uint32 valid_sec = 2; //接档时长
}
message DurInfo {
  uint32 income = 1; // 成员收礼
  uint32 valid_day = 2; //有效接档天数
}

message MultiPlayerCacheInfo {
  DailyInfo daily_info = 1;
  DurInfo week_info = 2;
  DurInfo month_info = 3;
}

message MultiPlayerDetialCacheInfo {
  repeated DailyInfo daily_info_list = 1;
  repeated DurInfo month_info_list = 2;
}

// 场景类型
enum SceneType {
   SCENE_TYPE_INVALID_UNSPECIFIED = 0;  // 无效
   SCENE_TYPE_USERCARD_LIVE = 1;  // 直播房用户资料卡
   SCENE_TYPE_USERCARD_PGC = 2;  // 公会公开房资料卡
   SCENE_TYPE_IM_PAGE = 3;    // im聊天框
}

// 检查用户是否有互动消息查看入口
message CheckUserInteractEntryReq{
   uint32 scene_type = 1;  // see SceneType
   uint32 uid = 2; 
   uint32 interact_uid = 3;  // 互动对方uid
   uint32 channel_id = 4;  // 房间id
   uint64 channel_live_id = 5; //直播场次id, 直播房内需要
}
message CheckUserInteractEntryResp {
   bool is_has_entry = 1;  // 是否有入口
   string entry_text = 2;  // 入口文案  
}


//互动消息
message InteractInfo {
   string interact_type = 1;
   string interact_value = 2; 
}

// 互动标识 
message InteractCertInfo {
  channellivefans.FansInfo fans_info = 1; //语音直播房粉丝信息 已变灰用户需要展示
  string cert_msg = 2;  // 标识内容信息
}

enum InteractUserType {
   INTERACT_USER_TYPE_INVALID_UNSPECIFIED = 0;   //无效
   INTERACT_USER_TYPE_COMMON = 1;  // 普通互动用户
   INTERACT_USER_TYPE_EXPIRE_FANS = 2;  // 粉团铭牌已变灰用户
   INTERACT_USER_TYPE_LIVE_SEVEN_NEW = 3; // 7天内第一次来本直播间
   INTERACT_USER_TYPE_PGC_SEVEN_NEW = 4; // 7天内第一次来本多人互动房间(pgc)
}

// 获取用户的互动消息
message GetUserInteractInfoReq {
   uint32 scene_type = 1;  // see SceneType
   uint32 uid = 2; 
   uint32 interact_uid = 3;  // 互动对方uid
   uint32 channel_id = 4;  // 房间id
   uint64 channel_live_id = 5; //直播场次id, 直播房内需要
}
message GetUserInteractInfoResp {
   uint32 user_type = 1; // see InteractUserType
   repeated InteractInfo info_list = 2;
   InteractCertInfo cert_info = 3; 
}

// 获取用户互动资料隐私查看权限
message GetUserInteractViewPerReq {
   uint32 uid = 1;   
}
message GetUserInteractViewPerResp {
   bool is_open = 1;  // 是否打开
}

// 设置互动资料隐私查看权限
message SetUserInteractViewPerReq {
   uint32 uid = 1;
   bool is_open = 2;  // 是否打开
}
message SetUserInteractViewPerResp {
} 

//*** 多人互动大厅任务 ***//
message GetGuildChannelListReq{
  uint32 guild_id=1;
}
message GetGuildChannelListResp {
  repeated MultiPlayerHallInfo list =1;
}
message AddMultiPlayerHallReq {
  repeated MultiPlayerHallInfo list=1; // 只需要传guild_id,channel_id
  bool is_limit_noself_guild =3; // 是否可配置 非本公会签约成员
  bool is_check=4; // 检查
}
message AddMultiPlayerHallResp {
  bool is_diffent=5; // 为true就是限制不一样
}
message ListMultiPlayerHallReq {
  uint32 guild_id=1;
  uint32 page =2;          // 页码 从0开始
  uint32 page_size = 3;
}
message ListMultiPlayerHallResp {
  uint32 total=1;
  repeated MultiPlayerHallInfo list=2;
}
message DelMultiPlayerHallReq {
  uint32 guild_id=1;
  uint32 channel_id=2;
}
message DelMultiPlayerHallResp {
}
message MultiPlayerHallInfo {
  uint32 guild_id=1;
  uint32 guild_short_id=2;
  string guild_name=3;
  uint32 channel_id=4;
  string channel_view_id=5;
  string channel_name=6;
  uint32 update_time=7;
  bool is_limit_noself_guild=8;
}

message GetGuildMultiPlayerHallReq {
  uint32 guild_id=1;
}
message GetGuildMultiPlayerHallResp {
  message GuildMultiPlayerHall {
    uint32 channel_id=1;
    bool is_del=2;
  }
  repeated GuildMultiPlayerHall list=1;
}


// 日任务类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum HALL_TASK_DAY_TYPE{
  HALL_TASK_DAY_TYPE_INVALID = 0; // 无效值
  HALL_TASK_DAY_TYPE_HOLD_TIME = 1;            // 接档时间
  HALL_TASK_DAY_TYPE_VALID_OPEN_CNT = 2;       // 有效开局数
  HALL_TASK_DAY_TYPE_GOD_FLY_TICKET_CNT = 3;   // 大神带飞券
  HALL_TASK_DAY_TYPE_TBEAN_INCOME = 4;         // 流水  T豆收礼流水
}
// 周任务类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum HALL_TASK_WEEK_TYPE{
  HALL_TASK_WEEK_TYPE_INVALID = 0; // 无效值
  HALL_TASK_WEEK_TYPE_VALID_HOLD_DAY_CNT = 1;   // 有效接档天数
  HALL_TASK_WEEK_TYPE_FINISH_DAY_TASK_CNT = 2;  // 完成日任务次数
  HALL_TASK_WEEK_TYPE_VALID_OPEN_CNT = 3;       // 有效开局数
  HALL_TASK_WEEK_TYPE_GOD_FLY_TICKET_CNT = 4;   // 大神带飞券
  HALL_TASK_WEEK_TYPE_TBEAN_INCOME = 5;         // 流水   T豆收礼流水
}

// 承接大厅分配任务变更类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum HALL_TASK_CHANGE_TYPE {
  HALL_TASK_CHANGE_TYPE_INVALID = 0; 
  HALL_TASK_CHANGE_TYPE_ADD = 1; // 新增
  HALL_TASK_CHANGE_TYPE_UPDATE = 2; // 覆盖更新
  HALL_TASK_CHANGE_TYPE_CANCEL_CONTRACT_DELETE = 3; // 解约删除
  HALL_TASK_CHANGE_TYPE_GUILD_DELETE = 4; // 公会删除
  HALL_TASK_CHANGE_TYPE_HALL_CHANNEL_DELETE = 5; // 运营删除承接大厅
}

// 承接大厅送礼流水记录 礼物来源
// buf:lint:ignore ENUM_PASCAL_CASE
enum HALL_TASK_PRESENT_SOURCE_TYPE {
  HALL_TASK_PRESENT_SOURCE_TYPE_INVALID = 0; 
  HALL_TASK_PRESENT_SOURCE_TYPE_TICKET = 1; // 大神带飞券-背包
  HALL_TASK_PRESENT_SOURCE_TYPE_TBEAN = 2;  // T豆购买
}

 

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetHallTaskConfListReq{
  uint32 guild_id = 1;
  uint32 task_id=2;
  string task_group_name = 3; // 任务组合名称
  uint32 page = 4; // 第一页从0开始
  uint32 pageSize = 5;
}
message GetHallTaskConfListResp{
  uint32 total=1;
  repeated HallTaskConfDetialInfo list=2;
}
// 任务配置详情
message HallTaskConfDetialInfo {
  uint32 task_id=1; // 已存在 修改时传
  string task_group_name = 2; // 任务组合名称
  repeated HallTaskConfInfo day_task_list = 3; // 日任务
  repeated HallTaskConfInfo week_task_list = 4; // 周任务
  string reward_msg = 5; // 任务奖励说明
  bool has_next_week_effect=6; // 是否存在下周生效记录
  bool has_distribute=7; //是否已经分配给用户
}
message HallTaskConfInfo {
  uint32 task_type=1; // see HALL_TASK_DAY_TYPE,HALL_TASK_WEEK_TYPE
  repeated float val_list=2; //梯度数值 
}

message AddHallTaskConfReq {
  uint32 guild_id = 1;
  HallTaskConfDetialInfo info = 2; 
}
message AddHallTaskConfResp {
}

message DelHallTaskConfReq {
  uint32 guild_id=1;
  uint32 task_id=2;
  uint32 op_uid=3;
}
message DelHallTaskConfResp {
}


// 分配任务
message DistributeHallTaskReq {
  uint32 guild_id=1;
  uint32 task_id=2;
  repeated uint32 uid_list = 3;
  bool is_force_update = 4; // 是否覆盖
  uint32 op_uid=5;
}
message DistributeHallTaskResp {
  repeated HallTaskInfo confict_list=1; // 有冲突才返回
}
message HallTaskInfo {
  uint32 guild_id=1;
  uint32 uid=2;
  uint32 task_id=5;
  string task_group_name=6;
 
}

// 查询分配任务
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildHallTaskReq {
  uint32 guild_id = 1;
  string task_group_name = 2;
  uint32 uid=3;
  uint32 page = 4; // 第一页从0开始
  uint32 pageSize = 5;
}
message GetGuildHallTaskResp {
  uint32 total=1;
  repeated HallTaskInfo list=2;
}

enum HallStatisticsType {
  HallStatisticsDay=0; // 日任务
  HallStatisticsWeek=1; // 周任务
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildHallTaskStatsReq {
  uint32 guild_id = 1;
  uint32 stats_type=2; // 厅数据统计方式 HallStatisticsType
  uint32 begin_ts = 3;
  uint32 end_ts = 4;
  uint32 page = 5; // 第一页从0开始
  uint32 pageSize = 6;
}
message GetGuildHallTaskStatsResp {
  uint32 total=1;
  repeated HallStatisticsInfo list=2;
}
message HallStatisticsInfo {
  string date=1; // 时间字符串
  uint32 channel_id=2;
  uint32 enter_channel_cnt=4; // 进房人数
  uint32 has_ticket_cnt=5; // 有体验券的人数
  uint32 used_ticket_cnt=6; // 使用券的人数
  uint32 hold_sign_cnt=7; // 接档人数
  uint64 channel_fee=8; // 房间流水
  uint32 record_id=9; // 厅数据id
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildHallTaskStatsDetialReq {
  uint32 guild_id = 1;
  uint32 stats_type=2; // 厅数据统计方式 HallStatisticsType
  uint32 record_id=3;// 厅数据id
  uint32 task_type=4;// see HALL_TASK_DAY_TYPE,HALL_TASK_WEEK_TYPE
  uint32 uid=5;
  uint32 page = 6; // 第一页从0开始
  uint32 pageSize = 7;
}
message GetGuildHallTaskStatsDetialResp {
  uint32 total=1;
  repeated GuildHallTaskStatsDetial list=2;
}
message GuildHallTaskStatsDetial{
  string date=1; // 时间字符串
  string task_name=2;
  uint32 uid=3;
  string val=6; // 完成情况
}

message GetHallTaskReq {
  uint32 uid=1;
  uint32 channel_id=2; // 客户端调用要传
}
message GetHallTaskResp {
  uint32 task_id=1;
  string task_group_name = 2; // 任务组合名称
  string reward_msg = 3; // 任务奖励说明
  repeated HallTaskDetial day_task_list=4;
  repeated HallTaskDetial week_task_list=5;
  string jump_url=6;
}
message HallTaskDetial {
  string task_name = 1; // 任务名称
  string task_progress = 2; // 任务进度  例如 1h20min/2h
  float rate = 3; // 进度比率
  uint32 task_val=4; // 已完成第几个梯度
  repeated float val_list=5; //梯度数值 
  string date=6; // 当日日期
  uint32 task_type=7;
}



message GetHallTaskHistoryReq {
  uint32 uid=1;

}
message GetHallTaskHistoryResp {
  repeated HallTaskHistoryDetial list=1;
}
message HallTaskHistoryDetial {
  string date=1; // 自然周
  repeated HallTaskDetial day_task_list=2;
  repeated HallTaskDetial week_task_list=3;
}


message GetHallTaskDistributeHistoryReq {
  uint32 uid=1;
}
message GetHallTaskDistributeHistoryResp {
  repeated GetHallTaskDistributeHistory list=1;
}
message GetHallTaskDistributeHistory {
  uint32 uid=1;
  uint32 sign_guild_id=2;
  uint32 guild_id=3;
  uint32 task_id=4;
  uint32 op_uid=5;
  uint32 change_type=6;
  uint32 create_time=7;
}

message DelHallTaskReq {
  uint32 uid=1;
  uint32 guild_id=2;
  uint32 op_uid=3;
}
message DelHallTaskResp {
}

// 大厅任务缓存结构
message HallTaskCacheInfo {
  uint32 uid=1;
  uint32 guild_id=2; // 分配任务的公会id, 不是签约公会id
  uint32 task_id=3;
  HallTaskVal day_task_val=4;
  HallTaskVal week_task_val=5;
}
// 各种值，共用一个msg
message HallTaskVal {
  uint32 valid_sec=1; // 接档时间-秒
  uint32 valid_open_cnt=2; // 有效开局数
  uint32 ticket_cnt=3; // 大神带飞券数量
  uint32 income=4; // T豆收礼数
  uint32 valid_hold_day=5; // 有效接档天数
  uint32 finish_day_task_cnt=6; // 完成日任务次数
}

message GetHallTaskConfByIdReq {
  uint32 guild_id=1;
  uint32 task_id=2;
  uint32 query_type=3; //0-查看，1-修改
}
message GetHallTaskConfByIdResp{
  HallTaskConfDetialInfo info=1;
}


//*** 多人互动大厅任务 end***//


message GetValidHoldDayUidReq {
  uint32 channel_id=1;
  uint32 begin_ts=2;
  uint32 end_ts=3;
}
message GetValidHoldDayUidResp {
  repeated uint32 uids=1;
}

// 检查公会的子母关系
message GetBindGuildInfoReq {
  uint32 guild_a = 1;
  uint32 guild_b = 2; 
}
message GetBindGuildInfoResp {
  bool is_bind = 1;  // 是否绑定关系
}

message GuildAb {
  uint32 guild_a = 1;
  uint32 guild_b = 2;
  bool   is_bind = 3;
}
// 批量获取公会绑定关系
message BatchGetBindGuildInfoReq {
  repeated GuildAb list = 1;
}
message BatchGetBindGuildInfoResp {
  repeated GuildAb list = 1;
}

// 配置里面，会设置自己是自己的子公会，使用的时候需要注意
message BindGuildInfo {
  uint32 guild_id = 1; 
  uint32 bind_guild_id = 2;  // 母公会
}

//获取子母公会配置列表
message GetBindGuildInfoListReq{
}
message GetBindGuildInfoListResp{
  repeated BindGuildInfo info_list = 1;
}
