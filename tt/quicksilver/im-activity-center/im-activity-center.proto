syntax = "proto3";

package im_activity_center;
option go_package = "golang.52tt.com/protocol/services/im-activity-center";

service ImActivityCenter {
    // 获取可见用户配置
    rpc GetVisibleUserConf (GetVisibleUserConfReq) returns (GetVisibleUserConfResp) {}
    // 新增活动
    rpc AddImActivity (AddImActivityReq) returns (AddImActivityResp) {}
    // 删除活动
    rpc DelImActivity (DelImActivityReq) returns (DelImActivityResp) {}
    // 更新活动信息
    rpc UpdateImActivity (UpdateImActivityReq) returns (UpdateImActivityResp) {}
    // 推荐活动（推送红点）
    rpc RecommendImActivity (RecommendImActivityReq) returns (RecommendImActivityResp) {}
    // 调整活动排序
    rpc RearrangeImActivity (RearrangeImActivityReq) returns (RearrangeImActivityResp) {}
    // 获取分类（板块）下的活动（包括已过期的）
    rpc GetImActivitiesByCategoryId (GetImActivitiesByCategoryIdReq) returns (GetImActivitiesByCategoryIdResp) {}
    // 获取活动
    rpc GetImActivityById (GetImActivityByIdReq) returns (GetImActivityByIdResp) {}

    // 新增活动分类（板块）
    rpc AddImActivityCategory (AddImActivityCategoryReq) returns (AddImActivityCategoryResp) {}
    // 删除活动分类（板块）
    rpc DelImActivityCategory (DelImActivityCategoryReq) returns (DelImActivityCategoryResp) {}
    // 更新活动分类（板块）
    rpc UpdateImActivityCategory (UpdateImActivityCategoryReq) returns (UpdateImActivityCategoryResp) {}
    // 调整分类（板块）排序
    rpc RearrangeImActivityCategory (RearrangeImActivityCategoryReq) returns (RearrangeImActivityCategoryResp) {}
    // 获取所有活动分类（板块）
    rpc GetAllImActivityCategory (GetAllImActivityCategoryReq) returns (GetAllImActivityCategoryResp) {}
    // 获取分类
    rpc GetImActivityCategoryById (GetImActivityCategoryByIdReq) returns (GetImActivityCategoryByIdResp) {}

    // 新增IM活动中心入口图标
    rpc AddImActivityCenterEntranceIcon (AddImActivityCenterEntranceIconReq) returns (AddImActivityCenterEntranceIconResp) {}
    // 删除IM活动中心入口图标
    rpc DelImActivityCenterEntranceIcon (DelImActivityCenterEntranceIconReq) returns (DelImActivityCenterEntranceIconResp) {}
    // 更新IM活动中心入口图标信息
    rpc UpdateImActivityCenterEntranceIcon (UpdateImActivityCenterEntranceIconReq) returns (UpdateImActivityCenterEntranceIconResp) {}
    // 获取所有IM活动中心入口图标
    rpc GetAllImActivityCenterEntranceIcon (GetAllImActivityCenterEntranceIconReq) returns (GetAllImActivityCenterEntranceIconResp) {}

    // 新增IM活动中心入口文案
    rpc AddImActivityCenterEntranceText (AddImActivityCenterEntranceTextReq) returns (AddImActivityCenterEntranceTextResp) {}
    // 删除IM活动中心入口文案
    rpc DelImActivityCenterEntranceText (DelImActivityCenterEntranceTextReq) returns (DelImActivityCenterEntranceTextResp) {}
    // 更新IM活动中心入口文案信息
    rpc UpdateImActivityCenterEntranceText (UpdateImActivityCenterEntranceTextReq) returns (UpdateImActivityCenterEntranceTextResp) {}
    // 获取所有IM活动中心入口文案
    rpc GetAllImActivityCenterEntranceText (GetAllImActivityCenterEntranceTextReq) returns (GetAllImActivityCenterEntranceTextResp) {}
    // 调整IM活动中心入口文案排序
    rpc RearrangeImActivityCenterEntranceText (RearrangeImActivityCenterEntranceTextReq) returns (RearrangeImActivityCenterEntranceTextResp) {}

    // 活动中心入口
    rpc ImActivityCenterEntrance (ImActivityCenterEntranceReq) returns (ImActivityCenterEntranceResp) {}
}

message GetVisibleUserConfReq{}
message GetVisibleUserConfResp{
    repeated VisibleUser users = 1;
}

message AddImActivityReq{
    ImActivity activity = 1; // 不需要id
}
message AddImActivityResp{
    ImActivity activity = 1; // 多返回id
}

message DelImActivityReq{
    string activity_id = 1;
}
message DelImActivityResp{}

message UpdateImActivityReq{
    ImActivity activity = 1;
}
message UpdateImActivityResp{}

message RecommendImActivityReq{
    string activity_id = 1;
}
message RecommendImActivityResp{}

message RearrangeImActivityReq{
    repeated ImActivity activities = 1;
}
message RearrangeImActivityResp{}

message GetImActivitiesByCategoryIdReq{
    string category_id = 1;
}
message GetImActivitiesByCategoryIdResp{
    repeated ImActivity activities = 1;
}

message GetAllImActivityCenterEntranceIconReq{}
message GetAllImActivityCenterEntranceIconResp{
    repeated ImActivityCenterEntranceIcon icons = 1;
}

message ImActivityCenterEntranceReq{}
message ImActivityCenterEntranceResp{
    repeated ImActivity top_activities = 1;
    repeated Category categories = 2;
    string icon = 3; // 入口图片
    repeated string texts = 4; // 入口文案
}

enum VisibleUserType{
    VisibleUserType_UNDEFINED = 0;
    VisibleUserType_ALL = 1; // 所有用户
    VisibleUserType_FRESHMAN = 2; // 新用户
}

message VisibleUser{
    uint32 type = 1;
    string desc = 2; // 描述
    uint32 period = 3; // 新用户可见时间，单位：天
}

message Category{
    ImActivityCategory category = 1;
    repeated ImActivity activities = 2;
}

message Text{
    repeated string texts = 1;
    uint32 show_begin_at = 2; // 开始展示时间
    uint32 show_end_at = 3; // 结束展示时间
}

message ImActivity{
    string id = 1;
    string title = 2; // 标题
    string icon = 3; // 图片
    VisibleUser user = 4; // 可见用户
    string jump_url = 5; // 跳转链接
    uint32 begin_at = 6; // 开始时间
    uint32 end_at = 7; // 结束时间
    uint32 last_push_time = 8; // 上次红点推送时间，0代表不推送
    ImActivityCategory category = 9;
    repeated uint32 os_types = 10;
    uint32 show_begin_at = 11; // 开始时间（用于展示）
    uint32 show_end_at = 12; // 结束时间（用于展示）
}

enum OsType{
    UNKNOWN = 0;
    ANDROID = 1;
    IOS = 2;
}

message ImActivityCenterEntranceIcon{
    string id = 1;
    string url = 2;
    uint32 show_begin_at = 3; // 开始展示时间
    uint32 show_end_at = 4; // 结束展示时间
}

message ImActivityCenterEntranceText{
    string id = 1;
    string text = 2;
    uint32 show_begin_at = 3; // 开始展示时间
    uint32 show_end_at = 4; // 结束展示时间
}

message AddImActivityCenterEntranceIconReq{
    ImActivityCenterEntranceIcon icon = 1; // 不需要id
}
message AddImActivityCenterEntranceIconResp{
    ImActivityCenterEntranceIcon icon = 1; // 多返回id
}

message DelImActivityCenterEntranceIconReq{
    string id = 1;
}
message DelImActivityCenterEntranceIconResp{}

message UpdateImActivityCenterEntranceIconReq{
    ImActivityCenterEntranceIcon icon = 1;
}
message UpdateImActivityCenterEntranceIconResp{}

message ImActivityCategory{
    string id = 1;
    string title = 2; // 标题
    repeated string channels = 3; // 渠道号
    VisibleUser user = 4; // 对于特定渠道号用户生效
}

message AddImActivityCategoryReq{
    ImActivityCategory category = 1; // 不需要id
}
message AddImActivityCategoryResp{
    ImActivityCategory category = 1; // 多返回id
}

message DelImActivityCategoryReq{
    string category_id = 1;
}
message DelImActivityCategoryResp{}

message UpdateImActivityCategoryReq{
    ImActivityCategory category = 1;
}
message UpdateImActivityCategoryResp{}

message RearrangeImActivityCategoryReq{
    repeated ImActivityCategory categories = 1;
}
message RearrangeImActivityCategoryResp{}

message GetAllImActivityCategoryReq{}
message GetAllImActivityCategoryResp{
    repeated ImActivityCategory categories = 1;
}

message AddImActivityCenterEntranceTextReq{
    ImActivityCenterEntranceText text = 1; // 不需要id
}
message AddImActivityCenterEntranceTextResp{
    ImActivityCenterEntranceText text = 1; // 多返回id
}

message DelImActivityCenterEntranceTextReq{
    string id = 1;
}
message DelImActivityCenterEntranceTextResp{}

message UpdateImActivityCenterEntranceTextReq{
    ImActivityCenterEntranceText text = 1;
}
message UpdateImActivityCenterEntranceTextResp{}

message GetAllImActivityCenterEntranceTextReq{}
message GetAllImActivityCenterEntranceTextResp{
    repeated ImActivityCenterEntranceText texts = 1;
}

message GetImActivityByIdReq{
    string id = 1;
}
message GetImActivityByIdResp{
    ImActivity activity = 1;
}

message GetImActivityCategoryByIdReq{
    string id = 1;
}
message GetImActivityCategoryByIdResp{
    ImActivityCategory category = 1;
}

message RearrangeImActivityCenterEntranceTextReq{
    repeated ImActivityCenterEntranceText texts = 1;
}
message RearrangeImActivityCenterEntranceTextResp{}
