syntax = "proto3";

// namespace
option go_package = "golang.52tt.com/protocol/services/channel-vote-pk-go";
package channel_vote_pk_go;

service ChannelVotePkGo {

  // 用户投票
  rpc ChannelVotePkVote (ChannelVotePkVoteReq) returns (ChannelVotePkVoteResp);

  // 开始pk
  rpc ChannelVotePkStart (ChannelVotePkStartReq) returns (ChannelVotePkStartResp);

  // 取消pk
  rpc ChannelPkCancel (ChannelPkCancelReq) returns (ChannelPKCancelResp);

  // 进房时获取pk信息
  rpc GetChannelVotePKInfo (GetChannelVotePKInfoReq) returns (GetChannelVotePKInfoResp);

  // 搜索pk候选人
  rpc SearchPkCandidate (SearchPkCandidateReq) returns (SearchPkCandidateResp);

  // 获取pk状态
  rpc GetVotePkStatus (GetVotePkStatusReq) returns (GetVotePkStatusResp);

  // 增加pk票数
  rpc AddVotePkTicket (AddVotePkTicketReq) returns (AddVotePkTicketResp);

  // 原channelcommonvotepk相关接口

  //设置人气投票的相关信息
  rpc SetChannelCommonPkInfo( SetChannelCommonPkInfoReq ) returns( SetChannelCommonPkInfoResp );

  //获得观众的剩余票数
  rpc GetUserLeftVoteCnt ( GetUserLeftVoteCntReq ) returns ( GetUserLeftVoteCntResp );

  //投票
  rpc ChannelCommonPkVote ( ChannelCommonPkVoteReq ) returns ( ChannelCommonPkVoteResp );


  // 设置房间可投票
  rpc BatchSetChannelVote (BatchSetChannelVoteReq) returns (BatchSetChannelVoteResp) {}

};


enum ChannelVotePkType
{
  INVALID_TYPE = 0; //无效
  VOTE_PK_TYPE = 1; //投票类型
  DIAMOND_PK_TYPE = 2; //红钻
  TBEAN_PK_TYPE = 3;  // T豆
  USER_DEFINED_TYPE = 100; /* 自定义pk */
}

message ChannelVotePkCompetitor
{
  uint32 uid = 1;
  uint32 vote = 2;
  uint32 rank = 3;
}

message ChannelPkInfo
{
  uint32 uid = 1; //发起玩家
  uint32 channel_id = 2;
  uint32 type = 3; //enum ChannelVotePkType PK类型
  uint32 duration_min = 4;
  uint32 start_timestamp = 5; //开始时间戳
  uint32 person_type = 6; //ChannelVotePKNumType
  uint32 vote_cnt = 7; //如果是投票类型的情况，每个观众的票数
  string pk_name = 8; //PK 名字
  uint32 channel_type = 9; //房间类型
  string channel_name = 10; //房间名
}

message ChannelVotePKRankInfo
{
  ChannelPkInfo info = 1;
  repeated ChannelVotePkCompetitor competiter_list = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ChannelVotePkVoteReq
{
  uint32 uid = 1; //投票玩家
  uint32 to_uid = 2; //目标玩家
  uint32 vote_cnt = 3; //投票数量
  uint32 channel_id = 4;
  uint32 start_timestamp = 5; //开始时间戳
  uint32 send_timestamp = 6; //送礼时间
  uint32 pk_type = 7 ; // PK类型，投票0, 礼物1
  bool isLive = 8;   // 用来区分是不是直播房，部分逻辑有区别
}

message ChannelVotePkVoteResp
{
  uint32 code = 1; //错误码
  uint32 left_vote = 2; //剩余票数
  uint32 remain_extra_vote = 3; //剩余额外票数
}

message ChannelVotePkStartReq
{
  uint32 uid = 1;
  uint32 channel_id = 2;
  uint32 duration_min = 3; //持续分钟
  uint32 type = 4; //  enum ChannelVotePkType PK类型
  repeated uint32 uid_list = 5; //参与UID
  uint32 vote_cnt = 6; //每个观众的票数
  string pk_name = 7; //PK 名
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ChannelVotePkStartResp
{
  uint32 code = 1;
  uint32 channel_id = 2;
  uint32 duration_min = 3; //持续分钟
  uint32 start_timestamp = 4; //服务端时间，PK开始时间戳  channel_id和start_timestamp唯一确定一个PK对象
  uint32 left_vote = 5; //自己剩余票数
  bool isLive = 6;   // 用来区分是不是直播房，部分逻辑有区别
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ChannelPkCancelReq
{
  uint32 uid = 1;
  uint32 channel_id = 2;
  uint32 start_timestamp = 3;
  bool isLive = 4;   // 用来区分是不是直播房，部分逻辑有区别
}

message ChannelPKCancelResp
{
  uint32 code = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetChannelVotePKInfoReq
{
  uint32 uid = 1;
  uint32 channel_id = 2;
  bool isLive = 3;   // 用来区分是不是直播房，部分逻辑有区别
}

message GetChannelVotePKInfoResp
{
  uint32 code = 1;
  uint32 left_vote = 2; //剩余票数
  ChannelVotePKRankInfo pk_info = 3; //PK全量信息
}

message SearchPkCandidateReq{
  uint32 channel_id = 1;
  string key_word = 2;
}

message SearchPkCandidateResp{
  repeated PkCandidateInfo candidate_info = 1;
}

message PkCandidateInfo {
  uint32 uid = 1;
  string nickname = 2;
  string account = 3;
  string face_md5 = 4;
  uint32 sex = 5 ; //female 0 male 1
  string alias = 6;
}

message GetVotePkStatusReq{
  uint32 channel_id = 1;
}

message GetVotePkStatusResp{
  bool show_pk_switch = 1;
}

//人气投票的PK信息
message ChannelCommonPkInfo
{
  uint32 channel_id = 1;
  uint32 start_timestamp = 2; //开始时间戳
  uint32 duration_min = 3;
  uint32 vote_cnt_limit = 4; //每个观众的可投票数
}

message ChannelCommonPkCompetitor
{
  uint32 channel_id = 1;
  uint32 start_timestamp = 2;
  uint32 uid = 3;          //选手uid
}

message SetChannelCommonPkInfoReq
{
  ChannelCommonPkInfo info = 1;
}

message SetChannelCommonPkInfoResp
{
  uint32 code = 1;  //错误码
}


message GetUserLeftVoteCntReq
{
  uint32 uid = 1;
  uint32 channel_id = 2;
  uint32 start_timestamp = 3; //开始时间戳
}

message GetUserLeftVoteCntResp
{
  uint32 left_vote_cnt = 1;
  uint32 remain_extra_vote = 2; //剩余额外票数
}


message ChannelCommonPkVoteReq
{
  uint32 from_uid = 1;    //投票的观众
  uint32 channel_id = 2;
  uint32 start_timestamp = 3; //开始时间戳
  uint32 to_uid = 4;          //收票的选手
}

message ChannelCommonPkVoteResp
{
  uint32 code = 1;  //错误码
}

message AddVotePkTicketReq
{
  repeated uint32 uid_list = 1; //需要加票的用户
  uint32 add_cnt = 2; //加票数量
  uint32 channel_id = 3;
  uint32 start_timestamp = 4; //开始时间戳
  uint32 push_version = 5; /* push时间戳*/
  uint32 user_add_ticket_cnt = 6; /* 用户当前累积获得的总加票数 */
  uint32 user_remain_ticket_cnt = 7; /* 用户剩余可投额外票总数 */
  bool is_end_act = 8; /* 是否结束活动，客户端弹窗提示（乐窝活动未上架/未到预热时间/开关否到否为false，其余为true） */
}

message AddVotePkTicketResp
{
}

message BatchSetChannelVoteReq {
  repeated uint32 channel_ids = 1;
  uint32 ttl = 2;
}
message BatchSetChannelVoteResp {
}