syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-extend-api";

package channel_extend_api;

service ChannelExtendApi {
    rpc GetChannelEnterExtInfo (GetChannelEnterExtInfoReq) returns (GetChannelEnterExtInfoResp) {
    }
}

message GetChannelEnterExtInfoReq {
    uint32 uid = 1;  // 占位，这里仅表示该请求需要uid，uid在请求头部携带
    uint32 channel_id = 2;
    uint32 enter_source = 3;
    uint32 follow_friend_uid = 4;
    bool is_knock_door = 5;
}

message GetChannelEnterExtInfoResp {
    bytes channel_enter_opt = 1;     // 进房扩展信息 see channel_opt_v2.proto -> ChannelEnterOptV2
    uint32 user_confirm_status = 2;  // see channel_.proto -> MemberConfirmStatus 用于房间召集状态
    bytes enter_msg = 3;             // 附加文本
    bytes welcome_msg = 4;           // 欢迎语文本
}
