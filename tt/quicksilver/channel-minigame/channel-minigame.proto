syntax = "proto3";

package channel_minigame;
option go_package = "golang.52tt.com/protocol/services/channel-minigame";

service ChannelMinigame {
  rpc OpenNumBomb(OpenNumBombReq) returns(OpenNumBombResp){
  }
  rpc StartNumBomb(StartNumBombReq) returns(StartNumBombResp){
  }
  rpc PlayNumBomb(PlayNumBombReq) returns(PlayNumBombResp){
  }
  rpc CloseNumBomb(CloseNumBombReq) returns(CloseNumBombResp){
  }
  rpc GetNumBombStatus(GetNumBombStatusReq) returns(GetNumBombStatusResp){
  }
}

enum NumBombStatus {
  UNKNOWN = 0;  //异常
  SHUTDOWN = 1; //关闭
  PREPARING = 2;//准备中
  PLAYING = 3;  //进行中
  END_HIT = 4;  //游戏结束：有人猜中
  END_FAIL = 5; //游戏结束：发生异常
}
//炸弹状态变更通知
message NumBombInfo {
  uint64 seqno = 1;  //暂用时间戳
  NumBombStatus status = 2;
  uint32 min_num = 3;
  uint32 max_num = 4;
  uint32 bomb_num = 5; //最后一次猜的数字
  repeated ChannelMinigamePlayer players = 6;
  ChannelMinigamePlayer current_player = 7;
  string voice_url = 8; //语音地址，status=playing时用到
  uint32 opener = 9; //  开启人
  uint32 target_num = 10;
}

message ChannelMinigamePlayer{
  uint32 uid = 1;
  string user_name = 2;
}

//开启炸弹弹窗
message OpenNumBombReq {
  uint32 channel_id = 1;
  uint32 opener = 2;
  bool restart = 3;
}
message OpenNumBombResp {
  NumBombInfo info = 1; //响应中也携带该推送数据，以便第一时间返回
}

//开始玩炸弹
message StartNumBombReq {
  uint32 channel_id = 1;
  uint32 min_num = 2;
  uint32 max_num = 3;
  repeated ChannelMinigamePlayer players = 4;
  uint32 operator = 5;
}
message StartNumBombResp {
  NumBombInfo info = 1; //响应中也携带该推送数据，以便第一时间返回
}

//玩炸弹
message PlayNumBombReq {
  uint32 channel_id = 1;
  uint32 choose_num = 2;
  uint32 player = 3;
}
message PlayNumBombResp {
  bool hit = 1; //是否猜中了
  NumBombInfo info = 2; //响应中也携带该推送数据，以便第一时间返回
}

//关闭炸弹
message CloseNumBombReq {
  uint32 channel_id = 1;
  uint32 operator = 2;
}
message CloseNumBombResp {
}

//获取当前数字炸弹游戏状态
message GetNumBombStatusReq{
  uint32 channel_id = 1;
}

message GetNumBombStatusResp {
  NumBombInfo info = 1;
}
