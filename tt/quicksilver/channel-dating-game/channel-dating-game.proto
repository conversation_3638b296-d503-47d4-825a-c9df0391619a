syntax = "proto3";

package channel_dating_game;
option go_package = "golang.52tt.com/protocol/services/channel-dating-game";
import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service ChannelDatingGame {
  option (service.options.service_ext) = {
    service_name: "channel-dating-game"
  };
  
  rpc OpenDatingGameEntry ( OpenDatingGameEntryReq ) returns( OpenDatingGameEntryResp ) {}
  rpc CheckDatingGameEntry ( CheckDatingGameEntryReq ) returns( CheckDatingGameEntryResp ) {}

  rpc SetGamePhase ( SetGamePhaseReq ) returns( SetGamePhaseResp ) {}
  rpc GetGamePhase ( GetGamePhaseReq ) returns( GetGamePhaseResp ) {}

  rpc GetUserLikeBeatVal ( GetUserLikeBeatValReq ) returns( GetUserLikeBeatValResp ) {}
  rpc GetUserRankLikeBeatVal ( GetUserRankLikeBeatValReq ) returns( GetUserRankLikeBeatValResp ) {}
  rpc SetUserLikeBeatObj ( SetUserLikeBeatObjReq ) returns( SetUserLikeBeatObjResp ) {}
  rpc GetUserLikeBeatObj ( GetUserLikeBeatObjReq ) returns( GetUserLikeBeatObjResp ) {}
  rpc UserApplyMic ( UserApplyMicReq ) returns( UserApplyMicResp ) {}
  rpc GetApplyMicUserList ( GetApplyMicUserListReq ) returns( GetApplyMicUserListResp ) {}

  rpc InitDatingMember ( InitDatingMemberReq ) returns( InitDatingMemberResp ) {}

  rpc GetVipMicUser ( GetVipMicUserReq ) returns( GetVipMicUserResp ) {}

  rpc GetSelectLikeBeatObjUser ( GetSelectLikeBeatObjUserReq ) returns( GetSelectLikeBeatObjUserResp ) {}

  rpc ConfirmVipHoldMic ( ConfirmVipHoldMicReq ) returns( ConfirmVipHoldMicResp ) {}

  rpc GetDatingGameCurInfo ( GetDatingGameCurInfoReq ) returns( GetDatingGameCurInfoResp ) {}

  rpc GetOpenLikeUserList ( GetOpenLikeUserListReq ) returns( GetOpenLikeUserListResp ) {}

  rpc CloseDatingGameEntry( CloseDatingGameEntryReq ) returns( CloseDatingGameEntryResp ) {}

  rpc TestDrawImage ( TestDrawImageReq ) returns( TestDrawImageResp ) {}

  // 获取礼物kafka消费单数
  rpc CntPresentEvLogTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  // 获取礼物kafka消费订单
  rpc GetPresentEvLogOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  // 礼物Kafka消费补单
  rpc FixPresentEvLogOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

  // ================== 相亲厅牵手资源配置后台 ==================
  // 新增相亲厅牵手资源配置
  rpc AddDatingDrawCfg(AddDatingDrawCfgReq) returns (AddDatingDrawCfgResp) {}
  // 更新相亲厅牵手资源配置
  rpc UpdateDatingDrawCfg(UpdateDatingDrawCfgReq) returns (UpdateDatingDrawCfgResp) {}
  // 分页获取相亲厅牵手资源配置
  rpc BatGetDatingDrawCfg(BatGetDatingDrawCfgReq) returns (BatGetDatingDrawCfgResp) {}
  // 通过场景id获取相亲厅牵手资源配置
  rpc BatGetDrawCfgBySceneId(BatGetDrawCfgBySceneIdReq) returns (BatGetDrawCfgBySceneIdResp) {}
  // 删除牵手资源配置
  rpc BatDelDrawCfgBySceneId(BatDelDrawCfgBySceneIdReq) returns (BatDelDrawCfgBySceneIdResp) {}
  
  // 上架相亲厅牵手资源配置
  rpc ApplyDatingDrawCfg(ApplyDatingDrawCfgReq) returns (ApplyDatingDrawCfgResp) {}
  // 下架相亲厅牵手资源配置
  rpc CancelApplyDrawCfg(CancelApplyDrawCfgReq) returns (CancelApplyDrawCfgResp) {}
  // 获取相亲厅牵手资源上下架状态信息
  rpc GetApplyDrawCfgState(GetApplyDrawCfgStateReq) returns (GetApplyDrawCfgStateResp) {}
  // 编辑上下架配置
  rpc EditApplyDrawCfgState(EditApplyDrawCfgStateReq) returns (EditApplyDrawCfgStateResp) {}
  // 上架长期牵手场景前置检查
  rpc PreCheckApplyDrawCfg(PreCheckApplyDrawCfgReq) returns (PreCheckApplyDrawCfgResp) {}

  // 全量获取挚友相关场景信息
  rpc GetAllSceneFellowInfo(GetAllSceneFellowInfoReq) returns (GetAllSceneFellowInfoResp) {}

  // 获取本地生效中的相亲厅牵手资源配置
  rpc GetLocalApplyDrawCfg(GetLocalApplyDrawCfgReq) returns (GetLocalApplyDrawCfgResp) {}

  // 批量获取房间相亲当前阶段
  rpc BatchGetChannelDatingGamePhase(BatchGetChannelDatingGamePhaseReq) returns (BatchGetChannelDatingGamePhaseResp) {}
}


message HatUser {
  uint32 uid = 1;
  DatingGameHatCfg hat_cfg = 2;
}

// 获取相亲房信息
// 用于进房的时候拉取初始信息（比如麦上用户的心动值 / 当前帽子用户 / 当前土豪位用户 / 当前阶段 ）
message GetDatingGameCurInfoReq {
  uint32 channel_id = 1;
}

message GetDatingGameCurInfoResp {
  uint32 phase = 1;      // 房间的当前阶段 DatingGamePhaseType
  uint32 vip_uid = 2;  // vip麦的用户
  repeated HatUser hat_user_list = 3;  // 获得帽子的麦上用户
  repeated UserLikeBeatInfo like_beat_list = 4; // 麦上用户心动值和心动状态
  repeated OpenLikeUserInfo open_like_user_list = 5;  // 已经公布心动对象的用户
  uint32 apply_mic_len = 6; // 申请连麦人数
}

// 开启相亲游戏入口
message OpenDatingGameEntryReq {
  uint32 channel_id = 1;
  uint32 level = 2; // see ga::DatingGameLevel
}

message OpenDatingGameEntryResp{}

message CloseDatingGameEntryReq {
  uint32 channel_id = 1;
}

message CloseDatingGameEntryResp{}

message CheckDatingGameEntryReq{
  uint32 channel_id = 1;
}

message CheckDatingGameEntryResp{
  bool is_open = 1;
  uint32 level = 2;  // see ga::DatingGameLevel
}


// 设置当前阶段
message SetGamePhaseReq{
  uint32 channel_id = 1;
  uint32 target_phase = 2;  // ga::DatingGamePhase
  uint32 op_uid = 3;

}
message SetGamePhaseResp{
  uint32 from_phase = 1;  // ga::DatingGamePhase
}


message GetGamePhaseReq{
  uint32 channel_id = 1;
}
message GetGamePhaseResp{
  uint32 curr_phase = 1;  // ga::DatingGamePhase
}

// 初始化相亲游戏的成员(用于初始化 游戏开始时 已经在麦上的)
message DatingMember{
  uint32 uid = 1;
  uint32 mic_id = 2;
  uint32 sex = 3;
}

message InitDatingMemberReq{
  uint32 channel_id = 1;
  repeated DatingMember member_list = 2;
}
message InitDatingMemberResp{}


// 获取心动值
message GetUserLikeBeatValReq{
  uint32 channel_id = 1;
  repeated uint32 uid_list = 2;
}

message GetUserLikeBeatValResp{
  repeated UserLikeBeatInfo like_beat_info_list = 2;
}

message GetUserRankLikeBeatValReq{
  uint32 channel_id = 1;
}

message GetUserRankLikeBeatValResp{
  repeated UserLikeBeatInfo like_beat_info_list = 2;
}

message UserLikeBeatInfo{
  uint32 uid = 1;
  uint32 like_beat_val = 2;
  bool select_status = 3;    // 选择状态 0 未选（需根据阶段处理） 1 已选择
}

// 设置心动对象
message SetUserLikeBeatObjReq{
  uint32 channel_id = 1;
  uint32 select_uid = 2;
  uint32 like_uid = 3;
}
message SetUserLikeBeatObjResp{}

// 获取心动对象
message GetUserLikeBeatObjReq{
  uint32 channel_id = 1;
  uint32 open_uid = 2;
}

message GetUserLikeBeatObjResp{
  uint32 like_uid = 1;
}

message GetSelectLikeBeatObjUserReq{
  uint32 channel_id = 1;
  uint32 uid = 3;
}

message GetSelectLikeBeatObjUserResp{
  repeated uint32 select_uid_list = 1;
}

// 相互心动信息
message MatchLikeBeatInfo{
  uint32 uid_a = 1;
  uint32 uid_b = 2;
  uint32 like_beat_val_a = 3;
  uint32 like_beat_val_b = 4;
}

// 获取已公布的用户
message GetOpenLikeUserListReq{
  uint32 channel_id = 1;
  uint32 uid = 2;
}

message GetOpenLikeUserListResp{
  repeated OpenLikeUserInfo open_info_list = 1;
}

message OpenLikeUserInfo{
  uint32 open_uid = 1;
  uint32 like_uid = 2;
}

// 排麦
message UserApplyMicReq{
  uint32 channel_id = 1;
  uint32 uid = 2;
  bool is_cancel = 3;
}

message UserApplyMicResp{}

// 获取排麦用户列表
message GetApplyMicUserListReq{
  uint32 channel_id = 1;
  uint32 uid = 2;
}

message GetApplyMicUserListResp{
  repeated uint32 uid_list = 2;
}

// 帽子信息
message DatingGameHatCfg{
  uint32 hat_id = 1;
  string url = 2;
  string  md5 = 3;
  uint32 tbean_limit = 4;
  bool is_male = 5;
  uint32 level = 6;
}

// 获取vip用户
message GetVipMicUserReq{
  uint32 channel_id = 1;
}

message GetVipMicUserResp{
  uint32 vip_uid = 1;
}

// vip用户上麦确认
message ConfirmVipHoldMicReq{
  uint32 uid = 1;
  uint32 channel_id = 2;
}

message ConfirmVipHoldMicResp{
  uint32 pre_vip_uid = 1;
}

message TestDrawImageReq {
  uint32 cid = 1;
  uint32 uid_a = 2;
  uint32 uid_b = 3;
  uint32 v_a = 4;
  uint32 v_b = 5;
  bool   push_channel = 6; // 是否推送房间场景动画

  uint32 scence_id = 7;    // 牵手场景id
  bool use_store_conf = 8;   // 是否使用后台配置的场景资源
  int64 trigger_ts = 9;    // 触发时间戳，秒级，为0则表示使用当前时间
}

message TestDrawImageResp {}

// =================== 相亲资源配置后台接口 begin =======================
message DatingGameDrawCfg{
  uint32 scene_id = 1;              // 场景id，新增时忽略，修改时必填
  string name = 2;                  // 场景名
  uint32 tbean_limit = 3;           // 场景金额(T豆)
  string background_pic = 4;        // 挚友空间背景图
  string fellow_nameplate = 5;      // 挚友相亲铭牌，非唯一
  string fellow_nameplate_uniq = 6; // 挚友相亲铭牌,唯一
  
  string ch_background_pic = 7; // 牵手房间背景图
  string im_background_pic = 8; // 牵手im背景图
  string effect_url = 9;        // 场景动画资源
  string effect_url_md5 = 10;   // 场景动画md5
  
  uint32 breaknews_id = 11;     // 全服公告id，非必填
  uint32 hw_id_female= 12;      // 麦位框-女 suit_id，非必填
  uint32 hw_id_male = 13;       // 麦位框-男 suit_id，非必填

  string backgroud_param_json = 14; // 场景背景图参数,必填

  string remark = 15;  // 运营备注，非必填，限30字

  // 额外字段
  int64 begin_ts = 16;  // 开始时间
  int64 end_ts = 17;    // 结束时间
}

// 新增相亲厅牵手资源配置
message AddDatingDrawCfgReq{
  DatingGameDrawCfg cfg = 1;
}

message AddDatingDrawCfgResp{}


// 更新相亲厅牵手资源配置
message UpdateDatingDrawCfgReq{
    DatingGameDrawCfg cfg = 1;
}

message UpdateDatingDrawCfgResp{}

enum SortByType{
    SORT_TYPE_UNSPECIFIED = 0;  // 默认按update time降序排序
    SORT_TYPE_TBEAN = 1;
}


// 分页获取相亲厅牵手资源配置
message BatGetDatingDrawCfgReq{
  uint32 page = 1;  // 从第一页开始，1表示第一页
  uint32 page_size = 2;

  uint32 scene_id = 3;  // 场景id
  string name = 4;      // 场景名 支持模糊搜索

  uint32 sort_by = 5; // 排序字段, see SortByType
  bool is_asc = 6;    // 是否升序,默认降序
}

message BatGetDatingDrawCfgResp{
  repeated DatingGameDrawCfg draw_list = 1;
  uint32 total = 2;
}


// 通过场景id获取相亲厅牵手资源配置
message BatGetDrawCfgBySceneIdReq{
    repeated uint32 scene_id_list = 1;
    string name = 2;                    // 支持模糊搜索
}

message BatGetDrawCfgBySceneIdResp{
  repeated DatingGameDrawCfg cfg = 1;
}

enum TimeType{
    TIME_LIMIT_TYPE_UNSPECIFIED = 0;
    TIME_LIMIT_TYPE_LONG_TERM = 1;    // 长期
    TIME_LIMIT_TYPE_TIME_LIMIT = 2;   // 限时
  }

// 上架相亲厅牵手资源配置
message ApplyDatingDrawCfgReq{
  repeated uint32 scene_id = 1;  // 支持批量设置
  TimeType time_limit_type = 2;
  int64 begin_ts = 3;       // 限时套餐开始时间
  int64 end_ts = 4;         // 限时套餐结束时间

  bool use_in_all_lv = 5;   // 是否全局生效（所有等级房间生效）
  string remark = 6;        // 运营备注
}

message ApplyDatingDrawCfgResp{}

message PreCheckApplyDrawCfgReq{
  repeated uint32 scene_id = 1;  // 支持批量设置
  TimeType time_limit_type = 2;
  int64 begin_ts = 3;       // 限时套餐开始时间
  int64 end_ts = 4;         // 限时套餐结束时间

  bool use_in_all_lv = 5;   // 是否全局生效（所有等级房间生效）
  string remark = 6;        // 运营备注
}

message PreCheckApplyDrawCfgResp{
    string notify = 1;
}

// 下架相亲厅牵手资源配置
message CancelApplyDrawCfgReq{
    repeated uint32 apply_id = 1;
    string remark = 2;        // 运营备注
}

message CancelApplyDrawCfgResp{}


enum DrawState{
    DRAW_STATE_UNSPECIFIED = 0;
    DRAW_STATE_IN_USE = 1;    // 当前生效中, 按T豆价值倒序排序
    DRAW_STATE_PENDING = 2;   // 未生效, 按开始时间顺序排序
    DRAW_STATE_OFF = 3;       // 已下架, 按下架时间倒序排序
  }

message DrawApplyInfo{
    uint32 apply_id = 1;
    uint32 scene_id = 2;      // 上架后不可编辑，可下架
    int64 begin_ts = 3;       // 限时套餐开始时间
    int64 end_ts = 4;         // 限时套餐结束时间
    bool use_in_all_lv = 5;   // 是否全局生效（所有等级房间生效）

    int64 update_ts = 6;      // 更新时间
    string remarks = 7;        // 备注

    string scene_name = 8;          // 场景名
    string background_pic = 9;      // 场景背景图
    TimeType time_limit_type = 10;  // 长期/限时配置
    uint32 tbean = 11;          // 牵手值
}

// 获取相亲厅牵手资源上下架状态信息（全量返回）
message GetApplyDrawCfgStateReq{
    DrawState get_state = 1;
}

message GetApplyDrawCfgStateResp{
    repeated DrawApplyInfo apply_list = 1;
}

// 编辑已上架配置
message EditApplyDrawCfgStateReq{
    DrawApplyInfo cfg = 1;
}

message EditApplyDrawCfgStateResp{}

// =================== 相亲资源配置后台接口 end =======================

message SceneFellowInfo{
    uint32 scene_id = 1;     //场景ID
    string scene_name = 2;   //场景名称
    string nameplate = 3;    //铭牌 
    string nameplate_unique = 4;    //唯一铭牌 
    string background = 5;   //空间场景图
    uint32 scene_score = 6;  //场景牵手值
}
// 全量获取挚友相关场景信息
message GetAllSceneFellowInfoReq{}

message GetAllSceneFellowInfoResp{
    repeated SceneFellowInfo scene_list = 1;
}


message GetLocalApplyDrawCfgReq{
}

message GetLocalApplyDrawCfgResp{
    repeated DatingGameDrawCfg apply_list = 1;
}

message BatDelDrawCfgBySceneIdReq{
  repeated uint32 scene_id_list = 1;
  string secret_key = 2;
}

message BatDelDrawCfgBySceneIdResp{}

message BatchGetChannelDatingGamePhaseReq {
  repeated uint32 channel_id = 1;
}

message BatchGetChannelDatingGamePhaseResp {
  map<uint32, uint32> phase_map = 1; // key: channel_id, value: see
}