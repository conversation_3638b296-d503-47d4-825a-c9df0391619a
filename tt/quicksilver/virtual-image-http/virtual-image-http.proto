syntax = "proto3";

option go_package = "virtual-image-http";
package virtual_image_http;

// 无限换装卡 begin ///////////////////////////////////////////

/*
 * 以下所有无限换装卡接口，需要在 request url 中额外带上以下通用参数：
 * tt-client-type: string, 客户端类型，android/ios/pc
 * tt-client-version: uint32, 版本号
 * tt-market: uint32, 马甲包id
 * tt-device-id: string, 设备id
 */

enum PayChannel {
  PAY_CHANNEL_UNSPECIFIED = 0;
  PAY_CHANNEL_ALIPAY = 1;
  PAY_CHANNEL_WECHAT = 2;
  PAY_CHANNEL_APPSTORE = 3;
}

// uri: /tt-revenue-http-logic/virtual-image/card/get_card_info
// 获取卡开通信息
message GetCardInfoReq {
}

message GetCardInfoResp {
  UserProfile user_info = 1;
  CardStatus card_status = 2; // 卡状态
  string card_status_hint = 3; // 当前状态的提示文案
  repeated UserContractInfo contracts = 4; // 签约列表
  bool has_single_buy_order_waiting_redemption = 5; // 是否有单次购买的订单等待核销
  string right_video = 9; // 权益视频
}

message UserContractInfo {
  uint32 uid = 1;
  string contract_id = 2; // 签约ID
  PayChannel pay_channel = 3; // 支付渠道
  int64 next_pay_ts = 4; // 下次扣款时间戳
  int64 create_ts = 5; // 创建时间戳
}

enum CardStatus {
  CARD_STATUS_UNSPECIFIED = 0;
  CARD_STATUS_NOT_OPEN = 1; // 从未开通过
  CARD_STATUS_OPENING = 2; // 开通中
  CARD_STATUS_EXPIRE_ALERT = 3; // 即将过期
  CARD_STATUS_EXPIRED = 4; // 已过期
  CARD_STATUS_TRIAL = 5; // 体验中
}

message UserProfile {
  string account = 2; //账号
  string nickname = 3; // 昵称
  uint32 sex = 5; //用户性别
  string head_img_md5 = 7 ; //用户头像md5，如果有填该值，客户端必须用该值加上account去获取用户头像，避免获取头像回源到服务端
  string head_dy_img_md5 = 8 ; // 动态头像
}

// uri: /tt-revenue-http-logic/virtual-image/card/get_package_list
// 获取套餐列表
message GetPackageListReq {
}

message GetPackageListResp {
  repeated UserPackage package_list = 1;
}

// 套餐
message UserPackage {
  uint32 package_id = 1; // 套餐ID
  string name = 2; // 套餐名
  string desc = 3; // 描述角标
  string current_price = 4; // 现价RMB
  string original_price = 5; // 原价RMB，没有就不显示
  string discount_label = 6; // 优惠标签，如：首月/首日，没有则不显示
  string daily_price = 7; // 每日单价，该单价按每月30天计算，一位小数，向上取整
  string pay_price = 8; // 支付按钮上的价格
  string explanation = 9; // 套餐说明
  bool is_auto = 10; // 是否自动续费套餐
  repeated PayChannel pay_channel_list = 11; // 支持的支付渠道列表
  string product_id = 12; // APPSTORE 商品ID
}

// uri: /tt-revenue-http-logic/virtual-image/card/place_order
// 下单
message PlaceOrderReq {
  uint32 package_id = 1; // 套餐ID
  PayChannel pay_channel = 2; // 支付渠道
  repeated string original_transaction_ids = 3; // 苹果帐号使用，用于消除AB账号问题
  string face_auth_result_token = 4; // 人脸结果令牌，前端人脸成功后再次请求时携带
}

message PlaceOrderResp {
  // 1-7 都是货币组返回的参数，不知道客户端要用啥，就全都返回吧
  string order_no = 1; // 货币订单号
  string token = 2;
  string cli_order_no = 3; // 卡订单号
  string cli_order_title = 4; //订单标题
  string order_price = 5; //订单价格
  string tsk = 6; //加密字符串
  string channel_map = 7; //唤起支付渠道的参数, 用于安卓、IOS、前端使用
  string face_auth_context_json = 8; // 人脸上下文信息，当接口返回人脸相关错误码时使用
}

// uri: /tt-revenue-http-logic/virtual-image/card/cancel_order
// 取消订单，第3方支付取消时调用，无实际作用
message CancelOrderReq {
  string order_no = 1; // 货币订单号
}

message CancelOrderResp {
}

// uri: /tt-revenue-http-logic/virtual-image/card/get_purchase_history
// 获取购买历史
message GetPurchaseHistoryReq {
  uint32 page = 1;
  uint32 page_size = 2;
}

message GetPurchaseHistoryResp {
  repeated PurchaseRecord purchase_list = 1;
  bool has_more = 2;
}

message PurchaseRecord {
  string order_id = 1; //订单号，用于可能的去重
  string time = 2;
  string desc = 3;
  string price = 4;
  bool has_refunded = 5; // 是否已退款
}

// uri: /tt-revenue-http-logic/virtual-image/card/pay_callback
//支付回调
message PayCallbackReq {
  string order_no = 1; //支付平台订单
  string pay_channel = 2;
  string total_fee = 3;
  string order_status = 4;
  string cli_order_no = 5; //会员服务订单
  string other_order_no = 6; //支付渠道订单
  string other_status = 7 ;//第三方订单状态
  string notify_time = 8; //回调时间
  string uid = 9;//需要填收货人UID
  string buyer_id = 10; //绑定AppleID签约的UID
  string begin_time = 11; //当前支付会员开始时间，格式 2021-08-16 17:22:30（背景时区）
  string end_time = 12; //当前支付会员结束时间，格式 2021-08-16 17:22:30（背景时区）
}

// uri: /tt-revenue-http-logic/virtual-image/card/place_auto_pay_order
//自动扣款下单接口
message PlaceAutoPayOrderReq {
  uint32 super_player_uid = 1;//购买的用户UID
  string produce_id = 2; //APPSTORE 商品ID
  string contract_id = 3; //签约ID
}

// uri: /tt-revenue-http-logic/virtual-image/card/notify_contract
//签约回调
message NotifyContractReq {
  string buyer_id = 1; //绑定AppleID签约的UID
  string type = 2;
  string client_id = 3;
  string business_id = 4;
  string plan_id = 5;
  string id = 6; //会员系统订单
  string contract_id = 7; //
  string contract_channel = 8;//
  string order_no = 9;
  string product_id = 10 ; //APPStore 商品ID
  string next_pay_time = 11; //下次扣款时间，只允许APPSTROE使用
  string real_buyer_id = 12; //操作扣款的用户UID
}

// uri: /tt-revenue-http-logic/virtual-image/card/revoke_order
// 订单端外退款后回滚
message RevokeOrderReq {
  string order_id = 1; // 订单ID
  string notify_time = 2; // 通知时间
  string uid = 3; // 用户UID
}

// uri: /tt-revenue-http-logic/virtual-image/card/get_available_virtual_image_trial_card_list
// 获取可用的无限换装体验卡列表
message GetAvailableVirtualImageTrialCardListReq {
}

message GetAvailableVirtualImageTrialCardListResp {
  repeated VirtualImageTrialCard trial_card_list = 1;
}

message VirtualImageTrialCard {
  uint32 id = 1; // 功能卡配置id
  uint32 valid_time = 2; // 有效时长，单位天
  string img = 3; // 图片
  string desc = 4; // 描述
}

// uri: /tt-revenue-http-logic/virtual-image/card/use_virtual_image_trial_card
// 使用无限换装体验卡
message UseVirtualImageTrialCardReq {
  uint32 id = 1; // 功能卡配置id
}

message UseVirtualImageTrialCardResp {
}

// 无限换装卡 end ///////////////////////////////////////////
