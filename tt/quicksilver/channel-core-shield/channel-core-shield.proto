syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-core-shield";

import "ga_base.proto";

package channel_core_shield;

service ChannelCoreShield {
  // 上麦检测
  rpc HoldMicDetect(HoldMicDetectReq) returns (HoldMicDetectResp) {}

  // 抱上麦检测
  rpc TakeHoldMicDetect(TakeHoldMicDetectReq) returns (TakeHoldMicDetectResp) {}

  // 进房检测
  rpc ChannelEnterDetect(ChannelEnterDetectReq) returns (ChannelEnterDetectResp) {}
}

message DetectResult {
  bool pass = 1;          // 检测是否通过
  int32 reason_code = 2;  // 不通过原因错误码
  string reason = 3;      // 不通过原因说明
  bytes err_info = 4;     // 错误扩展信息，填充到 ga.BaseResp
}

message HoldMicDetectReq {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  uint32 channel_id = 3;
  uint32 channel_type = 4;
  uint32 mic_id = 5;
}

message HoldMicDetectResp {
  DetectResult result = 1;
}

message TakeHoldMicDetectReq {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  uint32 channel_id = 3;
  uint32 channel_type = 4;
  uint32 mic_id = 5;
}

message TakeHoldMicDetectResp {
  DetectResult result = 1;
}

message EnterChannelInfo {
  uint32 cid = 1;
  uint32 channel_type = 2;
  uint32 creator_uid = 3;   // 房主
  uint32 online_size = 4;   // 房间在线人数
  string name = 5;          // 房间名
}

message EnterUserInfo {
  uint32 uid = 1;
  uint32 user_type = 2;
}

message ChannelSchemeInfo {
  uint32 scheme_id = 1;
  uint32 scheme_detail_type = 2;
}

message ChannelEnterDetectReq {
  EnterUserInfo user = 1;
  EnterChannelInfo channel = 2;
  ChannelSchemeInfo cur_scheme_info = 3;  // 房间当前玩法信息
  uint32 enter_source = 4;
  ga.BaseReq base_req = 5;
}

message ChannelEnterDetectResp {
  DetectResult result = 1;
}
