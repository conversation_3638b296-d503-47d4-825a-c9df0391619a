syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-play-index";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package channel_play_index;

service ChannelPlayIndex {
    rpc GetTodayUserPlayInfo(GetTodayUserPlayInfoReq) returns (GetTodayUserPlayInfoResp) {}
    rpc CheckUserInRoom(CheckUserInRoomReq) returns (CheckUserInRoomResp) {}
    rpc IsFinishTask(IsFinishTaskReq) returns (IsFinishTaskResp) {}
    rpc BatchGetDaysContentNum(BatchGetDaysContentNumReq) returns (BatchGetDaysContentNumResp) {}
    rpc GetTodayDistrictPlayInfo(GetTodayDistrictPlayInfoReq) returns (GetTodayDistrictPlayInfoResp) {}
    rpc CheckUserTodaySendMsg(CheckUserTodaySendMsgReq) returns (CheckUserTodaySendMsgResp) {}
    rpc GetUserTodayChatUids(GetUserTodayChatUidsReq) returns (GetUserTodayChatUidsResp) {}
    rpc IsUserTodayAddNewFriend(IsUserTodayAddNewFriendReq) returns (IsUserTodayAddNewFriendResp) {}
    rpc GetTabsRoomCount(GetTabsRoomCountReq) returns (GetTabsRoomCountResp) {}
    rpc SetPublishTabCntByOneDay(SetPublishTabCntByOneDayReq) returns (SetPublishTabCntByOneDayResp) {}
    rpc GetPublishTabCntList(GetPublishTabCntListReq) returns (GetPublishTabCntListResp) {}

    rpc GetUserDayRoomDuration(GetUserDayRoomDurationReq) returns (GetUserDayRoomDurationResp) {}
    rpc GetUserDayOnlineDuration(GetUserDayOnlineDurationReq) returns (GetUserDayOnlineDurationResp) {}
    rpc IsTodayHasFollowOrBeFollow(IsTodayHasFollowOrBeFollowReq) returns (IsTodayHasFollowOrBeFollowResp) {}

    rpc GetRoomUids(GetRoomUidsRequest)returns(GetRoomUidsResponse){}

}


enum UserPlayType {
  USER_PLAY_TYPE_UNSPECIFIED = 0;
  // 房间次数
  USER_PLAY_TYPE_ROOM_CNT = 1;
  // 房间时长
  USER_PLAY_TYPE_ROOM_DURATION = 2;
}


message GetTodayUserPlayInfoReq {
    uint32 uid = 1;
    repeated UserPlayType user_play_types = 2;
    uint32 cid = 3;
}

message GetTodayUserPlayInfoResp {
    uint32 user_in_room_cnt = 1;
    uint32 user_in_room_duration = 2;
    uint32 tab_id = 3;
}

message UserInRoomInfo {
    uint32 channel_id = 1;
    int64 enter_room_ts = 2;
    int64 exit_room_ts = 3;
}


message CheckUserInRoomReq {
    uint32 uid = 1;
    repeated uint32 check_uids = 2;
}

message ExitRoomInfo {
    uint32 channel_id = 1;
    int64 exit_room_ms = 2;
    uint32 exit_uid = 3;
    uint32 tab_id = 4;
    uint32 same_room_uid = 5;
    int64 together_time = 6;
    int64 enter_room_ms = 7;
}

message CheckUserInRoomResp {
    map<string, ExitRoomInfo> exit_room_info_map = 1;
}

enum TaskEnum {
  TASK_ENUM_INVALID = 0 ;
  TASK_ENUM_ZONE_GUIDE = 1; // 专区引导进房时长任务
}
message IsFinishTaskReq {
   uint32 uid = 1;
   TaskEnum task_type = 2;
}

message IsFinishTaskResp {
   bool finish = 1;
}

message BatchGetDaysContentNumReq {
   repeated uint32 tab_ids = 1;
}

message BatchGetDaysContentNumResp {
  map <uint32, uint32> content_num_map = 1;
}

message GetTodayDistrictPlayInfoReq {
    uint32 uid = 1;
    uint32 tab_id = 2;
}

message GetTodayDistrictPlayInfoResp {
    uint32 user_in_game_district_duration = 1;
}

message CheckUserTodaySendMsgReq {
    uint32 uid = 1;
    repeated uint32 check_uids = 2;
}

message CheckUserTodaySendMsgResp {
    repeated uint32 exist_uids = 1;
}

message GetUserTodayChatUidsReq {
    uint32 uid = 1;
}

message GetUserTodayChatUidsResp {
    repeated uint32 chat_uids = 1;
}

message IsUserTodayAddNewFriendReq {
    uint32 uid = 1;
}

message IsUserTodayAddNewFriendResp {
    bool is_add_new_friend = 1;
}

message GetTabsRoomCountReq {
    repeated uint32 tab_ids = 1;
}

message GetTabsRoomCountResp {
    map<uint32, int64> count_map = 1;
}

enum PublishRecordType {
  CATE_TYPE_UNSPECIFIED = 0;
  // 一起开黑
  CATE_TYPE_KAIHEI = 1;
}

message SetPublishTabCntByOneDayReq {
    uint32 tab_id = 1;
    uint32 uid = 2;
    int64 publish_time = 3;
    PublishRecordType publish_type = 4;
}

message SetPublishTabCntByOneDayResp {
}

message GetPublishTabCntListReq {
    int64 query_time = 1;
    uint32 limit_cnt = 2;
    PublishRecordType publish_type = 3;
}

message GetPublishTabCntListResp {
    repeated uint32 tab_ids = 1;
}

message GetUserDayRoomDurationReq {
    uint32 uid = 1;
}

message GetUserDayRoomDurationResp {
    int64 room_duration = 1;
}

message GetUserDayOnlineDurationReq {
    uint32 uid = 1;
}

message GetUserDayOnlineDurationResp {
    int64 online_duration = 1;
}

message IsTodayHasFollowOrBeFollowReq {
    uint32 uid = 1;
}

message IsTodayHasFollowOrBeFollowResp {
    bool follow_status = 1;
}

message GetRoomUidsRequest{
    uint32 channel_id = 1;
}

message GetRoomUidsResponse{
  repeated uint32 uids = 1;
}