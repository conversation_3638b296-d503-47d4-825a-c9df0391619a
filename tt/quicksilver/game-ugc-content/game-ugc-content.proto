syntax = "proto3";

package game_ugc_content;
option go_package = "golang.52tt.com/protocol/services/game-ugc-content";

service GameUgcContent {
  // 运营后台--
  // 创建/更新话题
  rpc UpsertTopic(UpsertTopicReq) returns (UpsertTopicResp) {}
  // 获取话题列表
  rpc GetTopicList(GetTopicListReq) returns (GetTopicListResp) {}
  // 删除话题
  rpc DelTopic(DelTopicReq) returns (DelTopicResp) {}
  // 批量获取话题
  rpc BatGetTopics(BatGetTopicsReq) returns (BatGetTopicsResp) {}

//下面几个是动态相关详情
  // 创建/更新动态tab配置
  rpc UpsertConfigTab(UpsertConfigTabReq) returns (UpsertConfigTabResp) {}
  // 获取动态tab配置列表
  rpc GetConfigTabs(GetConfigTabsReq) returns (GetConfigTabsResp) {}
  // 删除动态tab配置
  rpc DelConfigTab(DelConfigTabReq) returns (DelConfigTabResp) {}

//下面几个是入口
  // 为玩法创建默认的开黑房和搭子卡tab，在创建玩法时调用
  rpc InsertDefaultConfigTabs(InsertDefaultConfigTabsReq) returns (InsertDefaultConfigTabsResp) {}
  // 更新玩法绑定的动态tab配置
  rpc SaveGameConfigTab(SaveGameConfigTabReq) returns (SaveGameConfigTabResp) {}
  // 获取玩法绑定的动态tab配置列表
  rpc GetGameConfigTabList(GetGameConfigTabListReq) returns (GetGameConfigTabListResp) {}
  // 删除玩法绑定的动态tab配置
  rpc DelGameConfigTab(DelGameConfigTabReq) returns (DelGameConfigTabResp) {}
  // 更新房间tab和搭子卡tab的名称的基本信息
  rpc BatchUpdateConfigTabsBaseInfo(BatchUpdateConfigTabsBaseInfoReq) returns (BatchUpdateConfigTabsBaseInfoResp) {}
  // 创建/更新configTab入口配置
  rpc UpsertConfigTabEntry(UpsertConfigTabEntryReq) returns (UpsertConfigTabEntryResp) {}

  // 创建/更新禁止用户发帖/搭子卡配置
  rpc UpsertBanUserPostConfig(UpsertBanUserPostConfigReq) returns (UpsertBanUserPostConfigResp) {}
  // 获取禁止用户发帖/搭子卡配置列表
  rpc GetBanUserPostConfigList(GetBanUserPostConfigListReq) returns (GetBanUserPostConfigListResp) {}
  // 删除禁止用户发帖/搭子卡配置
  rpc DelBanUserPostConfig(DelBanUserPostConfigReq) returns (DelBanUserPostConfigResp) {}
  // 批量添加禁止用户使用搭子卡功能/屏蔽搭子卡配置
  rpc BatchAddBanUserPostConfig(BatchAddBanUserPostConfigReq) returns (BatchAddBanUserPostConfigResp) {}
  // --运营后台end

  // 根据tab_id获取玩法绑定的动态tab配置(查缓存)
  rpc GetGameConfigTabDetailByTabId(GetGameConfigTabDetailByTabIdReq) returns (GetGameConfigTabDetailByTabIdResp) {}
  rpc BatGetGameConfigTabDetailByTabId(BatGetGameConfigTabDetailByTabIdReq) returns (BatGetGameConfigTabDetailByTabIdResp) {}

  // 根据config_tab_id获取对应配置信息(查缓存)
  rpc GetConfigTabInfoById(GetConfigTabInfoByIdReq) returns (GetConfigTabInfoByIdResp) {}
  // 查询某种类型的config_tab是否需要展示(查缓存)
  rpc IsConfigTabVisible(IsConfigTabVisibleReq) returns (IsConfigTabVisibleResp) {}
  // 根据类型获取玩法对应配置(查缓存)
  rpc GetConfigTabsMapByType(GetConfigTabsMapByTypeReq) returns (GetConfigTabsMapByTypeResp) {}
  // 查询是否有用户禁止发帖配置(查缓存)
  rpc GetActiveBanUserPostConfigWithCache(GetActiveBanUserPostConfigWithCacheReq) returns (GetActiveBanUserPostConfigWithCacheResp) {}
  // 查询所有玩法配置展示中的搭子卡配置(查缓存)
  rpc GetGamePalTabs(GetGamePalTabsReq) returns (GetGamePalTabsResp) {}
  // 批量获取帖子展示信息
  rpc BatchGetGameFeedPbs(BatchGetGameFeedPbsReq) returns (BatchGetGameFeedPbsResp) {}

  // 批量获取仅专区可见帖子列表
  rpc GetGameOnlyDistractPostList(GetGameOnlyDistractPostListReq) returns (GetGameOnlyDistractPostListResp) {}

  rpc GetBanGameHallUser(GetBanGameHallUserReq) returns (GetBanGameHallUserResp) {}
  rpc UpdateBanGameHallGetStatus(UpdateBanGameHallGetStatusReq) returns (UpdateBanGameHallGetStatusResp) {}
}

// 话题相关
message Topic {
  string topic_id = 1;    // 话题id
  string topic_name = 2;  // 话题名称
  uint32 post_count = 3;  // 动态总数
  int64 create_at = 4;  // 创建时间
  string display_topic_name = 5; //话题外显名称,用于搜索
  bool is_display = 6; // 是否外显,用于搜索
  string display_district_name = 7; //外显专区名称
}

message UpsertTopicReq {
  string topic_id = 1;  // 话题id
  string topic_name = 2;  // 话题名称
  string display_topic_name = 3; //话题外显名称
  bool is_display = 4; // 是否外显
  string display_district_name = 5; //外显专区名称
}

message UpsertTopicResp {
}

message GetTopicListReq {
  uint32 page = 1;    // 页数
  uint32 size = 2;    // 页面大小
  bool need_count = 3;  // 是否需要返回配置总数
  string topic_id = 4;  // 根据topic_id筛选配置
  string topic_name = 5;  // 根据topic_name筛选配置
  string display_topic_name = 6; //根据外显名称筛选
  string display_district_name = 7; //外显专区名称
}

message GetTopicListResp {
  repeated Topic topic_list = 1;  // 话题列表
  uint32 total = 2;
}

message DelTopicReq {
  string topic_id = 1;
}

message DelTopicResp {
}

// 创建动态tab和绑定话题相关
// 推送人群
enum CrowdType {
  CrowdTypeNone = 0;
  CrowdTypeAll = 1;     // 全部用户
  CrowdTypeGroup = 2;   // 根据人群包 id
}

// 区分不同类型的tab
enum ConfigTabType {
  ConfigTabType_None = 0;
  ConfigTabType_ChannelList = 1;  // 房间tab
  ConfigTabType_PostTab = 2;  // 动态tab
  ConfigTabType_ActivityPost = 3; // 活动海报tab
  ConfigTabType_ActivitySet = 4; // 活动中心tab
  ConfigTabType_GamePalCard = 5; // 搭子卡tab
  ConfigTabType_GroupChat = 6;   // 群聊tab
  ConfigTabType_GameHall = 7;   // 组队大厅tab
}

enum TabSubType {
  GAME = 0; //游戏分类
  COMPREHENSIVE_CHANNEL = 1;  //综合频道分类
}

enum ConfigTabDisplayStrategy {
  ConfigTabDisplayStrategy_ABTest = 0; // 根据ab实验控制是否展示
  ConfigTabDisplayStrategy_AllUser = 1; // 所有用户都展示
}

message BindTopic {
  string topic_id = 1;  // 话题id
  bool is_district_display = 2;  // 专区是否展示，和玩法相关
  bool is_default = 3;  // 是否默认话题
}

// 动态tab配置，这个用于帖子详情，而入口在ConfigTabEntry
message ConfigTab {
  string config_tab_id = 1;    // 动态tab_id
  string config_tab_name = 2;  // 动态tab名称
  CrowdType crowd_type = 3;    // 推送人群
  string crowd_group_id = 4;   // 人群包id
  Topic default_topic = 5;  // 默认话题
                                               repeated Topic other_topics = 6;    // 其他话题
  repeated string titles = 7;   // 关联的标题列表
  uint32 tab_id = 8;  // 绑定的玩法id
  ConfigTabType config_tab_type = 9;  // tab类型
  string crowd_group_name = 10; // 人群包名称
  uint32 crowd_group_user_count = 11; // 人群包人数
  repeated RecRule rec_rules = 12;
  uint32 force_rec_rule = 13; // 强制定位推荐规则， 为空代表没有开启
  TabSubType tab_sub_type = 14; // 游戏分类下玩法分类，区分普通玩法和综合频道
  bool is_hide = 15;  // 是否隐藏搭子卡tab
  PostTeleport post_teleport = 16; // 动态帖子小尾巴短链
  ConfigTabDisplayStrategy display_strategy = 17; // tab展示策略
  repeated BindTopic bind_topic_infos = 18; // 绑定话题信息
}

message RecRule {
  uint32 rec_rules = 1; // 1-默认热门推荐流规则，2-最新流推荐规则
  string name = 2;
}

message PostTeleport {
  string text = 1;  // 动态小尾巴文案
  string url = 2; // 动态小尾巴链接
}

// 接口请求来源
enum RequestSource {
  RequestSource_None = 0;
  RequestSource_ConfigTab = 1;  // 动态tab配置页面请求, 更新动态tab基本信息
  RequestSource_Title = 2;  // 动态tab标题配置页面请求, 只更新标题
  RequestSource_ActivityTab = 3;  // 【综合频道】活动tab配置页面请求, 更新综合频道相关tab信息
}

message UpsertConfigTabReq {
  ConfigTab config = 1;
  RequestSource source = 2; // 请求来源
}

message UpsertConfigTabResp {
}

message GetConfigTabsReq {
  uint32 page = 1;    // 页数
  uint32 size = 2;    // 页面大小
  bool need_count = 3;  // 是否需要返回配置总数
  string config_tab_id = 4;  // 根据config_tab_id筛选动态tab配置
  string config_tab_name = 5;  // 根据config_tab_name筛选动态tab配置
  string topic_id = 6;  // 根据topic_id筛选跟该话题绑定的动态tab配置
  uint32 tab_id = 7;  // 根据tab_id筛选与该玩法绑定的动态tab配置
  RequestSource source = 8; // 请求来源
  string teleport_url = 9; // 根据小尾巴短链链接筛选配置
}

message AllConfigTab {
  ConfigTab config_tab = 1;
  repeated Topic topics = 2; // 话题信息
}

message GetConfigTabsResp {
  repeated AllConfigTab all_config_tabs = 1;
  uint32 total = 2;
}

message DelConfigTabReq {
  string id = 1;
}

message DelConfigTabResp {
}

message AllGameConfigTab {
  ConfigTabEntry config_tab_entry = 1;
  repeated Topic topics = 2; // 话题信息
  repeated BindTopic bind_topics = 3; // 绑定话题信息
}

//sort
message GameConfigTab {
  uint32 tab_id = 1;  // 玩法id
  repeated AllGameConfigTab all_game_config_tabs = 2; // 子tab配置列表
  TabSubType tab_sub_type = 3;  // 游戏分类下玩法分类，区分普通玩法和综合频道两种类型的tab_id
}

message SaveGameConfigTabReq {
  uint32 tab_id = 1;  // 玩法id
  repeated ConfigTabEntry config_tab_entrys = 2;
  TabSubType tab_sub_type = 3;  // 游戏分类下玩法分类，区分普通玩法和综合频道两种类型的tab_id
}

message SaveGameConfigTabResp {
}

message GetGameConfigTabListReq {
  uint32 tab_id = 1;  // 根据tab_id筛选配置
  TabSubType tab_sub_type = 2;  // 游戏分类下玩法分类，区分普通玩法和综合频道两种类型的tab_id
}

message GetGameConfigTabListResp {
  repeated GameConfigTab game_config_tab_list = 1;
}

message DelGameConfigTabReq {
  uint32 tab_id = 1;
  TabSubType tab_sub_type = 2;  // 游戏分类下玩法分类，区分普通玩法和综合频道两种类型的tab_id
}

message DelGameConfigTabResp {
}

message GameConfigTabDetail {
  uint32 tab_id = 1;
  repeated ConfigTabInfo config_tabs = 2;
}

message GetGameConfigTabDetailByTabIdReq {
  uint32 tab_id = 1;
  TabSubType tab_sub_type = 2;  // 游戏分类下玩法分类，区分普通玩法和综合频道两种类型的tab_id
}

message GetGameConfigTabDetailByTabIdResp {
  GameConfigTabDetail config = 1;
}

message BatGetGameConfigTabDetailByTabIdReq {
  repeated uint32 tab_ids = 1;
}

message BatGetGameConfigTabDetailByTabIdResp {
  repeated GameConfigTabDetail config = 1;
}

message ConfigTabInfo {
  string config_tab_id = 1; // 动态tab_id
  string config_tab_name = 2; // 动态tab名称
  CrowdType crowd_type = 3;    // 推送人群
  string crowd_group_id = 4;   // 人群包id
  string default_topic_id = 5;   // 默认话题
  repeated string other_topic_ids = 6;  // 其他话题
  repeated string titles = 7;   // 标题列表
  ConfigTabType config_tab_type = 8;  // tab类型
  repeated RecRule rec_rules = 9; // 1-默认热门推荐流规则，2-最新流推荐规则
  uint32 force_rec_rule = 10; // 强制定位推荐规则， 0代表没有开启
  PostTeleport post_teleport = 11; // 动态帖子小尾巴短链
  ConfigTabDisplayStrategy display_strategy = 12; // tab展示策略
  repeated BindTopic bind_topics = 13; // 绑定话题信息
  map<string, Topic> topic_info_map = 14; // 话题信息
}

message GetConfigTabInfoByIdReq {
  string config_tab_id = 1;
}

message GetConfigTabInfoByIdResp {
  ConfigTabInfo config_tab_info = 1;
}

// 对应开黑专区发帖上报的业务数据
message GamePostBussInfo {
  uint32 tab_id = 1; // 主题玩法id
  string config_tab_id = 2; // 开黑专区动态配置tab id
  string title = 3; // 专区帖子标题
}

message GetGamePalTabsReq {
}

message GetGamePalTabsResp {
  map<uint32, ConfigTabInfo> game_pal_tabs = 1; // 玩法对应的搭子卡tab，只返回展示状态的玩法搭子卡，key:tab_id
}

// 根据类型和玩法查询对应配置，只返回需要展示的配置
message IsConfigTabVisibleReq {
  ConfigTabType config_tab_type = 1;  // 类型
  uint32 tab_id = 2; // 玩法
}

message IsConfigTabVisibleResp {
  bool is_visible = 1;
}

// 根据类型获取玩法对应配置
message GetConfigTabsMapByTypeReq {
  ConfigTabType config_tab_type = 1;  // 类型
}

message GetConfigTabsMapByTypeResp {
  map<uint32, ConfigTabInfo> config_tabs_map = 1; // key:tab_id
}

// 禁止发帖配置类型
enum BanConfigType {
  BanConfigTypeNone = 0;
  BanConfigTypeUser = 1; // 禁止单个用户发帖
  BanConfigTypeCrowdGroup = 2; // 禁止某个人群包用户发帖
}

// 配置生效状态
enum Status {
  StatusNone = 0;
  StatusActive = 1; // 生效中
  StatusInEffective = 2; // 未生效
  StatusExpired = 3; // 已过期
}

// 配置类型
enum BanPostType {
  BanPostTypePost = 0;  // 禁止专区发帖配置
  BanPostTypeGamePalCard = 1; // 限制使用游戏搭子功能配置
  BanPostTypeHideGamePalCard = 2; // 屏蔽用户发布的搭子卡配置
  BanGameHallSay = 3; // 限制组队大厅发言
}

message MultiUserInfo {
  string ttid = 1;  // 用户ttid
  uint32 uid = 2;  // 用户uid
  string user_nickname = 3; // 用户昵称
}

message BanUserPostConfig {
  string id = 1;
  BanConfigType config_type = 2;  // 区分禁止用户或人群包在专区发帖
  string ttid = 3;                // 用户ttid
  string crowd_group_id = 4;      // 人群包id
  repeated uint32 tab_ids = 5;    // 禁止发帖的玩法id列表
  int64 start_time = 6;   // 禁止发帖开始时间
  int64 end_time = 7;     // 禁止发帖结束时间
  string ban_reason = 8;  // 禁止原因
  int64 operate_time = 9; // 操作时间
  string operator = 10;   // 操作者
  Status status = 11;     // 配置生效状态
  string crowd_group_name = 12; // 人群包名称
  uint32 crowd_group_user_count = 13; // 人群包人数
  string user_nickname = 14;  // 用户昵称
  BanPostType ban_post_type = 15; // 区分专区禁止发帖配置和禁止发布搭子卡配置
  uint32 uid = 16; // 用户uid
  string warning_message = 17; // 助手推送警告文案
  repeated string ttids = 18;                // 用户ttids
  repeated uint32 uids = 19;                // 用户uids
  map<string, MultiUserInfo> user_info_map = 20; // 用户信息
}

message BatchAddBanUserPostConfigReq {
  repeated string ttids = 1;// 用户ttid
  int64 start_time = 2;     // 禁止使用开始时间
  int64 end_time = 3;       // 禁止使用结束时间
  string ban_reason = 4;    // 禁止原因
  string warning_message = 5; // 助手推送警告文案
  repeated BanPostType ban_post_types = 6; // 配置类型，每种类型单独创建一条配置
  string operator = 7;    // 操作者
}

message BatchAddBanUserPostConfigResp {
}

message UpsertBanUserPostConfigReq {
  BanUserPostConfig config = 1;
}

message UpsertBanUserPostConfigResp {
}

message GetBanUserPostConfigListReq {
  uint32 page = 1;     // 页数
  uint32 size = 2;     // 页面大小
  bool need_count = 3; // 是否需要返回total
  string ttid = 4;     // 根据ttid筛选
  Status status = 5;   // 根据配置生效状态筛选
  repeated BanPostType ban_post_types = 6;  // 根据类型筛选配置
}

message GetBanUserPostConfigListResp {
  repeated BanUserPostConfig configs = 1;
  uint32 total = 2;
}

message DelBanUserPostConfigReq {
  string id = 1;
}

message DelBanUserPostConfigResp {
}

message ActiveBanUserPostConfig {
  BanConfigType config_type = 1;  // 禁止发帖配置类型
  uint32 uid = 2;                 // 用户uid
  string crowd_group_id = 3;      // 人群包id
  uint32 tab_id = 4;              // 禁止发帖的玩法id
  string ban_reason = 5;          // 禁止原因
}

message GetActiveBanUserPostConfigWithCacheReq {
  uint32 tab_id = 1;  // 玩法id
  uint32 uid = 2;     // 用户uid
  BanPostType ban_post_type = 3;  // 区分专区禁止发帖配置和禁止发布搭子卡配置
}

message GetActiveBanUserPostConfigWithCacheResp {
  repeated ActiveBanUserPostConfig configs = 1;
}

message ConfigTabEntry {
  string config_tab_id = 1;    // config_tab_id
  string config_tab_name = 2;  // configtab名称
  uint32 tab_id = 3;  // 绑定的玩法id
  TabSubType tab_sub_type = 4; // 游戏分类下玩法分类，区分普通玩法和综合频道
  ConfigTabType config_tab_type = 5;  // tab类型
  bool is_hide = 6;  // 是否隐藏tab
  ConfigTabDisplayStrategy display_strategy = 7; // tab展示策略
}

message UpsertConfigTabEntryReq {
  ConfigTabEntry config_tab_entry = 1;
}

message UpsertConfigTabEntryResp {
}

message BatchUpdateConfigTabsBaseInfoReq {
  repeated ConfigTabEntry update_config_tabs = 1;
}

message BatchUpdateConfigTabsBaseInfoResp {
}

message InsertDefaultConfigTabsReq {
  uint32 tab_id = 1;
}

message InsertDefaultConfigTabsResp {
}

enum ReqSource {
  INVALID_SOURCE = 0;
  SEARCH_SOURCE = 1; //搜索
  GAME_RCMD_SOURCE = 2; //游戏专区推荐流
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchGetGameFeedPbsReq{
  repeated string post_id = 1; //帖子id列表
  uint32 req_source = 2; //请求来源
  uint32 req_uid = 3;
  uint32 marketId = 4;
  uint32 clientType = 5;
  bool need_show_button = 6; //是否需要展示按钮
  repeated uint32 followed_uid_list = 7; //关注的用户uid列表
  map<uint32, bool> is_black_user_map = 8; //黑产帖主
  map<uint32, string> user_remark_name_map = 9; //用户备注名
}

enum UnmarshalType {
  UNMARSHAL_TYPE_DEFAULT = 0;
  UNMARSHAL_TYPE_PROTOBUF = 1; //protobuf
  UNMARSHAL_TYPE_JSON = 2; //json
}


message GameFeedContent {
  uint32 unmarshal_type = 1;  //UnmarshalType
  bytes content = 2; //字节流数据，按照unmarshal_type去解析
}

message BatchGetGameFeedPbsResp{
  repeated GameFeedContent game_feed_content = 1;
}

message GetGameOnlyDistractPostListReq{
  uint32 uid = 1;
  int64 last_post_create_time = 2;
  uint32 limit = 3;
}

message GetGameOnlyDistractPostListResp{
  repeated string post_ids = 1;
}

message GetBanGameHallUserReq {
}

message GetBanGameHallUserResp {
  repeated BanUserPostConfig configs = 1;
}

message UpdateBanGameHallGetStatusReq {
  repeated string ids = 1;
}

message UpdateBanGameHallGetStatusResp {
}

message BatGetTopicsReq {
  repeated string topic_ids = 1;
}

message BatGetTopicsResp {
  repeated Topic topic_list = 1;
}