syntax = "proto3";

//import "tt/quicksilver/ga_base.proto";

option go_package = "golang.52tt.com/protocol/services/anchor-newbie-guide";
package anchor_newbie_guide;

service AnchorNewbieGuideService {
  // 获取新手指南信息
  rpc GetNewbieAnchorGuideInfo(GetNewbieAnchorGuideInfoReq) returns (GetNewbieAnchorGuideInfoResp) {}
  // 获取新手指南泡泡信息
  rpc GetNewbieGuidePopInfo(GetNewbieGuidePopInfoReq) returns (GetNewbieGuidePopInfoResp) {}
  // 获取新手引导挂件信息
  rpc GetNewbieWidgetInfo(GetNewbieWidgetInfoReq) returns (GetNewbieWidgetInfoResp) {}
  // 获取新手任务
  rpc GetNewbieAnchorTask(GetNewbieAnchorTaskReq) returns (GetNewbieAnchorTaskResp) {}
  // 领取新手任务奖励
  rpc ClaimNewbieAnchorTaskReward(ClaimNewbieAnchorTaskRewardReq) returns (ClaimNewbieAnchorTaskRewardResp) {}
  // 领取新手进阶任务奖励
  rpc ClaimNewbieAnchorTaskAdvancedReward(ClaimNewbieAnchorTaskAdvancedRewardReq) returns (ClaimNewbieAnchorTaskRewardResp) {}
  // 获取新手任务奖励列表
  rpc GetNewbieAnchorTaskRewardList(GetNewbieAnchorTaskRewardListReq) returns (GetNewbieAnchorTaskRewardListResp) {}
  // 获取直播间结束引导弹窗信息
  rpc GetChannelLiveEndGuidePopup(GetChannelLiveEndGuidePopupRequest) returns (GetChannelLiveEndGuidePopupResponse) {}
  // 发送直播间感谢互动
  rpc SendChannelLiveThanks(SendChannelLiveThanksRequest) returns (SendChannelLiveThanksResponse) {}

  // 测试接口 重置新手任务
  rpc TestResetNewbieAnchorTask(TestResetNewbieAnchorTaskReq) returns (TestResetNewbieAnchorTaskResp) {}
  // 测试接口 完成任务
  rpc TestCompleteNewbieAnchorTask(TestCompleteNewbieAnchorTaskReq) returns (TestCompleteNewbieAnchorTaskResp) {}
  // 测试接口 手动触发计划任务
  rpc TestTriggerTimer(TestTriggerTimerReq) returns (TestTriggerTimerResp) {}
}

// 获取新手指南信息
message GetNewbieAnchorGuideInfoReq {
  uint32 identity_type = 1; // 身份 see SIGN_ANCHOR_IDENTITY
  uint32 uid = 2; // 用户id
  bool   is_advance = 3; // 是否是进阶TAB
}
message GetNewbieAnchorGuideInfoResp {
  repeated NewbieGuideTheme theme_list = 1; // 主题列表
}

enum KnowledgeTaskStatus {
  KnowledgeTaskStatus_Invalid = 0; // 无效状态
  KnowledgeTaskStatus_NotFinish = 1; // 未完成
  KnowledgeTaskStatus_Finished = 2; // 已完成
}
// 知识点任务
message KnowledgeTask {
  string task_id = 1; // 任务id
  string task_name = 2; // 任务名称
  string jump_url = 3; // 跳转链接
  uint32 status = 4; // 任务状态 see KnowledgeTaskStatus
}
message NewbieGuideContent {
  string title = 1; // 标题
  string content = 2; // 内容
  repeated string urls = 3; // 配图链接
}

// 子主题信息
message NewbieGuideSubTheme {
  string theme_name = 1; // 子主题名称
  repeated NewbieGuideContent content_list = 2; // 内容列表
  repeated KnowledgeTask task_list = 3; // 知识点任务列表
}

// 主题信息
message NewbieGuideTheme {
  string theme_name_url = 1; // 主题名称URL
  repeated NewbieGuideSubTheme sub_theme_list = 2; // 子主题列表(数量为1时，没有子主题TAB)
  uint32 identity_type = 3; // 身份 see SIGN_ANCHOR_IDENTITY
}

message GetNewbieGuidePopInfoReq {
  uint32 uid = 1; // 用户id
  uint32 sign_day = 2; // 签约天数
  map<uint32, uint32> identity_obtain_days = 4; // 身份获取天数映射，key为身份类型，value为获取天数
}
message GetNewbieGuidePopInfoResp {
  string pop_txt = 1;
  uint32 pop_type = 2; // 弹窗
}

enum WidgetType {
  WIDGET_TYPE_INVALID_UNSPECIFIED = 0; // 无效
  WIDGET_TYPE_NEWBIE_ANCHOR_LIVE_READY = 1; // 听听开播准备页
  WIDGET_TYPE_NEWBIE_ANCHOR_LIVE_ROOM  = 2; // 房间
}
message GetNewbieWidgetInfoReq {
  uint32 uid = 1; // 用户id
  uint32 identity_type = 2; // 新手身份类型 see SIGN_ANCHOR_IDENTITY
  uint32 widget_type = 3; // see WidgetType
  uint32 obtain_day  = 4; // 新手身份获取天数
}

enum WidgetStatus {
  WIDGET_STATUS_INVALID_UNSPECIFIED = 0; // 无效
  WIDGET_STATUS_NORMAL = 1; // 普通转态，无任务无奖励
  WIDGET_STATUS_HAS_TASK = 2; // 有任务，无奖励
  WIDGET_STATUS_HAS_REWARD = 3; //  有奖励
}
message GetNewbieWidgetInfoResp {
  string title = 3; // 标题
  string task_progress = 4; // 任务进度
  uint32 status = 5; // 任务状态 see WidgetStatus
  string pop_txt = 6; // 引导泡泡文本
  uint32 pop_type = 7; // 引导泡泡类型
}

// 任务项
// 新手任务项
message NewbieAnchorTaskItem {
  string task_id = 1;          // 任务id
  string task_string = 2;      // 任务名称
  string task_icon = 3;        // 任务icon
  bool   is_completed = 4;     // 是否完成
  uint32 progress = 5;         // 进度N
  uint32 total = 6;            // 完成值M (N/M)
}

// 新手任务类型
enum NewbieAnchorTaskType {
  NEWBIE_ANCHOR_TASK_TYPE_UNSPECIFIED = 0;
  NEWBIE_ANCHOR_TASK_TYPE_BEGINNER = 1; // 入门任务
  NEWBIE_ANCHOR_TASK_TYPE_ADVANCED = 2; // 进阶任务
}

// 新手任务奖励状态
enum NewbieRewardStatus {
  NEWBIE_REWARD_STATUS_UNSPECIFIED = 0; // 未知状态
  NEWBIE_REWARD_STATUS_NOT_CLAIMED = 1; // 未领取
  NEWBIE_REWARD_STATUS_CLAIMED = 2; // 已领取
  NEWBIE_REWARD_STATUS_EXPIRED = 3; // 已过期
}

// 新手任务标识
enum NewbieAnchorTaskSign {
  NEWBIE_ANCHOR_TASK_SIGN_UNSPECIFIED = 0; // 未知状态
  NEWBIE_ANCHOR_TASK_SIGN_ONLINE = 1; // 在线时长
  NEWBIE_ANCHOR_TASK_SIGN_HOLD_MIC = 2; // 接档时长
  NEWBIE_ANCHOR_TASK_SIGN_VALID_DAYS = 3; // 有效接档天数
  NEWBIE_ANCHOR_TASK_SIGN_LIVE_TIME = 4; // 开播时长
  NEWBIE_ANCHOR_TASK_SIGN_NEW_FANS = 5; // 新增粉丝团人数
  NEWBIE_ANCHOR_TASK_SIGN_PK = 6; // PK次数
  NEWBIE_ANCHOR_TASK_SIGN_HELLO = 7; // 打招呼人数
  NEWBIE_ANCHOR_TASK_SIGN_PRESENT = 8; // 礼物收入
  NEWBIE_ANCHOR_TASK_SIGN_ACTIVE_DAYS = 9; // 累计活跃天
}

// 新手任务初始化方式
enum NewbieAnchorTaskInitType {
  NEWBIE_ANCHOR_TASK_INIT_TYPE_UNSPECIFIED = 0; // 未知状态
  NEWBIE_ANCHOR_TASK_INIT_TYPE_AUTO = 1; // 自动初始化
  NEWBIE_ANCHOR_TASK_INIT_TYPE_LIVE = 2; // 开播初始化
  NEWBIE_ANCHOR_TASK_INIT_TYPE_MIC = 3; // 上麦初始化
}

// 获取新手任务
message GetNewbieAnchorTaskReq {
  uint32 identity_type = 1; // 身份类型
  uint32 uid = 2; // 用户id
}

// 新手任务日期
message NewbieAnchorTaskDay {
  enum Status {
    STATUS_UNSPECIFIED = 0;
    STATUS_NOT_COMPLETED = 1; // 未完成
    STATUS_COMPLETED = 2; // 已完成
    STATUS_EXPIRED = 3; // 已过期
  }
  uint64 day = 1;                               // 日期天
  Status complete_status = 2;                   // 完成状态
  repeated NewbieAnchorTaskItem task_list = 3;  // 任务列表
}

// 获取新手入门任务
message NewbieAnchorTaskBeginner {
  repeated NewbieAnchorTaskDay days = 1;           // 任务日期列表
  uint32 completed_days = 2;                       // 实际累计完成天数
  message Reward {
    uint32 condition_days = 1;                     // 累计完成天数可领取
    string chest_icon = 3;                         // 奖励宝箱图标URL
    NewbieRewardStatus claim_status = 4;           // 领取状态
    string main_text = 7; // 主文案
    string sub_text = 8; // 副文案
  }
  repeated Reward rewards = 6;                     // 奖励列表
}

// 获取新手进阶任务
message NewbieAnchorTaskAdvanced {
  uint32 period = 1;                               // 周期 1 2
  uint64 period_start_time = 2;                    // 周期开始时间
  uint64 period_end_time = 3;                      // 周期结束时间
  repeated NewbieAnchorTaskItem task_list = 4;     // 任务列表
  NewbieRewardStatus claim_status = 5;             // 领取状态
  message RewardItem {
    string reward_name = 1;      // 奖励项名称
    string reward_icon = 2;      // 奖励项图标URL
  }
  repeated RewardItem reward_items = 6; // 奖励项列表
}

message GetNewbieAnchorTaskResp {
  NewbieAnchorTaskBeginner beginner_task = 1; // 新手入门任务
  repeated NewbieAnchorTaskAdvanced advanced_task_list = 2; // 新手进阶任务列表
  repeated TaskRewardItem auto_claim_reward_items = 3;      // 自动领取的奖励项列表
  bool is_waiting_begin = 5; // 等待任务开始阶段
}

// 领取新手入门任务奖励
message ClaimNewbieAnchorTaskRewardReq {
  uint32 identity_type = 1; // 身份类型
  uint32 uid = 2; // 用户id
}

// 领取新手进阶任务奖励
message ClaimNewbieAnchorTaskAdvancedRewardReq {
  uint32 identity_type = 1; // 身份类型
  uint32 period = 2; // 周期 1 2
  uint32 uid = 3; // 用户id
}

// 领取新手任务奖励
message ClaimNewbieAnchorTaskRewardResp {
  repeated TaskRewardItem rewards = 2;   // 该天数下的奖励列表
}

// 单个奖励项
message TaskRewardItem {
  string reward_name = 1;      // 奖励名称，如"优质新人头像框3天"
  string reward_icon = 2;      // 奖励图标URL
  uint32 count = 3;            // 数量（目前都是 天）
}

// 奖励条件
message TaskRewardCondition {
  string condition_desc = 1;             // 条件说明，如"累计完成3天任务"
  repeated TaskRewardItem rewards = 2;   // 该天数下的奖励列表
  bool is_claimed = 3;                   // 是否已领取
  string chest_icon = 4;                 // 奖励宝箱图标URL
}

// 获取新手任务奖励列表
message GetNewbieAnchorTaskRewardListReq {
  uint32 identity_type = 1; // 身份类型
  NewbieAnchorTaskType task_type = 2; // 任务类型
  uint32 uid = 4; // 用户id
}
message GetNewbieAnchorTaskRewardListResp {
  repeated TaskRewardCondition reward_condition_list = 1; // 奖励条件列表
}

// 测试重置新手任务
message TestResetNewbieAnchorTaskReq {
  bool certain = 1; // 是否确定
  uint32 uid = 2; // 用户id
}
message TestResetNewbieAnchorTaskResp {
}

// 测试完成新手任务
message TestCompleteNewbieAnchorTaskReq {
  bool certain = 1; // 是否确定
  uint32 uid = 2; // 用户id
  uint32 day = 3; // 签约完成天数
  repeated NewbieAnchorTaskSign task_sign = 4; // 任务标识
}
message TestCompleteNewbieAnchorTaskResp {
}

// 测试手动触发计划任务
message TestTriggerTimerReq {
  bool certain = 1; // 是否确定
  string task_name = 2; // 任务名称
}
message TestTriggerTimerResp {
}


message GetChannelLiveEndGuidePopupRequest {
  uint32 uid = 1; // 用户id
}

enum ChannelLiveEndGuidePopupType {
  CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_INVALID_UNSPECIFIED = 0; // 无效
  CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_USER_INFO = 1; // 用户资料
  CHANNEL_LIVE_END_GUIDE_POPUP_TYPE_ANCHOR_RECOMMEND = 2; // 主播推荐
}

message GetChannelLiveEndGuidePopupResponse {
  uint32 popup_type = 1; // see ChannelLiveEndGuidePopupType
  repeated InteractionUserInfo user_info_list = 2; // 用户资料列表
  repeated RecommendAnchorInfo anchor_info_list = 3; // 用户资料列表
}

message InteractionUserInfo {
  uint32 uid = 1; // 用户id
  string account = 2; // 用户账号
  string nickname = 3; // 用户昵称
  float nobility_level = 4; // 用户贵族等级
  repeated string tag_list = 5; // 用户标签列表
  string interaction_button_text = 6; // 互动按钮文案
  bool is_user_ol = 7; // 用户是否在线
}

// 主播认证标识
message NewbieAnchorCertInfo {
  string item_name = 1;           // 标识名称
  string base_imgurl = 2;         // 标识底图
  string shadow_color = 3;
}

message RecommendAnchorInfo {
  uint32 uid = 1; // 主播id
  uint32 channel_id = 2; // 主播房间id
  string account = 3; // 主播账号
  string nickname = 4; // 主播昵称
  repeated NewbieAnchorCertInfo cert_url_list = 5; // 标识列表
  bool is_user_ol = 6; // 用户是否在线
}

message SendChannelLiveThanksRequest {
  uint32 from_uid = 1; // 发送者用户id
  repeated uint32  uid_list = 2; // 互动用户id列表
}

message SendChannelLiveThanksResponse {
}