syntax = "proto3";

package channel_mic_middle;

option go_package = "golang.52tt.com/protocol/services/channel-mic-middle";

import "tt/quicksilver/extension/options/options.proto";

service ChannelMicMiddle {
  option (service.options.service_ext) = {
    service_name: "channel-mic-middle"
  };

  // 上麦
  rpc HoldMic(HoldMicReq) returns (HoldMicResp) {}

  // 抱上麦
  rpc TakeHoldMic(TakeHoldMicReq) returns (TakeHoldMicResp) {}

  // 换麦
  rpc ChangeMic(ChangeMicReq) returns (ChangeMicResp) {}

  // 下麦
  rpc ReleaseMic(ReleaseMicReq) returns (ReleaseMicResp) {}

  // 设置麦位状态
  rpc SetMicStatus(SetMicStatusReq) returns (SetMicStatusResp) {}

  // 踢下麦
  rpc KickOutMic(KickOutMicReq) returns (KickOutMicResp);

  // 设置自动锁麦开关
  rpc SetAutoDisableMicSwitch(SetAutoDisableMicSwitchReq) returns (SetAutoDisableMicSwitchResp) {}

  // 重置麦位数量
  rpc ResetMicNumber(ResetMicNumberReq) returns (ResetMicNumberResp) {}
}

// 麦位信息
message MicSpaceInfo
{
  uint32 mic_id = 1;                       // 麦位ID 1 - 9
  uint32 mic_state = 2;                    // ga::EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
  uint32 mic_uid = 3;                      // 麦上用户 如果麦上有人的话
  uint32 mic_ts = 4;                       // 麦位最后更新时间
}

// 麦位操作的权限
enum MicPermission {
  Invalid = 0;
  ModifyMicStatus = 1;                      // 修改麦位状态
  KickOnMicUser = 2;                        // 将麦上用户踢下麦
}

message HoldMicReq {
  string source = 1;                        // 操作来源
  uint32 uid = 2;
  uint32 cid = 3;
  uint32 mic_id = 4;                        // 麦位ID
  repeated MicPermission permissions = 5;   // 用户的麦位权限
  uint32 app_id = 6;                        // 仅用于oss上报
  uint32 market_id = 7;                     // 仅用于oss上报
}

message HoldMicResp {
  MicSpaceInfo hold_mic_info = 1;           // 用户所上麦位
  uint32 kicked_uid = 2;                    // 被踢下麦位的用户（只有在用户有KickOnMicUser权限时才有可能不为空）
  repeated MicSpaceInfo all_mic_list = 3;   // 全体麦位信息，包括各个麦位状态
  uint64 server_time_ms  = 4;               // 64bit 毫秒级 服务器时间
}

message TakeHoldMicReq {
  string source = 1;                        // 操作来源
  uint32 op_uid = 2;                        // 操作者uid
  uint32 target_uid = 3;                    // 被操作者uid
  uint32 cid = 4;
  uint32 mic_id = 5;                        // 麦位ID
  repeated MicPermission permissions = 6;   // 操作者的麦位权限
  uint32 app_id = 7;                        // 仅用于oss上报
  uint32 market_id = 8;                     // 仅用于oss上报
}

message TakeHoldMicResp {
  MicSpaceInfo hold_mic_info = 1;           // 用户所上麦位
  uint32 kicked_uid = 2;                    // 被踢下麦位的用户（只有在操作者有KickOnMicUser权限时才有可能不为空）
  repeated MicSpaceInfo all_mic_list = 3;   // 全体麦位信息，包括各个麦位状态
  uint64 server_time_ms  = 4;               // 64bit 毫秒级 服务器时间
}

message ChangeMicReq {
  string source = 1;                        // 操作来源
  uint32 uid = 2;
  uint32 cid = 3;
  uint32 mic_id = 4;                        // 目标麦位ID
}

message ChangeMicResp {
  MicSpaceInfo from_mic_info  = 1;           // 如果换成功 源麦位的状态信息
  MicSpaceInfo to_mic_info  = 2;             // 如果换成功 目标麦位的状态信息
  uint64 server_time_ms  = 3;                // 64bit 毫秒级 服务器时间
  repeated MicSpaceInfo all_mic_list = 4;    // 当前全量麦位列表信息
  uint32 mic_mode = 5;                       // 当前麦模式
}

message ReleaseMicReq {
  string source = 1;                         // 操作来源
  uint32 uid = 2;
  uint32 cid = 3;
}

message ReleaseMicResp {
  MicSpaceInfo close_mic_info = 1;           // 成功被下麦的麦位信息 如果没有下麦那么该值为空
  repeated MicSpaceInfo all_mic_list = 2;    // 全体麦位信息 包括各个麦位状态
  uint64 server_time_ms  = 3;                // 64bit 毫秒级 服务器时间
  bool is_auto_disable_mic = 4;              // 是否自动完成了锁麦
}

message SetMicStatusReq {
  string source = 1;                        // 操作来源
  uint32 op_uid = 2;
  uint32 cid = 3;
  repeated uint32 mic_id_list = 4;
  uint32 mic_state = 5;                     // ga::EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
}

message SetMicStatusResp{
  repeated MicSpaceInfo all_mic_list = 1;         // 当前全量麦位列表信息
  repeated MicSpaceInfo kicked_mic_list = 2;      // 如果麦位上有人 且 请求锁麦 那么原来麦上的用户会被踢下麦
  uint64 server_time_ms = 3;                      // 64bit 毫秒级 服务器时间
  uint32 mic_mode = 4;                            // mic mode
}

message KickOutMicReq {
  string source = 1;                        // 操作来源
  uint32 op_uid = 2;
  uint32 cid = 3;
  repeated uint32 target_uid_list = 4;      // 被踢uid列表，必须是真实Uid
  uint32 ban_second = 5;                    // 用户被踢下麦后，禁止上麦的时间（0为不限制）
  string toasts = 6;                        // 用户被踢下麦后的提醒文案
}

message KickOutMicResp {
  repeated uint32 disable_mic_id_list = 1;     // 如果开启了自动锁麦，这里是被锁的麦位id列表
  repeated MicSpaceInfo kicked_mic_list = 2;   // 成功被踢的麦位列表
  repeated MicSpaceInfo all_mic_list = 3;      // 当前全量麦位列表信息
  uint64 server_time_ms  = 4;                  // 64bit 毫秒级服务器时间
}

message SetAutoDisableMicSwitchReq {
  string source = 1;                               // 操作来源
  uint32 op_uid = 2;
  uint32 cid = 3;
  bool switch_on = 4;                              // true 打开自动锁麦 false 关闭自动锁麦
}

message SetAutoDisableMicSwitchResp {
}

message ResetMicNumberReq {
  // 操作人uid
  uint32 uid = 1;
  // 房间id
  uint32 cid = 2;
  // 指定设置的麦位数量
  uint32 mic_num = 3;
  // 麦位模式
  uint32 mic_mode = 4;
  // 玩法id
  uint32 scheme_id = 5;
  // 房间displayId
  uint32 channel_display_id = 6;
  // 房间类型
  uint32 channel_type = 7;
}

message ResetMicNumberResp {
  // 当前全量麦位列表信息
  repeated MicSpaceInfo all_mic_list = 1;
  // 64bit 毫秒级服务器时间
  uint64 server_time_ms = 2;
}