syntax="proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package unified_pay_rmb;
option go_package = "golang.52tt.com/protocol/services/unified-pay-rmb";

//统一人民币支付接口定义

//支付下单
message RmbPayOrderReq {
    string rmb_app_id = 1;      //业务id   必填
    uint32 uid 	      = 2;      //购买的用户UID  必填
    string pay_channel = 3;     //下单的渠道  必填
    string out_trade_no = 4;    //业务订单号  必填
    uint32 total_price  = 5;    //总价格rmb(单位分)  必填
    string order_title  = 6;    //订单标题-第三方展示, 必填
    string order_desc   = 7;    //订单描述
    uint32 market_id    = 8;    //market_id:  ga_base.proto中BaseReq.MarketId, 默认为TT

    string os_type = 10;        //系统类型 (i IOS系统 a android系统) 必填
    string version = 11;       //客户端版本号
    string remark =  12;       //备注
    string bundle_id = 13;     //IOS的produceID(appstore支付必传)
    string product_id = 14;    //IOS的product_id (appstore支付必传)
    string code = 15 ;         //端外支付code (公众号支付必传)
    string ttid = 16;          //端外支付TTID (公众号支付必传)
    string token = 17;         //端外支付校验 (公众号支付必传)
    string user_ip = 18;       //用户ip

    string deal_token = 19;    //链路校验token
    uint32 order_time = 20;    //下单时间（如果不传，取当前时间)
    string device_id  = 21;    //设备ID
    string recharge_type = 22; //支付类型
    string locale = 23;        //地区
}
message RmbPayOrderRsp{
    string order_no = 1;        //TPAY返回的订单号
    string token = 2;
    string cli_order_no = 3;    //支付订单号(统一支付生成)
    string cli_order_title = 4;  //订单标题
    string tsk = 5; //加密字符串
    string channel_map = 6; //唤起支付渠道的参数, 用于安卓、IOS、前端使用
    string new_deal_token = 7;   //返回的新的deal_token
}

//支付风控检查需要的参数
message RmbRiskArgs {
    string rmb_app_id = 1;      //业务id    
    uint32 uid        = 2;      //购买的用户UID  
    string pay_channel = 3;     //下单的渠道  
    uint32 total_price  = 4;    //总价格rmb(单位分)  
    string recharge_type = 5;   //支付类型
    string locale = 6;          //地区
    string product_id = 8;      //IOS的product_id (appstore支付必传)
}
//下单前置风控检查
message CheckRmbPayRiskReq {
    RmbRiskArgs risk_args = 1;  //支付风险检查参数
    string os_type = 2;         //系统类型 (i IOS系统 a android系统)
    string user_ip = 3;         //用户ip
    bytes  device_id  = 4;      //设备ID
    string request_id = 5;      //请求RequestId
    uint32 client_type = 6;     //clientType
    uint32 client_version = 7;  //client Version
    uint32 market_id   = 8;     //market id
    string face_result_token = 9; //人脸识别结果
}
message CheckRmbPayRiskRsp {
    uint32 face_scene        = 1; //人脸识别场景
    string face_auth_context_json = 2; 
}


//订单状态
enum RmbPayStatus 
{
    None   =  0;
    Paying =  1;      //支付中
    Success = 2;      //支付成功
    Failed  = 3;      //失败
}


//订单信息
message RmbPayOrderInfo
{
    string rmb_app_id =   1;  //业务id
    uint32 uid        =   2;  //购买的用户UID
    string pay_channel =  3;  //下单的渠道
    string out_trade_no = 4;  //业务订单号
    uint32 total_price  = 5;  //总价格rmb(单位分)
    uint32 market_id    = 6;  //market_id:  ga_base.proto中BaseReq.MarketId, 默认为TT
    uint32 status       = 7;  //订单状态	
    string pay_order_id = 8;  //支付订单id
    uint32 order_time   = 9;  //下单时间
    string order_no     = 10; //TPAY返回的订单号
}


//查询支付结果，支付时间
message GetRmbPayOrderInfoReq {
    string out_trade_no = 1;    //业务订单号，不填按支付订单查询
    string pay_order_id = 2;    //支付订单号
    uint32 order_time  =  3;    //下单时间(不传查当月)
    bool   from_notify =  4;    //是否来自内部服务(业务方不用填)
}
message GetRmbPayOrderInfoRsp {
    RmbPayOrderInfo order_info = 1;
}


service UnifiedPayRmb {
    //风控前置检查
    rpc CheckRmbPayRisk (CheckRmbPayRiskReq) returns (CheckRmbPayRiskRsp);
    //支付下单
    rpc RmbPayOrder (RmbPayOrderReq) returns (RmbPayOrderRsp);
    //获取支付订单信息
    rpc GetRmbPayOrderInfo (GetRmbPayOrderInfoReq) returns (GetRmbPayOrderInfoRsp);

    //以下内部服务使用
    rpc UpdateRmbPayOrderInfo (UpdateRmbPayOrderInfoReq) returns (UpdateRmbPayOrderInfoRsp);
    rpc GetNoFinishCbOrderList (GetNoFinishCbOrderListReq) returns (GetNoFinishCbOrderListRsp);
}


//失败订单原因
enum FaildReason
{
    Unknow   = 0;
    OrderFail = 1;      //下单失败
    PayFail   = 2;      //支付失败
}

//回调完成状态
enum CbFinishStatus
{
    No = 0;  //未完成回调
    Ok = 1;  //完成回调
    MaxTimes = 2; //回调超过最大次数
    NoNeedCall = 3 ;  //不需要回调
}

//未完成待更新的订单信息
message NoFinishOrderInfo {
    RmbPayOrderInfo base_info = 1;
    uint32 cb_finish    = 2;            //CbFinishStatus
    uint32 cb_num       = 3;
    uint32 failed_reason = 4;
    string failed_desc  = 5;
    string total_fee    = 6;   //TPAY-总金额
    string other_order_no = 7; //TPAY-支付渠道订单
    string other_status  = 8; //TPAY-支付渠道状态

}

//更新订单状态
message UpdateRmbPayOrderInfoReq {
    NoFinishOrderInfo order_info = 1;
}
message UpdateRmbPayOrderInfoRsp {

}

//获取未完成业务回调的订单列表
message GetNoFinishCbOrderListReq {
    uint32   order_time = 1;
    uint32   limit      = 2; //限制数量
}
message GetNoFinishCbOrderListRsp {
    repeated NoFinishOrderInfo pay_order_list = 1;
}



