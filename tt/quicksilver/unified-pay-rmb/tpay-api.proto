syntax="proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package tpay_api;

option go_package = "golang.52tt.com/protocol/services/unified-pay-rmb/tpay-api";


//与TPAY交互的 支付协议 

// 支付下单
//http://s-doc.ttyuyin.com/web/#/16/688

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ApiRestOrderReq{
    string order_type = 1; //订单类型类型 默认:BUY
    string os_type = 2; //系统类型 (i IOS系统 a android系统)
    string pay_channel = 3; //支付渠道,货币组指定
    string business_id = 4; //子业务id，货币组指定
    string fm = 5; //TT语音、欢游等对应在货币系统的标识,用于APP支付参数解密, 若payChannel不为空,必传
    string version = 6;
    string cli_order_no = 7;
    string cli_buyer_id = 8;
    string cli_price = 9;
    string cli_order_title = 10;
    string cli_order_desc = 11;
    string cli_notify_url = 12;
    string create_time = 13; //外部订单时间(“yyyy-MM-dd HH:mm:ss”)
    string remark = 14; //备注
    string bundle_id = 15; //IOS的包名(appstore支付必传)
    string product_id = 16; //商品ID(appstore必传)
    string user_ip   = 17;

    string code = 18; //端外支付code
    string time_out = 19; //订单支持超时时间（分钟）
    string deviceId  = 20;    //设备ID
}

message ApiResponseHeader {
    string result = 1;
    string message = 2;
}
message ApiRestOrderRsp {
    string order_no = 1;
    string token = 2;
    string cli_order_no = 3;
    string cli_order_title = 4;
    string order_price = 5;
    string tsk = 6; //加密字符串
    string channel_map = 7 ;//唤起支付渠道的参数, 用于安卓、IOS、前端使用
}



//TPAY支付结果回调
//http://s-doc.ttyuyin.com/web/#/16/689
message TPayResultCallbackReq {
	string order_no = 1;     // 订单号
	string pay_channel = 2;  // 支付渠道
	string total_fee   = 3;  // 金额数量
    string order_status= 4;   // 订单状态 (“0”失败 “1” 成功)
    string cli_order_no= 5;   // String	服务器订单号
    string other_order_no = 6; //支付渠道订单号
    string other_status   = 7; //第三方订单状态
    string notify_time    = 8; // 回调时间 (“yyyyMMddHHmmss”)
    string begin_time     = 9; // 苹果订阅的开始时间, 注：仅限苹果订阅订单
    string end_time       = 10; //苹果订阅的结束时间，注：仅限苹果订阅订单
}

