syntax="proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package unified_pay_rmb_cb;

option go_package = "golang.52tt.com/protocol/services/unified-pay-rmb/cb";

import "tt/quicksilver/unified-pay-rmb/unified-pay-rmb.proto";

//以下业务服务实现-支付结果回调功能
message PayRmbResultNotifyReq {
    unified_pay_rmb.RmbPayOrderInfo order_result = 1;
}

message PayRmbResultNotifyRsp {
    uint32 code = 1;           //0成功，其它失败
	string msg  = 2;           //错误信息 
}

service PayRmbCallback {
    rpc PayRmbResultNotify(PayRmbResultNotifyReq) returns (PayRmbResultNotifyRsp);
}
