syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-base-api";

package channel_base_api;

//service
service ChannelBaseApi {
  rpc KickOutChannelMember(KickOutChannelMemberReq) returns (KickOutChannelMemberResp);
  rpc GenChannelEnterToken(GenChannelEnterTokenReq) returns (GenChannelEnterTokenResp);
  rpc ChannelQuit(ChannelQuitReq) returns (ChannelQuitResp);
  //获取敲门进房token
  rpc GenKnockDoorToken(GenKnockDoorTokenReq) returns (GenKnockDoorTokenResp);
  //房间用户超时退房
  rpc ProcessExpireQuitChannel(ProcessExpireQuitChannelReq) returns (ProcessExpireQuitChannelResp);
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message KickOutChannelMemberReq {
  uint32 channel_id = 1;
  uint32 op_uid = 2;
  uint32 ban_enter_second = 3;         // enter again after 'ban_enter_second', 踢出以后禁止再次进该房间的时长
  repeated uint32 target_uid_list = 4; // 目标用户列表，限制最多踢10人
  uint32 kick_type = 5;                // 1: 管理员踢人 2: 内审平台踢人, see channel_opt_.proto -> ChannelKickedOpt.EChannelKickType
  string kick_text = 6;                // 踢人消息
  string opSource = 7;                 // 操作来源
}

message KickOutChannelMemberResp {
  repeated uint32 kickouted_uid_list = 1; // 成功被踢出房间的uid
}

message GenChannelEnterTokenReq {
  string scene = 1;              //业务场景值,需要找房间基础分配
  uint32 uid = 2;
  uint32 cid = 3;
  uint32 effective_second = 4;   //token有效时间，单位秒，10s ~ 600s(如果时间范围有特殊要求，可根据scene配置)
  bool   skip_password_check = 5; //是否跳过房间密码检查，是则在房间有密码的情况下也可以进入
  map<string, string> extra = 6;  //扩展属性
}
message GenChannelEnterTokenResp {
  string token = 1;
}

// deprecated
enum ChannelQuitType {
    CHANNEL_QUIT_TYPE_UNSPECIFIED = 0;
    CHANNEL_QUIT_TYPE_HEARTBEAT_EXPIRE = 1; // 心跳超时退房
}
message ChannelQuitReq {
  uint32 cid = 1;
  uint32 uid = 2;
  ChannelQuitType quit_type = 3; // deprecated  该字段已废弃，处理超时退房用ProcessExpireQuitChannel接口
}
message ChannelQuitResp {
}

message GenKnockDoorTokenReq {
  //用户id
  uint32 uid = 1;
  //房间id
  uint32 cid = 2;
  //跟随者uid
  uint32 follow_uid = 3;
  //0: normal, 1:开黑邀请
  uint32 token_type = 4;
  //token有效时间，单位秒
  uint32 effective_second = 5;
}
message GenKnockDoorTokenResp {
  //敲门进房token
  string token = 1;
}
message ProcessExpireQuitChannelReq {
  //超时用户列表
  repeated uint32 expire_uid_list = 1;
}
message ProcessExpireQuitChannelResp {
}