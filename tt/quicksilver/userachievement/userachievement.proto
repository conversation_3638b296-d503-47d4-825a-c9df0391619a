syntax = "proto3";

package userachievement;

option go_package = "golang.52tt.com/protocol/services/user-achievement";

service UserAchievement {

    //user official certify style
    rpc ModifyCertifyStyle(ModifyCertifyStyleReq) returns (ModifyCertifyStyleResp){}
    rpc DeleteCertifyStyle(DeleteCertifyStyleReq) returns (DeleteCertifyStyleResp){}
    rpc ListCertifyStyle(ListCertifyStyleReq) returns (ListCertifyStyleResp){}
    rpc ResetCertifyStyleSort(ResetCertifyStyleSortReq) returns (ResetCertifyStyleSortResp) {}

    //official certify
    rpc DelUserOfficialCertify(DelUserOfficialCertifyReq) returns (DelUserOfficialCertifyResp){}
    rpc SetUserOfficialCertify(SetUserOfficialCertifyReq) returns (SetUserOfficialCertifyResp){}
    rpc GetUserOfficialCertifyByUids(GetUserOfficialCertifyByUidsReq) returns (GetUserOfficialCertifyListResp){}
    rpc GetUserOfficialCertifyList(GetUserOfficialCertifyListReq) returns (GetUserOfficialCertifyListResp){}
    rpc GetUserOfficialCertify(GetUserOfficialCertifyReq) returns (UserOfficialCertifyInfo){}
}

//Certify Style
message CertifyStyle {
    string id = 1;          //唯一标志, 新增时可以不传，默认生成id
    string icon = 2;        //icon
    string background = 3;  //.9背景图
    string font_color = 4;  //eg: #f0f0f0
    uint32 version = 5;
    uint64 last_modify_at = 6;
    int64  weight = 7;      //权重
    string push_sample = 8; //恭喜你通过###获得了###认证，请到个人资料页中查看；推送模板，###是占位符
    string title = 9;       //认证类型
}

//删认证类型
message DeleteCertifyStyleReq {
    repeated string ids = 1;
}

message DeleteCertifyStyleResp {
}

//新增、修改认证类型
message ModifyCertifyStyleReq {
    CertifyStyle style = 1;
}

message ModifyCertifyStyleResp {
    string id = 1;
}

//list认证类型
message ListCertifyStyleReq {
    uint32 skip = 1;
    uint32 limit = 2;
}

message ListCertifyStyleResp {
    repeated CertifyStyle styles = 1;
}

//重置认证类型排序
message ResetCertifyStyleSortReq {
    message Sort {
        string id = 1;
        int64 weight = 2;
    }
    repeated Sort sort = 1;
}

message ResetCertifyStyleSortResp {

}

//Official Certify
message OfficialCertifyInfo {
    string title = 2;               //文本
    string style = 4;               //style id, 旧版要
    CertifyStyle style_info = 5;
}

message UserOfficialCertifyInfo {
    uint32 uid = 1;
    repeated OfficialCertifyInfo info = 2;
    string intro = 3;                       //认证简介
}

//message SetUserOfficialCertifyReq {
//    uint32 uid = 1;
//    string title = 2;
//    string intro = 3;
//    string style = 4;
//    enum OP {
//        ADD = 0;
//        MODIFY = 1;
//    }
//    OP op = 5;
//    repeated string push_args = 6;  //推送消息的参数，与推送模板的占位符个数要对应
//}

message ChangeInfo {
    string title = 1;       //认证描述(.9背景图显示的那个文本)
    string style = 3;       //style id
    enum OP {
        DEFAULT = 0;
        ADD = 1;            //新增的标记一下,才会推送
        MODIFY = 2;
    }
    OP op = 5;
    string push_args = 6;  //推送消息的参数，与推送模板的占位符个数要对应
}

//!!![全量]设置用户认证信息
message SetUserOfficialCertifyReq {
    uint32 uid = 1;
    repeated ChangeInfo info = 2;
    string intro = 3;           //简介(用户认证整段的那个描述)
}

message SetUserOfficialCertifyResp {

}

message DelUserOfficialCertifyReq {
    uint32 uid = 1;
}

message DelUserOfficialCertifyResp {

}

message GetUserOfficialCertifyByUidsReq{
    repeated uint32 uids = 1;
}

message GetUserOfficialCertifyListReq {
    uint32 page_no = 1;
    uint32 page_size = 2;
    string style = 3;
    string title = 4;       //描述模糊搜索
    uint32 uid = 5;         //指定uid搜索
}

message GetUserOfficialCertifyReq {
    uint32 uid = 1;
}

message GetUserOfficialCertifyListResp{
    repeated UserOfficialCertifyInfo info_list = 1;
    uint32 total_count = 2;
}
