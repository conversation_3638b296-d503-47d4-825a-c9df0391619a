syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/user-recall-award";
package user_recall_award;

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service UserRecallAward {

  // -------------------- 客户端协议 begain ----------------------------------------
  // 获取用户回归登录礼包信息
  rpc GetRecallLoginAwardInfo(GetRecallLoginAwardInfoReq) returns (GetRecallLoginAwardInfoResp) {}
  // 查看召回状态
  rpc CheckRecallStatus(CheckRecallStatusReq) returns (CheckRecallStatusResp) {}
  // 批量查看召回状态
  rpc BatchCheckRecallStatus(BatchCheckRecallStatusReq) returns (BatchCheckRecallStatusResp) {}
  // 批量查看召回状态(判断流失时间)
  rpc BatchCheckRecallStatusWithLastLoginTime(BatchCheckRecallStatusWithLastLoginTimeReq) returns (BatchCheckRecallStatusWithLastLoginTimeResp) {}
  // 批量查询邀请奖励
  rpc BatchGetInviteAwardInfo(BatchGetInviteAwardInfoReq) returns (BatchGetInviteAwardInfoResp) {}
  // 获取回归奖励信息
  rpc GetReturnAwardInfo(GetReturnAwardInfoReq) returns (GetReturnAwardInfoResp) {}
  // 获取邀请奖励
  rpc GetInviteAward(GetInviteAwardReq) returns (GetInviteAwardResp) {}
  // 获取回归奖励
  rpc GetReturnAward(GetReturnAwardReq) returns (GetReturnAwardResp) {}
  // 获取最贵奖励配置
  rpc GetMostExpAwardInfo(GetMostExpAwardInfoReq) returns (GetMostExpAwardInfoResp) {}

  // 数据清清除接口（测试用）
  rpc ClearData(ClearDataReq) returns (ClearDataResp) {}

  // 订单数对账
  rpc CntOrder(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

  // 订单列表对账
  rpc GetOrderId (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  // 自动补单
  rpc ReplaceOrder (ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp);

  // -------------------- web端协议 begain ----------------------------------------

  // 获取回归奖励
  rpc GetRecallAward(GetRecallAwardReq) returns (GetRecallAwardResp) {}
  // 设置回归奖励
  rpc UpdateRecallAward(UpdateRecallAwardReq) returns (UpdateRecallAwardResp) {}

}

// 获取用户回归登录礼包信息
message GetRecallLoginAwardInfoReq {
  uint32 recalled_uid = 1;  // 被召回uid
  uint32 consume_val = 2; // 累计消费
  int64 last_login_time = 3; // 被召回用户最后登陆时间，用于区分召回批次
}

message GetRecallLoginAwardInfoResp {
  uint32 min_val = 1;     // 最小可领取的奖励
  uint32 max_val = 2;     // 最大可领取的奖励
  uint32 expire = 3;      // 剩余过期时间 单位s
  AwardInfo award_info = 4; // 奖励信息
}

enum AwardReceiveStatus {
  AWARD_RECEIVE_STATUS_UNKNOWN = 0;
  AWARD_RECEIVE_STATUS_NOT_RECEIVE = 1; // 待领取
  AWARD_RECEIVE_STATUS_RECEIVED = 2; // 已领取
}

message AwardInfo {
  uint32 gift_id = 1;    // 礼包ID, 卡片等级
  string gift_name = 2; // 礼包名称
  uint32 gift_cnt = 3;  // 礼包数量
  string gift_icon = 4;   // 礼包图标
  uint32 gift_val = 5;  // 礼包价值
  uint32 expire_day = 6; // 过期天数
  uint32 award_type = 7; // 奖励类型
  uint32 gift_type = 8; // 礼包类型
}

enum RecallStatus {
  RECALL_STATUS_UNKNOWN = 0;
  RECALL_STATUS_DOING = 1;
  RECALL_STATUS_HAS_AWARD = 2;
  RECALL_STATUS_END = 3;
}

// 检查召回状态
message CheckRecallStatusReq {
  uint32 recalled_uid = 1; // 被召回uid
}

message CheckRecallStatusResp {
  uint32 recall_status = 1; // 见 RecallStatus
}

// 检查召回状态
message BatchCheckRecallStatusReq {
  repeated uint32 recalled_uid_list = 1; // 被召回uid
}

message BatchCheckRecallStatusResp {
  map<uint32, uint32> recall_status = 1; // 见 RecallStatus
}

// 检查召回状态
message BatchCheckRecallStatusWithLastLoginTimeReq {
  repeated uint32 recalled_uid_list = 1; // 被召回uid
  repeated uint32 last_login_time = 2; // 流失时间
}

message BatchCheckRecallStatusWithLastLoginTimeResp {
  map<uint32, uint32> recall_status = 1; // 见 RecallStatus
}


message RecalledUserInfo {
  uint32 recalled_uid = 1; // 被召回uid
  uint32 consume_val = 2; // 累计消费
  int64 last_login_time = 3; // 被召回用户最后登陆时间，用于区分召回批次
}

// 批量查询邀请奖励
message BatchGetInviteAwardInfoReq {
  repeated RecalledUserInfo recalled_user = 1; // 被召回人信息
}

message InviteAwardInfo {
  uint32 recalled_uid = 1; // 被召回uid
  AwardInfo award_info = 2; // 奖励信息
}

message BatchGetInviteAwardInfoResp {
  repeated InviteAwardInfo award_infos = 1;
}

// 获取回归奖励信息
message GetReturnAwardInfoReq {
  uint32 recalled_uid = 1;
}

message ReturnConsumeAwardInfo {
  AwardInfo award_info = 1; // 奖励信息
  uint32 receive_val = 2; // 领取条件
  uint32 receive_status = 3; // 领取状态, 见AwardReceiveStatus, 0:不可领取(待完成) 1:待领取 2:已领取
}

message GetReturnAwardInfoResp {
  uint32 task_limited_time = 1;              // 任务限时时间
  uint32 con_login_day = 2;             //  连续登录天数
  uint32 max_login_day = 3;             // 最大登录天数
  repeated AwardInfo login_award = 4;   // 登录奖励
  uint32 login_award_receive_status = 5;  // 登录奖励领取领取状态, 见, AwardReceiveStatus
  uint32 con_consume_val = 6;            // 连续消费
  uint32 max_consume_val = 7;           // 最大消费
  repeated ReturnConsumeAwardInfo consume_award = 8; // 消费奖励
  uint32 award_limited_time = 9;                 // 奖励领取时间
}

enum AwardType {
  AWARD_TYPE_UNKNOWN = 0;
  AWARD_TYPE_LOGIN = 1; // 登录奖励
  AWARD_TYPE_CONTINUE_LOGIN = 2; // 连续登录奖励
  AWARD_TYPE_CONSUME = 3; // 消费奖励
  AWARD_TYPE_INVITE = 4; // 邀请奖励
}

message GetInviteAwardReq {
  uint32 recalled_uid = 1; // 被召回uid
  uint32 invite_uid = 2; // 邀请人uid
  int64 last_login_time = 3; // 被召回用户最后登陆时间，用于区分召回批次
}

message GetInviteAwardResp {
  AwardInfo award_info = 1; // 奖励信息
}

message GetReturnAwardReq {
  uint32 recalled_uid = 1; // 被召回uid
  uint32 award_type = 2; // 奖励类型, 见AwardType
  uint32 consume_lv = 3;  // 奖励对应的消费等级, 当award_type为AWARD_TYPE_CONSUME时有效
}

message GetReturnAwardResp {
  AwardInfo award_info = 1; // 奖励信息
}

message GetMostExpAwardInfoReq {}

message GetMostExpAwardInfoResp {
  repeated AwardInfo award_info_list = 1; // 奖励列表
}

//----------运营后台相关




//邀请奖励
message InviteAward{
  uint32 id = 1;           //库存id 
  uint32 min_val = 2;    //回归用户历史消费最小值
  uint32 max_val = 3;    //回归用户历史消费最大值
  AwardConfig award = 4;    //奖励
}

message InviteAwardList{
  repeated InviteAward list = 1;
}

//奖励物品类型
enum AwardGiftType {
  AWARD_GIFT_TYPE_UNSPECIFIED = 0;         // 无效值
  AWARD_GIFT_TYPE_PACKAGE = 1;             // 包裹礼物
  AWARD_GIFT_TYPE_NOBLE_CARD = 2;          // 贵族体验卡
  AWARD_GIFT_TYPE_MIC_STYLE = 3;           // 麦位框
  AWARD_GIFT_TYPE_HORSE = 4;               // 坐骑
}

//运营后台奖励信息
message AwardConfig{
  uint32 gift_type = 1;        // 消费类型 see AwardGiftType
  string gift_id = 2;          //包裹id、贵族体验卡等级、坐骑id、麦位框id
  uint32 day_count = 3;        //天数
}


//回归奖励
message ReturnedAwardChance{
  uint32 chance = 1;        //概率值
  AwardConfig award = 2;    //奖励信息
}

message ReturnedAward{
  uint32 id = 1;                            //库存id 
  uint32 min_val = 2;                       //回归用户历史消费最小值
  uint32 max_val = 3;                       //回归用户历史消费最大值
  repeated ReturnedAwardChance list = 4;    //回归奖励信息
}


//消费奖励
message ConsumeAward{
  uint32 price = 1;           // 消费T豆
  AwardConfig award = 2;      //奖励信息
}

//任务奖励
message TaskAward{
  repeated AwardConfig login = 1;        //连续登录奖励
  repeated ConsumeAward consume = 2;    // 消费奖励
}

message GetRecallAwardReq{
}

message GetRecallAwardResp{
  InviteAwardList invite = 1;  //邀请奖励
  repeated ReturnedAward returned = 2;  //回归奖励 
  TaskAward task = 3;         //任务奖励
  uint32 update_time = 4;
}

message UpdateRecallAwardReq{
  InviteAwardList invite = 1;  //邀请奖励
  repeated ReturnedAward returned = 2;  //回归奖励 
  TaskAward task = 3;         //任务奖励
}

message UpdateRecallAwardResp{
}

// 清除数据
message ClearDataReq {
  uint32 uid = 1;
}

message ClearDataResp {}
