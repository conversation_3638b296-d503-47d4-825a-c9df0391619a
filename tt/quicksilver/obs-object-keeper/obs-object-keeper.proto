syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/obsobjectkeeper";
package obsobjectkeeper;

//enum STATUS {
//  UPLOADING = 0;
//  REVIEWING = 1;
//  REVIEW_PASS = 2;
//  REVIEW_DENY = 3;
//  DELETED = 4;
//}

//enum ERROR {
//  SUCCESS = 0;
//  OBJECT_NOT_FOUND = 1;
//}

//message StartRecordReq {
//  string appid = 1;
//  string scope = 2;
//  string object = 3;
//  int64 time = 4;
//  string multi_upload_id = 5;
//  uint32 scope_id = 6;
//}

//message StartRecordResp {
//  ERROR result = 1;
//}

//message UpdateStatusReq {
//  string object = 1;
//  STATUS status = 2;
//  int64 time = 3;

//  // deprecated
//  string url = 4; // 兼容模式，没使用obs系统的，没调用Start，因此没记录的，这里在update的时候，如果携带url，那就生成记录并且记录url
//}

//message UpdateStatusResp {
//  ERROR result = 1;
//  STATUS status = 2; // 更新成功则返回上次的状态，失败则返回当前的状态。 TODO 状态之间的覆盖互斥处理
//}

//message GetObjectInfoReq {
//  string object = 1;
//}

//message GetObjectInfoResp {
//  ERROR result = 1;
//  string appid = 2;
//  string scope = 3;
//  string object = 4;
//  int64 time = 5;
//  STATUS status = 6;
//  string url = 7;   // deprecated
//  string multi_upload_id = 8;
//  uint32 scope_id = 9;
//}

//message SetObjectInfoReq {
//  string appid = 1;
//  string scope = 2;
//  string object = 3;
//  int64 time = 4;
//  STATUS status = 5;
//  string multi_upload_id = 6;
//  uint32 scope_id = 7;
//}

//message SetObjectInfoResp {
//  ERROR result = 1;
//}

service ObsObjectKeeper {
  //rpc StartRecord (StartRecordReq) returns (StartRecordResp) {}
  //rpc UpdateStatus (UpdateStatusReq) returns (UpdateStatusResp) {}

  //rpc GetObjectInfo (GetObjectInfoReq) returns (GetObjectInfoResp) {}
  //rpc SetObjectInfo (SetObjectInfoReq) returns (SetObjectInfoResp) {}

    rpc PutObjectMeta (PutObjectMetaReq) returns (PutObjectMetaResp){}
    rpc GetObjectMeta (GetObjectMetaReq) returns (GetObjectMetaResp){}
    rpc UpdateObjectStatus (UpdateObjectStatusReq) returns (UpdateObjectStatusResp){}
    rpc GetObjectStatus (GetObjectStatusReq) returns  (GetObjectStatusResp){} 
}
message PutObjectMetaReq {
    ObjectMeta meta = 1;
}
message PutObjectMetaResp {
    //empty
}
message GetObjectMetaReq {
    ObjectId oid = 1;
}
message GetObjectMetaResp {
    bool exists = 1;
    ObjectMeta meta = 2;
}
message UpdateObjectStatusReq {
    ObjectId oid = 1;
    int32 status = 2;   //obs/common/model/ostatus.go
    int64 update_status_at = 3; 
}
message UpdateObjectStatusResp {
    //empty
}
message GetObjectStatusReq {
    ObjectId oid = 1;
}
message GetObjectStatusResp {
    bool exists = 1;
    int32 status = 2;   //obs/common/model/ostatus.go
    int64 update_status_at  = 3;
}
message ObjectMeta {
    ObjectId oid = 1;
    int32 status = 2;   //obs/common/model/ostatus.go
    int64 update_status_at  = 3;
    string url = 4;         //optional
    string uploadid = 5;    //optional
    string custom_id = 6;
    string mp = 7;
    string bi = 8;
    int64 file_size = 9;
}
// app+scope+key or provider+bucket+key
message ObjectId {
    string app = 1; 
    string scope = 2; 
    string provider = 3;
    string bucket = 4;
    string key = 5;
}


