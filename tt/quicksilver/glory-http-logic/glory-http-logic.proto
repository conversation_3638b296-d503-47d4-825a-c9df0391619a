syntax = "proto3";


option go_package = "golang.52tt.com/protocol/services/glory-http-logic";
package glory_http_logic;

import "tt/quicksilver/glory-reward/glory-reward.proto";

//荣耀世界-HTTP服务协议定义

// -------------------- 名流殿堂协议定义 begin ----------------------------------

enum LiveStatus {
    NotInLiving = 0;
    InLiving = 1;    // 直播中
    InPk = 2;        // pk中
    WatchLiving = 3; // 观看直播中
};
//名流用户
message CelebrityUserProfile {
    uint32 uid = 1;
    string account = 2;      //账号
    string nickname = 3;     //昵称
    int32 sex = 4;           //用户性别
    string ukw_account = 5;  //神秘人帐号(如果为空，不是神秘人)
    string ukw_nickname = 6; //神秘人帐号呢称
    uint32 channel_id   = 7; //房间id
    uint32 live_status  = 8; //LiveStatus
}

//名流周榜用户信息
message CelebrityRankInfo {
    CelebrityUserProfile  user  = 1;  //用户信息
    uint32                score = 2;  //荣耀星钻数量     
}

//获取名流周榜信息-请求
message GetCelebrityWeekRankReq {
    bool   is_cur_week = 1;    // 是否是当前周
}
message GetCelebrityWeekRankRsp {
    repeated  CelebrityRankInfo rank_list = 1;
    uint32    my_score = 2;                //我的荣耀星钻
    CelebrityUserProfile my_info = 3;      //我的信息
    bool      is_filter = 4;               //自己是否被过滤
}


// 名流殿堂-信息
message CelebrityPalaceInfo {
    CelebrityUserProfile user    = 1;     //用户信息
    uint32 celebrity_id          = 2;     //名流id
    uint32 rank                  = 3;     //第几位达成
    uint32 ts                    = 4;     //达成时间戳
    uint32 sum                   = 5;     //累计送礼个数
    uint32 last_ts               = 6;     //最新送礼时间
    uint32 last_num              = 7;     //最新送礼数量
}

message GiftInfo {
    uint32 gift_id           = 1;       //礼物id
    string gift_name         = 2;       //礼物名称
    string gift_icon_url     = 3;       //礼物icon
}
//获取名流殿堂顶部信息-请求
message GetCelebrityPalaceTopInfoReq {
    bool   is_cur_periods     = 1;      // 是否是当期
}
message GetCelebrityPalaceTopInfoRsp {
    CelebrityPalaceInfo       new_info     = 1;           //最新
    CelebrityPalaceInfo       best_info    = 2;           //最强
    GiftInfo                  curr_gift    = 3;           //当前礼物id
    CelebrityPalaceInfo       self_info    = 4;           //自己信息，如果celebrity_id=0，未达成
}

////获取名流殿堂分页列表-请求
message GetCelebrityPalaceInfoListReq {
    bool   is_cur_periods     = 1;      // 是否是当期
    uint32 last_celebrity_id  = 2;      //上一页最后一位的id,首页填0
}
message GetCelebrityPalaceInfoListRsp {
    repeated CelebrityPalaceInfo info_list = 1;           //其它列表(分页)
    bool                      is_all_end   = 2;           //true最后一页
}


//名流殿堂-回顾-请求
message ReplayCelebrityPalaceReq {
    uint32   celebrity_id  = 1;     //名流id
    bool   is_cur_periods  = 2;     // 是否是当期
}
message ReplayCelebrityPalaceRsp {
    CelebrityUserProfile   send_user      = 1;    //送礼者
    CelebrityUserProfile   to_user        = 2;    //收礼者
    GiftInfo               gift           = 3;    //礼物
    uint32                 ts             = 4;    //送礼时间
    uint32                 gift_num       = 5;    //送礼数量
    string                 show_cid       = 6;    //送礼所在房间展示id
}

// -------------------- 名流殿堂协议定义 end   ----------------------------------

// -------------------- 积分获取协议定义 start ----------------------------------
// IsNeedNoticeReq 是否需要红点提醒
message IsNeedNoticeReq {
    glory_reward.NoticeType type = 1; // 红点提示位置
}
message IsNeedNoticeResp {
    bool is_notice = 1; // 是否进行提醒
}

// ReceiveRewardReq 领取任务奖励
message ReceiveRewardReq {
    repeated uint32 task_id = 1; // 领取任务id列表
}
message ReceiveRewardResp {
}

message GetRewardFragmentInfoReq {
}
message GetRewardFragmentInfoResp {
    uint32 frag_fame = 1; // 声望星钻
    uint32 frag_glory = 2; // 荣耀星钻
    string account = 3; // 用户信息
    string nickname = 4; // 用户昵称
}

// ChallengeTaskInfo 挑战任务信息
message ChallengeTaskInfo {
    uint32 task_id = 1; // 任务ID，除首送挑战外都是这里
    glory_reward.GloryFragmentType frag_type = 2; // 碎片类型
    glory_reward.RewardType reward_type = 3; // 奖励类型
    glory_reward.TaskType task_type = 4; // 任务状态
    uint32 finish_num = 5; // 已完成数量
    uint32 need_finish_num = 6; // 总共需要完成的数量
    string icon_url = 7; // 首送挑战待送礼礼物图标
    string gift_name = 8; // 礼物名称
    uint32 gift_price = 9; // 礼物价值
    uint32 reward_num = 10; // 当前任务可以获取到的碎片数量
    bool is_this_week_task = 11; // 是否本周任务
}

// GetChallengeTaskDetailReq 获取所有挑战任务详情
message GetChallengeTaskDetailReq {
}
message GetChallengeTaskDetailResp {
    repeated ChallengeTaskInfo finish_tasks = 1; // 已完成挑战任务
    repeated ChallengeTaskInfo week_tasks = 2; // 本周未完成挑战任务
    repeated ChallengeTaskInfo gift_tasks = 3; // 礼物挑战任务
    bool is_notice = 4; // 是否需要红点提醒
}

// GetWeekTaskListReq 获取周任务信息
message GetWeekTaskListReq {
}
message GetWeekTaskListResp {
    repeated ChallengeTaskInfo task_info = 1; // 挑战信息列表
    repeated ChallengeTaskInfo gift_tasks = 2; // 礼物挑战任务
}

// TaskInfo 获取奖励的任务信息
message FragmentRewardInfo {
    glory_reward.GloryFragmentType frag_type = 1; // 碎片类型
    glory_reward.RewardType reward_type = 2; // 奖励类型
    glory_reward.TaskType task_type = 3; // 任务状态
    string remark = 4; // 获取说明(当为任务时展示该字段)
    string msg_icon_url = 5; // 当为基础奖励时，展示礼物图标
    string gift_name = 6; // 当为基础奖励时，礼物名称
    uint32 count = 7; // 送礼数量
    int64 reward_time = 8; // 获取时间
    int64 expire_time = 9; // 到期时间
    uint32 frag_num = 10; // 碎片数量
}

// GetRewardDetailReq 获取星钻获取明细信息
message GetRewardDetailReq {
    uint32 offset = 1;
    uint32 limit = 2;
    glory_reward.FragmentRewardType type = 3; // 筛选参数，如果不填默认查全部
}
message GetRewardDetailResp {
    repeated FragmentRewardInfo info = 1;
    uint32 total = 2;
}

// 获取入口状态
message GetEnterStatusReq {
}
message GetEnterStatusResp {
  bool is_open = 1; // 是否开启
}

// -------------------- 积分获取协议定义 end  ----------------------------------