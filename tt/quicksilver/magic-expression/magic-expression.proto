syntax = "proto3";

package magic_expression;
option go_package = "golang.52tt.com/protocol/services/magic-expression";

service MagicExpression {
  // 获取魔法表情
  rpc GetMagicExpressionInfoList(GetMagicExpressionInfoListReq) returns (GetMagicExpressionInfoListResp) {}
  // 推送房间麦上魔法表情
  rpc MagicExpressionPush(MagicExpressionPushReq) returns (MagicExpressionPushResp) {}
}

// ResourceType 资源类型
enum ResourceType {
  RESOURCE_TYPE_UNSPECIFIED = 0;
  RESOURCE_TYPE_DEFAULT = 1; // 默认
  RESOURCE_TYPE_SUPER_PLAYER = 2; // 超级玩家专属
  RESOURCE_TYPE_SVIP = 3; // SVIP专属
}

// MagicExpressionResource 魔法表情资源
message MagicExpressionResource {
  uint32 id = 1; // 自增ID，全局不重复
  string name = 2; // 表情名称
  string url = 3; // 表情资源包
  string md5 = 4; // 表情资源MD5
  ResourceType type = 5; // 表情资源类型
  string default_icon = 6; // 默认图标
  bool is_display = 7; // 是否外显
  string jump_url = 8; // 跳转链接（如果不可用时且存在当前链接时进行链接跳转，PC不可跳转则弹文案）
  string notice_text = 9; // 通知文案
  bool is_noble = 10; // 是否贵族专属
  string privilege_icon = 11; // 特权图标
  uint32 sort_id = 12; // 排序ID
}

// MagicExpressionTab 魔法表情TAB信息
message MagicExpressionTab {
  uint32 magic_tab_id = 1; // Tab的ID（默认Tab的ID固定为1，方便客户端进行处理，TabID不可用999，999为交互表情tabId）
  string tab_name = 2; // Tab的名称
  string tab_icon = 3; // Tab的图标
  repeated MagicExpressionResource items = 4; // Tab包含的资源列表
  uint32 version = 6; // Tab版本
  bool use_sort_id = 7; // 是否使用排序ID
}

// 获取魔法表情
message GetMagicExpressionInfoListReq {
}
message GetMagicExpressionInfoListResp {
  repeated MagicExpressionTab tabs = 1;
  string notice_pic = 2; // 通知图片
}

// 麦上魔法表情
message MagicExpressionPushReq{
  uint32 channel_id = 1; // 房间id
  uint32 magic_id = 2; // 表情id
  uint32 tab_id = 3; // tabId，旧版本传0
  uint32 uid = 4; // 触发uid
  bool is_old_push = 5; // 是否老版本推送
  uint32 opt_max_num = 6; // 最大推送次数 已废弃旧字段，做接口转移兼容处理
  repeated uint32 magic_num_list = 7; // 表情数量列表 已废弃旧字段，做接口转移兼容处理
}
message MagicExpressionPushResp {
}